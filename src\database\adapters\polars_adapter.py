"""Polars adapter for high-performance data processing.

This module provides a Polars-based adapter for efficient data processing,
offering significant performance improvements over pandas for large datasets.
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

try:
    import polars as pl
    POLARS_AVAILABLE = True
except ImportError:
    POLARS_AVAILABLE = False
    pl = None

import pandas as pd
from ..exceptions import DataProcessingError

logger = logging.getLogger(__name__)


class PolarsAdapter:
    """High-performance data processing adapter using Polars.
    
    Provides efficient data processing capabilities with automatic fallback
    to pandas when Polars is not available.
    """
    
    def __init__(self, use_polars: bool = True):
        """Initialize the Polars adapter.
        
        Args:
            use_polars: Whether to use Polars when available
        """
        self.use_polars = use_polars and POLARS_AVAILABLE
        
        if not POLARS_AVAILABLE and use_polars:
            logger.warning(
                "Polars not available. Falling back to pandas. "
                "Install polars for better performance: pip install polars"
            )
    
    def read_csv(
        self,
        file_path: Union[str, Path],
        **kwargs
    ) -> Union[pl.DataFrame, pd.DataFrame]:
        """Read CSV file using Polars or pandas.
        
        Args:
            file_path: Path to CSV file
            **kwargs: Additional arguments for reading
            
        Returns:
            DataFrame (Polars or pandas)
        """
        try:
            if self.use_polars:
                # Convert pandas-style kwargs to Polars format
                polars_kwargs = self._convert_pandas_to_polars_kwargs(kwargs)
                return pl.read_csv(str(file_path), **polars_kwargs)
            else:
                return pd.read_csv(file_path, **kwargs)
        except Exception as e:
            raise DataProcessingError(
                f"Failed to read CSV file: {e}",
                error_code="CSV_READ_FAILED",
                details={"file_path": str(file_path), "kwargs": kwargs}
            )
    
    def read_parquet(
        self,
        file_path: Union[str, Path],
        **kwargs
    ) -> Union[pl.DataFrame, pd.DataFrame]:
        """Read Parquet file using Polars or pandas.
        
        Args:
            file_path: Path to Parquet file
            **kwargs: Additional arguments for reading
            
        Returns:
            DataFrame (Polars or pandas)
        """
        try:
            if self.use_polars:
                return pl.read_parquet(str(file_path), **kwargs)
            else:
                return pd.read_parquet(file_path, **kwargs)
        except Exception as e:
            raise DataProcessingError(
                f"Failed to read Parquet file: {e}",
                error_code="PARQUET_READ_FAILED",
                details={"file_path": str(file_path), "kwargs": kwargs}
            )
    
    def to_pandas(self, df: Union[pl.DataFrame, pd.DataFrame]) -> pd.DataFrame:
        """Convert DataFrame to pandas format.
        
        Args:
            df: Input DataFrame
            
        Returns:
            pandas DataFrame
        """
        if self.use_polars and isinstance(df, pl.DataFrame):
            return df.to_pandas()
        return df
    
    def to_polars(self, df: pd.DataFrame) -> Union[pl.DataFrame, pd.DataFrame]:
        """Convert pandas DataFrame to Polars format.
        
        Args:
            df: pandas DataFrame
            
        Returns:
            Polars DataFrame if available, otherwise pandas DataFrame
        """
        if self.use_polars:
            return pl.from_pandas(df)
        return df
    
    def filter_data(
        self,
        df: Union[pl.DataFrame, pd.DataFrame],
        conditions: Dict[str, Any]
    ) -> Union[pl.DataFrame, pd.DataFrame]:
        """Filter data based on conditions.
        
        Args:
            df: Input DataFrame
            conditions: Filter conditions
            
        Returns:
            Filtered DataFrame
        """
        try:
            if self.use_polars and isinstance(df, pl.DataFrame):
                return self._filter_polars(df, conditions)
            else:
                return self._filter_pandas(df, conditions)
        except Exception as e:
            raise DataProcessingError(
                f"Failed to filter data: {e}",
                error_code="DATA_FILTER_FAILED",
                details={"conditions": conditions}
            )
    
    def aggregate_data(
        self,
        df: Union[pl.DataFrame, pd.DataFrame],
        group_by: List[str],
        aggregations: Dict[str, str]
    ) -> Union[pl.DataFrame, pd.DataFrame]:
        """Aggregate data by groups.
        
        Args:
            df: Input DataFrame
            group_by: Columns to group by
            aggregations: Aggregation functions for columns
            
        Returns:
            Aggregated DataFrame
        """
        try:
            if self.use_polars and isinstance(df, pl.DataFrame):
                return self._aggregate_polars(df, group_by, aggregations)
            else:
                return self._aggregate_pandas(df, group_by, aggregations)
        except Exception as e:
            raise DataProcessingError(
                f"Failed to aggregate data: {e}",
                error_code="DATA_AGGREGATION_FAILED",
                details={"group_by": group_by, "aggregations": aggregations}
            )
    
    def join_data(
        self,
        left: Union[pl.DataFrame, pd.DataFrame],
        right: Union[pl.DataFrame, pd.DataFrame],
        on: Union[str, List[str]],
        how: str = "inner"
    ) -> Union[pl.DataFrame, pd.DataFrame]:
        """Join two DataFrames.
        
        Args:
            left: Left DataFrame
            right: Right DataFrame
            on: Column(s) to join on
            how: Join type
            
        Returns:
            Joined DataFrame
        """
        try:
            if self.use_polars and isinstance(left, pl.DataFrame) and isinstance(right, pl.DataFrame):
                return left.join(right, on=on, how=how)
            else:
                # Convert to pandas if needed
                left_pd = self.to_pandas(left)
                right_pd = self.to_pandas(right)
                return left_pd.merge(right_pd, on=on, how=how)
        except Exception as e:
            raise DataProcessingError(
                f"Failed to join data: {e}",
                error_code="DATA_JOIN_FAILED",
                details={"on": on, "how": how}
            )
    
    def get_memory_usage(self, df: Union[pl.DataFrame, pd.DataFrame]) -> int:
        """Get memory usage of DataFrame in bytes.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Memory usage in bytes
        """
        if self.use_polars and isinstance(df, pl.DataFrame):
            return df.estimated_size()
        else:
            return df.memory_usage(deep=True).sum()
    
    def optimize_memory(
        self,
        df: Union[pl.DataFrame, pd.DataFrame]
    ) -> Union[pl.DataFrame, pd.DataFrame]:
        """Optimize DataFrame memory usage.
        
        Args:
            df: Input DataFrame
            
        Returns:
            Memory-optimized DataFrame
        """
        if self.use_polars and isinstance(df, pl.DataFrame):
            # Polars is already memory-optimized
            return df
        else:
            return self._optimize_pandas_memory(df)
    
    def _convert_pandas_to_polars_kwargs(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Convert pandas kwargs to Polars format."""
        polars_kwargs = kwargs.copy()
        
        # Map common pandas parameters to Polars equivalents
        if 'nrows' in polars_kwargs:
            polars_kwargs['n_rows'] = polars_kwargs.pop('nrows')
        
        if 'usecols' in polars_kwargs:
            polars_kwargs['columns'] = polars_kwargs.pop('usecols')
        
        return polars_kwargs
    
    def _filter_polars(self, df: pl.DataFrame, conditions: Dict[str, Any]) -> pl.DataFrame:
        """Filter Polars DataFrame."""
        result = df
        for column, value in conditions.items():
            if isinstance(value, (list, tuple)):
                result = result.filter(pl.col(column).is_in(value))
            else:
                result = result.filter(pl.col(column) == value)
        return result
    
    def _filter_pandas(self, df: pd.DataFrame, conditions: Dict[str, Any]) -> pd.DataFrame:
        """Filter pandas DataFrame."""
        result = df
        for column, value in conditions.items():
            if isinstance(value, (list, tuple)):
                result = result[result[column].isin(value)]
            else:
                result = result[result[column] == value]
        return result
    
    def _aggregate_polars(self, df: pl.DataFrame, group_by: List[str], aggregations: Dict[str, str]) -> pl.DataFrame:
        """Aggregate Polars DataFrame."""
        agg_exprs = []
        for column, func in aggregations.items():
            if func == 'sum':
                agg_exprs.append(pl.col(column).sum().alias(f"{column}_{func}"))
            elif func == 'mean':
                agg_exprs.append(pl.col(column).mean().alias(f"{column}_{func}"))
            elif func == 'count':
                agg_exprs.append(pl.col(column).count().alias(f"{column}_{func}"))
            elif func == 'max':
                agg_exprs.append(pl.col(column).max().alias(f"{column}_{func}"))
            elif func == 'min':
                agg_exprs.append(pl.col(column).min().alias(f"{column}_{func}"))
        
        return df.group_by(group_by).agg(agg_exprs)
    
    def _aggregate_pandas(self, df: pd.DataFrame, group_by: List[str], aggregations: Dict[str, str]) -> pd.DataFrame:
        """Aggregate pandas DataFrame."""
        return df.groupby(group_by).agg(aggregations).reset_index()
    
    def _optimize_pandas_memory(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize pandas DataFrame memory usage."""
        optimized_df = df.copy()
        
        for col in optimized_df.columns:
            col_type = optimized_df[col].dtype
            
            if col_type != 'object':
                c_min = optimized_df[col].min()
                c_max = optimized_df[col].max()
                
                if str(col_type)[:3] == 'int':
                    if c_min > pd.iinfo(pd.Int8Dtype()).min and c_max < pd.iinfo(pd.Int8Dtype()).max:
                        optimized_df[col] = optimized_df[col].astype(pd.Int8Dtype())
                    elif c_min > pd.iinfo(pd.Int16Dtype()).min and c_max < pd.iinfo(pd.Int16Dtype()).max:
                        optimized_df[col] = optimized_df[col].astype(pd.Int16Dtype())
                    elif c_min > pd.iinfo(pd.Int32Dtype()).min and c_max < pd.iinfo(pd.Int32Dtype()).max:
                        optimized_df[col] = optimized_df[col].astype(pd.Int32Dtype())
                
                elif str(col_type)[:5] == 'float':
                    if c_min > pd.finfo(pd.Float32Dtype()).min and c_max < pd.finfo(pd.Float32Dtype()).max:
                        optimized_df[col] = optimized_df[col].astype(pd.Float32Dtype())
        
        return optimized_df


# Global adapter instance
polars_adapter = PolarsAdapter()