1. 你是一名资深Python数据工程专家和软件架构师，精通Pandas数据处理、PostgreSQL数据库操作和模块化设计。 

请根据我提供的D:\automator\config\import\universal_file_mapping_config.xls映射表
根据提供的路径和文件（cdr\2024\Q3\SP\Shared_Benchmark_Q3_DE_2024_HTTP_File_DL_2024-08-19_13-07-46.xlsx)依据映射关系表自动匹配到对应的行。也能成功匹配到xlsx	Vodafone	0	cdr_vdf	2024Q3_http_file_dl	3这些列。

比如：cdr\2024\Q3\SP\Shared_Benchmark_Q3_DE_2024_HTTP_File_DL_2024-08-19_13-07-46.xlsx，对应的是：文件扩展格式是xlsx，要检查格式是否符合要求，Vodafone对应读取sheet name为Vodafone的sheet页数据，
skip_rows是指将读取该sheet页是要跳过第一行（因为列名从第2行开始），cdr_vdf是指导入到automator的这个schemas下，数据表名2024Q3_http_file_dl 对应的是postgresql的数据表名，基于这个表名创建数据表。 数据表列名：除了增加一个id 自动增加的主键（bigint）外，date使用datatime类型（使用业界最佳实践也可以），其他数据列均采用text。
要解决数据列名超长（>63)，要解决数据列数量超多（提供过滤数据列的机制，比如指定列或多列不导入，指定包括某类字符串的数据列不导入等等），另外要解决特殊字符的导入问题（全部替换为"_")，并且要遵守postgresql的命名规则的最佳实践。（要实现大小写不敏感）

目录结构如下：
D:.
│   .gitignore
│
├───cdr
│   ├───2022
│   │   ├───Q1
│   │   │   ├───SP
│   │   │   │       Shared_Benchmark_Q1_DE_DNS_2022-04-01_14-00-54.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_HTTP_FDTT_DL_2022-04-01_14-00-54.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_HTTP_FDTT_UL_2022-04-01_14-00-54.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_HTTP_File_DL_2022-04-01_14-00-54.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_HTTP_File_UL_2022-04-01_14-00-54.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_HTTP_Live_2022-04-01_14-00-54.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_HTTP_Static_2022-04-01_14-00-54.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_Ping_40_2022-04-01_14-00-54.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_Ping_800_2022-04-01_14-00-54.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_Youtube_2022-04-01_14-00-54.xlsx
│   │   │   │
│   │   │   └───Voice
│   │   │           Shared_Benchmark_Q1_DE_Voice_M2M_Calls_2022-04-01_14-00-54.xlsx
│   │   │           Shared_Benchmark_Q1_DE_Voice_M2M_SQ_Samples_2022-04-01_14-00-54.xlsx
│   │   │           Shared_Benchmark_Q1_DE_Voice_OTT_Calls_2022-04-01_14-00-54.xlsx
│   │   │           Shared_Benchmark_Q1_DE_Voice_OTT_SQ_Samples_2022-04-01_14-00-54.xlsx
│   │
│   │   │
│   │
│   ├───2025
│   │   ├───Q1
│   │   │   ├───SP
│   │   │   │       Shared_Benchmark_Q1_DE_2025_Conversational_App_Calls_2025-03-24_10-14-42.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_2025_Conversational_App_SQ_Samples_2025-03-24_10-14-42.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_2025_DNS_2025-03-24_10-14-42.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_2025_HTTP_Browsing_2025-03-24_10-14-42.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_2025_HTTP_FDTT_DL_2025-03-24_10-14-42.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_2025_HTTP_FDTT_UL_2025-03-24_10-14-42.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_2025_HTTP_File_DL_2025-03-24_10-14-42.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_2025_HTTP_File_UL_2025-03-24_10-14-42.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_2025_Interactivity_2025-03-24_10-14-42.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_2025_Ping_2025-03-24_10-14-42.xlsx
│   │   │   │       Shared_Benchmark_Q1_DE_2025_Youtube_2025-03-24_10-14-42.xlsx
│   │   │   │
│   │   │   └───Voice
│   │   │           Shared_Benchmark_Q1_DE_2025_Voice_M2M_Calls_2025-03-24_10-14-42.xlsx
│   │   │           Shared_Benchmark_Q1_DE_2025_Voice_M2M_SQ_Samples_2025-03-24_10-14-42.xlsx
├───cfg
│   ├───CW05
│   │   ├───CFG_05_South
│   │   │       BKPFileOf_swmengine-26-0_pack2161165768.tar.gz
│   │   │       BKPFileOf_swmengine-27-0_pack2161165769.tar.gz
│   │   │       BKPFileOf_swmengine-28-0_pack2161165770.tar.gz
│   │   │       BKPFileOf_swmengine-29-0_pack2161165771.tar.gz
│   │   │       BKPFileOf_swmengine-31-0_pack2161165772.tar.gz
│   │   │       BKPFileOf_swmengine-32-0_pack2161165773.tar.gz
│   │   │       BKPFileOf_swmengine-33-0_pack2161165774.tar.gz
│   │   │       BKPFileOf_swmengine-34-0_pack2161165775.tar.gz
│   │   │       BKPFileOf_swmengine-36-0_pack2161165776.tar.gz
│   │   │       BKPFileOf_swmengine-37-0_pack2161165777.tar.gz
│   │   │
│   │   └───CFG_05_West
│   │           BKPFileOf_swmengine-31-0_pack2157584050.tar.gz
│   │           BKPFileOf_swmengine-32-0_pack2157584051.tar.gz
│   │           BKPFileOf_swmengine-33-0_pack2157584052.tar.gz
│   │           BKPFileOf_swmengine-34-0_pack2157584053.tar.gz
│   │           BKPFileOf_swmengine-36-0_pack2157584054.tar.gz
│   │           BKPFileOf_swmengine-37-0_pack2157584055.tar.gz
│   │           BKPFileOf_swmengine-38-0_pack2157584056.tar.gz
│   │           BKPFileOf_swmengine-39-0_pack2157584057.tar.gz
│   │           BKPFileOf_swmengine-41-0_pack2157584058.tar.gz
│   │           BKPFileOf_swmengine-42-0_pack2157584085.tar.gz
│   │
│   └───CW20
│       ├───CW20_South
│       │       BKPFileOf_swmengine-26-0_pack2161165768.tar.gz
│       │       BKPFileOf_swmengine-27-0_pack2161165769.tar.gz
│       │       BKPFileOf_swmengine-28-0_pack2161165770.tar.gz
│       │       BKPFileOf_swmengine-29-0_pack2161165771.tar.gz
│       │       BKPFileOf_swmengine-31-0_pack2161165772.tar.gz
│       │       BKPFileOf_swmengine-32-0_pack2161165773.tar.gz
│       │       BKPFileOf_swmengine-33-0_pack2161165774.tar.gz
│       │       BKPFileOf_swmengine-34-0_pack2161165775.tar.gz
│       │       BKPFileOf_swmengine-36-0_pack2161165776.tar.gz
│       │       BKPFileOf_swmengine-37-0_pack2161165777.tar.gz
│       │
│       └───CW20_West
│               BKPFileOf_swmengine-31-0_pack2157584050.tar.gz
│               BKPFileOf_swmengine-32-0_pack2157584051.tar.gz
│               BKPFileOf_swmengine-33-0_pack2157584052.tar.gz
│               BKPFileOf_swmengine-34-0_pack2157584053.tar.gz
│               BKPFileOf_swmengine-36-0_pack2157584054.tar.gz
│               BKPFileOf_swmengine-37-0_pack2157584055.tar.gz
│               BKPFileOf_swmengine-38-0_pack2157584056.tar.gz
│               BKPFileOf_swmengine-39-0_pack2157584057.tar.gz
│               BKPFileOf_swmengine-41-0_pack2157584058.tar.gz
│               BKPFileOf_swmengine-42-0_pack2157584085.tar.gz
│
├───ep
│   │   test_ep_2023w01_20250525_012406.csv
│   │   test_ep_2023w01_20250525_013048.csv
│   │   test_ep_2023w01_20250525_013714.csv
│   │   test_ep_2023w01_20250525_014340.csv
│   │   test_ep_2023w01_20250525_020139.csv
│   │   test_ep_2023w01_20250525_020716.csv
│   │   test_ep_2023w01_20250525_021356.csv
│   │   test_ep_2023w01_20250525_021513.csv
│   │   test_ep_2023w01_20250525_021638.csv
│   │   test_ep_2023w01_20250525_022033.csv
│   │   test_ep_2023w01_20250525_022115.csv
│   │   test_ep_2023w01_20250525_022241.csv
│   │
│   ├───2024
│   │   ├───CW01
│   │   │       GSMCELL_CW01.xlsx
│   │   │       LTECELL_CW01.xlsx
│   │   │       NRCELL_CW01.xlsx
│   │   │       TEF_Sites_CW01.xlsx
│   │   │
│   │   ├───CW05
│   │   │       GSMCELL_CW05.xlsx
│   │   │       LTECELL_CW05.xlsx
│   │   │       NRCELL_CW05.xlsx
│   │   │       TEF_Sites_CW05.xlsx
│   │   │
│   │   ├───CW09
│   │   │   │   GSMCELL_CW09.xlsx
│   │   │   │   LTECELL_CW09.xlsx
│   │   │   │   NRCELL_CW09.xlsx
│   │   │   │   TEF_Sites_CW09.xlsx
│   │   │   │
│   │   │   └───QGIS
│   │   │           GSMCELL_CW05_M.gpkg
│   │   │           GSMCELL_CW05_mm.gpkg
│   │   │           LTECELL_CW09_M.gpkg
│   │   │           LTECELL_CW09_mm.gpkg
│   │   │           NRCELL_CW09_M.gpkg
│   │   │           NRCELL_CW09_mm.gpkg
│   │   │           TEF_Sites_CW09.gpkg
│   │   │           Telefonica_CW09.qgz
│   │   ├───CW14
│   │   │   │   GSMCELL_CW14.xlsx
│   │   │   │   LTECELL_CW14.xlsx
│   │   │   │   NRCELL_CW14.xlsx
│   │   │   │   TEF_Sites_CW14.xlsx
│   │   │   │
│   │   ├───CW26
│   │   │   │   GSMCELL_CW26.xlsx
│   │   │   │   LTECELL_CW26.xlsx
│   │   │   │   NRCELL_CW26.xlsx
│   │   │   │   TEF_SITE_CW26.xlsx
│   │   │   │
│   │   │   └───CW26_QGIS
│   │   │           GSMCELL_CW26_M.gpkg
│   │   │           GSMCELL_CW26_mm.gpkg
│   │   │           LTECELL_CW26_M.gpkg
│   │   │           LTECELL_CW26_mm.gpkg
│   │   │           NRCELL_CW26_M.gpkg
│   │   │           NRCELL_CW26_mm.gpkg
│   │   │           QGIS_CW26_mainProject.qgz
│   │   │           SITE_GoPack.gpkg
│   │   │           TEF_SITE_CW26.gpkg
│   │   │
│   │   ├───CW30
│   │   │   │   GSMCELL_CW30.xlsx
│   │   │   │   LTECELL_CW30.xlsx
│   │   │   │   NRCELL_CW30.xlsx
│   │   │   │   TEF_SITE_CW30.xlsx
│   │   │   │
│   │   │   └───QGIS_CW30
│   │   │           GSMCELL_CW30_M.gpkg
│   │   │           GSMCELL_CW30_mm.gpkg
│   │   │           LTECELL_CW30_M.gpkg
│   │   │           LTECELL_CW30_mm.gpkg
│   │   │           NRCELL_CW30_M.gpkg
│   │   │           NRCELL_CW30_mm.gpkg
│   │   │           QGIS_CW30.qgz
│   │   │           TEF_SITE_CW30.gpkg
│   │   │           TEF_SITE_CW30.qml
│   │   │
│   │   ├───CW36
│   │   │       GSMCELL_CW36.xlsx
│   │   │       LTECELL_CW36.xlsx
│   │   │       NRCELL_CW36.xlsx
│   │   │
│   │   │
│   │   └───CW51
│   │           GSMCELL_CW51.xlsx
│   │           LTECELL_CW51.xlsx
│   │           NRCELL_CW51.xlsx
│   │
│   └───2025
│       └───CW03
│               GSMCELL_CW03.xlsx
│               LTECELL_CW03.xlsx
│               NRCELL_CW03.xlsx
│               TEF_SITE_CW03.xlsx
│
├───kpi
│   ├───2019
│   │       Basic kpis_Query_Result_20190613101929451.xlsx
│   │
│   └───2025
│           Whole network KPI 30 days.xlsx
│
├───nlg
│   ├───2023
│   │       NLG_CUBE_aktuell_2023-01-03.xlsb
│   │       NLG_CUBE_aktuell_2023-12-13.xlsb
│   │       NLG_CUBE_aktuell_2023-12-20.xlsb
│   │
│   ├───2024
│   │       NLG_CUBE_aktuell-2024-08-20.xlsb
│   │       NLG_CUBE_aktuell_2024-02-22.xlsb
│   │       NLG_CUBE_aktuell_2024-11-10.xlsb
│   │       NLG_CUBE_aktuell_2024-12-18.xlsb
│   │
│   └───2025
│           NLG_CUBE_aktuell_2025-04-04.xlsb
│           NLG_CUBE_aktuell_2025-05-06.xlsb
│
└───score
        20210607_shared_benchmark_ch_q2_ch_campaign_01-FlatTable.xlsx
        20220316-shared_benchmark_q1_ch_ch_campaign_01-FlatTable.xlsx
        2022Q1_20220316-shared_benchmark_q1_ch_ch_campaign_01-FlatTable.xlsx
		
4. 数据文件均在D:\automator\data\interim， 请根据提供的映射文件的对应关系，将上述数据文件全部导入到postgreSQL对应的schemas的对应的数据表中。要严格按配置文件（excel）中的对应关系来，如果不在这个对应关系里的，给我提示，然后跳过处理。

5. 由于数据文件非常多，数据量非常大，要求高性能、可支持处理大数据文件。符合github python 多文件、大数据处理的业界最佳实践。


请结合上述需求给我实现上述需求的claude3.7 sonnet的提示词