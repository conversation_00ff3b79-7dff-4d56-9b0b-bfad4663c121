"""Aggregation functions and operations for query building.

This module provides classes for building various aggregation functions
with proper syntax and validation.
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from loguru import logger

from ..exceptions import ValidationError
from .dialects import Dialect


class AggregateFunction(Enum):
    """Types of aggregate functions."""

    COUNT = "COUNT"
    SUM = "SUM"
    AVG = "AVG"
    MIN = "MIN"
    MAX = "MAX"
    STDDEV = "STDDEV"
    VARIANCE = "VARIANCE"
    GROUP_CONCAT = "GROUP_CONCAT"
    STRING_AGG = "STRING_AGG"
    ARRAY_AGG = "ARRAY_AGG"
    JSON_AGG = "JSON_AGG"
    JSON_OBJECT_AGG = "JSON_OBJECT_AGG"
    BOOL_AND = "BOOL_AND"
    BOOL_OR = "BOOL_OR"
    BIT_AND = "BIT_AND"
    BIT_OR = "BIT_OR"
    CORR = "CORR"
    COVAR_POP = "COVAR_POP"
    COVAR_SAMP = "COVAR_SAMP"
    REGR_SLOPE = "REGR_SLOPE"
    REGR_INTERCEPT = "REGR_INTERCEPT"
    REGR_R2 = "REGR_R2"


class WindowFunction(Enum):
    """Types of window functions."""

    ROW_NUMBER = "ROW_NUMBER"
    RANK = "RANK"
    DENSE_RANK = "DENSE_RANK"
    PERCENT_RANK = "PERCENT_RANK"
    CUME_DIST = "CUME_DIST"
    NTILE = "NTILE"
    LAG = "LAG"
    LEAD = "LEAD"
    FIRST_VALUE = "FIRST_VALUE"
    LAST_VALUE = "LAST_VALUE"
    NTH_VALUE = "NTH_VALUE"


class Aggregation(ABC):
    """Abstract base class for aggregation operations."""

    def __init__(self, alias: Optional[str] = None):
        """Initialize aggregation.

        Args:
            alias: Optional alias for the aggregation
        """
        self.alias = alias

    @abstractmethod
    def build(self, dialect: Dialect) -> str:
        """Build the aggregation SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Aggregation SQL string
        """
        pass

    def _validate_identifier(self, identifier: str, dialect: Dialect) -> str:
        """Validate and escape SQL identifier.

        Args:
            identifier: SQL identifier
            dialect: SQL dialect

        Returns:
            Escaped identifier
        """
        return dialect.escape_identifier(identifier)

    def _build_with_alias(self, sql: str, dialect: Dialect) -> str:
        """Build SQL with optional alias.

        Args:
            sql: Base SQL
            dialect: SQL dialect

        Returns:
            SQL with alias if specified
        """
        if self.alias:
            alias_name = self._validate_identifier(self.alias, dialect)
            return f"{sql} AS {alias_name}"
        return sql


class SimpleAggregation(Aggregation):
    """Simple aggregation function."""

    def __init__(
        self,
        function: AggregateFunction,
        column: Optional[str] = None,
        distinct: bool = False,
        alias: Optional[str] = None,
    ):
        """Initialize simple aggregation.

        Args:
            function: Aggregate function
            column: Column name (None for COUNT(*))
            distinct: Whether to use DISTINCT
            alias: Optional alias
        """
        super().__init__(alias)
        self.function = function
        self.column = column
        self.distinct = distinct

    def build(self, dialect: Dialect) -> str:
        """Build simple aggregation SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Aggregation SQL string
        """
        try:
            if self.column is None:
                # COUNT(*) case
                if self.function == AggregateFunction.COUNT:
                    sql = "COUNT(*)"
                else:
                    raise ValidationError(
                        f"Function {self.function.value} requires a column"
                    )
            else:
                column_name = self._validate_identifier(self.column, dialect)
                distinct_clause = "DISTINCT " if self.distinct else ""
                sql = f"{self.function.value}({distinct_clause}{column_name})"

            return self._build_with_alias(sql, dialect)

        except Exception as e:
            logger.error(f"Failed to build simple aggregation: {e}")
            raise ValidationError(f"Failed to build aggregation: {e}")


class ConditionalAggregation(Aggregation):
    """Aggregation with FILTER clause."""

    def __init__(
        self,
        function: AggregateFunction,
        column: Optional[str] = None,
        condition: Optional[str] = None,
        distinct: bool = False,
        alias: Optional[str] = None,
    ):
        """Initialize conditional aggregation.

        Args:
            function: Aggregate function
            column: Column name
            condition: Filter condition
            distinct: Whether to use DISTINCT
            alias: Optional alias
        """
        super().__init__(alias)
        self.function = function
        self.column = column
        self.condition = condition
        self.distinct = distinct

    def build(self, dialect: Dialect) -> str:
        """Build conditional aggregation SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Aggregation SQL string
        """
        try:
            if self.column is None and self.function != AggregateFunction.COUNT:
                raise ValidationError(
                    f"Function {self.function.value} requires a column"
                )

            if self.column is None:
                base_sql = "COUNT(*)"
            else:
                column_name = self._validate_identifier(self.column, dialect)
                distinct_clause = "DISTINCT " if self.distinct else ""
                base_sql = f"{self.function.value}({distinct_clause}{column_name})"

            if self.condition:
                sql = f"{base_sql} FILTER (WHERE {self.condition})"
            else:
                sql = base_sql

            return self._build_with_alias(sql, dialect)

        except Exception as e:
            logger.error(f"Failed to build conditional aggregation: {e}")
            raise ValidationError(f"Failed to build conditional aggregation: {e}")


class WindowAggregation(Aggregation):
    """Window function aggregation."""

    def __init__(
        self,
        function: Union[WindowFunction, AggregateFunction],
        column: Optional[str] = None,
        partition_by: Optional[List[str]] = None,
        order_by: Optional[List[str]] = None,
        frame: Optional[str] = None,
        alias: Optional[str] = None,
    ):
        """Initialize window aggregation.

        Args:
            function: Window or aggregate function
            column: Column name
            partition_by: PARTITION BY columns
            order_by: ORDER BY columns
            frame: Window frame specification
            alias: Optional alias
        """
        super().__init__(alias)
        self.function = function
        self.column = column
        self.partition_by = partition_by or []
        self.order_by = order_by or []
        self.frame = frame

    def build(self, dialect: Dialect) -> str:
        """Build window aggregation SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Window aggregation SQL string
        """
        try:
            # Build function call
            if self.column is None:
                if isinstance(self.function, WindowFunction) and self.function in [
                    WindowFunction.ROW_NUMBER,
                    WindowFunction.RANK,
                    WindowFunction.DENSE_RANK,
                    WindowFunction.PERCENT_RANK,
                    WindowFunction.CUME_DIST,
                ]:
                    func_sql = f"{self.function.value}()"
                else:
                    raise ValidationError(
                        f"Function {self.function.value} requires a column"
                    )
            else:
                column_name = self._validate_identifier(self.column, dialect)
                func_sql = f"{self.function.value}({column_name})"

            # Build OVER clause
            over_parts = []

            if self.partition_by:
                partition_columns = [
                    self._validate_identifier(col, dialect) for col in self.partition_by
                ]
                over_parts.append(f"PARTITION BY {', '.join(partition_columns)}")

            if self.order_by:
                order_columns = [
                    self._validate_identifier(col, dialect) for col in self.order_by
                ]
                over_parts.append(f"ORDER BY {', '.join(order_columns)}")

            if self.frame:
                over_parts.append(self.frame)

            over_clause = f"OVER ({' '.join(over_parts)})" if over_parts else "OVER ()"
            sql = f"{func_sql} {over_clause}"

            return self._build_with_alias(sql, dialect)

        except Exception as e:
            logger.error(f"Failed to build window aggregation: {e}")
            raise ValidationError(f"Failed to build window aggregation: {e}")


class CustomAggregation(Aggregation):
    """Custom aggregation function."""

    def __init__(
        self, function_name: str, arguments: List[str], alias: Optional[str] = None
    ):
        """Initialize custom aggregation.

        Args:
            function_name: Custom function name
            arguments: Function arguments
            alias: Optional alias
        """
        super().__init__(alias)
        self.function_name = function_name
        self.arguments = arguments

    def build(self, dialect: Dialect) -> str:
        """Build custom aggregation SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Custom aggregation SQL string
        """
        try:
            func_name = self._validate_identifier(self.function_name, dialect)

            if self.arguments:
                args = [
                    self._validate_identifier(arg, dialect) for arg in self.arguments
                ]
                sql = f"{func_name}({', '.join(args)})"
            else:
                sql = f"{func_name}()"

            return self._build_with_alias(sql, dialect)

        except Exception as e:
            logger.error(f"Failed to build custom aggregation: {e}")
            raise ValidationError(f"Failed to build custom aggregation: {e}")


class GroupBy:
    """GROUP BY clause builder."""

    def __init__(self, columns: List[str], having: Optional[str] = None):
        """Initialize GROUP BY.

        Args:
            columns: Columns to group by
            having: Optional HAVING clause
        """
        self.columns = columns
        self.having = having

        if not columns:
            raise ValidationError("GROUP BY requires at least one column")

    def build(self, dialect: Dialect) -> str:
        """Build GROUP BY SQL.

        Args:
            dialect: SQL dialect

        Returns:
            GROUP BY SQL string
        """
        try:
            # Validate and escape column names
            escaped_columns = [dialect.escape_identifier(col) for col in self.columns]
            group_by_sql = f"GROUP BY {', '.join(escaped_columns)}"

            if self.having:
                group_by_sql += f" HAVING {self.having}"

            return group_by_sql

        except Exception as e:
            logger.error(f"Failed to build GROUP BY: {e}")
            raise ValidationError(f"Failed to build GROUP BY: {e}")


# Convenience functions for creating aggregations
def count(
    column: Optional[str] = None, distinct: bool = False, alias: Optional[str] = None
) -> SimpleAggregation:
    """Create COUNT aggregation.

    Args:
        column: Column name (None for COUNT(*))
        distinct: Whether to use DISTINCT
        alias: Optional alias

    Returns:
        SimpleAggregation instance
    """
    return SimpleAggregation(AggregateFunction.COUNT, column, distinct, alias)


def sum_agg(
    column: str, distinct: bool = False, alias: Optional[str] = None
) -> SimpleAggregation:
    """Create SUM aggregation.

    Args:
        column: Column name
        distinct: Whether to use DISTINCT
        alias: Optional alias

    Returns:
        SimpleAggregation instance
    """
    return SimpleAggregation(AggregateFunction.SUM, column, distinct, alias)


def avg(
    column: str, distinct: bool = False, alias: Optional[str] = None
) -> SimpleAggregation:
    """Create AVG aggregation.

    Args:
        column: Column name
        distinct: Whether to use DISTINCT
        alias: Optional alias

    Returns:
        SimpleAggregation instance
    """
    return SimpleAggregation(AggregateFunction.AVG, column, distinct, alias)


def min_agg(column: str, alias: Optional[str] = None) -> SimpleAggregation:
    """Create MIN aggregation.

    Args:
        column: Column name
        alias: Optional alias

    Returns:
        SimpleAggregation instance
    """
    return SimpleAggregation(AggregateFunction.MIN, column, False, alias)


def max_agg(column: str, alias: Optional[str] = None) -> SimpleAggregation:
    """Create MAX aggregation.

    Args:
        column: Column name
        alias: Optional alias

    Returns:
        SimpleAggregation instance
    """
    return SimpleAggregation(AggregateFunction.MAX, column, False, alias)


def stddev(column: str, alias: Optional[str] = None) -> SimpleAggregation:
    """Create STDDEV aggregation.

    Args:
        column: Column name
        alias: Optional alias

    Returns:
        SimpleAggregation instance
    """
    return SimpleAggregation(AggregateFunction.STDDEV, column, False, alias)


def variance(column: str, alias: Optional[str] = None) -> SimpleAggregation:
    """Create VARIANCE aggregation.

    Args:
        column: Column name
        alias: Optional alias

    Returns:
        SimpleAggregation instance
    """
    return SimpleAggregation(AggregateFunction.VARIANCE, column, False, alias)


def group_concat(
    column: str, separator: str = ",", alias: Optional[str] = None
) -> CustomAggregation:
    """Create GROUP_CONCAT aggregation.

    Args:
        column: Column name
        separator: Separator string
        alias: Optional alias

    Returns:
        CustomAggregation instance
    """
    return CustomAggregation("GROUP_CONCAT", [column, f"'{separator}'"], alias)


def string_agg(
    column: str, separator: str = ",", alias: Optional[str] = None
) -> CustomAggregation:
    """Create STRING_AGG aggregation.

    Args:
        column: Column name
        separator: Separator string
        alias: Optional alias

    Returns:
        CustomAggregation instance
    """
    return CustomAggregation("STRING_AGG", [column, f"'{separator}'"], alias)


def array_agg(column: str, alias: Optional[str] = None) -> SimpleAggregation:
    """Create ARRAY_AGG aggregation.

    Args:
        column: Column name
        alias: Optional alias

    Returns:
        SimpleAggregation instance
    """
    return SimpleAggregation(AggregateFunction.ARRAY_AGG, column, False, alias)


def json_agg(column: str, alias: Optional[str] = None) -> SimpleAggregation:
    """Create JSON_AGG aggregation.

    Args:
        column: Column name
        alias: Optional alias

    Returns:
        SimpleAggregation instance
    """
    return SimpleAggregation(AggregateFunction.JSON_AGG, column, False, alias)


def row_number(
    partition_by: Optional[List[str]] = None,
    order_by: Optional[List[str]] = None,
    alias: Optional[str] = None,
) -> WindowAggregation:
    """Create ROW_NUMBER window function.

    Args:
        partition_by: PARTITION BY columns
        order_by: ORDER BY columns
        alias: Optional alias

    Returns:
        WindowAggregation instance
    """
    return WindowAggregation(
        WindowFunction.ROW_NUMBER, None, partition_by, order_by, None, alias
    )


def rank(
    partition_by: Optional[List[str]] = None,
    order_by: Optional[List[str]] = None,
    alias: Optional[str] = None,
) -> WindowAggregation:
    """Create RANK window function.

    Args:
        partition_by: PARTITION BY columns
        order_by: ORDER BY columns
        alias: Optional alias

    Returns:
        WindowAggregation instance
    """
    return WindowAggregation(
        WindowFunction.RANK, None, partition_by, order_by, None, alias
    )


def dense_rank(
    partition_by: Optional[List[str]] = None,
    order_by: Optional[List[str]] = None,
    alias: Optional[str] = None,
) -> WindowAggregation:
    """Create DENSE_RANK window function.

    Args:
        partition_by: PARTITION BY columns
        order_by: ORDER BY columns
        alias: Optional alias

    Returns:
        WindowAggregation instance
    """
    return WindowAggregation(
        WindowFunction.DENSE_RANK, None, partition_by, order_by, None, alias
    )


def lag(
    column: str,
    offset: int = 1,
    default: Optional[str] = None,
    partition_by: Optional[List[str]] = None,
    order_by: Optional[List[str]] = None,
    alias: Optional[str] = None,
) -> CustomAggregation:
    """Create LAG window function.

    Args:
        column: Column name
        offset: Offset value
        default: Default value
        partition_by: PARTITION BY columns
        order_by: ORDER BY columns
        alias: Optional alias

    Returns:
        CustomAggregation instance
    """
    args = [column, str(offset)]
    if default is not None:
        args.append(default)
    return CustomAggregation("LAG", args, alias)


def lead(
    column: str,
    offset: int = 1,
    default: Optional[str] = None,
    partition_by: Optional[List[str]] = None,
    order_by: Optional[List[str]] = None,
    alias: Optional[str] = None,
) -> CustomAggregation:
    """Create LEAD window function.

    Args:
        column: Column name
        offset: Offset value
        default: Default value
        partition_by: PARTITION BY columns
        order_by: ORDER BY columns
        alias: Optional alias

    Returns:
        CustomAggregation instance
    """
    args = [column, str(offset)]
    if default is not None:
        args.append(default)
    return CustomAggregation("LEAD", args, alias)


def group_by(columns: List[str], having: Optional[str] = None) -> GroupBy:
    """Create GROUP BY clause.

    Args:
        columns: Columns to group by
        having: Optional HAVING clause

    Returns:
        GroupBy instance
    """
    return GroupBy(columns, having)
