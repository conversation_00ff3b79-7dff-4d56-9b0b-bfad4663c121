"""Tests for the generic helper utilities module.

This module contains comprehensive tests for various helper functions
in the helpers module.
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest

from src.database.utils.helpers import (
    camel_to_snake,
    chunk_list,
    deep_merge_dicts,
    ensure_directory_exists,
    flatten_dict,
    format_bytes,
    get_file_extension,
    is_valid_email,
    safe_file_name,
    sanitize_string,
    snake_to_camel,
    truncate_string,
)


class TestStringHelpers:
    """Test cases for string manipulation functions."""

    def test_sanitize_string(self):
        """Test the string cleaning function."""
        test_cases = [
            ("  hello world  ", "hello world"),
            ("\t\ntest\r\n", "test"),
            ("", ""),
            ("   ", ""),
            ("no spaces", "no spaces"),
        ]

        for input_str, expected in test_cases:
            result = sanitize_string(input_str)
            assert (
                result == expected
            ), f"sanitize_string('{input_str}') should return '{expected}', got '{result}'"

    def test_sanitize_string_with_max_length(self):
        """Test string cleaning with max length restriction."""
        test_cases = [
            ("hello world", 5, "hello"),
            ("  test string  ", 4, "test"),
            ("short", 10, "short"),
            ("exactly_ten", 10, "exactly_te"),  # Truncated and trailing space removed
        ]

        for input_str, max_len, expected in test_cases:
            result = sanitize_string(input_str, max_len)
            assert result == expected

    def test_sanitize_string_non_string_input(self):
        """Test handling of non-string inputs."""
        test_cases = [
            (123, "123"),
            (True, "True"),
            (None, "None"),
            ([], "[]"),
        ]

        for input_val, expected in test_cases:
            result = sanitize_string(input_val)
            assert result == expected

    def test_snake_to_camel(self):
        """Test snake_case to camelCase conversion."""
        test_cases = [
            ("user_name", False, "userName"),
            ("user_name", True, "UserName"),
            ("first_name_last_name", False, "firstNameLastName"),
            ("first_name_last_name", True, "FirstNameLastName"),
            ("single", False, "single"),
            ("single", True, "Single"),
            ("", False, ""),
            ("no_underscores", False, "noUnderscores"),
        ]

        for snake_str, capitalize_first, expected in test_cases:
            result = snake_to_camel(snake_str, capitalize_first)
            assert result == expected

    def test_camel_to_snake(self):
        """Test camelCase to snake_case conversion."""
        test_cases = [
            ("userName", "user_name"),
            ("UserName", "user_name"),
            ("firstName", "first_name"),
            ("XMLHttpRequest", "xmlhttp_request"),
            ("simple", "simple"),
            ("", ""),
            ("CamelCaseExample", "camel_case_example"),
            ("HTMLParser", "htmlparser"),
        ]

        for camel_str, expected in test_cases:
            result = camel_to_snake(camel_str)
            assert result == expected

    def test_truncate_string(self):
        """Test the string truncation function."""
        test_cases = [
            ("This is a long text", 10, "...", "This is..."),
            ("Short", 10, "...", "Short"),
            ("Exactly ten", 10, "...", "Exactly..."),  # Length exactly equals limit
            ("Test", 10, "***", "Test"),
            ("Long text here", 8, "..", "Long t.."),
        ]

        for text, max_len, suffix, expected in test_cases:
            result = truncate_string(text, max_len, suffix)
            assert result == expected


class TestFileHelpers:
    """Test cases for file handling functions."""

    def test_ensure_directory_exists(self):
        """Test the directory creation function."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test creating a new directory
            new_dir = Path(temp_dir) / "new_directory"
            result = ensure_directory_exists(new_dir)

            assert result.exists()
            assert result.is_dir()
            assert result == new_dir

            # Test existing directory
            result2 = ensure_directory_exists(new_dir)
            assert result2 == new_dir

            # Test nested directory creation
            nested_dir = new_dir / "nested" / "deep"
            result3 = ensure_directory_exists(nested_dir)
            assert result3.exists()
            assert result3.is_dir()

    def test_safe_file_name(self):
        """Test secure filename generation."""
        test_cases = [
            ("normal_file.txt", "_", "normal_file.txt"),
            ("file<name>.txt", "_", "file_name_.txt"),
            ("file/name.txt", "-", "file-name.txt"),
            ("file:name|test.txt", "_", "file_name_test.txt"),
            ('file"name.txt', "_", "file_name.txt"),
            (
                "___file___",
                "_",
                "file",
            ),  # Remove leading/trailing replacement characters
            ("", "_", "unnamed"),  # Empty filename
            ("___", "_", "unnamed"),  # Only unsafe characters
        ]

        for filename, replacement, expected in test_cases:
            result = safe_file_name(filename, replacement)
            assert result == expected

    def test_get_file_extension(self):
        """Test file extension retrieval."""
        test_cases = [
            ("file.txt", "txt"),
            ("data.csv", "csv"),
            ("/path/to/file.xlsx", "xlsx"),
            ("file.tar.gz", "gz"),
            ("no_extension", ""),
            (".hidden", ""),
            ("file.", ""),
        ]

        for file_path, expected in test_cases:
            result = get_file_extension(file_path)
            assert result == expected


class TestDataHelpers:
    """Test cases for data processing functions."""

    def test_flatten_dict(self):
        """Test the dictionary flattening function."""
        test_cases = [
            ({"a": {"b": {"c": 1}}}, ".", {"a.b.c": 1}),
            ({"x": {"y": 2}}, "_", {"x_y": 2}),
            ({"a": 1, "b": {"c": 2, "d": 3}}, ".", {"a": 1, "b.c": 2, "b.d": 3}),
            ({}, ".", {}),
            ({"simple": "value"}, ".", {"simple": "value"}),
        ]

        for nested_dict, separator, expected in test_cases:
            result = flatten_dict(nested_dict, separator)
            assert result == expected

    def test_chunk_list(self):
        """Test the list chunking function."""
        test_cases = [
            ([1, 2, 3, 4, 5], 2, [[1, 2], [3, 4], [5]]),
            (["a", "b", "c", "d"], 3, [["a", "b", "c"], ["d"]]),
            ([1, 2, 3], 5, [[1, 2, 3]]),  # Chunk size larger than list length
            ([], 2, []),  # Empty list
            ([1], 1, [[1]]),  # Single element list
        ]

        for lst, chunk_size, expected in test_cases:
            result = chunk_list(lst, chunk_size)
            assert result == expected

    def test_chunk_list_invalid_size(self):
        """Test invalid chunk sizes."""
        with pytest.raises(ValueError, match="Chunk size must be positive"):
            chunk_list([1, 2, 3], 0)

        with pytest.raises(ValueError, match="Chunk size must be positive"):
            chunk_list([1, 2, 3], -1)

    def test_deep_merge_dicts(self):
        """Test the deep dictionary merge function."""
        test_cases = [
            ({"a": {"b": 1}}, {"a": {"c": 2}}, {"a": {"b": 1, "c": 2}}),
            ({"x": 1}, {"x": 2}, {"x": 2}),  # Overwrite value
            ({}, {"a": 1}, {"a": 1}),  # Merge with empty dict
            ({"a": 1}, {}, {"a": 1}),  # Merge empty dict
            (
                {"a": {"b": {"c": 1}}},
                {"a": {"b": {"d": 2}}},
                {"a": {"b": {"c": 1, "d": 2}}},
            ),
        ]

        for dict1, dict2, expected in test_cases:
            result = deep_merge_dicts(dict1, dict2)
            assert result == expected


class TestFormatHelpers:
    """Test cases for formatting functions."""

    def test_format_bytes(self):
        """Test the byte formatting function."""
        test_cases = [
            (0, 2, "0 B"),
            (1024, 2, "1.00 KB"),
            (1048576, 2, "1.00 MB"),
            (1073741824, 1, "1.0 GB"),
            (1099511627776, 2, "1.00 TB"),
            (512, 2, "512.00 B"),
            (1536, 2, "1.50 KB"),  # 1.5 KB
        ]

        for bytes_value, decimal_places, expected in test_cases:
            result = format_bytes(bytes_value, decimal_places)
            assert result == expected


class TestValidationHelpers:
    """Test cases for validation functions."""

    def test_is_valid_email(self):
        """Test the email validation function."""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]

        invalid_emails = [
            "invalid-email",
            "@example.com",
            "user@",
            "user@.com",
            "<EMAIL>",
            "user@example",
            "",
            "user <EMAIL>",  # Contains space
            ".<EMAIL>",  # Starts with a period
            "<EMAIL>",  # Ends with a period
        ]

        for email in valid_emails:
            assert is_valid_email(email), f"'{email}' should be valid"

        for email in invalid_emails:
            assert not is_valid_email(email), f"'{email}' should be invalid"


class TestIntegrationScenarios:
    """Integration test scenarios."""

    def test_name_processing_workflow(self):
        """Test the full workflow for name handling."""
        # Simulate processing user-input table name
        user_input = "  User-Profile Table  "

        # 1. Clean the input
        cleaned = sanitize_string(user_input)
        assert cleaned == "User-Profile Table"

        # 2. Generate a secure filename
        safe_name = safe_file_name(cleaned)
        assert safe_name == "User-Profile Table"

        # 3. Convert to snake_case
        snake_name = camel_to_snake(safe_name.replace(" ", "_"))
        assert snake_name == "user-profile_table"

        # 4. Replace hyphens with underscores for table name rules
        final_name = snake_name.replace("-", "_")
        assert final_name == "user_profile_table"

        # 5. Validate the final name
        from src.database.utils.validators import InputValidator

        assert InputValidator.validate_table_name(final_name)

    def test_configuration_processing(self):
        """Test configuration processing scenario."""
        # Simulate nested configuration
        config = {
            "database": {
                "connection": {"host": "localhost", "port": 5432},
                "pool": {"size": 10},
            },
            "logging": {"level": "INFO"},
        }

        # Flatten the configuration
        flat_config = flatten_dict(config)
        expected_keys = [
            "database.connection.host",
            "database.connection.port",
            "database.pool.size",
            "logging.level",
        ]

        for key in expected_keys:
            assert key in flat_config

        assert flat_config["database.connection.host"] == "localhost"
        assert flat_config["database.connection.port"] == 5432

    def test_batch_processing_scenario(self):
        """Test batch processing scenario."""
        # Simulate batch processing of a large amount of data
        data = list(range(100))

        # Process in chunks
        chunks = chunk_list(data, 10)
        assert len(chunks) == 10
        assert all(len(chunk) == 10 for chunk in chunks)

        # Verify all data is included
        flattened = [item for chunk in chunks for item in chunk]
        assert flattened == data
