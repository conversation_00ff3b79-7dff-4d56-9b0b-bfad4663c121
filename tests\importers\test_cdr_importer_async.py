"""Tests for CDR importer async functionality."""

import pytest
import pandas as pd
import asyncio
from datetime import datetime
from unittest.mock import Mock, AsyncMock

from src.importers.cdr_importer import CDRImporter
from src.importers.base import ImportResult


class TestCDRImporterAsync:
    """Test cases for CDR importer async functionality."""

    @pytest.fixture
    def cdr_importer(self):
        """Create CDR importer instance."""
        return CDRImporter({})

    @pytest.fixture
    def sample_cdr_data(self):
        """Create sample CDR data for testing."""
        return pd.DataFrame({
            'call_id': ['call_001', 'call_002', 'call_003'],
            'caller_number': ['1234567890', '0987654321', '1122334455'],
            'called_number': ['5555555555', '6666666666', '7777777777'],
            'call_start_time': [datetime.now()] * 3,
            'call_duration': [120, 300, 45],
            'call_status': ['completed', 'completed', 'failed'],
            'cell_id': ['cell_001', 'cell_002', 'cell_001'],
            'cell_tower_lat': [40.7128, 40.7589, 40.7128],
            'cell_tower_lon': [-74.0060, -73.9851, -74.0060],
        })

    @pytest.mark.asyncio
    async def test_validate_cdr_data_async(self, cdr_importer, sample_cdr_data):
        """Test async CDR data validation."""
        validated_data = await cdr_importer._validate_cdr_data_async(sample_cdr_data)
        
        assert isinstance(validated_data, pd.DataFrame)
        assert len(validated_data) <= len(sample_cdr_data)  # May filter out invalid records
        assert 'call_id' in validated_data.columns

    @pytest.mark.asyncio
    async def test_validate_cdr_data_async_removes_duplicates(self, cdr_importer):
        """Test that async validation removes duplicate call IDs."""
        duplicate_data = pd.DataFrame({
            'call_id': ['call_001', 'call_001', 'call_002'],
            'caller_number': ['1234567890', '1234567890', '0987654321'],
            'call_duration': [120, 120, 300],
        })
        
        validated_data = await cdr_importer._validate_cdr_data_async(duplicate_data)
        
        assert len(validated_data) == 2  # Should remove one duplicate
        assert validated_data['call_id'].nunique() == 2

    @pytest.mark.asyncio
    async def test_validate_cdr_data_async_filters_negative_duration(self, cdr_importer):
        """Test that async validation filters negative call durations."""
        invalid_data = pd.DataFrame({
            'call_id': ['call_001', 'call_002', 'call_003'],
            'caller_number': ['1234567890', '0987654321', '1122334455'],
            'call_duration': [120, -50, 300],  # Negative duration
        })
        
        validated_data = await cdr_importer._validate_cdr_data_async(invalid_data)
        
        assert len(validated_data) == 2  # Should filter out negative duration
        assert all(validated_data['call_duration'] >= 0)

    @pytest.mark.asyncio
    async def test_transform_data_async(self, cdr_importer, sample_cdr_data):
        """Test async data transformation."""
        transformed_data = await cdr_importer._transform_data_async(sample_cdr_data)
        
        assert isinstance(transformed_data, pd.DataFrame)
        assert 'geometry' in transformed_data.columns
        assert 'import_timestamp' in transformed_data.columns

    @pytest.mark.asyncio
    async def test_transform_data_async_creates_geometry(self, cdr_importer, sample_cdr_data):
        """Test that async transformation creates geometry from coordinates."""
        transformed_data = await cdr_importer._transform_data_async(sample_cdr_data)
        
        # Check that geometry is created for valid coordinates
        valid_geometry_count = transformed_data['geometry'].notna().sum()
        assert valid_geometry_count > 0

    @pytest.mark.asyncio
    async def test_calculate_cdr_kpis(self, cdr_importer, sample_cdr_data):
        """Test CDR KPI calculation."""
        kpi_data = await cdr_importer._calculate_cdr_kpis(sample_cdr_data)
        
        if kpi_data is not None:
            assert isinstance(kpi_data, pd.DataFrame)
            assert 'kpi_type' in kpi_data.columns
            assert 'calculation_time' in kpi_data.columns

    @pytest.mark.asyncio
    async def test_calculate_cdr_kpis_empty_data(self, cdr_importer):
        """Test KPI calculation with empty data."""
        empty_data = pd.DataFrame()
        kpi_data = await cdr_importer._calculate_cdr_kpis(empty_data)
        
        assert kpi_data is None

    @pytest.mark.asyncio
    async def test_process_batch_async(self, cdr_importer, sample_cdr_data):
        """Test async batch processing."""
        result = await cdr_importer.process_batch_async(sample_cdr_data, batch_id="test_batch")

        assert isinstance(result, ImportResult)
        assert result.success is True
        assert result.metrics.records_processed > 0
        assert result.metrics.processing_time_seconds > 0
        assert result.metadata['batch_id'] == "test_batch"

    @pytest.mark.asyncio
    async def test_process_batch_async_with_error(self, cdr_importer):
        """Test async batch processing with error."""
        # Create data that will cause an error during transformation
        # Use None values that will cause issues in data processing
        invalid_data = pd.DataFrame({'call_id': [None, None, None]})

        # Mock the _transform_data_async method to raise an exception
        import unittest.mock
        with unittest.mock.patch.object(cdr_importer, '_transform_data_async', side_effect=Exception("Transformation failed")):
            result = await cdr_importer.process_batch_async(invalid_data, batch_id="error_batch")

        assert isinstance(result, ImportResult)
        assert result.success is False
        assert result.error_message is not None
        assert "Transformation failed" in result.error_message

    @pytest.mark.asyncio
    async def test_bulk_insert_async_without_db(self, cdr_importer, sample_cdr_data):
        """Test bulk insert async without database session."""
        # Should not raise error, just log warning
        await cdr_importer._bulk_insert_async(sample_cdr_data, "test_table", "test_batch")

    @pytest.mark.asyncio
    async def test_create_cdr_table_async(self, cdr_importer):
        """Test async CDR table creation."""
        # Should not raise error without database session
        await cdr_importer._create_cdr_table_async("test_table")

    def test_process_batch_sync_wrapper(self, cdr_importer, sample_cdr_data):
        """Test synchronous wrapper for async batch processing."""
        result = cdr_importer.process_batch(sample_cdr_data)
        
        assert isinstance(result, ImportResult)
        assert result.success is True

    @pytest.mark.asyncio
    async def test_process_batch_async_performance_metrics(self, cdr_importer, sample_cdr_data):
        """Test that async processing includes performance metrics."""
        result = await cdr_importer.process_batch_async(sample_cdr_data, batch_id="perf_test")

        assert result.metrics.processing_time_seconds > 0
        assert 'processing_time_seconds' in result.metadata
        assert result.metadata['processing_time_seconds'] > 0

    @pytest.mark.asyncio
    async def test_process_batch_async_geospatial_enhancement(self, cdr_importer, sample_cdr_data):
        """Test that async processing includes geospatial enhancement."""
        result = await cdr_importer.process_batch_async(sample_cdr_data, batch_id="geo_test")
        
        assert result.metadata['has_geometry'] is True
        assert 'geometry' in result.metadata['columns']
