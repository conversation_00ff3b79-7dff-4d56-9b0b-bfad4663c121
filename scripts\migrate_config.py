#!/usr/bin/env python3
"""Configuration migration script for Connect project.

This script helps migrate from the legacy configuration systems to the new
unified configuration manager, ensuring backward compatibility and smooth transition.

Usage:
    python scripts/migrate_config.py [--dry-run] [--backup]

Author: Vincent.Li
Email: <EMAIL>
Version: 1.0.0
"""

import argparse
import logging
import os
import shutil
import sys
from pathlib import Path
from typing import Dict, List, Tuple

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config import (
    get_config,
    get_legacy_config,
    get_pydantic_config,
    ConnectConfig,
    Settings
)

logger = logging.getLogger(__name__)


class ConfigMigrator:
    """Configuration migration utility.
    
    This class provides methods to migrate from legacy configuration systems
    to the new unified configuration manager.
    """
    
    def __init__(self, dry_run: bool = False, backup: bool = True):
        """Initialize the migrator.
        
        Args:
            dry_run: If True, only show what would be changed
            backup: If True, create backups of modified files
        """
        self.dry_run = dry_run
        self.backup = backup
        self.project_root = project_root
        self.changes: List[Tuple[str, str, str]] = []  # (file, old, new)
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def migrate(self) -> bool:
        """Run the complete migration process.
        
        Returns:
            bool: True if migration was successful
        """
        logger.info("Starting configuration migration...")
        
        try:
            # Step 1: Validate current configuration
            if not self._validate_current_config():
                logger.error("Current configuration validation failed")
                return False
            
            # Step 2: Test new unified configuration
            if not self._test_unified_config():
                logger.error("Unified configuration test failed")
                return False
            
            # Step 3: Find files that need migration
            files_to_migrate = self._find_files_to_migrate()
            logger.info(f"Found {len(files_to_migrate)} files to migrate")
            
            # Step 4: Create backups if requested
            if self.backup and not self.dry_run:
                self._create_backups(files_to_migrate)
            
            # Step 5: Migrate import statements
            for file_path in files_to_migrate:
                self._migrate_file(file_path)
            
            # Step 6: Update configuration usage patterns
            self._update_usage_patterns()
            
            # Step 7: Generate migration report
            self._generate_report()
            
            if self.dry_run:
                logger.info("Dry run completed. No files were modified.")
            else:
                logger.info("Migration completed successfully!")
            
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
    
    def _validate_current_config(self) -> bool:
        """Validate that current configuration systems work.
        
        Returns:
            bool: True if validation passes
        """
        logger.info("Validating current configuration systems...")
        
        try:
            # Test legacy Settings
            legacy_config = get_legacy_config()
            logger.info(f"Legacy config loaded: {type(legacy_config).__name__}")
            
            # Test Pydantic loader
            pydantic_config = get_pydantic_config()
            logger.info(f"Pydantic config loaded: {type(pydantic_config).__name__}")
            
            # Test unified config
            unified_config = get_config()
            logger.info(f"Unified config loaded: {type(unified_config).__name__}")
            
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def _test_unified_config(self) -> bool:
        """Test the new unified configuration system.
        
        Returns:
            bool: True if tests pass
        """
        logger.info("Testing unified configuration system...")
        
        try:
            config = get_config()
            
            # Test basic access
            assert hasattr(config, 'database'), "Database config missing"
            assert hasattr(config, 'logging'), "Logging config missing"
            assert hasattr(config, 'project'), "Project config missing"
            
            # Test database config
            db_config = config.database
            assert hasattr(db_config, 'host'), "Database host missing"
            assert hasattr(db_config, 'port'), "Database port missing"
            
            # Test reload functionality
            reloaded_config = get_config(reload=True)
            assert isinstance(reloaded_config, ConnectConfig), "Reload failed"
            
            logger.info("Unified configuration tests passed")
            return True
            
        except Exception as e:
            logger.error(f"Unified configuration test failed: {e}")
            return False
    
    def _find_files_to_migrate(self) -> List[Path]:
        """Find Python files that need migration.
        
        Returns:
            List[Path]: List of files to migrate
        """
        logger.info("Scanning for files to migrate...")
        
        files_to_migrate = []
        
        # Patterns to search for
        legacy_patterns = [
            'from src.config.settings import',
            'from src.config import Settings',
            'from src.config import ConfigManager',
            'from src.config import get_global_config_manager',
            'Settings()',
            'ConfigManager(',
            'get_global_config_manager(',
        ]
        
        # Search in source directories
        search_dirs = [
            self.project_root / 'src',
            self.project_root / 'tests',
            self.project_root / 'scripts',
        ]
        
        for search_dir in search_dirs:
            if not search_dir.exists():
                continue
                
            for py_file in search_dir.rglob('*.py'):
                if self._file_needs_migration(py_file, legacy_patterns):
                    files_to_migrate.append(py_file)
        
        return files_to_migrate
    
    def _file_needs_migration(self, file_path: Path, patterns: List[str]) -> bool:
        """Check if a file needs migration.
        
        Args:
            file_path: Path to the file
            patterns: List of patterns to search for
            
        Returns:
            bool: True if file needs migration
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            for pattern in patterns:
                if pattern in content:
                    return True
            
            return False
            
        except Exception as e:
            logger.warning(f"Could not read file {file_path}: {e}")
            return False
    
    def _create_backups(self, files: List[Path]) -> None:
        """Create backups of files to be modified.
        
        Args:
            files: List of files to backup
        """
        logger.info("Creating backups...")
        
        backup_dir = self.project_root / 'backups' / 'config_migration'
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        for file_path in files:
            relative_path = file_path.relative_to(self.project_root)
            backup_path = backup_dir / relative_path
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(file_path, backup_path)
            logger.debug(f"Backed up {file_path} to {backup_path}")
    
    def _migrate_file(self, file_path: Path) -> None:
        """Migrate a single file.
        
        Args:
            file_path: Path to the file to migrate
        """
        logger.info(f"Migrating {file_path}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply migration transformations
            content = self._apply_import_migrations(content)
            content = self._apply_usage_migrations(content)
            
            if content != original_content:
                self.changes.append((str(file_path), original_content, content))
                
                if not self.dry_run:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    logger.info(f"Migrated {file_path}")
                else:
                    logger.info(f"Would migrate {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to migrate {file_path}: {e}")
    
    def _apply_import_migrations(self, content: str) -> str:
        """Apply import statement migrations.
        
        Args:
            content: File content
            
        Returns:
            str: Migrated content
        """
        # Migration mappings
        import_migrations = {
            'from src.config.settings import Settings': 'from src.config import get_legacy_config',
            'from src.config.settings import ConfigManager': 'from src.config import get_config_manager',
            'from src.config.settings import get_global_config_manager': 'from src.config import get_config_manager',
            'from src.config import Settings': 'from src.config import get_legacy_config',
            'from src.config import ConfigManager': 'from src.config import get_config_manager',
            'from src.config import get_global_config_manager': 'from src.config import get_config_manager',
        }
        
        for old_import, new_import in import_migrations.items():
            content = content.replace(old_import, new_import)
        
        return content
    
    def _apply_usage_migrations(self, content: str) -> str:
        """Apply usage pattern migrations.
        
        Args:
            content: File content
            
        Returns:
            str: Migrated content
        """
        # Usage pattern migrations
        usage_migrations = {
            'Settings()': 'get_legacy_config()',
            'ConfigManager()': 'get_config_manager()',
            'get_global_config_manager()': 'get_config_manager()',
        }
        
        for old_usage, new_usage in usage_migrations.items():
            content = content.replace(old_usage, new_usage)
        
        return content
    
    def _update_usage_patterns(self) -> None:
        """Update common usage patterns to use unified config."""
        logger.info("Updating usage patterns...")
        
        # This would contain more sophisticated pattern matching
        # and replacement logic for complex usage scenarios
        pass
    
    def _generate_report(self) -> None:
        """Generate migration report."""
        report_path = self.project_root / 'migration_report.md'
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# Configuration Migration Report\n\n")
            f.write(f"Generated on: {os.popen('date').read().strip()}\n\n")
            f.write(f"Total files processed: {len(self.changes)}\n\n")
            
            if self.dry_run:
                f.write("**Note: This was a dry run. No files were actually modified.**\n\n")
            
            f.write("## Changes Made\n\n")
            
            for file_path, old_content, new_content in self.changes:
                f.write(f"### {file_path}\n\n")
                f.write("**Before:**\n```python\n")
                f.write(old_content[:500] + ("..." if len(old_content) > 500 else ""))
                f.write("\n```\n\n")
                f.write("**After:**\n```python\n")
                f.write(new_content[:500] + ("..." if len(new_content) > 500 else ""))
                f.write("\n```\n\n")
        
        logger.info(f"Migration report generated: {report_path}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Migrate Connect project configuration to unified system"
    )
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Show what would be changed without making changes'
    )
    parser.add_argument(
        '--backup',
        action='store_true',
        default=True,
        help='Create backups of modified files (default: True)'
    )
    parser.add_argument(
        '--no-backup',
        action='store_false',
        dest='backup',
        help='Do not create backups'
    )
    
    args = parser.parse_args()
    
    migrator = ConfigMigrator(dry_run=args.dry_run, backup=args.backup)
    success = migrator.migrate()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()