#!/usr/bin/env python3
"""
Advanced performance optimizer for Connect CLI
"""
import asyncio
import gc
import logging
import os
import psutil
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from concurrent.futures import Thr<PERSON><PERSON>ool<PERSON>xecutor, ProcessPoolExecutor
import pandas as pd

logger = logging.getLogger(__name__)


@dataclass
class SystemResources:
    """System resource information."""
    cpu_count: int
    cpu_percent: float
    memory_total_gb: float
    memory_available_gb: float
    memory_percent: float
    disk_free_gb: float


@dataclass
class FileMetrics:
    """File processing metrics."""
    file_path: str
    file_size_mb: float
    estimated_records: int
    complexity_score: float  # Based on columns, data types, etc.


@dataclass
class OptimizationStrategy:
    """Optimization strategy for file processing."""
    batch_size: int
    parallel_workers: int
    memory_limit_mb: int
    use_chunking: bool
    chunk_size: int
    use_streaming: bool
    executor_type: str  # 'thread' or 'process'


class PerformanceOptimizer:
    """Advanced performance optimizer for telecommunications data import."""
    
    def __init__(self):
        self.system_resources = self._get_system_resources()
        self.optimization_cache = {}
        
    def _get_system_resources(self) -> SystemResources:
        """Get current system resource information."""
        memory = psutil.virtual_memory()
        try:
            # Try to get disk usage, fallback to default values if it fails
            import os
            import platform
            if platform.system() == 'Windows':
                # Use C: drive as default on Windows
                disk = psutil.disk_usage('C:\\')
            else:
                disk = psutil.disk_usage('/')
        except (OSError, SystemError, Exception):
            # If disk usage fails, create a mock disk object with reasonable defaults
            from collections import namedtuple
            DiskUsage = namedtuple('DiskUsage', ['total', 'used', 'free'])
            # Default to 100GB free space if we can't determine actual disk usage
            disk = DiskUsage(total=500*1024**3, used=400*1024**3, free=100*1024**3)
        
        return SystemResources(
            cpu_count=psutil.cpu_count(),
            cpu_percent=psutil.cpu_percent(interval=1),
            memory_total_gb=memory.total / (1024**3),
            memory_available_gb=memory.available / (1024**3),
            memory_percent=memory.percent,
            disk_free_gb=float(disk.free) / (1024**3)
        )
    
    def analyze_file_complexity(self, file_path: Path) -> FileMetrics:
        """Analyze file complexity to determine optimal processing strategy."""
        file_size_mb = file_path.stat().st_size / (1024**2)
        
        # Estimate records based on file size and type
        if file_path.suffix.lower() in ['.csv', '.txt']:
            # Assume average 100 bytes per record for CSV
            estimated_records = int(file_size_mb * 1024 * 1024 / 100)
        elif file_path.suffix.lower() in ['.xlsx', '.xls']:
            # Excel files are more compressed, assume 50 bytes per record
            estimated_records = int(file_size_mb * 1024 * 1024 / 50)
        else:
            estimated_records = int(file_size_mb * 1000)  # Conservative estimate
        
        # Calculate complexity score (0-10)
        complexity_score = min(10, (file_size_mb / 100) + (estimated_records / 1000000))
        
        return FileMetrics(
            file_path=str(file_path),
            file_size_mb=file_size_mb,
            estimated_records=estimated_records,
            complexity_score=complexity_score
        )
    
    def optimize_for_file(self, file_metrics: FileMetrics) -> OptimizationStrategy:
        """Generate optimal processing strategy for a specific file."""
        cache_key = f"{file_metrics.file_size_mb}_{file_metrics.complexity_score}"
        
        if cache_key in self.optimization_cache:
            return self.optimization_cache[cache_key]
        
        resources = self._get_system_resources()
        
        # Base strategy
        strategy = OptimizationStrategy(
            batch_size=5000,
            parallel_workers=8,
            memory_limit_mb=512,
            use_chunking=False,
            chunk_size=10000,
            use_streaming=False,
            executor_type='thread'
        )
        
        # Adjust based on file size
        if file_metrics.file_size_mb < 10:  # Small files
            strategy.batch_size = 10000
            strategy.parallel_workers = min(8, resources.cpu_count)
            strategy.memory_limit_mb = 256
            strategy.executor_type = 'thread'
            
        elif file_metrics.file_size_mb < 100:  # Medium files
            strategy.batch_size = 5000
            strategy.parallel_workers = min(6, resources.cpu_count)
            strategy.memory_limit_mb = 512
            strategy.use_chunking = True
            strategy.chunk_size = 50000
            strategy.executor_type = 'thread'
            
        else:  # Large files (>100MB)
            strategy.batch_size = 2000
            strategy.parallel_workers = min(4, resources.cpu_count)
            strategy.memory_limit_mb = 1024
            strategy.use_chunking = True
            strategy.chunk_size = 25000
            strategy.use_streaming = True
            strategy.executor_type = 'process'
        
        # Adjust based on available memory
        if resources.memory_available_gb < 2:
            strategy.batch_size = max(1000, strategy.batch_size // 2)
            strategy.parallel_workers = 1
            strategy.memory_limit_mb = min(strategy.memory_limit_mb, 256)
            strategy.use_streaming = True
            
        elif resources.memory_available_gb > 8:
            strategy.batch_size = min(20000, strategy.batch_size * 2)
            strategy.parallel_workers = min(strategy.parallel_workers + 2, resources.cpu_count)
            strategy.memory_limit_mb = min(strategy.memory_limit_mb * 2, 2048)
        
        # Adjust based on CPU usage
        if resources.cpu_percent > 80:
            strategy.parallel_workers = max(1, strategy.parallel_workers - 1)
        
        self.optimization_cache[cache_key] = strategy
        return strategy
    
    def optimize_batch_processing(self, files: List[Path]) -> Dict[str, Any]:
        """Optimize batch processing for multiple files."""
        total_size_mb = sum(f.stat().st_size / (1024**2) for f in files)
        total_files = len(files)
        
        resources = self._get_system_resources()
        
        # Group files by size for optimal processing
        small_files = []  # <10MB
        medium_files = []  # 10-100MB
        large_files = []  # >100MB
        
        for file_path in files:
            size_mb = file_path.stat().st_size / (1024**2)
            if size_mb < 10:
                small_files.append(file_path)
            elif size_mb < 100:
                medium_files.append(file_path)
            else:
                large_files.append(file_path)
        
        # Determine optimal processing order and strategy
        # Improved memory calculation based on actual file processing requirements
        avg_file_size_mb = total_size_mb / len(files) if files else 0
        memory_per_file = avg_file_size_mb * 2  # More realistic: 2x file size in memory
        max_concurrent_files = min(4, len(files))  # Reasonable concurrent processing
        realistic_memory_requirement = memory_per_file * max_concurrent_files

        processing_plan = {
            'total_files': total_files,
            'total_size_mb': total_size_mb,
            'estimated_time_minutes': self._estimate_processing_time(total_size_mb),
            'memory_requirement_mb': realistic_memory_requirement,
            'recommended_order': [],
            'parallel_groups': []
        }
        
        # Process large files first (sequentially)
        if large_files:
            processing_plan['recommended_order'].append({
                    'type': 'large_files_sequential',
                    'files': large_files,
                    'parallel_workers': 4,
                    'batch_size': 2000
                })
        
        # Process medium files in small parallel groups
        if medium_files:
            # Group medium files into batches of 2-3
            for i in range(0, len(medium_files), 3):
                batch = medium_files[i:i+3]
                processing_plan['recommended_order'].append({
                    'type': 'medium_files_parallel',
                    'files': batch,
                    'parallel_workers': min(6, len(batch)),
                    'batch_size': 5000
                })
        
        # Process small files in larger parallel groups
        if small_files:
            # Group small files into batches of 4-6
            for i in range(0, len(small_files), 6):
                batch = small_files[i:i+6]
                processing_plan['recommended_order'].append({
                    'type': 'small_files_parallel',
                    'files': batch,
                    'parallel_workers': min(8, len(batch)),
                    'batch_size': 10000
                })
        
        return processing_plan
    
    def _estimate_processing_time(self, total_size_mb: float) -> float:
        """Estimate processing time in minutes."""
        # Base processing rate: ~50MB per minute (conservative estimate)
        base_rate_mb_per_minute = 50
        
        # Adjust based on system resources
        resources = self._get_system_resources()
        
        # CPU factor
        cpu_factor = min(2.0, resources.cpu_count / 4)
        
        # Memory factor
        memory_factor = min(2.0, resources.memory_available_gb / 4)
        
        # Combined factor
        performance_factor = (cpu_factor + memory_factor) / 2
        
        adjusted_rate = base_rate_mb_per_minute * performance_factor
        
        return max(1, total_size_mb / adjusted_rate)
    
    def monitor_performance_during_import(self) -> Dict[str, Any]:
        """Monitor system performance during import operations."""
        resources = self._get_system_resources()
        
        performance_status = {
            'timestamp': time.time(),
            'cpu_usage_percent': resources.cpu_percent,
            'memory_usage_percent': resources.memory_percent,
            'memory_available_gb': resources.memory_available_gb,
            'status': 'normal',
            'recommendations': []
        }
        
        # Determine status and recommendations
        if resources.cpu_percent > 90:
            performance_status['status'] = 'high_cpu'
            performance_status['recommendations'].append('Reduce parallel workers')
            
        if resources.memory_percent > 85:
            performance_status['status'] = 'high_memory'
            performance_status['recommendations'].append('Reduce batch size')
            performance_status['recommendations'].append('Enable streaming mode')
            
        if resources.memory_available_gb < 1:
            performance_status['status'] = 'critical_memory'
            performance_status['recommendations'].append('Stop non-essential processes')
            performance_status['recommendations'].append('Use minimal batch size')
            
        return performance_status
    
    def cleanup_memory(self):
        """Perform memory cleanup operations."""
        # Clear optimization cache
        self.optimization_cache.clear()
        
        # Force garbage collection
        gc.collect()
        
        logger.info("Memory cleanup completed")
    
    def get_optimal_chunk_reader_params(self, file_path: Path) -> Dict[str, Any]:
        """Get optimal parameters for chunk-based file reading."""
        file_metrics = self.analyze_file_complexity(file_path)
        strategy = self.optimize_for_file(file_metrics)
        
        params = {
            'chunksize': strategy.chunk_size if strategy.use_chunking else None,
            'low_memory': strategy.use_streaming,
            'engine': 'c' if file_path.suffix.lower() == '.csv' else 'auto'
        }
        
        # Excel-specific optimizations
        if file_path.suffix.lower() in ['.xlsx', '.xls']:
            params.update({
                'engine': 'openpyxl' if file_path.suffix.lower() == '.xlsx' else 'xlrd'
            })
        
        return params
