__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Data formatting module.

This module provides data formatting functionality for various export operations.
"""

import logging
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List, Optional, Union

import pandas as pd

# Configure logging
logger = logging.getLogger(__name__)


class DataFormatter:
    """Data formatter for preparing data for export."""

    def __init__(self, **kwargs):
        """Initialize data formatter.

        Args:
            **kwargs: Configuration options for formatting
        """
        self.config = kwargs
        self.date_format = kwargs.get("date_format", "%Y-%m-%d")
        self.datetime_format = kwargs.get("datetime_format", "%Y-%m-%d %H:%M:%S")
        self.decimal_places = kwargs.get("decimal_places", 2)
        self.null_value = kwargs.get("null_value", "")

        logger.info(f"Initialized DataFormatter with config: {self.config}")

    def format_data(self, data: Any) -> Any:
        """Format data for export.

        Args:
            data: Data to format

        Returns:
            Any: Formatted data
        """
        if isinstance(data, pd.DataFrame):
            return self._format_dataframe(data)
        elif isinstance(data, list):
            return [self._format_value(item) for item in data]
        elif isinstance(data, dict):
            return {key: self._format_value(value) for key, value in data.items()}
        else:
            return self._format_value(data)

    def _format_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Format pandas DataFrame."""
        formatted_df = df.copy()

        for column in formatted_df.columns:
            formatted_df[column] = formatted_df[column].apply(self._format_value)

        return formatted_df

    def _format_value(self, value: Any) -> Any:
        """Format individual value."""
        if pd.isna(value) or value is None:
            return self.null_value
        elif isinstance(value, datetime):
            return value.strftime(self.datetime_format)
        elif isinstance(value, pd.Timestamp):
            return value.strftime(self.datetime_format)
        elif isinstance(value, (float, Decimal)):
            if pd.isna(value):
                return self.null_value
            return round(float(value), self.decimal_places)
        elif isinstance(value, str):
            return value.strip()
        else:
            return value

    def format_currency(
        self, value: Union[float, int, Decimal], currency_symbol: str = "$"
    ) -> str:
        """Format value as currency.

        Args:
            value: Numeric value to format
            currency_symbol: Currency symbol to use

        Returns:
            str: Formatted currency string
        """
        if pd.isna(value) or value is None:
            return self.null_value

        formatted_value = f"{currency_symbol}{value:,.{self.decimal_places}f}"
        return formatted_value

    def format_percentage(
        self, value: Union[float, int], multiply_by_100: bool = True
    ) -> str:
        """Format value as percentage.

        Args:
            value: Numeric value to format
            multiply_by_100: Whether to multiply by 100

        Returns:
            str: Formatted percentage string
        """
        if pd.isna(value) or value is None:
            return self.null_value

        if multiply_by_100:
            value = value * 100

        return f"{value:.{self.decimal_places}f}%"

    def format_phone_number(
        self, value: str, format_pattern: str = "(###) ###-####"
    ) -> str:
        """Format phone number.

        Args:
            value: Phone number string
            format_pattern: Format pattern to use

        Returns:
            str: Formatted phone number
        """
        if not value or pd.isna(value):
            return self.null_value

        # Remove all non-digit characters
        digits = "".join(filter(str.isdigit, str(value)))

        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == "1":
            return f"+1 ({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        else:
            return str(value)  # Return original if can't format

    def truncate_text(
        self, value: str, max_length: int = 50, suffix: str = "..."
    ) -> str:
        """Truncate text to specified length.

        Args:
            value: Text to truncate
            max_length: Maximum length
            suffix: Suffix to add if truncated

        Returns:
            str: Truncated text
        """
        if not value or pd.isna(value):
            return self.null_value

        text = str(value)
        if len(text) <= max_length:
            return text

        return text[: max_length - len(suffix)] + suffix
