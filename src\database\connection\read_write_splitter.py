__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Read-Write Splitting Management for Database Connections.

This module provides intelligent routing of database queries to either the primary (write)
instance or appropriate read replica instances, incorporating load balancing and failover strategies.
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union

import asyncpg
from asyncpg import Connection

# Handle relative imports with fallback
try:
    from ...config import get_config
    from ...config.models import ConnectConfig, DatabaseConfig
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    # Add parent directories to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from config import get_config
    from config.models import ConnectConfig, DatabaseConfig
from ..exceptions import (
    ConnectionError,
    DatabaseError,
    NoAvailableReplicasError,
    PrimaryDatabaseUnavailableError,
)
from .health_check import HealthChecker, HealthStatus
from .pool import DatabasePoolManager

# Configure logging
logger = logging.getLogger(__name__)


class LoadBalancingStrategy(Enum):
    """Load balancing strategies for read replicas."""

    ROUND_ROBIN = "round_robin"
    RANDOM = "random"
    LEAST_CONNECTIONS = "least_connections"


class ReadWriteSplitter:
    """Read-Write Splitting Manager for Database Connections.

    This class intelligently routes database queries to either the primary (write)
    instance or appropriate read replica instances, incorporating load balancing
    and failover strategies.
    """

    def __init__(
        self,
        primary_config: DatabaseConfig,
        replica_configs: List[DatabaseConfig],
        pool_config: Optional[Dict[str, Any]] = None,
        load_balancing_strategy: LoadBalancingStrategy = LoadBalancingStrategy.ROUND_ROBIN,
        fallback_to_primary: bool = False,
        health_check_interval: int = 30,
    ):
        """Initialize the Read-Write Splitter.

        Args:
            primary_config: Configuration for the primary (write) database
            replica_configs: List of configurations for read replica databases
            pool_config: Optional pool configuration parameters
            load_balancing_strategy: Strategy for load balancing across replicas
            fallback_to_primary: Whether to fallback to primary when all replicas are down
            health_check_interval: Interval in seconds for health checks
        """
        self.primary_config = primary_config
        self.replica_configs = replica_configs
        self.load_balancing_strategy = load_balancing_strategy
        self.fallback_to_primary = fallback_to_primary
        self.health_check_interval = health_check_interval

        # Initialize connection pools
        self.primary_pool: Optional[DatabasePoolManager] = None
        self.replica_pools: List[DatabasePoolManager] = []

        # Initialize health checkers
        self.primary_health_checker: Optional[HealthChecker] = None
        self.replica_health_checkers: List[HealthChecker] = []

        # Load balancing state
        self._current_replica_index = 0
        self._replica_connection_counts: Dict[int, int] = {}

        # Initialization flag
        self._is_initialized = False

        logger.info(
            f"ReadWriteSplitter initialized with {len(replica_configs)} replicas, "
            f"strategy: {load_balancing_strategy.value}, "
            f"fallback_to_primary: {fallback_to_primary}"
        )

    async def initialize(self) -> None:
        """Initialize connection pools and health checkers.

        Raises:
            ConnectionError: If initialization fails
        """
        if self._is_initialized:
            logger.warning("ReadWriteSplitter is already initialized")
            return

        try:
            # Initialize primary pool
            logger.info("Initializing primary database pool")
            primary_config = get_config()
            primary_config.database = self.primary_config
            self.primary_pool = DatabasePoolManager(primary_config)
            await self.primary_pool.initialize_pool()

            # Initialize primary health checker
            self.primary_health_checker = HealthChecker(
                pool=self.primary_pool._pool, check_interval=self.health_check_interval
            )
            await self.primary_health_checker.start_monitoring()

            # Initialize replica pools
            logger.info(f"Initializing {len(self.replica_configs)} replica pools")
            for i, replica_config in enumerate(self.replica_configs):
                config = get_config()
                config.database = replica_config
                pool_manager = DatabasePoolManager(config)
                await pool_manager.initialize_pool()
                self.replica_pools.append(pool_manager)

                # Initialize replica health checker
                health_checker = HealthChecker(
                    pool=pool_manager._pool, check_interval=self.health_check_interval
                )
                await health_checker.start_monitoring()
                self.replica_health_checkers.append(health_checker)

                # Initialize connection count tracking
                self._replica_connection_counts[i] = 0

                logger.info(f"Replica {i} pool initialized successfully")

            self._is_initialized = True
            logger.info("ReadWriteSplitter initialization completed successfully")

        except Exception as e:
            error_msg = f"Failed to initialize ReadWriteSplitter: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ConnectionError(
                error_msg,
                error_code="READ_WRITE_SPLITTER_INIT_FAILED",
                original_exception=e,
            )

    async def close(self) -> None:
        """Close all connection pools and stop health checkers."""
        if not self._is_initialized:
            logger.debug("ReadWriteSplitter is not initialized")
            return

        try:
            logger.info("Closing ReadWriteSplitter")

            # Stop health checkers
            if self.primary_health_checker:
                await self.primary_health_checker.stop_monitoring()

            for health_checker in self.replica_health_checkers:
                await health_checker.stop_monitoring()

            # Close pools
            if self.primary_pool:
                await self.primary_pool.close_pool()

            for pool in self.replica_pools:
                await pool.close_pool()

            self._is_initialized = False
            logger.info("ReadWriteSplitter closed successfully")

        except Exception as e:
            logger.error(f"Error closing ReadWriteSplitter: {str(e)}", exc_info=True)

    async def acquire_connection(
        self, read_only: bool = False
    ) -> Tuple[Connection, DatabasePoolManager]:
        """Acquire a connection from the appropriate pool.

        Args:
            read_only: If True, acquire from read replica; if False, from primary

        Returns:
            Tuple of (connection, pool_manager)

        Raises:
            RuntimeError: If splitter is not initialized
            NoAvailableReplicasError: If no read replicas are available
            PrimaryDatabaseUnavailableError: If primary database is unavailable
            ConnectionError: If connection acquisition fails
        """
        if not self._is_initialized:
            raise RuntimeError(
                "ReadWriteSplitter is not initialized. Call initialize() first."
            )

        if read_only:
            return await self._acquire_read_connection()
        else:
            return await self._acquire_write_connection()

    @asynccontextmanager
    async def get_connection(self, read_only: bool = False):
        """Context manager for acquiring and releasing connections.

        Args:
            read_only: If True, acquire from read replica; if False, from primary

        Yields:
            Database connection
        """
        connection, pool_manager = await self.acquire_connection(read_only)
        try:
            yield connection
        finally:
            await pool_manager.release_connection(connection)

    async def _acquire_read_connection(self) -> Tuple[Connection, DatabasePoolManager]:
        """Acquire a connection from a read replica.

        Returns:
            Tuple of (connection, pool_manager)

        Raises:
            NoAvailableReplicasError: If no read replicas are available
            ConnectionError: If connection acquisition fails
        """
        if not self.replica_pools:
            if self.fallback_to_primary:
                logger.warning(
                    "No replicas configured, falling back to primary for read operation"
                )
                return await self._acquire_write_connection()
            else:
                raise NoAvailableReplicasError("No read replicas configured")

        # Get healthy replicas
        healthy_replicas = await self._get_healthy_replicas()

        if not healthy_replicas:
            if self.fallback_to_primary:
                logger.warning(
                    "No healthy replicas available, falling back to primary for read operation"
                )
                return await self._acquire_write_connection()
            else:
                raise NoAvailableReplicasError("No healthy read replicas available")

        # Select replica based on load balancing strategy
        selected_replica_index = self._select_replica(healthy_replicas)
        selected_pool = self.replica_pools[selected_replica_index]

        try:
            logger.debug(
                f"Acquiring read connection from replica {selected_replica_index}"
            )
            connection = await selected_pool.acquire_connection()
            self._replica_connection_counts[selected_replica_index] += 1

            logger.debug(
                f"Read connection acquired from replica {selected_replica_index} "
                f"(active connections: {self._replica_connection_counts[selected_replica_index]})"
            )

            return connection, selected_pool

        except Exception as e:
            logger.error(
                f"Failed to acquire connection from replica {selected_replica_index}: {str(e)}"
            )

            # Try failover to next healthy replica
            return await self._failover_read_connection(
                healthy_replicas, selected_replica_index
            )

    async def _acquire_write_connection(self) -> Tuple[Connection, DatabasePoolManager]:
        """Acquire a connection from the primary database.

        Returns:
            Tuple of (connection, pool_manager)

        Raises:
            PrimaryDatabaseUnavailableError: If primary database is unavailable
            ConnectionError: If connection acquisition fails
        """
        if not self.primary_pool:
            raise PrimaryDatabaseUnavailableError("Primary pool is not initialized")

        # Check primary health
        if self.primary_health_checker:
            health_result = await self.primary_health_checker.check_health()
            if health_result.status != HealthStatus.HEALTHY:
                logger.critical(
                    f"Primary database is unhealthy: {health_result.error_message}"
                )
                raise PrimaryDatabaseUnavailableError(
                    f"Primary database is unhealthy: {health_result.error_message}"
                )

        try:
            logger.debug("Acquiring write connection from primary")
            connection = await self.primary_pool.acquire_connection()

            logger.debug("Write connection acquired from primary")
            return connection, self.primary_pool

        except Exception as e:
            error_msg = f"Failed to acquire connection from primary database: {str(e)}"
            logger.critical(error_msg)
            raise PrimaryDatabaseUnavailableError(error_msg)

    async def _get_healthy_replicas(self) -> List[int]:
        """Get list of healthy replica indices.

        Returns:
            List of healthy replica indices
        """
        healthy_replicas = []

        for i, health_checker in enumerate(self.replica_health_checkers):
            try:
                health_result = await health_checker.check_health()
                if health_result.status == HealthStatus.HEALTHY:
                    healthy_replicas.append(i)
                else:
                    logger.debug(
                        f"Replica {i} is unhealthy: {health_result.error_message}"
                    )
            except Exception as e:
                logger.warning(f"Failed to check health of replica {i}: {str(e)}")

        logger.debug(
            f"Found {len(healthy_replicas)} healthy replicas: {healthy_replicas}"
        )
        return healthy_replicas

    def _select_replica(self, healthy_replicas: List[int]) -> int:
        """Select a replica based on the load balancing strategy.

        Args:
            healthy_replicas: List of healthy replica indices

        Returns:
            Selected replica index
        """
        if not healthy_replicas:
            raise NoAvailableReplicasError("No healthy replicas to select from")

        if self.load_balancing_strategy == LoadBalancingStrategy.ROUND_ROBIN:
            # Round-robin selection
            selected = healthy_replicas[
                self._current_replica_index % len(healthy_replicas)
            ]
            self._current_replica_index += 1
            return selected

        elif self.load_balancing_strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            # Select replica with least active connections
            min_connections = float("inf")
            selected = healthy_replicas[0]

            for replica_index in healthy_replicas:
                connections = self._replica_connection_counts.get(replica_index, 0)
                if connections < min_connections:
                    min_connections = connections
                    selected = replica_index

            return selected

        elif self.load_balancing_strategy == LoadBalancingStrategy.RANDOM:
            # Random selection using cryptographically secure random
            import secrets

            return secrets.choice(healthy_replicas)

        else:
            # Default to round-robin
            logger.warning(
                f"Unknown load balancing strategy: {self.load_balancing_strategy}"
            )
            return healthy_replicas[self._current_replica_index % len(healthy_replicas)]

    async def _failover_read_connection(
        self, healthy_replicas: List[int], failed_replica: int
    ) -> Tuple[Connection, DatabasePoolManager]:
        """Attempt failover to next available replica.

        Args:
            healthy_replicas: List of healthy replica indices
            failed_replica: Index of the failed replica

        Returns:
            Tuple of (connection, pool_manager)

        Raises:
            NoAvailableReplicasError: If no replicas are available for failover
        """
        logger.warning(f"Attempting failover from replica {failed_replica}")

        # Remove failed replica from healthy list
        remaining_replicas = [r for r in healthy_replicas if r != failed_replica]

        if not remaining_replicas:
            if self.fallback_to_primary:
                logger.warning(
                    "No healthy replicas available for failover, falling back to primary"
                )
                return await self._acquire_write_connection()
            else:
                raise NoAvailableReplicasError(
                    "No healthy replicas available for failover"
                )

        # Try next replica
        for replica_index in remaining_replicas:
            try:
                logger.info(f"Failover: trying replica {replica_index}")
                pool = self.replica_pools[replica_index]
                connection = await pool.acquire_connection()
                self._replica_connection_counts[replica_index] += 1

                logger.info(
                    f"Failover successful: connected to replica {replica_index}"
                )
                return connection, pool

            except Exception as e:
                logger.error(f"Failover failed for replica {replica_index}: {str(e)}")
                continue

        # All failover attempts failed
        if self.fallback_to_primary:
            logger.warning(
                "All replica failover attempts failed, falling back to primary"
            )
            return await self._acquire_write_connection()
        else:
            raise NoAvailableReplicasError("All replica failover attempts failed")

    async def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all database instances.

        Returns:
            Dictionary containing health status information
        """
        status = {
            "primary": None,
            "replicas": [],
            "summary": {
                "total_replicas": len(self.replica_pools),
                "healthy_replicas": 0,
                "primary_healthy": False,
            },
        }

        # Check primary health
        if self.primary_health_checker:
            try:
                primary_health = await self.primary_health_checker.check_health()
                status["primary"] = primary_health.to_dict()
                status["summary"]["primary_healthy"] = (
                    primary_health.status == HealthStatus.HEALTHY
                )
            except Exception as e:
                logger.error(f"Failed to check primary health: {str(e)}")
                status["primary"] = {"status": "error", "error": str(e)}

        # Check replica health
        for i, health_checker in enumerate(self.replica_health_checkers):
            try:
                replica_health = await health_checker.check_health()
                replica_status = replica_health.to_dict()
                replica_status["replica_index"] = i
                replica_status[
                    "active_connections"
                ] = self._replica_connection_counts.get(i, 0)
                status["replicas"].append(replica_status)

                if replica_health.status == HealthStatus.HEALTHY:
                    status["summary"]["healthy_replicas"] += 1

            except Exception as e:
                logger.error(f"Failed to check replica {i} health: {str(e)}")
                status["replicas"].append(
                    {
                        "replica_index": i,
                        "status": "error",
                        "error": str(e),
                        "active_connections": self._replica_connection_counts.get(i, 0),
                    }
                )

        return status

    def get_stats(self) -> Dict[str, Any]:
        """Get connection statistics.

        Returns:
            Dictionary containing connection statistics
        """
        return {
            "load_balancing_strategy": self.load_balancing_strategy.value,
            "fallback_to_primary": self.fallback_to_primary,
            "replica_connection_counts": self._replica_connection_counts.copy(),
            "current_replica_index": self._current_replica_index,
            "total_replicas": len(self.replica_pools),
            "is_initialized": self._is_initialized,
        }
