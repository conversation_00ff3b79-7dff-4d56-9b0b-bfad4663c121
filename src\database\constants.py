__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Constants for the database framework.

This module defines all constants used throughout the database framework,
including default values, error codes, and configuration constants.
"""

from enum import Enum
from typing import Final

# Default schema names
DEFAULT_SCHEMA: Final[str] = "public"
EP_SCHEMA: Final[str] = "ep_to2"
CDR_SCHEMA_TO2: Final[str] = "cdr_to2"
CDR_SCHEMA_VDF: Final[str] = "cdr_vdf"
CDR_SCHEMA_TDG: Final[str] = "cdr_tdg"
NLG_SCHEMA: Final[str] = "nlg_to2"
KPI_SCHEMA: Final[str] = "kpi_to2"
SCORE_SCHEMA: Final[str] = "score_to2"
CFG_SCHEMA: Final[str] = "cfg_to2"

# Database connection defaults
DEFAULT_HOST: Final[str] = "localhost"
DEFAULT_PORT: Final[int] = 5432
DEFAULT_POOL_SIZE: Final[int] = 10
DEFAULT_MAX_OVERFLOW: Final[int] = 20
DEFAULT_TIMEOUT: Final[int] = 30
DEFAULT_RECYCLE: Final[int] = 3600

# File processing constants
SUPPORTED_EXCEL_EXTENSIONS: Final[tuple[str, ...]] = (".xlsx", ".xls", ".xlsb")
SUPPORTED_CSV_EXTENSIONS: Final[tuple[str, ...]] = (".csv", ".tsv")
DEFAULT_ENCODING: Final[str] = "utf-8"
MAX_FILE_SIZE_MB: Final[int] = 100
MAX_ROWS_PER_BATCH: Final[int] = 10000

# Table naming patterns
EP_TABLE_PATTERN: Final[str] = "ep_{cell_type}_{year}_cw{week}"
CDR_TABLE_PATTERN: Final[str] = "cdr_{year}Q{quarter}_{service_type}"
NLG_TABLE_PATTERN: Final[str] = "nlg_cube_aktuell_{date}"
KPI_TABLE_PATTERN: Final[str] = "kpi_{metric_type}_{period}"
SCORE_TABLE_PATTERN: Final[str] = "score_{algorithm}_{date}"


# Error codes
class ErrorCode(str, Enum):
    """Enumeration of error codes used throughout the framework."""

    # Connection errors
    CONNECTION_FAILED = "DB_001"
    CONNECTION_TIMEOUT = "DB_002"
    CONNECTION_LOST = "DB_003"
    POOL_EXHAUSTED = "DB_004"

    # Authentication and authorization errors
    AUTH_FAILED = "DB_101"
    INVALID_CREDENTIALS = "DB_102"
    PERMISSION_DENIED = "DB_103"
    ACCESS_FORBIDDEN = "DB_104"

    # Query errors
    QUERY_SYNTAX_ERROR = "DB_201"
    QUERY_EXECUTION_ERROR = "DB_202"
    QUERY_TIMEOUT = "DB_203"
    INVALID_PARAMETERS = "DB_204"

    # Transaction errors
    TRANSACTION_FAILED = "DB_301"
    DEADLOCK_DETECTED = "DB_302"
    ROLLBACK_FAILED = "DB_303"
    COMMIT_FAILED = "DB_304"

    # Schema errors
    SCHEMA_NOT_FOUND = "DB_401"
    TABLE_NOT_FOUND = "DB_402"
    COLUMN_NOT_FOUND = "DB_403"
    CONSTRAINT_VIOLATION = "DB_404"
    DUPLICATE_KEY = "DB_405"

    # Validation errors
    INVALID_DATA_TYPE = "DB_501"
    VALUE_OUT_OF_RANGE = "DB_502"
    REQUIRED_FIELD_MISSING = "DB_503"
    INVALID_FORMAT = "DB_504"

    # File processing errors
    FILE_NOT_FOUND = "DB_601"
    FILE_FORMAT_ERROR = "DB_602"
    FILE_SIZE_EXCEEDED = "DB_603"
    ENCODING_ERROR = "DB_604"

    # Configuration errors
    CONFIG_NOT_FOUND = "DB_701"
    INVALID_CONFIG = "DB_702"
    MISSING_REQUIRED_CONFIG = "DB_703"

    # General errors
    UNKNOWN_ERROR = "DB_999"


# Data source types
class DataSourceType(str, Enum):
    """Enumeration of supported data source types."""

    EP = "ep"
    CDR = "cdr"
    NLG = "nlg"
    KPI = "kpi"
    SCORE = "score"
    CFG = "cfg"


# Cell types for EP data
class CellType(str, Enum):
    """Enumeration of cell types for EP data."""

    GSM = "gsm"
    UMTS = "umts"
    LTE = "lte"
    NR = "nr"


# Service types for CDR data
class ServiceType(str, Enum):
    """Enumeration of service types for CDR data."""

    VOICE = "voice"
    SMS = "sms"
    DATA = "data"
    M2M = "m2m"


# Operators
class Operator(str, Enum):
    """Enumeration of mobile operators."""

    TELEFONICA = "telefonica"
    VODAFONE = "vodafone"
    TELEKOM = "telekom"


# Priority levels
class Priority(str, Enum):
    """Enumeration of priority levels."""

    P0 = "P0"  # Critical - Core functionality
    P1 = "P1"  # High - Important features
    P2 = "P2"  # Medium - Nice to have
    P3 = "P3"  # Low - Future enhancements


# Logging levels
class LogLevel(str, Enum):
    """Enumeration of logging levels."""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


# SQL operation types
class SQLOperation(str, Enum):
    """Enumeration of SQL operation types."""

    SELECT = "SELECT"
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    CREATE = "CREATE"
    DROP = "DROP"
    ALTER = "ALTER"
    TRUNCATE = "TRUNCATE"


# Transaction isolation levels
class IsolationLevel(str, Enum):
    """Enumeration of transaction isolation levels."""

    READ_UNCOMMITTED = "READ_UNCOMMITTED"
    READ_COMMITTED = "READ_COMMITTED"
    REPEATABLE_READ = "REPEATABLE_READ"
    SERIALIZABLE = "SERIALIZABLE"


# Connection pool states
class PoolState(str, Enum):
    """Enumeration of connection pool states."""

    INITIALIZING = "INITIALIZING"
    ACTIVE = "ACTIVE"
    DRAINING = "DRAINING"
    CLOSED = "CLOSED"
    ERROR = "ERROR"


# Validation constraints
MAX_IDENTIFIER_LENGTH: Final[int] = 63  # PostgreSQL identifier limit
MAX_VARCHAR_LENGTH: Final[int] = 255
MAX_TEXT_LENGTH: Final[int] = 65535
MIN_POOL_SIZE: Final[int] = 1
MAX_POOL_SIZE: Final[int] = 100
MIN_TIMEOUT: Final[int] = 1
MAX_TIMEOUT: Final[int] = 3600

# Regular expressions for validation
TABLE_NAME_PATTERN: Final[str] = r"^[a-z][a-z0-9_]*$"
COLUMN_NAME_PATTERN: Final[str] = r"^[a-z][a-z0-9_]*$"
SCHEMA_NAME_PATTERN: Final[str] = r"^[a-z][a-z0-9_]*$"
INDEX_NAME_PATTERN: Final[str] = r"^idx_[a-z][a-z0-9_]*$"

# Date and time formats
DATE_FORMAT: Final[str] = "%Y-%m-%d"
DATETIME_FORMAT: Final[str] = "%Y-%m-%d %H:%M:%S"
TIMESTAMP_FORMAT: Final[str] = "%Y-%m-%d %H:%M:%S.%f"
FILE_DATE_FORMAT: Final[str] = "%Y%m%d"

# Batch processing settings
DEFAULT_BATCH_SIZE: Final[int] = 1000
MAX_BATCH_SIZE: Final[int] = 10000
MIN_BATCH_SIZE: Final[int] = 100

# Cache settings
DEFAULT_CACHE_TTL: Final[int] = 300  # 5 minutes
MAX_CACHE_SIZE: Final[int] = 1000

# Retry settings
DEFAULT_MAX_RETRIES: Final[int] = 3
DEFAULT_RETRY_DELAY: Final[float] = 1.0
MAX_RETRY_DELAY: Final[float] = 60.0
