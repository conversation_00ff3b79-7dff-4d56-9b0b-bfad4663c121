"""Query optimization utilities for database operations.

This module provides query optimization functionality to improve
performance of database operations.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple
from sqlalchemy import text

logger = logging.getLogger(__name__)


class QueryOptimizer:
    """Query optimizer for database operations.
    
    Provides basic query optimization and analysis functionality.
    """
    
    def __init__(self):
        """Initialize the query optimizer."""
        self.optimization_enabled = True
        self.cache_enabled = True
        self._query_cache: Dict[str, str] = {}
    
    def optimize_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> str:
        """Optimize a SQL query.
        
        Args:
            query: The SQL query to optimize
            params: Optional query parameters
            
        Returns:
            The optimized query string
        """
        if not self.optimization_enabled:
            return query
            
        # Check cache first
        cache_key = self._get_cache_key(query, params)
        if self.cache_enabled and cache_key in self._query_cache:
            return self._query_cache[cache_key]
        
        # Basic optimization - remove extra whitespace and normalize
        optimized = self._normalize_query(query)
        
        # Cache the result
        if self.cache_enabled:
            self._query_cache[cache_key] = optimized
            
        return optimized
    
    def analyze_query(self, query: str) -> Dict[str, Any]:
        """Analyze a query for potential optimizations.
        
        Args:
            query: The SQL query to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        analysis = {
            'query_type': self._detect_query_type(query),
            'has_joins': 'JOIN' in query.upper(),
            'has_subqueries': '(' in query and 'SELECT' in query.upper(),
            'has_aggregations': any(func in query.upper() for func in ['COUNT', 'SUM', 'AVG', 'MAX', 'MIN']),
            'estimated_complexity': self._estimate_complexity(query)
        }
        
        return analysis
    
    def suggest_indexes(self, query: str, table_name: str) -> List[str]:
        """Suggest indexes for query optimization.
        
        Args:
            query: The SQL query to analyze
            table_name: The primary table name
            
        Returns:
            List of suggested index creation statements
        """
        suggestions = []
        
        # Basic index suggestions based on WHERE clauses
        if 'WHERE' in query.upper():
            # This is a simplified implementation
            # In practice, you'd want more sophisticated analysis
            suggestions.append(f"-- Consider adding indexes on frequently queried columns in {table_name}")
        
        return suggestions
    
    def _normalize_query(self, query: str) -> str:
        """Normalize a query by removing extra whitespace.
        
        Args:
            query: The query to normalize
            
        Returns:
            The normalized query
        """
        # Remove extra whitespace
        normalized = ' '.join(query.split())
        
        # Remove trailing semicolon if present
        normalized = normalized.rstrip(';')
        
        return normalized
    
    def _detect_query_type(self, query: str) -> str:
        """Detect the type of SQL query.
        
        Args:
            query: The query to analyze
            
        Returns:
            The detected query type
        """
        query_upper = query.upper().strip()
        
        if query_upper.startswith('SELECT'):
            return 'SELECT'
        elif query_upper.startswith('INSERT'):
            return 'INSERT'
        elif query_upper.startswith('UPDATE'):
            return 'UPDATE'
        elif query_upper.startswith('DELETE'):
            return 'DELETE'
        elif query_upper.startswith('CREATE'):
            return 'CREATE'
        elif query_upper.startswith('DROP'):
            return 'DROP'
        else:
            return 'UNKNOWN'
    
    def _estimate_complexity(self, query: str) -> str:
        """Estimate query complexity.
        
        Args:
            query: The query to analyze
            
        Returns:
            Complexity level (LOW, MEDIUM, HIGH)
        """
        query_upper = query.upper()
        complexity_score = 0
        
        # Add points for various complexity factors
        if 'JOIN' in query_upper:
            complexity_score += 2
        if 'SUBQUERY' in query_upper or query_upper.count('SELECT') > 1:
            complexity_score += 3
        if any(func in query_upper for func in ['GROUP BY', 'ORDER BY', 'HAVING']):
            complexity_score += 1
        if any(func in query_upper for func in ['UNION', 'INTERSECT', 'EXCEPT']):
            complexity_score += 2
        
        if complexity_score <= 2:
            return 'LOW'
        elif complexity_score <= 5:
            return 'MEDIUM'
        else:
            return 'HIGH'
    
    def _get_cache_key(self, query: str, params: Optional[Dict[str, Any]] = None) -> str:
        """Generate a cache key for a query.
        
        Args:
            query: The query string
            params: Optional parameters
            
        Returns:
            A cache key string
        """
        key = query.strip()
        if params:
            # Sort params for consistent cache keys
            param_str = str(sorted(params.items()))
            key += f"__{param_str}"
        return key
    
    def clear_cache(self) -> None:
        """Clear the query cache."""
        self._query_cache.clear()
        logger.info("Query cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        return {
            'cache_size': len(self._query_cache),
            'cache_enabled': self.cache_enabled,
            'optimization_enabled': self.optimization_enabled
        }