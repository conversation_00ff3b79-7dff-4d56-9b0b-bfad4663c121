"""KPI Calculator for Telecommunications Data

This module provides comprehensive KPI calculation capabilities for telecommunications
networks including call success rates, signal quality metrics, handover performance,
and network availability indicators.
"""

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

import asyncio
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class KPIType(str, Enum):
    """KPI type enumeration"""
    CALL_SUCCESS_RATE = "call_success_rate"
    SIGNAL_STRENGTH = "signal_strength"
    HANDOVER_SUCCESS_RATE = "handover_success_rate"
    NETWORK_AVAILABILITY = "network_availability"
    THROUGHPUT = "throughput"
    LATENCY = "latency"
    COVERAGE_RATIO = "coverage_ratio"
    DROP_CALL_RATE = "drop_call_rate"


class AggregationLevel(str, Enum):
    """Aggregation level enumeration"""
    CELL = "cell"
    SITE = "site"
    REGION = "region"
    NETWORK = "network"


@dataclass
class KPIResult:
    """KPI calculation result"""
    kpi_type: KPIType
    value: float
    unit: str
    aggregation_level: AggregationLevel
    entity_id: str
    calculation_time: datetime
    data_points: int
    metadata: Dict[str, Any]


class TelecomKPICalculator:
    """Telecommunications KPI calculator with real-time capabilities"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize KPI calculator
        
        Args:
            config: Configuration dictionary for KPI calculations
        """
        self.config = config or {}
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Default thresholds for telecommunications KPIs
        self.thresholds = {
            KPIType.CALL_SUCCESS_RATE: 95.0,  # 95% minimum
            KPIType.SIGNAL_STRENGTH: -110.0,  # -110 dBm minimum RSRP
            KPIType.HANDOVER_SUCCESS_RATE: 98.0,  # 98% minimum
            KPIType.NETWORK_AVAILABILITY: 99.9,  # 99.9% minimum
            KPIType.DROP_CALL_RATE: 2.0,  # 2% maximum
            KPIType.COVERAGE_RATIO: 95.0,  # 95% minimum coverage
        }
        
        # Update thresholds from config
        if 'kpi_thresholds' in self.config:
            self.thresholds.update(self.config['kpi_thresholds'])
    
    async def calculate_call_success_rate(self,
                                        data: pd.DataFrame,
                                        aggregation_level: AggregationLevel = AggregationLevel.CELL,
                                        time_window: Optional[timedelta] = None) -> List[KPIResult]:
        """Calculate call success rate KPI
        
        Args:
            data: DataFrame with call data
            aggregation_level: Level of aggregation for KPI calculation
            time_window: Time window for calculation (None for all data)
            
        Returns:
            List of KPI results
        """
        try:
            if data.empty:
                return []
            
            # Filter by time window if specified
            if time_window and 'call_start_time' in data.columns:
                cutoff_time = datetime.now() - time_window
                data = data[pd.to_datetime(data['call_start_time']) >= cutoff_time]
            
            # Determine grouping column based on aggregation level
            group_col = self._get_grouping_column(aggregation_level, data)
            if not group_col:
                return []
            
            # Calculate success rate by group
            results = []
            grouped = data.groupby(group_col)
            
            for entity_id, group_data in grouped:
                total_calls = len(group_data)
                
                # Count successful calls (assuming 'call_status' column exists)
                if 'call_status' in group_data.columns:
                    successful_calls = len(group_data[group_data['call_status'] == 'completed'])
                elif 'call_duration' in group_data.columns:
                    # Alternative: consider calls with duration > 0 as successful
                    successful_calls = len(group_data[group_data['call_duration'] > 0])
                else:
                    # Fallback: assume all calls are successful
                    successful_calls = total_calls
                
                success_rate = (successful_calls / total_calls) * 100 if total_calls > 0 else 0.0
                
                result = KPIResult(
                    kpi_type=KPIType.CALL_SUCCESS_RATE,
                    value=success_rate,
                    unit="%",
                    aggregation_level=aggregation_level,
                    entity_id=str(entity_id),
                    calculation_time=datetime.now(),
                    data_points=total_calls,
                    metadata={
                        "total_calls": total_calls,
                        "successful_calls": successful_calls,
                        "threshold": self.thresholds[KPIType.CALL_SUCCESS_RATE],
                        "meets_threshold": success_rate >= self.thresholds[KPIType.CALL_SUCCESS_RATE]
                    }
                )
                results.append(result)
            
            self.logger.info(f"Calculated call success rate for {len(results)} entities")
            return results
            
        except Exception as e:
            self.logger.error(f"Call success rate calculation failed: {e}")
            return []
    
    async def calculate_signal_strength_kpi(self,
                                          data: pd.DataFrame,
                                          signal_column: str = "rsrp",
                                          aggregation_level: AggregationLevel = AggregationLevel.CELL) -> List[KPIResult]:
        """Calculate signal strength KPI
        
        Args:
            data: DataFrame with signal measurements
            signal_column: Column containing signal strength values
            aggregation_level: Level of aggregation for KPI calculation
            
        Returns:
            List of KPI results
        """
        try:
            if data.empty or signal_column not in data.columns:
                return []
            
            # Determine grouping column
            group_col = self._get_grouping_column(aggregation_level, data)
            if not group_col:
                return []
            
            results = []
            grouped = data.groupby(group_col)
            
            for entity_id, group_data in grouped:
                signal_values = group_data[signal_column].dropna()
                
                if signal_values.empty:
                    continue
                
                # Calculate average signal strength
                avg_signal = signal_values.mean()
                
                # Calculate percentage of measurements above threshold
                above_threshold = len(signal_values[signal_values >= self.thresholds[KPIType.SIGNAL_STRENGTH]])
                threshold_percentage = (above_threshold / len(signal_values)) * 100
                
                result = KPIResult(
                    kpi_type=KPIType.SIGNAL_STRENGTH,
                    value=avg_signal,
                    unit="dBm",
                    aggregation_level=aggregation_level,
                    entity_id=str(entity_id),
                    calculation_time=datetime.now(),
                    data_points=len(signal_values),
                    metadata={
                        "average_signal": avg_signal,
                        "min_signal": signal_values.min(),
                        "max_signal": signal_values.max(),
                        "std_signal": signal_values.std(),
                        "threshold": self.thresholds[KPIType.SIGNAL_STRENGTH],
                        "above_threshold_percentage": threshold_percentage,
                        "meets_threshold": avg_signal >= self.thresholds[KPIType.SIGNAL_STRENGTH]
                    }
                )
                results.append(result)
            
            self.logger.info(f"Calculated signal strength KPI for {len(results)} entities")
            return results
            
        except Exception as e:
            self.logger.error(f"Signal strength KPI calculation failed: {e}")
            return []
    
    async def calculate_handover_success_rate(self,
                                            data: pd.DataFrame,
                                            aggregation_level: AggregationLevel = AggregationLevel.CELL) -> List[KPIResult]:
        """Calculate handover success rate KPI
        
        Args:
            data: DataFrame with handover data
            aggregation_level: Level of aggregation for KPI calculation
            
        Returns:
            List of KPI results
        """
        try:
            if data.empty:
                return []
            
            # Check for required columns
            required_cols = ['source_cell', 'target_cell', 'handover_status']
            if not all(col in data.columns for col in required_cols):
                self.logger.warning("Missing required columns for handover KPI calculation")
                return []
            
            # Determine grouping column
            if aggregation_level == AggregationLevel.CELL:
                group_col = 'source_cell'
            else:
                group_col = self._get_grouping_column(aggregation_level, data)
            
            if not group_col:
                return []
            
            results = []
            grouped = data.groupby(group_col)
            
            for entity_id, group_data in grouped:
                total_handovers = len(group_data)
                successful_handovers = len(group_data[group_data['handover_status'] == 'success'])
                
                success_rate = (successful_handovers / total_handovers) * 100 if total_handovers > 0 else 0.0
                
                result = KPIResult(
                    kpi_type=KPIType.HANDOVER_SUCCESS_RATE,
                    value=success_rate,
                    unit="%",
                    aggregation_level=aggregation_level,
                    entity_id=str(entity_id),
                    calculation_time=datetime.now(),
                    data_points=total_handovers,
                    metadata={
                        "total_handovers": total_handovers,
                        "successful_handovers": successful_handovers,
                        "threshold": self.thresholds[KPIType.HANDOVER_SUCCESS_RATE],
                        "meets_threshold": success_rate >= self.thresholds[KPIType.HANDOVER_SUCCESS_RATE]
                    }
                )
                results.append(result)
            
            self.logger.info(f"Calculated handover success rate for {len(results)} entities")
            return results
            
        except Exception as e:
            self.logger.error(f"Handover success rate calculation failed: {e}")
            return []
    
    async def calculate_network_availability(self,
                                           data: pd.DataFrame,
                                           aggregation_level: AggregationLevel = AggregationLevel.SITE,
                                           time_window: timedelta = timedelta(hours=24)) -> List[KPIResult]:
        """Calculate network availability KPI
        
        Args:
            data: DataFrame with network status data
            aggregation_level: Level of aggregation for KPI calculation
            time_window: Time window for availability calculation
            
        Returns:
            List of KPI results
        """
        try:
            if data.empty or 'network_status' not in data.columns:
                return []
            
            # Filter by time window
            if 'timestamp' in data.columns:
                cutoff_time = datetime.now() - time_window
                data = data[pd.to_datetime(data['timestamp']) >= cutoff_time]
            
            # Determine grouping column
            group_col = self._get_grouping_column(aggregation_level, data)
            if not group_col:
                return []
            
            results = []
            grouped = data.groupby(group_col)
            
            for entity_id, group_data in grouped:
                total_measurements = len(group_data)
                available_measurements = len(group_data[group_data['network_status'] == 'available'])
                
                availability = (available_measurements / total_measurements) * 100 if total_measurements > 0 else 0.0
                
                result = KPIResult(
                    kpi_type=KPIType.NETWORK_AVAILABILITY,
                    value=availability,
                    unit="%",
                    aggregation_level=aggregation_level,
                    entity_id=str(entity_id),
                    calculation_time=datetime.now(),
                    data_points=total_measurements,
                    metadata={
                        "total_measurements": total_measurements,
                        "available_measurements": available_measurements,
                        "time_window_hours": time_window.total_seconds() / 3600,
                        "threshold": self.thresholds[KPIType.NETWORK_AVAILABILITY],
                        "meets_threshold": availability >= self.thresholds[KPIType.NETWORK_AVAILABILITY]
                    }
                )
                results.append(result)
            
            self.logger.info(f"Calculated network availability for {len(results)} entities")
            return results
            
        except Exception as e:
            self.logger.error(f"Network availability calculation failed: {e}")
            return []
    
    def _get_grouping_column(self, aggregation_level: AggregationLevel, data: pd.DataFrame) -> Optional[str]:
        """Get appropriate grouping column based on aggregation level
        
        Args:
            aggregation_level: Level of aggregation
            data: DataFrame to check for available columns
            
        Returns:
            Column name for grouping or None if not available
        """
        column_mapping = {
            AggregationLevel.CELL: ['cell_id', 'cell', 'cellid'],
            AggregationLevel.SITE: ['site_id', 'site', 'siteid', 'base_station_id'],
            AggregationLevel.REGION: ['region_id', 'region', 'area_id', 'area'],
            AggregationLevel.NETWORK: ['network_id', 'network', 'operator_id']
        }
        
        possible_columns = column_mapping.get(aggregation_level, [])
        
        for col in possible_columns:
            if col in data.columns:
                return col
        
        self.logger.warning(f"No suitable grouping column found for {aggregation_level}")
        return None
    
    async def calculate_all_kpis(self,
                               cdr_data: Optional[pd.DataFrame] = None,
                               ep_data: Optional[pd.DataFrame] = None,
                               network_data: Optional[pd.DataFrame] = None,
                               aggregation_level: AggregationLevel = AggregationLevel.CELL) -> Dict[KPIType, List[KPIResult]]:
        """Calculate all available KPIs from provided data
        
        Args:
            cdr_data: Call Detail Records data
            ep_data: Energy Points/External Provider data
            network_data: Network status data
            aggregation_level: Level of aggregation for KPI calculation
            
        Returns:
            Dictionary mapping KPI types to their results
        """
        results = {}
        
        try:
            # Calculate KPIs from CDR data
            if cdr_data is not None and not cdr_data.empty:
                call_success_results = await self.calculate_call_success_rate(cdr_data, aggregation_level)
                if call_success_results:
                    results[KPIType.CALL_SUCCESS_RATE] = call_success_results
            
            # Calculate KPIs from EP data
            if ep_data is not None and not ep_data.empty:
                signal_results = await self.calculate_signal_strength_kpi(ep_data, aggregation_level=aggregation_level)
                if signal_results:
                    results[KPIType.SIGNAL_STRENGTH] = signal_results
                
                # Calculate handover KPIs if handover data is available
                handover_results = await self.calculate_handover_success_rate(ep_data, aggregation_level)
                if handover_results:
                    results[KPIType.HANDOVER_SUCCESS_RATE] = handover_results
            
            # Calculate KPIs from network data
            if network_data is not None and not network_data.empty:
                availability_results = await self.calculate_network_availability(network_data, aggregation_level)
                if availability_results:
                    results[KPIType.NETWORK_AVAILABILITY] = availability_results
            
            total_kpis = sum(len(kpi_list) for kpi_list in results.values())
            self.logger.info(f"Calculated {total_kpis} KPIs across {len(results)} KPI types")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Comprehensive KPI calculation failed: {e}")
            return {}
    
    def export_kpi_results(self, results: Dict[KPIType, List[KPIResult]]) -> pd.DataFrame:
        """Export KPI results to DataFrame format
        
        Args:
            results: Dictionary of KPI results
            
        Returns:
            DataFrame containing all KPI results
        """
        try:
            all_results = []
            
            for kpi_type, kpi_list in results.items():
                for kpi_result in kpi_list:
                    result_dict = {
                        'kpi_type': kpi_result.kpi_type.value,
                        'value': kpi_result.value,
                        'unit': kpi_result.unit,
                        'aggregation_level': kpi_result.aggregation_level.value,
                        'entity_id': kpi_result.entity_id,
                        'calculation_time': kpi_result.calculation_time,
                        'data_points': kpi_result.data_points,
                    }
                    
                    # Add metadata as separate columns
                    for key, value in kpi_result.metadata.items():
                        result_dict[f'metadata_{key}'] = value
                    
                    all_results.append(result_dict)
            
            if all_results:
                return pd.DataFrame(all_results)
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"KPI export failed: {e}")
            return pd.DataFrame()
