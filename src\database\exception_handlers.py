"""Exception handling decorators and utilities.

This module provides decorators and utilities for standardized exception
handling across the database framework, including retry mechanisms,
degradation strategies, and alerting.
"""

import asyncio
import functools
import logging
import time
from typing import Any, Callable, Dict, List, Optional, Type, Union

from .exceptions import (
    DatabaseError,
    RetryableError,
    NonRetryableError,
    PerformanceError,
    ConnectionError,
    TimeoutError,
)


logger = logging.getLogger(__name__)


class ExceptionHandlerConfig:
    """Configuration for exception handling strategies."""
    
    def __init__(
        self,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        backoff_factor: float = 2.0,
        retryable_exceptions: Optional[List[Type[Exception]]] = None,
        non_retryable_exceptions: Optional[List[Type[Exception]]] = None,
        enable_degradation: bool = True,
        degradation_fallback: Optional[Callable] = None,
        enable_alerting: bool = True,
        alert_threshold: int = 1,
        context_fields: Optional[List[str]] = None,
    ):
        """Initialize exception handler configuration.
        
        Args:
            max_retries: Maximum number of retry attempts.
            retry_delay: Initial delay between retries in seconds.
            backoff_factor: Factor to multiply delay by for each retry.
            retryable_exceptions: List of exception types that can be retried.
            non_retryable_exceptions: List of exception types that should not be retried.
            enable_degradation: Whether to enable degradation fallback.
            degradation_fallback: Fallback function for degradation.
            enable_alerting: Whether to enable alerting on errors.
            alert_threshold: Number of errors before triggering alert.
            context_fields: List of context fields to include in error logs.
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.backoff_factor = backoff_factor
        self.retryable_exceptions = retryable_exceptions or [
            ConnectionError,
            TimeoutError,
            RetryableError,
        ]
        self.non_retryable_exceptions = non_retryable_exceptions or [
            NonRetryableError,
            ValueError,
            TypeError,
        ]
        self.enable_degradation = enable_degradation
        self.degradation_fallback = degradation_fallback
        self.enable_alerting = enable_alerting
        self.alert_threshold = alert_threshold
        self.context_fields = context_fields or [
            "user_id", "session_id", "operation_id", "request_id"
        ]


class ErrorContext:
    """Context information for error handling."""
    
    def __init__(self, **kwargs):
        """Initialize error context with arbitrary fields."""
        self.data = kwargs
        self.timestamp = time.time()
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get context value by key."""
        return self.data.get(key, default)
    
    def update(self, **kwargs) -> None:
        """Update context with new values."""
        self.data.update(kwargs)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert context to dictionary."""
        return {
            "timestamp": self.timestamp,
            **self.data
        }


class ExceptionHandler:
    """Centralized exception handler with retry, degradation, and alerting."""
    
    def __init__(self, config: Optional[ExceptionHandlerConfig] = None):
        """Initialize exception handler.
        
        Args:
            config: Exception handler configuration.
        """
        self.config = config or ExceptionHandlerConfig()
        self.error_counts: Dict[str, int] = {}
        self.last_alert_times: Dict[str, float] = {}
    
    def _is_retryable(self, exception: Exception) -> bool:
        """Check if exception is retryable.
        
        Args:
            exception: Exception to check.
            
        Returns:
            True if exception can be retried.
        """
        # Check non-retryable first (higher priority)
        for exc_type in self.config.non_retryable_exceptions:
            if isinstance(exception, exc_type):
                return False
        
        # Check retryable
        for exc_type in self.config.retryable_exceptions:
            if isinstance(exception, exc_type):
                return True
        
        # Default: not retryable
        return False
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt.
        
        Args:
            attempt: Current attempt number (0-based).
            
        Returns:
            Delay in seconds.
        """
        return self.config.retry_delay * (self.config.backoff_factor ** attempt)
    
    def _log_error(
        self,
        exception: Exception,
        context: Optional[ErrorContext] = None,
        attempt: int = 0,
        max_attempts: int = 0,
    ) -> None:
        """Log error with context information.
        
        Args:
            exception: Exception that occurred.
            context: Error context.
            attempt: Current attempt number.
            max_attempts: Maximum number of attempts.
        """
        log_data = {
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "attempt": attempt + 1,
            "max_attempts": max_attempts + 1,
        }
        
        if context:
            # Add configured context fields
            for field in self.config.context_fields:
                value = context.get(field)
                if value is not None:
                    log_data[field] = value
        
        if hasattr(exception, 'to_dict'):
            log_data["exception_details"] = exception.to_dict()
        
        if attempt < max_attempts:
            logger.warning(f"Operation failed, retrying: {log_data}")
        else:
            logger.error(f"Operation failed after all retries: {log_data}")
    
    def _should_alert(self, operation_name: str) -> bool:
        """Check if alert should be triggered.
        
        Args:
            operation_name: Name of the operation.
            
        Returns:
            True if alert should be triggered.
        """
        if not self.config.enable_alerting:
            return False
        
        self.error_counts[operation_name] = self.error_counts.get(operation_name, 0) + 1
        
        return self.error_counts[operation_name] >= self.config.alert_threshold
    
    def _trigger_alert(
        self,
        operation_name: str,
        exception: Exception,
        context: Optional[ErrorContext] = None,
    ) -> None:
        """Trigger alert for critical error.
        
        Args:
            operation_name: Name of the operation.
            exception: Exception that occurred.
            context: Error context.
        """
        current_time = time.time()
        last_alert = self.last_alert_times.get(operation_name, 0)
        
        # Avoid alert spam (minimum 5 minutes between alerts for same operation)
        if current_time - last_alert < 300:
            return
        
        self.last_alert_times[operation_name] = current_time
        
        alert_data = {
            "operation": operation_name,
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "error_count": self.error_counts.get(operation_name, 0),
            "timestamp": current_time,
        }
        
        if context:
            alert_data["context"] = context.to_dict()
        
        # Log critical alert (in production, this could send to monitoring system)
        logger.critical(f"ALERT: Critical error in {operation_name}: {alert_data}")
    
    async def handle_async(
        self,
        func: Callable,
        *args,
        context: Optional[ErrorContext] = None,
        operation_name: Optional[str] = None,
        **kwargs,
    ) -> Any:
        """Handle async function execution with retry and error handling.
        
        Args:
            func: Async function to execute.
            *args: Function arguments.
            context: Error context.
            operation_name: Name of the operation for logging.
            **kwargs: Function keyword arguments.
            
        Returns:
            Function result or degradation fallback result.
            
        Raises:
            Exception: If all retries fail and no degradation is available.
        """
        operation_name = operation_name or func.__name__
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            
            except Exception as e:
                last_exception = e
                self._log_error(e, context, attempt, self.config.max_retries)
                
                # Check if we should retry
                if attempt < self.config.max_retries and self._is_retryable(e):
                    delay = self._calculate_delay(attempt)
                    await asyncio.sleep(delay)
                    continue
                
                # No more retries or non-retryable error
                break
        
        # Check if alert should be triggered
        if self._should_alert(operation_name):
            self._trigger_alert(operation_name, last_exception, context)
        
        # Try degradation fallback
        if self.config.enable_degradation and self.config.degradation_fallback:
            try:
                logger.warning(f"Attempting degradation fallback for {operation_name}")
                if asyncio.iscoroutinefunction(self.config.degradation_fallback):
                    return await self.config.degradation_fallback(*args, **kwargs)
                else:
                    return self.config.degradation_fallback(*args, **kwargs)
            except Exception as fallback_error:
                logger.error(f"Degradation fallback failed: {fallback_error}")
        
        # Re-raise the last exception
        raise last_exception
    
    def handle_sync(
        self,
        func: Callable,
        *args,
        context: Optional[ErrorContext] = None,
        operation_name: Optional[str] = None,
        **kwargs,
    ) -> Any:
        """Handle sync function execution with retry and error handling.
        
        Args:
            func: Function to execute.
            *args: Function arguments.
            context: Error context.
            operation_name: Name of the operation for logging.
            **kwargs: Function keyword arguments.
            
        Returns:
            Function result or degradation fallback result.
            
        Raises:
            Exception: If all retries fail and no degradation is available.
        """
        operation_name = operation_name or func.__name__
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                return func(*args, **kwargs)
            
            except Exception as e:
                last_exception = e
                self._log_error(e, context, attempt, self.config.max_retries)
                
                # Check if we should retry
                if attempt < self.config.max_retries and self._is_retryable(e):
                    delay = self._calculate_delay(attempt)
                    time.sleep(delay)
                    continue
                
                # No more retries or non-retryable error
                break
        
        # Check if alert should be triggered
        if self._should_alert(operation_name):
            self._trigger_alert(operation_name, last_exception, context)
        
        # Try degradation fallback
        if self.config.enable_degradation and self.config.degradation_fallback:
            try:
                logger.warning(f"Attempting degradation fallback for {operation_name}")
                return self.config.degradation_fallback(*args, **kwargs)
            except Exception as fallback_error:
                logger.error(f"Degradation fallback failed: {fallback_error}")
        
        # Re-raise the last exception
        raise last_exception


# Global exception handler instance
_default_handler = ExceptionHandler()


def with_exception_handling(
    config: Optional[ExceptionHandlerConfig] = None,
    operation_name: Optional[str] = None,
    context_factory: Optional[Callable[..., ErrorContext]] = None,
):
    """Decorator for automatic exception handling with retry and degradation.
    
    Args:
        config: Exception handler configuration.
        operation_name: Name of the operation for logging.
        context_factory: Function to create error context from function arguments.
        
    Returns:
        Decorated function.
    """
    def decorator(func: Callable) -> Callable:
        handler = ExceptionHandler(config) if config else _default_handler
        op_name = operation_name or func.__name__
        
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                context = None
                if context_factory:
                    try:
                        context = context_factory(*args, **kwargs)
                    except Exception as e:
                        logger.warning(f"Failed to create error context: {e}")
                
                return await handler.handle_async(
                    func, *args, context=context, operation_name=op_name, **kwargs
                )
            
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                context = None
                if context_factory:
                    try:
                        context = context_factory(*args, **kwargs)
                    except Exception as e:
                        logger.warning(f"Failed to create error context: {e}")
                
                return handler.handle_sync(
                    func, *args, context=context, operation_name=op_name, **kwargs
                )
            
            return sync_wrapper
    
    return decorator


def with_retry(
    max_retries: int = 3,
    retry_delay: float = 1.0,
    backoff_factor: float = 2.0,
    retryable_exceptions: Optional[List[Type[Exception]]] = None,
):
    """Simple retry decorator.
    
    Args:
        max_retries: Maximum number of retry attempts.
        retry_delay: Initial delay between retries.
        backoff_factor: Factor to multiply delay by for each retry.
        retryable_exceptions: List of exception types that can be retried.
        
    Returns:
        Decorated function.
    """
    config = ExceptionHandlerConfig(
        max_retries=max_retries,
        retry_delay=retry_delay,
        backoff_factor=backoff_factor,
        retryable_exceptions=retryable_exceptions,
        enable_degradation=False,
        enable_alerting=False,
    )
    
    return with_exception_handling(config=config)


def with_degradation(fallback_func: Callable):
    """Degradation decorator that provides fallback functionality.
    
    Args:
        fallback_func: Fallback function to call on error.
        
    Returns:
        Decorated function.
    """
    config = ExceptionHandlerConfig(
        max_retries=0,
        enable_degradation=True,
        degradation_fallback=fallback_func,
        enable_alerting=False,
    )
    
    return with_exception_handling(config=config)


def with_alerting(
    alert_threshold: int = 1,
    context_fields: Optional[List[str]] = None,
):
    """Alerting decorator for monitoring critical errors.
    
    Args:
        alert_threshold: Number of errors before triggering alert.
        context_fields: List of context fields to include in alerts.
        
    Returns:
        Decorated function.
    """
    config = ExceptionHandlerConfig(
        max_retries=0,
        enable_degradation=False,
        enable_alerting=True,
        alert_threshold=alert_threshold,
        context_fields=context_fields,
    )
    
    return with_exception_handling(config=config)


def create_error_context(**kwargs) -> ErrorContext:
    """Create error context with provided fields.
    
    Args:
        **kwargs: Context fields.
        
    Returns:
        Error context instance.
    """
    return ErrorContext(**kwargs)