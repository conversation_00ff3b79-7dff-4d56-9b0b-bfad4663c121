"""Advanced query builder module for dynamic SQL construction.

This module provides sophisticated query building capabilities including:
- Dynamic SQL query construction
- Complex JOIN operations
- Advanced filtering and aggregation
- Query optimization and validation
- Support for multiple database dialects
"""

from .aggregations import Aggregation, avg, count, GroupBy, max_agg, min_agg, sum_agg
from .builder import DeleteQuery, InsertQuery, QueryBuilder, SelectQuery, UpdateQuery
from .conditions import AndCondition, Condition, NotCondition, OrCondition
from .dialects import MySQLDialect, PostgreSQLDialect, SQLiteDialect
from .joins import <PERSON>Join, InnerJoin, <PERSON><PERSON>, <PERSON><PERSON>oi<PERSON>, <PERSON><PERSON>oin
from .optimizers import CompositeOptimizer
from .validators import SQLInjectionValidator, CompositeValidator

__all__ = [
    "QueryBuilder",
    "SelectQuery",
    "InsertQuery",
    "UpdateQuery",
    "DeleteQuery",
    "Condition",
    "AndCondition",
    "OrCondition",
    "NotCondition",
    "Join",
    "InnerJoin",
    "LeftJoin",
    "<PERSON><PERSON>oin",
    "FullJoin",
    "Aggregation",
    "count",
    "sum_agg",
    "avg",
    "min_agg",
    "max_agg",
    "GroupBy",
    "CompositeOptimizer",
    "PostgreSQLDialect",
    "MySQLDialect",
    "SQLiteDialect",
    "SQLInjectionValidator",
    "CompositeValidator",
]

# P2 Priority Features:
# - Dynamic SQL query construction with type safety
# - Complex JOIN operations with automatic optimization
# - Advanced filtering with condition chaining
# - Aggregation and grouping support
# - Query validation and security checks
# - Multi-dialect support for different databases
# - Performance optimization hints
# - Query caching and reuse
