#!/usr/bin/env python3
"""
Unit tests for database connection management (session.py).

This module provides comprehensive test coverage for the connection management
functionality implemented in src/database/connection/session.py, including:
- Connection acquisition and release
- Error handling for various failure scenarios
- Session manager functionality
- Connection testing utilities
"""

import asyncio
import os
from typing import Optional
from unittest.mock import AsyncMock, MagicMock, Mock, patch

import asyncpg
import pytest
from asyncpg import Connection

from src.config import get_config

# Import the modules under test
from src.database.connection.session import (
    SessionManager,
    close_db_connection,
    get_db_connection,
    get_session_manager,
)
from src.database.connection.session import test_connection as db_test_connection
from src.database.exceptions import (
    ConfigurationError,
    ConnectionError,
    DatabaseError,
    TimeoutError,
)


class TestSessionManager:
    """Test cases for SessionManager class."""

    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        config.database = Mock()
        config.database.host = "localhost"
        config.database.port = 5432
        config.database.name = "test_db"
        config.database.user = "test_user"
        config.database.password = "test_password"
        config.pool = Mock()
        config.pool.timeout = 30
        return config

    @pytest.fixture
    def session_manager(self, mock_config):
        """Create a SessionManager instance for testing."""
        return SessionManager(mock_config)

    def test_session_manager_initialization(self, mock_config):
        """Test SessionManager initialization with config."""
        manager = SessionManager(mock_config)
        assert manager.config == mock_config
        assert manager._connection is None
        assert manager._is_connected is False

    def test_session_manager_initialization_without_config(self):
        """Test SessionManager initialization without config (uses default)."""
        with patch("src.database.connection.session.Config") as mock_config_class:
            mock_config_instance = Mock()
            mock_config_class.return_value = mock_config_instance

            manager = SessionManager()
            assert manager.config == mock_config_instance
            mock_config_class.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_connection_success(self, session_manager, mock_config):
        """Test successful database connection acquisition."""
        # Mock asyncpg.connect
        mock_connection = AsyncMock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=False)

        with patch("asyncpg.connect", new_callable=AsyncMock) as mock_connect:
            with patch("asyncio.wait_for", new_callable=AsyncMock) as mock_wait_for:
                mock_wait_for.return_value = mock_connection

                result = await session_manager.get_connection()

                assert result == mock_connection
                assert session_manager._connection == mock_connection
                assert session_manager._is_connected is True

                # Verify connection parameters
                expected_params = {
                    "host": "localhost",
                    "port": 5432,
                    "database": "test_db",
                    "user": "test_user",
                    "password": "test_password",
                    "command_timeout": 30,
                    "server_settings": {
                        "application_name": "connect_database_framework"
                    },
                }
                mock_wait_for.assert_called_once()
                args, kwargs = mock_wait_for.call_args
                assert kwargs["timeout"] == 30

    @pytest.mark.asyncio
    async def test_get_connection_reuse_existing(self, session_manager):
        """Test that existing connection is reused when still active."""
        # Set up existing connection
        mock_connection = Mock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=False)
        session_manager._connection = mock_connection

        result = await session_manager.get_connection()

        assert result == mock_connection

    @pytest.mark.asyncio
    async def test_get_connection_timeout_error(self, session_manager):
        """Test timeout error handling."""
        with patch("asyncpg.connect", new_callable=AsyncMock) as mock_connect, patch(
            "asyncio.wait_for", new_callable=AsyncMock
        ) as mock_wait_for:
            mock_wait_for.side_effect = asyncio.TimeoutError()

            with pytest.raises(TimeoutError) as exc_info:
                await session_manager.get_connection()

            assert "Database connection timeout" in str(exc_info.value)
            assert exc_info.value.error_code == "DB_CONNECTION_TIMEOUT"

    @pytest.mark.asyncio
    async def test_get_connection_invalid_database_error(self, session_manager):
        """Test invalid database name handling."""
        with patch("asyncpg.connect", new_callable=AsyncMock) as mock_connect, patch(
            "asyncio.wait_for", new_callable=AsyncMock
        ) as mock_wait_for:
            mock_wait_for.side_effect = asyncpg.InvalidCatalogNameError(
                "Database does not exist"
            )

            with pytest.raises(ConfigurationError) as exc_info:
                await session_manager.get_connection()

            assert "does not exist" in str(exc_info.value)
            assert exc_info.value.error_code == "DB_INVALID_DATABASE"

    @pytest.mark.asyncio
    async def test_get_connection_invalid_credentials_error(self, session_manager):
        """Test invalid credentials handling."""
        with patch("asyncpg.connect", new_callable=AsyncMock) as mock_connect, patch(
            "asyncio.wait_for", new_callable=AsyncMock
        ) as mock_wait_for:
            mock_wait_for.side_effect = asyncpg.InvalidPasswordError("Invalid password")

            with pytest.raises(ConnectionError) as exc_info:
                await session_manager.get_connection()

            assert "Invalid database credentials" in str(exc_info.value)
            assert exc_info.value.error_code == "DB_INVALID_CREDENTIALS"

    @pytest.mark.asyncio
    async def test_get_connection_server_unavailable_error(self, session_manager):
        """Test server unavailable handling."""
        with patch("asyncpg.connect", new_callable=AsyncMock) as mock_connect, patch(
            "asyncio.wait_for", new_callable=AsyncMock
        ) as mock_wait_for:
            mock_wait_for.side_effect = asyncpg.CannotConnectNowError(
                "Server not accepting connections"
            )

            with pytest.raises(ConnectionError) as exc_info:
                await session_manager.get_connection()

            assert "not accepting connections" in str(exc_info.value)
            assert exc_info.value.error_code == "DB_SERVER_UNAVAILABLE"

    @pytest.mark.asyncio
    async def test_get_connection_generic_error(self, session_manager):
        """Test generic connection error handling."""
        with patch("asyncpg.connect", new_callable=AsyncMock) as mock_connect, patch(
            "asyncio.wait_for", new_callable=AsyncMock
        ) as mock_wait_for:
            mock_wait_for.side_effect = Exception("Unexpected error")

            with pytest.raises(ConnectionError) as exc_info:
                await session_manager.get_connection()

            assert "Unexpected error connecting to database" in str(exc_info.value)
            assert exc_info.value.error_code == "DB_CONNECTION_FAILED"

    @pytest.mark.asyncio
    async def test_close_connection_success(self, session_manager):
        """Test successful connection closing."""
        # Set up active connection
        mock_connection = AsyncMock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=False)
        session_manager._connection = mock_connection
        session_manager._is_connected = True

        await session_manager.close_connection()

        mock_connection.close.assert_called_once()
        assert session_manager._is_connected is False
        assert session_manager._connection is None

    @pytest.mark.asyncio
    async def test_close_connection_no_connection(self, session_manager):
        """Test closing when no connection exists."""
        # No connection set
        session_manager._connection = None

        # Should not raise any exception
        await session_manager.close_connection()

    @pytest.mark.asyncio
    async def test_close_connection_already_closed(self, session_manager):
        """Test closing when connection is already closed."""
        # Set up closed connection
        mock_connection = Mock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=True)
        session_manager._connection = mock_connection

        # Should not raise any exception
        await session_manager.close_connection()

    @pytest.mark.asyncio
    async def test_close_connection_error(self, session_manager):
        """Test error handling during connection closing."""
        # Set up connection that fails to close
        mock_connection = AsyncMock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=False)
        mock_connection.close.side_effect = Exception("Close failed")
        session_manager._connection = mock_connection
        session_manager._is_connected = True

        with pytest.raises(ConnectionError) as exc_info:
            await session_manager.close_connection()

        assert "Error closing database connection" in str(exc_info.value)
        assert exc_info.value.error_code == "DB_CONNECTION_CLOSE_FAILED"

    def test_build_connection_params(self, session_manager, mock_config):
        """Test connection parameters building."""
        params = session_manager._build_connection_params()

        expected_params = {
            "host": "localhost",
            "port": 5432,
            "database": "test_db",
            "user": "test_user",
            "password": "test_password",
            "command_timeout": 30,
            "server_settings": {"application_name": "connect_database_framework"},
        }

        assert params == expected_params

    def test_build_connection_params_missing_config(self, session_manager):
        """Test connection parameters building with missing configuration."""
        # Remove required config attribute
        del session_manager.config.database.host

        with pytest.raises(ConfigurationError) as exc_info:
            session_manager._build_connection_params()

        assert "Missing required configuration" in str(exc_info.value)
        assert exc_info.value.error_code == "DB_CONFIG_MISSING"

    def test_is_connected_property(self, session_manager):
        """Test is_connected property."""
        # Initially not connected
        assert session_manager.is_connected is False

        # Set up active connection
        mock_connection = Mock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=False)
        session_manager._connection = mock_connection
        session_manager._is_connected = True

        assert session_manager.is_connected is True

        # Test with closed connection
        mock_connection.is_closed = Mock(return_value=True)
        assert session_manager.is_connected is False


class TestGlobalFunctions:
    """Test cases for global session management functions."""

    def test_get_session_manager_singleton(self):
        """Test that get_session_manager returns singleton instance."""
        with patch(
            "src.database.connection.session.SessionManager"
        ) as mock_session_manager_class:
            mock_instance = Mock()
            mock_session_manager_class.return_value = mock_instance

            # First call creates instance
            manager1 = get_session_manager()
            assert manager1 == mock_instance
            mock_session_manager_class.assert_called_once_with(None)

            # Second call returns same instance
            mock_session_manager_class.reset_mock()
            manager2 = get_session_manager()
            assert manager2 == mock_instance
            mock_session_manager_class.assert_not_called()

    def test_get_session_manager_with_config(self):
        """Test get_session_manager with new config creates new instance."""
        mock_config = Mock()

        with patch(
            "src.database.connection.session.SessionManager"
        ) as mock_session_manager_class:
            mock_instance = Mock()
            mock_session_manager_class.return_value = mock_instance

            manager = get_session_manager(mock_config)
            assert manager == mock_instance
            mock_session_manager_class.assert_called_once_with(mock_config)

    @pytest.mark.asyncio
    async def test_get_db_connection(self):
        """Test get_db_connection function."""
        mock_config = Mock()
        mock_connection = Mock(spec=Connection)

        with patch(
            "src.database.connection.session.get_session_manager"
        ) as mock_get_manager:
            mock_manager = Mock()
            mock_manager.get_connection = AsyncMock(return_value=mock_connection)
            mock_get_manager.return_value = mock_manager

            result = await get_db_connection(mock_config)

            assert result == mock_connection
            mock_get_manager.assert_called_once_with(mock_config)
            mock_manager.get_connection.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_db_connection_with_connection(self):
        """Test close_db_connection with specific connection."""
        mock_connection = AsyncMock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=False)

        await close_db_connection(mock_connection)

        mock_connection.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_db_connection_with_none(self):
        """Test close_db_connection with None (uses global manager)."""
        with patch(
            "src.database.connection.session.get_session_manager"
        ) as mock_get_manager:
            mock_manager = Mock()
            mock_manager.close_connection = AsyncMock()
            mock_get_manager.return_value = mock_manager

            await close_db_connection(None)

            mock_get_manager.assert_called_once()
            mock_manager.close_connection.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_db_connection_already_closed(self):
        """Test close_db_connection with already closed connection."""
        mock_connection = Mock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=True)

        # Should not raise any exception
        await close_db_connection(mock_connection)

    @pytest.mark.asyncio
    async def test_close_db_connection_error(self):
        """Test close_db_connection error handling."""
        mock_connection = AsyncMock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=False)
        mock_connection.close.side_effect = Exception("Close failed")

        with pytest.raises(ConnectionError) as exc_info:
            await close_db_connection(mock_connection)

        assert "Error closing database connection" in str(exc_info.value)
        assert exc_info.value.error_code == "DB_CONNECTION_CLOSE_FAILED"

    @pytest.mark.asyncio
    async def test_test_connection_success(self):
        """Test successful connection test."""
        mock_config = Mock()
        mock_connection = AsyncMock(spec=Connection)
        mock_connection.fetchval.return_value = 1

        with patch(
            "src.database.connection.session.get_db_connection"
        ) as mock_get_conn:
            with patch(
                "src.database.connection.session.close_db_connection"
            ) as mock_close_conn:
                mock_get_conn.return_value = mock_connection

                result = await db_test_connection(mock_config)

                assert result is True
                mock_get_conn.assert_called_once_with(mock_config)
                mock_connection.fetchval.assert_called_once_with("SELECT 1")
                mock_close_conn.assert_called_once_with(mock_connection)

    @pytest.mark.asyncio
    async def test_test_connection_failure(self):
        """Test connection test failure."""
        mock_config = Mock()

        with patch(
            "src.database.connection.session.get_db_connection"
        ) as mock_get_conn:
            with patch(
                "src.database.connection.session.close_db_connection"
            ) as mock_close_conn:
                mock_get_conn.side_effect = ConnectionError("Connection failed")

                result = await db_test_connection(mock_config)

                assert result is False
                mock_get_conn.assert_called_once_with(mock_config)
                # close_db_connection should NOT be called when connection is None
                # (get_db_connection failed)
                mock_close_conn.assert_not_called()

    @pytest.mark.asyncio
    async def test_test_connection_query_failure(self):
        """Test connection test with query failure."""
        mock_config = Mock()
        mock_connection = AsyncMock(spec=Connection)
        mock_connection.fetchval.side_effect = Exception("Query failed")

        with patch(
            "src.database.connection.session.get_db_connection"
        ) as mock_get_conn:
            with patch(
                "src.database.connection.session.close_db_connection"
            ) as mock_close_conn:
                mock_get_conn.return_value = mock_connection

                result = await db_test_connection(mock_config)

                assert result is False
                mock_close_conn.assert_called_once_with(mock_connection)

    @pytest.mark.asyncio
    async def test_test_connection_cleanup_error(self):
        """Test connection test with cleanup error (should be ignored)."""
        mock_config = Mock()
        mock_connection = AsyncMock(spec=Connection)
        mock_connection.fetchval.return_value = 1

        with patch(
            "src.database.connection.session.get_db_connection"
        ) as mock_get_conn:
            with patch(
                "src.database.connection.session.close_db_connection"
            ) as mock_close_conn:
                mock_get_conn.return_value = mock_connection
                mock_close_conn.side_effect = Exception("Cleanup failed")

                # Should still return True despite cleanup error
                result = await db_test_connection(mock_config)

                assert result is True


class TestIntegrationScenarios:
    """Integration test scenarios for session management."""

    @pytest.mark.asyncio
    async def test_full_connection_lifecycle(self):
        """Test complete connection lifecycle: acquire -> use -> close."""
        mock_config = Mock(spec=Config)
        mock_config.database = Mock()
        mock_config.database.host = "localhost"
        mock_config.database.port = 5432
        mock_config.database.name = "test_db"
        mock_config.database.user = "test_user"
        mock_config.database.password = os.getenv(
            "TEST_DB_PASSWORD", "secure_test_password_123!"
        )
        mock_config.pool = Mock()
        mock_config.pool.timeout = 30

        mock_connection = AsyncMock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=False)
        mock_connection.fetchval.return_value = 1

        with patch("asyncpg.connect", new_callable=AsyncMock) as mock_connect, patch(
            "asyncio.wait_for", new_callable=AsyncMock
        ) as mock_wait_for:
            mock_wait_for.return_value = mock_connection

            # Test connection acquisition
            connection = await get_db_connection(mock_config)
            assert connection == mock_connection

            # Test connection usage (simple query)
            result = await connection.fetchval("SELECT 1")
            assert result == 1

            # Test connection closing
            await close_db_connection(connection)
            mock_connection.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_multiple_session_managers(self):
        """Test behavior with multiple session manager instances."""
        config1 = Mock(spec=Config)
        config2 = Mock(spec=Config)

        # Reset global session manager for clean test
        import src.database.connection.session as session_module

        session_module._session_manager = None

        # First call creates manager with config1
        manager1 = get_session_manager(config1)
        assert manager1.config == config1

        # Second call with new config creates a new manager and replaces the global one
        manager2 = get_session_manager(config2)
        assert manager2.config == config2

        # manager1 and manager2 are different objects since each config creates new instance
        assert manager1 is not manager2

        # But the global reference now points to manager2
        current_manager = get_session_manager()  # No config, returns current global
        assert current_manager is manager2

    @pytest.mark.asyncio
    async def test_error_recovery_scenario(self):
        """Test error recovery in connection management."""
        mock_config = Mock(spec=Config)
        mock_config.database = Mock()
        mock_config.database.host = "localhost"
        mock_config.database.port = 5432
        mock_config.database.name = "test_db"
        mock_config.database.user = "test_user"
        mock_config.database.password = os.getenv(
            "TEST_DB_PASSWORD", "secure_test_password_123!"
        )
        mock_config.pool = Mock()
        mock_config.pool.timeout = 30

        session_manager = SessionManager(mock_config)

        # First connection attempt fails
        with patch(
            "src.database.connection.session.asyncpg.connect", new_callable=AsyncMock
        ) as mock_connect, patch(
            "src.database.connection.session.asyncio.wait_for", new_callable=AsyncMock
        ) as mock_wait_for:
            mock_wait_for.side_effect = asyncpg.InvalidPasswordError("Invalid password")

            with pytest.raises(ConnectionError):
                await session_manager.get_connection()

            # Manager should still be in clean state
            assert session_manager._connection is None
            assert session_manager._is_connected is False

        # Second connection attempt succeeds
        mock_connection = AsyncMock(spec=Connection)
        mock_connection.is_closed = Mock(return_value=False)

        with patch(
            "src.database.connection.session.asyncpg.connect", new_callable=AsyncMock
        ) as mock_connect, patch(
            "src.database.connection.session.asyncio.wait_for", new_callable=AsyncMock
        ) as mock_wait_for:
            mock_wait_for.return_value = mock_connection

            connection = await session_manager.get_connection()
            assert connection == mock_connection
            assert session_manager._is_connected is True
