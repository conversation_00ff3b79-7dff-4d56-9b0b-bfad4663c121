#!/usr/bin/env python3
"""
Example demonstrating the database logging framework usage.

This script shows how to configure and use the logging framework
following the PRD LOGGING_CONFIG structure.
"""

import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from database.config import Config, DatabaseConfig, LoggingConfig
from database.monitoring import get_logger, setup_logging
from database.monitoring.logger import (
    get_config_logger,
    get_connection_logger,
    get_exception_logger,
    get_monitoring_logger,
)


def main():
    """Demonstrate logging framework usage."""
    print("Database Logging Framework Example")
    print("=" * 40)

    # Create configuration
    config = Config(
        database=DatabaseConfig(
            host="localhost",
            port=5432,
            name="example_db",
            user="example_user",
            password="example_pass",
        ),
        logging=LoggingConfig(
            level="INFO",
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            file="logs/example_database.log",
            max_bytes=1024 * 1024,  # 1MB
            backup_count=3,
        ),
    )

    # Setup logging
    print("\n1. Setting up logging configuration...")
    setup_logging(config)
    print("   ✓ Logging configured successfully")

    # Get different loggers
    print("\n2. Getting specialized loggers...")
    main_logger = get_logger("main")
    connection_logger = get_connection_logger()
    monitoring_logger = get_monitoring_logger()
    config_logger = get_config_logger()
    exception_logger = get_exception_logger()

    print(f"   ✓ Main logger: {main_logger.name}")
    print(f"   ✓ Connection logger: {connection_logger.name}")
    print(f"   ✓ Monitoring logger: {monitoring_logger.name}")
    print(f"   ✓ Config logger: {config_logger.name}")
    print(f"   ✓ Exception logger: {exception_logger.name}")

    # Demonstrate logging at different levels
    print("\n3. Demonstrating logging at different levels...")

    main_logger.info("Application started successfully")
    main_logger.debug("Debug information (may not appear if level is INFO)")

    connection_logger.info("Establishing database connection")
    connection_logger.warning("Connection pool is running low")

    monitoring_logger.info("System monitoring started")
    monitoring_logger.error("High memory usage detected")

    config_logger.info("Configuration loaded from file")
    config_logger.warning("Using default values for missing configuration")

    exception_logger.error("Database connection failed")
    exception_logger.critical("System is in critical state")

    print("   ✓ Log messages sent to both console and file")

    # Show log file location
    log_file = config.logging.file
    if log_file and os.path.exists(log_file):
        print(f"\n4. Log file created at: {os.path.abspath(log_file)}")

        # Show last few lines of log file
        try:
            with open(log_file, "r", encoding="utf-8") as f:
                lines = f.readlines()
                if lines:
                    print("\n   Last few log entries:")
                    for line in lines[-5:]:
                        print(f"   {line.strip()}")
        except Exception as e:
            print(f"   Could not read log file: {e}")

    # Demonstrate LoggingConfig.to_dict_config()
    print("\n5. Logging configuration structure:")
    dict_config = config.logging.to_dict_config()
    print(f"   Version: {dict_config['version']}")
    print(f"   Disable existing loggers: {dict_config['disable_existing_loggers']}")
    print(f"   Formatters: {list(dict_config['formatters'].keys())}")
    print(f"   Handlers: {list(dict_config['handlers'].keys())}")
    print(f"   Loggers: {list(dict_config['loggers'].keys())}")

    print("\n✓ Example completed successfully!")
    print("\nCheck the log file for all logged messages.")


if __name__ == "__main__":
    main()
