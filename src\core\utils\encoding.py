"""Encoding Detection Utilities

This module provides utilities for detecting file encoding in the Connect
telecommunications data processing system.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import logging
from pathlib import Path
from typing import Optional

try:
    import chardet
    HAS_CHARDET = True
except ImportError:
    chardet = None
    HAS_CHARDET = False


logger = logging.getLogger(__name__)


async def detect_encoding(file_path: Path, sample_size: int = 8192) -> str:
    """Detect file encoding using chardet.
    
    Args:
        file_path: Path to the file
        sample_size: Number of bytes to sample for detection
        
    Returns:
        Detected encoding string
        
    Raises:
        FileNotFoundError: If file doesn't exist
        IOError: If file cannot be read
    """
    if not HAS_CHARDET:
        logger.warning("chardet not available, defaulting to utf-8")
        return 'utf-8'
        
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(sample_size)
            
        if not raw_data:
            logger.warning(f"File {file_path} is empty, defaulting to utf-8")
            return 'utf-8'
            
        result = chardet.detect(raw_data)
        encoding = result.get('encoding', 'utf-8')
        confidence = result.get('confidence', 0.0)
        
        logger.debug(
            f"Detected encoding for {file_path}: {encoding} "
            f"(confidence: {confidence:.2f})"
        )
        
        # Fallback to utf-8 if confidence is too low
        if confidence < 0.7:
            logger.warning(
                f"Low confidence ({confidence:.2f}) for detected encoding {encoding}, "
                f"using utf-8 instead"
            )
            return 'utf-8'
            
        return encoding or 'utf-8'
        
    except Exception as e:
        logger.error(f"Failed to detect encoding for {file_path}: {e}")
        return 'utf-8'


def detect_encoding_sync(file_path: Path, sample_size: int = 8192) -> str:
    """Synchronous version of detect_encoding.
    
    Args:
        file_path: Path to the file
        sample_size: Number of bytes to sample for detection
        
    Returns:
        Detected encoding string
    """
    if not HAS_CHARDET:
        logger.warning("chardet not available, defaulting to utf-8")
        return 'utf-8'
        
    try:
        with open(file_path, 'rb') as f:
            raw_data = f.read(sample_size)
            
        if not raw_data:
            logger.warning(f"File {file_path} is empty, defaulting to utf-8")
            return 'utf-8'
            
        result = chardet.detect(raw_data)
        encoding = result.get('encoding', 'utf-8')
        confidence = result.get('confidence', 0.0)
        
        logger.debug(
            f"Detected encoding for {file_path}: {encoding} "
            f"(confidence: {confidence:.2f})"
        )
        
        # Fallback to utf-8 if confidence is too low
        if confidence < 0.7:
            logger.warning(
                f"Low confidence ({confidence:.2f}) for detected encoding {encoding}, "
                f"using utf-8 instead"
            )
            return 'utf-8'
            
        return encoding or 'utf-8'
        
    except Exception as e:
        logger.error(f"Failed to detect encoding for {file_path}: {e}")
        return 'utf-8'