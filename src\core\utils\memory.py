# -*- coding: utf-8 -*-
"""
Memory Monitoring and Management Utilities

This module provides comprehensive memory monitoring and management capabilities
for the Connect telecommunications data processing system.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import gc
import os
import psutil
import threading
import time
import logging
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from contextlib import contextmanager


class MemoryError(Exception):
    """Base exception for memory-related errors."""
    pass


class MemoryLimitExceededError(MemoryError):
    """Exception raised when memory limit is exceeded."""
    pass


@dataclass
class MemorySnapshot:
    """Memory usage snapshot."""
    timestamp: datetime
    process_memory_mb: float
    system_memory_mb: float
    system_memory_percent: float
    available_memory_mb: float
    gc_objects: int
    gc_collections: Dict[int, int] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MemoryStats:
    """Memory usage statistics."""
    min_memory_mb: float
    max_memory_mb: float
    avg_memory_mb: float
    current_memory_mb: float
    peak_memory_mb: float
    memory_growth_mb: float
    gc_collections_total: int
    monitoring_duration_seconds: float
    snapshots_count: int
    metadata: Dict[str, Any] = field(default_factory=dict)


class MemoryMonitor:
    """Advanced memory monitoring and management system.
    
    Features:
    - Real-time memory usage tracking
    - Memory limit enforcement
    - Automatic garbage collection
    - Memory leak detection
    - Performance impact monitoring
    - Memory usage statistics and reporting
    - Context-based memory tracking
    """
    
    def __init__(
        self,
        memory_limit_mb: Optional[float] = None,
        warning_threshold: float = 0.8,
        critical_threshold: float = 0.95,
        auto_gc: bool = True,
        gc_threshold: float = 0.7,
        monitoring_interval: float = 1.0,
        max_snapshots: int = 1000
    ):
        """Initialize memory monitor.
        
        Args:
            memory_limit_mb: Maximum memory limit in MB (None for no limit)
            warning_threshold: Warning threshold as fraction of limit
            critical_threshold: Critical threshold as fraction of limit
            auto_gc: Enable automatic garbage collection
            gc_threshold: GC trigger threshold as fraction of limit
            monitoring_interval: Monitoring interval in seconds
            max_snapshots: Maximum number of snapshots to keep
        """
        self.memory_limit_mb = memory_limit_mb
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.auto_gc = auto_gc
        self.gc_threshold = gc_threshold
        self.monitoring_interval = monitoring_interval
        self.max_snapshots = max_snapshots
        
        self.logger = logging.getLogger(__name__)
        self.process = psutil.Process()
        
        # Monitoring state
        self._snapshots: List[MemorySnapshot] = []
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
        
        # Callbacks
        self._warning_callbacks: List[Callable[[MemorySnapshot], None]] = []
        self._critical_callbacks: List[Callable[[MemorySnapshot], None]] = []
        self._limit_callbacks: List[Callable[[MemorySnapshot], None]] = []
        
        # Statistics
        self._start_time = time.time()
        self._peak_memory_mb = 0.0
        self._initial_memory_mb = self.get_memory_usage()
    
    def get_memory_usage(self) -> float:
        """Get current process memory usage in MB.
        
        Returns:
            Memory usage in MB
        """
        try:
            memory_info = self.process.memory_info()
            return memory_info.rss / (1024 * 1024)  # Convert to MB
        except Exception as e:
            self.logger.warning(f"Failed to get memory usage: {e}")
            return 0.0
    
    def get_system_memory_info(self) -> Dict[str, float]:
        """Get system memory information.
        
        Returns:
            Dictionary with system memory info in MB
        """
        try:
            memory = psutil.virtual_memory()
            return {
                'total_mb': memory.total / (1024 * 1024),
                'available_mb': memory.available / (1024 * 1024),
                'used_mb': memory.used / (1024 * 1024),
                'percent': memory.percent,
                'free_mb': memory.free / (1024 * 1024)
            }
        except Exception as e:
            self.logger.warning(f"Failed to get system memory info: {e}")
            return {}
    
    def take_snapshot(self, metadata: Optional[Dict[str, Any]] = None) -> MemorySnapshot:
        """Take a memory usage snapshot.
        
        Args:
            metadata: Additional metadata to include
            
        Returns:
            Memory snapshot
        """
        process_memory = self.get_memory_usage()
        system_info = self.get_system_memory_info()
        
        # Get garbage collection stats
        gc_stats = {}
        for i in range(3):  # Python has 3 GC generations
            gc_stats[i] = gc.get_count()[i]
        
        snapshot = MemorySnapshot(
            timestamp=datetime.now(),
            process_memory_mb=process_memory,
            system_memory_mb=system_info.get('used_mb', 0),
            system_memory_percent=system_info.get('percent', 0),
            available_memory_mb=system_info.get('available_mb', 0),
            gc_objects=len(gc.get_objects()),
            gc_collections=gc_stats,
            metadata=metadata or {}
        )
        
        # Update peak memory
        self._peak_memory_mb = max(self._peak_memory_mb, process_memory)
        
        # Store snapshot
        with self._lock:
            self._snapshots.append(snapshot)
            
            # Limit snapshots
            if len(self._snapshots) > self.max_snapshots:
                self._snapshots = self._snapshots[-self.max_snapshots:]
        
        # Check thresholds
        self._check_thresholds(snapshot)
        
        return snapshot
    
    def start_monitoring(self) -> None:
        """Start continuous memory monitoring."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        
        self.logger.info("Memory monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop continuous memory monitoring."""
        self._monitoring = False
        
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
            self._monitor_thread = None
        
        self.logger.info("Memory monitoring stopped")
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while self._monitoring:
            try:
                self.take_snapshot()
                time.sleep(self.monitoring_interval)
            except Exception as e:
                self.logger.error(f"Error in memory monitoring loop: {e}")
                time.sleep(self.monitoring_interval)
    
    def _check_thresholds(self, snapshot: MemorySnapshot) -> None:
        """Check memory thresholds and trigger callbacks.
        
        Args:
            snapshot: Memory snapshot to check
        """
        if not self.memory_limit_mb:
            return
        
        usage_ratio = snapshot.process_memory_mb / self.memory_limit_mb
        
        # Check for limit exceeded
        if usage_ratio >= 1.0:
            self.logger.critical(
                f"Memory limit exceeded: {snapshot.process_memory_mb:.1f}MB / {self.memory_limit_mb:.1f}MB"
            )
            for callback in self._limit_callbacks:
                try:
                    callback(snapshot)
                except Exception as e:
                    self.logger.error(f"Error in limit callback: {e}")
            
            if self.auto_gc:
                self.force_garbage_collection()
        
        # Check for critical threshold
        elif usage_ratio >= self.critical_threshold:
            self.logger.warning(
                f"Critical memory usage: {snapshot.process_memory_mb:.1f}MB / {self.memory_limit_mb:.1f}MB "
                f"({usage_ratio:.1%})"
            )
            for callback in self._critical_callbacks:
                try:
                    callback(snapshot)
                except Exception as e:
                    self.logger.error(f"Error in critical callback: {e}")
            
            if self.auto_gc:
                self.force_garbage_collection()
        
        # Check for warning threshold
        elif usage_ratio >= self.warning_threshold:
            self.logger.info(
                f"High memory usage: {snapshot.process_memory_mb:.1f}MB / {self.memory_limit_mb:.1f}MB "
                f"({usage_ratio:.1%})"
            )
            for callback in self._warning_callbacks:
                try:
                    callback(snapshot)
                except Exception as e:
                    self.logger.error(f"Error in warning callback: {e}")
        
        # Auto GC based on threshold
        if self.auto_gc and usage_ratio >= self.gc_threshold:
            self.force_garbage_collection()
    
    def force_garbage_collection(self) -> Dict[str, int]:
        """Force garbage collection and return statistics.
        
        Returns:
            Dictionary with GC statistics
        """
        before_objects = len(gc.get_objects())
        before_memory = self.get_memory_usage()
        
        # Force collection for all generations
        collected = {}
        for generation in range(3):
            collected[generation] = gc.collect(generation)
        
        after_objects = len(gc.get_objects())
        after_memory = self.get_memory_usage()
        
        freed_objects = before_objects - after_objects
        freed_memory = before_memory - after_memory
        
        stats = {
            'freed_objects': freed_objects,
            'freed_memory_mb': freed_memory,
            'before_objects': before_objects,
            'after_objects': after_objects,
            'before_memory_mb': before_memory,
            'after_memory_mb': after_memory,
            **{f'collected_gen_{gen}': count for gen, count in collected.items()}
        }
        
        self.logger.debug(
            f"Garbage collection: freed {freed_objects} objects, {freed_memory:.1f}MB memory"
        )
        
        return stats
    
    def check_memory_limit(self, raise_on_exceed: bool = True) -> bool:
        """Check if memory limit is exceeded.
        
        Args:
            raise_on_exceed: Whether to raise exception on limit exceeded
            
        Returns:
            True if within limit, False if exceeded
            
        Raises:
            MemoryLimitExceededError: If limit exceeded and raise_on_exceed is True
        """
        if not self.memory_limit_mb:
            return True
        
        current_memory = self.get_memory_usage()
        
        if current_memory > self.memory_limit_mb:
            if raise_on_exceed:
                raise MemoryLimitExceededError(
                    f"Memory limit exceeded: {current_memory:.1f}MB > {self.memory_limit_mb:.1f}MB"
                )
            return False
        
        return True
    
    def get_memory_stats(self) -> MemoryStats:
        """Get memory usage statistics.
        
        Returns:
            Memory statistics
        """
        with self._lock:
            snapshots = self._snapshots.copy()
        
        if not snapshots:
            current_memory = self.get_memory_usage()
            return MemoryStats(
                min_memory_mb=current_memory,
                max_memory_mb=current_memory,
                avg_memory_mb=current_memory,
                current_memory_mb=current_memory,
                peak_memory_mb=self._peak_memory_mb,
                memory_growth_mb=current_memory - self._initial_memory_mb,
                gc_collections_total=0,
                monitoring_duration_seconds=time.time() - self._start_time,
                snapshots_count=0
            )
        
        memory_values = [s.process_memory_mb for s in snapshots]
        current_memory = self.get_memory_usage()
        
        # Calculate GC collections
        total_gc = sum(
            sum(s.gc_collections.values()) for s in snapshots
        )
        
        return MemoryStats(
            min_memory_mb=min(memory_values),
            max_memory_mb=max(memory_values),
            avg_memory_mb=sum(memory_values) / len(memory_values),
            current_memory_mb=current_memory,
            peak_memory_mb=self._peak_memory_mb,
            memory_growth_mb=current_memory - self._initial_memory_mb,
            gc_collections_total=total_gc,
            monitoring_duration_seconds=time.time() - self._start_time,
            snapshots_count=len(snapshots)
        )
    
    def get_snapshots(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> List[MemorySnapshot]:
        """Get memory snapshots with optional filtering.
        
        Args:
            start_time: Start time filter
            end_time: End time filter
            limit: Maximum number of snapshots to return
            
        Returns:
            List of memory snapshots
        """
        with self._lock:
            snapshots = self._snapshots.copy()
        
        # Apply time filters
        if start_time:
            snapshots = [s for s in snapshots if s.timestamp >= start_time]
        
        if end_time:
            snapshots = [s for s in snapshots if s.timestamp <= end_time]
        
        # Apply limit
        if limit:
            snapshots = snapshots[-limit:]
        
        return snapshots
    
    def clear_snapshots(self) -> None:
        """Clear all stored snapshots."""
        with self._lock:
            self._snapshots.clear()
    
    def add_warning_callback(self, callback: Callable[[MemorySnapshot], None]) -> None:
        """Add callback for warning threshold.
        
        Args:
            callback: Function to call when warning threshold is reached
        """
        self._warning_callbacks.append(callback)
    
    def add_critical_callback(self, callback: Callable[[MemorySnapshot], None]) -> None:
        """Add callback for critical threshold.
        
        Args:
            callback: Function to call when critical threshold is reached
        """
        self._critical_callbacks.append(callback)
    
    def add_limit_callback(self, callback: Callable[[MemorySnapshot], None]) -> None:
        """Add callback for memory limit exceeded.
        
        Args:
            callback: Function to call when memory limit is exceeded
        """
        self._limit_callbacks.append(callback)
    
    @contextmanager
    def memory_context(self, name: str = "operation", auto_gc: bool = True):
        """Context manager for memory tracking.
        
        Args:
            name: Name of the operation
            auto_gc: Whether to run GC after operation
            
        Yields:
            Memory context information
        """
        start_snapshot = self.take_snapshot({'operation': name, 'phase': 'start'})
        start_time = time.time()
        
        try:
            yield {
                'start_memory_mb': start_snapshot.process_memory_mb,
                'monitor': self
            }
        finally:
            end_time = time.time()
            
            if auto_gc:
                gc_stats = self.force_garbage_collection()
            else:
                gc_stats = {}
            
            end_snapshot = self.take_snapshot({
                'operation': name,
                'phase': 'end',
                'duration_seconds': end_time - start_time,
                'gc_stats': gc_stats
            })
            
            memory_delta = end_snapshot.process_memory_mb - start_snapshot.process_memory_mb
            
            self.logger.info(
                f"Memory context '{name}': {memory_delta:+.1f}MB change, "
                f"{end_time - start_time:.2f}s duration"
            )
    
    def estimate_memory_for_dataframe(self, df_info: Dict[str, Any]) -> float:
        """Estimate memory usage for a DataFrame.
        
        Args:
            df_info: DataFrame information (rows, columns, dtypes)
            
        Returns:
            Estimated memory usage in MB
        """
        rows = df_info.get('rows', 0)
        columns = df_info.get('columns', 0)
        dtypes = df_info.get('dtypes', {})
        
        # Estimate bytes per value based on dtype
        dtype_sizes = {
            'int8': 1, 'int16': 2, 'int32': 4, 'int64': 8,
            'uint8': 1, 'uint16': 2, 'uint32': 4, 'uint64': 8,
            'float16': 2, 'float32': 4, 'float64': 8,
            'bool': 1, 'datetime64': 8, 'timedelta64': 8,
            'object': 50,  # Estimate for strings/objects
            'category': 4   # Estimate for categorical
        }
        
        total_bytes = 0
        for dtype in dtypes.values():
            dtype_str = str(dtype).lower()
            
            # Find matching dtype size
            size = dtype_sizes.get(dtype_str, 50)  # Default to object size
            for key, value in dtype_sizes.items():
                if key in dtype_str:
                    size = value
                    break
            
            total_bytes += rows * size
        
        # Add overhead (index, metadata, etc.)
        overhead_factor = 1.2
        total_bytes *= overhead_factor
        
        return total_bytes / (1024 * 1024)  # Convert to MB
    
    def __enter__(self):
        """Context manager entry."""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_monitoring()