#!/usr/bin/env python3
"""
E2E测试性能监控工具

该模块提供:
1. 系统资源监控（CPU、内存、磁盘、网络）
2. 数据库性能监控
3. API响应时间监控
4. 自定义性能指标收集
5. 性能报告生成

使用方法:
    from tests.e2e.utils.performance_monitor import PerformanceMonitor
    
    monitor = PerformanceMonitor()
    with monitor.monitor_context("test_data_import"):
        # 执行测试代码
        pass
    
    report = monitor.generate_report()
"""

import os
import sys
import time
import threading
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from contextlib import contextmanager
import logging
from dataclasses import dataclass, asdict
from collections import defaultdict, deque

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    import psutil
    import psycopg2
    import requests
    import numpy as np
except ImportError as e:
    print(f"警告: 缺少可选依赖包: {e}")
    psutil = None
    psycopg2 = None
    requests = None
    np = None

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """系统性能指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_read_mb: float
    disk_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    process_count: int
    load_average: Optional[List[float]] = None


@dataclass
class DatabaseMetrics:
    """数据库性能指标"""
    timestamp: datetime
    active_connections: int
    idle_connections: int
    total_connections: int
    queries_per_second: float
    avg_query_time_ms: float
    slow_queries_count: int
    cache_hit_ratio: float
    deadlocks_count: int
    table_scans_count: int
    index_scans_count: int


@dataclass
class APIMetrics:
    """API性能指标"""
    timestamp: datetime
    endpoint: str
    method: str
    response_time_ms: float
    status_code: int
    request_size_bytes: int
    response_size_bytes: int
    error_message: Optional[str] = None


@dataclass
class CustomMetrics:
    """自定义性能指标"""
    timestamp: datetime
    metric_name: str
    metric_value: float
    metric_unit: str
    tags: Dict[str, str]


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, 
                 monitoring_interval: float = 1.0,
                 max_history_size: int = 10000,
                 database_url: Optional[str] = None):
        """初始化性能监控器
        
        Args:
            monitoring_interval: 监控间隔（秒）
            max_history_size: 最大历史记录数量
            database_url: 数据库连接URL
        """
        self.monitoring_interval = monitoring_interval
        self.max_history_size = max_history_size
        self.database_url = database_url
        
        # 性能数据存储
        self.system_metrics: deque = deque(maxlen=max_history_size)
        self.database_metrics: deque = deque(maxlen=max_history_size)
        self.api_metrics: deque = deque(maxlen=max_history_size)
        self.custom_metrics: deque = deque(maxlen=max_history_size)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.start_time: Optional[datetime] = None
        
        # 性能阈值
        self.thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage_percent': 90.0,
            'api_response_time_ms': 1000.0,
            'db_query_time_ms': 500.0
        }
        
        # 初始化系统监控
        if psutil:
            self.process = psutil.Process()
            self.initial_disk_io = psutil.disk_io_counters()
            self.initial_network_io = psutil.net_io_counters()
        else:
            self.process = None
            self.initial_disk_io = None
            self.initial_network_io = None
        
        logger.info("性能监控器初始化完成")
    
    def start_monitoring(self):
        """开始性能监控"""
        if self.is_monitoring:
            logger.warning("性能监控已在运行")
            return
        
        self.is_monitoring = True
        self.start_time = datetime.now()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        if not self.is_monitoring:
            logger.warning("性能监控未在运行")
            return
        
        self.is_monitoring = False
        
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        
        logger.info("性能监控已停止")
    
    @contextmanager
    def monitor_context(self, context_name: str = "test"):
        """性能监控上下文管理器
        
        Args:
            context_name: 上下文名称，用于标识监控会话
        """
        logger.info(f"开始监控上下文: {context_name}")
        
        # 记录开始时间
        start_time = time.time()
        self.record_custom_metric("context_start", 1.0, "count", {"context": context_name})
        
        # 开始监控
        self.start_monitoring()
        
        try:
            yield self
        finally:
            # 停止监控
            self.stop_monitoring()
            
            # 记录结束时间和持续时间
            end_time = time.time()
            duration = end_time - start_time
            
            self.record_custom_metric("context_end", 1.0, "count", {"context": context_name})
            self.record_custom_metric("context_duration", duration, "seconds", {"context": context_name})
            
            logger.info(f"监控上下文结束: {context_name}, 持续时间: {duration:.2f}秒")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 收集系统指标
                self._collect_system_metrics()
                
                # 收集数据库指标
                if self.database_url:
                    self._collect_database_metrics()
                
                # 等待下一次监控
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"监控循环出错: {e}")
                time.sleep(self.monitoring_interval)
    
    def _collect_system_metrics(self):
        """收集系统性能指标"""
        if not psutil:
            return
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=None)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk_usage = psutil.disk_usage('/')
            current_disk_io = psutil.disk_io_counters()
            
            # 网络使用情况
            current_network_io = psutil.net_io_counters()
            
            # 进程数量
            process_count = len(psutil.pids())
            
            # 负载平均值（仅Linux/Unix）
            load_average = None
            if hasattr(os, 'getloadavg'):
                try:
                    load_average = list(os.getloadavg())
                except OSError:
                    pass
            
            # 计算磁盘I/O增量
            disk_read_mb = 0
            disk_write_mb = 0
            if self.initial_disk_io and current_disk_io:
                disk_read_mb = (current_disk_io.read_bytes - self.initial_disk_io.read_bytes) / 1024 / 1024
                disk_write_mb = (current_disk_io.write_bytes - self.initial_disk_io.write_bytes) / 1024 / 1024
            
            # 计算网络I/O增量
            network_sent_mb = 0
            network_recv_mb = 0
            if self.initial_network_io and current_network_io:
                network_sent_mb = (current_network_io.bytes_sent - self.initial_network_io.bytes_sent) / 1024 / 1024
                network_recv_mb = (current_network_io.bytes_recv - self.initial_network_io.bytes_recv) / 1024 / 1024
            
            # 创建系统指标对象
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                memory_available_mb=memory.available / 1024 / 1024,
                disk_usage_percent=disk_usage.percent,
                disk_read_mb=disk_read_mb,
                disk_write_mb=disk_write_mb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                process_count=process_count,
                load_average=load_average
            )
            
            self.system_metrics.append(metrics)
            
            # 检查阈值
            self._check_system_thresholds(metrics)
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
    
    def _collect_database_metrics(self):
        """收集数据库性能指标"""
        if not psycopg2 or not self.database_url:
            return
        
        try:
            with psycopg2.connect(self.database_url) as conn:
                with conn.cursor() as cursor:
                    # 连接数统计
                    cursor.execute("""
                        SELECT state, count(*) 
                        FROM pg_stat_activity 
                        WHERE datname = current_database()
                        GROUP BY state
                    """)
                    
                    connection_stats = dict(cursor.fetchall())
                    active_connections = connection_stats.get('active', 0)
                    idle_connections = connection_stats.get('idle', 0)
                    total_connections = sum(connection_stats.values())
                    
                    # 查询统计
                    cursor.execute("""
                        SELECT 
                            sum(calls) as total_calls,
                            sum(total_time) as total_time,
                            sum(calls) / EXTRACT(EPOCH FROM (now() - stats_reset)) as qps
                        FROM pg_stat_statements 
                        WHERE dbid = (SELECT oid FROM pg_database WHERE datname = current_database())
                    """)
                    
                    query_stats = cursor.fetchone()
                    if query_stats and query_stats[0]:
                        queries_per_second = float(query_stats[2] or 0)
                        avg_query_time_ms = float(query_stats[1] or 0) / float(query_stats[0] or 1)
                    else:
                        queries_per_second = 0.0
                        avg_query_time_ms = 0.0
                    
                    # 慢查询统计
                    cursor.execute("""
                        SELECT count(*) 
                        FROM pg_stat_statements 
                        WHERE mean_time > 1000 
                        AND dbid = (SELECT oid FROM pg_database WHERE datname = current_database())
                    """)
                    
                    slow_queries_count = cursor.fetchone()[0] or 0
                    
                    # 缓存命中率
                    cursor.execute("""
                        SELECT 
                            sum(blks_hit) / (sum(blks_hit) + sum(blks_read)) * 100 as cache_hit_ratio
                        FROM pg_stat_database 
                        WHERE datname = current_database()
                    """)
                    
                    cache_hit_ratio = float(cursor.fetchone()[0] or 0)
                    
                    # 死锁统计
                    cursor.execute("""
                        SELECT deadlocks 
                        FROM pg_stat_database 
                        WHERE datname = current_database()
                    """)
                    
                    deadlocks_count = cursor.fetchone()[0] or 0
                    
                    # 扫描统计
                    cursor.execute("""
                        SELECT 
                            sum(seq_scan) as table_scans,
                            sum(idx_scan) as index_scans
                        FROM pg_stat_user_tables
                    """)
                    
                    scan_stats = cursor.fetchone()
                    table_scans_count = scan_stats[0] or 0
                    index_scans_count = scan_stats[1] or 0
                    
                    # 创建数据库指标对象
                    metrics = DatabaseMetrics(
                        timestamp=datetime.now(),
                        active_connections=active_connections,
                        idle_connections=idle_connections,
                        total_connections=total_connections,
                        queries_per_second=queries_per_second,
                        avg_query_time_ms=avg_query_time_ms,
                        slow_queries_count=slow_queries_count,
                        cache_hit_ratio=cache_hit_ratio,
                        deadlocks_count=deadlocks_count,
                        table_scans_count=table_scans_count,
                        index_scans_count=index_scans_count
                    )
                    
                    self.database_metrics.append(metrics)
                    
                    # 检查数据库阈值
                    self._check_database_thresholds(metrics)
        
        except Exception as e:
            logger.error(f"收集数据库指标失败: {e}")
    
    def record_api_call(self, 
                       endpoint: str,
                       method: str,
                       response_time_ms: float,
                       status_code: int,
                       request_size_bytes: int = 0,
                       response_size_bytes: int = 0,
                       error_message: Optional[str] = None):
        """记录API调用指标
        
        Args:
            endpoint: API端点
            method: HTTP方法
            response_time_ms: 响应时间（毫秒）
            status_code: HTTP状态码
            request_size_bytes: 请求大小（字节）
            response_size_bytes: 响应大小（字节）
            error_message: 错误消息
        """
        metrics = APIMetrics(
            timestamp=datetime.now(),
            endpoint=endpoint,
            method=method,
            response_time_ms=response_time_ms,
            status_code=status_code,
            request_size_bytes=request_size_bytes,
            response_size_bytes=response_size_bytes,
            error_message=error_message
        )
        
        self.api_metrics.append(metrics)
        
        # 检查API阈值
        if response_time_ms > self.thresholds['api_response_time_ms']:
            logger.warning(f"API响应时间超过阈值: {endpoint} {method} - {response_time_ms:.2f}ms")
    
    def record_custom_metric(self,
                           metric_name: str,
                           metric_value: float,
                           metric_unit: str,
                           tags: Optional[Dict[str, str]] = None):
        """记录自定义指标
        
        Args:
            metric_name: 指标名称
            metric_value: 指标值
            metric_unit: 指标单位
            tags: 标签字典
        """
        metrics = CustomMetrics(
            timestamp=datetime.now(),
            metric_name=metric_name,
            metric_value=metric_value,
            metric_unit=metric_unit,
            tags=tags or {}
        )
        
        self.custom_metrics.append(metrics)
        
        logger.debug(f"记录自定义指标: {metric_name}={metric_value}{metric_unit}")
    
    def _check_system_thresholds(self, metrics: SystemMetrics):
        """检查系统性能阈值"""
        if metrics.cpu_percent > self.thresholds['cpu_percent']:
            logger.warning(f"CPU使用率超过阈值: {metrics.cpu_percent:.1f}%")
        
        if metrics.memory_percent > self.thresholds['memory_percent']:
            logger.warning(f"内存使用率超过阈值: {metrics.memory_percent:.1f}%")
        
        if metrics.disk_usage_percent > self.thresholds['disk_usage_percent']:
            logger.warning(f"磁盘使用率超过阈值: {metrics.disk_usage_percent:.1f}%")
    
    def _check_database_thresholds(self, metrics: DatabaseMetrics):
        """检查数据库性能阈值"""
        if metrics.avg_query_time_ms > self.thresholds['db_query_time_ms']:
            logger.warning(f"数据库平均查询时间超过阈值: {metrics.avg_query_time_ms:.2f}ms")
        
        if metrics.cache_hit_ratio < 90.0:
            logger.warning(f"数据库缓存命中率过低: {metrics.cache_hit_ratio:.1f}%")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要
        
        Returns:
            性能摘要字典
        """
        summary = {
            'monitoring_duration': None,
            'system_metrics': {},
            'database_metrics': {},
            'api_metrics': {},
            'custom_metrics': {},
            'alerts': []
        }
        
        # 计算监控持续时间
        if self.start_time:
            duration = datetime.now() - self.start_time
            summary['monitoring_duration'] = duration.total_seconds()
        
        # 系统指标摘要
        if self.system_metrics and np:
            cpu_values = [m.cpu_percent for m in self.system_metrics]
            memory_values = [m.memory_percent for m in self.system_metrics]
            
            summary['system_metrics'] = {
                'cpu_avg': float(np.mean(cpu_values)),
                'cpu_max': float(np.max(cpu_values)),
                'cpu_min': float(np.min(cpu_values)),
                'memory_avg': float(np.mean(memory_values)),
                'memory_max': float(np.max(memory_values)),
                'memory_min': float(np.min(memory_values)),
                'samples_count': len(self.system_metrics)
            }
        
        # 数据库指标摘要
        if self.database_metrics and np:
            query_times = [m.avg_query_time_ms for m in self.database_metrics]
            qps_values = [m.queries_per_second for m in self.database_metrics]
            
            summary['database_metrics'] = {
                'avg_query_time_ms': float(np.mean(query_times)),
                'max_query_time_ms': float(np.max(query_times)),
                'avg_qps': float(np.mean(qps_values)),
                'max_qps': float(np.max(qps_values)),
                'samples_count': len(self.database_metrics)
            }
        
        # API指标摘要
        if self.api_metrics and np:
            response_times = [m.response_time_ms for m in self.api_metrics]
            status_codes = [m.status_code for m in self.api_metrics]
            
            summary['api_metrics'] = {
                'avg_response_time_ms': float(np.mean(response_times)),
                'max_response_time_ms': float(np.max(response_times)),
                'min_response_time_ms': float(np.min(response_times)),
                'total_requests': len(self.api_metrics),
                'success_rate': len([s for s in status_codes if 200 <= s < 300]) / len(status_codes) * 100,
                'error_rate': len([s for s in status_codes if s >= 400]) / len(status_codes) * 100
            }
        
        # 自定义指标摘要
        if self.custom_metrics:
            custom_summary = defaultdict(list)
            for metric in self.custom_metrics:
                custom_summary[metric.metric_name].append(metric.metric_value)
            
            summary['custom_metrics'] = {
                name: {
                    'avg': float(np.mean(values)) if np else sum(values) / len(values),
                    'max': float(np.max(values)) if np else max(values),
                    'min': float(np.min(values)) if np else min(values),
                    'count': len(values)
                }
                for name, values in custom_summary.items()
            }
        
        return summary
    
    def generate_report(self, output_file: Optional[str] = None) -> Dict[str, Any]:
        """生成性能报告
        
        Args:
            output_file: 输出文件路径（可选）
        
        Returns:
            性能报告字典
        """
        logger.info("生成性能报告")
        
        report = {
            'report_timestamp': datetime.now().isoformat(),
            'monitoring_info': {
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'monitoring_interval': self.monitoring_interval,
                'thresholds': self.thresholds
            },
            'performance_summary': self.get_performance_summary(),
            'detailed_metrics': {
                'system_metrics_count': len(self.system_metrics),
                'database_metrics_count': len(self.database_metrics),
                'api_metrics_count': len(self.api_metrics),
                'custom_metrics_count': len(self.custom_metrics)
            },
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告到文件
        if output_file:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"性能报告已保存到: {output_path}")
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成性能优化建议
        
        Returns:
            建议列表
        """
        recommendations = []
        summary = self.get_performance_summary()
        
        # 系统性能建议
        if 'system_metrics' in summary and summary['system_metrics']:
            sys_metrics = summary['system_metrics']
            
            if sys_metrics.get('cpu_avg', 0) > 70:
                recommendations.append("CPU使用率较高，建议优化算法或增加并行处理")
            
            if sys_metrics.get('memory_avg', 0) > 80:
                recommendations.append("内存使用率较高，建议优化内存使用或增加内存容量")
        
        # 数据库性能建议
        if 'database_metrics' in summary and summary['database_metrics']:
            db_metrics = summary['database_metrics']
            
            if db_metrics.get('avg_query_time_ms', 0) > 100:
                recommendations.append("数据库查询时间较长，建议添加索引或优化查询")
            
            if db_metrics.get('avg_qps', 0) > 1000:
                recommendations.append("数据库查询频率较高，建议使用缓存或读写分离")
        
        # API性能建议
        if 'api_metrics' in summary and summary['api_metrics']:
            api_metrics = summary['api_metrics']
            
            if api_metrics.get('avg_response_time_ms', 0) > 500:
                recommendations.append("API响应时间较长，建议优化业务逻辑或使用缓存")
            
            if api_metrics.get('error_rate', 0) > 5:
                recommendations.append("API错误率较高，建议检查错误处理和输入验证")
        
        if not recommendations:
            recommendations.append("系统性能表现良好，无特殊优化建议")
        
        return recommendations
    
    def export_metrics_csv(self, output_dir: str):
        """导出指标数据为CSV文件
        
        Args:
            output_dir: 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        try:
            import pandas as pd
            
            # 导出系统指标
            if self.system_metrics:
                system_data = [asdict(m) for m in self.system_metrics]
                df_system = pd.DataFrame(system_data)
                df_system.to_csv(output_path / "system_metrics.csv", index=False)
            
            # 导出数据库指标
            if self.database_metrics:
                db_data = [asdict(m) for m in self.database_metrics]
                df_db = pd.DataFrame(db_data)
                df_db.to_csv(output_path / "database_metrics.csv", index=False)
            
            # 导出API指标
            if self.api_metrics:
                api_data = [asdict(m) for m in self.api_metrics]
                df_api = pd.DataFrame(api_data)
                df_api.to_csv(output_path / "api_metrics.csv", index=False)
            
            # 导出自定义指标
            if self.custom_metrics:
                custom_data = [asdict(m) for m in self.custom_metrics]
                df_custom = pd.DataFrame(custom_data)
                df_custom.to_csv(output_path / "custom_metrics.csv", index=False)
            
            logger.info(f"指标数据已导出到: {output_path}")
            
        except ImportError:
            logger.warning("pandas未安装，无法导出CSV文件")
    
    def clear_metrics(self):
        """清空所有指标数据"""
        self.system_metrics.clear()
        self.database_metrics.clear()
        self.api_metrics.clear()
        self.custom_metrics.clear()
        
        logger.info("所有指标数据已清空")


class APIPerformanceDecorator:
    """API性能监控装饰器"""
    
    def __init__(self, monitor: PerformanceMonitor):
        self.monitor = monitor
    
    def __call__(self, endpoint: str, method: str = "GET"):
        def decorator(func: Callable):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                error_message = None
                status_code = 200
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    error_message = str(e)
                    status_code = 500
                    raise
                finally:
                    end_time = time.time()
                    response_time_ms = (end_time - start_time) * 1000
                    
                    self.monitor.record_api_call(
                        endpoint=endpoint,
                        method=method,
                        response_time_ms=response_time_ms,
                        status_code=status_code,
                        error_message=error_message
                    )
            
            return wrapper
        return decorator


if __name__ == "__main__":
    # 示例用法
    monitor = PerformanceMonitor()
    
    # 使用上下文管理器
    with monitor.monitor_context("example_test"):
        # 模拟一些工作
        time.sleep(2)
        
        # 记录自定义指标
        monitor.record_custom_metric("test_metric", 42.0, "units")
        
        # 记录API调用
        monitor.record_api_call("/api/test", "GET", 150.0, 200)
    
    # 生成报告
    report = monitor.generate_report("performance_report.json")
    print("性能报告生成完成")