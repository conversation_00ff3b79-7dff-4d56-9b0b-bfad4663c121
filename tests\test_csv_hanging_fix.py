#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to reproduce and verify the CSV hanging issue fix.

This script tests the CSV processor's ability to handle:
1. Windows temporary files with 8.3 naming format
2. Large files that could cause hanging
3. Files with encoding issues
4. Timeout scenarios
5. Fallback mechanisms
"""

import asyncio
import logging
import os
import tempfile
import time
from pathlib import Path
from typing import List, Tuple

import pandas as pd
import pytest

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

# Import the fixed CSV processor
try:
    from src.database.etl.processors.csv_processor import read_csv_to_dataframe
except ImportError:
    logger.error("Could not import csv_processor. Make sure you're running from the project root.")
    raise


class CSVHangingTestSuite:
    """Test suite for CSV hanging issue verification."""
    
    def __init__(self):
        self.test_results: List[Tuple[str, bool, str]] = []
        self.temp_files: List[Path] = []
    
    def cleanup(self):
        """Clean up temporary test files."""
        for temp_file in self.temp_files:
            try:
                if temp_file.exists():
                    temp_file.unlink()
                    logger.debug(f"Cleaned up temp file: {temp_file}")
            except Exception as e:
                logger.warning(f"Could not clean up {temp_file}: {e}")
    
    def create_test_csv(self, content: str, suffix: str = ".csv") -> Path:
        """Create a temporary CSV file with given content."""
        with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False, encoding='utf-8') as f:
            f.write(content)
            temp_path = Path(f.name)
            self.temp_files.append(temp_path)
            return temp_path
    
    def create_large_csv(self, rows: int = 100000) -> Path:
        """Create a large CSV file for testing."""
        content = "id,name,value,description\n"
        for i in range(rows):
            content += f"{i},test_name_{i},{i * 1.5},Description for row {i} with some longer text\n"
        
        return self.create_test_csv(content)
    
    def create_problematic_csv(self) -> Path:
        """Create a CSV with encoding and parsing issues."""
        content = '''id,name,value,notes
1,"Test, Name",123,"Normal note"
2,Problematic"Quote,456,"Note with \"embedded quotes\""
3,Unicode Test,789,"Note with unicode: ñáéíóú"
4,"Multiline\nNote",101,"This has\na line break"
5,Empty Value,,999,
'''
        return self.create_test_csv(content)
    
    async def test_basic_functionality(self) -> bool:
        """Test basic CSV reading functionality."""
        test_name = "Basic CSV Reading"
        try:
            # Create simple test CSV
            content = "id,name,value\n1,test,123\n2,test2,456\n"
            temp_file = self.create_test_csv(content)
            
            # Test reading
            start_time = time.time()
            df = await read_csv_to_dataframe(temp_file)
            duration = time.time() - start_time
            
            # Verify results
            assert len(df) == 2
            assert list(df.columns) == ['id', 'name', 'value']
            assert duration < 5.0  # Should complete quickly
            
            self.test_results.append((test_name, True, f"Completed in {duration:.2f}s"))
            return True
            
        except Exception as e:
            self.test_results.append((test_name, False, str(e)))
            return False
    
    async def test_windows_temp_file(self) -> bool:
        """Test reading from Windows temporary directory."""
        test_name = "Windows Temp File"
        try:
            # Create file in Windows temp directory
            temp_dir = Path(tempfile.gettempdir())
            content = "id,name,value\n1,temp_test,789\n"
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, dir=temp_dir) as f:
                f.write(content)
                temp_file = Path(f.name)
                self.temp_files.append(temp_file)
            
            logger.info(f"Testing Windows temp file: {temp_file}")
            
            # Test reading with timeout
            start_time = time.time()
            df = await asyncio.wait_for(read_csv_to_dataframe(temp_file), timeout=30.0)
            duration = time.time() - start_time
            
            # Verify results
            assert len(df) == 1
            assert df.iloc[0]['name'] == 'temp_test'
            
            self.test_results.append((test_name, True, f"Completed in {duration:.2f}s"))
            return True
            
        except asyncio.TimeoutError:
            self.test_results.append((test_name, False, "Timeout - hanging issue still present"))
            return False
        except Exception as e:
            self.test_results.append((test_name, False, str(e)))
            return False
    
    async def test_large_file_handling(self) -> bool:
        """Test handling of large CSV files."""
        test_name = "Large File Handling"
        try:
            # Create large CSV (about 10MB)
            temp_file = self.create_large_csv(100000)
            file_size = temp_file.stat().st_size
            logger.info(f"Testing large file: {file_size} bytes")
            
            # Test reading with timeout
            start_time = time.time()
            df = await asyncio.wait_for(read_csv_to_dataframe(temp_file), timeout=60.0)
            duration = time.time() - start_time
            
            # Verify results
            assert len(df) == 100000
            assert len(df.columns) == 4
            
            self.test_results.append((test_name, True, f"Processed {file_size} bytes in {duration:.2f}s"))
            return True
            
        except asyncio.TimeoutError:
            self.test_results.append((test_name, False, "Timeout on large file"))
            return False
        except Exception as e:
            self.test_results.append((test_name, False, str(e)))
            return False
    
    async def test_encoding_fallback(self) -> bool:
        """Test encoding fallback mechanisms."""
        test_name = "Encoding Fallback"
        try:
            # Create file with problematic encoding
            content = "id,name,value\n1,test_ñáéíóú,123\n"
            temp_file = self.create_test_csv(content)
            
            # Test reading
            start_time = time.time()
            df = await read_csv_to_dataframe(temp_file)
            duration = time.time() - start_time
            
            # Verify results
            assert len(df) == 1
            
            self.test_results.append((test_name, True, f"Handled encoding in {duration:.2f}s"))
            return True
            
        except Exception as e:
            self.test_results.append((test_name, False, str(e)))
            return False
    
    async def test_problematic_csv_parsing(self) -> bool:
        """Test parsing of problematic CSV files."""
        test_name = "Problematic CSV Parsing"
        try:
            # Create CSV with parsing challenges
            temp_file = self.create_problematic_csv()
            
            # Test reading
            start_time = time.time()
            df = await read_csv_to_dataframe(temp_file)
            duration = time.time() - start_time
            
            # Verify results (should handle most rows even with issues)
            assert len(df) >= 3  # At least some rows should be parsed
            
            self.test_results.append((test_name, True, f"Parsed problematic CSV in {duration:.2f}s"))
            return True
            
        except Exception as e:
            self.test_results.append((test_name, False, str(e)))
            return False
    
    async def test_concurrent_processing(self) -> bool:
        """Test concurrent CSV processing."""
        test_name = "Concurrent Processing"
        try:
            # Create multiple test files
            files = []
            for i in range(5):
                content = f"id,name,value\n{i},test_{i},{i*100}\n"
                files.append(self.create_test_csv(content, f"_test_{i}.csv"))
            
            # Process concurrently
            start_time = time.time()
            tasks = [read_csv_to_dataframe(f) for f in files]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            duration = time.time() - start_time
            
            # Verify results
            successful = sum(1 for r in results if isinstance(r, pd.DataFrame))
            assert successful == 5
            
            self.test_results.append((test_name, True, f"Processed {successful} files concurrently in {duration:.2f}s"))
            return True
            
        except Exception as e:
            self.test_results.append((test_name, False, str(e)))
            return False
    
    async def run_all_tests(self) -> bool:
        """Run all tests and return overall success."""
        logger.info("Starting CSV hanging fix verification tests...")
        
        tests = [
            self.test_basic_functionality,
            self.test_windows_temp_file,
            self.test_large_file_handling,
            self.test_encoding_fallback,
            self.test_problematic_csv_parsing,
            self.test_concurrent_processing,
        ]
        
        overall_success = True
        
        for test in tests:
            try:
                success = await test()
                if not success:
                    overall_success = False
            except Exception as e:
                logger.error(f"Test {test.__name__} failed with exception: {e}")
                self.test_results.append((test.__name__, False, f"Exception: {e}"))
                overall_success = False
        
        return overall_success
    
    def print_results(self):
        """Print test results summary."""
        print("\n" + "="*80)
        print("CSV HANGING FIX VERIFICATION RESULTS")
        print("="*80)
        
        passed = 0
        failed = 0
        
        for test_name, success, details in self.test_results:
            status = "PASS" if success else "FAIL"
            print(f"{status:4} | {test_name:25} | {details}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print("-"*80)
        print(f"Total: {passed + failed}, Passed: {passed}, Failed: {failed}")
        
        if failed == 0:
            print("\n✅ ALL TESTS PASSED - CSV hanging issue appears to be fixed!")
        else:
            print(f"\n❌ {failed} TESTS FAILED - CSV hanging issue may still exist")
        
        print("="*80)


async def main():
    """Main test execution function."""
    test_suite = CSVHangingTestSuite()
    
    try:
        # Run all tests
        overall_success = await test_suite.run_all_tests()
        
        # Print results
        test_suite.print_results()
        
        # Return appropriate exit code
        return 0 if overall_success else 1
        
    finally:
        # Always cleanup
        test_suite.cleanup()


if __name__ == "__main__":
    import sys
    
    # Run the test suite
    exit_code = asyncio.run(main())
    sys.exit(exit_code)