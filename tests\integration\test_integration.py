"""Integration tests for the Connect database framework.

This module contains integration tests that verify the interaction between
different components of the system as specified in Task 22.
"""

import asyncio
import os
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch, AsyncMock

import numpy as np
import pandas as pd
import pytest
from shapely.geometry import Point, Polygon

from src.config.models import DatabaseConfig
from src.database.connection.session import SessionManager
from src.database.connection.pool import DatabasePoolManager
from src.database.operations.crud import CRUDOperations
from src.database.operations.importer import DataImporter
from src.database.operations.exporter import DataExporter
from src.database.schema.manager import SchemaManager
from src.database.geospatial.processor import GeospatialProcessor
from src.database.monitoring.logger import DatabaseLogger
from src.database.monitoring.health import HealthChecker


@pytest.mark.integration
class TestDatabaseIntegration:
    """Integration tests for database components."""

    @pytest.fixture
    async def integrated_system(self, test_config):
        """Set up integrated database system for testing."""
        config = DatabaseConfig(test_config)
        pool_manager = DatabasePoolManager(config)
        session_manager = SessionManager(pool_manager)
        crud_manager = CRUDOperations(session_manager)
        schema_manager = SchemaManager(session_manager)
        health_checker = HealthChecker(pool_manager)
        logger = DatabaseLogger(config)
        
        system = {
            'config': config,
            'pool_manager': pool_manager,
            'session_manager': session_manager,
            'crud_manager': crud_manager,
            'schema_manager': schema_manager,
            'health_checker': health_checker,
            'logger': logger
        }
        
        yield system
        
        # Cleanup
        if health_checker.is_monitoring():
            await health_checker.stop_monitoring()
        await pool_manager.close()

    @pytest.mark.asyncio
    async def test_connection_pool_integration(self, integrated_system):
        """Test integration between connection pool and session management."""
        pool_manager = integrated_system['pool_manager']
        session_manager = integrated_system['session_manager']
        
        # Test pool initialization
        await pool_manager.initialize()
        assert pool_manager.is_initialized
        
        # Test session creation from pool
        session = await session_manager.get_session()
        assert session is not None
        
        # Test session health
        is_healthy = await session_manager.check_session_health(session)
        assert is_healthy
        
        # Test session return to pool
        await session_manager.return_session(session)
        
        # Test pool statistics
        stats = await pool_manager.get_pool_stats()
        assert 'active_connections' in stats
        assert 'idle_connections' in stats
        assert stats['active_connections'] >= 0
        assert stats['idle_connections'] >= 0

    @pytest.mark.asyncio
    async def test_crud_schema_integration(self, integrated_system):
        """Test integration between CRUD operations and schema management."""
        crud_manager = integrated_system['crud_manager']
        schema_manager = integrated_system['schema_manager']
        
        # Test schema creation
        table_schema = {
            'table_name': 'integration_test',
            'columns': [
                {'name': 'id', 'type': 'INTEGER', 'primary_key': True},
                {'name': 'name', 'type': 'VARCHAR(100)', 'nullable': False},
                {'name': 'value', 'type': 'DECIMAL(10,2)', 'nullable': True},
                {'name': 'created_at', 'type': 'TIMESTAMP', 'default': 'CURRENT_TIMESTAMP'}
            ]
        }
        
        create_result = await schema_manager.create_table(table_schema)
        assert create_result.success
        
        # Test table existence
        table_exists = await schema_manager.table_exists('integration_test', 'public')
        assert table_exists
        
        # Test CRUD operations on created table
        test_data = pd.DataFrame({
            'id': [1, 2, 3],
            'name': ['Test1', 'Test2', 'Test3'],
            'value': [10.5, 20.3, 30.7]
        })
        
        # Insert data
        insert_result = await crud_manager.bulk_insert('integration_test', test_data)
        assert insert_result.success
        assert insert_result.records_processed == len(test_data)
        
        # Select data
        selected_data = await crud_manager.select_all('integration_test')
        assert len(selected_data) == len(test_data)
        
        # Update data
        update_query = "UPDATE integration_test SET value = value * 2 WHERE id = 1"
        update_result = await crud_manager.execute(update_query)
        assert update_result is not None
        
        # Verify update
        updated_data = await crud_manager.execute_query(
            "SELECT * FROM integration_test WHERE id = 1"
        )
        assert len(updated_data) == 1
        assert updated_data.iloc[0]['value'] == 21.0  # 10.5 * 2
        
        # Delete data
        delete_result = await crud_manager.execute(
            "DELETE FROM integration_test WHERE id = 3"
        )
        assert delete_result is not None
        
        # Verify deletion
        remaining_data = await crud_manager.select_all('integration_test')
        assert len(remaining_data) == 2
        
        # Test schema modification
        alter_result = await schema_manager.add_column(
            'integration_test', 'description', 'TEXT'
        )
        assert alter_result.success
        
        # Verify column addition
        table_info = await schema_manager.get_table_info('integration_test')
        column_names = [col['name'] for col in table_info['columns']]
        assert 'description' in column_names
        
        # Cleanup
        drop_result = await schema_manager.drop_table('integration_test')
        assert drop_result.success

    @pytest.mark.asyncio
    async def test_health_monitoring_integration(self, integrated_system):
        """Test integration of health monitoring with database operations."""
        health_checker = integrated_system['health_checker']
        crud_manager = integrated_system['crud_manager']
        logger = integrated_system['logger']
        
        # Test initial health check
        health_status = await health_checker.check_database_health()
        assert health_status['status'] in ['healthy', 'degraded', 'unhealthy']
        assert 'connection_pool' in health_status
        assert 'response_time' in health_status
        
        # Test health monitoring during operations
        test_data = pd.DataFrame({
            'id': range(100),
            'data': [f"test_{i}" for i in range(100)]
        })
        
        # Monitor health before operation
        pre_health = await health_checker.check_database_health()
        
        # Perform database operation
        start_time = time.time()
        insert_result = await crud_manager.bulk_insert('health_test', test_data)
        operation_time = time.time() - start_time
        
        # Monitor health after operation
        post_health = await health_checker.check_database_health()
        
        # Verify operation success
        assert insert_result.success
        
        # Verify health monitoring captured the operation
        assert post_health['response_time'] >= 0
        
        # Test health alerting
        if operation_time > 5.0:  # Slow operation threshold
            logger.warning(f"Slow operation detected: {operation_time:.2f}s")
        
        # Test connection pool health
        pool_health = await health_checker.check_connection_pool_health()
        assert 'active_connections' in pool_health
        assert 'idle_connections' in pool_health
        assert 'max_connections' in pool_health
        
        # Verify pool is within limits
        assert pool_health['active_connections'] <= pool_health['max_connections']
        
        # Test resource monitoring
        resource_usage = await health_checker.get_resource_usage()
        assert 'memory_usage' in resource_usage
        assert 'cpu_usage' in resource_usage
        assert resource_usage['memory_usage'] >= 0
        assert resource_usage['cpu_usage'] >= 0


@pytest.mark.integration
class TestDataProcessingIntegration:
    """Integration tests for data processing workflows."""

    @pytest.fixture
    async def data_processing_system(self, test_config):
        """Set up data processing system for testing."""
        config = DatabaseConfig(test_config)
        pool_manager = DatabasePoolManager(config)
        session_manager = SessionManager(pool_manager)
        crud_manager = CRUDOperations(session_manager)
        data_importer = DataImporter(crud_manager)
        data_exporter = DataExporter(crud_manager)
        geospatial_processor = GeospatialProcessor()
        
        system = {
            'config': config,
            'pool_manager': pool_manager,
            'session_manager': session_manager,
            'crud_manager': crud_manager,
            'data_importer': data_importer,
            'data_exporter': data_exporter,
            'geospatial_processor': geospatial_processor
        }
        
        yield system
        
        # Cleanup
        await pool_manager.close()

    @pytest.mark.asyncio
    async def test_import_export_integration(self, data_processing_system, tmp_path):
        """Test integration between data import and export operations."""
        data_importer = data_processing_system['data_importer']
        data_exporter = data_processing_system['data_exporter']
        
        # Create test data
        original_data = pd.DataFrame({
            'id': range(1, 101),
            'name': [f"Item_{i}" for i in range(1, 101)],
            'category': np.random.choice(['A', 'B', 'C'], 100),
            'value': np.random.uniform(10, 100, 100),
            'date': pd.date_range('2024-01-01', periods=100, freq='1D')
        })
        
        # Save to CSV
        input_file = tmp_path / "test_input.csv"
        original_data.to_csv(input_file, index=False)
        
        # Test import
        import_result = await data_importer.import_csv_data(
            str(input_file), table_name="import_export_test"
        )
        assert import_result.success
        assert import_result.records_processed == len(original_data)
        
        # Test export
        output_file = tmp_path / "test_output.csv"
        export_result = await data_exporter.export_to_csv(
            "import_export_test", str(output_file)
        )
        assert export_result.success
        
        # Verify data integrity
        exported_data = pd.read_csv(output_file)
        assert len(exported_data) == len(original_data)
        
        # Compare data (allowing for minor floating point differences)
        for col in ['id', 'name', 'category']:
            assert exported_data[col].equals(original_data[col])
        
        # Check numeric values with tolerance
        np.testing.assert_allclose(
            exported_data['value'].values,
            original_data['value'].values,
            rtol=1e-10
        )
        
        # Test different export formats
        json_file = tmp_path / "test_output.json"
        json_export_result = await data_exporter.export_to_json(
            "import_export_test", str(json_file)
        )
        assert json_export_result.success
        
        # Verify JSON export
        import json
        with open(json_file, 'r') as f:
            json_data = json.load(f)
        assert len(json_data) == len(original_data)

    @pytest.mark.asyncio
    async def test_geospatial_data_integration(self, data_processing_system, tmp_path):
        """Test integration of geospatial data processing."""
        data_importer = data_processing_system['data_importer']
        geospatial_processor = data_processing_system['geospatial_processor']
        crud_manager = data_processing_system['crud_manager']
        
        # Create geospatial test data
        geo_data = pd.DataFrame({
            'site_id': ['SITE_001', 'SITE_002', 'SITE_003', 'SITE_004'],
            'latitude': [52.5200, 52.5300, 52.4800, 52.4500],
            'longitude': [13.4050, 13.4150, 13.3500, 13.3000],
            'coverage_radius': [1000, 1500, 2000, 2500],
            'technology': ['LTE', '5G', 'LTE', '5G']
        })
        
        geo_file = tmp_path / "geo_test.csv"
        geo_data.to_csv(geo_file, index=False)
        
        # Import geospatial data
        import_result = await data_importer.import_csv_data(
            str(geo_file), table_name="geo_sites"
        )
        assert import_result.success
        
        # Retrieve data for processing
        imported_data = await crud_manager.select_all("geo_sites")
        
        # Test geospatial operations
        site_points = []
        coverage_areas = []
        
        for _, row in imported_data.iterrows():
            # Create point geometry
            point = Point(row['longitude'], row['latitude'])
            site_points.append({
                'site_id': row['site_id'],
                'point': point,
                'technology': row['technology']
            })
            
            # Create coverage area
            coverage = geospatial_processor.create_buffer(
                point, row['coverage_radius']
            )
            coverage_areas.append({
                'site_id': row['site_id'],
                'coverage': coverage,
                'area_km2': coverage.area / 1000000  # Convert to km²
            })
        
        # Test distance calculations
        distances = []
        for i, site1 in enumerate(site_points):
            for j, site2 in enumerate(site_points[i+1:], i+1):
                distance = geospatial_processor.calculate_distance(
                    site1['point'], site2['point']
                )
                distances.append({
                    'site1': site1['site_id'],
                    'site2': site2['site_id'],
                    'distance_m': distance
                })
        
        assert len(distances) == 6  # 4 sites = 6 pairs
        assert all(d['distance_m'] > 0 for d in distances)
        
        # Test coverage overlap analysis
        overlaps = []
        for i, area1 in enumerate(coverage_areas):
            for j, area2 in enumerate(coverage_areas[i+1:], i+1):
                if area1['coverage'].intersects(area2['coverage']):
                    intersection = area1['coverage'].intersection(area2['coverage'])
                    overlap_area = intersection.area / 1000000  # km²
                    overlaps.append({
                        'site1': area1['site_id'],
                        'site2': area2['site_id'],
                        'overlap_km2': overlap_area
                    })
        
        # Test point-in-polygon queries
        test_points = [
            Point(13.4100, 52.5250),  # Should be in coverage
            Point(13.2000, 52.4000),  # Should be outside coverage
            Point(13.4000, 52.5000)   # Might be in coverage
        ]
        
        coverage_results = []
        for i, test_point in enumerate(test_points):
            covered_by = []
            for area in coverage_areas:
                if area['coverage'].contains(test_point):
                    covered_by.append(area['site_id'])
            
            coverage_results.append({
                'point_id': i,
                'longitude': test_point.x,
                'latitude': test_point.y,
                'covered_by': covered_by,
                'coverage_count': len(covered_by)
            })
        
        # Store geospatial analysis results
        distances_df = pd.DataFrame(distances)
        distances_file = tmp_path / "site_distances.csv"
        distances_df.to_csv(distances_file, index=False)
        
        if overlaps:
            overlaps_df = pd.DataFrame(overlaps)
            overlaps_file = tmp_path / "coverage_overlaps.csv"
            overlaps_df.to_csv(overlaps_file, index=False)
        
        coverage_df = pd.DataFrame(coverage_results)
        coverage_file = tmp_path / "coverage_analysis.csv"
        coverage_df.to_csv(coverage_file, index=False)
        
        # Assertions
        assert len(site_points) == len(geo_data)
        assert len(coverage_areas) == len(geo_data)
        assert all(area['area_km2'] > 0 for area in coverage_areas)
        assert len(coverage_results) == len(test_points)

    @pytest.mark.asyncio
    async def test_batch_processing_integration(self, data_processing_system, tmp_path):
        """Test integration of batch data processing operations."""
        data_importer = data_processing_system['data_importer']
        crud_manager = data_processing_system['crud_manager']
        
        # Create multiple data files for batch processing
        batch_files = []
        total_records = 0
        
        for batch_id in range(5):
            batch_data = pd.DataFrame({
                'batch_id': [batch_id] * 200,
                'record_id': range(batch_id * 200, (batch_id + 1) * 200),
                'value': np.random.uniform(0, 100, 200),
                'category': np.random.choice(['X', 'Y', 'Z'], 200),
                'timestamp': pd.date_range(
                    f'2024-01-{batch_id + 1:02d}', periods=200, freq='1H'
                )
            })
            
            batch_file = tmp_path / f"batch_{batch_id}.csv"
            batch_data.to_csv(batch_file, index=False)
            batch_files.append(str(batch_file))
            total_records += len(batch_data)
        
        # Test batch import
        import_results = []
        for i, file_path in enumerate(batch_files):
            table_name = f"batch_data_{i}"
            result = await data_importer.import_csv_data(file_path, table_name)
            import_results.append({
                'batch_id': i,
                'table_name': table_name,
                'success': result.success,
                'records': result.records_processed
            })
        
        # Verify all imports succeeded
        assert all(r['success'] for r in import_results)
        assert sum(r['records'] for r in import_results) == total_records
        
        # Test cross-batch analysis
        all_data = []
        for result in import_results:
            table_data = await crud_manager.select_all(result['table_name'])
            all_data.append(table_data)
        
        # Combine all batch data
        combined_data = pd.concat(all_data, ignore_index=True)
        assert len(combined_data) == total_records
        
        # Test aggregation across batches
        batch_summary = combined_data.groupby('batch_id').agg({
            'record_id': 'count',
            'value': ['mean', 'sum', 'std'],
            'category': lambda x: x.value_counts().to_dict()
        })
        
        assert len(batch_summary) == 5  # 5 batches
        
        # Test batch cleanup
        cleanup_results = []
        for result in import_results:
            cleanup_result = await crud_manager.execute(
                f"DROP TABLE IF EXISTS {result['table_name']}"
            )
            cleanup_results.append(cleanup_result is not None)
        
        assert all(cleanup_results)


@pytest.mark.integration
class TestConcurrencyIntegration:
    """Integration tests for concurrent operations."""

    @pytest.fixture
    async def concurrent_system(self, test_config):
        """Set up system for concurrency testing."""
        config = DatabaseConfig(test_config)
        pool_manager = DatabasePoolManager(config)
        session_manager = SessionManager(pool_manager)
        crud_manager = CRUDOperations(session_manager)
        
        system = {
            'config': config,
            'pool_manager': pool_manager,
            'session_manager': session_manager,
            'crud_manager': crud_manager
        }
        
        yield system
        
        # Cleanup
        await pool_manager.close()

    @pytest.mark.asyncio
    async def test_concurrent_read_write_operations(self, concurrent_system):
        """Test concurrent read and write operations."""
        crud_manager = concurrent_system['crud_manager']
        
        # Initialize test data
        initial_data = pd.DataFrame({
            'id': range(1000),
            'value': np.random.uniform(0, 100, 1000),
            'status': ['active'] * 1000
        })
        
        insert_result = await crud_manager.bulk_insert('concurrent_test', initial_data)
        assert insert_result.success
        
        # Define concurrent operations
        async def reader_task(reader_id: int, iterations: int):
            """Concurrent reader task."""
            results = []
            for i in range(iterations):
                try:
                    data = await crud_manager.execute_query(
                        f"SELECT COUNT(*) as count FROM concurrent_test WHERE id % {reader_id + 1} = 0"
                    )
                    results.append({
                        'reader_id': reader_id,
                        'iteration': i,
                        'count': data.iloc[0]['count'] if len(data) > 0 else 0,
                        'success': True
                    })
                except Exception as e:
                    results.append({
                        'reader_id': reader_id,
                        'iteration': i,
                        'error': str(e),
                        'success': False
                    })
                
                # Small delay to allow other operations
                await asyncio.sleep(0.01)
            
            return results
        
        async def writer_task(writer_id: int, iterations: int):
            """Concurrent writer task."""
            results = []
            for i in range(iterations):
                try:
                    # Update some records
                    update_query = f"""
                    UPDATE concurrent_test 
                    SET value = value + {writer_id} 
                    WHERE id % {writer_id + 2} = 0 AND id < 100
                    """
                    
                    result = await crud_manager.execute(update_query)
                    results.append({
                        'writer_id': writer_id,
                        'iteration': i,
                        'success': result is not None
                    })
                except Exception as e:
                    results.append({
                        'writer_id': writer_id,
                        'iteration': i,
                        'error': str(e),
                        'success': False
                    })
                
                # Small delay to allow other operations
                await asyncio.sleep(0.02)
            
            return results
        
        # Execute concurrent operations
        num_readers = 5
        num_writers = 3
        iterations_per_task = 10
        
        reader_tasks = [
            reader_task(i, iterations_per_task) for i in range(num_readers)
        ]
        writer_tasks = [
            writer_task(i, iterations_per_task) for i in range(num_writers)
        ]
        
        all_tasks = reader_tasks + writer_tasks
        
        start_time = time.time()
        results = await asyncio.gather(*all_tasks, return_exceptions=True)
        end_time = time.time()
        
        # Analyze results
        reader_results = results[:num_readers]
        writer_results = results[num_readers:]
        
        # Check reader success rates
        total_reads = 0
        successful_reads = 0
        
        for reader_result in reader_results:
            if isinstance(reader_result, Exception):
                continue
            
            for read_op in reader_result:
                total_reads += 1
                if read_op['success']:
                    successful_reads += 1
        
        # Check writer success rates
        total_writes = 0
        successful_writes = 0
        
        for writer_result in writer_results:
            if isinstance(writer_result, Exception):
                continue
            
            for write_op in writer_result:
                total_writes += 1
                if write_op['success']:
                    successful_writes += 1
        
        # Performance metrics
        total_time = end_time - start_time
        total_operations = total_reads + total_writes
        operations_per_second = total_operations / total_time
        
        read_success_rate = successful_reads / total_reads if total_reads > 0 else 0
        write_success_rate = successful_writes / total_writes if total_writes > 0 else 0
        
        # Assertions
        assert read_success_rate >= 0.95, f"Read success rate too low: {read_success_rate:.2%}"
        assert write_success_rate >= 0.90, f"Write success rate too low: {write_success_rate:.2%}"
        assert operations_per_second >= 50, f"Operations per second too low: {operations_per_second:.2f}"
        
        print(f"Concurrent Operations Results:")
        print(f"  Total operations: {total_operations}")
        print(f"  Read success rate: {read_success_rate:.2%}")
        print(f"  Write success rate: {write_success_rate:.2%}")
        print(f"  Operations per second: {operations_per_second:.2f}")
        print(f"  Total time: {total_time:.2f}s")

    @pytest.mark.asyncio
    async def test_transaction_isolation_integration(self, concurrent_system):
        """Test transaction isolation in concurrent scenarios."""
        crud_manager = concurrent_system['crud_manager']
        
        # Initialize test data
        test_data = pd.DataFrame({
            'id': range(100),
            'balance': [1000.0] * 100,
            'version': [1] * 100
        })
        
        insert_result = await crud_manager.bulk_insert('transaction_test', test_data)
        assert insert_result.success
        
        # Test concurrent transactions
        async def transfer_task(task_id: int, from_id: int, to_id: int, amount: float):
            """Simulate money transfer between accounts."""
            try:
                async with crud_manager.transaction() as tx:
                    # Read current balances
                    from_balance_query = f"SELECT balance FROM transaction_test WHERE id = {from_id}"
                    to_balance_query = f"SELECT balance FROM transaction_test WHERE id = {to_id}"
                    
                    from_result = await tx.execute_query(from_balance_query)
                    to_result = await tx.execute_query(to_balance_query)
                    
                    if len(from_result) == 0 or len(to_result) == 0:
                        raise Exception("Account not found")
                    
                    from_balance = from_result.iloc[0]['balance']
                    to_balance = to_result.iloc[0]['balance']
                    
                    # Check sufficient funds
                    if from_balance < amount:
                        raise Exception("Insufficient funds")
                    
                    # Simulate processing time
                    await asyncio.sleep(0.1)
                    
                    # Update balances
                    update_from = f"UPDATE transaction_test SET balance = {from_balance - amount} WHERE id = {from_id}"
                    update_to = f"UPDATE transaction_test SET balance = {to_balance + amount} WHERE id = {to_id}"
                    
                    await tx.execute(update_from)
                    await tx.execute(update_to)
                    
                    return {
                        'task_id': task_id,
                        'from_id': from_id,
                        'to_id': to_id,
                        'amount': amount,
                        'success': True
                    }
                    
            except Exception as e:
                return {
                    'task_id': task_id,
                    'from_id': from_id,
                    'to_id': to_id,
                    'amount': amount,
                    'success': False,
                    'error': str(e)
                }
        
        # Create concurrent transfer tasks
        transfer_tasks = [
            transfer_task(0, 0, 1, 100.0),
            transfer_task(1, 1, 2, 150.0),
            transfer_task(2, 2, 0, 200.0),
            transfer_task(3, 0, 2, 50.0),
            transfer_task(4, 1, 0, 75.0)
        ]
        
        # Execute concurrent transfers
        transfer_results = await asyncio.gather(*transfer_tasks, return_exceptions=True)
        
        # Verify transaction integrity
        final_balances = await crud_manager.execute_query(
            "SELECT id, balance FROM transaction_test WHERE id IN (0, 1, 2) ORDER BY id"
        )
        
        # Calculate expected balances based on successful transfers
        successful_transfers = [r for r in transfer_results if not isinstance(r, Exception) and r['success']]
        
        initial_balance = 1000.0
        expected_balances = {0: initial_balance, 1: initial_balance, 2: initial_balance}
        
        for transfer in successful_transfers:
            expected_balances[transfer['from_id']] -= transfer['amount']
            expected_balances[transfer['to_id']] += transfer['amount']
        
        # Verify balances
        for _, row in final_balances.iterrows():
            account_id = row['id']
            actual_balance = row['balance']
            expected_balance = expected_balances[account_id]
            
            assert abs(actual_balance - expected_balance) < 0.01, \
                f"Balance mismatch for account {account_id}: expected {expected_balance}, got {actual_balance}"
        
        # Verify total money conservation
        total_balance = final_balances['balance'].sum()
        expected_total = 3 * initial_balance  # 3 accounts * 1000 each
        
        assert abs(total_balance - expected_total) < 0.01, \
            f"Total balance mismatch: expected {expected_total}, got {total_balance}"
        
        print(f"Transaction Isolation Results:")
        print(f"  Successful transfers: {len(successful_transfers)}")
        print(f"  Failed transfers: {len(transfer_results) - len(successful_transfers)}")
        print(f"  Final balances: {dict(zip(final_balances['id'], final_balances['balance']))}")
        print(f"  Total balance: {total_balance} (expected: {expected_total})")

    @pytest.mark.asyncio
    async def test_connection_pool_stress_integration(self, concurrent_system):
        """Test connection pool under stress conditions."""
        pool_manager = concurrent_system['pool_manager']
        crud_manager = concurrent_system['crud_manager']
        
        # Test high concurrency stress
        async def stress_task(task_id: int, operations: int):
            """Stress test task."""
            results = []
            
            for op in range(operations):
                try:
                    # Mix of different operations
                    if op % 4 == 0:
                        # Insert operation
                        data = pd.DataFrame({
                            'task_id': [task_id],
                            'operation': [op],
                            'data': [f"stress_test_{task_id}_{op}"]
                        })
                        result = await crud_manager.bulk_insert(f"stress_test_{task_id}", data)
                        success = result.success
                        
                    elif op % 4 == 1:
                        # Select operation
                        query_result = await crud_manager.execute_query(
                            f"SELECT COUNT(*) as count FROM stress_test_{task_id}"
                        )
                        success = len(query_result) > 0
                        
                    elif op % 4 == 2:
                        # Update operation
                        update_result = await crud_manager.execute(
                            f"UPDATE stress_test_{task_id} SET data = data || '_updated' WHERE operation = {op - 1}"
                        )
                        success = update_result is not None
                        
                    else:
                        # Delete operation
                        delete_result = await crud_manager.execute(
                            f"DELETE FROM stress_test_{task_id} WHERE operation < {op - 2}"
                        )
                        success = delete_result is not None
                    
                    results.append({
                        'task_id': task_id,
                        'operation': op,
                        'success': success
                    })
                    
                except Exception as e:
                    results.append({
                        'task_id': task_id,
                        'operation': op,
                        'success': False,
                        'error': str(e)
                    })
                
                # Brief pause to allow other tasks
                if op % 10 == 0:
                    await asyncio.sleep(0.001)
            
            return results
        
        # Create high number of concurrent tasks
        num_tasks = 20
        operations_per_task = 25
        
        stress_tasks = [
            stress_task(i, operations_per_task) for i in range(num_tasks)
        ]
        
        # Monitor pool statistics during stress test
        initial_stats = await pool_manager.get_pool_stats()
        
        start_time = time.time()
        stress_results = await asyncio.gather(*stress_tasks, return_exceptions=True)
        end_time = time.time()
        
        final_stats = await pool_manager.get_pool_stats()
        
        # Analyze stress test results
        total_operations = 0
        successful_operations = 0
        failed_tasks = 0
        
        for task_result in stress_results:
            if isinstance(task_result, Exception):
                failed_tasks += 1
                continue
            
            for operation in task_result:
                total_operations += 1
                if operation['success']:
                    successful_operations += 1
        
        # Performance metrics
        total_time = end_time - start_time
        operations_per_second = total_operations / total_time
        success_rate = successful_operations / total_operations if total_operations > 0 else 0
        
        # Assertions
        assert success_rate >= 0.85, f"Success rate too low under stress: {success_rate:.2%}"
        assert failed_tasks <= num_tasks * 0.1, f"Too many failed tasks: {failed_tasks}/{num_tasks}"
        assert operations_per_second >= 100, f"Operations per second too low: {operations_per_second:.2f}"
        
        # Pool should remain stable
        assert final_stats['active_connections'] <= final_stats['max_connections']
        
        print(f"Connection Pool Stress Test Results:")
        print(f"  Total tasks: {num_tasks}")
        print(f"  Total operations: {total_operations}")
        print(f"  Success rate: {success_rate:.2%}")
        print(f"  Failed tasks: {failed_tasks}")
        print(f"  Operations per second: {operations_per_second:.2f}")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Initial pool stats: {initial_stats}")
        print(f"  Final pool stats: {final_stats}")