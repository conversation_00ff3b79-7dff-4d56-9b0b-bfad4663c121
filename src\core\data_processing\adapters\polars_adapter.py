# -*- coding: utf-8 -*-
"""
Polars Adapter for Data Processing

This module provides a Polars-based implementation of the data processing adapter,
optimized for large datasets with high performance and memory efficiency.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, AsyncGenerator

import pandas as pd

from .base_adapter import BaseAdapter, AdapterError
from ..types import (
    ProcessingEngine, ProcessingStatus, DataFormat,
    ProcessingMetrics, ProcessingConfig
)

try:
    import polars as pl
    POLARS_AVAILABLE = True
except ImportError:
    POLARS_AVAILABLE = False
    # Create dummy types for type hints when polars is not available
    class _DummyPolars:
        DataFrame = Any
        Expr = Any
        LazyFrame = Any
        Series = Any
        DataType = Any
        
        # Add common polars functions as dummy
        def __getattr__(self, name):
            return Any
    
    pl = _DummyPolars()


class PolarsAdapter(BaseAdapter):
    """Polars-based data processing adapter.
    
    Optimized for:
    - Large datasets (>1M records)
    - High performance processing
    - Memory efficiency
    - Parallel execution
    """
    
    @property
    def engine_name(self) -> str:
        """Get the name of the processing engine."""
        return "polars"
    
    @property
    def engine_type(self) -> ProcessingEngine:
        """Get the processing engine type."""
        return ProcessingEngine.POLARS
    
    def is_engine_available(self) -> bool:
        """Check if Polars is available."""
        return POLARS_AVAILABLE
    
    async def read_file(self, file_path: Path, **kwargs) -> pl.DataFrame:
        """Read data from a file using Polars.
        
        Args:
            file_path: Path to the file
            **kwargs: Additional arguments for reading
            
        Returns:
            Polars DataFrame
        """
        if not POLARS_AVAILABLE:
            raise AdapterError("Polars is not available. Please install polars: pip install polars")
        
        file_info = self.get_file_info(file_path)
        
        try:
            if file_info.format == DataFormat.CSV:
                # Polars CSV reading with optimizations
                read_params = {
                    'encoding': kwargs.get('encoding', 'utf8'),
                    'separator': kwargs.get('separator', kwargs.get('sep', self._detect_delimiter(file_path))),
                    'has_header': kwargs.get('has_header', True),
                    'ignore_errors': kwargs.get('ignore_errors', False),
                    'null_values': kwargs.get('null_values', ['', 'NULL', 'null', 'None', 'N/A', 'n/a']),
                    'try_parse_dates': kwargs.get('try_parse_dates', True),
                    'rechunk': kwargs.get('rechunk', True),  # Optimize memory layout
                }
                
                # Remove None values
                read_params = {k: v for k, v in read_params.items() if v is not None}
                
                df = pl.read_csv(file_path, **read_params)
                
            elif file_info.format == DataFormat.TSV:
                read_params = {
                    'separator': '\t',
                    'encoding': kwargs.get('encoding', 'utf8'),
                    'has_header': kwargs.get('has_header', True),
                    'ignore_errors': kwargs.get('ignore_errors', False),
                    'null_values': kwargs.get('null_values', ['', 'NULL', 'null', 'None', 'N/A', 'n/a']),
                    'try_parse_dates': kwargs.get('try_parse_dates', True),
                    'rechunk': True,
                }
                
                df = pl.read_csv(file_path, **read_params)
                
            elif file_info.format == DataFormat.EXCEL:
                # Polars doesn't have native Excel support, use pandas and convert
                pandas_df = pd.read_excel(file_path, **kwargs)
                df = pl.from_pandas(pandas_df)
                
            elif file_info.format == DataFormat.JSON:
                df = pl.read_json(file_path, **kwargs)
                
            elif file_info.format == DataFormat.JSONL:
                df = pl.read_ndjson(file_path, **kwargs)
                
            elif file_info.format == DataFormat.PARQUET:
                read_params = {
                    'use_pyarrow': kwargs.get('use_pyarrow', True),
                    'rechunk': kwargs.get('rechunk', True),
                }
                read_params.update(kwargs)
                df = pl.read_parquet(file_path, **read_params)
                
            elif file_info.format == DataFormat.FEATHER:
                df = pl.read_ipc(file_path, **kwargs)
                
            else:
                raise AdapterError(f"Unsupported file format for Polars: {file_info.format}")
            
            # Optimize data types if requested
            if kwargs.get('optimize_dtypes', True):
                df = self._optimize_dtypes(df)
            
            self.logger.info(f"Read {len(df)} records from {file_path} using Polars")
            return df
            
        except Exception as e:
            raise AdapterError(f"Failed to read file {file_path} with Polars: {e}")
    
    async def write_file(self, data: pl.DataFrame, file_path: Path, **kwargs) -> None:
        """Write Polars DataFrame to a file.
        
        Args:
            data: Polars DataFrame to write
            file_path: Path to write the file
            **kwargs: Additional arguments for writing
        """
        if not POLARS_AVAILABLE:
            raise AdapterError("Polars is not available")
        
        file_info = self.get_file_info(file_path)
        
        # Create parent directory if it doesn't exist
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            if file_info.format == DataFormat.CSV:
                write_params = {
                    'separator': kwargs.get('separator', ','),
                    'has_header': kwargs.get('has_header', True),
                    'quote': kwargs.get('quote', '"'),
                    'null_value': kwargs.get('null_value', ''),
                }
                write_params.update(kwargs)
                data.write_csv(file_path, **write_params)
                
            elif file_info.format == DataFormat.TSV:
                write_params = {
                    'separator': '\t',
                    'has_header': kwargs.get('has_header', True),
                    'quote': kwargs.get('quote', '"'),
                    'null_value': kwargs.get('null_value', ''),
                }
                write_params.update(kwargs)
                data.write_csv(file_path, **write_params)
                
            elif file_info.format == DataFormat.EXCEL:
                # Convert to pandas for Excel writing
                pandas_df = data.to_pandas()
                write_params = {
                    'index': False,
                    'engine': 'openpyxl',
                }
                write_params.update(kwargs)
                pandas_df.to_excel(file_path, **write_params)
                
            elif file_info.format == DataFormat.JSON:
                data.write_json(file_path, **kwargs)
                
            elif file_info.format == DataFormat.JSONL:
                data.write_ndjson(file_path, **kwargs)
                
            elif file_info.format == DataFormat.PARQUET:
                write_params = {
                    'use_pyarrow': kwargs.get('use_pyarrow', True),
                    'compression': kwargs.get('compression', 'snappy'),
                }
                write_params.update(kwargs)
                data.write_parquet(file_path, **write_params)
                
            elif file_info.format == DataFormat.FEATHER:
                data.write_ipc(file_path, **kwargs)
                
            else:
                raise AdapterError(f"Unsupported file format for Polars: {file_info.format}")
            
            self.logger.info(f"Wrote {len(data)} records to {file_path} using Polars")
            
        except Exception as e:
            raise AdapterError(f"Failed to write file {file_path} with Polars: {e}")
    
    async def process_chunk(self, chunk: pl.DataFrame, processor_func: callable) -> pl.DataFrame:
        """Process a Polars DataFrame chunk.
        
        Args:
            chunk: Polars DataFrame chunk to process
            processor_func: Processing function
            
        Returns:
            Processed Polars DataFrame chunk
        """
        try:
            # Run processor function in thread pool for CPU-intensive operations
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, processor_func, chunk)
            
            # Ensure result is a Polars DataFrame
            if not isinstance(result, pl.DataFrame):
                raise AdapterError(f"Processor function must return Polars DataFrame, got {type(result)}")
            
            return result
            
        except Exception as e:
            raise AdapterError(f"Failed to process chunk with Polars: {e}")
    
    async def validate_data(self, data: pl.DataFrame, validation_rules: Dict[str, Any]) -> Dict[str, Any]:
        """Validate Polars DataFrame against rules.
        
        Args:
            data: Polars DataFrame to validate
            validation_rules: Validation rules
            
        Returns:
            Validation results
        """
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'metrics': {
                'total_records': len(data),
                'valid_records': 0,
                'invalid_records': 0,
                'completeness': {},
                'data_types': {},
            }
        }
        
        try:
            # Check required columns
            required_columns = validation_rules.get('required_columns', [])
            missing_columns = set(required_columns) - set(data.columns)
            if missing_columns:
                results['errors'].append(f"Missing required columns: {missing_columns}")
                results['is_valid'] = False
            
            # Check data types
            expected_dtypes = validation_rules.get('dtypes', {})
            for column, expected_dtype in expected_dtypes.items():
                if column in data.columns:
                    actual_dtype = str(data[column].dtype)
                    results['metrics']['data_types'][column] = actual_dtype
                    
                    if not self._is_dtype_compatible(actual_dtype, expected_dtype):
                        results['warnings'].append(
                            f"Column {column} has dtype {actual_dtype}, expected {expected_dtype}"
                        )
            
            # Check completeness
            for column in data.columns:
                null_count = data[column].null_count()
                completeness = (len(data) - null_count) / len(data) if len(data) > 0 else 0
                results['metrics']['completeness'][column] = completeness
                
                min_completeness = validation_rules.get('min_completeness', {}).get(column, 0.0)
                if completeness < min_completeness:
                    results['errors'].append(
                        f"Column {column} completeness {completeness:.2%} below minimum {min_completeness:.2%}"
                    )
                    results['is_valid'] = False
            
            # Check value ranges for numeric columns
            value_ranges = validation_rules.get('value_ranges', {})
            for column, (min_val, max_val) in value_ranges.items():
                if column in data.columns:
                    col_dtype = data[column].dtype
                    if col_dtype.is_numeric():
                        out_of_range = data.filter(
                            (pl.col(column) < min_val) | (pl.col(column) > max_val)
                        ).height
                        if out_of_range > 0:
                            results['warnings'].append(
                                f"Column {column} has {out_of_range} values out of range [{min_val}, {max_val}]"
                            )
            
            # Check unique constraints
            unique_columns = validation_rules.get('unique_columns', [])
            for column in unique_columns:
                if column in data.columns:
                    total_count = data.height
                    unique_count = data[column].n_unique()
                    duplicates = total_count - unique_count
                    if duplicates > 0:
                        results['errors'].append(f"Column {column} has {duplicates} duplicate values")
                        results['is_valid'] = False
            
            # Calculate valid/invalid records
            if results['is_valid']:
                results['metrics']['valid_records'] = len(data)
            else:
                results['metrics']['invalid_records'] = len(data)
            
            return results
            
        except Exception as e:
            results['errors'].append(f"Validation error: {e}")
            results['is_valid'] = False
            return results
    
    async def transform_data(self, data: pl.DataFrame, transformations: List[Dict[str, Any]]) -> pl.DataFrame:
        """Transform Polars DataFrame using specified transformations.
        
        Args:
            data: Polars DataFrame to transform
            transformations: List of transformation specifications
            
        Returns:
            Transformed Polars DataFrame
        """
        result = data.clone()
        
        try:
            for transform in transformations:
                transform_type = transform.get('type')
                
                if transform_type == 'rename_columns':
                    mapping = transform.get('mapping', {})
                    # Polars rename using dictionary
                    result = result.rename(mapping)
                
                elif transform_type == 'drop_columns':
                    columns = transform.get('columns', [])
                    # Only drop columns that exist
                    existing_columns = [col for col in columns if col in result.columns]
                    if existing_columns:
                        result = result.drop(existing_columns)
                
                elif transform_type == 'add_column':
                    column_name = transform.get('name')
                    value = transform.get('value')
                    result = result.with_columns(pl.lit(value).alias(column_name))
                
                elif transform_type == 'convert_dtypes':
                    dtype_mapping = transform.get('mapping', {})
                    for column, dtype in dtype_mapping.items():
                        if column in result.columns:
                            result = result.with_columns(
                                self._convert_dtype_polars(pl.col(column), dtype).alias(column)
                            )
                
                elif transform_type == 'fill_na':
                    fill_values = transform.get('values', {})
                    for column, fill_value in fill_values.items():
                        if column in result.columns:
                            result = result.with_columns(
                                pl.col(column).fill_null(fill_value)
                            )
                
                elif transform_type == 'filter_rows':
                    condition = transform.get('condition')
                    if condition:
                        # Convert pandas-style query to Polars filter
                        # This is a simplified implementation
                        # In practice, you'd need a more sophisticated query parser
                        result = self._apply_filter_condition(result, condition)
                
                elif transform_type == 'sort':
                    columns = transform.get('columns', [])
                    ascending = transform.get('ascending', True)
                    if isinstance(ascending, bool):
                        ascending = [ascending] * len(columns)
                    
                    sort_exprs = []
                    for col, asc in zip(columns, ascending):
                        if asc:
                            sort_exprs.append(pl.col(col))
                        else:
                            sort_exprs.append(pl.col(col).sort(descending=True))
                    
                    result = result.sort(columns, descending=[not asc for asc in ascending])
                
                elif transform_type == 'group_by':
                    group_columns = transform.get('columns', [])
                    agg_functions = transform.get('aggregations', {})
                    
                    # Convert aggregation functions to Polars expressions
                    agg_exprs = []
                    for column, func in agg_functions.items():
                        if func == 'sum':
                            agg_exprs.append(pl.col(column).sum())
                        elif func == 'mean':
                            agg_exprs.append(pl.col(column).mean())
                        elif func == 'count':
                            agg_exprs.append(pl.col(column).count())
                        elif func == 'min':
                            agg_exprs.append(pl.col(column).min())
                        elif func == 'max':
                            agg_exprs.append(pl.col(column).max())
                        elif func == 'std':
                            agg_exprs.append(pl.col(column).std())
                        elif func == 'var':
                            agg_exprs.append(pl.col(column).var())
                    
                    if agg_exprs:
                        result = result.group_by(group_columns).agg(agg_exprs)
                
                elif transform_type == 'custom':
                    func = transform.get('function')
                    if callable(func):
                        result = func(result)
                
                else:
                    self.logger.warning(f"Unknown transformation type: {transform_type}")
            
            return result
            
        except Exception as e:
            raise AdapterError(f"Failed to transform data with Polars: {e}")
    
    def get_data_info(self, data: pl.DataFrame) -> Dict[str, Any]:
        """Get information about the Polars DataFrame.
        
        Args:
            data: Polars DataFrame
            
        Returns:
            Data information dictionary
        """
        return {
            'shape': (data.height, data.width),
            'columns': data.columns,
            'dtypes': {col: str(dtype) for col, dtype in zip(data.columns, data.dtypes)},
            'memory_usage_mb': data.estimated_size('mb'),
            'null_counts': {col: data[col].null_count() for col in data.columns},
            'numeric_columns': [col for col, dtype in zip(data.columns, data.dtypes) if dtype.is_numeric()],
            'string_columns': [col for col, dtype in zip(data.columns, data.dtypes) if dtype == pl.Utf8],
            'datetime_columns': [col for col, dtype in zip(data.columns, data.dtypes) if dtype.is_temporal()],
            'boolean_columns': [col for col, dtype in zip(data.columns, data.dtypes) if dtype == pl.Boolean],
        }
    
    def to_pandas(self, data: pl.DataFrame) -> pd.DataFrame:
        """Convert Polars DataFrame to Pandas DataFrame.
        
        Args:
            data: Polars DataFrame
            
        Returns:
            Pandas DataFrame
        """
        return data.to_pandas()
    
    def from_pandas(self, df: pd.DataFrame) -> pl.DataFrame:
        """Convert Pandas DataFrame to Polars DataFrame.
        
        Args:
            df: Pandas DataFrame
            
        Returns:
            Polars DataFrame
        """
        return pl.from_pandas(df)
    
    async def _read_file_chunks(self, file_path: Path, chunk_size: int) -> AsyncGenerator[pl.DataFrame, None]:
        """Read file in chunks using Polars.
        
        Args:
            file_path: Path to the file
            chunk_size: Size of each chunk
            
        Yields:
            Polars DataFrame chunks
        """
        file_info = self.get_file_info(file_path)
        
        try:
            if file_info.format in [DataFormat.CSV, DataFormat.TSV]:
                # Polars doesn't have built-in chunking, so we'll read and split
                data = await self.read_file(file_path)
                
                for i in range(0, data.height, chunk_size):
                    chunk = data.slice(i, chunk_size)
                    yield chunk
            
            else:
                # For other formats, read entire file and split
                data = await self.read_file(file_path)
                
                for i in range(0, data.height, chunk_size):
                    chunk = data.slice(i, chunk_size)
                    yield chunk
                    
        except Exception as e:
            raise AdapterError(f"Failed to read file chunks with Polars: {e}")
    
    def _detect_delimiter(self, file_path: Path, sample_size: int = 1024) -> str:
        """Detect CSV delimiter.
        
        Args:
            file_path: Path to CSV file
            sample_size: Size of sample to analyze
            
        Returns:
            Detected delimiter
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                sample = f.read(sample_size)
            
            # Common delimiters to test
            delimiters = [',', ';', '\t', '|']
            delimiter_counts = {}
            
            for delimiter in delimiters:
                count = sample.count(delimiter)
                if count > 0:
                    delimiter_counts[delimiter] = count
            
            if delimiter_counts:
                return max(delimiter_counts, key=delimiter_counts.get)
            else:
                return ','  # Default to comma
                
        except Exception:
            return ','  # Default to comma on error
    
    def _optimize_dtypes(self, df: pl.DataFrame) -> pl.DataFrame:
        """Optimize Polars DataFrame data types for memory efficiency.
        
        Args:
            df: Polars DataFrame to optimize
            
        Returns:
            Optimized Polars DataFrame
        """
        # Polars already has good type inference and memory optimization
        # Additional optimizations can be added here if needed
        return df.rechunk()  # Optimize memory layout
    
    def _is_dtype_compatible(self, actual: str, expected: str) -> bool:
        """Check if Polars data types are compatible.
        
        Args:
            actual: Actual data type
            expected: Expected data type
            
        Returns:
            True if compatible
        """
        # Normalize type names
        actual = actual.lower()
        expected = expected.lower()
        
        # Define compatibility groups for Polars types
        numeric_types = ['int', 'float', 'number']
        string_types = ['utf8', 'string', 'str']
        datetime_types = ['datetime', 'timestamp', 'date']
        
        # Check if both are in the same group
        for type_group in [numeric_types, string_types, datetime_types]:
            if any(t in actual for t in type_group) and any(t in expected for t in type_group):
                return True
        
        return actual == expected
    
    def _convert_dtype_polars(self, expr: pl.Expr, target_dtype: str) -> pl.Expr:
        """Convert Polars expression to target data type.
        
        Args:
            expr: Polars expression
            target_dtype: Target data type
            
        Returns:
            Converted Polars expression
        """
        try:
            if target_dtype.lower() in ['int', 'integer']:
                return expr.cast(pl.Int64)
            elif target_dtype.lower() in ['float', 'number']:
                return expr.cast(pl.Float64)
            elif target_dtype.lower() in ['str', 'string']:
                return expr.cast(pl.Utf8)
            elif target_dtype.lower() in ['datetime', 'timestamp']:
                return expr.cast(pl.Datetime)
            elif target_dtype.lower() == 'date':
                return expr.cast(pl.Date)
            elif target_dtype.lower() == 'boolean':
                return expr.cast(pl.Boolean)
            else:
                return expr  # Keep original if unknown type
        except Exception:
            self.logger.warning(f"Failed to convert expression to {target_dtype}, keeping original type")
            return expr
    
    def _apply_filter_condition(self, df: pl.DataFrame, condition: str) -> pl.DataFrame:
        """Apply filter condition to Polars DataFrame.
        
        Args:
            df: Polars DataFrame
            condition: Filter condition string
            
        Returns:
            Filtered DataFrame
        """
        # This is a simplified implementation
        # In practice, you'd need a more sophisticated query parser
        # to convert pandas-style queries to Polars expressions
        
        try:
            # Simple condition parsing (extend as needed)
            if '>' in condition:
                parts = condition.split('>')
                if len(parts) == 2:
                    column = parts[0].strip()
                    value = float(parts[1].strip())
                    return df.filter(pl.col(column) > value)
            elif '<' in condition:
                parts = condition.split('<')
                if len(parts) == 2:
                    column = parts[0].strip()
                    value = float(parts[1].strip())
                    return df.filter(pl.col(column) < value)
            elif '==' in condition:
                parts = condition.split('==')
                if len(parts) == 2:
                    column = parts[0].strip()
                    value = parts[1].strip().strip('"\'')
                    return df.filter(pl.col(column) == value)
            
            # If we can't parse the condition, return original DataFrame
            self.logger.warning(f"Could not parse filter condition: {condition}")
            return df
            
        except Exception as e:
            self.logger.warning(f"Error applying filter condition '{condition}': {e}")
            return df