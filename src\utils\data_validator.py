# -*- coding: utf-8 -*-
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""Data validation utilities.

This module provides comprehensive data validation functionality
for the Connect platform, including rule-based validation,
result reporting, and common validation patterns.
"""

import re
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional, Union
from enum import Enum


class ValidationSeverity(Enum):
    """Validation result severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationResult:
    """Result of a validation operation."""
    is_valid: bool
    messages: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    field_errors: Dict[str, List[str]] = field(default_factory=dict)
    
    def add_error(self, message: str, field: Optional[str] = None) -> None:
        """Add an error message.
        
        Args:
            message: Error message
            field: Optional field name
        """
        self.is_valid = False
        self.errors.append(message)
        self.messages.append(f"ERROR: {message}")
        
        if field:
            if field not in self.field_errors:
                self.field_errors[field] = []
            self.field_errors[field].append(message)
    
    def add_warning(self, message: str, field: Optional[str] = None) -> None:
        """Add a warning message.
        
        Args:
            message: Warning message
            field: Optional field name
        """
        self.warnings.append(message)
        self.messages.append(f"WARNING: {message}")
        
        if field:
            if field not in self.field_errors:
                self.field_errors[field] = []
            self.field_errors[field].append(f"WARNING: {message}")
    
    def merge(self, other: 'ValidationResult') -> None:
        """Merge another validation result into this one.
        
        Args:
            other: Another validation result to merge
        """
        if not other.is_valid:
            self.is_valid = False
        
        self.messages.extend(other.messages)
        self.errors.extend(other.errors)
        self.warnings.extend(other.warnings)
        
        for field, errors in other.field_errors.items():
            if field not in self.field_errors:
                self.field_errors[field] = []
            self.field_errors[field].extend(errors)


@dataclass
class ValidationRule:
    """Defines a validation rule."""
    name: str
    validator: Callable[[Any], bool]
    message: str
    severity: ValidationSeverity = ValidationSeverity.ERROR
    field: Optional[str] = None
    
    def validate(self, value: Any) -> ValidationResult:
        """Apply this rule to a value.
        
        Args:
            value: Value to validate
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        try:
            if not self.validator(value):
                if self.severity == ValidationSeverity.ERROR:
                    result.add_error(self.message, self.field)
                else:
                    result.add_warning(self.message, self.field)
        except Exception as e:
            result.add_error(f"Validation rule '{self.name}' failed: {str(e)}", self.field)
        
        return result


class DataValidator:
    """Comprehensive data validator with rule-based validation."""
    
    def __init__(self):
        """Initialize the data validator."""
        self._rules: List[ValidationRule] = []
        self._field_rules: Dict[str, List[ValidationRule]] = {}
    
    def add_rule(self, rule: ValidationRule) -> None:
        """Add a validation rule.
        
        Args:
            rule: Validation rule to add
        """
        self._rules.append(rule)
        
        if rule.field:
            if rule.field not in self._field_rules:
                self._field_rules[rule.field] = []
            self._field_rules[rule.field].append(rule)
    
    def validate(self, data: Any, field: Optional[str] = None) -> ValidationResult:
        """Validate data against all applicable rules.
        
        Args:
            data: Data to validate
            field: Optional field name for field-specific validation
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        # Apply general rules
        if field is None:
            rules_to_apply = self._rules
        else:
            rules_to_apply = self._field_rules.get(field, [])
        
        for rule in rules_to_apply:
            rule_result = rule.validate(data)
            result.merge(rule_result)
        
        return result
    
    def validate_dict(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate a dictionary with field-specific rules.
        
        Args:
            data: Dictionary to validate
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        # Validate each field
        for field, value in data.items():
            field_result = self.validate(value, field)
            result.merge(field_result)
        
        # Apply general rules to the entire dict
        general_result = self.validate(data)
        result.merge(general_result)
        
        return result
    
    @staticmethod
    def create_required_rule(field: str) -> ValidationRule:
        """Create a rule that checks if a field is required.
        
        Args:
            field: Field name
            
        Returns:
            Validation rule
        """
        return ValidationRule(
            name=f"required_{field}",
            validator=lambda x: x is not None and x != "",
            message=f"Field '{field}' is required",
            field=field
        )
    
    @staticmethod
    def create_type_rule(field: str, expected_type: type) -> ValidationRule:
        """Create a rule that checks field type.
        
        Args:
            field: Field name
            expected_type: Expected type
            
        Returns:
            Validation rule
        """
        return ValidationRule(
            name=f"type_{field}",
            validator=lambda x: isinstance(x, expected_type),
            message=f"Field '{field}' must be of type {expected_type.__name__}",
            field=field
        )
    
    @staticmethod
    def create_range_rule(field: str, min_val: Union[int, float], max_val: Union[int, float]) -> ValidationRule:
        """Create a rule that checks numeric range.
        
        Args:
            field: Field name
            min_val: Minimum value
            max_val: Maximum value
            
        Returns:
            Validation rule
        """
        return ValidationRule(
            name=f"range_{field}",
            validator=lambda x: isinstance(x, (int, float)) and min_val <= x <= max_val,
            message=f"Field '{field}' must be between {min_val} and {max_val}",
            field=field
        )
    
    @staticmethod
    def create_length_rule(field: str, min_len: int, max_len: int) -> ValidationRule:
        """Create a rule that checks string/list length.
        
        Args:
            field: Field name
            min_len: Minimum length
            max_len: Maximum length
            
        Returns:
            Validation rule
        """
        return ValidationRule(
            name=f"length_{field}",
            validator=lambda x: hasattr(x, '__len__') and min_len <= len(x) <= max_len,
            message=f"Field '{field}' length must be between {min_len} and {max_len}",
            field=field
        )
    
    @staticmethod
    def create_email_rule(field: str) -> ValidationRule:
        """Create a rule that validates email format.
        
        Args:
            field: Field name
            
        Returns:
            Validation rule
        """
        email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        
        return ValidationRule(
            name=f"email_{field}",
            validator=lambda x: isinstance(x, str) and email_pattern.match(x) is not None,
            message=f"Field '{field}' must be a valid email address",
            field=field
        )
    
    @staticmethod
    def create_regex_rule(field: str, pattern: str, message: Optional[str] = None) -> ValidationRule:
        """Create a rule that validates against a regex pattern.
        
        Args:
            field: Field name
            pattern: Regex pattern
            message: Optional custom message
            
        Returns:
            Validation rule
        """
        compiled_pattern = re.compile(pattern)
        default_message = f"Field '{field}' does not match required pattern"
        
        return ValidationRule(
            name=f"regex_{field}",
            validator=lambda x: isinstance(x, str) and compiled_pattern.match(x) is not None,
            message=message or default_message,
            field=field
        )