__author__ = "<PERSON><PERSON>Li"
__email__ = "<EMAIL>"

"""Vendor tagger for automatic CDR data vendor tag setting.

This module provides functionality to automatically tag CDR (Call Detail Record)
data with vendor information based on geospatial location matching.
"""

import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import asyncpg
import geopandas as gpd
import pandas as pd
from loguru import logger
from shapely.geometry import Point

# Handle relative imports with fallback
try:
    from ..config import settings
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    # Add parent directories to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(current_dir))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from config import settings
from ..connection.session import get_connection
from ..exceptions import DatabaseError, GeospatialError
from ..utils.batch_processor import BatchProcessor
from ..utils.performance import measure_performance
from .polygon_handler import PolygonHandler
from .processor import GeospatialProcessor


class VendorTagger:
    """Automatic vendor tagging for CDR data based on geospatial location.

    This class provides:
    - Automatic vendor identification based on coordinates
    - Batch processing for large CDR datasets
    - Caching for improved performance
    - Integration with existing polygon data
    """

    def __init__(
        self, polygon_handler: Optional[PolygonHandler] = None, connection_pool=None
    ):
        """Initialize the VendorTagger.

        Args:
            polygon_handler: Optional PolygonHandler instance
            connection_pool: Optional database connection pool
        """
        self.polygon_handler = polygon_handler or PolygonHandler()
        self.connection_pool = connection_pool
        self.batch_processor = BatchProcessor()
        self.vendor_cache = {}
        self.cache_hits = 0
        self.cache_misses = 0

    async def initialize_vendor_data(self, polygon_dir: Union[str, Path]) -> None:
        """Initialize vendor polygon data for tagging.

        Args:
            polygon_dir: Directory containing vendor polygon files
        """
        try:
            logger.info("Initializing vendor data for tagging")
            await self.polygon_handler.load_vendor_polygons(polygon_dir)

            # Pre-calculate spatial indices for faster lookups
            await self._build_spatial_indices()

            logger.info("Vendor data initialization completed")

        except Exception as e:
            logger.error(f"Failed to initialize vendor data: {e}")
            raise GeospatialError(f"Failed to initialize vendor data: {e}")

    async def _build_spatial_indices(self) -> None:
        """Build spatial indices for faster point-in-polygon queries."""
        try:
            for vendor, gdf in self.polygon_handler.vendor_regions.items():
                # Create spatial index using rtree
                spatial_index = gdf.sindex
                logger.info(
                    f"Built spatial index for {vendor} with {len(gdf)} polygons"
                )

        except Exception as e:
            logger.warning(f"Failed to build spatial indices: {e}")

    @measure_performance
    async def tag_cdr_data(
        self,
        cdr_data: pd.DataFrame,
        lon_col: str = "longitude",
        lat_col: str = "latitude",
        batch_size: int = 10000,
        use_cache: bool = True,
    ) -> pd.DataFrame:
        """Tag CDR data with vendor information based on coordinates.

        Args:
            cdr_data: DataFrame containing CDR data with coordinates
            lon_col: Name of longitude column
            lat_col: Name of latitude column
            batch_size: Number of records to process in each batch
            use_cache: Whether to use coordinate caching

        Returns:
            DataFrame with added vendor tags
        """
        try:
            if not self.polygon_handler.vendor_regions:
                raise GeospatialError("Vendor polygon data not initialized")

            logger.info(f"Tagging {len(cdr_data)} CDR records with vendor information")

            # Validate required columns
            if lon_col not in cdr_data.columns or lat_col not in cdr_data.columns:
                raise GeospatialError(
                    f"Required coordinate columns not found: {lon_col}, {lat_col}"
                )

            # Initialize result columns
            cdr_data = cdr_data.copy()
            cdr_data["vendor"] = None
            cdr_data["polygon_id"] = None
            cdr_data["confidence"] = 0.0

            # Process in batches
            total_batches = (len(cdr_data) + batch_size - 1) // batch_size

            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, len(cdr_data))

                batch_data = cdr_data.iloc[start_idx:end_idx]

                logger.info(
                    f"Processing batch {batch_idx + 1}/{total_batches} ({len(batch_data)} records)"
                )

                # Tag batch
                tagged_batch = await self._tag_batch(
                    batch_data, lon_col, lat_col, use_cache
                )

                # Update main dataframe
                cdr_data.iloc[start_idx:end_idx] = tagged_batch

            # Calculate tagging statistics
            stats = self._calculate_tagging_stats(cdr_data)
            logger.info(f"Tagging completed: {stats}")

            return cdr_data

        except Exception as e:
            logger.error(f"Failed to tag CDR data: {e}")
            raise GeospatialError(f"Failed to tag CDR data: {e}")

    async def _tag_batch(
        self, batch_data: pd.DataFrame, lon_col: str, lat_col: str, use_cache: bool
    ) -> pd.DataFrame:
        """Tag a batch of CDR records.

        Args:
            batch_data: Batch of CDR data
            lon_col: Longitude column name
            lat_col: Latitude column name
            use_cache: Whether to use caching

        Returns:
            Tagged batch data
        """
        try:
            batch_result = batch_data.copy()

            for idx, row in batch_data.iterrows():
                lon, lat = row[lon_col], row[lat_col]

                # Skip invalid coordinates
                if pd.isna(lon) or pd.isna(lat):
                    continue

                # Check cache first
                cache_key = f"{lon:.6f},{lat:.6f}"
                if use_cache and cache_key in self.vendor_cache:
                    vendor_info = self.vendor_cache[cache_key]
                    self.cache_hits += 1
                else:
                    # Find vendor for coordinates
                    vendor_info = await self._find_vendor_for_point(lon, lat)

                    if use_cache:
                        self.vendor_cache[cache_key] = vendor_info
                        self.cache_misses += 1

                # Update batch result
                if vendor_info:
                    batch_result.loc[idx, "vendor"] = vendor_info["vendor"]
                    batch_result.loc[idx, "polygon_id"] = vendor_info["polygon_id"]
                    batch_result.loc[idx, "confidence"] = vendor_info["confidence"]

            return batch_result

        except Exception as e:
            logger.error(f"Failed to tag batch: {e}")
            raise GeospatialError(f"Failed to tag batch: {e}")

    async def _find_vendor_for_point(
        self, lon: float, lat: float
    ) -> Optional[Dict[str, Any]]:
        """Find vendor information for a specific point.

        Args:
            lon: Longitude
            lat: Latitude

        Returns:
            Dictionary with vendor information or None
        """
        try:
            point = Point(lon, lat)
            best_match = None
            highest_confidence = 0.0

            # Check each vendor's polygons
            for vendor, gdf in self.polygon_handler.vendor_regions.items():
                # Use spatial index for faster lookup
                possible_matches_index = list(gdf.sindex.intersection(point.bounds))
                possible_matches = gdf.iloc[possible_matches_index]

                # Check actual containment
                for idx, polygon_row in possible_matches.iterrows():
                    if polygon_row.geometry.contains(point):
                        # Calculate confidence based on polygon area (smaller = higher confidence)
                        area_km2 = polygon_row["area_km2"]
                        confidence = min(1.0, max(0.1, 1.0 / (1.0 + area_km2 / 100)))

                        if confidence > highest_confidence:
                            highest_confidence = confidence
                            best_match = {
                                "vendor": vendor,
                                "polygon_id": polygon_row.get("polygon_id", idx),
                                "confidence": confidence,
                                "area_km2": area_km2,
                            }

            return best_match

        except Exception as e:
            logger.error(f"Failed to find vendor for point ({lon}, {lat}): {e}")
            return None

    def _calculate_tagging_stats(self, tagged_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate tagging statistics.

        Args:
            tagged_data: Tagged CDR data

        Returns:
            Dictionary with statistics
        """
        try:
            total_records = len(tagged_data)
            tagged_records = tagged_data["vendor"].notna().sum()
            untagged_records = total_records - tagged_records

            vendor_counts = tagged_data["vendor"].value_counts().to_dict()

            stats = {
                "total_records": total_records,
                "tagged_records": tagged_records,
                "untagged_records": untagged_records,
                "tagging_rate": tagged_records / total_records
                if total_records > 0
                else 0,
                "vendor_distribution": vendor_counts,
                "cache_hits": self.cache_hits,
                "cache_misses": self.cache_misses,
                "cache_hit_rate": self.cache_hits
                / (self.cache_hits + self.cache_misses)
                if (self.cache_hits + self.cache_misses) > 0
                else 0,
            }

            return stats

        except Exception as e:
            logger.error(f"Failed to calculate tagging stats: {e}")
            return {"error": str(e)}

    async def tag_cdr_from_database(
        self,
        table_name: str,
        schema: str = "cdr",
        lon_col: str = "longitude",
        lat_col: str = "latitude",
        batch_size: int = 10000,
        update_in_place: bool = True,
    ) -> Dict[str, Any]:
        """Tag CDR data directly from database table.

        Args:
            table_name: CDR table name
            schema: Schema name
            lon_col: Longitude column name
            lat_col: Latitude column name
            batch_size: Batch size for processing
            update_in_place: Whether to update the original table

        Returns:
            Dictionary with tagging results
        """
        try:
            logger.info(f"Tagging CDR data from {schema}.{table_name}")

            async with get_connection() as conn:
                # Get total record count with safe identifier validation
                import re

                # Validate identifiers to prevent injection
                if not re.match(r"^[a-zA-Z_][a-zA-Z0-9_]*$", schema):
                    raise ValueError(f"Invalid schema name: {schema}")
                if not re.match(r"^[a-zA-Z_][a-zA-Z0-9_]*$", table_name):
                    raise ValueError(f"Invalid table name: {table_name}")

                count_result = await conn.fetchval(
                    f'SELECT COUNT(*) FROM "{schema}"."{table_name}"'
                )
                total_records = count_result

                logger.info(f"Found {total_records} records to process")

                # Add vendor columns if they don't exist
                if update_in_place:
                    await self._ensure_vendor_columns(conn, table_name, schema)

                # Process in batches
                total_batches = (total_records + batch_size - 1) // batch_size
                processed_records = 0

                for batch_idx in range(total_batches):
                    offset = batch_idx * batch_size

                    # Fetch batch with safe identifiers
                    query = f"""
                        SELECT *, ctid as row_id
                        FROM "{schema}"."{table_name}"
                        ORDER BY ctid
                        LIMIT $1 OFFSET $2
                    """
                    rows = await conn.fetch(query, batch_size, offset)

                    batch_records = await conn.fetch(query)
                    batch_df = pd.DataFrame(batch_records)

                    if batch_df.empty:
                        break

                    logger.info(f"Processing batch {batch_idx + 1}/{total_batches}")

                    # Tag batch
                    tagged_batch = await self.tag_cdr_data(
                        batch_df, lon_col, lat_col, batch_size=len(batch_df)
                    )

                    # Update database if requested
                    if update_in_place:
                        await self._update_batch_in_database(
                            conn, tagged_batch, table_name, schema
                        )

                    processed_records += len(batch_df)

                # Calculate final statistics
                stats = await self._get_final_tagging_stats(conn, table_name, schema)
                stats["processed_records"] = processed_records

                logger.info(f"Database tagging completed: {stats}")
                return stats

        except Exception as e:
            logger.error(f"Failed to tag CDR data from database: {e}")
            raise GeospatialError(f"Failed to tag CDR data from database: {e}")

    async def _ensure_vendor_columns(
        self, conn: asyncpg.Connection, table_name: str, schema: str
    ) -> None:
        """Ensure vendor columns exist in the table.

        Args:
            conn: Database connection
            table_name: Table name
            schema: Schema name
        """
        try:
            # Check if columns exist using parameterized query
            columns_query = """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = $1
                AND table_name = $2
                AND column_name IN ('vendor', 'polygon_id', 'confidence')
            """
            existing_columns = await conn.fetch(columns_query, schema, table_name)

            existing_columns = [
                row["column_name"] for row in await conn.fetch(columns_query)
            ]

            # Add missing columns
            if "vendor" not in existing_columns:
                await conn.execute(
                    f"ALTER TABLE {schema}.{table_name} ADD COLUMN vendor VARCHAR(50)"
                )

            if "polygon_id" not in existing_columns:
                await conn.execute(
                    f"ALTER TABLE {schema}.{table_name} ADD COLUMN polygon_id INTEGER"
                )

            if "confidence" not in existing_columns:
                await conn.execute(
                    f"ALTER TABLE {schema}.{table_name} ADD COLUMN confidence FLOAT"
                )

            logger.info("Vendor columns ensured in database table")

        except Exception as e:
            logger.error(f"Failed to ensure vendor columns: {e}")
            raise

    async def _update_batch_in_database(
        self,
        conn: asyncpg.Connection,
        tagged_batch: pd.DataFrame,
        table_name: str,
        schema: str,
    ) -> None:
        """Update batch records in database with vendor tags.

        Args:
            conn: Database connection
            tagged_batch: Tagged batch data
            table_name: Table name
            schema: Schema name
        """
        try:
            # Update records with vendor information
            for _, row in tagged_batch.iterrows():
                if pd.notna(row.get("vendor")):
                    update_query = f"""
                        UPDATE "{schema}"."{table_name}"
                        SET vendor = $1, polygon_id = $2, confidence = $3
                        WHERE ctid = $4
                    """

                    await conn.execute(
                        update_query,
                        row["vendor"],
                        row.get("polygon_id"),
                        row.get("confidence"),
                        row["row_id"],
                    )

        except Exception as e:
            logger.error(f"Failed to update batch in database: {e}")
            raise

    async def _get_final_tagging_stats(
        self, conn: asyncpg.Connection, table_name: str, schema: str
    ) -> Dict[str, Any]:
        """Get final tagging statistics from database.

        Args:
            conn: Database connection
            table_name: Table name
            schema: Schema name

        Returns:
            Dictionary with statistics
        """
        try:
            # Get overall statistics with safe identifiers
            stats_query = f"""
                SELECT
                    COUNT(*) as total_records,
                    COUNT(vendor) as tagged_records,
                    COUNT(*) - COUNT(vendor) as untagged_records
                FROM "{schema}"."{table_name}"
            """

            stats_result = await conn.fetchrow(stats_query)

            # Get vendor distribution with safe identifiers
            vendor_query = f"""
                SELECT vendor, COUNT(*) as count
                FROM "{schema}"."{table_name}"
                WHERE vendor IS NOT NULL
                GROUP BY vendor
                ORDER BY count DESC
            """

            vendor_results = await conn.fetch(vendor_query)
            vendor_distribution = {
                row["vendor"]: row["count"] for row in vendor_results
            }

            stats = {
                "total_records": stats_result["total_records"],
                "tagged_records": stats_result["tagged_records"],
                "untagged_records": stats_result["untagged_records"],
                "tagging_rate": stats_result["tagged_records"]
                / stats_result["total_records"]
                if stats_result["total_records"] > 0
                else 0,
                "vendor_distribution": vendor_distribution,
            }

            return stats

        except Exception as e:
            logger.error(f"Failed to get final tagging stats: {e}")
            return {"error": str(e)}

    async def clear_cache(self) -> None:
        """Clear the vendor cache."""
        self.vendor_cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
        logger.info("Vendor cache cleared")

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        return {
            "cache_size": len(self.vendor_cache),
            "cache_hits": self.cache_hits,
            "cache_misses": self.cache_misses,
            "hit_rate": self.cache_hits / (self.cache_hits + self.cache_misses)
            if (self.cache_hits + self.cache_misses) > 0
            else 0,
        }
