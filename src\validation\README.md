# Connect 统一验证框架

## 概述

Connect 统一验证框架是一个专为电信数据分析系统设计的高性能、可扩展的数据验证解决方案。该框架整合了分散在各个导入器中的验证逻辑，提供了统一的接口和强大的验证能力。

## 🎯 核心特性

### 🚀 高性能
- **大数据处理**: 支持500万行数据在10秒内完成验证
- **并行验证**: 支持多线程并行验证，充分利用多核CPU
- **内存优化**: 单机环境下内存使用控制在16GB以内
- **缓存机制**: 智能缓存验证框架，提升重复验证性能

### 🔧 灵活可扩展
- **模块化设计**: 验证器、规则、工厂类分离，易于扩展
- **插件化规则**: 支持自定义验证规则，满足特定业务需求
- **多数据类型**: 支持CDR、KPI、CFG等多种电信数据类型
- **配置驱动**: 通过配置文件灵活调整验证行为

### 🛡️ 企业级质量
- **全面测试**: 单元测试、集成测试、性能测试覆盖率>90%
- **错误处理**: 完善的异常处理机制，提供详细的错误信息
- **日志记录**: 结构化日志，支持问题追踪和性能分析
- **监控指标**: 内置性能指标收集，支持运维监控

### 📊 电信业务专业化
- **电信数据理解**: 深度理解CDR、KPI等电信业务数据特点
- **业务规则**: 内置电信行业标准验证规则
- **地理空间支持**: 支持基站、小区等地理空间数据验证
- **时序数据**: 专门优化时序数据的验证性能

## 🏗️ 架构设计

### 核心组件

```
统一验证框架
├── 核心层 (Core)
│   ├── ValidationFramework     # 主验证框架
│   ├── ValidationRule         # 验证规则抽象基类
│   ├── ValidationContext      # 验证上下文
│   └── ValidationResult       # 验证结果
├── 验证器层 (Validators)
│   ├── DataStructureValidator # 数据结构验证器
│   ├── DataValueValidator     # 数据值验证器
│   ├── TelecomDataValidator   # 电信数据验证器
│   ├── DatabaseValidator      # 数据库验证器
│   └── FileValidator          # 文件验证器
├── 规则层 (Rules)
│   ├── CDRValidationRules     # CDR验证规则
│   ├── KPIValidationRules     # KPI验证规则
│   └── CFGValidationRules     # CFG验证规则
├── 工厂层 (Factory)
│   └── ValidationFactory      # 验证工厂
└── 异常层 (Exceptions)
    └── ValidationError        # 验证异常
```

### 数据流

```
输入数据 → ValidationFactory → ValidationFramework → Validators → Rules → ValidationResult
    ↓              ↓                    ↓              ↓         ↓           ↓
  DataFrame    选择验证器           并行执行         应用规则   收集问题    返回结果
```

## 🚀 快速开始

### 安装依赖

```bash
pip install pandas numpy pydantic
```

### 基本使用

```python
from src.validation import validate_cdr, validate_kpi, get_validation_factory
import pandas as pd

# 方式1: 使用便捷函数
data = pd.DataFrame({
    'CALLING_NUMBER': ['1234567890'],
    'CALLED_NUMBER': ['0987654321'],
    'CALL_START_TIME': ['2023-01-01 10:00:00'],
    'CALL_END_TIME': ['2023-01-01 10:05:00'],
    'CALL_DURATION': [300],
    'CELL_ID': [1234],
    'LAC': [567],
    'IMSI': ['123456789012345']
})

result = validate_cdr(data)
print(f"验证结果: {'通过' if result.is_valid else '失败'}")
print(f"问题数量: {len(result.issues)}")

# 方式2: 使用工厂类
factory = get_validation_factory()
result = factory.validate_cdr(data)

# 方式3: 自定义配置
from src.validation.factory import ValidationFactory

custom_factory = ValidationFactory({
    'parallel_validation': True,
    'max_workers': 4,
    'cache_enabled': True,
    'strict_mode': False
})

result = custom_factory.validate_cdr(data)
```

### 文件验证

```python
from src.validation import validate_cdr_file

# 验证CSV文件
result = validate_cdr_file('data/cdr_data.csv')

if not result.is_valid:
    for issue in result.issues:
        print(f"错误: {issue.message} (行: {issue.row_index})")
```

### 批量验证

```python
from src.validation.factory import ValidationFactory

factory = ValidationFactory({
    'parallel_validation': True,
    'max_workers': 8
})

# 批量验证多个文件
files = ['data/cdr1.csv', 'data/cdr2.csv', 'data/cdr3.csv']
results = []

for file_path in files:
    result = factory.validate_cdr_file(file_path)
    results.append(result)
    print(f"{file_path}: {'✓' if result.is_valid else '✗'}")
```

## 📋 验证规则

### CDR验证规则

#### 数据结构验证
- 必需列检查: `CALLING_NUMBER`, `CALLED_NUMBER`, `CALL_START_TIME`, `CALL_END_TIME`, `CALL_DURATION`, `CELL_ID`, `LAC`, `IMSI`
- 数据行数检查: 不能为空
- 列类型检查: 确保数据类型正确

#### 数据值验证
- 空值检查: 关键字段不能为空
- 数值范围: `CALL_DURATION` >= 0
- 时间逻辑: `CALL_END_TIME` >= `CALL_START_TIME`
- 数据格式: 时间格式、电话号码格式等

#### 电信业务验证
- 电话号码格式: 符合电信标准
- IMSI格式: 15位数字
- 小区ID范围: 合理的小区ID范围
- 通话时长逻辑: 与开始结束时间一致

### KPI验证规则

#### 数据结构验证
- 必需列检查: `CELL_ID`, `KPI_NAME`, `KPI_VALUE`, `MEASUREMENT_TIME`, `LAC`
- 数据完整性检查

#### 数据值验证
- KPI值范围: 根据KPI类型验证合理范围
- 时间格式: 测量时间格式正确
- 数值类型: KPI值为数值类型

#### 电信业务验证
- KPI名称标准化: 符合电信KPI命名规范
- 测量时间序列: 时间序列的连续性和合理性
- 小区ID一致性: 与网络配置数据一致

## ⚙️ 配置选项

### 基本配置

```python
config = {
    # 并行验证
    'parallel_validation': True,
    'max_workers': 4,
    
    # 缓存设置
    'cache_enabled': True,
    'max_cache_size': 100,
    
    # 验证模式
    'strict_mode': True,
    'fail_fast': False,
    
    # 性能设置
    'batch_size': 10000,
    'timeout': 300,
    
    # 日志设置
    'log_level': 'INFO',
    'log_validation_details': False
}
```

### 高级配置

```python
advanced_config = {
    # 自定义验证器
    'custom_validators': {
        'structure': CustomStructureValidator,
        'value': CustomValueValidator
    },
    
    # 自定义规则
    'custom_rules': {
        'cdr': [CustomCDRRule1(), CustomCDRRule2()],
        'kpi': [CustomKPIRule1()]
    },
    
    # 性能优化
    'memory_limit_mb': 1024,
    'chunk_size': 50000,
    'use_multiprocessing': False,
    
    # 错误处理
    'max_errors_per_rule': 100,
    'continue_on_error': True,
    'error_sampling_rate': 0.1
}
```

## 🔧 自定义扩展

### 自定义验证规则

```python
from src.validation.core import ValidationRule, ValidationIssue, ValidationType, ValidationSeverity
import pandas as pd

class CustomPhoneNumberRule(ValidationRule):
    """自定义电话号码验证规则"""
    
    def __init__(self):
        super().__init__(
            name="custom_phone_number",
            description="验证电话号码格式",
            validation_type=ValidationType.VALUE,
            severity=ValidationSeverity.ERROR
        )
    
    def validate(self, data: pd.DataFrame, context=None) -> list:
        issues = []
        
        if 'CALLING_NUMBER' in data.columns:
            invalid_phones = data[~data['CALLING_NUMBER'].str.match(r'^1[3-9]\d{9}$')]
            
            for idx, row in invalid_phones.iterrows():
                issues.append(ValidationIssue(
                    rule_name=self.name,
                    message=f"无效的电话号码格式: {row['CALLING_NUMBER']}",
                    severity=self.severity,
                    row_index=idx,
                    column='CALLING_NUMBER',
                    value=row['CALLING_NUMBER']
                ))
        
        return issues

# 使用自定义规则
from src.validation.factory import ValidationFactory

factory = ValidationFactory({
    'custom_rules': {
        'cdr': [CustomPhoneNumberRule()]
    }
})

result = factory.validate_cdr(data)
```

### 自定义验证器

```python
from src.validation.validators import BaseValidator

class CustomBusinessValidator(BaseValidator):
    """自定义业务验证器"""
    
    def validate(self, data: pd.DataFrame, rules: list, context=None):
        all_issues = []
        
        for rule in rules:
            if hasattr(rule, 'validate_business_logic'):
                issues = rule.validate_business_logic(data, context)
                all_issues.extend(issues)
        
        return all_issues

# 注册自定义验证器
factory = ValidationFactory({
    'custom_validators': {
        'business': CustomBusinessValidator
    }
})
```

## 📊 性能优化

### 大数据处理

```python
# 分块处理大数据集
from src.validation.factory import ValidationFactory

def validate_large_dataset(file_path, chunk_size=50000):
    factory = ValidationFactory({
        'parallel_validation': True,
        'max_workers': 8,
        'batch_size': chunk_size
    })
    
    all_results = []
    
    for chunk in pd.read_csv(file_path, chunksize=chunk_size):
        result = factory.validate_cdr(chunk)
        all_results.append(result)
        
        if not result.is_valid:
            print(f"块验证失败: {len(result.issues)} 个问题")
    
    return all_results
```

### 并行验证

```python
from concurrent.futures import ThreadPoolExecutor

def parallel_file_validation(file_paths):
    factory = ValidationFactory({
        'parallel_validation': True,
        'max_workers': 4
    })
    
    def validate_file(file_path):
        return factory.validate_cdr_file(file_path)
    
    with ThreadPoolExecutor(max_workers=8) as executor:
        results = list(executor.map(validate_file, file_paths))
    
    return results
```

### 内存优化

```python
# 内存高效的验证配置
memory_efficient_config = {
    'parallel_validation': False,  # 减少内存使用
    'batch_size': 10000,          # 小批次处理
    'cache_enabled': False,       # 禁用缓存节省内存
    'fail_fast': True,           # 快速失败
    'memory_limit_mb': 512       # 内存限制
}

factory = ValidationFactory(memory_efficient_config)
```

## 🔍 监控和调试

### 性能监控

```python
from src.validation.factory import ValidationFactory
import time

factory = ValidationFactory({
    'performance_monitoring': True,
    'collect_metrics': True
})

# 执行验证
start_time = time.time()
result = factory.validate_cdr(data)
end_time = time.time()

# 获取性能指标
metrics = factory.get_performance_metrics()
print(f"验证时间: {end_time - start_time:.3f}s")
print(f"处理速度: {len(data) / (end_time - start_time):.0f} rows/s")
print(f"内存使用: {metrics.get('memory_usage_mb', 0):.2f}MB")
```

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

factory = ValidationFactory({
    'log_level': 'DEBUG',
    'log_validation_details': True,
    'debug_mode': True
})

result = factory.validate_cdr(data)

# 查看详细的验证报告
for issue in result.issues:
    print(f"规则: {issue.rule_name}")
    print(f"消息: {issue.message}")
    print(f"位置: 行{issue.row_index}, 列{issue.column}")
    print(f"值: {issue.value}")
    print("---")
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pytest src/validation/tests/ -v

# 运行特定测试
pytest src/validation/tests/test_core.py -v

# 运行性能测试
pytest src/validation/tests/test_performance.py -v

# 运行集成测试
pytest src/validation/tests/test_integration.py -v

# 生成覆盖率报告
pytest src/validation/tests/ --cov=src.validation --cov-report=html
```

### 性能基准测试

```bash
# 运行基准测试
python -m src.validation.tests.test_performance

# 内存使用测试
python -m pytest src/validation/tests/test_performance.py::TestMemoryPerformance -v

# 并发测试
python -m pytest src/validation/tests/test_performance.py::TestConcurrencyPerformance -v
```

## 📚 最佳实践

### 1. 性能优化
- 对于大数据集，启用并行验证
- 使用适当的批次大小避免内存溢出
- 启用缓存提升重复验证性能
- 在生产环境中禁用详细日志

### 2. 错误处理
- 使用 `fail_fast=False` 收集所有验证问题
- 实现自定义错误处理逻辑
- 记录验证失败的详细信息用于调试

### 3. 扩展性
- 将业务特定的验证规则封装为独立的类
- 使用配置文件管理验证规则
- 实现插件化的验证器架构

### 4. 监控
- 在生产环境中启用性能监控
- 设置适当的告警阈值
- 定期分析验证性能趋势

## 🔄 迁移指南

从现有验证逻辑迁移到统一验证框架，请参考 [迁移指南](migration_guide.md)。

## 📖 API 文档

详细的API文档请参考各模块的docstring和类型注解。

## 🤝 贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## 🆘 支持

如有问题或建议，请创建 Issue 或联系开发团队。

---

**Connect 统一验证框架** - 为电信数据分析提供企业级验证解决方案