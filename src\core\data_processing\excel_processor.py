# -*- coding: utf-8 -*-
"""
Excel Processor for Unified Data Processing

This module provides comprehensive Excel file processing capabilities
supporting .xlsx, .xls, and .xlsb formats with memory optimization.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable, AsyncGenerator

import pandas as pd
from .types import (
    ProcessingEngine, ProcessingMode, ProcessingStatus, DataFormat,
    ProcessingMetrics, ProcessingResult, ProcessingConfig, ChunkInfo
)
from .adapters import create_adapter, BaseAdapter
from ..utils.memory import MemoryMonitor
from ..utils.validation import validate_excel_structure


class ExcelProcessor:
    """High-performance Excel processor with multi-format support.
    
    Features:
    - Support for .xlsx, .xls, .xlsb formats
    - Multi-sheet processing
    - Memory-optimized chunked reading
    - Multiple engine support (Pandas/Polars)
    - Async processing capabilities
    - Comprehensive error handling
    - Performance monitoring
    """
    
    # Supported Excel formats
    SUPPORTED_FORMATS = {'.xlsx', '.xls', '.xlsb'}
    
    # Default engines for different formats
    DEFAULT_ENGINES = {
        '.xlsx': 'openpyxl',
        '.xls': 'xlrd',
        '.xlsb': 'pyxlsb'
    }
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        """Initialize Excel processor.
        
        Args:
            config: Processing configuration
        """
        self.config = config or ProcessingConfig()
        self.logger = logging.getLogger(__name__)
        self.memory_monitor = MemoryMonitor()
        
        # Processing state
        self._current_adapter: Optional[BaseAdapter] = None
        self._processing_metrics = ProcessingMetrics()
    
    async def read_excel(
        self,
        file_path: Union[str, Path],
        sheet_name: Union[str, int, List[str], None] = 0,
        engine: Optional[ProcessingEngine] = None,
        excel_engine: Optional[str] = None,
        chunk_size: Optional[int] = None,
        **kwargs
    ) -> Union[pd.DataFrame, Dict[str, pd.DataFrame], AsyncGenerator[pd.DataFrame, None]]:
        """Read Excel file with automatic optimization.
        
        Args:
            file_path: Path to Excel file
            sheet_name: Sheet name(s) to read (0 for first sheet, None for all)
            engine: Processing engine to use (Pandas/Polars)
            excel_engine: Excel reading engine (openpyxl, xlrd, pyxlsb)
            chunk_size: Size of chunks for processing (None for full read)
            **kwargs: Additional arguments for Excel reading
            
        Returns:
            DataFrame, dict of DataFrames, or async generator of DataFrame chunks
        """
        file_path = Path(file_path)
        start_time = time.time()
        
        try:
            # Validate file
            if not file_path.exists():
                raise FileNotFoundError(f"Excel file not found: {file_path}")
            
            # Validate format
            if file_path.suffix.lower() not in self.SUPPORTED_FORMATS:
                raise ValueError(
                    f"Unsupported Excel format: {file_path.suffix}. "
                    f"Supported formats: {', '.join(self.SUPPORTED_FORMATS)}"
                )
            
            # Determine Excel engine
            if excel_engine is None:
                excel_engine = self.DEFAULT_ENGINES.get(
                    file_path.suffix.lower(), 'openpyxl'
                )
            
            # Create adapter
            adapter = create_adapter(
                engine=engine,
                config=self.config,
                file_path=file_path
            )
            self._current_adapter = adapter
            
            # Prepare read parameters
            read_params = {
                'sheet_name': sheet_name,
                'engine': excel_engine,
                **kwargs
            }
            
            # Get sheet information
            sheet_info = await self._get_sheet_info(file_path, excel_engine)
            
            # Determine processing strategy
            if isinstance(sheet_name, list) or sheet_name is None:
                # Multiple sheets
                return await self._read_multiple_sheets(
                    adapter, file_path, sheet_info, read_params, chunk_size
                )
            else:
                # Single sheet
                return await self._read_single_sheet(
                    adapter, file_path, sheet_name, read_params, chunk_size
                )
                
        except Exception as e:
            self._processing_metrics.status = ProcessingStatus.FAILED
            self._processing_metrics.error_message = str(e)
            self.logger.error(f"Failed to read Excel file {file_path}: {e}")
            raise
    
    async def write_excel(
        self,
        data: Union[pd.DataFrame, Dict[str, pd.DataFrame]],
        file_path: Union[str, Path],
        engine: Optional[ProcessingEngine] = None,
        excel_engine: str = 'openpyxl',
        **kwargs
    ) -> ProcessingResult:
        """Write data to Excel file.
        
        Args:
            data: Data to write (DataFrame or dict of DataFrames)
            file_path: Output file path
            engine: Processing engine to use
            excel_engine: Excel writing engine
            **kwargs: Additional arguments for Excel writing
            
        Returns:
            Processing result
        """
        file_path = Path(file_path)
        start_time = time.time()
        
        try:
            # Create adapter
            adapter = create_adapter(
                engine=engine,
                config=self.config
            )
            
            # Prepare write parameters
            write_params = {
                'engine': excel_engine,
                **kwargs
            }
            
            # Write based on data type
            if isinstance(data, dict):
                # Multiple sheets
                total_records = await self._write_multiple_sheets(
                    adapter, data, file_path, write_params
                )
            else:
                # Single sheet
                total_records = await self._write_single_sheet(
                    adapter, data, file_path, write_params
                )
            
            # Create result
            result = ProcessingResult(
                status=ProcessingStatus.COMPLETED,
                records_processed=total_records,
                processing_time=time.time() - start_time,
                output_path=file_path,
                metrics=self._processing_metrics
            )
            
            self.logger.info(f"Successfully wrote {total_records:,} records to {file_path}")
            return result
            
        except Exception as e:
            result = ProcessingResult(
                status=ProcessingStatus.FAILED,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
            self.logger.error(f"Failed to write Excel file {file_path}: {e}")
            return result
    
    async def process_excel(
        self,
        file_path: Union[str, Path],
        processor_func: Callable,
        output_path: Optional[Union[str, Path]] = None,
        sheet_name: Union[str, int, List[str], None] = 0,
        engine: Optional[ProcessingEngine] = None,
        chunk_size: Optional[int] = None,
        mode: ProcessingMode = ProcessingMode.SYNC,
        **kwargs
    ) -> ProcessingResult:
        """Process Excel file with custom function.
        
        Args:
            file_path: Input Excel file path
            processor_func: Function to process data
            output_path: Output file path (optional)
            sheet_name: Sheet name(s) to process
            engine: Processing engine to use
            chunk_size: Size of chunks for processing
            mode: Processing mode (sequential/parallel/async)
            **kwargs: Additional arguments
            
        Returns:
            Processing result
        """
        file_path = Path(file_path)
        start_time = time.time()
        total_records = 0
        
        try:
            # Read Excel data
            data = await self.read_excel(
                file_path=file_path,
                sheet_name=sheet_name,
                engine=engine,
                chunk_size=chunk_size,
                **kwargs
            )
            
            # Process based on data type
            if isinstance(data, dict):
                # Multiple sheets
                processed_data = {}
                for sheet, df in data.items():
                    processed_df = await self._process_dataframe(
                        df, processor_func, mode
                    )
                    processed_data[sheet] = processed_df
                    total_records += len(processed_df)
                
                result_data = processed_data
            
            elif hasattr(data, '__aiter__'):
                # Chunked data
                processed_chunks = []
                async for chunk in data:
                    processed_chunk = await self._process_dataframe(
                        chunk, processor_func, mode
                    )
                    processed_chunks.append(processed_chunk)
                    total_records += len(processed_chunk)
                
                result_data = pd.concat(processed_chunks, ignore_index=True)
            
            else:
                # Single DataFrame
                result_data = await self._process_dataframe(
                    data, processor_func, mode
                )
                total_records = len(result_data)
            
            # Write output if specified
            if output_path:
                await self.write_excel(result_data, output_path, engine=engine)
            
            # Create result
            result = ProcessingResult(
                status=ProcessingStatus.COMPLETED,
                records_processed=total_records,
                processing_time=time.time() - start_time,
                output_path=Path(output_path) if output_path else None,
                metrics=self._processing_metrics
            )
            
            self.logger.info(
                f"Successfully processed {total_records:,} records in {result.processing_time:.2f}s"
            )
            return result
            
        except Exception as e:
            result = ProcessingResult(
                status=ProcessingStatus.FAILED,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
            self.logger.error(f"Failed to process Excel file {file_path}: {e}")
            return result
    
    async def get_sheet_names(
        self,
        file_path: Union[str, Path],
        excel_engine: Optional[str] = None
    ) -> List[str]:
        """Get list of sheet names in Excel file.
        
        Args:
            file_path: Path to Excel file
            excel_engine: Excel reading engine
            
        Returns:
            List of sheet names
        """
        file_path = Path(file_path)
        
        try:
            # Determine Excel engine
            if excel_engine is None:
                excel_engine = self.DEFAULT_ENGINES.get(
                    file_path.suffix.lower(), 'openpyxl'
                )
            
            # Get sheet information
            sheet_info = await self._get_sheet_info(file_path, excel_engine)
            return list(sheet_info.keys())
            
        except Exception as e:
            self.logger.error(f"Failed to get sheet names from {file_path}: {e}")
            return []
    
    async def validate_excel(
        self,
        file_path: Union[str, Path],
        validation_rules: Dict[str, Any],
        sheet_name: Union[str, int, None] = 0,
        engine: Optional[ProcessingEngine] = None,
        sample_size: Optional[int] = None
    ) -> Dict[str, Any]:
        """Validate Excel file structure and content.
        
        Args:
            file_path: Excel file path
            validation_rules: Validation rules
            sheet_name: Sheet to validate
            engine: Processing engine to use
            sample_size: Number of records to sample for validation
            
        Returns:
            Validation results
        """
        file_path = Path(file_path)
        
        try:
            # Basic structure validation
            structure_result = await validate_excel_structure(file_path)
            if not structure_result['is_valid']:
                return structure_result
            
            # Create adapter
            adapter = create_adapter(
                engine=engine,
                config=self.config,
                file_path=file_path
            )
            
            # Read sample data
            read_params = {'nrows': sample_size} if sample_size else {}
            data = await self.read_excel(
                file_path=file_path,
                sheet_name=sheet_name,
                engine=engine,
                **read_params
            )
            
            # Handle multiple sheets
            if isinstance(data, dict):
                # Validate first sheet or specified sheet
                if isinstance(sheet_name, str) and sheet_name in data:
                    data = data[sheet_name]
                else:
                    data = next(iter(data.values()))
            
            # Validate data content
            validation_result = await adapter.validate_data(data, validation_rules)
            
            # Combine structure and content validation
            combined_result = {
                **structure_result,
                **validation_result,
                'structure_valid': structure_result['is_valid'],
                'content_valid': validation_result['is_valid'],
                'is_valid': structure_result['is_valid'] and validation_result['is_valid']
            }
            
            return combined_result
            
        except Exception as e:
            return {
                'is_valid': False,
                'errors': [f"Validation error: {e}"]
            }
    
    def get_processing_metrics(self) -> ProcessingMetrics:
        """Get current processing metrics.
        
        Returns:
            Processing metrics
        """
        return self._processing_metrics
    
    async def _get_sheet_info(
        self,
        file_path: Path,
        excel_engine: str
    ) -> Dict[str, Dict[str, Any]]:
        """Get information about sheets in Excel file.
        
        Args:
            file_path: Path to Excel file
            excel_engine: Excel reading engine
            
        Returns:
            Dictionary with sheet information
        """
        try:
            # Use pandas to get basic sheet info
            excel_file = pd.ExcelFile(file_path, engine=excel_engine)
            sheet_info = {}
            
            for sheet_name in excel_file.sheet_names:
                # Get basic info without loading full data
                try:
                    # Read just the header to get column info
                    sample_df = pd.read_excel(
                        excel_file, sheet_name=sheet_name, nrows=0
                    )
                    
                    sheet_info[sheet_name] = {
                        'columns': list(sample_df.columns),
                        'column_count': len(sample_df.columns)
                    }
                except Exception as e:
                    self.logger.warning(f"Could not read sheet {sheet_name}: {e}")
                    sheet_info[sheet_name] = {
                        'columns': [],
                        'column_count': 0,
                        'error': str(e)
                    }
            
            excel_file.close()
            return sheet_info
            
        except Exception as e:
            self.logger.error(f"Failed to get sheet info: {e}")
            return {}
    
    async def _read_single_sheet(
        self,
        adapter: BaseAdapter,
        file_path: Path,
        sheet_name: Union[str, int],
        read_params: Dict[str, Any],
        chunk_size: Optional[int]
    ) -> Union[pd.DataFrame, AsyncGenerator[pd.DataFrame, None]]:
        """Read single Excel sheet.
        
        Args:
            adapter: Data processing adapter
            file_path: Path to Excel file
            sheet_name: Sheet name or index
            read_params: Parameters for reading
            chunk_size: Chunk size for processing
            
        Returns:
            DataFrame or async generator of chunks
        """
        # Update read parameters
        read_params['sheet_name'] = sheet_name
        
        # Determine if chunked processing is needed
        if chunk_size is None:
            chunk_size = await self._determine_chunk_size(file_path)
        
        if chunk_size and chunk_size > 0:
            # Chunked processing
            self.logger.info(f"Reading Excel sheet in chunks of {chunk_size:,} records")
            return self._read_excel_chunks(
                adapter, file_path, chunk_size, read_params
            )
        else:
            # Full sheet reading
            self.logger.info(f"Reading entire Excel sheet: {sheet_name}")
            data = await adapter.read_file(file_path, **read_params)
            
            # Convert to pandas if needed
            if hasattr(data, 'to_pandas'):
                data = data.to_pandas()
            
            # Update metrics
            self._processing_metrics.records_processed = len(data)
            self._processing_metrics.status = ProcessingStatus.COMPLETED
            
            return data
    
    async def _read_multiple_sheets(
        self,
        adapter: BaseAdapter,
        file_path: Path,
        sheet_info: Dict[str, Dict[str, Any]],
        read_params: Dict[str, Any],
        chunk_size: Optional[int]
    ) -> Dict[str, pd.DataFrame]:
        """Read multiple Excel sheets.
        
        Args:
            adapter: Data processing adapter
            file_path: Path to Excel file
            sheet_info: Information about sheets
            read_params: Parameters for reading
            chunk_size: Chunk size for processing
            
        Returns:
            Dictionary of DataFrames
        """
        result = {}
        total_records = 0
        
        # Determine which sheets to read
        sheet_name = read_params.get('sheet_name')
        if isinstance(sheet_name, list):
            sheets_to_read = sheet_name
        elif sheet_name is None:
            sheets_to_read = list(sheet_info.keys())
        else:
            sheets_to_read = [sheet_name]
        
        # Read each sheet
        for sheet in sheets_to_read:
            if sheet in sheet_info:
                try:
                    self.logger.info(f"Reading sheet: {sheet}")
                    
                    # Read single sheet
                    sheet_data = await self._read_single_sheet(
                        adapter, file_path, sheet, read_params.copy(), chunk_size
                    )
                    
                    # Handle chunked data
                    if hasattr(sheet_data, '__aiter__'):
                        chunks = []
                        async for chunk in sheet_data:
                            chunks.append(chunk)
                        sheet_data = pd.concat(chunks, ignore_index=True)
                    
                    result[sheet] = sheet_data
                    total_records += len(sheet_data)
                    
                except Exception as e:
                    self.logger.error(f"Failed to read sheet {sheet}: {e}")
                    # Continue with other sheets
                    continue
        
        # Update metrics
        self._processing_metrics.records_processed = total_records
        self._processing_metrics.status = ProcessingStatus.COMPLETED
        
        return result
    
    async def _write_single_sheet(
        self,
        adapter: BaseAdapter,
        data: pd.DataFrame,
        file_path: Path,
        write_params: Dict[str, Any]
    ) -> int:
        """Write single sheet to Excel file.
        
        Args:
            adapter: Data processing adapter
            data: DataFrame to write
            file_path: Output file path
            write_params: Parameters for writing
            
        Returns:
            Number of records written
        """
        # Convert data if needed
        if hasattr(adapter, 'from_pandas') and isinstance(data, pd.DataFrame):
            data = adapter.from_pandas(data)
        
        # Write file
        await adapter.write_file(data, file_path, **write_params)
        
        return len(data)
    
    async def _write_multiple_sheets(
        self,
        adapter: BaseAdapter,
        data: Dict[str, pd.DataFrame],
        file_path: Path,
        write_params: Dict[str, Any]
    ) -> int:
        """Write multiple sheets to Excel file.
        
        Args:
            adapter: Data processing adapter
            data: Dictionary of DataFrames
            file_path: Output file path
            write_params: Parameters for writing
            
        Returns:
            Total number of records written
        """
        total_records = 0
        
        # Use ExcelWriter for multiple sheets
        with pd.ExcelWriter(file_path, engine=write_params.get('engine', 'openpyxl')) as writer:
            for sheet_name, df in data.items():
                # Convert data if needed
                if hasattr(adapter, 'from_pandas') and not isinstance(df, pd.DataFrame):
                    df = df.to_pandas()
                
                # Write sheet
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                total_records += len(df)
        
        return total_records
    
    async def _determine_chunk_size(self, file_path: Path) -> Optional[int]:
        """Determine optimal chunk size for Excel file.
        
        Args:
            file_path: Path to file
            
        Returns:
            Optimal chunk size or None for full read
        """
        try:
            file_size = file_path.stat().st_size
            
            # Use chunking for files larger than threshold
            if file_size > self.config.chunk_size_threshold:
                # Excel files are typically more memory-intensive than CSV
                # Use smaller chunk sizes
                chunk_size = min(
                    self.config.max_chunk_size // 2,  # Smaller chunks for Excel
                    max(self.config.min_chunk_size, 10000)  # Minimum 10k records
                )
                
                self.logger.debug(f"Excel file size: {file_size / (1024*1024):.1f}MB, chunk size: {chunk_size:,}")
                return chunk_size
            else:
                return None  # Read entire file
                
        except Exception as e:
            self.logger.warning(f"Failed to determine chunk size: {e}")
            return self.config.default_chunk_size // 2
    
    async def _read_excel_chunks(
        self,
        adapter: BaseAdapter,
        file_path: Path,
        chunk_size: int,
        read_params: Dict[str, Any]
    ) -> AsyncGenerator[pd.DataFrame, None]:
        """Read Excel file in chunks.
        
        Args:
            adapter: Data processing adapter
            file_path: Path to Excel file
            chunk_size: Size of each chunk
            read_params: Parameters for reading
            
        Yields:
            DataFrame chunks
        """
        try:
            chunk_count = 0
            total_records = 0
            skip_rows = 0
            
            while True:
                try:
                    # Read chunk with skiprows and nrows
                    chunk_params = read_params.copy()
                    chunk_params.update({
                        'skiprows': skip_rows if skip_rows > 0 else None,
                        'nrows': chunk_size
                    })
                    
                    # Read chunk
                    chunk = await adapter.read_file(file_path, **chunk_params)
                    
                    # Convert to pandas if needed
                    if hasattr(chunk, 'to_pandas'):
                        chunk = chunk.to_pandas()
                    
                    # Check if we got any data
                    if len(chunk) == 0:
                        break
                    
                    chunk_count += 1
                    total_records += len(chunk)
                    
                    # Update metrics
                    self._processing_metrics.chunks_processed = chunk_count
                    self._processing_metrics.records_processed = total_records
                    
                    # Create chunk info
                    chunk_info = ChunkInfo(
                        chunk_id=chunk_count,
                        start_row=skip_rows,
                        end_row=skip_rows + len(chunk) - 1,
                        record_count=len(chunk)
                    )
                    
                    # Add chunk info as metadata
                    chunk.attrs = {'chunk_info': chunk_info}
                    
                    self.logger.debug(f"Processed Excel chunk {chunk_count}: {len(chunk):,} records")
                    
                    yield chunk
                    
                    # Update skip_rows for next chunk
                    skip_rows += len(chunk)
                    
                    # If we got less than chunk_size, we're done
                    if len(chunk) < chunk_size:
                        break
                    
                    # Memory management
                    if chunk_count % 5 == 0:  # Every 5 chunks (Excel is more memory intensive)
                        if self.memory_monitor.should_gc():
                            await self.memory_monitor.force_gc()
                
                except Exception as e:
                    if "No columns to parse from file" in str(e) or "Worksheet index" in str(e):
                        # End of file or sheet
                        break
                    else:
                        raise
            
            self.logger.info(
                f"Completed Excel chunked reading: {chunk_count} chunks, {total_records:,} total records"
            )
            
        except Exception as e:
            self.logger.error(f"Error in Excel chunked reading: {e}")
            raise
    
    async def _process_dataframe(
        self,
        data: pd.DataFrame,
        processor_func: Callable,
        mode: ProcessingMode
    ) -> pd.DataFrame:
        """Process DataFrame with specified mode.
        
        Args:
            data: DataFrame to process
            processor_func: Processing function
            mode: Processing mode
            
        Returns:
            Processed DataFrame
        """
        try:
            if mode == ProcessingMode.ASYNC:
                # Run processor in thread pool
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, processor_func, data)
            else:
                # Sequential processing
                result = processor_func(data)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing DataFrame: {e}")
            raise