#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect平台测试数据生成器测试

本模块测试TestDataGenerator类的功能。

作者: Connect质量工程团队
日期: 2024-01-20
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from tests.fixtures.data_generator import TestDataGenerator


class TestDataGeneratorTests:
    """测试数据生成器测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.generator = TestDataGenerator(output_dir=self.temp_dir)
    
    def teardown_method(self):
        """测试后清理"""
        if Path(self.temp_dir).exists():
            shutil.rmtree(self.temp_dir)
    
    def test_generator_initialization(self):
        """测试生成器初始化"""
        assert self.generator.output_dir.exists()
        assert len(self.generator.roles) > 0
        assert len(self.generator.departments) > 0
        assert len(self.generator.site_types) > 0
        assert len(self.generator.operators) > 0
        assert len(self.generator.technologies) > 0
    
    def test_generate_users(self):
        """测试用户数据生成"""
        users = self.generator.generate_users(count=10)
        assert len(users) == 10
        
        for user in users:
            assert hasattr(user, 'id')
            assert hasattr(user, 'username')
            assert hasattr(user, 'email')
            assert hasattr(user, 'role')
            assert hasattr(user, 'department')
            assert hasattr(user, 'created_at')
            assert user.role in self.generator.roles
            assert user.department in self.generator.departments
    
    def test_generate_sites(self):
        """测试站点数据生成"""
        sites = self.generator.generate_sites(count=5)
        assert len(sites) == 5
        
        for site in sites:
            assert hasattr(site, 'id')
            assert hasattr(site, 'site_name')
            assert hasattr(site, 'latitude')
            assert hasattr(site, 'longitude')
            assert hasattr(site, 'site_type')
            assert hasattr(site, 'operator')
            assert hasattr(site, 'technology')
            assert site.site_type in self.generator.site_types
            assert site.operator in self.generator.operators
            assert site.technology in self.generator.technologies
            assert -90 <= site.latitude <= 90
            assert -180 <= site.longitude <= 180
    
    def test_generate_cdr_data(self):
        """测试CDR数据生成"""
        cdr_data = self.generator.generate_cdr_data(count=20)
        assert len(cdr_data) == 20
        
        for record in cdr_data:
            assert hasattr(record, 'id')
            assert hasattr(record, 'call_id')
            assert hasattr(record, 'start_time')
            assert hasattr(record, 'end_time')
            assert hasattr(record, 'duration')
            assert hasattr(record, 'caller_number')
            assert hasattr(record, 'called_number')
            assert hasattr(record, 'cell_id')
            assert hasattr(record, 'latitude')
            assert hasattr(record, 'longitude')
            assert record.duration >= 0
    
    def test_generate_ep_data(self):
        """测试EP数据生成"""
        ep_data = self.generator.generate_ep_data(count=15)
        assert len(ep_data) == 15
        
        for record in ep_data:
            assert hasattr(record, 'id')
            assert hasattr(record, 'site_id')
            assert hasattr(record, 'start_time')
            assert hasattr(record, 'rsrp')
            assert hasattr(record, 'rsrq')
            assert hasattr(record, 'sinr')
            assert hasattr(record, 'throughput_dl')
            assert hasattr(record, 'throughput_ul')
            assert hasattr(record, 'latency')
            assert hasattr(record, 'packet_loss')
            assert -140 <= record.rsrp <= -44
            assert -20 <= record.rsrq <= -3
            assert -10 <= record.sinr <= 30
            assert 0 <= record.packet_loss <= 5
    
    def test_generate_large_dataset(self):
        """测试大数据集生成"""
        datasets = self.generator.generate_large_dataset()
        
        assert 'users' in datasets
        assert 'sites' in datasets
        assert 'cdr_data' in datasets
        assert 'ep_data' in datasets
        
        assert len(datasets['users']) > 0
        assert len(datasets['sites']) > 0
        assert len(datasets['cdr_data']) > 0
        assert len(datasets['ep_data']) > 0
    
    def test_save_to_json(self):
        """测试JSON保存功能"""
        test_data = [{'id': 1, 'name': 'test'}]
        self.generator.save_to_json(test_data, 'test_data')
        
        json_file = self.generator.output_dir / 'test_data.json'
        assert json_file.exists()
        
        import json
        with open(json_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        assert loaded_data == test_data
    
    def test_save_to_csv(self):
        """测试CSV保存功能"""
        test_data = [{'id': 1, 'name': 'test'}, {'id': 2, 'name': 'test2'}]
        self.generator.save_to_csv(test_data, 'test_data')
        
        csv_file = self.generator.output_dir / 'test_data.csv'
        assert csv_file.exists()
        
        import pandas as pd
        df = pd.read_csv(csv_file)
        assert len(df) == 2
        assert 'id' in df.columns
        assert 'name' in df.columns
    
    def test_save_to_sqlite(self):
        """测试SQLite保存功能"""
        datasets = {
            'test_table': [{'id': 1, 'name': 'test'}]
        }
        self.generator.save_to_sqlite(datasets)
        
        db_file = self.generator.output_dir / 'test_data.db'
        assert db_file.exists()
        
        import sqlite3
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='test_table'")
        assert cursor.fetchone() is not None
        
        # 检查数据
        cursor.execute("SELECT * FROM test_table")
        rows = cursor.fetchall()
        assert len(rows) == 1
        
        conn.close()
    
    @pytest.mark.performance
    def test_generate_performance_test_data(self):
        """测试性能测试数据生成"""
        datasets = self.generator.generate_performance_test_data(size='small')
        
        assert 'users' in datasets
        assert 'sites' in datasets
        assert 'cdr_data' in datasets
        assert 'ep_data' in datasets
        
        # 小数据集应该有合理的数量
        assert len(datasets['users']) >= 10
        assert len(datasets['sites']) >= 50
        assert len(datasets['cdr_data']) >= 1000
        assert len(datasets['ep_data']) >= 500
    
    @pytest.mark.slow
    def test_generate_large_performance_data(self):
        """测试大型性能测试数据生成"""
        datasets = self.generator.generate_performance_test_data(size='large')
        
        # 大数据集应该有更多数据
        assert len(datasets['cdr_data']) >= 100000
        assert len(datasets['ep_data']) >= 50000