"""Excel data importer.

This module provides Excel import functionality for various data formats.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from .base import BaseImporter, ImportError, ImportResult

# Configure logging
logger = logging.getLogger(__name__)


class ExcelImporter(BaseImporter):
    """Excel data importer."""

    def __init__(
        self, source_path: Union[str, Path], sheet_name: Union[str, int] = 0, **kwargs
    ):
        """Initialize Excel importer.

        Args:
            source_path: Path to the Excel file
            sheet_name: Name or index of the sheet to import
            **kwargs: Additional configuration options
        """
        super().__init__(source_path, **kwargs)
        self.sheet_name = sheet_name

        # Validate Excel file extension
        if self.source_path.suffix.lower() not in [".xlsx", ".xls", ".xlsm"]:
            logger.warning(
                f"File extension {self.source_path.suffix} may not be an Excel file"
            )

    async def import_data(self, **kwargs) -> ImportResult:
        """Import data from Excel file.

        Args:
            **kwargs: Additional import options
                - header: Row number to use as column names (default: 0)
                - skiprows: Rows to skip at the beginning
                - nrows: Number of rows to read
                - usecols: Columns to use
                - dtype: Data type for columns

        Returns:
            ImportResult: Result of the import operation

        Raises:
            ImportError: If import fails
        """
        try:
            self.validate_source()

            header = kwargs.get("header", 0)
            skiprows = kwargs.get("skiprows", None)
            nrows = kwargs.get("nrows", None)
            usecols = kwargs.get("usecols", None)
            dtype = kwargs.get("dtype", None)

            # Read Excel file
            df = pd.read_excel(
                self.source_path,
                sheet_name=self.sheet_name,
                header=header,
                skiprows=skiprows,
                nrows=nrows,
                usecols=usecols,
                dtype=dtype,
            )

            records_imported = len(df)
            file_size = self.source_path.stat().st_size

            logger.info(
                f"Successfully imported {records_imported} records from {self.source_path}"
            )

            return ImportResult(
                success=True,
                source_path=self.source_path,
                records_imported=records_imported,
                file_size_bytes=file_size,
                metadata={
                    "sheet_name": self.sheet_name,
                    "columns": list(df.columns),
                    "shape": df.shape,
                    "data_types": df.dtypes.to_dict(),
                },
            )

        except Exception as e:
            error_msg = f"Failed to import Excel: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ImportResult(
                success=False, source_path=self.source_path, error_message=error_msg
            )

    def get_sheet_names(self) -> List[str]:
        """Get list of sheet names in the Excel file.

        Returns:
            List[str]: List of sheet names
        """
        try:
            self.validate_source()
            excel_file = pd.ExcelFile(self.source_path)
            return excel_file.sheet_names
        except Exception as e:
            logger.error(f"Failed to get sheet names: {e}")
            return []

    def preview_data(self, num_rows: int = 5) -> Optional[pd.DataFrame]:
        """Preview first few rows of Excel data.

        Args:
            num_rows: Number of rows to preview

        Returns:
            DataFrame: Preview of the data
        """
        try:
            self.validate_source()
            return pd.read_excel(
                self.source_path, sheet_name=self.sheet_name, nrows=num_rows
            )
        except Exception as e:
            logger.error(f"Failed to preview Excel data: {e}")
            return None

    def get_sheet_info(self) -> Dict[str, Any]:
        """Get information about all sheets in the Excel file.

        Returns:
            Dict: Sheet information
        """
        try:
            self.validate_source()
            excel_file = pd.ExcelFile(self.source_path)

            sheet_info = {}
            for sheet_name in excel_file.sheet_names:
                try:
                    # Read just a sample to get info
                    sample_df = pd.read_excel(
                        excel_file, sheet_name=sheet_name, nrows=0
                    )
                    sheet_info[sheet_name] = {
                        "columns": list(sample_df.columns),
                        "num_columns": len(sample_df.columns),
                    }
                except Exception as e:
                    sheet_info[sheet_name] = {"error": str(e)}

            return {"sheets": sheet_info, "num_sheets": len(excel_file.sheet_names)}

        except Exception as e:
            logger.error(f"Failed to get sheet info: {e}")
            return {}
