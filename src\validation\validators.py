"""Concrete validator implementations.

This module provides specific validator classes that implement common
validation patterns used throughout the Connect system.
"""

import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

import pandas as pd
from loguru import logger

from ..types.telecom_types import DataSourceType
from .core import (
    ValidationContext,
    ValidationFramework,
    ValidationIssue,
    ValidationResult,
    ValidationRule,
    ValidationSeverity,
    ValidationType,
)
from .exceptions import (
    DataValidationError,
    FileValidationError,
    SchemaValidationError,
    TelecomValidationError,
)


class DataStructureValidator(ValidationRule):
    """Validator for data structure requirements."""
    
    def __init__(
        self,
        name: str,
        required_columns: List[str],
        optional_columns: Optional[List[str]] = None,
        forbidden_columns: Optional[List[str]] = None,
        min_rows: int = 0,
        max_rows: Optional[int] = None,
        **kwargs
    ):
        super().__init__(
            name=name,
            validation_type=ValidationType.SCHEMA,
            **kwargs
        )
        self.required_columns = required_columns
        self.optional_columns = optional_columns or []
        self.forbidden_columns = forbidden_columns or []
        self.min_rows = min_rows
        self.max_rows = max_rows
    
    def validate(self, data: Any, context: Optional[ValidationContext] = None) -> ValidationResult:
        """Validate data structure."""
        result = ValidationResult(success=True, total_rules=1)
        
        if not isinstance(data, pd.DataFrame):
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=self.severity,
                message=f"Expected DataFrame, got {type(data).__name__}"
            )
            result.add_issue(issue)
            return result
        
        # Check required columns
        missing_columns = [col for col in self.required_columns if col not in data.columns]
        if missing_columns:
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=self.severity,
                message=f"Missing required columns: {missing_columns}",
                details={"missing_columns": missing_columns}
            )
            result.add_issue(issue)
        
        # Check forbidden columns
        forbidden_present = [col for col in self.forbidden_columns if col in data.columns]
        if forbidden_present:
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=ValidationSeverity.WARNING,
                message=f"Forbidden columns present: {forbidden_present}",
                details={"forbidden_columns": forbidden_present}
            )
            result.add_issue(issue)
        
        # Check row count
        if len(data) < self.min_rows:
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=self.severity,
                message=f"Insufficient rows: {len(data)} < {self.min_rows}",
                details={"actual_rows": len(data), "min_rows": self.min_rows}
            )
            result.add_issue(issue)
        
        if self.max_rows and len(data) > self.max_rows:
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=ValidationSeverity.WARNING,
                message=f"Too many rows: {len(data)} > {self.max_rows}",
                details={"actual_rows": len(data), "max_rows": self.max_rows}
            )
            result.add_issue(issue)
        
        if not result.issues:
            result.passed_rules = 1
        
        return result


class DataValueValidator(ValidationRule):
    """Validator for data value requirements."""
    
    def __init__(
        self,
        name: str,
        column_rules: Dict[str, Dict[str, Any]],
        allow_nulls: bool = True,
        null_columns: Optional[Set[str]] = None,
        **kwargs
    ):
        super().__init__(
            name=name,
            validation_type=ValidationType.DATA_TYPE,
            **kwargs
        )
        self.column_rules = column_rules
        self.allow_nulls = allow_nulls
        self.null_columns = null_columns or set()
    
    def validate(self, data: Any, context: Optional[ValidationContext] = None) -> ValidationResult:
        """Validate data values."""
        result = ValidationResult(success=True, total_rules=len(self.column_rules))
        
        if not isinstance(data, pd.DataFrame):
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=self.severity,
                message=f"Expected DataFrame, got {type(data).__name__}"
            )
            result.add_issue(issue)
            return result
        
        for column, rules in self.column_rules.items():
            if column not in data.columns:
                continue
            
            column_data = data[column]
            
            # Check null values
            if not self.allow_nulls and column not in self.null_columns:
                null_count = column_data.isnull().sum()
                if null_count > 0:
                    issue = ValidationIssue(
                        rule_name=self.name,
                        validation_type=self.validation_type,
                        severity=self.severity,
                        message=f"Column {column} contains {null_count} null values",
                        column=column,
                        details={"null_count": null_count}
                    )
                    result.add_issue(issue)
            
            # Check data type
            if "dtype" in rules:
                expected_dtype = rules["dtype"]
                if not pd.api.types.is_dtype_equal(column_data.dtype, expected_dtype):
                    issue = ValidationIssue(
                        rule_name=self.name,
                        validation_type=self.validation_type,
                        severity=ValidationSeverity.WARNING,
                        message=f"Column {column} has unexpected dtype: {column_data.dtype} (expected: {expected_dtype})",
                        column=column,
                        details={"actual_dtype": str(column_data.dtype), "expected_dtype": str(expected_dtype)}
                    )
                    result.add_issue(issue)
            
            # Check range
            if "min_value" in rules or "max_value" in rules:
                numeric_data = pd.to_numeric(column_data, errors="coerce")
                
                if "min_value" in rules:
                    min_val = rules["min_value"]
                    invalid_count = (numeric_data < min_val).sum()
                    if invalid_count > 0:
                        issue = ValidationIssue(
                            rule_name=self.name,
                            validation_type=ValidationType.RANGE,
                            severity=self.severity,
                            message=f"Column {column} has {invalid_count} values below minimum {min_val}",
                            column=column,
                            details={"invalid_count": invalid_count, "min_value": min_val}
                        )
                        result.add_issue(issue)
                
                if "max_value" in rules:
                    max_val = rules["max_value"]
                    invalid_count = (numeric_data > max_val).sum()
                    if invalid_count > 0:
                        issue = ValidationIssue(
                            rule_name=self.name,
                            validation_type=ValidationType.RANGE,
                            severity=self.severity,
                            message=f"Column {column} has {invalid_count} values above maximum {max_val}",
                            column=column,
                            details={"invalid_count": invalid_count, "max_value": max_val}
                        )
                        result.add_issue(issue)
            
            # Check pattern
            if "pattern" in rules:
                pattern = rules["pattern"]
                if isinstance(pattern, str):
                    pattern = re.compile(pattern)
                
                string_data = column_data.astype(str)
                invalid_mask = ~string_data.str.match(pattern, na=False)
                invalid_count = invalid_mask.sum()
                
                if invalid_count > 0:
                    issue = ValidationIssue(
                        rule_name=self.name,
                        validation_type=ValidationType.PATTERN,
                        severity=self.severity,
                        message=f"Column {column} has {invalid_count} values not matching pattern",
                        column=column,
                        details={"invalid_count": invalid_count, "pattern": rules["pattern"]}
                    )
                    result.add_issue(issue)
        
        if not result.issues:
            result.passed_rules = len(self.column_rules)
        
        return result


class TelecomDataValidator(ValidationRule):
    """Validator for telecom-specific data requirements."""
    
    def __init__(
        self,
        name: str,
        data_source_type: DataSourceType,
        **kwargs
    ):
        super().__init__(
            name=name,
            validation_type=ValidationType.TELECOM_SPECIFIC,
            **kwargs
        )
        self.data_source_type = data_source_type
    
    def validate(self, data: Any, context: Optional[ValidationContext] = None) -> ValidationResult:
        """Validate telecom-specific requirements."""
        result = ValidationResult(success=True, total_rules=1)
        
        if not isinstance(data, pd.DataFrame):
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=self.severity,
                message=f"Expected DataFrame, got {type(data).__name__}"
            )
            result.add_issue(issue)
            return result
        
        if self.data_source_type == DataSourceType.CDR:
            self._validate_cdr_data(data, result)
        elif self.data_source_type == DataSourceType.KPI:
            self._validate_kpi_data(data, result)
        elif self.data_source_type == DataSourceType.CFG:
            self._validate_cfg_data(data, result)
        
        if not result.issues:
            result.passed_rules = 1
        
        return result
    
    def _validate_cdr_data(self, data: pd.DataFrame, result: ValidationResult) -> None:
        """Validate CDR-specific requirements."""
        # Validate call duration consistency
        if all(col in data.columns for col in ["CALL_START_TIME", "CALL_END_TIME", "CALL_DURATION"]):
            start_times = pd.to_datetime(data["CALL_START_TIME"], errors="coerce")
            end_times = pd.to_datetime(data["CALL_END_TIME"], errors="coerce")
            durations = pd.to_numeric(data["CALL_DURATION"], errors="coerce")
            
            # Check for end time before start time
            invalid_times = (end_times < start_times) & start_times.notnull() & end_times.notnull()
            if invalid_times.any():
                count = invalid_times.sum()
                issue = ValidationIssue(
                    rule_name=self.name,
                    validation_type=self.validation_type,
                    severity=self.severity,
                    message=f"Found {count} records where call end time is before start time",
                    details={"invalid_count": count}
                )
                result.add_issue(issue)
            
            # Check for negative durations
            negative_durations = (durations < 0) & durations.notnull()
            if negative_durations.any():
                count = negative_durations.sum()
                issue = ValidationIssue(
                    rule_name=self.name,
                    validation_type=self.validation_type,
                    severity=self.severity,
                    message=f"Found {count} records with negative call duration",
                    details={"invalid_count": count}
                )
                result.add_issue(issue)
    
    def _validate_kpi_data(self, data: pd.DataFrame, result: ValidationResult) -> None:
        """Validate KPI-specific requirements."""
        # Validate KPI values are numeric
        if "KPI_VALUE" in data.columns:
            kpi_values = pd.to_numeric(data["KPI_VALUE"], errors="coerce")
            invalid_count = kpi_values.isnull().sum() - data["KPI_VALUE"].isnull().sum()
            
            if invalid_count > 0:
                issue = ValidationIssue(
                    rule_name=self.name,
                    validation_type=self.validation_type,
                    severity=self.severity,
                    message=f"Found {invalid_count} non-numeric KPI values",
                    column="KPI_VALUE",
                    details={"invalid_count": invalid_count}
                )
                result.add_issue(issue)
        
        # Validate measurement time format
        if "MEASUREMENT_TIME" in data.columns:
            measurement_times = pd.to_datetime(data["MEASUREMENT_TIME"], errors="coerce")
            invalid_count = measurement_times.isnull().sum() - data["MEASUREMENT_TIME"].isnull().sum()
            
            if invalid_count > 0:
                issue = ValidationIssue(
                    rule_name=self.name,
                    validation_type=self.validation_type,
                    severity=self.severity,
                    message=f"Found {invalid_count} invalid measurement time formats",
                    column="MEASUREMENT_TIME",
                    details={"invalid_count": invalid_count}
                )
                result.add_issue(issue)
    
    def _validate_cfg_data(self, data: pd.DataFrame, result: ValidationResult) -> None:
        """Validate configuration data requirements."""
        # Validate CONFIG_ID is not null and unique
        if "CONFIG_ID" in data.columns:
            null_count = data["CONFIG_ID"].isnull().sum()
            if null_count > 0:
                issue = ValidationIssue(
                    rule_name=self.name,
                    validation_type=self.validation_type,
                    severity=self.severity,
                    message=f"Found {null_count} null CONFIG_ID values",
                    column="CONFIG_ID",
                    details={"null_count": null_count}
                )
                result.add_issue(issue)
            
            duplicate_count = data["CONFIG_ID"].duplicated().sum()
            if duplicate_count > 0:
                issue = ValidationIssue(
                    rule_name=self.name,
                    validation_type=ValidationType.UNIQUENESS,
                    severity=ValidationSeverity.WARNING,
                    message=f"Found {duplicate_count} duplicate CONFIG_ID values",
                    column="CONFIG_ID",
                    details={"duplicate_count": duplicate_count}
                )
                result.add_issue(issue)


class DatabaseValidator(ValidationRule):
    """Validator for database-related requirements."""
    
    def __init__(
        self,
        name: str,
        max_name_length: int = 63,
        name_pattern: str = r"^[a-zA-Z][a-zA-Z0-9_]*$",
        **kwargs
    ):
        super().__init__(
            name=name,
            validation_type=ValidationType.SCHEMA,
            **kwargs
        )
        self.max_name_length = max_name_length
        self.name_pattern = re.compile(name_pattern)
    
    def validate(self, data: Any, context: Optional[ValidationContext] = None) -> ValidationResult:
        """Validate database naming requirements."""
        result = ValidationResult(success=True, total_rules=1)
        
        if isinstance(data, str):
            # Validate single name
            self._validate_name(data, result)
        elif isinstance(data, (list, tuple)):
            # Validate list of names
            for name in data:
                if isinstance(name, str):
                    self._validate_name(name, result)
        elif isinstance(data, pd.DataFrame):
            # Validate column names
            for column in data.columns:
                self._validate_name(column, result, context="column")
        
        if not result.issues:
            result.passed_rules = 1
        
        return result
    
    def _validate_name(self, name: str, result: ValidationResult, context: str = "name") -> None:
        """Validate a single name."""
        if not name:
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=self.severity,
                message=f"Empty {context}: '{name}'",
                details={"context": context, "name": name}
            )
            result.add_issue(issue)
            return
        
        if len(name) > self.max_name_length:
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=self.severity,
                message=f"{context.capitalize()} too long: '{name}' ({len(name)} > {self.max_name_length})",
                details={"context": context, "name": name, "length": len(name), "max_length": self.max_name_length}
            )
            result.add_issue(issue)
        
        if not self.name_pattern.match(name):
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=ValidationType.PATTERN,
                severity=self.severity,
                message=f"Invalid {context} format: '{name}'",
                details={"context": context, "name": name, "pattern": self.name_pattern.pattern}
            )
            result.add_issue(issue)


class FileValidator(ValidationRule):
    """Validator for file-related requirements."""
    
    def __init__(
        self,
        name: str,
        allowed_extensions: Optional[List[str]] = None,
        max_size_mb: Optional[float] = None,
        min_size_mb: Optional[float] = None,
        **kwargs
    ):
        super().__init__(
            name=name,
            validation_type=ValidationType.FILE_FORMAT,
            **kwargs
        )
        self.allowed_extensions = [ext.lower() for ext in (allowed_extensions or [])]
        self.max_size_mb = max_size_mb
        self.min_size_mb = min_size_mb
    
    def validate(self, data: Any, context: Optional[ValidationContext] = None) -> ValidationResult:
        """Validate file requirements."""
        result = ValidationResult(success=True, total_rules=1)
        
        file_path = None
        if isinstance(data, (str, Path)):
            file_path = Path(data)
        elif context and context.file_path:
            file_path = Path(context.file_path)
        
        if not file_path:
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=self.severity,
                message="No file path provided for validation"
            )
            result.add_issue(issue)
            return result
        
        # Check if file exists
        if not file_path.exists():
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=self.severity,
                message=f"File does not exist: {file_path}",
                details={"file_path": str(file_path)}
            )
            result.add_issue(issue)
            return result
        
        # Check file extension
        if self.allowed_extensions:
            file_ext = file_path.suffix.lower()
            if file_ext not in self.allowed_extensions:
                issue = ValidationIssue(
                    rule_name=self.name,
                    validation_type=self.validation_type,
                    severity=self.severity,
                    message=f"Invalid file extension: {file_ext} (allowed: {self.allowed_extensions})",
                    details={"file_extension": file_ext, "allowed_extensions": self.allowed_extensions}
                )
                result.add_issue(issue)
        
        # Check file size
        try:
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            
            if self.min_size_mb and file_size_mb < self.min_size_mb:
                issue = ValidationIssue(
                    rule_name=self.name,
                    validation_type=self.validation_type,
                    severity=self.severity,
                    message=f"File too small: {file_size_mb:.2f}MB < {self.min_size_mb}MB",
                    details={"file_size_mb": file_size_mb, "min_size_mb": self.min_size_mb}
                )
                result.add_issue(issue)
            
            if self.max_size_mb and file_size_mb > self.max_size_mb:
                issue = ValidationIssue(
                    rule_name=self.name,
                    validation_type=self.validation_type,
                    severity=self.severity,
                    message=f"File too large: {file_size_mb:.2f}MB > {self.max_size_mb}MB",
                    details={"file_size_mb": file_size_mb, "max_size_mb": self.max_size_mb}
                )
                result.add_issue(issue)
        
        except OSError as e:
            issue = ValidationIssue(
                rule_name=self.name,
                validation_type=self.validation_type,
                severity=ValidationSeverity.WARNING,
                message=f"Could not check file size: {e}",
                details={"error": str(e)}
            )
            result.add_issue(issue)
        
        if not result.issues:
            result.passed_rules = 1
        
        return result