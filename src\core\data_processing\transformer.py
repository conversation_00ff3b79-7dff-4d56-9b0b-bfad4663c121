# -*- coding: utf-8 -*-
"""
Data Transformer for Unified Data Processing

This module provides advanced data transformation pipelines with support
for telecommunications-specific transformations and optimizations.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import asyncio
import logging
import time
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable, Tuple
from enum import Enum
from dataclasses import dataclass, field

import pandas as pd
import numpy as np
from .types import (
    ProcessingEngine, ProcessingMode, ProcessingStatus, QualityLevel,
    ProcessingMetrics, ProcessingResult, ProcessingConfig
)
from .adapters import create_adapter, BaseAdapter
from ..utils.memory import MemoryMonitor


class TransformationType(Enum):
    """Types of data transformations."""
    COLUMN_RENAME = "column_rename"
    COLUMN_DROP = "column_drop"
    COLUMN_ADD = "column_add"
    DATA_TYPE_CONVERT = "data_type_convert"
    VALUE_MAP = "value_map"
    VALUE_REPLACE = "value_replace"
    FILTER_ROWS = "filter_rows"
    SORT = "sort"
    GROUP_AGGREGATE = "group_aggregate"
    JOIN = "join"
    PIVOT = "pivot"
    MELT = "melt"
    CUSTOM_FUNCTION = "custom_function"
    TELECOM_NORMALIZE = "telecom_normalize"
    GEOGRAPHIC_TRANSFORM = "geographic_transform"
    TIME_SERIES_RESAMPLE = "time_series_resample"


class AggregationFunction(Enum):
    """Aggregation functions for group operations."""
    SUM = "sum"
    MEAN = "mean"
    MEDIAN = "median"
    MIN = "min"
    MAX = "max"
    COUNT = "count"
    STD = "std"
    VAR = "var"
    FIRST = "first"
    LAST = "last"
    NUNIQUE = "nunique"


@dataclass
class TransformationRule:
    """Configuration for a single transformation operation."""
    name: str
    transformation_type: TransformationType
    parameters: Dict[str, Any] = field(default_factory=dict)
    condition: Optional[str] = None  # SQL-like condition for conditional transforms
    enabled: bool = True
    priority: int = 0  # Lower numbers execute first
    description: Optional[str] = None


@dataclass
class TransformationPipeline:
    """Configuration for a complete transformation pipeline."""
    name: str
    rules: List[TransformationRule] = field(default_factory=list)
    description: Optional[str] = None
    version: str = "1.0"
    created_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TransformationResult:
    """Result of a transformation operation."""
    rule_name: str
    transformation_type: TransformationType
    status: ProcessingStatus
    records_affected: int = 0
    processing_time: float = 0.0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class DataTransformer:
    """Advanced data transformation system with pipeline support.
    
    Features:
    - Configurable transformation pipelines
    - Telecommunications-specific transformations
    - Memory-optimized processing
    - Conditional transformations
    - Rollback capabilities
    - Performance monitoring
    - Async processing support
    """
    
    # Telecommunications-specific transformation presets
    TELECOM_PRESETS = {
        'cdr_standardization': [
            TransformationRule(
                name="normalize_msisdn",
                transformation_type=TransformationType.TELECOM_NORMALIZE,
                parameters={'field_type': 'msisdn', 'format': 'e164'}
            ),
            TransformationRule(
                name="convert_duration",
                transformation_type=TransformationType.DATA_TYPE_CONVERT,
                parameters={'columns': ['duration'], 'target_type': 'int32'}
            ),
            TransformationRule(
                name="standardize_timestamps",
                transformation_type=TransformationType.DATA_TYPE_CONVERT,
                parameters={'columns': ['start_time', 'end_time'], 'target_type': 'datetime64[ns]'}
            )
        ],
        'kpi_aggregation': [
            TransformationRule(
                name="hourly_aggregation",
                transformation_type=TransformationType.TIME_SERIES_RESAMPLE,
                parameters={'freq': '1H', 'agg_func': 'mean'}
            ),
            TransformationRule(
                name="add_quality_indicators",
                transformation_type=TransformationType.COLUMN_ADD,
                parameters={
                    'columns': {
                        'quality_score': 'lambda x: (x["rsrp"] + x["rsrq"]) / 2',
                        'coverage_level': 'lambda x: "good" if x["rsrp"] > -100 else "poor"'
                    }
                }
            )
        ],
        'geographic_enhancement': [
            TransformationRule(
                name="add_geographic_features",
                transformation_type=TransformationType.GEOGRAPHIC_TRANSFORM,
                parameters={
                    'operations': ['distance_to_center', 'grid_assignment', 'density_calculation']
                }
            )
        ]
    }
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        """Initialize data transformer.
        
        Args:
            config: Processing configuration
        """
        self.config = config or ProcessingConfig()
        self.logger = logging.getLogger(__name__)
        self.memory_monitor = MemoryMonitor()
        
        # Processing state
        self._current_adapter: Optional[BaseAdapter] = None
        self._processing_metrics = ProcessingMetrics()
        self._transformation_history: List[TransformationResult] = []
        self._rollback_data: Optional[Any] = None
    
    async def transform(
        self,
        data: Union[pd.DataFrame, Any],
        pipeline: Union[TransformationPipeline, List[TransformationRule], str],
        engine: Optional[ProcessingEngine] = None,
        mode: ProcessingMode = ProcessingMode.SYNC,
        enable_rollback: bool = False
    ) -> ProcessingResult:
        """Apply transformation pipeline to data.
        
        Args:
            data: Data to transform
            pipeline: Transformation pipeline, rules list, or preset name
            engine: Processing engine to use
            mode: Processing mode
            enable_rollback: Whether to enable rollback capability
            
        Returns:
            Processing result with transformed data
        """
        start_time = time.time()
        
        try:
            # Create adapter
            adapter = create_adapter(engine=engine, config=self.config)
            self._current_adapter = adapter
            
            # Convert to pandas for transformation
            if not isinstance(data, pd.DataFrame):
                if hasattr(data, 'to_pandas'):
                    data = data.to_pandas()
                else:
                    data = pd.DataFrame(data)
            
            # Store original data for rollback if enabled
            if enable_rollback:
                self._rollback_data = data.copy()
            
            # Get transformation rules
            if isinstance(pipeline, str):
                rules = self._get_preset_rules(pipeline)
            elif isinstance(pipeline, TransformationPipeline):
                rules = pipeline.rules
            else:
                rules = pipeline
            
            # Sort rules by priority
            rules = sorted([rule for rule in rules if rule.enabled], key=lambda x: x.priority)
            
            # Apply transformations
            transformed_data = data.copy()
            transformation_results = []
            
            for rule in rules:
                self.logger.debug(f"Applying transformation: {rule.name}")
                
                try:
                    result = await self._apply_transformation_rule(
                        transformed_data, rule, mode
                    )
                    
                    if result.status == ProcessingStatus.COMPLETED:
                        transformed_data = result.metadata.get('transformed_data', transformed_data)
                    
                    transformation_results.append(result)
                    
                except Exception as e:
                    error_result = TransformationResult(
                        rule_name=rule.name,
                        transformation_type=rule.transformation_type,
                        status=ProcessingStatus.FAILED,
                        error_message=str(e)
                    )
                    transformation_results.append(error_result)
                    
                    self.logger.error(f"Transformation {rule.name} failed: {e}")
                    
                    # Stop on error unless configured otherwise
                    if not self.config.continue_on_error:
                        break
            
            # Convert back to original format if needed
            if hasattr(adapter, 'from_pandas') and not isinstance(data, pd.DataFrame):
                result_data = adapter.from_pandas(transformed_data)
            else:
                result_data = transformed_data
            
            # Calculate metrics
            total_records_affected = sum(r.records_affected for r in transformation_results)
            successful_transformations = sum(1 for r in transformation_results if r.status == ProcessingStatus.COMPLETED)
            
            # Create result
            result = ProcessingResult(
                status=ProcessingStatus.COMPLETED,
                records_processed=len(data),
                processing_time=time.time() - start_time,
                data=result_data,
                metadata={
                    'transformation_results': transformation_results,
                    'total_transformations': len(rules),
                    'successful_transformations': successful_transformations,
                    'records_affected': total_records_affected,
                    'original_shape': data.shape,
                    'final_shape': transformed_data.shape
                },
                metrics=self._processing_metrics
            )
            
            # Store transformation history
            self._transformation_history.extend(transformation_results)
            
            self.logger.info(
                f"Transformation completed: {successful_transformations}/{len(rules)} rules applied, "
                f"{total_records_affected:,} records affected"
            )
            
            return result
            
        except Exception as e:
            result = ProcessingResult(
                status=ProcessingStatus.FAILED,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
            self.logger.error(f"Data transformation failed: {e}")
            return result
    
    async def create_pipeline(
        self,
        name: str,
        rules: List[TransformationRule],
        description: Optional[str] = None
    ) -> TransformationPipeline:
        """Create a new transformation pipeline.
        
        Args:
            name: Pipeline name
            rules: List of transformation rules
            description: Pipeline description
            
        Returns:
            Created transformation pipeline
        """
        pipeline = TransformationPipeline(
            name=name,
            rules=rules,
            description=description
        )
        
        self.logger.info(f"Created transformation pipeline: {name} with {len(rules)} rules")
        return pipeline
    
    async def validate_pipeline(
        self,
        pipeline: TransformationPipeline,
        sample_data: Optional[pd.DataFrame] = None
    ) -> Dict[str, Any]:
        """Validate transformation pipeline.
        
        Args:
            pipeline: Pipeline to validate
            sample_data: Sample data for validation
            
        Returns:
            Validation result
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'rule_validations': []
        }
        
        # Validate each rule
        for rule in pipeline.rules:
            rule_validation = await self._validate_transformation_rule(rule, sample_data)
            validation_result['rule_validations'].append(rule_validation)
            
            if not rule_validation['is_valid']:
                validation_result['is_valid'] = False
                validation_result['errors'].extend(rule_validation['errors'])
            
            validation_result['warnings'].extend(rule_validation['warnings'])
        
        # Check for rule conflicts
        conflicts = await self._check_rule_conflicts(pipeline.rules)
        if conflicts:
            validation_result['warnings'].extend(conflicts)
        
        return validation_result
    
    async def rollback(self) -> Optional[pd.DataFrame]:
        """Rollback to original data before transformations.
        
        Returns:
            Original data if rollback is available
        """
        if self._rollback_data is not None:
            self.logger.info("Rolling back to original data")
            return self._rollback_data.copy()
        else:
            self.logger.warning("No rollback data available")
            return None
    
    def get_transformation_history(self) -> List[TransformationResult]:
        """Get transformation history.
        
        Returns:
            List of transformation results
        """
        return self._transformation_history.copy()
    
    def get_available_presets(self) -> List[str]:
        """Get list of available transformation presets.
        
        Returns:
            List of preset names
        """
        return list(self.TELECOM_PRESETS.keys())
    
    # Transformation rule application methods
    async def _apply_transformation_rule(
        self,
        data: pd.DataFrame,
        rule: TransformationRule,
        mode: ProcessingMode
    ) -> TransformationResult:
        """Apply a single transformation rule."""
        start_time = time.time()
        
        try:
            # Check condition if specified
            if rule.condition and not await self._evaluate_condition(data, rule.condition):
                return TransformationResult(
                    rule_name=rule.name,
                    transformation_type=rule.transformation_type,
                    status=ProcessingStatus.SKIPPED,
                    processing_time=time.time() - start_time,
                    metadata={'reason': 'Condition not met'}
                )
            
            # Apply transformation based on type
            if rule.transformation_type == TransformationType.COLUMN_RENAME:
                result_data, records_affected = await self._apply_column_rename(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.COLUMN_DROP:
                result_data, records_affected = await self._apply_column_drop(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.COLUMN_ADD:
                result_data, records_affected = await self._apply_column_add(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.DATA_TYPE_CONVERT:
                result_data, records_affected = await self._apply_data_type_convert(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.VALUE_MAP:
                result_data, records_affected = await self._apply_value_map(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.VALUE_REPLACE:
                result_data, records_affected = await self._apply_value_replace(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.FILTER_ROWS:
                result_data, records_affected = await self._apply_filter_rows(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.SORT:
                result_data, records_affected = await self._apply_sort(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.GROUP_AGGREGATE:
                result_data, records_affected = await self._apply_group_aggregate(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.TELECOM_NORMALIZE:
                result_data, records_affected = await self._apply_telecom_normalize(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.GEOGRAPHIC_TRANSFORM:
                result_data, records_affected = await self._apply_geographic_transform(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.TIME_SERIES_RESAMPLE:
                result_data, records_affected = await self._apply_time_series_resample(data, rule.parameters)
            
            elif rule.transformation_type == TransformationType.CUSTOM_FUNCTION:
                result_data, records_affected = await self._apply_custom_function(data, rule.parameters)
            
            else:
                raise ValueError(f"Unsupported transformation type: {rule.transformation_type}")
            
            return TransformationResult(
                rule_name=rule.name,
                transformation_type=rule.transformation_type,
                status=ProcessingStatus.COMPLETED,
                records_affected=records_affected,
                processing_time=time.time() - start_time,
                metadata={'transformed_data': result_data}
            )
            
        except Exception as e:
            return TransformationResult(
                rule_name=rule.name,
                transformation_type=rule.transformation_type,
                status=ProcessingStatus.FAILED,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    # Individual transformation implementations
    async def _apply_column_rename(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply column rename transformation."""
        column_mapping = params.get('mapping', {})
        
        if not column_mapping:
            return data, 0
        
        # Filter mapping to only include existing columns
        existing_mapping = {old: new for old, new in column_mapping.items() if old in data.columns}
        
        if existing_mapping:
            result_data = data.rename(columns=existing_mapping)
            return result_data, len(existing_mapping)
        
        return data, 0
    
    async def _apply_column_drop(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply column drop transformation."""
        columns_to_drop = params.get('columns', [])
        
        if not columns_to_drop:
            return data, 0
        
        # Filter to only include existing columns
        existing_columns = [col for col in columns_to_drop if col in data.columns]
        
        if existing_columns:
            result_data = data.drop(columns=existing_columns)
            return result_data, len(existing_columns)
        
        return data, 0
    
    async def _apply_column_add(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply column add transformation."""
        columns_to_add = params.get('columns', {})
        
        if not columns_to_add:
            return data, 0
        
        result_data = data.copy()
        added_count = 0
        
        for col_name, col_definition in columns_to_add.items():
            try:
                if isinstance(col_definition, str):
                    # Evaluate expression
                    if col_definition.startswith('lambda'):
                        # Lambda function
                        func = eval(col_definition)
                        result_data[col_name] = result_data.apply(func, axis=1)
                    else:
                        # Simple value or expression
                        result_data[col_name] = eval(col_definition, {'data': result_data, 'np': np, 'pd': pd})
                else:
                    # Direct value
                    result_data[col_name] = col_definition
                
                added_count += 1
                
            except Exception as e:
                self.logger.warning(f"Failed to add column {col_name}: {e}")
        
        return result_data, added_count
    
    async def _apply_data_type_convert(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply data type conversion transformation."""
        columns = params.get('columns', [])
        target_type = params.get('target_type', 'str')
        errors = params.get('errors', 'coerce')
        
        if not columns:
            return data, 0
        
        result_data = data.copy()
        converted_count = 0
        
        for col in columns:
            if col in result_data.columns:
                try:
                    if target_type == 'datetime64[ns]':
                        result_data[col] = pd.to_datetime(result_data[col], errors=errors)
                    elif target_type in ['int32', 'int64', 'float32', 'float64']:
                        result_data[col] = pd.to_numeric(result_data[col], errors=errors).astype(target_type)
                    else:
                        result_data[col] = result_data[col].astype(target_type)
                    
                    converted_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"Failed to convert column {col} to {target_type}: {e}")
        
        return result_data, converted_count
    
    async def _apply_value_map(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply value mapping transformation."""
        column = params.get('column')
        mapping = params.get('mapping', {})
        
        if not column or not mapping or column not in data.columns:
            return data, 0
        
        result_data = data.copy()
        original_values = result_data[column].copy()
        
        result_data[column] = result_data[column].map(mapping).fillna(result_data[column])
        
        # Count changed values
        changed_count = (original_values != result_data[column]).sum()
        
        return result_data, changed_count
    
    async def _apply_value_replace(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply value replacement transformation."""
        column = params.get('column')
        old_value = params.get('old_value')
        new_value = params.get('new_value')
        regex = params.get('regex', False)
        
        if not column or column not in data.columns:
            return data, 0
        
        result_data = data.copy()
        original_values = result_data[column].copy()
        
        if regex:
            result_data[column] = result_data[column].astype(str).str.replace(old_value, str(new_value), regex=True)
        else:
            result_data[column] = result_data[column].replace(old_value, new_value)
        
        # Count changed values
        changed_count = (original_values != result_data[column]).sum()
        
        return result_data, changed_count
    
    async def _apply_filter_rows(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply row filtering transformation."""
        condition = params.get('condition')
        
        if not condition:
            return data, 0
        
        try:
            # Evaluate condition
            mask = eval(condition, {'data': data, 'np': np, 'pd': pd})
            result_data = data[mask]
            
            removed_count = len(data) - len(result_data)
            return result_data, removed_count
            
        except Exception as e:
            self.logger.warning(f"Failed to apply filter condition: {e}")
            return data, 0
    
    async def _apply_sort(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply sorting transformation."""
        columns = params.get('columns', [])
        ascending = params.get('ascending', True)
        
        if not columns:
            return data, 0
        
        # Filter to existing columns
        existing_columns = [col for col in columns if col in data.columns]
        
        if existing_columns:
            result_data = data.sort_values(by=existing_columns, ascending=ascending)
            return result_data, len(data)  # All rows affected by sorting
        
        return data, 0
    
    async def _apply_group_aggregate(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply group aggregation transformation."""
        group_by = params.get('group_by', [])
        agg_functions = params.get('agg_functions', {})
        
        if not group_by or not agg_functions:
            return data, 0
        
        try:
            # Filter to existing columns
            existing_group_by = [col for col in group_by if col in data.columns]
            existing_agg_functions = {col: func for col, func in agg_functions.items() if col in data.columns}
            
            if existing_group_by and existing_agg_functions:
                result_data = data.groupby(existing_group_by).agg(existing_agg_functions).reset_index()
                return result_data, len(data)  # All original rows were aggregated
            
        except Exception as e:
            self.logger.warning(f"Failed to apply group aggregation: {e}")
        
        return data, 0
    
    async def _apply_telecom_normalize(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply telecommunications-specific normalization."""
        field_type = params.get('field_type')
        format_type = params.get('format', 'standard')
        
        if not field_type:
            return data, 0
        
        result_data = data.copy()
        total_affected = 0
        
        # Find columns that match the field type
        matching_columns = []
        for col in data.columns:
            col_lower = col.lower() if isinstance(col, str) else str(col).lower()
            if field_type.lower() in col_lower:
                matching_columns.append(col)
        
        for col in matching_columns:
            try:
                if field_type.lower() == 'msisdn':
                    # Normalize MSISDN to E.164 format
                    original_values = result_data[col].copy()
                    
                    # Remove non-numeric characters except +
                    result_data[col] = result_data[col].astype(str).str.replace(r'[^+\d]', '', regex=True)
                    
                    # Add + prefix if missing
                    mask = ~result_data[col].str.startswith('+')
                    result_data.loc[mask, col] = '+' + result_data.loc[mask, col]
                    
                    # Count changes
                    changed_count = (original_values != result_data[col]).sum()
                    total_affected += changed_count
                
                elif field_type.lower() == 'imsi':
                    # Normalize IMSI (15 digits)
                    original_values = result_data[col].copy()
                    
                    # Keep only digits and pad/truncate to 15 digits
                    result_data[col] = result_data[col].astype(str).str.replace(r'\D', '', regex=True)
                    result_data[col] = result_data[col].str.zfill(15).str[:15]
                    
                    changed_count = (original_values != result_data[col]).sum()
                    total_affected += changed_count
                
                elif field_type.lower() == 'cell_id':
                    # Normalize Cell ID to integer
                    original_values = result_data[col].copy()
                    
                    result_data[col] = pd.to_numeric(result_data[col], errors='coerce').astype('Int64')
                    
                    changed_count = (original_values != result_data[col]).sum()
                    total_affected += changed_count
                
            except Exception as e:
                self.logger.warning(f"Failed to normalize {field_type} in column {col}: {e}")
        
        return result_data, total_affected
    
    async def _apply_geographic_transform(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply geographic transformations."""
        operations = params.get('operations', [])
        
        if not operations:
            return data, 0
        
        result_data = data.copy()
        total_affected = 0
        
        # Find latitude and longitude columns
        lat_col = None
        lon_col = None
        
        for col in data.columns:
            col_lower = col.lower() if isinstance(col, str) else str(col).lower()
            if 'lat' in col_lower and not lat_col:
                lat_col = col
            elif 'lon' in col_lower and not lon_col:
                lon_col = col
        
        if not (lat_col and lon_col):
            self.logger.warning("Geographic transformation requires latitude and longitude columns")
            return data, 0
        
        try:
            for operation in operations:
                if operation == 'distance_to_center':
                    # Calculate distance to geographic center
                    center_lat = params.get('center_lat', result_data[lat_col].mean())
                    center_lon = params.get('center_lon', result_data[lon_col].mean())
                    
                    # Haversine distance calculation (simplified)
                    lat_diff = np.radians(result_data[lat_col] - center_lat)
                    lon_diff = np.radians(result_data[lon_col] - center_lon)
                    
                    a = (np.sin(lat_diff/2)**2 + 
                         np.cos(np.radians(center_lat)) * np.cos(np.radians(result_data[lat_col])) * 
                         np.sin(lon_diff/2)**2)
                    
                    distance = 2 * 6371 * np.arcsin(np.sqrt(a))  # Earth radius = 6371 km
                    result_data['distance_to_center_km'] = distance
                    total_affected += len(result_data)
                
                elif operation == 'grid_assignment':
                    # Assign to geographic grid
                    grid_size = params.get('grid_size', 0.01)  # ~1km grid
                    
                    result_data['grid_lat'] = (result_data[lat_col] / grid_size).astype(int) * grid_size
                    result_data['grid_lon'] = (result_data[lon_col] / grid_size).astype(int) * grid_size
                    result_data['grid_id'] = (result_data['grid_lat'].astype(str) + '_' + 
                                            result_data['grid_lon'].astype(str))
                    total_affected += len(result_data)
                
                elif operation == 'density_calculation':
                    # Calculate point density in surrounding area
                    radius_km = params.get('radius_km', 1.0)
                    
                    # Simplified density calculation
                    # In a real implementation, you'd use spatial indexing
                    densities = []
                    for idx, row in result_data.iterrows():
                        lat_min, lat_max = row[lat_col] - radius_km/111, row[lat_col] + radius_km/111
                        lon_min, lon_max = row[lon_col] - radius_km/111, row[lon_col] + radius_km/111
                        
                        nearby_count = len(result_data[
                            (result_data[lat_col] >= lat_min) & (result_data[lat_col] <= lat_max) &
                            (result_data[lon_col] >= lon_min) & (result_data[lon_col] <= lon_max)
                        ])
                        densities.append(nearby_count)
                    
                    result_data['point_density'] = densities
                    total_affected += len(result_data)
            
        except Exception as e:
            self.logger.warning(f"Failed to apply geographic transformation: {e}")
        
        return result_data, total_affected
    
    async def _apply_time_series_resample(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply time series resampling."""
        freq = params.get('freq', '1H')
        agg_func = params.get('agg_func', 'mean')
        time_column = params.get('time_column')
        
        # Find time column if not specified
        if not time_column:
            for col in data.columns:
                if any(keyword in col.lower() for keyword in ['time', 'date', 'timestamp']):
                    time_column = col
                    break
        
        if not time_column or time_column not in data.columns:
            self.logger.warning("Time series resampling requires a time column")
            return data, 0
        
        try:
            result_data = data.copy()
            
            # Convert time column to datetime
            result_data[time_column] = pd.to_datetime(result_data[time_column])
            
            # Set time column as index
            result_data = result_data.set_index(time_column)
            
            # Resample
            if agg_func == 'mean':
                result_data = result_data.resample(freq).mean()
            elif agg_func == 'sum':
                result_data = result_data.resample(freq).sum()
            elif agg_func == 'count':
                result_data = result_data.resample(freq).count()
            elif agg_func == 'min':
                result_data = result_data.resample(freq).min()
            elif agg_func == 'max':
                result_data = result_data.resample(freq).max()
            else:
                result_data = result_data.resample(freq).agg(agg_func)
            
            # Reset index
            result_data = result_data.reset_index()
            
            return result_data, len(data)  # All original rows were resampled
            
        except Exception as e:
            self.logger.warning(f"Failed to apply time series resampling: {e}")
            return data, 0
    
    async def _apply_custom_function(self, data: pd.DataFrame, params: Dict[str, Any]) -> Tuple[pd.DataFrame, int]:
        """Apply custom transformation function."""
        function = params.get('function')
        function_args = params.get('args', {})
        
        if not function:
            return data, 0
        
        try:
            if isinstance(function, str):
                # Evaluate function string
                func = eval(function)
            else:
                func = function
            
            result_data = func(data, **function_args)
            
            if isinstance(result_data, pd.DataFrame):
                return result_data, len(data)
            else:
                self.logger.warning("Custom function did not return a DataFrame")
                return data, 0
            
        except Exception as e:
            self.logger.warning(f"Failed to apply custom function: {e}")
            return data, 0
    
    # Helper methods
    async def _evaluate_condition(self, data: pd.DataFrame, condition: str) -> bool:
        """Evaluate transformation condition."""
        try:
            result = eval(condition, {'data': data, 'len': len, 'np': np, 'pd': pd})
            return bool(result)
        except Exception as e:
            self.logger.warning(f"Failed to evaluate condition '{condition}': {e}")
            return False
    
    def _get_preset_rules(self, preset_name: str) -> List[TransformationRule]:
        """Get transformation rules for a preset."""
        if preset_name in self.TELECOM_PRESETS:
            return self.TELECOM_PRESETS[preset_name].copy()
        else:
            raise ValueError(f"Unknown preset: {preset_name}")
    
    async def _validate_transformation_rule(self, rule: TransformationRule, sample_data: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """Validate a single transformation rule."""
        validation = {
            'rule_name': rule.name,
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # Check required parameters based on transformation type
        required_params = self._get_required_parameters(rule.transformation_type)
        
        for param in required_params:
            if param not in rule.parameters:
                validation['is_valid'] = False
                validation['errors'].append(f"Missing required parameter: {param}")
        
        # Validate against sample data if provided
        if sample_data is not None:
            if rule.transformation_type in [TransformationType.COLUMN_RENAME, TransformationType.COLUMN_DROP]:
                columns = rule.parameters.get('columns', [])
                if isinstance(rule.parameters.get('mapping'), dict):
                    columns = list(rule.parameters['mapping'].keys())
                
                missing_columns = [col for col in columns if col not in sample_data.columns]
                if missing_columns:
                    validation['warnings'].append(f"Columns not found in sample data: {missing_columns}")
        
        return validation
    
    def _get_required_parameters(self, transformation_type: TransformationType) -> List[str]:
        """Get required parameters for transformation type."""
        param_map = {
            TransformationType.COLUMN_RENAME: ['mapping'],
            TransformationType.COLUMN_DROP: ['columns'],
            TransformationType.COLUMN_ADD: ['columns'],
            TransformationType.DATA_TYPE_CONVERT: ['columns', 'target_type'],
            TransformationType.VALUE_MAP: ['column', 'mapping'],
            TransformationType.VALUE_REPLACE: ['column', 'old_value', 'new_value'],
            TransformationType.FILTER_ROWS: ['condition'],
            TransformationType.SORT: ['columns'],
            TransformationType.GROUP_AGGREGATE: ['group_by', 'agg_functions'],
            TransformationType.TELECOM_NORMALIZE: ['field_type'],
            TransformationType.GEOGRAPHIC_TRANSFORM: ['operations'],
            TransformationType.TIME_SERIES_RESAMPLE: ['freq'],
            TransformationType.CUSTOM_FUNCTION: ['function']
        }
        
        return param_map.get(transformation_type, [])
    
    async def _check_rule_conflicts(self, rules: List[TransformationRule]) -> List[str]:
        """Check for conflicts between transformation rules."""
        conflicts = []
        
        # Check for column operations conflicts
        dropped_columns = set()
        renamed_columns = {}
        
        for rule in sorted(rules, key=lambda x: x.priority):
            if rule.transformation_type == TransformationType.COLUMN_DROP:
                columns = rule.parameters.get('columns', [])
                dropped_columns.update(columns)
            
            elif rule.transformation_type == TransformationType.COLUMN_RENAME:
                mapping = rule.parameters.get('mapping', {})
                for old_col, new_col in mapping.items():
                    if old_col in dropped_columns:
                        conflicts.append(f"Rule '{rule.name}' tries to rename dropped column '{old_col}'")
                    renamed_columns[old_col] = new_col
            
            elif rule.transformation_type in [TransformationType.DATA_TYPE_CONVERT, TransformationType.VALUE_MAP]:
                columns = rule.parameters.get('columns', [rule.parameters.get('column')])
                for col in columns:
                    if col and col in dropped_columns:
                        conflicts.append(f"Rule '{rule.name}' tries to operate on dropped column '{col}'")
        
        return conflicts