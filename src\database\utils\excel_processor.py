"""
Enhanced Excel processor for telecommunications data with operator sheet detection.

This module provides functionality for processing Excel files with multiple
operator sheets and routing data to appropriate schemas.
"""

import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import pandas as pd

from .table_naming import OperatorDetector, TableNamingManager

logger = logging.getLogger(__name__)


class MultiOperatorExcelProcessor:
    """Processes Excel files with multiple operator sheets."""
    
    def __init__(self, config: Dict):
        """Initialize with database configuration.
        
        Args:
            config: Database configuration
        """
        self.config = config
        self.operator_detector = OperatorDetector(config)
        self.table_naming_manager = TableNamingManager(config)
        
    def process_excel_file(self, file_path: Path, data_type: str = 'cdr') -> List[Dict[str, Any]]:
        """Process Excel file and extract data from operator sheets.
        
        Args:
            file_path: Path to Excel file
            data_type: Type of data (cdr, ep, nlg, etc.)
            
        Returns:
            List of processing results for each operator sheet
        """
        results = []
        
        try:
            # Detect operators in the Excel file
            detected_operators = self.operator_detector.detect_operators_in_excel(file_path)
            
            if not detected_operators:
                logger.warning(f"No operators detected in {file_path}, processing as single sheet")
                # Fallback to single sheet processing
                return self._process_single_sheet(file_path, data_type)
            
            # Process each operator sheet
            for operator, schema in detected_operators:
                try:
                    result = self._process_operator_sheet(
                        file_path, operator, schema, data_type
                    )
                    if result:
                        results.append(result)
                        
                except Exception as e:
                    logger.error(f"Failed to process operator '{operator}' in {file_path}: {e}")
                    results.append({
                        'success': False,
                        'operator': operator,
                        'schema': schema,
                        'file_path': str(file_path),
                        'error': str(e)
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to process Excel file {file_path}: {e}")
            return [{
                'success': False,
                'file_path': str(file_path),
                'error': str(e)
            }]
    
    def _process_operator_sheet(self, file_path: Path, operator: str, 
                              schema: str, data_type: str) -> Optional[Dict[str, Any]]:
        """Process a specific operator sheet.
        
        Args:
            file_path: Path to Excel file
            operator: Operator name
            schema: Target schema name
            data_type: Type of data
            
        Returns:
            Processing result dictionary
        """
        try:
            # Find the sheet for this operator
            sheet_name = self._find_operator_sheet(file_path, operator)
            if not sheet_name:
                logger.warning(f"No sheet found for operator '{operator}' in {file_path}")
                return None
            
            # Generate table name
            table_name = self.table_naming_manager.generate_table_name(
                data_type, file_path, operator, sheet_name
            )
            
            # Read the sheet data
            data_config = self.config.get('telecom_data_sources', {}).get(data_type, {})
            skip_rows = data_config.get('skip_rows', 0)
            header_row = data_config.get('header_row', 0)
            
            # Adjust header_row for skip_rows
            actual_header = header_row - skip_rows if skip_rows > 0 else header_row
            
            df = pd.read_excel(
                file_path,
                sheet_name=sheet_name,
                skiprows=skip_rows,
                header=actual_header
            )
            
            if df.empty:
                logger.warning(f"Empty data in sheet '{sheet_name}' for operator '{operator}'")
                return {
                    'success': True,
                    'operator': operator,
                    'schema': schema,
                    'table_name': table_name,
                    'sheet_name': sheet_name,
                    'file_path': str(file_path),
                    'records_count': 0,
                    'dataframe': df
                }
            
            logger.info(f"Processed sheet '{sheet_name}' for operator '{operator}': {len(df)} rows")
            
            return {
                'success': True,
                'operator': operator,
                'schema': schema,
                'table_name': table_name,
                'sheet_name': sheet_name,
                'file_path': str(file_path),
                'records_count': len(df),
                'dataframe': df
            }
            
        except Exception as e:
            logger.error(f"Failed to process operator sheet for '{operator}': {e}")
            return {
                'success': False,
                'operator': operator,
                'schema': schema,
                'file_path': str(file_path),
                'error': str(e)
            }
    
    def _find_operator_sheet(self, file_path: Path, operator: str) -> Optional[str]:
        """Find the sheet name for a specific operator.
        
        Args:
            file_path: Path to Excel file
            operator: Operator name to find
            
        Returns:
            Sheet name or None if not found
        """
        try:
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            # Define operator patterns for fuzzy matching
            operator_patterns = {
                'telefonica': ['telefonica', 'tef', 'movistar', 'o2'],
                'vodafone': ['vodafone', 'vdf', 'voda'],
                'telekom': ['telekom', 'dtag', 'deutsche telekom', 'dt', 'magenta']
            }
            
            patterns = operator_patterns.get(operator, [operator])
            
            for sheet_name in sheet_names:
                sheet_name_lower = sheet_name.lower()
                for pattern in patterns:
                    if pattern in sheet_name_lower:
                        return sheet_name
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to find operator sheet for '{operator}': {e}")
            return None
    
    def _process_single_sheet(self, file_path: Path, data_type: str) -> List[Dict[str, Any]]:
        """Process Excel file as single sheet (fallback).
        
        Args:
            file_path: Path to Excel file
            data_type: Type of data
            
        Returns:
            List with single processing result
        """
        try:
            # Generate table name without operator
            table_name = self.table_naming_manager.generate_table_name(
                data_type, file_path
            )
            
            # Get default schema for data type
            data_config = self.config.get('telecom_data_sources', {}).get(data_type, {})
            schema = data_config.get('schema_name', 'public')
            skip_rows = data_config.get('skip_rows', 0)
            header_row = data_config.get('header_row', 0)
            
            # Adjust header_row for skip_rows
            actual_header = header_row - skip_rows if skip_rows > 0 else header_row
            
            # Read the first sheet
            df = pd.read_excel(
                file_path,
                skiprows=skip_rows,
                header=actual_header
            )
            
            return [{
                'success': True,
                'operator': None,
                'schema': schema,
                'table_name': table_name,
                'sheet_name': None,
                'file_path': str(file_path),
                'records_count': len(df),
                'dataframe': df
            }]
            
        except Exception as e:
            logger.error(f"Failed to process single sheet Excel file {file_path}: {e}")
            return [{
                'success': False,
                'file_path': str(file_path),
                'error': str(e)
            }]
    
    def get_sheet_info(self, file_path: Path) -> Dict[str, Any]:
        """Get information about sheets in Excel file.
        
        Args:
            file_path: Path to Excel file
            
        Returns:
            Dictionary with sheet information
        """
        try:
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            # Detect operators
            detected_operators = self.operator_detector.detect_operators_in_excel(file_path)
            
            return {
                'file_path': str(file_path),
                'sheet_count': len(sheet_names),
                'sheet_names': sheet_names,
                'detected_operators': detected_operators,
                'has_multiple_operators': len(detected_operators) > 1
            }
            
        except Exception as e:
            logger.error(f"Failed to get sheet info for {file_path}: {e}")
            return {
                'file_path': str(file_path),
                'error': str(e)
            }
