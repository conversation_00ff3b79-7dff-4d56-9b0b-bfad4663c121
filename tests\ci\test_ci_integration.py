#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - CI/CD测试集成

本模块提供CI/CD流水线的测试集成功能，包括：
- 测试结果收集和分析
- 质量门禁检查
- 监控系统集成
- 通知和报告
- 部署决策支持

作者: Connect质量工程团队
创建时间: 2024-01-20
"""

import os
import sys
import json
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.config.test_config import config
from tests.test_suite_manager import _TestSuiteManager, TestExecutionResult


class QualityGateStatus(Enum):
    """质量门禁状态"""
    PASSED = "passed"
    FAILED = "failed"
    WARNING = "warning"
    BLOCKED = "blocked"


class DeploymentDecision(Enum):
    """部署决策"""
    APPROVE = "approve"
    REJECT = "reject"
    MANUAL_REVIEW = "manual_review"
    CONDITIONAL = "conditional"


@dataclass
class QualityMetrics:
    """质量指标"""
    test_success_rate: float
    code_coverage: float
    performance_score: float
    security_score: float
    reliability_score: float
    maintainability_score: float
    
    @property
    def overall_score(self) -> float:
        """综合质量分数"""
        weights = {
            'test_success_rate': 0.25,
            'code_coverage': 0.15,
            'performance_score': 0.20,
            'security_score': 0.25,
            'reliability_score': 0.10,
            'maintainability_score': 0.05
        }
        
        return sum(
            getattr(self, metric) * weight 
            for metric, weight in weights.items()
        )


@dataclass
class QualityGateRule:
    """质量门禁规则"""
    name: str
    metric: str
    operator: str  # '>=', '>', '<=', '<', '==', '!='
    threshold: float
    severity: str  # 'critical', 'major', 'minor'
    description: str
    
    def evaluate(self, value: float) -> bool:
        """评估规则"""
        if self.operator == '>=':
            return value >= self.threshold
        elif self.operator == '>':
            return value > self.threshold
        elif self.operator == '<=':
            return value <= self.threshold
        elif self.operator == '<':
            return value < self.threshold
        elif self.operator == '==':
            return value == self.threshold
        elif self.operator == '!=':
            return value != self.threshold
        else:
            raise ValueError(f"不支持的操作符: {self.operator}")


@dataclass
class QualityGateResult:
    """质量门禁结果"""
    status: QualityGateStatus
    passed_rules: List[str]
    failed_rules: List[str]
    warning_rules: List[str]
    metrics: QualityMetrics
    deployment_decision: DeploymentDecision
    recommendations: List[str]
    timestamp: datetime
    
    @property
    def is_deployable(self) -> bool:
        """是否可部署"""
        return self.deployment_decision in [DeploymentDecision.APPROVE, DeploymentDecision.CONDITIONAL]


class CIIntegration:
    """CI/CD集成器"""
    
    def __init__(self):
        self.quality_gates = self._load_quality_gates()
        self.notification_channels = self._load_notification_channels()
        self.monitoring_endpoints = self._load_monitoring_endpoints()
    
    def _load_quality_gates(self) -> List[QualityGateRule]:
        """加载质量门禁规则"""
        return [
            # 测试成功率规则
            QualityGateRule(
                name="test_success_rate_critical",
                metric="test_success_rate",
                operator=">=",
                threshold=95.0,
                severity="critical",
                description="测试成功率必须达到95%以上"
            ),
            QualityGateRule(
                name="test_success_rate_warning",
                metric="test_success_rate",
                operator=">=",
                threshold=90.0,
                severity="major",
                description="测试成功率建议达到90%以上"
            ),
            
            # 代码覆盖率规则
            QualityGateRule(
                name="code_coverage_critical",
                metric="code_coverage",
                operator=">=",
                threshold=80.0,
                severity="critical",
                description="代码覆盖率必须达到80%以上"
            ),
            QualityGateRule(
                name="code_coverage_warning",
                metric="code_coverage",
                operator=">=",
                threshold=85.0,
                severity="minor",
                description="代码覆盖率建议达到85%以上"
            ),
            
            # 性能分数规则
            QualityGateRule(
                name="performance_score_critical",
                metric="performance_score",
                operator=">=",
                threshold=80.0,
                severity="critical",
                description="性能分数必须达到80分以上"
            ),
            
            # 安全分数规则
            QualityGateRule(
                name="security_score_critical",
                metric="security_score",
                operator=">=",
                threshold=90.0,
                severity="critical",
                description="安全分数必须达到90分以上"
            ),
            
            # 可靠性分数规则
            QualityGateRule(
                name="reliability_score_critical",
                metric="reliability_score",
                operator=">=",
                threshold=85.0,
                severity="critical",
                description="可靠性分数必须达到85分以上"
            ),
            
            # 可维护性分数规则
            QualityGateRule(
                name="maintainability_score_warning",
                metric="maintainability_score",
                operator=">=",
                threshold=70.0,
                severity="minor",
                description="可维护性分数建议达到70分以上"
            )
        ]
    
    def _load_notification_channels(self) -> Dict[str, Dict[str, Any]]:
        """加载通知渠道配置"""
        return {
            'slack': {
                'webhook_url': os.getenv('SLACK_WEBHOOK_URL'),
                'channel': '#connect-ci',
                'enabled': bool(os.getenv('SLACK_WEBHOOK_URL'))
            },
            'email': {
                'smtp_server': os.getenv('SMTP_SERVER', 'localhost'),
                'smtp_port': int(os.getenv('SMTP_PORT', '587')),
                'username': os.getenv('SMTP_USERNAME'),
                'password': os.getenv('SMTP_PASSWORD'),
                'recipients': os.getenv('EMAIL_RECIPIENTS', '').split(','),
                'enabled': bool(os.getenv('SMTP_USERNAME'))
            },
            'teams': {
                'webhook_url': os.getenv('TEAMS_WEBHOOK_URL'),
                'enabled': bool(os.getenv('TEAMS_WEBHOOK_URL'))
            }
        }
    
    def _load_monitoring_endpoints(self) -> Dict[str, str]:
        """加载监控系统端点"""
        return {
            'prometheus': os.getenv('PROMETHEUS_PUSHGATEWAY_URL', 'http://localhost:9091'),
            'grafana': os.getenv('GRAFANA_API_URL', 'http://localhost:3000/api'),
            'datadog': os.getenv('DATADOG_API_URL', 'https://api.datadoghq.com/api/v1'),
            'newrelic': os.getenv('NEWRELIC_API_URL', 'https://api.newrelic.com/v2')
        }
    
    def collect_test_results(self, results_dir: str = 'tests/reports') -> Dict[str, Any]:
        """收集测试结果"""
        results = {
            'test_results': [],
            'coverage_data': {},
            'performance_data': {},
            'security_data': {},
            'timestamp': datetime.now().isoformat()
        }
        
        results_path = Path(results_dir)
        
        # 收集JUnit XML结果
        for junit_file in results_path.glob('junit_*.xml'):
            try:
                import xml.etree.ElementTree as ET
                tree = ET.parse(junit_file)
                root = tree.getroot()
                
                suite_data = {
                    'suite_name': junit_file.stem.replace('junit_', ''),
                    'tests': int(root.get('tests', 0)),
                    'failures': int(root.get('failures', 0)),
                    'errors': int(root.get('errors', 0)),
                    'skipped': int(root.get('skipped', 0)),
                    'time': float(root.get('time', 0))
                }
                
                suite_data['passed'] = suite_data['tests'] - suite_data['failures'] - suite_data['errors'] - suite_data['skipped']
                suite_data['success_rate'] = (suite_data['passed'] / suite_data['tests'] * 100) if suite_data['tests'] > 0 else 0
                
                results['test_results'].append(suite_data)
                
            except Exception as e:
                print(f"解析JUnit文件失败 {junit_file}: {e}")
        
        # 收集覆盖率数据
        coverage_file = results_path / 'coverage' / 'coverage.xml'
        if coverage_file.exists():
            try:
                import xml.etree.ElementTree as ET
                tree = ET.parse(coverage_file)
                root = tree.getroot()
                
                coverage_elem = root.find('.//coverage')
                if coverage_elem is not None:
                    results['coverage_data'] = {
                        'line_rate': float(coverage_elem.get('line-rate', 0)) * 100,
                        'branch_rate': float(coverage_elem.get('branch-rate', 0)) * 100,
                        'lines_covered': int(coverage_elem.get('lines-covered', 0)),
                        'lines_valid': int(coverage_elem.get('lines-valid', 0))
                    }
                    
            except Exception as e:
                print(f"解析覆盖率文件失败: {e}")
        
        # 收集性能数据
        performance_files = list(results_path.glob('performance_*.json'))
        if performance_files:
            try:
                with open(performance_files[0], 'r', encoding='utf-8') as f:
                    results['performance_data'] = json.load(f)
            except Exception as e:
                print(f"解析性能数据失败: {e}")
        
        # 收集安全数据
        security_files = list(results_path.glob('security_*.json'))
        if security_files:
            try:
                with open(security_files[0], 'r', encoding='utf-8') as f:
                    results['security_data'] = json.load(f)
            except Exception as e:
                print(f"解析安全数据失败: {e}")
        
        return results
    
    def calculate_quality_metrics(self, test_results: Dict[str, Any]) -> QualityMetrics:
        """计算质量指标"""
        # 计算测试成功率
        total_tests = sum(suite['tests'] for suite in test_results['test_results'])
        total_passed = sum(suite['passed'] for suite in test_results['test_results'])
        test_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # 获取代码覆盖率
        code_coverage = test_results['coverage_data'].get('line_rate', 0)
        
        # 计算性能分数
        performance_data = test_results['performance_data']
        performance_score = self._calculate_performance_score(performance_data)
        
        # 计算安全分数
        security_data = test_results['security_data']
        security_score = self._calculate_security_score(security_data)
        
        # 计算可靠性分数
        reliability_score = self._calculate_reliability_score(test_results)
        
        # 计算可维护性分数
        maintainability_score = self._calculate_maintainability_score(test_results)
        
        return QualityMetrics(
            test_success_rate=test_success_rate,
            code_coverage=code_coverage,
            performance_score=performance_score,
            security_score=security_score,
            reliability_score=reliability_score,
            maintainability_score=maintainability_score
        )
    
    def _calculate_performance_score(self, performance_data: Dict[str, Any]) -> float:
        """计算性能分数"""
        if not performance_data:
            return 0.0
        
        # 基于响应时间、吞吐量、资源使用率等计算分数
        score = 100.0
        
        # 响应时间评分 (目标: <500ms)
        avg_response_time = performance_data.get('avg_response_time', 0)
        if avg_response_time > 500:
            score -= min(30, (avg_response_time - 500) / 100 * 10)
        
        # 吞吐量评分 (目标: >1000 req/s)
        throughput = performance_data.get('throughput', 0)
        if throughput < 1000:
            score -= min(20, (1000 - throughput) / 100 * 5)
        
        # CPU使用率评分 (目标: <80%)
        cpu_usage = performance_data.get('cpu_usage', 0)
        if cpu_usage > 80:
            score -= min(25, (cpu_usage - 80) / 10 * 5)
        
        # 内存使用率评分 (目标: <80%)
        memory_usage = performance_data.get('memory_usage', 0)
        if memory_usage > 80:
            score -= min(25, (memory_usage - 80) / 10 * 5)
        
        return max(0, score)
    
    def _calculate_security_score(self, security_data: Dict[str, Any]) -> float:
        """计算安全分数"""
        if not security_data:
            return 0.0
        
        score = 100.0
        
        # 漏洞扣分
        vulnerabilities = security_data.get('vulnerabilities', [])
        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'low')
            if severity == 'critical':
                score -= 30
            elif severity == 'high':
                score -= 20
            elif severity == 'medium':
                score -= 10
            elif severity == 'low':
                score -= 5
        
        # 安全配置检查
        security_checks = security_data.get('security_checks', {})
        failed_checks = sum(1 for check in security_checks.values() if not check)
        total_checks = len(security_checks)
        
        if total_checks > 0:
            check_score = (total_checks - failed_checks) / total_checks * 100
            score = min(score, check_score)
        
        return max(0, score)
    
    def _calculate_reliability_score(self, test_results: Dict[str, Any]) -> float:
        """计算可靠性分数"""
        score = 100.0
        
        # 基于测试稳定性
        test_suites = test_results['test_results']
        for suite in test_suites:
            if suite['errors'] > 0:
                score -= suite['errors'] * 5
            if suite['success_rate'] < 95:
                score -= (95 - suite['success_rate']) * 2
        
        # 基于性能稳定性
        performance_data = test_results['performance_data']
        if performance_data:
            response_time_std = performance_data.get('response_time_std', 0)
            if response_time_std > 100:  # 响应时间标准差过大
                score -= min(20, response_time_std / 50)
        
        return max(0, score)
    
    def _calculate_maintainability_score(self, test_results: Dict[str, Any]) -> float:
        """计算可维护性分数"""
        score = 100.0
        
        # 基于代码覆盖率
        coverage = test_results['coverage_data'].get('line_rate', 0)
        if coverage < 80:
            score -= (80 - coverage) * 2
        
        # 基于测试质量
        total_tests = sum(suite['tests'] for suite in test_results['test_results'])
        if total_tests < 100:  # 测试数量不足
            score -= (100 - total_tests) * 0.5
        
        return max(0, score)
    
    def evaluate_quality_gates(self, metrics: QualityMetrics) -> QualityGateResult:
        """评估质量门禁"""
        passed_rules = []
        failed_rules = []
        warning_rules = []
        recommendations = []
        
        critical_failures = 0
        major_failures = 0
        
        for rule in self.quality_gates:
            metric_value = getattr(metrics, rule.metric)
            
            if rule.evaluate(metric_value):
                passed_rules.append(rule.name)
            else:
                if rule.severity == 'critical':
                    failed_rules.append(rule.name)
                    critical_failures += 1
                    recommendations.append(f"关键问题: {rule.description}")
                elif rule.severity == 'major':
                    failed_rules.append(rule.name)
                    major_failures += 1
                    recommendations.append(f"重要问题: {rule.description}")
                else:
                    warning_rules.append(rule.name)
                    recommendations.append(f"建议改进: {rule.description}")
        
        # 确定状态和部署决策
        if critical_failures > 0:
            status = QualityGateStatus.FAILED
            deployment_decision = DeploymentDecision.REJECT
        elif major_failures > 0:
            status = QualityGateStatus.WARNING
            deployment_decision = DeploymentDecision.MANUAL_REVIEW
        elif warning_rules:
            status = QualityGateStatus.WARNING
            deployment_decision = DeploymentDecision.CONDITIONAL
        else:
            status = QualityGateStatus.PASSED
            deployment_decision = DeploymentDecision.APPROVE
        
        # 添加综合评分建议
        overall_score = metrics.overall_score
        if overall_score < 70:
            recommendations.append(f"综合质量分数较低({overall_score:.1f}分)，建议全面改进")
        elif overall_score < 85:
            recommendations.append(f"综合质量分数一般({overall_score:.1f}分)，建议重点改进")
        
        return QualityGateResult(
            status=status,
            passed_rules=passed_rules,
            failed_rules=failed_rules,
            warning_rules=warning_rules,
            metrics=metrics,
            deployment_decision=deployment_decision,
            recommendations=recommendations,
            timestamp=datetime.now()
        )
    
    def send_notifications(self, gate_result: QualityGateResult, 
                          build_info: Dict[str, Any] = None):
        """发送通知"""
        if build_info is None:
            build_info = {
                'branch': os.getenv('CI_COMMIT_REF_NAME', 'unknown'),
                'commit': os.getenv('CI_COMMIT_SHA', 'unknown')[:8],
                'build_number': os.getenv('CI_PIPELINE_ID', 'unknown'),
                'build_url': os.getenv('CI_PIPELINE_URL', '')
            }
        
        # Slack通知
        if self.notification_channels['slack']['enabled']:
            self._send_slack_notification(gate_result, build_info)
        
        # 邮件通知
        if self.notification_channels['email']['enabled']:
            self._send_email_notification(gate_result, build_info)
        
        # Teams通知
        if self.notification_channels['teams']['enabled']:
            self._send_teams_notification(gate_result, build_info)
    
    def _send_slack_notification(self, gate_result: QualityGateResult, 
                                build_info: Dict[str, Any]):
        """发送Slack通知"""
        try:
            webhook_url = self.notification_channels['slack']['webhook_url']
            
            # 确定颜色
            color_map = {
                QualityGateStatus.PASSED: 'good',
                QualityGateStatus.WARNING: 'warning',
                QualityGateStatus.FAILED: 'danger',
                QualityGateStatus.BLOCKED: 'danger'
            }
            
            color = color_map.get(gate_result.status, 'warning')
            
            # 构建消息
            message = {
                'channel': self.notification_channels['slack']['channel'],
                'username': 'Connect CI Bot',
                'icon_emoji': ':robot_face:',
                'attachments': [{
                    'color': color,
                    'title': f"Connect质量门禁检查 - {gate_result.status.value.upper()}",
                    'fields': [
                        {
                            'title': '分支',
                            'value': build_info['branch'],
                            'short': True
                        },
                        {
                            'title': '提交',
                            'value': build_info['commit'],
                            'short': True
                        },
                        {
                            'title': '构建号',
                            'value': build_info['build_number'],
                            'short': True
                        },
                        {
                            'title': '部署决策',
                            'value': gate_result.deployment_decision.value,
                            'short': True
                        },
                        {
                            'title': '测试成功率',
                            'value': f"{gate_result.metrics.test_success_rate:.1f}%",
                            'short': True
                        },
                        {
                            'title': '代码覆盖率',
                            'value': f"{gate_result.metrics.code_coverage:.1f}%",
                            'short': True
                        },
                        {
                            'title': '综合分数',
                            'value': f"{gate_result.metrics.overall_score:.1f}分",
                            'short': True
                        }
                    ],
                    'footer': 'Connect CI/CD',
                    'ts': int(gate_result.timestamp.timestamp())
                }]
            }
            
            if gate_result.recommendations:
                message['attachments'][0]['fields'].append({
                    'title': '改进建议',
                    'value': '\n'.join(gate_result.recommendations[:5]),
                    'short': False
                })
            
            if build_info.get('build_url'):
                message['attachments'][0]['title_link'] = build_info['build_url']
            
            response = requests.post(webhook_url, json=message, timeout=10)
            response.raise_for_status()
            
        except Exception as e:
            print(f"发送Slack通知失败: {e}")
    
    def _send_email_notification(self, gate_result: QualityGateResult, 
                                build_info: Dict[str, Any]):
        """发送邮件通知"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            config = self.notification_channels['email']
            
            # 创建邮件
            msg = MIMEMultipart('alternative')
            msg['Subject'] = f"Connect质量门禁检查 - {gate_result.status.value.upper()}"
            msg['From'] = config['username']
            msg['To'] = ', '.join(config['recipients'])
            
            # HTML内容
            html_content = f"""
            <html>
            <body>
                <h2>Connect质量门禁检查结果</h2>
                <p><strong>状态:</strong> {gate_result.status.value.upper()}</p>
                <p><strong>部署决策:</strong> {gate_result.deployment_decision.value}</p>
                
                <h3>构建信息</h3>
                <ul>
                    <li>分支: {build_info['branch']}</li>
                    <li>提交: {build_info['commit']}</li>
                    <li>构建号: {build_info['build_number']}</li>
                </ul>
                
                <h3>质量指标</h3>
                <ul>
                    <li>测试成功率: {gate_result.metrics.test_success_rate:.1f}%</li>
                    <li>代码覆盖率: {gate_result.metrics.code_coverage:.1f}%</li>
                    <li>性能分数: {gate_result.metrics.performance_score:.1f}分</li>
                    <li>安全分数: {gate_result.metrics.security_score:.1f}分</li>
                    <li>综合分数: {gate_result.metrics.overall_score:.1f}分</li>
                </ul>
                
                <h3>改进建议</h3>
                <ul>
            """
            
            for recommendation in gate_result.recommendations:
                html_content += f"<li>{recommendation}</li>"
            
            html_content += """
                </ul>
            </body>
            </html>
            """
            
            msg.attach(MIMEText(html_content, 'html'))
            
            # 发送邮件
            with smtplib.SMTP(config['smtp_server'], config['smtp_port']) as server:
                server.starttls()
                server.login(config['username'], config['password'])
                server.send_message(msg)
            
        except Exception as e:
            print(f"发送邮件通知失败: {e}")
    
    def _send_teams_notification(self, gate_result: QualityGateResult, 
                                build_info: Dict[str, Any]):
        """发送Teams通知"""
        try:
            webhook_url = self.notification_channels['teams']['webhook_url']
            
            # 确定主题颜色
            theme_color_map = {
                QualityGateStatus.PASSED: '00FF00',
                QualityGateStatus.WARNING: 'FFA500',
                QualityGateStatus.FAILED: 'FF0000',
                QualityGateStatus.BLOCKED: 'FF0000'
            }
            
            theme_color = theme_color_map.get(gate_result.status, 'FFA500')
            
            message = {
                '@type': 'MessageCard',
                '@context': 'http://schema.org/extensions',
                'themeColor': theme_color,
                'summary': f"Connect质量门禁检查 - {gate_result.status.value.upper()}",
                'sections': [{
                    'activityTitle': 'Connect质量门禁检查',
                    'activitySubtitle': f"状态: {gate_result.status.value.upper()}",
                    'facts': [
                        {'name': '分支', 'value': build_info['branch']},
                        {'name': '提交', 'value': build_info['commit']},
                        {'name': '构建号', 'value': build_info['build_number']},
                        {'name': '部署决策', 'value': gate_result.deployment_decision.value},
                        {'name': '测试成功率', 'value': f"{gate_result.metrics.test_success_rate:.1f}%"},
                        {'name': '代码覆盖率', 'value': f"{gate_result.metrics.code_coverage:.1f}%"},
                        {'name': '综合分数', 'value': f"{gate_result.metrics.overall_score:.1f}分"}
                    ],
                    'markdown': True
                }]
            }
            
            if build_info.get('build_url'):
                message['potentialAction'] = [{
                    '@type': 'OpenUri',
                    'name': '查看构建详情',
                    'targets': [{
                        'os': 'default',
                        'uri': build_info['build_url']
                    }]
                }]
            
            response = requests.post(webhook_url, json=message, timeout=10)
            response.raise_for_status()
            
        except Exception as e:
            print(f"发送Teams通知失败: {e}")
    
    def push_metrics_to_monitoring(self, metrics: QualityMetrics, 
                                  gate_result: QualityGateResult):
        """推送指标到监控系统"""
        # 推送到Prometheus
        self._push_to_prometheus(metrics, gate_result)
        
        # 推送到DataDog
        self._push_to_datadog(metrics, gate_result)
        
        # 推送到New Relic
        self._push_to_newrelic(metrics, gate_result)
    
    def _push_to_prometheus(self, metrics: QualityMetrics, 
                           gate_result: QualityGateResult):
        """推送指标到Prometheus"""
        try:
            from prometheus_client import CollectorRegistry, Gauge, push_to_gateway
            
            registry = CollectorRegistry()
            
            # 创建指标
            test_success_rate = Gauge('connect_test_success_rate', 
                                    'Test success rate percentage', 
                                    registry=registry)
            code_coverage = Gauge('connect_code_coverage', 
                                'Code coverage percentage', 
                                registry=registry)
            performance_score = Gauge('connect_performance_score', 
                                    'Performance score', 
                                    registry=registry)
            security_score = Gauge('connect_security_score', 
                                 'Security score', 
                                 registry=registry)
            overall_score = Gauge('connect_overall_quality_score', 
                                'Overall quality score', 
                                registry=registry)
            quality_gate_status = Gauge('connect_quality_gate_status', 
                                      'Quality gate status (1=passed, 0=failed)', 
                                      registry=registry)
            
            # 设置指标值
            test_success_rate.set(metrics.test_success_rate)
            code_coverage.set(metrics.code_coverage)
            performance_score.set(metrics.performance_score)
            security_score.set(metrics.security_score)
            overall_score.set(metrics.overall_score)
            quality_gate_status.set(1 if gate_result.status == QualityGateStatus.PASSED else 0)
            
            # 推送到网关
            gateway_url = self.monitoring_endpoints['prometheus']
            push_to_gateway(gateway_url, job='connect_ci', registry=registry)
            
        except Exception as e:
            print(f"推送指标到Prometheus失败: {e}")
    
    def _push_to_datadog(self, metrics: QualityMetrics, 
                        gate_result: QualityGateResult):
        """推送指标到DataDog"""
        try:
            api_key = os.getenv('DATADOG_API_KEY')
            if not api_key:
                return
            
            api_url = self.monitoring_endpoints['datadog']
            
            # 准备指标数据
            timestamp = int(time.time())
            metrics_data = {
                'series': [
                    {
                        'metric': 'connect.ci.test_success_rate',
                        'points': [[timestamp, metrics.test_success_rate]],
                        'tags': ['environment:ci', 'service:connect']
                    },
                    {
                        'metric': 'connect.ci.code_coverage',
                        'points': [[timestamp, metrics.code_coverage]],
                        'tags': ['environment:ci', 'service:connect']
                    },
                    {
                        'metric': 'connect.ci.performance_score',
                        'points': [[timestamp, metrics.performance_score]],
                        'tags': ['environment:ci', 'service:connect']
                    },
                    {
                        'metric': 'connect.ci.security_score',
                        'points': [[timestamp, metrics.security_score]],
                        'tags': ['environment:ci', 'service:connect']
                    },
                    {
                        'metric': 'connect.ci.overall_score',
                        'points': [[timestamp, metrics.overall_score]],
                        'tags': ['environment:ci', 'service:connect']
                    },
                    {
                        'metric': 'connect.ci.quality_gate_status',
                        'points': [[timestamp, 1 if gate_result.status == QualityGateStatus.PASSED else 0]],
                        'tags': ['environment:ci', 'service:connect']
                    }
                ]
            }
            
            headers = {
                'Content-Type': 'application/json',
                'DD-API-KEY': api_key
            }
            
            response = requests.post(
                f"{api_url}/series",
                json=metrics_data,
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            
        except Exception as e:
            print(f"推送指标到DataDog失败: {e}")
    
    def _push_to_newrelic(self, metrics: QualityMetrics, 
                         gate_result: QualityGateResult):
        """推送指标到New Relic"""
        try:
            api_key = os.getenv('NEWRELIC_API_KEY')
            if not api_key:
                return
            
            api_url = self.monitoring_endpoints['newrelic']
            
            # 准备事件数据
            event_data = {
                'eventType': 'ConnectCIQualityGate',
                'timestamp': int(gate_result.timestamp.timestamp() * 1000),
                'testSuccessRate': metrics.test_success_rate,
                'codeCoverage': metrics.code_coverage,
                'performanceScore': metrics.performance_score,
                'securityScore': metrics.security_score,
                'overallScore': metrics.overall_score,
                'qualityGateStatus': gate_result.status.value,
                'deploymentDecision': gate_result.deployment_decision.value,
                'branch': os.getenv('CI_COMMIT_REF_NAME', 'unknown'),
                'commit': os.getenv('CI_COMMIT_SHA', 'unknown'),
                'buildNumber': os.getenv('CI_PIPELINE_ID', 'unknown')
            }
            
            headers = {
                'Content-Type': 'application/json',
                'X-Api-Key': api_key
            }
            
            response = requests.post(
                f"{api_url}/events",
                json=[event_data],
                headers=headers,
                timeout=10
            )
            response.raise_for_status()
            
        except Exception as e:
            print(f"推送指标到New Relic失败: {e}")
    
    def generate_ci_report(self, gate_result: QualityGateResult, 
                          output_file: str = None) -> str:
        """生成CI报告"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"tests/reports/ci_report_{timestamp}.json"
        
        report_data = {
            'timestamp': gate_result.timestamp.isoformat(),
            'status': gate_result.status.value,
            'deployment_decision': gate_result.deployment_decision.value,
            'is_deployable': gate_result.is_deployable,
            'metrics': asdict(gate_result.metrics),
            'quality_gates': {
                'passed': gate_result.passed_rules,
                'failed': gate_result.failed_rules,
                'warnings': gate_result.warning_rules
            },
            'recommendations': gate_result.recommendations,
            'build_info': {
                'branch': os.getenv('CI_COMMIT_REF_NAME', 'unknown'),
                'commit': os.getenv('CI_COMMIT_SHA', 'unknown'),
                'build_number': os.getenv('CI_PIPELINE_ID', 'unknown'),
                'build_url': os.getenv('CI_PIPELINE_URL', '')
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        return output_file


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Connect CI/CD集成')
    parser.add_argument('--results-dir', default='tests/reports',
                       help='测试结果目录')
    parser.add_argument('--output', type=str,
                       help='CI报告输出文件')
    parser.add_argument('--notify', action='store_true',
                       help='发送通知')
    parser.add_argument('--push-metrics', action='store_true',
                       help='推送指标到监控系统')
    
    args = parser.parse_args()
    
    # 创建CI集成器
    ci = CIIntegration()
    
    print("开始CI/CD集成处理...")
    
    try:
        # 收集测试结果
        print("收集测试结果...")
        test_results = ci.collect_test_results(args.results_dir)
        
        # 计算质量指标
        print("计算质量指标...")
        metrics = ci.calculate_quality_metrics(test_results)
        
        # 评估质量门禁
        print("评估质量门禁...")
        gate_result = ci.evaluate_quality_gates(metrics)
        
        # 生成CI报告
        print("生成CI报告...")
        report_file = ci.generate_ci_report(gate_result, args.output)
        
        # 发送通知
        if args.notify:
            print("发送通知...")
            ci.send_notifications(gate_result)
        
        # 推送指标
        if args.push_metrics:
            print("推送指标到监控系统...")
            ci.push_metrics_to_monitoring(metrics, gate_result)
        
        # 打印结果
        print(f"\n{'='*60}")
        print(f"CI/CD集成处理完成")
        print(f"{'='*60}")
        print(f"质量门禁状态: {gate_result.status.value.upper()}")
        print(f"部署决策: {gate_result.deployment_decision.value}")
        print(f"综合质量分数: {metrics.overall_score:.1f}分")
        print(f"CI报告: {report_file}")
        
        if gate_result.recommendations:
            print(f"\n改进建议:")
            for i, rec in enumerate(gate_result.recommendations[:5], 1):
                print(f"  {i}. {rec}")
        
        # 设置退出码
        if gate_result.deployment_decision == DeploymentDecision.REJECT:
            sys.exit(1)
        elif gate_result.deployment_decision == DeploymentDecision.MANUAL_REVIEW:
            sys.exit(2)
        else:
            sys.exit(0)
            
    except Exception as e:
        print(f"CI/CD集成处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()