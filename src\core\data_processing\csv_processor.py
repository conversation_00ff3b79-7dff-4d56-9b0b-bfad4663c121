# -*- coding: utf-8 -*-
"""
CSV Processor for Unified Data Processing

This module provides high-performance CSV processing capabilities with
chunking support, encoding detection, and memory optimization.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, AsyncGenerator, Union, Callable

import pandas as pd
from .types import (
    ProcessingEngine, ProcessingMode, ProcessingStatus, DataFormat,
    ProcessingMetrics, ProcessingResult, ProcessingConfig, ChunkInfo
)
from .adapters import create_adapter, BaseAdapter
from ..utils.memory import MemoryMonitor
from ..utils.encoding import detect_encoding
from ..utils.validation import validate_csv_structure


class CSVProcessor:
    """High-performance CSV processor with chunking and optimization.
    
    Features:
    - Automatic encoding detection
    - Memory-optimized chunked processing
    - Multiple engine support (Pandas/Polars)
    - Async processing capabilities
    - Comprehensive error handling
    - Performance monitoring
    """
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        """Initialize CSV processor.
        
        Args:
            config: Processing configuration
        """
        self.config = config or ProcessingConfig()
        self.logger = logging.getLogger(__name__)
        self.memory_monitor = MemoryMonitor()
        
        # Processing state
        self._current_adapter: Optional[BaseAdapter] = None
        self._processing_metrics = ProcessingMetrics()
    
    async def read_csv(
        self,
        file_path: Union[str, Path],
        engine: Optional[ProcessingEngine] = None,
        chunk_size: Optional[int] = None,
        encoding: Optional[str] = None,
        delimiter: Optional[str] = None,
        **kwargs
    ) -> Union[pd.DataFrame, AsyncGenerator[pd.DataFrame, None]]:
        """Read CSV file with automatic optimization.
        
        Args:
            file_path: Path to CSV file
            engine: Processing engine to use
            chunk_size: Size of chunks for processing (None for full read)
            encoding: File encoding (auto-detected if None)
            delimiter: CSV delimiter (auto-detected if None)
            **kwargs: Additional arguments for CSV reading
            
        Returns:
            DataFrame or async generator of DataFrame chunks
        """
        file_path = Path(file_path)
        start_time = time.time()
        
        try:
            # Validate file
            if not file_path.exists():
                raise FileNotFoundError(f"CSV file not found: {file_path}")
            
            # Detect encoding if not provided
            if encoding is None:
                encoding = await self._detect_encoding(file_path)
            
            # Detect delimiter if not provided
            if delimiter is None:
                delimiter = await self._detect_delimiter(file_path, encoding)
            
            # Create adapter
            adapter = create_adapter(
                engine=engine,
                config=self.config,
                file_path=file_path
            )
            self._current_adapter = adapter
            
            # Prepare read parameters
            read_params = {
                'encoding': encoding,
                'separator': delimiter,
                **kwargs
            }
            
            # Determine if chunked processing is needed
            if chunk_size is None:
                chunk_size = await self._determine_chunk_size(file_path)
            
            if chunk_size and chunk_size > 0:
                # Chunked processing
                self.logger.info(f"Reading CSV in chunks of {chunk_size:,} records")
                return self._read_csv_chunks(
                    adapter, file_path, chunk_size, read_params
                )
            else:
                # Full file reading
                self.logger.info("Reading entire CSV file")
                data = await adapter.read_file(file_path, **read_params)
                
                # Convert to pandas if needed
                if hasattr(data, 'to_pandas'):
                    data = data.to_pandas()
                
                # Update metrics
                self._processing_metrics.records_processed = len(data)
                self._processing_metrics.processing_time = time.time() - start_time
                self._processing_metrics.status = ProcessingStatus.COMPLETED
                
                return data
                
        except Exception as e:
            self._processing_metrics.status = ProcessingStatus.FAILED
            self._processing_metrics.error_message = str(e)
            self.logger.error(f"Failed to read CSV file {file_path}: {e}")
            raise
    
    async def write_csv(
        self,
        data: Union[pd.DataFrame, Any],
        file_path: Union[str, Path],
        engine: Optional[ProcessingEngine] = None,
        encoding: str = 'utf-8',
        delimiter: str = ',',
        **kwargs
    ) -> ProcessingResult:
        """Write data to CSV file.
        
        Args:
            data: Data to write (DataFrame or compatible)
            file_path: Output file path
            engine: Processing engine to use
            encoding: File encoding
            delimiter: CSV delimiter
            **kwargs: Additional arguments for CSV writing
            
        Returns:
            Processing result
        """
        file_path = Path(file_path)
        start_time = time.time()
        
        try:
            # Create adapter
            adapter = create_adapter(
                engine=engine,
                config=self.config
            )
            
            # Convert data if needed
            if hasattr(adapter, 'from_pandas') and isinstance(data, pd.DataFrame):
                data = adapter.from_pandas(data)
            
            # Prepare write parameters
            write_params = {
                'encoding': encoding,
                'separator': delimiter,
                **kwargs
            }
            
            # Write file
            await adapter.write_file(data, file_path, **write_params)
            
            # Create result
            result = ProcessingResult(
                status=ProcessingStatus.COMPLETED,
                records_processed=len(data),
                processing_time=time.time() - start_time,
                output_path=file_path,
                metrics=self._processing_metrics
            )
            
            self.logger.info(f"Successfully wrote {len(data):,} records to {file_path}")
            return result
            
        except Exception as e:
            result = ProcessingResult(
                status=ProcessingStatus.FAILED,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
            self.logger.error(f"Failed to write CSV file {file_path}: {e}")
            return result
    
    async def process_csv(
        self,
        file_path: Union[str, Path],
        processor_func: Callable,
        output_path: Optional[Union[str, Path]] = None,
        engine: Optional[ProcessingEngine] = None,
        chunk_size: Optional[int] = None,
        mode: ProcessingMode = ProcessingMode.SYNC,
        **kwargs
    ) -> ProcessingResult:
        """Process CSV file with custom function.
        
        Args:
            file_path: Input CSV file path
            processor_func: Function to process data chunks
            output_path: Output file path (optional)
            engine: Processing engine to use
            chunk_size: Size of chunks for processing
            mode: Processing mode (sequential/parallel/async)
            **kwargs: Additional arguments
            
        Returns:
            Processing result
        """
        file_path = Path(file_path)
        start_time = time.time()
        processed_chunks = []
        total_records = 0
        
        try:
            # Read CSV in chunks
            chunk_generator = await self.read_csv(
                file_path=file_path,
                engine=engine,
                chunk_size=chunk_size,
                **kwargs
            )
            
            # Process chunks based on mode
            if mode == ProcessingMode.SYNC:
                async for chunk in chunk_generator:
                    processed_chunk = await self._process_chunk_async(
                        chunk, processor_func
                    )
                    processed_chunks.append(processed_chunk)
                    total_records += len(processed_chunk)
                    
                    # Memory management
                    if self.memory_monitor.should_gc():
                        await self.memory_monitor.force_gc()
            
            elif mode == ProcessingMode.PARALLEL:
                # Collect chunks for parallel processing
                chunks = []
                async for chunk in chunk_generator:
                    chunks.append(chunk)
                
                # Process chunks in parallel
                tasks = [
                    self._process_chunk_async(chunk, processor_func)
                    for chunk in chunks
                ]
                processed_chunks = await asyncio.gather(*tasks)
                total_records = sum(len(chunk) for chunk in processed_chunks)
            
            elif mode == ProcessingMode.ASYNC:
                # Async processing with concurrency control
                semaphore = asyncio.Semaphore(self.config.max_concurrent_chunks)
                tasks = []
                
                async for chunk in chunk_generator:
                    task = self._process_chunk_with_semaphore(
                        semaphore, chunk, processor_func
                    )
                    tasks.append(task)
                
                processed_chunks = await asyncio.gather(*tasks)
                total_records = sum(len(chunk) for chunk in processed_chunks)
            
            # Combine results if needed
            if processed_chunks and output_path:
                combined_data = pd.concat(processed_chunks, ignore_index=True)
                await self.write_csv(combined_data, output_path, engine=engine)
            
            # Create result
            result = ProcessingResult(
                status=ProcessingStatus.COMPLETED,
                records_processed=total_records,
                processing_time=time.time() - start_time,
                output_path=Path(output_path) if output_path else None,
                metrics=self._processing_metrics
            )
            
            self.logger.info(
                f"Successfully processed {total_records:,} records in {result.processing_time:.2f}s"
            )
            return result
            
        except Exception as e:
            result = ProcessingResult(
                status=ProcessingStatus.FAILED,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
            self.logger.error(f"Failed to process CSV file {file_path}: {e}")
            return result
    
    async def validate_csv(
        self,
        file_path: Union[str, Path],
        validation_rules: Dict[str, Any],
        engine: Optional[ProcessingEngine] = None,
        sample_size: Optional[int] = None
    ) -> Dict[str, Any]:
        """Validate CSV file structure and content.
        
        Args:
            file_path: CSV file path
            validation_rules: Validation rules
            engine: Processing engine to use
            sample_size: Number of records to sample for validation
            
        Returns:
            Validation results
        """
        file_path = Path(file_path)
        
        try:
            # Basic structure validation
            structure_result = await validate_csv_structure(file_path)
            if not structure_result['is_valid']:
                return structure_result
            
            # Create adapter
            adapter = create_adapter(
                engine=engine,
                config=self.config,
                file_path=file_path
            )
            
            # Read sample data
            if sample_size:
                # Read first chunk for validation
                chunk_size = min(sample_size, self.config.default_chunk_size)
                data_generator = await self.read_csv(
                    file_path=file_path,
                    engine=engine,
                    chunk_size=chunk_size
                )
                
                # Get first chunk
                async for data in data_generator:
                    break
            else:
                # Read entire file
                data = await self.read_csv(
                    file_path=file_path,
                    engine=engine
                )
            
            # Validate data content
            validation_result = await adapter.validate_data(data, validation_rules)
            
            # Combine structure and content validation
            combined_result = {
                **structure_result,
                **validation_result,
                'structure_valid': structure_result['is_valid'],
                'content_valid': validation_result['is_valid'],
                'is_valid': structure_result['is_valid'] and validation_result['is_valid']
            }
            
            return combined_result
            
        except Exception as e:
            return {
                'is_valid': False,
                'errors': [f"Validation error: {e}"]
            }
    
    def get_processing_metrics(self) -> ProcessingMetrics:
        """Get current processing metrics.
        
        Returns:
            Processing metrics
        """
        return self._processing_metrics
    
    async def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding.
        
        Args:
            file_path: Path to file
            
        Returns:
            Detected encoding
        """
        try:
            encoding = await detect_encoding(file_path)
            self.logger.debug(f"Detected encoding: {encoding}")
            return encoding
        except Exception as e:
            self.logger.warning(f"Failed to detect encoding, using utf-8: {e}")
            return 'utf-8'
    
    async def _detect_delimiter(self, file_path: Path, encoding: str) -> str:
        """Detect CSV delimiter.
        
        Args:
            file_path: Path to CSV file
            encoding: File encoding
            
        Returns:
            Detected delimiter
        """
        try:
            # Read sample of file
            sample_size = min(8192, file_path.stat().st_size)
            
            with open(file_path, 'r', encoding=encoding) as f:
                sample = f.read(sample_size)
            
            # Test common delimiters
            delimiters = [',', ';', '\t', '|', ':']
            delimiter_scores = {}
            
            for delimiter in delimiters:
                lines = sample.split('\n')[:10]  # Test first 10 lines
                if len(lines) < 2:
                    continue
                
                # Count fields per line
                field_counts = []
                for line in lines:
                    if line.strip():
                        field_counts.append(len(line.split(delimiter)))
                
                if field_counts:
                    # Score based on consistency
                    avg_fields = sum(field_counts) / len(field_counts)
                    consistency = 1.0 - (max(field_counts) - min(field_counts)) / max(1, avg_fields)
                    delimiter_scores[delimiter] = consistency * avg_fields
            
            if delimiter_scores:
                best_delimiter = max(delimiter_scores, key=delimiter_scores.get)
                self.logger.debug(f"Detected delimiter: '{best_delimiter}'")
                return best_delimiter
            else:
                self.logger.debug("Could not detect delimiter, using comma")
                return ','
                
        except Exception as e:
            self.logger.warning(f"Failed to detect delimiter, using comma: {e}")
            return ','
    
    async def _determine_chunk_size(self, file_path: Path) -> Optional[int]:
        """Determine optimal chunk size for file.
        
        Args:
            file_path: Path to file
            
        Returns:
            Optimal chunk size or None for full read
        """
        try:
            file_size = file_path.stat().st_size
            
            # Use chunking for files larger than threshold
            if file_size > self.config.chunk_size_threshold:
                # Estimate records per MB
                estimated_records_per_mb = 5000  # Conservative estimate
                file_size_mb = file_size / (1024 * 1024)
                estimated_total_records = int(file_size_mb * estimated_records_per_mb)
                
                # Calculate chunk size to keep memory usage reasonable
                target_memory_mb = self.config.max_memory_usage_mb
                estimated_memory_per_record = 200  # bytes
                max_records_in_memory = (target_memory_mb * 1024 * 1024) // estimated_memory_per_record
                
                chunk_size = min(
                    max_records_in_memory,
                    self.config.max_chunk_size,
                    max(self.config.min_chunk_size, estimated_total_records // 10)
                )
                
                self.logger.debug(
                    f"File size: {file_size_mb:.1f}MB, estimated records: {estimated_total_records:,}, "
                    f"chunk size: {chunk_size:,}"
                )
                
                return chunk_size
            else:
                return None  # Read entire file
                
        except Exception as e:
            self.logger.warning(f"Failed to determine chunk size: {e}")
            return self.config.default_chunk_size
    
    async def _read_csv_chunks(
        self,
        adapter: BaseAdapter,
        file_path: Path,
        chunk_size: int,
        read_params: Dict[str, Any]
    ) -> AsyncGenerator[pd.DataFrame, None]:
        """Read CSV file in chunks.
        
        Args:
            adapter: Data processing adapter
            file_path: Path to CSV file
            chunk_size: Size of each chunk
            read_params: Parameters for reading
            
        Yields:
            DataFrame chunks
        """
        try:
            chunk_count = 0
            total_records = 0
            
            async for chunk in adapter._read_file_chunks(file_path, chunk_size):
                # Convert to pandas if needed
                if hasattr(chunk, 'to_pandas'):
                    chunk = chunk.to_pandas()
                
                chunk_count += 1
                total_records += len(chunk)
                
                # Update metrics
                self._processing_metrics.chunks_processed = chunk_count
                self._processing_metrics.records_processed = total_records
                
                # Create chunk info
                chunk_info = ChunkInfo(
                    chunk_id=chunk_count,
                    start_row=(chunk_count - 1) * chunk_size,
                    end_row=(chunk_count - 1) * chunk_size + len(chunk) - 1,
                    record_count=len(chunk)
                )
                
                # Add chunk info as metadata
                chunk.attrs = {'chunk_info': chunk_info}
                
                self.logger.debug(f"Processed chunk {chunk_count}: {len(chunk):,} records")
                
                yield chunk
                
                # Memory management
                if chunk_count % 10 == 0:  # Every 10 chunks
                    if self.memory_monitor.should_gc():
                        await self.memory_monitor.force_gc()
            
            self.logger.info(
                f"Completed chunked reading: {chunk_count} chunks, {total_records:,} total records"
            )
            
        except Exception as e:
            self.logger.error(f"Error in chunked reading: {e}")
            raise
    
    async def _process_chunk_async(
        self,
        chunk: pd.DataFrame,
        processor_func: Callable
    ) -> pd.DataFrame:
        """Process a chunk asynchronously.
        
        Args:
            chunk: DataFrame chunk to process
            processor_func: Processing function
            
        Returns:
            Processed DataFrame chunk
        """
        try:
            # Run processor in thread pool for CPU-intensive operations
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, processor_func, chunk)
            return result
        except Exception as e:
            self.logger.error(f"Error processing chunk: {e}")
            raise
    
    async def _process_chunk_with_semaphore(
        self,
        semaphore: asyncio.Semaphore,
        chunk: pd.DataFrame,
        processor_func: Callable
    ) -> pd.DataFrame:
        """Process chunk with semaphore for concurrency control.
        
        Args:
            semaphore: Asyncio semaphore for concurrency control
            chunk: DataFrame chunk to process
            processor_func: Processing function
            
        Returns:
            Processed DataFrame chunk
        """
        async with semaphore:
            return await self._process_chunk_async(chunk, processor_func)