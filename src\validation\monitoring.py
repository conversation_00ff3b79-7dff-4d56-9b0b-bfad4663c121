"""统一验证框架监控和指标收集

提供验证框架的性能监控、指标收集和告警功能。
"""

import time
import threading
import psutil
import json
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
from contextlib import contextmanager
import logging
from pathlib import Path


@dataclass
class ValidationMetrics:
    """验证指标数据类"""
    timestamp: datetime
    validation_type: str
    data_size: int
    validation_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    throughput_rows_per_second: float
    error_count: int
    warning_count: int
    success_rate: float
    cache_hit_rate: float = 0.0
    parallel_workers: int = 1
    batch_size: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'validation_type': self.validation_type,
            'data_size': self.data_size,
            'validation_time': self.validation_time,
            'memory_usage_mb': self.memory_usage_mb,
            'cpu_usage_percent': self.cpu_usage_percent,
            'throughput_rows_per_second': self.throughput_rows_per_second,
            'error_count': self.error_count,
            'warning_count': self.warning_count,
            'success_rate': self.success_rate,
            'cache_hit_rate': self.cache_hit_rate,
            'parallel_workers': self.parallel_workers,
            'batch_size': self.batch_size
        }


@dataclass
class AlertThreshold:
    """告警阈值配置"""
    metric_name: str
    threshold_value: float
    comparison: str  # 'gt', 'lt', 'eq'
    severity: str    # 'warning', 'error', 'critical'
    description: str
    enabled: bool = True


@dataclass
class Alert:
    """告警信息"""
    timestamp: datetime
    metric_name: str
    current_value: float
    threshold_value: float
    severity: str
    description: str
    validation_type: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'metric_name': self.metric_name,
            'current_value': self.current_value,
            'threshold_value': self.threshold_value,
            'severity': self.severity,
            'description': self.description,
            'validation_type': self.validation_type
        }


class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self, name: str = ""):
        self.name = name
        self.start_time = None
        self.end_time = None
        self.duration = None
    
    def start(self):
        """开始计时"""
        self.start_time = time.time()
        return self
    
    def stop(self):
        """停止计时"""
        if self.start_time is None:
            raise ValueError("Timer not started")
        
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        return self.duration
    
    def __enter__(self):
        return self.start()
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self._baseline_memory = None
        self._baseline_cpu = None
    
    def get_memory_usage(self) -> float:
        """获取内存使用量（MB）"""
        return self.process.memory_info().rss / 1024 / 1024
    
    def get_cpu_usage(self) -> float:
        """获取CPU使用率（%）"""
        return self.process.cpu_percent()
    
    def get_memory_delta(self) -> float:
        """获取内存使用增量"""
        current_memory = self.get_memory_usage()
        if self._baseline_memory is None:
            self._baseline_memory = current_memory
            return 0.0
        return current_memory - self._baseline_memory
    
    def set_baseline(self):
        """设置基线"""
        self._baseline_memory = self.get_memory_usage()
        self._baseline_cpu = self.get_cpu_usage()
    
    def _get_disk_usage_percent(self) -> float:
        """获取磁盘使用率，兼容Windows和Unix系统"""
        try:
            import os
            if hasattr(psutil, 'disk_usage'):
                # 在Windows上使用当前工作目录，在Unix上使用根目录
                if os.name == 'nt':  # Windows
                    current_dir = os.path.abspath(os.getcwd())
                    return psutil.disk_usage(current_dir).percent
                else:  # Unix/Linux/macOS
                    return psutil.disk_usage('/').percent
            else:
                return 0.0
        except Exception:
            return 0.0
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            'memory_total_gb': psutil.virtual_memory().total / 1024 / 1024 / 1024,
            'memory_available_gb': psutil.virtual_memory().available / 1024 / 1024 / 1024,
            'cpu_count': psutil.cpu_count(),
            'cpu_freq_mhz': psutil.cpu_freq().current if psutil.cpu_freq() else 0,
            'disk_usage_percent': self._get_disk_usage_percent()
        }


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics_history: deque = deque(maxlen=max_history)
        self.aggregated_metrics: Dict[str, Any] = defaultdict(list)
        self.system_monitor = SystemMonitor()
        self._lock = threading.Lock()
    
    def record_validation_metrics(self, 
                                validation_type: str,
                                data_size: int,
                                validation_time: float,
                                error_count: int,
                                warning_count: int,
                                cache_hit_rate: float = 0.0,
                                parallel_workers: int = 1,
                                batch_size: int = 0) -> ValidationMetrics:
        """记录验证指标"""
        
        # 获取系统资源使用情况
        memory_usage = self.system_monitor.get_memory_usage()
        cpu_usage = self.system_monitor.get_cpu_usage()
        
        # 计算吞吐量和成功率
        throughput = data_size / validation_time if validation_time > 0 else 0
        total_issues = error_count + warning_count
        success_rate = (data_size - error_count) / data_size if data_size > 0 else 0
        
        # 创建指标对象
        metrics = ValidationMetrics(
            timestamp=datetime.now(),
            validation_type=validation_type,
            data_size=data_size,
            validation_time=validation_time,
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage,
            throughput_rows_per_second=throughput,
            error_count=error_count,
            warning_count=warning_count,
            success_rate=success_rate,
            cache_hit_rate=cache_hit_rate,
            parallel_workers=parallel_workers,
            batch_size=batch_size
        )
        
        # 存储指标
        with self._lock:
            self.metrics_history.append(metrics)
            
            # 更新聚合指标
            self.aggregated_metrics[validation_type].append(metrics)
            if len(self.aggregated_metrics[validation_type]) > self.max_history:
                self.aggregated_metrics[validation_type].pop(0)
        
        return metrics
    
    def get_recent_metrics(self, count: int = 10) -> List[ValidationMetrics]:
        """获取最近的指标"""
        with self._lock:
            return list(self.metrics_history)[-count:]
    
    def get_metrics_by_type(self, validation_type: str, count: int = 10) -> List[ValidationMetrics]:
        """按类型获取指标"""
        with self._lock:
            type_metrics = self.aggregated_metrics.get(validation_type, [])
            return type_metrics[-count:]
    
    def get_aggregated_stats(self, validation_type: Optional[str] = None, 
                           time_window: Optional[timedelta] = None) -> Dict[str, Any]:
        """获取聚合统计信息"""
        with self._lock:
            if validation_type:
                metrics_list = self.aggregated_metrics.get(validation_type, [])
            else:
                metrics_list = list(self.metrics_history)
            
            # 时间窗口过滤
            if time_window:
                cutoff_time = datetime.now() - time_window
                metrics_list = [m for m in metrics_list if m.timestamp >= cutoff_time]
            
            if not metrics_list:
                return {}
            
            # 计算统计信息
            validation_times = [m.validation_time for m in metrics_list]
            throughputs = [m.throughput_rows_per_second for m in metrics_list]
            memory_usages = [m.memory_usage_mb for m in metrics_list]
            success_rates = [m.success_rate for m in metrics_list]
            
            return {
                'count': len(metrics_list),
                'avg_validation_time': sum(validation_times) / len(validation_times),
                'max_validation_time': max(validation_times),
                'min_validation_time': min(validation_times),
                'avg_throughput': sum(throughputs) / len(throughputs),
                'max_throughput': max(throughputs),
                'avg_memory_usage': sum(memory_usages) / len(memory_usages),
                'max_memory_usage': max(memory_usages),
                'avg_success_rate': sum(success_rates) / len(success_rates),
                'total_data_processed': sum(m.data_size for m in metrics_list),
                'total_errors': sum(m.error_count for m in metrics_list),
                'total_warnings': sum(m.warning_count for m in metrics_list)
            }
    
    def export_metrics(self, file_path: str, format: str = 'json') -> None:
        """导出指标数据"""
        with self._lock:
            metrics_data = [m.to_dict() for m in self.metrics_history]
        
        if format.lower() == 'json':
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"Unsupported format: {format}")
    
    def clear_metrics(self) -> None:
        """清空指标数据"""
        with self._lock:
            self.metrics_history.clear()
            self.aggregated_metrics.clear()


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.thresholds: List[AlertThreshold] = []
        self.alerts_history: deque = deque(maxlen=1000)
        self.alert_handlers: List[Callable[[Alert], None]] = []
        self._lock = threading.Lock()
        
        # 默认告警阈值
        self._setup_default_thresholds()
    
    def _setup_default_thresholds(self):
        """设置默认告警阈值"""
        default_thresholds = [
            AlertThreshold(
                metric_name='validation_time',
                threshold_value=30.0,
                comparison='gt',
                severity='warning',
                description='验证时间过长'
            ),
            AlertThreshold(
                metric_name='validation_time',
                threshold_value=60.0,
                comparison='gt',
                severity='error',
                description='验证时间严重超时'
            ),
            AlertThreshold(
                metric_name='memory_usage_mb',
                threshold_value=2048.0,
                comparison='gt',
                severity='warning',
                description='内存使用过高'
            ),
            AlertThreshold(
                metric_name='memory_usage_mb',
                threshold_value=4096.0,
                comparison='gt',
                severity='error',
                description='内存使用严重超标'
            ),
            AlertThreshold(
                metric_name='success_rate',
                threshold_value=0.9,
                comparison='lt',
                severity='warning',
                description='验证成功率过低'
            ),
            AlertThreshold(
                metric_name='success_rate',
                threshold_value=0.8,
                comparison='lt',
                severity='error',
                description='验证成功率严重过低'
            ),
            AlertThreshold(
                metric_name='throughput_rows_per_second',
                threshold_value=1000.0,
                comparison='lt',
                severity='warning',
                description='处理吞吐量过低'
            )
        ]
        
        self.thresholds.extend(default_thresholds)
    
    def add_threshold(self, threshold: AlertThreshold) -> None:
        """添加告警阈值"""
        with self._lock:
            self.thresholds.append(threshold)
    
    def remove_threshold(self, metric_name: str, threshold_value: float) -> None:
        """移除告警阈值"""
        with self._lock:
            self.thresholds = [
                t for t in self.thresholds 
                if not (t.metric_name == metric_name and t.threshold_value == threshold_value)
            ]
    
    def add_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """添加告警处理器"""
        self.alert_handlers.append(handler)
    
    def check_metrics(self, metrics: ValidationMetrics) -> List[Alert]:
        """检查指标并生成告警"""
        alerts = []
        
        with self._lock:
            for threshold in self.thresholds:
                if not threshold.enabled:
                    continue
                
                # 获取指标值
                metric_value = getattr(metrics, threshold.metric_name, None)
                if metric_value is None:
                    continue
                
                # 检查阈值
                triggered = False
                if threshold.comparison == 'gt' and metric_value > threshold.threshold_value:
                    triggered = True
                elif threshold.comparison == 'lt' and metric_value < threshold.threshold_value:
                    triggered = True
                elif threshold.comparison == 'eq' and metric_value == threshold.threshold_value:
                    triggered = True
                
                if triggered:
                    alert = Alert(
                        timestamp=datetime.now(),
                        metric_name=threshold.metric_name,
                        current_value=metric_value,
                        threshold_value=threshold.threshold_value,
                        severity=threshold.severity,
                        description=threshold.description,
                        validation_type=metrics.validation_type
                    )
                    
                    alerts.append(alert)
                    self.alerts_history.append(alert)
                    
                    # 触发告警处理器
                    for handler in self.alert_handlers:
                        try:
                            handler(alert)
                        except Exception as e:
                            logging.error(f"Alert handler error: {e}")
        
        return alerts
    
    def get_recent_alerts(self, count: int = 10) -> List[Alert]:
        """获取最近的告警"""
        with self._lock:
            return list(self.alerts_history)[-count:]
    
    def get_alerts_by_severity(self, severity: str) -> List[Alert]:
        """按严重程度获取告警"""
        with self._lock:
            return [alert for alert in self.alerts_history if alert.severity == severity]


class ValidationMonitor:
    """验证监控器主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.enabled = self.config.get('enabled', True)
        self.collect_metrics = self.config.get('collect_metrics', True)
        self.export_metrics = self.config.get('export_metrics', False)
        self.metrics_export_path = self.config.get('metrics_export_path')
        
        # 初始化组件
        self.metrics_collector = MetricsCollector(
            max_history=self.config.get('max_history', 1000)
        )
        self.alert_manager = AlertManager()
        self.system_monitor = SystemMonitor()
        
        # 设置告警处理器
        self._setup_alert_handlers()
        
        # 设置自动导出
        if self.export_metrics and self.metrics_export_path:
            self._setup_auto_export()
    
    def _setup_alert_handlers(self):
        """设置告警处理器"""
        # 日志告警处理器
        def log_alert_handler(alert: Alert):
            level = logging.ERROR if alert.severity in ['error', 'critical'] else logging.WARNING
            logging.log(level, f"Validation Alert: {alert.description} - {alert.metric_name}={alert.current_value} (threshold={alert.threshold_value})")
        
        self.alert_manager.add_alert_handler(log_alert_handler)
    
    def _setup_auto_export(self):
        """设置自动导出"""
        def auto_export():
            try:
                export_path = Path(self.metrics_export_path)
                export_path.parent.mkdir(parents=True, exist_ok=True)
                
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                file_path = export_path.parent / f"{export_path.stem}_{timestamp}.json"
                
                self.metrics_collector.export_metrics(str(file_path))
                logging.info(f"Metrics exported to {file_path}")
            except Exception as e:
                logging.error(f"Failed to export metrics: {e}")
        
        # 定期导出（这里简化实现，实际可以使用定时器）
        # 在实际使用中，可以集成到应用的定时任务中
        pass
    
    @contextmanager
    def monitor_validation(self, validation_type: str, data_size: int):
        """验证监控上下文管理器"""
        if not self.enabled:
            yield None
            return
        
        # 设置基线
        self.system_monitor.set_baseline()
        
        # 开始计时
        timer = PerformanceTimer(f"{validation_type}_validation")
        timer.start()
        
        validation_context = {
            'validation_type': validation_type,
            'data_size': data_size,
            'start_time': datetime.now(),
            'timer': timer
        }
        
        try:
            yield validation_context
        finally:
            # 停止计时
            validation_time = timer.stop()
            
            # 收集指标（如果启用）
            if self.collect_metrics:
                # 从上下文获取额外信息
                error_count = validation_context.get('error_count', 0)
                warning_count = validation_context.get('warning_count', 0)
                cache_hit_rate = validation_context.get('cache_hit_rate', 0.0)
                parallel_workers = validation_context.get('parallel_workers', 1)
                batch_size = validation_context.get('batch_size', 0)
                
                # 记录指标
                metrics = self.metrics_collector.record_validation_metrics(
                    validation_type=validation_type,
                    data_size=data_size,
                    validation_time=validation_time,
                    error_count=error_count,
                    warning_count=warning_count,
                    cache_hit_rate=cache_hit_rate,
                    parallel_workers=parallel_workers,
                    batch_size=batch_size
                )
                
                # 检查告警
                alerts = self.alert_manager.check_metrics(metrics)
                
                # 更新上下文
                validation_context['metrics'] = metrics
                validation_context['alerts'] = alerts
    
    def get_performance_summary(self, validation_type: Optional[str] = None, 
                              time_window: Optional[timedelta] = None) -> Dict[str, Any]:
        """获取性能摘要"""
        stats = self.metrics_collector.get_aggregated_stats(validation_type, time_window)
        recent_alerts = self.alert_manager.get_recent_alerts(10)
        system_info = self.system_monitor.get_system_info()
        
        return {
            'statistics': stats,
            'recent_alerts': [alert.to_dict() for alert in recent_alerts],
            'system_info': system_info,
            'monitoring_enabled': self.enabled,
            'metrics_collection_enabled': self.collect_metrics
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        recent_metrics = self.metrics_collector.get_recent_metrics(5)
        recent_alerts = self.alert_manager.get_recent_alerts(5)
        
        # 计算健康分数
        health_score = 100.0
        
        if recent_metrics:
            avg_success_rate = sum(m.success_rate for m in recent_metrics) / len(recent_metrics)
            health_score *= avg_success_rate
        
        # 根据告警降低健康分数
        error_alerts = [a for a in recent_alerts if a.severity in ['error', 'critical']]
        warning_alerts = [a for a in recent_alerts if a.severity == 'warning']
        
        health_score -= len(error_alerts) * 10
        health_score -= len(warning_alerts) * 5
        health_score = max(0, health_score)
        
        # 确定健康状态
        if health_score >= 90:
            status = 'healthy'
        elif health_score >= 70:
            status = 'warning'
        elif health_score >= 50:
            status = 'degraded'
        else:
            status = 'unhealthy'
        
        return {
            'status': status,
            'health_score': health_score,
            'recent_error_count': len(error_alerts),
            'recent_warning_count': len(warning_alerts),
            'last_validation_time': recent_metrics[-1].timestamp.isoformat() if recent_metrics else None
        }
    
    def export_report(self, file_path: str, include_history: bool = True) -> None:
        """导出监控报告"""
        report = {
            'generated_at': datetime.now().isoformat(),
            'summary': self.get_performance_summary(),
            'health_status': self.get_health_status(),
            'configuration': self.config
        }
        
        if include_history:
            report['metrics_history'] = [
                m.to_dict() for m in self.metrics_collector.get_recent_metrics(100)
            ]
            report['alerts_history'] = [
                a.to_dict() for a in self.alert_manager.get_recent_alerts(50)
            ]
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)


# 全局监控器实例
_global_monitor: Optional[ValidationMonitor] = None


def get_monitor(config: Optional[Dict[str, Any]] = None) -> ValidationMonitor:
    """获取全局监控器实例"""
    global _global_monitor
    
    if _global_monitor is None:
        _global_monitor = ValidationMonitor(config)
    
    return _global_monitor


def configure_monitoring(config: Dict[str, Any]) -> None:
    """配置监控"""
    global _global_monitor
    _global_monitor = ValidationMonitor(config)


@contextmanager
def monitor_validation(validation_type: str, data_size: int, config: Optional[Dict[str, Any]] = None):
    """验证监控上下文管理器（便捷函数）"""
    monitor = get_monitor(config)
    with monitor.monitor_validation(validation_type, data_size) as context:
        yield context


def get_performance_metrics() -> Dict[str, Any]:
    """获取性能指标（便捷函数）"""
    monitor = get_monitor()
    return monitor.get_performance_summary()


def get_health_status() -> Dict[str, Any]:
    """获取健康状态（便捷函数）"""
    monitor = get_monitor()
    return monitor.get_health_status()