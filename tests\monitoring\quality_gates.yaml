# Connect质量门禁配置
# 定义各种质量标准和阈值

# 全局质量门禁设置
global:
  enabled: true
  fail_on_error: true
  notification_channels:
    - slack
    - email
  
# 代码质量门禁
code_quality:
  # 代码覆盖率要求
  coverage:
    line_coverage:
      threshold: 85.0
      operator: ">="
      critical: true
    branch_coverage:
      threshold: 80.0
      operator: ">="
      critical: true
    function_coverage:
      threshold: 90.0
      operator: ">="
      critical: false
  
  # 代码复杂度
  complexity:
    cyclomatic_complexity:
      threshold: 10
      operator: "<="
      critical: true
    cognitive_complexity:
      threshold: 15
      operator: "<="
      critical: false
  
  # 代码质量指标
  quality_metrics:
    maintainability_index:
      threshold: 70
      operator: ">="
      critical: false
    technical_debt_ratio:
      threshold: 5.0
      operator: "<="
      critical: true
    code_smells:
      threshold: 50
      operator: "<="
      critical: false
    duplicated_lines_density:
      threshold: 3.0
      operator: "<="
      critical: true

# 测试质量门禁
test_quality:
  # 测试覆盖率
  test_coverage:
    unit_test_coverage:
      threshold: 80.0
      operator: ">="
      critical: true
    integration_test_coverage:
      threshold: 70.0
      operator: ">="
      critical: true
    e2e_test_coverage:
      threshold: 60.0
      operator: ">="
      critical: false
  
  # 测试通过率
  pass_rates:
    unit_tests:
      threshold: 95.0
      operator: ">="
      critical: true
    integration_tests:
      threshold: 90.0
      operator: ">="
      critical: true
    e2e_tests:
      threshold: 85.0
      operator: ">="
      critical: true
    performance_tests:
      threshold: 80.0
      operator: ">="
      critical: false
    security_tests:
      threshold: 100.0
      operator: ">="
      critical: true
  
  # 测试稳定性
  stability:
    flaky_test_rate:
      threshold: 5.0
      operator: "<="
      critical: false
    test_execution_time:
      threshold: 1800  # 30分钟
      operator: "<="
      critical: true

# 性能质量门禁
performance:
  # 响应时间要求
  response_time:
    api_avg_response_time:
      threshold: 500  # ms
      operator: "<="
      critical: true
    api_95th_percentile:
      threshold: 1000  # ms
      operator: "<="
      critical: true
    page_load_time:
      threshold: 3000  # ms
      operator: "<="
      critical: true
    database_query_time:
      threshold: 100  # ms
      operator: "<="
      critical: false
  
  # 吞吐量要求
  throughput:
    requests_per_second:
      threshold: 100
      operator: ">="
      critical: false
    concurrent_users:
      threshold: 20
      operator: ">="
      critical: true
    data_processing_rate:
      threshold: 500000  # 每秒处理50万行数据
      operator: ">="
      critical: true
  
  # 资源使用
  resource_usage:
    cpu_usage:
      threshold: 80.0  # %
      operator: "<="
      critical: true
    memory_usage:
      threshold: 85.0  # %
      operator: "<="
      critical: true
    disk_io:
      threshold: 1000  # IOPS
      operator: "<="
      critical: false
    network_latency:
      threshold: 50  # ms
      operator: "<="
      critical: false
  
  # 错误率
  error_rates:
    http_error_rate:
      threshold: 0.1  # %
      operator: "<="
      critical: true
    application_error_rate:
      threshold: 0.05  # %
      operator: "<="
      critical: true
    timeout_rate:
      threshold: 1.0  # %
      operator: "<="
      critical: false

# 安全质量门禁
security:
  # 漏洞检测
  vulnerabilities:
    critical_vulnerabilities:
      threshold: 0
      operator: "=="
      critical: true
    high_vulnerabilities:
      threshold: 0
      operator: "=="
      critical: true
    medium_vulnerabilities:
      threshold: 5
      operator: "<="
      critical: false
    low_vulnerabilities:
      threshold: 10
      operator: "<="
      critical: false
  
  # 安全扫描
  security_scans:
    dependency_scan_pass:
      threshold: true
      operator: "=="
      critical: true
    static_analysis_pass:
      threshold: true
      operator: "=="
      critical: true
    dynamic_analysis_pass:
      threshold: true
      operator: "=="
      critical: false
    penetration_test_pass:
      threshold: true
      operator: "=="
      critical: false
  
  # 合规性检查
  compliance:
    gdpr_compliance:
      threshold: true
      operator: "=="
      critical: true
    data_encryption:
      threshold: true
      operator: "=="
      critical: true
    access_control:
      threshold: true
      operator: "=="
      critical: true
    audit_logging:
      threshold: true
      operator: "=="
      critical: false
  
  # 安全配置
  security_config:
    secure_headers:
      threshold: true
      operator: "=="
      critical: true
    ssl_configuration:
      threshold: true
      operator: "=="
      critical: true
    authentication_strength:
      threshold: 80
      operator: ">="
      critical: true
    session_security:
      threshold: true
      operator: "=="
      critical: true

# 业务质量门禁
business:
  # 数据质量
  data_quality:
    data_accuracy:
      threshold: 99.9  # %
      operator: ">="
      critical: true
    data_completeness:
      threshold: 95.0  # %
      operator: ">="
      critical: true
    data_consistency:
      threshold: 99.0  # %
      operator: ">="
      critical: true
    data_freshness:
      threshold: 300  # 秒
      operator: "<="
      critical: false
  
  # 功能正确性
  functionality:
    calculation_accuracy:
      threshold: 99.99  # %
      operator: ">="
      critical: true
    geo_analysis_precision:
      threshold: 95.0  # %
      operator: ">="
      critical: true
    report_generation_success:
      threshold: 98.0  # %
      operator: ">="
      critical: true
    data_export_integrity:
      threshold: 100.0  # %
      operator: ">="
      critical: true
  
  # 用户体验
  user_experience:
    page_load_satisfaction:
      threshold: 90.0  # %
      operator: ">="
      critical: false
    feature_usability_score:
      threshold: 80.0
      operator: ">="
      critical: false
    error_message_clarity:
      threshold: 85.0  # %
      operator: ">="
      critical: false

# 可用性质量门禁
availability:
  # 系统可用性
  uptime:
    system_availability:
      threshold: 99.5  # %
      operator: ">="
      critical: true
    service_availability:
      threshold: 99.0  # %
      operator: ">="
      critical: true
    database_availability:
      threshold: 99.9  # %
      operator: ">="
      critical: true
  
  # 故障恢复
  recovery:
    mttr:  # Mean Time To Recovery
      threshold: 300  # 秒
      operator: "<="
      critical: true
    rto:  # Recovery Time Objective
      threshold: 600  # 秒
      operator: "<="
      critical: true
    rpo:  # Recovery Point Objective
      threshold: 60  # 秒
      operator: "<="
      critical: false
  
  # 监控和告警
  monitoring:
    alert_response_time:
      threshold: 60  # 秒
      operator: "<="
      critical: true
    false_positive_rate:
      threshold: 5.0  # %
      operator: "<="
      critical: false
    monitoring_coverage:
      threshold: 95.0  # %
      operator: ">="
      critical: true

# 部署质量门禁
deployment:
  # 部署成功率
  deployment_success:
    deployment_success_rate:
      threshold: 95.0  # %
      operator: ">="
      critical: true
    rollback_rate:
      threshold: 5.0  # %
      operator: "<="
      critical: false
    deployment_time:
      threshold: 1800  # 秒
      operator: "<="
      critical: false
  
  # 环境一致性
  environment_consistency:
    config_drift:
      threshold: 0
      operator: "=="
      critical: true
    version_consistency:
      threshold: true
      operator: "=="
      critical: true
    dependency_consistency:
      threshold: true
      operator: "=="
      critical: true
  
  # 部署验证
  post_deployment:
    health_check_pass:
      threshold: true
      operator: "=="
      critical: true
    smoke_test_pass:
      threshold: true
      operator: "=="
      critical: true
    integration_test_pass:
      threshold: true
      operator: "=="
      critical: true

# 通知配置
notifications:
  slack:
    webhook_url: "${SLACK_WEBHOOK_URL}"
    channel: "#quality-alerts"
    username: "Quality Gate Bot"
    icon_emoji: ":warning:"
    
  email:
    smtp_server: "${SMTP_SERVER}"
    smtp_port: 587
    username: "${SMTP_USERNAME}"
    password: "${SMTP_PASSWORD}"
    from_email: "<EMAIL>"
    to_emails:
      - "<EMAIL>"
      - "<EMAIL>"
    
  teams:
    webhook_url: "${TEAMS_WEBHOOK_URL}"
    
  webhook:
    url: "${WEBHOOK_URL}"
    headers:
      Authorization: "Bearer ${WEBHOOK_TOKEN}"
      Content-Type: "application/json"

# 报告配置
reporting:
  # 报告生成
  generation:
    auto_generate: true
    schedule: "0 8 * * *"  # 每天早上8点
    formats:
      - html
      - json
      - pdf
    
  # 报告内容
  content:
    include_trends: true
    include_details: true
    include_recommendations: true
    retention_days: 90
    
  # 报告分发
  distribution:
    email_recipients:
      - "<EMAIL>"
      - "<EMAIL>"
      - "<EMAIL>"
    storage_path: "reports/quality-gates"
    
# 历史数据配置
history:
  retention:
    test_results: 180  # 天
    quality_metrics: 365  # 天
    performance_data: 90  # 天
    security_scans: 365  # 天
    
  aggregation:
    daily_summary: true
    weekly_summary: true
    monthly_summary: true
    
# 集成配置
integrations:
  jenkins:
    enabled: true
    webhook_path: "/webhook/jenkins"
    
  github:
    enabled: true
    webhook_path: "/webhook/github"
    secret: "${GITHUB_WEBHOOK_SECRET}"
    
  gitlab:
    enabled: true
    webhook_path: "/webhook/gitlab"
    token: "${GITLAB_WEBHOOK_TOKEN}"
    
  sonarqube:
    enabled: true
    server_url: "${SONARQUBE_URL}"
    token: "${SONARQUBE_TOKEN}"
    
  jira:
    enabled: false
    server_url: "${JIRA_URL}"
    username: "${JIRA_USERNAME}"
    api_token: "${JIRA_API_TOKEN}"
    project_key: "CONNECT"