"""Tests for telecommunications type definitions."""

import pytest
from datetime import datetime
from pathlib import Path

# Import directly from the module file to avoid conflicts with built-in types
import importlib.util
spec = importlib.util.spec_from_file_location('telecom_types', 'src/types/telecom_types.py')
telecom_types = importlib.util.module_from_spec(spec)
spec.loader.exec_module(telecom_types)


class TestTelecomTypes:
    """Test cases for telecommunications type definitions."""

    def test_data_source_type_enum(self):
        """Test DataSourceType enum values."""
        assert telecom_types.DataSourceType.CDR == "cdr"
        assert telecom_types.DataSourceType.EP == "ep"
        assert telecom_types.DataSourceType.KPI == "kpi"
        assert telecom_types.DataSourceType.NLG == "nlg"
        assert telecom_types.DataSourceType.CFG == "cfg"

    def test_processing_status_enum(self):
        """Test ProcessingStatus enum values."""
        assert telecom_types.ProcessingStatus.PENDING == "pending"
        assert telecom_types.ProcessingStatus.PROCESSING == "processing"
        assert telecom_types.ProcessingStatus.COMPLETED == "completed"
        assert telecom_types.ProcessingStatus.FAILED == "failed"
        assert telecom_types.ProcessingStatus.CANCELLED == "cancelled"
        assert telecom_types.ProcessingStatus.RETRYING == "retrying"

    def test_signal_type_enum(self):
        """Test SignalType enum values."""
        assert telecom_types.SignalType.RSRP == "rsrp"
        assert telecom_types.SignalType.RSRQ == "rsrq"
        assert telecom_types.SignalType.SINR == "sinr"
        assert telecom_types.SignalType.RSSI == "rssi"

    def test_network_technology_enum(self):
        """Test NetworkTechnology enum values."""
        assert telecom_types.NetworkTechnology.GSM == "gsm"
        assert telecom_types.NetworkTechnology.UMTS == "umts"
        assert telecom_types.NetworkTechnology.LTE == "lte"
        assert telecom_types.NetworkTechnology.NR == "nr"

    def test_call_status_enum(self):
        """Test CallStatus enum values."""
        assert telecom_types.CallStatus.INITIATED == "initiated"
        assert telecom_types.CallStatus.CONNECTED == "connected"
        assert telecom_types.CallStatus.COMPLETED == "completed"
        assert telecom_types.CallStatus.FAILED == "failed"
        assert telecom_types.CallStatus.DROPPED == "dropped"

    def test_cdr_record_creation(self):
        """Test CDRRecord dataclass creation."""
        cdr = telecom_types.CDRRecord(
            call_id="call_001",
            caller_number="1234567890",
            called_number="0987654321",
            call_start_time=datetime.now(),
            call_end_time=None,
            call_duration=120,
            call_status=telecom_types.CallStatus.COMPLETED,
            cell_id="cell_001",
            lac=123,
            imsi="123456789012345",
            cell_tower_lat=40.7128,
            cell_tower_lon=-74.0060,
            network_technology=telecom_types.NetworkTechnology.LTE
        )
        
        assert cdr.call_id == "call_001"
        assert cdr.caller_number == "1234567890"
        assert cdr.call_status == telecom_types.CallStatus.COMPLETED
        assert cdr.network_technology == telecom_types.NetworkTechnology.LTE

    def test_ep_record_creation(self):
        """Test EPRecord dataclass creation."""
        ep = telecom_types.EPRecord(
            measurement_id="m001",
            timestamp=datetime.now(),
            latitude=40.7128,
            longitude=-74.0060,
            rsrp=-85.0,
            rsrq=-10.0,
            sinr=15.0,
            cell_id="cell_001",
            frequency=2100.0,
            network_technology=telecom_types.NetworkTechnology.LTE,
            signal_quality="good"
        )
        
        assert ep.measurement_id == "m001"
        assert ep.latitude == 40.7128
        assert ep.longitude == -74.0060
        assert ep.rsrp == -85.0

    def test_kpi_record_creation(self):
        """Test KPIRecord dataclass creation."""
        kpi = telecom_types.KPIRecord(
            kpi_id="kpi_001",
            kpi_type="call_success_rate",
            value=95.5,
            unit="percentage",
            entity_id="cell_001",
            entity_type="cell",
            calculation_time=datetime.now(),
            data_points=1000,
            threshold=95.0,
            meets_threshold=True
        )
        
        assert kpi.kpi_id == "kpi_001"
        assert kpi.kpi_type == "call_success_rate"
        assert kpi.value == 95.5
        assert kpi.meets_threshold is True

    def test_import_result_creation(self):
        """Test ImportResult dataclass creation."""
        result = telecom_types.ImportResult(
            success=True,
            records_imported=1000,
            records_failed=5,
            source_path=Path("test.csv"),
            error_message=None,
            processing_time=1.5,
            file_size_bytes=1024,
            metadata={"test": "value"}
        )
        
        assert result.success is True
        assert result.records_imported == 1000
        assert result.processing_time == 1.5
        assert result.metadata["test"] == "value"

    def test_import_result_post_init(self):
        """Test ImportResult post_init method."""
        result = telecom_types.ImportResult(
            success=True,
            records_imported=100,
            processing_time=1.0
        )
        
        # metadata should be initialized to empty dict
        assert result.metadata == {}

    def test_validation_result_creation(self):
        """Test ValidationResult dataclass creation."""
        result = telecom_types.ValidationResult(
            is_valid=True,
            errors=["Error 1", "Error 2"],
            warnings=["Warning 1"],
            validated_records=100,
            invalid_records=5
        )
        
        assert result.is_valid is True
        assert len(result.errors) == 2
        assert len(result.warnings) == 1

    def test_validation_result_post_init(self):
        """Test ValidationResult post_init method."""
        result = telecom_types.ValidationResult(
            is_valid=False,
            validated_records=95,
            invalid_records=5
        )
        
        # errors and warnings should be initialized to empty lists
        assert result.errors == []
        assert result.warnings == []

    def test_processing_metrics_creation(self):
        """Test ProcessingMetrics dataclass creation."""
        metrics = telecom_types.ProcessingMetrics(
            start_time=datetime.now(),
            end_time=datetime.now(),
            processing_time_seconds=1.5,
            memory_usage_mb=256.0,
            cpu_usage_percent=75.0,
            records_per_second=1000.0,
            throughput_mbps=10.0
        )
        
        assert metrics.processing_time_seconds == 1.5
        assert metrics.memory_usage_mb == 256.0
        assert metrics.records_per_second == 1000.0

    def test_database_config_creation(self):
        """Test DatabaseConfig dataclass creation."""
        config = telecom_types.DatabaseConfig(
            host="localhost",
            port=5432,
            name="connect",
            user="connect_user",
            password="secret",
            ssl_mode="require",
            pool_min_size=5,
            pool_max_size=20,
            pool_timeout=30,
            connection_timeout=30,
            command_timeout=60
        )
        
        assert config.host == "localhost"
        assert config.port == 5432
        assert config.pool_min_size == 5

    def test_telecom_config_creation(self):
        """Test TelecomConfig dataclass creation."""
        config = telecom_types.TelecomConfig(
            cdr_batch_size=10000,
            ep_batch_size=5000,
            kpi_calculation_interval=300,
            max_memory_usage_mb=2048,
            processing_timeout_seconds=3600,
            parallel_workers=4
        )
        
        assert config.cdr_batch_size == 10000
        assert config.ep_batch_size == 5000
        assert config.parallel_workers == 4

    def test_data_container_creation(self):
        """Test DataContainer generic class."""
        import pandas as pd
        
        data = pd.DataFrame({'col1': [1, 2, 3]})
        container = telecom_types.DataContainer(data, metadata={"source": "test"})
        
        assert container.get_size() == 3
        assert not container.is_empty()
        assert container.metadata["source"] == "test"

    def test_data_container_empty_check(self):
        """Test DataContainer empty check."""
        import pandas as pd
        
        empty_data = pd.DataFrame()
        container = telecom_types.DataContainer(empty_data)
        
        assert container.is_empty()
        assert container.get_size() == 0
