#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 数据库测试数据生成器
基于docs/database/database-framework.md需求的测试数据生成工具

本模块提供：
- 测试数据生成和管理
- 多种数据类型支持
- 性能测试数据集
- 边界条件测试数据
"""

import random
import string
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Any, Optional, Union, Generator
from dataclasses import dataclass, field
from pathlib import Path
import json
import csv
import pandas as pd
import numpy as np
from faker import Faker
from shapely.geometry import Point, Polygon, LineString
from shapely.wkt import dumps as wkt_dumps
import logging


@dataclass
class DataGenerationConfig:
    """数据生成配置"""
    seed: int = 42
    locale: str = 'zh_CN'
    batch_size: int = 1000
    max_memory_mb: int = 512
    output_format: str = 'pandas'  # pandas, dict, json, csv
    include_nulls: bool = True
    null_probability: float = 0.05
    include_duplicates: bool = True
    duplicate_probability: float = 0.02
    include_invalid_data: bool = False
    invalid_data_probability: float = 0.01
    

@dataclass
class TableSchema:
    """表结构定义"""
    name: str
    columns: Dict[str, Dict[str, Any]]
    primary_key: List[str] = field(default_factory=list)
    foreign_keys: Dict[str, Dict[str, str]] = field(default_factory=dict)
    indexes: List[List[str]] = field(default_factory=list)
    constraints: Dict[str, Any] = field(default_factory=dict)
    

class DatabaseTestDataGenerator:
    """数据库测试数据生成器"""
    
    def __init__(self, config: DataGenerationConfig = None):
        self.config = config or DataGenerationConfig()
        self.faker = Faker(self.config.locale)
        self.faker.seed_instance(self.config.seed)
        
        # 设置随机种子
        random.seed(self.config.seed)
        np.random.seed(self.config.seed)
        
        self.logger = logging.getLogger(__name__)
        
        # 预定义的数据模式
        self._setup_telecom_schemas()
        
        # 缓存生成的数据
        self._data_cache: Dict[str, Any] = {}
        
    def _setup_telecom_schemas(self):
        """设置电信行业相关的表结构"""
        self.schemas = {
            'subscribers': TableSchema(
                name='subscribers',
                columns={
                    'subscriber_id': {'type': 'varchar', 'length': 20, 'nullable': False},
                    'msisdn': {'type': 'varchar', 'length': 15, 'nullable': False},
                    'imsi': {'type': 'varchar', 'length': 15, 'nullable': False},
                    'subscriber_type': {'type': 'varchar', 'length': 20, 'nullable': False},
                    'activation_date': {'type': 'datetime', 'nullable': False},
                    'status': {'type': 'varchar', 'length': 10, 'nullable': False},
                    'plan_id': {'type': 'varchar', 'length': 10, 'nullable': True},
                    'location_area_code': {'type': 'varchar', 'length': 10, 'nullable': True},
                    'created_at': {'type': 'datetime', 'nullable': False},
                    'updated_at': {'type': 'datetime', 'nullable': True}
                },
                primary_key=['subscriber_id'],
                indexes=[['msisdn'], ['imsi'], ['status'], ['activation_date']]
            ),
            
            'call_detail_records': TableSchema(
                name='call_detail_records',
                columns={
                    'cdr_id': {'type': 'varchar', 'length': 32, 'nullable': False},
                    'calling_number': {'type': 'varchar', 'length': 15, 'nullable': False},
                    'called_number': {'type': 'varchar', 'length': 15, 'nullable': False},
                    'call_start_time': {'type': 'datetime', 'nullable': False},
                    'call_end_time': {'type': 'datetime', 'nullable': True},
                    'duration_seconds': {'type': 'integer', 'nullable': True},
                    'call_type': {'type': 'varchar', 'length': 20, 'nullable': False},
                    'charging_info': {'type': 'decimal', 'precision': 10, 'scale': 4, 'nullable': True},
                    'cell_id': {'type': 'varchar', 'length': 20, 'nullable': True},
                    'location_lat': {'type': 'decimal', 'precision': 10, 'scale': 7, 'nullable': True},
                    'location_lon': {'type': 'decimal', 'precision': 10, 'scale': 7, 'nullable': True},
                    'network_type': {'type': 'varchar', 'length': 10, 'nullable': True},
                    'roaming_flag': {'type': 'boolean', 'nullable': False, 'default': False}
                },
                primary_key=['cdr_id'],
                indexes=[['calling_number'], ['called_number'], ['call_start_time'], ['cell_id']]
            ),
            
            'network_events': TableSchema(
                name='network_events',
                columns={
                    'event_id': {'type': 'varchar', 'length': 32, 'nullable': False},
                    'event_type': {'type': 'varchar', 'length': 30, 'nullable': False},
                    'event_timestamp': {'type': 'datetime', 'nullable': False},
                    'subscriber_id': {'type': 'varchar', 'length': 20, 'nullable': True},
                    'cell_id': {'type': 'varchar', 'length': 20, 'nullable': True},
                    'event_data': {'type': 'json', 'nullable': True},
                    'severity_level': {'type': 'varchar', 'length': 10, 'nullable': False},
                    'processed': {'type': 'boolean', 'nullable': False, 'default': False},
                    'location_geometry': {'type': 'geometry', 'nullable': True}
                },
                primary_key=['event_id'],
                indexes=[['event_type'], ['event_timestamp'], ['subscriber_id'], ['severity_level']]
            ),
            
            'base_stations': TableSchema(
                name='base_stations',
                columns={
                    'cell_id': {'type': 'varchar', 'length': 20, 'nullable': False},
                    'cell_name': {'type': 'varchar', 'length': 100, 'nullable': True},
                    'latitude': {'type': 'decimal', 'precision': 10, 'scale': 7, 'nullable': False},
                    'longitude': {'type': 'decimal', 'precision': 10, 'scale': 7, 'nullable': False},
                    'coverage_area': {'type': 'geometry', 'nullable': True},
                    'technology': {'type': 'varchar', 'length': 10, 'nullable': False},
                    'frequency_band': {'type': 'varchar', 'length': 20, 'nullable': True},
                    'capacity': {'type': 'integer', 'nullable': True},
                    'status': {'type': 'varchar', 'length': 10, 'nullable': False},
                    'installation_date': {'type': 'date', 'nullable': True},
                    'last_maintenance': {'type': 'datetime', 'nullable': True}
                },
                primary_key=['cell_id'],
                indexes=[['technology'], ['status'], ['installation_date']]
            )
        }
    
    def generate_subscriber_data(self, count: int = 1000) -> pd.DataFrame:
        """生成用户数据"""
        self.logger.info(f"生成 {count} 条用户数据")
        
        data = []
        subscriber_types = ['prepaid', 'postpaid', 'corporate', 'mvno']
        statuses = ['active', 'inactive', 'suspended', 'terminated']
        
        for i in range(count):
            # 生成基础数据
            subscriber_id = f"SUB{str(i+1).zfill(10)}"
            msisdn = self._generate_msisdn()
            imsi = self._generate_imsi()
            
            # 随机选择类型和状态
            subscriber_type = random.choice(subscriber_types)
            status = random.choice(statuses)
            
            # 生成时间数据
            activation_date = self.faker.date_time_between(start_date='-2y', end_date='now')
            created_at = activation_date - timedelta(days=random.randint(0, 30))
            updated_at = self.faker.date_time_between(start_date=activation_date, end_date='now')
            
            # 生成可选字段
            plan_id = f"PLAN{random.randint(1, 50):03d}" if random.random() > 0.1 else None
            location_area_code = f"LAC{random.randint(1, 999):03d}" if random.random() > 0.05 else None
            
            # 处理空值
            if self.config.include_nulls and random.random() < self.config.null_probability:
                if random.choice([True, False]):
                    plan_id = None
                else:
                    location_area_code = None
            
            data.append({
                'subscriber_id': subscriber_id,
                'msisdn': msisdn,
                'imsi': imsi,
                'subscriber_type': subscriber_type,
                'activation_date': activation_date,
                'status': status,
                'plan_id': plan_id,
                'location_area_code': location_area_code,
                'created_at': created_at,
                'updated_at': updated_at
            })
        
        df = pd.DataFrame(data)
        
        # 添加重复数据
        if self.config.include_duplicates:
            duplicate_count = int(count * self.config.duplicate_probability)
            if duplicate_count > 0:
                duplicate_indices = random.sample(range(len(df)), min(duplicate_count, len(df)))
                duplicates = df.iloc[duplicate_indices].copy()
                # 修改主键以避免约束冲突
                for idx, row in duplicates.iterrows():
                    duplicates.at[idx, 'subscriber_id'] = f"DUP{row['subscriber_id'][3:]}"
                df = pd.concat([df, duplicates], ignore_index=True)
        
        return df
    
    def generate_cdr_data(self, count: int = 10000, subscriber_ids: List[str] = None) -> pd.DataFrame:
        """生成通话详单数据"""
        self.logger.info(f"生成 {count} 条CDR数据")
        
        if subscriber_ids is None:
            # 生成一些默认的用户ID
            subscriber_ids = [f"SUB{str(i+1).zfill(10)}" for i in range(1000)]
        
        data = []
        call_types = ['voice', 'sms', 'data', 'video', 'conference']
        network_types = ['2G', '3G', '4G', '5G']
        
        for i in range(count):
            # 生成CDR ID
            cdr_id = str(uuid.uuid4()).replace('-', '')
            
            # 随机选择主叫和被叫
            calling_number = self._generate_msisdn()
            called_number = self._generate_msisdn()
            
            # 生成通话时间
            call_start_time = self.faker.date_time_between(start_date='-30d', end_date='now')
            
            # 生成通话时长（某些类型可能没有时长）
            call_type = random.choice(call_types)
            if call_type in ['voice', 'video', 'conference']:
                duration_seconds = random.randint(1, 3600)  # 1秒到1小时
                call_end_time = call_start_time + timedelta(seconds=duration_seconds)
            elif call_type == 'sms':
                duration_seconds = 0
                call_end_time = call_start_time
            else:  # data
                duration_seconds = random.randint(60, 7200)  # 1分钟到2小时
                call_end_time = call_start_time + timedelta(seconds=duration_seconds)
            
            # 生成计费信息
            if call_type == 'sms':
                charging_info = Decimal(str(round(random.uniform(0.1, 0.5), 4)))
            elif call_type == 'data':
                charging_info = Decimal(str(round(random.uniform(0.01, 10.0), 4)))
            else:
                charging_info = Decimal(str(round(random.uniform(0.05, 5.0), 4)))
            
            # 生成位置信息
            cell_id = f"CELL{random.randint(1, 10000):05d}"
            location_lat, location_lon = self._generate_coordinates()
            
            # 随机选择网络类型
            network_type = random.choice(network_types)
            
            # 漫游标志
            roaming_flag = random.random() < 0.05  # 5%的漫游概率
            
            # 处理空值
            if self.config.include_nulls and random.random() < self.config.null_probability:
                nullable_fields = ['call_end_time', 'duration_seconds', 'charging_info', 
                                 'cell_id', 'location_lat', 'location_lon', 'network_type']
                field_to_null = random.choice(nullable_fields)
                if field_to_null == 'call_end_time':
                    call_end_time = None
                elif field_to_null == 'duration_seconds':
                    duration_seconds = None
                elif field_to_null == 'charging_info':
                    charging_info = None
                elif field_to_null == 'cell_id':
                    cell_id = None
                elif field_to_null == 'location_lat':
                    location_lat = None
                elif field_to_null == 'location_lon':
                    location_lon = None
                elif field_to_null == 'network_type':
                    network_type = None
            
            data.append({
                'cdr_id': cdr_id,
                'calling_number': calling_number,
                'called_number': called_number,
                'call_start_time': call_start_time,
                'call_end_time': call_end_time,
                'duration_seconds': duration_seconds,
                'call_type': call_type,
                'charging_info': charging_info,
                'cell_id': cell_id,
                'location_lat': location_lat,
                'location_lon': location_lon,
                'network_type': network_type,
                'roaming_flag': roaming_flag
            })
        
        return pd.DataFrame(data)
    
    def generate_network_events_data(self, count: int = 5000) -> pd.DataFrame:
        """生成网络事件数据"""
        self.logger.info(f"生成 {count} 条网络事件数据")
        
        data = []
        event_types = [
            'handover', 'call_setup_failure', 'call_drop', 'registration',
            'location_update', 'sms_failure', 'data_session_start', 'data_session_end',
            'network_congestion', 'equipment_alarm', 'security_event'
        ]
        severity_levels = ['low', 'medium', 'high', 'critical']
        
        for i in range(count):
            # 生成事件ID
            event_id = str(uuid.uuid4()).replace('-', '')
            
            # 随机选择事件类型和严重程度
            event_type = random.choice(event_types)
            severity_level = random.choice(severity_levels)
            
            # 生成时间戳
            event_timestamp = self.faker.date_time_between(start_date='-7d', end_date='now')
            
            # 生成可选字段
            subscriber_id = f"SUB{random.randint(1, 1000):010d}" if random.random() > 0.3 else None
            cell_id = f"CELL{random.randint(1, 10000):05d}" if random.random() > 0.2 else None
            
            # 生成事件数据（JSON格式）
            event_data = self._generate_event_data(event_type)
            
            # 生成几何位置
            location_geometry = None
            if random.random() > 0.4:  # 60%的概率有位置信息
                lat, lon = self._generate_coordinates()
                location_geometry = wkt_dumps(Point(lon, lat))
            
            # 处理状态
            processed = random.random() < 0.7  # 70%已处理
            
            data.append({
                'event_id': event_id,
                'event_type': event_type,
                'event_timestamp': event_timestamp,
                'subscriber_id': subscriber_id,
                'cell_id': cell_id,
                'event_data': json.dumps(event_data) if event_data else None,
                'severity_level': severity_level,
                'processed': processed,
                'location_geometry': location_geometry
            })
        
        return pd.DataFrame(data)
    
    def generate_base_stations_data(self, count: int = 1000) -> pd.DataFrame:
        """生成基站数据"""
        self.logger.info(f"生成 {count} 条基站数据")
        
        data = []
        technologies = ['2G', '3G', '4G', '5G']
        frequency_bands = ['900MHz', '1800MHz', '2100MHz', '2600MHz', '3500MHz']
        statuses = ['active', 'inactive', 'maintenance', 'planned']
        
        for i in range(count):
            # 生成基站ID
            cell_id = f"CELL{str(i+1).zfill(5)}"
            
            # 生成基站名称
            cell_name = f"{self.faker.city()}_BS_{i+1}"
            
            # 生成坐标
            latitude, longitude = self._generate_coordinates()
            
            # 生成覆盖区域（圆形区域）
            coverage_radius = random.uniform(0.5, 5.0)  # 0.5到5公里半径
            coverage_area = self._generate_coverage_area(latitude, longitude, coverage_radius)
            
            # 随机选择技术和频段
            technology = random.choice(technologies)
            frequency_band = random.choice(frequency_bands)
            
            # 生成容量（根据技术类型）
            if technology == '5G':
                capacity = random.randint(1000, 10000)
            elif technology == '4G':
                capacity = random.randint(500, 5000)
            elif technology == '3G':
                capacity = random.randint(100, 1000)
            else:  # 2G
                capacity = random.randint(50, 500)
            
            # 随机选择状态
            status = random.choice(statuses)
            
            # 生成安装日期
            installation_date = self.faker.date_between(start_date='-10y', end_date='-1y')
            
            # 生成最后维护时间
            last_maintenance = None
            if status in ['active', 'maintenance']:
                last_maintenance = self.faker.date_time_between(
                    start_date=installation_date, 
                    end_date='now'
                )
            
            data.append({
                'cell_id': cell_id,
                'cell_name': cell_name,
                'latitude': Decimal(str(round(latitude, 7))),
                'longitude': Decimal(str(round(longitude, 7))),
                'coverage_area': coverage_area,
                'technology': technology,
                'frequency_band': frequency_band,
                'capacity': capacity,
                'status': status,
                'installation_date': installation_date,
                'last_maintenance': last_maintenance
            })
        
        return pd.DataFrame(data)
    
    def generate_performance_test_data(self, table_name: str, size: str = 'medium') -> pd.DataFrame:
        """生成性能测试数据"""
        size_configs = {
            'small': 1000,
            'medium': 10000,
            'large': 100000,
            'xlarge': 1000000
        }
        
        count = size_configs.get(size, 10000)
        self.logger.info(f"生成 {size} 规模的 {table_name} 性能测试数据 ({count} 条记录)")
        
        if table_name == 'subscribers':
            return self.generate_subscriber_data(count)
        elif table_name == 'call_detail_records':
            return self.generate_cdr_data(count)
        elif table_name == 'network_events':
            return self.generate_network_events_data(count)
        elif table_name == 'base_stations':
            return self.generate_base_stations_data(count)
        else:
            raise ValueError(f"不支持的表名: {table_name}")
    
    def generate_boundary_test_data(self, table_name: str) -> Dict[str, pd.DataFrame]:
        """生成边界条件测试数据"""
        self.logger.info(f"生成 {table_name} 边界条件测试数据")
        
        boundary_data = {}
        
        if table_name == 'subscribers':
            # 空数据集
            boundary_data['empty'] = pd.DataFrame(columns=[
                'subscriber_id', 'msisdn', 'imsi', 'subscriber_type', 
                'activation_date', 'status', 'plan_id', 'location_area_code',
                'created_at', 'updated_at'
            ])
            
            # 最小数据集
            boundary_data['minimal'] = self.generate_subscriber_data(1)
            
            # 最大长度字段
            max_data = self.generate_subscriber_data(1)
            max_data.at[0, 'subscriber_id'] = 'A' * 20  # 最大长度
            max_data.at[0, 'msisdn'] = '1' * 15
            max_data.at[0, 'imsi'] = '2' * 15
            boundary_data['max_length'] = max_data
            
            # 特殊字符
            special_data = self.generate_subscriber_data(1)
            special_data.at[0, 'subscriber_id'] = 'SUB_TEST_@#$%^&*()'
            boundary_data['special_chars'] = special_data
        
        elif table_name == 'call_detail_records':
            # 空数据集
            boundary_data['empty'] = pd.DataFrame(columns=[
                'cdr_id', 'calling_number', 'called_number', 'call_start_time',
                'call_end_time', 'duration_seconds', 'call_type', 'charging_info',
                'cell_id', 'location_lat', 'location_lon', 'network_type', 'roaming_flag'
            ])
            
            # 最小数据集
            boundary_data['minimal'] = self.generate_cdr_data(1)
            
            # 极端时长
            extreme_data = self.generate_cdr_data(3)
            extreme_data.at[0, 'duration_seconds'] = 0  # 最短
            extreme_data.at[1, 'duration_seconds'] = 86400  # 24小时
            extreme_data.at[2, 'duration_seconds'] = None  # 空值
            boundary_data['extreme_duration'] = extreme_data
            
            # 极端坐标
            coord_data = self.generate_cdr_data(4)
            coord_data.at[0, 'location_lat'] = Decimal('-90.0000000')  # 南极
            coord_data.at[0, 'location_lon'] = Decimal('-180.0000000')  # 最西
            coord_data.at[1, 'location_lat'] = Decimal('90.0000000')   # 北极
            coord_data.at[1, 'location_lon'] = Decimal('180.0000000')  # 最东
            coord_data.at[2, 'location_lat'] = Decimal('0.0000000')    # 赤道
            coord_data.at[2, 'location_lon'] = Decimal('0.0000000')    # 本初子午线
            coord_data.at[3, 'location_lat'] = None  # 空值
            coord_data.at[3, 'location_lon'] = None
            boundary_data['extreme_coordinates'] = coord_data
        
        return boundary_data
    
    def generate_invalid_test_data(self, table_name: str) -> pd.DataFrame:
        """生成无效数据用于负面测试"""
        if not self.config.include_invalid_data:
            return pd.DataFrame()
        
        self.logger.info(f"生成 {table_name} 无效测试数据")
        
        if table_name == 'subscribers':
            invalid_data = []
            
            # 重复主键
            invalid_data.append({
                'subscriber_id': 'SUB0000000001',
                'msisdn': '1234567890',
                'imsi': '123456789012345',
                'subscriber_type': 'prepaid',
                'activation_date': datetime.now(),
                'status': 'active',
                'plan_id': None,
                'location_area_code': None,
                'created_at': datetime.now(),
                'updated_at': None
            })
            
            # 重复主键（第二条）
            invalid_data.append({
                'subscriber_id': 'SUB0000000001',  # 重复
                'msisdn': '0987654321',
                'imsi': '543210987654321',
                'subscriber_type': 'postpaid',
                'activation_date': datetime.now(),
                'status': 'active',
                'plan_id': None,
                'location_area_code': None,
                'created_at': datetime.now(),
                'updated_at': None
            })
            
            # 超长字段
            invalid_data.append({
                'subscriber_id': 'A' * 25,  # 超过20字符限制
                'msisdn': '1' * 20,  # 超过15字符限制
                'imsi': '2' * 20,    # 超过15字符限制
                'subscriber_type': 'invalid_type_that_is_too_long',
                'activation_date': datetime.now(),
                'status': 'invalid_status',
                'plan_id': None,
                'location_area_code': None,
                'created_at': datetime.now(),
                'updated_at': None
            })
            
            # 空的必填字段
            invalid_data.append({
                'subscriber_id': None,  # 必填字段为空
                'msisdn': None,         # 必填字段为空
                'imsi': '123456789012345',
                'subscriber_type': 'prepaid',
                'activation_date': datetime.now(),
                'status': 'active',
                'plan_id': None,
                'location_area_code': None,
                'created_at': datetime.now(),
                'updated_at': None
            })
            
            return pd.DataFrame(invalid_data)
        
        return pd.DataFrame()
    
    def _generate_msisdn(self) -> str:
        """生成MSISDN（手机号码）"""
        # 中国手机号码格式
        prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                   '150', '151', '152', '153', '155', '156', '157', '158', '159',
                   '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
        prefix = random.choice(prefixes)
        suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
        return f"{prefix}{suffix}"
    
    def _generate_imsi(self) -> str:
        """生成IMSI"""
        # 中国移动、联通、电信的MCC+MNC
        mcc_mnc_list = ['46000', '46001', '46002', '46003', '46007', '46008']
        mcc_mnc = random.choice(mcc_mnc_list)
        msin = ''.join([str(random.randint(0, 9)) for _ in range(10)])
        return f"{mcc_mnc}{msin}"
    
    def _generate_coordinates(self) -> tuple:
        """生成中国境内的坐标"""
        # 中国大陆坐标范围（大致）
        lat_min, lat_max = 18.0, 54.0
        lon_min, lon_max = 73.0, 135.0
        
        latitude = random.uniform(lat_min, lat_max)
        longitude = random.uniform(lon_min, lon_max)
        
        return latitude, longitude
    
    def _generate_coverage_area(self, center_lat: float, center_lon: float, radius_km: float) -> str:
        """生成基站覆盖区域（圆形）"""
        # 将公里转换为度（大致）
        radius_deg = radius_km / 111.0  # 1度约等于111公里
        
        # 生成圆形的多边形近似
        angles = np.linspace(0, 2 * np.pi, 36)  # 36个点形成圆
        points = []
        
        for angle in angles:
            lat = center_lat + radius_deg * np.cos(angle)
            lon = center_lon + radius_deg * np.sin(angle)
            points.append((lon, lat))  # 注意：Shapely使用(x, y)即(lon, lat)格式
        
        polygon = Polygon(points)
        return wkt_dumps(polygon)
    
    def _generate_event_data(self, event_type: str) -> Dict[str, Any]:
        """根据事件类型生成相应的事件数据"""
        base_data = {
            'timestamp': datetime.now().isoformat(),
            'source': 'network_monitor',
            'version': '1.0'
        }
        
        if event_type == 'handover':
            base_data.update({
                'source_cell': f"CELL{random.randint(1, 10000):05d}",
                'target_cell': f"CELL{random.randint(1, 10000):05d}",
                'handover_type': random.choice(['intra_freq', 'inter_freq', 'inter_rat']),
                'success': random.choice([True, False]),
                'duration_ms': random.randint(50, 500)
            })
        
        elif event_type == 'call_setup_failure':
            base_data.update({
                'failure_cause': random.choice(['network_busy', 'user_busy', 'no_answer', 'rejected']),
                'attempt_count': random.randint(1, 5),
                'call_type': random.choice(['voice', 'video', 'data'])
            })
        
        elif event_type == 'network_congestion':
            base_data.update({
                'congestion_level': random.choice(['low', 'medium', 'high']),
                'affected_services': random.sample(['voice', 'sms', 'data', 'video'], random.randint(1, 4)),
                'utilization_percent': random.randint(70, 100)
            })
        
        elif event_type == 'security_event':
            base_data.update({
                'threat_type': random.choice(['unauthorized_access', 'dos_attack', 'malware', 'fraud']),
                'risk_level': random.choice(['low', 'medium', 'high', 'critical']),
                'blocked': random.choice([True, False])
            })
        
        return base_data
    
    def save_data(self, data: pd.DataFrame, filename: str, format_type: str = None) -> Path:
        """保存数据到文件"""
        if format_type is None:
            format_type = self.config.output_format
        
        output_path = Path(filename)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        if format_type.lower() == 'csv':
            data.to_csv(output_path, index=False, encoding='utf-8')
        elif format_type.lower() == 'json':
            data.to_json(output_path, orient='records', date_format='iso', force_ascii=False, indent=2)
        elif format_type.lower() == 'parquet':
            data.to_parquet(output_path, index=False)
        elif format_type.lower() == 'excel':
            data.to_excel(output_path, index=False)
        else:
            raise ValueError(f"不支持的格式: {format_type}")
        
        self.logger.info(f"数据已保存到: {output_path}")
        return output_path
    
    def generate_complete_dataset(self, output_dir: str = './test_data') -> Dict[str, Path]:
        """生成完整的测试数据集"""
        self.logger.info("开始生成完整的测试数据集")
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        generated_files = {}
        
        # 生成各表的标准数据
        tables = ['subscribers', 'call_detail_records', 'network_events', 'base_stations']
        
        for table in tables:
            self.logger.info(f"生成 {table} 数据")
            
            if table == 'subscribers':
                data = self.generate_subscriber_data(1000)
            elif table == 'call_detail_records':
                data = self.generate_cdr_data(5000)
            elif table == 'network_events':
                data = self.generate_network_events_data(2000)
            elif table == 'base_stations':
                data = self.generate_base_stations_data(500)
            
            # 保存为多种格式
            for fmt in ['csv', 'json']:
                filename = output_path / f"{table}.{fmt}"
                file_path = self.save_data(data, filename, fmt)
                generated_files[f"{table}_{fmt}"] = file_path
        
        # 生成性能测试数据
        for size in ['small', 'medium', 'large']:
            for table in ['subscribers', 'call_detail_records']:
                self.logger.info(f"生成 {table} {size} 性能测试数据")
                data = self.generate_performance_test_data(table, size)
                filename = output_path / 'performance' / f"{table}_{size}.csv"
                file_path = self.save_data(data, filename, 'csv')
                generated_files[f"{table}_{size}_perf"] = file_path
        
        # 生成边界条件测试数据
        for table in ['subscribers', 'call_detail_records']:
            self.logger.info(f"生成 {table} 边界条件测试数据")
            boundary_data = self.generate_boundary_test_data(table)
            
            for condition, data in boundary_data.items():
                filename = output_path / 'boundary' / f"{table}_{condition}.csv"
                file_path = self.save_data(data, filename, 'csv')
                generated_files[f"{table}_{condition}_boundary"] = file_path
        
        # 生成无效数据
        for table in ['subscribers']:
            self.logger.info(f"生成 {table} 无效测试数据")
            invalid_data = self.generate_invalid_test_data(table)
            if not invalid_data.empty:
                filename = output_path / 'invalid' / f"{table}_invalid.csv"
                file_path = self.save_data(invalid_data, filename, 'csv')
                generated_files[f"{table}_invalid"] = file_path
        
        self.logger.info(f"完整测试数据集生成完成，共 {len(generated_files)} 个文件")
        return generated_files
    
    def get_schema(self, table_name: str) -> TableSchema:
        """获取表结构定义"""
        return self.schemas.get(table_name)
    
    def list_available_tables(self) -> List[str]:
        """列出可用的表名"""
        return list(self.schemas.keys())
    
    def clear_cache(self):
        """清理数据缓存"""
        self._data_cache.clear()
        self.logger.info("数据缓存已清理")


def create_test_data_generator(config: Dict[str, Any] = None) -> DatabaseTestDataGenerator:
    """创建测试数据生成器的工厂函数"""
    if config:
        data_config = DataGenerationConfig(**config)
    else:
        data_config = DataGenerationConfig()
    
    return DatabaseTestDataGenerator(data_config)


if __name__ == "__main__":
    # 示例用法
    import argparse
    
    parser = argparse.ArgumentParser(description="数据库测试数据生成器")
    parser.add_argument('--table', choices=['subscribers', 'call_detail_records', 'network_events', 'base_stations', 'all'], 
                       default='all', help='要生成的表数据')
    parser.add_argument('--count', type=int, default=1000, help='生成的记录数')
    parser.add_argument('--output', default='./test_data', help='输出目录')
    parser.add_argument('--format', choices=['csv', 'json', 'parquet'], default='csv', help='输出格式')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建生成器
    config = DataGenerationConfig(
        seed=args.seed,
        output_format=args.format
    )
    generator = DatabaseTestDataGenerator(config)
    
    if args.table == 'all':
        # 生成完整数据集
        generated_files = generator.generate_complete_dataset(args.output)
        print(f"\n生成完成！共生成 {len(generated_files)} 个文件:")
        for name, path in generated_files.items():
            print(f"  {name}: {path}")
    else:
        # 生成指定表的数据
        if args.table == 'subscribers':
            data = generator.generate_subscriber_data(args.count)
        elif args.table == 'call_detail_records':
            data = generator.generate_cdr_data(args.count)
        elif args.table == 'network_events':
            data = generator.generate_network_events_data(args.count)
        elif args.table == 'base_stations':
            data = generator.generate_base_stations_data(args.count)
        
        # 保存数据
        output_file = Path(args.output) / f"{args.table}.{args.format}"
        file_path = generator.save_data(data, output_file, args.format)
        print(f"\n数据已生成: {file_path}")
        print(f"记录数: {len(data)}")