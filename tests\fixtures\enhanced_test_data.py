"""Enhanced test data generator for comprehensive testing."""

import json
import random
import string
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import pandas as pd
from faker import Faker
import numpy as np
from shapely.geometry import Point, Polygon
from shapely.ops import unary_union


# Initialize Faker with multiple locales
fake = Faker(['en_US', 'zh_CN'])
Faker.seed(12345)  # Ensure reproducible test data


@dataclass
class DatabaseTestData:
    """Database-specific test data structure."""
    table_name: str
    schema_name: str
    data: List[Dict[str, Any]]
    constraints: Dict[str, Any]
    indexes: List[str]
    metadata: Dict[str, Any]


@dataclass
class PerformanceTestData:
    """Performance test data structure."""
    dataset_size: int
    complexity_level: str
    data_type: str
    generation_time: float
    memory_usage: float
    data: Any


@dataclass
class GeospatialTestData:
    """Geospatial test data structure."""
    geometry_type: str
    coordinates: List[Tuple[float, float]]
    properties: Dict[str, Any]
    srid: int
    bbox: Tuple[float, float, float, float]


class EnhancedTestDataGenerator:
    """Enhanced test data generator with comprehensive capabilities."""
    
    def __init__(self, seed: int = 12345):
        """Initialize the enhanced test data generator.
        
        Args:
            seed: Random seed for reproducible data generation
        """
        self.seed = seed
        random.seed(seed)
        np.random.seed(seed)
        Faker.seed(seed)
        
        # Data type mappings
        self.sql_types = {
            'integer': int,
            'bigint': int,
            'varchar': str,
            'text': str,
            'decimal': float,
            'float': float,
            'boolean': bool,
            'timestamp': datetime,
            'date': datetime,
            'json': dict,
            'uuid': str
        }
        
        # Common test scenarios
        self.test_scenarios = {
            'small': {'rows': 100, 'complexity': 'low'},
            'medium': {'rows': 1000, 'complexity': 'medium'},
            'large': {'rows': 10000, 'complexity': 'high'},
            'xlarge': {'rows': 100000, 'complexity': 'extreme'}
        }
        
    def generate_database_schema_data(self, 
                                     schema_name: str,
                                     table_configs: List[Dict[str, Any]]) -> List[DatabaseTestData]:
        """Generate test data for database schema.
        
        Args:
            schema_name: Name of the database schema
            table_configs: List of table configuration dictionaries
            
        Returns:
            List of DatabaseTestData objects
        """
        schema_data = []
        
        for config in table_configs:
            table_name = config['name']
            columns = config['columns']
            row_count = config.get('row_count', 100)
            
            # Generate table data
            table_data = []
            for _ in range(row_count):
                row = {}
                for col_name, col_config in columns.items():
                    row[col_name] = self._generate_column_value(col_config)
                table_data.append(row)
                
            # Create database test data object
            db_data = DatabaseTestData(
                table_name=table_name,
                schema_name=schema_name,
                data=table_data,
                constraints=config.get('constraints', {}),
                indexes=config.get('indexes', []),
                metadata={
                    'generated_at': datetime.now(),
                    'row_count': row_count,
                    'generator_version': '1.0'
                }
            )
            schema_data.append(db_data)
            
        return schema_data
        
    def _generate_column_value(self, col_config: Dict[str, Any]) -> Any:
        """Generate value for a specific column.
        
        Args:
            col_config: Column configuration dictionary
            
        Returns:
            Generated value for the column
        """
        col_type = col_config['type']
        nullable = col_config.get('nullable', True)
        
        # Handle null values
        if nullable and random.random() < 0.05:  # 5% chance of null
            return None
            
        # Generate value based on type
        if col_type == 'integer':
            min_val = col_config.get('min', 1)
            max_val = col_config.get('max', 1000000)
            return random.randint(min_val, max_val)
            
        elif col_type == 'bigint':
            min_val = col_config.get('min', 1)
            max_val = col_config.get('max', 9223372036854775807)
            return random.randint(min_val, max_val)
            
        elif col_type == 'varchar':
            max_length = col_config.get('max_length', 255)
            if 'pattern' in col_config:
                return self._generate_pattern_string(col_config['pattern'])
            return fake.text(max_nb_chars=max_length)
            
        elif col_type == 'text':
            return fake.text(max_nb_chars=col_config.get('max_length', 1000))
            
        elif col_type in ['decimal', 'float']:
            min_val = col_config.get('min', 0.0)
            max_val = col_config.get('max', 1000.0)
            precision = col_config.get('precision', 2)
            return round(random.uniform(min_val, max_val), precision)
            
        elif col_type == 'boolean':
            return random.choice([True, False])
            
        elif col_type == 'timestamp':
            start_date = col_config.get('start_date', datetime.now() - timedelta(days=365))
            end_date = col_config.get('end_date', datetime.now())
            return fake.date_time_between(start_date=start_date, end_date=end_date)
            
        elif col_type == 'date':
            start_date = col_config.get('start_date', datetime.now() - timedelta(days=365))
            end_date = col_config.get('end_date', datetime.now())
            return fake.date_between(start_date=start_date, end_date=end_date)
            
        elif col_type == 'json':
            return self._generate_json_data(col_config.get('schema', {}))
            
        elif col_type == 'uuid':
            return str(uuid.uuid4())
            
        else:
            return fake.text(max_nb_chars=50)
            
    def _generate_pattern_string(self, pattern: str) -> str:
        """Generate string based on pattern.
        
        Args:
            pattern: Pattern string (e.g., 'email', 'phone', 'name')
            
        Returns:
            Generated string matching the pattern
        """
        patterns = {
            'email': fake.email,
            'phone': fake.phone_number,
            'name': fake.name,
            'company': fake.company,
            'address': fake.address,
            'city': fake.city,
            'country': fake.country,
            'url': fake.url,
            'ipv4': fake.ipv4,
            'ipv6': fake.ipv6,
            'mac_address': fake.mac_address,
            'credit_card': fake.credit_card_number,
            'ssn': fake.ssn
        }
        
        generator = patterns.get(pattern, fake.text)
        return generator()
        
    def _generate_json_data(self, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Generate JSON data based on schema.
        
        Args:
            schema: JSON schema definition
            
        Returns:
            Generated JSON data
        """
        if not schema:
            return {
                'id': random.randint(1, 1000),
                'name': fake.name(),
                'value': random.uniform(0, 100),
                'active': random.choice([True, False])
            }
            
        json_data = {}
        for key, config in schema.items():
            if isinstance(config, dict) and 'type' in config:
                json_data[key] = self._generate_column_value(config)
            else:
                json_data[key] = config
                
        return json_data
        
    def generate_performance_test_data(self, 
                                      scenario: str,
                                      data_type: str = 'mixed') -> PerformanceTestData:
        """Generate performance test data.
        
        Args:
            scenario: Test scenario ('small', 'medium', 'large', 'xlarge')
            data_type: Type of data to generate
            
        Returns:
            PerformanceTestData object
        """
        start_time = datetime.now()
        
        config = self.test_scenarios.get(scenario, self.test_scenarios['medium'])
        row_count = config['rows']
        complexity = config['complexity']
        
        if data_type == 'numeric':
            data = self._generate_numeric_data(row_count, complexity)
        elif data_type == 'text':
            data = self._generate_text_data(row_count, complexity)
        elif data_type == 'geospatial':
            data = self._generate_geospatial_data(row_count, complexity)
        else:  # mixed
            data = self._generate_mixed_data(row_count, complexity)
            
        generation_time = (datetime.now() - start_time).total_seconds()
        
        return PerformanceTestData(
            dataset_size=row_count,
            complexity_level=complexity,
            data_type=data_type,
            generation_time=generation_time,
            memory_usage=self._estimate_memory_usage(data),
            data=data
        )
        
    def _generate_numeric_data(self, row_count: int, complexity: str) -> List[Dict[str, Any]]:
        """Generate numeric test data."""
        data = []
        
        for i in range(row_count):
            row = {
                'id': i + 1,
                'value1': random.uniform(0, 1000),
                'value2': random.randint(1, 1000000),
                'value3': random.gauss(100, 15),
                'timestamp': fake.date_time_this_year()
            }
            
            if complexity in ['medium', 'high', 'extreme']:
                row.update({
                    'value4': random.exponential(2),
                    'value5': random.lognormvariate(0, 1),
                    'calculated': row['value1'] * row['value2'] / 100
                })
                
            if complexity in ['high', 'extreme']:
                row.update({
                    'matrix_data': np.random.rand(10, 10).tolist(),
                    'series_data': np.random.rand(100).tolist()
                })
                
            data.append(row)
            
        return data
        
    def _generate_text_data(self, row_count: int, complexity: str) -> List[Dict[str, Any]]:
        """Generate text test data."""
        data = []
        
        for i in range(row_count):
            row = {
                'id': i + 1,
                'title': fake.sentence(),
                'description': fake.text(max_nb_chars=200),
                'category': fake.word(),
                'tags': [fake.word() for _ in range(random.randint(1, 5))]
            }
            
            if complexity in ['medium', 'high', 'extreme']:
                row.update({
                    'content': fake.text(max_nb_chars=1000),
                    'metadata': {
                        'author': fake.name(),
                        'created': fake.date_time_this_year().isoformat(),
                        'language': random.choice(['en', 'zh', 'es', 'fr'])
                    }
                })
                
            if complexity in ['high', 'extreme']:
                row.update({
                    'full_text': fake.text(max_nb_chars=5000),
                    'keywords': [fake.word() for _ in range(random.randint(10, 50))],
                    'nested_data': {
                        'level1': {
                            'level2': {
                                'level3': fake.text(max_nb_chars=100)
                            }
                        }
                    }
                })
                
            data.append(row)
            
        return data
        
    def _generate_geospatial_data(self, row_count: int, complexity: str) -> List[GeospatialTestData]:
        """Generate geospatial test data."""
        data = []
        
        # Define bounding box for test area (e.g., around Beijing)
        min_lat, max_lat = 39.4, 40.4
        min_lon, max_lon = 115.7, 117.4
        
        for i in range(row_count):
            lat = random.uniform(min_lat, max_lat)
            lon = random.uniform(min_lon, max_lon)
            
            if complexity == 'low':
                geometry_type = 'Point'
                coordinates = [(lon, lat)]
            elif complexity == 'medium':
                geometry_type = random.choice(['Point', 'LineString'])
                if geometry_type == 'Point':
                    coordinates = [(lon, lat)]
                else:
                    # Generate line with 3-5 points
                    num_points = random.randint(3, 5)
                    coordinates = [
                        (lon + random.uniform(-0.01, 0.01), lat + random.uniform(-0.01, 0.01))
                        for _ in range(num_points)
                    ]
            else:  # high or extreme
                geometry_type = random.choice(['Point', 'LineString', 'Polygon'])
                if geometry_type == 'Point':
                    coordinates = [(lon, lat)]
                elif geometry_type == 'LineString':
                    num_points = random.randint(5, 20)
                    coordinates = [
                        (lon + random.uniform(-0.05, 0.05), lat + random.uniform(-0.05, 0.05))
                        for _ in range(num_points)
                    ]
                else:  # Polygon
                    # Generate simple polygon
                    center_lon, center_lat = lon, lat
                    radius = random.uniform(0.001, 0.01)
                    num_points = random.randint(4, 10)
                    coordinates = []
                    for j in range(num_points):
                        angle = (2 * np.pi * j) / num_points
                        point_lon = center_lon + radius * np.cos(angle)
                        point_lat = center_lat + radius * np.sin(angle)
                        coordinates.append((point_lon, point_lat))
                    coordinates.append(coordinates[0])  # Close the polygon
                    
            # Calculate bounding box
            lons = [coord[0] for coord in coordinates]
            lats = [coord[1] for coord in coordinates]
            bbox = (min(lons), min(lats), max(lons), max(lats))
            
            geo_data = GeospatialTestData(
                geometry_type=geometry_type,
                coordinates=coordinates,
                properties={
                    'id': i + 1,
                    'name': fake.city(),
                    'type': random.choice(['residential', 'commercial', 'industrial']),
                    'population': random.randint(1000, 100000) if geometry_type == 'Polygon' else None,
                    'elevation': random.uniform(0, 1000)
                },
                srid=4326,  # WGS84
                bbox=bbox
            )
            
            data.append(geo_data)
            
        return data
        
    def _generate_mixed_data(self, row_count: int, complexity: str) -> List[Dict[str, Any]]:
        """Generate mixed type test data."""
        data = []
        
        for i in range(row_count):
            row = {
                'id': i + 1,
                'name': fake.name(),
                'email': fake.email(),
                'age': random.randint(18, 80),
                'salary': random.uniform(30000, 200000),
                'is_active': random.choice([True, False]),
                'created_at': fake.date_time_this_year(),
                'location': {
                    'lat': random.uniform(39.4, 40.4),
                    'lon': random.uniform(115.7, 117.4)
                }
            }
            
            if complexity in ['medium', 'high', 'extreme']:
                row.update({
                    'department': fake.job(),
                    'skills': [fake.word() for _ in range(random.randint(3, 8))],
                    'performance_scores': [random.uniform(0, 100) for _ in range(12)],
                    'metadata': {
                        'last_login': fake.date_time_this_month().isoformat(),
                        'login_count': random.randint(1, 1000),
                        'preferences': {
                            'theme': random.choice(['light', 'dark']),
                            'language': random.choice(['en', 'zh', 'es']),
                            'notifications': random.choice([True, False])
                        }
                    }
                })
                
            if complexity in ['high', 'extreme']:
                row.update({
                    'projects': [
                        {
                            'id': j + 1,
                            'name': fake.catch_phrase(),
                            'status': random.choice(['active', 'completed', 'on_hold']),
                            'budget': random.uniform(10000, 500000),
                            'team_size': random.randint(3, 15)
                        }
                        for j in range(random.randint(1, 5))
                    ],
                    'time_series': {
                        'dates': [fake.date_this_year().isoformat() for _ in range(30)],
                        'values': [random.uniform(0, 100) for _ in range(30)]
                    }
                })
                
            data.append(row)
            
        return data
        
    def _estimate_memory_usage(self, data: Any) -> float:
        """Estimate memory usage of data in MB."""
        try:
            import sys
            if isinstance(data, list):
                return sum(sys.getsizeof(item) for item in data) / (1024 * 1024)
            else:
                return sys.getsizeof(data) / (1024 * 1024)
        except:
            return 0.0
            
    def generate_invalid_data(self, data_type: str, count: int = 100) -> List[Dict[str, Any]]:
        """Generate invalid test data for negative testing.
        
        Args:
            data_type: Type of invalid data to generate
            count: Number of invalid records to generate
            
        Returns:
            List of invalid data records
        """
        invalid_data = []
        
        for i in range(count):
            if data_type == 'sql_injection':
                invalid_data.append({
                    'id': i + 1,
                    'malicious_input': random.choice([
                        "'; DROP TABLE users; --",
                        "1' OR '1'='1",
                        "admin'--",
                        "' UNION SELECT * FROM passwords--",
                        "1; DELETE FROM logs; --"
                    ])
                })
            elif data_type == 'xss':
                invalid_data.append({
                    'id': i + 1,
                    'malicious_script': random.choice([
                        "<script>alert('XSS')</script>",
                        "javascript:alert('XSS')",
                        "<img src=x onerror=alert('XSS')>",
                        "<svg onload=alert('XSS')>",
                        "<iframe src=javascript:alert('XSS')></iframe>"
                    ])
                })
            elif data_type == 'buffer_overflow':
                invalid_data.append({
                    'id': i + 1,
                    'oversized_data': 'A' * random.randint(10000, 100000)
                })
            elif data_type == 'null_bytes':
                invalid_data.append({
                    'id': i + 1,
                    'null_byte_data': f"test\x00data{i}"
                })
            elif data_type == 'unicode_issues':
                invalid_data.append({
                    'id': i + 1,
                    'unicode_data': random.choice([
                        "\u202e\u202d",  # Right-to-left override
                        "\ufeff",       # Byte order mark
                        "\u200b",       # Zero width space
                        "\ud800",       # High surrogate
                        "\udc00"        # Low surrogate
                    ])
                })
            else:
                # Generic invalid data
                invalid_data.append({
                    'id': i + 1,
                    'invalid_field': None,
                    'negative_number': -random.randint(1, 1000),
                    'empty_string': '',
                    'special_chars': ''.join(random.choices('!@#$%^&*()[]{}|\\:;"\'<>?,./`~', k=20))
                })
                
        return invalid_data
        
    def export_test_data(self, data: Any, filepath: str, format: str = 'json'):
        """Export test data to file.
        
        Args:
            data: Test data to export
            filepath: Output file path
            format: Export format ('json', 'csv', 'excel')
        """
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        if format == 'json':
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str, ensure_ascii=False)
        elif format == 'csv' and isinstance(data, list):
            df = pd.DataFrame(data)
            df.to_csv(filepath, index=False, encoding='utf-8')
        elif format == 'excel' and isinstance(data, list):
            df = pd.DataFrame(data)
            df.to_excel(filepath, index=False)
        else:
            raise ValueError(f"Unsupported format: {format}")
            
    def cleanup_test_data(self, data_paths: List[str]):
        """Clean up generated test data files.
        
        Args:
            data_paths: List of file paths to clean up
        """
        for path in data_paths:
            try:
                Path(path).unlink(missing_ok=True)
            except Exception as e:
                print(f"Error cleaning up {path}: {e}")


# Global instance for convenience
enhanced_generator = EnhancedTestDataGenerator()


# Convenience functions
def generate_test_schema_data(schema_name: str, table_configs: List[Dict[str, Any]]) -> List[DatabaseTestData]:
    """Convenience function to generate schema test data."""
    return enhanced_generator.generate_database_schema_data(schema_name, table_configs)


def generate_performance_data(scenario: str, data_type: str = 'mixed') -> PerformanceTestData:
    """Convenience function to generate performance test data."""
    return enhanced_generator.generate_performance_test_data(scenario, data_type)


def generate_geospatial_data(row_count: int, complexity: str = 'medium') -> List[GeospatialTestData]:
    """Convenience function to generate geospatial test data."""
    return enhanced_generator._generate_geospatial_data(row_count, complexity)


def generate_invalid_data(data_type: str, count: int = 100) -> List[Dict[str, Any]]:
    """Convenience function to generate invalid test data."""
    return enhanced_generator.generate_invalid_data(data_type, count)