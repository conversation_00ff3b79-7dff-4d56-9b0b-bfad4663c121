"""Geospatial Processing Module for Telecommunications Data

This module provides comprehensive geospatial processing capabilities including
coordinate transformations, geometric operations, and telecommunications-specific
spatial processing functions.
"""

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

import logging
import numpy as np
import pandas as pd
import geopandas as gpd
from typing import Dict, List, Optional, Tuple, Union, Any
from shapely.geometry import Point, Polygon, LineString, MultiPoint
from shapely.ops import transform, unary_union
import pyproj
from functools import partial

logger = logging.getLogger(__name__)


class GeospatialProcessor:
    """Comprehensive geospatial processor for telecommunications data"""
    
    def __init__(self, default_crs: str = "EPSG:4326"):
        """Initialize geospatial processor
        
        Args:
            default_crs: Default coordinate reference system
        """
        self.default_crs = default_crs
        self.logger = logging.getLogger(self.__class__.__name__)
        self._transformers = {}  # Cache for coordinate transformers
        
    def create_points_from_coordinates(self, 
                                     data: pd.DataFrame,
                                     lat_col: str = "latitude",
                                     lon_col: str = "longitude",
                                     crs: str = None) -> gpd.GeoDataFrame:
        """Create GeoDataFrame with Point geometries from coordinate columns
        
        Args:
            data: DataFrame with coordinate columns
            lat_col: Latitude column name
            lon_col: Longitude column name
            crs: Coordinate reference system
            
        Returns:
            GeoDataFrame with Point geometries
        """
        try:
            if data.empty:
                return gpd.GeoDataFrame(columns=list(data.columns) + ['geometry'])
            
            # Validate coordinate columns
            if lat_col not in data.columns or lon_col not in data.columns:
                raise ValueError(f"Missing coordinate columns: {lat_col}, {lon_col}")
            
            # Remove rows with invalid coordinates
            valid_coords = (
                data[lat_col].notna() & 
                data[lon_col].notna() &
                (data[lat_col].between(-90, 90)) &
                (data[lon_col].between(-180, 180))
            )
            
            clean_data = data[valid_coords].copy()
            
            if clean_data.empty:
                self.logger.warning("No valid coordinates found")
                return gpd.GeoDataFrame(columns=list(data.columns) + ['geometry'])
            
            # Create Point geometries
            geometry = [Point(lon, lat) for lon, lat in 
                       zip(clean_data[lon_col], clean_data[lat_col])]
            
            # Create GeoDataFrame
            gdf = gpd.GeoDataFrame(clean_data, geometry=geometry, crs=crs or self.default_crs)
            
            self.logger.info(f"Created {len(gdf)} point geometries from coordinates")
            return gdf
            
        except Exception as e:
            self.logger.error(f"Failed to create points from coordinates: {e}")
            raise
    
    def transform_crs(self, gdf: gpd.GeoDataFrame, target_crs: str) -> gpd.GeoDataFrame:
        """Transform GeoDataFrame to different coordinate reference system
        
        Args:
            gdf: Input GeoDataFrame
            target_crs: Target CRS
            
        Returns:
            Transformed GeoDataFrame
        """
        try:
            if gdf.empty:
                return gdf
            
            if gdf.crs is None:
                self.logger.warning("Input GeoDataFrame has no CRS, assuming WGS84")
                gdf = gdf.set_crs(self.default_crs)
            
            if str(gdf.crs) == target_crs:
                self.logger.debug("GeoDataFrame already in target CRS")
                return gdf
            
            # Transform to target CRS
            transformed_gdf = gdf.to_crs(target_crs)
            
            self.logger.info(f"Transformed CRS from {gdf.crs} to {target_crs}")
            return transformed_gdf
            
        except Exception as e:
            self.logger.error(f"CRS transformation failed: {e}")
            raise
    
    def calculate_distances(self, 
                          gdf: gpd.GeoDataFrame,
                          reference_point: Point = None,
                          reference_gdf: gpd.GeoDataFrame = None,
                          distance_col: str = "distance_m") -> gpd.GeoDataFrame:
        """Calculate distances from geometries to reference point or nearest geometry
        
        Args:
            gdf: Input GeoDataFrame
            reference_point: Single reference point
            reference_gdf: Reference GeoDataFrame for nearest distance calculation
            distance_col: Name for distance column
            
        Returns:
            GeoDataFrame with distance column added
        """
        try:
            if gdf.empty:
                return gdf
            
            result_gdf = gdf.copy()
            
            # Transform to projected CRS for accurate distance calculation
            if gdf.crs and 'EPSG:4326' in str(gdf.crs):
                # Use appropriate UTM zone for the data
                bounds = gdf.total_bounds
                center_lon = (bounds[0] + bounds[2]) / 2
                utm_zone = int((center_lon + 180) / 6) + 1
                utm_crs = f"EPSG:{32600 + utm_zone}"  # Northern hemisphere
                
                projected_gdf = self.transform_crs(gdf, utm_crs)
            else:
                projected_gdf = gdf
            
            if reference_point:
                # Calculate distance to single reference point
                if gdf.crs and 'EPSG:4326' in str(gdf.crs):
                    # Transform reference point to same CRS
                    transformer = pyproj.Transformer.from_crs(
                        self.default_crs, utm_crs, always_xy=True
                    )
                    ref_x, ref_y = transformer.transform(reference_point.x, reference_point.y)
                    ref_point_proj = Point(ref_x, ref_y)
                else:
                    ref_point_proj = reference_point
                
                distances = projected_gdf.geometry.distance(ref_point_proj)
                
            elif reference_gdf is not None:
                # Calculate distance to nearest geometry in reference GeoDataFrame
                if not reference_gdf.empty:
                    # Transform reference GeoDataFrame to same CRS
                    if gdf.crs and 'EPSG:4326' in str(gdf.crs):
                        ref_projected = self.transform_crs(reference_gdf, utm_crs)
                    else:
                        ref_projected = reference_gdf
                    
                    # Calculate nearest distances
                    distances = []
                    for geom in projected_gdf.geometry:
                        min_dist = ref_projected.geometry.distance(geom).min()
                        distances.append(min_dist)
                    distances = pd.Series(distances)
                else:
                    distances = pd.Series([np.nan] * len(projected_gdf))
            else:
                raise ValueError("Either reference_point or reference_gdf must be provided")
            
            result_gdf[distance_col] = distances
            
            self.logger.info(f"Calculated distances for {len(result_gdf)} geometries")
            return result_gdf
            
        except Exception as e:
            self.logger.error(f"Distance calculation failed: {e}")
            raise
    
    def create_buffers(self, 
                      gdf: gpd.GeoDataFrame,
                      buffer_distance: float,
                      buffer_col: str = "buffer_geometry") -> gpd.GeoDataFrame:
        """Create buffer geometries around input geometries
        
        Args:
            gdf: Input GeoDataFrame
            buffer_distance: Buffer distance in CRS units
            buffer_col: Name for buffer geometry column
            
        Returns:
            GeoDataFrame with buffer geometries added
        """
        try:
            if gdf.empty:
                return gdf
            
            result_gdf = gdf.copy()
            
            # Create buffers
            buffer_geometries = gdf.geometry.buffer(buffer_distance)
            result_gdf[buffer_col] = buffer_geometries
            
            self.logger.info(f"Created buffers for {len(result_gdf)} geometries")
            return result_gdf
            
        except Exception as e:
            self.logger.error(f"Buffer creation failed: {e}")
            raise
    
    def calculate_coverage_area(self, gdf: gpd.GeoDataFrame) -> float:
        """Calculate total coverage area from geometries
        
        Args:
            gdf: GeoDataFrame with geometries
            
        Returns:
            Total coverage area in CRS units
        """
        try:
            if gdf.empty:
                return 0.0
            
            # Union all geometries to avoid double counting overlaps
            union_geom = unary_union(gdf.geometry.tolist())
            
            # Calculate area
            if hasattr(union_geom, 'area'):
                total_area = union_geom.area
            else:
                total_area = 0.0
            
            self.logger.info(f"Calculated total coverage area: {total_area}")
            return total_area
            
        except Exception as e:
            self.logger.error(f"Coverage area calculation failed: {e}")
            return 0.0
    
    def create_voronoi_polygons(self, 
                               points_gdf: gpd.GeoDataFrame,
                               boundary: Polygon = None) -> gpd.GeoDataFrame:
        """Create Voronoi polygons from point geometries
        
        Args:
            points_gdf: GeoDataFrame with Point geometries
            boundary: Optional boundary polygon to clip results
            
        Returns:
            GeoDataFrame with Voronoi polygons
        """
        try:
            if points_gdf.empty:
                return gpd.GeoDataFrame(columns=['geometry'])
            
            from scipy.spatial import Voronoi
            
            # Extract coordinates
            coords = [(geom.x, geom.y) for geom in points_gdf.geometry if geom]
            
            if len(coords) < 3:
                self.logger.warning("Need at least 3 points for Voronoi diagram")
                return gpd.GeoDataFrame(columns=['geometry'])
            
            # Create Voronoi diagram
            vor = Voronoi(coords)
            
            # Convert to polygons
            polygons = []
            for region in vor.regions:
                if len(region) > 0 and -1 not in region:
                    polygon_coords = [vor.vertices[i] for i in region]
                    if len(polygon_coords) >= 3:
                        polygon = Polygon(polygon_coords)
                        if boundary:
                            polygon = polygon.intersection(boundary)
                        if polygon.is_valid and not polygon.is_empty:
                            polygons.append(polygon)
            
            # Create GeoDataFrame
            voronoi_gdf = gpd.GeoDataFrame(
                geometry=polygons, 
                crs=points_gdf.crs
            )
            
            self.logger.info(f"Created {len(voronoi_gdf)} Voronoi polygons")
            return voronoi_gdf
            
        except Exception as e:
            self.logger.error(f"Voronoi polygon creation failed: {e}")
            return gpd.GeoDataFrame(columns=['geometry'])
    
    def interpolate_signal_strength(self,
                                  points_gdf: gpd.GeoDataFrame,
                                  signal_column: str,
                                  grid_resolution: float = 0.001,
                                  method: str = "idw") -> gpd.GeoDataFrame:
        """Interpolate signal strength values across a grid
        
        Args:
            points_gdf: GeoDataFrame with signal measurements
            signal_column: Column containing signal values
            grid_resolution: Grid cell size
            method: Interpolation method ('idw' for inverse distance weighting)
            
        Returns:
            GeoDataFrame with interpolated grid
        """
        try:
            if points_gdf.empty or signal_column not in points_gdf.columns:
                return gpd.GeoDataFrame(columns=['geometry', 'interpolated_value'])
            
            # Get bounds and create grid
            bounds = points_gdf.total_bounds
            x_coords = np.arange(bounds[0], bounds[2], grid_resolution)
            y_coords = np.arange(bounds[1], bounds[3], grid_resolution)
            
            # Create grid points
            grid_points = []
            interpolated_values = []
            
            # Extract known points and values
            known_points = [(geom.x, geom.y) for geom in points_gdf.geometry]
            known_values = points_gdf[signal_column].values
            
            for x in x_coords:
                for y in y_coords:
                    grid_point = Point(x, y)
                    
                    if method == "idw":
                        # Inverse distance weighting
                        distances = [
                            np.sqrt((x - kx)**2 + (y - ky)**2) 
                            for kx, ky in known_points
                        ]
                        
                        # Avoid division by zero
                        distances = [max(d, 1e-10) for d in distances]
                        weights = [1/d**2 for d in distances]
                        
                        interpolated_value = sum(w * v for w, v in zip(weights, known_values)) / sum(weights)
                    else:
                        # Simple nearest neighbor
                        min_dist_idx = np.argmin([
                            np.sqrt((x - kx)**2 + (y - ky)**2) 
                            for kx, ky in known_points
                        ])
                        interpolated_value = known_values[min_dist_idx]
                    
                    grid_points.append(grid_point)
                    interpolated_values.append(interpolated_value)
            
            # Create result GeoDataFrame
            result_gdf = gpd.GeoDataFrame({
                'geometry': grid_points,
                'interpolated_value': interpolated_values
            }, crs=points_gdf.crs)
            
            self.logger.info(f"Interpolated signal strength on {len(result_gdf)} grid points")
            return result_gdf
            
        except Exception as e:
            self.logger.error(f"Signal strength interpolation failed: {e}")
            return gpd.GeoDataFrame(columns=['geometry', 'interpolated_value'])
    
    def clip_to_boundary(self, 
                        gdf: gpd.GeoDataFrame,
                        boundary: Union[Polygon, gpd.GeoDataFrame]) -> gpd.GeoDataFrame:
        """Clip GeoDataFrame to boundary geometry
        
        Args:
            gdf: Input GeoDataFrame
            boundary: Boundary polygon or GeoDataFrame
            
        Returns:
            Clipped GeoDataFrame
        """
        try:
            if gdf.empty:
                return gdf
            
            if isinstance(boundary, gpd.GeoDataFrame):
                if boundary.empty:
                    return gpd.GeoDataFrame(columns=gdf.columns, crs=gdf.crs)
                boundary_geom = unary_union(boundary.geometry.tolist())
            else:
                boundary_geom = boundary
            
            # Clip geometries
            clipped_gdf = gdf[gdf.geometry.intersects(boundary_geom)].copy()
            clipped_gdf['geometry'] = clipped_gdf.geometry.intersection(boundary_geom)
            
            # Remove empty geometries
            clipped_gdf = clipped_gdf[~clipped_gdf.geometry.is_empty]
            
            self.logger.info(f"Clipped to {len(clipped_gdf)} geometries within boundary")
            return clipped_gdf
            
        except Exception as e:
            self.logger.error(f"Clipping to boundary failed: {e}")
            return gpd.GeoDataFrame(columns=gdf.columns, crs=gdf.crs)
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics
        
        Returns:
            Dictionary with processing statistics
        """
        return {
            'default_crs': self.default_crs,
            'cached_transformers': len(self._transformers),
        }
