# -*- coding: utf-8 -*-
"""
Data Processing Adapters

This module provides adapters for different data processing engines,
allowing seamless switching between Pandas, Polars, and other engines.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

from .base_adapter import BaseAdapter, AdapterError
from .pandas_adapter import PandasAdapter
from .polars_adapter import PolarsAdapter
from .adapter_factory import AdapterFactory, create_adapter, get_optimal_engine, get_engine_recommendations

__version__ = "1.0.0"
__description__ = "Data processing engine adapters for Connect telecommunications system"

# Export all adapter classes and utilities
__all__ = [
    # Base classes
    "BaseAdapter",
    "AdapterError",
    
    # Concrete adapters
    "PandasAdapter",
    "PolarsAdapter",
    
    # Factory and utilities
    "AdapterFactory",
    "create_adapter",
    "get_optimal_engine",
    "get_engine_recommendations",
]

# Adapter registry for dynamic loading
ADAPTER_REGISTRY = {
    "pandas": PandasAdapter,
    "polars": PolarsAdapter,
}

# Default adapter configuration
DEFAULT_ADAPTER_CONFIG = {
    "engine": "auto",
    "memory_limit_mb": 1024,
    "chunk_size": 50000,
    "enable_optimization": True,
}

# Performance thresholds for adapter selection
ADAPTER_SELECTION_THRESHOLDS = {
    "pandas_max_records": 1_000_000,
    "polars_min_records": 500_000,
    "memory_threshold_mb": 512,
}