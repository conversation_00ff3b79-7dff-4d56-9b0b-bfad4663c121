#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 完整业务流程E2E测试

本模块提供完整的端到端测试，覆盖所有主要业务流程，包括：
- 数据导入和处理流程
- 路测分析完整流程
- 站点管理和GAP分析
- KPI监控和告警
- 用户权限和安全流程
- 数据导出和报告生成

作者: Connect质量工程师
创建时间: 2024-12-19
"""

import asyncio
import json
import os
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch

import pytest
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException

# 测试数据和配置
TEST_CONFIG = {
    'base_url': os.getenv('E2E_BASE_URL', 'http://localhost:3000'),
    'api_url': os.getenv('E2E_API_URL', 'http://localhost:8000'),
    'test_timeout': int(os.getenv('E2E_TIMEOUT', '30')),
    'headless': os.getenv('E2E_HEADLESS', 'true').lower() == 'true',
    'test_data_dir': Path(__file__).parent / 'test_data'
}

# 测试用户数据
TEST_USERS = {
    'admin': {
        'username': '<EMAIL>',
        'password': 'Admin123!',
        'role': 'admin'
    },
    'analyst': {
        'username': '<EMAIL>',
        'password': 'Analyst123!',
        'role': 'analyst'
    },
    'viewer': {
        'username': '<EMAIL>',
        'password': 'Viewer123!',
        'role': 'viewer'
    }
}

# 测试数据文件
TEST_DATA_FILES = {
    'cdr_data': 'sample_cdr_data.csv',
    'ep_data': 'sample_ep_data.csv',
    'site_data': 'sample_site_data.csv',
    'kpi_data': 'sample_kpi_data.json',
    'large_dataset': 'large_test_dataset.csv'
}


class _E2ETestBase:
    """E2E测试基类"""
    
    def __init__(self):
        self.driver = None
        self.wait = None
        self.api_session = requests.Session()
        self.test_data = {}
        self.cleanup_tasks = []
    
    def setup_driver(self):
        """设置WebDriver"""
        chrome_options = Options()
        
        if TEST_CONFIG['headless']:
            chrome_options.add_argument('--headless')
        
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, TEST_CONFIG['test_timeout'])
            self.driver.implicitly_wait(10)
        except Exception as e:
            pytest.skip(f"无法启动Chrome浏览器: {e}")
    
    def teardown_driver(self):
        """清理WebDriver"""
        if self.driver:
            self.driver.quit()
    
    def login(self, user_type: str = 'admin') -> bool:
        """用户登录"""
        user = TEST_USERS.get(user_type)
        if not user:
            return False
        
        try:
            self.driver.get(f"{TEST_CONFIG['base_url']}/login")
            
            # 输入用户名
            username_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            username_input.clear()
            username_input.send_keys(user['username'])
            
            # 输入密码
            password_input = self.driver.find_element(By.NAME, "password")
            password_input.clear()
            password_input.send_keys(user['password'])
            
            # 点击登录按钮
            login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            # 等待登录成功
            self.wait.until(
                EC.url_contains("/dashboard")
            )
            
            return True
        
        except TimeoutException:
            return False
    
    def logout(self):
        """用户登出"""
        try:
            # 点击用户菜单
            user_menu = self.wait.until(
                EC.element_to_be_clickable((By.CLASS_NAME, "user-menu"))
            )
            user_menu.click()
            
            # 点击登出
            logout_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '登出')]"))
            )
            logout_button.click()
            
            # 等待跳转到登录页
            self.wait.until(
                EC.url_contains("/login")
            )
            
        except TimeoutException:
            pass
    
    def navigate_to(self, path: str):
        """导航到指定页面"""
        self.driver.get(f"{TEST_CONFIG['base_url']}{path}")
    
    def wait_for_element(self, locator, timeout=None):
        """等待元素出现"""
        timeout = timeout or TEST_CONFIG['test_timeout']
        wait = WebDriverWait(self.driver, timeout)
        return wait.until(EC.presence_of_element_located(locator))
    
    def wait_for_loading_complete(self):
        """等待页面加载完成"""
        try:
            # 等待加载指示器消失
            self.wait.until(
                EC.invisibility_of_element_located((By.CLASS_NAME, "loading-spinner"))
            )
        except TimeoutException:
            pass
    
    def api_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发送API请求"""
        url = f"{TEST_CONFIG['api_url']}{endpoint}"
        return self.api_session.request(method, url, **kwargs)
    
    def create_test_file(self, filename: str, content: str) -> Path:
        """创建测试文件"""
        test_file = TEST_CONFIG['test_data_dir'] / filename
        test_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.cleanup_tasks.append(lambda: test_file.unlink(missing_ok=True))
        return test_file
    
    def cleanup(self):
        """清理测试数据"""
        for task in self.cleanup_tasks:
            try:
                task()
            except Exception:
                pass
        self.cleanup_tasks.clear()


class TestDataManagementFlow(_E2ETestBase):
    """数据管理完整流程测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.setup_driver()
        assert self.login('admin'), "管理员登录失败"
    
    def teardown_method(self):
        """测试后清理"""
        self.cleanup()
        self.teardown_driver()
    
    @pytest.mark.e2e
    @pytest.mark.slow
    def test_complete_data_import_flow(self):
        """完整数据导入流程测试"""
        # 1. 导航到数据管理页面
        self.navigate_to('/data-management')
        self.wait_for_loading_complete()
        
        # 2. 创建测试CDR数据文件
        cdr_content = self._generate_cdr_test_data()
        cdr_file = self.create_test_file('test_cdr.csv', cdr_content)
        
        # 3. 上传CDR数据
        upload_button = self.wait_for_element((By.ID, "upload-cdr-button"))
        upload_button.click()
        
        file_input = self.wait_for_element((By.INPUT, "file"))
        file_input.send_keys(str(cdr_file))
        
        # 4. 配置导入参数
        self._configure_import_settings()
        
        # 5. 开始导入
        import_button = self.wait_for_element((By.ID, "start-import-button"))
        import_button.click()
        
        # 6. 等待导入完成
        self._wait_for_import_completion()
        
        # 7. 验证导入结果
        self._verify_import_results()
        
        # 8. 检查数据质量报告
        self._check_data_quality_report()
    
    def test_ep_data_import_flow(self):
        """EP数据导入流程测试"""
        # 1. 导航到EP数据导入页面
        self.navigate_to('/data-management/ep-import')
        self.wait_for_loading_complete()
        
        # 2. 创建测试EP数据文件
        ep_content = self._generate_ep_test_data()
        ep_file = self.create_test_file('test_ep.csv', ep_content)
        
        # 3. 上传EP数据
        self._upload_ep_data(ep_file)
        
        # 4. 验证EP数据处理
        self._verify_ep_data_processing()
    
    def test_large_dataset_processing(self):
        """大数据集处理测试"""
        # 1. 创建大数据集（500万行）
        large_dataset = self._generate_large_dataset()
        
        # 2. 分批上传处理
        self._process_large_dataset_in_batches(large_dataset)
        
        # 3. 监控处理进度
        self._monitor_processing_progress()
        
        # 4. 验证处理结果
        self._verify_large_dataset_results()
    
    def _generate_cdr_test_data(self) -> str:
        """生成CDR测试数据"""
        header = "timestamp,cell_id,imsi,duration,data_volume,location_lat,location_lng\n"
        
        rows = []
        base_time = datetime.now() - timedelta(hours=24)
        
        for i in range(1000):
            timestamp = (base_time + timedelta(minutes=i)).isoformat()
            cell_id = f"CELL_{i % 100:03d}"
            imsi = f"46000{i:010d}"
            duration = 30 + (i % 300)
            data_volume = 1024 + (i % 10240)
            lat = 39.9 + (i % 100) * 0.001
            lng = 116.3 + (i % 100) * 0.001
            
            rows.append(f"{timestamp},{cell_id},{imsi},{duration},{data_volume},{lat},{lng}")
        
        return header + "\n".join(rows)
    
    def _generate_ep_test_data(self) -> str:
        """生成EP测试数据"""
        header = "timestamp,cell_id,rsrp,rsrq,sinr,throughput_dl,throughput_ul,location_lat,location_lng\n"
        
        rows = []
        base_time = datetime.now() - timedelta(hours=12)
        
        for i in range(500):
            timestamp = (base_time + timedelta(minutes=i)).isoformat()
            cell_id = f"CELL_{i % 50:03d}"
            rsrp = -80 + (i % 40)
            rsrq = -10 + (i % 20)
            sinr = 5 + (i % 30)
            throughput_dl = 10 + (i % 90)
            throughput_ul = 5 + (i % 45)
            lat = 39.9 + (i % 50) * 0.002
            lng = 116.3 + (i % 50) * 0.002
            
            rows.append(f"{timestamp},{cell_id},{rsrp},{rsrq},{sinr},{throughput_dl},{throughput_ul},{lat},{lng}")
        
        return header + "\n".join(rows)
    
    def _configure_import_settings(self):
        """配置导入设置"""
        # 选择数据类型
        data_type_select = self.wait_for_element((By.ID, "data-type-select"))
        data_type_select.click()
        
        cdr_option = self.wait_for_element((By.XPATH, "//option[@value='cdr']"))
        cdr_option.click()
        
        # 设置时间范围
        start_date = self.driver.find_element(By.ID, "start-date")
        start_date.clear()
        start_date.send_keys((datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'))
        
        end_date = self.driver.find_element(By.ID, "end-date")
        end_date.clear()
        end_date.send_keys(datetime.now().strftime('%Y-%m-%d'))
    
    def _wait_for_import_completion(self):
        """等待导入完成"""
        # 等待进度条出现
        progress_bar = self.wait_for_element((By.CLASS_NAME, "import-progress"))
        
        # 等待导入完成（最多等待5分钟）
        timeout = 300
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                success_message = self.driver.find_element(
                    By.XPATH, "//div[contains(@class, 'success-message')]"
                )
                if success_message.is_displayed():
                    break
            except:
                pass
            
            time.sleep(2)
        else:
            raise TimeoutException("数据导入超时")
    
    def _verify_import_results(self):
        """验证导入结果"""
        # 检查导入统计
        stats_element = self.wait_for_element((By.CLASS_NAME, "import-stats"))
        stats_text = stats_element.text
        
        assert "成功导入" in stats_text
        assert "1000" in stats_text  # 验证记录数
        
        # 检查数据预览
        preview_table = self.wait_for_element((By.CLASS_NAME, "data-preview-table"))
        rows = preview_table.find_elements(By.TAG_NAME, "tr")
        
        assert len(rows) > 1  # 至少有表头和一行数据
    
    def _check_data_quality_report(self):
        """检查数据质量报告"""
        # 点击数据质量报告标签
        quality_tab = self.wait_for_element((By.ID, "quality-report-tab"))
        quality_tab.click()
        
        # 等待报告加载
        self.wait_for_loading_complete()
        
        # 检查质量指标
        completeness = self.wait_for_element((By.ID, "completeness-score"))
        accuracy = self.wait_for_element((By.ID, "accuracy-score"))
        consistency = self.wait_for_element((By.ID, "consistency-score"))
        
        assert float(completeness.text.rstrip('%')) >= 95
        assert float(accuracy.text.rstrip('%')) >= 95
        assert float(consistency.text.rstrip('%')) >= 95
    
    def _upload_ep_data(self, ep_file: Path):
        """上传EP数据"""
        upload_area = self.wait_for_element((By.CLASS_NAME, "ep-upload-area"))
        upload_area.click()
        
        file_input = self.wait_for_element((By.INPUT, "file"))
        file_input.send_keys(str(ep_file))
        
        upload_button = self.wait_for_element((By.ID, "upload-ep-button"))
        upload_button.click()
    
    def _verify_ep_data_processing(self):
        """验证EP数据处理"""
        # 等待处理完成
        self._wait_for_processing_completion("ep-processing")
        
        # 检查处理结果
        result_summary = self.wait_for_element((By.CLASS_NAME, "ep-processing-result"))
        assert "处理完成" in result_summary.text
        
        # 验证地理空间索引创建
        spatial_index_status = self.wait_for_element((By.ID, "spatial-index-status"))
        assert "已创建" in spatial_index_status.text
    
    def _generate_large_dataset(self) -> List[str]:
        """生成大数据集"""
        # 生成500万行测试数据
        dataset = []
        header = "timestamp,cell_id,imsi,duration,data_volume,location_lat,location_lng"
        dataset.append(header)
        
        base_time = datetime.now() - timedelta(days=7)
        
        for i in range(5000000):
            timestamp = (base_time + timedelta(seconds=i)).isoformat()
            cell_id = f"CELL_{i % 1000:04d}"
            imsi = f"46000{i:010d}"
            duration = 30 + (i % 300)
            data_volume = 1024 + (i % 10240)
            lat = 39.9 + (i % 1000) * 0.0001
            lng = 116.3 + (i % 1000) * 0.0001
            
            row = f"{timestamp},{cell_id},{imsi},{duration},{data_volume},{lat},{lng}"
            dataset.append(row)
        
        return dataset
    
    def _process_large_dataset_in_batches(self, dataset: List[str]):
        """分批处理大数据集"""
        batch_size = 100000
        total_batches = len(dataset) // batch_size + 1
        
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min((batch_num + 1) * batch_size, len(dataset))
            
            batch_data = dataset[start_idx:end_idx]
            
            # 创建批次文件
            batch_content = "\n".join(batch_data)
            batch_file = self.create_test_file(f'batch_{batch_num}.csv', batch_content)
            
            # 上传批次
            self._upload_batch(batch_file, batch_num, total_batches)
    
    def _upload_batch(self, batch_file: Path, batch_num: int, total_batches: int):
        """上传数据批次"""
        # 使用API上传以提高效率
        with open(batch_file, 'rb') as f:
            files = {'file': f}
            data = {
                'batch_number': batch_num,
                'total_batches': total_batches,
                'data_type': 'cdr'
            }
            
            response = self.api_request(
                'POST',
                '/api/data/upload-batch',
                files=files,
                data=data
            )
            
            assert response.status_code == 200
    
    def _monitor_processing_progress(self):
        """监控处理进度"""
        # 导航到处理监控页面
        self.navigate_to('/data-management/processing-monitor')
        
        # 等待处理完成（最多等待30分钟）
        timeout = 1800
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                progress_element = self.wait_for_element((By.ID, "processing-progress"))
                progress_text = progress_element.text
                
                if "100%" in progress_text or "完成" in progress_text:
                    break
                    
                # 检查是否有错误
                error_element = self.driver.find_elements(By.CLASS_NAME, "error-message")
                if error_element:
                    raise Exception(f"处理出错: {error_element[0].text}")
                
            except:
                pass
            
            time.sleep(10)
        else:
            raise TimeoutException("大数据集处理超时")
    
    def _verify_large_dataset_results(self):
        """验证大数据集处理结果"""
        # 检查处理统计
        stats_response = self.api_request('GET', '/api/data/processing-stats')
        assert stats_response.status_code == 200
        
        stats = stats_response.json()
        assert stats['total_records'] >= 5000000
        assert stats['success_rate'] >= 0.99
        
        # 检查数据库性能
        perf_response = self.api_request('GET', '/api/data/performance-metrics')
        assert perf_response.status_code == 200
        
        perf_metrics = perf_response.json()
        assert perf_metrics['query_response_time'] < 3.0  # 查询响应时间小于3秒
        assert perf_metrics['index_efficiency'] > 0.9  # 索引效率大于90%
    
    def _wait_for_processing_completion(self, process_type: str):
        """等待处理完成"""
        timeout = 300
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                status_element = self.wait_for_element((By.ID, f"{process_type}-status"))
                if "完成" in status_element.text or "成功" in status_element.text:
                    break
            except:
                pass
            
            time.sleep(2)
        else:
            raise TimeoutException(f"{process_type} 处理超时")


class TestDriveTestAnalysisFlow(_E2ETestBase):
    """路测分析完整流程测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.setup_driver()
        assert self.login('analyst'), "分析师登录失败"
    
    def teardown_method(self):
        """测试后清理"""
        self.cleanup()
        self.teardown_driver()
    
    @pytest.mark.e2e
    def test_complete_drive_test_analysis(self):
        """完整路测分析流程测试"""
        # 1. 导航到路测分析页面
        self.navigate_to('/drive-test-analysis')
        self.wait_for_loading_complete()
        
        # 2. 选择分析区域
        self._select_analysis_area()
        
        # 3. 配置分析参数
        self._configure_analysis_parameters()
        
        # 4. 开始分析
        self._start_analysis()
        
        # 5. 等待分析完成
        self._wait_for_analysis_completion()
        
        # 6. 查看分析结果
        self._view_analysis_results()
        
        # 7. 生成热力图
        self._generate_heatmap()
        
        # 8. 导出分析报告
        self._export_analysis_report()
    
    def test_real_time_drive_test_monitoring(self):
        """实时路测监控测试"""
        # 1. 导航到实时监控页面
        self.navigate_to('/drive-test/real-time')
        
        # 2. 启动实时监控
        self._start_real_time_monitoring()
        
        # 3. 模拟实时数据
        self._simulate_real_time_data()
        
        # 4. 验证实时更新
        self._verify_real_time_updates()
        
        # 5. 测试告警功能
        self._test_real_time_alerts()
    
    def test_multi_technology_analysis(self):
        """多技术分析测试"""
        # 1. 选择多技术分析
        self.navigate_to('/drive-test-analysis/multi-tech')
        
        # 2. 配置技术参数
        self._configure_multi_tech_parameters()
        
        # 3. 执行对比分析
        self._execute_comparison_analysis()
        
        # 4. 验证分析结果
        self._verify_multi_tech_results()
    
    def _select_analysis_area(self):
        """选择分析区域"""
        # 等待地图加载
        map_container = self.wait_for_element((By.ID, "analysis-map"))
        
        # 点击区域选择工具
        area_tool = self.wait_for_element((By.ID, "area-selection-tool"))
        area_tool.click()
        
        # 在地图上绘制区域（模拟点击）
        self.driver.execute_script("""
            var map = window.analysisMap;
            var bounds = [
                [39.9, 116.3],
                [39.95, 116.35]
            ];
            map.selectArea(bounds);
        """)
        
        # 确认区域选择
        confirm_button = self.wait_for_element((By.ID, "confirm-area-button"))
        confirm_button.click()
    
    def _configure_analysis_parameters(self):
        """配置分析参数"""
        # 选择时间范围
        time_range_select = self.wait_for_element((By.ID, "time-range-select"))
        time_range_select.click()
        
        last_24h_option = self.wait_for_element((By.XPATH, "//option[@value='24h']"))
        last_24h_option.click()
        
        # 选择分析指标
        metrics_checkboxes = self.driver.find_elements(By.CLASS_NAME, "metric-checkbox")
        for checkbox in metrics_checkboxes[:3]:  # 选择前3个指标
            if not checkbox.is_selected():
                checkbox.click()
        
        # 设置分析精度
        precision_slider = self.wait_for_element((By.ID, "analysis-precision-slider"))
        self.driver.execute_script("arguments[0].value = 80;", precision_slider)
    
    def _start_analysis(self):
        """开始分析"""
        start_button = self.wait_for_element((By.ID, "start-analysis-button"))
        start_button.click()
        
        # 确认分析参数
        confirm_dialog = self.wait_for_element((By.CLASS_NAME, "confirm-dialog"))
        confirm_button = confirm_dialog.find_element(By.CLASS_NAME, "confirm-button")
        confirm_button.click()
    
    def _wait_for_analysis_completion(self):
        """等待分析完成"""
        # 等待进度条出现
        progress_bar = self.wait_for_element((By.CLASS_NAME, "analysis-progress"))
        
        # 等待分析完成（最多等待10分钟）
        timeout = 600
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                completion_message = self.driver.find_element(
                    By.XPATH, "//div[contains(@class, 'analysis-complete')]"
                )
                if completion_message.is_displayed():
                    break
            except:
                pass
            
            time.sleep(5)
        else:
            raise TimeoutException("路测分析超时")
    
    def _view_analysis_results(self):
        """查看分析结果"""
        # 切换到结果标签
        results_tab = self.wait_for_element((By.ID, "results-tab"))
        results_tab.click()
        
        # 验证结果表格
        results_table = self.wait_for_element((By.CLASS_NAME, "results-table"))
        rows = results_table.find_elements(By.TAG_NAME, "tr")
        assert len(rows) > 1  # 至少有表头和一行数据
        
        # 验证统计信息
        stats_panel = self.wait_for_element((By.CLASS_NAME, "analysis-stats"))
        stats_text = stats_panel.text
        
        assert "总测试点" in stats_text
        assert "平均RSRP" in stats_text
        assert "覆盖率" in stats_text
    
    def _generate_heatmap(self):
        """生成热力图"""
        # 点击热力图标签
        heatmap_tab = self.wait_for_element((By.ID, "heatmap-tab"))
        heatmap_tab.click()
        
        # 选择热力图类型
        heatmap_type_select = self.wait_for_element((By.ID, "heatmap-type-select"))
        heatmap_type_select.click()
        
        rsrp_option = self.wait_for_element((By.XPATH, "//option[@value='rsrp']"))
        rsrp_option.click()
        
        # 生成热力图
        generate_button = self.wait_for_element((By.ID, "generate-heatmap-button"))
        generate_button.click()
        
        # 等待热力图生成
        heatmap_canvas = self.wait_for_element((By.ID, "heatmap-canvas"), timeout=60)
        assert heatmap_canvas.is_displayed()
    
    def _export_analysis_report(self):
        """导出分析报告"""
        # 点击导出按钮
        export_button = self.wait_for_element((By.ID, "export-report-button"))
        export_button.click()
        
        # 选择导出格式
        format_select = self.wait_for_element((By.ID, "export-format-select"))
        format_select.click()
        
        pdf_option = self.wait_for_element((By.XPATH, "//option[@value='pdf']"))
        pdf_option.click()
        
        # 确认导出
        confirm_export_button = self.wait_for_element((By.ID, "confirm-export-button"))
        confirm_export_button.click()
        
        # 等待导出完成
        download_link = self.wait_for_element((By.ID, "download-link"), timeout=120)
        assert download_link.is_displayed()
    
    def _start_real_time_monitoring(self):
        """启动实时监控"""
        start_monitoring_button = self.wait_for_element((By.ID, "start-monitoring-button"))
        start_monitoring_button.click()
        
        # 等待监控启动
        monitoring_status = self.wait_for_element((By.ID, "monitoring-status"))
        assert "监控中" in monitoring_status.text
    
    def _simulate_real_time_data(self):
        """模拟实时数据"""
        # 通过API发送模拟数据
        for i in range(10):
            test_data = {
                'timestamp': datetime.now().isoformat(),
                'cell_id': f'CELL_{i:03d}',
                'rsrp': -80 + (i % 20),
                'rsrq': -10 + (i % 10),
                'location': {
                    'lat': 39.9 + i * 0.001,
                    'lng': 116.3 + i * 0.001
                }
            }
            
            response = self.api_request(
                'POST',
                '/api/drive-test/real-time-data',
                json=test_data
            )
            assert response.status_code == 200
            
            time.sleep(1)
    
    def _verify_real_time_updates(self):
        """验证实时更新"""
        # 检查实时数据表格
        real_time_table = self.wait_for_element((By.ID, "real-time-data-table"))
        
        # 等待数据更新
        time.sleep(5)
        
        rows = real_time_table.find_elements(By.TAG_NAME, "tr")
        assert len(rows) > 1  # 应该有数据行
        
        # 检查最新时间戳
        latest_timestamp = rows[1].find_elements(By.TAG_NAME, "td")[0].text
        assert latest_timestamp  # 应该有时间戳
    
    def _test_real_time_alerts(self):
        """测试实时告警"""
        # 发送触发告警的数据
        alert_data = {
            'timestamp': datetime.now().isoformat(),
            'cell_id': 'CELL_ALERT',
            'rsrp': -120,  # 低信号强度
            'rsrq': -20,   # 低信号质量
            'location': {
                'lat': 39.9,
                'lng': 116.3
            }
        }
        
        response = self.api_request(
            'POST',
            '/api/drive-test/real-time-data',
            json=alert_data
        )
        assert response.status_code == 200
        
        # 检查告警通知
        alert_notification = self.wait_for_element((By.CLASS_NAME, "alert-notification"), timeout=30)
        assert "信号质量告警" in alert_notification.text
    
    def _configure_multi_tech_parameters(self):
        """配置多技术参数"""
        # 选择技术类型
        tech_checkboxes = self.driver.find_elements(By.CLASS_NAME, "tech-checkbox")
        for checkbox in tech_checkboxes[:2]:  # 选择前两种技术
            if not checkbox.is_selected():
                checkbox.click()
        
        # 设置对比参数
        comparison_metrics = self.driver.find_elements(By.CLASS_NAME, "comparison-metric")
        for metric in comparison_metrics[:3]:  # 选择前3个对比指标
            metric.click()
    
    def _execute_comparison_analysis(self):
        """执行对比分析"""
        execute_button = self.wait_for_element((By.ID, "execute-comparison-button"))
        execute_button.click()
        
        # 等待对比分析完成
        self._wait_for_processing_completion("comparison-analysis")
    
    def _verify_multi_tech_results(self):
        """验证多技术分析结果"""
        # 检查对比图表
        comparison_chart = self.wait_for_element((By.ID, "comparison-chart"))
        assert comparison_chart.is_displayed()
        
        # 检查技术性能对比表
        performance_table = self.wait_for_element((By.ID, "tech-performance-table"))
        rows = performance_table.find_elements(By.TAG_NAME, "tr")
        assert len(rows) >= 3  # 至少有表头和两行技术数据


class TestSiteManagementFlow(_E2ETestBase):
    """站点管理完整流程测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.setup_driver()
        assert self.login('admin'), "管理员登录失败"
    
    def teardown_method(self):
        """测试后清理"""
        self.cleanup()
        self.teardown_driver()
    
    @pytest.mark.e2e
    def test_complete_site_management_flow(self):
        """完整站点管理流程测试"""
        # 1. 导航到站点管理页面
        self.navigate_to('/site-management')
        self.wait_for_loading_complete()
        
        # 2. 添加新站点
        self._add_new_site()
        
        # 3. 编辑站点信息
        self._edit_site_information()
        
        # 4. 执行GAP分析
        self._perform_gap_analysis()
        
        # 5. 查看覆盖率分析
        self._view_coverage_analysis()
        
        # 6. 生成站点报告
        self._generate_site_report()
    
    def test_gap_analysis_workflow(self):
        """GAP分析工作流测试"""
        # 1. 导航到GAP分析页面
        self.navigate_to('/site-management/gap-analysis')
        
        # 2. 配置GAP分析参数
        self._configure_gap_analysis_parameters()
        
        # 3. 执行GAP分析
        self._execute_gap_analysis()
        
        # 4. 查看GAP分析结果
        self._view_gap_analysis_results()
        
        # 5. 生成站点建议
        self._generate_site_recommendations()
    
    def test_coverage_optimization(self):
        """覆盖优化测试"""
        # 1. 导航到覆盖优化页面
        self.navigate_to('/site-management/coverage-optimization')
        
        # 2. 分析当前覆盖情况
        self._analyze_current_coverage()
        
        # 3. 运行优化算法
        self._run_optimization_algorithm()
        
        # 4. 查看优化建议
        self._view_optimization_suggestions()
        
        # 5. 应用优化方案
        self._apply_optimization_plan()
    
    def _add_new_site(self):
        """添加新站点"""
        # 点击添加站点按钮
        add_site_button = self.wait_for_element((By.ID, "add-site-button"))
        add_site_button.click()
        
        # 填写站点信息
        site_name_input = self.wait_for_element((By.ID, "site-name-input"))
        site_name_input.send_keys("测试站点001")
        
        site_id_input = self.driver.find_element(By.ID, "site-id-input")
        site_id_input.send_keys("SITE_TEST_001")
        
        latitude_input = self.driver.find_element(By.ID, "latitude-input")
        latitude_input.send_keys("39.9042")
        
        longitude_input = self.driver.find_element(By.ID, "longitude-input")
        longitude_input.send_keys("116.4074")
        
        # 选择站点类型
        site_type_select = self.driver.find_element(By.ID, "site-type-select")
        site_type_select.click()
        
        macro_option = self.wait_for_element((By.XPATH, "//option[@value='macro']"))
        macro_option.click()
        
        # 保存站点
        save_button = self.driver.find_element(By.ID, "save-site-button")
        save_button.click()
        
        # 验证站点添加成功
        success_message = self.wait_for_element((By.CLASS_NAME, "success-message"))
        assert "站点添加成功" in success_message.text
    
    def _edit_site_information(self):
        """编辑站点信息"""
        # 在站点列表中找到刚添加的站点
        site_row = self.wait_for_element(
            (By.XPATH, "//tr[contains(., 'SITE_TEST_001')]")
        )
        
        # 点击编辑按钮
        edit_button = site_row.find_element(By.CLASS_NAME, "edit-button")
        edit_button.click()
        
        # 修改站点信息
        site_name_input = self.wait_for_element((By.ID, "edit-site-name-input"))
        site_name_input.clear()
        site_name_input.send_keys("测试站点001-已更新")
        
        # 添加备注
        notes_textarea = self.driver.find_element(By.ID, "site-notes-textarea")
        notes_textarea.send_keys("这是一个测试站点，用于E2E测试")
        
        # 保存修改
        update_button = self.driver.find_element(By.ID, "update-site-button")
        update_button.click()
        
        # 验证修改成功
        success_message = self.wait_for_element((By.CLASS_NAME, "success-message"))
        assert "站点更新成功" in success_message.text
    
    def _perform_gap_analysis(self):
        """执行GAP分析"""
        # 选择站点进行GAP分析
        site_checkbox = self.wait_for_element(
            (By.XPATH, "//tr[contains(., 'SITE_TEST_001')]//input[@type='checkbox']")
        )
        site_checkbox.click()
        
        # 点击GAP分析按钮
        gap_analysis_button = self.wait_for_element((By.ID, "gap-analysis-button"))
        gap_analysis_button.click()
        
        # 等待分析完成
        self._wait_for_processing_completion("gap-analysis")
    
    def _view_coverage_analysis(self):
        """查看覆盖率分析"""
        # 切换到覆盖率分析标签
        coverage_tab = self.wait_for_element((By.ID, "coverage-analysis-tab"))
        coverage_tab.click()
        
        # 验证覆盖率地图
        coverage_map = self.wait_for_element((By.ID, "coverage-map"))
        assert coverage_map.is_displayed()
        
        # 验证覆盖率统计
        coverage_stats = self.wait_for_element((By.CLASS_NAME, "coverage-stats"))
        stats_text = coverage_stats.text
        
        assert "覆盖率" in stats_text
        assert "%" in stats_text
    
    def _generate_site_report(self):
        """生成站点报告"""
        # 点击生成报告按钮
        generate_report_button = self.wait_for_element((By.ID, "generate-site-report-button"))
        generate_report_button.click()
        
        # 选择报告类型
        report_type_select = self.wait_for_element((By.ID, "report-type-select"))
        report_type_select.click()
        
        comprehensive_option = self.wait_for_element((By.XPATH, "//option[@value='comprehensive']"))
        comprehensive_option.click()
        
        # 确认生成
        confirm_generate_button = self.wait_for_element((By.ID, "confirm-generate-button"))
        confirm_generate_button.click()
        
        # 等待报告生成
        report_download_link = self.wait_for_element((By.ID, "report-download-link"), timeout=120)
        assert report_download_link.is_displayed()
    
    def _configure_gap_analysis_parameters(self):
        """配置GAP分析参数"""
        # 设置分析区域
        area_input = self.wait_for_element((By.ID, "analysis-area-input"))
        area_input.send_keys("北京市朝阳区")
        
        # 设置信号阈值
        signal_threshold_input = self.driver.find_element(By.ID, "signal-threshold-input")
        signal_threshold_input.clear()
        signal_threshold_input.send_keys("-95")
        
        # 选择分析频段
        frequency_select = self.driver.find_element(By.ID, "frequency-select")
        frequency_select.click()
        
        lte_option = self.wait_for_element((By.XPATH, "//option[@value='lte']"))
        lte_option.click()
    
    def _execute_gap_analysis(self):
        """执行GAP分析"""
        execute_button = self.wait_for_element((By.ID, "execute-gap-analysis-button"))
        execute_button.click()
        
        # 等待分析完成
        self._wait_for_processing_completion("gap-analysis-execution")
    
    def _view_gap_analysis_results(self):
        """查看GAP分析结果"""
        # 切换到结果标签
        results_tab = self.wait_for_element((By.ID, "gap-results-tab"))
        results_tab.click()
        
        # 验证GAP区域地图
        gap_map = self.wait_for_element((By.ID, "gap-areas-map"))
        assert gap_map.is_displayed()
        
        # 验证GAP统计
        gap_stats = self.wait_for_element((By.CLASS_NAME, "gap-statistics"))
        stats_text = gap_stats.text
        
        assert "GAP区域数量" in stats_text
        assert "覆盖空洞" in stats_text
    
    def _generate_site_recommendations(self):
        """生成站点建议"""
        # 点击生成建议按钮
        generate_recommendations_button = self.wait_for_element((By.ID, "generate-recommendations-button"))
        generate_recommendations_button.click()
        
        # 等待建议生成
        recommendations_panel = self.wait_for_element((By.CLASS_NAME, "site-recommendations"), timeout=60)
        
        # 验证建议内容
        recommendations_text = recommendations_panel.text
        assert "建议新增站点" in recommendations_text or "优化现有站点" in recommendations_text
    
    def _analyze_current_coverage(self):
        """分析当前覆盖情况"""
        # 点击分析按钮
        analyze_button = self.wait_for_element((By.ID, "analyze-coverage-button"))
        analyze_button.click()
        
        # 等待分析完成
        self._wait_for_processing_completion("coverage-analysis")
        
        # 验证分析结果
        analysis_results = self.wait_for_element((By.CLASS_NAME, "coverage-analysis-results"))
        assert analysis_results.is_displayed()
    
    def _run_optimization_algorithm(self):
        """运行优化算法"""
        # 选择优化算法
        algorithm_select = self.wait_for_element((By.ID, "optimization-algorithm-select"))
        algorithm_select.click()
        
        genetic_option = self.wait_for_element((By.XPATH, "//option[@value='genetic']"))
        genetic_option.click()
        
        # 设置优化参数
        iterations_input = self.driver.find_element(By.ID, "optimization-iterations-input")
        iterations_input.clear()
        iterations_input.send_keys("100")
        
        # 开始优化
        start_optimization_button = self.driver.find_element(By.ID, "start-optimization-button")
        start_optimization_button.click()
        
        # 等待优化完成
        self._wait_for_processing_completion("coverage-optimization")
    
    def _view_optimization_suggestions(self):
        """查看优化建议"""
        # 切换到建议标签
        suggestions_tab = self.wait_for_element((By.ID, "optimization-suggestions-tab"))
        suggestions_tab.click()
        
        # 验证建议列表
        suggestions_list = self.wait_for_element((By.CLASS_NAME, "optimization-suggestions-list"))
        suggestions = suggestions_list.find_elements(By.CLASS_NAME, "suggestion-item")
        
        assert len(suggestions) > 0
    
    def _apply_optimization_plan(self):
        """应用优化方案"""
        # 选择要应用的建议
        first_suggestion = self.wait_for_element((By.CLASS_NAME, "suggestion-item"))
        suggestion_checkbox = first_suggestion.find_element(By.INPUT, "checkbox")
        suggestion_checkbox.click()
        
        # 应用选中的建议
        apply_button = self.wait_for_element((By.ID, "apply-optimization-button"))
        apply_button.click()
        
        # 确认应用
        confirm_dialog = self.wait_for_element((By.CLASS_NAME, "confirm-dialog"))
        confirm_button = confirm_dialog.find_element(By.CLASS_NAME, "confirm-button")
        confirm_button.click()
        
        # 验证应用成功
        success_message = self.wait_for_element((By.CLASS_NAME, "success-message"))
        assert "优化方案应用成功" in success_message.text


class TestKPIMonitoringFlow(_E2ETestBase):
    """KPI监控完整流程测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.setup_driver()
        assert self.login('analyst'), "分析师登录失败"
    
    def teardown_method(self):
        """测试后清理"""
        self.cleanup()
        self.teardown_driver()
    
    @pytest.mark.e2e
    def test_complete_kpi_monitoring_flow(self):
        """完整KPI监控流程测试"""
        # 1. 导航到KPI监控页面
        self.navigate_to('/kpi-monitoring')
        self.wait_for_loading_complete()
        
        # 2. 配置KPI监控
        self._configure_kpi_monitoring()
        
        # 3. 查看实时KPI
        self._view_real_time_kpi()
        
        # 4. 设置KPI告警
        self._setup_kpi_alerts()
        
        # 5. 生成KPI报告
        self._generate_kpi_report()
        
        # 6. 测试告警触发
        self._test_alert_triggering()
    
    def test_kpi_trend_analysis(self):
        """KPI趋势分析测试"""
        # 1. 导航到趋势分析页面
        self.navigate_to('/kpi-monitoring/trend-analysis')
        
        # 2. 选择分析时间范围
        self._select_analysis_time_range()
        
        # 3. 选择KPI指标
        self._select_kpi_metrics()
        
        # 4. 生成趋势图表
        self._generate_trend_charts()
        
        # 5. 分析趋势模式
        self._analyze_trend_patterns()
    
    def test_kpi_comparison_analysis(self):
        """KPI对比分析测试"""
        # 1. 导航到对比分析页面
        self.navigate_to('/kpi-monitoring/comparison')
        
        # 2. 选择对比维度
        self._select_comparison_dimensions()
        
        # 3. 执行对比分析
        self._execute_comparison_analysis()
        
        # 4. 查看对比结果
        self._view_comparison_results()
    
    def _configure_kpi_monitoring(self):
        """配置KPI监控"""
        # 点击配置按钮
        config_button = self.wait_for_element((By.ID, "kpi-config-button"))
        config_button.click()
        
        # 选择监控指标
        kpi_checkboxes = self.driver.find_elements(By.CLASS_NAME, "kpi-checkbox")
        for checkbox in kpi_checkboxes[:5]:  # 选择前5个KPI
            if not checkbox.is_selected():
                checkbox.click()
        
        # 设置刷新间隔
        refresh_interval_select = self.driver.find_element(By.ID, "refresh-interval-select")
        refresh_interval_select.click()
        
        interval_5min = self.wait_for_element((By.XPATH, "//option[@value='5']"))
        interval_5min.click()
        
        # 保存配置
        save_config_button = self.driver.find_element(By.ID, "save-kpi-config-button")
        save_config_button.click()
        
        # 验证配置保存成功
        success_message = self.wait_for_element((By.CLASS_NAME, "success-message"))
        assert "配置保存成功" in success_message.text
    
    def _view_real_time_kpi(self):
        """查看实时KPI"""
        # 等待KPI仪表板加载
        kpi_dashboard = self.wait_for_element((By.ID, "kpi-dashboard"))
        
        # 验证KPI卡片
        kpi_cards = kpi_dashboard.find_elements(By.CLASS_NAME, "kpi-card")
        assert len(kpi_cards) >= 5  # 应该有至少5个KPI卡片
        
        # 验证每个KPI卡片都有数值
        for card in kpi_cards:
            kpi_value = card.find_element(By.CLASS_NAME, "kpi-value")
            assert kpi_value.text.strip() != ""  # 应该有数值
        
        # 验证实时更新
        first_card_value = kpi_cards[0].find_element(By.CLASS_NAME, "kpi-value").text
        
        # 等待5秒后检查数值是否更新
        time.sleep(5)
        updated_value = kpi_cards[0].find_element(By.CLASS_NAME, "kpi-value").text
        # 注意：在测试环境中，数值可能不会变化，这里主要验证页面没有错误
    
    def _setup_kpi_alerts(self):
        """设置KPI告警"""
        # 点击告警设置按钮
        alert_setup_button = self.wait_for_element((By.ID, "alert-setup-button"))
        alert_setup_button.click()
        
        # 添加新告警规则
        add_alert_button = self.wait_for_element((By.ID, "add-alert-button"))
        add_alert_button.click()
        
        # 选择KPI指标
        kpi_select = self.wait_for_element((By.ID, "alert-kpi-select"))
        kpi_select.click()
        
        rsrp_option = self.wait_for_element((By.XPATH, "//option[@value='rsrp']"))
        rsrp_option.click()
        
        # 设置告警条件
        condition_select = self.driver.find_element(By.ID, "alert-condition-select")
        condition_select.click()
        
        less_than_option = self.wait_for_element((By.XPATH, "//option[@value='less_than']"))
        less_than_option.click()
        
        # 设置阈值
        threshold_input = self.driver.find_element(By.ID, "alert-threshold-input")
        threshold_input.send_keys("-100")
        
        # 设置告警级别
        severity_select = self.driver.find_element(By.ID, "alert-severity-select")
        severity_select.click()
        
        high_option = self.wait_for_element((By.XPATH, "//option[@value='high']"))
        high_option.click()
        
        # 保存告警规则
        save_alert_button = self.driver.find_element(By.ID, "save-alert-button")
        save_alert_button.click()
        
        # 验证告警规则保存成功
        success_message = self.wait_for_element((By.CLASS_NAME, "success-message"))
        assert "告警规则保存成功" in success_message.text
    
    def _generate_kpi_report(self):
        """生成KPI报告"""
        # 点击生成报告按钮
        generate_report_button = self.wait_for_element((By.ID, "generate-kpi-report-button"))
        generate_report_button.click()
        
        # 选择报告时间范围
        time_range_select = self.wait_for_element((By.ID, "report-time-range-select"))
        time_range_select.click()
        
        last_week_option = self.wait_for_element((By.XPATH, "//option[@value='last_week']"))
        last_week_option.click()
        
        # 选择报告格式
        format_select = self.driver.find_element(By.ID, "report-format-select")
        format_select.click()
        
        excel_option = self.wait_for_element((By.XPATH, "//option[@value='excel']"))
        excel_option.click()
        
        # 确认生成
        confirm_generate_button = self.driver.find_element(By.ID, "confirm-generate-report-button")
        confirm_generate_button.click()
        
        # 等待报告生成
        download_link = self.wait_for_element((By.ID, "kpi-report-download-link"), timeout=120)
        assert download_link.is_displayed()
    
    def _test_alert_triggering(self):
        """测试告警触发"""
        # 通过API发送触发告警的数据
        alert_data = {
            'timestamp': datetime.now().isoformat(),
            'kpi_type': 'rsrp',
            'value': -105,  # 低于阈值-100
            'cell_id': 'CELL_ALERT_TEST',
            'location': {
                'lat': 39.9,
                'lng': 116.3
            }
        }
        
        response = self.api_request(
            'POST',
            '/api/kpi/real-time-data',
            json=alert_data
        )
        assert response.status_code == 200
        
        # 检查告警通知
        alert_notification = self.wait_for_element((By.CLASS_NAME, "alert-notification"), timeout=30)
        assert "RSRP告警" in alert_notification.text or "信号强度告警" in alert_notification.text
    
    def _select_analysis_time_range(self):
        """选择分析时间范围"""
        # 设置开始时间
        start_date_input = self.wait_for_element((By.ID, "trend-start-date"))
        start_date_input.clear()
        start_date_input.send_keys((datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
        
        # 设置结束时间
        end_date_input = self.driver.find_element(By.ID, "trend-end-date")
        end_date_input.clear()
        end_date_input.send_keys(datetime.now().strftime('%Y-%m-%d'))
    
    def _select_kpi_metrics(self):
        """选择KPI指标"""
        # 选择要分析的KPI指标
        kpi_checkboxes = self.driver.find_elements(By.CLASS_NAME, "trend-kpi-checkbox")
        for checkbox in kpi_checkboxes[:3]:  # 选择前3个KPI
            if not checkbox.is_selected():
                checkbox.click()
    
    def _generate_trend_charts(self):
        """生成趋势图表"""
        # 点击生成图表按钮
        generate_chart_button = self.wait_for_element((By.ID, "generate-trend-chart-button"))
        generate_chart_button.click()
        
        # 等待图表生成
        trend_chart = self.wait_for_element((By.ID, "kpi-trend-chart"), timeout=60)
        assert trend_chart.is_displayed()
    
    def _analyze_trend_patterns(self):
        """分析趋势模式"""
        # 点击模式分析按钮
        pattern_analysis_button = self.wait_for_element((By.ID, "pattern-analysis-button"))
        pattern_analysis_button.click()
        
        # 等待分析完成
        pattern_results = self.wait_for_element((By.CLASS_NAME, "pattern-analysis-results"), timeout=60)
        
        # 验证分析结果
        results_text = pattern_results.text
        assert "趋势分析" in results_text or "模式识别" in results_text
    
    def _select_comparison_dimensions(self):
        """选择对比维度"""
        # 选择对比类型
        comparison_type_select = self.wait_for_element((By.ID, "comparison-type-select"))
        comparison_type_select.click()
        
        time_comparison_option = self.wait_for_element((By.XPATH, "//option[@value='time_comparison']"))
        time_comparison_option.click()
        
        # 选择对比时间段
        period1_select = self.driver.find_element(By.ID, "comparison-period1-select")
        period1_select.click()
        
        this_week_option = self.wait_for_element((By.XPATH, "//option[@value='this_week']"))
        this_week_option.click()
        
        period2_select = self.driver.find_element(By.ID, "comparison-period2-select")
        period2_select.click()
        
        last_week_option = self.wait_for_element((By.XPATH, "//option[@value='last_week']"))
        last_week_option.click()
    
    def _execute_comparison_analysis(self):
        """执行对比分析"""
        execute_button = self.wait_for_element((By.ID, "execute-kpi-comparison-button"))
        execute_button.click()
        
        # 等待分析完成
        self._wait_for_processing_completion("kpi-comparison")
    
    def _view_comparison_results(self):
        """查看对比结果"""
        # 验证对比图表
        comparison_chart = self.wait_for_element((By.ID, "kpi-comparison-chart"))
        assert comparison_chart.is_displayed()
        
        # 验证对比统计表
        comparison_table = self.wait_for_element((By.ID, "kpi-comparison-table"))
        rows = comparison_table.find_elements(By.TAG_NAME, "tr")
        assert len(rows) > 1  # 至少有表头和一行数据


class TestUserPermissionFlow(_E2ETestBase):
    """用户权限完整流程测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.setup_driver()
    
    def teardown_method(self):
        """测试后清理"""
        self.cleanup()
        self.teardown_driver()
    
    @pytest.mark.e2e
    def test_admin_permissions(self):
        """管理员权限测试"""
        # 1. 管理员登录
        assert self.login('admin'), "管理员登录失败"
        
        # 2. 验证管理员可以访问所有功能
        self._verify_admin_access()
        
        # 3. 测试用户管理功能
        self._test_user_management()
        
        # 4. 测试系统配置功能
        self._test_system_configuration()
        
        # 5. 登出
        self.logout()
    
    def test_analyst_permissions(self):
        """分析师权限测试"""
        # 1. 分析师登录
        assert self.login('analyst'), "分析师登录失败"
        
        # 2. 验证分析师权限
        self._verify_analyst_access()
        
        # 3. 测试数据分析功能
        self._test_data_analysis_access()
        
        # 4. 验证受限功能
        self._verify_analyst_restrictions()
        
        # 5. 登出
        self.logout()
    
    def test_viewer_permissions(self):
        """查看者权限测试"""
        # 1. 查看者登录
        assert self.login('viewer'), "查看者登录失败"
        
        # 2. 验证查看者权限
        self._verify_viewer_access()
        
        # 3. 验证只读访问
        self._verify_read_only_access()
        
        # 4. 验证功能限制
        self._verify_viewer_restrictions()
        
        # 5. 登出
        self.logout()
    
    def test_permission_escalation_prevention(self):
        """权限提升防护测试"""
        # 1. 以低权限用户登录
        assert self.login('viewer'), "查看者登录失败"
        
        # 2. 尝试访问高权限功能
        self._attempt_privilege_escalation()
        
        # 3. 验证访问被拒绝
        self._verify_access_denied()
        
        # 4. 登出
        self.logout()
    
    def _verify_admin_access(self):
        """验证管理员访问权限"""
        # 验证可以访问用户管理
        self.navigate_to('/admin/user-management')
        user_management_page = self.wait_for_element((By.ID, "user-management-page"))
        assert user_management_page.is_displayed()
        
        # 验证可以访问系统配置
        self.navigate_to('/admin/system-config')
        system_config_page = self.wait_for_element((By.ID, "system-config-page"))
        assert system_config_page.is_displayed()
        
        # 验证可以访问所有数据管理功能
        self.navigate_to('/data-management')
        data_management_page = self.wait_for_element((By.ID, "data-management-page"))
        assert data_management_page.is_displayed()
    
    def _test_user_management(self):
        """测试用户管理功能"""
        self.navigate_to('/admin/user-management')
        
        # 添加新用户
        add_user_button = self.wait_for_element((By.ID, "add-user-button"))
        add_user_button.click()
        
        # 填写用户信息
        username_input = self.wait_for_element((By.ID, "new-username-input"))
        username_input.send_keys("<EMAIL>")
        
        password_input = self.driver.find_element(By.ID, "new-password-input")
        password_input.send_keys("TestUser123!")
        
        role_select = self.driver.find_element(By.ID, "new-user-role-select")
        role_select.click()
        
        analyst_option = self.wait_for_element((By.XPATH, "//option[@value='analyst']"))
        analyst_option.click()
        
        # 保存用户
        save_user_button = self.driver.find_element(By.ID, "save-new-user-button")
        save_user_button.click()
        
        # 验证用户创建成功
        success_message = self.wait_for_element((By.CLASS_NAME, "success-message"))
        assert "用户创建成功" in success_message.text
    
    def _test_system_configuration(self):
        """测试系统配置功能"""
        self.navigate_to('/admin/system-config')
        
        # 修改系统设置
        max_upload_size_input = self.wait_for_element((By.ID, "max-upload-size-input"))
        max_upload_size_input.clear()
        max_upload_size_input.send_keys("100")
        
        # 保存配置
        save_config_button = self.driver.find_element(By.ID, "save-system-config-button")
        save_config_button.click()
        
        # 验证配置保存成功
        success_message = self.wait_for_element((By.CLASS_NAME, "success-message"))
        assert "配置保存成功" in success_message.text
    
    def _verify_analyst_access(self):
        """验证分析师访问权限"""
        # 验证可以访问数据分析
        self.navigate_to('/drive-test-analysis')
        analysis_page = self.wait_for_element((By.ID, "drive-test-analysis-page"))
        assert analysis_page.is_displayed()
        
        # 验证可以访问KPI监控
        self.navigate_to('/kpi-monitoring')
        kpi_page = self.wait_for_element((By.ID, "kpi-monitoring-page"))
        assert kpi_page.is_displayed()
    
    def _test_data_analysis_access(self):
        """测试数据分析访问"""
        self.navigate_to('/drive-test-analysis')
        
        # 验证可以执行分析
        analysis_button = self.wait_for_element((By.ID, "start-analysis-button"))
        assert analysis_button.is_enabled()
        
        # 验证可以查看结果
        results_tab = self.driver.find_element(By.ID, "results-tab")
        assert results_tab.is_displayed()
    
    def _verify_analyst_restrictions(self):
        """验证分析师权限限制"""
        # 尝试访问用户管理（应该被拒绝）
        self.navigate_to('/admin/user-management')
        
        try:
            error_message = self.wait_for_element((By.CLASS_NAME, "access-denied-message"), timeout=5)
            assert "访问被拒绝" in error_message.text or "权限不足" in error_message.text
        except TimeoutException:
            # 如果没有错误消息，检查是否被重定向
            current_url = self.driver.current_url
            assert "/admin/user-management" not in current_url
    
    def _verify_viewer_access(self):
        """验证查看者访问权限"""
        # 验证可以访问仪表板
        self.navigate_to('/dashboard')
        dashboard_page = self.wait_for_element((By.ID, "dashboard-page"))
        assert dashboard_page.is_displayed()
        
        # 验证可以查看报告
        self.navigate_to('/reports')
        reports_page = self.wait_for_element((By.ID, "reports-page"))
        assert reports_page.is_displayed()
    
    def _verify_read_only_access(self):
        """验证只读访问"""
        self.navigate_to('/dashboard')
        
        # 验证没有编辑按钮
        edit_buttons = self.driver.find_elements(By.CLASS_NAME, "edit-button")
        for button in edit_buttons:
            assert not button.is_enabled() or not button.is_displayed()
    
    def _verify_viewer_restrictions(self):
        """验证查看者功能限制"""
        # 尝试访问数据管理（应该被拒绝）
        self.navigate_to('/data-management')
        
        try:
            error_message = self.wait_for_element((By.CLASS_NAME, "access-denied-message"), timeout=5)
            assert "访问被拒绝" in error_message.text or "权限不足" in error_message.text
        except TimeoutException:
            # 如果没有错误消息，检查是否被重定向
            current_url = self.driver.current_url
            assert "/data-management" not in current_url
    
    def _attempt_privilege_escalation(self):
        """尝试权限提升"""
        # 尝试通过直接URL访问管理功能
        restricted_urls = [
            '/admin/user-management',
            '/admin/system-config',
            '/data-management/import',
            '/site-management/edit'
        ]
        
        for url in restricted_urls:
            self.navigate_to(url)
            time.sleep(2)  # 等待页面响应
            
            # 检查是否被重定向或显示错误
            current_url = self.driver.current_url
            if url in current_url:
                # 如果URL没有改变，检查是否有访问拒绝消息
                try:
                    error_message = self.driver.find_element(By.CLASS_NAME, "access-denied-message")
                    assert error_message.is_displayed()
                except:
                    # 如果没有错误消息，这可能是一个安全问题
                    assert False, f"可能存在权限提升漏洞：{url}"
    
    def _verify_access_denied(self):
        """验证访问被拒绝"""
        # 这个方法在_attempt_privilege_escalation中已经包含了验证逻辑
        pass


if __name__ == "__main__":
    # 运行E2E测试
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "--maxfail=5",
        "--durations=10"
    ])