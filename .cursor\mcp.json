{"mcpServers": {"excel": {"command": "cmd", "args": ["/c", "npx", "--yes", "@negokaz/excel-mcp-server"], "env": {"EXCEL_MCP_PAGING_CELLS_LIMIT": "4000"}, "fromGalleryId": "negokaz.excel-mcp-server"}, "Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "fromGalleryId": "upstash.context7"}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "fromGalleryId": "modelcontextprotocol.servers_fetch"}, "Figma AI Bridge": {"command": "npx", "args": ["-y", "figma-developer-mcp", "--st<PERSON>"], "env": {"FIGMA_API_KEY": "*********************************************"}, "fromGalleryId": "GLips.Figma-Context-MCP"}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "fromGalleryId": "executeautomation.mcp-playwright"}, "memory": {"command": "npx", "args": ["@modelcontextprotocol/server-memory"], "fromGalleryId": "modelcontextprotocol.servers_memory", "env": {}}, "docker": {"command": "uvx", "args": ["docker-mcp"], "fromGalleryId": "QuantGeekDev.docker-mcp"}, "browser-tools": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp@1.2.0"], "enabled": true, "fromGalleryId": "AgentDeskAI.browser-tools-mcp"}, "GitHub": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}, "fromGalleryId": "modelcontextprotocol.servers_github"}, "PostgreSQL": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres", "postgresql://to2:TO2@localhost:5432/connect"], "env": {}, "fromGalleryId": "modelcontextprotocol.servers_postgres"}, "Sequential Thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}, "fromGalleryId": "modelcontextprotocol.servers_sequentialthinking"}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************", "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY_HERE", "OPENAI_API_KEY": "YOUR_OPENAI_KEY_HERE", "GOOGLE_API_KEY": "AIzaSyCwW3OXtO28VYk46bePr_BGtWvUClccLS8", "MISTRAL_API_KEY": "YOUR_MISTRAL_KEY_HERE", "OPENROUTER_API_KEY": "YOUR_OPENROUTER_KEY_HERE", "XAI_API_KEY": "YOUR_XAI_KEY_HERE", "AZURE_OPENAI_API_KEY": "YOUR_AZURE_KEY_HERE"}}, "Puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {"PUPPETEER_SKIP_CHROMIUM_DOWNLOAD": "false"}, "enabled": true, "fromGalleryId": "modelcontextprotocol.servers_puppeteer"}, "text-editor": {"command": "uvx", "args": ["mcp-text-editor"]}, "everything-search": {"command": "uvx", "args": ["mcp-server-everything-search"], "env": {"EVERYTHING_SDK_PATH": "D:/ProgramData/Everything-SDK/dll/Everything64.dll"}, "fromGalleryId": "mamertofabian.mcp-everything-search"}, "Time": {"command": "python", "args": ["-m", "mcp_server_time", "--local-timezone", "Europe/Berlin"], "fromGalleryId": "modelcontextprotocol.servers_time"}, "Notion": {"command": "npx", "args": ["-y", "notion-mcp-server"], "env": {"NOTION_PAGE_ID": "a8f3e694-67bd-4b10-a844-8cf233669a96", "NOTION_TOKEN": "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"}, "fromGalleryId": "awkoy.notion-mcp-server"}, "magic-mcp": {"command": "npx", "args": ["-y", "@21st-dev/magic@latest", "API_KEY=\"AIzaSyCwW3OXtO28VYk46bePr_BGtWvUClccLS8\""]}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "autoApprove": ["interactive_feedback"]}, "Codegen": {"command": "uvx", "args": ["--from", "git+https://github.com/codegen-sh/codegen-sdk.git#egg=codegen-mcp-server&subdirectory=codegen-examples/examples/codegen-mcp-server", "codegen-mcp-server", "--yes"], "env": {}, "fromGalleryId": "codegen-sh.codegen-sdk_codegen-mcp-server"}, "promptx": {"command": "npx", "args": ["-y", "-f", "dpml-prompt@snapshot", "mcp-server"]}}}