{"webhook_urls": {"slack": "", "teams": "", "discord": ""}, "monitoring_endpoints": {"prometheus": "", "grafana": "", "datadog": ""}, "thresholds": {"pass_rate_warning": 90.0, "pass_rate_critical": 80.0, "coverage_warning": 80.0, "coverage_critical": 70.0, "security_score_warning": 80.0, "security_score_critical": 70.0, "performance_warning": 3.0, "performance_critical": 5.0, "reliability_warning": 85.0, "reliability_critical": 75.0}, "dashboard": {"output_dir": "./dashboard", "template_dir": "./templates", "static_dir": "./static", "auto_refresh_interval": 300, "chart_theme": "seaborn", "enable_dark_mode": false}, "notifications": {"enabled": true, "channels": ["slack", "teams"], "alert_levels": ["critical", "warning"], "quiet_hours": {"enabled": false, "start": "22:00", "end": "08:00", "timezone": "Asia/Shanghai"}, "rate_limiting": {"enabled": true, "max_alerts_per_hour": 10, "cooldown_minutes": 30}}, "metrics": {"retention_days": 30, "aggregation_interval": "5m", "custom_metrics": {"business_kpis": {"data_processing_throughput": {"unit": "records/second", "warning_threshold": 1000, "critical_threshold": 500}, "api_response_time_p95": {"unit": "milliseconds", "warning_threshold": 2000, "critical_threshold": 5000}, "database_connection_pool_usage": {"unit": "percentage", "warning_threshold": 80, "critical_threshold": 95}}}}, "reporting": {"daily_summary": {"enabled": true, "time": "09:00", "recipients": ["<EMAIL>"]}, "weekly_report": {"enabled": true, "day": "monday", "time": "10:00", "recipients": ["<EMAIL>"]}, "monthly_analysis": {"enabled": true, "day": 1, "time": "09:00", "recipients": ["<EMAIL>"]}}, "integrations": {"jira": {"enabled": false, "url": "", "username": "", "api_token": "", "project_key": "CONNECT", "auto_create_issues": {"enabled": false, "issue_type": "Bug", "priority_mapping": {"critical": "Highest", "warning": "High"}}}, "github": {"enabled": true, "token": "", "repository": "company/connect", "auto_create_issues": {"enabled": false, "labels": ["bug", "automated"], "assignees": ["team-lead"]}}, "email": {"enabled": false, "smtp_server": "", "smtp_port": 587, "username": "", "password": "", "from_address": "<EMAIL>", "use_tls": true}}, "security": {"api_key_rotation": {"enabled": true, "interval_days": 90}, "webhook_verification": {"enabled": true, "secret_key": ""}, "rate_limiting": {"enabled": true, "requests_per_minute": 60}}, "performance": {"cache": {"enabled": true, "ttl_seconds": 300, "max_size_mb": 100}, "compression": {"enabled": true, "algorithm": "gzip", "level": 6}, "batch_processing": {"enabled": true, "batch_size": 100, "flush_interval_seconds": 30}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": {"enabled": true, "path": "./logs/monitoring.log", "max_size_mb": 50, "backup_count": 5}, "syslog": {"enabled": false, "host": "localhost", "port": 514, "facility": "local0"}}}