#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 数据库测试质量门控管理器
基于docs/database/database-framework.md需求的质量门控系统

本模块提供：
- 质量门控定义和配置
- 自动化质量检查
- 阈值管理和验证
- 质量报告生成
- CI/CD集成支持
"""

import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
import yaml

from tests.framework.comprehensive_test_framework import (
    TestExecutionResult,
    QualityGate,
    TestPriority,
    TestStatus
)


class QualityGateType(Enum):
    """质量门控类型"""
    COVERAGE = "coverage"
    SUCCESS_RATE = "success_rate"
    PERFORMANCE = "performance"
    SECURITY = "security"
    RELIABILITY = "reliability"
    MAINTAINABILITY = "maintainability"
    CUSTOM = "custom"


class QualityGateOperator(Enum):
    """质量门控操作符"""
    GREATER_THAN = "gt"
    GREATER_EQUAL = "gte"
    LESS_THAN = "lt"
    LESS_EQUAL = "lte"
    EQUAL = "eq"
    NOT_EQUAL = "ne"
    IN = "in"
    NOT_IN = "not_in"


class QualityGateSeverity(Enum):
    """质量门控严重程度"""
    BLOCKER = "blocker"      # 阻塞发布
    CRITICAL = "critical"    # 严重问题
    MAJOR = "major"          # 主要问题
    MINOR = "minor"          # 次要问题
    INFO = "info"            # 信息提示


@dataclass
class QualityGateRule:
    """质量门控规则"""
    name: str
    description: str
    gate_type: QualityGateType
    metric_name: str
    operator: QualityGateOperator
    threshold: Union[float, int, str, List]
    severity: QualityGateSeverity = QualityGateSeverity.MAJOR
    enabled: bool = True
    blocking: bool = False
    priority: TestPriority = TestPriority.P1
    tags: List[str] = field(default_factory=list)
    custom_validator: Optional[Callable] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.error_message is None:
            self.error_message = f"{self.name} 质量门控检查失败"


@dataclass
class QualityGateResult:
    """质量门控检查结果"""
    rule_name: str
    passed: bool
    actual_value: Any
    threshold: Any
    operator: QualityGateOperator
    severity: QualityGateSeverity
    blocking: bool
    message: str
    execution_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class QualityGateReport:
    """质量门控报告"""
    total_rules: int = 0
    passed_rules: int = 0
    failed_rules: int = 0
    blocked_rules: int = 0
    success_rate: float = 0.0
    overall_status: str = "UNKNOWN"
    execution_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    results: List[QualityGateResult] = field(default_factory=list)
    summary: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if self.total_rules > 0:
            self.success_rate = (self.passed_rules / self.total_rules) * 100
            
        # 确定整体状态
        if self.blocked_rules > 0:
            self.overall_status = "BLOCKED"
        elif self.failed_rules > 0:
            self.overall_status = "FAILED"
        elif self.passed_rules == self.total_rules:
            self.overall_status = "PASSED"
        else:
            self.overall_status = "PARTIAL"


class DatabaseQualityGateManager:
    """数据库测试质量门控管理器"""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.logger = logging.getLogger(__name__)
        self.rules: Dict[str, QualityGateRule] = {}
        self.rule_groups: Dict[str, List[str]] = {}
        self.config_path = config_path
        
        # 加载配置
        if config_path and config_path.exists():
            self.load_config(config_path)
        else:
            self._setup_default_rules()
    
    def _setup_default_rules(self):
        """设置默认质量门控规则"""
        self.logger.info("设置默认质量门控规则")
        
        # P0级别规则（阻塞性）
        self.add_rule(QualityGateRule(
            name="p0_success_rate",
            description="P0测试成功率必须达到100%",
            gate_type=QualityGateType.SUCCESS_RATE,
            metric_name="p0_success_rate",
            operator=QualityGateOperator.EQUAL,
            threshold=100.0,
            severity=QualityGateSeverity.BLOCKER,
            blocking=True,
            priority=TestPriority.P0,
            tags=["core", "blocking"]
        ))
        
        # 整体成功率规则
        self.add_rule(QualityGateRule(
            name="overall_success_rate",
            description="整体测试成功率必须达到95%以上",
            gate_type=QualityGateType.SUCCESS_RATE,
            metric_name="overall_success_rate",
            operator=QualityGateOperator.GREATER_EQUAL,
            threshold=95.0,
            severity=QualityGateSeverity.CRITICAL,
            blocking=True,
            priority=TestPriority.P1,
            tags=["success_rate", "critical"]
        ))
        
        # 代码覆盖率规则
        self.add_rule(QualityGateRule(
            name="code_coverage",
            description="代码覆盖率必须达到80%以上",
            gate_type=QualityGateType.COVERAGE,
            metric_name="coverage_percentage",
            operator=QualityGateOperator.GREATER_EQUAL,
            threshold=80.0,
            severity=QualityGateSeverity.MAJOR,
            blocking=False,
            priority=TestPriority.P1,
            tags=["coverage", "quality"]
        ))
        
        # 性能规则
        self.add_rule(QualityGateRule(
            name="avg_response_time",
            description="平均响应时间不能超过100ms",
            gate_type=QualityGateType.PERFORMANCE,
            metric_name="avg_response_time",
            operator=QualityGateOperator.LESS_EQUAL,
            threshold=100.0,
            severity=QualityGateSeverity.MAJOR,
            blocking=False,
            priority=TestPriority.P2,
            tags=["performance", "response_time"]
        ))
        
        self.add_rule(QualityGateRule(
            name="p95_response_time",
            description="P95响应时间不能超过500ms",
            gate_type=QualityGateType.PERFORMANCE,
            metric_name="p95_response_time",
            operator=QualityGateOperator.LESS_EQUAL,
            threshold=500.0,
            severity=QualityGateSeverity.MAJOR,
            blocking=False,
            priority=TestPriority.P2,
            tags=["performance", "response_time"]
        ))
        
        # 内存使用规则
        self.add_rule(QualityGateRule(
            name="memory_usage",
            description="内存使用不能超过1GB",
            gate_type=QualityGateType.PERFORMANCE,
            metric_name="memory_usage_mb",
            operator=QualityGateOperator.LESS_EQUAL,
            threshold=1024.0,
            severity=QualityGateSeverity.MAJOR,
            blocking=False,
            priority=TestPriority.P2,
            tags=["performance", "memory"]
        ))
        
        # 可靠性规则
        self.add_rule(QualityGateRule(
            name="no_critical_failures",
            description="不能有严重失败的测试",
            gate_type=QualityGateType.RELIABILITY,
            metric_name="critical_failures",
            operator=QualityGateOperator.EQUAL,
            threshold=0,
            severity=QualityGateSeverity.CRITICAL,
            blocking=True,
            priority=TestPriority.P1,
            tags=["reliability", "failures"]
        ))
        
        # 安全规则
        self.add_rule(QualityGateRule(
            name="security_vulnerabilities",
            description="不能有高危安全漏洞",
            gate_type=QualityGateType.SECURITY,
            metric_name="high_security_issues",
            operator=QualityGateOperator.EQUAL,
            threshold=0,
            severity=QualityGateSeverity.BLOCKER,
            blocking=True,
            priority=TestPriority.P0,
            tags=["security", "vulnerabilities"]
        ))
        
        # 设置规则组
        self.rule_groups = {
            "blocking": ["p0_success_rate", "overall_success_rate", "no_critical_failures", "security_vulnerabilities"],
            "performance": ["avg_response_time", "p95_response_time", "memory_usage"],
            "quality": ["code_coverage", "overall_success_rate"],
            "security": ["security_vulnerabilities"],
            "p0": ["p0_success_rate", "security_vulnerabilities"],
            "p1": ["overall_success_rate", "code_coverage", "no_critical_failures"]
        }
    
    def add_rule(self, rule: QualityGateRule):
        """添加质量门控规则"""
        self.rules[rule.name] = rule
        self.logger.debug(f"添加质量门控规则: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除质量门控规则"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            self.logger.debug(f"移除质量门控规则: {rule_name}")
        else:
            self.logger.warning(f"规则不存在: {rule_name}")
    
    def enable_rule(self, rule_name: str):
        """启用质量门控规则"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = True
            self.logger.debug(f"启用质量门控规则: {rule_name}")
    
    def disable_rule(self, rule_name: str):
        """禁用质量门控规则"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = False
            self.logger.debug(f"禁用质量门控规则: {rule_name}")
    
    def add_rule_group(self, group_name: str, rule_names: List[str]):
        """添加规则组"""
        self.rule_groups[group_name] = rule_names
        self.logger.debug(f"添加规则组: {group_name}")
    
    def get_rules_by_group(self, group_name: str) -> List[QualityGateRule]:
        """获取指定组的规则"""
        if group_name not in self.rule_groups:
            return []
        
        return [self.rules[name] for name in self.rule_groups[group_name] 
                if name in self.rules and self.rules[name].enabled]
    
    def get_rules_by_priority(self, priority: TestPriority) -> List[QualityGateRule]:
        """获取指定优先级的规则"""
        return [rule for rule in self.rules.values() 
                if rule.priority == priority and rule.enabled]
    
    def get_blocking_rules(self) -> List[QualityGateRule]:
        """获取阻塞性规则"""
        return [rule for rule in self.rules.values() 
                if rule.blocking and rule.enabled]
    
    def _evaluate_rule(self, rule: QualityGateRule, metrics: Dict[str, Any]) -> QualityGateResult:
        """评估单个规则"""
        start_time = time.time()
        
        # 获取实际值
        actual_value = metrics.get(rule.metric_name)
        if actual_value is None:
            return QualityGateResult(
                rule_name=rule.name,
                passed=False,
                actual_value=None,
                threshold=rule.threshold,
                operator=rule.operator,
                severity=rule.severity,
                blocking=rule.blocking,
                message=f"指标 {rule.metric_name} 不存在",
                execution_time=time.time() - start_time
            )
        
        # 执行自定义验证器
        if rule.custom_validator:
            try:
                passed = rule.custom_validator(actual_value, rule.threshold)
                message = f"{rule.description} - 自定义验证" + ("通过" if passed else "失败")
            except Exception as e:
                passed = False
                message = f"自定义验证器执行失败: {e}"
        else:
            # 标准操作符验证
            passed, message = self._evaluate_operator(rule, actual_value)
        
        return QualityGateResult(
            rule_name=rule.name,
            passed=passed,
            actual_value=actual_value,
            threshold=rule.threshold,
            operator=rule.operator,
            severity=rule.severity,
            blocking=rule.blocking,
            message=message,
            execution_time=time.time() - start_time,
            details={
                "description": rule.description,
                "gate_type": rule.gate_type.value,
                "tags": rule.tags
            }
        )
    
    def _evaluate_operator(self, rule: QualityGateRule, actual_value: Any) -> tuple[bool, str]:
        """评估操作符"""
        try:
            if rule.operator == QualityGateOperator.GREATER_THAN:
                passed = actual_value > rule.threshold
            elif rule.operator == QualityGateOperator.GREATER_EQUAL:
                passed = actual_value >= rule.threshold
            elif rule.operator == QualityGateOperator.LESS_THAN:
                passed = actual_value < rule.threshold
            elif rule.operator == QualityGateOperator.LESS_EQUAL:
                passed = actual_value <= rule.threshold
            elif rule.operator == QualityGateOperator.EQUAL:
                passed = actual_value == rule.threshold
            elif rule.operator == QualityGateOperator.NOT_EQUAL:
                passed = actual_value != rule.threshold
            elif rule.operator == QualityGateOperator.IN:
                passed = actual_value in rule.threshold
            elif rule.operator == QualityGateOperator.NOT_IN:
                passed = actual_value not in rule.threshold
            else:
                passed = False
            
            status = "通过" if passed else "失败"
            message = f"{rule.description} - 实际值: {actual_value}, 阈值: {rule.threshold}, 结果: {status}"
            
            return passed, message
            
        except Exception as e:
            return False, f"操作符评估失败: {e}"
    
    def evaluate_rules(self, metrics: Dict[str, Any], 
                      rule_names: Optional[List[str]] = None,
                      rule_group: Optional[str] = None,
                      priority: Optional[TestPriority] = None) -> QualityGateReport:
        """评估质量门控规则"""
        start_time = time.time()
        
        # 确定要评估的规则
        if rule_names:
            rules_to_evaluate = [self.rules[name] for name in rule_names 
                               if name in self.rules and self.rules[name].enabled]
        elif rule_group:
            rules_to_evaluate = self.get_rules_by_group(rule_group)
        elif priority:
            rules_to_evaluate = self.get_rules_by_priority(priority)
        else:
            rules_to_evaluate = [rule for rule in self.rules.values() if rule.enabled]
        
        self.logger.info(f"开始评估 {len(rules_to_evaluate)} 个质量门控规则")
        
        # 评估规则
        results = []
        for rule in rules_to_evaluate:
            result = self._evaluate_rule(rule, metrics)
            results.append(result)
            
            log_level = logging.INFO if result.passed else logging.WARNING
            self.logger.log(log_level, f"规则 {rule.name}: {result.message}")
        
        # 统计结果
        total_rules = len(results)
        passed_rules = sum(1 for r in results if r.passed)
        failed_rules = total_rules - passed_rules
        blocked_rules = sum(1 for r in results if not r.passed and r.blocking)
        
        # 生成摘要
        summary = self._generate_summary(results, metrics)
        
        report = QualityGateReport(
            total_rules=total_rules,
            passed_rules=passed_rules,
            failed_rules=failed_rules,
            blocked_rules=blocked_rules,
            execution_time=time.time() - start_time,
            results=results,
            summary=summary
        )
        
        self.logger.info(f"质量门控评估完成: {report.overall_status}, "
                        f"通过率: {report.success_rate:.1f}%, "
                        f"阻塞规则: {blocked_rules}")
        
        return report
    
    def _generate_summary(self, results: List[QualityGateResult], 
                         metrics: Dict[str, Any]) -> Dict[str, Any]:
        """生成评估摘要"""
        summary = {
            "by_severity": {},
            "by_gate_type": {},
            "blocking_failures": [],
            "critical_failures": [],
            "recommendations": []
        }
        
        # 按严重程度统计
        for severity in QualityGateSeverity:
            severity_results = [r for r in results if r.severity == severity]
            summary["by_severity"][severity.value] = {
                "total": len(severity_results),
                "passed": sum(1 for r in severity_results if r.passed),
                "failed": sum(1 for r in severity_results if not r.passed)
            }
        
        # 按门控类型统计
        for gate_type in QualityGateType:
            type_results = [r for r in results 
                           if r.details.get("gate_type") == gate_type.value]
            if type_results:
                summary["by_gate_type"][gate_type.value] = {
                    "total": len(type_results),
                    "passed": sum(1 for r in type_results if r.passed),
                    "failed": sum(1 for r in type_results if not r.passed)
                }
        
        # 收集阻塞和严重失败
        for result in results:
            if not result.passed:
                if result.blocking:
                    summary["blocking_failures"].append({
                        "rule": result.rule_name,
                        "message": result.message,
                        "actual_value": result.actual_value,
                        "threshold": result.threshold
                    })
                
                if result.severity in [QualityGateSeverity.BLOCKER, QualityGateSeverity.CRITICAL]:
                    summary["critical_failures"].append({
                        "rule": result.rule_name,
                        "severity": result.severity.value,
                        "message": result.message
                    })
        
        # 生成建议
        summary["recommendations"] = self._generate_recommendations(results, metrics)
        
        return summary
    
    def _generate_recommendations(self, results: List[QualityGateResult], 
                                 metrics: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        failed_results = [r for r in results if not r.passed]
        
        # 成功率相关建议
        success_rate_failures = [r for r in failed_results 
                               if "success_rate" in r.rule_name.lower()]
        if success_rate_failures:
            recommendations.append("建议检查失败的测试用例，提高测试通过率")
        
        # 覆盖率相关建议
        coverage_failures = [r for r in failed_results 
                           if "coverage" in r.rule_name.lower()]
        if coverage_failures:
            recommendations.append("建议增加测试用例以提高代码覆盖率")
        
        # 性能相关建议
        performance_failures = [r for r in failed_results 
                              if r.details.get("gate_type") == "performance"]
        if performance_failures:
            recommendations.append("建议优化性能，检查慢查询和资源使用")
        
        # 安全相关建议
        security_failures = [r for r in failed_results 
                           if r.details.get("gate_type") == "security"]
        if security_failures:
            recommendations.append("建议立即修复安全漏洞，进行安全审计")
        
        # 阻塞性失败建议
        blocking_failures = [r for r in failed_results if r.blocking]
        if blocking_failures:
            recommendations.append("存在阻塞性问题，建议优先修复后再发布")
        
        return recommendations
    
    def evaluate_test_results(self, test_results: Dict[TestPriority, List[TestExecutionResult]]) -> QualityGateReport:
        """评估测试结果的质量门控"""
        # 从测试结果中提取指标
        metrics = self._extract_metrics_from_test_results(test_results)
        
        # 评估所有规则
        return self.evaluate_rules(metrics)
    
    def _extract_metrics_from_test_results(self, 
                                          test_results: Dict[TestPriority, List[TestExecutionResult]]) -> Dict[str, Any]:
        """从测试结果中提取指标"""
        metrics = {}
        
        # 整体统计
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        skipped_tests = 0
        total_duration = 0.0
        
        # 性能指标
        response_times = []
        memory_usage = []
        cpu_usage = []
        
        # 按优先级统计
        priority_stats = {}
        
        for priority, results in test_results.items():
            priority_total = 0
            priority_passed = 0
            priority_failed = 0
            
            for result in results:
                # 整体统计
                total_tests += result.tests_total
                passed_tests += result.tests_passed
                failed_tests += result.tests_failed
                skipped_tests += result.tests_skipped
                total_duration += result.execution_time
                
                # 优先级统计
                priority_total += result.tests_total
                priority_passed += result.tests_passed
                priority_failed += result.tests_failed
                
                # 性能指标
                if result.performance_metrics:
                    if 'response_time_ms' in result.performance_metrics:
                        response_times.append(result.performance_metrics['response_time_ms'])
                    if 'memory_usage_mb' in result.performance_metrics:
                        memory_usage.append(result.performance_metrics['memory_usage_mb'])
                    if 'cpu_usage_percent' in result.performance_metrics:
                        cpu_usage.append(result.performance_metrics['cpu_usage_percent'])
            
            # 计算优先级成功率
            if priority_total > 0:
                priority_success_rate = (priority_passed / priority_total) * 100
                priority_stats[priority.value] = {
                    'total': priority_total,
                    'passed': priority_passed,
                    'failed': priority_failed,
                    'success_rate': priority_success_rate
                }
                
                # 添加到指标中
                metrics[f"{priority.value.lower()}_success_rate"] = priority_success_rate
        
        # 计算整体指标
        if total_tests > 0:
            metrics['overall_success_rate'] = (passed_tests / total_tests) * 100
            metrics['overall_failure_rate'] = (failed_tests / total_tests) * 100
        else:
            metrics['overall_success_rate'] = 0.0
            metrics['overall_failure_rate'] = 0.0
        
        metrics['total_tests'] = total_tests
        metrics['passed_tests'] = passed_tests
        metrics['failed_tests'] = failed_tests
        metrics['skipped_tests'] = skipped_tests
        metrics['total_duration'] = total_duration
        
        # 性能指标
        if response_times:
            response_times_sorted = sorted(response_times)
            n = len(response_times_sorted)
            metrics['avg_response_time'] = sum(response_times) / len(response_times)
            metrics['max_response_time'] = max(response_times)
            metrics['min_response_time'] = min(response_times)
            metrics['p95_response_time'] = response_times_sorted[int(n * 0.95)] if n > 0 else 0
            metrics['p99_response_time'] = response_times_sorted[int(n * 0.99)] if n > 0 else 0
        
        if memory_usage:
            metrics['avg_memory_usage_mb'] = sum(memory_usage) / len(memory_usage)
            metrics['max_memory_usage_mb'] = max(memory_usage)
            metrics['memory_usage_mb'] = metrics['avg_memory_usage_mb']  # 兼容性
        
        if cpu_usage:
            metrics['avg_cpu_usage_percent'] = sum(cpu_usage) / len(cpu_usage)
            metrics['max_cpu_usage_percent'] = max(cpu_usage)
            metrics['cpu_usage_percent'] = metrics['avg_cpu_usage_percent']  # 兼容性
        
        # 模拟其他指标
        metrics['coverage_percentage'] = 85.0  # 模拟覆盖率
        metrics['critical_failures'] = failed_tests  # 严重失败数
        metrics['high_security_issues'] = 0  # 高危安全问题数
        
        # 优先级统计
        metrics['priority_stats'] = priority_stats
        
        return metrics
    
    def load_config(self, config_path: Path):
        """从配置文件加载规则"""
        self.logger.info(f"从配置文件加载质量门控规则: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.yaml' or config_path.suffix.lower() == '.yml':
                    config = yaml.safe_load(f)
                else:
                    config = json.load(f)
            
            # 清空现有规则
            self.rules.clear()
            self.rule_groups.clear()
            
            # 加载规则
            if 'rules' in config:
                for rule_config in config['rules']:
                    rule = QualityGateRule(
                        name=rule_config['name'],
                        description=rule_config['description'],
                        gate_type=QualityGateType(rule_config['gate_type']),
                        metric_name=rule_config['metric_name'],
                        operator=QualityGateOperator(rule_config['operator']),
                        threshold=rule_config['threshold'],
                        severity=QualityGateSeverity(rule_config.get('severity', 'major')),
                        enabled=rule_config.get('enabled', True),
                        blocking=rule_config.get('blocking', False),
                        priority=TestPriority(rule_config.get('priority', 'P1')),
                        tags=rule_config.get('tags', []),
                        error_message=rule_config.get('error_message')
                    )
                    self.add_rule(rule)
            
            # 加载规则组
            if 'rule_groups' in config:
                self.rule_groups = config['rule_groups']
            
            self.logger.info(f"成功加载 {len(self.rules)} 个规则和 {len(self.rule_groups)} 个规则组")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self._setup_default_rules()
    
    def save_config(self, config_path: Path):
        """保存配置到文件"""
        self.logger.info(f"保存质量门控配置到: {config_path}")
        
        config = {
            'rules': [],
            'rule_groups': self.rule_groups
        }
        
        # 序列化规则
        for rule in self.rules.values():
            rule_config = {
                'name': rule.name,
                'description': rule.description,
                'gate_type': rule.gate_type.value,
                'metric_name': rule.metric_name,
                'operator': rule.operator.value,
                'threshold': rule.threshold,
                'severity': rule.severity.value,
                'enabled': rule.enabled,
                'blocking': rule.blocking,
                'priority': rule.priority.value,
                'tags': rule.tags
            }
            if rule.error_message:
                rule_config['error_message'] = rule.error_message
            
            config['rules'].append(rule_config)
        
        # 保存文件
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.yaml' or config_path.suffix.lower() == '.yml':
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
                else:
                    json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置保存成功: {config_path}")
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
    
    def get_rule_summary(self) -> Dict[str, Any]:
        """获取规则摘要"""
        enabled_rules = [rule for rule in self.rules.values() if rule.enabled]
        
        summary = {
            'total_rules': len(self.rules),
            'enabled_rules': len(enabled_rules),
            'disabled_rules': len(self.rules) - len(enabled_rules),
            'blocking_rules': len([rule for rule in enabled_rules if rule.blocking]),
            'by_priority': {},
            'by_severity': {},
            'by_gate_type': {},
            'rule_groups': len(self.rule_groups)
        }
        
        # 按优先级统计
        for priority in TestPriority:
            count = len([rule for rule in enabled_rules if rule.priority == priority])
            if count > 0:
                summary['by_priority'][priority.value] = count
        
        # 按严重程度统计
        for severity in QualityGateSeverity:
            count = len([rule for rule in enabled_rules if rule.severity == severity])
            if count > 0:
                summary['by_severity'][severity.value] = count
        
        # 按门控类型统计
        for gate_type in QualityGateType:
            count = len([rule for rule in enabled_rules if rule.gate_type == gate_type])
            if count > 0:
                summary['by_gate_type'][gate_type.value] = count
        
        return summary


def create_quality_gate_manager(config_path: Optional[Path] = None) -> DatabaseQualityGateManager:
    """创建质量门控管理器的工厂函数"""
    return DatabaseQualityGateManager(config_path)


if __name__ == "__main__":
    # 示例用法
    import argparse
    
    parser = argparse.ArgumentParser(description="数据库测试质量门控管理器")
    parser.add_argument('--config', help='质量门控配置文件路径')
    parser.add_argument('--save-config', help='保存默认配置到指定路径')
    parser.add_argument('--test-metrics', help='测试指标JSON文件路径')
    parser.add_argument('--output', default='./quality_gate_report.json', help='报告输出路径')
    parser.add_argument('--group', help='只评估指定规则组')
    parser.add_argument('--priority', choices=['P0', 'P1', 'P2', 'P3'], help='只评估指定优先级')
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建质量门控管理器
    config_path = Path(args.config) if args.config else None
    manager = create_quality_gate_manager(config_path)
    
    # 保存默认配置
    if args.save_config:
        manager.save_config(Path(args.save_config))
        print(f"默认配置已保存到: {args.save_config}")
        exit(0)
    
    # 显示规则摘要
    summary = manager.get_rule_summary()
    print(f"\n质量门控规则摘要:")
    print(f"  总规则数: {summary['total_rules']}")
    print(f"  启用规则: {summary['enabled_rules']}")
    print(f"  阻塞规则: {summary['blocking_rules']}")
    print(f"  规则组数: {summary['rule_groups']}")
    
    # 评估测试指标
    if args.test_metrics:
        try:
            with open(args.test_metrics, 'r', encoding='utf-8') as f:
                metrics = json.load(f)
            
            # 确定评估参数
            priority = TestPriority(args.priority) if args.priority else None
            
            # 执行评估
            report = manager.evaluate_rules(
                metrics=metrics,
                rule_group=args.group,
                priority=priority
            )
            
            # 保存报告
            report_data = {
                'timestamp': report.timestamp.isoformat(),
                'overall_status': report.overall_status,
                'total_rules': report.total_rules,
                'passed_rules': report.passed_rules,
                'failed_rules': report.failed_rules,
                'blocked_rules': report.blocked_rules,
                'success_rate': report.success_rate,
                'execution_time': report.execution_time,
                'summary': report.summary,
                'results': [
                    {
                        'rule_name': r.rule_name,
                        'passed': r.passed,
                        'actual_value': r.actual_value,
                        'threshold': r.threshold,
                        'operator': r.operator.value,
                        'severity': r.severity.value,
                        'blocking': r.blocking,
                        'message': r.message,
                        'execution_time': r.execution_time
                    }
                    for r in report.results
                ]
            }
            
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n质量门控评估完成:")
            print(f"  整体状态: {report.overall_status}")
            print(f"  通过率: {report.success_rate:.1f}%")
            print(f"  阻塞规则失败: {report.blocked_rules}")
            print(f"  报告已保存到: {args.output}")
            
            # 显示失败的规则
            failed_results = [r for r in report.results if not r.passed]
            if failed_results:
                print(f"\n失败的规则:")
                for result in failed_results:
                    print(f"  - {result.rule_name}: {result.message}")
            
        except Exception as e:
            print(f"评估测试指标时出错: {e}")
    
    else:
        print("\n使用 --test-metrics 参数指定测试指标文件进行评估")