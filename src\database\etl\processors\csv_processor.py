__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""CSV data processor module.

This module provides functionality for reading and processing CSV files
into pandas DataFrames with async support for efficient file operations.
"""

import asyncio
import logging
import os
from io import String<PERSON>
from pathlib import Path
from typing import Any, Dict, Optional, Union

import aiofiles
import pandas as pd

# Handle relative imports with fallback
try:
    from ...exceptions import DatabaseError, FileOperationError, ValidationError
    from ...utils.validators import InputValidator
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    # Add parent directories to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from exceptions import DatabaseError, FileOperationError, ValidationError
    from utils.validators import InputValidator

# Import health monitoring
try:
    from src.monitoring.csv_health_monitor import start_csv_processing, end_csv_processing
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("CSV health monitoring not available - monitoring disabled")

logger = logging.getLogger(__name__)


async def read_csv_to_dataframe(
    file_path: Union[str, Path],
    encoding: str = "utf-8",
    delimiter: str = ",",
    header: Union[int, str, None] = "infer",
    dtype: Optional[Dict[str, Any]] = None,
    na_values: Optional[list] = None,
    skip_blank_lines: bool = True,
    comment: Optional[str] = None,
    nrows: Optional[int] = None,
    skiprows: Optional[Union[int, list]] = None,
    **kwargs,
) -> pd.DataFrame:
    """Read CSV file asynchronously into a pandas DataFrame.

    This function provides async file reading capabilities for CSV files,
    supporting various CSV formats and configurations.

    Args:
        file_path: Path to the CSV file
        encoding: File encoding (default: 'utf-8')
        delimiter: Field delimiter (default: ',')
        header: Row number(s) to use as column names, or 'infer' to detect
        dtype: Data type for columns
        na_values: Additional strings to recognize as NA/NaN
        skip_blank_lines: Skip blank lines rather than interpreting as NaN
        comment: Character(s) to split comments
        nrows: Number of rows to read
        skiprows: Line numbers to skip or number of lines to skip
        **kwargs: Additional arguments passed to pandas.read_csv

    Returns:
        pandas.DataFrame: The loaded data

    Raises:
        ValidationError: If file path is invalid
        FileOperationError: If file cannot be read
        DatabaseError: If CSV parsing fails
    """
    # Validate input parameters
    validator = InputValidator()

    # Convert to Path object for easier handling
    file_path = Path(file_path)

    # Validate file path
    if not file_path.exists():
        raise FileOperationError(f"CSV file not found: {file_path}")

    if not file_path.is_file():
        raise ValidationError(f"Path is not a file: {file_path}")

    # Get file size for strategy selection
    file_size = file_path.stat().st_size
    
    # Start health monitoring
    process_id = None
    strategy_used = "unknown"
    if MONITORING_AVAILABLE:
        process_id = start_csv_processing(str(file_path), file_size)

    # Check file extension
    if file_path.suffix.lower() not in [".csv", ".tsv", ".txt"]:
        logger.warning(f"File extension '{file_path.suffix}' may not be a CSV file")

    # Adjust delimiter for TSV files
    if file_path.suffix.lower() == ".tsv" and delimiter == ",":
        delimiter = "\t"
        logger.info("Detected TSV file, using tab delimiter")

    try:
        logger.info(f"Reading CSV file: {file_path}")

        # Read file content with multiple fallback strategies to prevent hanging
        content = None
        file_size = file_path.stat().st_size
        
        # Determine optimal timeout based on file size (minimum 10s, max 120s)
        timeout = min(max(10.0, file_size / (1024 * 1024) * 2), 120.0)
        
        logger.debug(f"File size: {file_size} bytes, using timeout: {timeout}s")
        
        # Strategy 1: Async reading with adaptive timeout
        try:
            strategy_used = "async"
            async with aiofiles.open(file_path, mode="r", encoding=encoding, buffering=8192) as file:
                # For large files, read in chunks to prevent memory issues
                if file_size > 50 * 1024 * 1024:  # 50MB threshold
                    logger.info(f"Large file detected ({file_size} bytes), using chunked reading")
                    strategy_used = "async_chunked"
                    chunks = []
                    chunk_size = 1024 * 1024  # 1MB chunks
                    
                    while True:
                        chunk = await asyncio.wait_for(file.read(chunk_size), timeout=30.0)
                        if not chunk:
                            break
                        chunks.append(chunk)
                        
                        # Prevent excessive memory usage
                        if len(chunks) > 100:  # Max ~100MB in memory
                            raise FileOperationError(f"File too large for async processing: {file_path}")
                    
                    content = ''.join(chunks)
                else:
                    # Small to medium files - read all at once
                    content = await asyncio.wait_for(file.read(), timeout=timeout)
                    
        except asyncio.TimeoutError:
            logger.warning(f"Async read timeout for {file_path}, attempting synchronous fallback")
            content = None
        except (OSError, PermissionError, UnicodeDecodeError) as e:
            logger.warning(f"Async read failed for {file_path}: {e}, attempting synchronous fallback")
            content = None
        except Exception as e:
            logger.warning(f"Unexpected async read error for {file_path}: {e}, attempting synchronous fallback")
            content = None
            
        # Strategy 2: Synchronous fallback for Windows temp file issues
        if content is None:
            try:
                logger.info(f"Using synchronous fallback for {file_path}")
                strategy_used = "sync_fallback"
                
                # Windows-specific: Normalize path to handle 8.3 format
                if file_path.name.startswith('tmp') and '~' in str(file_path):
                    # Try to resolve the full path for Windows temp files
                    try:
                        file_path = file_path.resolve()
                        logger.debug(f"Resolved Windows temp path to: {file_path}")
                    except Exception:
                        pass  # Continue with original path
                
                # Use synchronous reading with explicit encoding handling
                with open(file_path, 'r', encoding=encoding, buffering=8192) as f:
                    if file_size > 100 * 1024 * 1024:  # 100MB threshold for sync
                        raise FileOperationError(f"File too large for synchronous processing: {file_path}")
                    content = f.read()
                    
            except UnicodeDecodeError:
                # Try alternative encodings for problematic files
                strategy_used = "encoding_fallback"
                for alt_encoding in ['utf-8-sig', 'latin1', 'cp1252']:
                    try:
                        logger.info(f"Trying alternative encoding {alt_encoding} for {file_path}")
                        with open(file_path, 'r', encoding=alt_encoding, buffering=8192) as f:
                            content = f.read()
                        encoding = alt_encoding  # Update encoding for logging
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise FileOperationError(f"Could not decode file with any supported encoding: {file_path}")
            except Exception as e:
                raise FileOperationError(f"Failed to read CSV file {file_path}: {e}")
        
        if content is None:
            raise FileOperationError(f"All reading strategies failed for file: {file_path}")
            
        logger.debug(f"Successfully read {len(content)} characters from {file_path} using encoding {encoding}")

        # Parse CSV content using pandas with proper resource management
        csv_buffer = StringIO(content)
        df = None
        
    except Exception as file_read_error:
        # Handle any errors from the file reading try block
        error_msg = f"Failed to read CSV file {file_path}: {file_read_error}"
        logger.error(error_msg)
        if MONITORING_AVAILABLE and process_id:
            end_csv_processing(process_id, success=False, error_message=error_msg, strategy_used=strategy_used)
        raise FileOperationError(error_msg) from file_read_error
        
    try:
        
        # Prepare pandas read_csv arguments
        read_args = {
            "delimiter": delimiter,
            "header": header,
            "dtype": dtype,
            "na_values": na_values,
            "skip_blank_lines": skip_blank_lines,
            "comment": comment,
            "nrows": nrows,
            "skiprows": skiprows,
            **kwargs,
        }

        # Remove None values from arguments
        read_args = {k: v for k, v in read_args.items() if v is not None}

        # Read CSV into DataFrame with error handling
        try:
            df = pd.read_csv(csv_buffer, **read_args)
        except pd.errors.EmptyDataError:
            logger.warning(f"CSV file {file_path} is empty or contains no data")
            # Return empty DataFrame with proper structure
            df = pd.DataFrame()
        except pd.errors.ParserError as e:
            # Try with different parsing options for problematic files
            logger.warning(f"CSV parsing failed with standard options: {e}")
            logger.info("Attempting to parse with relaxed options")
            
            # Reset buffer position
            csv_buffer.seek(0)
            
            # Try with more lenient parsing
            relaxed_args = read_args.copy()
            relaxed_args.update({
                'on_bad_lines': 'skip',  # Updated parameter name
                'quoting': 3,  # QUOTE_NONE
                'engine': 'python'  # More flexible parser
            })
            
            try:
                df = pd.read_csv(csv_buffer, **relaxed_args)
                logger.info("Successfully parsed CSV with relaxed options")
            except Exception as relaxed_error:
                raise DatabaseError(f"Failed to parse CSV file {file_path}: {relaxed_error}")
        
        # Clear content from memory immediately after parsing
        content = None
        
        logger.info(
            f"Successfully loaded CSV with {len(df)} rows and {len(df.columns)} columns"
        )

        # Basic validation
        if df.empty:
            logger.warning("Loaded DataFrame is empty")
        else:
            # Log column information for non-empty DataFrames
            logger.debug(f"Columns: {list(df.columns)}")
            logger.debug(f"Data types: {df.dtypes.to_dict()}")
            
            # Memory usage logging for large DataFrames
            memory_usage = df.memory_usage(deep=True).sum()
            if memory_usage > 10 * 1024 * 1024:  # 10MB threshold
                logger.info(f"DataFrame memory usage: {memory_usage / (1024*1024):.2f} MB")
        
        # End health monitoring - success
        if MONITORING_AVAILABLE and process_id:
            end_csv_processing(process_id, success=True, strategy_used=strategy_used)
        
        return df
        
    except Exception as csv_parse_error:
        # Handle any parsing errors from the CSV processing try block
        error_msg = f"Failed to parse CSV content: {csv_parse_error}"
        logger.error(error_msg)
        if MONITORING_AVAILABLE and process_id:
            end_csv_processing(process_id, success=False, error_message=error_msg, strategy_used=strategy_used)
        raise DatabaseError(error_msg) from csv_parse_error

    except UnicodeDecodeError as e:
        error_msg = f"Failed to decode file with encoding '{encoding}': {e}"
        logger.error(error_msg)
        if MONITORING_AVAILABLE and process_id:
            end_csv_processing(process_id, success=False, error_message=error_msg, strategy_used=strategy_used)
        raise FileOperationError(error_msg) from e

    except pd.errors.EmptyDataError as e:
        error_msg = f"CSV file is empty or contains no data: {e}"
        logger.error(error_msg)
        if MONITORING_AVAILABLE and process_id:
            end_csv_processing(process_id, success=False, error_message=error_msg, strategy_used=strategy_used)
        raise DatabaseError(error_msg) from e

    except pd.errors.ParserError as e:
        error_msg = f"Failed to parse CSV file: {e}"
        logger.error(error_msg)
        if MONITORING_AVAILABLE and process_id:
            end_csv_processing(process_id, success=False, error_message=error_msg, strategy_used=strategy_used)
        raise DatabaseError(error_msg) from e

    except FileOperationError as e:
        # Re-raise FileOperationError as-is
        if MONITORING_AVAILABLE and process_id:
            end_csv_processing(process_id, success=False, error_message=str(e), strategy_used=strategy_used)
        raise
        
    except DatabaseError as e:
        # Re-raise DatabaseError as-is
        if MONITORING_AVAILABLE and process_id:
            end_csv_processing(process_id, success=False, error_message=str(e), strategy_used=strategy_used)
        raise
        
    except Exception as e:
        error_msg = f"Unexpected error reading CSV file '{file_path}': {e}"
        logger.error(error_msg)
        if MONITORING_AVAILABLE and process_id:
            end_csv_processing(process_id, success=False, error_message=error_msg, strategy_used=strategy_used)
        raise DatabaseError(error_msg) from e
        
    finally:
        # Final cleanup to prevent memory leaks
        try:
            if 'content' in locals():
                content = None
            if 'csv_buffer' in locals() and csv_buffer is not None:
                csv_buffer.close()
        except Exception:
            pass  # Ignore cleanup errors


async def validate_csv_structure(
    file_path: Union[str, Path],
    expected_columns: Optional[list] = None,
    min_rows: int = 0,
    max_rows: Optional[int] = None,
    encoding: str = "utf-8",
) -> Dict[str, Any]:
    """Validate CSV file structure without loading the entire file.

    Args:
        file_path: Path to the CSV file
        expected_columns: List of expected column names
        min_rows: Minimum number of rows required
        max_rows: Maximum number of rows allowed
        encoding: File encoding

    Returns:
        Dict containing validation results

    Raises:
        ValidationError: If validation fails
        FileOperationError: If file cannot be read
    """
    file_path = Path(file_path)

    if not file_path.exists():
        raise FileOperationError(f"CSV file not found: {file_path}")

    try:
        # Read only the first few rows to check structure with timeout
        sample_df = await asyncio.wait_for(
            read_csv_to_dataframe(
                file_path, encoding=encoding, nrows=min(100, max_rows) if max_rows else 100
            ),
            timeout=10.0  # 10 second timeout for validation
        )

        validation_result = {
            "valid": True,
            "columns": list(sample_df.columns),
            "column_count": len(sample_df.columns),
            "sample_row_count": len(sample_df),
            "data_types": sample_df.dtypes.to_dict(),
            "issues": [],
        }

        # Check expected columns
        if expected_columns:
            missing_columns = set(expected_columns) - set(sample_df.columns)
            extra_columns = set(sample_df.columns) - set(expected_columns)

            if missing_columns:
                validation_result["issues"].append(
                    f"Missing columns: {list(missing_columns)}"
                )
                validation_result["valid"] = False

            if extra_columns:
                validation_result["issues"].append(
                    f"Extra columns: {list(extra_columns)}"
                )

        # Check minimum rows (estimate from sample)
        if len(sample_df) < min_rows:
            # Try to get actual row count with timeout
            try:
                full_df = await asyncio.wait_for(
                    read_csv_to_dataframe(file_path, encoding=encoding),
                    timeout=20.0  # 20 second timeout for full file read
                )
                actual_rows = len(full_df)
                validation_result["estimated_total_rows"] = actual_rows

                if actual_rows < min_rows:
                    validation_result["issues"].append(
                        f"Insufficient rows: {actual_rows} < {min_rows}"
                    )
                    validation_result["valid"] = False
            except asyncio.TimeoutError:
                validation_result["issues"].append(
                    "Could not determine total row count: File reading timeout"
                )
            except Exception as e:
                validation_result["issues"].append(
                    f"Could not determine total row count: {e}"
                )

        return validation_result

    except Exception as e:
        logger.error(f"CSV validation failed for '{file_path}': {e}")
        raise ValidationError(f"CSV validation failed: {e}") from e


def detect_csv_delimiter(file_path: Union[str, Path], encoding: str = "utf-8") -> str:
    """Detect the delimiter used in a CSV file.

    Args:
        file_path: Path to the CSV file
        encoding: File encoding

    Returns:
        Detected delimiter character

    Raises:
        FileOperationError: If file cannot be read
    """
    import csv

    file_path = Path(file_path)

    if not file_path.exists():
        raise FileOperationError(f"CSV file not found: {file_path}")

    try:
        # Use a smaller sample size to prevent hanging on large files
        with open(file_path, "r", encoding=encoding) as file:
            # Read only a small sample to prevent hanging
            sample = file.read(512)  # Reduced from 1024 to 512 bytes

        # Ensure we have some content to analyze
        if not sample.strip():
            logger.warning(f"File '{file_path}' appears to be empty")
            return ","

        # Use csv.Sniffer to detect delimiter
        sniffer = csv.Sniffer()
        delimiter = sniffer.sniff(sample).delimiter

        logger.info(f"Detected delimiter: '{delimiter}' for file: {file_path}")
        return delimiter

    except Exception as e:
        logger.warning(f"Could not detect delimiter for '{file_path}': {e}")
        # Return default delimiter
        return ","
