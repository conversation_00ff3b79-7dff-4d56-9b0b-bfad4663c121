#!/usr/bin/env python3
"""测试数据生成器

该模块提供各种类型的测试数据生成功能，支持数据库测试、API测试、性能测试等场景。

主要功能:
- 生成随机测试数据
- 生成特定格式的数据（JSON、CSV、SQL等）
- 生成边界值和异常数据
- 生成大量数据用于性能测试
- 支持数据模板和自定义规则
"""

import json
import random
import string
import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import csv
import faker
import pandas as pd
import numpy as np


class DataType(Enum):
    """数据类型枚举"""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    DECIMAL = "decimal"
    BOOLEAN = "boolean"
    DATE = "date"
    DATETIME = "datetime"
    TIME = "time"
    UUID = "uuid"
    EMAIL = "email"
    PHONE = "phone"
    URL = "url"
    JSON = "json"
    LIST = "list"
    DICT = "dict"
    BINARY = "binary"
    NULL = "null"


class DataCategory(Enum):
    """数据类别枚举"""
    PERSONAL = "personal"  # 个人信息
    BUSINESS = "business"  # 商业数据
    TECHNICAL = "technical"  # 技术数据
    FINANCIAL = "financial"  # 金融数据
    GEOGRAPHIC = "geographic"  # 地理数据
    TELECOM = "telecom"  # 电信数据
    RANDOM = "random"  # 随机数据


@dataclass
class DataRule:
    """数据生成规则"""
    data_type: DataType
    min_value: Optional[Union[int, float, str]] = None
    max_value: Optional[Union[int, float, str]] = None
    length: Optional[int] = None
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    pattern: Optional[str] = None
    choices: Optional[List[Any]] = None
    nullable: bool = False
    null_probability: float = 0.1
    custom_generator: Optional[Callable] = None
    format_string: Optional[str] = None
    locale: str = "zh_CN"


@dataclass
class DataTemplate:
    """数据模板"""
    name: str
    description: str
    fields: Dict[str, DataRule] = field(default_factory=dict)
    relationships: Dict[str, str] = field(default_factory=dict)
    constraints: List[str] = field(default_factory=list)
    category: DataCategory = DataCategory.RANDOM


class EnhancedTestDataGenerator:
    """增强的测试数据生成器"""
    
    def __init__(self, locale: str = "zh_CN", seed: Optional[int] = None):
        """初始化数据生成器
        
        Args:
            locale: 本地化设置
            seed: 随机种子，用于生成可重复的数据
        """
        self.locale = locale
        self.fake = faker.Faker(locale)
        
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
            faker.Faker.seed(seed)
        
        self.templates: Dict[str, DataTemplate] = {}
        self._register_default_templates()
    
    def _register_default_templates(self):
        """注册默认数据模板"""
        # 用户信息模板
        user_template = DataTemplate(
            name="user",
            description="用户基本信息",
            category=DataCategory.PERSONAL,
            fields={
                "id": DataRule(DataType.INTEGER, min_value=1, max_value=999999),
                "username": DataRule(DataType.STRING, min_length=3, max_length=20),
                "email": DataRule(DataType.EMAIL),
                "phone": DataRule(DataType.PHONE),
                "name": DataRule(DataType.STRING),
                "age": DataRule(DataType.INTEGER, min_value=18, max_value=80),
                "gender": DataRule(DataType.STRING, choices=["男", "女", "其他"]),
                "created_at": DataRule(DataType.DATETIME),
                "is_active": DataRule(DataType.BOOLEAN),
                "profile": DataRule(DataType.JSON)
            }
        )
        self.register_template(user_template)
        
        # 电信数据模板
        telecom_template = DataTemplate(
            name="telecom_record",
            description="电信通话记录",
            category=DataCategory.TELECOM,
            fields={
                "call_id": DataRule(DataType.UUID),
                "caller_number": DataRule(DataType.PHONE),
                "callee_number": DataRule(DataType.PHONE),
                "call_start_time": DataRule(DataType.DATETIME),
                "call_duration": DataRule(DataType.INTEGER, min_value=1, max_value=7200),
                "call_type": DataRule(DataType.STRING, choices=["voice", "video", "conference"]),
                "call_status": DataRule(DataType.STRING, choices=["completed", "failed", "busy", "no_answer"]),
                "data_usage_mb": DataRule(DataType.FLOAT, min_value=0.1, max_value=1024.0),
                "location_lat": DataRule(DataType.FLOAT, min_value=-90.0, max_value=90.0),
                "location_lng": DataRule(DataType.FLOAT, min_value=-180.0, max_value=180.0),
                "cell_tower_id": DataRule(DataType.STRING, pattern=r"TOWER_\d{6}"),
                "signal_strength": DataRule(DataType.INTEGER, min_value=-120, max_value=-30)
            }
        )
        self.register_template(telecom_template)
        
        # 数据库测试模板
        db_test_template = DataTemplate(
            name="database_test",
            description="数据库测试数据",
            category=DataCategory.TECHNICAL,
            fields={
                "id": DataRule(DataType.INTEGER, min_value=1),
                "varchar_field": DataRule(DataType.STRING, max_length=255),
                "text_field": DataRule(DataType.STRING, min_length=100, max_length=1000),
                "int_field": DataRule(DataType.INTEGER),
                "float_field": DataRule(DataType.FLOAT),
                "decimal_field": DataRule(DataType.DECIMAL),
                "bool_field": DataRule(DataType.BOOLEAN),
                "date_field": DataRule(DataType.DATE),
                "datetime_field": DataRule(DataType.DATETIME),
                "json_field": DataRule(DataType.JSON, nullable=True),
                "nullable_field": DataRule(DataType.STRING, nullable=True, null_probability=0.3)
            }
        )
        self.register_template(db_test_template)
        
        # 性能测试模板
        performance_template = DataTemplate(
            name="performance_test",
            description="性能测试数据",
            category=DataCategory.TECHNICAL,
            fields={
                "id": DataRule(DataType.INTEGER, min_value=1),
                "large_text": DataRule(DataType.STRING, min_length=1000, max_length=10000),
                "binary_data": DataRule(DataType.BINARY, min_length=1024, max_length=1048576),
                "json_data": DataRule(DataType.JSON),
                "timestamp": DataRule(DataType.DATETIME),
                "random_number": DataRule(DataType.FLOAT)
            }
        )
        self.register_template(performance_template)
    
    def register_template(self, template: DataTemplate):
        """注册数据模板
        
        Args:
            template: 数据模板
        """
        self.templates[template.name] = template
    
    def get_template(self, name: str) -> Optional[DataTemplate]:
        """获取数据模板
        
        Args:
            name: 模板名称
            
        Returns:
            数据模板，如果不存在则返回None
        """
        return self.templates.get(name)
    
    def list_templates(self) -> List[str]:
        """列出所有模板名称
        
        Returns:
            模板名称列表
        """
        return list(self.templates.keys())
    
    def generate_value(self, rule: DataRule) -> Any:
        """根据规则生成单个值
        
        Args:
            rule: 数据生成规则
            
        Returns:
            生成的值
        """
        # 检查是否应该生成NULL值
        if rule.nullable and random.random() < rule.null_probability:
            return None
        
        # 如果有自定义生成器，使用自定义生成器
        if rule.custom_generator:
            return rule.custom_generator()
        
        # 如果有选择列表，从中随机选择
        if rule.choices:
            return random.choice(rule.choices)
        
        # 根据数据类型生成值
        if rule.data_type == DataType.STRING:
            return self._generate_string(rule)
        elif rule.data_type == DataType.INTEGER:
            return self._generate_integer(rule)
        elif rule.data_type == DataType.FLOAT:
            return self._generate_float(rule)
        elif rule.data_type == DataType.DECIMAL:
            return self._generate_decimal(rule)
        elif rule.data_type == DataType.BOOLEAN:
            return random.choice([True, False])
        elif rule.data_type == DataType.DATE:
            return self._generate_date(rule)
        elif rule.data_type == DataType.DATETIME:
            return self._generate_datetime(rule)
        elif rule.data_type == DataType.TIME:
            return self._generate_time(rule)
        elif rule.data_type == DataType.UUID:
            return str(uuid.uuid4())
        elif rule.data_type == DataType.EMAIL:
            return self.fake.email()
        elif rule.data_type == DataType.PHONE:
            return self.fake.phone_number()
        elif rule.data_type == DataType.URL:
            return self.fake.url()
        elif rule.data_type == DataType.JSON:
            return self._generate_json(rule)
        elif rule.data_type == DataType.LIST:
            return self._generate_list(rule)
        elif rule.data_type == DataType.DICT:
            return self._generate_dict(rule)
        elif rule.data_type == DataType.BINARY:
            return self._generate_binary(rule)
        elif rule.data_type == DataType.NULL:
            return None
        else:
            raise ValueError(f"不支持的数据类型: {rule.data_type}")
    
    def _generate_string(self, rule: DataRule) -> str:
        """生成字符串"""
        if rule.pattern:
            # 如果有模式，使用faker生成
            return self.fake.bothify(rule.pattern)
        
        # 确定长度
        if rule.length:
            length = rule.length
        elif rule.min_length and rule.max_length:
            length = random.randint(rule.min_length, rule.max_length)
        elif rule.min_length:
            length = random.randint(rule.min_length, rule.min_length + 50)
        elif rule.max_length:
            length = random.randint(1, rule.max_length)
        else:
            length = random.randint(5, 20)
        
        # 生成字符串
        if rule.format_string == "name":
            return self.fake.name()
        elif rule.format_string == "address":
            return self.fake.address()
        elif rule.format_string == "company":
            return self.fake.company()
        elif rule.format_string == "text":
            return self.fake.text(max_nb_chars=length)
        else:
            return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    def _generate_integer(self, rule: DataRule) -> int:
        """生成整数"""
        min_val = rule.min_value if rule.min_value is not None else -2147483648
        max_val = rule.max_value if rule.max_value is not None else 2147483647
        return random.randint(int(min_val), int(max_val))
    
    def _generate_float(self, rule: DataRule) -> float:
        """生成浮点数"""
        min_val = rule.min_value if rule.min_value is not None else -1000000.0
        max_val = rule.max_value if rule.max_value is not None else 1000000.0
        return random.uniform(float(min_val), float(max_val))
    
    def _generate_decimal(self, rule: DataRule) -> Decimal:
        """生成Decimal"""
        min_val = rule.min_value if rule.min_value is not None else -1000000.0
        max_val = rule.max_value if rule.max_value is not None else 1000000.0
        value = random.uniform(float(min_val), float(max_val))
        return Decimal(f"{value:.2f}")
    
    def _generate_date(self, rule: DataRule) -> datetime:
        """生成日期"""
        start_date = datetime(2020, 1, 1)
        end_date = datetime.now()
        
        if rule.min_value:
            start_date = datetime.fromisoformat(str(rule.min_value))
        if rule.max_value:
            end_date = datetime.fromisoformat(str(rule.max_value))
        
        return self.fake.date_between(start_date=start_date.date(), end_date=end_date.date())
    
    def _generate_datetime(self, rule: DataRule) -> datetime:
        """生成日期时间"""
        start_date = datetime(2020, 1, 1)
        end_date = datetime.now()
        
        if rule.min_value:
            start_date = datetime.fromisoformat(str(rule.min_value))
        if rule.max_value:
            end_date = datetime.fromisoformat(str(rule.max_value))
        
        return self.fake.date_time_between(start_date=start_date, end_date=end_date)
    
    def _generate_time(self, rule: DataRule) -> str:
        """生成时间"""
        return self.fake.time()
    
    def _generate_json(self, rule: DataRule) -> Dict[str, Any]:
        """生成JSON对象"""
        # 生成简单的JSON对象
        return {
            "id": random.randint(1, 1000),
            "name": self.fake.name(),
            "value": random.uniform(0, 100),
            "active": random.choice([True, False]),
            "timestamp": datetime.now().isoformat(),
            "tags": [self.fake.word() for _ in range(random.randint(1, 5))]
        }
    
    def _generate_list(self, rule: DataRule) -> List[Any]:
        """生成列表"""
        length = rule.length or random.randint(1, 10)
        return [self.fake.word() for _ in range(length)]
    
    def _generate_dict(self, rule: DataRule) -> Dict[str, Any]:
        """生成字典"""
        size = rule.length or random.randint(1, 5)
        return {self.fake.word(): self.fake.word() for _ in range(size)}
    
    def _generate_binary(self, rule: DataRule) -> bytes:
        """生成二进制数据"""
        length = rule.length or random.randint(rule.min_length or 10, rule.max_length or 100)
        return bytes(random.getrandbits(8) for _ in range(length))
    
    def generate_record(self, template_name: str, **overrides) -> Dict[str, Any]:
        """根据模板生成单条记录
        
        Args:
            template_name: 模板名称
            **overrides: 字段值覆盖
            
        Returns:
            生成的记录
        """
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"模板 '{template_name}' 不存在")
        
        record = {}
        for field_name, rule in template.fields.items():
            if field_name in overrides:
                record[field_name] = overrides[field_name]
            else:
                record[field_name] = self.generate_value(rule)
        
        return record
    
    def generate_records(self, template_name: str, count: int, **overrides) -> List[Dict[str, Any]]:
        """根据模板生成多条记录
        
        Args:
            template_name: 模板名称
            count: 记录数量
            **overrides: 字段值覆盖
            
        Returns:
            生成的记录列表
        """
        return [self.generate_record(template_name, **overrides) for _ in range(count)]
    
    def generate_dataframe(self, template_name: str, count: int, **overrides) -> pd.DataFrame:
        """根据模板生成DataFrame
        
        Args:
            template_name: 模板名称
            count: 记录数量
            **overrides: 字段值覆盖
            
        Returns:
            生成的DataFrame
        """
        records = self.generate_records(template_name, count, **overrides)
        return pd.DataFrame(records)
    
    def generate_csv_file(self, template_name: str, count: int, output_file: str, **overrides):
        """生成CSV文件
        
        Args:
            template_name: 模板名称
            count: 记录数量
            output_file: 输出文件路径
            **overrides: 字段值覆盖
        """
        df = self.generate_dataframe(template_name, count, **overrides)
        df.to_csv(output_file, index=False, encoding='utf-8')
    
    def generate_json_file(self, template_name: str, count: int, output_file: str, **overrides):
        """生成JSON文件
        
        Args:
            template_name: 模板名称
            count: 记录数量
            output_file: 输出文件路径
            **overrides: 字段值覆盖
        """
        records = self.generate_records(template_name, count, **overrides)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2, default=str)
    
    def generate_sql_insert_file(self, template_name: str, count: int, table_name: str, 
                               output_file: str, **overrides):
        """生成SQL INSERT语句文件
        
        Args:
            template_name: 模板名称
            count: 记录数量
            table_name: 表名
            output_file: 输出文件路径
            **overrides: 字段值覆盖
        """
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"模板 '{template_name}' 不存在")
        
        records = self.generate_records(template_name, count, **overrides)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            # 写入表结构注释
            f.write(f"-- 表: {table_name}\n")
            f.write(f"-- 模板: {template_name}\n")
            f.write(f"-- 记录数: {count}\n\n")
            
            # 生成INSERT语句
            if records:
                columns = list(records[0].keys())
                f.write(f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES\n")
                
                for i, record in enumerate(records):
                    values = []
                    for col in columns:
                        value = record[col]
                        if value is None:
                            values.append('NULL')
                        elif isinstance(value, str):
                            # 转义单引号
                            escaped_value = value.replace("'", "''")
                            values.append(f"'{escaped_value}'")
                        elif isinstance(value, (dict, list)):
                            # JSON数据
                            json_str = json.dumps(value, ensure_ascii=False)
                            escaped_json = json_str.replace("'", "''")
                            values.append(f"'{escaped_json}'")
                        elif isinstance(value, datetime):
                            values.append(f"'{value.isoformat()}'")
                        elif isinstance(value, bool):
                            values.append('TRUE' if value else 'FALSE')
                        else:
                            values.append(str(value))
                    
                    line_end = "," if i < len(records) - 1 else ";"
                    f.write(f"  ({', '.join(values)}){line_end}\n")
    
    def generate_boundary_values(self, rule: DataRule) -> List[Any]:
        """生成边界值测试数据
        
        Args:
            rule: 数据生成规则
            
        Returns:
            边界值列表
        """
        boundary_values = []
        
        if rule.data_type == DataType.INTEGER:
            if rule.min_value is not None:
                boundary_values.extend([rule.min_value - 1, rule.min_value, rule.min_value + 1])
            if rule.max_value is not None:
                boundary_values.extend([rule.max_value - 1, rule.max_value, rule.max_value + 1])
            boundary_values.extend([0, -1, 1, -2147483648, 2147483647])
        
        elif rule.data_type == DataType.STRING:
            if rule.min_length is not None:
                boundary_values.append('a' * (rule.min_length - 1) if rule.min_length > 0 else '')
                boundary_values.append('a' * rule.min_length)
                boundary_values.append('a' * (rule.min_length + 1))
            if rule.max_length is not None:
                boundary_values.append('a' * (rule.max_length - 1))
                boundary_values.append('a' * rule.max_length)
                boundary_values.append('a' * (rule.max_length + 1))
            
            # 特殊字符串
            boundary_values.extend([
                '', ' ', '\n', '\t', '\r\n',
                "'", '"', "'\"", 
                '<script>alert("xss")</script>',
                'SELECT * FROM users;',
                '中文测试',
                '🚀🎉💯',  # emoji
                'a' * 1000  # 长字符串
            ])
        
        elif rule.data_type == DataType.FLOAT:
            if rule.min_value is not None:
                boundary_values.extend([rule.min_value - 0.1, rule.min_value, rule.min_value + 0.1])
            if rule.max_value is not None:
                boundary_values.extend([rule.max_value - 0.1, rule.max_value, rule.max_value + 0.1])
            boundary_values.extend([0.0, -0.1, 0.1, float('inf'), float('-inf'), float('nan')])
        
        # 添加NULL值（如果允许）
        if rule.nullable:
            boundary_values.append(None)
        
        return boundary_values
    
    def generate_invalid_values(self, rule: DataRule) -> List[Any]:
        """生成无效值测试数据
        
        Args:
            rule: 数据生成规则
            
        Returns:
            无效值列表
        """
        invalid_values = []
        
        # 类型不匹配的值
        if rule.data_type == DataType.INTEGER:
            invalid_values.extend(['not_a_number', 3.14, True, [], {}])
        elif rule.data_type == DataType.STRING:
            invalid_values.extend([123, 3.14, True, [], {}])
        elif rule.data_type == DataType.BOOLEAN:
            invalid_values.extend(['true', 'false', 1, 0, 'yes', 'no'])
        elif rule.data_type == DataType.EMAIL:
            invalid_values.extend([
                'invalid_email', '@example.com', 'user@', 'user@.com',
                'user <EMAIL>', 'user@exam ple.com'
            ])
        elif rule.data_type == DataType.PHONE:
            invalid_values.extend([
                '123', 'abc', '123-abc-456', '+86 abc def ghij'
            ])
        
        # 如果不允许NULL，添加NULL值
        if not rule.nullable:
            invalid_values.append(None)
        
        return invalid_values
    
    def generate_performance_test_data(self, template_name: str, size_mb: float, 
                                     output_dir: str) -> Dict[str, str]:
        """生成性能测试数据
        
        Args:
            template_name: 模板名称
            size_mb: 目标数据大小（MB）
            output_dir: 输出目录
            
        Returns:
            生成的文件路径字典
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 估算记录数量
        sample_record = self.generate_record(template_name)
        sample_size = len(json.dumps(sample_record, default=str).encode('utf-8'))
        estimated_count = int((size_mb * 1024 * 1024) / sample_size)
        
        files = {}
        
        # 生成CSV文件
        csv_file = output_path / f"{template_name}_performance_{size_mb}mb.csv"
        self.generate_csv_file(template_name, estimated_count, str(csv_file))
        files['csv'] = str(csv_file)
        
        # 生成JSON文件
        json_file = output_path / f"{template_name}_performance_{size_mb}mb.json"
        self.generate_json_file(template_name, estimated_count, str(json_file))
        files['json'] = str(json_file)
        
        # 生成SQL文件
        sql_file = output_path / f"{template_name}_performance_{size_mb}mb.sql"
        self.generate_sql_insert_file(template_name, estimated_count, 
                                    f"test_{template_name}", str(sql_file))
        files['sql'] = str(sql_file)
        
        return files
    
    def create_custom_template(self, name: str, description: str, 
                             fields_config: Dict[str, Dict[str, Any]], 
                             category: DataCategory = DataCategory.RANDOM) -> DataTemplate:
        """创建自定义数据模板
        
        Args:
            name: 模板名称
            description: 模板描述
            fields_config: 字段配置字典
            category: 数据类别
            
        Returns:
            创建的数据模板
        """
        fields = {}
        for field_name, config in fields_config.items():
            data_type = DataType(config.get('type', 'string'))
            rule = DataRule(
                data_type=data_type,
                min_value=config.get('min_value'),
                max_value=config.get('max_value'),
                length=config.get('length'),
                min_length=config.get('min_length'),
                max_length=config.get('max_length'),
                pattern=config.get('pattern'),
                choices=config.get('choices'),
                nullable=config.get('nullable', False),
                null_probability=config.get('null_probability', 0.1),
                format_string=config.get('format')
            )
            fields[field_name] = rule
        
        template = DataTemplate(
            name=name,
            description=description,
            fields=fields,
            category=category
        )
        
        self.register_template(template)
        return template
    
    def export_template(self, template_name: str, output_file: str):
        """导出模板配置到文件
        
        Args:
            template_name: 模板名称
            output_file: 输出文件路径
        """
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"模板 '{template_name}' 不存在")
        
        # 转换为可序列化的格式
        template_dict = {
            'name': template.name,
            'description': template.description,
            'category': template.category.value,
            'fields': {}
        }
        
        for field_name, rule in template.fields.items():
            template_dict['fields'][field_name] = {
                'type': rule.data_type.value,
                'min_value': rule.min_value,
                'max_value': rule.max_value,
                'length': rule.length,
                'min_length': rule.min_length,
                'max_length': rule.max_length,
                'pattern': rule.pattern,
                'choices': rule.choices,
                'nullable': rule.nullable,
                'null_probability': rule.null_probability,
                'format': rule.format_string
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(template_dict, f, ensure_ascii=False, indent=2)
    
    def import_template(self, template_file: str) -> DataTemplate:
        """从文件导入模板配置
        
        Args:
            template_file: 模板文件路径
            
        Returns:
            导入的数据模板
        """
        with open(template_file, 'r', encoding='utf-8') as f:
            template_dict = json.load(f)
        
        return self.create_custom_template(
            name=template_dict['name'],
            description=template_dict['description'],
            fields_config=template_dict['fields'],
            category=DataCategory(template_dict.get('category', 'random'))
        )


# 使用示例
if __name__ == "__main__":
    # 创建数据生成器
    generator = EnhancedTestDataGenerator(seed=42)
    
    # 生成用户数据
    users = generator.generate_records("user", 10)
    print("生成的用户数据:")
    for user in users[:3]:
        print(json.dumps(user, ensure_ascii=False, indent=2, default=str))
    
    # 生成电信数据
    telecom_data = generator.generate_records("telecom_record", 5)
    print("\n生成的电信数据:")
    for record in telecom_data[:2]:
        print(json.dumps(record, ensure_ascii=False, indent=2, default=str))
    
    # 生成边界值测试数据
    string_rule = DataRule(DataType.STRING, min_length=5, max_length=10)
    boundary_values = generator.generate_boundary_values(string_rule)
    print(f"\n字符串边界值: {boundary_values[:10]}")
    
    # 创建自定义模板
    custom_template = generator.create_custom_template(
        name="api_test",
        description="API测试数据",
        fields_config={
            "request_id": {"type": "uuid"},
            "method": {"type": "string", "choices": ["GET", "POST", "PUT", "DELETE"]},
            "url": {"type": "url"},
            "status_code": {"type": "integer", "min_value": 200, "max_value": 599},
            "response_time": {"type": "float", "min_value": 0.1, "max_value": 5.0}
        }
    )
    
    # 生成API测试数据
    api_data = generator.generate_records("api_test", 3)
    print("\n生成的API测试数据:")
    for record in api_data:
        print(json.dumps(record, ensure_ascii=False, indent=2, default=str))