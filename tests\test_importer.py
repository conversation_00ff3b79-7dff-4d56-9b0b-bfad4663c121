"""Tests for data import operations.

This module contains comprehensive tests for the DataImporter class
and CSV import functionality.
"""

import asyncio
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, mock_open, patch

import pandas as pd
import pytest

from src.database.exceptions import (
    DatabaseError,
    FileOperationError,
    SchemaNotFoundError,
    TableExistsError,
    ValidationError,
)
from src.database.operations.importer import DataImporter, import_csv_to_table


class TestDataImporter:
    """Test cases for DataImporter class."""

    @pytest.fixture
    def mock_session_manager(self):
        """Create mock session manager."""
        session_manager = MagicMock()
        session_manager.pool = AsyncMock()

        # Mock pool.acquire context manager
        class MockAsyncContextManager:
            async def __aenter__(self):
                mock_conn = AsyncMock()
                mock_conn.fetch.return_value = [
                    {"column_name": "id", "data_type": "integer"},
                    {"column_name": "name", "data_type": "text"},
                    {"column_name": "age", "data_type": "integer"},
                    {"column_name": "email", "data_type": "text"},
                ]
                mock_conn.copy_records_to_table = AsyncMock(return_value="COPY 3")
                return mock_conn

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        # Correctly mock the acquire method to return the context manager
        def mock_acquire():
            return MockAsyncContextManager()

        session_manager.pool.acquire = mock_acquire
        return session_manager

    @pytest.fixture
    def mock_schema_manager(self):
        """Create mock schema manager."""
        schema_manager = AsyncMock()
        schema_manager.ensure_schema_exists = AsyncMock()
        schema_manager.table_exists = AsyncMock(return_value=False)
        schema_manager.create_table_from_dataframe = AsyncMock()
        schema_manager.handle_table_operation = AsyncMock()
        schema_manager.get_table_info = AsyncMock(return_value={})
        schema_manager.schema_exists = AsyncMock(return_value=True)
        return schema_manager

    @pytest.fixture
    def data_importer(self, mock_session_manager, mock_schema_manager):
        """Create DataImporter instance with mocked dependencies."""
        return DataImporter(mock_session_manager, mock_schema_manager)

    @pytest.fixture
    def sample_csv_content(self):
        """Sample CSV content for testing."""
        return "id,name,age,email\n1,John Doe,30,<EMAIL>\n2,Jane Smith,25,<EMAIL>\n3,Bob Johnson,35,<EMAIL>"

    @pytest.fixture
    def sample_dataframe(self):
        """Sample DataFrame for testing."""
        return pd.DataFrame(
            {
                "id": [1, 2, 3],
                "name": ["John Doe", "Jane Smith", "Bob Johnson"],
                "age": [30, 25, 35],
                "email": ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            }
        )

    @pytest.fixture
    def temp_csv_file(self, sample_csv_content):
        """Create temporary CSV file for testing."""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False) as f:
            f.write(sample_csv_content)
            temp_path = Path(f.name)

        yield temp_path

        # Cleanup
        if temp_path.exists():
            temp_path.unlink()

    @pytest.mark.asyncio
    async def test_import_csv_success(
        self, data_importer, temp_csv_file, sample_dataframe
    ):
        """Test successful CSV import."""
        # Mock the CSV reading
        with patch(
            "src.database.operations.importer.read_csv_to_dataframe"
        ) as mock_read_csv:
            mock_read_csv.return_value = sample_dataframe

            # Mock the copy operation
            data_importer._copy_dataframe_to_table = AsyncMock(return_value=3)

            result = await data_importer.import_csv(
                file_path=temp_csv_file, table_name="test_table", schema_name="public"
            )

            assert result["success"] is True
            assert result["rows_imported"] == 3
            assert result["table_created"] is True
            assert result["table_name"] == "test_table"
            assert result["schema_name"] == "public"

            # Verify method calls
            data_importer.schema_manager.ensure_schema_exists.assert_called_once_with(
                "public"
            )
            data_importer.schema_manager.table_exists.assert_called_once_with(
                "test_table", "public"
            )
            data_importer.schema_manager.create_table_from_dataframe.assert_called_once()

    @pytest.mark.asyncio
    async def test_import_csv_invalid_table_name(self, data_importer, temp_csv_file):
        """Test CSV import with invalid table name."""
        with pytest.raises(ValidationError, match="Invalid table name"):
            await data_importer.import_csv(
                file_path=temp_csv_file,
                table_name="invalid-table-name!",
                schema_name="public",
            )

    @pytest.mark.asyncio
    async def test_import_csv_invalid_schema_name(self, data_importer, temp_csv_file):
        """Test CSV import with invalid schema name."""
        with pytest.raises(ValidationError, match="Invalid schema name"):
            await data_importer.import_csv(
                file_path=temp_csv_file,
                table_name="test_table",
                schema_name="invalid-schema!",
            )

    @pytest.mark.asyncio
    async def test_import_csv_file_not_found(self, data_importer):
        """Test CSV import with non-existent file."""
        with pytest.raises(FileOperationError, match="CSV file not found"):
            await data_importer.import_csv(
                file_path="/non/existent/file.csv",
                table_name="test_table",
                schema_name="public",
            )

    @pytest.mark.asyncio
    async def test_import_csv_invalid_if_exists_strategy(
        self, data_importer, temp_csv_file
    ):
        """Test CSV import with invalid if_exists strategy."""
        with pytest.raises(ValidationError, match="Invalid if_exists_strategy"):
            await data_importer.import_csv(
                file_path=temp_csv_file,
                table_name="test_table",
                schema_name="public",
                if_exists_strategy="invalid_strategy",
            )

    @pytest.mark.asyncio
    async def test_import_csv_empty_file(self, data_importer, temp_csv_file):
        """Test CSV import with empty DataFrame."""
        empty_df = pd.DataFrame()

        with patch(
            "src.database.operations.importer.read_csv_to_dataframe"
        ) as mock_read_csv:
            mock_read_csv.return_value = empty_df

            result = await data_importer.import_csv(
                file_path=temp_csv_file, table_name="test_table", schema_name="public"
            )

            assert result["success"] is True
            assert result["rows_imported"] == 0
            assert result["message"] == "No data to import (empty CSV)"

    @pytest.mark.asyncio
    async def test_import_csv_table_exists_fail_strategy(
        self, data_importer, temp_csv_file, sample_dataframe
    ):
        """Test CSV import when table exists with 'fail' strategy."""
        # Mock table exists
        data_importer.schema_manager.table_exists.return_value = True

        with patch(
            "src.database.operations.importer.read_csv_to_dataframe"
        ) as mock_read_csv:
            mock_read_csv.return_value = sample_dataframe

            with pytest.raises(TableExistsError, match="already exists"):
                await data_importer.import_csv(
                    file_path=temp_csv_file,
                    table_name="existing_table",
                    schema_name="public",
                    if_exists_strategy="fail",
                )

    @pytest.mark.asyncio
    async def test_import_csv_table_exists_skip_strategy(
        self, data_importer, temp_csv_file, sample_dataframe
    ):
        """Test CSV import when table exists with 'skip' strategy."""
        # Mock table exists
        data_importer.schema_manager.table_exists.return_value = True

        with patch(
            "src.database.operations.importer.read_csv_to_dataframe"
        ) as mock_read_csv:
            mock_read_csv.return_value = sample_dataframe

            result = await data_importer.import_csv(
                file_path=temp_csv_file,
                table_name="existing_table",
                schema_name="public",
                if_exists_strategy="skip",
            )

            assert result["success"] is True
            assert result["rows_imported"] == 0
            assert result["message"] == "Import skipped (table exists)"

    @pytest.mark.asyncio
    async def test_import_csv_table_exists_replace_strategy(
        self, data_importer, temp_csv_file, sample_dataframe
    ):
        """Test CSV import when table exists with 'replace' strategy."""
        # Mock table exists initially, then doesn't exist after drop
        data_importer.schema_manager.table_exists.side_effect = [True, False]
        data_importer._copy_dataframe_to_table = AsyncMock(return_value=3)

        with patch(
            "src.database.operations.importer.read_csv_to_dataframe"
        ) as mock_read_csv:
            mock_read_csv.return_value = sample_dataframe

            result = await data_importer.import_csv(
                file_path=temp_csv_file,
                table_name="existing_table",
                schema_name="public",
                if_exists_strategy="replace",
            )

            assert result["success"] is True
            assert result["rows_imported"] == 3
            assert result["table_created"] is True

            # Verify table was dropped
            data_importer.schema_manager.handle_table_operation.assert_called_once_with(
                "existing_table", "public", "drop"
            )

    @pytest.mark.asyncio
    async def test_import_csv_table_exists_append_strategy(
        self, data_importer, temp_csv_file, sample_dataframe
    ):
        """Test CSV import when table exists with 'append' strategy."""
        # Mock table exists
        data_importer.schema_manager.table_exists.return_value = True
        data_importer._copy_dataframe_to_table = AsyncMock(return_value=3)

        with patch(
            "src.database.operations.importer.read_csv_to_dataframe"
        ) as mock_read_csv:
            mock_read_csv.return_value = sample_dataframe

            result = await data_importer.import_csv(
                file_path=temp_csv_file,
                table_name="existing_table",
                schema_name="public",
                if_exists_strategy="append",
            )

            assert result["success"] is True
            assert result["rows_imported"] == 3
            assert result["table_created"] is False

            # Verify table was not created (since it exists)
            data_importer.schema_manager.create_table_from_dataframe.assert_not_called()

    @pytest.mark.asyncio
    async def test_import_csv_with_column_mapping(
        self, data_importer, temp_csv_file, sample_dataframe
    ):
        """Test CSV import with column mapping."""
        column_mapping = {"name": "full_name", "email": "email_address"}
        data_importer._copy_dataframe_to_table = AsyncMock(return_value=3)

        with patch(
            "src.database.operations.importer.read_csv_to_dataframe"
        ) as mock_read_csv:
            mock_read_csv.return_value = sample_dataframe

            result = await data_importer.import_csv(
                file_path=temp_csv_file,
                table_name="test_table",
                schema_name="public",
                column_mapping=column_mapping,
            )

            assert result["success"] is True
            assert result["rows_imported"] == 3

    @pytest.mark.asyncio
    async def test_import_csv_with_data_types(
        self, data_importer, temp_csv_file, sample_dataframe
    ):
        """Test CSV import with data type specifications."""
        data_types = {"id": "int64", "age": "int32"}
        data_importer._copy_dataframe_to_table = AsyncMock(return_value=3)

        with patch(
            "src.database.operations.importer.read_csv_to_dataframe"
        ) as mock_read_csv:
            mock_read_csv.return_value = sample_dataframe

            result = await data_importer.import_csv(
                file_path=temp_csv_file,
                table_name="test_table",
                schema_name="public",
                data_types=data_types,
            )

            assert result["success"] is True
            assert result["rows_imported"] == 3

    @pytest.mark.asyncio
    async def test_copy_dataframe_to_table(self, data_importer, sample_dataframe):
        """Test copying DataFrame to database table."""
        # Mock connection and database operations
        mock_conn = AsyncMock()
        mock_conn.fetch.return_value = [
            {"column_name": "id", "data_type": "integer"},
            {"column_name": "name", "data_type": "text"},
            {"column_name": "age", "data_type": "integer"},
            {"column_name": "email", "data_type": "text"},
        ]
        # Configure the mock to not raise any exceptions
        mock_conn.copy_records_to_table = AsyncMock(return_value="COPY 2")
        mock_conn.copy_records_to_table.side_effect = None

        # Mock pool.acquire context manager
        class MockAsyncContextManager:
            async def __aenter__(self):
                return mock_conn

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        # Correctly mock the acquire method to return the context manager
        def mock_acquire():
            return MockAsyncContextManager()

        data_importer.session_manager.pool.acquire = mock_acquire

        result = await data_importer._copy_dataframe_to_table(
            df=sample_dataframe,
            table_name="test_table",
            schema_name="public",
            batch_size=2,
        )

        assert result == 3

        # Verify copy_records_to_table was called
        assert (
            mock_conn.copy_records_to_table.call_count == 2
        )  # 3 rows with batch_size=2

    @pytest.mark.asyncio
    async def test_import_multiple_csv(
        self, data_importer, temp_csv_file, sample_dataframe
    ):
        """Test importing multiple CSV files."""
        file_mappings = [
            {
                "file_path": str(temp_csv_file),
                "table_name": "table1",
                "schema_name": "public",
            },
            {
                "file_path": str(temp_csv_file),
                "table_name": "table2",
                "if_exists_strategy": "append",
            },
        ]

        # Mock import_csv method
        data_importer.import_csv = AsyncMock(
            side_effect=[
                {"success": True, "rows_imported": 3, "table_name": "table1"},
                {"success": True, "rows_imported": 3, "table_name": "table2"},
            ]
        )

        results = await data_importer.import_multiple_csv(file_mappings)

        assert len(results) == 2
        assert all(result["success"] for result in results)
        assert results[0]["table_name"] == "table1"
        assert results[1]["table_name"] == "table2"

    @pytest.mark.asyncio
    async def test_import_multiple_csv_with_error(self, data_importer, temp_csv_file):
        """Test importing multiple CSV files with one failing."""
        file_mappings = [
            {"file_path": str(temp_csv_file), "table_name": "table1"},
            {"file_path": "/non/existent/file.csv", "table_name": "table2"},
        ]

        # Mock import_csv method - first succeeds, second fails
        data_importer.import_csv = AsyncMock(
            side_effect=[
                {"success": True, "rows_imported": 3, "table_name": "table1"},
                FileOperationError("File not found"),
            ]
        )

        results = await data_importer.import_multiple_csv(file_mappings)

        assert len(results) == 2
        assert results[0]["success"] is True
        assert results[1]["success"] is False
        assert "error" in results[1]

    @pytest.mark.asyncio
    async def test_validate_import_requirements(self, data_importer, temp_csv_file):
        """Test validation of import requirements."""
        result = await data_importer.validate_import_requirements(
            file_path=temp_csv_file, table_name="test_table", schema_name="public"
        )

        assert result["valid"] is True
        assert "file_info" in result
        assert "table_info" in result
        assert result["file_info"]["path"] == str(temp_csv_file)

    @pytest.mark.asyncio
    async def test_validate_import_requirements_invalid_file(self, data_importer):
        """Test validation with non-existent file."""
        result = await data_importer.validate_import_requirements(
            file_path="/non/existent/file.csv",
            table_name="test_table",
            schema_name="public",
        )

        assert result["valid"] is False
        assert any("File not found" in issue for issue in result["issues"])

    @pytest.mark.asyncio
    async def test_validate_import_requirements_invalid_identifiers(
        self, data_importer, temp_csv_file
    ):
        """Test validation with invalid table/schema names."""
        result = await data_importer.validate_import_requirements(
            file_path=temp_csv_file,
            table_name="invalid-table!",
            schema_name="invalid-schema!",
        )

        assert result["valid"] is False
        assert any("Invalid table name" in issue for issue in result["issues"])
        assert any("Invalid schema name" in issue for issue in result["issues"])


class TestConvenienceFunctions:
    """Test cases for convenience functions."""

    @pytest.mark.asyncio
    async def test_import_csv_to_table(self):
        """Test convenience function for CSV import."""
        mock_session_manager = MagicMock()

        with patch(
            "src.database.operations.importer.DataImporter"
        ) as mock_importer_class:
            mock_importer = AsyncMock()
            mock_importer.import_csv.return_value = {
                "success": True,
                "rows_imported": 5,
            }
            mock_importer_class.return_value = mock_importer

            result = await import_csv_to_table(
                session_manager=mock_session_manager,
                file_path="test.csv",
                table_name="test_table",
            )

            assert result["success"] is True
            assert result["rows_imported"] == 5

            # Verify DataImporter was created and import_csv was called
            mock_importer_class.assert_called_once_with(mock_session_manager)
            mock_importer.import_csv.assert_called_once_with(
                file_path="test.csv", table_name="test_table", schema_name="public"
            )


if __name__ == "__main__":
    pytest.main([__file__])
