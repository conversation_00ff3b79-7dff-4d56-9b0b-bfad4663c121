"""End-to-end testing framework for comprehensive business process testing."""

import asyncio
import json
import time
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union, Callable
from unittest.mock import Mock, patch
import logging

import pytest
import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Import project modules
try:
    from src.config.models import DatabaseConfig
    from src.database.connection import DatabaseConnection, ConnectionPool
    from src.database.operations import CRUDOperations
    from src.database.schema import SchemaManager
    from src.database.monitoring import DatabaseMonitor
    from src.database.exceptions import DatabaseError, ConnectionError
except ImportError:
    # Fallback for testing without full project structure
    DatabaseConfig = Mock
    DatabaseConnection = Mock
    ConnectionPool = Mock
    CRUDOperations = Mock
    SchemaManager = Mock
    DatabaseMonitor = Mock
    DatabaseError = Exception
    ConnectionError = Exception

# Import test tools
from tests.tools.data_generator import TestDataGenerator
from tests.tools.performance_monitor import PerformanceMonitor
from tests.tools.memory_profiler import MemoryProfiler
from tests.config.test_config_manager import get_current_config


logger = logging.getLogger(__name__)


@dataclass
class E2ETestStep:
    """Single step in an end-to-end test scenario."""
    name: str
    description: str
    action: Callable
    expected_result: Any
    timeout: float = 30.0
    retry_count: int = 0
    retry_delay: float = 1.0
    cleanup_action: Optional[Callable] = None
    prerequisites: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class E2ETestScenario:
    """Complete end-to-end test scenario."""
    name: str
    description: str
    steps: List[E2ETestStep]
    setup_actions: List[Callable] = field(default_factory=list)
    teardown_actions: List[Callable] = field(default_factory=list)
    test_data: Dict[str, Any] = field(default_factory=dict)
    environment: str = 'development'
    tags: List[str] = field(default_factory=list)
    priority: str = 'medium'
    estimated_duration: float = 300.0


@dataclass
class E2ETestResult:
    """Result of an end-to-end test execution."""
    scenario_name: str
    start_time: datetime
    end_time: datetime
    duration: float
    status: str  # 'passed', 'failed', 'skipped', 'error'
    step_results: List[Dict[str, Any]]
    error_message: Optional[str] = None
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    memory_metrics: Dict[str, Any] = field(default_factory=dict)
    artifacts: List[str] = field(default_factory=list)


class E2ETestFramework:
    """End-to-end testing framework for comprehensive business process testing."""
    
    def __init__(self, config_manager=None):
        """Initialize E2E test framework.
        
        Args:
            config_manager: Test configuration manager
        """
        self.config = get_current_config() if not config_manager else config_manager.get_current_config()
        self.scenarios: Dict[str, E2ETestScenario] = {}
        self.results: List[E2ETestResult] = []
        self.data_generator = TestDataGenerator()
        self.performance_monitor = PerformanceMonitor()
        self.memory_profiler = MemoryProfiler()
        
        # Test context
        self.test_context: Dict[str, Any] = {}
        self.shared_resources: Dict[str, Any] = {}
        self.cleanup_actions: List[Callable] = []
        
        # Database connections
        self.db_engine = None
        self.db_session = None
        self.connection_pool = None
        
    def register_scenario(self, scenario: E2ETestScenario):
        """Register an E2E test scenario.
        
        Args:
            scenario: E2ETestScenario to register
        """
        self.scenarios[scenario.name] = scenario
        logger.info(f"Registered E2E scenario: {scenario.name}")
        
    def create_scenario(self, 
                       name: str,
                       description: str,
                       steps: List[E2ETestStep],
                       **kwargs) -> E2ETestScenario:
        """Create and register an E2E test scenario.
        
        Args:
            name: Scenario name
            description: Scenario description
            steps: List of test steps
            **kwargs: Additional scenario parameters
            
        Returns:
            Created E2ETestScenario
        """
        scenario = E2ETestScenario(
            name=name,
            description=description,
            steps=steps,
            **kwargs
        )
        self.register_scenario(scenario)
        return scenario
        
    @contextmanager
    def test_environment(self, scenario_name: str):
        """Context manager for E2E test environment setup and teardown.
        
        Args:
            scenario_name: Name of the scenario being executed
        """
        scenario = self.scenarios.get(scenario_name)
        if not scenario:
            raise ValueError(f"Unknown scenario: {scenario_name}")
            
        logger.info(f"Setting up E2E test environment for: {scenario_name}")
        
        try:
            # Setup database connections
            self._setup_database_connections()
            
            # Execute setup actions
            for setup_action in scenario.setup_actions:
                setup_action(self.test_context)
                
            # Start monitoring
            self.performance_monitor.start_monitoring()
            self.memory_profiler.start_monitoring()
            
            yield self
            
        finally:
            # Stop monitoring
            self.performance_monitor.stop_monitoring()
            self.memory_profiler.stop_monitoring()
            
            # Execute teardown actions
            for teardown_action in scenario.teardown_actions:
                try:
                    teardown_action(self.test_context)
                except Exception as e:
                    logger.warning(f"Teardown action failed: {e}")
                    
            # Execute cleanup actions
            for cleanup_action in self.cleanup_actions:
                try:
                    cleanup_action()
                except Exception as e:
                    logger.warning(f"Cleanup action failed: {e}")
                    
            # Close database connections
            self._cleanup_database_connections()
            
            # Clear test context
            self.test_context.clear()
            self.cleanup_actions.clear()
            
            logger.info(f"Cleaned up E2E test environment for: {scenario_name}")
            
    def _setup_database_connections(self):
        """Setup database connections for testing."""
        if not self.config:
            logger.warning("No test configuration available")
            return
            
        try:
            # Create database engine
            db_config = self.config.database
            database_url = f"postgresql://{db_config.username}:{db_config.password}@{db_config.host}:{db_config.port}/{db_config.database}"
            
            self.db_engine = create_engine(
                database_url,
                pool_size=db_config.pool_size,
                max_overflow=db_config.max_overflow,
                pool_timeout=db_config.pool_timeout,
                pool_recycle=db_config.pool_recycle,
                echo=db_config.echo
            )
            
            # Create session maker
            Session = sessionmaker(bind=self.db_engine)
            self.db_session = Session()
            
            # Store in shared resources
            self.shared_resources['db_engine'] = self.db_engine
            self.shared_resources['db_session'] = self.db_session
            
            logger.info("Database connections established")
            
        except Exception as e:
            logger.error(f"Failed to setup database connections: {e}")
            raise
            
    def _cleanup_database_connections(self):
        """Cleanup database connections."""
        try:
            if self.db_session:
                self.db_session.close()
                self.db_session = None
                
            if self.db_engine:
                self.db_engine.dispose()
                self.db_engine = None
                
            logger.info("Database connections cleaned up")
            
        except Exception as e:
            logger.warning(f"Error cleaning up database connections: {e}")
            
    async def execute_scenario(self, scenario_name: str) -> E2ETestResult:
        """Execute an E2E test scenario.
        
        Args:
            scenario_name: Name of the scenario to execute
            
        Returns:
            E2ETestResult object
        """
        scenario = self.scenarios.get(scenario_name)
        if not scenario:
            raise ValueError(f"Unknown scenario: {scenario_name}")
            
        start_time = datetime.now()
        step_results = []
        status = 'passed'
        error_message = None
        
        logger.info(f"Starting E2E scenario: {scenario_name}")
        
        try:
            with self.test_environment(scenario_name):
                # Execute each step
                for i, step in enumerate(scenario.steps):
                    step_result = await self._execute_step(step, i + 1)
                    step_results.append(step_result)
                    
                    if step_result['status'] != 'passed':
                        status = 'failed'
                        error_message = step_result.get('error_message')
                        break
                        
        except Exception as e:
            status = 'error'
            error_message = str(e)
            logger.error(f"E2E scenario {scenario_name} failed with error: {e}")
            
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Collect performance and memory metrics
        performance_metrics = self.performance_monitor.get_summary()
        memory_metrics = self.memory_profiler.get_memory_stats()
        
        result = E2ETestResult(
            scenario_name=scenario_name,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            status=status,
            step_results=step_results,
            error_message=error_message,
            performance_metrics=performance_metrics,
            memory_metrics=memory_metrics
        )
        
        self.results.append(result)
        
        logger.info(f"Completed E2E scenario: {scenario_name} ({status}) in {duration:.2f}s")
        
        return result
        
    async def _execute_step(self, step: E2ETestStep, step_number: int) -> Dict[str, Any]:
        """Execute a single test step.
        
        Args:
            step: E2ETestStep to execute
            step_number: Step number in the scenario
            
        Returns:
            Step execution result dictionary
        """
        logger.info(f"Executing step {step_number}: {step.name}")
        
        start_time = datetime.now()
        status = 'passed'
        error_message = None
        actual_result = None
        
        try:
            # Check prerequisites
            for prereq in step.prerequisites:
                if prereq not in self.test_context:
                    raise ValueError(f"Prerequisite not met: {prereq}")
                    
            # Execute step with retry logic
            for attempt in range(step.retry_count + 1):
                try:
                    # Execute the step action
                    if asyncio.iscoroutinefunction(step.action):
                        actual_result = await asyncio.wait_for(
                            step.action(self.test_context, self.shared_resources),
                            timeout=step.timeout
                        )
                    else:
                        actual_result = step.action(self.test_context, self.shared_resources)
                        
                    # Validate result if expected result is provided
                    if step.expected_result is not None:
                        if not self._validate_result(actual_result, step.expected_result):
                            raise AssertionError(f"Result validation failed. Expected: {step.expected_result}, Got: {actual_result}")
                            
                    break  # Success, exit retry loop
                    
                except Exception as e:
                    if attempt < step.retry_count:
                        logger.warning(f"Step {step.name} failed (attempt {attempt + 1}), retrying: {e}")
                        await asyncio.sleep(step.retry_delay)
                    else:
                        raise
                        
        except asyncio.TimeoutError:
            status = 'failed'
            error_message = f"Step timed out after {step.timeout}s"
        except Exception as e:
            status = 'failed'
            error_message = str(e)
            logger.error(f"Step {step.name} failed: {e}")
            
        finally:
            # Execute cleanup action if provided
            if step.cleanup_action:
                try:
                    if asyncio.iscoroutinefunction(step.cleanup_action):
                        await step.cleanup_action(self.test_context, self.shared_resources)
                    else:
                        step.cleanup_action(self.test_context, self.shared_resources)
                except Exception as e:
                    logger.warning(f"Step cleanup failed: {e}")
                    
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        step_result = {
            'step_number': step_number,
            'step_name': step.name,
            'description': step.description,
            'status': status,
            'duration': duration,
            'expected_result': step.expected_result,
            'actual_result': actual_result,
            'error_message': error_message,
            'metadata': step.metadata
        }
        
        logger.info(f"Step {step_number} completed: {status} in {duration:.2f}s")
        
        return step_result
        
    def _validate_result(self, actual: Any, expected: Any) -> bool:
        """Validate actual result against expected result.
        
        Args:
            actual: Actual result from step execution
            expected: Expected result
            
        Returns:
            True if validation passes, False otherwise
        """
        if callable(expected):
            # Expected is a validation function
            return expected(actual)
        elif isinstance(expected, dict) and 'validator' in expected:
            # Expected contains a validator function
            validator = expected['validator']
            return validator(actual)
        else:
            # Direct comparison
            return actual == expected
            
    def execute_all_scenarios(self, 
                             tags: Optional[List[str]] = None,
                             priority: Optional[str] = None) -> List[E2ETestResult]:
        """Execute all registered scenarios with optional filtering.
        
        Args:
            tags: Optional list of tags to filter scenarios
            priority: Optional priority level to filter scenarios
            
        Returns:
            List of E2ETestResult objects
        """
        scenarios_to_run = []
        
        for scenario in self.scenarios.values():
            # Filter by tags
            if tags and not any(tag in scenario.tags for tag in tags):
                continue
                
            # Filter by priority
            if priority and scenario.priority != priority:
                continue
                
            scenarios_to_run.append(scenario.name)
            
        logger.info(f"Executing {len(scenarios_to_run)} E2E scenarios")
        
        results = []
        for scenario_name in scenarios_to_run:
            try:
                result = asyncio.run(self.execute_scenario(scenario_name))
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to execute scenario {scenario_name}: {e}")
                
        return results
        
    def generate_test_report(self, output_path: Optional[str] = None) -> Dict[str, Any]:
        """Generate comprehensive test report.
        
        Args:
            output_path: Optional path to save the report
            
        Returns:
            Test report dictionary
        """
        if not self.results:
            return {'error': 'No test results available'}
            
        total_scenarios = len(self.results)
        passed_scenarios = len([r for r in self.results if r.status == 'passed'])
        failed_scenarios = len([r for r in self.results if r.status == 'failed'])
        error_scenarios = len([r for r in self.results if r.status == 'error'])
        
        total_duration = sum(r.duration for r in self.results)
        avg_duration = total_duration / total_scenarios if total_scenarios > 0 else 0
        
        report = {
            'summary': {
                'total_scenarios': total_scenarios,
                'passed': passed_scenarios,
                'failed': failed_scenarios,
                'errors': error_scenarios,
                'success_rate': (passed_scenarios / total_scenarios * 100) if total_scenarios > 0 else 0,
                'total_duration': total_duration,
                'average_duration': avg_duration
            },
            'scenarios': [
                {
                    'name': result.scenario_name,
                    'status': result.status,
                    'duration': result.duration,
                    'start_time': result.start_time.isoformat(),
                    'end_time': result.end_time.isoformat(),
                    'error_message': result.error_message,
                    'step_count': len(result.step_results),
                    'failed_steps': len([s for s in result.step_results if s['status'] != 'passed'])
                }
                for result in self.results
            ],
            'performance_summary': self._aggregate_performance_metrics(),
            'memory_summary': self._aggregate_memory_metrics(),
            'generated_at': datetime.now().isoformat()
        }
        
        if output_path:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, default=str)
                
            logger.info(f"Test report saved to: {output_path}")
            
        return report
        
    def _aggregate_performance_metrics(self) -> Dict[str, Any]:
        """Aggregate performance metrics from all test results."""
        all_metrics = [r.performance_metrics for r in self.results if r.performance_metrics]
        
        if not all_metrics:
            return {}
            
        # Calculate aggregated metrics
        execution_times = [m.get('total_execution_time', 0) for m in all_metrics]
        memory_usage = [m.get('peak_memory_mb', 0) for m in all_metrics]
        cpu_usage = [m.get('avg_cpu_percent', 0) for m in all_metrics]
        
        return {
            'avg_execution_time': sum(execution_times) / len(execution_times) if execution_times else 0,
            'max_execution_time': max(execution_times) if execution_times else 0,
            'avg_memory_usage_mb': sum(memory_usage) / len(memory_usage) if memory_usage else 0,
            'max_memory_usage_mb': max(memory_usage) if memory_usage else 0,
            'avg_cpu_usage_percent': sum(cpu_usage) / len(cpu_usage) if cpu_usage else 0,
            'max_cpu_usage_percent': max(cpu_usage) if cpu_usage else 0
        }
        
    def _aggregate_memory_metrics(self) -> Dict[str, Any]:
        """Aggregate memory metrics from all test results."""
        all_metrics = [r.memory_metrics for r in self.results if r.memory_metrics]
        
        if not all_metrics:
            return {}
            
        # Calculate aggregated metrics
        rss_values = [m.get('memory_rss', {}).get('max', 0) for m in all_metrics]
        leak_counts = [m.get('leaks_detected', 0) for m in all_metrics]
        
        return {
            'avg_rss_mb': sum(rss_values) / len(rss_values) if rss_values else 0,
            'max_rss_mb': max(rss_values) if rss_values else 0,
            'total_leaks_detected': sum(leak_counts),
            'scenarios_with_leaks': len([c for c in leak_counts if c > 0])
        }
        
    def clear_results(self):
        """Clear all test results."""
        self.results.clear()
        logger.info("Test results cleared")
        
    def get_scenario_names(self) -> List[str]:
        """Get list of registered scenario names."""
        return list(self.scenarios.keys())
        
    def get_scenario(self, name: str) -> Optional[E2ETestScenario]:
        """Get scenario by name."""
        return self.scenarios.get(name)


# Global E2E framework instance
e2e_framework = E2ETestFramework()


# Convenience functions
def register_e2e_scenario(scenario: E2ETestScenario):
    """Register an E2E test scenario."""
    e2e_framework.register_scenario(scenario)


def create_e2e_scenario(name: str, description: str, steps: List[E2ETestStep], **kwargs) -> E2ETestScenario:
    """Create and register an E2E test scenario."""
    return e2e_framework.create_scenario(name, description, steps, **kwargs)


def execute_e2e_scenario(scenario_name: str) -> E2ETestResult:
    """Execute an E2E test scenario."""
    return asyncio.run(e2e_framework.execute_scenario(scenario_name))


def create_test_step(name: str, 
                    description: str, 
                    action: Callable, 
                    expected_result: Any = None,
                    **kwargs) -> E2ETestStep:
    """Create a test step."""
    return E2ETestStep(
        name=name,
        description=description,
        action=action,
        expected_result=expected_result,
        **kwargs
    )