"""Generic Excel importer with multi-sheet processing capabilities.

This module provides a specialized Excel importer that can handle Excel files
with multiple sheets, complex formatting, and large datasets with memory optimization.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import logging
from typing import Any, Dict, List, Optional, Union, Iterator
from pathlib import Path
from datetime import datetime
import io

import pandas as pd
import numpy as np
from pydantic import BaseModel, Field, ConfigDict

from ..base import (
    AbstractImporter,
    ValidationMixin,
    ProcessingMixin,
    PerformanceMixin,
    ImporterConfig,
    ImportResult,
    ImportStatus
)


class ExcelConfig(BaseModel):
    """Configuration specific to Excel import."""
    # Sheet selection
    sheet_names: Optional[List[str]] = Field(default=None, description="Specific sheets to import (None for all)")
    sheet_indices: Optional[List[int]] = Field(default=None, description="Sheet indices to import (0-based)")
    skip_sheets: List[str] = Field(default_factory=list, description="Sheet names to skip")
    
    # Data reading settings
    header_row: int = Field(default=0, description="Header row index (0-based)")
    skip_rows: int = Field(default=0, description="Number of rows to skip at start")
    skip_footer: int = Field(default=0, description="Number of rows to skip at end")
    max_rows: Optional[int] = Field(default=None, description="Maximum rows to read per sheet")
    
    # Column settings
    use_columns: Optional[List[Union[str, int]]] = Field(default=None, description="Columns to use")
    column_names: Optional[List[str]] = Field(default=None, description="Custom column names")
    
    # Data type settings
    auto_detect_types: bool = Field(default=True, description="Auto-detect column data types")
    date_columns: List[str] = Field(default_factory=list, description="Columns to parse as dates")
    numeric_columns: List[str] = Field(default_factory=list, description="Columns to parse as numeric")
    string_columns: List[str] = Field(default_factory=list, description="Columns to keep as strings")
    
    # Excel-specific settings
    engine: str = Field(default="openpyxl", description="Excel engine ('openpyxl', 'xlrd', 'calamine')")
    keep_default_na: bool = Field(default=True, description="Keep default NaN values")
    na_values: List[str] = Field(
        default_factory=lambda: ['', 'NULL', 'null', 'N/A', 'n/a', '#N/A', 'NA', '#NULL!', '#DIV/0!'],
        description="Values to treat as NaN"
    )
    
    # Performance settings
    chunk_size: int = Field(default=10000, description="Chunk size for large sheets")
    memory_limit_mb: int = Field(default=512, description="Memory limit for processing")
    
    # Data processing settings
    merge_sheets: bool = Field(default=False, description="Merge all sheets into single DataFrame")
    add_sheet_column: bool = Field(default=True, description="Add sheet name column when merging")
    remove_empty_rows: bool = Field(default=True, description="Remove completely empty rows")
    remove_empty_columns: bool = Field(default=True, description="Remove completely empty columns")
    strip_whitespace: bool = Field(default=True, description="Strip whitespace from string columns")
    
    # Validation settings
    validate_headers: bool = Field(default=True, description="Validate column headers")
    required_columns: List[str] = Field(default_factory=list, description="Required column names")
    max_error_rate: float = Field(default=0.05, description="Maximum acceptable error rate")
    
    # Output settings
    preserve_formatting: bool = Field(default=False, description="Preserve Excel formatting")
    include_formulas: bool = Field(default=False, description="Include formula cells")
    add_metadata: bool = Field(default=True, description="Add file and sheet metadata")
    
    model_config = ConfigDict(
        extra="allow"
    )
class ExcelImporter(AbstractImporter, ValidationMixin, ProcessingMixin, PerformanceMixin):
    """High-performance Excel importer with multi-sheet processing capabilities.
    
    This importer provides comprehensive Excel processing with support for:
    - Multi-sheet processing with flexible selection
    - Memory-optimized reading for large Excel files
    - Automatic data type detection and conversion
    - Excel-specific formatting and formula handling
    - Data cleaning and validation
    - Performance monitoring and optimization
    """
    
    # Supported Excel engines and their capabilities
    ENGINE_CAPABILITIES = {
        'openpyxl': {'formats': ['.xlsx'], 'features': ['formulas', 'formatting', 'charts']},
        'xlrd': {'formats': ['.xls', '.xlsx'], 'features': ['legacy_support']},
        'calamine': {'formats': ['.xlsx', '.xlsm', '.xlsb'], 'features': ['performance', 'memory_efficient']}
    }
    
    def __init__(self, config: Optional[Union[ImporterConfig, Dict[str, Any]]] = None, **kwargs):
        """Initialize Excel importer.
        
        Args:
            config: Importer configuration
            **kwargs: Additional configuration parameters
        """
        super().__init__(config=config, **kwargs)
        
        # Excel-specific configuration
        excel_config_dict = kwargs.get('excel_config', {})
        if isinstance(excel_config_dict, ExcelConfig):
            self.excel_config = excel_config_dict
        else:
            self.excel_config = ExcelConfig(**excel_config_dict)
            
        # Set data type
        self.data_type = 'Excel'
        self.name = 'Excel Importer'
        self.supported_formats = ['.xlsx', '.xls', '.xlsm', '.xlsb']
        
        # Configure processing for Excel data
        self.configure_processing({
            'batch_size': self.excel_config.chunk_size,
            'enable_parallel': True,
            'memory_limit_mb': self.excel_config.memory_limit_mb,
            'use_polars': False  # Pandas for better Excel handling
        })
        
        # Configure validation
        self.configure_validation({
            'enable_strict_validation': self.excel_config.validate_headers,
            'max_error_rate': self.excel_config.max_error_rate
        })
        
        # Configure performance monitoring
        self.configure_performance({
            'enable_monitoring': True,
            'excel_chunk_size': self.excel_config.chunk_size
        })
        
        self.logger.info(f"Excel Importer initialized with engine: {self.excel_config.engine}")
        
    async def import_data(self, source: Union[str, Path], **kwargs) -> ImportResult:
        """Import Excel data with comprehensive processing.
        
        Args:
            source: Excel file path
            **kwargs: Additional import parameters
            
        Returns:
            Import result with metrics and status
        """
        start_time = datetime.now()
        
        try:
            # Start performance monitoring
            await self.start_performance_monitoring()
            
            with self.track_operation("excel_import", source=str(source)) as op_metrics:
                # Validate source
                if not await self.validate_source(source):
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        message="Source validation failed",
                        records_processed=0,
                        errors=["Invalid or inaccessible Excel file"]
                    )
                    
                # Analyze Excel file
                file_info = await self._analyze_excel_file(source)
                
                # Load and process data
                data_dict = await self._load_excel_data(source, file_info, **kwargs)
                
                if not data_dict:
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        message="No data loaded from Excel file",
                        records_processed=0
                    )
                    
                # Process sheets
                processed_data = await self._process_excel_sheets(data_dict)
                
                # Merge sheets if requested
                if self.excel_config.merge_sheets and len(processed_data) > 1:
                    final_data = self._merge_sheets(processed_data)
                    total_records = len(final_data)
                else:
                    final_data = processed_data
                    total_records = sum(len(df) for df in processed_data.values())
                    
                # Update operation metrics
                op_metrics.records_processed = total_records
                if isinstance(final_data, pd.DataFrame):
                    op_metrics.bytes_processed = final_data.memory_usage(deep=True).sum()
                else:
                    op_metrics.bytes_processed = sum(df.memory_usage(deep=True).sum() for df in final_data.values())
                    
                # Validate data
                validation_result = await self._validate_excel_data(final_data)
                
                if not validation_result['is_valid']:
                    if validation_result['error_rate'] > self.excel_config.max_error_rate:
                        return ImportResult(
                            status=ImportStatus.FAILED,
                            message=f"Data quality below threshold: {validation_result['error_rate']:.2%}",
                            records_processed=total_records,
                            errors=validation_result['errors'][:10]
                        )
                    else:
                        self.logger.warning(f"Data quality issues detected: {validation_result['error_rate']:.2%}")
                        
                # Store data if database operations are available
                if hasattr(self, 'db_ops') and self.db_ops:
                    try:
                        await self._store_excel_data(final_data, source, file_info)
                    except Exception as e:
                        self.logger.error(f"Database storage failed: {e}")
                        return ImportResult(
                            status=ImportStatus.FAILED,
                            message=f"Database storage failed: {e}",
                            records_processed=total_records,
                            errors=[str(e)]
                        )
                        
                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # Prepare metadata
                metadata = {
                    'file_info': file_info,
                    'sheets_processed': list(processed_data.keys()) if isinstance(final_data, dict) else ['merged'],
                    'total_sheets': len(data_dict)
                }
                
                if isinstance(final_data, pd.DataFrame):
                    metadata.update({
                        'columns': list(final_data.columns),
                        'data_types': final_data.dtypes.to_dict(),
                        'memory_usage_mb': final_data.memory_usage(deep=True).sum() / 1024 / 1024
                    })
                else:
                    metadata['sheet_info'] = {
                        sheet: {
                            'rows': len(df),
                            'columns': list(df.columns),
                            'memory_mb': df.memory_usage(deep=True).sum() / 1024 / 1024
                        }
                        for sheet, df in final_data.items()
                    }
                    
                return ImportResult(
                    status=ImportStatus.SUCCESS,
                    message=f"Successfully imported {total_records} records from Excel",
                    records_processed=total_records,
                    processing_time_seconds=processing_time,
                    data_quality_score=validation_result['quality_score'],
                    warnings=validation_result.get('warnings', [])[:5],
                    metadata=metadata
                )
                
        except Exception as e:
            self.logger.error(f"Excel import failed: {e}")
            return ImportResult(
                status=ImportStatus.FAILED,
                message=f"Import failed: {e}",
                records_processed=0,
                errors=[str(e)]
            )
            
        finally:
            await self.stop_performance_monitoring()
            
    async def _analyze_excel_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Analyze Excel file to detect characteristics.
        
        Args:
            file_path: Path to Excel file
            
        Returns:
            Dictionary with file analysis results
        """
        file_path = Path(file_path)
        
        # Basic file info
        stat = file_path.stat()
        file_info = {
            'file_size_bytes': stat.st_size,
            'file_size_mb': stat.st_size / 1024 / 1024,
            'file_format': file_path.suffix.lower(),
            'sheets': [],
            'total_sheets': 0,
            'engine_used': self.excel_config.engine
        }
        
        try:
            # Get sheet information
            excel_file = pd.ExcelFile(file_path, engine=self.excel_config.engine)
            sheet_names = excel_file.sheet_names
            
            file_info['total_sheets'] = len(sheet_names)
            
            # Analyze each sheet
            for sheet_name in sheet_names:
                try:
                    # Read just the first few rows to get info
                    sample_df = pd.read_excel(
                        excel_file,
                        sheet_name=sheet_name,
                        nrows=5,
                        header=self.excel_config.header_row
                    )
                    
                    sheet_info = {
                        'name': sheet_name,
                        'columns': list(sample_df.columns),
                        'column_count': len(sample_df.columns),
                        'sample_data': sample_df.head(3).to_dict('records'),
                        'estimated_rows': None  # Will be calculated if needed
                    }
                    
                    # Try to estimate row count for small sheets
                    try:
                        full_df = pd.read_excel(
                            excel_file,
                            sheet_name=sheet_name,
                            header=self.excel_config.header_row,
                            usecols=[0]  # Just first column for counting
                        )
                        sheet_info['estimated_rows'] = len(full_df)
                    except Exception:
                        # For large sheets, skip row counting
                        sheet_info['estimated_rows'] = 'large'
                        
                    file_info['sheets'].append(sheet_info)
                    
                except Exception as e:
                    self.logger.warning(f"Could not analyze sheet '{sheet_name}': {e}")
                    file_info['sheets'].append({
                        'name': sheet_name,
                        'error': str(e)
                    })
                    
            excel_file.close()
            
        except Exception as e:
            self.logger.warning(f"File analysis failed: {e}")
            file_info['analysis_error'] = str(e)
            
        return file_info
        
    async def _load_excel_data(self, file_path: Union[str, Path], file_info: Dict[str, Any], **kwargs) -> Dict[str, pd.DataFrame]:
        """Load Excel data from specified sheets.
        
        Args:
            file_path: Path to Excel file
            file_info: File analysis results
            **kwargs: Additional parameters
            
        Returns:
            Dictionary mapping sheet names to DataFrames
        """
        file_path = Path(file_path)
        data_dict = {}
        
        # Determine which sheets to load
        sheets_to_load = self._get_sheets_to_load(file_info)
        
        if not sheets_to_load:
            self.logger.warning("No sheets selected for loading")
            return data_dict
            
        # Prepare pandas read_excel parameters
        read_params = {
            'io': file_path,
            'engine': self.excel_config.engine,
            'header': self.excel_config.header_row,
            'skiprows': self.excel_config.skip_rows,
            'skipfooter': self.excel_config.skip_footer,
            'nrows': self.excel_config.max_rows,
            'usecols': self.excel_config.use_columns,
            'names': self.excel_config.column_names,
            'na_values': self.excel_config.na_values,
            'keep_default_na': self.excel_config.keep_default_na
        }
        
        # Add date parsing if specified
        if self.excel_config.date_columns:
            read_params['parse_dates'] = self.excel_config.date_columns
            
        # Add data type specifications
        if not self.excel_config.auto_detect_types:
            dtype_dict = {}
            
            # Set string columns
            for col in self.excel_config.string_columns:
                dtype_dict[col] = 'str'
                
            # Set numeric columns
            for col in self.excel_config.numeric_columns:
                dtype_dict[col] = 'float64'
                
            if dtype_dict:
                read_params['dtype'] = dtype_dict
                
        # Load each sheet
        for sheet_name in sheets_to_load:
            try:
                self.logger.info(f"Loading sheet: {sheet_name}")
                
                read_params['sheet_name'] = sheet_name
                data = pd.read_excel(**read_params)
                
                # Add metadata if requested
                if self.excel_config.add_metadata:
                    data = self._add_sheet_metadata(data, file_path, sheet_name)
                    
                data_dict[sheet_name] = data
                self.logger.info(f"Loaded {len(data)} rows from sheet '{sheet_name}'")
                
            except Exception as e:
                self.logger.error(f"Failed to load sheet '{sheet_name}': {e}")
                continue
                
        return data_dict
        
    def _get_sheets_to_load(self, file_info: Dict[str, Any]) -> List[str]:
        """Determine which sheets to load based on configuration.
        
        Args:
            file_info: File analysis results
            
        Returns:
            List of sheet names to load
        """
        available_sheets = [sheet['name'] for sheet in file_info.get('sheets', []) if 'error' not in sheet]
        
        if not available_sheets:
            return []
            
        # If specific sheet names are specified
        if self.excel_config.sheet_names:
            sheets_to_load = []
            for sheet_name in self.excel_config.sheet_names:
                if sheet_name in available_sheets:
                    sheets_to_load.append(sheet_name)
                else:
                    self.logger.warning(f"Requested sheet '{sheet_name}' not found")
            return sheets_to_load
            
        # If specific sheet indices are specified
        if self.excel_config.sheet_indices:
            sheets_to_load = []
            for index in self.excel_config.sheet_indices:
                if 0 <= index < len(available_sheets):
                    sheets_to_load.append(available_sheets[index])
                else:
                    self.logger.warning(f"Sheet index {index} out of range")
            return sheets_to_load
            
        # Load all sheets except those in skip list
        sheets_to_load = []
        for sheet_name in available_sheets:
            if sheet_name not in self.excel_config.skip_sheets:
                sheets_to_load.append(sheet_name)
                
        return sheets_to_load
        
    def _add_sheet_metadata(self, data: pd.DataFrame, file_path: Path, sheet_name: str) -> pd.DataFrame:
        """Add sheet metadata columns to DataFrame.
        
        Args:
            data: DataFrame to enhance
            file_path: Source file path
            sheet_name: Sheet name
            
        Returns:
            Enhanced DataFrame
        """
        if self.excel_config.add_sheet_column:
            data['_sheet_name'] = sheet_name
            
        if self.excel_config.add_metadata:
            data['_source_file'] = file_path.name
            data['_import_timestamp'] = datetime.now()
            data['_file_size_mb'] = file_path.stat().st_size / 1024 / 1024
            data['_row_number'] = range(1, len(data) + 1)
            
        return data
        
    async def _process_excel_sheets(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Process loaded Excel sheets.
        
        Args:
            data_dict: Dictionary of sheet DataFrames
            
        Returns:
            Dictionary of processed DataFrames
        """
        processed_data = {}
        
        for sheet_name, data in data_dict.items():
            try:
                self.logger.info(f"Processing sheet: {sheet_name}")
                
                # Clean data
                cleaned_data = await self._clean_excel_data(data)
                
                # Store processed data
                processed_data[sheet_name] = cleaned_data
                
                self.logger.info(f"Processed sheet '{sheet_name}': {len(cleaned_data)} rows")
                
            except Exception as e:
                self.logger.error(f"Failed to process sheet '{sheet_name}': {e}")
                continue
                
        return processed_data
        
    async def _clean_excel_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean Excel data according to configuration.
        
        Args:
            data: DataFrame to clean
            
        Returns:
            Cleaned DataFrame
        """
        original_rows = len(data)
        original_cols = len(data.columns)
        
        # Remove duplicate columns and standardize column names
        from src.utils.column_deduplicator import ColumnDeduplicator
        
        # Remove duplicate columns, keeping the most complete one
        data, dedup_report = ColumnDeduplicator.remove_duplicate_columns(
            data, keep_strategy='best'
        )
        
        if dedup_report['total_removed'] > 0:
            self.logger.info(f"Removed {dedup_report['total_removed']} duplicate columns in Excel data")
        
        # Standardize column names to lowercase
        clean_columns = []
        for col in data.columns:
            clean_name = str(col).strip().lower()
            clean_columns.append(clean_name)
        
        data.columns = clean_columns
        
        # Remove empty rows
        if self.excel_config.remove_empty_rows:
            data = data.dropna(how='all')
            
        # Remove empty columns
        if self.excel_config.remove_empty_columns:
            data = data.dropna(axis=1, how='all')
            
        # Strip whitespace from string columns
        if self.excel_config.strip_whitespace:
            string_columns = data.select_dtypes(include=['object']).columns
            for col in string_columns:
                data[col] = data[col].astype(str).str.strip()
                # Convert back to NaN if empty after stripping
                data[col] = data[col].replace('', np.nan)
                
        # Log cleaning results
        rows_removed = original_rows - len(data)
        cols_removed = original_cols - len(data.columns)
        
        if rows_removed > 0 or cols_removed > 0:
            self.logger.info(f"Data cleaning: removed {rows_removed} rows, {cols_removed} columns")
            
        return data
        
    def _merge_sheets(self, data_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Merge multiple sheets into a single DataFrame.
        
        Args:
            data_dict: Dictionary of sheet DataFrames
            
        Returns:
            Merged DataFrame
        """
        if not data_dict:
            return pd.DataFrame()
            
        if len(data_dict) == 1:
            return list(data_dict.values())[0]
            
        # Ensure all DataFrames have the same columns
        all_columns = set()
        for df in data_dict.values():
            all_columns.update(df.columns)
            
        # Standardize columns across all DataFrames
        standardized_dfs = []
        for sheet_name, df in data_dict.items():
            # Add missing columns with NaN
            for col in all_columns:
                if col not in df.columns:
                    df[col] = np.nan
                    
            # Reorder columns consistently
            df = df.reindex(columns=sorted(all_columns))
            
            # Add sheet identifier if not already present
            if '_sheet_name' not in df.columns and self.excel_config.add_sheet_column:
                df['_sheet_name'] = sheet_name
                
            standardized_dfs.append(df)
            
        # Merge all DataFrames
        merged_df = pd.concat(standardized_dfs, ignore_index=True)
        
        self.logger.info(f"Merged {len(data_dict)} sheets into {len(merged_df)} rows")
        
        return merged_df
        
    async def _validate_excel_data(self, data: Union[pd.DataFrame, Dict[str, pd.DataFrame]]) -> Dict[str, Any]:
        """Validate Excel data quality.
        
        Args:
            data: DataFrame or dictionary of DataFrames to validate
            
        Returns:
            Validation results
        """
        validation_result = {
            'is_valid': True,
            'error_rate': 0.0,
            'quality_score': 1.0,
            'errors': [],
            'warnings': []
        }
        
        # Handle both single DataFrame and dictionary of DataFrames
        if isinstance(data, pd.DataFrame):
            data_dict = {'merged': data}
        else:
            data_dict = data
            
        total_cells = 0
        total_errors = 0
        
        for sheet_name, df in data_dict.items():
            if len(df) == 0:
                validation_result['warnings'].append(f"Sheet '{sheet_name}' is empty")
                continue
                
            sheet_cells = df.size
            total_cells += sheet_cells
            
            # Check required columns
            if self.excel_config.required_columns:
                missing_columns = set(self.excel_config.required_columns) - set(df.columns)
                if missing_columns:
                    validation_result['errors'].append(f"Sheet '{sheet_name}' missing required columns: {missing_columns}")
                    validation_result['is_valid'] = False
                    
            # Calculate missing data rate
            missing_cells = df.isnull().sum().sum()
            missing_rate = missing_cells / sheet_cells if sheet_cells > 0 else 0
            
            if missing_rate > 0.5:
                validation_result['warnings'].append(f"Sheet '{sheet_name}' has high missing data rate: {missing_rate:.1%}")
                
            # Check for duplicate rows
            duplicate_rows = df.duplicated().sum()
            if duplicate_rows > 0:
                duplicate_rate = duplicate_rows / len(df)
                if duplicate_rate > 0.1:
                    validation_result['warnings'].append(f"Sheet '{sheet_name}' has high duplicate rate: {duplicate_rate:.1%}")
                    
            # Check data type consistency
            for col in df.columns:
                if col.startswith('_'):  # Skip metadata columns
                    continue
                    
                # Check for mixed types in object columns
                if df[col].dtype == 'object':
                    sample_values = df[col].dropna().head(100)
                    if len(sample_values) > 0:
                        type_counts = sample_values.apply(type).value_counts()
                        if len(type_counts) > 1:
                            validation_result['warnings'].append(f"Sheet '{sheet_name}' column '{col}' has mixed data types")
                            
        # Calculate overall quality score
        error_rate = total_errors / total_cells if total_cells > 0 else 0
        missing_rate = sum(df.isnull().sum().sum() for df in data_dict.values()) / total_cells if total_cells > 0 else 0
        quality_score = max(0, 1 - error_rate - missing_rate * 0.5)
        
        validation_result['error_rate'] = error_rate
        validation_result['quality_score'] = quality_score
        
        return validation_result
        
    async def _store_excel_data(self, data: Union[pd.DataFrame, Dict[str, pd.DataFrame]], source_path: Union[str, Path], file_info: Dict[str, Any]) -> None:
        """Store Excel data in database.
        
        Args:
            data: DataFrame or dictionary of DataFrames to store
            source_path: Source file path
            file_info: File information
        """
        if not hasattr(self, 'db_ops') or not self.db_ops:
            raise ValueError("Database operations not configured")
            
        file_name = Path(source_path).stem
        
        # Handle both single DataFrame and dictionary of DataFrames
        if isinstance(data, pd.DataFrame):
            # Single merged DataFrame
            table_name = f'excel_import_{file_name.lower().replace(" ", "_")}'
            await self.db_ops.store_dataframe(data, table_name, if_exists='replace')
            
            # Create indexes
            await self._create_excel_indexes(table_name, data)
            
        else:
            # Multiple sheets
            for sheet_name, df in data.items():
                safe_sheet_name = sheet_name.lower().replace(' ', '_').replace('-', '_')
                table_name = f'excel_{file_name.lower().replace(" ", "_")}_{safe_sheet_name}'
                
                await self.db_ops.store_dataframe(df, table_name, if_exists='replace')
                
                # Create indexes
                await self._create_excel_indexes(table_name, df)
                
    async def _create_excel_indexes(self, table_name: str, data: pd.DataFrame) -> None:
        """Create indexes on Excel data table.
        
        Args:
            table_name: Name of the table
            data: DataFrame with the data
        """
        if not hasattr(self.db_ops, 'create_index'):
            return
            
        # Index on timestamp columns
        timestamp_cols = [col for col in data.columns if 'timestamp' in col.lower() or 'date' in col.lower()]
        for col in timestamp_cols:
            try:
                await self.db_ops.create_index(table_name, col)
            except Exception as e:
                self.logger.warning(f"Failed to create index on {col}: {e}")
                
        # Index on sheet name if present
        if '_sheet_name' in data.columns:
            try:
                await self.db_ops.create_index(table_name, '_sheet_name')
            except Exception as e:
                self.logger.warning(f"Failed to create index on _sheet_name: {e}")
                
    async def validate_source(self, source: Union[str, Path]) -> bool:
        """Validate Excel data source.
        
        Args:
            source: Data source to validate
            
        Returns:
            True if source is valid
        """
        try:
            source_path = Path(source)
            
            # Check if file exists
            if not source_path.exists():
                self.logger.error(f"Source file does not exist: {source}")
                return False
                
            # Check file format
            if source_path.suffix.lower() not in self.supported_formats:
                self.logger.error(f"Unsupported file format: {source_path.suffix}")
                return False
                
            # Check file size
            file_size_mb = source_path.stat().st_size / 1024 / 1024
            if file_size_mb > 2000:  # 2GB limit
                self.logger.error(f"File too large: {file_size_mb:.1f}MB")
                return False
                
            # Try to open with pandas
            try:
                excel_file = pd.ExcelFile(source_path, engine=self.excel_config.engine)
                if not excel_file.sheet_names:
                    self.logger.error("No sheets found in Excel file")
                    return False
                excel_file.close()
            except Exception as e:
                self.logger.error(f"Cannot read Excel file: {e}")
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"Source validation failed: {e}")
            return False
            
    async def get_source_info(self, source: Union[str, Path]) -> Dict[str, Any]:
        """Get information about Excel data source.
        
        Args:
            source: Data source
            
        Returns:
            Source information dictionary
        """
        try:
            source_path = Path(source)
            stat = source_path.stat()
            
            # Analyze file
            file_info = await self._analyze_excel_file(source_path)
            
            return {
                'file_path': str(source_path),
                'file_name': source_path.name,
                'file_size_bytes': stat.st_size,
                'file_size_mb': stat.st_size / 1024 / 1024,
                'file_format': source_path.suffix.lower(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'is_supported': source_path.suffix.lower() in self.supported_formats,
                'total_sheets': file_info['total_sheets'],
                'sheet_names': [sheet['name'] for sheet in file_info.get('sheets', [])],
                'engine_used': file_info['engine_used'],
                'sheets_info': file_info.get('sheets', []),
                'requires_chunked_reading': file_info['file_size_mb'] > 100
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get source info: {e}")
            return {'error': str(e)}
            
    def get_sheet_info(self, data_dict: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """Get detailed information about Excel sheets.
        
        Args:
            data_dict: Dictionary of sheet DataFrames
            
        Returns:
            Sheet information dictionary
        """
        sheet_info = {}
        
        for sheet_name, data in data_dict.items():
            info = {
                'rows': len(data),
                'columns': len(data.columns),
                'column_names': list(data.columns),
                'data_types': data.dtypes.to_dict(),
                'memory_usage_mb': data.memory_usage(deep=True).sum() / 1024 / 1024,
                'missing_data_percentage': (data.isnull().sum().sum() / data.size * 100) if data.size > 0 else 0,
                'duplicate_rows': data.duplicated().sum()
            }
            
            # Add sample data
            if len(data) > 0:
                info['sample_data'] = data.head(3).to_dict('records')
                
            sheet_info[sheet_name] = info
            
        return sheet_info
        
    def suggest_sheet_selection(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest which sheets to import based on analysis.
        
        Args:
            file_info: File analysis results
            
        Returns:
            Sheet selection suggestions
        """
        suggestions = {
            'recommended_sheets': [],
            'skip_sheets': [],
            'reasons': {}
        }
        
        for sheet in file_info.get('sheets', []):
            sheet_name = sheet['name']
            
            # Skip sheets with errors
            if 'error' in sheet:
                suggestions['skip_sheets'].append(sheet_name)
                suggestions['reasons'][sheet_name] = f"Error: {sheet['error']}"
                continue
                
            # Skip empty sheets
            if sheet.get('estimated_rows') == 0:
                suggestions['skip_sheets'].append(sheet_name)
                suggestions['reasons'][sheet_name] = "Empty sheet"
                continue
                
            # Skip sheets with very few columns (likely metadata)
            if sheet.get('column_count', 0) < 2:
                suggestions['skip_sheets'].append(sheet_name)
                suggestions['reasons'][sheet_name] = "Too few columns"
                continue
                
            # Skip sheets with common non-data names
            skip_patterns = ['summary', 'metadata', 'info', 'readme', 'instructions']
            if any(pattern in sheet_name.lower() for pattern in skip_patterns):
                suggestions['skip_sheets'].append(sheet_name)
                suggestions['reasons'][sheet_name] = "Likely metadata sheet"
                continue
                
            # Recommend data sheets
            suggestions['recommended_sheets'].append(sheet_name)
            suggestions['reasons'][sheet_name] = "Data sheet"
            
        return suggestions