"""NLG (Network Log) data importer.

This module provides functionality for importing and processing NLG data files.
NLG files typically contain network log data for telecommunications networks.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime
import re

import pandas as pd
from sqlalchemy import BigInteger, Column, DateTime, Text, func

from .base import AbstractImporter, TelecomImportError, ImportResult
from .base.abstract_importer import ImportMetrics, ImportStatus

# Configure logging
logger = logging.getLogger(__name__)


class NLGImporter(AbstractImporter):
    """Network Log data importer."""

    def __init__(
        self,
        settings_or_source_path: Union[str, Path, Any] = "",
        config: Optional[Dict[str, Any]] = None,
        db_session=None,
        performance_logger=None,
        **kwargs,
    ):
        """Initialize NLG importer.

        Args:
            settings_or_source_path: Settings object or path to the NLG file
            config: Configuration dictionary
            db_session: Database session for direct database operations
            performance_logger: Logger for performance metrics
            **kwargs: Additional configuration options
        """
        self.name = "NLGImporter"
        self.supported_formats = ["xlsb", "xlsx", "xls", "csv"]
        
        # Handle Settings object or source path
        if hasattr(settings_or_source_path, 'get') or hasattr(settings_or_source_path, 'database'):
            # It's a Settings/Config object
            self.settings = settings_or_source_path
            source_path = ""
        else:
            # It's a source path
            self.settings = None
            source_path = settings_or_source_path
            
        self.config = config or {}
        self.db_session = db_session
        self.performance_logger = performance_logger

        # Initialize database components if session provided
        if db_session:
            from src.database.operations import BulkOperations
            from src.database.schema import SchemaManager

            self.schema_manager = SchemaManager(db_session)
            self.bulk_operations = BulkOperations(db_session)

        # Remove source_path from kwargs to avoid duplicate argument error
        filtered_kwargs = {k: v for k, v in kwargs.items() if k != 'source_path'}
        super().__init__(config, **filtered_kwargs)

        # Set source path after initialization
        self.source_path = Path(settings_or_source_path) if settings_or_source_path else None

    def set_database_context(self, pool=None, db_manager=None, db_ops=None, schema_manager=None):
        """Set database context for the importer."""
        if pool:
            self.pool_manager = pool
        if db_manager:
            self.db_manager = db_manager
        if db_ops:
            self.db_ops = db_ops
        if schema_manager:
            self.schema_manager = schema_manager

        # Initialize bulk operations if we have a database session
        if schema_manager and hasattr(schema_manager, 'pool'):
            from src.database.operations.bulk_operations import BulkOperations
            self.bulk_operations = BulkOperations(schema_manager.pool)

    def validate_file(self, file_path: Union[str, Path]) -> bool:
        """Validate NLG data file.

        Args:
            file_path: Path to the file

        Returns:
            bool: True if file is valid

        Raises:
            ImportError: If file is invalid
        """
        path = Path(file_path)

        # Check if file exists
        if not path.exists():
            raise ImportError(f"File not found: {file_path}")

        # Check file extension
        if path.suffix.lower() not in [".xlsb", ".xlsx", ".xls"]:
            raise ImportError(
                f"Unsupported file format: {path.suffix}. "
                f"Supported formats: {', '.join(self.supported_formats)}"
            )

        # Try to read the file to validate it's a valid Excel file
        try:
            # Get the optimal engine for this file type
            engine = self._get_optimal_engine(path.suffix.lower())
            # Just read the first few rows to validate
            pd.read_excel(file_path, nrows=1, engine=engine)
        except Exception as e:
            raise ImportError(f"Invalid Excel file: {str(e)}")

        return True

    def _get_optimal_engine(self, file_extension: str) -> str:
        """Get optimal pandas engine for file type.

        Args:
            file_extension: File extension (e.g., '.xlsb', '.xlsx')

        Returns:
            str: Engine name
        """
        engine_map = {
            '.xlsb': 'pyxlsb',  # Fastest for binary Excel files
            '.xlsx': 'openpyxl',  # Good for modern Excel files
            '.xls': 'xlrd'  # For legacy Excel files
        }
        return engine_map.get(file_extension, 'openpyxl')

    def _clean_column_name(self, col_name: str) -> str:
        """Clean and standardize column names using unified rules.

        Args:
            col_name: Original column name

        Returns:
            str: Cleaned column name
        """
        import re
        import unicodedata

        # Convert to string and strip whitespace
        clean_name = str(col_name).strip()

        # Normalize unicode characters (handle German, Spanish, etc.)
        clean_name = unicodedata.normalize('NFKD', clean_name)
        clean_name = clean_name.encode('ascii', 'ignore').decode('ascii')

        # Convert to lowercase
        clean_name = clean_name.lower()

        # Replace spaces, hyphens, and other special chars with underscores
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', clean_name)

        # Replace multiple underscores with single underscore
        clean_name = re.sub(r'_+', '_', clean_name)

        # Remove leading/trailing underscores
        clean_name = clean_name.strip('_')

        # Ensure it doesn't start with a number
        if clean_name and clean_name[0].isdigit():
            clean_name = f'col_{clean_name}'

        # Ensure minimum length
        if not clean_name:
            clean_name = 'unnamed_column'

        return clean_name

    def _read_excel_chunked(self, file_path: Union[str, Path], sheet_name: str,
                           skip_rows: int, engine: str, chunk_size: int = 10000) -> pd.DataFrame:
        """Read Excel file in chunks to optimize memory usage with improved performance.

        Args:
            file_path: Path to Excel file
            sheet_name: Sheet name to read
            skip_rows: Number of rows to skip
            engine: Pandas engine to use
            chunk_size: Number of rows per chunk

        Returns:
            pd.DataFrame: Combined data from all chunks
        """
        logger.info(f"Reading Excel file in chunks of {chunk_size} rows using optimized method")

        try:
            # For .xlsb files, use a more efficient approach
            if str(file_path).lower().endswith('.xlsb'):
                # Read the entire file at once for .xlsb as chunked reading is often slower
                logger.info("Using optimized .xlsb reading strategy")
                data = pd.read_excel(
                    file_path,
                    sheet_name=sheet_name,
                    skiprows=skip_rows,
                    header=0,
                    engine=engine
                )
                return data

            # For other Excel formats, use improved chunked reading
            # Open the Excel file once and keep it open
            with pd.ExcelFile(file_path, engine=engine) as excel_file:
                # First, read just the header to get column names
                header_df = pd.read_excel(
                    excel_file,
                    sheet_name=sheet_name,
                    skiprows=skip_rows,
                    nrows=1,
                    header=0
                )
                columns = header_df.columns.tolist()

                # Read file in chunks using the same ExcelFile object
                chunks = []
                chunk_num = 0
                max_chunks = 1000  # Safety limit to prevent infinite loops
                consecutive_empty_chunks = 0
                max_empty_chunks = 3  # Stop after 3 consecutive empty chunks

                while chunk_num < max_chunks:
                    try:
                        # Calculate skip rows for this chunk
                        skip_for_chunk = skip_rows + 1 + (chunk_num * chunk_size)  # +1 for header

                        chunk_df = pd.read_excel(
                            excel_file,
                            sheet_name=sheet_name,
                            skiprows=skip_for_chunk,
                            nrows=chunk_size,
                            header=None,  # No header since we're skipping it
                            names=columns  # Use the column names we got earlier
                        )

                        # If chunk is empty, we might be reaching the end
                        if chunk_df.empty:
                            consecutive_empty_chunks += 1
                            if consecutive_empty_chunks >= max_empty_chunks:
                                logger.info(f"Reached end of file after {consecutive_empty_chunks} empty chunks")
                                break
                        else:
                            consecutive_empty_chunks = 0  # Reset counter
                            chunks.append(chunk_df)

                        chunk_num += 1

                        # Log progress for large files (reduced frequency)
                        if chunk_num % 5 == 0:
                            logger.info(f"Processed {chunk_num} chunks ({len(chunks) * chunk_size} rows)")

                    except Exception as e:
                        # If we get an error, it might mean we've reached the end
                        logger.debug(f"Chunk reading ended at chunk {chunk_num}: {e}")
                        break

                if chunk_num >= max_chunks:
                    logger.warning(f"Reached maximum chunk limit ({max_chunks}), stopping to prevent infinite loop")

                if not chunks:
                    logger.warning("No data chunks were read successfully")
                    return pd.DataFrame()

                # Combine all chunks efficiently
                logger.info(f"Combining {len(chunks)} chunks into final DataFrame")
                combined_df = pd.concat(chunks, ignore_index=True)
                return combined_df

        except Exception as e:
            logger.error(f"Error in chunked reading: {e}")
            # Fallback to regular reading
            logger.info("Falling back to regular Excel reading")
            return pd.read_excel(
                file_path,
                sheet_name=sheet_name,
                skiprows=skip_rows,
                header=0,
                engine=engine
            )

    def validate_data_structure(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate NLG data structure.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []

        # Check if data is empty
        if data.empty:
            errors.append("Data is empty")
            return False, errors

        # Accept any column structure - no required columns validation

        # Check for basic data integrity
        if data.isnull().all().all():
            errors.append("All data is null")

        return len(errors) == 0, errors

    def validate_data_values(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate NLG data values.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple of (is_valid, error_messages)
        """
        errors = []

        # Validate log time if present
        time_columns = [col for col in data.columns if 'time' in col.lower() or 'date' in col.lower()]
        for col in time_columns:
            if data[col].isnull().all():
                errors.append(f"All values in {col} are null")

        # Validate network type if present
        network_columns = [col for col in data.columns if 'network' in col.lower() or 'type' in col.lower()]
        for col in network_columns:
            if data[col].isnull().all():
                errors.append(f"All values in {col} are null")

        # Validate cell ID if present
        id_columns = [col for col in data.columns if 'id' in col.lower() or 'cell' in col.lower()]
        for col in id_columns:
            if data[col].isnull().all():
                errors.append(f"All values in {col} are null")

        # Check for completely empty rows
        empty_rows = data.isnull().all(axis=1).sum()
        if empty_rows > 0:
            errors.append(f"Found {empty_rows} completely empty rows")

        return len(errors) == 0, errors

    def transform_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Transform NLG data.

        Args:
            data: DataFrame to transform

        Returns:
            pd.DataFrame: Transformed data
        """
        # Create a copy to avoid modifying the original
        transformed = data.copy()

        # Standardize column names to uppercase
        transformed.columns = [col.upper() for col in transformed.columns]

        # Convert date/time columns to datetime
        time_columns = [col for col in transformed.columns if 'TIME' in col or 'DATE' in col]
        for col in time_columns:
            if col in transformed.columns:
                transformed[col] = pd.to_datetime(
                    transformed[col], errors="coerce"
                )

        # Add import timestamp
        transformed["IMPORT_TIMESTAMP"] = datetime.now()

        # Fill missing values for critical columns
        if 'NETWORK_TYPE' in transformed.columns:
            transformed['NETWORK_TYPE'] = transformed['NETWORK_TYPE'].fillna('UNKNOWN')
        
        if 'CELL_ID' in transformed.columns:
            transformed['CELL_ID'] = transformed['CELL_ID'].fillna('N/A')

        return transformed

    def process_batch(self, data: pd.DataFrame) -> ImportResult:
        """Process a batch of NLG data.

        Args:
            data: DataFrame to process

        Returns:
            ImportResult: Result of the batch processing
        """
        try:
            # Transform data
            transformed_data = self.transform_data(data)

            # Insert into database if session available
            if hasattr(self, "bulk_operations"):
                table_name = self.config.get(
                    "nlg_table_name", "network_location_geometries"
                )

                # Create table if it doesn't exist
                # Note: Table creation will be handled by create_nlg_table_sync method
                self.create_nlg_table_sync(table_name, list(transformed_data.columns))

                # Insert data directly with correct schema
                schema = self.config.get("nlg_schema", "nlg_to2")
                self.bulk_operations.bulk_insert_dataframe(transformed_data, table_name, schema=schema)

            return ImportResult(
                success=True,
                records_imported=len(transformed_data),
                metadata={
                    "columns": list(transformed_data.columns),
                },
            )

        except Exception as e:
            logger.error(f"Error processing NLG batch: {str(e)}", exc_info=True)
            return ImportResult(
                success=False, error_message=f"Batch processing error: {str(e)}"
            )

    async def create_nlg_table(self, table_name: str, data_columns: List[str] = None) -> None:
        """Create NLG table with appropriate schema.

        Args:
            table_name: Name of the table to create
            data_columns: List of data column names from the Excel file
        """
        from src.database.schema import TableSchema, ColumnSchema
        import re

        # Base columns with primary key and timestamp
        columns = [
            ColumnSchema(name="id", data_type="BIGSERIAL", primary_key=True),
            ColumnSchema(name="created_at", data_type="TIMESTAMP", default="CURRENT_TIMESTAMP"),
        ]
        
        # Add data columns as TEXT type
        if data_columns:
            for col_name in data_columns:
                # Use unified column name cleaning
                clean_name = self._clean_column_name(col_name)

                if clean_name and clean_name not in ['id', 'created_at']:
                    columns.append(ColumnSchema(name=clean_name, data_type="TEXT"))

        # Ensure schema exists first
        await self.schema_manager.ensure_schema_exists("nlg_to2")

        table_schema = TableSchema(
            name=table_name,
            schema="nlg_to2",  # Set the schema name
            columns=columns,
            indexes=[],  # Simplified - no indexes for now to avoid complexity
        )

        # Create table
        await self.schema_manager.create_table(table_schema)

    def create_nlg_table_sync(self, table_name: str, data_columns: List[str] = None) -> None:
        """Create NLG table in database with dynamic schema (synchronous version).

        Args:
            table_name: Name of the table to create
            data_columns: List of data column names from the file
        """
        import asyncio
        import concurrent.futures

        async def _create_table_with_check():
            """Async helper to create table with existence check."""
            try:
                # Check if table exists first
                table_exists = await self.schema_manager.table_exists(table_name, "nlg_to2")
                if table_exists:
                    # Table already exists, no need to create
                    return True

                # Create the table
                await self.create_nlg_table(table_name, data_columns)
                return True
            except Exception as e:
                # Only log if it's not a "table already exists" error
                error_msg = str(e).lower()
                if "already exists" not in error_msg and "duplicate" not in error_msg:
                    if hasattr(self, 'logger'):
                        self.logger.debug(f"Table creation info for {table_name}: {e}")
                return False

        # Run the async function safely
        try:
            # Try to get current event loop
            try:
                loop = asyncio.get_running_loop()
                # We're in an event loop, create a new thread with its own loop
                def run_in_new_loop():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(_create_table_with_check())
                    finally:
                        new_loop.close()
                        asyncio.set_event_loop(None)

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_new_loop)
                    result = future.result(timeout=30)  # 30 second timeout
                    return result
            except RuntimeError:
                # No event loop running, safe to use asyncio.run
                return asyncio.run(_create_table_with_check())
        except concurrent.futures.TimeoutError:
            # Timeout is not necessarily an error - table might be created by another process
            if hasattr(self, 'logger'):
                self.logger.debug(f"Table creation timeout for {table_name} - table may already exist")
            return False
        except Exception as e:
            # Only log if it's a real error, not a "table exists" situation
            error_msg = str(e).lower()
            if "already exists" not in error_msg and "duplicate" not in error_msg:
                if hasattr(self, 'logger'):
                    self.logger.debug(f"Table creation info for {table_name}: {e}")
            return False

    def _detect_csv_structure(self, file_path: Path) -> dict:
        """Detect CSV file structure including comments, headers, and delimiters."""
        import csv

        structure = {
            'skip_rows': 0,
            'header_row': 0,
            'delimiter': ',',
            'encoding': 'utf-8',
            'comment_char': None
        }

        # Try different encodings
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    lines = []
                    for i, line in enumerate(f):
                        lines.append(line.strip())
                        if i >= 20:  # Read first 20 lines for analysis
                            break

                structure['encoding'] = encoding
                break
            except UnicodeDecodeError:
                continue

        if not lines:
            return structure

        # Detect comment lines and skip rows
        skip_rows = 0
        for i, line in enumerate(lines):
            if line.startswith('#') or line.startswith('//') or not line.strip():
                skip_rows += 1
            else:
                break

        structure['skip_rows'] = skip_rows

        # Find header row (first non-comment, non-empty line)
        header_line = None
        for i in range(skip_rows, len(lines)):
            if lines[i].strip():
                header_line = lines[i]
                break

        if header_line:
            # Detect delimiter
            sniffer = csv.Sniffer()
            try:
                dialect = sniffer.sniff(header_line, delimiters=',;\t|:')
                structure['delimiter'] = dialect.delimiter
            except:
                # Fallback delimiter detection
                delimiters = [',', ';', '\t', '|', ':']
                delimiter_counts = {}
                for delim in delimiters:
                    delimiter_counts[delim] = header_line.count(delim)

                if delimiter_counts:
                    structure['delimiter'] = max(delimiter_counts, key=delimiter_counts.get)

        return structure

    async def import_data(self, file_path: Union[str, Path] = None, **kwargs) -> ImportResult:
        """Import NLG data from Excel files.

        Args:
            file_path: Path to the file (can be positional or keyword argument)
            **kwargs: Additional import options
                - batch_size: Number of records per batch
                - sheet_name: Excel sheet name or index

        Returns:
            ImportResult: Result of the import operation
        """
        file_path = file_path or kwargs.get("file_path") or self.source_path
        batch_size = kwargs.get("batch_size", 1000)
        
        # Get configuration from settings or config
        if self.settings:
            # Get from settings object
            data_sources_config = self.settings.get('data_sources', {})
            nlg_config = data_sources_config.get('nlg', {})
        else:
            # Get from config object (Pydantic model)
            data_sources_config = getattr(self.config, 'data_sources', {})
            if isinstance(data_sources_config, dict):
                nlg_config = data_sources_config.get('nlg', {})
            else:
                nlg_config = {}
        
        # Extract configuration parameters
        sheet_name = nlg_config.get('sheet_name', 'Techno_2G_4G_5G')
        skip_rows = nlg_config.get('skip_rows', 4)
        header_row = nlg_config.get('header_row', 4)
        schema_name = nlg_config.get('schema_name', 'nlg_to2')
        table_name_pattern = nlg_config.get('table_name_pattern', 'nlg_cube_aktuell_{date}')

        start_time = time.time()
        
        try:
            # Quick file validation without reading data
            path = Path(file_path)
            if not path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            if path.stat().st_size == 0:
                raise ValueError(f"File is empty: {file_path}")

            # Start performance monitoring if available
            if self.performance_logger:
                self.performance_logger.start_operation("nlg_import")

            # Read file with format detection
            path = Path(file_path)
            try:
                if path.suffix.lower() == '.csv':
                    # Handle CSV files with configuration-driven parsing
                    structure = self._detect_csv_structure_with_config(path, 'nlg')

                    import csv
                    try:
                        data = pd.read_csv(
                            file_path,
                            encoding=structure['encoding'],
                            delimiter=structure['delimiter'],
                            skiprows=structure['skip_rows'],
                            header=structure['header_row'],  # Use configured header row
                            engine='python',  # More flexible parser
                            on_bad_lines='skip',  # Skip problematic lines
                            quoting=csv.QUOTE_MINIMAL,
                            skipinitialspace=True,
                            comment='#'  # Skip lines starting with #
                        )
                    except (UnicodeDecodeError, pd.errors.ParserError) as e:
                        logger.warning(f"Standard CSV reading failed: {e}, trying fallback options")
                        # Fallback with more lenient parsing
                        encodings = ['utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
                        for encoding in encodings:
                            try:
                                data = pd.read_csv(
                                    file_path,
                                    encoding=encoding,
                                    delimiter=structure['delimiter'],
                                    skiprows=structure['skip_rows'],
                                    header=structure['header_row'],  # Use configured header row
                                    engine='python',
                                    on_bad_lines='skip',
                                    quoting=csv.QUOTE_NONE,
                                    skipinitialspace=True,
                                    low_memory=False,
                                    comment='#'
                                )
                                break
                            except Exception:
                                continue
                        else:
                            raise ValueError(f"Could not read CSV file with any supported encoding: {file_path}")
                else:
                    # Handle Excel files with optimizations
                    # Determine optimal engine for file type
                    engine = self._get_optimal_engine(path.suffix.lower())

                    # First, get available sheet names
                    excel_file = pd.ExcelFile(file_path, engine=engine)
                    available_sheets = excel_file.sheet_names

                    # Try to find the configured sheet, or use the first available sheet
                    if sheet_name in available_sheets:
                        target_sheet = sheet_name
                    else:
                        target_sheet = available_sheets[0]  # Use first sheet as fallback
                        logger.warning(f"Sheet '{sheet_name}' not found, using '{target_sheet}' instead")

                    # Optimize reading strategy based on file type and size
                    file_size = path.stat().st_size
                    file_size_mb = file_size / 1024 / 1024

                    # For .xlsb files, always read directly (chunked reading is often slower)
                    if path.suffix.lower() == '.xlsb':
                        logger.info(f"Reading .xlsb file directly ({file_size_mb:.1f}MB)")
                        data = pd.read_excel(
                            file_path,
                            sheet_name=target_sheet,
                            skiprows=skip_rows,
                            header=0,
                            engine=engine
                        )
                    else:
                        # For other formats, use chunked reading for files > 20MB
                        use_chunked_reading = file_size > 20 * 1024 * 1024  # Lowered threshold to 20MB

                        if use_chunked_reading:
                            logger.info(f"Large file detected ({file_size_mb:.1f}MB), using chunked reading")
                            data = self._read_excel_chunked(
                                file_path, target_sheet, skip_rows, engine
                            )
                        else:
                            # Read with skip_rows and header configuration
                            data = pd.read_excel(
                                file_path,
                                sheet_name=target_sheet,
                                skiprows=skip_rows,
                                header=0,  # After skipping rows, header is at row 0
                                engine=engine
                            )

                    excel_file.close()  # Explicitly close to free memory

            except Exception as e:
                raise ImportError(f"Failed to read file: {str(e)}")

            # Generate table name from filename
            filename = path.stem  # Get filename without extension
            # Extract date from filename (assuming format like NLG_CUBE_aktuell_2023-01-03)
            import re
            from src.database.utils.validators import InputValidator

            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
            if date_match:
                date_str = date_match.group(1).replace('-', '')
                raw_table_name = table_name_pattern.replace('{date}', date_str)
            else:
                # Fallback to timestamp if no date found
                date_str = datetime.now().strftime('%Y%m%d')
                raw_table_name = table_name_pattern.replace('{date}', date_str)

            # Validate and fix table name to meet PostgreSQL requirements
            table_name = InputValidator.validate_and_fix_table_name(raw_table_name)
            
            # Add schema prefix
            full_table_name = f"{schema_name}.{table_name}"

            # Remove duplicate columns and standardize column names
            from src.utils.column_deduplicator import ColumnDeduplicator
            
            # Remove duplicate columns, keeping the most complete one
            data, dedup_report = ColumnDeduplicator.remove_duplicate_columns(
                data, keep_strategy='best'
            )
            
            if dedup_report['total_removed'] > 0:
                logger.info(f"Removed {dedup_report['total_removed']} duplicate columns in NLG data")
            
            # Clean column names and prepare data
            original_columns = [str(col).strip() for col in data.columns]
            data_columns = original_columns.copy()
            
            # Create mapping from original to clean column names
            column_mapping = {}
            clean_columns = []
            
            for col_name in original_columns:
                # Use unified column name cleaning
                clean_name = self._clean_column_name(col_name)

                if clean_name and clean_name not in ['id', 'created_at']:
                    column_mapping[col_name] = clean_name
                    clean_columns.append(clean_name)
            
            # Rename columns to match database schema
            data = data.rename(columns=column_mapping)
            
            # Remove empty rows
            data = data.dropna(how='all')

            # Optimize memory usage before string conversion
            logger.info(f"Optimizing memory usage for {len(data)} records")

            # Convert all data to string (TEXT format) with memory optimization
            for col in data.columns:
                # Use categorical for repeated values to save memory
                if data[col].dtype == 'object':
                    unique_ratio = data[col].nunique() / len(data)
                    if unique_ratio < 0.5:  # If less than 50% unique values
                        data[col] = data[col].astype('category').astype(str).replace('nan', '')
                    else:
                        data[col] = data[col].astype(str).replace('nan', '')
                else:
                    data[col] = data[col].astype(str).replace('nan', '')

            # Process data in batches with optimized database operations
            total_records = len(data)
            processed_records = 0

            # Pre-create schema and table once (outside the loop)
            if hasattr(self, "bulk_operations"):
                # Create schema if it doesn't exist
                if not await self.schema_manager.schema_exists(schema_name):
                    await self.schema_manager.create_schema(schema_name)

                # Create table if it doesn't exist
                if not await self.schema_manager.table_exists(table_name, schema_name):
                    await self.create_nlg_table(full_table_name, original_columns)

            # Process in larger batches for better performance
            optimized_batch_size = min(batch_size * 2, 5000)  # Increase batch size but cap at 5000
            logger.info(f"Processing {total_records} records in batches of {optimized_batch_size}")

            import threading
            import queue

            # Process data in batches using simple sequential processing
            for i in range(0, total_records, optimized_batch_size):
                batch = data.iloc[i : i + optimized_batch_size].copy()
                
                try:
                    if hasattr(self, "bulk_operations"):
                        self.bulk_operations.bulk_insert_dataframe(batch, table_name, schema=schema_name)
                    processed_records += len(batch)
                    
                    # Log progress
                    progress = (processed_records / total_records) * 100
                    if i % (optimized_batch_size * 10) == 0:  # Log every 10 batches
                        logger.info(f"Progress: {processed_records}/{total_records} ({progress:.1f}%)")
                        
                except Exception as e:
                    logger.error(f"Batch processing error: {e}")
                    continue
            
            logger.info(f"Final Progress: {processed_records}/{total_records} records processed")

            # Stop performance monitoring if available
            if self.performance_logger:
                self.performance_logger.end_operation("nlg_import")

            # Create metrics
            metrics = ImportMetrics()
            metrics.records_processed = processed_records
            metrics.processing_time_seconds = time.time() - start_time

            return ImportResult(
                status=ImportStatus.COMPLETED,
                metrics=metrics,
                source_info={
                    "source_path": str(path),
                    "file_size_bytes": path.stat().st_size,
                    "file_type": path.suffix,
                    "columns": list(data.columns),
                }
            )

        except Exception as e:
            logger.error(f"NLG import error: {str(e)}", exc_info=True)
            end_time = time.time()
            error_metrics = ImportMetrics(
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.fromtimestamp(end_time),
                records_processed=0,
                records_validated=0,
                records_failed=0,
                processing_time_seconds=end_time - start_time,
                errors=[str(e)]
            )
            return ImportResult(
                status=ImportStatus.FAILED,
                data=None,
                metrics=error_metrics,
                source_info={'path': str(file_path) if file_path else None},
                validation_results=[],
                error_message=f"Import failed: {str(e)}",
                warnings=[]
            )

    def import_file(self, file_path: Union[str, Path]) -> ImportResult:
        """Synchronous wrapper for import_data.

        Args:
            file_path: Path to the file

        Returns:
            ImportResult: Result of the import operation
        """
        import asyncio

        # Update source path
        self.source_path = Path(file_path)

        # Run import asynchronously
        return asyncio.run(self.import_data(file_path=file_path))

    def get_source_info(self) -> Dict[str, Any]:
        """Get source information for the importer."""
        return {
            'source_path': str(self.source_path) if self.source_path else None,
            'data_type': 'nlg',
            'supported_formats': self.supported_formats
        }

    async def validate_source(self, source_path: str = None) -> bool:
        """Validate the source file."""
        try:
            from pathlib import Path

            path = Path(source_path or self.source_path)

            # Check if file exists
            if not path.exists():
                return False

            # Check file extension
            if path.suffix.lower() not in ['.xlsx', '.xls']:
                return False

            return True

        except Exception:
            return False
