"""Column deduplication utility for handling duplicate columns in DataFrames.

This module provides functionality to detect and remove duplicate columns,
keeping the most complete version of each duplicate column.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import logging

logger = logging.getLogger(__name__)


class ColumnDeduplicator:
    """Utility class for handling duplicate columns in DataFrames."""
    
    @staticmethod
    def detect_duplicate_columns(df: pd.DataFrame) -> Dict[str, List[str]]:
        """Detect duplicate column names in DataFrame.
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            Dictionary mapping base column names to lists of duplicate column names
        """
        duplicates = {}
        seen_columns = set()
        
        for col in df.columns:
            clean_col = str(col).strip().lower()
            
            if clean_col in seen_columns:
                if clean_col not in duplicates:
                    duplicates[clean_col] = []
                duplicates[clean_col].append(col)
            else:
                seen_columns.add(clean_col)
                
        return duplicates
    
    @staticmethod
    def calculate_column_completeness(df: pd.DataFrame, column: str) -> float:
        """Calculate completeness score for a column.
        
        Args:
            df: DataFrame containing the column
            column: Column name to analyze
            
        Returns:
            Completeness score (0.0 to 1.0)
        """
        if column not in df.columns:
            return 0.0
            
        total_rows = len(df)
        if total_rows == 0:
            return 0.0
            
        # Handle case where column name appears multiple times (duplicate columns)
        column_data = df[column]
        if isinstance(column_data, pd.DataFrame):
            # If multiple columns with same name, use the first one
            column_data = column_data.iloc[:, 0]
            
        # Count non-null, non-empty values
        non_null_count = column_data.notna().sum()
        
        # For string columns, also check for empty strings
        if column_data.dtype == 'object':
            non_empty_count = (column_data.astype(str).str.strip() != '').sum()
            valid_count = min(non_null_count, non_empty_count)
        else:
            valid_count = non_null_count
            
        return valid_count / total_rows
    
    @staticmethod
    def calculate_column_uniqueness(df: pd.DataFrame, column: str) -> float:
        """Calculate uniqueness score for a column.
        
        Args:
            df: DataFrame containing the column
            column: Column name to analyze
            
        Returns:
            Uniqueness score (0.0 to 1.0)
        """
        if column not in df.columns:
            return 0.0
            
        # Handle case where column name appears multiple times (duplicate columns)
        column_data = df[column]
        if isinstance(column_data, pd.DataFrame):
            # If multiple columns with same name, use the first one
            column_data = column_data.iloc[:, 0]
            
        valid_values = column_data.dropna()
        if len(valid_values) == 0:
            return 0.0
            
        unique_count = valid_values.nunique()
        return unique_count / len(valid_values)
    
    @staticmethod
    def calculate_column_score(df: pd.DataFrame, column: str) -> float:
        """Calculate overall quality score for a column.
        
        Args:
            df: DataFrame containing the column
            column: Column name to analyze
            
        Returns:
            Quality score (0.0 to 1.0)
        """
        completeness = ColumnDeduplicator.calculate_column_completeness(df, column)
        uniqueness = ColumnDeduplicator.calculate_column_uniqueness(df, column)
        
        # Weight completeness more heavily than uniqueness
        score = (completeness * 0.7) + (uniqueness * 0.3)
        return score
    
    @staticmethod
    def select_best_duplicate_column(df: pd.DataFrame, duplicate_columns: List[str]) -> str:
        """Select the best column from a list of duplicate columns.
        
        Args:
            df: DataFrame containing the columns
            duplicate_columns: List of duplicate column names
            
        Returns:
            Name of the best column to keep
        """
        if not duplicate_columns:
            return None
            
        if len(duplicate_columns) == 1:
            return duplicate_columns[0]
            
        best_column = duplicate_columns[0]
        best_score = ColumnDeduplicator.calculate_column_score(df, best_column)
        
        for col in duplicate_columns[1:]:
            score = ColumnDeduplicator.calculate_column_score(df, col)
            if score > best_score:
                best_score = score
                best_column = col
                
        return best_column
    
    @staticmethod
    def remove_duplicate_columns(df: pd.DataFrame, 
                               keep_strategy: str = 'best') -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Remove duplicate columns from DataFrame.
        
        Args:
            df: DataFrame to process
            keep_strategy: Strategy for handling duplicates:
                - 'best': Keep the most complete column (default)
                - 'first': Keep the first occurrence
                - 'last': Keep the last occurrence
                - 'rename': Rename duplicates with incremental suffixes
            
        Returns:
            Tuple of (cleaned DataFrame, removal report)
        """
        if df.empty:
            return df.copy(), {'removed_columns': [], 'kept_columns': [], 'duplicate_groups': {}, 'renamed_columns': {}}
            
        # Handle rename strategy differently
        if keep_strategy == 'rename':
            return ColumnDeduplicator._rename_duplicate_columns(df)
            
        # Detect duplicate columns for removal strategies
        duplicate_groups = {}
        columns_to_keep_indices = []
        columns_kept = []
        
        # Group columns by their cleaned names with indices
        column_groups = {}
        for idx, col in enumerate(df.columns):
            clean_name = str(col).strip().lower()
            if clean_name not in column_groups:
                column_groups[clean_name] = []
            column_groups[clean_name].append((idx, col))
        
        # Process each group
        for clean_name, column_info_list in column_groups.items():
            if len(column_info_list) > 1:
                # This is a duplicate group
                columns = [col for idx, col in column_info_list]
                duplicate_groups[clean_name] = columns
                
                if keep_strategy == 'best':
                    # Calculate scores for each column and find the best one
                    best_idx = 0
                    best_score = -1
                    for i, (idx, col) in enumerate(column_info_list):
                        # Use column index to access specific column
                        col_data = df.iloc[:, idx]
                        score = ColumnDeduplicator.calculate_column_completeness(df, col) * 0.7 + \
                               ColumnDeduplicator.calculate_column_uniqueness(df, col) * 0.3
                        if score > best_score:
                            best_score = score
                            best_idx = i
                    
                    keep_idx, best_column = column_info_list[best_idx]
                elif keep_strategy == 'first':
                    keep_idx, best_column = column_info_list[0]
                elif keep_strategy == 'last':
                    keep_idx, best_column = column_info_list[-1]
                else:
                    keep_idx, best_column = column_info_list[0]  # Default to first
                
                columns_to_keep_indices.append(keep_idx)
                columns_kept.append(best_column)
                        
                logger.info(f"Duplicate columns for '{clean_name}': {columns}, keeping '{best_column}'")
            else:
                # Single column, keep it
                idx, col = column_info_list[0]
                columns_to_keep_indices.append(idx)
                columns_kept.append(col)
        
        # Create cleaned DataFrame by selecting only the columns to keep
        cleaned_df = df.iloc[:, columns_to_keep_indices]
        
        # Calculate removed columns
        all_indices = set(range(len(df.columns)))
        kept_indices = set(columns_to_keep_indices)
        removed_indices = all_indices - kept_indices
        columns_to_remove = [df.columns[i] for i in removed_indices]
        
        # Create report
        report = {
            'removed_columns': columns_to_remove,
            'kept_columns': columns_kept,
            'duplicate_groups': duplicate_groups,
            'total_removed': len(columns_to_remove),
            'original_column_count': len(df.columns),
            'final_column_count': len(cleaned_df.columns),
            'renamed_columns': {}  # Empty for removal strategies
        }
        
        if columns_to_remove:
            logger.info(f"Removed {len(columns_to_remove)} duplicate columns: {columns_to_remove}")
        
        return cleaned_df, report
    
    @staticmethod
    def _rename_duplicate_columns(df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """Rename duplicate columns with incremental suffixes.
        
        Args:
            df: DataFrame to process
            
        Returns:
            Tuple of (DataFrame with renamed columns, rename report)
        """
        if df.empty:
            return df.copy(), {'removed_columns': [], 'kept_columns': [], 'duplicate_groups': {}, 'renamed_columns': {}}
            
        # Track column name occurrences
        column_counts = {}
        new_column_names = []
        renamed_columns = {}
        duplicate_groups = {}
        
        for idx, col in enumerate(df.columns):
            clean_name = str(col).strip().lower()
            
            if clean_name not in column_counts:
                column_counts[clean_name] = 0
                duplicate_groups[clean_name] = []
            
            column_counts[clean_name] += 1
            duplicate_groups[clean_name].append(col)
            
            if column_counts[clean_name] == 1:
                # First occurrence, keep original name
                new_name = col
            else:
                # Subsequent occurrences, add suffix
                new_name = f"{col}_{column_counts[clean_name] - 1}"
                renamed_columns[col] = new_name
                
            new_column_names.append(new_name)
        
        # Create new DataFrame with renamed columns
        renamed_df = df.copy()
        renamed_df.columns = new_column_names
        
        # Filter duplicate groups to only include actual duplicates
        actual_duplicate_groups = {k: v for k, v in duplicate_groups.items() if len(v) > 1}
        
        # Create report
        report = {
            'removed_columns': [],  # No columns removed in rename strategy
            'kept_columns': list(new_column_names),
            'duplicate_groups': actual_duplicate_groups,
            'renamed_columns': renamed_columns,
            'total_removed': 0,
            'total_renamed': len(renamed_columns),
            'original_column_count': len(df.columns),
            'final_column_count': len(renamed_df.columns)
        }
        
        if renamed_columns:
            logger.info(f"Renamed {len(renamed_columns)} duplicate columns: {renamed_columns}")
        
        return renamed_df, report
    
    @staticmethod
    def standardize_column_names(df: pd.DataFrame) -> pd.DataFrame:
        """Standardize column names to lowercase and clean format.
        
        Args:
            df: DataFrame to process
            
        Returns:
            DataFrame with standardized column names
        """
        if df.empty:
            return df.copy()
            
        # First remove duplicates, then standardize names
        cleaned_df, _ = ColumnDeduplicator.remove_duplicate_columns(df)
        
        # Standardize remaining column names
        new_columns = []
        for col in cleaned_df.columns:
            clean_name = str(col).strip().lower()
            # Replace special characters with underscores
            import re
            clean_name = re.sub(r'[^a-z0-9_]', '_', clean_name)
            clean_name = re.sub(r'_+', '_', clean_name)  # Remove multiple underscores
            clean_name = clean_name.strip('_')  # Remove leading/trailing underscores
            new_columns.append(clean_name)
        
        cleaned_df.columns = new_columns
        return cleaned_df