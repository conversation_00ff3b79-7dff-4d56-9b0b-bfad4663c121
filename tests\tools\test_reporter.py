"""Test reporting and analysis tools."""

import json
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
import xml.etree.ElementTree as ET
from jinja2 import Template


@dataclass
class TestResult:
    """Container for individual test result."""
    name: str
    status: str  # passed, failed, skipped, error
    duration: float = 0.0
    message: str = ""
    traceback: str = ""
    file_path: str = ""
    line_number: int = 0
    markers: List[str] = field(default_factory=list)
    custom_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TestSuite:
    """Container for test suite results."""
    name: str
    tests: List[TestResult] = field(default_factory=list)
    setup_duration: float = 0.0
    teardown_duration: float = 0.0
    total_duration: float = 0.0
    
    @property
    def passed_count(self) -> int:
        return len([t for t in self.tests if t.status == 'passed'])
    
    @property
    def failed_count(self) -> int:
        return len([t for t in self.tests if t.status == 'failed'])
    
    @property
    def skipped_count(self) -> int:
        return len([t for t in self.tests if t.status == 'skipped'])
    
    @property
    def error_count(self) -> int:
        return len([t for t in self.tests if t.status == 'error'])
    
    @property
    def total_count(self) -> int:
        return len(self.tests)
    
    @property
    def success_rate(self) -> float:
        if self.total_count == 0:
            return 0.0
        return (self.passed_count / self.total_count) * 100


@dataclass
class CoverageData:
    """Container for coverage data."""
    total_statements: int = 0
    covered_statements: int = 0
    missing_lines: List[int] = field(default_factory=list)
    excluded_lines: List[int] = field(default_factory=list)
    file_coverage: Dict[str, float] = field(default_factory=dict)
    
    @property
    def coverage_percentage(self) -> float:
        if self.total_statements == 0:
            return 0.0
        return (self.covered_statements / self.total_statements) * 100


class TestReporter:
    """Generate comprehensive test reports."""
    
    def __init__(self, output_dir: str = "test_reports"):
        """Initialize the test reporter.
        
        Args:
            output_dir: Directory to save reports
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.test_suites: List[TestSuite] = []
        self.coverage_data: Optional[CoverageData] = None
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        
    def start_session(self):
        """Start a test session."""
        self.start_time = datetime.now()
        self.test_suites.clear()
        
    def end_session(self):
        """End a test session."""
        self.end_time = datetime.now()
        
    def add_test_suite(self, suite: TestSuite):
        """Add a test suite to the report.
        
        Args:
            suite: TestSuite to add
        """
        self.test_suites.append(suite)
        
    def set_coverage_data(self, coverage: CoverageData):
        """Set coverage data for the report.
        
        Args:
            coverage: CoverageData to set
        """
        self.coverage_data = coverage
        
    def parse_junit_xml(self, xml_file: str) -> List[TestSuite]:
        """Parse JUnit XML file and extract test results.
        
        Args:
            xml_file: Path to JUnit XML file
            
        Returns:
            List of TestSuite objects
        """
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        suites = []
        
        # Handle both <testsuite> and <testsuites> root elements
        if root.tag == 'testsuites':
            suite_elements = root.findall('testsuite')
        else:
            suite_elements = [root]
            
        for suite_elem in suite_elements:
            suite_name = suite_elem.get('name', 'Unknown')
            suite = TestSuite(
                name=suite_name,
                total_duration=float(suite_elem.get('time', 0))
            )
            
            for test_elem in suite_elem.findall('testcase'):
                test_name = test_elem.get('name', 'Unknown')
                test_duration = float(test_elem.get('time', 0))
                test_file = test_elem.get('file', '')
                test_line = int(test_elem.get('line', 0))
                
                # Determine test status
                status = 'passed'
                message = ''
                traceback = ''
                
                failure = test_elem.find('failure')
                error = test_elem.find('error')
                skipped = test_elem.find('skipped')
                
                if failure is not None:
                    status = 'failed'
                    message = failure.get('message', '')
                    traceback = failure.text or ''
                elif error is not None:
                    status = 'error'
                    message = error.get('message', '')
                    traceback = error.text or ''
                elif skipped is not None:
                    status = 'skipped'
                    message = skipped.get('message', '')
                    
                test_result = TestResult(
                    name=test_name,
                    status=status,
                    duration=test_duration,
                    message=message,
                    traceback=traceback,
                    file_path=test_file,
                    line_number=test_line
                )
                
                suite.tests.append(test_result)
                
            suites.append(suite)
            
        return suites
        
    def parse_coverage_xml(self, xml_file: str) -> CoverageData:
        """Parse coverage XML file and extract coverage data.
        
        Args:
            xml_file: Path to coverage XML file
            
        Returns:
            CoverageData object
        """
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        coverage = CoverageData()
        
        # Parse overall coverage
        coverage_elem = root.find('.//coverage')
        if coverage_elem is not None:
            coverage.total_statements = int(coverage_elem.get('lines-valid', 0))
            coverage.covered_statements = int(coverage_elem.get('lines-covered', 0))
            
        # Parse file-level coverage
        for class_elem in root.findall('.//class'):
            filename = class_elem.get('filename', '')
            lines_valid = int(class_elem.get('lines-valid', 0))
            lines_covered = int(class_elem.get('lines-covered', 0))
            
            if lines_valid > 0:
                file_coverage = (lines_covered / lines_valid) * 100
                coverage.file_coverage[filename] = file_coverage
                
            # Parse missing lines
            lines_elem = class_elem.find('lines')
            if lines_elem is not None:
                missing_lines = []
                for line_elem in lines_elem.findall('line'):
                    if line_elem.get('hits', '0') == '0':
                        missing_lines.append(int(line_elem.get('number', 0)))
                coverage.missing_lines.extend(missing_lines)
                
        return coverage
        
    def generate_summary_stats(self) -> Dict[str, Any]:
        """Generate summary statistics for all test suites.
        
        Returns:
            Dictionary with summary statistics
        """
        total_tests = sum(suite.total_count for suite in self.test_suites)
        total_passed = sum(suite.passed_count for suite in self.test_suites)
        total_failed = sum(suite.failed_count for suite in self.test_suites)
        total_skipped = sum(suite.skipped_count for suite in self.test_suites)
        total_errors = sum(suite.error_count for suite in self.test_suites)
        total_duration = sum(suite.total_duration for suite in self.test_suites)
        
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        stats = {
            'total_tests': total_tests,
            'passed': total_passed,
            'failed': total_failed,
            'skipped': total_skipped,
            'errors': total_errors,
            'success_rate': success_rate,
            'total_duration': total_duration,
            'average_duration': total_duration / total_tests if total_tests > 0 else 0,
            'test_suites': len(self.test_suites)
        }
        
        if self.coverage_data:
            stats['coverage'] = {
                'percentage': self.coverage_data.coverage_percentage,
                'total_statements': self.coverage_data.total_statements,
                'covered_statements': self.coverage_data.covered_statements,
                'missing_lines_count': len(self.coverage_data.missing_lines)
            }
            
        if self.start_time and self.end_time:
            session_duration = (self.end_time - self.start_time).total_seconds()
            stats['session_duration'] = session_duration
            stats['start_time'] = self.start_time.isoformat()
            stats['end_time'] = self.end_time.isoformat()
            
        return stats
        
    def generate_json_report(self, filename: str = "test_report.json") -> str:
        """Generate JSON test report.
        
        Args:
            filename: Output filename
            
        Returns:
            Path to generated report
        """
        report_data = {
            'summary': self.generate_summary_stats(),
            'test_suites': [
                {
                    'name': suite.name,
                    'total_count': suite.total_count,
                    'passed_count': suite.passed_count,
                    'failed_count': suite.failed_count,
                    'skipped_count': suite.skipped_count,
                    'error_count': suite.error_count,
                    'success_rate': suite.success_rate,
                    'total_duration': suite.total_duration,
                    'setup_duration': suite.setup_duration,
                    'teardown_duration': suite.teardown_duration,
                    'tests': [
                        {
                            'name': test.name,
                            'status': test.status,
                            'duration': test.duration,
                            'message': test.message,
                            'file_path': test.file_path,
                            'line_number': test.line_number,
                            'markers': test.markers,
                            'custom_data': test.custom_data
                        }
                        for test in suite.tests
                    ]
                }
                for suite in self.test_suites
            ]
        }
        
        if self.coverage_data:
            report_data['coverage'] = {
                'total_statements': self.coverage_data.total_statements,
                'covered_statements': self.coverage_data.covered_statements,
                'coverage_percentage': self.coverage_data.coverage_percentage,
                'missing_lines': self.coverage_data.missing_lines,
                'excluded_lines': self.coverage_data.excluded_lines,
                'file_coverage': self.coverage_data.file_coverage
            }
            
        output_path = self.output_dir / filename
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
            
        return str(output_path)
        
    def generate_html_report(self, filename: str = "test_report.html") -> str:
        """Generate HTML test report.
        
        Args:
            filename: Output filename
            
        Returns:
            Path to generated report
        """
        html_template = Template("""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
        .metric { background: white; padding: 15px; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; }
        .metric-label { color: #666; font-size: 0.9em; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .error { color: #fd7e14; }
        .suite { margin-bottom: 30px; border: 1px solid #ddd; border-radius: 5px; }
        .suite-header { background-color: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; }
        .suite-name { font-size: 1.2em; font-weight: bold; }
        .suite-stats { font-size: 0.9em; color: #666; margin-top: 5px; }
        .test-list { padding: 0; }
        .test-item { padding: 10px 15px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; }
        .test-item:last-child { border-bottom: none; }
        .test-name { font-weight: 500; }
        .test-status { padding: 4px 8px; border-radius: 3px; font-size: 0.8em; font-weight: bold; }
        .status-passed { background-color: #d4edda; color: #155724; }
        .status-failed { background-color: #f8d7da; color: #721c24; }
        .status-skipped { background-color: #fff3cd; color: #856404; }
        .status-error { background-color: #f5c6cb; color: #721c24; }
        .test-duration { color: #666; font-size: 0.9em; }
        .coverage-section { margin-top: 30px; }
        .coverage-bar { width: 100%; height: 20px; background-color: #e9ecef; border-radius: 10px; overflow: hidden; }
        .coverage-fill { height: 100%; background-color: #28a745; transition: width 0.3s ease; }
        .file-coverage { margin-top: 15px; }
        .file-item { display: flex; justify-content: space-between; padding: 5px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Report</h1>
        <p>Generated on {{ summary.end_time or 'Unknown' }}</p>
        {% if summary.session_duration %}
        <p>Session Duration: {{ "%.2f"|format(summary.session_duration) }} seconds</p>
        {% endif %}
    </div>
    
    <div class="summary">
        <div class="metric">
            <div class="metric-value">{{ summary.total_tests }}</div>
            <div class="metric-label">Total Tests</div>
        </div>
        <div class="metric">
            <div class="metric-value passed">{{ summary.passed }}</div>
            <div class="metric-label">Passed</div>
        </div>
        <div class="metric">
            <div class="metric-value failed">{{ summary.failed }}</div>
            <div class="metric-label">Failed</div>
        </div>
        <div class="metric">
            <div class="metric-value skipped">{{ summary.skipped }}</div>
            <div class="metric-label">Skipped</div>
        </div>
        <div class="metric">
            <div class="metric-value error">{{ summary.errors }}</div>
            <div class="metric-label">Errors</div>
        </div>
        <div class="metric">
            <div class="metric-value">{{ "%.1f"|format(summary.success_rate) }}%</div>
            <div class="metric-label">Success Rate</div>
        </div>
        {% if summary.coverage %}
        <div class="metric">
            <div class="metric-value">{{ "%.1f"|format(summary.coverage.percentage) }}%</div>
            <div class="metric-label">Code Coverage</div>
        </div>
        {% endif %}
    </div>
    
    {% if coverage %}
    <div class="coverage-section">
        <h2>Code Coverage</h2>
        <div class="coverage-bar">
            <div class="coverage-fill" style="width: {{ coverage.coverage_percentage }}%"></div>
        </div>
        <p>{{ coverage.covered_statements }} / {{ coverage.total_statements }} statements covered</p>
        
        {% if coverage.file_coverage %}
        <div class="file-coverage">
            <h3>File Coverage</h3>
            {% for file, percentage in coverage.file_coverage.items() %}
            <div class="file-item">
                <span>{{ file }}</span>
                <span>{{ "%.1f"|format(percentage) }}%</span>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <h2>Test Suites</h2>
    {% for suite in test_suites %}
    <div class="suite">
        <div class="suite-header">
            <div class="suite-name">{{ suite.name }}</div>
            <div class="suite-stats">
                {{ suite.total_count }} tests | 
                <span class="passed">{{ suite.passed_count }} passed</span> | 
                <span class="failed">{{ suite.failed_count }} failed</span> | 
                <span class="skipped">{{ suite.skipped_count }} skipped</span> | 
                <span class="error">{{ suite.error_count }} errors</span> | 
                Duration: {{ "%.3f"|format(suite.total_duration) }}s
            </div>
        </div>
        <div class="test-list">
            {% for test in suite.tests %}
            <div class="test-item">
                <div>
                    <div class="test-name">{{ test.name }}</div>
                    {% if test.message %}
                    <div style="font-size: 0.8em; color: #666; margin-top: 2px;">{{ test.message }}</div>
                    {% endif %}
                </div>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span class="test-duration">{{ "%.3f"|format(test.duration) }}s</span>
                    <span class="test-status status-{{ test.status }}">{{ test.status.upper() }}</span>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endfor %}
</body>
</html>
        """)
        
        summary = self.generate_summary_stats()
        
        html_content = html_template.render(
            summary=summary,
            test_suites=self.test_suites,
            coverage=self.coverage_data
        )
        
        output_path = self.output_dir / filename
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        return str(output_path)
        
    def generate_markdown_report(self, filename: str = "test_report.md") -> str:
        """Generate Markdown test report.
        
        Args:
            filename: Output filename
            
        Returns:
            Path to generated report
        """
        summary = self.generate_summary_stats()
        
        lines = [
            "# Test Report",
            "",
            f"Generated on: {summary.get('end_time', 'Unknown')}",
            "",
            "## Summary",
            "",
            f"- **Total Tests**: {summary['total_tests']}",
            f"- **Passed**: {summary['passed']} ✅",
            f"- **Failed**: {summary['failed']} ❌",
            f"- **Skipped**: {summary['skipped']} ⏭️",
            f"- **Errors**: {summary['errors']} 💥",
            f"- **Success Rate**: {summary['success_rate']:.1f}%",
            f"- **Total Duration**: {summary['total_duration']:.3f}s",
            ""
        ]
        
        if 'coverage' in summary:
            lines.extend([
                "## Code Coverage",
                "",
                f"- **Coverage**: {summary['coverage']['percentage']:.1f}%",
                f"- **Statements**: {summary['coverage']['covered_statements']}/{summary['coverage']['total_statements']}",
                ""
            ])
            
        lines.extend(["## Test Suites", ""])
        
        for suite in self.test_suites:
            lines.extend([
                f"### {suite.name}",
                "",
                f"- Tests: {suite.total_count}",
                f"- Passed: {suite.passed_count}",
                f"- Failed: {suite.failed_count}",
                f"- Skipped: {suite.skipped_count}",
                f"- Errors: {suite.error_count}",
                f"- Success Rate: {suite.success_rate:.1f}%",
                f"- Duration: {suite.total_duration:.3f}s",
                ""
            ])
            
            if suite.tests:
                lines.extend(["#### Test Results", ""])
                for test in suite.tests:
                    status_emoji = {
                        'passed': '✅',
                        'failed': '❌',
                        'skipped': '⏭️',
                        'error': '💥'
                    }.get(test.status, '❓')
                    
                    lines.append(f"- {status_emoji} **{test.name}** ({test.duration:.3f}s)")
                    if test.message:
                        lines.append(f"  - {test.message}")
                        
                lines.append("")
                
        output_path = self.output_dir / filename
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
            
        return str(output_path)
        
    def generate_all_reports(self, base_name: str = "test_report") -> Dict[str, str]:
        """Generate all report formats.
        
        Args:
            base_name: Base name for report files
            
        Returns:
            Dictionary mapping format to file path
        """
        reports = {}
        
        reports['json'] = self.generate_json_report(f"{base_name}.json")
        reports['html'] = self.generate_html_report(f"{base_name}.html")
        reports['markdown'] = self.generate_markdown_report(f"{base_name}.md")
        
        return reports
        
    def load_from_files(self, junit_xml: Optional[str] = None, coverage_xml: Optional[str] = None):
        """Load test and coverage data from files.
        
        Args:
            junit_xml: Path to JUnit XML file
            coverage_xml: Path to coverage XML file
        """
        if junit_xml and os.path.exists(junit_xml):
            self.test_suites = self.parse_junit_xml(junit_xml)
            
        if coverage_xml and os.path.exists(coverage_xml):
            self.coverage_data = self.parse_coverage_xml(coverage_xml)