# Connect平台测试环境配置
# 用于部署验证和环境特定的测试配置

# 默认配置
default: &default
  timeout: 30
  max_retries: 3
  health_check_interval: 5
  performance_threshold: 1.0
  security_checks_enabled: true
  
  # 测试数据配置
  test_data:
    sample_users: 10
    sample_sites: 100
    sample_cdr_records: 1000
    sample_ep_records: 500
    
  # 监控配置
  monitoring:
    metrics_enabled: true
    logging_level: INFO
    alert_thresholds:
      response_time: 2.0
      error_rate: 0.05
      cpu_usage: 0.8
      memory_usage: 0.8

# 开发环境配置
development:
  <<: *default
  
  # 环境信息
  name: "开发环境"
  base_url: "https://connect-dev.example.com"
  api_url: "https://connect-dev.example.com/api/v1"
  
  # 认证配置
  auth:
    admin_username: "admin"
    admin_password_env: "DEV_ADMIN_PASSWORD"
    test_username: "testuser"
    test_password_env: "DEV_TEST_PASSWORD"
    
  # 数据库配置
  database:
    url_env: "DEV_DATABASE_URL"
    pool_size: 5
    max_overflow: 10
    
  # Redis配置
  redis:
    url_env: "DEV_REDIS_URL"
    db: 0
    
  # 性能配置
  performance:
    threshold: 2.0  # 开发环境较宽松
    concurrent_users: 5
    test_duration: 60
    
  # 安全配置
  security:
    ssl_verify: false  # 开发环境可能使用自签名证书
    required_headers:
      - "X-Content-Type-Options"
      - "X-Frame-Options"
    
  # 功能测试配置
  features:
    data_import_enabled: true
    file_upload_max_size: "100MB"
    export_formats: ["csv", "excel", "pdf"]
    
  # 监控配置
  monitoring:
    logging_level: DEBUG
    metrics_retention: "7d"
    
# 预发布环境配置
staging:
  <<: *default
  
  # 环境信息
  name: "预发布环境"
  base_url: "https://connect-staging.example.com"
  api_url: "https://connect-staging.example.com/api/v1"
  
  # 认证配置
  auth:
    admin_username: "admin"
    admin_password_env: "STAGING_ADMIN_PASSWORD"
    test_username: "testuser"
    test_password_env: "STAGING_TEST_PASSWORD"
    
  # 数据库配置
  database:
    url_env: "STAGING_DATABASE_URL"
    pool_size: 10
    max_overflow: 20
    
  # Redis配置
  redis:
    url_env: "STAGING_REDIS_URL"
    db: 0
    
  # 性能配置
  performance:
    threshold: 1.0  # 预发布环境中等要求
    concurrent_users: 15
    test_duration: 300
    
  # 安全配置
  security:
    ssl_verify: true
    required_headers:
      - "X-Content-Type-Options"
      - "X-Frame-Options"
      - "X-XSS-Protection"
      - "Strict-Transport-Security"
      - "Content-Security-Policy"
    
  # 功能测试配置
  features:
    data_import_enabled: true
    file_upload_max_size: "500MB"
    export_formats: ["csv", "excel", "pdf", "json"]
    
  # 监控配置
  monitoring:
    logging_level: INFO
    metrics_retention: "30d"
    alert_thresholds:
      response_time: 1.5
      error_rate: 0.02
      
  # 测试数据配置
  test_data:
    sample_users: 50
    sample_sites: 1000
    sample_cdr_records: 10000
    sample_ep_records: 5000

# 生产环境配置
production:
  <<: *default
  
  # 环境信息
  name: "生产环境"
  base_url: "https://connect.example.com"
  api_url: "https://connect.example.com/api/v1"
  
  # 认证配置
  auth:
    admin_username: "admin"
    admin_password_env: "PROD_ADMIN_PASSWORD"
    test_username: "readonly_user"
    test_password_env: "PROD_READONLY_PASSWORD"
    
  # 数据库配置
  database:
    url_env: "PROD_DATABASE_URL"
    pool_size: 20
    max_overflow: 50
    
  # Redis配置
  redis:
    url_env: "PROD_REDIS_URL"
    db: 0
    
  # 性能配置
  performance:
    threshold: 0.5  # 生产环境严格要求
    concurrent_users: 20
    test_duration: 600
    
  # 安全配置
  security:
    ssl_verify: true
    required_headers:
      - "X-Content-Type-Options"
      - "X-Frame-Options"
      - "X-XSS-Protection"
      - "Strict-Transport-Security"
      - "Content-Security-Policy"
      - "Referrer-Policy"
      - "Permissions-Policy"
    
  # 功能测试配置
  features:
    data_import_enabled: true
    file_upload_max_size: "1GB"
    export_formats: ["csv", "excel", "pdf", "json"]
    
  # 监控配置
  monitoring:
    logging_level: WARNING
    metrics_retention: "90d"
    alert_thresholds:
      response_time: 0.8
      error_rate: 0.01
      cpu_usage: 0.7
      memory_usage: 0.7
      
  # 测试数据配置（生产环境只读测试）
  test_data:
    readonly_tests_only: true
    sample_queries: 10
    
  # 备份和恢复配置
  backup:
    enabled: true
    retention_days: 30
    verification_enabled: true

# 测试套件配置
test_suites:
  # 冒烟测试
  smoke:
    description: "快速验证核心功能"
    tests:
      - "application_health"
      - "database_connectivity"
      - "authentication"
    timeout: 300
    
  # 功能测试
  functional:
    description: "完整功能验证"
    tests:
      - "application_health"
      - "database_connectivity"
      - "redis_connectivity"
      - "authentication"
      - "core_functionality"
      - "version_deployment"
    timeout: 900
    
  # 性能测试
  performance:
    description: "性能基线验证"
    tests:
      - "performance_baseline"
      - "load_testing"
      - "stress_testing"
    timeout: 1800
    
  # 安全测试
  security:
    description: "安全配置验证"
    tests:
      - "security_headers"
      - "ssl_configuration"
      - "authentication_security"
    timeout: 600
    
  # 完整测试
  full:
    description: "完整部署验证"
    tests:
      - "application_health"
      - "database_connectivity"
      - "redis_connectivity"
      - "authentication"
      - "core_functionality"
      - "version_deployment"
      - "performance_baseline"
      - "security_headers"
    timeout: 2400

# 通知配置
notifications:
  # Slack通知
  slack:
    enabled: true
    webhook_url_env: "SLACK_WEBHOOK_URL"
    channels:
      success: "#deployments"
      failure: "#alerts"
    
  # 邮件通知
  email:
    enabled: true
    smtp_server_env: "SMTP_SERVER"
    smtp_port_env: "SMTP_PORT"
    username_env: "SMTP_USERNAME"
    password_env: "SMTP_PASSWORD"
    recipients:
      - "<EMAIL>"
      - "<EMAIL>"
    
  # Teams通知
  teams:
    enabled: false
    webhook_url_env: "TEAMS_WEBHOOK_URL"

# 报告配置
reporting:
  # 报告格式
  formats:
    - "json"
    - "html"
    - "junit"
    
  # 报告存储
  storage:
    local_path: "tests/reports/deployment"
    s3_bucket_env: "DEPLOYMENT_REPORTS_BUCKET"
    retention_days: 90
    
  # 报告内容
  content:
    include_screenshots: true
    include_logs: true
    include_metrics: true
    include_recommendations: true

# 集成配置
integrations:
  # CI/CD集成
  ci_cd:
    github_actions: true
    jenkins: false
    gitlab_ci: false
    
  # 监控集成
  monitoring:
    prometheus:
      enabled: true
      pushgateway_url_env: "PROMETHEUS_PUSHGATEWAY_URL"
    datadog:
      enabled: false
      api_key_env: "DATADOG_API_KEY"
    newrelic:
      enabled: false
      api_key_env: "NEWRELIC_API_KEY"
    
  # 质量门禁集成
  quality_gates:
    sonarqube:
      enabled: true
      server_url_env: "SONAR_HOST_URL"
      token_env: "SONAR_TOKEN"
    codecov:
      enabled: true
      token_env: "CODECOV_TOKEN"

# 环境特定的覆盖配置
environment_overrides:
  # 开发环境覆盖
  development:
    security:
      ssl_verify: false
    monitoring:
      logging_level: DEBUG
      
  # 生产环境覆盖
  production:
    test_data:
      readonly_tests_only: true
    features:
      destructive_tests_disabled: true
    monitoring:
      logging_level: ERROR
      detailed_metrics: false