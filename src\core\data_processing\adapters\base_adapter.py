# -*- coding: utf-8 -*-
"""
Base Adapter for Data Processing Engines

This module defines the abstract base class for all data processing engine adapters,
providing a unified interface for different engines like Pandas, Polars, etc.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import asyncio
import gc
import logging
import psutil
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, AsyncGenerator, Iterator

import pandas as pd
from pydantic import BaseModel

from ..types import (
    ProcessingEngine, ProcessingMode, ProcessingStatus, DataFormat,
    ProcessingMetrics, ProcessingResult, ProcessingConfig,
    ChunkInfo, FileInfo
)
class AdapterError(Exception):
    """Base exception for adapter-related errors."""
    pass


class EngineNotAvailableError(AdapterError):
    """Raised when a processing engine is not available."""
    pass


class MemoryLimitExceededError(AdapterError):
    """Raised when memory usage exceeds configured limits."""
    pass


class ProcessingTimeoutError(AdapterError):
    """Raised when processing operations timeout."""
    pass


class BaseAdapter(ABC):
    """Abstract base class for data processing engine adapters."""
    
    def __init__(self, config: ProcessingConfig):
        """Initialize the adapter with configuration.
        
        Args:
            config: Processing configuration
        """
        self.config = config
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        self.metrics = ProcessingMetrics()
        self._process = psutil.Process()
        self._start_memory = self._get_memory_usage()
        
        # Validate engine availability
        if not self.is_engine_available():
            raise EngineNotAvailableError(f"Engine {self.engine_name} is not available")
    
    @property
    @abstractmethod
    def engine_name(self) -> str:
        """Get the name of the processing engine."""
        pass
    
    @property
    @abstractmethod
    def engine_type(self) -> ProcessingEngine:
        """Get the processing engine type."""
        pass
    
    @abstractmethod
    def is_engine_available(self) -> bool:
        """Check if the processing engine is available."""
        pass
    
    @abstractmethod
    async def read_file(self, file_path: Path, **kwargs) -> Any:
        """Read data from a file.
        
        Args:
            file_path: Path to the file
            **kwargs: Additional arguments for reading
            
        Returns:
            Data object (DataFrame, etc.)
        """
        pass
    
    @abstractmethod
    async def write_file(self, data: Any, file_path: Path, **kwargs) -> None:
        """Write data to a file.
        
        Args:
            data: Data object to write
            file_path: Path to write the file
            **kwargs: Additional arguments for writing
        """
        pass
    
    @abstractmethod
    async def process_chunk(self, chunk: Any, processor_func: callable) -> Any:
        """Process a data chunk.
        
        Args:
            chunk: Data chunk to process
            processor_func: Processing function
            
        Returns:
            Processed data chunk
        """
        pass
    
    @abstractmethod
    async def validate_data(self, data: Any, validation_rules: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data against rules.
        
        Args:
            data: Data to validate
            validation_rules: Validation rules
            
        Returns:
            Validation results
        """
        pass
    
    @abstractmethod
    async def transform_data(self, data: Any, transformations: List[Dict[str, Any]]) -> Any:
        """Transform data using specified transformations.
        
        Args:
            data: Data to transform
            transformations: List of transformation specifications
            
        Returns:
            Transformed data
        """
        pass
    
    @abstractmethod
    def get_data_info(self, data: Any) -> Dict[str, Any]:
        """Get information about the data.
        
        Args:
            data: Data object
            
        Returns:
            Data information dictionary
        """
        pass
    
    @abstractmethod
    def to_pandas(self, data: Any) -> pd.DataFrame:
        """Convert data to Pandas DataFrame.
        
        Args:
            data: Data object
            
        Returns:
            Pandas DataFrame
        """
        pass
    
    @abstractmethod
    def from_pandas(self, df: pd.DataFrame) -> Any:
        """Convert Pandas DataFrame to engine-specific format.
        
        Args:
            df: Pandas DataFrame
            
        Returns:
            Engine-specific data object
        """
        pass
    
    # Common utility methods
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            memory_info = self._process.memory_info()
            return memory_info.rss / (1024 * 1024)  # Convert to MB
        except Exception:
            return 0.0
    
    def _check_memory_limit(self) -> None:
        """Check if memory usage exceeds configured limits."""
        current_memory = self._get_memory_usage()
        if current_memory > self.config.memory_limit_mb:
            raise MemoryLimitExceededError(
                f"Memory usage ({current_memory:.1f}MB) exceeds limit ({self.config.memory_limit_mb}MB)"
            )
    
    def _update_memory_metrics(self) -> None:
        """Update memory usage metrics."""
        current_memory = self._get_memory_usage()
        self.metrics.memory_usage_mb = current_memory
        if current_memory > self.metrics.memory_peak_mb:
            self.metrics.memory_peak_mb = current_memory
    
    def _trigger_gc_if_needed(self, force: bool = False) -> None:
        """Trigger garbage collection if needed."""
        if not self.config.enable_gc and not force:
            return
            
        current_memory = self._get_memory_usage()
        memory_threshold = self.config.memory_limit_mb * self.config.memory_warning_threshold
        
        if current_memory > memory_threshold or force:
            self.logger.debug(f"Triggering GC: memory usage {current_memory:.1f}MB")
            gc.collect()
    
    async def _process_with_timeout(self, coro, timeout_seconds: Optional[int] = None) -> Any:
        """Execute a coroutine with timeout.
        
        Args:
            coro: Coroutine to execute
            timeout_seconds: Timeout in seconds
            
        Returns:
            Coroutine result
            
        Raises:
            ProcessingTimeoutError: If operation times out
        """
        timeout = timeout_seconds or self.config.async_timeout_seconds
        
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            raise ProcessingTimeoutError(f"Operation timed out after {timeout} seconds")
    
    @asynccontextmanager
    async def _processing_context(self, operation_name: str):
        """Context manager for processing operations.
        
        Args:
            operation_name: Name of the operation
        """
        start_time = datetime.now()
        self.logger.info(f"Starting {operation_name}")
        
        try:
            yield
        except Exception as e:
            self.logger.error(f"Error in {operation_name}: {e}")
            self.metrics.errors.append(str(e))
            raise
        finally:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            self.logger.info(f"Completed {operation_name} in {duration:.2f}s")
            
            # Update metrics
            self._update_memory_metrics()
            if self.config.enable_gc:
                self._trigger_gc_if_needed()
    
    async def process_file_chunked(
        self,
        file_path: Path,
        processor_func: callable,
        chunk_size: Optional[int] = None
    ) -> AsyncGenerator[ProcessingResult, None]:
        """Process a file in chunks.
        
        Args:
            file_path: Path to the file
            processor_func: Processing function
            chunk_size: Size of each chunk
            
        Yields:
            ProcessingResult for each chunk
        """
        chunk_size = chunk_size or self.config.chunk_size
        
        async with self._processing_context(f"chunked processing of {file_path}"):
            chunk_id = 0
            
            async for chunk_data in self._read_file_chunks(file_path, chunk_size):
                chunk_info = ChunkInfo(
                    chunk_id=chunk_id,
                    start_row=chunk_id * chunk_size,
                    end_row=(chunk_id + 1) * chunk_size - 1,
                    size=len(chunk_data) if hasattr(chunk_data, '__len__') else 0
                )
                
                try:
                    # Check memory before processing
                    self._check_memory_limit()
                    
                    # Process chunk
                    chunk_info.status = ProcessingStatus.PROCESSING
                    processed_chunk = await self.process_chunk(chunk_data, processor_func)
                    
                    # Create result
                    result = ProcessingResult(
                        status=ProcessingStatus.COMPLETED,
                        data=processed_chunk,
                        metrics=ProcessingMetrics(
                            records_processed=chunk_info.size,
                            chunks_processed=1
                        ),
                        metadata={"chunk_info": chunk_info}
                    )
                    
                    chunk_info.status = ProcessingStatus.COMPLETED
                    yield result
                    
                except Exception as e:
                    chunk_info.status = ProcessingStatus.FAILED
                    chunk_info.error = str(e)
                    
                    result = ProcessingResult(
                        status=ProcessingStatus.FAILED,
                        error_message=str(e),
                        metadata={"chunk_info": chunk_info}
                    )
                    
                    yield result
                
                finally:
                    chunk_id += 1
                    
                    # Trigger GC periodically
                    if chunk_id % self.config.gc_frequency == 0:
                        self._trigger_gc_if_needed()
    
    @abstractmethod
    async def _read_file_chunks(self, file_path: Path, chunk_size: int) -> AsyncGenerator[Any, None]:
        """Read file in chunks.
        
        Args:
            file_path: Path to the file
            chunk_size: Size of each chunk
            
        Yields:
            Data chunks
        """
        pass
    
    def get_file_info(self, file_path: Path) -> FileInfo:
        """Get information about a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            FileInfo object
        """
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Determine format from extension
        format_map = {
            '.csv': DataFormat.CSV,
            '.tsv': DataFormat.TSV,
            '.xlsx': DataFormat.EXCEL,
            '.xls': DataFormat.EXCEL,
            '.xlsb': DataFormat.EXCEL,
            '.json': DataFormat.JSON,
            '.jsonl': DataFormat.JSONL,
            '.parquet': DataFormat.PARQUET,
            '.feather': DataFormat.FEATHER,
            '.h5': DataFormat.HDF5,
            '.hdf5': DataFormat.HDF5,
        }
        
        file_format = format_map.get(file_path.suffix.lower(), DataFormat.CSV)
        
        return FileInfo(
            path=file_path,
            size_bytes=file_path.stat().st_size,
            format=file_format
        )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary.
        
        Returns:
            Performance summary dictionary
        """
        return {
            "engine": self.engine_name,
            "records_processed": self.metrics.records_processed,
            "processing_time": self.metrics.processing_time_seconds,
            "throughput": self.metrics.calculate_throughput(),
            "memory_usage_mb": self.metrics.memory_usage_mb,
            "memory_peak_mb": self.metrics.memory_peak_mb,
            "error_rate": self.metrics.calculate_error_rate(),
            "quality_level": self.metrics.get_quality_level().value
        }