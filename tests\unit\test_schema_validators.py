#!/usr/bin/env python3
"""
Unit tests for Schema Validators module.

Tests the SchemaValidator class functionality including table structure validation,
schema consistency checks, and discrepancy reporting.
"""

import asyncio
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.database.exceptions import DatabaseError, ValidationError
from src.database.schema.validators import (
    ColumnDefinition,
    SchemaValidator,
    TableStructure,
    ValidationResult,
    ValidationSeverity,
)
from src.database.utils.validators import InputValidator


class TestSchemaValidator:
    """Test cases for SchemaValidator class."""

    @pytest.fixture
    def mock_pool(self):
        """Create a mock connection pool."""
        pool = AsyncMock()
        return pool

    @pytest.fixture
    def mock_connection(self):
        """Create a mock database connection."""
        conn = AsyncMock()
        # Ensure fetch and fetchval return awaitable results
        conn.fetch = AsyncMock()
        conn.fetchval = AsyncMock()
        return conn

    @pytest.fixture
    def validator(self, mock_pool):
        """Create SchemaValidator instance with mock pool."""
        return SchemaValidator(mock_pool)

    @pytest.fixture
    def sample_expected_structure(self):
        """Sample expected table structure for testing."""
        return {
            "columns": {
                "id": {"data_type": "integer", "nullable": False, "primary_key": True},
                "name": {
                    "data_type": "character varying",
                    "nullable": False,
                    "max_length": 100,
                },
                "email": {
                    "data_type": "character varying",
                    "nullable": True,
                    "unique": True,
                },
                "created_at": {
                    "data_type": "timestamp without time zone",
                    "nullable": False,
                },
            },
            "primary_keys": ["id"],
        }

    @pytest.fixture
    def sample_actual_structure(self):
        """Sample actual table structure from database."""
        return {
            "table_name": "users",
            "schema_name": "public",
            "columns": [
                {
                    "name": "id",
                    "data_type": "integer",
                    "nullable": False,
                    "default": None,
                    "max_length": None,
                    "precision": 32,
                    "scale": 0,
                    "primary_key": True,
                    "foreign_key": None,
                    "unique": False,
                },
                {
                    "name": "name",
                    "data_type": "character varying",
                    "nullable": False,
                    "default": None,
                    "max_length": 100,
                    "precision": None,
                    "scale": None,
                    "primary_key": False,
                    "foreign_key": None,
                    "unique": False,
                },
                {
                    "name": "email",
                    "data_type": "character varying",
                    "nullable": True,
                    "default": None,
                    "max_length": 255,
                    "precision": None,
                    "scale": None,
                    "primary_key": False,
                    "foreign_key": None,
                    "unique": True,
                },
                {
                    "name": "created_at",
                    "data_type": "timestamp without time zone",
                    "nullable": False,
                    "default": "CURRENT_TIMESTAMP",
                    "max_length": None,
                    "precision": None,
                    "scale": None,
                    "primary_key": False,
                    "foreign_key": None,
                    "unique": False,
                },
            ],
            "primary_keys": ["id"],
            "foreign_keys": {},
            "unique_columns": ["email"],
        }

    def test_validator_initialization(self, mock_pool):
        """Test SchemaValidator initialization."""
        validator = SchemaValidator(mock_pool)
        assert validator.pool == mock_pool
        assert isinstance(validator.validator, InputValidator)
        assert validator._engine is None

    def test_validator_initialization_with_custom_validator(self, mock_pool):
        """Test SchemaValidator initialization with custom validator."""
        custom_validator = InputValidator()
        validator = SchemaValidator(mock_pool, custom_validator)
        assert validator.validator == custom_validator

    @pytest.mark.asyncio
    async def test_validate_table_structure_invalid_table_name(self, validator):
        """Test validation with invalid table name."""
        with pytest.raises(ValidationError, match="Invalid table name"):
            await validator.validate_table_structure("123invalid", "public", {})

    @pytest.mark.asyncio
    async def test_validate_table_structure_invalid_schema_name(self, validator):
        """Test validation with invalid schema name."""
        with pytest.raises(ValidationError, match="Invalid schema name"):
            await validator.validate_table_structure("users", "123invalid", {})

    @pytest.mark.asyncio
    async def test_validate_table_structure_table_not_exists(
        self, validator, mock_pool, mock_connection
    ):
        """Test validation when table doesn't exist."""
        from unittest.mock import MagicMock

        async_context_manager = MagicMock()
        async_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
        async_context_manager.__aexit__ = AsyncMock(return_value=None)
        mock_pool.acquire = MagicMock(return_value=async_context_manager)

        # Mock table existence check
        with patch.object(validator, "_table_exists", return_value=False):
            result = await validator.validate_table_structure(
                "nonexistent", "public", {}
            )

        assert not result.is_valid
        assert result.severity == ValidationSeverity.CRITICAL
        assert len(result.discrepancies) == 1
        assert result.discrepancies[0]["type"] == "table_missing"
        assert "not found" in result.summary

    @pytest.mark.asyncio
    async def test_validate_table_structure_success(
        self,
        validator,
        mock_pool,
        mock_connection,
        sample_expected_structure,
        sample_actual_structure,
    ):
        """Test successful table structure validation."""
        from unittest.mock import MagicMock

        async_context_manager = MagicMock()
        async_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
        async_context_manager.__aexit__ = AsyncMock(return_value=None)
        mock_pool.acquire = MagicMock(return_value=async_context_manager)

        with patch.object(validator, "_table_exists", return_value=True), patch.object(
            validator, "_get_table_structure", return_value=sample_actual_structure
        ):
            result = await validator.validate_table_structure(
                "users", "public", sample_expected_structure
            )

        assert result.is_valid
        assert result.severity == ValidationSeverity.INFO
        assert len(result.discrepancies) == 0
        assert "is valid" in result.summary

    @pytest.mark.asyncio
    async def test_validate_table_structure_with_discrepancies(
        self, validator, mock_pool, mock_connection, sample_expected_structure
    ):
        """Test table structure validation with discrepancies."""
        from unittest.mock import MagicMock

        async_context_manager = MagicMock()
        async_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
        async_context_manager.__aexit__ = AsyncMock(return_value=None)
        mock_pool.acquire = MagicMock(return_value=async_context_manager)

        # Create actual structure with discrepancies
        actual_structure_with_issues = {
            "table_name": "users",
            "schema_name": "public",
            "columns": [
                {
                    "name": "id",
                    "data_type": "bigint",  # Different type
                    "nullable": False,
                    "primary_key": True,
                    "foreign_key": None,
                    "unique": False,
                },
                {
                    "name": "username",  # Different name
                    "data_type": "character varying",
                    "nullable": False,
                    "primary_key": False,
                    "foreign_key": None,
                    "unique": False,
                },
                # Missing 'email' and 'created_at' columns
                {
                    "name": "extra_column",  # Unexpected column
                    "data_type": "text",
                    "nullable": True,
                    "primary_key": False,
                    "foreign_key": None,
                    "unique": False,
                },
            ],
            "primary_keys": ["id"],
            "foreign_keys": {},
            "unique_columns": [],
        }

        with patch.object(validator, "_table_exists", return_value=True), patch.object(
            validator, "_get_table_structure", return_value=actual_structure_with_issues
        ):
            result = await validator.validate_table_structure(
                "users", "public", sample_expected_structure
            )

        assert not result.is_valid
        assert result.severity == ValidationSeverity.ERROR
        assert len(result.discrepancies) > 0

        # Check for specific discrepancies
        discrepancy_types = [d["type"] for d in result.discrepancies]
        assert "missing_column" in discrepancy_types
        assert "unexpected_column" in discrepancy_types
        assert "data_type_mismatch" in discrepancy_types

    @pytest.mark.asyncio
    async def test_validate_multiple_tables(self, validator):
        """Test validation of multiple tables."""
        table_definitions = [
            {
                "table_name": "users",
                "schema_name": "public",
                "expected_structure": {"columns": {"id": {"data_type": "integer"}}},
            },
            {
                "table_name": "posts",
                "schema_name": "public",
                "expected_structure": {"columns": {"id": {"data_type": "integer"}}},
            },
        ]

        # Mock successful validation for both tables
        mock_result = ValidationResult(
            is_valid=True,
            discrepancies=[],
            summary="Valid",
            severity=ValidationSeverity.INFO,
        )

        with patch.object(
            validator, "validate_table_structure", return_value=mock_result
        ):
            results = await validator.validate_multiple_tables(table_definitions)

        assert len(results) == 2
        assert "public.users" in results
        assert "public.posts" in results
        assert all(result.is_valid for result in results.values())

    @pytest.mark.asyncio
    async def test_validate_multiple_tables_incomplete_definition(self, validator):
        """Test validation with incomplete table definitions."""
        table_definitions = [
            {
                "table_name": "users",
                # Missing schema_name and expected_structure
            },
            {
                "table_name": "posts",
                "schema_name": "public",
                "expected_structure": {"columns": {"id": {"data_type": "integer"}}},
            },
        ]

        mock_result = ValidationResult(
            is_valid=True,
            discrepancies=[],
            summary="Valid",
            severity=ValidationSeverity.INFO,
        )

        with patch.object(
            validator, "validate_table_structure", return_value=mock_result
        ):
            results = await validator.validate_multiple_tables(table_definitions)

        # Only the complete definition should be processed
        assert len(results) == 1
        assert "public.posts" in results

    @pytest.mark.asyncio
    async def test_table_exists_true(self, validator, mock_pool, mock_connection):
        """Test _table_exists method when table exists."""
        from unittest.mock import MagicMock

        async_context_manager = MagicMock()
        async_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
        async_context_manager.__aexit__ = AsyncMock(return_value=None)
        mock_pool.acquire = MagicMock(return_value=async_context_manager)
        mock_connection.fetchval.return_value = True

        result = await validator._table_exists("users", "public")

        assert result is True
        mock_connection.fetchval.assert_called_once()

    @pytest.mark.asyncio
    async def test_table_exists_false(self, validator, mock_pool, mock_connection):
        """Test _table_exists method when table doesn't exist."""
        from unittest.mock import MagicMock

        async_context_manager = MagicMock()
        async_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
        async_context_manager.__aexit__ = AsyncMock(return_value=None)
        mock_pool.acquire = MagicMock(return_value=async_context_manager)
        mock_connection.fetchval.return_value = False

        result = await validator._table_exists("nonexistent", "public")

        assert result is False
        mock_connection.fetchval.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_table_structure(self, validator, mock_pool, mock_connection):
        """Test _get_table_structure method."""
        from unittest.mock import MagicMock

        async_context_manager = MagicMock()
        async_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
        async_context_manager.__aexit__ = AsyncMock(return_value=None)
        mock_pool.acquire = MagicMock(return_value=async_context_manager)

        # Mock database responses
        class MockRecord(dict):
            def __getattr__(self, key):
                return self[key]

        mock_columns = [
            MockRecord(
                {
                    "column_name": "id",
                    "data_type": "integer",
                    "is_nullable": "NO",
                    "column_default": None,
                    "character_maximum_length": None,
                    "numeric_precision": 32,
                    "numeric_scale": 0,
                }
            ),
            MockRecord(
                {
                    "column_name": "name",
                    "data_type": "character varying",
                    "is_nullable": "NO",
                    "column_default": None,
                    "character_maximum_length": 100,
                    "numeric_precision": None,
                    "numeric_scale": None,
                }
            ),
        ]

        mock_primary_keys = [MockRecord({"column_name": "id"})]
        mock_foreign_keys = []
        mock_unique_constraints = []

        # Configure mock to return different results for different queries
        mock_connection.fetch.side_effect = [
            mock_columns,
            mock_primary_keys,
            mock_foreign_keys,
            mock_unique_constraints,
        ]

        result = await validator._get_table_structure("users", "public")

        assert result["table_name"] == "users"
        assert result["schema_name"] == "public"
        assert len(result["columns"]) == 2
        assert result["columns"][0]["name"] == "id"
        assert result["columns"][0]["primary_key"] is True
        assert result["primary_keys"] == ["id"]

    def test_compare_structures_valid(
        self, validator, sample_actual_structure, sample_expected_structure
    ):
        """Test _compare_structures method with valid structures."""
        result = validator._compare_structures(
            sample_actual_structure, sample_expected_structure, "users", "public"
        )

        assert result.is_valid
        assert result.severity == ValidationSeverity.INFO
        assert len(result.discrepancies) == 0
        assert "is valid" in result.summary

    def test_compare_structures_missing_column(
        self, validator, sample_actual_structure, sample_expected_structure
    ):
        """Test _compare_structures method with missing column."""
        # Add an expected column that doesn't exist in actual
        sample_expected_structure["columns"]["missing_col"] = {
            "data_type": "text",
            "nullable": True,
        }

        result = validator._compare_structures(
            sample_actual_structure, sample_expected_structure, "users", "public"
        )

        assert not result.is_valid
        assert result.severity == ValidationSeverity.ERROR
        assert any(d["type"] == "missing_column" for d in result.discrepancies)

    def test_compare_structures_unexpected_column(
        self, validator, sample_actual_structure, sample_expected_structure
    ):
        """Test _compare_structures method with unexpected column."""
        # Add an actual column that's not expected
        sample_actual_structure["columns"].append(
            {
                "name": "unexpected_col",
                "data_type": "text",
                "nullable": True,
                "primary_key": False,
                "foreign_key": None,
                "unique": False,
            }
        )

        result = validator._compare_structures(
            sample_actual_structure, sample_expected_structure, "users", "public"
        )

        assert not result.is_valid
        assert result.severity == ValidationSeverity.WARNING
        assert any(d["type"] == "unexpected_column" for d in result.discrepancies)

    def test_compare_structures_primary_key_mismatch(
        self, validator, sample_actual_structure, sample_expected_structure
    ):
        """Test _compare_structures method with primary key mismatch."""
        # Change expected primary keys
        sample_expected_structure["primary_keys"] = ["id", "name"]

        result = validator._compare_structures(
            sample_actual_structure, sample_expected_structure, "users", "public"
        )

        assert not result.is_valid
        assert result.severity == ValidationSeverity.ERROR
        assert any(d["type"] == "primary_key_mismatch" for d in result.discrepancies)

    def test_compare_column_properties_data_type_mismatch(self, validator):
        """Test _compare_column_properties method with data type mismatch."""
        actual_col = {"data_type": "integer", "nullable": False}
        expected_col = {"data_type": "bigint", "nullable": False}

        result = ValidationResult(
            is_valid=True,
            discrepancies=[],
            summary="",
            severity=ValidationSeverity.INFO,
        )

        validator._compare_column_properties(
            actual_col, expected_col, "test_col", result
        )

        assert not result.is_valid
        assert result.severity == ValidationSeverity.ERROR
        assert any(d["type"] == "data_type_mismatch" for d in result.discrepancies)

    def test_compare_column_properties_nullable_mismatch(self, validator):
        """Test _compare_column_properties method with nullable mismatch."""
        actual_col = {"data_type": "integer", "nullable": True}
        expected_col = {"data_type": "integer", "nullable": False}

        result = ValidationResult(
            is_valid=True,
            discrepancies=[],
            summary="",
            severity=ValidationSeverity.INFO,
        )

        validator._compare_column_properties(
            actual_col, expected_col, "test_col", result
        )

        assert not result.is_valid
        assert result.severity == ValidationSeverity.WARNING
        assert any(d["type"] == "nullable_mismatch" for d in result.discrepancies)

    @pytest.mark.asyncio
    async def test_validate_schema_consistency_schema_not_exists(
        self, validator, mock_pool, mock_connection
    ):
        """Test schema consistency validation when schema doesn't exist."""
        # Setup async context manager for pool.acquire()
        from unittest.mock import MagicMock

        async_context_manager = MagicMock()
        async_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
        async_context_manager.__aexit__ = AsyncMock(return_value=None)
        mock_pool.acquire = MagicMock(return_value=async_context_manager)
        mock_connection.fetchval.return_value = False

        result = await validator.validate_schema_consistency("nonexistent")

        assert not result.is_valid
        assert result.severity == ValidationSeverity.CRITICAL
        assert any(d["type"] == "schema_missing" for d in result.discrepancies)
        assert "not found" in result.summary

    @pytest.mark.asyncio
    async def test_validate_schema_consistency_success(
        self, validator, mock_pool, mock_connection
    ):
        """Test successful schema consistency validation."""
        # Setup async context manager for pool.acquire()
        from unittest.mock import MagicMock

        async_context_manager = MagicMock()
        async_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
        async_context_manager.__aexit__ = AsyncMock(return_value=None)
        mock_pool.acquire = MagicMock(return_value=async_context_manager)

        # Mock responses for schema existence, tables, and orphaned FKs
        mock_connection.fetchval.return_value = True

        # Create mock records for tables query
        class MockRecord(dict):
            def __getattr__(self, key):
                return self[key]

        table_records = [
            MockRecord({"table_name": "users"}),
            MockRecord({"table_name": "posts"}),
        ]

        mock_connection.fetch.side_effect = [
            table_records,  # tables
            [],  # no orphaned foreign keys
        ]

        result = await validator.validate_schema_consistency("public")

        assert result.is_valid
        assert result.severity == ValidationSeverity.INFO
        assert "is consistent" in result.summary
        assert "2 tables" in result.summary

    @pytest.mark.asyncio
    async def test_validate_schema_consistency_orphaned_fks(
        self, validator, mock_pool, mock_connection
    ):
        """Test schema consistency validation with orphaned foreign keys."""
        # Setup async context manager for pool.acquire()
        from unittest.mock import MagicMock

        async_context_manager = MagicMock()
        async_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
        async_context_manager.__aexit__ = AsyncMock(return_value=None)
        mock_pool.acquire = MagicMock(return_value=async_context_manager)

        # Mock responses
        mock_connection.fetchval.return_value = True

        # Create mock records for orphaned FKs query
        class MockRecord(dict):
            def __getattr__(self, key):
                return self[key]

        # Create mock records for tables query
        table_records = [MockRecord({"table_name": "users"})]

        orphaned_fk_records = [
            MockRecord(
                {
                    "table_name": "posts",
                    "column_name": "user_id",
                    "referenced_table": "nonexistent_table",
                }
            )
        ]

        mock_connection.fetch.side_effect = [
            table_records,  # tables
            orphaned_fk_records,  # orphaned foreign key
        ]

        result = await validator.validate_schema_consistency("public")

        assert not result.is_valid
        assert result.severity == ValidationSeverity.ERROR
        assert any(d["type"] == "orphaned_foreign_key" for d in result.discrepancies)
        assert "consistency issues" in result.summary

    @pytest.mark.asyncio
    async def test_database_error_handling(self, validator, mock_pool, mock_connection):
        """Test database error handling during validation."""
        # Setup async context manager for pool.acquire()
        from unittest.mock import MagicMock

        async_context_manager = MagicMock()
        async_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
        async_context_manager.__aexit__ = AsyncMock(return_value=None)
        mock_pool.acquire = MagicMock(return_value=async_context_manager)

        # Mock database error
        mock_connection.fetch.side_effect = Exception("Database connection error")

        with pytest.raises(DatabaseError, match="Unexpected error during validation"):
            await validator.validate_table_structure("users", "public", {})

    def test_validation_result_add_discrepancy(self):
        """Test ValidationResult.add_discrepancy method."""
        result = ValidationResult(
            is_valid=True,
            discrepancies=[],
            summary="",
            severity=ValidationSeverity.INFO,
        )

        discrepancy = {
            "type": "test_error",
            "message": "Test error message",
            "severity": ValidationSeverity.ERROR.value,
        }

        result.add_discrepancy(discrepancy)

        assert not result.is_valid
        assert len(result.discrepancies) == 1
        assert result.discrepancies[0] == discrepancy

    def test_column_definition_dataclass(self):
        """Test ColumnDefinition dataclass."""
        col_def = ColumnDefinition(
            name="test_col", data_type="integer", nullable=False, primary_key=True
        )

        assert col_def.name == "test_col"
        assert col_def.data_type == "integer"
        assert col_def.nullable is False
        assert col_def.primary_key is True
        assert col_def.foreign_key is None

    def test_table_structure_dataclass(self):
        """Test TableStructure dataclass."""
        columns = [
            ColumnDefinition(name="id", data_type="integer", primary_key=True),
            ColumnDefinition(name="name", data_type="varchar", nullable=False),
        ]

        table_struct = TableStructure(
            table_name="users", schema_name="public", columns=columns
        )

        assert table_struct.table_name == "users"
        assert table_struct.schema_name == "public"
        assert len(table_struct.columns) == 2
        assert table_struct.indexes is None
        assert table_struct.constraints is None
