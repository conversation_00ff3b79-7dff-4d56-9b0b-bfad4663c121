"""Unified validation framework validator tests

Tests for DataStructureValidator, DataValueValidator, TelecomDataValidator and other validators.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import tempfile
import os

from ..validators import (
    DataStructureValidator,
    DataValueValidator,
    TelecomDataValidator,
    DatabaseValidator,
    FileValidator
)
from ..core import ValidationContext, ValidationSeverity
from ..exceptions import ValidationError
from .conftest import performance_test, memory_test


class TestDataStructureValidator:
    """Data structure validator test class"""
    
    def test_required_columns_validation_success(self, sample_cdr_data):
        """Test required columns validation success"""
        required_columns = ["CALL_ID", "CALLER_NUMBER", "CALLED_NUMBER"]
        validator = DataStructureValidator(
            required_columns=required_columns
        )
        
        issue = validator.validate(sample_cdr_data)
        assert issue is None
    
    def test_required_columns_validation_failure(self, missing_columns_data):
        """Test required columns validation failure"""
        required_columns = ["CALL_ID", "CALLER_NUMBER", "CALLED_NUMBER", "CALL_START_TIME"]
        validator = DataStructureValidator(
            required_columns=required_columns
        )
        
        issue = validator.validate(missing_columns_data)
        assert issue is not None
        assert issue.severity == ValidationSeverity.ERROR
        assert "Missing required columns" in issue.message
        assert "CALLED_NUMBER" in issue.details["missing_columns"]
    
    def test_forbidden_columns_validation(self, sample_cdr_data):
        """Test forbidden columns validation"""
        forbidden_columns = ["CALL_ID"]  # CALL_ID exists in the data
        validator = DataStructureValidator(
            forbidden_columns=forbidden_columns
        )
        
        issue = validator.validate(sample_cdr_data)
        assert issue is not None
        assert issue.severity == ValidationSeverity.WARNING
        assert "Forbidden columns found" in issue.message
        assert "CALL_ID" in issue.details["forbidden_columns"]
    
    def test_min_rows_validation_success(self, sample_cdr_data):
        """Test minimum rows validation success"""
        validator = DataStructureValidator(min_rows=3)
        
        issue = validator.validate(sample_cdr_data)
        assert issue is None
    
    def test_min_rows_validation_failure(self, sample_cdr_data):
        """Test minimum rows validation failure"""
        validator = DataStructureValidator(min_rows=10)
        
        issue = validator.validate(sample_cdr_data)
        assert issue is not None
        assert issue.severity == ValidationSeverity.ERROR
        assert "Insufficient rows" in issue.message
    
    def test_max_rows_validation_success(self, sample_cdr_data):
        """Test maximum rows validation success"""
        validator = DataStructureValidator(max_rows=10)
        
        issue = validator.validate(sample_cdr_data)
        assert issue is None
    
    def test_max_rows_validation_failure(self, sample_cdr_data):
        """Test maximum rows validation failure"""
        validator = DataStructureValidator(max_rows=3)
        
        issue = validator.validate(sample_cdr_data)
        assert issue is not None
        assert issue.severity == ValidationSeverity.WARNING
        assert "Too many rows" in issue.message
    
    def test_empty_dataframe_validation(self, empty_dataframe):
        """Test empty DataFrame validation"""
        validator = DataStructureValidator(
            required_columns=["CALL_ID"],
            min_rows=1
        )
        
        issue = validator.validate(empty_dataframe)
        assert issue is not None
        assert "DataFrame is empty" in issue.message
    
    def test_duplicate_columns_validation(self):
        """Test duplicate columns validation"""
        # Create DataFrame with duplicate columns
        data = pd.DataFrame({
            'CALL_ID': ['CDR001', 'CDR002'],
            'CALLER_NUMBER': ['13800138001', '13800138002']
        })
        # Manually add duplicate column
        data['CALL_ID_DUPLICATE'] = data['CALL_ID']
        data.columns = ['CALL_ID', 'CALLER_NUMBER', 'CALL_ID']  # Create duplicate column names
        
        validator = DataStructureValidator()
        
        issue = validator.validate(data)
        assert issue is not None
        assert "Duplicate columns found" in issue.message


class TestDataValueValidator:
    """Data value validator test class"""
    
    def test_null_values_validation_success(self, sample_cdr_data):
        """Test null values validation success"""
        validator = DataValueValidator(
            null_columns=["CALL_ID", "CALLER_NUMBER"]
        )
        
        issue = validator.validate(sample_cdr_data)
        assert issue is None
    
    def test_null_values_validation_failure(self, invalid_cdr_data):
        """Test null values validation failure"""
        validator = DataValueValidator(
            null_columns=["CALL_ID"]
        )
        
        issue = validator.validate(invalid_cdr_data)
        assert issue is not None
        assert issue.severity == ValidationSeverity.ERROR
        assert "Null values found" in issue.message
        assert "CALL_ID" in issue.details["columns_with_nulls"]
    
    def test_data_types_validation_success(self, sample_cdr_data):
        """Test data types validation success"""
        type_mapping = {
            "CALL_DURATION": "int64",
            "LAC": "int64"
        }
        validator = DataValueValidator(type_mapping=type_mapping)
        
        issue = validator.validate(sample_cdr_data)
        assert issue is None
    
    def test_data_types_validation_failure(self, invalid_kpi_data):
        """Test data types validation failure"""
        type_mapping = {
            "KPI_VALUE": "float64"
        }
        validator = DataValueValidator(type_mapping=type_mapping)
        
        issue = validator.validate(invalid_kpi_data)
        assert issue is not None
        assert "Data type validation failed" in issue.message
    
    def test_range_validation_success(self, sample_cdr_data):
        """Test range validation success"""
        range_mapping = {
            "CALL_DURATION": (0, 7200),  # 0 to 2 hours
            "LONGITUDE": (116.0, 117.0),
            "LATITUDE": (39.0, 40.0)
        }
        validator = DataValueValidator(range_mapping=range_mapping)
        
        issue = validator.validate(sample_cdr_data)
        assert issue is None
    
    def test_range_validation_failure(self, invalid_cdr_data):
        """Test range validation failure"""
        range_mapping = {
            "CALL_DURATION": (0, 7200)  # No negative values allowed
        }
        validator = DataValueValidator(range_mapping=range_mapping)
        
        issue = validator.validate(invalid_cdr_data)
        assert issue is not None
        assert "Range validation failed" in issue.message
        assert "CALL_DURATION" in issue.details["out_of_range_columns"]
    
    def test_pattern_validation_success(self, sample_cdr_data):
        """Test pattern validation success"""
        pattern_mapping = {
            "CALLER_NUMBER": r"^138\d{8}$",  # 11 digits starting with 138
            "CELL_ID": r"^CELL\d{3}$"  # CELL prefix with 3 digits
        }
        validator = DataValueValidator(pattern_mapping=pattern_mapping)
        
        issue = validator.validate(sample_cdr_data)
        assert issue is None
    
    def test_pattern_validation_failure(self):
        """Test pattern validation failure"""
        data = pd.DataFrame({
            'PHONE_NUMBER': ['13800138001', '1380013800', 'invalid_phone'],  # Third one doesn't match
        })
        
        pattern_mapping = {
            "PHONE_NUMBER": r"^138\d{8}$"
        }
        validator = DataValueValidator(pattern_mapping=pattern_mapping)
        
        issue = validator.validate(data)
        assert issue is not None
        assert "Pattern validation failed" in issue.message
    
    def test_unique_values_validation_success(self, sample_cdr_data):
        """Test unique values validation success"""
        validator = DataValueValidator(
            unique_columns=["CALL_ID"]
        )
        
        issue = validator.validate(sample_cdr_data)
        assert issue is None
    
    def test_unique_values_validation_failure(self):
        """Test unique values validation failure"""
        data = pd.DataFrame({
            'CALL_ID': ['CDR001', 'CDR002', 'CDR001'],  # CDR001 duplicated
            'CALLER_NUMBER': ['13800138001', '13800138002', '13800138003']
        })
        
        validator = DataValueValidator(
            unique_columns=["CALL_ID"]
        )
        
        issue = validator.validate(data)
        assert issue is not None
        assert "Duplicate values found" in issue.message
        assert "CALL_ID" in issue.details["duplicate_columns"]
    
    def test_custom_validation_function(self, sample_cdr_data):
        """Test custom validation function"""
        def validate_call_duration(data):
            """Custom validation: call duration should be reasonable"""
            if 'CALL_DURATION' in data.columns:
                invalid_durations = data[
                    (data['CALL_DURATION'] < 0) | (data['CALL_DURATION'] > 7200)
                ]
                if not invalid_durations.empty:
                    return f"Invalid call durations found: {len(invalid_durations)} records"
            return None
        
        validator = DataValueValidator(
            custom_validators=[validate_call_duration]
        )
        
        issue = validator.validate(sample_cdr_data)
        assert issue is None
    
    def test_custom_validation_function_failure(self, invalid_cdr_data):
        """Test custom validation function failure"""
        def validate_call_duration(data):
            if 'CALL_DURATION' in data.columns:
                invalid_durations = data[data['CALL_DURATION'] < 0]
                if not invalid_durations.empty:
                    return f"Negative call durations found: {len(invalid_durations)} records"
            return None
        
        validator = DataValueValidator(
            custom_validators=[validate_call_duration]
        )
        
        issue = validator.validate(invalid_cdr_data)
        assert issue is not None
        assert "Custom validation failed" in issue.message
        assert "Negative call durations" in issue.details["validation_errors"][0]


class TestTelecomDataValidator:
    """Telecom data validator test class"""
    
    def test_cdr_validation_success(self, sample_cdr_data):
        """Test CDR validation success"""
        validator = TelecomDataValidator(data_type="cdr")
        
        issue = validator.validate(sample_cdr_data)
        assert issue is None
    
    def test_cdr_validation_failure(self, invalid_cdr_data):
        """Test CDR validation failure"""
        validator = TelecomDataValidator(data_type="cdr")
        
        issue = validator.validate(invalid_cdr_data)
        assert issue is not None
        assert "CDR validation failed" in issue.message
    
    def test_kpi_validation_success(self, sample_kpi_data):
        """Test KPI validation success"""
        validator = TelecomDataValidator(data_type="kpi")
        
        issue = validator.validate(sample_kpi_data)
        assert issue is None
    
    def test_kpi_validation_failure(self, invalid_kpi_data):
        """Test KPI validation failure"""
        validator = TelecomDataValidator(data_type="kpi")
        
        issue = validator.validate(invalid_kpi_data)
        assert issue is not None
        assert "KPI validation failed" in issue.message
    
    def test_cfg_validation_success(self, sample_cfg_data):
        """Test configuration validation success"""
        validator = TelecomDataValidator(data_type="cfg")
        
        issue = validator.validate(sample_cfg_data)
        assert issue is None
    
    def test_call_duration_consistency(self, sample_cdr_data):
        """Test call duration consistency"""
        validator = TelecomDataValidator(data_type="cdr")
        
        # Modify data to make duration inconsistent
        data = sample_cdr_data.copy()
        data.loc[0, 'CALL_DURATION'] = 999999  # Doesn't match actual time difference
        
        issue = validator.validate(data)
        # Should detect duration inconsistency, but specific implementation may be in subclass
        # assert issue is not None
    
    def test_coordinate_validation(self, sample_cdr_data):
        """Test coordinate validation"""
        validator = TelecomDataValidator(data_type="cdr")
        
        # Modify data to make coordinates invalid
        data = sample_cdr_data.copy()
        data.loc[0, 'LONGITUDE'] = 200  # Invalid longitude
        data.loc[1, 'LATITUDE'] = 100   # Invalid latitude
        
        issue = validator.validate(data)
        assert issue is not None
        assert "Telecom validation failed" in issue.message
    
    def test_phone_number_format(self, sample_cdr_data):
        """Test phone number format"""
        validator = TelecomDataValidator(data_type="cdr")
        
        # Modify data to make phone number format invalid
        data = sample_cdr_data.copy()
        data.loc[0, 'CALLER_NUMBER'] = '123'  # Invalid format
        
        issue = validator.validate(data)
        assert issue is not None
    
    def test_unsupported_data_type(self, sample_cdr_data):
        """Test unsupported data type"""
        with pytest.raises(ValidationError):
            TelecomDataValidator(data_type="unsupported")
    
    def test_cell_id_format(self, sample_cdr_data):
        """Test cell ID format"""
        validator = TelecomDataValidator(data_type="cdr")
        
        # Modify data to make cell ID format invalid
        data = sample_cdr_data.copy()
        data.loc[0, 'CELL_ID'] = ''  # Empty cell ID
        
        issue = validator.validate(data)
        assert issue is not None


class TestDatabaseValidator:
    """Database validator test class"""
    
    def test_schema_name_validation_success(self):
        """Test schema name validation success"""
        validator = DatabaseValidator()
        
        valid_names = ['public', 'telecom_data', 'user_schema_123']
        for name in valid_names:
            issue = validator.validate({'schema_name': name})
            assert issue is None, f"Schema name '{name}' should be valid"
    
    def test_schema_name_validation_failure(self):
        """Test schema name validation failure"""
        validator = DatabaseValidator()
        
        invalid_names = ['123invalid', 'schema-name', 'schema name', '']
        for name in invalid_names:
            issue = validator.validate({'schema_name': name})
            assert issue is not None, f"Schema name '{name}' should be invalid"
    
    def test_table_name_validation_success(self):
        """Test table name validation success"""
        validator = DatabaseValidator()
        
        valid_names = ['cdr_data', 'kpi_measurements', 'user_table_123']
        for name in valid_names:
            issue = validator.validate({'table_name': name})
            assert issue is None, f"Table name '{name}' should be valid"
    
    def test_table_name_validation_failure(self):
        """Test table name validation failure"""
        validator = DatabaseValidator()
        
        invalid_names = ['123table', 'table-name', 'table name', 'select']
        for name in invalid_names:
            issue = validator.validate({'table_name': name})
            assert issue is not None, f"Table name '{name}' should be invalid"
    
    def test_column_name_validation_success(self):
        """Test column name validation success"""
        validator = DatabaseValidator()
        
        valid_names = ['call_id', 'caller_number', 'measurement_time']
        for name in valid_names:
            issue = validator.validate({'column_name': name})
            assert issue is None, f"Column name '{name}' should be valid"
    
    def test_column_name_validation_failure(self):
        """Test column name validation failure"""
        validator = DatabaseValidator()
        
        invalid_names = ['123column', 'column-name', 'column name', 'from']
        for name in invalid_names:
            issue = validator.validate({'column_name': name})
            assert issue is not None, f"Column name '{name}' should be invalid"


class TestFileValidator:
    """File validator test class"""
    
    def test_file_exists_validation_success(self, temp_csv_file):
        """Test file exists validation success"""
        validator = FileValidator()
        
        issue = validator.validate({'file_path': temp_csv_file})
        assert issue is None
    
    def test_file_exists_validation_failure(self):
        """Test file exists validation failure"""
        validator = FileValidator()
        
        issue = validator.validate({'file_path': '/non/existent/file.csv'})
        assert issue is not None
        assert "File does not exist" in issue.message
    
    def test_file_extension_validation_success(self, temp_csv_file):
        """Test file extension validation success"""
        validator = FileValidator(allowed_extensions=['.csv', '.txt'])
        
        issue = validator.validate({'file_path': temp_csv_file})
        assert issue is None
    
    def test_file_extension_validation_failure(self, temp_csv_file):
        """Test file extension validation failure"""
        validator = FileValidator(allowed_extensions=['.txt', '.json'])
        
        issue = validator.validate({'file_path': temp_csv_file})
        assert issue is not None
        assert "Invalid file extension" in issue.message
    
    def test_file_size_validation_success(self, temp_csv_file):
        """Test file size validation success"""
        validator = FileValidator(max_size_mb=10)  # 10MB limit
        
        issue = validator.validate({'file_path': temp_csv_file})
        assert issue is None
    
    def test_file_size_validation_failure(self, temp_csv_file):
        """Test file size validation failure"""
        validator = FileValidator(max_size_mb=0.001)  # Very small limit
        
        issue = validator.validate({'file_path': temp_csv_file})
        assert issue is not None
        assert "File size exceeds limit" in issue.message
    
    def test_file_readable_validation(self, temp_csv_file):
        """Test file readability validation"""
        validator = FileValidator()
        
        issue = validator.validate({'file_path': temp_csv_file})
        assert issue is None
    
    @pytest.mark.skipif(os.name == 'nt', reason="Permission tests may not work on Windows")
    def test_file_readable_validation_failure(self, temp_csv_file):
        """Test file unreadable validation failure"""
        # Remove read permissions
        os.chmod(temp_csv_file, 0o000)
        
        try:
            validator = FileValidator()
            issue = validator.validate({'file_path': temp_csv_file})
            assert issue is not None
            assert "File is not readable" in issue.message
        finally:
            # Restore permissions for cleanup
            os.chmod(temp_csv_file, 0o644)


class TestValidatorPerformance:
    """Validator performance test class"""
    
    @performance_test(max_time=1.0)
    def test_structure_validator_performance(self, large_dataset):
        """Test structure validator performance"""
        validator = DataStructureValidator(
            required_columns=list(large_dataset.columns),
            min_rows=1000
        )
        
        issue = validator.validate(large_dataset)
        assert issue is None
    
    @performance_test(max_time=2.0)
    def test_value_validator_performance(self, large_dataset):
        """Test value validator performance"""
        validator = DataValueValidator(
            null_columns=['CALL_ID', 'CALLER_NUMBER'],
            range_mapping={
                'CALL_DURATION': (0, 7200),
                'LONGITUDE': (116.0, 117.0),
                'LATITUDE': (39.0, 40.0)
            }
        )
        
        issue = validator.validate(large_dataset)
        # May have some validation failures, but should complete within time limit
    
    @memory_test(max_memory_mb=100)
    def test_telecom_validator_memory(self, large_dataset):
        """Test telecom validator memory usage"""
        validator = TelecomDataValidator(data_type="cdr")
        
        issue = validator.validate(large_dataset)
        # Memory usage should be within limits


class TestValidatorIntegration:
    """Validator integration test class"""
    
    def test_multiple_validators_chain(self, sample_cdr_data):
        """Test multiple validators chain call"""
        validators = [
            DataStructureValidator(
                required_columns=['CALL_ID', 'CALLER_NUMBER', 'CALLED_NUMBER']
            ),
            DataValueValidator(
                null_columns=['CALL_ID'],
                range_mapping={'CALL_DURATION': (0, 7200)}
            ),
            TelecomDataValidator(data_type="cdr")
        ]
        
        issues = []
        for validator in validators:
            issue = validator.validate(sample_cdr_data)
            if issue:
                issues.append(issue)
        
        assert len(issues) == 0, f"Validation issues found: {[issue.message for issue in issues]}"
    
    def test_validator_with_context(self, sample_cdr_data, validation_context):
        """Test validator with context"""
        validator = TelecomDataValidator(data_type="cdr")
        
        issue = validator.validate(sample_cdr_data, validation_context)
        assert issue is None
    
    def test_validator_error_aggregation(self, invalid_cdr_data):
        """Test validator error aggregation"""
        validators = [
            DataStructureValidator(required_columns=['MISSING_COLUMN']),
            DataValueValidator(null_columns=['CALL_ID']),
            DataValueValidator(range_mapping={'CALL_DURATION': (0, 1000)})
        ]
        
        issues = []
        for validator in validators:
            issue = validator.validate(invalid_cdr_data)
            if issue:
                issues.append(issue)
        
        assert len(issues) > 0
        # Should have multiple different types of validation errors
        issue_types = [issue.rule_name for issue in issues]
        assert len(set(issue_types)) > 1  # At least two different error types


if __name__ == "__main__":
    pytest.main([__file__])