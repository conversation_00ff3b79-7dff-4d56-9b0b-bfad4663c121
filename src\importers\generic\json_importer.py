"""Generic JSON importer with nested structure processing capabilities.

This module provides a specialized JSON importer that can handle complex JSON structures,
nested objects, arrays, and large JSON files with streaming support.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union, Iterator, Generator
from pathlib import Path
from datetime import datetime
import io

import pandas as pd
import numpy as np
from pydantic import BaseModel, Field, ConfigDict
import ijson  # For streaming JSON parsing

from ..base import (
    AbstractImporter,
    ValidationMixin,
    ProcessingMixin,
    PerformanceMixin,
    ImporterConfig,
    ImportResult,
    ImportStatus
)


class JSONConfig(BaseModel):
    """Configuration specific to JSON import."""
    # JSON structure settings
    json_path: Optional[str] = Field(default=None, description="JSONPath to extract data (e.g., '$.data[*]')")
    root_key: Optional[str] = Field(default=None, description="Root key containing the data array")
    nested_separator: str = Field(default=".", description="Separator for flattening nested objects")
    
    # Data extraction settings
    flatten_nested: bool = Field(default=True, description="Flatten nested objects into columns")
    max_nesting_depth: int = Field(default=5, description="Maximum depth for flattening")
    array_handling: str = Field(default="expand", description="How to handle arrays: 'expand', 'join', 'first', 'ignore'")
    array_separator: str = Field(default="|", description="Separator for joining array values")
    
    # Data type settings
    auto_detect_types: bool = Field(default=True, description="Auto-detect column data types")
    date_columns: List[str] = Field(default_factory=list, description="Columns to parse as dates")
    date_formats: List[str] = Field(
        default_factory=lambda: ['%Y-%m-%d', '%Y-%m-%dT%H:%M:%S', '%Y-%m-%d %H:%M:%S', 'ISO8601'],
        description="Date formats to try"
    )
    numeric_columns: List[str] = Field(default_factory=list, description="Columns to parse as numeric")
    string_columns: List[str] = Field(default_factory=list, description="Columns to keep as strings")
    
    # File processing settings
    encoding: str = Field(default="utf-8", description="File encoding")
    streaming: bool = Field(default=False, description="Use streaming parser for large files")
    chunk_size: int = Field(default=10000, description="Chunk size for streaming")
    memory_limit_mb: int = Field(default=512, description="Memory limit for processing")
    
    # JSON parsing settings
    strict_parsing: bool = Field(default=False, description="Strict JSON parsing")
    allow_comments: bool = Field(default=False, description="Allow JSON with comments")
    allow_trailing_commas: bool = Field(default=False, description="Allow trailing commas")
    
    # Data cleaning settings
    remove_empty_objects: bool = Field(default=True, description="Remove empty objects")
    remove_null_columns: bool = Field(default=True, description="Remove columns with all null values")
    normalize_column_names: bool = Field(default=True, description="Normalize column names")
    strip_whitespace: bool = Field(default=True, description="Strip whitespace from strings")
    
    # Schema settings
    infer_schema: bool = Field(default=True, description="Infer schema from data")
    schema_sample_size: int = Field(default=1000, description="Sample size for schema inference")
    required_fields: List[str] = Field(default_factory=list, description="Required field names")
    
    # Output settings
    preserve_original: bool = Field(default=False, description="Preserve original JSON structure")
    add_metadata: bool = Field(default=True, description="Add metadata columns")
    add_json_path: bool = Field(default=False, description="Add JSON path for each record")
    
    model_config = ConfigDict(
        extra="allow"
    )
class JSONImporter(AbstractImporter, ValidationMixin, ProcessingMixin, PerformanceMixin):
    """High-performance JSON importer with nested structure processing.
    
    This importer provides comprehensive JSON processing with support for:
    - Complex nested JSON structures
    - Streaming processing for large JSON files
    - Flexible data extraction with JSONPath
    - Automatic schema inference and data type detection
    - Array handling with multiple strategies
    - Data cleaning and normalization
    - Performance monitoring and optimization
    """
    
    # Supported array handling strategies
    ARRAY_STRATEGIES = {
        'expand': 'Create separate rows for each array element',
        'join': 'Join array elements into a single string',
        'first': 'Take only the first element',
        'ignore': 'Ignore array fields'
    }
    
    def __init__(self, config: Optional[Union[ImporterConfig, Dict[str, Any]]] = None, **kwargs):
        """Initialize JSON importer.
        
        Args:
            config: Importer configuration
            **kwargs: Additional configuration parameters
        """
        super().__init__(config=config, **kwargs)
        
        # JSON-specific configuration
        json_config_dict = kwargs.get('json_config', {})
        if isinstance(json_config_dict, JSONConfig):
            self.json_config = json_config_dict
        else:
            self.json_config = JSONConfig(**json_config_dict)
            
        # Set data type
        self.data_type = 'JSON'
        self.name = 'JSON Importer'
        self.supported_formats = ['.json', '.jsonl', '.ndjson']
        
        # Configure processing for JSON data
        self.configure_processing({
            'batch_size': self.json_config.chunk_size,
            'enable_parallel': True,
            'memory_limit_mb': self.json_config.memory_limit_mb,
            'use_polars': False  # Pandas for better JSON handling
        })
        
        # Configure validation
        self.configure_validation({
            'enable_strict_validation': self.json_config.strict_parsing,
            'required_fields': self.json_config.required_fields
        })
        
        # Configure performance monitoring
        self.configure_performance({
            'enable_monitoring': True,
            'json_chunk_size': self.json_config.chunk_size
        })
        
        self.logger.info(f"JSON Importer initialized with streaming: {self.json_config.streaming}")
        
    async def import_data(self, source: Union[str, Path], **kwargs) -> ImportResult:
        """Import JSON data with comprehensive processing.
        
        Args:
            source: JSON file path
            **kwargs: Additional import parameters
            
        Returns:
            Import result with metrics and status
        """
        start_time = datetime.now()
        
        try:
            # Start performance monitoring
            await self.start_performance_monitoring()
            
            with self.track_operation("json_import", source=str(source)) as op_metrics:
                # Validate source
                if not await self.validate_source(source):
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        message="Source validation failed",
                        records_processed=0,
                        errors=["Invalid or inaccessible JSON file"]
                    )
                    
                # Analyze JSON file
                file_info = await self._analyze_json_file(source)
                
                # Load and process data
                if self.json_config.streaming and file_info['file_size_mb'] > 50:
                    data = await self._load_json_streaming(source, file_info, **kwargs)
                else:
                    data = await self._load_json_standard(source, file_info, **kwargs)
                    
                if data is None or len(data) == 0:
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        message="No data loaded from JSON file",
                        records_processed=0
                    )
                    
                # Process data
                processed_data = await self._process_json_data(data)
                
                # Update operation metrics
                op_metrics.records_processed = len(processed_data)
                op_metrics.bytes_processed = processed_data.memory_usage(deep=True).sum()
                
                # Validate data
                validation_result = await self._validate_json_data(processed_data)
                
                if not validation_result['is_valid']:
                    if validation_result['error_rate'] > 0.1:  # 10% error threshold
                        return ImportResult(
                            status=ImportStatus.FAILED,
                            message=f"Data quality below threshold: {validation_result['error_rate']:.2%}",
                            records_processed=len(processed_data),
                            errors=validation_result['errors'][:10]
                        )
                    else:
                        self.logger.warning(f"Data quality issues detected: {validation_result['error_rate']:.2%}")
                        
                # Store data if database operations are available
                if hasattr(self, 'db_ops') and self.db_ops:
                    try:
                        await self._store_json_data(processed_data, source, file_info)
                    except Exception as e:
                        self.logger.error(f"Database storage failed: {e}")
                        return ImportResult(
                            status=ImportStatus.FAILED,
                            message=f"Database storage failed: {e}",
                            records_processed=len(processed_data),
                            errors=[str(e)]
                        )
                        
                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # Prepare metadata
                metadata = {
                    'file_info': file_info,
                    'columns': list(processed_data.columns),
                    'data_types': processed_data.dtypes.to_dict(),
                    'memory_usage_mb': processed_data.memory_usage(deep=True).sum() / 1024 / 1024,
                    'schema_info': self._get_schema_info(processed_data)
                }
                
                return ImportResult(
                    status=ImportStatus.SUCCESS,
                    message=f"Successfully imported {len(processed_data)} records from JSON",
                    records_processed=len(processed_data),
                    processing_time_seconds=processing_time,
                    data_quality_score=validation_result['quality_score'],
                    warnings=validation_result.get('warnings', [])[:5],
                    metadata=metadata
                )
                
        except Exception as e:
            self.logger.error(f"JSON import failed: {e}")
            return ImportResult(
                status=ImportStatus.FAILED,
                message=f"Import failed: {e}",
                records_processed=0,
                errors=[str(e)]
            )
            
        finally:
            await self.stop_performance_monitoring()
            
    async def _analyze_json_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Analyze JSON file to detect characteristics.
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            Dictionary with file analysis results
        """
        file_path = Path(file_path)
        
        # Basic file info
        stat = file_path.stat()
        file_info = {
            'file_size_bytes': stat.st_size,
            'file_size_mb': stat.st_size / 1024 / 1024,
            'file_format': file_path.suffix.lower(),
            'encoding': self.json_config.encoding,
            'is_line_delimited': file_path.suffix.lower() in ['.jsonl', '.ndjson']
        }
        
        try:
            # Sample the file to understand structure
            with open(file_path, 'r', encoding=self.json_config.encoding) as f:
                if file_info['is_line_delimited']:
                    # Line-delimited JSON
                    sample_lines = []
                    for i, line in enumerate(f):
                        if i >= 10:  # Sample first 10 lines
                            break
                        try:
                            sample_lines.append(json.loads(line.strip()))
                        except json.JSONDecodeError:
                            continue
                            
                    file_info['sample_records'] = sample_lines
                    file_info['structure_type'] = 'line_delimited'
                    
                else:
                    # Regular JSON
                    # Read first 1KB to detect structure
                    sample_content = f.read(1024)
                    f.seek(0)
                    
                    # Try to parse as JSON
                    try:
                        if sample_content.strip().startswith('['):
                            file_info['structure_type'] = 'array'
                        elif sample_content.strip().startswith('{'):
                            file_info['structure_type'] = 'object'
                        else:
                            file_info['structure_type'] = 'unknown'
                            
                        # For small files, load sample data
                        if file_info['file_size_mb'] < 10:
                            sample_data = json.load(f)
                            if isinstance(sample_data, list):
                                file_info['sample_records'] = sample_data[:10]
                                file_info['estimated_records'] = len(sample_data)
                            elif isinstance(sample_data, dict):
                                file_info['sample_records'] = [sample_data]
                                file_info['estimated_records'] = 1
                                file_info['root_keys'] = list(sample_data.keys())
                                
                    except json.JSONDecodeError as e:
                        file_info['parse_error'] = str(e)
                        
        except Exception as e:
            self.logger.warning(f"File analysis failed: {e}")
            file_info['analysis_error'] = str(e)
            
        return file_info
        
    async def _load_json_standard(self, file_path: Union[str, Path], file_info: Dict[str, Any], **kwargs) -> pd.DataFrame:
        """Load JSON data using standard parsing.
        
        Args:
            file_path: Path to JSON file
            file_info: File analysis results
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with loaded data
        """
        file_path = Path(file_path)
        
        try:
            with open(file_path, 'r', encoding=self.json_config.encoding) as f:
                if file_info['is_line_delimited']:
                    # Line-delimited JSON
                    records = []
                    for line_num, line in enumerate(f, 1):
                        try:
                            record = json.loads(line.strip())
                            if self.json_config.add_metadata:
                                record['_line_number'] = line_num
                            records.append(record)
                        except json.JSONDecodeError as e:
                            self.logger.warning(f"Skipping invalid JSON on line {line_num}: {e}")
                            continue
                            
                else:
                    # Regular JSON
                    data = json.load(f)
                    
                    # Extract records based on structure
                    records = self._extract_records(data, file_info)
                    
            # Convert to DataFrame
            if records:
                data = pd.DataFrame(records)
                
                # Add metadata
                if self.json_config.add_metadata:
                    data = self._add_json_metadata(data, file_path)
                    
                return data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Failed to load JSON data: {e}")
            raise
            
    async def _load_json_streaming(self, file_path: Union[str, Path], file_info: Dict[str, Any], **kwargs) -> pd.DataFrame:
        """Load JSON data using streaming parser.
        
        Args:
            file_path: Path to JSON file
            file_info: File analysis results
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with loaded data
        """
        file_path = Path(file_path)
        records = []
        
        try:
            with open(file_path, 'rb') as f:
                if file_info['is_line_delimited']:
                    # Stream line-delimited JSON
                    line_num = 0
                    for line in f:
                        line_num += 1
                        try:
                            record = json.loads(line.decode(self.json_config.encoding).strip())
                            if self.json_config.add_metadata:
                                record['_line_number'] = line_num
                            records.append(record)
                            
                            # Process in chunks
                            if len(records) >= self.json_config.chunk_size:
                                chunk_df = pd.DataFrame(records)
                                if len(records) == self.json_config.chunk_size:  # First chunk
                                    data = chunk_df
                                else:
                                    data = pd.concat([data, chunk_df], ignore_index=True)
                                records = []
                                
                        except json.JSONDecodeError as e:
                            self.logger.warning(f"Skipping invalid JSON on line {line_num}: {e}")
                            continue
                            
                else:
                    # Stream regular JSON array
                    if self.json_config.json_path:
                        # Use JSONPath for extraction
                        parser = ijson.parse(f)
                        for prefix, event, value in parser:
                            if event == 'start_array' and prefix == self.json_config.json_path.replace('$', '').replace('[*]', ''):
                                continue
                            elif event in ['start_map', 'end_map'] and prefix.startswith(self.json_config.json_path.replace('$', '').replace('[*]', '')):
                                if event == 'end_map':
                                    records.append(value)
                                    
                    else:
                        # Stream array items
                        items = ijson.items(f, 'item')
                        for item in items:
                            records.append(item)
                            
                            # Process in chunks
                            if len(records) >= self.json_config.chunk_size:
                                chunk_df = pd.DataFrame(records)
                                if 'data' not in locals():
                                    data = chunk_df
                                else:
                                    data = pd.concat([data, chunk_df], ignore_index=True)
                                records = []
                                
            # Process remaining records
            if records:
                chunk_df = pd.DataFrame(records)
                if 'data' not in locals():
                    data = chunk_df
                else:
                    data = pd.concat([data, chunk_df], ignore_index=True)
                    
            # Add metadata
            if self.json_config.add_metadata and 'data' in locals():
                data = self._add_json_metadata(data, file_path)
                
            return data if 'data' in locals() else pd.DataFrame()
            
        except Exception as e:
            self.logger.error(f"Failed to stream JSON data: {e}")
            raise
            
    def _extract_records(self, data: Any, file_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract records from JSON data structure.
        
        Args:
            data: Parsed JSON data
            file_info: File analysis results
            
        Returns:
            List of record dictionaries
        """
        records = []
        
        if isinstance(data, list):
            # Array of records
            records = data
            
        elif isinstance(data, dict):
            if self.json_config.root_key and self.json_config.root_key in data:
                # Extract from specific key
                root_data = data[self.json_config.root_key]
                if isinstance(root_data, list):
                    records = root_data
                else:
                    records = [root_data]
            else:
                # Single object
                records = [data]
                
        else:
            # Primitive value
            records = [{'value': data}]
            
        return records
        
    def _add_json_metadata(self, data: pd.DataFrame, file_path: Path) -> pd.DataFrame:
        """Add JSON metadata columns to DataFrame.
        
        Args:
            data: DataFrame to enhance
            file_path: Source file path
            
        Returns:
            Enhanced DataFrame
        """
        if self.json_config.add_metadata:
            data['_source_file'] = file_path.name
            data['_import_timestamp'] = datetime.now()
            data['_file_size_mb'] = file_path.stat().st_size / 1024 / 1024
            
            if '_line_number' not in data.columns:
                data['_record_number'] = range(1, len(data) + 1)
                
        return data
        
    async def _process_json_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Process loaded JSON data.
        
        Args:
            data: DataFrame to process
            
        Returns:
            Processed DataFrame
        """
        if len(data) == 0:
            return data
            
        # Flatten nested structures
        if self.json_config.flatten_nested:
            data = self._flatten_nested_data(data)
            
        # Handle arrays
        data = self._handle_array_columns(data)
        
        # Clean data
        data = await self._clean_json_data(data)
        
        # Convert data types
        data = self._convert_data_types(data)
        
        # Normalize column names
        if self.json_config.normalize_column_names:
            data = self._normalize_column_names(data)
            
        return data
        
    def _flatten_nested_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Flatten nested JSON structures.
        
        Args:
            data: DataFrame with nested structures
            
        Returns:
            DataFrame with flattened columns
        """
        def flatten_dict(d: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
            """Recursively flatten a nested dictionary."""
            items = []
            for k, v in d.items():
                new_key = f"{parent_key}{sep}{k}" if parent_key else k
                
                if isinstance(v, dict) and len(str(new_key).split(sep)) <= self.json_config.max_nesting_depth:
                    items.extend(flatten_dict(v, new_key, sep=sep).items())
                else:
                    items.append((new_key, v))
            return dict(items)
            
        flattened_records = []
        
        for _, row in data.iterrows():
            flattened_record = {}
            
            for col, value in row.items():
                if isinstance(value, dict):
                    # Flatten nested dictionary
                    flattened_dict = flatten_dict(value, col, self.json_config.nested_separator)
                    flattened_record.update(flattened_dict)
                else:
                    flattened_record[col] = value
                    
            flattened_records.append(flattened_record)
            
        return pd.DataFrame(flattened_records)
        
    def _handle_array_columns(self, data: pd.DataFrame) -> pd.DataFrame:
        """Handle array columns according to configuration.
        
        Args:
            data: DataFrame with potential array columns
            
        Returns:
            DataFrame with processed array columns
        """
        array_columns = []
        
        # Identify array columns
        for col in data.columns:
            if data[col].apply(lambda x: isinstance(x, list)).any():
                array_columns.append(col)
                
        if not array_columns:
            return data
            
        strategy = self.json_config.array_handling
        
        if strategy == 'expand':
            # Expand arrays into separate rows
            expanded_dfs = []
            
            for _, row in data.iterrows():
                row_data = row.to_dict()
                
                # Find the maximum array length
                max_length = 1
                for col in array_columns:
                    if isinstance(row_data[col], list):
                        max_length = max(max_length, len(row_data[col]))
                        
                # Create expanded rows
                for i in range(max_length):
                    expanded_row = row_data.copy()
                    
                    for col in array_columns:
                        if isinstance(row_data[col], list):
                            if i < len(row_data[col]):
                                expanded_row[col] = row_data[col][i]
                            else:
                                expanded_row[col] = None
                                
                    expanded_dfs.append(expanded_row)
                    
            return pd.DataFrame(expanded_dfs)
            
        elif strategy == 'join':
            # Join array elements into strings
            for col in array_columns:
                data[col] = data[col].apply(
                    lambda x: self.json_config.array_separator.join(map(str, x)) if isinstance(x, list) else x
                )
                
        elif strategy == 'first':
            # Take first element of arrays
            for col in array_columns:
                data[col] = data[col].apply(
                    lambda x: x[0] if isinstance(x, list) and len(x) > 0 else None
                )
                
        elif strategy == 'ignore':
            # Remove array columns
            data = data.drop(columns=array_columns)
            
        return data
        
    async def _clean_json_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean JSON data according to configuration.
        
        Args:
            data: DataFrame to clean
            
        Returns:
            Cleaned DataFrame
        """
        original_rows = len(data)
        original_cols = len(data.columns)
        
        # Remove duplicate columns and standardize column names
        from src.utils.column_deduplicator import ColumnDeduplicator
        
        # Remove duplicate columns, keeping the most complete one
        data, dedup_report = ColumnDeduplicator.remove_duplicate_columns(
            data, keep_strategy='best'
        )
        
        if dedup_report['total_removed'] > 0:
            self.logger.info(f"Removed {dedup_report['total_removed']} duplicate columns in JSON data")
        
        # Standardize column names to lowercase
        clean_columns = []
        for col in data.columns:
            clean_name = str(col).strip().lower()
            clean_columns.append(clean_name)
        
        data.columns = clean_columns
        
        # Remove null columns
        if self.json_config.remove_null_columns:
            null_columns = data.columns[data.isnull().all()].tolist()
            if null_columns:
                data = data.drop(columns=null_columns)
                self.logger.info(f"Removed {len(null_columns)} null columns")
                
        # Strip whitespace from string columns
        if self.json_config.strip_whitespace:
            string_columns = data.select_dtypes(include=['object']).columns
            for col in string_columns:
                data[col] = data[col].astype(str).str.strip()
                # Convert back to NaN if empty after stripping
                data[col] = data[col].replace('', np.nan)
                
        # Log cleaning results
        rows_removed = original_rows - len(data)
        cols_removed = original_cols - len(data.columns)
        
        if rows_removed > 0 or cols_removed > 0:
            self.logger.info(f"Data cleaning: removed {rows_removed} rows, {cols_removed} columns")
            
        return data
        
    def _convert_data_types(self, data: pd.DataFrame) -> pd.DataFrame:
        """Convert data types according to configuration.
        
        Args:
            data: DataFrame to convert
            
        Returns:
            DataFrame with converted types
        """
        if not self.json_config.auto_detect_types:
            return data
            
        # Convert date columns
        for col in self.json_config.date_columns:
            if col in data.columns:
                data[col] = self._convert_to_datetime(data[col])
                
        # Convert numeric columns
        for col in self.json_config.numeric_columns:
            if col in data.columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')
                
        # Keep string columns as strings
        for col in self.json_config.string_columns:
            if col in data.columns:
                data[col] = data[col].astype(str)
                
        # Auto-detect remaining columns
        for col in data.columns:
            if col not in (self.json_config.date_columns + self.json_config.numeric_columns + self.json_config.string_columns):
                if col.startswith('_'):  # Skip metadata columns
                    continue
                    
                # Try to convert to numeric
                if data[col].dtype == 'object':
                    numeric_data = pd.to_numeric(data[col], errors='coerce')
                    if not numeric_data.isna().all():
                        # If more than 50% can be converted to numeric, convert the column
                        if (numeric_data.notna().sum() / len(data)) > 0.5:
                            data[col] = numeric_data
                            
        return data
        
    def _convert_to_datetime(self, series: pd.Series) -> pd.Series:
        """Convert series to datetime using multiple formats.
        
        Args:
            series: Series to convert
            
        Returns:
            Converted datetime series
        """
        for date_format in self.json_config.date_formats:
            try:
                if date_format == 'ISO8601':
                    return pd.to_datetime(series, errors='coerce')
                else:
                    return pd.to_datetime(series, format=date_format, errors='coerce')
            except Exception:
                continue
                
        # If all formats fail, try pandas default
        return pd.to_datetime(series, errors='coerce')
        
    def _normalize_column_names(self, data: pd.DataFrame) -> pd.DataFrame:
        """Normalize column names.
        
        Args:
            data: DataFrame to normalize
            
        Returns:
            DataFrame with normalized column names
        """
        # Create mapping of old to new column names
        column_mapping = {}
        
        for col in data.columns:
            # Skip metadata columns
            if col.startswith('_'):
                continue
                
            # Normalize column name
            new_col = (col.lower() if isinstance(col, str) else str(col).lower()).replace(' ', '_').replace('-', '_')
            # Remove special characters except underscore
            new_col = ''.join(c for c in new_col if c.isalnum() or c == '_')
            # Ensure it doesn't start with a number
            if new_col and new_col[0].isdigit():
                new_col = f'col_{new_col}'
                
            if new_col and new_col != col:
                column_mapping[col] = new_col
                
        if column_mapping:
            data = data.rename(columns=column_mapping)
            self.logger.info(f"Normalized {len(column_mapping)} column names")
            
        return data
        
    async def _validate_json_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Validate JSON data quality.
        
        Args:
            data: DataFrame to validate
            
        Returns:
            Validation results
        """
        validation_result = {
            'is_valid': True,
            'error_rate': 0.0,
            'quality_score': 1.0,
            'errors': [],
            'warnings': []
        }
        
        if len(data) == 0:
            validation_result['errors'].append("No data to validate")
            validation_result['is_valid'] = False
            return validation_result
            
        # Check required fields
        if self.json_config.required_fields:
            missing_fields = set(self.json_config.required_fields) - set(data.columns)
            if missing_fields:
                validation_result['errors'].append(f"Missing required fields: {missing_fields}")
                validation_result['is_valid'] = False
                
        # Calculate missing data rate
        total_cells = data.size
        missing_cells = data.isnull().sum().sum()
        missing_rate = missing_cells / total_cells if total_cells > 0 else 0
        
        if missing_rate > 0.5:
            validation_result['warnings'].append(f"High missing data rate: {missing_rate:.1%}")
            
        # Check for duplicate rows
        duplicate_rows = data.duplicated().sum()
        if duplicate_rows > 0:
            duplicate_rate = duplicate_rows / len(data)
            if duplicate_rate > 0.1:
                validation_result['warnings'].append(f"High duplicate rate: {duplicate_rate:.1%}")
                
        # Check data type consistency
        for col in data.columns:
            if col.startswith('_'):  # Skip metadata columns
                continue
                
            # Check for mixed types in object columns
            if data[col].dtype == 'object':
                sample_values = data[col].dropna().head(100)
                if len(sample_values) > 0:
                    type_counts = sample_values.apply(type).value_counts()
                    if len(type_counts) > 1:
                        validation_result['warnings'].append(f"Column '{col}' has mixed data types")
                        
        # Calculate overall quality score
        error_rate = len(validation_result['errors']) / max(1, len(data.columns))
        quality_score = max(0, 1 - error_rate - missing_rate * 0.5)
        
        validation_result['error_rate'] = error_rate
        validation_result['quality_score'] = quality_score
        
        return validation_result
        
    async def _store_json_data(self, data: pd.DataFrame, source_path: Union[str, Path], file_info: Dict[str, Any]) -> None:
        """Store JSON data in database.
        
        Args:
            data: DataFrame to store
            source_path: Source file path
            file_info: File information
        """
        if not hasattr(self, 'db_ops') or not self.db_ops:
            raise ValueError("Database operations not configured")
            
        file_name = Path(source_path).stem
        table_name = f'json_import_{file_name.lower().replace(" ", "_")}'
        
        await self.db_ops.store_dataframe(data, table_name, if_exists='replace')
        
        # Create indexes
        await self._create_json_indexes(table_name, data)
        
    async def _create_json_indexes(self, table_name: str, data: pd.DataFrame) -> None:
        """Create indexes on JSON data table.
        
        Args:
            table_name: Name of the table
            data: DataFrame with the data
        """
        if not hasattr(self.db_ops, 'create_index'):
            return
            
        # Index on timestamp columns
        timestamp_cols = [col for col in data.columns if 'timestamp' in col.lower() or 'date' in col.lower()]
        for col in timestamp_cols:
            try:
                await self.db_ops.create_index(table_name, col)
            except Exception as e:
                self.logger.warning(f"Failed to create index on {col}: {e}")
                
        # Index on ID columns
        id_cols = [col for col in data.columns if 'id' in col.lower()]
        for col in id_cols:
            try:
                await self.db_ops.create_index(table_name, col)
            except Exception as e:
                self.logger.warning(f"Failed to create index on {col}: {e}")
                
    def _get_schema_info(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get schema information from DataFrame.
        
        Args:
            data: DataFrame to analyze
            
        Returns:
            Schema information
        """
        schema_info = {
            'total_columns': len(data.columns),
            'data_types': data.dtypes.to_dict(),
            'null_counts': data.isnull().sum().to_dict(),
            'unique_counts': data.nunique().to_dict()
        }
        
        # Add column categories
        schema_info['column_categories'] = {
            'numeric': data.select_dtypes(include=[np.number]).columns.tolist(),
            'datetime': data.select_dtypes(include=['datetime64']).columns.tolist(),
            'string': data.select_dtypes(include=['object']).columns.tolist(),
            'boolean': data.select_dtypes(include=['bool']).columns.tolist()
        }
        
        return schema_info
        
    async def validate_source(self, source: Union[str, Path]) -> bool:
        """Validate JSON data source.
        
        Args:
            source: Data source to validate
            
        Returns:
            True if source is valid
        """
        try:
            source_path = Path(source)
            
            # Check if file exists
            if not source_path.exists():
                self.logger.error(f"Source file does not exist: {source}")
                return False
                
            # Check file format
            if source_path.suffix.lower() not in self.supported_formats:
                self.logger.error(f"Unsupported file format: {source_path.suffix}")
                return False
                
            # Check file size
            file_size_mb = source_path.stat().st_size / 1024 / 1024
            if file_size_mb > 5000:  # 5GB limit
                self.logger.error(f"File too large: {file_size_mb:.1f}MB")
                return False
                
            # Try to parse JSON
            try:
                with open(source_path, 'r', encoding=self.json_config.encoding) as f:
                    if source_path.suffix.lower() in ['.jsonl', '.ndjson']:
                        # Line-delimited JSON - check first line
                        first_line = f.readline().strip()
                        if first_line:
                            json.loads(first_line)
                    else:
                        # Regular JSON - check if it's valid
                        # Read first 1KB to validate structure
                        sample = f.read(1024)
                        if sample.strip():
                            # Try to parse the sample
                            if sample.strip().startswith('{') or sample.strip().startswith('['):
                                # Looks like valid JSON start
                                pass
                            else:
                                self.logger.error("File does not appear to contain valid JSON")
                                return False
                                
            except (json.JSONDecodeError, UnicodeDecodeError) as e:
                self.logger.error(f"Cannot parse JSON file: {e}")
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"Source validation failed: {e}")
            return False
            
    async def get_source_info(self, source: Union[str, Path]) -> Dict[str, Any]:
        """Get information about JSON data source.
        
        Args:
            source: Data source
            
        Returns:
            Source information dictionary
        """
        try:
            source_path = Path(source)
            stat = source_path.stat()
            
            # Analyze file
            file_info = await self._analyze_json_file(source_path)
            
            return {
                'file_path': str(source_path),
                'file_name': source_path.name,
                'file_size_bytes': stat.st_size,
                'file_size_mb': stat.st_size / 1024 / 1024,
                'file_format': source_path.suffix.lower(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'is_supported': source_path.suffix.lower() in self.supported_formats,
                'encoding': file_info['encoding'],
                'structure_type': file_info.get('structure_type', 'unknown'),
                'is_line_delimited': file_info['is_line_delimited'],
                'estimated_records': file_info.get('estimated_records'),
                'sample_records': file_info.get('sample_records', [])[:3],
                'requires_streaming': file_info['file_size_mb'] > 50
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get source info: {e}")
            return {'error': str(e)}
            
    def suggest_configuration(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest optimal configuration based on file analysis.
        
        Args:
            file_info: File analysis results
            
        Returns:
            Configuration suggestions
        """
        suggestions = {
            'streaming': file_info['file_size_mb'] > 50,
            'chunk_size': min(10000, max(1000, int(file_info.get('estimated_records', 10000) / 10))),
            'flatten_nested': True,
            'array_handling': 'expand' if file_info.get('estimated_records', 0) < 100000 else 'join',
            'auto_detect_types': True,
            'remove_null_columns': True,
            'normalize_column_names': True
        }
        
        # Analyze sample records for better suggestions
        sample_records = file_info.get('sample_records', [])
        if sample_records:
            # Check for nested structures
            has_nested = any(
                isinstance(v, dict) for record in sample_records for v in record.values()
            )
            suggestions['flatten_nested'] = has_nested
            
            # Check for arrays
            has_arrays = any(
                isinstance(v, list) for record in sample_records for v in record.values()
            )
            if has_arrays:
                # Suggest join for large datasets, expand for small ones
                suggestions['array_handling'] = 'expand' if file_info.get('estimated_records', 0) < 10000 else 'join'
                
        return suggestions