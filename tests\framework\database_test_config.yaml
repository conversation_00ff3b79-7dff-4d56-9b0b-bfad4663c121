# Connect电信数据分析平台 - 数据库框架测试配置
# 基于docs/database/database-framework.md需求的测试配置

# 数据库连接配置
database:
  # 测试数据库连接信息
  test_db:
    host: localhost
    port: 5432
    name: test_connect
    user: test_user
    password: test_password
    ssl_mode: prefer
    connection_timeout: 30
    command_timeout: 60
  
  # 连接池配置
  pool:
    min_size: 5
    max_size: 20
    timeout: 60
    retry_attempts: 3
    retry_delay: 1.0

# 测试执行配置
testing:
  # 基本配置
  test_data_size: 1000
  performance_threshold_ms: 1000
  memory_limit_mb: 512
  coverage_threshold: 80.0
  parallel_workers: 4
  timeout_seconds: 300
  retry_attempts: 3
  cleanup_on_exit: true
  
  # 测试环境
  environment:
    temp_dir: "./test_temp"
    log_level: "INFO"
    log_file: "test_execution.log"
    report_dir: "./test_reports"
    artifact_dir: "./test_artifacts"

# P0优先级测试配置（核心功能 - MVP）
p0_tests:
  description: "核心功能测试 - 最小可行产品"
  coverage_threshold: 90.0
  success_rate_threshold: 100.0
  max_execution_time_ms: 500
  blocking: true
  
  suites:
    core_config:
      description: "核心配置管理测试"
      test_paths: ["tests/unit/test_config.py"]
      markers: ["p0", "config", "core"]
      timeout: 60
      parallel: false
      performance_thresholds:
        max_execution_time_ms: 100
    
    core_connection:
      description: "核心连接管理测试"
      test_paths: ["tests/unit/test_connection.py"]
      markers: ["p0", "connection", "core"]
      timeout: 120
      parallel: false
      performance_thresholds:
        max_execution_time_ms: 200
    
    core_schema:
      description: "核心模式管理测试"
      test_paths: ["tests/unit/test_schema.py"]
      markers: ["p0", "schema", "core"]
      timeout: 120
      parallel: false
      coverage_threshold: 85.0
      performance_thresholds:
        max_execution_time_ms: 300
    
    core_operations:
      description: "核心CRUD操作测试"
      test_paths: ["tests/unit/test_operations.py"]
      markers: ["p0", "crud", "core"]
      timeout: 180
      parallel: true
      coverage_threshold: 85.0
      performance_thresholds:
        max_execution_time_ms: 500

# P1优先级测试配置（重要功能 - 生产就绪）
p1_tests:
  description: "重要功能测试 - 生产就绪"
  coverage_threshold: 80.0
  success_rate_threshold: 95.0
  max_execution_time_ms: 1000
  blocking: true
  
  suites:
    integration_database:
      description: "数据库集成测试"
      test_paths: ["tests/integration/test_database_integration.py"]
      markers: ["p1", "integration", "database"]
      timeout: 300
      parallel: true
      dependencies: ["core_config", "core_connection"]
      performance_thresholds:
        max_execution_time_ms: 1000
    
    data_import_export:
      description: "数据导入导出测试"
      test_paths: ["tests/unit/test_importers.py", "tests/unit/test_exporters.py"]
      markers: ["p1", "import", "export"]
      timeout: 240
      parallel: true
      performance_thresholds:
        max_execution_time_ms: 2000
    
    geospatial:
      description: "地理空间处理测试"
      test_paths: ["tests/unit/test_geospatial.py"]
      markers: ["p1", "geospatial", "gis"]
      timeout: 180
      parallel: true
      coverage_threshold: 75.0
      performance_thresholds:
        max_execution_time_ms: 1500
    
    monitoring:
      description: "监控和日志测试"
      test_paths: ["tests/unit/test_monitoring.py"]
      markers: ["p1", "monitoring", "logging"]
      timeout: 120
      parallel: true
      coverage_threshold: 75.0
      performance_thresholds:
        max_execution_time_ms: 500

# P2优先级测试配置（有价值功能 - 功能增强）
p2_tests:
  description: "有价值功能测试 - 功能增强"
  coverage_threshold: 70.0
  success_rate_threshold: 90.0
  max_execution_time_ms: 3000
  blocking: false
  
  suites:
    performance:
      description: "性能测试"
      test_paths: ["tests/performance/test_database_performance.py"]
      markers: ["p2", "performance", "load"]
      timeout: 600
      parallel: false
      dependencies: ["integration_database"]
      coverage_threshold: 60.0
      performance_thresholds:
        max_execution_time_ms: 5000
        max_memory_mb: 256
        min_throughput_ops_per_sec: 100
    
    batch_processing:
      description: "批处理测试"
      test_paths: ["tests/unit/test_batch_processor.py"]
      markers: ["p2", "batch", "etl"]
      timeout: 300
      parallel: true
      coverage_threshold: 70.0
      performance_thresholds:
        max_execution_time_ms: 3000
    
    etl_pipeline:
      description: "ETL管道测试"
      test_paths: ["tests/unit/test_etl.py"]
      markers: ["p2", "etl", "pipeline"]
      timeout: 240
      parallel: true
      coverage_threshold: 70.0
      performance_thresholds:
        max_execution_time_ms: 2000

# P3优先级测试配置（可选功能 - 高级功能）
p3_tests:
  description: "可选功能测试 - 高级功能"
  coverage_threshold: 60.0
  success_rate_threshold: 85.0
  max_execution_time_ms: 5000
  blocking: false
  
  suites:
    security:
      description: "安全测试"
      test_paths: ["tests/security/test_database_security.py"]
      markers: ["p3", "security", "auth"]
      timeout: 300
      parallel: false
      coverage_threshold: 60.0
      performance_thresholds:
        max_execution_time_ms: 2000
    
    e2e_scenarios:
      description: "端到端场景测试"
      test_paths: ["tests/e2e/test_complete_workflows.py"]
      markers: ["p3", "e2e", "workflow"]
      timeout: 900
      parallel: false
      dependencies: ["integration_database", "performance"]
      coverage_threshold: 50.0
      performance_thresholds:
        max_execution_time_ms: 10000
    
    schema_validation:
      description: "模式验证测试"
      test_paths: ["tests/unit/test_schema_validators.py"]
      markers: ["p3", "schema", "validation"]
      timeout: 180
      parallel: true
      coverage_threshold: 65.0
      performance_thresholds:
        max_execution_time_ms: 1000

# 质量门控配置
quality_gates:
  p0_core_quality:
    description: "P0核心功能质量门控"
    coverage_threshold: 90.0
    success_rate_threshold: 100.0
    performance_thresholds:
      max_execution_time_ms: 500
      max_memory_mb: 128
    security_checks:
      - sql_injection
      - access_control
    blocking: true
  
  p1_production_ready:
    description: "P1生产就绪质量门控"
    coverage_threshold: 80.0
    success_rate_threshold: 95.0
    performance_thresholds:
      max_execution_time_ms: 1000
      max_memory_mb: 256
      min_throughput_ops_per_sec: 50
    security_checks:
      - authentication
      - authorization
    blocking: true
  
  p2_feature_enhancement:
    description: "P2功能增强质量门控"
    coverage_threshold: 70.0
    success_rate_threshold: 90.0
    performance_thresholds:
      max_execution_time_ms: 3000
      max_memory_mb: 512
    security_checks:
      - data_encryption
    blocking: false
  
  p3_advanced_features:
    description: "P3高级功能质量门控"
    coverage_threshold: 60.0
    success_rate_threshold: 85.0
    performance_thresholds:
      max_execution_time_ms: 5000
      max_memory_mb: 1024
    security_checks:
      - audit_logging
    blocking: false

# 测试数据配置
test_data:
  # EP数据配置
  ep_data:
    cell_count: 1000
    technologies: ["LTE", "5G", "UMTS"]
    vendors: ["Ericsson", "Nokia", "Huawei"]
    frequency_bands: [800, 900, 1800, 2100, 2600, 3500]
    power_range: [20, 50]
    
  # CDR数据配置
  cdr_data:
    record_count: 10000
    service_types: ["voice", "data", "sms"]
    duration_range: [10, 3600]
    operators: ["telefonica", "vodafone", "telekom"]
    
  # 地理空间数据配置
  geospatial_data:
    point_count: 500
    polygon_count: 100
    coordinate_bounds:
      min_lat: 47.0
      max_lat: 55.0
      min_lon: 5.0
      max_lon: 15.0

# 性能监控配置
performance_monitoring:
  enabled: true
  metrics:
    - cpu_usage
    - memory_usage
    - disk_io
    - network_io
    - database_connections
    - query_execution_time
  
  thresholds:
    cpu_usage_percent: 80
    memory_usage_percent: 85
    disk_io_mb_per_sec: 100
    max_query_time_ms: 1000
    max_connection_count: 50
  
  sampling_interval_ms: 1000
  retention_hours: 24

# 报告配置
reporting:
  formats: ["json", "html", "junit"]
  include_performance_metrics: true
  include_coverage_details: true
  include_error_details: true
  include_screenshots: false
  
  html_report:
    template: "default"
    include_charts: true
    include_trends: true
  
  junit_report:
    include_properties: true
    include_system_out: true
    include_system_err: true

# CI/CD集成配置
ci_cd:
  # GitHub Actions配置
  github_actions:
    trigger_on: ["push", "pull_request"]
    python_versions: ["3.9", "3.10", "3.11"]
    os_matrix: ["ubuntu-latest", "windows-latest"]
    
  # 部署门控
  deployment_gates:
    require_p0_pass: true
    require_p1_pass: true
    allow_p2_fail: true
    allow_p3_fail: true
    min_coverage_percent: 80
    max_critical_issues: 0
    max_high_issues: 5
  
  # 通知配置
  notifications:
    slack:
      enabled: false
      webhook_url: ""
      channels: ["#quality-engineering", "#database-team"]
    
    email:
      enabled: false
      smtp_server: ""
      recipients: ["<EMAIL>", "<EMAIL>"]

# 安全测试配置
security_testing:
  enabled: true
  
  # SQL注入测试
  sql_injection:
    enabled: true
    test_payloads:
      - "'; DROP TABLE users; --"
      - "' OR '1'='1"
      - "' UNION SELECT * FROM information_schema.tables --"
    
  # 访问控制测试
  access_control:
    enabled: true
    test_scenarios:
      - unauthorized_access
      - privilege_escalation
      - data_exposure
  
  # 数据加密测试
  data_encryption:
    enabled: true
    algorithms: ["AES-256", "RSA-2048"]
    
  # 审计日志测试
  audit_logging:
    enabled: true
    required_events:
      - user_login
      - data_access
      - schema_changes
      - permission_changes

# 环境特定配置
environments:
  development:
    database:
      host: "localhost"
      port: 5432
      name: "connect_dev"
    testing:
      parallel_workers: 2
      timeout_seconds: 180
  
  staging:
    database:
      host: "staging-db.connect.com"
      port: 5432
      name: "connect_staging"
    testing:
      parallel_workers: 4
      timeout_seconds: 300
  
  production:
    database:
      host: "prod-db.connect.com"
      port: 5432
      name: "connect_prod"
    testing:
      parallel_workers: 8
      timeout_seconds: 600
      cleanup_on_exit: false