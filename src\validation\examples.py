"""Examples and usage patterns for the unified validation framework.

This module provides practical examples of how to use the validation
framework in different scenarios within the Connect system.
"""

import pandas as pd
from pathlib import Path
from typing import Dict, Any

from .factory import (
    ValidationFactory,
    validate_cdr_data,
    validate_kpi_data,
    validate_cfg_data,
    validate_cdr_file,
    validate_kpi_file,
    validate_cfg_file,
)
from .core import ValidationFramework, ValidationSeverity
from .validators import DataStructureValidator, DataValueValidator
from .rules import CDRValidationRules, KPIValidationRules


def example_basic_cdr_validation():
    """Example: Basic CDR data validation."""
    print("=== Basic CDR Data Validation ===")
    
    # Sample CDR data
    cdr_data = pd.DataFrame({
        'CALL_ID': ['CALL_001', 'CALL_002', 'CALL_003'],
        'CALLER_NUMBER': ['+1234567890', '+0987654321', '+1122334455'],
        'CALLED_NUMBER': ['+0987654321', '+1234567890', '+5566778899'],
        'CALL_START_TIME': ['2024-01-01 10:00:00', '2024-01-01 10:05:00', '2024-01-01 10:10:00'],
        'CALL_END_TIME': ['2024-01-01 10:03:00', '2024-01-01 10:07:00', '2024-01-01 10:12:00'],
        'CALL_DURATION': [180, 120, 120],
        'CALL_STATUS': ['COMPLETED', 'COMPLETED', 'COMPLETED'],
        'CELL_ID': ['CELL_001', 'CELL_002', 'CELL_001'],
        'LAC': ['LAC_001', 'LAC_001', 'LAC_001'],
        'LONGITUDE': [116.3974, 116.3975, 116.3976],
        'LATITUDE': [39.9093, 39.9094, 39.9095]
    })
    
    # Validate using convenience function
    result = validate_cdr_data(cdr_data)
    
    print(f"Validation Result: {'PASSED' if result.success else 'FAILED'}")
    print(f"Rules passed: {result.passed_rules}/{result.total_rules}")
    print(f"Issues found: {len(result.issues)}")
    
    if result.issues:
        print("\nIssues:")
        for issue in result.issues:
            print(f"  - {issue.severity.value}: {issue.message}")
    
    return result


def example_cdr_file_validation():
    """Example: CDR file validation."""
    print("\n=== CDR File Validation ===")
    
    # Create sample CDR file
    sample_file = Path("sample_cdr.csv")
    cdr_data = pd.DataFrame({
        'CALL_ID': ['CALL_001', 'CALL_002'],
        'CALLER_NUMBER': ['+1234567890', '+0987654321'],
        'CALLED_NUMBER': ['+0987654321', '+1234567890'],
        'CALL_START_TIME': ['2024-01-01 10:00:00', '2024-01-01 10:05:00'],
        'CALL_END_TIME': ['2024-01-01 10:03:00', '2024-01-01 10:07:00'],
        'CALL_DURATION': [180, 120],
        'CALL_STATUS': ['COMPLETED', 'COMPLETED'],
        'CELL_ID': ['CELL_001', 'CELL_002'],
        'LAC': ['LAC_001', 'LAC_001'],
        'LONGITUDE': [116.3974, 116.3975],
        'LATITUDE': [39.9093, 39.9094]
    })
    
    try:
        # Save sample file
        cdr_data.to_csv(sample_file, index=False)
        
        # Validate file
        result = validate_cdr_file(sample_file)
        
        print(f"File validation result: {'PASSED' if result.success else 'FAILED'}")
        print(f"Rules passed: {result.passed_rules}/{result.total_rules}")
        
        if result.issues:
            print("\nFile validation issues:")
            for issue in result.issues:
                print(f"  - {issue.severity.value}: {issue.message}")
        
        return result
        
    finally:
        # Clean up
        if sample_file.exists():
            sample_file.unlink()


def example_kpi_validation_with_errors():
    """Example: KPI validation with intentional errors."""
    print("\n=== KPI Validation with Errors ===")
    
    # Sample KPI data with errors
    kpi_data = pd.DataFrame({
        'KPI_ID': ['KPI_001', 'KPI_002', 'KPI_003'],
        'KPI_NAME': ['Throughput', 'Latency', 'Error Rate'],
        'KPI_VALUE': [100.5, 'invalid_value', -50],  # Error: invalid value
        'MEASUREMENT_TIME': ['2024-01-01 10:00:00', 'invalid_time', '2024-01-01 10:10:00'],  # Error: invalid time
        'CELL_ID': ['CELL_001', 'CELL_002', ''],  # Error: empty cell ID
        'LONGITUDE': [116.3974, 200, 116.3976],  # Error: invalid longitude
        'LATITUDE': [39.9093, 39.9094, 100]  # Error: invalid latitude
    })
    
    # Validate data
    result = validate_kpi_data(kpi_data)
    
    print(f"Validation Result: {'PASSED' if result.success else 'FAILED'}")
    print(f"Rules passed: {result.passed_rules}/{result.total_rules}")
    print(f"Issues found: {len(result.issues)}")
    
    if result.issues:
        print("\nDetailed issues:")
        for issue in result.issues:
            print(f"  - {issue.severity.value} [{issue.validation_type.value}]: {issue.message}")
            if issue.column:
                print(f"    Column: {issue.column}")
            if issue.details:
                print(f"    Details: {issue.details}")
    
    return result


def example_custom_validation_framework():
    """Example: Creating custom validation framework."""
    print("\n=== Custom Validation Framework ===")
    
    # Create validation factory
    factory = ValidationFactory()
    
    # Create custom validators
    custom_structure_validator = DataStructureValidator(
        name="custom_structure",
        required_columns=["ID", "NAME", "VALUE"],
        optional_columns=["DESCRIPTION"],
        min_rows=1,
        severity=ValidationSeverity.ERROR
    )
    
    custom_value_validator = DataValueValidator(
        name="custom_values",
        column_rules={
            "ID": {
                "dtype": "object",
                "pattern": r"^[A-Z]{3}_[0-9]{3}$"
            },
            "VALUE": {
                "dtype": "float64",
                "min_value": 0,
                "max_value": 100
            }
        },
        allow_nulls=False,
        severity=ValidationSeverity.ERROR
    )
    
    # Create custom framework
    framework = factory.create_framework(
        name="custom_data_validation",
        data_type="database",  # Use database as base
        custom_rules=[custom_structure_validator, custom_value_validator]
    )
    
    # Sample data
    custom_data = pd.DataFrame({
        'ID': ['ABC_001', 'DEF_002', 'GHI_003'],
        'NAME': ['Item 1', 'Item 2', 'Item 3'],
        'VALUE': [25.5, 75.0, 150.0],  # Error: last value > 100
        'DESCRIPTION': ['Desc 1', None, 'Desc 3']
    })
    
    # Validate using custom framework
    result = framework.validate(custom_data)
    
    print(f"Custom validation result: {'PASSED' if result.success else 'FAILED'}")
    print(f"Rules passed: {result.passed_rules}/{result.total_rules}")
    
    if result.issues:
        print("\nCustom validation issues:")
        for issue in result.issues:
            print(f"  - {issue.severity.value}: {issue.message}")
    
    return result


def example_validation_with_context():
    """Example: Validation with additional context."""
    print("\n=== Validation with Context ===")
    
    # Sample data
    data = pd.DataFrame({
        'CONFIG_ID': ['CFG_001', 'CFG_002'],
        'CONFIG_NAME': ['Setting 1', 'Setting 2'],
        'CONFIG_VALUE': ['value1', 'value2'],
        'CONFIG_TYPE': ['string', 'string'],
        'CREATED_TIME': ['2024-01-01 10:00:00', '2024-01-01 10:05:00']
    })
    
    # Additional context
    context = {
        'source_system': 'test_system',
        'import_batch': 'batch_001',
        'user_id': 'test_user'
    }
    
    # Validate with context
    result = validate_cfg_data(data, context=context)
    
    print(f"Validation with context: {'PASSED' if result.success else 'FAILED'}")
    print(f"Context provided: {context}")
    
    return result


def example_parallel_validation():
    """Example: Parallel validation for large datasets."""
    print("\n=== Parallel Validation ===")
    
    # Create larger dataset
    import numpy as np
    
    size = 10000
    large_cdr_data = pd.DataFrame({
        'CALL_ID': [f'CALL_{i:06d}' for i in range(size)],
        'CALLER_NUMBER': [f'+123456{i:04d}' for i in range(size)],
        'CALLED_NUMBER': [f'+987654{i:04d}' for i in range(size)],
        'CALL_START_TIME': pd.date_range('2024-01-01', periods=size, freq='1min'),
        'CALL_END_TIME': pd.date_range('2024-01-01 00:03:00', periods=size, freq='1min'),
        'CALL_DURATION': np.random.randint(60, 300, size),
        'CALL_STATUS': ['COMPLETED'] * size,
        'CELL_ID': [f'CELL_{i%100:03d}' for i in range(size)],
        'LAC': [f'LAC_{i%10:03d}' for i in range(size)],
        'LONGITUDE': np.random.uniform(116.0, 117.0, size),
        'LATITUDE': np.random.uniform(39.0, 40.0, size)
    })
    
    # Create factory with parallel processing
    factory = ValidationFactory()
    framework = factory.create_framework(
        name="parallel_cdr_validation",
        data_type="cdr",
        enable_parallel=True,
        max_workers=4
    )
    
    print(f"Validating {len(large_cdr_data)} records with parallel processing...")
    
    import time
    start_time = time.time()
    
    result = framework.validate(large_cdr_data)
    
    end_time = time.time()
    
    print(f"Parallel validation completed in {end_time - start_time:.2f} seconds")
    print(f"Result: {'PASSED' if result.success else 'FAILED'}")
    print(f"Rules passed: {result.passed_rules}/{result.total_rules}")
    
    return result


def example_validation_report():
    """Example: Generate validation report."""
    print("\n=== Validation Report ===")
    
    # Get validation factory info
    factory = ValidationFactory()
    
    # Create some frameworks
    cdr_framework = factory.create_framework("report_cdr", "cdr")
    kpi_framework = factory.create_framework("report_kpi", "kpi")
    
    print("Available validation frameworks:")
    for name in factory.list_frameworks():
        info = factory.get_framework_info(name)
        if info:
            print(f"\n  Framework: {info['name']}")
            print(f"    Rules: {info['rule_count']}")
            print(f"    Parallel: {info['parallel_enabled']}")
            print(f"    Rule details:")
            for rule in info['rules']:
                print(f"      - {rule['name']} ({rule['type']}, {rule['severity']})")


def run_all_examples():
    """Run all validation examples."""
    print("Running Unified Validation Framework Examples")
    print("=" * 50)
    
    try:
        # Run examples
        example_basic_cdr_validation()
        example_cdr_file_validation()
        example_kpi_validation_with_errors()
        example_custom_validation_framework()
        example_validation_with_context()
        example_parallel_validation()
        example_validation_report()
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"\nError running examples: {e}")
        raise


if __name__ == "__main__":
    run_all_examples()