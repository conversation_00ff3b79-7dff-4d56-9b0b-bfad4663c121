#!/usr/bin/env python3
"""
Connect项目重构进度验证脚本

此脚本验证重构路线图中各项任务的完成情况，
确保重构按计划进行且不破坏现有功能。
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Tuple
import importlib.util

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class RefactoringValidator:
    """重构验证器"""
    
    def __init__(self):
        self.project_root = project_root
        self.results = {}
        
    def validate_p0_1_cleanup(self) -> Tuple[bool, str]:
        """验证P0-1: 临时文件清理"""
        print("🧹 验证P0-1: 临时文件清理...")
        
        # 检查应该被删除的临时文件
        temp_files = [
            "debug_imports.py",
            "debug_test_import.py", 
            "pytest_debug.py",
            "test_minimal.py",
            "test_pytest_basic.py",
            "test_async_simple.py",
            "test_etl_minimal.py",
            "test_incremental.py",
            "test_simple_etl.py",
            "simple_test.py",
            "simple_import_test.py",
            "analyze_dependencies.py",
            "check_pytest_config.py",
            "fix_project_issues.py",
            "test.csv"
        ]
        
        remaining_files = []
        for file_path in temp_files:
            if (self.project_root / file_path).exists():
                remaining_files.append(file_path)
        
        if remaining_files:
            return False, f"仍存在临时文件: {remaining_files}"
        
        return True, "✅ 所有临时文件已清理"
    
    def validate_p0_2_config_unification(self) -> Tuple[bool, str]:
        """验证P0-2: 配置管理统一"""
        print("⚙️ 验证P0-2: 配置管理统一...")
        
        # 检查应该被删除的重复配置文件
        duplicate_configs = [
            "config/schema.py",
            "config_manager.py"
        ]
        
        remaining_configs = []
        for config_path in duplicate_configs:
            if (self.project_root / config_path).exists():
                remaining_configs.append(config_path)
        
        if remaining_configs:
            return False, f"仍存在重复配置文件: {remaining_configs}"
        
        # 检查新Pydantic配置系统是否可用
        try:
            from src.config import get_config
            config = get_config()
            
            # 验证电信配置是否存在
            if not hasattr(config, 'telecom'):
                return False, "电信配置缺失"
            
            if not hasattr(config.telecom, 'cdr'):
                return False, "CDR配置缺失"
                
            return True, "✅ 配置管理已统一"
            
        except Exception as e:
            return False, f"配置系统加载失败: {e}"
    
    def validate_p0_3_importer_patterns(self) -> Tuple[bool, str]:
        """验证P0-3: 导入器设计模式"""
        print("🔧 验证P0-3: 导入器设计模式...")
        
        try:
            # 检查导入器继承关系
            from src.importers.base import AbstractImporter
            from src.importers.ep_importer import EPImporter
            from src.importers.cdr_importer import CDRImporter
            from src.importers.nlg_importer import NLGImporter

            # 验证继承关系
            if not issubclass(EPImporter, AbstractImporter):
                return False, "EPImporter未继承AbstractImporter"

            if not issubclass(CDRImporter, AbstractImporter):
                return False, "CDRImporter未继承AbstractImporter"

            if not issubclass(NLGImporter, AbstractImporter):
                return False, "NLGImporter未继承AbstractImporter"
            
            # 验证配置加载方式
            ep_importer = EPImporter(source_path="test.xlsx")
            if not hasattr(ep_importer, 'data_type'):
                return False, "EPImporter缺少data_type属性"
            
            if ep_importer.data_type != 'ep':
                return False, "EPImporter data_type不正确"
            
            return True, "✅ 导入器设计模式已统一"
            
        except Exception as e:
            return False, f"导入器验证失败: {e}"
    
    def validate_core_functionality(self) -> Tuple[bool, str]:
        """验证核心功能完整性"""
        print("🔍 验证核心功能完整性...")
        
        try:
            # 测试配置系统
            from src.config import get_config
            config = get_config()
            
            # 测试数据库配置
            db_url = config.get_database_url()
            if not db_url.startswith('postgresql://'):
                return False, "数据库URL格式不正确"
            
            # 测试导入器初始化
            from src.importers.ep_importer import EPImporter
            ep_importer = EPImporter(source_path="test.xlsx")
            
            if not hasattr(ep_importer, 'batch_size'):
                return False, "导入器缺少batch_size配置"
            
            return True, "✅ 核心功能完整"
            
        except Exception as e:
            return False, f"核心功能验证失败: {e}"
    
    def validate_backward_compatibility(self) -> Tuple[bool, str]:
        """验证向后兼容性"""
        print("🔄 验证向后兼容性...")
        
        try:
            # 测试旧的导入方式是否仍然工作
            from src.importers.ep_importer import EPImporter
            
            # 测试旧的配置方式
            ep_importer = EPImporter(
                source_path="test.xlsx",
                config={"batch_size": 1000}
            )
            
            if ep_importer.batch_size != 500:  # 应该使用新配置系统的值
                return False, "配置优先级不正确"
            
            return True, "✅ 向后兼容性保持"
            
        except Exception as e:
            return False, f"向后兼容性验证失败: {e}"
    
    def run_validation(self) -> Dict[str, Tuple[bool, str]]:
        """运行完整验证"""
        print("🚀 开始Connect项目重构验证...\n")
        
        validations = [
            ("P0-1: 临时文件清理", self.validate_p0_1_cleanup),
            ("P0-2: 配置管理统一", self.validate_p0_2_config_unification),
            ("P0-3: 导入器设计模式", self.validate_p0_3_importer_patterns),
            ("核心功能完整性", self.validate_core_functionality),
            ("向后兼容性", self.validate_backward_compatibility),
        ]
        
        results = {}
        passed = 0
        total = len(validations)
        
        for name, validator in validations:
            try:
                success, message = validator()
                results[name] = (success, message)
                
                if success:
                    passed += 1
                    print(f"   {message}")
                else:
                    print(f"   ❌ {message}")
                    
            except Exception as e:
                results[name] = (False, f"验证异常: {e}")
                print(f"   ❌ 验证异常: {e}")
            
            print()
        
        # 输出总结
        print("=" * 60)
        print(f"📊 重构验证总结")
        print("=" * 60)
        print(f"总验证项: {total}")
        print(f"✅ 通过: {passed}")
        print(f"❌ 失败: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有验证项通过！重构进展良好。")
        else:
            print(f"\n⚠️ 有 {total - passed} 项验证失败，需要修复。")
        
        return results

def main():
    """主函数"""
    validator = RefactoringValidator()
    results = validator.run_validation()
    
    # 返回适当的退出码
    failed_count = sum(1 for success, _ in results.values() if not success)
    sys.exit(failed_count)

if __name__ == "__main__":
    main()
