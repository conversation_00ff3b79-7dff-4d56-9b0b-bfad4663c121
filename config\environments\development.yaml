# 开发环境配置
# 覆盖基础配置中的特定设置

database:
  host: ${DATABASE_HOST:localhost}
  port: ${DATABASE_PORT:5432}
  name: ${DATABASE_NAME:connect}
  user: ${DATABASE_USER:to2}
  password: ${DATABASE_PASSWORD:TO2}
  ssl_mode: prefer
  pool:
    min_size: 2
    max_size: 10

# 电信域开发配置
telecom:
  cdr:
    batch_size: 1000  # 开发环境使用较小批次
  ep:
    batch_size: 2000  # 增加批次大小以提高性能
    # EP性能优化配置 - 解决"Disabled complex analysis"问题
    large_dataset_threshold: 50000  # 提高阈值，允许更大数据集进行复杂分析
    enable_performance_optimizations: true
    skip_analysis_for_large_datasets: false  # 强制启用复杂分析
    max_neighbor_search_records: 100000  # 提高邻区搜索记录限制
    use_optimized_algorithms: true
    
    # 确保所有分析功能启用
    enable_neighbor_detection: true
    enable_interference_analysis: true
    enable_topology_analysis: true
    enable_spatial_indexing: true
    enable_coverage_calculation: true
    
    # 内存管理优化
    memory_threshold_gb: 1.0  # 降低内存阈值要求
    max_memory_usage_mb: 4096  # 增加内存使用限制
    
  performance:
    parallel_workers: 4  # 增加并行工作线程
    max_memory_usage_mb: 4096  # 增加系统内存限制
    processing_timeout_seconds: 7200  # 增加处理超时时间

logging:
  level: DEBUG
  handlers:
    console:
      level: DEBUG
    file:
      level: DEBUG

debug: true
testing: false
