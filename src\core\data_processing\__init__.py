# -*- coding: utf-8 -*-
"""
Unified Data Processing Layer

This module provides a centralized, high-performance data processing framework
for the Connect telecommunications system. It consolidates data processing logic
from various importers into reusable, optimized components.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

__version__ = "1.0.0"
__description__ = "Unified Data Processing Layer for Connect System"

# Core processors
from .csv_processor import CSVProcessor
from .excel_processor import ExcelProcessor
from .data_cleaner import DataCleaner
from .transformer import DataTransformer, TransformationType, TransformationRule
from .batch_processor import (
    BatchProcessor,
    BatchStrategy,
    BatchMode,
    BatchConfig,
    BatchInfo,
    BatchResult,
    BatchProcessingResult
)

# Adapters
from .adapters import (
    BaseAdapter,
    PandasAdapter,
    PolarsAdapter,
    AdapterFactory,
    create_adapter,
    get_optimal_engine,
    get_engine_recommendations
)

# Common types and utilities
from .types import (
    ProcessingConfig,
    ProcessingResult,
    ProcessingMetrics,
    ChunkInfo,
    FileInfo,
    ProcessingEngine,
    ProcessingMode,
    DataFormat,
    ProcessingStatus,
    QualityLevel
)

# Utilities
from ..utils import (
    MemoryMonitor,
    PerformanceMonitor,
    DataValidator,
    TelecomValidator,
    TelecomLogger,
    get_logger
)

__all__ = [
    # Core processors
    "CSVProcessor",
    "ExcelProcessor",
    "DataCleaner",
    "DataTransformer",
    "BatchProcessor",
    
    # Adapters
    "BaseAdapter",
    "PandasAdapter",
    "PolarsAdapter",
    "AdapterFactory",
    "create_adapter",
    "get_optimal_engine",
    "get_engine_recommendations",
    
    # Types and enums
    "TransformationType",
    "BatchStrategy",
    "BatchMode",
    
    # Configuration classes
    "ProcessingConfig",
    "BatchConfig",
    
    # Data classes
    "TransformationRule",
    "BatchInfo",
    "BatchResult",
    "BatchProcessingResult",
    "ProcessingResult",
    "ProcessingMetrics",
    "ChunkInfo",
    "FileInfo",
    "ProcessingEngine",
    "ProcessingMode",
    "DataFormat",
    "ProcessingStatus",
    "QualityLevel",
    
    # Utilities
    "MemoryMonitor",
    "PerformanceMonitor",
    "DataValidator",
    "TelecomValidator",
    "TelecomLogger",
    "get_logger"
]

# Performance targets and constants
PERFORMANCE_TARGETS = {
    "cdr_records_per_second": 5000,
    "memory_limit_gb": 2,
    "max_file_size_gb": 10,
    "pandas_threshold_records": 1_000_000,
    "polars_threshold_records": 1_000_000
}

# Supported formats
SUPPORTED_FORMATS = {
    "csv": ["csv", "tsv", "txt"],
    "excel": ["xlsx", "xls", "xlsb"],
    "json": ["json", "jsonl", "ndjson"]
}

# Default configurations
DEFAULT_CONFIGS = {
    "csv": {
        "chunk_size": 50000,
        "engine": "auto",
        "memory_limit_mb": 1024,
        "enable_async": True
    },
    "excel": {
        "chunk_size": 10000,
        "engine": "auto", 
        "memory_limit_mb": 1024,
        "enable_async": True
    },
    "batch": {
        "batch_size": 10000,
        "max_workers": 4,
        "memory_limit_mb": 1024,
        "enable_parallel": True
    }
}