"""Integration tests for ReadWriteSplitter with real database connections.

These tests require actual database instances to be running and configured.
They test the real-world behavior of read-write splitting functionality.
"""

import asyncio
import os
from typing import List

import pytest

from src.config import get_config as load_config
from src.config.models import DatabaseConfig
from src.database.connection import LoadBalancingStrategy, ReadWriteSplitter
from src.database.exceptions import (
    NoAvailableReplicasError,
    PrimaryDatabaseUnavailableError,
)

# Skip integration tests if not in integration test environment
pytestmark = pytest.mark.skipif(
    not os.getenv("RUN_INTEGRATION_TESTS"),
    reason="Integration tests require RUN_INTEGRATION_TESTS environment variable",
)


class TestReadWriteSplitterIntegration:
    """Integration test cases for ReadWriteSplitter."""

    @pytest.fixture(scope="class")
    def database_configs(self):
        """Database configuration fixture for integration tests."""
        # These should be configured based on your test environment
        primary_config = DatabaseConfig(
            host=os.getenv("TEST_PRIMARY_HOST", "localhost"),
            port=int(os.getenv("TEST_PRIMARY_PORT", "5432")),
            name=os.getenv("TEST_DB_NAME", "test_connect_db"),
            user=os.getenv("TEST_DB_USER", "test_user"),
            password=os.getenv("TEST_DB_PASSWORD", "test_password"),
        )

        replica_configs = [
            DatabaseConfig(
                host=os.getenv("TEST_REPLICA1_HOST", "localhost"),
                port=int(os.getenv("TEST_REPLICA1_PORT", "5433")),
                name=os.getenv("TEST_DB_NAME", "test_connect_db"),
                user=os.getenv("TEST_REPLICA_USER", "test_readonly"),
                password=os.getenv("TEST_REPLICA_PASSWORD", "readonly_password"),
            ),
            DatabaseConfig(
                host=os.getenv("TEST_REPLICA2_HOST", "localhost"),
                port=int(os.getenv("TEST_REPLICA2_PORT", "5434")),
                name=os.getenv("TEST_DB_NAME", "test_connect_db"),
                user=os.getenv("TEST_REPLICA_USER", "test_readonly"),
                password=os.getenv("TEST_REPLICA_PASSWORD", "readonly_password"),
            ),
        ]

        return primary_config, replica_configs

    @pytest.fixture
    async def splitter(self, database_configs):
        """ReadWriteSplitter fixture with real database connections."""
        primary_config, replica_configs = database_configs

        splitter = ReadWriteSplitter(
            primary_config=primary_config,
            replica_configs=replica_configs,
            load_balancing_strategy=LoadBalancingStrategy.ROUND_ROBIN,
            fallback_to_primary=True,
            health_check_interval=10,
        )

        await splitter.initialize()
        yield splitter
        await splitter.close()

    @pytest.mark.asyncio
    async def test_real_database_initialization(self, splitter):
        """Test initialization with real database connections."""
        # Verify that pools and health checkers are properly initialized
        assert splitter.primary_pool is not None
        assert len(splitter.replica_pools) > 0
        assert splitter.primary_health_checker is not None
        assert len(splitter.replica_health_checkers) > 0

        # Check health status
        health_status = await splitter.get_health_status()
        assert "summary" in health_status
        assert "details" in health_status

    @pytest.mark.asyncio
    async def test_write_operation_on_primary(self, splitter):
        """Test that write operations are executed on the primary database."""
        # Create a test table
        async with splitter.get_connection(read_only=False) as conn:
            await conn.execute(
                """
                CREATE TABLE IF NOT EXISTS test_read_write_split (
                    id SERIAL PRIMARY KEY,
                    data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            # Insert test data
            await conn.execute(
                "INSERT INTO test_read_write_split (data) VALUES ($1)",
                "test_write_data",
            )

        # Verify data was written
        async with splitter.get_connection(read_only=False) as conn:
            result = await conn.fetchval(
                "SELECT COUNT(*) FROM test_read_write_split WHERE data = $1",
                "test_write_data",
            )
            assert result > 0

    @pytest.mark.asyncio
    async def test_read_operation_on_replica(self, splitter):
        """Test that read operations can be executed on replica databases."""
        # First, ensure we have some data (write to primary)
        async with splitter.get_connection(read_only=False) as conn:
            await conn.execute(
                "INSERT INTO test_read_write_split (data) VALUES ($1)", "test_read_data"
            )

        # Wait a moment for replication (in real scenarios)
        await asyncio.sleep(1)

        # Read from replica
        async with splitter.get_connection(read_only=True) as conn:
            result = await conn.fetchrow("SELECT version()")
            assert result is not None
            assert "version" in result

    @pytest.mark.asyncio
    async def test_load_balancing_distribution(self, splitter):
        """Test that read operations are distributed across replicas."""
        # Perform multiple read operations
        connection_sources = []

        for i in range(10):
            async with splitter.get_connection(read_only=True) as conn:
                # Get the connection's server info to identify which database we're connected to
                result = await conn.fetchrow(
                    "SELECT inet_server_addr() as server_addr, inet_server_port() as server_port"
                )
                connection_sources.append(
                    (result["server_addr"], result["server_port"])
                )

        # Verify that we used multiple different connections (load balancing)
        unique_sources = set(connection_sources)
        assert len(unique_sources) >= 1  # At least one source should be used

        # If we have multiple replicas, we should see distribution
        if len(splitter.replica_pools) > 1:
            # With round-robin, we should see multiple sources
            assert len(unique_sources) > 1 or len(connection_sources) <= len(
                splitter.replica_pools
            )

    @pytest.mark.asyncio
    async def test_transaction_on_primary(self, splitter):
        """Test transaction handling on primary database."""
        async with splitter.get_connection(read_only=False) as conn:
            async with conn.transaction():
                # Insert data within transaction
                await conn.execute(
                    "INSERT INTO test_read_write_split (data) VALUES ($1)",
                    "transaction_test_data",
                )

                # Verify data exists within transaction
                result = await conn.fetchval(
                    "SELECT COUNT(*) FROM test_read_write_split WHERE data = $1",
                    "transaction_test_data",
                )
                assert result > 0

        # Verify data persisted after transaction commit
        async with splitter.get_connection(read_only=False) as conn:
            result = await conn.fetchval(
                "SELECT COUNT(*) FROM test_read_write_split WHERE data = $1",
                "transaction_test_data",
            )
            assert result > 0

    @pytest.mark.asyncio
    async def test_concurrent_read_write_operations(self, splitter):
        """Test concurrent read and write operations."""

        async def write_operation(data_suffix):
            async with splitter.get_connection(read_only=False) as conn:
                await conn.execute(
                    "INSERT INTO test_read_write_split (data) VALUES ($1)",
                    f"concurrent_write_{data_suffix}",
                )

        async def read_operation():
            async with splitter.get_connection(read_only=True) as conn:
                result = await conn.fetchval(
                    "SELECT COUNT(*) FROM test_read_write_split"
                )
                return result

        # Create concurrent tasks
        write_tasks = [write_operation(i) for i in range(5)]
        read_tasks = [read_operation() for _ in range(10)]

        # Execute all tasks concurrently
        all_tasks = write_tasks + read_tasks
        results = await asyncio.gather(*all_tasks, return_exceptions=True)

        # Verify no exceptions occurred
        exceptions = [r for r in results if isinstance(r, Exception)]
        assert len(exceptions) == 0, f"Exceptions occurred: {exceptions}"

        # Verify read results
        read_results = results[5:]  # Last 10 results are from read operations
        assert all(isinstance(r, int) and r >= 0 for r in read_results)

    @pytest.mark.asyncio
    async def test_health_monitoring(self, splitter):
        """Test health monitoring functionality."""
        # Get initial health status
        health_status = await splitter.get_health_status()

        assert "summary" in health_status
        assert "details" in health_status

        summary = health_status["summary"]
        assert "primary_healthy" in summary
        assert "healthy_replicas" in summary
        assert "total_replicas" in summary

        # Primary should be healthy for tests to work
        assert summary["primary_healthy"] is True

        # At least some replicas should be healthy
        assert summary["healthy_replicas"] >= 0
        assert summary["total_replicas"] == len(splitter.replica_pools)

    @pytest.mark.asyncio
    async def test_connection_statistics(self, splitter):
        """Test connection statistics tracking."""
        # Perform some operations to generate statistics
        for _ in range(5):
            async with splitter.get_connection(read_only=True) as conn:
                await conn.fetchval("SELECT 1")

        for _ in range(3):
            async with splitter.get_connection(read_only=False) as conn:
                await conn.fetchval("SELECT 1")

        # Get statistics
        stats = splitter.get_stats()

        assert "load_balancing_strategy" in stats
        assert "replica_connection_counts" in stats
        assert "fallback_to_primary" in stats
        assert "health_check_interval" in stats

        # Verify strategy
        assert (
            stats["load_balancing_strategy"] == LoadBalancingStrategy.ROUND_ROBIN.value
        )

        # Verify connection counts (should have some activity)
        connection_counts = stats["replica_connection_counts"]
        assert isinstance(connection_counts, list)
        assert len(connection_counts) == len(splitter.replica_pools)

    @pytest.mark.asyncio
    async def test_different_load_balancing_strategies(self, database_configs):
        """Test different load balancing strategies."""
        primary_config, replica_configs = database_configs

        strategies = [
            LoadBalancingStrategy.ROUND_ROBIN,
            LoadBalancingStrategy.RANDOM,
            LoadBalancingStrategy.LEAST_CONNECTIONS,
        ]

        for strategy in strategies:
            splitter = ReadWriteSplitter(
                primary_config=primary_config,
                replica_configs=replica_configs,
                load_balancing_strategy=strategy,
                fallback_to_primary=True,
            )

            try:
                await splitter.initialize()

                # Perform read operations with this strategy
                for _ in range(3):
                    async with splitter.get_connection(read_only=True) as conn:
                        result = await conn.fetchval("SELECT 1")
                        assert result == 1

                # Verify strategy is set correctly
                stats = splitter.get_stats()
                assert stats["load_balancing_strategy"] == strategy.value

            finally:
                await splitter.close()

    @pytest.mark.asyncio
    async def test_cleanup_after_operations(self, splitter):
        """Test cleanup of test data after operations."""
        # Clean up test table
        async with splitter.get_connection(read_only=False) as conn:
            await conn.execute("DROP TABLE IF EXISTS test_read_write_split")

        # Verify table is dropped
        async with splitter.get_connection(read_only=False) as conn:
            result = await conn.fetchval(
                """
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_name = 'test_read_write_split'
            """
            )
            assert result == 0


class TestReadWriteSplitterConfigIntegration:
    """Integration tests for configuration-based ReadWriteSplitter."""

    @pytest.mark.asyncio
    async def test_config_file_integration(self):
        """Test ReadWriteSplitter with configuration file."""
        # Skip if config file doesn't exist
        config_path = "config/database_read_write_example.yaml"
        if not os.path.exists(config_path):
            pytest.skip(f"Configuration file {config_path} not found")

        try:
            # Load configuration
            config = load_config(config_path)

            if config.read_write and config.read_write.enabled:
                # Create splitter from configuration
                splitter = ReadWriteSplitter(
                    primary_config=config.read_write.primary,
                    replica_configs=config.read_write.replicas,
                    load_balancing_strategy=LoadBalancingStrategy(
                        config.read_write.load_balancing_strategy
                    ),
                    fallback_to_primary=config.read_write.fallback_to_primary,
                    health_check_interval=config.read_write.health_check_interval,
                )

                # Test basic functionality
                await splitter.initialize()

                # Test health status
                health_status = await splitter.get_health_status()
                assert "summary" in health_status

                await splitter.close()
            else:
                pytest.skip("Read-write splitting not enabled in configuration")

        except Exception as e:
            pytest.skip(f"Configuration test failed: {e}")


if __name__ == "__main__":
    # Run integration tests
    pytest.main([__file__, "-v"])
