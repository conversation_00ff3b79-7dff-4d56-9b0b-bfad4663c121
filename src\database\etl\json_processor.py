"""JSON file processing for ETL operations.

This module provides comprehensive JSON file processing capabilities including
reading from various JSON formats, writing with formatting, and data validation.
"""

import bz2
import gzip
import json
import lzma
from dataclasses import dataclass, field
from datetime import date, datetime
from io import String<PERSON>
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union

import numpy as np
import pandas as pd
from loguru import logger

try:
    import jsonlines

    JSONLINES_AVAILABLE = True
except ImportError:
    JSONLINES_AVAILABLE = False
    logger.warning("jsonlines not available. JSONL processing will be limited.")

try:
    import orjson

    ORJSON_AVAILABLE = True
except ImportError:
    ORJSON_AVAILABLE = False
    logger.warning("orjson not available. JSON processing will use standard library.")

from ..exceptions import ProcessingError, ValidationError
from ..utils.progress_tracker import ProgressTracker


class JSONFormat:
    """JSON format types."""

    STANDARD = "standard"  # Regular JSON
    LINES = "lines"  # JSON Lines (JSONL)
    ARRAY = "array"  # JSON array
    NESTED = "nested"  # Nested JSON objects


class CompressionType:
    """Compression types."""

    NONE = "none"
    GZIP = "gzip"
    BZIP2 = "bzip2"
    LZMA = "lzma"


@dataclass
class JSONReadOptions:
    """Options for reading JSON files."""

    format_type: str = JSONFormat.STANDARD
    encoding: str = "utf-8"
    compression: str = CompressionType.NONE
    lines: bool = False  # For pandas read_json
    orient: str = "records"  # For pandas read_json
    typ: str = "frame"  # For pandas read_json
    dtype: Optional[Dict[str, str]] = None
    convert_axes: bool = True
    convert_dates: Union[bool, List[str]] = True
    keep_default_dates: bool = True
    numpy: bool = False
    precise_float: bool = False
    date_unit: Optional[str] = None
    encoding_errors: str = "strict"
    chunksize: Optional[int] = None
    nrows: Optional[int] = None
    storage_options: Optional[Dict[str, Any]] = None
    custom_parser: Optional[Callable] = None


@dataclass
class JSONWriteOptions:
    """Options for writing JSON files."""

    format_type: str = JSONFormat.STANDARD
    encoding: str = "utf-8"
    compression: str = CompressionType.NONE
    orient: str = "records"  # For pandas to_json
    date_format: str = "iso"
    double_precision: int = 10
    force_ascii: bool = False
    date_unit: str = "ms"
    default_handler: Optional[Callable] = None
    lines: bool = False  # For pandas to_json
    indent: Optional[int] = None
    separators: Optional[tuple] = None
    sort_keys: bool = False
    ensure_ascii: bool = False
    storage_options: Optional[Dict[str, Any]] = None


@dataclass
class JSONProcessingResult:
    """Result of JSON processing operation."""

    success: bool
    data: Optional[Union[pd.DataFrame, Dict, List, Any]] = None
    file_path: Optional[str] = None
    format_detected: Optional[str] = None
    records_processed: int = 0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)
    execution_time: Optional[float] = None


class JSONProcessor:
    """JSON file processor for ETL operations."""

    def __init__(self):
        """Initialize JSON processor."""
        self.supported_extensions = [
            ".json",
            ".jsonl",
            ".ndjson",
            ".json.gz",
            ".jsonl.gz",
        ]

    def read_json(
        self,
        file_path: Union[str, Path],
        options: Optional[JSONReadOptions] = None,
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> JSONProcessingResult:
        """Read JSON file.

        Args:
            file_path: Path to JSON file
            options: Read options
            progress_tracker: Optional progress tracker

        Returns:
            JSONProcessingResult
        """
        try:
            start_time = datetime.now()
            file_path = Path(file_path)

            if not file_path.exists():
                raise ProcessingError(f"JSON file not found: {file_path}")

            if options is None:
                options = JSONReadOptions()

            # Auto-detect format and compression
            detected_format, detected_compression = self._detect_format(file_path)

            if options.format_type == JSONFormat.STANDARD:
                options.format_type = detected_format
            if options.compression == CompressionType.NONE:
                options.compression = detected_compression

            logger.info(
                f"Reading JSON file: {file_path} (format: {options.format_type}, compression: {options.compression})"
            )

            if progress_tracker:
                progress_tracker.start_task(f"Reading JSON file: {file_path.name}", 1)

            # Read based on format
            if options.format_type == JSONFormat.LINES:
                data = self._read_jsonlines(file_path, options)
            elif options.format_type == JSONFormat.ARRAY:
                data = self._read_json_array(file_path, options)
            elif options.format_type == JSONFormat.NESTED:
                data = self._read_nested_json(file_path, options)
            else:
                data = self._read_standard_json(file_path, options)

            if progress_tracker:
                progress_tracker.update_progress(1)
                progress_tracker.complete_task()

            execution_time = (datetime.now() - start_time).total_seconds()

            # Calculate statistics
            records_processed = 0
            if isinstance(data, pd.DataFrame):
                records_processed = len(data)
            elif isinstance(data, list):
                records_processed = len(data)
            elif isinstance(data, dict):
                records_processed = 1

            logger.info(f"Successfully read JSON file: {file_path}")

            return JSONProcessingResult(
                success=True,
                data=data,
                file_path=str(file_path),
                format_detected=options.format_type,
                records_processed=records_processed,
                statistics={
                    "file_size_bytes": file_path.stat().st_size,
                    "format": options.format_type,
                    "compression": options.compression,
                    "encoding": options.encoding,
                },
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Failed to read JSON file {file_path}: {e}")
            return JSONProcessingResult(success=False, errors=[str(e)])

    def write_json(
        self,
        data: Union[pd.DataFrame, Dict, List, Any],
        file_path: Union[str, Path],
        options: Optional[JSONWriteOptions] = None,
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> JSONProcessingResult:
        """Write data to JSON file.

        Args:
            data: Data to write
            file_path: Output file path
            options: Write options
            progress_tracker: Optional progress tracker

        Returns:
            JSONProcessingResult
        """
        try:
            start_time = datetime.now()
            file_path = Path(file_path)

            # Create directory if it doesn't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)

            if options is None:
                options = JSONWriteOptions()

            logger.info(
                f"Writing JSON file: {file_path} (format: {options.format_type})"
            )

            if progress_tracker:
                progress_tracker.start_task(f"Writing JSON file: {file_path.name}", 1)

            # Write based on format
            if options.format_type == JSONFormat.LINES:
                self._write_jsonlines(data, file_path, options)
            elif options.format_type == JSONFormat.ARRAY:
                self._write_json_array(data, file_path, options)
            else:
                self._write_standard_json(data, file_path, options)

            if progress_tracker:
                progress_tracker.update_progress(1)
                progress_tracker.complete_task()

            execution_time = (datetime.now() - start_time).total_seconds()

            # Calculate statistics
            records_processed = 0
            if isinstance(data, pd.DataFrame):
                records_processed = len(data)
            elif isinstance(data, list):
                records_processed = len(data)
            elif isinstance(data, dict):
                records_processed = 1

            logger.info(f"Successfully wrote JSON file: {file_path}")

            return JSONProcessingResult(
                success=True,
                file_path=str(file_path),
                records_processed=records_processed,
                statistics={
                    "file_size_bytes": file_path.stat().st_size
                    if file_path.exists()
                    else 0,
                    "format": options.format_type,
                    "compression": options.compression,
                    "encoding": options.encoding,
                },
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Failed to write JSON file {file_path}: {e}")
            return JSONProcessingResult(success=False, errors=[str(e)])

    def _detect_format(self, file_path: Path) -> tuple:
        """Detect JSON format and compression.

        Args:
            file_path: Path to JSON file

        Returns:
            Tuple of (format, compression)
        """
        # Detect compression
        compression = CompressionType.NONE
        if file_path.suffix.lower() == ".gz":
            compression = CompressionType.GZIP
        elif file_path.suffix.lower() == ".bz2":
            compression = CompressionType.BZIP2
        elif file_path.suffix.lower() == ".xz":
            compression = CompressionType.LZMA

        # Detect format
        format_type = JSONFormat.STANDARD
        if ".jsonl" in file_path.name.lower() or ".ndjson" in file_path.name.lower():
            format_type = JSONFormat.LINES

        return format_type, compression

    def _open_file(
        self,
        file_path: Path,
        mode: str,
        options: Union[JSONReadOptions, JSONWriteOptions],
    ):
        """Open file with appropriate compression.

        Args:
            file_path: Path to file
            mode: File mode
            options: Read or write options

        Returns:
            File handle
        """
        if options.compression == CompressionType.GZIP:
            return gzip.open(file_path, mode + "t", encoding=options.encoding)
        elif options.compression == CompressionType.BZIP2:
            return bz2.open(file_path, mode + "t", encoding=options.encoding)
        elif options.compression == CompressionType.LZMA:
            return lzma.open(file_path, mode + "t", encoding=options.encoding)
        else:
            return open(file_path, mode, encoding=options.encoding)

    def _read_standard_json(self, file_path: Path, options: JSONReadOptions):
        """Read standard JSON file.

        Args:
            file_path: Path to JSON file
            options: Read options

        Returns:
            Parsed data
        """
        if options.custom_parser:
            with self._open_file(file_path, "r", options) as f:
                return options.custom_parser(f)

        # Try pandas first for DataFrame conversion
        try:
            return pd.read_json(
                file_path,
                orient=options.orient,
                typ=options.typ,
                dtype=options.dtype,
                convert_axes=options.convert_axes,
                convert_dates=options.convert_dates,
                keep_default_dates=options.keep_default_dates,
                numpy=options.numpy,
                precise_float=options.precise_float,
                date_unit=options.date_unit,
                encoding=options.encoding,
                encoding_errors=options.encoding_errors,
                lines=options.lines,
                chunksize=options.chunksize,
                nrows=options.nrows,
                storage_options=options.storage_options,
            )
        except Exception:
            # Fallback to standard JSON
            with self._open_file(file_path, "r", options) as f:
                if ORJSON_AVAILABLE:
                    content = f.read()
                    return orjson.loads(content)
                else:
                    return json.load(f)

    def _read_jsonlines(self, file_path: Path, options: JSONReadOptions):
        """Read JSON Lines file.

        Args:
            file_path: Path to JSONL file
            options: Read options

        Returns:
            List of parsed objects or DataFrame
        """
        records = []

        if JSONLINES_AVAILABLE and options.compression == CompressionType.NONE:
            # Use jsonlines library
            with jsonlines.open(file_path, mode="r") as reader:
                for i, obj in enumerate(reader):
                    if options.nrows and i >= options.nrows:
                        break
                    records.append(obj)
        else:
            # Manual parsing
            with self._open_file(file_path, "r", options) as f:
                for i, line in enumerate(f):
                    if options.nrows and i >= options.nrows:
                        break
                    line = line.strip()
                    if line:
                        if ORJSON_AVAILABLE:
                            records.append(orjson.loads(line))
                        else:
                            records.append(json.loads(line))

        # Convert to DataFrame if possible
        if records and options.typ == "frame":
            try:
                return pd.DataFrame(records)
            except Exception:
                return records
        else:
            return records

    def _read_json_array(self, file_path: Path, options: JSONReadOptions):
        """Read JSON array file.

        Args:
            file_path: Path to JSON file
            options: Read options

        Returns:
            List or DataFrame
        """
        with self._open_file(file_path, "r", options) as f:
            if ORJSON_AVAILABLE:
                content = f.read()
                data = orjson.loads(content)
            else:
                data = json.load(f)

        if not isinstance(data, list):
            raise ProcessingError("Expected JSON array format")

        # Apply nrows limit
        if options.nrows:
            data = data[: options.nrows]

        # Convert to DataFrame if possible
        if data and options.typ == "frame":
            try:
                return pd.DataFrame(data)
            except Exception:
                return data
        else:
            return data

    def _read_nested_json(self, file_path: Path, options: JSONReadOptions):
        """Read nested JSON file and flatten.

        Args:
            file_path: Path to JSON file
            options: Read options

        Returns:
            Flattened data
        """
        with self._open_file(file_path, "r", options) as f:
            if ORJSON_AVAILABLE:
                content = f.read()
                data = orjson.loads(content)
            else:
                data = json.load(f)

        # Flatten nested structure
        flattened = self._flatten_json(data)

        if options.typ == "frame":
            try:
                return pd.json_normalize(flattened)
            except Exception:
                return flattened
        else:
            return flattened

    def _write_standard_json(
        self, data: Any, file_path: Path, options: JSONWriteOptions
    ):
        """Write standard JSON file.

        Args:
            data: Data to write
            file_path: Output file path
            options: Write options
        """
        if isinstance(data, pd.DataFrame):
            # Use pandas to_json
            data.to_json(
                file_path,
                orient=options.orient,
                date_format=options.date_format,
                double_precision=options.double_precision,
                force_ascii=options.force_ascii,
                date_unit=options.date_unit,
                default_handler=options.default_handler,
                lines=options.lines,
                compression=options.compression
                if options.compression != CompressionType.NONE
                else None,
                index=False,
                indent=options.indent,
                storage_options=options.storage_options,
            )
        else:
            # Use standard JSON
            with self._open_file(file_path, "w", options) as f:
                if ORJSON_AVAILABLE:
                    # orjson doesn't support all options, so use standard json for complex cases
                    if options.indent or options.separators or options.sort_keys:
                        json.dump(
                            data,
                            f,
                            indent=options.indent,
                            separators=options.separators,
                            sort_keys=options.sort_keys,
                            ensure_ascii=options.ensure_ascii,
                            default=options.default_handler,
                        )
                    else:
                        f.write(
                            orjson.dumps(data, default=options.default_handler).decode()
                        )
                else:
                    json.dump(
                        data,
                        f,
                        indent=options.indent,
                        separators=options.separators,
                        sort_keys=options.sort_keys,
                        ensure_ascii=options.ensure_ascii,
                        default=options.default_handler,
                    )

    def _write_jsonlines(self, data: Any, file_path: Path, options: JSONWriteOptions):
        """Write JSON Lines file.

        Args:
            data: Data to write
            file_path: Output file path
            options: Write options
        """
        if isinstance(data, pd.DataFrame):
            records = data.to_dict("records")
        elif isinstance(data, list):
            records = data
        else:
            records = [data]

        if JSONLINES_AVAILABLE and options.compression == CompressionType.NONE:
            # Use jsonlines library
            with jsonlines.open(file_path, mode="w") as writer:
                for record in records:
                    writer.write(record)
        else:
            # Manual writing
            with self._open_file(file_path, "w", options) as f:
                for record in records:
                    if ORJSON_AVAILABLE:
                        line = orjson.dumps(
                            record, default=options.default_handler
                        ).decode()
                    else:
                        line = json.dumps(
                            record,
                            ensure_ascii=options.ensure_ascii,
                            default=options.default_handler,
                        )
                    f.write(line + "\n")

    def _write_json_array(self, data: Any, file_path: Path, options: JSONWriteOptions):
        """Write JSON array file.

        Args:
            data: Data to write
            file_path: Output file path
            options: Write options
        """
        if isinstance(data, pd.DataFrame):
            array_data = data.to_dict("records")
        elif isinstance(data, list):
            array_data = data
        else:
            array_data = [data]

        with self._open_file(file_path, "w", options) as f:
            if ORJSON_AVAILABLE:
                if options.indent:
                    # orjson doesn't support indent, use standard json
                    json.dump(
                        array_data,
                        f,
                        indent=options.indent,
                        ensure_ascii=options.ensure_ascii,
                        default=options.default_handler,
                    )
                else:
                    f.write(
                        orjson.dumps(
                            array_data, default=options.default_handler
                        ).decode()
                    )
            else:
                json.dump(
                    array_data,
                    f,
                    indent=options.indent,
                    ensure_ascii=options.ensure_ascii,
                    default=options.default_handler,
                )

    def _flatten_json(self, data: Any, parent_key: str = "", sep: str = ".") -> Dict:
        """Flatten nested JSON structure.

        Args:
            data: Data to flatten
            parent_key: Parent key prefix
            sep: Separator for nested keys

        Returns:
            Flattened dictionary
        """
        items = []

        if isinstance(data, dict):
            for k, v in data.items():
                new_key = f"{parent_key}{sep}{k}" if parent_key else k
                if isinstance(v, (dict, list)):
                    items.extend(self._flatten_json(v, new_key, sep).items())
                else:
                    items.append((new_key, v))
        elif isinstance(data, list):
            for i, v in enumerate(data):
                new_key = f"{parent_key}{sep}{i}" if parent_key else str(i)
                if isinstance(v, (dict, list)):
                    items.extend(self._flatten_json(v, new_key, sep).items())
                else:
                    items.append((new_key, v))
        else:
            return {parent_key: data}

        return dict(items)

    def validate_json_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Validate JSON file structure.

        Args:
            file_path: Path to JSON file

        Returns:
            Validation results
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                return {"valid": False, "errors": [f"File not found: {file_path}"]}

            file_size = file_path.stat().st_size

            if file_size == 0:
                return {"valid": False, "errors": ["File is empty"]}

            # Try to parse JSON
            errors = []
            warnings = []

            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    # Read first few lines to detect format
                    first_lines = []
                    for i, line in enumerate(f):
                        first_lines.append(line.strip())
                        if i >= 10:  # Check first 10 lines
                            break

                # Reset file pointer
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                # Try to parse as standard JSON
                try:
                    json.loads(content)
                    format_detected = JSONFormat.STANDARD
                except json.JSONDecodeError:
                    # Try as JSON Lines
                    try:
                        for line in first_lines:
                            if line:
                                json.loads(line)
                        format_detected = JSONFormat.LINES
                    except json.JSONDecodeError as e:
                        errors.append(f"Invalid JSON format: {e}")
                        format_detected = "unknown"

            except UnicodeDecodeError as e:
                errors.append(f"Encoding error: {e}")
                format_detected = "unknown"

            # Check file size warnings
            if file_size > 100 * 1024 * 1024:  # 100MB
                warnings.append("Large file size may impact performance")

            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "info": {
                    "file_size_bytes": file_size,
                    "format_detected": format_detected,
                    "file_extension": file_path.suffix,
                },
            }

        except Exception as e:
            return {"valid": False, "errors": [f"Validation failed: {e}"]}

    def convert_format(
        self,
        input_path: Union[str, Path],
        output_path: Union[str, Path],
        input_format: str,
        output_format: str,
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> JSONProcessingResult:
        """Convert between JSON formats.

        Args:
            input_path: Input file path
            output_path: Output file path
            input_format: Input format type
            output_format: Output format type
            progress_tracker: Optional progress tracker

        Returns:
            JSONProcessingResult
        """
        try:
            start_time = datetime.now()

            logger.info(
                f"Converting JSON format from {input_format} to {output_format}"
            )

            if progress_tracker:
                progress_tracker.start_task("Converting JSON format", 2)

            # Read with input format
            read_options = JSONReadOptions(format_type=input_format)
            read_result = self.read_json(input_path, read_options)

            if not read_result.success:
                return read_result

            if progress_tracker:
                progress_tracker.update_progress(1)

            # Write with output format
            write_options = JSONWriteOptions(format_type=output_format)
            write_result = self.write_json(read_result.data, output_path, write_options)

            if progress_tracker:
                progress_tracker.update_progress(2)
                progress_tracker.complete_task()

            execution_time = (datetime.now() - start_time).total_seconds()

            if write_result.success:
                logger.info(f"Successfully converted JSON format")

                return JSONProcessingResult(
                    success=True,
                    file_path=str(output_path),
                    records_processed=read_result.records_processed,
                    statistics={
                        "input_format": input_format,
                        "output_format": output_format,
                        "input_file": str(input_path),
                        "output_file": str(output_path),
                    },
                    execution_time=execution_time,
                )
            else:
                return write_result

        except Exception as e:
            logger.error(f"Failed to convert JSON format: {e}")
            return JSONProcessingResult(success=False, errors=[str(e)])

    def merge_json_files(
        self,
        file_paths: List[Union[str, Path]],
        output_path: Union[str, Path],
        merge_strategy: str = "array",
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> JSONProcessingResult:
        """Merge multiple JSON files.

        Args:
            file_paths: List of JSON file paths
            output_path: Output file path
            merge_strategy: 'array' (combine into array) or 'lines' (JSON Lines)
            progress_tracker: Optional progress tracker

        Returns:
            JSONProcessingResult
        """
        try:
            start_time = datetime.now()

            if not file_paths:
                raise ProcessingError("No files provided for merging")

            logger.info(f"Merging {len(file_paths)} JSON files")

            if progress_tracker:
                progress_tracker.start_task("Merging JSON files", len(file_paths) + 1)

            merged_data = []
            total_records = 0

            for i, file_path in enumerate(file_paths):
                file_path = Path(file_path)

                if not file_path.exists():
                    logger.warning(f"File not found, skipping: {file_path}")
                    continue

                logger.info(f"Processing file: {file_path.name}")

                # Read file
                read_result = self.read_json(file_path)

                if read_result.success:
                    data = read_result.data

                    if isinstance(data, list):
                        merged_data.extend(data)
                        total_records += len(data)
                    elif isinstance(data, pd.DataFrame):
                        records = data.to_dict("records")
                        merged_data.extend(records)
                        total_records += len(records)
                    else:
                        merged_data.append(data)
                        total_records += 1
                else:
                    logger.warning(f"Failed to read file: {file_path}")

                if progress_tracker:
                    progress_tracker.update_progress(i + 1)

            # Write merged data
            if merge_strategy == "lines":
                write_options = JSONWriteOptions(format_type=JSONFormat.LINES)
            else:
                write_options = JSONWriteOptions(format_type=JSONFormat.ARRAY)

            write_result = self.write_json(merged_data, output_path, write_options)

            if progress_tracker:
                progress_tracker.update_progress(len(file_paths) + 1)
                progress_tracker.complete_task()

            execution_time = (datetime.now() - start_time).total_seconds()

            if write_result.success:
                logger.info(f"Successfully merged JSON files to: {output_path}")

                return JSONProcessingResult(
                    success=True,
                    file_path=str(output_path),
                    records_processed=total_records,
                    statistics={
                        "input_files": len(file_paths),
                        "total_records": total_records,
                        "merge_strategy": merge_strategy,
                        "output_format": write_options.format_type,
                    },
                    execution_time=execution_time,
                )
            else:
                return write_result

        except Exception as e:
            logger.error(f"Failed to merge JSON files: {e}")
            return JSONProcessingResult(success=False, errors=[str(e)])


# Convenience functions
def read_json_file(
    file_path: Union[str, Path], format_type: str = JSONFormat.STANDARD, **kwargs
) -> Union[pd.DataFrame, Dict, List]:
    """Convenience function to read JSON file.

    Args:
        file_path: Path to JSON file
        format_type: JSON format type
        **kwargs: Additional read options

    Returns:
        Parsed data
    """
    processor = JSONProcessor()
    options = JSONReadOptions(format_type=format_type, **kwargs)
    result = processor.read_json(file_path, options)

    if not result.success:
        raise ProcessingError(f"Failed to read JSON file: {result.errors}")

    return result.data


def write_json_file(
    data: Union[pd.DataFrame, Dict, List],
    file_path: Union[str, Path],
    format_type: str = JSONFormat.STANDARD,
    **kwargs,
) -> bool:
    """Convenience function to write JSON file.

    Args:
        data: Data to write
        file_path: Output file path
        format_type: JSON format type
        **kwargs: Additional write options

    Returns:
        True if successful
    """
    processor = JSONProcessor()
    options = JSONWriteOptions(format_type=format_type, **kwargs)
    result = processor.write_json(data, file_path, options)

    if not result.success:
        raise ProcessingError(f"Failed to write JSON file: {result.errors}")

    return True


def json_to_dataframe(
    json_data: Union[str, Dict, List], normalize: bool = True
) -> pd.DataFrame:
    """Convert JSON data to DataFrame.

    Args:
        json_data: JSON data (string, dict, or list)
        normalize: Whether to normalize nested structures

    Returns:
        DataFrame
    """
    if isinstance(json_data, str):
        data = json.loads(json_data)
    else:
        data = json_data

    if normalize and isinstance(data, (dict, list)):
        return pd.json_normalize(data)
    else:
        return pd.DataFrame(data)


def dataframe_to_json(
    df: pd.DataFrame, format_type: str = JSONFormat.STANDARD, **kwargs
) -> str:
    """Convert DataFrame to JSON string.

    Args:
        df: DataFrame to convert
        format_type: JSON format type
        **kwargs: Additional conversion options

    Returns:
        JSON string
    """
    if format_type == JSONFormat.LINES:
        records = df.to_dict("records")
        lines = []
        for record in records:
            lines.append(json.dumps(record, **kwargs))
        return "\n".join(lines)
    else:
        return df.to_json(orient="records", **kwargs)
