"""统一验证框架核心组件测试

测试ValidationFramework、ValidationRule、ValidationResult等核心组件的功能。
"""

import pytest
import pandas as pd
from datetime import datetime
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor

from ..core import (
    ValidationFramework,
    ValidationRule,
    ValidationResult,
    ValidationIssue,
    ValidationContext,
    ValidationType,
    ValidationSeverity
)
from ..exceptions import (
    ValidationError,
    ValidationRuleError,
    ValidationTimeoutError
)
from .conftest import performance_test, memory_test


class TestValidationRule:
    """ValidationRule测试类"""
    
    def test_validation_rule_creation(self):
        """测试验证规则创建"""
        rule = ValidationRule(
            name="test_rule",
            validation_type=ValidationType.STRUCTURE,
            severity=ValidationSeverity.ERROR,
            description="Test rule description"
        )
        
        assert rule.name == "test_rule"
        assert rule.validation_type == ValidationType.STRUCTURE
        assert rule.severity == ValidationSeverity.ERROR
        assert rule.description == "Test rule description"
        assert rule.enabled is True
    
    def test_validation_rule_abstract_validate(self):
        """测试抽象validate方法"""
        rule = ValidationRule("test_rule", ValidationType.STRUCTURE)
        
        with pytest.raises(NotImplementedError):
            rule.validate(pd.DataFrame(), None)
    
    def test_validation_rule_str_repr(self):
        """测试字符串表示"""
        rule = ValidationRule("test_rule", ValidationType.STRUCTURE)
        
        assert str(rule) == "ValidationRule(test_rule, STRUCTURE, ERROR)"
        assert "test_rule" in repr(rule)


class ConcreteValidationRule(ValidationRule):
    """具体的验证规则实现，用于测试"""
    
    def __init__(self, name="concrete_rule", should_pass=True, execution_time=0.0):
        super().__init__(name, ValidationType.VALUE)
        self.should_pass = should_pass
        self.execution_time = execution_time
        self.call_count = 0
    
    def validate(self, data, context=None):
        import time
        self.call_count += 1
        
        if self.execution_time > 0:
            time.sleep(self.execution_time)
        
        if not self.should_pass:
            return ValidationIssue(
                rule_name=self.name,
                severity=self.severity,
                message=f"Rule {self.name} failed",
                details={"data_shape": data.shape if hasattr(data, 'shape') else None}
            )
        
        return None


class TestValidationIssue:
    """ValidationIssue测试类"""
    
    def test_validation_issue_creation(self):
        """测试验证问题创建"""
        issue = ValidationIssue(
            rule_name="test_rule",
            severity=ValidationSeverity.ERROR,
            message="Test error message",
            details={"column": "test_column", "value": "test_value"}
        )
        
        assert issue.rule_name == "test_rule"
        assert issue.severity == ValidationSeverity.ERROR
        assert issue.message == "Test error message"
        assert issue.details["column"] == "test_column"
        assert issue.details["value"] == "test_value"
    
    def test_validation_issue_str_repr(self):
        """测试字符串表示"""
        issue = ValidationIssue(
            rule_name="test_rule",
            severity=ValidationSeverity.WARNING,
            message="Test warning"
        )
        
        assert str(issue) == "WARNING: Test warning (test_rule)"
        assert "test_rule" in repr(issue)
        assert "WARNING" in repr(issue)


class TestValidationResult:
    """ValidationResult测试类"""
    
    def test_validation_result_creation(self):
        """测试验证结果创建"""
        issues = [
            ValidationIssue("rule1", ValidationSeverity.ERROR, "Error 1"),
            ValidationIssue("rule2", ValidationSeverity.WARNING, "Warning 1")
        ]
        
        result = ValidationResult(
            success=False,
            issues=issues,
            total_rules=5,
            passed_rules=3,
            execution_time=1.5,
            context=ValidationContext("test", "/test/file.csv")
        )
        
        assert result.success is False
        assert len(result.issues) == 2
        assert result.total_rules == 5
        assert result.passed_rules == 3
        assert result.execution_time == 1.5
        assert result.context.data_type == "test"
    
    def test_validation_result_error_count(self):
        """测试错误计数"""
        issues = [
            ValidationIssue("rule1", ValidationSeverity.ERROR, "Error 1"),
            ValidationIssue("rule2", ValidationSeverity.ERROR, "Error 2"),
            ValidationIssue("rule3", ValidationSeverity.WARNING, "Warning 1")
        ]
        
        result = ValidationResult(success=False, issues=issues)
        
        assert result.error_count == 2
        assert result.warning_count == 1
    
    def test_validation_result_summary(self):
        """测试结果摘要"""
        issues = [
            ValidationIssue("rule1", ValidationSeverity.ERROR, "Error 1"),
            ValidationIssue("rule2", ValidationSeverity.WARNING, "Warning 1")
        ]
        
        result = ValidationResult(
            success=False,
            issues=issues,
            total_rules=3,
            passed_rules=1
        )
        
        summary = result.get_summary()
        
        assert "Success: False" in summary
        assert "Total Rules: 3" in summary
        assert "Passed Rules: 1" in summary
        assert "Errors: 1" in summary
        assert "Warnings: 1" in summary


class TestValidationContext:
    """ValidationContext测试类"""
    
    def test_validation_context_creation(self):
        """测试验证上下文创建"""
        metadata = {"batch_id": "BATCH001", "importer": "TestImporter"}
        
        context = ValidationContext(
            data_type="cdr",
            file_path="/test/data.csv",
            metadata=metadata
        )
        
        assert context.data_type == "cdr"
        assert context.file_path == "/test/data.csv"
        assert context.metadata["batch_id"] == "BATCH001"
        assert context.metadata["importer"] == "TestImporter"
    
    def test_validation_context_defaults(self):
        """测试默认值"""
        context = ValidationContext("test")
        
        assert context.data_type == "test"
        assert context.file_path is None
        assert context.metadata == {}


class TestValidationFramework:
    """ValidationFramework测试类"""
    
    def test_framework_creation(self):
        """测试框架创建"""
        rules = [
            ConcreteValidationRule("rule1"),
            ConcreteValidationRule("rule2")
        ]
        
        framework = ValidationFramework(
            name="test_framework",
            rules=rules,
            enable_parallel=False
        )
        
        assert framework.name == "test_framework"
        assert len(framework.rules) == 2
        assert framework.enable_parallel is False
        assert framework.max_workers == 4
    
    def test_add_rule(self):
        """测试添加规则"""
        framework = ValidationFramework("test_framework")
        rule = ConcreteValidationRule("new_rule")
        
        framework.add_rule(rule)
        
        assert len(framework.rules) == 1
        assert framework.rules[0].name == "new_rule"
    
    def test_remove_rule(self):
        """测试移除规则"""
        rule1 = ConcreteValidationRule("rule1")
        rule2 = ConcreteValidationRule("rule2")
        
        framework = ValidationFramework("test_framework", [rule1, rule2])
        
        framework.remove_rule("rule1")
        
        assert len(framework.rules) == 1
        assert framework.rules[0].name == "rule2"
    
    def test_get_rule(self):
        """测试获取规则"""
        rule = ConcreteValidationRule("target_rule")
        framework = ValidationFramework("test_framework", [rule])
        
        found_rule = framework.get_rule("target_rule")
        not_found_rule = framework.get_rule("non_existent")
        
        assert found_rule is rule
        assert not_found_rule is None
    
    def test_enable_disable_rule(self):
        """测试启用/禁用规则"""
        rule = ConcreteValidationRule("test_rule")
        framework = ValidationFramework("test_framework", [rule])
        
        # 禁用规则
        framework.disable_rule("test_rule")
        assert not rule.enabled
        
        # 启用规则
        framework.enable_rule("test_rule")
        assert rule.enabled
    
    def test_validate_success(self, sample_cdr_data, validation_context):
        """测试验证成功"""
        rules = [
            ConcreteValidationRule("rule1", should_pass=True),
            ConcreteValidationRule("rule2", should_pass=True)
        ]
        
        framework = ValidationFramework("test_framework", rules)
        result = framework.validate(sample_cdr_data, validation_context)
        
        assert result.success is True
        assert len(result.issues) == 0
        assert result.total_rules == 2
        assert result.passed_rules == 2
        assert result.execution_time > 0
    
    def test_validate_failure(self, sample_cdr_data, validation_context):
        """测试验证失败"""
        rules = [
            ConcreteValidationRule("rule1", should_pass=True),
            ConcreteValidationRule("rule2", should_pass=False)
        ]
        
        framework = ValidationFramework("test_framework", rules)
        result = framework.validate(sample_cdr_data, validation_context)
        
        assert result.success is False
        assert len(result.issues) == 1
        assert result.issues[0].rule_name == "rule2"
        assert result.total_rules == 2
        assert result.passed_rules == 1
    
    def test_validate_disabled_rule(self, sample_cdr_data, validation_context):
        """测试禁用的规则不被执行"""
        rule = ConcreteValidationRule("disabled_rule", should_pass=False)
        rule.enabled = False
        
        framework = ValidationFramework("test_framework", [rule])
        result = framework.validate(sample_cdr_data, validation_context)
        
        assert result.success is True
        assert len(result.issues) == 0
        assert rule.call_count == 0  # 规则未被调用
    
    @performance_test(max_time=2.0)
    def test_validate_parallel(self, sample_cdr_data, validation_context):
        """测试并行验证"""
        rules = [
            ConcreteValidationRule(f"rule{i}", should_pass=True, execution_time=0.1)
            for i in range(10)
        ]
        
        framework = ValidationFramework(
            "parallel_framework",
            rules,
            enable_parallel=True,
            max_workers=4
        )
        
        result = framework.validate(sample_cdr_data, validation_context)
        
        assert result.success is True
        assert result.total_rules == 10
        assert result.passed_rules == 10
        # 并行执行应该比串行快
        assert result.execution_time < 1.0
    
    def test_validate_timeout(self, sample_cdr_data, validation_context):
        """测试验证超时"""
        rules = [
            ConcreteValidationRule("slow_rule", execution_time=2.0)
        ]
        
        framework = ValidationFramework(
            "timeout_framework",
            rules,
            timeout=1.0
        )
        
        with pytest.raises(ValidationTimeoutError):
            framework.validate(sample_cdr_data, validation_context)
    
    def test_validate_rule_exception(self, sample_cdr_data, validation_context):
        """测试规则执行异常"""
        class ExceptionRule(ValidationRule):
            def __init__(self):
                super().__init__("exception_rule", ValidationType.VALUE)
            
            def validate(self, data, context=None):
                raise ValueError("Test exception")
        
        framework = ValidationFramework("exception_framework", [ExceptionRule()])
        
        with pytest.raises(ValidationRuleError):
            framework.validate(sample_cdr_data, validation_context)
    
    def test_validate_empty_data(self, empty_dataframe, validation_context):
        """测试空数据验证"""
        rule = ConcreteValidationRule("test_rule")
        framework = ValidationFramework("test_framework", [rule])
        
        result = framework.validate(empty_dataframe, validation_context)
        
        assert result.success is True
        assert rule.call_count == 1  # 规则仍然被执行
    
    def test_validate_no_rules(self, sample_cdr_data, validation_context):
        """测试无规则验证"""
        framework = ValidationFramework("empty_framework", [])
        
        result = framework.validate(sample_cdr_data, validation_context)
        
        assert result.success is True
        assert result.total_rules == 0
        assert result.passed_rules == 0
        assert len(result.issues) == 0
    
    def test_validate_with_severity_filter(self, sample_cdr_data, validation_context):
        """测试严重程度过滤"""
        rules = [
            ConcreteValidationRule("error_rule", should_pass=False),
            ConcreteValidationRule("warning_rule", should_pass=False)
        ]
        rules[1].severity = ValidationSeverity.WARNING
        
        framework = ValidationFramework(
            "severity_framework",
            rules,
            severity_threshold=ValidationSeverity.ERROR
        )
        
        result = framework.validate(sample_cdr_data, validation_context)
        
        # 只有ERROR级别的问题会导致验证失败
        assert result.success is False
        assert len(result.issues) == 2  # 两个问题都被记录
        assert result.error_count == 1
        assert result.warning_count == 1
    
    @memory_test(max_memory_mb=50)
    def test_validate_memory_usage(self, large_dataset, validation_context):
        """测试内存使用"""
        rules = [
            ConcreteValidationRule(f"rule{i}")
            for i in range(5)
        ]
        
        framework = ValidationFramework("memory_framework", rules)
        result = framework.validate(large_dataset, validation_context)
        
        assert result.success is True
    
    def test_framework_statistics(self, sample_cdr_data, validation_context):
        """测试框架统计信息"""
        rules = [
            ConcreteValidationRule("rule1", should_pass=True),
            ConcreteValidationRule("rule2", should_pass=False),
            ConcreteValidationRule("rule3", should_pass=True)
        ]
        
        framework = ValidationFramework("stats_framework", rules)
        result = framework.validate(sample_cdr_data, validation_context)
        
        stats = framework.get_statistics()
        
        assert stats["total_validations"] == 1
        assert stats["total_rules_executed"] == 3
        assert stats["success_rate"] == 0.0  # 因为有一个规则失败
        assert "average_execution_time" in stats
    
    def test_framework_reset_statistics(self, sample_cdr_data, validation_context):
        """测试重置统计信息"""
        rule = ConcreteValidationRule("test_rule")
        framework = ValidationFramework("reset_framework", [rule])
        
        # 执行验证
        framework.validate(sample_cdr_data, validation_context)
        
        # 检查统计信息
        stats_before = framework.get_statistics()
        assert stats_before["total_validations"] == 1
        
        # 重置统计信息
        framework.reset_statistics()
        
        # 检查重置后的统计信息
        stats_after = framework.get_statistics()
        assert stats_after["total_validations"] == 0
        assert stats_after["total_rules_executed"] == 0
    
    def test_framework_str_repr(self):
        """测试字符串表示"""
        framework = ValidationFramework("test_framework", [])
        
        assert str(framework) == "ValidationFramework(test_framework, 0 rules)"
        assert "test_framework" in repr(framework)


class TestValidationEnums:
    """验证枚举测试类"""
    
    def test_validation_type_enum(self):
        """测试ValidationType枚举"""
        assert ValidationType.STRUCTURE.value == "structure"
        assert ValidationType.VALUE.value == "value"
        assert ValidationType.BUSINESS_LOGIC.value == "business_logic"
        assert ValidationType.SCHEMA.value == "schema"
        assert ValidationType.FILE.value == "file"
    
    def test_validation_severity_enum(self):
        """测试ValidationSeverity枚举"""
        assert ValidationSeverity.INFO.value == "info"
        assert ValidationSeverity.WARNING.value == "warning"
        assert ValidationSeverity.ERROR.value == "error"
        assert ValidationSeverity.CRITICAL.value == "critical"
    
    def test_severity_comparison(self):
        """测试严重程度比较"""
        # 测试严重程度的数值比较
        severities = [
            ValidationSeverity.INFO,
            ValidationSeverity.WARNING,
            ValidationSeverity.ERROR,
            ValidationSeverity.CRITICAL
        ]
        
        # 验证顺序
        for i in range(len(severities) - 1):
            assert severities[i].value < severities[i + 1].value or True  # 字符串比较


if __name__ == "__main__":
    pytest.main([__file__])