"""Example usage of performance monitoring and decorators.

This example demonstrates how to use the MetricsCollector,
performance decorators, and general-purpose decorators in real scenarios.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List

from src.database.exceptions import ConnectionError, TimeoutError
from src.database.monitoring.metrics import MetricsCollector, get_metrics_collector
from src.database.utils.decorators import (
    cache_result,
    log_calls,
    rate_limit,
    retry,
    timeout,
    validate_args,
)
from src.database.utils.performance import (
    PerformanceMonitor,
    PerformanceTimer,
    time_execution,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# Example 1: Using MetricsCollector
def metrics_collector_example():
    """Demonstrate MetricsCollector usage."""
    print("\n=== MetricsCollector Example ===")

    # Get the global metrics collector
    collector = get_metrics_collector()

    # Simulate some database operations
    collector.record_query_execution(
        query="SELECT * FROM users WHERE active = true",
        execution_time=0.125,
        rows_affected=150,
        success=True,
    )

    collector.record_query_execution(
        query="UPDATE users SET last_login = NOW() WHERE id = $1",
        execution_time=0.045,
        rows_affected=1,
        success=True,
    )

    collector.record_query_execution(
        query="SELECT * FROM non_existent_table",
        execution_time=0.012,
        rows_affected=0,
        success=False,
        error_message="Table 'non_existent_table' doesn't exist",
    )

    # Simulate pool metrics
    collector.collect_pool_metrics({"size": 20, "used": 8, "free": 12, "waiting": 2})

    # Get and display summary
    summary = collector.get_metrics_summary()
    print(f"Total queries: {summary.total_queries}")
    print(f"Successful queries: {summary.successful_queries}")
    print(f"Failed queries: {summary.failed_queries}")
    print(f"Average execution time: {summary.average_execution_time:.3f}s")
    print(f"Total rows affected: {summary.total_rows_affected}")
    print(f"Recent pool metrics: {len(summary.recent_pool_metrics)}")


# Example 2: Using @time_execution decorator
@time_execution
def simulate_database_query(query: str, delay: float = 0.1) -> Dict[str, Any]:
    """Simulate a database query with timing."""
    time.sleep(delay)  # Simulate query execution time
    return {"query": query, "rows": 42, "execution_time": delay}


@time_execution
async def simulate_async_database_query(
    query: str, delay: float = 0.1
) -> Dict[str, Any]:
    """Simulate an async database query with timing."""
    await asyncio.sleep(delay)  # Simulate async query execution time
    return {"query": query, "rows": 24, "execution_time": delay}


def performance_decorator_example():
    """Demonstrate @time_execution decorator."""
    print("\n=== Performance Decorator Example ===")

    # Sync function timing
    result = simulate_database_query("SELECT * FROM products", 0.05)
    print(f"Sync query result: {result['rows']} rows")

    # Performance timer context manager
    with PerformanceTimer("manual_operation") as timer:
        time.sleep(0.03)
        print(f"Manual operation completed")
    print(f"Manual operation took: {timer.elapsed_time:.3f}s")


async def async_performance_example():
    """Demonstrate async performance monitoring."""
    print("\n=== Async Performance Example ===")

    # Async function timing
    result = await simulate_async_database_query("SELECT * FROM orders", 0.08)
    print(f"Async query result: {result['rows']} rows")


# Example 3: Using @retry decorator
@retry(max_attempts=3, delay=0.1, backoff=2.0, exceptions=(ConnectionError,))
def unreliable_database_connection() -> str:
    """Simulate an unreliable database connection."""
    import random

    if random.random() < 0.7:  # 70% chance of failure
        raise ConnectionError("Database connection failed")

    return "Connected successfully"


@retry(
    max_attempts=5, delay=0.05, jitter=True, exceptions=(TimeoutError, ConnectionError)
)
async def unreliable_async_operation() -> str:
    """Simulate an unreliable async operation."""
    import random

    await asyncio.sleep(0.01)  # Simulate some work

    if random.random() < 0.6:  # 60% chance of failure
        if random.random() < 0.5:
            raise ConnectionError("Connection lost")
        else:
            raise TimeoutError("Operation timed out")

    return "Operation completed successfully"


def retry_decorator_example():
    """Demonstrate @retry decorator."""
    print("\n=== Retry Decorator Example ===")

    try:
        result = unreliable_database_connection()
        print(f"Connection result: {result}")
    except ConnectionError as e:
        print(f"Connection failed after retries: {e}")


async def async_retry_example():
    """Demonstrate async retry decorator."""
    print("\n=== Async Retry Example ===")

    try:
        result = await unreliable_async_operation()
        print(f"Async operation result: {result}")
    except (ConnectionError, TimeoutError) as e:
        print(f"Async operation failed after retries: {e}")


# Example 4: Using @timeout decorator
@timeout(0.2)
async def slow_database_operation() -> str:
    """Simulate a slow database operation with timeout."""
    await asyncio.sleep(0.5)  # This will timeout
    return "Operation completed"


@timeout(0.5)
async def fast_database_operation() -> str:
    """Simulate a fast database operation with timeout."""
    await asyncio.sleep(0.1)  # This will complete
    return "Fast operation completed"


async def timeout_decorator_example():
    """Demonstrate @timeout decorator."""
    print("\n=== Timeout Decorator Example ===")

    # Fast operation (should succeed)
    try:
        result = await fast_database_operation()
        print(f"Fast operation: {result}")
    except TimeoutError as e:
        print(f"Fast operation timed out: {e}")

    # Slow operation (should timeout)
    try:
        result = await slow_database_operation()
        print(f"Slow operation: {result}")
    except TimeoutError as e:
        print(f"Slow operation timed out: {e}")


# Example 5: Using @validate_args decorator
@validate_args(
    lambda user_id: isinstance(user_id, int) and user_id > 0,
    lambda email: isinstance(email, str) and "@" in email,
)
def update_user_email(user_id: int, email: str) -> str:
    """Update user email with validation."""
    return f"Updated user {user_id} email to {email}"


def validation_decorator_example():
    """Demonstrate @validate_args decorator."""
    print("\n=== Validation Decorator Example ===")

    # Valid arguments
    try:
        result = update_user_email(123, "<EMAIL>")
        print(f"Valid update: {result}")
    except ValueError as e:
        print(f"Validation error: {e}")

    # Invalid arguments
    try:
        result = update_user_email(-1, "invalid-email")
        print(f"Invalid update: {result}")
    except ValueError as e:
        print(f"Validation error: {e}")


# Example 6: Using @cache_result decorator
@cache_result(ttl=2.0, maxsize=50)
def expensive_calculation(n: int) -> int:
    """Simulate an expensive calculation with caching."""
    print(f"Performing expensive calculation for {n}")
    time.sleep(0.1)  # Simulate expensive operation
    return n * n * n


def cache_decorator_example():
    """Demonstrate @cache_result decorator."""
    print("\n=== Cache Decorator Example ===")

    # First call (should execute)
    start_time = time.time()
    result1 = expensive_calculation(5)
    time1 = time.time() - start_time
    print(f"First call result: {result1}, time: {time1:.3f}s")

    # Second call (should hit cache)
    start_time = time.time()
    result2 = expensive_calculation(5)
    time2 = time.time() - start_time
    print(f"Second call result: {result2}, time: {time2:.3f}s")

    # Cache info
    cache_info = expensive_calculation.cache_info()
    print(f"Cache info: {cache_info}")


# Example 7: Using @rate_limit decorator
@rate_limit(calls_per_second=2.0)
def api_call(endpoint: str) -> str:
    """Simulate an API call with rate limiting."""
    return f"Called {endpoint} at {time.time():.2f}"


def rate_limit_decorator_example():
    """Demonstrate @rate_limit decorator."""
    print("\n=== Rate Limit Decorator Example ===")

    # Make multiple calls (should be rate limited)
    for i in range(3):
        result = api_call(f"/api/endpoint/{i}")
        print(f"API call {i+1}: {result}")


# Example 8: Using PerformanceMonitor
def performance_monitor_example():
    """Demonstrate PerformanceMonitor usage."""
    print("\n=== Performance Monitor Example ===")

    monitor = PerformanceMonitor()

    # Monitor different operations
    operations = ["query_users", "update_profile", "delete_cache"]

    for op in operations:
        for i in range(3):
            monitor.start_operation(op)
            time.sleep(0.02 + i * 0.01)  # Variable execution time
            monitor.end_operation(op)

    # Get and display statistics
    stats = monitor.get_stats()
    for operation, data in stats.items():
        avg_time = data["total_time"] / data["count"]
        print(f"{operation}: {data['count']} calls, avg time: {avg_time:.3f}s")


# Example 9: Using @log_calls decorator
@log_calls(include_args=True, include_result=True, max_arg_length=50)
def database_transaction(operation: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """Simulate a database transaction with logging."""
    time.sleep(0.05)  # Simulate transaction time
    return {
        "operation": operation,
        "status": "success",
        "affected_rows": len(data),
        "timestamp": time.time(),
    }


def logging_decorator_example():
    """Demonstrate @log_calls decorator."""
    print("\n=== Logging Decorator Example ===")

    # This will log the function call and result
    result = database_transaction(
        "INSERT", {"name": "John", "email": "<EMAIL>"}
    )
    print(f"Transaction result: {result['status']}")


async def automatic_metrics_collection_example():
    """Demonstrate automatic metrics collection."""
    print("\n=== Automatic Metrics Collection Example ===")

    collector = get_metrics_collector()

    # Mock pool manager for demonstration
    class MockPoolManager:
        def __init__(self):
            self.stats = {"size": 10, "used": 0, "free": 10, "waiting": 0}

        def get_pool_stats(self):
            # Simulate changing pool usage
            import random

            self.stats["used"] = random.randint(0, 8)
            self.stats["free"] = self.stats["size"] - self.stats["used"]
            self.stats["waiting"] = random.randint(0, 3)
            return self.stats

    mock_pool = MockPoolManager()

    # Start automatic collection
    await collector.start_collection(mock_pool, interval=0.5)
    print("Started automatic metrics collection...")

    # Simulate some database operations while collection is running
    for i in range(5):
        await asyncio.sleep(0.3)
        collector.record_query_execution(
            query=f"SELECT * FROM table_{i}",
            execution_time=0.02 + i * 0.01,
            rows_affected=10 + i,
            success=True,
        )
        print(f"Recorded query {i+1}")

    # Stop collection
    await collector.stop_collection()
    print("Stopped automatic metrics collection")

    # Show final summary
    summary = collector.get_metrics_summary()
    print(
        f"Final summary: {summary.total_queries} queries, {len(summary.recent_pool_metrics)} pool snapshots"
    )


async def main():
    """Run all examples."""
    print("Performance Monitoring and Decorators Examples")
    print("=" * 50)

    # Run synchronous examples
    metrics_collector_example()
    performance_decorator_example()
    retry_decorator_example()
    validation_decorator_example()
    cache_decorator_example()
    rate_limit_decorator_example()
    performance_monitor_example()
    logging_decorator_example()

    # Run asynchronous examples
    await async_performance_example()
    await async_retry_example()
    await timeout_decorator_example()
    await automatic_metrics_collection_example()

    print("\n=== All Examples Completed ===")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
