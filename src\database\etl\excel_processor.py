"""Excel file processing for ETL operations.

This module provides comprehensive Excel file processing capabilities including
reading from multiple sheets, writing with formatting, and data validation.
"""

from dataclasses import dataclass, field
from datetime import date, datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from loguru import logger

try:
    import openpyxl
    from openpyxl.chart import BarChart, Reference
    from openpyxl.styles import Alignment, Border, Font, PatternFill, Side
    from openpyxl.utils.dataframe import dataframe_to_rows

    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    logger.warning("openpyxl not available. Excel writing features will be limited.")

try:
    import xlsxwriter

    XLSXWRITER_AVAILABLE = True
except ImportError:
    XLSXWRITER_AVAILABLE = False
    logger.warning(
        "xlsxwriter not available. Advanced Excel formatting will be limited."
    )

from ..exceptions import ProcessingError, ValidationError
from ..utils.progress_tracker import ProgressTracker


@dataclass
class ExcelReadOptions:
    """Options for reading Excel files."""

    sheet_name: Optional[Union[str, int, List[Union[str, int]]]] = None
    header: Optional[Union[int, List[int]]] = 0
    index_col: Optional[Union[int, str, List[Union[int, str]]]] = None
    usecols: Optional[Union[str, List[Union[int, str]]]] = None
    skiprows: Optional[Union[int, List[int]]] = None
    nrows: Optional[int] = None
    na_values: Optional[List[str]] = None
    converters: Optional[Dict[str, callable]] = None
    dtype: Optional[Dict[str, str]] = None
    parse_dates: Optional[Union[bool, List[str]]] = False
    date_parser: Optional[callable] = None
    thousands: Optional[str] = None
    decimal: str = "."
    comment: Optional[str] = None
    skipfooter: int = 0
    convert_float: bool = True
    mangle_dupe_cols: bool = True
    storage_options: Optional[Dict[str, Any]] = None


@dataclass
class ExcelWriteOptions:
    """Options for writing Excel files."""

    sheet_name: str = "Sheet1"
    index: bool = True
    header: bool = True
    startrow: int = 0
    startcol: int = 0
    engine: Optional[str] = None
    merge_cells: bool = True
    encoding: Optional[str] = None
    inf_rep: str = "inf"
    verbose: bool = True
    freeze_panes: Optional[Tuple[int, int]] = None
    storage_options: Optional[Dict[str, Any]] = None


@dataclass
class CellStyle:
    """Cell styling options."""

    font_name: str = "Calibri"
    font_size: int = 11
    font_bold: bool = False
    font_italic: bool = False
    font_color: str = "000000"
    fill_color: Optional[str] = None
    fill_pattern: str = "solid"
    border_style: str = "thin"
    border_color: str = "000000"
    alignment_horizontal: str = "general"
    alignment_vertical: str = "bottom"
    number_format: str = "General"
    wrap_text: bool = False


@dataclass
class ExcelProcessingResult:
    """Result of Excel processing operation."""

    success: bool
    data: Optional[Union[pd.DataFrame, Dict[str, pd.DataFrame]]] = None
    file_path: Optional[str] = None
    sheets_processed: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)
    execution_time: Optional[float] = None


class ExcelProcessor:
    """Excel file processor for ETL operations."""

    def __init__(self):
        """Initialize Excel processor."""
        self.supported_extensions = [".xlsx", ".xls", ".xlsm", ".xlsb"]

    def read_excel(
        self,
        file_path: Union[str, Path],
        options: Optional[ExcelReadOptions] = None,
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> ExcelProcessingResult:
        """Read Excel file.

        Args:
            file_path: Path to Excel file
            options: Read options
            progress_tracker: Optional progress tracker

        Returns:
            ExcelProcessingResult
        """
        try:
            start_time = datetime.now()
            file_path = Path(file_path)

            if not file_path.exists():
                raise ProcessingError(f"Excel file not found: {file_path}")

            if file_path.suffix.lower() not in self.supported_extensions:
                raise ProcessingError(f"Unsupported file extension: {file_path.suffix}")

            if options is None:
                options = ExcelReadOptions()

            logger.info(f"Reading Excel file: {file_path}")

            if progress_tracker:
                progress_tracker.start_task(f"Reading Excel file: {file_path.name}", 1)

            # Prepare pandas read_excel arguments
            read_args = {
                "io": str(file_path),
                "sheet_name": options.sheet_name,
                "header": options.header,
                "index_col": options.index_col,
                "usecols": options.usecols,
                "skiprows": options.skiprows,
                "nrows": options.nrows,
                "na_values": options.na_values,
                "converters": options.converters,
                "dtype": options.dtype,
                "parse_dates": options.parse_dates,
                "date_parser": options.date_parser,
                "thousands": options.thousands,
                "decimal": options.decimal,
                "comment": options.comment,
                "skipfooter": options.skipfooter,
                "convert_float": options.convert_float,
                "mangle_dupe_cols": options.mangle_dupe_cols,
                "storage_options": options.storage_options,
            }

            # Remove None values
            read_args = {k: v for k, v in read_args.items() if v is not None}

            # Read Excel file
            data = pd.read_excel(**read_args)

            # Determine sheets processed
            if isinstance(data, dict):
                sheets_processed = list(data.keys())
                total_rows = sum(len(df) for df in data.values())
                total_cols = sum(len(df.columns) for df in data.values())
            else:
                sheets_processed = [options.sheet_name or "Sheet1"]
                total_rows = len(data)
                total_cols = len(data.columns)

            if progress_tracker:
                progress_tracker.update_progress(1)
                progress_tracker.complete_task()

            execution_time = (datetime.now() - start_time).total_seconds()

            logger.info(f"Successfully read Excel file: {file_path}")

            return ExcelProcessingResult(
                success=True,
                data=data,
                file_path=str(file_path),
                sheets_processed=sheets_processed,
                statistics={
                    "total_rows": total_rows,
                    "total_columns": total_cols,
                    "sheets_count": len(sheets_processed),
                    "file_size_bytes": file_path.stat().st_size,
                },
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Failed to read Excel file {file_path}: {e}")
            return ExcelProcessingResult(success=False, errors=[str(e)])

    def write_excel(
        self,
        data: Union[pd.DataFrame, Dict[str, pd.DataFrame]],
        file_path: Union[str, Path],
        options: Optional[ExcelWriteOptions] = None,
        styles: Optional[Dict[str, CellStyle]] = None,
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> ExcelProcessingResult:
        """Write data to Excel file.

        Args:
            data: DataFrame or dict of DataFrames to write
            file_path: Output file path
            options: Write options
            styles: Cell styles
            progress_tracker: Optional progress tracker

        Returns:
            ExcelProcessingResult
        """
        try:
            start_time = datetime.now()
            file_path = Path(file_path)

            # Create directory if it doesn't exist
            file_path.parent.mkdir(parents=True, exist_ok=True)

            if options is None:
                options = ExcelWriteOptions()

            logger.info(f"Writing Excel file: {file_path}")

            # Determine number of sheets
            if isinstance(data, dict):
                sheets_to_write = list(data.keys())
                total_sheets = len(sheets_to_write)
            else:
                sheets_to_write = [options.sheet_name]
                total_sheets = 1

            if progress_tracker:
                progress_tracker.start_task(
                    f"Writing Excel file: {file_path.name}", total_sheets
                )

            # Write using ExcelWriter
            with pd.ExcelWriter(str(file_path), engine=options.engine) as writer:
                if isinstance(data, dict):
                    for i, (sheet_name, df) in enumerate(data.items()):
                        self._write_sheet(df, writer, sheet_name, options, styles)

                        if progress_tracker:
                            progress_tracker.update_progress(i + 1)
                else:
                    self._write_sheet(data, writer, options.sheet_name, options, styles)

                    if progress_tracker:
                        progress_tracker.update_progress(1)

            # Apply additional formatting if openpyxl is available
            if OPENPYXL_AVAILABLE and styles:
                self._apply_custom_formatting(file_path, data, styles)

            if progress_tracker:
                progress_tracker.complete_task()

            execution_time = (datetime.now() - start_time).total_seconds()

            # Calculate statistics
            if isinstance(data, dict):
                total_rows = sum(len(df) for df in data.values())
                total_cols = sum(len(df.columns) for df in data.values())
            else:
                total_rows = len(data)
                total_cols = len(data.columns)

            logger.info(f"Successfully wrote Excel file: {file_path}")

            return ExcelProcessingResult(
                success=True,
                file_path=str(file_path),
                sheets_processed=sheets_to_write,
                statistics={
                    "total_rows": total_rows,
                    "total_columns": total_cols,
                    "sheets_count": total_sheets,
                    "file_size_bytes": file_path.stat().st_size
                    if file_path.exists()
                    else 0,
                },
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Failed to write Excel file {file_path}: {e}")
            return ExcelProcessingResult(success=False, errors=[str(e)])

    def _write_sheet(
        self,
        df: pd.DataFrame,
        writer: pd.ExcelWriter,
        sheet_name: str,
        options: ExcelWriteOptions,
        styles: Optional[Dict[str, CellStyle]] = None,
    ):
        """Write single sheet to Excel.

        Args:
            df: DataFrame to write
            writer: Excel writer
            sheet_name: Sheet name
            options: Write options
            styles: Cell styles
        """
        # Write DataFrame
        df.to_excel(
            writer,
            sheet_name=sheet_name,
            index=options.index,
            header=options.header,
            startrow=options.startrow,
            startcol=options.startcol,
            merge_cells=options.merge_cells,
            inf_rep=options.inf_rep,
        )

        # Get worksheet for additional formatting
        worksheet = writer.sheets[sheet_name]

        # Apply freeze panes if specified
        if options.freeze_panes:
            worksheet.freeze_panes = options.freeze_panes

        # Auto-adjust column widths
        self._auto_adjust_columns(worksheet, df)

    def _auto_adjust_columns(self, worksheet, df: pd.DataFrame):
        """Auto-adjust column widths.

        Args:
            worksheet: Excel worksheet
            df: DataFrame
        """
        try:
            if hasattr(worksheet, "column_dimensions"):  # openpyxl
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if (
                                cell.value is not None
                                and len(str(cell.value)) > max_length
                            ):
                                max_length = len(str(cell.value))
                        except (AttributeError, TypeError, ValueError) as e:
                            # Log specific error for debugging
                            logger.debug(f"Error processing cell value: {e}")

                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

        except Exception as e:
            logger.warning(f"Failed to auto-adjust columns: {e}")

    def _apply_custom_formatting(
        self,
        file_path: Path,
        data: Union[pd.DataFrame, Dict[str, pd.DataFrame]],
        styles: Dict[str, CellStyle],
    ):
        """Apply custom formatting using openpyxl.

        Args:
            file_path: Excel file path
            data: Data that was written
            styles: Cell styles to apply
        """
        try:
            workbook = openpyxl.load_workbook(file_path)

            if isinstance(data, dict):
                for sheet_name, df in data.items():
                    if sheet_name in workbook.sheetnames:
                        worksheet = workbook[sheet_name]
                        self._apply_sheet_styles(worksheet, df, styles)
            else:
                worksheet = workbook.active
                self._apply_sheet_styles(worksheet, data, styles)

            workbook.save(file_path)

        except Exception as e:
            logger.warning(f"Failed to apply custom formatting: {e}")

    def _apply_sheet_styles(
        self, worksheet, df: pd.DataFrame, styles: Dict[str, CellStyle]
    ):
        """Apply styles to worksheet.

        Args:
            worksheet: Excel worksheet
            df: DataFrame
            styles: Cell styles
        """
        try:
            # Apply header style
            if "header" in styles:
                header_style = styles["header"]
                for col_num, column_title in enumerate(df.columns, 1):
                    cell = worksheet.cell(row=1, column=col_num)
                    self._apply_cell_style(cell, header_style)

            # Apply data style
            if "data" in styles:
                data_style = styles["data"]
                for row_num in range(2, len(df) + 2):
                    for col_num in range(1, len(df.columns) + 1):
                        cell = worksheet.cell(row=row_num, column=col_num)
                        self._apply_cell_style(cell, data_style)

        except Exception as e:
            logger.warning(f"Failed to apply sheet styles: {e}")

    def _apply_cell_style(self, cell, style: CellStyle):
        """Apply style to individual cell.

        Args:
            cell: Excel cell
            style: Cell style
        """
        try:
            # Font
            cell.font = Font(
                name=style.font_name,
                size=style.font_size,
                bold=style.font_bold,
                italic=style.font_italic,
                color=style.font_color,
            )

            # Fill
            if style.fill_color:
                cell.fill = PatternFill(
                    start_color=style.fill_color,
                    end_color=style.fill_color,
                    fill_type=style.fill_pattern,
                )

            # Border
            side = Side(style=style.border_style, color=style.border_color)
            cell.border = Border(left=side, right=side, top=side, bottom=side)

            # Alignment
            cell.alignment = Alignment(
                horizontal=style.alignment_horizontal,
                vertical=style.alignment_vertical,
                wrap_text=style.wrap_text,
            )

            # Number format
            cell.number_format = style.number_format

        except Exception as e:
            logger.warning(f"Failed to apply cell style: {e}")

    def get_sheet_names(self, file_path: Union[str, Path]) -> List[str]:
        """Get sheet names from Excel file.

        Args:
            file_path: Path to Excel file

        Returns:
            List of sheet names

        Raises:
            ProcessingError: If file cannot be read
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                raise ProcessingError(f"Excel file not found: {file_path}")

            if OPENPYXL_AVAILABLE:
                workbook = openpyxl.load_workbook(file_path, read_only=True)
                return workbook.sheetnames
            else:
                # Fallback using pandas
                excel_file = pd.ExcelFile(file_path)
                return excel_file.sheet_names

        except Exception as e:
            raise ProcessingError(f"Failed to get sheet names: {e}")

    def validate_excel_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Validate Excel file structure.

        Args:
            file_path: Path to Excel file

        Returns:
            Validation results
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                return {"valid": False, "errors": [f"File not found: {file_path}"]}

            if file_path.suffix.lower() not in self.supported_extensions:
                return {
                    "valid": False,
                    "errors": [f"Unsupported file extension: {file_path.suffix}"],
                }

            # Try to read file info
            sheet_names = self.get_sheet_names(file_path)
            file_size = file_path.stat().st_size

            # Basic validation
            errors = []
            warnings = []

            if file_size == 0:
                errors.append("File is empty")

            if not sheet_names:
                errors.append("No sheets found in file")

            if file_size > 100 * 1024 * 1024:  # 100MB
                warnings.append("Large file size may impact performance")

            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings,
                "info": {
                    "file_size_bytes": file_size,
                    "sheet_count": len(sheet_names),
                    "sheet_names": sheet_names,
                    "file_extension": file_path.suffix,
                },
            }

        except Exception as e:
            return {"valid": False, "errors": [f"Validation failed: {e}"]}

    def merge_excel_files(
        self,
        file_paths: List[Union[str, Path]],
        output_path: Union[str, Path],
        merge_strategy: str = "sheets",
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> ExcelProcessingResult:
        """Merge multiple Excel files.

        Args:
            file_paths: List of Excel file paths
            output_path: Output file path
            merge_strategy: 'sheets' (separate sheets) or 'concat' (concatenate data)
            progress_tracker: Optional progress tracker

        Returns:
            ExcelProcessingResult
        """
        try:
            start_time = datetime.now()
            output_path = Path(output_path)

            if not file_paths:
                raise ProcessingError("No files provided for merging")

            logger.info(f"Merging {len(file_paths)} Excel files")

            if progress_tracker:
                progress_tracker.start_task("Merging Excel files", len(file_paths))

            merged_data = {}
            total_rows = 0
            total_cols = 0

            for i, file_path in enumerate(file_paths):
                file_path = Path(file_path)

                if not file_path.exists():
                    logger.warning(f"File not found, skipping: {file_path}")
                    continue

                logger.info(f"Processing file: {file_path.name}")

                # Read all sheets from file
                data = pd.read_excel(file_path, sheet_name=None)

                if merge_strategy == "sheets":
                    # Add each sheet with file prefix
                    for sheet_name, df in data.items():
                        new_sheet_name = f"{file_path.stem}_{sheet_name}"
                        merged_data[new_sheet_name] = df
                        total_rows += len(df)
                        total_cols += len(df.columns)

                elif merge_strategy == "concat":
                    # Concatenate all data
                    if "merged_data" not in merged_data:
                        merged_data["merged_data"] = []

                    for sheet_name, df in data.items():
                        # Add source file column
                        df["source_file"] = file_path.name
                        df["source_sheet"] = sheet_name
                        merged_data["merged_data"].append(df)
                        total_rows += len(df)
                        total_cols = max(total_cols, len(df.columns))

                if progress_tracker:
                    progress_tracker.update_progress(i + 1)

            # Finalize concatenation if needed
            if merge_strategy == "concat" and "merged_data" in merged_data:
                merged_data["merged_data"] = pd.concat(
                    merged_data["merged_data"], ignore_index=True, sort=False
                )

            # Write merged data
            write_result = self.write_excel(merged_data, output_path)

            if progress_tracker:
                progress_tracker.complete_task()

            execution_time = (datetime.now() - start_time).total_seconds()

            if write_result.success:
                logger.info(f"Successfully merged Excel files to: {output_path}")

                return ExcelProcessingResult(
                    success=True,
                    file_path=str(output_path),
                    sheets_processed=list(merged_data.keys()),
                    statistics={
                        "input_files": len(file_paths),
                        "total_rows": total_rows,
                        "total_columns": total_cols,
                        "output_sheets": len(merged_data),
                        "merge_strategy": merge_strategy,
                    },
                    execution_time=execution_time,
                )
            else:
                return write_result

        except Exception as e:
            logger.error(f"Failed to merge Excel files: {e}")
            return ExcelProcessingResult(success=False, errors=[str(e)])

    def split_excel_file(
        self,
        file_path: Union[str, Path],
        output_dir: Union[str, Path],
        split_by: str = "sheet",
        split_column: Optional[str] = None,
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> ExcelProcessingResult:
        """Split Excel file into multiple files.

        Args:
            file_path: Input Excel file path
            output_dir: Output directory
            split_by: 'sheet' or 'column'
            split_column: Column to split by (if split_by='column')
            progress_tracker: Optional progress tracker

        Returns:
            ExcelProcessingResult
        """
        try:
            start_time = datetime.now()
            file_path = Path(file_path)
            output_dir = Path(output_dir)

            if not file_path.exists():
                raise ProcessingError(f"Input file not found: {file_path}")

            output_dir.mkdir(parents=True, exist_ok=True)

            logger.info(f"Splitting Excel file: {file_path}")

            # Read all sheets
            data = pd.read_excel(file_path, sheet_name=None)

            output_files = []
            total_files = 0

            if split_by == "sheet":
                total_files = len(data)

                if progress_tracker:
                    progress_tracker.start_task("Splitting by sheets", total_files)

                for i, (sheet_name, df) in enumerate(data.items()):
                    output_file = output_dir / f"{file_path.stem}_{sheet_name}.xlsx"

                    write_result = self.write_excel(df, output_file)
                    if write_result.success:
                        output_files.append(str(output_file))

                    if progress_tracker:
                        progress_tracker.update_progress(i + 1)

            elif split_by == "column":
                if not split_column:
                    raise ProcessingError("Split column must be specified")

                # Process each sheet
                for sheet_name, df in data.items():
                    if split_column not in df.columns:
                        logger.warning(
                            f"Split column '{split_column}' not found in sheet '{sheet_name}'"
                        )
                        continue

                    unique_values = df[split_column].unique()
                    total_files += len(unique_values)

                if progress_tracker:
                    progress_tracker.start_task("Splitting by column", total_files)

                file_count = 0
                for sheet_name, df in data.items():
                    if split_column not in df.columns:
                        continue

                    unique_values = df[split_column].unique()

                    for value in unique_values:
                        filtered_df = df[df[split_column] == value]
                        output_file = (
                            output_dir / f"{file_path.stem}_{sheet_name}_{value}.xlsx"
                        )

                        write_result = self.write_excel(filtered_df, output_file)
                        if write_result.success:
                            output_files.append(str(output_file))

                        file_count += 1
                        if progress_tracker:
                            progress_tracker.update_progress(file_count)

            else:
                raise ProcessingError(f"Unknown split method: {split_by}")

            if progress_tracker:
                progress_tracker.complete_task()

            execution_time = (datetime.now() - start_time).total_seconds()

            logger.info(f"Successfully split Excel file into {len(output_files)} files")

            return ExcelProcessingResult(
                success=True,
                statistics={
                    "input_file": str(file_path),
                    "output_files": output_files,
                    "split_method": split_by,
                    "split_column": split_column,
                    "files_created": len(output_files),
                },
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Failed to split Excel file: {e}")
            return ExcelProcessingResult(success=False, errors=[str(e)])


# Convenience functions
def read_excel_file(
    file_path: Union[str, Path], sheet_name: Optional[Union[str, int]] = None, **kwargs
) -> pd.DataFrame:
    """Convenience function to read Excel file.

    Args:
        file_path: Path to Excel file
        sheet_name: Sheet name or index
        **kwargs: Additional pandas read_excel arguments

    Returns:
        DataFrame
    """
    processor = ExcelProcessor()
    options = ExcelReadOptions(sheet_name=sheet_name, **kwargs)
    result = processor.read_excel(file_path, options)

    if not result.success:
        raise ProcessingError(f"Failed to read Excel file: {result.errors}")

    return result.data


def write_excel_file(
    data: Union[pd.DataFrame, Dict[str, pd.DataFrame]],
    file_path: Union[str, Path],
    **kwargs,
) -> bool:
    """Convenience function to write Excel file.

    Args:
        data: DataFrame or dict of DataFrames
        file_path: Output file path
        **kwargs: Additional write options

    Returns:
        True if successful
    """
    processor = ExcelProcessor()
    options = ExcelWriteOptions(**kwargs)
    result = processor.write_excel(data, file_path, options)

    if not result.success:
        raise ProcessingError(f"Failed to write Excel file: {result.errors}")

    return True
