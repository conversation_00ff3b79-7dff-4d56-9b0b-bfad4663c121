__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Type definitions and aliases for the database framework.

This module defines custom types, type aliases, and type hints used
throughout the database framework to improve code clarity and type safety.
"""

from datetime import date, datetime
from decimal import Decimal
from pathlib import Path
from typing import (
    Any,
    Callable,
    Dict,
    Generic,
    List,
    Literal,
    NamedTuple,
    Optional,
    Protocol,
    Tuple,
    TypedDict,
    TypeVar,
    Union,
    runtime_checkable,
)

import pandas as pd
from sqlalchemy import Connection, Engine, Result
from sqlalchemy.pool import Pool

# Basic type aliases
JSONValue = Union[str, int, float, bool, None, Dict[str, Any], List[Any]]
JSONDict = Dict[str, JSONValue]
JSONList = List[JSONValue]

# Database-specific types
ConnectionString = str
TableName = str
ColumnName = str
SchemaName = str
IndexName = str
QueryString = str
ParameterDict = Dict[str, Any]

# File and path types
FilePath = Union[str, Path]
FileExtension = str
FileName = str

# Data processing types
DataFrame = pd.DataFrame
Series = pd.Series
ExcelFile = pd.ExcelFile

# Numeric types
Numeric = Union[int, float, Decimal]
PositiveInt = int  # Should be > 0
NonNegativeInt = int  # Should be >= 0

# Date and time types
DateLike = Union[str, date, datetime]
Timestamp = datetime
DateString = str

# Configuration types
ConfigDict = Dict[str, Any]
ConnectionParams = Dict[str, Union[str, int, bool]]
PoolParams = Dict[str, Union[int, float]]

# Error handling types
ErrorCode = str
ErrorMessage = str
ErrorDetails = Dict[str, Any]

# Callback and handler types
T = TypeVar("T")
CallbackFunction = Callable[[T], None]
ErrorHandler = Callable[[Exception], None]
ProgressCallback = Callable[[int, int], None]  # (current, total)

# SQL-related types
SQLResult = Result
SQLConnection = Connection
SQLEngine = Engine
SQLPool = Pool
RowData = Dict[str, Any]
RowList = List[RowData]

# Batch processing types
BatchSize = int
BatchData = List[Dict[str, Any]]
BatchProcessor = Callable[[BatchData], None]

# Validation types
ValidationRule = Callable[[Any], bool]
ValidationResult = Tuple[bool, Optional[str]]  # (is_valid, error_message)
Validator = Callable[[Any], ValidationResult]


# Schema definition types
class ColumnDefinition(TypedDict, total=False):
    """Type definition for database column specifications."""

    name: str
    type: str
    nullable: bool
    default: Optional[Any]
    primary_key: bool
    foreign_key: Optional[str]
    unique: bool
    index: bool
    comment: Optional[str]


class TableDefinition(TypedDict):
    """Type definition for database table specifications."""

    name: str
    schema: Optional[str]
    columns: List[ColumnDefinition]
    indexes: Optional[List[str]]
    constraints: Optional[List[str]]
    comment: Optional[str]


class DataSourceMapping(TypedDict, total=False):
    """Type definition for data source to database mapping."""

    source_column: str
    target_column: str
    data_type: str
    transformation: Optional[str]
    validation: Optional[str]


# File processing types
class FileMetadata(NamedTuple):
    """Metadata for processed files."""

    path: Path
    size: int
    modified: datetime
    checksum: str
    rows: Optional[int] = None
    columns: Optional[int] = None


class ProcessingResult(NamedTuple):
    """Result of data processing operation."""

    success: bool
    rows_processed: int
    errors: List[str]
    warnings: List[str]
    duration: float
    metadata: Optional[Dict[str, Any]] = None


class QueryResult(NamedTuple):
    """Result of a database query operation."""

    data: List[Dict[str, Any]]
    row_count: int
    columns: List[str]


class BulkOperationResult(NamedTuple):
    """Result of a bulk database operation."""

    total_processed: int
    successful: int
    failed: int
    failed_records: List[Dict[str, Any]]


# Connection and pool types
class ConnectionInfo(NamedTuple):
    """Information about a database connection."""

    host: str
    port: int
    database: str
    user: str
    connected_at: datetime
    last_activity: datetime
    transaction_count: int


class PoolStats(NamedTuple):
    """Statistics for connection pool."""

    size: int
    checked_in: int
    checked_out: int
    overflow: int
    invalid: int


# Query and execution types
class QueryMetrics(NamedTuple):
    """Metrics for query execution."""

    query: str
    duration: float
    rows_affected: int
    rows_returned: int
    executed_at: datetime
    parameters: Optional[Dict[str, Any]] = None


# Protocol definitions for type checking
@runtime_checkable
class Connectable(Protocol):
    """Protocol for objects that can provide database connections."""

    def connect(self) -> Connection:
        """Establish a database connection."""
        ...

    def execute(
        self, query: str, parameters: Optional[Dict[str, Any]] = None
    ) -> Result:
        """Execute a query."""
        ...


@runtime_checkable
class Configurable(Protocol):
    """Protocol for configurable objects."""

    def configure(self, config: ConfigDict) -> None:
        """Configure the object with provided configuration."""
        ...

    def get_config(self) -> ConfigDict:
        """Get current configuration."""
        ...


@runtime_checkable
class Validatable(Protocol):
    """Protocol for objects that can be validated."""

    def validate(self) -> ValidationResult:
        """Validate the object."""
        ...


@runtime_checkable
class DataProcessor(Protocol):
    """Protocol for data processing objects."""

    def process(self, data: Any) -> ProcessingResult:
        """Process input data."""
        ...


# Generic types for framework components
ConfigurableT = TypeVar("ConfigurableT", bound=Configurable)
ValidatableT = TypeVar("ValidatableT", bound=Validatable)
ProcessorT = TypeVar("ProcessorT", bound=DataProcessor)

# Literal types for specific values
LogLevel = Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
IsolationLevel = Literal[
    "READ_UNCOMMITTED", "READ_COMMITTED", "REPEATABLE_READ", "SERIALIZABLE"
]
DataSourceType = Literal["ep", "cdr", "nlg", "kpi", "score", "cfg"]
Operator = Literal["telefonica", "vodafone", "telekom"]
CellType = Literal["gsm", "umts", "lte", "nr"]
ServiceType = Literal["voice", "sms", "data", "m2m"]
Priority = Literal["P0", "P1", "P2", "P3"]

# Union types for common patterns
Identifier = Union[str, int]
OptionalString = Optional[str]
OptionalInt = Optional[int]
OptionalFloat = Optional[float]
OptionalBool = Optional[bool]
OptionalDict = Optional[Dict[str, Any]]
OptionalList = Optional[List[Any]]

# Function signature types
ConnectionFactory = Callable[[], Connection]
QueryBuilder = Callable[[Dict[str, Any]], str]
DataTransformer = Callable[[DataFrame], DataFrame]
ErrorReporter = Callable[[Exception, Dict[str, Any]], None]

# Async types (for future async support)
AsyncCallable = Callable[..., Any]  # Placeholder for async callable types

# Complex nested types
NestedConfig = Dict[str, Union[str, int, float, bool, List[Any], Dict[str, Any]]]
MultiLevelDict = Dict[str, Dict[str, Any]]
ParameterMapping = Dict[str, Union[str, Callable[[Any], Any]]]


# Type guards and type checking helpers
def is_valid_table_name(name: Any) -> bool:
    """Type guard for valid table names."""
    return isinstance(name, str) and name.isidentifier() and name.islower()


def is_valid_column_name(name: Any) -> bool:
    """Type guard for valid column names."""
    return isinstance(name, str) and name.isidentifier() and name.islower()


def is_connection_string(value: Any) -> bool:
    """Type guard for connection strings."""
    return isinstance(value, str) and "://" in value


# Export commonly used types
__all__ = [
    # Basic types
    "JSONValue",
    "JSONDict",
    "JSONList",
    "ConnectionString",
    "TableName",
    "ColumnName",
    "SchemaName",
    "IndexName",
    "QueryString",
    "ParameterDict",
    "FilePath",
    "FileExtension",
    "FileName",
    # Data types
    "DataFrame",
    "Series",
    "ExcelFile",
    "Numeric",
    "PositiveInt",
    "NonNegativeInt",
    "DateLike",
    "Timestamp",
    "DateString",
    # Configuration types
    "ConfigDict",
    "ConnectionParams",
    "PoolParams",
    # Error types
    "ErrorCode",
    "ErrorMessage",
    "ErrorDetails",
    # Callback types
    "CallbackFunction",
    "ErrorHandler",
    "ProgressCallback",
    # SQL types
    "SQLResult",
    "SQLConnection",
    "SQLEngine",
    "SQLPool",
    "RowData",
    "RowList",
    # Batch processing
    "BatchSize",
    "BatchData",
    "BatchProcessor",
    # Validation
    "ValidationRule",
    "ValidationResult",
    "Validator",
    # Schema definitions
    "ColumnDefinition",
    "TableDefinition",
    "DataSourceMapping",
    # Named tuples
    "FileMetadata",
    "ProcessingResult",
    "QueryResult",
    "BulkOperationResult",
    "ConnectionInfo",
    "PoolStats",
    "QueryMetrics",
    # Protocols
    "Connectable",
    "Configurable",
    "Validatable",
    "DataProcessor",
    # Literal types
    "LogLevel",
    "IsolationLevel",
    "DataSourceType",
    "Operator",
    "CellType",
    "ServiceType",
    "Priority",
    # Type guards
    "is_valid_table_name",
    "is_valid_column_name",
    "is_connection_string",
]
