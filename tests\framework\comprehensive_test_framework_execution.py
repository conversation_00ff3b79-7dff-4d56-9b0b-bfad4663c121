# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 综合测试框架执行模块

本模块提供测试执行、监控和报告功能：
- 测试套件执行管理
- 实时监控和度量
- 质量门控检查
- 测试报告生成
- CI/CD集成支持

作者: Connect质量工程团队
创建时间: 2024-01-20
版本: 1.0.0
"""

import os
import sys
import time
import json
import asyncio
import logging
import subprocess
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable, Union
from pathlib import Path
from dataclasses import dataclass, asdict, field
from concurrent.futures import ThreadPoolExecutor, as_completed
from contextlib import contextmanager, asynccontextmanager

import pytest
import coverage
from junit_xml import TestSuite, TestCase
import yaml

# 导入框架核心组件
from .comprehensive_test_framework import (
    ComprehensiveTestFramework, TestSuiteConfig, TestExecutionResult,
    QualityGate, TestStatus, TestPriority, TestType
)

logger = logging.getLogger(__name__)


class TestExecutionEngine:
    """测试执行引擎"""
    
    def __init__(self, framework: ComprehensiveTestFramework):
        """初始化测试执行引擎
        
        Args:
            framework: 综合测试框架实例
        """
        self.framework = framework
        self.current_execution: Optional[TestExecutionResult] = None
        self.execution_context: Dict[str, Any] = {}
        
    @contextmanager
    def test_execution_context(self, suite_config: TestSuiteConfig):
        """测试执行上下文管理器
        
        Args:
            suite_config: 测试套件配置
        """
        start_time = datetime.now()
        
        # 创建执行结果对象
        result = TestExecutionResult(
            suite_name=suite_config.name,
            status=TestStatus.RUNNING,
            start_time=start_time,
            end_time=start_time,  # 临时值
            duration=0.0,
            test_count=0,
            passed_count=0,
            failed_count=0,
            skipped_count=0,
            error_count=0
        )
        
        self.current_execution = result
        
        try:
            # 环境设置
            if suite_config.environment_setup:
                self._execute_environment_setup(suite_config)
            
            # 启动监控
            self.framework.performance_monitor.start_monitoring()
            self.framework.memory_profiler.start_monitoring()
            
            logger.info(f"Started execution of test suite: {suite_config.name}")
            
            yield result
            
        except Exception as e:
            result.status = TestStatus.ERROR
            result.error_details.append(str(e))
            logger.error(f"Test suite execution failed: {e}")
            raise
            
        finally:
            # 停止监控
            self.framework.performance_monitor.stop_monitoring()
            self.framework.memory_profiler.stop_monitoring()
            
            # 环境清理
            if suite_config.environment_teardown:
                self._execute_environment_teardown(suite_config)
            
            # 更新执行结果
            end_time = datetime.now()
            result.end_time = end_time
            result.duration = (end_time - start_time).total_seconds()
            
            # 收集性能和内存数据
            result.performance_metrics = self.framework.performance_monitor.get_metrics()
            result.memory_usage = self.framework.memory_profiler.get_report()
            
            # 添加到框架结果列表
            self.framework.execution_results.append(result)
            
            logger.info(f"Completed execution of test suite: {suite_config.name} in {result.duration:.2f}s")
    
    def _execute_environment_setup(self, suite_config: TestSuiteConfig):
        """执行环境设置
        
        Args:
            suite_config: 测试套件配置
        """
        try:
            self.framework.setup_environment(suite_config)
        except Exception as e:
            logger.error(f"Environment setup failed: {e}")
            raise
    
    def _execute_environment_teardown(self, suite_config: TestSuiteConfig):
        """执行环境清理
        
        Args:
            suite_config: 测试套件配置
        """
        try:
            self.framework.teardown_environment(suite_config)
        except Exception as e:
            logger.warning(f"Environment teardown failed: {e}")
    
    def execute_suite(self, suite_name: str, **kwargs) -> TestExecutionResult:
        """执行单个测试套件
        
        Args:
            suite_name: 测试套件名称
            **kwargs: 额外的pytest参数
            
        Returns:
            测试执行结果
        """
        suite_config = self.framework.get_suite(suite_name)
        if not suite_config:
            raise ValueError(f"Test suite '{suite_name}' not found")
        
        # 验证依赖
        is_valid, missing_deps = self.framework.validate_dependencies(suite_name)
        if not is_valid:
            raise ValueError(f"Missing dependencies for suite '{suite_name}': {missing_deps}")
        
        with self.test_execution_context(suite_config) as result:
            try:
                # 构建pytest命令
                pytest_args = self._build_pytest_args(suite_config, **kwargs)
                
                # 执行测试
                exit_code = pytest.main(pytest_args)
                
                # 解析测试结果
                self._parse_test_results(result, suite_config)
                
                # 设置状态
                if exit_code == 0:
                    result.status = TestStatus.PASSED
                elif exit_code == 1:
                    result.status = TestStatus.FAILED
                elif exit_code == 2:
                    result.status = TestStatus.ERROR
                else:
                    result.status = TestStatus.ERROR
                
            except Exception as e:
                result.status = TestStatus.ERROR
                result.error_details.append(str(e))
                logger.error(f"Test execution failed: {e}")
        
        return result
    
    def execute_suites(self, suite_names: Optional[List[str]] = None, 
                      parallel: bool = False, **kwargs) -> List[TestExecutionResult]:
        """执行多个测试套件
        
        Args:
            suite_names: 要执行的套件名称列表，None表示所有套件
            parallel: 是否并行执行
            **kwargs: 额外的pytest参数
            
        Returns:
            测试执行结果列表
        """
        if suite_names is None:
            suite_names = list(self.framework.test_suites.keys())
        
        # 获取执行顺序
        execution_order = self.framework.get_execution_order(suite_names)
        
        results = []
        
        if parallel:
            # 并行执行（仅限于没有依赖关系的套件）
            results = self._execute_suites_parallel(execution_order, **kwargs)
        else:
            # 串行执行
            results = self._execute_suites_sequential(execution_order, **kwargs)
        
        return results
    
    def _execute_suites_sequential(self, suite_names: List[str], **kwargs) -> List[TestExecutionResult]:
        """串行执行测试套件
        
        Args:
            suite_names: 套件名称列表
            **kwargs: 额外的pytest参数
            
        Returns:
            测试执行结果列表
        """
        results = []
        
        for suite_name in suite_names:
            try:
                result = self.execute_suite(suite_name, **kwargs)
                results.append(result)
                
                # 如果关键测试失败，可能需要停止后续执行
                suite_config = self.framework.get_suite(suite_name)
                if (suite_config and suite_config.priority in [TestPriority.P0, TestPriority.P1] 
                    and result.status == TestStatus.FAILED):
                    logger.warning(f"Critical test suite '{suite_name}' failed, considering stopping execution")
                    
            except Exception as e:
                logger.error(f"Failed to execute suite '{suite_name}': {e}")
                # 创建错误结果
                error_result = TestExecutionResult(
                    suite_name=suite_name,
                    status=TestStatus.ERROR,
                    start_time=datetime.now(),
                    end_time=datetime.now(),
                    duration=0.0,
                    test_count=0,
                    passed_count=0,
                    failed_count=0,
                    skipped_count=0,
                    error_count=1,
                    error_details=[str(e)]
                )
                results.append(error_result)
        
        return results
    
    def _execute_suites_parallel(self, suite_names: List[str], **kwargs) -> List[TestExecutionResult]:
        """并行执行测试套件
        
        Args:
            suite_names: 套件名称列表
            **kwargs: 额外的pytest参数
            
        Returns:
            测试执行结果列表
        """
        results = []
        
        # 分组：有依赖的串行执行，无依赖的并行执行
        independent_suites = []
        dependent_suites = []
        
        for suite_name in suite_names:
            suite_config = self.framework.get_suite(suite_name)
            if suite_config and not suite_config.dependencies:
                independent_suites.append(suite_name)
            else:
                dependent_suites.append(suite_name)
        
        # 并行执行独立套件
        if independent_suites:
            with ThreadPoolExecutor(max_workers=min(len(independent_suites), 4)) as executor:
                future_to_suite = {
                    executor.submit(self.execute_suite, suite_name, **kwargs): suite_name
                    for suite_name in independent_suites
                }
                
                for future in as_completed(future_to_suite):
                    suite_name = future_to_suite[future]
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        logger.error(f"Parallel execution failed for suite '{suite_name}': {e}")
        
        # 串行执行有依赖的套件
        if dependent_suites:
            dependent_results = self._execute_suites_sequential(dependent_suites, **kwargs)
            results.extend(dependent_results)
        
        return results
    
    def _build_pytest_args(self, suite_config: TestSuiteConfig, **kwargs) -> List[str]:
        """构建pytest命令参数
        
        Args:
            suite_config: 测试套件配置
            **kwargs: 额外参数
            
        Returns:
            pytest参数列表
        """
        args = []
        
        # 添加测试路径
        for path in suite_config.test_paths:
            if Path(path).exists():
                args.append(str(path))
        
        # 添加标记过滤
        if suite_config.markers:
            marker_expr = " or ".join(suite_config.markers)
            args.extend(["-m", marker_expr])
        
        # 添加并行执行
        if suite_config.parallel:
            args.extend(["-n", "auto"])
        
        # 添加超时
        if suite_config.timeout:
            args.extend(["--timeout", str(suite_config.timeout)])
        
        # 添加重试
        if suite_config.retry_count > 0:
            args.extend(["--reruns", str(suite_config.retry_count)])
        
        # 添加覆盖率
        args.extend(["--cov", "src", "--cov-report", "xml", "--cov-report", "html"])
        
        # 添加JUnit XML报告
        report_file = self.framework.work_dir / "test_reports" / f"{suite_config.name}_junit.xml"
        args.extend(["--junitxml", str(report_file)])
        
        # 添加详细输出
        args.extend(["-v", "--tb=short"])
        
        # 添加自定义参数
        for key, value in kwargs.items():
            if key.startswith("pytest_"):
                arg_name = key.replace("pytest_", "--")
                if value is True:
                    args.append(arg_name)
                elif value is not False:
                    args.extend([arg_name, str(value)])
        
        return args
    
    def _parse_test_results(self, result: TestExecutionResult, suite_config: TestSuiteConfig):
        """解析测试结果
        
        Args:
            result: 测试执行结果对象
            suite_config: 测试套件配置
        """
        # 解析JUnit XML报告
        report_file = self.framework.work_dir / "test_reports" / f"{suite_config.name}_junit.xml"
        
        if report_file.exists():
            try:
                import xml.etree.ElementTree as ET
                tree = ET.parse(report_file)
                root = tree.getroot()
                
                # 解析测试统计
                result.test_count = int(root.get('tests', 0))
                result.failed_count = int(root.get('failures', 0))
                result.error_count = int(root.get('errors', 0))
                result.skipped_count = int(root.get('skipped', 0))
                result.passed_count = result.test_count - result.failed_count - result.error_count - result.skipped_count
                
                # 收集错误详情
                for testcase in root.findall('.//testcase'):
                    failure = testcase.find('failure')
                    error = testcase.find('error')
                    
                    if failure is not None:
                        result.error_details.append(f"FAILURE in {testcase.get('name')}: {failure.text}")
                    elif error is not None:
                        result.error_details.append(f"ERROR in {testcase.get('name')}: {error.text}")
                
            except Exception as e:
                logger.warning(f"Failed to parse test results: {e}")
        
        # 解析覆盖率报告
        coverage_file = self.framework.work_dir / "coverage.xml"
        if coverage_file.exists():
            try:
                import xml.etree.ElementTree as ET
                tree = ET.parse(coverage_file)
                root = tree.getroot()
                
                # 获取总体覆盖率
                coverage_elem = root.find('.//coverage')
                if coverage_elem is not None:
                    line_rate = coverage_elem.get('line-rate')
                    if line_rate:
                        result.coverage_percentage = float(line_rate) * 100
                
            except Exception as e:
                logger.warning(f"Failed to parse coverage report: {e}")


class QualityGateChecker:
    """质量门控检查器"""
    
    def __init__(self, framework: ComprehensiveTestFramework):
        """初始化质量门控检查器
        
        Args:
            framework: 综合测试框架实例
        """
        self.framework = framework
    
    def check_quality_gate(self, gate_name: str, 
                          execution_results: List[TestExecutionResult]) -> Tuple[bool, Dict[str, Any]]:
        """检查质量门控
        
        Args:
            gate_name: 质量门控名称
            execution_results: 测试执行结果列表
            
        Returns:
            (是否通过, 检查详情)
        """
        gate = self.framework.get_quality_gate(gate_name)
        if not gate:
            return False, {"error": f"Quality gate '{gate_name}' not found"}
        
        check_results = {
            "gate_name": gate_name,
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "overall_passed": True,
            "blocking": gate.blocking
        }
        
        # 检查覆盖率
        coverage_passed = self._check_coverage(gate, execution_results)
        check_results["checks"]["coverage"] = coverage_passed
        if not coverage_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查成功率
        success_rate_passed = self._check_success_rate(gate, execution_results)
        check_results["checks"]["success_rate"] = success_rate_passed
        if not success_rate_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查性能指标
        performance_passed = self._check_performance(gate, execution_results)
        check_results["checks"]["performance"] = performance_passed
        if not performance_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查安全性
        security_passed = self._check_security(gate, execution_results)
        check_results["checks"]["security"] = security_passed
        if not security_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 执行自定义检查
        custom_passed = self._check_custom(gate, execution_results)
        check_results["checks"]["custom"] = custom_passed
        if not custom_passed["passed"]:
            check_results["overall_passed"] = False
        
        return check_results["overall_passed"], check_results
    
    def _check_coverage(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查代码覆盖率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            覆盖率检查结果
        """
        coverages = [r.coverage_percentage for r in execution_results 
                    if r.coverage_percentage is not None]
        
        if not coverages:
            return {
                "passed": False,
                "message": "No coverage data available",
                "threshold": gate.coverage_threshold,
                "actual": None
            }
        
        avg_coverage = sum(coverages) / len(coverages)
        passed = avg_coverage >= gate.coverage_threshold
        
        return {
            "passed": passed,
            "message": f"Coverage: {avg_coverage:.1f}% (threshold: {gate.coverage_threshold}%)",
            "threshold": gate.coverage_threshold,
            "actual": avg_coverage
        }
    
    def _check_success_rate(self, gate: QualityGate, 
                           execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查测试成功率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            成功率检查结果
        """
        if not execution_results:
            return {
                "passed": False,
                "message": "No test results available",
                "threshold": gate.success_rate_threshold,
                "actual": None
            }
        
        total_tests = sum(r.test_count for r in execution_results)
        total_passed = sum(r.passed_count for r in execution_results)
        
        if total_tests == 0:
            return {
                "passed": False,
                "message": "No tests executed",
                "threshold": gate.success_rate_threshold,
                "actual": 0.0
            }
        
        success_rate = (total_passed / total_tests) * 100
        passed = success_rate >= gate.success_rate_threshold
        
        return {
            "passed": passed,
            "message": f"Success rate: {success_rate:.1f}% (threshold: {gate.success_rate_threshold}%)",
            "threshold": gate.success_rate_threshold,
            "actual": success_rate
        }
    
    def _check_performance(self, gate: QualityGate, 
                          execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查性能指标
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            性能检查结果
        """
        if not gate.performance_thresholds:
            return {"passed": True, "message": "No performance thresholds defined"}
        
        performance_results = []
        overall_passed = True
        
        for result in execution_results:
            if result.performance_metrics:
                metrics = result.performance_metrics
                
                for metric_name, threshold in gate.performance_thresholds.items():
                    actual_value = getattr(metrics, metric_name, None)
                    
                    if actual_value is not None:
                        # 大部分性能指标是越小越好
                        if metric_name.endswith('_ops_sec') or metric_name.endswith('_throughput'):
                            # 吞吐量类指标是越大越好
                            passed = actual_value >= threshold
                        else:
                            # 响应时间、内存使用等是越小越好
                            passed = actual_value <= threshold
                        
                        performance_results.append({
                            "metric": metric_name,
                            "threshold": threshold,
                            "actual": actual_value,
                            "passed": passed
                        })
                        
                        if not passed:
                            overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Performance checks: {len([r for r in performance_results if r['passed']])}/{len(performance_results)} passed",
            "details": performance_results
        }
    
    def _check_security(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查安全性
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            安全检查结果
        """
        if not gate.security_checks:
            return {"passed": True, "message": "No security checks defined"}
        
        # 这里可以集成实际的安全扫描工具
        # 目前返回模拟结果
        security_results = []
        
        for check in gate.security_checks:
            # 模拟安全检查
            passed = True  # 实际实现中应该调用相应的安全扫描工具
            security_results.append({
                "check": check,
                "passed": passed,
                "message": f"Security check '{check}' passed" if passed else f"Security check '{check}' failed"
            })
        
        overall_passed = all(r["passed"] for r in security_results)
        
        return {
            "passed": overall_passed,
            "message": f"Security checks: {len([r for r in security_results if r['passed']])}/{len(security_results)} passed",
            "details": security_results
        }
    
    def _check_custom(self, gate: QualityGate, 
                     execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """执行自定义检查
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            自定义检查结果
        """
        if not gate.custom_checks:
            return {"passed": True, "message": "No custom checks defined"}
        
        custom_results = []
        overall_passed = True
        
        for check_func in gate.custom_checks:
            try:
                result = check_func(execution_results)
                custom_results.append(result)
                if not result.get("passed", False):
                    overall_passed = False
            except Exception as e:
                custom_results.append({
                    "passed": False,
                    "message": f"Custom check failed: {e}"
                })
                overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Custom checks: {len([r for r in custom_results if r.get('passed', False)])}/{len(custom_results)} passed",
            "details": custom_results
        }

class QualityGateChecker:
    """质量门控检查器"""
    
    def __init__(self, framework: ComprehensiveTestFramework):
        """初始化质量门控检查器
        
        Args:
            framework: 综合测试框架实例
        """
        self.framework = framework
    
    def check_quality_gate(self, gate_name: str, 
                          execution_results: List[TestExecutionResult]) -> Tuple[bool, Dict[str, Any]]:
        """检查质量门控
        
        Args:
            gate_name: 质量门控名称
            execution_results: 测试执行结果列表
            
        Returns:
            (是否通过, 检查详情)
        """
        gate = self.framework.get_quality_gate(gate_name)
        if not gate:
            return False, {"error": f"Quality gate '{gate_name}' not found"}
        
        check_results = {
            "gate_name": gate_name,
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "overall_passed": True,
            "blocking": gate.blocking
        }
        
        # 检查覆盖率
        coverage_passed = self._check_coverage(gate, execution_results)
        check_results["checks"]["coverage"] = coverage_passed
        if not coverage_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查成功率
        success_rate_passed = self._check_success_rate(gate, execution_results)
        check_results["checks"]["success_rate"] = success_rate_passed
        if not success_rate_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查性能指标
        performance_passed = self._check_performance(gate, execution_results)
        check_results["checks"]["performance"] = performance_passed
        if not performance_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查安全性
        security_passed = self._check_security(gate, execution_results)
        check_results["checks"]["security"] = security_passed
        if not security_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 执行自定义检查
        custom_passed = self._check_custom(gate, execution_results)
        check_results["checks"]["custom"] = custom_passed
        if not custom_passed["passed"]:
            check_results["overall_passed"] = False
        
        return check_results["overall_passed"], check_results
    
    def _check_coverage(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查代码覆盖率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            覆盖率检查结果
        """
        coverages = [r.coverage_percentage for r in execution_results 
                    if r.coverage_percentage is not None]
        
        if not coverages:
            return {
                "passed": False,
                "message": "No coverage data available",
                "threshold": gate.coverage_threshold,
                "actual": None
            }
        
        avg_coverage = sum(coverages) / len(coverages)
        passed = avg_coverage >= gate.coverage_threshold
        
        return {
            "passed": passed,
            "message": f"Coverage: {avg_coverage:.1f}% (threshold: {gate.coverage_threshold}%)",
            "threshold": gate.coverage_threshold,
            "actual": avg_coverage
        }
    
    def _check_success_rate(self, gate: QualityGate, 
                           execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查测试成功率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            成功率检查结果
        """
        if not execution_results:
            return {
                "passed": False,
                "message": "No test results available",
                "threshold": gate.success_rate_threshold,
                "actual": None
            }
        
        total_tests = sum(r.test_count for r in execution_results)
        total_passed = sum(r.passed_count for r in execution_results)
        
        if total_tests == 0:
            return {
                "passed": False,
                "message": "No tests executed",
                "threshold": gate.success_rate_threshold,
                "actual": 0.0
            }
        
        success_rate = (total_passed / total_tests) * 100
        passed = success_rate >= gate.success_rate_threshold
        
        return {
            "passed": passed,
            "message": f"Success rate: {success_rate:.1f}% (threshold: {gate.success_rate_threshold}%)",
            "threshold": gate.success_rate_threshold,
            "actual": success_rate
        }
    
    def _check_performance(self, gate: QualityGate, 
                          execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查性能指标
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            性能检查结果
        """
        if not gate.performance_thresholds:
            return {"passed": True, "message": "No performance thresholds defined"}
        
        performance_results = []
        overall_passed = True
        
        for result in execution_results:
            if result.performance_metrics:
                metrics = result.performance_metrics
                
                for metric_name, threshold in gate.performance_thresholds.items():
                    actual_value = getattr(metrics, metric_name, None)
                    
                    if actual_value is not None:
                        # 大部分性能指标是越小越好
                        if metric_name.endswith('_ops_sec') or metric_name.endswith('_throughput'):
                            # 吞吐量类指标是越大越好
                            passed = actual_value >= threshold
                        else:
                            # 响应时间、内存使用等是越小越好
                            passed = actual_value <= threshold
                        
                        performance_results.append({
                            "metric": metric_name,
                            "threshold": threshold,
                            "actual": actual_value,
                            "passed": passed
                        })
                        
                        if not passed:
                            overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Performance checks: {len([r for r in performance_results if r['passed']])}/{len(performance_results)} passed",
            "details": performance_results
        }
    
    def _check_security(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查安全性
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            安全检查结果
        """
        if not gate.security_checks:
            return {"passed": True, "message": "No security checks defined"}
        
        # 这里可以集成实际的安全扫描工具
        # 目前返回模拟结果
        security_results = []
        
        for check in gate.security_checks:
            # 模拟安全检查
            passed = True  # 实际实现中应该调用相应的安全扫描工具
            security_results.append({
                "check": check,
                "passed": passed,
                "message": f"Security check '{check}' passed" if passed else f"Security check '{check}' failed"
            })
        
        overall_passed = all(r["passed"] for r in security_results)
        
        return {
            "passed": overall_passed,
            "message": f"Security checks: {len([r for r in security_results if r['passed']])}/{len(security_results)} passed",
            "details": security_results
        }
    
    def _check_custom(self, gate: QualityGate, 
                     execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """执行自定义检查
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            自定义检查结果
        """
        if not gate.custom_checks:
            return {"passed": True, "message": "No custom checks defined"}
        
        custom_results = []
        overall_passed = True
        
        for check_func in gate.custom_checks:
            try:
                result = check_func(execution_results)
                custom_results.append(result)
                if not result.get("passed", False):
                    overall_passed = False
            except Exception as e:
                custom_results.append({
                    "passed": False,
                    "message": f"Custom check failed: {e}"
                })
                overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Custom checks: {len([r for r in custom_results if r.get('passed', False)])}/{len(custom_results)} passed",
            "details": custom_results
        }

class QualityGateChecker:
    """质量门控检查器"""
    
    def __init__(self, framework: ComprehensiveTestFramework):
        """初始化质量门控检查器
        
        Args:
            framework: 综合测试框架实例
        """
        self.framework = framework
    
    def check_quality_gate(self, gate_name: str, 
                          execution_results: List[TestExecutionResult]) -> Tuple[bool, Dict[str, Any]]:
        """检查质量门控
        
        Args:
            gate_name: 质量门控名称
            execution_results: 测试执行结果列表
            
        Returns:
            (是否通过, 检查详情)
        """
        gate = self.framework.get_quality_gate(gate_name)
        if not gate:
            return False, {"error": f"Quality gate '{gate_name}' not found"}
        
        check_results = {
            "gate_name": gate_name,
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "overall_passed": True,
            "blocking": gate.blocking
        }
        
        # 检查覆盖率
        coverage_passed = self._check_coverage(gate, execution_results)
        check_results["checks"]["coverage"] = coverage_passed
        if not coverage_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查成功率
        success_rate_passed = self._check_success_rate(gate, execution_results)
        check_results["checks"]["success_rate"] = success_rate_passed
        if not success_rate_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查性能指标
        performance_passed = self._check_performance(gate, execution_results)
        check_results["checks"]["performance"] = performance_passed
        if not performance_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查安全性
        security_passed = self._check_security(gate, execution_results)
        check_results["checks"]["security"] = security_passed
        if not security_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 执行自定义检查
        custom_passed = self._check_custom(gate, execution_results)
        check_results["checks"]["custom"] = custom_passed
        if not custom_passed["passed"]:
            check_results["overall_passed"] = False
        
        return check_results["overall_passed"], check_results
    
    def _check_coverage(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查代码覆盖率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            覆盖率检查结果
        """
        coverages = [r.coverage_percentage for r in execution_results 
                    if r.coverage_percentage is not None]
        
        if not coverages:
            return {
                "passed": False,
                "message": "No coverage data available",
                "threshold": gate.coverage_threshold,
                "actual": None
            }
        
        avg_coverage = sum(coverages) / len(coverages)
        passed = avg_coverage >= gate.coverage_threshold
        
        return {
            "passed": passed,
            "message": f"Coverage: {avg_coverage:.1f}% (threshold: {gate.coverage_threshold}%)",
            "threshold": gate.coverage_threshold,
            "actual": avg_coverage
        }
    
    def _check_success_rate(self, gate: QualityGate, 
                           execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查测试成功率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            成功率检查结果
        """
        if not execution_results:
            return {
                "passed": False,
                "message": "No test results available",
                "threshold": gate.success_rate_threshold,
                "actual": None
            }
        
        total_tests = sum(r.test_count for r in execution_results)
        total_passed = sum(r.passed_count for r in execution_results)
        
        if total_tests == 0:
            return {
                "passed": False,
                "message": "No tests executed",
                "threshold": gate.success_rate_threshold,
                "actual": 0.0
            }
        
        success_rate = (total_passed / total_tests) * 100
        passed = success_rate >= gate.success_rate_threshold
        
        return {
            "passed": passed,
            "message": f"Success rate: {success_rate:.1f}% (threshold: {gate.success_rate_threshold}%)",
            "threshold": gate.success_rate_threshold,
            "actual": success_rate
        }
    
    def _check_performance(self, gate: QualityGate, 
                          execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查性能指标
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            性能检查结果
        """
        if not gate.performance_thresholds:
            return {"passed": True, "message": "No performance thresholds defined"}
        
        performance_results = []
        overall_passed = True
        
        for result in execution_results:
            if result.performance_metrics:
                metrics = result.performance_metrics
                
                for metric_name, threshold in gate.performance_thresholds.items():
                    actual_value = getattr(metrics, metric_name, None)
                    
                    if actual_value is not None:
                        # 大部分性能指标是越小越好
                        if metric_name.endswith('_ops_sec') or metric_name.endswith('_throughput'):
                            # 吞吐量类指标是越大越好
                            passed = actual_value >= threshold
                        else:
                            # 响应时间、内存使用等是越小越好
                            passed = actual_value <= threshold
                        
                        performance_results.append({
                            "metric": metric_name,
                            "threshold": threshold,
                            "actual": actual_value,
                            "passed": passed
                        })
                        
                        if not passed:
                            overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Performance checks: {len([r for r in performance_results if r['passed']])}/{len(performance_results)} passed",
            "details": performance_results
        }
    
    def _check_security(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查安全性
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            安全检查结果
        """
        if not gate.security_checks:
            return {"passed": True, "message": "No security checks defined"}
        
        # 这里可以集成实际的安全扫描工具
        # 目前返回模拟结果
        security_results = []
        
        for check in gate.security_checks:
            # 模拟安全检查
            passed = True  # 实际实现中应该调用相应的安全扫描工具
            security_results.append({
                "check": check,
                "passed": passed,
                "message": f"Security check '{check}' passed" if passed else f"Security check '{check}' failed"
            })
        
        overall_passed = all(r["passed"] for r in security_results)
        
        return {
            "passed": overall_passed,
            "message": f"Security checks: {len([r for r in security_results if r['passed']])}/{len(security_results)} passed",
            "details": security_results
        }
    
    def _check_custom(self, gate: QualityGate, 
                     execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """执行自定义检查
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            自定义检查结果
        """
        if not gate.custom_checks:
            return {"passed": True, "message": "No custom checks defined"}
        
        custom_results = []
        overall_passed = True
        
        for check_func in gate.custom_checks:
            try:
                result = check_func(execution_results)
                custom_results.append(result)
                if not result.get("passed", False):
                    overall_passed = False
            except Exception as e:
                custom_results.append({
                    "passed": False,
                    "message": f"Custom check failed: {e}"
                })
                overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Custom checks: {len([r for r in custom_results if r.get('passed', False)])}/{len(custom_results)} passed",
            "details": custom_results
        }

class QualityGateChecker:
    """质量门控检查器"""
    
    def __init__(self, framework: ComprehensiveTestFramework):
        """初始化质量门控检查器
        
        Args:
            framework: 综合测试框架实例
        """
        self.framework = framework
    
    def check_quality_gate(self, gate_name: str, 
                          execution_results: List[TestExecutionResult]) -> Tuple[bool, Dict[str, Any]]:
        """检查质量门控
        
        Args:
            gate_name: 质量门控名称
            execution_results: 测试执行结果列表
            
        Returns:
            (是否通过, 检查详情)
        """
        gate = self.framework.get_quality_gate(gate_name)
        if not gate:
            return False, {"error": f"Quality gate '{gate_name}' not found"}
        
        check_results = {
            "gate_name": gate_name,
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "overall_passed": True,
            "blocking": gate.blocking
        }
        
        # 检查覆盖率
        coverage_passed = self._check_coverage(gate, execution_results)
        check_results["checks"]["coverage"] = coverage_passed
        if not coverage_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查成功率
        success_rate_passed = self._check_success_rate(gate, execution_results)
        check_results["checks"]["success_rate"] = success_rate_passed
        if not success_rate_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查性能指标
        performance_passed = self._check_performance(gate, execution_results)
        check_results["checks"]["performance"] = performance_passed
        if not performance_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查安全性
        security_passed = self._check_security(gate, execution_results)
        check_results["checks"]["security"] = security_passed
        if not security_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 执行自定义检查
        custom_passed = self._check_custom(gate, execution_results)
        check_results["checks"]["custom"] = custom_passed
        if not custom_passed["passed"]:
            check_results["overall_passed"] = False
        
        return check_results["overall_passed"], check_results
    
    def _check_coverage(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查代码覆盖率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            覆盖率检查结果
        """
        coverages = [r.coverage_percentage for r in execution_results 
                    if r.coverage_percentage is not None]
        
        if not coverages:
            return {
                "passed": False,
                "message": "No coverage data available",
                "threshold": gate.coverage_threshold,
                "actual": None
            }
        
        avg_coverage = sum(coverages) / len(coverages)
        passed = avg_coverage >= gate.coverage_threshold
        
        return {
            "passed": passed,
            "message": f"Coverage: {avg_coverage:.1f}% (threshold: {gate.coverage_threshold}%)",
            "threshold": gate.coverage_threshold,
            "actual": avg_coverage
        }
    
    def _check_success_rate(self, gate: QualityGate, 
                           execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查测试成功率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            成功率检查结果
        """
        if not execution_results:
            return {
                "passed": False,
                "message": "No test results available",
                "threshold": gate.success_rate_threshold,
                "actual": None
            }
        
        total_tests = sum(r.test_count for r in execution_results)
        total_passed = sum(r.passed_count for r in execution_results)
        
        if total_tests == 0:
            return {
                "passed": False,
                "message": "No tests executed",
                "threshold": gate.success_rate_threshold,
                "actual": 0.0
            }
        
        success_rate = (total_passed / total_tests) * 100
        passed = success_rate >= gate.success_rate_threshold
        
        return {
            "passed": passed,
            "message": f"Success rate: {success_rate:.1f}% (threshold: {gate.success_rate_threshold}%)",
            "threshold": gate.success_rate_threshold,
            "actual": success_rate
        }
    
    def _check_performance(self, gate: QualityGate, 
                          execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查性能指标
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            性能检查结果
        """
        if not gate.performance_thresholds:
            return {"passed": True, "message": "No performance thresholds defined"}
        
        performance_results = []
        overall_passed = True
        
        for result in execution_results:
            if result.performance_metrics:
                metrics = result.performance_metrics
                
                for metric_name, threshold in gate.performance_thresholds.items():
                    actual_value = getattr(metrics, metric_name, None)
                    
                    if actual_value is not None:
                        # 大部分性能指标是越小越好
                        if metric_name.endswith('_ops_sec') or metric_name.endswith('_throughput'):
                            # 吞吐量类指标是越大越好
                            passed = actual_value >= threshold
                        else:
                            # 响应时间、内存使用等是越小越好
                            passed = actual_value <= threshold
                        
                        performance_results.append({
                            "metric": metric_name,
                            "threshold": threshold,
                            "actual": actual_value,
                            "passed": passed
                        })
                        
                        if not passed:
                            overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Performance checks: {len([r for r in performance_results if r['passed']])}/{len(performance_results)} passed",
            "details": performance_results
        }
    
    def _check_security(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查安全性
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            安全检查结果
        """
        if not gate.security_checks:
            return {"passed": True, "message": "No security checks defined"}
        
        # 这里可以集成实际的安全扫描工具
        # 目前返回模拟结果
        security_results = []
        
        for check in gate.security_checks:
            # 模拟安全检查
            passed = True  # 实际实现中应该调用相应的安全扫描工具
            security_results.append({
                "check": check,
                "passed": passed,
                "message": f"Security check '{check}' passed" if passed else f"Security check '{check}' failed"
            })
        
        overall_passed = all(r["passed"] for r in security_results)
        
        return {
            "passed": overall_passed,
            "message": f"Security checks: {len([r for r in security_results if r['passed']])}/{len(security_results)} passed",
            "details": security_results
        }
    
    def _check_custom(self, gate: QualityGate, 
                     execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """执行自定义检查
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            自定义检查结果
        """
        if not gate.custom_checks:
            return {"passed": True, "message": "No custom checks defined"}
        
        custom_results = []
        overall_passed = True
        
        for check_func in gate.custom_checks:
            try:
                result = check_func(execution_results)
                custom_results.append(result)
                if not result.get("passed", False):
                    overall_passed = False
            except Exception as e:
                custom_results.append({
                    "passed": False,
                    "message": f"Custom check failed: {e}"
                })
                overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Custom checks: {len([r for r in custom_results if r.get('passed', False)])}/{len(custom_results)} passed",
            "details": custom_results
        }

class QualityGateChecker:
    """质量门控检查器"""
    
    def __init__(self, framework: ComprehensiveTestFramework):
        """初始化质量门控检查器
        
        Args:
            framework: 综合测试框架实例
        """
        self.framework = framework
    
    def check_quality_gate(self, gate_name: str, 
                          execution_results: List[TestExecutionResult]) -> Tuple[bool, Dict[str, Any]]:
        """检查质量门控
        
        Args:
            gate_name: 质量门控名称
            execution_results: 测试执行结果列表
            
        Returns:
            (是否通过, 检查详情)
        """
        gate = self.framework.get_quality_gate(gate_name)
        if not gate:
            return False, {"error": f"Quality gate '{gate_name}' not found"}
        
        check_results = {
            "gate_name": gate_name,
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "overall_passed": True,
            "blocking": gate.blocking
        }
        
        # 检查覆盖率
        coverage_passed = self._check_coverage(gate, execution_results)
        check_results["checks"]["coverage"] = coverage_passed
        if not coverage_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查成功率
        success_rate_passed = self._check_success_rate(gate, execution_results)
        check_results["checks"]["success_rate"] = success_rate_passed
        if not success_rate_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查性能指标
        performance_passed = self._check_performance(gate, execution_results)
        check_results["checks"]["performance"] = performance_passed
        if not performance_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查安全性
        security_passed = self._check_security(gate, execution_results)
        check_results["checks"]["security"] = security_passed
        if not security_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 执行自定义检查
        custom_passed = self._check_custom(gate, execution_results)
        check_results["checks"]["custom"] = custom_passed
        if not custom_passed["passed"]:
            check_results["overall_passed"] = False
        
        return check_results["overall_passed"], check_results
    
    def _check_coverage(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查代码覆盖率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            覆盖率检查结果
        """
        coverages = [r.coverage_percentage for r in execution_results 
                    if r.coverage_percentage is not None]
        
        if not coverages:
            return {
                "passed": False,
                "message": "No coverage data available",
                "threshold": gate.coverage_threshold,
                "actual": None
            }
        
        avg_coverage = sum(coverages) / len(coverages)
        passed = avg_coverage >= gate.coverage_threshold
        
        return {
            "passed": passed,
            "message": f"Coverage: {avg_coverage:.1f}% (threshold: {gate.coverage_threshold}%)",
            "threshold": gate.coverage_threshold,
            "actual": avg_coverage
        }
    
    def _check_success_rate(self, gate: QualityGate, 
                           execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查测试成功率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            成功率检查结果
        """
        if not execution_results:
            return {
                "passed": False,
                "message": "No test results available",
                "threshold": gate.success_rate_threshold,
                "actual": None
            }
        
        total_tests = sum(r.test_count for r in execution_results)
        total_passed = sum(r.passed_count for r in execution_results)
        
        if total_tests == 0:
            return {
                "passed": False,
                "message": "No tests executed",
                "threshold": gate.success_rate_threshold,
                "actual": 0.0
            }
        
        success_rate = (total_passed / total_tests) * 100
        passed = success_rate >= gate.success_rate_threshold
        
        return {
            "passed": passed,
            "message": f"Success rate: {success_rate:.1f}% (threshold: {gate.success_rate_threshold}%)",
            "threshold": gate.success_rate_threshold,
            "actual": success_rate
        }
    
    def _check_performance(self, gate: QualityGate, 
                          execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查性能指标
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            性能检查结果
        """
        if not gate.performance_thresholds:
            return {"passed": True, "message": "No performance thresholds defined"}
        
        performance_results = []
        overall_passed = True
        
        for result in execution_results:
            if result.performance_metrics:
                metrics = result.performance_metrics
                
                for metric_name, threshold in gate.performance_thresholds.items():
                    actual_value = getattr(metrics, metric_name, None)
                    
                    if actual_value is not None:
                        # 大部分性能指标是越小越好
                        if metric_name.endswith('_ops_sec') or metric_name.endswith('_throughput'):
                            # 吞吐量类指标是越大越好
                            passed = actual_value >= threshold
                        else:
                            # 响应时间、内存使用等是越小越好
                            passed = actual_value <= threshold
                        
                        performance_results.append({
                            "metric": metric_name,
                            "threshold": threshold,
                            "actual": actual_value,
                            "passed": passed
                        })
                        
                        if not passed:
                            overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Performance checks: {len([r for r in performance_results if r['passed']])}/{len(performance_results)} passed",
            "details": performance_results
        }
    
    def _check_security(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查安全性
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            安全检查结果
        """
        if not gate.security_checks:
            return {"passed": True, "message": "No security checks defined"}
        
        # 这里可以集成实际的安全扫描工具
        # 目前返回模拟结果
        security_results = []
        
        for check in gate.security_checks:
            # 模拟安全检查
            passed = True  # 实际实现中应该调用相应的安全扫描工具
            security_results.append({
                "check": check,
                "passed": passed,
                "message": f"Security check '{check}' passed" if passed else f"Security check '{check}' failed"
            })
        
        overall_passed = all(r["passed"] for r in security_results)
        
        return {
            "passed": overall_passed,
            "message": f"Security checks: {len([r for r in security_results if r['passed']])}/{len(security_results)} passed",
            "details": security_results
        }
    
    def _check_custom(self, gate: QualityGate, 
                     execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """执行自定义检查
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            自定义检查结果
        """
        if not gate.custom_checks:
            return {"passed": True, "message": "No custom checks defined"}
        
        custom_results = []
        overall_passed = True
        
        for check_func in gate.custom_checks:
            try:
                result = check_func(execution_results)
                custom_results.append(result)
                if not result.get("passed", False):
                    overall_passed = False
            except Exception as e:
                custom_results.append({
                    "passed": False,
                    "message": f"Custom check failed: {e}"
                })
                overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Custom checks: {len([r for r in custom_results if r.get('passed', False)])}/{len(custom_results)} passed",
            "details": custom_results
        }

class QualityGateChecker:
    """质量门控检查器"""
    
    def __init__(self, framework: ComprehensiveTestFramework):
        """初始化质量门控检查器
        
        Args:
            framework: 综合测试框架实例
        """
        self.framework = framework
    
    def check_quality_gate(self, gate_name: str, 
                          execution_results: List[TestExecutionResult]) -> Tuple[bool, Dict[str, Any]]:
        """检查质量门控
        
        Args:
            gate_name: 质量门控名称
            execution_results: 测试执行结果列表
            
        Returns:
            (是否通过, 检查详情)
        """
        gate = self.framework.get_quality_gate(gate_name)
        if not gate:
            return False, {"error": f"Quality gate '{gate_name}' not found"}
        
        check_results = {
            "gate_name": gate_name,
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "overall_passed": True,
            "blocking": gate.blocking
        }
        
        # 检查覆盖率
        coverage_passed = self._check_coverage(gate, execution_results)
        check_results["checks"]["coverage"] = coverage_passed
        if not coverage_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查成功率
        success_rate_passed = self._check_success_rate(gate, execution_results)
        check_results["checks"]["success_rate"] = success_rate_passed
        if not success_rate_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查性能指标
        performance_passed = self._check_performance(gate, execution_results)
        check_results["checks"]["performance"] = performance_passed
        if not performance_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 检查安全性
        security_passed = self._check_security(gate, execution_results)
        check_results["checks"]["security"] = security_passed
        if not security_passed["passed"]:
            check_results["overall_passed"] = False
        
        # 执行自定义检查
        custom_passed = self._check_custom(gate, execution_results)
        check_results["checks"]["custom"] = custom_passed
        if not custom_passed["passed"]:
            check_results["overall_passed"] = False
        
        return check_results["overall_passed"], check_results
    
    def _check_coverage(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查代码覆盖率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            覆盖率检查结果
        """
        coverages = [r.coverage_percentage for r in execution_results 
                    if r.coverage_percentage is not None]
        
        if not coverages:
            return {
                "passed": False,
                "message": "No coverage data available",
                "threshold": gate.coverage_threshold,
                "actual": None
            }
        
        avg_coverage = sum(coverages) / len(coverages)
        passed = avg_coverage >= gate.coverage_threshold
        
        return {
            "passed": passed,
            "message": f"Coverage: {avg_coverage:.1f}% (threshold: {gate.coverage_threshold}%)",
            "threshold": gate.coverage_threshold,
            "actual": avg_coverage
        }
    
    def _check_success_rate(self, gate: QualityGate, 
                           execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查测试成功率
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            成功率检查结果
        """
        if not execution_results:
            return {
                "passed": False,
                "message": "No test results available",
                "threshold": gate.success_rate_threshold,
                "actual": None
            }
        
        total_tests = sum(r.test_count for r in execution_results)
        total_passed = sum(r.passed_count for r in execution_results)
        
        if total_tests == 0:
            return {
                "passed": False,
                "message": "No tests executed",
                "threshold": gate.success_rate_threshold,
                "actual": 0.0
            }
        
        success_rate = (total_passed / total_tests) * 100
        passed = success_rate >= gate.success_rate_threshold
        
        return {
            "passed": passed,
            "message": f"Success rate: {success_rate:.1f}% (threshold: {gate.success_rate_threshold}%)",
            "threshold": gate.success_rate_threshold,
            "actual": success_rate
        }
    
    def _check_performance(self, gate: QualityGate, 
                          execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查性能指标
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            性能检查结果
        """
        if not gate.performance_thresholds:
            return {"passed": True, "message": "No performance thresholds defined"}
        
        performance_results = []
        overall_passed = True
        
        for result in execution_results:
            if result.performance_metrics:
                metrics = result.performance_metrics
                
                for metric_name, threshold in gate.performance_thresholds.items():
                    actual_value = getattr(metrics, metric_name, None)
                    
                    if actual_value is not None:
                        # 大部分性能指标是越小越好
                        if metric_name.endswith('_ops_sec') or metric_name.endswith('_throughput'):
                            # 吞吐量类指标是越大越好
                            passed = actual_value >= threshold
                        else:
                            # 响应时间、内存使用等是越小越好
                            passed = actual_value <= threshold
                        
                        performance_results.append({
                            "metric": metric_name,
                            "threshold": threshold,
                            "actual": actual_value,
                            "passed": passed
                        })
                        
                        if not passed:
                            overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Performance checks: {len([r for r in performance_results if r['passed']])}/{len(performance_results)} passed",
            "details": performance_results
        }
    
    def _check_security(self, gate: QualityGate, 
                       execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """检查安全性
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            安全检查结果
        """
        if not gate.security_checks:
            return {"passed": True, "message": "No security checks defined"}
        
        # 这里可以集成实际的安全扫描工具
        # 目前返回模拟结果
        security_results = []
        
        for check in gate.security_checks:
            # 模拟安全检查
            passed = True  # 实际实现中应该调用相应的安全扫描工具
            security_results.append({
                "check": check,
                "passed": passed,
                "message": f"Security check '{check}' passed" if passed else f"Security check '{check}' failed"
            })
        
        overall_passed = all(r["passed"] for r in security_results)
        
        return {
            "passed": overall_passed,
            "message": f"Security checks: {len([r for r in security_results if r['passed']])}/{len(security_results)} passed",
            "details": security_results
        }
    
    def _check_custom(self, gate: QualityGate, 
                     execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """执行自定义检查
        
        Args:
            gate: 质量门控配置
            execution_results: 测试执行结果列表
            
        Returns:
            自定义检查结果
        """
        if not gate.custom_checks:
            return {"passed": True, "message": "No custom checks defined"}
        
        custom_results = []
        overall_passed = True
        
        for check_func in gate.custom_checks:
            try:
                result = check_func(execution_results)
                custom_results.append(result)
                if not result.get("passed", False):
                    overall_passed = False
            except Exception as e:
                custom_results.append({
                    "passed": False,
                    "message": f"Custom check failed: {e}"
                })
                overall_passed = False
        
        return {
            "passed": overall_passed,
            "message": f"Custom checks: {len([r for r in custom_results if r.get('passed', False)])}/{len(custom_results)} passed",
            "details": custom_results
        }