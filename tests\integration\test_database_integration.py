"""Integration tests for database operations."""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from src.config import get_config
from src.database.connection import SessionManager
from src.database.operations import (
    CRUDOperations,
    DatabaseManager,
    DataExporter,
    DataImporter,
    TableOperationManager,
    BulkOperations,
    TransactionManager
)
from src.importers.base import AbstractImporter
from src.exporters.base import BaseExporter


class TestDatabaseIntegration:
    """Test database integration functionality."""

    @pytest.fixture
    def mock_config(self):
        """Create mock database configuration."""
        config = Mock(spec=DatabaseConfig)
        config.host = "localhost"
        config.port = 5432
        config.database = "test_db"
        config.username = "test_user"
        config.password = "test_pass"
        config.driver = "postgresql"
        return config

    @pytest.fixture
    def mock_session_manager(self, mock_config):
        """Create mock session manager."""
        with patch('src.database.connection.SessionManager') as mock:
            session_manager = Mock(spec=SessionManager)
            session_manager.config = mock_config
            mock.return_value = session_manager
            yield session_manager

    def test_crud_operations_integration(self, mock_session_manager):
        """Test CRUD operations integration."""
        # Create test data
        test_data = pd.DataFrame({
            'id': [1, 2, 3],
            'name': ['Test1', 'Test2', 'Test3'],
            'value': [10.5, 20.3, 30.1]
        })

        with patch('src.database.operations.crud.CRUDOperations') as mock_crud:
            crud_ops = mock_crud.return_value
            crud_ops.create_table.return_value = True
            crud_ops.insert_dataframe.return_value = True
            crud_ops.select_all.return_value = test_data

            # Test create table
            result = crud_ops.create_table('test_table', test_data.dtypes.to_dict())
            assert result is True

            # Test insert data
            result = crud_ops.insert_dataframe('test_table', test_data)
            assert result is True

            # Test select data
            result = crud_ops.select_all('test_table')
            assert isinstance(result, pd.DataFrame)
            assert len(result) == 3

    def test_bulk_operations_integration(self, mock_session_manager):
        """Test bulk operations integration."""
        # Create large test dataset
        test_data = pd.DataFrame({
            'id': range(1, 1001),
            'timestamp': pd.date_range('2023-01-01', periods=1000, freq='H'),
            'value': np.random.uniform(0, 100, 1000),
            'category': np.random.choice(['A', 'B', 'C'], 1000)
        })

        with patch('src.database.operations.bulk_operations.BulkOperations') as mock_bulk:
            bulk_ops = mock_bulk.return_value
            bulk_ops.bulk_insert_dataframe.return_value = True
            bulk_ops.bulk_update.return_value = 500
            bulk_ops.bulk_delete.return_value = 100

            # Test bulk insert
            result = bulk_ops.bulk_insert_dataframe('test_table', test_data)
            assert result is True

            # Test bulk update
            update_data = {'value': 999}
            condition = "category = 'A'"
            result = bulk_ops.bulk_update('test_table', update_data, condition)
            assert result == 500

            # Test bulk delete
            result = bulk_ops.bulk_delete('test_table', "value < 10")
            assert result == 100

    def test_transaction_management(self, mock_session_manager):
        """Test transaction management."""
        with patch('src.database.operations.transaction_manager.TransactionManager') as mock_tx:
            tx_manager = mock_tx.return_value
            tx_manager.begin_transaction.return_value = True
            tx_manager.commit_transaction.return_value = True
            tx_manager.rollback_transaction.return_value = True

            # Test successful transaction
            tx_manager.begin_transaction()
            # Simulate some operations
            tx_manager.commit_transaction()

            # Test rollback
            tx_manager.begin_transaction()
            # Simulate error condition
            tx_manager.rollback_transaction()

            # Verify calls
            assert tx_manager.begin_transaction.call_count == 2
            assert tx_manager.commit_transaction.call_count == 1
            assert tx_manager.rollback_transaction.call_count == 1

    def test_data_import_export_integration(self, mock_session_manager, tmp_path):
        """Test data import and export integration."""
        # Create test data file
        test_data = pd.DataFrame({
            'id': range(1, 101),
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='H'),
            'value': np.random.uniform(10, 100, 100),
            'category': np.random.choice(['A', 'B', 'C'], 100)
        })

        input_file = tmp_path / "input_data.csv"
        test_data.to_csv(input_file, index=False)
        output_file = tmp_path / "output_data.xlsx"

        with patch('src.importers.base.AbstractImporter') as mock_importer, \
             patch('src.exporters.base.BaseExporter') as mock_exporter:
            
            # Configure mocks
            importer = mock_importer.return_value
            exporter = mock_exporter.return_value
            importer.import_data.return_value = test_data
            exporter.export_data.return_value = True

            # Test import
            imported_data = importer.import_data(str(input_file))
            assert isinstance(imported_data, pd.DataFrame)
            assert len(imported_data) == 100

            # Test export
            result = exporter.export_data(test_data, str(output_file))
            assert result is True

    def test_table_operations_integration(self, mock_session_manager):
        """Test table operations integration."""
        with patch('src.database.operations.table_operations.TableOperationManager') as mock_table:
            table_ops = mock_table.return_value
            table_ops.create_table.return_value = True
            table_ops.drop_table.return_value = True
            table_ops.table_exists.return_value = True
            table_ops.get_table_info.return_value = {
                'columns': ['id', 'name', 'value'],
                'row_count': 1000,
                'size': '1.2MB'
            }

            # Test table creation
            schema = {
                'id': 'INTEGER PRIMARY KEY',
                'name': 'VARCHAR(100)',
                'value': 'DECIMAL(10,2)'
            }
            result = table_ops.create_table('test_table', schema)
            assert result is True

            # Test table existence check
            exists = table_ops.table_exists('test_table')
            assert exists is True

            # Test table info
            info = table_ops.get_table_info('test_table')
            assert 'columns' in info
            assert 'row_count' in info

            # Test table drop
            result = table_ops.drop_table('test_table')
            assert result is True


class TestFullSystemIntegration:
    """Full system integration tests."""

    def test_complete_data_pipeline(self, tmp_path):
        """Test complete data pipeline from import to export."""
        # Create test data file
        test_data = pd.DataFrame({
            'id': range(1, 101),
            'timestamp': pd.date_range('2023-01-01', periods=100, freq='H'),
            'value': np.random.uniform(10, 100, 100),
            'category': np.random.choice(['A', 'B', 'C'], 100)
        })

        input_file = tmp_path / "input_data.csv"
        test_data.to_csv(input_file, index=False)
        output_file = tmp_path / "output_data.xlsx"

        # Mock all database components
        with patch('src.database.connection.SessionManager') as mock_session, \
             patch('src.importers.base.BaseImporter') as mock_import, \
             patch('src.exporters.base.BaseExporter') as mock_export:
            
            # Configure mocks
            mock_import.return_value.import_data.return_value = test_data
            mock_export.return_value.export_data.return_value = True
            
            # Test the complete pipeline
            assert input_file.exists()
            assert len(test_data) == 100
            
            # Verify basic functionality
            importer = mock_import.return_value
            exporter = mock_export.return_value
            
            # Test import
            imported_data = importer.import_data(str(input_file))
            assert isinstance(imported_data, pd.DataFrame)
            
            # Test export
            export_result = exporter.export_data(test_data, str(output_file))
            assert export_result is True
