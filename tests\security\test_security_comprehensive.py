#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect平台增强安全测试

本模块提供全面的安全测试功能，包括：
- OWASP Top 10漏洞检测
- 渗透测试和漏洞扫描
- 认证和授权安全测试
- 数据安全和隐私保护测试
- API安全测试
- 合规性检查（GDPR、行业标准）
- 安全配置审计

作者: Connect质量工程团队
创建时间: 2024-01-20
"""

import base64
import hashlib
import json
import logging
import os
import re
import requests
import subprocess
import tempfile
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urljoin, urlparse

import jwt
import pytest
from cryptography.fernet import Fernet
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SecurityScanner:
    """安全扫描器"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        self.vulnerabilities = []
        self.scan_results = {
            'timestamp': datetime.now().isoformat(),
            'target': base_url,
            'vulnerabilities': [],
            'security_headers': {},
            'ssl_info': {},
            'authentication_tests': {},
            'authorization_tests': {},
            'data_protection_tests': {},
            'compliance_tests': {}
        }
    
    def scan_security_headers(self) -> Dict[str, Any]:
        """扫描安全头"""
        logger.info("扫描安全头")
        
        try:
            response = self.session.get(self.base_url)
            headers = response.headers
            
            security_headers = {
                'X-Content-Type-Options': headers.get('X-Content-Type-Options'),
                'X-Frame-Options': headers.get('X-Frame-Options'),
                'X-XSS-Protection': headers.get('X-XSS-Protection'),
                'Strict-Transport-Security': headers.get('Strict-Transport-Security'),
                'Content-Security-Policy': headers.get('Content-Security-Policy'),
                'Referrer-Policy': headers.get('Referrer-Policy'),
                'Permissions-Policy': headers.get('Permissions-Policy')
            }
            
            # 检查缺失的安全头
            missing_headers = []
            for header, value in security_headers.items():
                if not value:
                    missing_headers.append(header)
                    self.vulnerabilities.append({
                        'type': 'Missing Security Header',
                        'severity': 'Medium',
                        'description': f'缺失安全头: {header}',
                        'recommendation': f'添加 {header} 头以增强安全性'
                    })
            
            self.scan_results['security_headers'] = {
                'present': {k: v for k, v in security_headers.items() if v},
                'missing': missing_headers,
                'score': (len(security_headers) - len(missing_headers)) / len(security_headers) * 100
            }
            
            logger.info(f"安全头扫描完成，得分: {self.scan_results['security_headers']['score']:.1f}%")
            
        except Exception as e:
            logger.error(f"安全头扫描失败: {e}")
            self.vulnerabilities.append({
                'type': 'Scan Error',
                'severity': 'Low',
                'description': f'安全头扫描失败: {e}'
            })
        
        return self.scan_results['security_headers']
    
    def test_sql_injection(self) -> List[Dict[str, Any]]:
        """测试SQL注入漏洞"""
        logger.info("测试SQL注入漏洞")
        
        sql_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM users --",
            "1' AND (SELECT COUNT(*) FROM users) > 0 --",
            "' OR 1=1#",
            "admin'--",
            "' OR 'x'='x",
            "1; WAITFOR DELAY '00:00:05' --"
        ]
        
        test_endpoints = [
            '/api/auth/login',
            '/api/data/query',
            '/api/users/search',
            '/api/sites/filter'
        ]
        
        sql_vulnerabilities = []
        
        for endpoint in test_endpoints:
            for payload in sql_payloads:
                try:
                    # 测试GET参数
                    response = self.session.get(
                        urljoin(self.base_url, endpoint),
                        params={'q': payload, 'id': payload}
                    )
                    
                    if self._detect_sql_error(response.text):
                        vulnerability = {
                            'type': 'SQL Injection',
                            'severity': 'High',
                            'endpoint': endpoint,
                            'payload': payload,
                            'method': 'GET',
                            'description': f'在 {endpoint} 发现SQL注入漏洞',
                            'recommendation': '使用参数化查询和输入验证'
                        }
                        sql_vulnerabilities.append(vulnerability)
                        self.vulnerabilities.append(vulnerability)
                    
                    # 测试POST数据
                    response = self.session.post(
                        urljoin(self.base_url, endpoint),
                        json={'username': payload, 'password': payload, 'query': payload}
                    )
                    
                    if self._detect_sql_error(response.text):
                        vulnerability = {
                            'type': 'SQL Injection',
                            'severity': 'High',
                            'endpoint': endpoint,
                            'payload': payload,
                            'method': 'POST',
                            'description': f'在 {endpoint} 发现SQL注入漏洞',
                            'recommendation': '使用参数化查询和输入验证'
                        }
                        sql_vulnerabilities.append(vulnerability)
                        self.vulnerabilities.append(vulnerability)
                    
                except Exception as e:
                    logger.debug(f"SQL注入测试异常: {e}")
        
        logger.info(f"SQL注入测试完成，发现 {len(sql_vulnerabilities)} 个漏洞")
        return sql_vulnerabilities
    
    def test_xss_vulnerabilities(self) -> List[Dict[str, Any]]:
        """测试XSS漏洞"""
        logger.info("测试XSS漏洞")
        
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "'><script>alert('XSS')</script>",
            "\"><script>alert('XSS')</script>",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>"
        ]
        
        test_endpoints = [
            '/api/search',
            '/api/comments',
            '/api/feedback',
            '/api/reports/generate'
        ]
        
        xss_vulnerabilities = []
        
        for endpoint in test_endpoints:
            for payload in xss_payloads:
                try:
                    # 测试反射型XSS
                    response = self.session.get(
                        urljoin(self.base_url, endpoint),
                        params={'q': payload, 'search': payload}
                    )
                    
                    if payload in response.text and 'text/html' in response.headers.get('content-type', ''):
                        vulnerability = {
                            'type': 'Reflected XSS',
                            'severity': 'High',
                            'endpoint': endpoint,
                            'payload': payload,
                            'description': f'在 {endpoint} 发现反射型XSS漏洞',
                            'recommendation': '对用户输入进行HTML编码和CSP防护'
                        }
                        xss_vulnerabilities.append(vulnerability)
                        self.vulnerabilities.append(vulnerability)
                    
                    # 测试存储型XSS
                    response = self.session.post(
                        urljoin(self.base_url, endpoint),
                        json={'content': payload, 'message': payload}
                    )
                    
                    # 再次获取页面检查是否存储了恶意脚本
                    response = self.session.get(urljoin(self.base_url, endpoint))
                    if payload in response.text:
                        vulnerability = {
                            'type': 'Stored XSS',
                            'severity': 'Critical',
                            'endpoint': endpoint,
                            'payload': payload,
                            'description': f'在 {endpoint} 发现存储型XSS漏洞',
                            'recommendation': '对用户输入进行严格过滤和输出编码'
                        }
                        xss_vulnerabilities.append(vulnerability)
                        self.vulnerabilities.append(vulnerability)
                    
                except Exception as e:
                    logger.debug(f"XSS测试异常: {e}")
        
        logger.info(f"XSS测试完成，发现 {len(xss_vulnerabilities)} 个漏洞")
        return xss_vulnerabilities
    
    def test_authentication_bypass(self) -> List[Dict[str, Any]]:
        """测试认证绕过漏洞"""
        logger.info("测试认证绕过漏洞")
        
        auth_vulnerabilities = []
        
        # 测试未授权访问
        protected_endpoints = [
            '/api/admin/users',
            '/api/admin/config',
            '/api/data/export',
            '/api/reports/sensitive'
        ]
        
        for endpoint in protected_endpoints:
            try:
                # 不带认证头的请求
                response = self.session.get(urljoin(self.base_url, endpoint))
                
                if response.status_code == 200:
                    vulnerability = {
                        'type': 'Authentication Bypass',
                        'severity': 'Critical',
                        'endpoint': endpoint,
                        'description': f'{endpoint} 允许未授权访问',
                        'recommendation': '添加适当的认证检查'
                    }
                    auth_vulnerabilities.append(vulnerability)
                    self.vulnerabilities.append(vulnerability)
                
                # 测试无效token
                invalid_tokens = [
                    'Bearer invalid_token',
                    'Bearer ',
                    'Bearer null',
                    'Bearer undefined'
                ]
                
                for token in invalid_tokens:
                    headers = {'Authorization': token}
                    response = self.session.get(
                        urljoin(self.base_url, endpoint),
                        headers=headers
                    )
                    
                    if response.status_code == 200:
                        vulnerability = {
                            'type': 'Invalid Token Acceptance',
                            'severity': 'High',
                            'endpoint': endpoint,
                            'token': token,
                            'description': f'{endpoint} 接受无效token',
                            'recommendation': '加强token验证逻辑'
                        }
                        auth_vulnerabilities.append(vulnerability)
                        self.vulnerabilities.append(vulnerability)
                
            except Exception as e:
                logger.debug(f"认证测试异常: {e}")
        
        logger.info(f"认证绕过测试完成，发现 {len(auth_vulnerabilities)} 个漏洞")
        return auth_vulnerabilities
    
    def test_authorization_flaws(self) -> List[Dict[str, Any]]:
        """测试授权缺陷"""
        logger.info("测试授权缺陷")
        
        authz_vulnerabilities = []
        
        # 模拟不同权限级别的用户
        user_tokens = {
            'admin': 'admin_token_here',
            'user': 'user_token_here',
            'viewer': 'viewer_token_here'
        }
        
        # 测试垂直权限提升
        admin_endpoints = [
            '/api/admin/users',
            '/api/admin/config',
            '/api/admin/logs'
        ]
        
        for endpoint in admin_endpoints:
            for role, token in user_tokens.items():
                if role != 'admin':
                    try:
                        headers = {'Authorization': f'Bearer {token}'}
                        response = self.session.get(
                            urljoin(self.base_url, endpoint),
                            headers=headers
                        )
                        
                        if response.status_code == 200:
                            vulnerability = {
                                'type': 'Vertical Privilege Escalation',
                                'severity': 'Critical',
                                'endpoint': endpoint,
                                'user_role': role,
                                'description': f'{role} 用户可以访问管理员端点 {endpoint}',
                                'recommendation': '实施基于角色的访问控制'
                            }
                            authz_vulnerabilities.append(vulnerability)
                            self.vulnerabilities.append(vulnerability)
                        
                    except Exception as e:
                        logger.debug(f"授权测试异常: {e}")
        
        # 测试水平权限提升（IDOR）
        user_specific_endpoints = [
            '/api/users/1/profile',
            '/api/users/2/data',
            '/api/projects/1/details'
        ]
        
        for endpoint in user_specific_endpoints:
            try:
                # 使用用户1的token访问用户2的资源
                headers = {'Authorization': f'Bearer {user_tokens["user"]}'}
                response = self.session.get(
                    urljoin(self.base_url, endpoint),
                    headers=headers
                )
                
                if response.status_code == 200:
                    vulnerability = {
                        'type': 'Insecure Direct Object Reference (IDOR)',
                        'severity': 'High',
                        'endpoint': endpoint,
                        'description': f'用户可以访问其他用户的资源: {endpoint}',
                        'recommendation': '验证用户对资源的所有权'
                    }
                    authz_vulnerabilities.append(vulnerability)
                    self.vulnerabilities.append(vulnerability)
                
            except Exception as e:
                logger.debug(f"IDOR测试异常: {e}")
        
        logger.info(f"授权缺陷测试完成，发现 {len(authz_vulnerabilities)} 个漏洞")
        return authz_vulnerabilities
    
    def test_data_exposure(self) -> List[Dict[str, Any]]:
        """测试敏感数据暴露"""
        logger.info("测试敏感数据暴露")
        
        data_vulnerabilities = []
        
        # 测试敏感信息泄露
        test_endpoints = [
            '/api/config',
            '/api/debug',
            '/api/info',
            '/api/status',
            '/.env',
            '/config.json',
            '/api/users',
            '/api/logs'
        ]
        
        sensitive_patterns = [
            r'password["\']?\s*[:=]\s*["\']([^"\']]+)',
            r'api[_-]?key["\']?\s*[:=]\s*["\']([^"\']]+)',
            r'secret["\']?\s*[:=]\s*["\']([^"\']]+)',
            r'token["\']?\s*[:=]\s*["\']([^"\']]+)',
            r'database[_-]?url["\']?\s*[:=]\s*["\']([^"\']]+)',
            r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b',  # 信用卡号
            r'\b\d{3}-\d{2}-\d{4}\b',  # SSN
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'  # 邮箱
        ]
        
        for endpoint in test_endpoints:
            try:
                response = self.session.get(urljoin(self.base_url, endpoint))
                
                if response.status_code == 200:
                    content = response.text
                    
                    for pattern in sensitive_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            vulnerability = {
                                'type': 'Sensitive Data Exposure',
                                'severity': 'High',
                                'endpoint': endpoint,
                                'pattern': pattern,
                                'matches_count': len(matches),
                                'description': f'在 {endpoint} 发现敏感信息泄露',
                                'recommendation': '移除敏感信息或限制访问权限'
                            }
                            data_vulnerabilities.append(vulnerability)
                            self.vulnerabilities.append(vulnerability)
                
            except Exception as e:
                logger.debug(f"数据暴露测试异常: {e}")
        
        logger.info(f"敏感数据暴露测试完成，发现 {len(data_vulnerabilities)} 个漏洞")
        return data_vulnerabilities
    
    def test_csrf_protection(self) -> List[Dict[str, Any]]:
        """测试CSRF保护"""
        logger.info("测试CSRF保护")
        
        csrf_vulnerabilities = []
        
        # 测试状态改变操作的CSRF保护
        state_changing_endpoints = [
            ('/api/users', 'POST'),
            ('/api/users/1', 'PUT'),
            ('/api/users/1', 'DELETE'),
            ('/api/config', 'POST'),
            ('/api/data/import', 'POST')
        ]
        
        for endpoint, method in state_changing_endpoints:
            try:
                # 不带CSRF token的请求
                if method == 'POST':
                    response = self.session.post(
                        urljoin(self.base_url, endpoint),
                        json={'test': 'data'}
                    )
                elif method == 'PUT':
                    response = self.session.put(
                        urljoin(self.base_url, endpoint),
                        json={'test': 'data'}
                    )
                elif method == 'DELETE':
                    response = self.session.delete(urljoin(self.base_url, endpoint))
                
                # 如果请求成功且没有CSRF保护，则存在漏洞
                if response.status_code in [200, 201, 204]:
                    vulnerability = {
                        'type': 'CSRF Vulnerability',
                        'severity': 'Medium',
                        'endpoint': endpoint,
                        'method': method,
                        'description': f'{endpoint} 缺少CSRF保护',
                        'recommendation': '实施CSRF token验证'
                    }
                    csrf_vulnerabilities.append(vulnerability)
                    self.vulnerabilities.append(vulnerability)
                
            except Exception as e:
                logger.debug(f"CSRF测试异常: {e}")
        
        logger.info(f"CSRF保护测试完成，发现 {len(csrf_vulnerabilities)} 个漏洞")
        return csrf_vulnerabilities
    
    def _detect_sql_error(self, content: str) -> bool:
        """检测SQL错误信息"""
        sql_errors = [
            'sql syntax',
            'mysql_fetch',
            'ora-\d+',
            'postgresql',
            'sqlite',
            'sqlserver',
            'syntax error',
            'unclosed quotation mark',
            'quoted string not properly terminated'
        ]
        
        for error in sql_errors:
            if re.search(error, content, re.IGNORECASE):
                return True
        return False
    
    def generate_security_report(self) -> Dict[str, Any]:
        """生成安全扫描报告"""
        self.scan_results['vulnerabilities'] = self.vulnerabilities
        self.scan_results['summary'] = {
            'total_vulnerabilities': len(self.vulnerabilities),
            'critical': len([v for v in self.vulnerabilities if v.get('severity') == 'Critical']),
            'high': len([v for v in self.vulnerabilities if v.get('severity') == 'High']),
            'medium': len([v for v in self.vulnerabilities if v.get('severity') == 'Medium']),
            'low': len([v for v in self.vulnerabilities if v.get('severity') == 'Low'])
        }
        
        return self.scan_results


class ComplianceChecker:
    """合规性检查器"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        self.compliance_results = {
            'gdpr': {},
            'iso27001': {},
            'pci_dss': {},
            'telecom_regulations': {}
        }
    
    def check_gdpr_compliance(self) -> Dict[str, Any]:
        """检查GDPR合规性"""
        logger.info("检查GDPR合规性")
        
        gdpr_checks = {
            'privacy_policy': False,
            'cookie_consent': False,
            'data_portability': False,
            'right_to_erasure': False,
            'data_protection_officer': False,
            'breach_notification': False,
            'consent_management': False
        }
        
        try:
            # 检查隐私政策
            response = self.session.get(urljoin(self.base_url, '/privacy-policy'))
            if response.status_code == 200:
                gdpr_checks['privacy_policy'] = True
            
            # 检查Cookie同意
            response = self.session.get(self.base_url)
            if 'cookie' in response.text.lower() and 'consent' in response.text.lower():
                gdpr_checks['cookie_consent'] = True
            
            # 检查数据可移植性API
            response = self.session.get(urljoin(self.base_url, '/api/data/export'))
            if response.status_code in [200, 401, 403]:  # 端点存在
                gdpr_checks['data_portability'] = True
            
            # 检查删除权API
            response = self.session.delete(urljoin(self.base_url, '/api/users/me'))
            if response.status_code in [200, 401, 403]:  # 端点存在
                gdpr_checks['right_to_erasure'] = True
            
        except Exception as e:
            logger.error(f"GDPR合规性检查失败: {e}")
        
        compliance_score = sum(gdpr_checks.values()) / len(gdpr_checks) * 100
        
        self.compliance_results['gdpr'] = {
            'checks': gdpr_checks,
            'score': compliance_score,
            'compliant': compliance_score >= 80
        }
        
        logger.info(f"GDPR合规性得分: {compliance_score:.1f}%")
        return self.compliance_results['gdpr']
    
    def check_data_encryption(self) -> Dict[str, Any]:
        """检查数据加密"""
        logger.info("检查数据加密")
        
        encryption_checks = {
            'https_enabled': False,
            'tls_version': None,
            'cipher_strength': None,
            'certificate_valid': False,
            'hsts_enabled': False
        }
        
        try:
            # 检查HTTPS
            if self.base_url.startswith('https://'):
                encryption_checks['https_enabled'] = True
                
                # 检查TLS版本和加密套件
                import ssl
                import socket
                
                hostname = urlparse(self.base_url).hostname
                port = urlparse(self.base_url).port or 443
                
                context = ssl.create_default_context()
                with socket.create_connection((hostname, port)) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        encryption_checks['tls_version'] = ssock.version()
                        encryption_checks['cipher_strength'] = ssock.cipher()[2]
                        encryption_checks['certificate_valid'] = True
            
            # 检查HSTS
            response = self.session.get(self.base_url)
            if 'Strict-Transport-Security' in response.headers:
                encryption_checks['hsts_enabled'] = True
            
        except Exception as e:
            logger.error(f"加密检查失败: {e}")
        
        return encryption_checks


class PenetrationTester:
    """渗透测试器"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()
        self.findings = []
    
    def test_directory_traversal(self) -> List[Dict[str, Any]]:
        """测试目录遍历漏洞"""
        logger.info("测试目录遍历漏洞")
        
        traversal_payloads = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
            '....//....//....//etc/passwd',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
            '..%252f..%252f..%252fetc%252fpasswd'
        ]
        
        test_endpoints = [
            '/api/files',
            '/api/download',
            '/api/export',
            '/api/reports'
        ]
        
        findings = []
        
        for endpoint in test_endpoints:
            for payload in traversal_payloads:
                try:
                    response = self.session.get(
                        urljoin(self.base_url, endpoint),
                        params={'file': payload, 'path': payload}
                    )
                    
                    # 检查是否返回了系统文件内容
                    if ('root:' in response.text or 
                        'localhost' in response.text or 
                        '[boot loader]' in response.text):
                        
                        finding = {
                            'type': 'Directory Traversal',
                            'severity': 'High',
                            'endpoint': endpoint,
                            'payload': payload,
                            'description': f'在 {endpoint} 发现目录遍历漏洞',
                            'recommendation': '验证和过滤文件路径参数'
                        }
                        findings.append(finding)
                        self.findings.append(finding)
                
                except Exception as e:
                    logger.debug(f"目录遍历测试异常: {e}")
        
        logger.info(f"目录遍历测试完成，发现 {len(findings)} 个漏洞")
        return findings
    
    def test_file_upload_vulnerabilities(self) -> List[Dict[str, Any]]:
        """测试文件上传漏洞"""
        logger.info("测试文件上传漏洞")
        
        findings = []
        upload_endpoints = ['/api/upload', '/api/import', '/api/files/upload']
        
        # 恶意文件测试
        malicious_files = {
            'php_shell.php': b'<?php system($_GET["cmd"]); ?>',
            'jsp_shell.jsp': b'<%@ page import="java.io.*" %><%String cmd = request.getParameter("cmd");Process p = Runtime.getRuntime().exec(cmd);%>',
            'script.js': b'alert("XSS");',
            'malware.exe': b'MZ\x90\x00\x03\x00\x00\x00\x04\x00\x00\x00\xff\xff',
            'large_file.txt': b'A' * (10 * 1024 * 1024)  # 10MB文件
        }
        
        for endpoint in upload_endpoints:
            for filename, content in malicious_files.items():
                try:
                    files = {'file': (filename, content, 'application/octet-stream')}
                    response = self.session.post(
                        urljoin(self.base_url, endpoint),
                        files=files
                    )
                    
                    if response.status_code == 200:
                        finding = {
                            'type': 'Malicious File Upload',
                            'severity': 'Critical' if filename.endswith(('.php', '.jsp', '.exe')) else 'Medium',
                            'endpoint': endpoint,
                            'filename': filename,
                            'description': f'成功上传恶意文件 {filename} 到 {endpoint}',
                            'recommendation': '实施文件类型验证和内容扫描'
                        }
                        findings.append(finding)
                        self.findings.append(finding)
                
                except Exception as e:
                    logger.debug(f"文件上传测试异常: {e}")
        
        logger.info(f"文件上传测试完成，发现 {len(findings)} 个漏洞")
        return findings
    
    def test_api_rate_limiting(self) -> List[Dict[str, Any]]:
        """测试API速率限制"""
        logger.info("测试API速率限制")
        
        findings = []
        test_endpoints = ['/api/auth/login', '/api/data/query', '/api/search']
        
        for endpoint in test_endpoints:
            try:
                # 快速发送大量请求
                responses = []
                for i in range(100):
                    response = self.session.get(urljoin(self.base_url, endpoint))
                    responses.append(response.status_code)
                    
                    if i > 10 and all(status != 429 for status in responses[-10:]):
                        # 如果连续10个请求都没有被限制，可能存在问题
                        finding = {
                            'type': 'Missing Rate Limiting',
                            'severity': 'Medium',
                            'endpoint': endpoint,
                            'description': f'{endpoint} 缺少速率限制保护',
                            'recommendation': '实施API速率限制'
                        }
                        findings.append(finding)
                        self.findings.append(finding)
                        break
            
            except Exception as e:
                logger.debug(f"速率限制测试异常: {e}")
        
        logger.info(f"API速率限制测试完成，发现 {len(findings)} 个问题")
        return findings


class TestSecurityEnhanced:
    """增强安全测试类"""
    
    def setup_method(self):
        """测试方法设置"""
        self.base_url = os.getenv('TEST_BASE_URL', 'http://localhost:8000')
        self.scanner = SecurityScanner(self.base_url)
        self.compliance_checker = ComplianceChecker(self.base_url)
        self.penetration_tester = PenetrationTester(self.base_url)
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        logger.info(f"临时目录: {self.temp_dir}")
    
    def teardown_method(self):
        """测试方法清理"""
        # 清理临时文件
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @pytest.mark.security
    def test_owasp_top_10_vulnerabilities(self):
        """测试OWASP Top 10漏洞"""
        logger.info("开始OWASP Top 10漏洞扫描")
        
        # 1. 注入攻击
        sql_vulns = self.scanner.test_sql_injection()
        assert len(sql_vulns) == 0, f"发现 {len(sql_vulns)} 个SQL注入漏洞"
        
        # 2. 失效的身份认证
        auth_vulns = self.scanner.test_authentication_bypass()
        assert len(auth_vulns) == 0, f"发现 {len(auth_vulns)} 个认证绕过漏洞"
        
        # 3. 敏感数据泄露
        data_vulns = self.scanner.test_data_exposure()
        assert len(data_vulns) == 0, f"发现 {len(data_vulns)} 个敏感数据泄露"
        
        # 4. XML外部实体(XXE)
        # 这里可以添加XXE测试
        
        # 5. 失效的访问控制
        authz_vulns = self.scanner.test_authorization_flaws()
        assert len(authz_vulns) == 0, f"发现 {len(authz_vulns)} 个授权缺陷"
        
        # 6. 安全配置错误
        headers = self.scanner.scan_security_headers()
        assert headers['score'] >= 80, f"安全头配置得分过低: {headers['score']:.1f}%"
        
        # 7. 跨站脚本(XSS)
        xss_vulns = self.scanner.test_xss_vulnerabilities()
        assert len(xss_vulns) == 0, f"发现 {len(xss_vulns)} 个XSS漏洞"
        
        # 8. 不安全的反序列化
        # 这里可以添加反序列化测试
        
        # 9. 使用含有已知漏洞的组件
        # 这里可以添加依赖库漏洞扫描
        
        # 10. 不足的日志记录和监控
        # 这里可以添加日志监控测试
        
        logger.info("OWASP Top 10漏洞扫描完成")
    
    @pytest.mark.security
    def test_authentication_security(self):
        """测试认证安全性"""
        logger.info("测试认证安全性")
        
        # 测试密码策略
        weak_passwords = ['123456', 'password', 'admin', '111111', 'qwerty']
        
        for password in weak_passwords:
            response = requests.post(
                f"{self.base_url}/api/auth/register",
                json={
                    'username': f'test_{uuid.uuid4().hex[:8]}',
                    'password': password,
                    'email': f'test_{uuid.uuid4().hex[:8]}@example.com'
                }
            )
            
            # 应该拒绝弱密码
            assert response.status_code != 200, f"系统接受了弱密码: {password}"
        
        # 测试暴力破解保护
        failed_attempts = 0
        for i in range(10):
            response = requests.post(
                f"{self.base_url}/api/auth/login",
                json={'username': 'admin', 'password': f'wrong_password_{i}'}
            )
            
            if response.status_code == 401:
                failed_attempts += 1
            elif response.status_code == 429:  # 被限制
                break
        
        # 应该在多次失败后限制登录
        assert failed_attempts < 10, "系统没有实施暴力破解保护"
        
        # 测试JWT安全性
        # 这里可以添加JWT token测试
        
        logger.info("认证安全性测试完成")
    
    @pytest.mark.security
    def test_data_protection_compliance(self):
        """测试数据保护合规性"""
        logger.info("测试数据保护合规性")
        
        # GDPR合规性检查
        gdpr_result = self.compliance_checker.check_gdpr_compliance()
        assert gdpr_result['compliant'], f"GDPR合规性不足，得分: {gdpr_result['score']:.1f}%"
        
        # 数据加密检查
        encryption_result = self.compliance_checker.check_data_encryption()
        assert encryption_result['https_enabled'], "未启用HTTPS加密"
        assert encryption_result['hsts_enabled'], "未启用HSTS"
        
        # 测试个人数据处理
        personal_data_endpoints = [
            '/api/users/profile',
            '/api/data/personal',
            '/api/reports/user-data'
        ]
        
        for endpoint in personal_data_endpoints:
            # 测试数据最小化原则
            response = requests.get(f"{self.base_url}{endpoint}")
            if response.status_code == 200:
                data = response.json()
                # 检查是否返回了不必要的敏感字段
                sensitive_fields = ['ssn', 'credit_card', 'password', 'secret']
                for field in sensitive_fields:
                    assert field not in str(data).lower(), f"端点 {endpoint} 返回了敏感字段: {field}"
        
        logger.info("数据保护合规性测试完成")
    
    @pytest.mark.security
    def test_penetration_testing(self):
        """执行渗透测试"""
        logger.info("开始渗透测试")
        
        # 目录遍历测试
        traversal_vulns = self.penetration_tester.test_directory_traversal()
        assert len(traversal_vulns) == 0, f"发现 {len(traversal_vulns)} 个目录遍历漏洞"
        
        # 文件上传漏洞测试
        upload_vulns = self.penetration_tester.test_file_upload_vulnerabilities()
        critical_upload_vulns = [v for v in upload_vulns if v['severity'] == 'Critical']
        assert len(critical_upload_vulns) == 0, f"发现 {len(critical_upload_vulns)} 个严重文件上传漏洞"
        
        # API速率限制测试
        rate_limit_issues = self.penetration_tester.test_api_rate_limiting()
        # 速率限制问题是中等严重性，可以有一些，但不应该太多
        assert len(rate_limit_issues) <= 2, f"发现过多API速率限制问题: {len(rate_limit_issues)}"
        
        logger.info("渗透测试完成")
    
    @pytest.mark.security
    def test_api_security(self):
        """测试API安全性"""
        logger.info("测试API安全性")
        
        # 测试CORS配置
        response = requests.options(
            f"{self.base_url}/api/data",
            headers={'Origin': 'https://malicious-site.com'}
        )
        
        cors_header = response.headers.get('Access-Control-Allow-Origin')
        assert cors_header != '*', "CORS配置过于宽松，允许所有来源"
        
        # 测试HTTP方法安全性
        dangerous_methods = ['TRACE', 'TRACK', 'DEBUG']
        for method in dangerous_methods:
            response = requests.request(method, f"{self.base_url}/api/data")
            assert response.status_code in [405, 501], f"危险HTTP方法 {method} 被允许"
        
        # 测试API版本控制
        api_endpoints = ['/api/v1/data', '/api/v2/data']
        for endpoint in api_endpoints:
            response = requests.get(f"{self.base_url}{endpoint}")
            if response.status_code == 200:
                # 检查是否有适当的版本控制
                assert 'version' in response.headers or 'api-version' in response.headers, \
                    f"API端点 {endpoint} 缺少版本信息"
        
        # 测试输入验证
        malicious_inputs = [
            {'id': 'DROP TABLE users'},
            {'id': '<script>alert("xss")</script>'},
            {'id': '../../../etc/passwd'},
            {'id': 'A' * 10000}  # 超长输入
        ]
        
        for malicious_input in malicious_inputs:
            response = requests.post(
                f"{self.base_url}/api/data/query",
                json=malicious_input
            )
            # 应该返回400错误或过滤恶意输入
            assert response.status_code in [400, 422] or \
                   malicious_input['id'] not in response.text, \
                   f"API接受了恶意输入: {malicious_input}"
        
        logger.info("API安全性测试完成")
    
    @pytest.mark.security
    def test_session_management(self):
        """测试会话管理安全性"""
        logger.info("测试会话管理安全性")
        
        # 测试会话固定攻击
        session = requests.Session()
        
        # 登录前获取会话ID
        response = session.get(f"{self.base_url}/api/auth/status")
        pre_login_cookies = session.cookies.get_dict()
        
        # 登录
        login_response = session.post(
            f"{self.base_url}/api/auth/login",
            json={'username': 'test_user', 'password': 'test_password'}
        )
        
        if login_response.status_code == 200:
            # 登录后检查会话ID是否改变
            post_login_cookies = session.cookies.get_dict()
            
            # 会话ID应该在登录后改变
            session_changed = False
            for cookie_name in ['sessionid', 'JSESSIONID', 'PHPSESSID']:
                if (cookie_name in pre_login_cookies and 
                    cookie_name in post_login_cookies and 
                    pre_login_cookies[cookie_name] != post_login_cookies[cookie_name]):
                    session_changed = True
                    break
            
            # 如果有会话cookie，应该在登录后改变
            if pre_login_cookies:
                assert session_changed, "会话ID在登录后没有改变，可能存在会话固定漏洞"
        
        # 测试会话超时
        # 这里可以添加会话超时测试
        
        # 测试安全cookie属性
        for cookie in session.cookies:
            if cookie.name.lower() in ['sessionid', 'token', 'auth']:
                assert cookie.secure or not self.base_url.startswith('https://'), \
                    f"安全cookie {cookie.name} 缺少Secure属性"
                assert cookie.has_nonstandard_attr('HttpOnly'), \
                    f"安全cookie {cookie.name} 缺少HttpOnly属性"
        
        logger.info("会话管理安全性测试完成")
    
    def test_generate_security_report(self):
        """生成安全测试报告"""
        logger.info("生成安全测试报告")
        
        # 执行完整的安全扫描
        self.scanner.scan_security_headers()
        self.scanner.test_sql_injection()
        self.scanner.test_xss_vulnerabilities()
        self.scanner.test_authentication_bypass()
        self.scanner.test_authorization_flaws()
        self.scanner.test_data_exposure()
        self.scanner.test_csrf_protection()
        
        # 生成报告
        security_report = self.scanner.generate_security_report()
        
        # 添加合规性检查结果
        security_report['compliance'] = {
            'gdpr': self.compliance_checker.check_gdpr_compliance(),
            'encryption': self.compliance_checker.check_data_encryption()
        }
        
        # 添加渗透测试结果
        security_report['penetration_testing'] = {
            'directory_traversal': self.penetration_tester.test_directory_traversal(),
            'file_upload': self.penetration_tester.test_file_upload_vulnerabilities(),
            'rate_limiting': self.penetration_tester.test_api_rate_limiting()
        }
        
        # 保存报告
        report_file = os.path.join(self.temp_dir, 'security_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(security_report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"安全测试报告已生成: {report_file}")
        
        # 验证报告
        assert os.path.exists(report_file)
        assert os.path.getsize(report_file) > 0
        
        # 检查安全评分
        total_critical = security_report['summary']['critical']
        total_high = security_report['summary']['high']
        
        # 严重和高危漏洞应该为0
        assert total_critical == 0, f"发现 {total_critical} 个严重漏洞"
        assert total_high == 0, f"发现 {total_high} 个高危漏洞"
        
        logger.info("安全测试报告验证通过")
        
        return security_report


if __name__ == '__main__':
    # 运行安全测试
    pytest.main([
        __file__,
        '-v',
        '-m', 'security',
        '--tb=short',
        '--durations=10'
    ])