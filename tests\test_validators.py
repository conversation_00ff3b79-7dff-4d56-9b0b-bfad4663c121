"""Tests for the data validation utility module.

This module contains comprehensive tests for the InputValidator class,
validating various valid and invalid inputs.
"""

import pytest

from src.database.utils.validators import InputValidator


class TestInputValidator:
    """Test cases for the InputValidator class."""

    def test_validate_schema_name_valid_inputs(self):
        """Test valid schema names."""
        valid_names = [
            "schema1",
            "my_schema",
            "Schema_Name",
            "a",
            "test_schema_123",
            "CamelCaseSchema",
            "schema_with_numbers_123",
        ]

        for name in valid_names:
            assert InputValidator.validate_schema_name(
                name
            ), f"'{name}' should be valid"

    def test_validate_schema_name_invalid_inputs(self):
        """Test invalid schema names."""
        invalid_names = [
            "",  # Empty string
            "123schema",  # Starts with a number
            "_schema",  # Starts with an underscore
            "schema-name",  # Contains a hyphen
            "schema name",  # Contains a space
            "schema.name",  # Contains a period
            "schema@name",  # Contains a special character
            "a" * 64,  # Exceeds maximum length
            "schema!",  # Contains an exclamation mark
            "schema#name",  # Contains a hash symbol
        ]

        for name in invalid_names:
            assert not InputValidator.validate_schema_name(
                name
            ), f"'{name}' should be invalid"

    def test_validate_schema_name_non_string_inputs(self):
        """Test non-string inputs."""
        non_string_inputs = [
            None,
            123,
            [],
            {},
            True,
            False,
        ]

        for input_val in non_string_inputs:
            assert not InputValidator.validate_schema_name(
                input_val
            ), f"{input_val} should be invalid"

    def test_validate_table_name_valid_inputs(self):
        """Test valid table names."""
        valid_names = [
            "users",
            "user_profiles",
            "Table1",
            "my_table_123",
            "CamelCaseTable",
            "a",
            "table_with_long_name_but_within_limit",
        ]

        for name in valid_names:
            assert InputValidator.validate_table_name(name), f"'{name}' should be valid"

    def test_validate_table_name_invalid_inputs(self):
        """Test invalid table names."""
        invalid_names = [
            "",  # Empty string
            "2table",  # Starts with a number
            "_table",  # Starts with an underscore
            "table-name",  # Contains a hyphen
            "table name",  # Contains a space
            "table.name",  # Contains a period
            "table@name",  # Contains a special character
            "a" * 64,  # Exceeds maximum length
            "table$",  # Contains a dollar sign
        ]

        for name in invalid_names:
            assert not InputValidator.validate_table_name(
                name
            ), f"'{name}' should be invalid"

    def test_validate_column_name_valid_inputs(self):
        """Test valid column names."""
        valid_names = [
            "id",
            "user_id",
            "firstName",
            "last_name",
            "Column123",
            "a",
            "created_at",
            "isActive",
        ]

        for name in valid_names:
            assert InputValidator.validate_column_name(
                name
            ), f"'{name}' should be valid"

    def test_validate_column_name_invalid_inputs(self):
        """Test invalid column names."""
        invalid_names = [
            "",  # Empty string
            "1column",  # Starts with a number
            "_column",  # Starts with an underscore
            "column-name",  # Contains a hyphen
            "column name",  # Contains a space
            "column.name",  # Contains a period
            "column@name",  # Contains a special character
            "a" * 64,  # Exceeds maximum length
            "column%",  # Contains a percent sign
        ]

        for name in invalid_names:
            assert not InputValidator.validate_column_name(
                name
            ), f"'{name}' should be invalid"

    def test_validate_identifier_valid_inputs(self):
        """Test valid inputs for generic identifier validation."""
        valid_names = [
            "identifier",
            "my_identifier",
            "Identifier123",
            "a",
            "CamelCase",
        ]

        for name in valid_names:
            is_valid, error_msg = InputValidator.validate_identifier(name)
            assert is_valid, f"'{name}' should be valid, but got error: {error_msg}"
            assert error_msg is None

    def test_validate_identifier_invalid_inputs(self):
        """Test invalid inputs for generic identifier validation."""
        test_cases = [
            ("", "identifier cannot be empty"),
            ("123invalid", "identifier must start with a letter"),
            ("_invalid", "identifier must start with a letter"),
            (
                "invalid-name",
                "identifier can only contain letters, numbers, and underscores",
            ),
            (
                "invalid name",
                "identifier can only contain letters, numbers, and underscores",
            ),
            ("a" * 64, f"identifier length cannot exceed 63 characters"),
        ]

        for name, expected_error_part in test_cases:
            is_valid, error_msg = InputValidator.validate_identifier(name)
            assert not is_valid, f"'{name}' should be invalid"
            assert error_msg is not None
            assert expected_error_part in error_msg

    def test_validate_identifier_non_string_inputs(self):
        """Test non-string inputs for generic identifier validation."""
        non_string_inputs = [None, 123, [], {}, True]

        for input_val in non_string_inputs:
            is_valid, error_msg = InputValidator.validate_identifier(input_val)
            assert not is_valid
            assert "must be a string" in error_msg

    def test_validate_identifier_custom_type(self):
        """Test error messages for custom identifier types."""
        is_valid, error_msg = InputValidator.validate_identifier(
            "123invalid", "table name"
        )
        assert not is_valid
        assert "table name must start with a letter" in error_msg

    def test_is_lowercase_recommended(self):
        """Test lowercase recommendation check."""
        test_cases = [
            ("lowercase", True),
            ("lower_case", True),
            ("lowercase123", True),
            ("CamelCase", False),
            ("UPPERCASE", False),
            ("Mixed_Case", False),
            ("", True),  # Empty string is considered lowercase
        ]

        for name, expected in test_cases:
            result = InputValidator.is_lowercase_recommended(name)
            assert result == expected, f"'{name}' lowercase check should be {expected}"

    def test_normalize_name(self):
        """Test name normalization."""
        test_cases = [
            ("CamelCase", "camelcase"),
            ("UPPERCASE", "uppercase"),
            ("Mixed_Case", "mixed_case"),
            ("lowercase", "lowercase"),
            ("", ""),
            ("123ABC", "123abc"),
        ]

        for input_name, expected in test_cases:
            result = InputValidator.normalize_name(input_name)
            assert (
                result == expected
            ), f"'{input_name}' should normalize to '{expected}', got '{result}'"

    def test_normalize_name_non_string(self):
        """Test normalization of non-string inputs."""
        test_cases = [
            (123, "123"),
            (True, "true"),
            (None, "none"),
        ]

        for input_val, expected in test_cases:
            result = InputValidator.normalize_name(input_val)
            assert (
                result == expected
            ), f"{input_val} should normalize to '{expected}', got '{result}'"

    def test_max_length_boundary(self):
        """Test maximum length boundary conditions."""
        # Test a valid name with exactly 63 characters
        valid_63_char = "a" + "b" * 62  # Starts with a letter, total 63 characters
        assert InputValidator.validate_schema_name(valid_63_char)
        assert InputValidator.validate_table_name(valid_63_char)
        assert InputValidator.validate_column_name(valid_63_char)

        # Test an invalid name with 64 characters
        invalid_64_char = "a" + "b" * 63  # Starts with a letter, total 64 characters
        assert not InputValidator.validate_schema_name(invalid_64_char)
        assert not InputValidator.validate_table_name(invalid_64_char)
        assert not InputValidator.validate_column_name(invalid_64_char)

    def test_pattern_edge_cases(self):
        """Test regex boundary cases."""
        # Test single characters
        assert InputValidator.validate_schema_name("a")
        assert InputValidator.validate_schema_name("Z")

        # Test ending with a number
        assert InputValidator.validate_schema_name("name123")

        # Test underscore in the middle and at the end
        assert InputValidator.validate_schema_name("name_with_underscores")
        assert InputValidator.validate_schema_name("name_")

        # Test mixed case
        assert InputValidator.validate_schema_name("CamelCase_with_123")
