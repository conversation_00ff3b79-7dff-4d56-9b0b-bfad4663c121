"""CDR (Call Detail Record) data importer.

This module provides functionality for importing and processing CDR data.
"""

import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
from shapely.geometry import Point

from .base import AbstractImporter, TelecomImportError, ImportResult, ImportStatus, ImportMetrics

# Monkey patch pandas.read_csv to debug calls
_original_read_csv = pd.read_csv

def debug_read_csv(*args, **kwargs):
    import traceback
    print(f"DEBUG: pandas.read_csv called with args={args[:1]}, kwargs keys={list(kwargs.keys())}")
    print(f"DEBUG: Call stack:")
    for line in traceback.format_stack()[-3:-1]:
        print(f"  {line.strip()}")
    return _original_read_csv(*args, **kwargs)

pd.read_csv = debug_read_csv

# Configure logging
logger = logging.getLogger(__name__)


class CDRImporter(AbstractImporter):
    """Enhanced Call Detail Record data importer with multi-operator support."""

    def __init__(
        self,
        source_path: Union[str, Path] = None,
        config: Optional[Dict[str, Any]] = None,
        db_session=None,
        performance_logger=None,
        operator: str = None,
        batch_size: int = 10000,
        **kwargs,
    ):
        """Initialize CDR importer.

        Args:
            source_path: Path to source data file
            config: Configuration dictionary
            db_session: Database session for direct database operations
            performance_logger: Logger for performance metrics
            operator: Operator name (telefonica, vodafone, telekom). If None, auto-detect from filename
            batch_size: Batch size for processing
            **kwargs: Additional configuration options
        """
        self.name = "CDRImporter"
        self.supported_formats = ["csv", "xlsx", "xls"]
        self.source_path = source_path

        # Auto-detect operator if not provided
        if operator is None and source_path:
            operator = self._detect_operator_from_path(source_path)

        self.operator = operator or 'telefonica'  # Default fallback
        self.batch_size = batch_size

        # Operator-specific configurations
        self.operator_configs = {
            'telefonica': {
                'schema': 'cdr_to2',
                'optional_columns': ["CALLED_NUMBER", "CELL_ID", "SUCCESS_FLAG", "BYTES_UP", "BYTES_DOWN"]
            },
            'vodafone': {
                'schema': 'cdr_vdf',
                'optional_columns': ["MSISDN_B", "LAC_CI", "CALL_RESULT", "DATA_VOL_UP", "DATA_VOL_DOWN"]
            },
            'telekom': {
                'schema': 'cdr_tdg',
                'optional_columns': ["B_NUMBER", "CELL_IDENTITY", "TERMINATION_CAUSE", "VOLUME_UP", "VOLUME_DOWN"]
            }
        }

        # Get operator-specific configuration
        self.operator_config = self.operator_configs.get(operator, self.operator_configs['telefonica'])

        self.config = config or {}
        self.db_session = db_session
        self.performance_logger = performance_logger

        # Initialize database components if session provided
        if db_session:
            from src.database.operations import BulkOperations
            from src.database.schema import SchemaManager

            self.schema_manager = SchemaManager(db_session)
            self.bulk_operations = BulkOperations(db_session)

        # Database context components (for unified manager integration)
        self.pool_manager = None
        self.db_manager = None

        # Prepare config for AbstractImporter
        importer_config = {
            'name': self.name,
            'data_type': 'cdr',
            'supported_formats': self.supported_formats,
            'batch_size': self.config.get('batch_size', 10000),
            **kwargs
        }
        super().__init__(config=importer_config)

    def _detect_operator_from_path(self, file_path: Union[str, Path]) -> str:
        """Auto-detect operator from file path or filename.

        Args:
            file_path: Path to the CDR file

        Returns:
            str: Detected operator name (telefonica, vodafone, telekom)
        """
        path_str = str(file_path).lower()
        filename = Path(file_path).name.lower()

        # Check for operator keywords in path and filename
        if any(keyword in path_str for keyword in ['telefonica', 'tef', 'movistar']):
            logger.info(f"Detected Telefonica operator from path: {file_path}")
            return 'telefonica'
        elif any(keyword in path_str for keyword in ['vodafone', 'vdf', 'voda']):
            logger.info(f"Detected Vodafone operator from path: {file_path}")
            return 'vodafone'
        elif any(keyword in path_str for keyword in ['telekom', 'tdg', 'deutsche', 'dt']):
            logger.info(f"Detected Telekom operator from path: {file_path}")
            return 'telekom'
        else:
            # Try to detect from directory structure
            path_parts = Path(file_path).parts
            for part in path_parts:
                part_lower = part.lower()
                if 'telefonica' in part_lower or 'tef' in part_lower:
                    logger.info(f"Detected Telefonica operator from directory: {part}")
                    return 'telefonica'
                elif 'vodafone' in part_lower or 'vdf' in part_lower:
                    logger.info(f"Detected Vodafone operator from directory: {part}")
                    return 'vodafone'
                elif 'telekom' in part_lower or 'tdg' in part_lower:
                    logger.info(f"Detected Telekom operator from directory: {part}")
                    return 'telekom'

            logger.warning(f"Could not detect operator from path: {file_path}, defaulting to telefonica")
            return 'telefonica'

    def get_operator_schema(self) -> str:
        """Get the database schema for the current operator.

        Returns:
            str: Schema name for the operator
        """
        return self.operator_configs.get(self.operator, {}).get('schema', 'cdr_to2')

    def detect_operator_from_content(self, data: pd.DataFrame) -> str:
        """Detect operator from data content (column names, values).

        Args:
            data: DataFrame with CDR data

        Returns:
            str: Detected operator name
        """
        columns_lower = [col.lower() for col in data.columns]

        # Telefonica-specific column patterns
        telefonica_indicators = ['numero_origen', 'numero_destino', 'celda_id', 'latitud', 'longitud']
        if any(indicator in columns_lower for indicator in telefonica_indicators):
            logger.info("Detected Telefonica operator from column names")
            return 'telefonica'

        # Vodafone-specific column patterns
        vodafone_indicators = ['a_number', 'b_number', 'cell_id', 'lat', 'lon', 'msisdn_b']
        if any(indicator in columns_lower for indicator in vodafone_indicators):
            logger.info("Detected Vodafone operator from column names")
            return 'vodafone'

        # Telekom-specific column patterns
        telekom_indicators = ['b_number', 'cell_identity', 'termination_cause', 'volume_up', 'volume_down']
        if any(indicator in columns_lower for indicator in telekom_indicators):
            logger.info("Detected Telekom operator from column names")
            return 'telekom'

        # If no specific patterns found, keep current operator
        logger.info(f"Could not detect operator from content, keeping current: {self.operator}")
        return self.operator

    def _clean_column_name(self, col_name: str) -> str:
        """Clean and standardize column names using unified rules.

        Args:
            col_name: Original column name

        Returns:
            str: Cleaned column name
        """
        import re
        import unicodedata

        # Convert to string and strip whitespace
        clean_name = str(col_name).strip()

        # Normalize unicode characters (handle German, Spanish, etc.)
        clean_name = unicodedata.normalize('NFKD', clean_name)
        clean_name = clean_name.encode('ascii', 'ignore').decode('ascii')

        # Convert to lowercase
        clean_name = clean_name.lower()

        # Replace spaces, hyphens, and other special chars with underscores
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', clean_name)

        # Replace multiple underscores with single underscore
        clean_name = re.sub(r'_+', '_', clean_name)

        # Remove leading/trailing underscores
        clean_name = clean_name.strip('_')

        # Ensure it doesn't start with a number
        if clean_name and clean_name[0].isdigit():
            clean_name = f'col_{clean_name}'

        # Ensure minimum length
        if not clean_name:
            clean_name = 'unnamed_column'

        return clean_name

    def set_database_context(self, **kwargs):
        """Set database context components for unified manager integration."""
        self.pool_manager = kwargs.get('pool')
        self.db_manager = kwargs.get('db_manager')
        if 'schema_manager' in kwargs:
            self.schema_manager = kwargs['schema_manager']

    async def import_data(self, file_path=None, **kwargs) -> ImportResult:
        """Import CDR data with multi-operator Excel support."""

        import time
        start_time = time.time()
        results = []

        try:
            if file_path:
                self.source_path = Path(file_path)
            if not self.source_path:
                raise ValueError("No source path specified for CDR import")

            if self.source_path.suffix.lower() not in ['.xlsx', '.xls']:
                # For non-Excel, use existing logic
                data = await self._read_data_async()
                if data.empty:
                    return ImportResult(success=False, error_message="No data found")
                processed_data = self._process_operator_data(data)
                storage_result = await self._store_data(processed_data, self.get_operator_schema())
                results.append(storage_result)
            else:
                # Excel handling with multi-sheets
                excel_file = pd.ExcelFile(self.source_path)
                sheet_names = excel_file.sheet_names
                table_name = self.get_table_name(str(self.source_path))

                for sheet in sheet_names:
                    sheet_lower = sheet.lower()
                    if 'telefonica' in sheet_lower:
                        operator = 'telefonica'
                    elif 'vodafone' in sheet_lower:
                        operator = 'vodafone'
                    elif 'telekom' in sheet_lower:
                        operator = 'telekom'
                    else:
                        continue

                    data = pd.read_excel(excel_file, sheet_name=sheet)
                    if data.empty:
                        continue

                    validation = self._validate_cdr_data(data)
                    if not validation['is_valid']:
                        continue

                    processed_data = self._process_operator_data(data)
                    schema = self.operator_configs[operator]['schema']
                    storage_result = await self._store_data(processed_data, schema, table_name)
                    results.append({'operator': operator, 'sheet': sheet, 'records': len(data), 'schema': schema})

            end_time = time.time()
            total_records = sum(r['records'] for r in results if 'records' in r)
            return ImportResult(
                status=ImportStatus.COMPLETED,
                metadata={'results': results, 'message': "Multi-operator CDR import completed", 'records_imported': total_records}
            )

        except Exception as e:
            return ImportResult(status=ImportStatus.FAILED, error_message=str(e))

    def _detect_csv_structure(self, file_path: Path) -> dict:
        """Detect CSV file structure including comments, headers, and delimiters."""
        import csv

        structure = {
            'skip_rows': 0,
            'header_row': 0,
            'delimiter': ',',
            'encoding': 'utf-8',
            'comment_char': None
        }

        # Try different encodings
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    lines = []
                    for i, line in enumerate(f):
                        lines.append(line.strip())
                        if i >= 20:  # Read first 20 lines for analysis
                            break

                structure['encoding'] = encoding
                break
            except UnicodeDecodeError:
                continue

        if not lines:
            return structure

        # Detect comment lines and skip rows
        skip_rows = 0
        for i, line in enumerate(lines):
            if line.startswith('#') or line.startswith('//') or not line.strip():
                skip_rows += 1
            else:
                break

        structure['skip_rows'] = skip_rows

        # Find header row (first non-comment, non-empty line)
        header_line = None
        for i in range(skip_rows, len(lines)):
            if lines[i].strip():
                header_line = lines[i]
                break

        if header_line:
            # Detect delimiter
            sniffer = csv.Sniffer()
            try:
                dialect = sniffer.sniff(header_line, delimiters=',;\t|:')
                structure['delimiter'] = dialect.delimiter
            except:
                # Fallback delimiter detection
                delimiters = [',', ';', '\t', '|', ':']
                delimiter_counts = {}
                for delim in delimiters:
                    delimiter_counts[delim] = header_line.count(delim)

                if delimiter_counts:
                    structure['delimiter'] = max(delimiter_counts, key=delimiter_counts.get)

        return structure

    async def _read_data_async(self) -> pd.DataFrame:
        """Read data from source file asynchronously with intelligent parsing."""

        import asyncio
        from pathlib import Path

        def read_file():
            source_path = Path(self.source_path)
            print(f"DEBUG: _read_data_async.read_file called for {source_path}")

            if source_path.suffix.lower() == '.csv':
                # Use configuration-driven CSV structure detection
                structure = self._detect_csv_structure_with_config(source_path, 'cdr')
                self.logger.info(f"Using CDR CSV structure: {structure}")

                # Enhanced CSV reading with configuration-based structure detection
                import csv
                try:
                    result = pd.read_csv(
                        source_path,
                        encoding=structure['encoding'],
                        delimiter=structure['delimiter'],
                        skiprows=structure['skip_rows'],
                        header=structure['header_row'],  # Use configured header row
                        engine='python',  # More flexible parser
                        on_bad_lines='skip',  # Skip problematic lines
                        quoting=csv.QUOTE_MINIMAL,
                        skipinitialspace=True,
                        comment='#',  # Skip lines starting with #
                        dtype=str,              # Read all columns as strings initially
                        na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA', 'na'],
                        keep_default_na=True
                    )
                    self.logger.info(f"Successfully read {len(result)} rows from CDR CSV")
                    return result
                except (UnicodeDecodeError, pd.errors.ParserError) as e:
                    logger.warning(f"Standard CSV reading failed: {e}, trying fallback options")
                    # Fallback with more lenient parsing
                    encodings = ['utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
                    for encoding in encodings:
                        try:
                            return pd.read_csv(
                                source_path,
                                encoding=encoding,
                                delimiter=structure['delimiter'],
                                skiprows=structure['skip_rows'],
                                engine='python',
                                on_bad_lines='skip',
                                quoting=csv.QUOTE_NONE,
                                skipinitialspace=True,
                                low_memory=False,
                                comment='#',
                                dtype=str,              # Read all as strings
                                na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA', 'na'],
                                keep_default_na=True
                            )
                        except Exception:
                            continue
                    raise ValueError(f"Could not read CSV file with any supported encoding: {source_path}")
            elif source_path.suffix.lower() in ['.xlsx', '.xls']:
                # For Excel files, use enhanced sheet detection
                excel_file = pd.ExcelFile(source_path)
                sheet_names = excel_file.sheet_names

                # Use enhanced sheet detection
                target_sheet, detected_operator = self._detect_operator_sheet(sheet_names, self.operator)

                # Update operator if detected from sheet analysis
                if detected_operator and detected_operator != self.operator:
                    self.logger.info(f"Updated operator from sheet analysis: {self.operator} -> {detected_operator}")
                    self.operator = detected_operator

                self.logger.info(f"Selected sheet: {target_sheet} for operator: {self.operator}")
                return pd.read_excel(source_path, sheet_name=target_sheet)
            else:
                raise ValueError(f"Unsupported file format: {source_path.suffix}")

        # Run file reading in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        data = await loop.run_in_executor(None, read_file)

        logger.info(f"Read {len(data)} records from {self.source_path}")
        return data

    def _detect_operator_sheet(self, sheet_names: List[str], current_operator: str) -> Tuple[str, str]:
        """
        Enhanced operator and sheet detection for CDR Excel files.

        Args:
            sheet_names: List of sheet names in the Excel file
            current_operator: Currently detected operator

        Returns:
            Tuple of (selected_sheet_name, detected_operator)
        """
        # Define comprehensive operator patterns with fuzzy matching
        operator_patterns = {
            'telefonica': ['telefonica', 'tef', 'o2', 'movistar', 'to2', 'client31'],
            'vodafone': ['vodafone', 'vdf', 'vf', 'voda', 'client33'],
            'telekom': ['telekom', 'tdg', 'dt', 'deutsche', 'magenta', 'client32']
        }

        # Score each sheet for operator matching
        sheet_scores = {}
        detected_operator = current_operator

        for sheet_name in sheet_names:
            sheet_lower = sheet_name.lower()
            max_score = 0
            best_operator = None

            # Skip obviously non-data sheets
            if any(skip_word in sheet_lower for skip_word in ['summary', 'statistics', 'info', 'readme', 'legend']):
                continue

            # Score each operator pattern
            for operator, patterns in operator_patterns.items():
                score = 0
                for pattern in patterns:
                    if pattern in sheet_lower:
                        # Exact match gets higher score
                        if pattern == sheet_lower:
                            score += 10
                        # Partial match gets lower score
                        else:
                            score += 5

                if score > max_score:
                    max_score = score
                    best_operator = operator

            sheet_scores[sheet_name] = (max_score, best_operator)

        # Find the best matching sheet
        best_sheet = None
        best_score = 0

        for sheet_name, (score, operator) in sheet_scores.items():
            if score > best_score:
                best_score = score
                best_sheet = sheet_name
                if operator:
                    detected_operator = operator

        # If no operator-specific sheet found, try current operator
        if not best_sheet and current_operator in operator_patterns:
            patterns = operator_patterns[current_operator]
            for sheet_name in sheet_names:
                sheet_lower = sheet_name.lower()
                for pattern in patterns:
                    if pattern in sheet_lower:
                        best_sheet = sheet_name
                        break
                if best_sheet:
                    break

        # Fallback to first data sheet (non-summary)
        if not best_sheet:
            for sheet_name in sheet_names:
                sheet_lower = sheet_name.lower()
                if not any(skip_word in sheet_lower for skip_word in ['summary', 'statistics', 'info', 'readme']):
                    best_sheet = sheet_name
                    break

        # Final fallback to first sheet
        if not best_sheet:
            best_sheet = sheet_names[0]

        self.logger.info(f"Sheet detection results: {dict(sheet_scores)}")
        self.logger.info(f"Selected sheet: {best_sheet}, detected operator: {detected_operator}")

        return best_sheet, detected_operator

    def detect_all_operator_sheets(self, sheet_names: List[str]) -> Dict[str, str]:
        """
        Detect all operator sheets in an Excel file for multi-operator processing.

        Args:
            sheet_names: List of sheet names in the Excel file

        Returns:
            Dictionary mapping operator to sheet name
        """
        operator_patterns = {
            'telefonica': ['telefonica', 'tef', 'o2', 'movistar', 'to2', 'client31'],
            'vodafone': ['vodafone', 'vdf', 'vf', 'voda', 'client33'],
            'telekom': ['telekom', 'tdg', 'dt', 'deutsche', 'magenta', 'client32']
        }

        detected_sheets = {}

        for sheet_name in sheet_names:
            sheet_lower = sheet_name.lower()

            # Skip obviously non-data sheets
            if any(skip_word in sheet_lower for skip_word in ['summary', 'statistics', 'info', 'readme', 'legend']):
                continue

            # Find matching operator
            for operator, patterns in operator_patterns.items():
                for pattern in patterns:
                    if pattern in sheet_lower:
                        detected_sheets[operator] = sheet_name
                        self.logger.info(f"Detected {operator} sheet: {sheet_name}")
                        break
                if operator in detected_sheets:
                    break

        return detected_sheets

    def _validate_cdr_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Validate CDR data quality and completeness."""

        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }

        # Skip required columns validation - accept any column structure
        # Data completeness check is optional and informational only

        return validation_result

    def _process_operator_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Process data with operator-specific transformations."""

        processed_data = data.copy()

        # Operator-specific column mapping
        if self.operator == 'vodafone':
            column_mapping = {
                'RECORD_ID': 'CALL_ID',
                'MSISDN_A': 'CALLER_NUMBER',
                'MSISDN_B': 'CALLED_NUMBER',
                'CALL_START': 'CALL_START_TIME',
                'CALL_DURATION': 'DURATION'
            }
            processed_data = processed_data.rename(columns=column_mapping)

        elif self.operator == 'telekom':
            column_mapping = {
                'CDR_ID': 'CALL_ID',
                'A_NUMBER': 'CALLER_NUMBER',
                'B_NUMBER': 'CALLED_NUMBER',
                'TIMESTAMP_START': 'CALL_START_TIME',
                'CALL_LENGTH': 'DURATION'
            }
            processed_data = processed_data.rename(columns=column_mapping)

        # Add operator metadata
        processed_data['OPERATOR'] = self.operator
        processed_data['IMPORT_TIMESTAMP'] = pd.Timestamp.now()

        return processed_data

    async def _mock_store_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Mock data storage for testing purposes."""

        # Simulate storage delay
        import asyncio
        await asyncio.sleep(0.1)

        return {
            'records_stored': len(data),
            'target_schema': self.operator_config['schema'],
            'storage_method': 'mock',
            'batch_count': (len(data) + self.batch_size - 1) // self.batch_size
        }

        super().__init__(source_path=kwargs.get("source_path", ""), **kwargs)

    def validate_file(self, file_path: Union[str, Path]) -> bool:
        """Validate CDR data file.

        Args:
            file_path: Path to the file

        Returns:
            bool: True if file is valid

        Raises:
            ImportError: If file is invalid
        """
        path = Path(file_path)

        if not path.exists():
            raise ImportError(f"File does not exist: {path}")

        if path.suffix.lower() not in [".csv", ".xlsx", ".xls"]:
            raise ImportError(f"Unsupported file format: {path.suffix}")

        return True

    def validate_data_structure(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate CDR data structure.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple[bool, List[str]]: Validation result and error messages
        """
        errors = []

        # Accept any column structure - no required columns validation
        if data.empty:
            errors.append("Data file is empty")

        return len(errors) == 0, errors

    def validate_data_values(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate CDR data values.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple[bool, List[str]]: Validation result and error messages
        """
        errors = []

        # Skip required columns validation - accept any data structure

        # Validate call duration if present
        if "CALL_DURATION" in data.columns:
            negative_duration = (data["CALL_DURATION"] < 0).sum()
            if negative_duration > 0:
                errors.append(
                    f"Found {negative_duration} negative call duration values"
                )

        # Validate call times if both start and end times are present
        if "CALL_START_TIME" in data.columns and "CALL_END_TIME" in data.columns:
            # Convert to datetime if not already
            if not pd.api.types.is_datetime64_dtype(data["CALL_START_TIME"]):
                data["CALL_START_TIME"] = pd.to_datetime(
                    data["CALL_START_TIME"], errors="coerce"
                )

            if not pd.api.types.is_datetime64_dtype(data["CALL_END_TIME"]):
                data["CALL_END_TIME"] = pd.to_datetime(
                    data["CALL_END_TIME"], errors="coerce"
                )

            # Check for end time before start time
            invalid_times = (
                (data["CALL_END_TIME"] < data["CALL_START_TIME"])
                & data["CALL_START_TIME"].notnull()
                & data["CALL_END_TIME"].notnull()
            ).sum()

            if invalid_times > 0:
                errors.append(
                    f"Found {invalid_times} records where call end time is before start time"
                )

        return len(errors) == 0, errors

    def transform_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Transform CDR data.

        Args:
            data: DataFrame to transform

        Returns:
            pd.DataFrame: Transformed data
        """
        # Create a copy to avoid modifying the original
        transformed = data.copy()

        # Remove duplicate columns and standardize column names
        from src.utils.column_deduplicator import ColumnDeduplicator
        
        # Remove duplicate columns, keeping the most complete one
        transformed, dedup_report = ColumnDeduplicator.remove_duplicate_columns(
            transformed, keep_strategy='best'
        )
        
        if dedup_report['total_removed'] > 0:
            logger.info(f"Removed {dedup_report['total_removed']} duplicate columns in CDR data")
        
        # Standardize column names to lowercase
        new_columns = []
        for col in transformed.columns:
            clean_col = col.lower() if isinstance(col, str) else str(col).lower()
            new_columns.append(clean_col)
        
        transformed.columns = new_columns

        # Convert call times to datetime if needed
        datetime_columns = ["call_start_time", "call_end_time"]
        for col in datetime_columns:
            if col in transformed.columns and not pd.api.types.is_datetime64_dtype(
                transformed[col]
            ):
                transformed[col] = pd.to_datetime(transformed[col], errors="coerce")

        # Calculate call duration if not present but start and end times are available
        if (
            "call_duration" not in transformed.columns
            and "call_start_time" in transformed.columns
            and "call_end_time" in transformed.columns
        ):
            transformed["call_duration"] = (
                transformed["call_end_time"] - transformed["call_start_time"]
            ).dt.total_seconds()

        # Add geometry column if cell tower coordinates are available
        if (
            "cell_tower_lat" in transformed.columns
            and "cell_tower_lon" in transformed.columns
        ):
            transformed["geometry"] = transformed.apply(
                lambda row: Point(row["cell_tower_lon"], row["cell_tower_lat"])
                if pd.notnull(row["cell_tower_lon"])
                and pd.notnull(row["cell_tower_lat"])
                else None,
                axis=1,
            )

        # Add import timestamp
        transformed["import_timestamp"] = pd.Timestamp.now()

        return transformed

    async def _validate_cdr_data_async(self, data: pd.DataFrame) -> pd.DataFrame:
        """Asynchronously validate CDR data with telecommunications-specific checks."""
        # Remove duplicates based on call_id
        if 'call_id' in data.columns:
            data = data.drop_duplicates(subset=['call_id'])

        # Validate call duration
        if 'call_duration' in data.columns:
            data = data[data['call_duration'] >= 0]

        # Validate phone numbers format (basic check)
        if 'caller_number' in data.columns:
            data = data[data['caller_number'].str.len() >= 7]

        return data

    async def _transform_data_async(self, data: pd.DataFrame) -> pd.DataFrame:
        """Asynchronously transform CDR data with geospatial enhancement."""
        transformed = self.transform_data(data)

        # Add geospatial enhancements if coordinates are available
        if 'cell_tower_lat' in transformed.columns and 'cell_tower_lon' in transformed.columns:
            from shapely.geometry import Point
            transformed['geometry'] = transformed.apply(
                lambda row: Point(row['cell_tower_lon'], row['cell_tower_lat'])
                if pd.notna(row['cell_tower_lat']) and pd.notna(row['cell_tower_lon'])
                else None, axis=1
            )

        return transformed

    async def _calculate_cdr_kpis(self, data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Calculate KPIs from CDR data."""
        if data.empty:
            return None

        try:
            # Calculate basic KPIs
            kpis = []

            # Call success rate by cell
            if 'cell_id' in data.columns and 'call_status' in data.columns:
                cell_kpis = data.groupby('cell_id').agg({
                    'call_id': 'count',
                    'call_status': lambda x: (x == 'completed').sum(),
                    'call_duration': 'mean'
                }).reset_index()

                cell_kpis['success_rate'] = (cell_kpis['call_status'] / cell_kpis['call_id']) * 100
                cell_kpis['kpi_type'] = 'call_success_rate'
                cell_kpis['calculation_time'] = pd.Timestamp.now()

                kpis.append(cell_kpis)

            if kpis:
                return pd.concat(kpis, ignore_index=True)

        except Exception as e:
            logger.warning(f"KPI calculation failed: {e}")

        return None

    async def _bulk_insert_async(self, data: pd.DataFrame, table_name: str, batch_id: str = None):
        """Asynchronously insert data using bulk operations."""
        if hasattr(self, "bulk_operations"):
            # Use existing bulk operations with correct schema
            schema = self.operator_config.get('schema', 'cdr_to2')
            self.bulk_operations.bulk_insert_dataframe(data, table_name, schema=schema)
        else:
            logger.warning(f"Bulk operations not available for table {table_name}")

    async def _create_cdr_table_async(self, table_name: str, data_columns: List[str] = None):
        """Asynchronously create CDR table."""
        # Use the updated async method
        await self.create_cdr_table(table_name, data_columns)

    async def process_batch_async(self, data: pd.DataFrame, batch_id: str = None) -> ImportResult:
        """Process a batch of CDR data asynchronously with telecommunications optimizations.

        Args:
            data: DataFrame containing CDR data to process
            batch_id: Optional batch identifier for tracking

        Returns:
            ImportResult: Result of the batch processing
        """
        import time
        start_time = time.time()

        try:
            logger.info(f"Processing CDR batch {batch_id}: {len(data)} records")

            # Enhanced data validation for telecommunications
            validated_data = await self._validate_cdr_data_async(data)

            # Transform data with geospatial enhancement
            transformed_data = await self._transform_data_async(validated_data)

            # Calculate telecommunications-specific KPIs
            kpi_data = await self._calculate_cdr_kpis(transformed_data)

            # Insert into database if session available
            if hasattr(self, "bulk_operations"):
                table_name = self.config.get("cdr_table_name", "call_detail_records")

                # Create table if it doesn't exist
                target_schema = self.operator_configs.get(self.operator, {}).get('schema', 'cdr_to2')
                if not await self.schema_manager.table_exists(table_name, target_schema):
                    # Ensure schema exists first
                    await self.schema_manager.ensure_schema_exists(target_schema)
                    await self.create_cdr_table(table_name, list(transformed_data.columns), target_schema)

                # Insert data using async bulk operations
                await self._bulk_insert_async(transformed_data, table_name, batch_id)

                # Insert KPI data if available
                if kpi_data is not None:
                    kpi_table = self.config.get("cdr_kpi_table", "cdr_kpis")
                    await self._bulk_insert_async(kpi_data, kpi_table, batch_id)

            processing_time = time.time() - start_time

            # Create metrics object
            metrics = ImportMetrics(
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.now(),
                records_processed=len(transformed_data),
                processing_time_seconds=processing_time,
                throughput_records_per_second=len(transformed_data) / processing_time if processing_time > 0 else 0
            )

            return ImportResult(
                status=ImportStatus.COMPLETED,
                data=transformed_data,
                metrics=metrics,
                metadata={
                    "batch_id": batch_id,
                    "columns": list(transformed_data.columns),
                    "has_geometry": "geometry" in transformed_data.columns,
                    "kpi_calculated": kpi_data is not None,
                    "processing_time_seconds": processing_time,
                },
            )

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"CDR batch processing failed for {batch_id}: {e}")

            # Create metrics object for failed operation
            metrics = ImportMetrics(
                start_time=datetime.fromtimestamp(start_time),
                end_time=datetime.now(),
                records_processed=0,
                processing_time_seconds=processing_time,
                errors=[str(e)]
            )

            return ImportResult(
                status=ImportStatus.FAILED,
                error_message=str(e),
                metrics=metrics,
                metadata={
                    "batch_id": batch_id,
                    "processing_time_seconds": processing_time,
                }
            )

    def process_batch(self, data: pd.DataFrame) -> ImportResult:
        """Process a batch of CDR data (synchronous wrapper for backward compatibility).

        Args:
            data: DataFrame to process

        Returns:
            ImportResult: Result of the batch processing
        """
        import asyncio

        # Run async version for enhanced processing
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an event loop, create a task
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.process_batch_async(data))
                    return future.result()
            else:
                return loop.run_until_complete(self.process_batch_async(data))
        except RuntimeError:
            # No event loop running, create new one
            return asyncio.run(self.process_batch_async(data))

    async def create_cdr_table(self, table_name: str, data_columns: List[str] = None, target_schema: str = None) -> None:
        """Create CDR table in database with dynamic schema.

        Args:
            table_name: Name of the table to create
            data_columns: List of data column names from the file
            target_schema: Target schema name (e.g., cdr_to2, cdr_vdf, cdr_tdg)
        """
        from src.database.schema import ColumnSchema, TableSchema
        import re

        # Determine target schema based on operator
        if not target_schema:
            target_schema = self.operator_configs.get(self.operator, {}).get('schema', 'cdr_to2')

        # Base columns with primary key and timestamp
        columns = [
            ColumnSchema(name="id", data_type="BIGSERIAL", primary_key=True),
            ColumnSchema(name="created_at", data_type="TIMESTAMP", default="CURRENT_TIMESTAMP"),
        ]

        # Add data columns as TEXT type (following PRD requirements)
        if data_columns:
            for col_name in data_columns:
                # Use unified column name cleaning
                clean_name = self._clean_column_name(col_name)

                if clean_name and clean_name not in ['id', 'created_at']:
                    columns.append(ColumnSchema(name=clean_name, data_type="TEXT"))

        # Define table schema
        table_schema = TableSchema(
            name=table_name,
            schema=target_schema,  # Set the schema name
            columns=columns,
            indexes=[],  # Simplified - no indexes for now to avoid complexity
        )

        # Create table (would use schema manager if available)
        if hasattr(self, 'schema_manager') and self.schema_manager:
            await self.schema_manager.create_table(table_schema)
        else:
            logger.info(f"Table schema created for {table_name}: {len(table_schema.columns)} columns")

    async def import_data(self, file_path=None, **kwargs) -> ImportResult:
        """Import CDR data from file.

        Args:
            file_path: Path to the file (can be positional or keyword argument)
            **kwargs: Additional import options
                - batch_size: Number of records per batch

        Returns:
            ImportResult: Result of the import operation
        """
        print(f"DEBUG: CDR import_data called with file_path={file_path}")
        file_path = file_path or kwargs.get("file_path") or self.source_path
        batch_size = kwargs.get("batch_size", 10000)

        try:
            # Validate file
            self.validate_file(file_path)

            # Start performance monitoring if available
            if self.performance_logger:
                self.performance_logger.start_operation("cdr_import")

            # Read file based on extension with intelligent parsing
            path = Path(file_path)
            if path.suffix.lower() == ".csv":
                # Use configuration-driven CSV structure detection
                structure = self._detect_csv_structure_with_config(path, 'cdr')

                # Enhanced CSV reading with configuration-based structure detection
                import csv
                try:
                    df = pd.read_csv(
                        file_path,
                        encoding=structure['encoding'],
                        delimiter=structure['delimiter'],
                        skiprows=structure['skip_rows'],
                        header=structure['header_row'],  # Use configured header row
                        engine='python',  # More flexible parser
                        on_bad_lines='skip',  # Skip problematic lines
                        quoting=csv.QUOTE_MINIMAL,
                        skipinitialspace=True,
                        comment='#',  # Skip lines starting with #
                        dtype=str,              # Read all columns as strings initially
                        na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA', 'na'],
                        keep_default_na=True
                    )
                except (UnicodeDecodeError, pd.errors.ParserError) as e:
                    self.logger.warning(f"Standard CSV reading failed: {e}, trying with fallback options")
                    # Fallback with different encodings and more lenient parsing
                    encodings = ['utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
                    df = None
                    for encoding in encodings:
                        try:
                            df = pd.read_csv(
                                file_path,
                                encoding=encoding,
                                delimiter=structure['delimiter'],
                                skiprows=structure['skip_rows'],
                                engine='python',
                                on_bad_lines='skip',
                                quoting=csv.QUOTE_NONE,
                                skipinitialspace=True,
                                low_memory=False,
                                comment='#',
                                dtype=str,              # Read all as strings
                                na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA', 'na'],
                                keep_default_na=True
                            )
                            self.logger.info(f"Successfully read CSV with encoding: {encoding}")
                            break
                        except Exception:
                            continue
                    if df is None:
                        raise ImportError(f"Could not read CSV file with any supported encoding: {file_path}")
            elif path.suffix.lower() in [".xlsx", ".xls"]:
                # For Excel files, try to detect the correct sheet based on operator
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names

                # Try to find operator-specific sheet
                target_sheet = None
                operator_lower = self.operator.lower()

                for sheet_name in sheet_names:
                    sheet_lower = sheet_name.lower()
                    if operator_lower in sheet_lower:
                        target_sheet = sheet_name
                        self.logger.info(f"Found operator-specific sheet: {sheet_name}")
                        break

                # If no operator-specific sheet found, use first non-statistics sheet
                if not target_sheet:
                    for sheet_name in sheet_names:
                        if 'statistics' not in sheet_name.lower():
                            target_sheet = sheet_name
                            self.logger.info(f"Using first data sheet: {sheet_name}")
                            break

                # Fallback to first sheet
                if not target_sheet:
                    target_sheet = sheet_names[0]
                    self.logger.warning(f"Using fallback sheet: {target_sheet}")

                df = pd.read_excel(file_path, sheet_name=target_sheet)
            else:
                raise ImportError(f"Unsupported file format: {path.suffix}")

            # Validate data
            is_valid_structure, structure_errors = self.validate_data_structure(df)
            if not is_valid_structure:
                return ImportResult(
                    success=False,
                    source_path=path,
                    error_message=f"Data structure validation failed: {', '.join(structure_errors)}",
                )

            is_valid_values, value_errors = self.validate_data_values(df)
            if not is_valid_values:
                return ImportResult(
                    success=False,
                    source_path=path,
                    error_message=f"Data value validation failed: {', '.join(value_errors)}",
                )

            # Process data in batches
            total_records = len(df)
            processed_records = 0

            for i in range(0, total_records, batch_size):
                batch = df.iloc[i : i + batch_size]
                batch_result = self.process_batch(batch)

                if batch_result.status != ImportStatus.COMPLETED:
                    return ImportResult(
                        success=False,
                        source_path=path,
                        records_imported=processed_records,
                        error_message=f"Failed at batch starting at record {i}: {batch_result.error_message}",
                    )

                processed_records += len(batch)

            # Stop performance monitoring if available
            if self.performance_logger:
                self.performance_logger.end_operation("cdr_import")

            return ImportResult(
                success=True,
                source_path=path,
                records_imported=processed_records,
                records_failed=0,
                file_size_bytes=path.stat().st_size,
                processing_time=0.0,
                metadata={"file_type": path.suffix, "columns": list(df.columns)},
            )

        except Exception as e:
            logger.error(f"CDR import error: {str(e)}", exc_info=True)
            return ImportResult(
                success=False,
                source_path=Path(file_path) if file_path else None,
                error_message=f"Import failed: {str(e)}",
                records_imported=0,
                records_failed=0,
                processing_time=0.0
            )

    def import_file(self, file_path: Union[str, Path]) -> ImportResult:
        """Synchronous wrapper for import_data.

        Args:
            file_path: Path to the file

        Returns:
            ImportResult: Result of the import operation
        """
        import asyncio

        # Update source path
        self.source_path = Path(file_path)

        # Run import asynchronously
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an event loop, create a task
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self.import_data(file_path=file_path))
                    return future.result()
            else:
                return loop.run_until_complete(self.import_data(file_path=file_path))
        except RuntimeError:
            # No event loop running, create new one
            return asyncio.run(self.import_data(file_path=file_path))

    def get_source_info(self) -> Dict[str, Any]:
        """Get source information for the importer."""
        return {
            'source_path': str(self.source_path) if self.source_path else None,
            'data_type': 'cdr',
            'operator': self.operator,
            'supported_formats': self.supported_formats
        }

    async def validate_source(self, source_path: str = None) -> bool:
        """Validate the source file."""
        try:
            from pathlib import Path

            path = Path(source_path or self.source_path)

            # Check if file exists
            if not path.exists():
                return False

            # Check file extension
            if path.suffix.lower() not in ['.csv', '.xlsx', '.xls']:
                return False

            return True

        except Exception:
            return False
