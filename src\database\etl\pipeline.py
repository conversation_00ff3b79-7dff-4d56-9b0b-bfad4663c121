# -*- coding: utf-8 -*-
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""ETL Pipeline Module.

This module provides a complete ETL (Extract, Transform, Load) pipeline
that orchestrates data extraction, cleaning/transformation, and loading operations.
"""

import asyncio
import json
import logging
import re
from datetime import datetime
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union

import pandas as pd

# Use built-in transformer for data cleaning
# DataCleaner functionality is now integrated into the transformer module
class DataCleaner:
    """Built-in data cleaner using transformer functionality."""
    
    def clean_dataframe(self, df, clean_columns=True, add_metadata=True, 
                       validate_types=True, null_strategy="default", **kwargs):
        """Clean dataframe using built-in functionality.
        
        Args:
            df: Input dataframe
            clean_columns: Whether to clean column names
            add_metadata: Whether to add metadata columns
            validate_types: Whether to validate data types
            null_strategy: Strategy for handling null values
            **kwargs: Additional options
            
        Returns:
            Cleaned dataframe
        """
        cleaned_df = df.copy()
        
        # Basic cleaning operations
        if clean_columns:
            # Clean column names
            cleaned_df.columns = [self._clean_column_name(col) for col in cleaned_df.columns]
        
        if add_metadata:
            # Add basic metadata
            cleaned_df['_processed_at'] = pd.Timestamp.now()
            cleaned_df['_row_id'] = range(len(cleaned_df))
        
        return cleaned_df
    
    def _clean_column_name(self, name):
        """Clean column name."""
        # Convert to lowercase and replace spaces/special chars with underscores
        cleaned = re.sub(r'[^a-zA-Z0-9_]', '_', str(name).lower())
        # Remove multiple underscores
        cleaned = re.sub(r'_+', '_', cleaned)
        # Remove leading/trailing underscores
        cleaned = cleaned.strip('_')
        return cleaned or 'unnamed_column'

from ..connection.pool import DatabasePoolManager as ConnectionPool
from ..exceptions import (
    ETLError,
    PipelineError,
    DataProcessingError,
    BatchProcessingError,
    RetryableError,
    NonRetryableError,
    FileOperationError,
    ConfigurationError,
    TransformationError,
)
from ..exception_handlers import (
    with_exception_handling,
    with_retry,
    ExceptionHandlerConfig,
    create_error_context,
)
from .extractor import DataExtractor
from .loader import DataLoader

# Setup logging
logger = logging.getLogger(__name__)


class ETLPipeline:
    """Complete ETL Pipeline for data processing.

    Orchestrates the entire ETL process:
    1. Extract: Read data from various file formats
    2. Transform: Clean and transform data using DataCleaner
    3. Load: Load data into PostgreSQL database

    Features:
    - Support for multiple file formats (CSV, TSV, JSON, TXT)
    - Configurable data cleaning and transformation
    - Batch processing for large datasets
    - Error handling and recovery
    - Pipeline monitoring and logging
    - Transaction management
    """

    def __init__(self, config_or_connection_pool, connection_pool=None):
        """Initialize the ETL pipeline.

        Args:
            config_or_connection_pool: Either a config object or connection pool (for backward compatibility)
            connection_pool: Database connection pool (when first arg is config)
        """
        # Handle both old and new initialization patterns
        if hasattr(config_or_connection_pool, 'pipeline') or hasattr(config_or_connection_pool, 'etl'):
            # New pattern: first arg is config
            self.config = config_or_connection_pool
            self.connection_pool = connection_pool
            # Extract pipeline attributes from config
            if hasattr(self.config, 'pipeline'):
                self.name = getattr(self.config.pipeline, 'name', 'default_pipeline')
                self.parallel_workers = getattr(self.config.pipeline, 'parallel_workers', 1)
            else:
                self.name = 'default_pipeline'
                self.parallel_workers = 1
        else:
            # Old pattern: first arg is connection pool
            self.connection_pool = config_or_connection_pool
            self.config = None
            self.name = 'default_pipeline'
            self.parallel_workers = 1
            
        self.logger = logging.getLogger(self.__class__.__name__)

        # Initialize components
        self.extractor = DataExtractor()
        self.transformer = DataCleaner()
        if self.connection_pool:
            self.loader = DataLoader(self.connection_pool)
        else:
            self.loader = None

        # Pipeline state
        self.pipeline_id = None
        self.start_time = None
        self.end_time = None
        self.status = "initialized"
        self.results = {}

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=2,
            retry_delay=5.0,
            retryable_exceptions=[RetryableError, ConnectionError],
            enable_degradation=True,
            enable_alerting=True,
            alert_threshold=1,
        ),
        operation_name="etl_pipeline",
        context_factory=lambda self, config, pipeline_id=None: create_error_context(
            pipeline_id=pipeline_id or f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            config_keys=list(config.keys()) if config else [],
            pipeline_name=getattr(self, 'name', 'default_pipeline'),
        ),
    )
    async def run_pipeline(
        self, config: Dict[str, Any], pipeline_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Run the complete ETL pipeline.

        Args:
            config: Pipeline configuration
            pipeline_id: Optional pipeline identifier

        Returns:
            Dict[str, Any]: Pipeline execution results
        """
        self.pipeline_id = (
            pipeline_id or f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        self.start_time = datetime.now()
        self.status = "running"

        self.logger.info(f"Starting ETL pipeline {self.pipeline_id}")

        try:
            # Validate configuration
            await self._validate_config_async(config)

            # Extract data
            extracted_data = await self._extract_phase_with_retry(config.get("extract", {}))

            # Transform data
            transformed_data = await self._transform_phase_with_retry(
                extracted_data, config.get("transform", {})
            )

            # Load data
            load_results = await self._load_phase_with_retry(
                transformed_data, config.get("load", {})
            )

            self.status = "completed"
            self.end_time = datetime.now()

            self.results = {
                "pipeline_id": self.pipeline_id,
                "status": self.status,
                "start_time": self.start_time.isoformat(),
                "end_time": self.end_time.isoformat(),
                "duration_seconds": (self.end_time - self.start_time).total_seconds(),
                "extracted_files": len(extracted_data),
                "load_results": load_results,
                "success": True,
            }

            self.logger.info(f"ETL pipeline {self.pipeline_id} completed successfully")
            return self.results

        except Exception as e:
            self.status = "failed"
            self.end_time = datetime.now()

            # Wrap in appropriate exception type
            if isinstance(e, (ETLError, PipelineError, DataProcessingError)):
                raise e
            else:
                raise PipelineError(
                    f"ETL pipeline {self.pipeline_id} failed",
                    pipeline_name=self.name,
                    stage="pipeline_execution",
                    original_error=str(e),
                ) from e

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=1,
            retry_delay=2.0,
            retryable_exceptions=[RetryableError],
            enable_alerting=True,
        ),
        operation_name="pipeline_stages",
        context_factory=lambda self, config: create_error_context(
            pipeline_name=getattr(self, 'name', 'default_pipeline'),
            stages_count=len(config.get('stages', [])),
        ),
    )
    async def run(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Run pipeline with stages configuration.
        
        Args:
            config: Pipeline configuration with stages
            
        Returns:
            Dict[str, Any]: Pipeline execution results
        """
        stages = config.get('stages', [])
        stages_completed = 0
        current_data = None
        
        if not stages:
            raise PipelineError(
                "No stages defined in pipeline configuration",
                pipeline_name=self.name,
                stage="configuration",
            )
        
        try:
            for stage_index, stage in enumerate(stages):
                try:
                    result = await self._execute_stage_with_retry(stage, current_data)
                    current_data = result.get('data')
                    stages_completed += 1
                except Exception as e:
                    stage_name = stage.get('name', f'stage_{stage_index}')
                    raise PipelineError(
                        f"Stage '{stage_name}' failed",
                        pipeline_name=self.name,
                        stage=stage_name,
                        original_error=str(e),
                    ) from e
                
            return {
                'status': 'success',
                'stages_completed': stages_completed,
                'data': current_data
            }
        except PipelineError:
            raise
        except Exception as e:
            raise PipelineError(
                f"Pipeline execution failed at stage {stages_completed + 1}",
                pipeline_name=self.name,
                stage=f"stage_{stages_completed}",
                original_error=str(e),
            ) from e
    
    @with_retry(
        max_retries=2,
        retry_delay=1.0,
        retryable_exceptions=[RetryableError, ConnectionError],
    )
    async def _execute_stage_with_retry(self, stage: Dict[str, Any], data: Any = None) -> Dict[str, Any]:
        """Execute a single pipeline stage with retry mechanism.
        
        Args:
            stage: Stage configuration
            data: Input data for the stage
            
        Returns:
            Dict[str, Any]: Stage execution result
        """
        return await self._execute_stage(stage, data)
    
    async def _execute_stage(self, stage: Dict[str, Any], data: Any = None) -> Dict[str, Any]:
        """Execute a single pipeline stage.
        
        Args:
            stage: Stage configuration
            data: Input data for the stage
            
        Returns:
            Dict[str, Any]: Stage execution result
        """
        stage_type = stage.get('type')
        stage_name = stage.get('name', stage_type)
        
        self.logger.info(f"Executing stage: {stage_name}")
        
        try:
            if stage_type == 'extract':
                result_data = await self._extract_phase(stage.get('config', {}))
            elif stage_type == 'transform':
                result_data = await self._transform_phase(data, stage.get('config', {}))
            elif stage_type == 'validate':
                # For validation, return the input data if validation passes
                await self._validate_phase(data, stage.get('config', {}))
                result_data = data
            elif stage_type == 'load':
                await self._load_phase(data, stage.get('config', {}))
                result_data = data
            else:
                raise PipelineError(
                    f"Unknown stage type: {stage_type}",
                    pipeline_name=self.name,
                    stage=stage_name,
                )
                
            return {
                'status': 'success',
                'data': result_data,
                'stage': stage_name
            }
        except PipelineError:
            raise
        except Exception as e:
            self.logger.error(f"Stage {stage_name} failed: {e}")
            raise PipelineError(
                f"Stage '{stage_name}' execution failed",
                pipeline_name=self.name,
                stage=stage_name,
                original_error=str(e),
            ) from e

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=1,
            retry_delay=2.0,
            retryable_exceptions=[RetryableError, BatchProcessingError],
            enable_alerting=True,
        ),
        operation_name="parallel_processing",
        context_factory=lambda self, data_batches: create_error_context(
            pipeline_name=getattr(self, 'name', 'default_pipeline'),
            batch_count=len(data_batches),
            parallel_workers=getattr(self, 'parallel_workers', 1),
        ),
    )
    async def process_parallel(self, data_batches: List[Any]) -> List[Dict[str, Any]]:
        """Process multiple data batches in parallel.
        
        Args:
            data_batches: List of data batches to process
            
        Returns:
            List[Dict[str, Any]]: Results from processing each batch
        """
        if not data_batches:
            return []
        
        try:
            tasks = [self._process_batch_with_retry(batch, i) for i, batch in enumerate(data_batches)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check for exceptions in results
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    raise BatchProcessingError(
                        f"Batch {i} processing failed",
                        batch_index=i,
                        total_batches=len(data_batches),
                        original_error=str(result),
                    ) from result
                processed_results.append(result)
            
            return processed_results
        except Exception as e:
            if isinstance(e, BatchProcessingError):
                raise e
            else:
                raise BatchProcessingError(
                    "Parallel processing failed",
                    batch_index=-1,
                    total_batches=len(data_batches),
                    original_error=str(e),
                ) from e
    
    @with_retry(
        max_retries=3,
        retry_delay=0.5,
        retryable_exceptions=[RetryableError, ConnectionError, TimeoutError],
    )
    async def _process_batch_with_retry(self, batch: Any, batch_index: int = 0) -> Dict[str, Any]:
        """Process a single batch with retry mechanism.
        
        Args:
            batch: Data batch to process
            batch_index: Index of the batch for tracking
            
        Returns:
            Dict[str, Any]: Batch processing result
        """
        try:
            return await self._process_batch(batch)
        except Exception as e:
            raise BatchProcessingError(
                f"Batch {batch_index} processing failed",
                batch_index=batch_index,
                original_error=str(e),
            ) from e
    
    async def _process_batch(self, batch: Any) -> Dict[str, Any]:
        """Process a single data batch.
        
        Args:
            batch: Data batch to process
            
        Returns:
            Dict[str, Any]: Processing result
        """
        # Default implementation - can be overridden
        return {
            'status': 'success',
            'records': len(batch) if hasattr(batch, '__len__') else 1
        }
    
    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=0,  # No retries for metrics collection
            enable_degradation=True,
            degradation_fallback=lambda: {"status": "degraded", "message": "Metrics collection failed"},
        ),
        operation_name="get_metrics",
    )
    def get_metrics(self) -> Dict[str, Any]:
        """Get pipeline metrics and monitoring information.
        
        Returns:
            Dict[str, Any]: Pipeline metrics
        """
        try:
            return {
                'pipeline_name': getattr(self, 'name', 'default_pipeline'),
                'start_time': getattr(self, 'start_time', None),
                'stages_completed': getattr(self, 'stages_completed', 0),
                'records_processed': getattr(self, 'records_processed', 0),
                'status': getattr(self, 'status', 'initialized'),
                'parallel_workers': getattr(self, 'parallel_workers', 1),
                'error_rate': self._calculate_error_rate() if hasattr(self, '_calculate_error_rate') else 0,
                'throughput': self._calculate_throughput() if hasattr(self, '_calculate_throughput') else 0,
            }
        except Exception as e:
            self.logger.warning(f"Failed to collect complete metrics: {e}")
            # Return basic metrics if detailed collection fails
            return {
                'pipeline_name': getattr(self, 'name', 'default_pipeline'),
                'status': getattr(self, 'status', 'initialized'),
                'error': f"Metrics collection failed: {str(e)}"
            }

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=2,
            retry_delay=3.0,
            retryable_exceptions=[RetryableError, ConnectionError],
            enable_alerting=True,
        ),
        operation_name="file_pipeline",
        context_factory=lambda self, file_path, **kwargs: create_error_context(
            pipeline_name=getattr(self, 'name', 'default_pipeline'),
            file_path=str(file_path),
            operation="file_processing",
        ),
    )
    async def run_file_pipeline(
        self,
        file_path: Union[str, Path],
        table_name: str,
        schema_name: str = "public",
        transform_config: Optional[Dict[str, Any]] = None,
        load_config: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Run ETL pipeline for a single file.

        Args:
            file_path: Path to the input file
            table_name: Target table name
            schema_name: Target schema name
            transform_config: Transformation configuration
            load_config: Loading configuration

        Returns:
            Dict[str, Any]: Pipeline results
        """
        config = {
            "extract": {"files": [{"path": str(file_path)}]},
            "transform": transform_config or {},
            "load": {
                "mappings": [
                    {
                        "source": str(file_path),
                        "table_name": table_name,
                        "schema_name": schema_name,
                        **(load_config or {}),
                    }
                ]
            },
        }

        try:
            # Validate file exists and is readable
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileOperationError(
                    f"File not found: {file_path}",
                    file_path=str(file_path),
                    operation="read",
                )
            
            return await self.run_pipeline(config)
        except (PipelineError, FileOperationError):
            raise
        except Exception as e:
            self.logger.error(f"File pipeline failed for {file_path}: {e}")
            raise PipelineError(
                f"File pipeline processing failed for {file_path}",
                pipeline_name=self.name,
                stage="file_processing",
                original_error=str(e),
            ) from e

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=1,
            retry_delay=5.0,
            retryable_exceptions=[RetryableError, ConnectionError, BatchProcessingError],
            enable_alerting=True,
        ),
        operation_name="batch_pipeline",
        context_factory=lambda self, file_mappings, **kwargs: create_error_context(
            pipeline_name=getattr(self, 'name', 'default_pipeline'),
            batch_size=len(file_mappings),
            operation="batch_processing",
        ),
    )
    async def run_batch_pipeline(
        self,
        file_mappings: List[Dict[str, Any]],
        transform_config: Optional[Dict[str, Any]] = None,
        use_transaction: bool = True,
    ) -> Dict[str, Any]:
        """Run ETL pipeline for multiple files.

        Args:
            file_mappings: List of file to table mappings
            transform_config: Transformation configuration
            use_transaction: Whether to use a single transaction

        Returns:
            Dict[str, Any]: Pipeline results
        """
        try:
            # Validate all files exist
            for mapping in file_mappings:
                file_path = Path(mapping.get("file_path", ""))
                if not file_path.exists():
                    raise FileOperationError(
                        f"File not found: {file_path}",
                        file_path=str(file_path),
                        operation="read",
                    )
            
            config = {
                "extract": {
                    "files": [{"path": mapping["file_path"]} for mapping in file_mappings]
                },
                "transform": transform_config or {},
                "load": {"mappings": file_mappings, "use_transaction": use_transaction},
            }

            return await self.run_pipeline(config)
        except (PipelineError, FileOperationError, BatchProcessingError):
            raise
        except Exception as e:
            self.logger.error(f"Batch pipeline failed: {e}")
            raise PipelineError(
                "Batch pipeline processing failed",
                pipeline_name=self.name,
                stage="batch_processing",
                original_error=str(e),
            ) from e

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=2,
            retry_delay=2.0,
            retryable_exceptions=[RetryableError, FileOperationError, ConnectionError],
            enable_alerting=True,
        ),
        operation_name="extract_phase",
        context_factory=lambda self, extract_config: create_error_context(
            pipeline_name=getattr(self, 'name', 'default_pipeline'),
            stage="extract",
            file_count=len(extract_config.get('files', [])),
        ),
    )
    async def _extract_phase(
        self, extract_config: Dict[str, Any]
    ) -> Dict[str, pd.DataFrame]:
        """Execute the extraction phase.

        Args:
            extract_config: Extraction configuration

        Returns:
            Dict[str, pd.DataFrame]: Extracted data
        """
        try:
            self.logger.info("Starting extraction phase")

            files = extract_config.get("files", [])
            if not files:
                raise PipelineError(
                    "No files specified for extraction",
                    pipeline_name=self.name,
                    stage="extract",
                )

            file_paths = [file_info["path"] for file_info in files]
            
            # Validate all files exist before processing
            for file_path in file_paths:
                path_obj = Path(file_path)
                if not path_obj.exists():
                    raise FileOperationError(
                        f"File not found: {file_path}",
                        file_path=file_path,
                        operation="read",
                    )

            # Extract data from all files
            extracted_data = await self.extractor.extract_multiple_files(
            file_paths, **extract_config.get("options", {})
        )

            # Filter out failed extractions
            successful_extractions = {
                path: df for path, df in extracted_data.items() if df is not None
            }

            if not successful_extractions:
                raise PipelineError(
                    "No files were successfully extracted",
                    pipeline_name=self.name,
                    stage="extract",
                )

            self.logger.info(
                f"Extraction phase completed: {len(successful_extractions)} files"
            )
            return successful_extractions
        except (PipelineError, FileOperationError):
            raise
        except Exception as e:
            self.logger.error(f"Extraction phase failed: {e}")
            raise PipelineError(
                "Extraction phase failed",
                pipeline_name=self.name,
                stage="extract",
                original_error=str(e),
            ) from e

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=1,
            retry_delay=1.0,
            retryable_exceptions=[RetryableError, TransformationError],
            enable_alerting=True,
        ),
        operation_name="transform_phase",
        context_factory=lambda self, extracted_data, transform_config: create_error_context(
            pipeline_name=getattr(self, 'name', 'default_pipeline'),
            stage="transform",
            data_count=len(extracted_data),
        ),
    )
    async def _transform_phase(
        self, extracted_data: Dict[str, pd.DataFrame], transform_config: Dict[str, Any]
    ) -> Dict[str, pd.DataFrame]:
        """Execute the transformation phase.

        Args:
            extracted_data: Data from extraction phase
            transform_config: Transformation configuration

        Returns:
            Dict[str, pd.DataFrame]: Transformed data
        """
        try:
            self.logger.info("Starting transformation phase")

            if not extracted_data:
                raise PipelineError(
                    "No data provided for transformation",
                    pipeline_name=self.name,
                    stage="transform",
                )

            transformed_data = {}

            for file_path, df in extracted_data.items():
                try:
                    self.logger.info(f"Transforming data from {file_path}")

                    if df is None or df.empty:
                        self.logger.warning(f"Empty dataframe for {file_path}, skipping")
                        continue

                    # Apply data cleaning
                    cleaned_df = self.transformer.clean_dataframe(
                        df,
                        clean_columns=transform_config.get("clean_columns", True),
                        add_metadata=transform_config.get("add_metadata", True),
                        validate_types=transform_config.get("validate_types", True),
                        null_strategy=transform_config.get("null_strategy", "default"),
                        **transform_config.get("cleaner_options", {}),
                    )
                except Exception as e:
                    raise TransformationError(
                        f"Failed to transform data from {file_path}",
                        file_path=file_path,
                        operation="data_cleaning",
                        original_error=str(e),
                    ) from e

                # Apply custom transformations if specified
                custom_transforms = transform_config.get("custom_transforms", [])
                try:
                    for transform_func in custom_transforms:
                        if callable(transform_func):
                            cleaned_df = transform_func(cleaned_df)
                except Exception as e:
                    raise TransformationError(
                        f"Custom transformation failed for {file_path}",
                        file_path=file_path,
                        operation="custom_transform",
                        original_error=str(e),
                    ) from e

                transformed_data[file_path] = cleaned_df

                self.logger.info(
                    f"Transformation completed for {file_path}: "
                    f"{len(cleaned_df)} rows, {len(cleaned_df.columns)} columns"
                )

            self.logger.info(
                f"Transformation phase completed: {len(transformed_data)} files"
            )
            return transformed_data
        except (PipelineError, TransformationError):
            raise
        except Exception as e:
            self.logger.error(f"Transformation phase failed: {e}")
            raise PipelineError(
                "Transformation phase failed",
                pipeline_name=self.name,
                stage="transform",
                original_error=str(e),
            ) from e

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=2,
            retry_delay=3.0,
            retryable_exceptions=[RetryableError, ConnectionError, TimeoutError],
            enable_alerting=True,
        ),
        operation_name="load_phase",
        context_factory=lambda self, transformed_data, load_config: create_error_context(
            pipeline_name=getattr(self, 'name', 'default_pipeline'),
            stage="load",
            data_count=len(transformed_data),
            use_transaction=load_config.get('use_transaction', True),
        ),
    )
    async def _load_phase(
        self, transformed_data: Dict[str, pd.DataFrame], load_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute the loading phase.

        Args:
            transformed_data: Data from transformation phase
            load_config: Loading configuration

        Returns:
            Dict[str, Any]: Loading results
        """
        try:
            self.logger.info("Starting loading phase")

            if not transformed_data:
                raise PipelineError(
                    "No data provided for loading",
                    pipeline_name=self.name,
                    stage="load",
                )

            mappings = load_config.get("mappings", [])
            if not mappings:
                raise PipelineError(
                    "No load mappings specified",
                    pipeline_name=self.name,
                    stage="load",
                )

            use_transaction = load_config.get("use_transaction", True)
            load_results = []

            if use_transaction:
                # Load all data in a single transaction
                try:
                    load_results = await self._load_with_transaction(
                        transformed_data, mappings, load_config
                    )
                except Exception as e:
                    raise PipelineError(
                        "Transaction-based loading failed",
                        pipeline_name=self.name,
                        stage="load",
                        original_error=str(e),
                    ) from e
            else:
                # Load each file separately
                for mapping in mappings:
                    try:
                        source_path = mapping.get("source") or mapping.get("file_path")
                        if source_path in transformed_data:
                            df = transformed_data[source_path]
                            result = await self.loader.load_dataframe(
                                df=df,
                                table_name=mapping["table_name"],
                                schema_name=mapping.get("schema_name", "public"),
                                if_exists=mapping.get("if_exists", "append"),
                                create_table=mapping.get("create_table", True),
                                batch_size=mapping.get("batch_size", 10000),
                                use_transaction=False,
                            )
                            load_results.append(
                                {
                                    "source": source_path,
                                    "table": f"{mapping.get('schema_name', 'public')}.{mapping['table_name']}",
                                    **result,
                                }
                            )
                        else:
                            self.logger.warning(f"No data found for source: {source_path}")
                    except Exception as e:
                        self.logger.error(f"Failed to load data for {source_path}: {e}")
                        load_results.append(
                            {
                                "source": source_path,
                                "table": f"{mapping.get('schema_name', 'public')}.{mapping['table_name']}",
                                "success": False,
                                "error": str(e),
                            }
                        )

            successful_loads = sum(1 for result in load_results if result.get("success"))

            self.logger.info(
                f"Loading phase completed: {successful_loads}/{len(load_results)} successful"
            )

            return {
                "total_loads": len(load_results),
                "successful_loads": successful_loads,
                "results": load_results,
            }
        except (PipelineError, ConnectionError, TimeoutError):
            raise
        except Exception as e:
            self.logger.error(f"Loading phase failed: {e}")
            raise PipelineError(
                "Loading phase failed",
                pipeline_name=self.name,
                stage="load",
                original_error=str(e),
            ) from e

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=2,
            retry_delay=1.0,
            backoff_factor=2.0,
            enable_alerting=True,
            retryable_exceptions=[RetryableError, FileOperationError, ConnectionError],
        ),
        operation_name="load_with_transaction",
    )
    async def _load_with_transaction(
        self,
        transformed_data: Dict[str, pd.DataFrame],
        mappings: List[Dict[str, Any]],
        load_config: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """Load all data within a single transaction."""
        try:
            conn = await self.connection_pool.acquire_connection()
            load_results = []

            try:
                async with conn.transaction():
                    for mapping in mappings:
                        try:
                            source_path = mapping.get("source") or mapping.get("file_path")
                            if source_path in transformed_data:
                                df = transformed_data[source_path]

                                # Use the loader's internal method for transaction-aware loading
                                result = await self.loader._perform_load(
                                    conn=conn,
                                    df=df,
                                    table_name=mapping["table_name"],
                                    schema_name=mapping.get("schema_name", "public"),
                                    if_exists=mapping.get("if_exists", "append"),
                                    create_table=mapping.get("create_table", True),
                                    batch_size=mapping.get("batch_size", 10000),
                                )

                                load_results.append(
                                    {
                                        "source": source_path,
                                        "table": f"{mapping.get('schema_name', 'public')}.{mapping['table_name']}",
                                        **result,
                                    }
                                )
                            else:
                                self.logger.warning(f"No data found for source: {source_path}")
                        except Exception as e:
                            self.logger.error(f"Failed to load {source_path} in transaction: {e}")
                            raise PipelineError(
                                f"Transaction loading failed for {source_path}",
                                pipeline_name=self.name,
                                stage="load",
                                original_error=str(e),
                            ) from e

                    self.logger.info("All data loaded successfully in transaction")

            except Exception as e:
                self.logger.error(f"Transaction failed during loading: {str(e)}")
                raise
            finally:
                await self.connection_pool.release_connection(conn)

            return load_results
        except (PipelineError, ConnectionError, TimeoutError):
            raise
        except Exception as e:
            self.logger.error(f"Transaction loading failed: {e}")
            raise PipelineError(
                "Transaction loading failed",
                pipeline_name=self.name,
                stage="load",
                original_error=str(e),
            ) from e

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=0,
            enable_alerting=True,
        ),
        operation_name="validate_config",
    )
    def _validate_config(self, config: Dict[str, Any]):
        """Validate pipeline configuration.

        Args:
            config: Pipeline configuration to validate

        Raises:
            ValueError: If configuration is invalid
        """
        try:
            required_sections = ["extract", "load"]
            for section in required_sections:
                if section not in config:
                    raise ConfigurationError(f"Missing required configuration section: {section}")

            # Validate extract configuration
            extract_config = config["extract"]
            if "files" not in extract_config or not extract_config["files"]:
                raise ConfigurationError("Extract configuration must specify files")

            # Validate load configuration
            load_config = config["load"]
            if "mappings" not in load_config or not load_config["mappings"]:
                raise ConfigurationError("Load configuration must specify mappings")

            # Validate each mapping
            for mapping in load_config["mappings"]:
                if "table_name" not in mapping:
                    raise ConfigurationError("Each load mapping must specify table_name")
        except ConfigurationError:
            raise
        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            raise ConfigurationError(
                "Configuration validation failed",
                original_error=str(e),
            ) from e

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=2,
            retry_delay=0.5,
            enable_alerting=True,
            retryable_exceptions=[RetryableError, ConnectionError],
        ),
        operation_name="validate_pipeline",
    )
    async def validate_pipeline(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate pipeline configuration and data sources.

        Args:
            config: Pipeline configuration

        Returns:
            Dict[str, Any]: Validation results
        """
        validation_results = {"valid": True, "errors": [], "warnings": []}

        try:
            # Validate configuration structure
            self._validate_config(config)

            # Validate file existence
            extract_config = config.get("extract", {})
            for file_info in extract_config.get("files", []):
                try:
                    file_path = Path(file_info["path"])
                    if not file_path.exists():
                        validation_results["errors"].append(f"File not found: {file_path}")
                        validation_results["valid"] = False
                    else:
                        # Validate file structure
                        file_validation = await self.extractor.validate_file_structure(
                            file_path
                        )
                        if not file_validation.get("valid", True):
                            validation_results["warnings"].append(
                                f"File structure issues in {file_path}: {file_validation}"
                            )
                except Exception as e:
                    validation_results["errors"].append(
                        f"File validation failed for {file_info.get('path', 'unknown')}: {str(e)}"
                    )
                    validation_results["valid"] = False

            # Validate database connectivity
            try:
                conn = await self.connection_pool.acquire_connection()
                await self.connection_pool.release_connection(conn)
            except Exception as e:
                validation_results["errors"].append(
                    f"Database connection failed: {str(e)}"
                )
                validation_results["valid"] = False

        except (ConfigurationError, ValidationError):
            validation_results["errors"].append(
                f"Configuration validation failed: {str(e)}"
            )
            validation_results["valid"] = False
        except Exception as e:
            self.logger.error(f"Pipeline validation failed: {e}")
            validation_results["errors"].append(
                f"Pipeline validation failed: {str(e)}"
            )
            validation_results["valid"] = False

        return validation_results

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=0,
            enable_degradation=True,
            degradation_fallback=lambda: {"pipeline_id": None, "status": "unknown"},
            enable_alerting=True,
        ),
        operation_name="get_pipeline_status",
    )
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get current pipeline status.

        Returns:
            Dict[str, Any]: Pipeline status information
        """
        try:
            return {
                "pipeline_id": self.pipeline_id,
                "status": self.status,
                "start_time": self.start_time.isoformat() if self.start_time else None,
                "end_time": self.end_time.isoformat() if self.end_time else None,
                "duration_seconds": (
                    (self.end_time - self.start_time).total_seconds()
                    if self.start_time and self.end_time
                    else None
                ),
            }
        except Exception as e:
            self.logger.error(f"Failed to get pipeline status: {e}")
            raise PipelineError(
                "Failed to get pipeline status",
                pipeline_name=self.name,
                original_error=str(e),
            ) from e

    @with_exception_handling(
        config=ExceptionHandlerConfig(
            max_retries=2,
            retry_delay=1.0,
            enable_alerting=True,
            retryable_exceptions=[RetryableError],
        ),
        operation_name="cleanup_pipeline",
    )
    async def cleanup_pipeline(self):
        """Clean up pipeline resources."""
        try:
            self.logger.info(f"Cleaning up pipeline {self.pipeline_id}")
            # Add any cleanup logic here if needed
            self.status = "cleaned_up"
        except Exception as e:
            self.logger.error(f"Pipeline cleanup failed: {e}")
            raise PipelineError(
                "Pipeline cleanup failed",
                pipeline_name=self.name,
                original_error=str(e),
            ) from e

    def _should_continue_on_error(self, error: Exception) -> bool:
        """Determine if pipeline should continue on error.
        
        Args:
            error: The exception that occurred
            
        Returns:
            bool: True if pipeline should continue, False otherwise
        """
        if hasattr(self, 'error_handling'):
            return self.error_handling == 'continue'
        return False

    def _should_retry_on_error(self, error: Exception, attempt: int) -> bool:
        """Determine if pipeline should retry on error.
        
        Args:
            error: The exception that occurred
            attempt: Current attempt number
            
        Returns:
            bool: True if pipeline should retry, False otherwise
        """
        if hasattr(self, 'error_handling') and self.error_handling == 'retry':
            max_retries = getattr(self, 'max_retries', 3)
            return attempt < max_retries
        return False
