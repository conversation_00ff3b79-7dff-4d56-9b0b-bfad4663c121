#!/usr/bin/env python3
"""
E2E测试环境设置脚本

该脚本负责:
1. 创建和配置测试数据库
2. 初始化测试数据
3. 启动必要的服务
4. 验证环境配置
5. 生成测试报告

使用方法:
    python setup_test_environment.py --action setup
    python setup_test_environment.py --action teardown
    python setup_test_environment.py --action reset
"""

import os
import sys
import yaml
import asyncio
import argparse
import subprocess
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging
import time
import json
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    import psycopg2
    from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
    import pandas as pd
    import requests
except ImportError as e:
    print(f"缺少必要的依赖包: {e}")
    print("请运行: pip install psycopg2-binary pandas requests")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('e2e_setup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class TestEnvironmentConfig:
    """测试环境配置数据类"""
    database_config: Dict[str, Any]
    api_config: Dict[str, Any]
    test_data_config: Dict[str, Any]
    performance_config: Dict[str, Any]
    security_config: Dict[str, Any]
    monitoring_config: Dict[str, Any]
    cleanup_config: Dict[str, Any]


class TestEnvironmentManager:
    """E2E测试环境管理器"""
    
    def __init__(self, config_path: str):
        """初始化环境管理器"""
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.temp_dirs: List[Path] = []
        self.created_databases: List[str] = []
        self.started_services: List[str] = []
        
    def _load_config(self) -> TestEnvironmentConfig:
        """加载测试配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            return TestEnvironmentConfig(
                database_config=config_data.get('database', {}),
                api_config=config_data.get('api', {}),
                test_data_config=config_data.get('test_data', {}),
                performance_config=config_data.get('performance', {}),
                security_config=config_data.get('security', {}),
                monitoring_config=config_data.get('monitoring', {}),
                cleanup_config=config_data.get('cleanup', {})
            )
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    async def setup_environment(self) -> bool:
        """设置完整的测试环境"""
        logger.info("开始设置E2E测试环境...")
        
        try:
            # 1. 验证系统依赖
            await self._verify_system_dependencies()
            
            # 2. 设置数据库
            await self._setup_database()
            
            # 3. 创建测试数据
            await self._create_test_data()
            
            # 4. 启动服务
            await self._start_services()
            
            # 5. 验证环境
            await self._verify_environment()
            
            # 6. 初始化监控
            await self._setup_monitoring()
            
            logger.info("E2E测试环境设置完成")
            return True
            
        except Exception as e:
            logger.error(f"环境设置失败: {e}")
            await self.teardown_environment()
            return False
    
    async def teardown_environment(self) -> bool:
        """清理测试环境"""
        logger.info("开始清理E2E测试环境...")
        
        try:
            # 1. 停止服务
            await self._stop_services()
            
            # 2. 清理数据库
            await self._cleanup_database()
            
            # 3. 清理临时文件
            await self._cleanup_temp_files()
            
            # 4. 清理监控
            await self._cleanup_monitoring()
            
            logger.info("E2E测试环境清理完成")
            return True
            
        except Exception as e:
            logger.error(f"环境清理失败: {e}")
            return False
    
    async def reset_environment(self) -> bool:
        """重置测试环境"""
        logger.info("重置E2E测试环境...")
        
        success = await self.teardown_environment()
        if success:
            success = await self.setup_environment()
        
        return success
    
    async def _verify_system_dependencies(self):
        """验证系统依赖"""
        logger.info("验证系统依赖...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            raise RuntimeError("需要Python 3.8或更高版本")
        
        # 检查必要的命令行工具
        required_commands = ['psql', 'docker', 'git']
        for cmd in required_commands:
            if not shutil.which(cmd):
                logger.warning(f"未找到命令: {cmd}")
        
        # 检查Python包
        required_packages = [
            'psycopg2', 'pandas', 'requests', 'pytest',
            'sqlalchemy', 'fastapi', 'celery'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.warning(f"缺少Python包: {missing_packages}")
    
    async def _setup_database(self):
        """设置测试数据库"""
        logger.info("设置测试数据库...")
        
        db_config = self.config.database_config.get('test_db', {})
        
        # 连接到PostgreSQL服务器
        admin_conn_params = {
            'host': db_config.get('host', 'localhost'),
            'port': db_config.get('port', 5432),
            'user': 'postgres',  # 使用管理员用户
            'password': os.getenv('POSTGRES_ADMIN_PASSWORD', 'postgres')
        }
        
        try:
            # 创建数据库连接
            conn = psycopg2.connect(**admin_conn_params)
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            # 创建测试数据库
            test_db_name = db_config.get('database', 'connect_test')
            cursor.execute(f"DROP DATABASE IF EXISTS {test_db_name}")
            cursor.execute(f"CREATE DATABASE {test_db_name}")
            self.created_databases.append(test_db_name)
            
            # 创建测试用户
            test_user = db_config.get('username', 'test_user')
            test_password = db_config.get('password', 'test_password')
            
            cursor.execute(f"DROP USER IF EXISTS {test_user}")
            cursor.execute(f"CREATE USER {test_user} WITH PASSWORD '{test_password}'")
            cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {test_db_name} TO {test_user}")
            
            cursor.close()
            conn.close()
            
            # 连接到测试数据库并设置PostGIS
            test_conn_params = {
                'host': db_config.get('host', 'localhost'),
                'port': db_config.get('port', 5432),
                'database': test_db_name,
                'user': test_user,
                'password': test_password
            }
            
            test_conn = psycopg2.connect(**test_conn_params)
            test_conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            test_cursor = test_conn.cursor()
            
            # 启用PostGIS扩展
            if self.config.database_config.get('postgis', {}).get('enabled', True):
                test_cursor.execute("CREATE EXTENSION IF NOT EXISTS postgis")
                test_cursor.execute("CREATE EXTENSION IF NOT EXISTS postgis_topology")
            
            # 创建测试表结构
            await self._create_test_tables(test_cursor)
            
            test_cursor.close()
            test_conn.close()
            
            logger.info(f"测试数据库 {test_db_name} 创建成功")
            
        except Exception as e:
            logger.error(f"数据库设置失败: {e}")
            raise
    
    async def _create_test_tables(self, cursor):
        """创建测试表结构"""
        logger.info("创建测试表结构...")
        
        # EP数据表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS ep_data (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP WITH TIME ZONE,
                longitude DOUBLE PRECISION,
                latitude DOUBLE PRECISION,
                geom GEOMETRY(POINT, 4326),
                signal_strength DOUBLE PRECISION,
                technology VARCHAR(10),
                operator VARCHAR(50),
                cell_id VARCHAR(50),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)
        
        # CDR数据表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS cdr_data (
                id SERIAL PRIMARY KEY,
                call_id VARCHAR(100),
                start_time TIMESTAMP WITH TIME ZONE,
                end_time TIMESTAMP WITH TIME ZONE,
                duration INTEGER,
                caller_location GEOMETRY(POINT, 4326),
                callee_location GEOMETRY(POINT, 4326),
                call_type VARCHAR(20),
                success BOOLEAN,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)
        
        # 站点数据表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS site_data (
                id SERIAL PRIMARY KEY,
                site_id VARCHAR(50) UNIQUE,
                site_name VARCHAR(100),
                location GEOMETRY(POINT, 4326),
                technology VARCHAR(10),
                operator VARCHAR(50),
                status VARCHAR(20),
                coverage_radius DOUBLE PRECISION,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)
        
        # KPI数据表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS kpi_data (
                id SERIAL PRIMARY KEY,
                site_id VARCHAR(50),
                timestamp TIMESTAMP WITH TIME ZONE,
                kpi_type VARCHAR(50),
                kpi_value DOUBLE PRECISION,
                unit VARCHAR(20),
                threshold_min DOUBLE PRECISION,
                threshold_max DOUBLE PRECISION,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)
        
        # 创建索引
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_ep_data_geom ON ep_data USING GIST (geom)",
            "CREATE INDEX IF NOT EXISTS idx_ep_data_timestamp ON ep_data (timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_cdr_data_start_time ON cdr_data (start_time)",
            "CREATE INDEX IF NOT EXISTS idx_site_data_location ON site_data USING GIST (location)",
            "CREATE INDEX IF NOT EXISTS idx_kpi_data_site_timestamp ON kpi_data (site_id, timestamp)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    async def _create_test_data(self):
        """创建测试数据文件"""
        logger.info("创建测试数据文件...")
        
        test_data_dir = Path(self.config.test_data_config.get('files', {}).get('base_path', 'tests/e2e/data'))
        test_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建EP数据测试文件
        await self._create_ep_test_data(test_data_dir)
        
        # 创建CDR数据测试文件
        await self._create_cdr_test_data(test_data_dir)
        
        # 创建站点数据测试文件
        await self._create_site_test_data(test_data_dir)
        
        # 创建KPI数据测试文件
        await self._create_kpi_test_data(test_data_dir)
    
    async def _create_ep_test_data(self, data_dir: Path):
        """创建EP数据测试文件"""
        import numpy as np
        
        # 小数据集 (1000行)
        small_data = {
            'timestamp': pd.date_range('2024-01-01', periods=1000, freq='1min'),
            'longitude': np.random.uniform(116.0, 117.0, 1000),
            'latitude': np.random.uniform(39.5, 40.5, 1000),
            'signal_strength': np.random.uniform(-120, -60, 1000),
            'technology': np.random.choice(['4G', '5G'], 1000),
            'operator': np.random.choice(['移动', '联通', '电信'], 1000),
            'cell_id': [f'CELL_{i:06d}' for i in range(1000)]
        }
        
        df_small = pd.DataFrame(small_data)
        df_small.to_csv(data_dir / 'ep_data_small.csv', index=False)
        
        # 中等数据集 (10000行)
        medium_data = {
            'timestamp': pd.date_range('2024-01-01', periods=10000, freq='30s'),
            'longitude': np.random.uniform(116.0, 117.0, 10000),
            'latitude': np.random.uniform(39.5, 40.5, 10000),
            'signal_strength': np.random.uniform(-120, -60, 10000),
            'technology': np.random.choice(['4G', '5G'], 10000),
            'operator': np.random.choice(['移动', '联通', '电信'], 10000),
            'cell_id': [f'CELL_{i:06d}' for i in range(10000)]
        }
        
        df_medium = pd.DataFrame(medium_data)
        df_medium.to_csv(data_dir / 'ep_data_medium.csv', index=False)
        
        # 大数据集 (100000行)
        large_data = {
            'timestamp': pd.date_range('2024-01-01', periods=100000, freq='6s'),
            'longitude': np.random.uniform(116.0, 117.0, 100000),
            'latitude': np.random.uniform(39.5, 40.5, 100000),
            'signal_strength': np.random.uniform(-120, -60, 100000),
            'technology': np.random.choice(['4G', '5G'], 100000),
            'operator': np.random.choice(['移动', '联通', '电信'], 100000),
            'cell_id': [f'CELL_{i:06d}' for i in range(100000)]
        }
        
        df_large = pd.DataFrame(large_data)
        df_large.to_csv(data_dir / 'ep_data_large.csv', index=False)
        
        # 无效数据集
        invalid_data = {
            'timestamp': ['invalid_date', '2024-01-01 10:00:00', None],
            'longitude': ['invalid_lon', 116.5, 200.0],  # 超出范围
            'latitude': [39.9, 'invalid_lat', -100.0],   # 超出范围
            'signal_strength': [-50, 'invalid_signal', None],
            'technology': ['4G', 'INVALID_TECH', '5G'],
            'operator': ['移动', None, ''],
            'cell_id': ['CELL_001', '', None]
        }
        
        df_invalid = pd.DataFrame(invalid_data)
        df_invalid.to_csv(data_dir / 'ep_data_invalid.csv', index=False)
    
    async def _create_cdr_test_data(self, data_dir: Path):
        """创建CDR数据测试文件"""
        import numpy as np
        
        # 小数据集
        small_data = {
            'call_id': [f'CALL_{i:08d}' for i in range(1000)],
            'start_time': pd.date_range('2024-01-01', periods=1000, freq='5min'),
            'duration': np.random.randint(10, 3600, 1000),
            'caller_longitude': np.random.uniform(116.0, 117.0, 1000),
            'caller_latitude': np.random.uniform(39.5, 40.5, 1000),
            'callee_longitude': np.random.uniform(116.0, 117.0, 1000),
            'callee_latitude': np.random.uniform(39.5, 40.5, 1000),
            'call_type': np.random.choice(['voice', 'video', 'data'], 1000),
            'success': np.random.choice([True, False], 1000, p=[0.95, 0.05])
        }
        
        df_small = pd.DataFrame(small_data)
        df_small['end_time'] = df_small['start_time'] + pd.to_timedelta(df_small['duration'], unit='s')
        df_small.to_csv(data_dir / 'cdr_data_small.csv', index=False)
    
    async def _create_site_test_data(self, data_dir: Path):
        """创建站点数据测试文件"""
        import numpy as np
        
        # 基础站点数据
        basic_data = {
            'site_id': [f'SITE_{i:05d}' for i in range(100)],
            'site_name': [f'基站_{i:03d}' for i in range(100)],
            'longitude': np.random.uniform(116.0, 117.0, 100),
            'latitude': np.random.uniform(39.5, 40.5, 100),
            'technology': np.random.choice(['4G', '5G'], 100),
            'operator': np.random.choice(['移动', '联通', '电信'], 100),
            'status': np.random.choice(['active', 'inactive', 'maintenance'], 100, p=[0.8, 0.1, 0.1]),
            'coverage_radius': np.random.uniform(500, 2000, 100)
        }
        
        df_basic = pd.DataFrame(basic_data)
        df_basic.to_csv(data_dir / 'site_data_basic.csv', index=False)
    
    async def _create_kpi_test_data(self, data_dir: Path):
        """创建KPI数据测试文件"""
        import numpy as np
        
        # 日KPI数据
        dates = pd.date_range('2024-01-01', periods=30, freq='D')
        sites = [f'SITE_{i:05d}' for i in range(10)]
        kpi_types = ['throughput', 'latency', 'packet_loss', 'availability']
        
        daily_data = []
        for date in dates:
            for site in sites:
                for kpi_type in kpi_types:
                    if kpi_type == 'throughput':
                        value = np.random.uniform(50, 200)
                        unit = 'Mbps'
                    elif kpi_type == 'latency':
                        value = np.random.uniform(10, 50)
                        unit = 'ms'
                    elif kpi_type == 'packet_loss':
                        value = np.random.uniform(0, 5)
                        unit = '%'
                    else:  # availability
                        value = np.random.uniform(95, 100)
                        unit = '%'
                    
                    daily_data.append({
                        'site_id': site,
                        'timestamp': date,
                        'kpi_type': kpi_type,
                        'kpi_value': value,
                        'unit': unit
                    })
        
        df_daily = pd.DataFrame(daily_data)
        df_daily.to_csv(data_dir / 'kpi_daily.csv', index=False)
    
    async def _start_services(self):
        """启动必要的服务"""
        logger.info("启动测试服务...")
        
        # 这里可以启动API服务、Redis、Celery等
        # 示例：启动FastAPI服务
        try:
            # 检查API服务是否已运行
            api_url = self.config.api_config.get('base_url', 'http://localhost:8000')
            response = requests.get(f"{api_url}/health", timeout=5)
            if response.status_code == 200:
                logger.info("API服务已运行")
            else:
                logger.warning("API服务响应异常")
        except requests.exceptions.RequestException:
            logger.warning("API服务未运行，可能需要手动启动")
    
    async def _verify_environment(self):
        """验证环境配置"""
        logger.info("验证测试环境...")
        
        # 验证数据库连接
        await self._verify_database_connection()
        
        # 验证API服务
        await self._verify_api_service()
        
        # 验证测试数据
        await self._verify_test_data()
    
    async def _verify_database_connection(self):
        """验证数据库连接"""
        db_config = self.config.database_config.get('test_db', {})
        
        try:
            conn_params = {
                'host': db_config.get('host', 'localhost'),
                'port': db_config.get('port', 5432),
                'database': db_config.get('database', 'connect_test'),
                'user': db_config.get('username', 'test_user'),
                'password': db_config.get('password', 'test_password')
            }
            
            conn = psycopg2.connect(**conn_params)
            cursor = conn.cursor()
            cursor.execute("SELECT version()")
            version = cursor.fetchone()[0]
            logger.info(f"数据库连接成功: {version}")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    async def _verify_api_service(self):
        """验证API服务"""
        api_url = self.config.api_config.get('base_url', 'http://localhost:8000')
        
        try:
            response = requests.get(f"{api_url}/health", timeout=10)
            if response.status_code == 200:
                logger.info("API服务验证成功")
            else:
                logger.warning(f"API服务响应异常: {response.status_code}")
        except Exception as e:
            logger.warning(f"API服务验证失败: {e}")
    
    async def _verify_test_data(self):
        """验证测试数据"""
        test_data_dir = Path(self.config.test_data_config.get('files', {}).get('base_path', 'tests/e2e/data'))
        
        required_files = [
            'ep_data_small.csv',
            'ep_data_medium.csv',
            'ep_data_large.csv',
            'cdr_data_small.csv',
            'site_data_basic.csv',
            'kpi_daily.csv'
        ]
        
        for file_name in required_files:
            file_path = test_data_dir / file_name
            if file_path.exists():
                logger.info(f"测试数据文件存在: {file_name}")
            else:
                logger.error(f"测试数据文件缺失: {file_name}")
                raise FileNotFoundError(f"缺失测试数据文件: {file_name}")
    
    async def _setup_monitoring(self):
        """设置监控"""
        logger.info("设置测试监控...")
        
        # 创建监控目录
        monitoring_dir = Path('tests/e2e/monitoring')
        monitoring_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建监控配置文件
        monitoring_config = {
            'start_time': datetime.now().isoformat(),
            'environment': 'e2e_test',
            'database': self.config.database_config,
            'api': self.config.api_config,
            'performance_thresholds': self.config.performance_config
        }
        
        with open(monitoring_dir / 'environment_config.json', 'w') as f:
            json.dump(monitoring_config, f, indent=2, ensure_ascii=False)
    
    async def _stop_services(self):
        """停止服务"""
        logger.info("停止测试服务...")
        
        for service in self.started_services:
            try:
                # 这里实现具体的服务停止逻辑
                logger.info(f"停止服务: {service}")
            except Exception as e:
                logger.error(f"停止服务失败 {service}: {e}")
    
    async def _cleanup_database(self):
        """清理数据库"""
        logger.info("清理测试数据库...")
        
        if not self.config.cleanup_config.get('after_suite', {}).get('enabled', True):
            logger.info("跳过数据库清理")
            return
        
        db_config = self.config.database_config.get('test_db', {})
        
        try:
            admin_conn_params = {
                'host': db_config.get('host', 'localhost'),
                'port': db_config.get('port', 5432),
                'user': 'postgres',
                'password': os.getenv('POSTGRES_ADMIN_PASSWORD', 'postgres')
            }
            
            conn = psycopg2.connect(**admin_conn_params)
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            
            for db_name in self.created_databases:
                cursor.execute(f"DROP DATABASE IF EXISTS {db_name}")
                logger.info(f"删除测试数据库: {db_name}")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"数据库清理失败: {e}")
    
    async def _cleanup_temp_files(self):
        """清理临时文件"""
        logger.info("清理临时文件...")
        
        for temp_dir in self.temp_dirs:
            try:
                if temp_dir.exists():
                    shutil.rmtree(temp_dir)
                    logger.info(f"删除临时目录: {temp_dir}")
            except Exception as e:
                logger.error(f"删除临时目录失败 {temp_dir}: {e}")
    
    async def _cleanup_monitoring(self):
        """清理监控"""
        logger.info("清理测试监控...")
        
        # 生成最终监控报告
        monitoring_dir = Path('tests/e2e/monitoring')
        if monitoring_dir.exists():
            final_report = {
                'end_time': datetime.now().isoformat(),
                'cleanup_completed': True,
                'databases_cleaned': self.created_databases,
                'temp_dirs_cleaned': [str(d) for d in self.temp_dirs]
            }
            
            with open(monitoring_dir / 'cleanup_report.json', 'w') as f:
                json.dump(final_report, f, indent=2, ensure_ascii=False)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='E2E测试环境管理')
    parser.add_argument(
        '--action',
        choices=['setup', 'teardown', 'reset', 'verify'],
        required=True,
        help='要执行的操作'
    )
    parser.add_argument(
        '--config',
        default='tests/e2e/config/test_config.yaml',
        help='配置文件路径'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='启用详细日志'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建环境管理器
    manager = TestEnvironmentManager(args.config)
    
    # 执行操作
    async def run_action():
        if args.action == 'setup':
            success = await manager.setup_environment()
        elif args.action == 'teardown':
            success = await manager.teardown_environment()
        elif args.action == 'reset':
            success = await manager.reset_environment()
        elif args.action == 'verify':
            success = await manager._verify_environment()
            success = True  # verify不返回bool
        
        return success
    
    # 运行异步操作
    success = asyncio.run(run_action())
    
    if success:
        logger.info(f"操作 '{args.action}' 执行成功")
        sys.exit(0)
    else:
        logger.error(f"操作 '{args.action}' 执行失败")
        sys.exit(1)


if __name__ == '__main__':
    main()