# 监控仪表板依赖包
# 用于测试结果监控、可视化和告警通知

# 核心依赖
requests>=2.31.0
pandas>=2.0.0
numpy>=1.24.0
jinja2>=3.1.0

# 可视化
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# 数据处理
pytz>=2023.3
python-dateutil>=2.8.2

# HTTP客户端
httpx>=0.24.0
aiohttp>=3.8.0

# 配置管理
pyyaml>=6.0
toml>=0.10.2

# 日志和监控
structlog>=23.1.0
prometheus-client>=0.17.0

# 通知服务
slack-sdk>=3.21.0
pymsteams>=0.2.2

# 安全
cryptography>=41.0.0

# 开发和测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0
black>=23.7.0
flake8>=6.0.0
mypy>=1.5.0

# AWS集成（可选）
boto3>=1.28.0
botocore>=1.31.0

# 数据库连接（可选）
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0

# 性能监控
psutil>=5.9.0
memory-profiler>=0.61.0

# 图像处理
Pillow>=10.0.0

# 模板引擎增强
markupsafe>=2.1.0

# 时间处理
arrow>=1.2.0

# 数据验证
pydantic>=2.0.0

# 异步支持
asyncio-mqtt>=0.13.0

# 缓存
redis>=4.6.0

# 文件处理
openpyxl>=3.1.0

# 网络工具
ping3>=4.0.0