# Connect Project

一个现代化的地理空间数据处理和分析项目，支持标准开发环境和QGIS空间计算环境。

## 🚀 快速开始

### 环境要求

- **Python**: 3.12+ (推荐 3.12.7)
- **操作系统**: Windows 10/11, macOS, Linux
- **QGIS**: 3.30+ (可选，用于空间计算)

### 一键环境设置

```bash
# 克隆项目
git clone <repository-url>
cd connect

# 运行环境设置脚本
python setup_environment.py
```

## 🔧 环境管理

### 标准开发环境 (Poetry)

**特点**: 现代化依赖管理，适合日常开发

```bash
# 激活环境
poetry shell

# 安装依赖
poetry install

# 运行脚本
poetry run python your_script.py

# 添加新依赖
poetry add package_name

# 开发依赖
poetry add --group dev package_name
```

### QGIS空间计算环境

**特点**: 集成QGIS功能，支持高级空间分析

```bash
# 检测QGIS环境
python setup_environment.py --setup-qgis

# 使用QGIS Python运行脚本
# (路径会在设置时显示)
C:/OSGeo4W/apps/Python312/python.exe your_qgis_script.py

# 或在标准环境中使用QGIS集成
poetry run python -c "from src.geo import QGISIntegration; qgis = QGISIntegration()"
```

## Project Structure

```
connect/
├── src/                    # Source code directory
│   ├── database/          # Database framework module
│   │   ├── connection/    # Database connection management
│   │   │   ├── __init__.py
│   │   │   ├── pool.py    # Connection pool management
│   │   │   ├── session.py # Database session management
│   │   │   ├── health_check.py # Database health monitoring
│   │   │   └── read_write_splitter.py # Read-write splitting
│   │   ├── config.py      # Database configuration
│   │   └── exceptions.py  # Database exceptions
│   ├── geo/               # Geospatial processing module
│   │   ├── __init__.py
│   │   ├── vector.py      # Vector data processing
│   │   ├── raster.py      # Raster data processing
│   │   ├── geometry.py    # Geometry object processing
│   │   └── qgis_integration.py  # QGIS integration
│   └── config/            # Configuration management module
│       ├── __init__.py
│       ├── environment.py # Environment management
│       └── settings.py    # Settings management
├── examples/              # Example code
│   ├── basic_usage.py     # Basic usage example
│   └── read_write_splitter_example.py # Read-write splitting example
├── tests/                 # Test directory
│   ├── test_read_write_splitter.py # Unit tests
│   └── integration/       # Integration tests
│       └── test_read_write_splitter_integration.py
├── data/                  # Data directory
│   ├── input/            # Input data
│   ├── output/           # Output data
│   └── temp/             # Temporary data
├── config/               # Configuration file directory
│   └── database_read_write_example.yaml # Database config example
├── logs/                 # Log file directory
├── pyproject.toml        # Poetry configuration file
└── README.md            # Project description
```

## Environment Requirements

- Python 3.10+
- Poetry (for dependency management)
- QGIS 3.30+ (optional, for QGIS integration features)

## Installation and Setup

### 1. Clone the Project

```bash
git clone <repository-url>
cd connect
```

### 2. Install Poetry (if not already installed)

```bash
# Windows (PowerShell)
(Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -

# Or use pip
pip install poetry
```

### 3. Configure Poetry

```bash
# Configure Poetry to create a virtual environment in the project directory
poetry config virtualenvs.in-project true
```

### 4. Install Dependencies

```bash
# Install project dependencies
poetry install

# Activate virtual environment
poetry shell
```

### 5. Verify Installation

```bash
# Run basic usage example
python examples/basic_usage.py
```

## Core Module Usage

### Geospatial Processing

#### Vector Data Processing

```python
from src.geo import VectorProcessor

# Create vector processor
vector_processor = VectorProcessor()

# Read vector file
gdf = vector_processor.read_file('data/input/cities.shp')

# Create buffer
buffered = vector_processor.create_buffer(gdf, 1000)  # 1000 meter buffer

# Spatial join
joined = vector_processor.spatial_join(gdf1, gdf2)

# Save results
vector_processor.save_to_file(buffered, 'data/output/buffered.geojson')
```

#### Raster Data Processing

```python
from src.geo import RasterProcessor

# Create raster processor
raster_processor = RasterProcessor()

# Read raster file
raster_data = raster_processor.read_raster('data/input/elevation.tif')

# Reproject
reprojected = raster_processor.reproject(raster_data, 'EPSG:3857')

# Clip
clipped = raster_processor.clip_by_geometry(raster_data, polygon_geometry)

# Save results
raster_processor.save_raster(clipped, 'data/output/clipped.tif')
```

#### Geometry Object Processing

```python
from src.geo import GeometryProcessor

# Create geometry processor
geo_processor = GeometryProcessor()

# Create geometry objects
point = geo_processor.create_point(116.4074, 39.9042)
rectangle = geo_processor.create_rectangle(116.3, 39.8, 116.5, 40.0)

# Geometric operations
buffer = geo_processor.create_buffer(point, 1000)
intersection = geo_processor.intersection(buffer, rectangle)

# Coordinate transformation
transformed = geo_processor.transform_coordinates(point, 'EPSG:4326', 'EPSG:3857')
```

### QGIS Integration

```python
from src.geo.qgis_integration import QGISIntegration

# Create QGIS integrator
with QGISIntegration() as qgis:
    # Load layer
    layer = qgis.load_vector_layer('data/input/cities.shp')

    # Run processing algorithm
    result = qgis.run_processing_algorithm('native:buffer', {
        'INPUT': layer,
        'DISTANCE': 1000,
        'OUTPUT': 'memory:'
    })

    # Export results
    qgis.export_layer_to_file(result['OUTPUT'], 'data/output/buffered.shp')
```

### Environment Management

```python
from config.settings import Settings

# 加载项目配置
settings = Settings()
print(settings.get_all_settings())
```

### Database Read-Write Splitting

```python
from src.database.connection import ReadWriteSplitter, LoadBalancingStrategy
from src.database.config import DatabaseConfig

# Configure primary database (for writes)
primary_config = DatabaseConfig(
    host="primary-db.example.com",
    port=5432,
    name="connect_db",
    user="connect_user",
    password="connect_password"
)

# Configure read replicas
replica_configs = [
    DatabaseConfig(
        host="replica1-db.example.com",
        port=5432,
        name="connect_db",
        user="readonly_user",
        password="readonly_password"
    ),
    DatabaseConfig(
        host="replica2-db.example.com",
        port=5432,
        name="connect_db",
        user="readonly_user",
        password="readonly_password"
    )
]

# Initialize read-write splitter
splitter = ReadWriteSplitter(
    primary_config=primary_config,
    replica_configs=replica_configs,
    load_balancing_strategy=LoadBalancingStrategy.ROUND_ROBIN,
    fallback_to_primary=True,
    health_check_interval=30
)

# Initialize the splitter
await splitter.initialize()

# Read operations (uses replicas)
async with splitter.get_connection(read_only=True) as conn:
    result = await conn.fetchrow("SELECT * FROM users WHERE id = $1", user_id)

# Write operations (uses primary)
async with splitter.get_connection(read_only=False) as conn:
    await conn.execute(
        "INSERT INTO users (name, email) VALUES ($1, $2)",
        "John Doe", "<EMAIL>"
    )

# Check health status
health_status = await splitter.get_health_status()
print(f"Primary healthy: {health_status['summary']['primary_healthy']}")
print(f"Healthy replicas: {health_status['summary']['healthy_replicas']}")

# Get connection statistics
stats = splitter.get_stats()
print(f"Load balancing strategy: {stats['load_balancing_strategy']}")
print(f"Connection counts: {stats['replica_connection_counts']}")

# Clean up
await splitter.close()
```

### Configuration Management

```python
from config import Settings

# Create settings manager
settings = Settings()

# Get configuration
default_crs = settings.get('geo.default_crs')
max_workers = settings.get('processing.max_workers')

# Update configuration
settings.set('geo.precision', 8)
settings.update_section('processing', {'max_workers': 8})

# Save configuration
settings.save_to_file('config/base.yaml')
```

## Configuration Files

The project supports multiple configuration file formats, YAML is recommended:

```yaml
# config/base.yaml
project:
  name: "Connect Project"
  version: "1.0.0"
  description: "Geospatial data processing project"

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/app.log"

# Database configuration with read-write splitting
database:
  host: "localhost"
  port: 5432
  name: "connect_db"
  user: "connect_user"
  password: "connect_password"

pool:
  size: 10
  max_overflow: 20
  timeout: 30
  recycle: 3600

# Read-write splitting configuration
read_write:
  enabled: true

  # Primary database (write operations)
  primary:
    host: "primary-db.example.com"
    port: 5432
    name: "connect_db"
    user: "connect_user"
    password: "connect_password"

  # Read replica databases
  replicas:
    - host: "replica1-db.example.com"
      port: 5432
      name: "connect_db"
      user: "connect_readonly_user"
      password: "connect_readonly_password"

    - host: "replica2-db.example.com"
      port: 5432
      name: "connect_db"
      user: "connect_readonly_user"
      password: "connect_readonly_password"

  # Load balancing strategy: "round_robin", "random", or "least_connections"
  load_balancing_strategy: "round_robin"

  # Whether to fallback to primary when all replicas are unavailable
  fallback_to_primary: true

  # Health check interval in seconds
  health_check_interval: 30

geo:
  default_crs: "EPSG:4326"
  output_format: "GeoJSON"
  precision: 6

qgis:
  auto_detect: true
  path: null
  plugins_enabled: true

data:
  input_dir: "data/input"
  output_dir: "data/output"
  temp_dir: "data/temp"
  cache_enabled: true

processing:
  max_workers: 4
  chunk_size: 1000
  memory_limit: "2GB"
```

## Development Guide

### Adding New Geoprocessing Features

1. Add new methods in the corresponding module
2. Write unit tests
3. Update documentation
4. Add usage demonstrations in examples

### Extending QGIS Integration

1. Add new QGIS API calls in `qgis_integration.py`
2. Handle QGIS version compatibility
3. Add error handling and logging

### Environment Configuration Best Practices

1. Use Poetry to manage dependencies
2. Keep virtual environments isolated
3. Regularly update dependency versions
4. Use configuration files to manage environment variables

## Troubleshooting

### Common Issues

#### 1. QGIS Integration Failure

```bash
# Check QGIS installation path
dir C:\OSGeo4W

# Verify Python version compatibility
python --version
C:\OSGeo4W\apps\Python312\python.exe --version
```

#### 2. Dependency Installation Failure

```bash
# Clear Poetry cache
poetry cache clear --all pypi

# Reinstall dependencies
poetry install --no-cache
```

#### 3. Environment Variable Issues

```bash
# Reload environment variables
refreshenv

# Or restart PowerShell
```

### Logging and Debugging

The project uses Python's standard logging module, log files are saved in the `logs/` directory.

```python
import logging

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)

# View detailed error messages
logger = logging.getLogger(__name__)
logger.debug("Debug message")
```

## Contribution Guide

1. Fork the project
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgements

- [GeoPandas](https://geopandas.org/) - Geospatial data processing
- [Shapely](https://shapely.readthedocs.io/) - Geometric object manipulation
- [Rasterio](https://rasterio.readthedocs.io/) - Raster data processing
- [QGIS](https://qgis.org/) - Geographic Information System
- [Poetry](https://python-poetry.org/) - Python dependency management

## Contact

If you have any questions or suggestions, please contact us via:

- Project Issues: [GitHub Issues](https://github.com/your-repo/connect/issues)
- Email: <EMAIL>

## 🚀 Features

- **Multi-source Data Integration**: Support for EP, CDR, NLG, KPI, Score, and Config data sources
- **Flexible Configuration**: YAML-based configuration with Pydantic validation
- **Robust Error Handling**: Comprehensive exception hierarchy for different error scenarios
- **Type Safety**: Full type hints and validation throughout the framework
- **Async Support**: Built-in support for asynchronous database operations
- **Connection Pooling**: Efficient database connection management
- **Schema Management**: Automated schema creation and validation
- **File Processing**: Support for various file formats (Excel, CSV, XLSB)
- **Testing Framework**: Comprehensive test suite with fixtures and mocks

## 📋 Requirements

- Python 3.8+
- PostgreSQL 12+
- See `requirements.txt` for complete dependency list

## 🛠️ Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd connect
   ```

2. **Create a virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure the database**:
   - Update the configuration in `config/base.yaml` with your database credentials

## ⚙️ Configuration

The framework uses YAML configuration files located in the `config/` directory. See the existing `config/base.yaml` for the complete configuration structure.

## 🏗️ Project Structure

```
connect/
├── src/
│   └── database/
│       ├── __init__.py          # Main package initialization
│       ├── config.py            # Configuration management
│       ├── exceptions.py        # Custom exception classes
│       ├── constants.py         # Framework constants
│       └── types.py             # Type definitions
├── tests/
│   ├── conftest.py             # Test configuration
│   ├── fixtures/               # Test fixtures
│   ├── data/                   # Test data files
│   ├── test_config.py          # Configuration tests
│   └── test_exceptions.py      # Exception tests
├── config/
│   └── base.yaml               # Main configuration (includes database, telecom, etc.)
├── requirements.txt            # Python dependencies
└── README.md                   # This file
```

## 🚦 Quick Start

### Basic Usage

```python
from src.database import DatabaseConfig, load_config
from src.database.exceptions import DatabaseError

# Load configuration
try:
    config_dict = load_config('config/base.yaml')
    db_config = DatabaseConfig(**config_dict['database'])
    print(f"Connected to {db_config.host}:{db_config.port}/{db_config.name}")
except DatabaseError as e:
    print(f"Configuration error: {e}")
```

### Working with Data Sources

```python
from src.database.config import DataSourceConfig

# Configure EP data source
ep_config = DataSourceConfig(
    schema_name="ep",
    file_extensions=[".xlsx", ".xls"],
    skip_rows=1,
    header_row=1,
    table_name_pattern="ep_{cell_type}_{year}_cw{week}"
)

print(f"EP schema: {ep_config.schema_name}")
print(f"Supported formats: {ep_config.file_extensions}")
```

### Error Handling

```python
from src.database.exceptions import (
    ConnectionError,
    ValidationError,
    QueryError
)

try:
    # Database operations here
    pass
except ConnectionError as e:
    print(f"Connection failed: {e}")
    print(f"Error code: {e.error_code}")
    print(f"Details: {e.details}")
except ValidationError as e:
    print(f"Validation error: {e}")
except QueryError as e:
    print(f"Query error: {e}")
```

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src/database

# Run specific test file
pytest tests/test_config.py

# Run with verbose output
pytest -v
```

## 📊 Data Sources

The framework supports multiple data sources as defined in the PRD:

### EP (Engineering Parameters)
- **Purpose**: Network engineering performance data
- **Format**: Excel files (.xlsx, .xls)
- **Schema**: `ep`
- **Table Pattern**: `ep_{cell_type}_{year}_cw{week}`

### CDR (Call Detail Records)
- **Purpose**: Call and service usage records
- **Format**: Excel files (.xlsx, .xls)
- **Schema**: Operator-specific (cdr_to2, cdr_vdf, cdr_tdg)
- **Table Pattern**: `cdr_{year}Q{quarter}_{service_type}`

### NLG (Network Level Graphs)
- **Purpose**: Network topology and performance graphs
- **Format**: Excel Binary (.xlsb)
- **Schema**: `nlg`
- **Table Pattern**: `nlg_cube_{date}`

### KPI (Key Performance Indicators)
- **Purpose**: Business and technical KPIs
- **Format**: Excel (.xlsx) and CSV (.csv)
- **Schema**: `kpi`
- **Table Pattern**: `kpi_{metric_type}_{period}`

### Score
- **Purpose**: Quality and performance scoring
- **Format**: Excel (.xlsx) and CSV (.csv)
- **Schema**: `score`
- **Table Pattern**: `score_{algorithm}_{date}`

### Config
- **Purpose**: System and application configuration
- **Format**: YAML (.yaml) and JSON (.json)
- **Schema**: `cfg`
- **Table Pattern**: `cfg_{config_type}`

## 🔧 Development

### Code Style

The project follows PEP 8 and uses:
- **Black** for code formatting
- **isort** for import sorting
- **flake8** for linting
- **mypy** for type checking

### Adding New Data Sources

1. **Update configuration schema** in `src/database/config.py`
2. **Add constants** in `src/database/constants.py`
3. **Define types** in `src/database/types.py`
4. **Create tests** in `tests/`
5. **Update documentation**

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/new-feature`
3. **Make changes and add tests**
4. **Run the test suite**: `pytest`
5. **Commit changes**: `git commit -am 'Add new feature'`
6. **Push to branch**: `git push origin feature/new-feature`
7. **Create a Pull Request**

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- **Documentation**: Check this README and inline code documentation
- **Issues**: Create an issue on the repository
- **Testing**: Run the test suite to verify functionality

---

**Note**: This framework is part of the Connect platform ecosystem and is designed to work seamlessly with other Connect components.
