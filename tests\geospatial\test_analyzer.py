"""Tests for geospatial analyzer module."""

import pytest
import pandas as pd
import geopandas as gpd
from shapely.geometry import Point, Polygon
import numpy as np

from src.geospatial.analyzer import SpatialAnalyzer


class TestSpatialAnalyzer:
    """Test cases for SpatialAnalyzer class."""

    @pytest.fixture
    def analyzer(self):
        """Create SpatialAnalyzer instance."""
        return SpatialAnalyzer()

    @pytest.fixture
    def sample_gdf(self):
        """Create sample GeoDataFrame for testing."""
        points = [
            Point(-74.0060, 40.7128),  # NYC
            Point(-73.9851, 40.7589),  # NYC
            Point(-74.0060, 40.7128),  # NYC (duplicate)
        ]
        
        return gpd.GeoDataFrame({
            'measurement_id': ['m001', 'm002', 'm003'],
            'rsrp': [-85, -95, -105],
            'cell_id': ['cell_001', 'cell_002', 'cell_001'],
            'geometry': points
        })

    def test_analyzer_initialization(self, analyzer):
        """Test analyzer initialization."""
        assert analyzer.crs == "EPSG:4326"
        assert hasattr(analyzer, 'logger')

    def test_analyze_coverage(self, analyzer, sample_gdf):
        """Test coverage analysis."""
        result = analyzer.analyze_coverage(sample_gdf)
        
        assert isinstance(result, dict)
        assert 'coverage_ratio' in result
        assert 'total_area' in result
        assert 'covered_area' in result
        assert result['coverage_ratio'] >= 0
        assert result['coverage_ratio'] <= 100

    def test_analyze_coverage_empty_data(self, analyzer):
        """Test coverage analysis with empty data."""
        empty_gdf = gpd.GeoDataFrame(columns=['geometry'])
        result = analyzer.analyze_coverage(empty_gdf)
        
        assert result['coverage_ratio'] == 0
        assert result['total_area'] == 0
        assert result['covered_area'] == 0

    def test_analyze_signal_quality(self, analyzer, sample_gdf):
        """Test signal quality analysis."""
        result = analyzer.analyze_signal_quality(sample_gdf, signal_column='rsrp')

        assert isinstance(result, dict)
        assert 'overall_statistics' in result
        assert 'quality_distribution' in result
        assert 'spatial_patterns' in result
        assert 'signal_mean' in result['overall_statistics']

    def test_analyze_signal_quality_missing_column(self, analyzer, sample_gdf):
        """Test signal quality analysis with missing column."""
        result = analyzer.analyze_signal_quality(sample_gdf, signal_column='missing_column')

        assert result['quality_distribution'] == {}
        assert result['spatial_patterns'] == {}

    def test_find_coverage_gaps(self, analyzer, sample_gdf):
        """Test coverage gap identification."""
        gaps = analyzer.find_coverage_gaps(sample_gdf, signal_column='rsrp', threshold=-100.0)

        assert isinstance(gaps, gpd.GeoDataFrame)
        assert 'geometry' in gaps.columns
        assert 'gap_area' in gaps.columns
        assert 'severity' in gaps.columns

    def test_create_heat_map_data(self, analyzer, sample_gdf):
        """Test heat map data creation."""
        heat_map = analyzer.create_heat_map_data(sample_gdf, value_column='rsrp', grid_size=0.01)

        assert isinstance(heat_map, gpd.GeoDataFrame)
        assert 'geometry' in heat_map.columns
        assert 'value' in heat_map.columns
        assert 'count' in heat_map.columns

    def test_analyze_handover_patterns(self, analyzer):
        """Test handover pattern analysis."""
        # Create sample handover data
        handover_data = gpd.GeoDataFrame({
            'source_cell': ['cell_001', 'cell_002', 'cell_001'],
            'target_cell': ['cell_002', 'cell_003', 'cell_003'],
            'handover_success': [True, False, True],
            'geometry': [Point(-74.0060, 40.7128), Point(-73.9851, 40.7589), Point(-74.0060, 40.7128)]
        })

        result = analyzer.analyze_handover_patterns(handover_data)

        assert isinstance(result, dict)
        assert 'handover_success_rate' in result
        assert 'patterns' in result
        assert 'total_handovers' in result

    def test_analyze_coverage_with_threshold(self, analyzer, sample_gdf):
        """Test coverage analysis with different threshold."""
        result = analyzer.analyze_coverage(sample_gdf, threshold=-100.0)

        assert isinstance(result, dict)
        assert result['coverage_ratio'] >= 0
        assert result['threshold_used'] == -100.0

    def test_empty_data_handling(self, analyzer):
        """Test handling of empty data."""
        empty_gdf = gpd.GeoDataFrame(columns=['geometry', 'rsrp'])

        coverage_result = analyzer.analyze_coverage(empty_gdf)
        assert coverage_result['coverage_ratio'] == 0.0

        signal_result = analyzer.analyze_signal_quality(empty_gdf)
        assert signal_result['quality_distribution'] == {}

        gaps_result = analyzer.find_coverage_gaps(empty_gdf)
        assert isinstance(gaps_result, gpd.GeoDataFrame)
