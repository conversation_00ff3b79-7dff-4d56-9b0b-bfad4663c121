"""Error handling and recovery for ETL operations.

This module provides comprehensive error handling, logging, and recovery
mechanisms for ETL operations.
"""

import json
import sys
import time
import traceback
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Type, Union

import pandas as pd
from loguru import logger

from ..exceptions import DataError, ProcessingError, ValidationError
from ..utils.progress_tracker import ProgressTracker


class ErrorSeverity(Enum):
    """Error severity levels."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories."""

    DATA_QUALITY = "data_quality"
    VALIDATION = "validation"
    TRANSFORMATION = "transformation"
    IO_ERROR = "io_error"
    MEMORY_ERROR = "memory_error"
    NETWORK_ERROR = "network_error"
    PERMISSION_ERROR = "permission_error"
    CONFIGURATION_ERROR = "configuration_error"
    SYSTEM_ERROR = "system_error"
    UNKNOWN = "unknown"


class RecoveryAction(Enum):
    """Recovery actions."""

    RETRY = "retry"
    SKIP = "skip"
    FALLBACK = "fallback"
    ABORT = "abort"
    MANUAL = "manual"
    IGNORE = "ignore"


@dataclass
class ErrorInfo:
    """Information about an error."""

    error_id: str
    timestamp: datetime
    severity: ErrorSeverity
    category: ErrorCategory
    message: str
    exception_type: str
    traceback: str
    context: Dict[str, Any] = field(default_factory=dict)
    recovery_action: Optional[RecoveryAction] = None
    retry_count: int = 0
    resolved: bool = False
    resolution_notes: Optional[str] = None


@dataclass
class RecoveryStrategy:
    """Recovery strategy configuration."""

    action: RecoveryAction
    max_retries: int = 3
    retry_delay: float = 1.0  # seconds
    exponential_backoff: bool = True
    fallback_function: Optional[Callable] = None
    condition_function: Optional[
        Callable
    ] = None  # Function to check if strategy applies
    priority: int = 0  # Higher priority strategies are tried first


@dataclass
class ErrorHandlingConfig:
    """Error handling configuration."""

    log_errors: bool = True
    save_error_data: bool = True
    error_data_path: Optional[str] = None
    max_error_count: int = 100
    stop_on_critical: bool = True
    enable_recovery: bool = True
    default_recovery_action: RecoveryAction = RecoveryAction.ABORT
    error_report_format: str = "json"  # json, csv, html
    include_stack_trace: bool = True
    auto_categorize: bool = True


class ErrorHandler:
    """Comprehensive error handler for ETL operations."""

    def __init__(self, config: Optional[ErrorHandlingConfig] = None):
        """Initialize error handler.

        Args:
            config: Error handling configuration
        """
        self.config = config or ErrorHandlingConfig()
        self.errors: List[ErrorInfo] = []
        self.recovery_strategies: Dict[ErrorCategory, List[RecoveryStrategy]] = {}
        self.error_callbacks: List[Callable] = []
        self.error_counter = 0

        # Set up error data directory
        if self.config.save_error_data and self.config.error_data_path:
            self.error_data_dir = Path(self.config.error_data_path)
            self.error_data_dir.mkdir(parents=True, exist_ok=True)
        else:
            self.error_data_dir = None

        # Register default recovery strategies
        self._register_default_strategies()

    def add_recovery_strategy(
        self, category: ErrorCategory, strategy: RecoveryStrategy
    ) -> None:
        """Add recovery strategy for error category.

        Args:
            category: Error category
            strategy: Recovery strategy
        """
        if category not in self.recovery_strategies:
            self.recovery_strategies[category] = []

        self.recovery_strategies[category].append(strategy)

        # Sort by priority (higher first)
        self.recovery_strategies[category].sort(key=lambda x: x.priority, reverse=True)

        logger.debug(
            f"Added recovery strategy for {category.value}: {strategy.action.value}"
        )

    def add_error_callback(self, callback: Callable[[ErrorInfo], None]) -> None:
        """Add error callback function.

        Args:
            callback: Callback function that receives ErrorInfo
        """
        self.error_callbacks.append(callback)
        logger.debug("Added error callback")

    def handle_error(
        self,
        exception: Exception,
        context: Optional[Dict[str, Any]] = None,
        data: Optional[pd.DataFrame] = None,
    ) -> RecoveryAction:
        """Handle an error and determine recovery action.

        Args:
            exception: The exception that occurred
            context: Additional context information
            data: Optional data that caused the error

        Returns:
            Recovery action to take
        """
        try:
            # Create error info
            error_info = self._create_error_info(exception, context)

            # Add to error list
            self.errors.append(error_info)
            self.error_counter += 1

            # Log error
            if self.config.log_errors:
                self._log_error(error_info)

            # Save error data
            if self.config.save_error_data and data is not None:
                self._save_error_data(error_info, data)

            # Call error callbacks
            for callback in self.error_callbacks:
                try:
                    callback(error_info)
                except Exception as e:
                    logger.error(f"Error callback failed: {e}")

            # Check if we should stop
            if self._should_stop(error_info):
                return RecoveryAction.ABORT

            # Determine recovery action
            recovery_action = self._determine_recovery_action(error_info)
            error_info.recovery_action = recovery_action

            return recovery_action

        except Exception as e:
            logger.error(f"Error in error handler: {e}")
            return RecoveryAction.ABORT

    def execute_with_recovery(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with error handling and recovery.

        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Function result or None if failed

        Raises:
            Exception: If all recovery attempts fail
        """
        last_exception = None

        for attempt in range(self.config.max_error_count):
            try:
                return func(*args, **kwargs)

            except Exception as e:
                last_exception = e

                # Handle the error
                context = {
                    "function": func.__name__,
                    "attempt": attempt + 1,
                    "args": str(args)[:200],  # Limit length
                    "kwargs": str(kwargs)[:200],
                }

                recovery_action = self.handle_error(e, context)

                if recovery_action == RecoveryAction.ABORT:
                    break
                elif recovery_action == RecoveryAction.SKIP:
                    return None
                elif recovery_action == RecoveryAction.RETRY:
                    # Wait before retry
                    if attempt > 0:
                        delay = self._calculate_retry_delay(attempt)
                        time.sleep(delay)
                    continue
                elif recovery_action == RecoveryAction.FALLBACK:
                    # Try fallback function if available
                    fallback_result = self._try_fallback(e, func, *args, **kwargs)
                    if fallback_result is not None:
                        return fallback_result
                elif recovery_action == RecoveryAction.IGNORE:
                    return None

        # All attempts failed
        if last_exception:
            raise last_exception

    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of all errors.

        Returns:
            Error summary dictionary
        """
        if not self.errors:
            return {"total_errors": 0}

        # Count by severity
        severity_counts = {}
        for severity in ErrorSeverity:
            severity_counts[severity.value] = sum(
                1 for e in self.errors if e.severity == severity
            )

        # Count by category
        category_counts = {}
        for category in ErrorCategory:
            category_counts[category.value] = sum(
                1 for e in self.errors if e.category == category
            )

        # Count by recovery action
        recovery_counts = {}
        for action in RecoveryAction:
            recovery_counts[action.value] = sum(
                1 for e in self.errors if e.recovery_action == action
            )

        # Recent errors (last 10)
        recent_errors = self.errors[-10:] if len(self.errors) > 10 else self.errors

        return {
            "total_errors": len(self.errors),
            "severity_breakdown": severity_counts,
            "category_breakdown": category_counts,
            "recovery_action_breakdown": recovery_counts,
            "resolved_errors": sum(1 for e in self.errors if e.resolved),
            "unresolved_errors": sum(1 for e in self.errors if not e.resolved),
            "recent_errors": [
                {
                    "id": e.error_id,
                    "timestamp": e.timestamp.isoformat(),
                    "severity": e.severity.value,
                    "category": e.category.value,
                    "message": e.message[:100],  # Truncate long messages
                }
                for e in recent_errors
            ],
        }

    def generate_error_report(
        self, output_path: Optional[Union[str, Path]] = None
    ) -> str:
        """Generate comprehensive error report.

        Args:
            output_path: Optional path to save report

        Returns:
            Report content as string
        """
        if self.config.error_report_format == "json":
            report = self._generate_json_report()
        elif self.config.error_report_format == "csv":
            report = self._generate_csv_report()
        elif self.config.error_report_format == "html":
            report = self._generate_html_report()
        else:
            report = self._generate_text_report()

        # Save to file if path provided
        if output_path:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, "w", encoding="utf-8") as f:
                f.write(report)

            logger.info(f"Error report saved to: {output_path}")

        return report

    def clear_errors(self, resolved_only: bool = False) -> None:
        """Clear error history.

        Args:
            resolved_only: If True, only clear resolved errors
        """
        if resolved_only:
            self.errors = [e for e in self.errors if not e.resolved]
            logger.info("Cleared resolved errors")
        else:
            self.errors.clear()
            self.error_counter = 0
            logger.info("Cleared all errors")

    def mark_error_resolved(
        self, error_id: str, resolution_notes: Optional[str] = None
    ) -> bool:
        """Mark an error as resolved.

        Args:
            error_id: Error ID to mark as resolved
            resolution_notes: Optional resolution notes

        Returns:
            True if error was found and marked, False otherwise
        """
        for error in self.errors:
            if error.error_id == error_id:
                error.resolved = True
                error.resolution_notes = resolution_notes
                logger.info(f"Marked error {error_id} as resolved")
                return True

        logger.warning(f"Error {error_id} not found")
        return False

    def _create_error_info(
        self, exception: Exception, context: Optional[Dict[str, Any]] = None
    ) -> ErrorInfo:
        """Create ErrorInfo from exception.

        Args:
            exception: The exception
            context: Additional context

        Returns:
            ErrorInfo object
        """
        error_id = f"ERR_{int(time.time() * 1000)}_{self.error_counter}"

        # Determine severity
        severity = self._categorize_severity(exception)

        # Determine category
        category = self._categorize_error(exception)

        # Get traceback
        tb_str = traceback.format_exc() if self.config.include_stack_trace else ""

        return ErrorInfo(
            error_id=error_id,
            timestamp=datetime.now(),
            severity=severity,
            category=category,
            message=str(exception),
            exception_type=type(exception).__name__,
            traceback=tb_str,
            context=context or {},
        )

    def _categorize_severity(self, exception: Exception) -> ErrorSeverity:
        """Categorize error severity.

        Args:
            exception: The exception

        Returns:
            Error severity
        """
        # Critical errors
        if isinstance(exception, (MemoryError, SystemError, KeyboardInterrupt)):
            return ErrorSeverity.CRITICAL

        # High severity errors
        if isinstance(exception, (FileNotFoundError, PermissionError, ConnectionError)):
            return ErrorSeverity.HIGH

        # Medium severity errors
        if isinstance(exception, (ValueError, TypeError, ProcessingError)):
            return ErrorSeverity.MEDIUM

        # Default to low
        return ErrorSeverity.LOW

    def _categorize_error(self, exception: Exception) -> ErrorCategory:
        """Categorize error type.

        Args:
            exception: The exception

        Returns:
            Error category
        """
        if not self.config.auto_categorize:
            return ErrorCategory.UNKNOWN

        # Data quality errors
        if isinstance(exception, (DataError, ValidationError)):
            return ErrorCategory.DATA_QUALITY

        # Validation errors
        if (
            isinstance(exception, (ValueError, TypeError))
            and "validation" in str(exception).lower()
        ):
            return ErrorCategory.VALIDATION

        # IO errors
        if isinstance(exception, (FileNotFoundError, IOError, OSError)):
            return ErrorCategory.IO_ERROR

        # Memory errors
        if isinstance(exception, MemoryError):
            return ErrorCategory.MEMORY_ERROR

        # Network errors
        if isinstance(exception, (ConnectionError, TimeoutError)):
            return ErrorCategory.NETWORK_ERROR

        # Permission errors
        if isinstance(exception, PermissionError):
            return ErrorCategory.PERMISSION_ERROR

        # System errors
        if isinstance(exception, (SystemError, SystemExit)):
            return ErrorCategory.SYSTEM_ERROR

        # Processing errors
        if isinstance(exception, ProcessingError):
            return ErrorCategory.TRANSFORMATION

        return ErrorCategory.UNKNOWN

    def _should_stop(self, error_info: ErrorInfo) -> bool:
        """Determine if processing should stop.

        Args:
            error_info: Error information

        Returns:
            True if processing should stop
        """
        # Stop on critical errors if configured
        if (
            self.config.stop_on_critical
            and error_info.severity == ErrorSeverity.CRITICAL
        ):
            return True

        # Stop if max error count reached
        if len(self.errors) >= self.config.max_error_count:
            return True

        return False

    def _determine_recovery_action(self, error_info: ErrorInfo) -> RecoveryAction:
        """Determine recovery action for error.

        Args:
            error_info: Error information

        Returns:
            Recovery action
        """
        if not self.config.enable_recovery:
            return self.config.default_recovery_action

        # Get strategies for this category
        strategies = self.recovery_strategies.get(error_info.category, [])

        # Try each strategy in priority order
        for strategy in strategies:
            # Check if strategy condition is met
            if strategy.condition_function:
                try:
                    if not strategy.condition_function(error_info):
                        continue
                except Exception as e:
                    logger.error(f"Strategy condition function failed: {e}")
                    continue

            # Check retry count
            if strategy.action == RecoveryAction.RETRY:
                if error_info.retry_count >= strategy.max_retries:
                    continue

            return strategy.action

        # No strategy found, use default
        return self.config.default_recovery_action

    def _calculate_retry_delay(self, attempt: int) -> float:
        """Calculate retry delay.

        Args:
            attempt: Attempt number (0-based)

        Returns:
            Delay in seconds
        """
        base_delay = 1.0

        # Find retry strategy for exponential backoff
        for strategies in self.recovery_strategies.values():
            for strategy in strategies:
                if strategy.action == RecoveryAction.RETRY:
                    base_delay = strategy.retry_delay
                    if strategy.exponential_backoff:
                        return base_delay * (2**attempt)
                    else:
                        return base_delay

        return base_delay

    def _try_fallback(
        self, exception: Exception, original_func: Callable, *args, **kwargs
    ) -> Any:
        """Try fallback function.

        Args:
            exception: Original exception
            original_func: Original function that failed
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Fallback result or None if no fallback available
        """
        # Find fallback strategy
        for strategies in self.recovery_strategies.values():
            for strategy in strategies:
                if (
                    strategy.action == RecoveryAction.FALLBACK
                    and strategy.fallback_function
                ):
                    try:
                        logger.info(
                            f"Trying fallback function for {original_func.__name__}"
                        )
                        return strategy.fallback_function(*args, **kwargs)
                    except Exception as e:
                        logger.error(f"Fallback function failed: {e}")

        return None

    def _log_error(self, error_info: ErrorInfo) -> None:
        """Log error information.

        Args:
            error_info: Error information
        """
        log_message = f"[{error_info.error_id}] {error_info.severity.value.upper()}: {error_info.message}"

        if error_info.context:
            log_message += f" | Context: {error_info.context}"

        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message)
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(log_message)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)

        # Log traceback for high severity errors
        if (
            error_info.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]
            and error_info.traceback
        ):
            logger.debug(
                f"Traceback for {error_info.error_id}:\n{error_info.traceback}"
            )

    def _save_error_data(self, error_info: ErrorInfo, data: pd.DataFrame) -> None:
        """Save error data to file.

        Args:
            error_info: Error information
            data: Data that caused the error
        """
        if not self.error_data_dir:
            return

        try:
            # Save data
            data_file = self.error_data_dir / f"{error_info.error_id}_data.csv"
            data.to_csv(data_file, index=False)

            # Save error info
            error_file = self.error_data_dir / f"{error_info.error_id}_info.json"
            error_dict = {
                "error_id": error_info.error_id,
                "timestamp": error_info.timestamp.isoformat(),
                "severity": error_info.severity.value,
                "category": error_info.category.value,
                "message": error_info.message,
                "exception_type": error_info.exception_type,
                "context": error_info.context,
                "traceback": error_info.traceback,
            }

            with open(error_file, "w", encoding="utf-8") as f:
                json.dump(error_dict, f, indent=2)

            logger.debug(f"Saved error data for {error_info.error_id}")

        except Exception as e:
            logger.error(f"Failed to save error data: {e}")

    def _register_default_strategies(self) -> None:
        """Register default recovery strategies."""
        # IO Error strategies
        self.add_recovery_strategy(
            ErrorCategory.IO_ERROR,
            RecoveryStrategy(
                action=RecoveryAction.RETRY, max_retries=3, retry_delay=1.0, priority=10
            ),
        )

        # Network Error strategies
        self.add_recovery_strategy(
            ErrorCategory.NETWORK_ERROR,
            RecoveryStrategy(
                action=RecoveryAction.RETRY,
                max_retries=5,
                retry_delay=2.0,
                exponential_backoff=True,
                priority=10,
            ),
        )

        # Memory Error strategies
        self.add_recovery_strategy(
            ErrorCategory.MEMORY_ERROR,
            RecoveryStrategy(action=RecoveryAction.ABORT, priority=10),
        )

        # Data Quality strategies
        self.add_recovery_strategy(
            ErrorCategory.DATA_QUALITY,
            RecoveryStrategy(action=RecoveryAction.SKIP, priority=5),
        )

        # Validation strategies
        self.add_recovery_strategy(
            ErrorCategory.VALIDATION,
            RecoveryStrategy(action=RecoveryAction.SKIP, priority=5),
        )

    def _generate_json_report(self) -> str:
        """Generate JSON error report.

        Returns:
            JSON report string
        """
        report_data = {
            "summary": self.get_error_summary(),
            "errors": [
                {
                    "error_id": e.error_id,
                    "timestamp": e.timestamp.isoformat(),
                    "severity": e.severity.value,
                    "category": e.category.value,
                    "message": e.message,
                    "exception_type": e.exception_type,
                    "context": e.context,
                    "recovery_action": e.recovery_action.value
                    if e.recovery_action
                    else None,
                    "retry_count": e.retry_count,
                    "resolved": e.resolved,
                    "resolution_notes": e.resolution_notes,
                }
                for e in self.errors
            ],
        }

        return json.dumps(report_data, indent=2)

    def _generate_csv_report(self) -> str:
        """Generate CSV error report.

        Returns:
            CSV report string
        """
        if not self.errors:
            return "No errors to report"

        # Convert errors to DataFrame
        error_data = []
        for e in self.errors:
            error_data.append(
                {
                    "error_id": e.error_id,
                    "timestamp": e.timestamp.isoformat(),
                    "severity": e.severity.value,
                    "category": e.category.value,
                    "message": e.message,
                    "exception_type": e.exception_type,
                    "recovery_action": e.recovery_action.value
                    if e.recovery_action
                    else "",
                    "retry_count": e.retry_count,
                    "resolved": e.resolved,
                    "resolution_notes": e.resolution_notes or "",
                }
            )

        df = pd.DataFrame(error_data)
        return df.to_csv(index=False)

    def _generate_html_report(self) -> str:
        """Generate HTML error report.

        Returns:
            HTML report string
        """
        summary = self.get_error_summary()

        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>ETL Error Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .summary {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
        .error {{ border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 5px; }}
        .critical {{ border-left: 5px solid #d32f2f; }}
        .high {{ border-left: 5px solid #f57c00; }}
        .medium {{ border-left: 5px solid #fbc02d; }}
        .low {{ border-left: 5px solid #388e3c; }}
        .resolved {{ background-color: #e8f5e8; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <h1>ETL Error Report</h1>

    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Errors:</strong> {summary.get('total_errors', 0)}</p>
        <p><strong>Resolved:</strong> {summary.get('resolved_errors', 0)}</p>
        <p><strong>Unresolved:</strong> {summary.get('unresolved_errors', 0)}</p>
    </div>

    <h2>Error Details</h2>
"""

        for error in self.errors:
            severity_class = error.severity.value
            resolved_class = "resolved" if error.resolved else ""

            html += f"""
    <div class="error {severity_class} {resolved_class}">
        <h3>{error.error_id} - {error.severity.value.upper()}</h3>
        <p><strong>Time:</strong> {error.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p><strong>Category:</strong> {error.category.value}</p>
        <p><strong>Message:</strong> {error.message}</p>
        <p><strong>Type:</strong> {error.exception_type}</p>
        {f'<p><strong>Recovery Action:</strong> {error.recovery_action.value}</p>' if error.recovery_action else ''}
        {f'<p><strong>Resolution:</strong> {error.resolution_notes}</p>' if error.resolution_notes else ''}
    </div>
"""

        html += """
</body>
</html>
"""

        return html

    def _generate_text_report(self) -> str:
        """Generate text error report.

        Returns:
            Text report string
        """
        summary = self.get_error_summary()

        report = "ETL ERROR REPORT\n"
        report += "=" * 50 + "\n\n"

        report += "SUMMARY\n"
        report += "-" * 20 + "\n"
        report += f"Total Errors: {summary.get('total_errors', 0)}\n"
        report += f"Resolved: {summary.get('resolved_errors', 0)}\n"
        report += f"Unresolved: {summary.get('unresolved_errors', 0)}\n\n"

        if self.errors:
            report += "ERROR DETAILS\n"
            report += "-" * 20 + "\n"

            for error in self.errors:
                report += f"\n[{error.error_id}] {error.severity.value.upper()}\n"
                report += f"Time: {error.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
                report += f"Category: {error.category.value}\n"
                report += f"Message: {error.message}\n"
                report += f"Type: {error.exception_type}\n"

                if error.recovery_action:
                    report += f"Recovery Action: {error.recovery_action.value}\n"

                if error.resolved:
                    report += "Status: RESOLVED\n"
                    if error.resolution_notes:
                        report += f"Resolution: {error.resolution_notes}\n"
                else:
                    report += "Status: UNRESOLVED\n"

                report += "-" * 40 + "\n"

        return report


# Convenience functions
def create_error_handler(
    log_errors: bool = True,
    save_error_data: bool = False,
    error_data_path: Optional[str] = None,
    stop_on_critical: bool = True,
) -> ErrorHandler:
    """Create error handler with common configuration.

    Args:
        log_errors: Whether to log errors
        save_error_data: Whether to save error data
        error_data_path: Path to save error data
        stop_on_critical: Whether to stop on critical errors

    Returns:
        Configured ErrorHandler
    """
    config = ErrorHandlingConfig(
        log_errors=log_errors,
        save_error_data=save_error_data,
        error_data_path=error_data_path,
        stop_on_critical=stop_on_critical,
    )

    return ErrorHandler(config)


def create_resilient_handler(
    max_retries: int = 5, enable_fallback: bool = True
) -> ErrorHandler:
    """Create resilient error handler with retry strategies.

    Args:
        max_retries: Maximum number of retries
        enable_fallback: Whether to enable fallback strategies

    Returns:
        Resilient ErrorHandler
    """
    config = ErrorHandlingConfig(
        enable_recovery=True, default_recovery_action=RecoveryAction.RETRY
    )

    handler = ErrorHandler(config)

    # Add aggressive retry strategies
    for category in [ErrorCategory.IO_ERROR, ErrorCategory.NETWORK_ERROR]:
        handler.add_recovery_strategy(
            category,
            RecoveryStrategy(
                action=RecoveryAction.RETRY,
                max_retries=max_retries,
                retry_delay=1.0,
                exponential_backoff=True,
                priority=10,
            ),
        )

    return handler


def create_strict_handler() -> ErrorHandler:
    """Create strict error handler that aborts on any error.

    Returns:
        Strict ErrorHandler
    """
    config = ErrorHandlingConfig(
        enable_recovery=False,
        default_recovery_action=RecoveryAction.ABORT,
        stop_on_critical=True,
        max_error_count=1,
    )

    return ErrorHandler(config)
