# -*- coding: utf-8 -*-
"""QGIS integration module.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

Provides integration functionality with QGIS, including QGIS Python API usage.
"""

import logging
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

logger = logging.getLogger(__name__)


class QGISIntegration:
    """QGIS Integrator.

    Provides integration functionality with QGIS, including QGIS Python API calls.
    """

    def __init__(self, qgis_path: Optional[str] = None):
        """Initialize QGIS integrator.

        Args:
            qgis_path: QGIS installation path, auto-detect if None
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.qgis_path = qgis_path or self._detect_qgis_path()
        self.qgis_available = False
        self.qgs_app = None

        if self.qgis_path:
            self._setup_qgis_environment()

    def _detect_qgis_path(self) -> Optional[str]:
        """Auto-detect QGIS installation path with enhanced detection logic.

        Returns:
            str: QGIS installation path, None if not found
        """
        # Check environment variable first
        env_path = os.environ.get('QGIS_PREFIX_PATH')
        if env_path and Path(env_path).exists():
            self.logger.info(f"Using QGIS path from environment: {env_path}")
            return env_path

        # Common installation paths with version detection
        base_paths = [
            r"C:\OSGeo4W64",
            r"C:\OSGeo4W", 
            r"C:\Program Files\QGIS 3.34",
            r"C:\Program Files\QGIS 3.32",
            r"C:\Program Files\QGIS 3.30",
            r"C:\Program Files\QGIS 3.28",
            r"C:\QGIS",
            # Conda environments
            os.path.expanduser("~/miniconda3/envs/qgis"),
            os.path.expanduser("~/anaconda3/envs/qgis"),
        ]

        # Auto-detect version-specific paths
        program_files = Path(r"C:\Program Files")
        if program_files.exists():
            for qgis_dir in program_files.glob("QGIS*"):
                if qgis_dir.is_dir():
                    base_paths.append(str(qgis_dir))

        for path in base_paths:
            path_obj = Path(path)
            if path_obj.exists():
                # Verify it's a valid QGIS installation
                if self._validate_qgis_installation(path_obj):
                    self.logger.info(f"Detected valid QGIS installation: {path}")
                    return path
                else:
                    self.logger.debug(f"Invalid QGIS installation at: {path}")

        self.logger.warning("No valid QGIS installation detected")
        return None

    def _validate_qgis_installation(self, qgis_path: Path) -> bool:
        """Validate if the path contains a valid QGIS installation.
        
        Args:
            qgis_path: Path to potential QGIS installation
            
        Returns:
            bool: True if valid QGIS installation found
        """
        # Check for key QGIS directories and files
        required_paths = [
            qgis_path / "apps" / "qgis",
            qgis_path / "bin",
        ]
        
        # Alternative structure for some installations
        alt_paths = [
            qgis_path / "qgis",
            qgis_path / "lib",
        ]
        
        return any(path.exists() for path in required_paths) or \
               any(path.exists() for path in alt_paths)

    def _setup_qgis_environment(self) -> None:
        """Setup QGIS environment variables and Python paths with enhanced error handling."""
        try:
            qgis_path = Path(self.qgis_path)
            self.logger.info(f"Setting up QGIS environment for: {qgis_path}")

            # Setup QGIS related environment variables
            os.environ["QGIS_PREFIX_PATH"] = str(qgis_path)
            
            # Try different Qt plugin paths based on installation type
            qt_plugin_paths = [
                qgis_path / "apps" / "Qt5" / "plugins",
                qgis_path / "apps" / "Qt6" / "plugins", 
                qgis_path / "plugins",
                qgis_path / "lib" / "qt" / "plugins",
            ]
            
            for qt_path in qt_plugin_paths:
                if qt_path.exists():
                    os.environ["QT_QPA_PLATFORM_PLUGIN_PATH"] = str(qt_path)
                    self.logger.debug(f"Set Qt plugin path: {qt_path}")
                    break

            # Detect Python version dynamically
            python_version = f"Python{sys.version_info.major}{sys.version_info.minor}"
            
            # Add QGIS Python paths with multiple possible structures
            potential_python_paths = [
                # OSGeo4W structure
                qgis_path / "apps" / "qgis" / "python",
                qgis_path / "apps" / python_version / "Lib" / "site-packages",
                qgis_path / "apps" / "qgis" / "python" / "plugins",
                # Alternative structures
                qgis_path / "python",
                qgis_path / "lib" / "python" / "site-packages",
                qgis_path / "share" / "qgis" / "python",
                # Conda structure
                qgis_path / "lib" / "python3.12" / "site-packages",
                qgis_path / "lib" / "python3.11" / "site-packages",
            ]

            added_paths = []
            for path in potential_python_paths:
                if path.exists() and str(path) not in sys.path:
                    sys.path.insert(0, str(path))
                    added_paths.append(str(path))
                    self.logger.debug(f"Added Python path: {path}")

            if added_paths:
                self.logger.info(f"QGIS environment setup completed. Added {len(added_paths)} Python paths.")
            else:
                self.logger.warning("No QGIS Python paths found, QGIS functionality may be limited")

        except Exception as e:
            self.logger.error(f"QGIS environment setup failed: {e}")
            self.logger.info("Continuing without QGIS integration - falling back to basic geospatial processing")

    def initialize_qgis(self) -> bool:
        """Initialize QGIS application with graceful degradation.

        Returns:
            bool: Whether initialization was successful
        """
        if not self.qgis_path:
            self.logger.warning("No QGIS path detected, skipping QGIS initialization")
            return False
            
        try:
            # Test QGIS availability first
            try:
                from qgis.core import QgsApplication
                from qgis.gui import QgsMapCanvas
            except ImportError as e:
                self.logger.warning(f"QGIS modules not available: {e}")
                self.logger.info("Falling back to basic geospatial processing without QGIS")
                return False

            # Initialize QGIS application
            QgsApplication.setPrefixPath(self.qgis_path, True)
            self.qgs_app = QgsApplication([], False)
            
            # Test if QGIS can be properly initialized
            try:
                self.qgs_app.initQgis()
                
                # Verify QGIS is working by testing basic functionality
                from qgis.core import QgsVectorLayer
                test_layer = QgsVectorLayer("Point", "test", "memory")
                if not test_layer.isValid():
                    raise RuntimeError("QGIS initialization test failed")
                    
            except Exception as init_error:
                self.logger.error(f"QGIS initialization failed: {init_error}")
                if self.qgs_app:
                    try:
                        self.qgs_app.exitQgis()
                    except:
                        pass
                    self.qgs_app = None
                return False

            self.qgis_available = True
            self.logger.info("QGIS application initialized successfully")
            return True

        except ImportError as e:
            self.logger.warning(f"Cannot import QGIS modules: {e}")
            self.logger.info("Continuing with basic geospatial processing capabilities")
            return False
        except Exception as e:
            self.logger.error(f"QGIS initialization failed: {e}")
            self.logger.info("Falling back to alternative geospatial processing methods")
            return False

    def cleanup_qgis(self) -> None:
        """Cleanup QGIS resources."""
        if self.qgs_app:
            try:
                self.qgs_app.exitQgis()
                self.qgis_available = False
                self.logger.info("QGIS resources cleanup completed")
            except Exception as e:
                self.logger.error(f"QGIS resources cleanup failed: {e}")

    def load_vector_layer(
        self, file_path: Union[str, Path], layer_name: Optional[str] = None
    ):
        """Load vector layer.

        Args:
            file_path: Vector file path
            layer_name: Layer name

        Returns:
            QgsVectorLayer: QGIS vector layer object
        """
        if not self.qgis_available:
            raise RuntimeError("QGIS not initialized")

        try:
            from qgis.core import QgsVectorLayer

            file_path = str(file_path)
            layer_name = layer_name or Path(file_path).stem

            layer = QgsVectorLayer(file_path, layer_name, "ogr")

            if not layer.isValid():
                raise ValueError(f"Cannot load vector layer: {file_path}")

            self.logger.info(f"Vector layer loaded successfully: {layer_name}")
            return layer

        except Exception as e:
            self.logger.error(f"Failed to load vector layer: {e}")
            raise ValueError(f"Failed to load vector layer: {e}")

    def load_raster_layer(
        self, file_path: Union[str, Path], layer_name: Optional[str] = None
    ):
        """Load raster layer.

        Args:
            file_path: Raster file path
            layer_name: Layer name

        Returns:
            QgsRasterLayer: QGIS raster layer object
        """
        if not self.qgis_available:
            raise RuntimeError("QGIS not initialized")

        try:
            from qgis.core import QgsRasterLayer

            file_path = str(file_path)
            layer_name = layer_name or Path(file_path).stem

            layer = QgsRasterLayer(file_path, layer_name)

            if not layer.isValid():
                raise ValueError(f"Cannot load raster layer: {file_path}")

            self.logger.info(f"Raster layer loaded successfully: {layer_name}")
            return layer

        except Exception as e:
            self.logger.error(f"Failed to load raster layer: {e}")
            raise ValueError(f"Failed to load raster layer: {e}")

    def run_processing_algorithm(
        self, algorithm_id: str, parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run QGIS processing algorithm.

        Args:
            algorithm_id: Algorithm ID
            parameters: Algorithm parameters

        Returns:
            dict: Algorithm execution result
        """
        if not self.qgis_available:
            raise RuntimeError("QGIS not initialized")

        try:
            import processing
            from qgis.core import QgsProcessingFeedback

            feedback = QgsProcessingFeedback()
            result = processing.run(algorithm_id, parameters, feedback=feedback)

            self.logger.info(
                f"Processing algorithm executed successfully: {algorithm_id}"
            )
            return result

        except Exception as e:
            self.logger.error(f"Processing algorithm execution failed: {e}")
            raise ValueError(f"Processing algorithm execution failed: {e}")

    def get_available_algorithms(self) -> List[str]:
        """Get list of available processing algorithms.

        Returns:
            list: List of algorithm IDs
        """
        if not self.qgis_available:
            raise RuntimeError("QGIS not initialized")

        try:
            from qgis.core import QgsApplication

            registry = QgsApplication.processingRegistry()
            algorithms = []

            for provider in registry.providers():
                for algorithm in provider.algorithms():
                    algorithms.append(algorithm.id())

            self.logger.info(f"Found {len(algorithms)} available algorithms")
            return sorted(algorithms)

        except Exception as e:
            self.logger.error(f"Failed to get algorithm list: {e}")
            return []

    def create_project(self) -> "QgsProject":
        """Create QGIS project.

        Returns:
            QgsProject: QGIS project object
        """
        if not self.qgis_available:
            raise RuntimeError("QGIS not initialized")

        try:
            from qgis.core import QgsProject

            project = QgsProject.instance()
            project.clear()

            self.logger.info("QGIS project created successfully")
            return project

        except Exception as e:
            self.logger.error(f"Failed to create QGIS project: {e}")
            raise ValueError(f"Failed to create QGIS project: {e}")

    def save_project(self, project, file_path: Union[str, Path]) -> None:
        """Save QGIS project.

        Args:
            project: QGIS project object
            file_path: Project file path
        """
        try:
            file_path = str(file_path)
            success = project.write(file_path)

            if success:
                self.logger.info(f"QGIS project saved successfully: {file_path}")
            else:
                raise ValueError("Project save failed")

        except Exception as e:
            self.logger.error(f"Failed to save QGIS project: {e}")
            raise ValueError(f"Failed to save QGIS project: {e}")

    def export_layer_to_file(
        self, layer, file_path: Union[str, Path], driver_name: str = "GPKG"
    ) -> None:
        """Export layer to file.

        Args:
            layer: QGIS layer object
            file_path: Output file path
            driver_name: Output format driver name
        """
        try:
            from qgis.core import QgsVectorFileWriter

            file_path = str(file_path)

            if hasattr(layer, "dataProvider"):  # Vector layer
                error = QgsVectorFileWriter.writeAsVectorFormat(
                    layer, file_path, "utf-8", layer.crs(), driver_name
                )

                if error[0] == QgsVectorFileWriter.NoError:
                    self.logger.info(f"Layer exported successfully: {file_path}")
                else:
                    raise ValueError(f"Layer export failed: {error[1]}")
            else:
                raise ValueError("Unsupported layer type")

        except Exception as e:
            self.logger.error(f"Failed to export layer: {e}")
            raise ValueError(f"Failed to export layer: {e}")

    def get_qgis_info(self) -> Dict[str, Any]:
        """Get QGIS information.

        Returns:
            dict: QGIS information dictionary
        """
        info = {"QGIS_path": self.qgis_path, "QGIS_available": self.qgis_available}

        if self.qgis_available:
            try:
                from qgis.core import Qgis

                info["QGIS_version"] = Qgis.QGIS_VERSION
                info["QGIS_version_int"] = Qgis.QGIS_VERSION_INT
            except ImportError:
                # QGIS not available, skip version info
                pass

        return info

    def __enter__(self):
        """Context manager entry."""
        self.initialize_qgis()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup_qgis()
