# Connect项目环境变量配置
# 复制此文件为.env并填入实际值

# 应用环境 (development, testing, staging, production)
ENVIRONMENT=development

# 数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=connect
DATABASE_USER=postgres
DATABASE_PASSWORD=your_password_here

# 测试数据库配置
TEST_DATABASE_HOST=localhost
TEST_DATABASE_PORT=5432
TEST_DATABASE_NAME=connect_test
TEST_DATABASE_USER=postgres
TEST_DATABASE_PASSWORD=your_test_password_here

# JWT密钥
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_REFRESH_SECRET_KEY=your_jwt_refresh_secret_key_here

# API密钥 (如果需要)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# 监控配置
MONITORING_ENABLED=true
METRICS_ENDPOINT=http://localhost:9090

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/connect.log

# 开发工具配置
DEBUG=true
TESTING=false