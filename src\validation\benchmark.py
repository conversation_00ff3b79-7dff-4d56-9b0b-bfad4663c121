"""统一验证框架性能基准测试

提供全面的性能基准测试，评估验证框架在不同数据规模、
并发场景和配置下的性能表现。
"""

import time
import psutil
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import json
import logging
from pathlib import Path
from datetime import datetime
import gc
import threading
import multiprocessing

# 导入验证框架组件
try:
    from .factory import ValidationFactory
    from .core import ValidationFramework
    from .monitoring import PerformanceTimer, ValidationMonitor
    from .utils import generate_sample_data
    from .config import ValidationConfig, ConfigManager
except ImportError:
    # 如果作为独立脚本运行
    import sys
    sys.path.append(str(Path(__file__).parent))
    from factory import ValidationFactory
    from core import ValidationFramework
    from monitoring import PerformanceTimer, ValidationMonitor
    from utils import generate_sample_data
    from config import ValidationConfig, ConfigManager


@dataclass
class BenchmarkConfig:
    """基准测试配置"""
    # 数据规模配置
    data_sizes: List[int] = None  # [1000, 10000, 100000, 500000, 1000000]
    data_types: List[str] = None  # ['cdr', 'kpi', 'cfg']
    
    # 并发配置
    thread_counts: List[int] = None  # [1, 2, 4, 8, 16]
    process_counts: List[int] = None  # [1, 2, 4]
    
    # 测试配置
    iterations: int = 3
    warmup_iterations: int = 1
    timeout_seconds: int = 300
    
    # 输出配置
    output_dir: str = 'benchmark_results'
    save_detailed_results: bool = True
    generate_report: bool = True
    
    def __post_init__(self):
        if self.data_sizes is None:
            self.data_sizes = [1000, 10000, 50000, 100000, 500000]
        if self.data_types is None:
            self.data_types = ['cdr', 'kpi']
        if self.thread_counts is None:
            self.thread_counts = [1, 2, 4, 8]
        if self.process_counts is None:
            self.process_counts = [1, 2, 4]


@dataclass
class BenchmarkResult:
    """基准测试结果"""
    test_name: str
    data_type: str
    data_size: int
    thread_count: int = 1
    process_count: int = 1
    
    # 性能指标
    execution_time: float = 0.0
    throughput: float = 0.0  # 行/秒
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    
    # 验证结果
    validation_success: bool = True
    error_count: int = 0
    warning_count: int = 0
    
    # 系统资源
    peak_memory_mb: float = 0.0
    avg_cpu_percent: float = 0.0
    
    # 额外信息
    timestamp: str = ''
    notes: str = ''
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, interval: float = 0.1):
        self.interval = interval
        self.monitoring = False
        self.cpu_samples = []
        self.memory_samples = []
        self.thread = None
    
    def start(self):
        """开始监控"""
        self.monitoring = True
        self.cpu_samples = []
        self.memory_samples = []
        self.thread = threading.Thread(target=self._monitor)
        self.thread.start()
    
    def stop(self) -> Tuple[float, float, float]:
        """停止监控并返回统计信息"""
        self.monitoring = False
        if self.thread:
            self.thread.join()
        
        if not self.cpu_samples or not self.memory_samples:
            return 0.0, 0.0, 0.0
        
        avg_cpu = sum(self.cpu_samples) / len(self.cpu_samples)
        peak_memory = max(self.memory_samples)
        avg_memory = sum(self.memory_samples) / len(self.memory_samples)
        
        return avg_cpu, peak_memory, avg_memory
    
    def _monitor(self):
        """监控循环"""
        process = psutil.Process()
        
        while self.monitoring:
            try:
                # CPU使用率
                cpu_percent = process.cpu_percent()
                self.cpu_samples.append(cpu_percent)
                
                # 内存使用
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                self.memory_samples.append(memory_mb)
                
                time.sleep(self.interval)
            except Exception:
                break


class BenchmarkRunner:
    """基准测试运行器"""
    
    def __init__(self, config: BenchmarkConfig):
        self.config = config
        self.results: List[BenchmarkResult] = []
        self.logger = self._setup_logger()
        
        # 创建输出目录
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('benchmark')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def run_all_benchmarks(self) -> List[BenchmarkResult]:
        """运行所有基准测试"""
        self.logger.info("开始运行基准测试套件...")
        
        # 1. 数据规模性能测试
        self._run_data_scale_benchmarks()
        
        # 2. 并发性能测试
        self._run_concurrency_benchmarks()
        
        # 3. 内存效率测试
        self._run_memory_efficiency_benchmarks()
        
        # 4. 验证规则性能测试
        self._run_validation_rule_benchmarks()
        
        # 5. 缓存性能测试
        self._run_cache_performance_benchmarks()
        
        # 保存结果
        if self.config.save_detailed_results:
            self._save_results()
        
        # 生成报告
        if self.config.generate_report:
            self._generate_report()
        
        self.logger.info(f"基准测试完成，共运行 {len(self.results)} 个测试")
        return self.results
    
    def _run_data_scale_benchmarks(self):
        """数据规模性能测试"""
        self.logger.info("运行数据规模性能测试...")
        
        for data_type in self.config.data_types:
            for data_size in self.config.data_sizes:
                self.logger.info(f"测试 {data_type} 数据，规模: {data_size:,} 行")
                
                result = self._run_single_benchmark(
                    test_name=f"data_scale_{data_type}_{data_size}",
                    data_type=data_type,
                    data_size=data_size
                )
                
                if result:
                    self.results.append(result)
    
    def _run_concurrency_benchmarks(self):
        """并发性能测试"""
        self.logger.info("运行并发性能测试...")
        
        # 固定数据规模进行并发测试
        test_data_size = 50000
        
        for data_type in self.config.data_types:
            # 线程并发测试
            for thread_count in self.config.thread_counts:
                self.logger.info(f"测试 {data_type} 数据，线程数: {thread_count}")
                
                result = self._run_concurrent_benchmark(
                    test_name=f"thread_concurrency_{data_type}_{thread_count}",
                    data_type=data_type,
                    data_size=test_data_size,
                    thread_count=thread_count,
                    use_processes=False
                )
                
                if result:
                    self.results.append(result)
            
            # 进程并发测试
            for process_count in self.config.process_counts:
                if process_count <= multiprocessing.cpu_count():
                    self.logger.info(f"测试 {data_type} 数据，进程数: {process_count}")
                    
                    result = self._run_concurrent_benchmark(
                        test_name=f"process_concurrency_{data_type}_{process_count}",
                        data_type=data_type,
                        data_size=test_data_size,
                        process_count=process_count,
                        use_processes=True
                    )
                    
                    if result:
                        self.results.append(result)
    
    def _run_memory_efficiency_benchmarks(self):
        """内存效率测试"""
        self.logger.info("运行内存效率测试...")
        
        # 测试大数据集的内存使用
        large_sizes = [100000, 500000, 1000000]
        
        for data_type in self.config.data_types:
            for data_size in large_sizes:
                if data_size <= max(self.config.data_sizes):
                    self.logger.info(f"内存效率测试 {data_type} 数据，规模: {data_size:,} 行")
                    
                    result = self._run_memory_benchmark(
                        test_name=f"memory_efficiency_{data_type}_{data_size}",
                        data_type=data_type,
                        data_size=data_size
                    )
                    
                    if result:
                        self.results.append(result)
    
    def _run_validation_rule_benchmarks(self):
        """验证规则性能测试"""
        self.logger.info("运行验证规则性能测试...")
        
        test_data_size = 10000
        
        # 测试不同类型的验证规则
        rule_types = ['structure', 'value', 'telecom', 'all']
        
        for data_type in self.config.data_types:
            for rule_type in rule_types:
                self.logger.info(f"测试 {data_type} 数据的 {rule_type} 规则")
                
                result = self._run_rule_benchmark(
                    test_name=f"rule_performance_{data_type}_{rule_type}",
                    data_type=data_type,
                    data_size=test_data_size,
                    rule_type=rule_type
                )
                
                if result:
                    self.results.append(result)
    
    def _run_cache_performance_benchmarks(self):
        """缓存性能测试"""
        self.logger.info("运行缓存性能测试...")
        
        test_data_size = 10000
        
        for data_type in self.config.data_types:
            # 测试缓存命中性能
            result = self._run_cache_benchmark(
                test_name=f"cache_performance_{data_type}",
                data_type=data_type,
                data_size=test_data_size
            )
            
            if result:
                self.results.append(result)
    
    def _run_single_benchmark(
        self,
        test_name: str,
        data_type: str,
        data_size: int
    ) -> Optional[BenchmarkResult]:
        """运行单个基准测试"""
        try:
            # 生成测试数据
            data = generate_sample_data(data_type, data_size)
            
            # 创建验证框架
            factory = ValidationFactory()
            framework = factory.create_framework(data_type)
            
            # 预热
            for _ in range(self.config.warmup_iterations):
                framework.validate(data.head(min(1000, data_size)))
            
            # 清理内存
            gc.collect()
            
            # 开始监控
            monitor = SystemMonitor()
            monitor.start()
            
            # 执行测试
            execution_times = []
            
            for iteration in range(self.config.iterations):
                start_time = time.time()
                
                result = framework.validate(data)
                
                end_time = time.time()
                execution_times.append(end_time - start_time)
            
            # 停止监控
            avg_cpu, peak_memory, avg_memory = monitor.stop()
            
            # 计算平均执行时间
            avg_execution_time = sum(execution_times) / len(execution_times)
            throughput = data_size / avg_execution_time
            
            return BenchmarkResult(
                test_name=test_name,
                data_type=data_type,
                data_size=data_size,
                execution_time=avg_execution_time,
                throughput=throughput,
                memory_usage_mb=avg_memory,
                peak_memory_mb=peak_memory,
                cpu_usage_percent=avg_cpu,
                validation_success=result.is_valid,
                error_count=len([i for i in result.issues if i.severity.value == 'ERROR']),
                warning_count=len([i for i in result.issues if i.severity.value == 'WARNING'])
            )
            
        except Exception as e:
            self.logger.error(f"基准测试失败 {test_name}: {e}")
            return None
    
    def _run_concurrent_benchmark(
        self,
        test_name: str,
        data_type: str,
        data_size: int,
        thread_count: int = 1,
        process_count: int = 1,
        use_processes: bool = False
    ) -> Optional[BenchmarkResult]:
        """运行并发基准测试"""
        try:
            # 生成测试数据
            data = generate_sample_data(data_type, data_size)
            
            # 将数据分割为多个块
            worker_count = process_count if use_processes else thread_count
            chunk_size = data_size // worker_count
            data_chunks = [data.iloc[i:i+chunk_size] for i in range(0, data_size, chunk_size)]
            
            # 开始监控
            monitor = SystemMonitor()
            monitor.start()
            
            start_time = time.time()
            
            if use_processes:
                with ProcessPoolExecutor(max_workers=process_count) as executor:
                    futures = [executor.submit(self._validate_chunk, chunk, data_type) 
                              for chunk in data_chunks]
                    results = [future.result() for future in futures]
            else:
                with ThreadPoolExecutor(max_workers=thread_count) as executor:
                    futures = [executor.submit(self._validate_chunk, chunk, data_type) 
                              for chunk in data_chunks]
                    results = [future.result() for future in futures]
            
            end_time = time.time()
            
            # 停止监控
            avg_cpu, peak_memory, avg_memory = monitor.stop()
            
            execution_time = end_time - start_time
            throughput = data_size / execution_time
            
            # 合并验证结果
            total_errors = sum(r['error_count'] for r in results)
            total_warnings = sum(r['warning_count'] for r in results)
            all_valid = all(r['is_valid'] for r in results)
            
            return BenchmarkResult(
                test_name=test_name,
                data_type=data_type,
                data_size=data_size,
                thread_count=thread_count,
                process_count=process_count,
                execution_time=execution_time,
                throughput=throughput,
                memory_usage_mb=avg_memory,
                peak_memory_mb=peak_memory,
                cpu_usage_percent=avg_cpu,
                validation_success=all_valid,
                error_count=total_errors,
                warning_count=total_warnings
            )
            
        except Exception as e:
            self.logger.error(f"并发基准测试失败 {test_name}: {e}")
            return None
    
    def _validate_chunk(self, data_chunk: pd.DataFrame, data_type: str) -> Dict[str, Any]:
        """验证数据块"""
        factory = ValidationFactory()
        framework = factory.create_framework(data_type)
        result = framework.validate(data_chunk)
        
        return {
            'is_valid': result.is_valid,
            'error_count': len([i for i in result.issues if i.severity.value == 'ERROR']),
            'warning_count': len([i for i in result.issues if i.severity.value == 'WARNING'])
        }
    
    def _run_memory_benchmark(
        self,
        test_name: str,
        data_type: str,
        data_size: int
    ) -> Optional[BenchmarkResult]:
        """运行内存基准测试"""
        try:
            # 记录初始内存
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024
            
            # 生成测试数据
            data = generate_sample_data(data_type, data_size)
            data_memory = process.memory_info().rss / 1024 / 1024
            
            # 创建验证框架
            factory = ValidationFactory()
            framework = factory.create_framework(data_type)
            
            # 开始监控
            monitor = SystemMonitor()
            monitor.start()
            
            start_time = time.time()
            result = framework.validate(data)
            end_time = time.time()
            
            # 停止监控
            avg_cpu, peak_memory, avg_memory = monitor.stop()
            
            execution_time = end_time - start_time
            throughput = data_size / execution_time
            
            # 计算内存使用
            memory_overhead = peak_memory - initial_memory
            
            return BenchmarkResult(
                test_name=test_name,
                data_type=data_type,
                data_size=data_size,
                execution_time=execution_time,
                throughput=throughput,
                memory_usage_mb=memory_overhead,
                peak_memory_mb=peak_memory,
                cpu_usage_percent=avg_cpu,
                validation_success=result.is_valid,
                error_count=len([i for i in result.issues if i.severity.value == 'ERROR']),
                warning_count=len([i for i in result.issues if i.severity.value == 'WARNING']),
                notes=f"数据内存: {data_memory-initial_memory:.1f}MB, 验证开销: {memory_overhead:.1f}MB"
            )
            
        except Exception as e:
            self.logger.error(f"内存基准测试失败 {test_name}: {e}")
            return None
    
    def _run_rule_benchmark(
        self,
        test_name: str,
        data_type: str,
        data_size: int,
        rule_type: str
    ) -> Optional[BenchmarkResult]:
        """运行规则性能基准测试"""
        try:
            # 生成测试数据
            data = generate_sample_data(data_type, data_size)
            
            # 创建特定规则的验证框架
            factory = ValidationFactory()
            
            if rule_type == 'all':
                framework = factory.create_framework(data_type)
            else:
                # 创建只包含特定类型规则的框架
                from .rules import ValidationRuleFactory
                rule_factory = ValidationRuleFactory()
                
                if rule_type == 'structure':
                    rules = rule_factory.create_structure_rules(data_type)
                elif rule_type == 'value':
                    rules = rule_factory.create_value_rules(data_type)
                elif rule_type == 'telecom':
                    rules = rule_factory.create_telecom_rules(data_type)
                else:
                    rules = rule_factory.create_rules(data_type)
                
                framework = ValidationFramework(rules)
            
            # 开始监控
            monitor = SystemMonitor()
            monitor.start()
            
            start_time = time.time()
            result = framework.validate(data)
            end_time = time.time()
            
            # 停止监控
            avg_cpu, peak_memory, avg_memory = monitor.stop()
            
            execution_time = end_time - start_time
            throughput = data_size / execution_time
            
            return BenchmarkResult(
                test_name=test_name,
                data_type=data_type,
                data_size=data_size,
                execution_time=execution_time,
                throughput=throughput,
                memory_usage_mb=avg_memory,
                peak_memory_mb=peak_memory,
                cpu_usage_percent=avg_cpu,
                validation_success=result.is_valid,
                error_count=len([i for i in result.issues if i.severity.value == 'ERROR']),
                warning_count=len([i for i in result.issues if i.severity.value == 'WARNING']),
                notes=f"规则类型: {rule_type}"
            )
            
        except Exception as e:
            self.logger.error(f"规则基准测试失败 {test_name}: {e}")
            return None
    
    def _run_cache_benchmark(
        self,
        test_name: str,
        data_type: str,
        data_size: int
    ) -> Optional[BenchmarkResult]:
        """运行缓存性能基准测试"""
        try:
            # 生成测试数据
            data = generate_sample_data(data_type, data_size)
            
            # 创建验证框架
            factory = ValidationFactory()
            framework = factory.create_framework(data_type)
            
            # 第一次验证 (缓存未命中)
            start_time = time.time()
            result1 = framework.validate(data)
            first_time = time.time() - start_time
            
            # 第二次验证 (缓存命中)
            start_time = time.time()
            result2 = framework.validate(data)
            second_time = time.time() - start_time
            
            # 计算缓存效果
            cache_speedup = first_time / second_time if second_time > 0 else 1.0
            
            return BenchmarkResult(
                test_name=test_name,
                data_type=data_type,
                data_size=data_size,
                execution_time=second_time,
                throughput=data_size / second_time,
                validation_success=result2.is_valid,
                error_count=len([i for i in result2.issues if i.severity.value == 'ERROR']),
                warning_count=len([i for i in result2.issues if i.severity.value == 'WARNING']),
                notes=f"首次: {first_time:.3f}s, 缓存: {second_time:.3f}s, 加速: {cache_speedup:.1f}x"
            )
            
        except Exception as e:
            self.logger.error(f"缓存基准测试失败 {test_name}: {e}")
            return None
    
    def _save_results(self):
        """保存测试结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存详细结果
        results_file = self.output_dir / f'benchmark_results_{timestamp}.json'
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump([asdict(result) for result in self.results], f, indent=2, ensure_ascii=False)
        
        # 保存CSV格式
        csv_file = self.output_dir / f'benchmark_results_{timestamp}.csv'
        df = pd.DataFrame([asdict(result) for result in self.results])
        df.to_csv(csv_file, index=False, encoding='utf-8')
        
        self.logger.info(f"结果已保存到: {results_file} 和 {csv_file}")
    
    def _generate_report(self):
        """生成性能报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = self.output_dir / f'benchmark_report_{timestamp}.html'
        
        # 生成HTML报告
        html_content = self._create_html_report()
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"性能报告已生成: {report_file}")
    
    def _create_html_report(self) -> str:
        """创建HTML报告"""
        # 计算汇总统计
        df = pd.DataFrame([asdict(result) for result in self.results])
        
        # 按数据类型分组的性能统计
        performance_by_type = df.groupby('data_type').agg({
            'throughput': ['mean', 'max', 'min'],
            'execution_time': ['mean', 'max', 'min'],
            'memory_usage_mb': ['mean', 'max', 'min'],
            'cpu_usage_percent': ['mean', 'max', 'min']
        }).round(2)
        
        # 按数据规模的性能趋势
        scale_performance = df[df['test_name'].str.contains('data_scale')].groupby(['data_type', 'data_size']).agg({
            'throughput': 'mean',
            'execution_time': 'mean',
            'memory_usage_mb': 'mean'
        }).round(2)
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>验证框架性能基准测试报告</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #e8f4f8; border-radius: 5px; }}
        table {{ border-collapse: collapse; width: 100%; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .good {{ color: green; font-weight: bold; }}
        .warning {{ color: orange; font-weight: bold; }}
        .error {{ color: red; font-weight: bold; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 统一验证框架性能基准测试报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>测试配置: {len(self.results)} 个测试，{self.config.iterations} 次迭代</p>
    </div>
    
    <div class="section">
        <h2>📊 性能概览</h2>
        <div class="metric">
            <strong>最高吞吐量:</strong> {df['throughput'].max():,.0f} 行/秒
        </div>
        <div class="metric">
            <strong>平均执行时间:</strong> {df['execution_time'].mean():.3f} 秒
        </div>
        <div class="metric">
            <strong>平均内存使用:</strong> {df['memory_usage_mb'].mean():.1f} MB
        </div>
        <div class="metric">
            <strong>平均CPU使用:</strong> {df['cpu_usage_percent'].mean():.1f}%
        </div>
    </div>
    
    <div class="section">
        <h2>📈 按数据类型的性能统计</h2>
        {performance_by_type.to_html()}
    </div>
    
    <div class="section">
        <h2>📏 数据规模性能趋势</h2>
        {scale_performance.to_html()}
    </div>
    
    <div class="section">
        <h2>📋 详细测试结果</h2>
        {df.to_html(index=False, classes='table')}
    </div>
    
    <div class="section">
        <h2>🎯 性能建议</h2>
        <ul>
            <li>{'✅ 性能表现优秀' if df['throughput'].mean() > 10000 else '⚠️ 建议优化性能'}</li>
            <li>{'✅ 内存使用合理' if df['memory_usage_mb'].mean() < 500 else '⚠️ 建议优化内存使用'}</li>
            <li>{'✅ CPU使用效率高' if df['cpu_usage_percent'].mean() < 80 else '⚠️ 建议优化CPU使用'}</li>
        </ul>
    </div>
</body>
</html>
        """
        
        return html


def run_benchmark(
    data_sizes: List[int] = None,
    data_types: List[str] = None,
    output_dir: str = 'benchmark_results',
    iterations: int = 3
) -> List[BenchmarkResult]:
    """运行基准测试的便捷函数"""
    config = BenchmarkConfig(
        data_sizes=data_sizes,
        data_types=data_types,
        output_dir=output_dir,
        iterations=iterations
    )
    
    runner = BenchmarkRunner(config)
    return runner.run_all_benchmarks()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='统一验证框架性能基准测试')
    parser.add_argument('--data-sizes', nargs='+', type=int, 
                       default=[1000, 10000, 50000, 100000],
                       help='测试数据规模')
    parser.add_argument('--data-types', nargs='+', 
                       default=['cdr', 'kpi'],
                       help='测试数据类型')
    parser.add_argument('--iterations', type=int, default=3,
                       help='测试迭代次数')
    parser.add_argument('--output-dir', default='benchmark_results',
                       help='输出目录')
    parser.add_argument('--quick', action='store_true',
                       help='快速测试模式')
    
    args = parser.parse_args()
    
    if args.quick:
        # 快速测试模式
        config = BenchmarkConfig(
            data_sizes=[1000, 10000],
            data_types=['cdr'],
            thread_counts=[1, 2],
            process_counts=[1],
            iterations=1,
            output_dir=args.output_dir
        )
    else:
        config = BenchmarkConfig(
            data_sizes=args.data_sizes,
            data_types=args.data_types,
            iterations=args.iterations,
            output_dir=args.output_dir
        )
    
    runner = BenchmarkRunner(config)
    results = runner.run_all_benchmarks()
    
    # 打印汇总结果
    print("\n🎉 基准测试完成!")
    print(f"总测试数: {len(results)}")
    
    if results:
        df = pd.DataFrame([asdict(result) for result in results])
        print(f"最高吞吐量: {df['throughput'].max():,.0f} 行/秒")
        print(f"平均执行时间: {df['execution_time'].mean():.3f} 秒")
        print(f"平均内存使用: {df['memory_usage_mb'].mean():.1f} MB")
        print(f"结果保存在: {args.output_dir}")


if __name__ == '__main__':
    main()