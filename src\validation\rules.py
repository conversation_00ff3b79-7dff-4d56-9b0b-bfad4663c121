"""Business-specific validation rules.

This module provides pre-configured validation rules for different
types of telecom data used in the Connect system.
"""

from typing import Dict, List, Optional

from ..types.telecom_types import DataSourceType
from .core import ValidationSeverity
from .validators import (
    DataStructureValidator,
    DataValueValidator,
    DatabaseValidator,
    FileValidator,
    TelecomDataValidator,
)


class CDRValidationRules:
    """Validation rules for CDR (Call Detail Record) data."""
    
    @staticmethod
    def get_structure_validator() -> DataStructureValidator:
        """Get CDR data structure validator."""
        return DataStructureValidator(
            name="cdr_structure",
            required_columns=[
                "CALL_ID",
                "CALLER_NUMBER",
                "CALLED_NUMBER",
                "CALL_START_TIME",
                "CALL_END_TIME",
                "CALL_DURATION",
                "CALL_STATUS",
                "CELL_ID",
                "LAC",
                "LONGITUDE",
                "LATITUDE"
            ],
            optional_columns=[
                "CALL_TYPE",
                "NETWORK_TYPE",
                "SIGNAL_STRENGTH",
                "HANDOVER_COUNT",
                "DATA_VOLUME_UP",
                "DATA_VOLUME_DOWN"
            ],
            min_rows=1,
            severity=ValidationSeverity.ERROR
        )
    
    @staticmethod
    def get_value_validator() -> DataValueValidator:
        """Get CDR data value validator."""
        return DataValueValidator(
            name="cdr_values",
            column_rules={
                "CALL_ID": {
                    "dtype": "object",
                    "pattern": r"^[A-Za-z0-9_-]+$"
                },
                "CALLER_NUMBER": {
                    "dtype": "object",
                    "pattern": r"^\+?[0-9]{8,15}$"
                },
                "CALLED_NUMBER": {
                    "dtype": "object",
                    "pattern": r"^\+?[0-9]{8,15}$"
                },
                "CALL_DURATION": {
                    "dtype": "float64",
                    "min_value": 0
                },
                "LONGITUDE": {
                    "dtype": "float64",
                    "min_value": -180,
                    "max_value": 180
                },
                "LATITUDE": {
                    "dtype": "float64",
                    "min_value": -90,
                    "max_value": 90
                },
                "SIGNAL_STRENGTH": {
                    "dtype": "float64",
                    "min_value": -150,
                    "max_value": 0
                }
            },
            allow_nulls=False,
            null_columns={"CALL_TYPE", "NETWORK_TYPE", "SIGNAL_STRENGTH", "HANDOVER_COUNT"},
            severity=ValidationSeverity.ERROR
        )
    
    @staticmethod
    def get_telecom_validator() -> TelecomDataValidator:
        """Get CDR telecom-specific validator."""
        return TelecomDataValidator(
            name="cdr_telecom",
            data_source_type=DataSourceType.CDR,
            severity=ValidationSeverity.ERROR
        )
    
    @staticmethod
    def get_file_validator() -> FileValidator:
        """Get CDR file validator."""
        return FileValidator(
            name="cdr_file",
            allowed_extensions=[".csv", ".xlsx", ".xls"],
            max_size_mb=500,  # 500MB max for CDR files
            min_size_mb=0.001,  # 1KB min
            severity=ValidationSeverity.ERROR
        )


class KPIValidationRules:
    """Validation rules for KPI (Key Performance Indicator) data."""
    
    @staticmethod
    def get_structure_validator() -> DataStructureValidator:
        """Get KPI data structure validator."""
        return DataStructureValidator(
            name="kpi_structure",
            required_columns=[
                "KPI_ID",
                "KPI_NAME",
                "KPI_VALUE",
                "MEASUREMENT_TIME",
                "CELL_ID",
                "LONGITUDE",
                "LATITUDE"
            ],
            optional_columns=[
                "KPI_UNIT",
                "KPI_CATEGORY",
                "NETWORK_TYPE",
                "VENDOR",
                "TECHNOLOGY"
            ],
            min_rows=1,
            severity=ValidationSeverity.ERROR
        )
    
    @staticmethod
    def get_value_validator() -> DataValueValidator:
        """Get KPI data value validator."""
        return DataValueValidator(
            name="kpi_values",
            column_rules={
                "KPI_ID": {
                    "dtype": "object",
                    "pattern": r"^[A-Za-z0-9_-]+$"
                },
                "KPI_NAME": {
                    "dtype": "object",
                    "pattern": r"^[A-Za-z0-9_\s-]+$"
                },
                "KPI_VALUE": {
                    "dtype": "float64",
                    "min_value": -999999,
                    "max_value": 999999
                },
                "CELL_ID": {
                    "dtype": "object",
                    "pattern": r"^[A-Za-z0-9_-]+$"
                },
                "LONGITUDE": {
                    "dtype": "float64",
                    "min_value": -180,
                    "max_value": 180
                },
                "LATITUDE": {
                    "dtype": "float64",
                    "min_value": -90,
                    "max_value": 90
                }
            },
            allow_nulls=False,
            null_columns={"KPI_UNIT", "KPI_CATEGORY", "NETWORK_TYPE", "VENDOR"},
            severity=ValidationSeverity.ERROR
        )
    
    @staticmethod
    def get_telecom_validator() -> TelecomDataValidator:
        """Get KPI telecom-specific validator."""
        return TelecomDataValidator(
            name="kpi_telecom",
            data_source_type=DataSourceType.KPI,
            severity=ValidationSeverity.ERROR
        )
    
    @staticmethod
    def get_file_validator() -> FileValidator:
        """Get KPI file validator."""
        return FileValidator(
            name="kpi_file",
            allowed_extensions=[".csv", ".xlsx", ".xls"],
            max_size_mb=200,  # 200MB max for KPI files
            min_size_mb=0.001,  # 1KB min
            severity=ValidationSeverity.ERROR
        )


class CFGValidationRules:
    """Validation rules for CFG (Configuration) data."""
    
    @staticmethod
    def get_structure_validator() -> DataStructureValidator:
        """Get CFG data structure validator."""
        return DataStructureValidator(
            name="cfg_structure",
            required_columns=[
                "CONFIG_ID",
                "CONFIG_NAME",
                "CONFIG_VALUE",
                "CONFIG_TYPE",
                "CREATED_TIME"
            ],
            optional_columns=[
                "CONFIG_DESCRIPTION",
                "CONFIG_CATEGORY",
                "IS_ACTIVE",
                "UPDATED_TIME",
                "UPDATED_BY"
            ],
            min_rows=1,
            severity=ValidationSeverity.ERROR
        )
    
    @staticmethod
    def get_value_validator() -> DataValueValidator:
        """Get CFG data value validator."""
        return DataValueValidator(
            name="cfg_values",
            column_rules={
                "CONFIG_ID": {
                    "dtype": "object",
                    "pattern": r"^[A-Za-z0-9_-]+$"
                },
                "CONFIG_NAME": {
                    "dtype": "object",
                    "pattern": r"^[A-Za-z0-9_\s-]+$"
                },
                "CONFIG_TYPE": {
                    "dtype": "object",
                    "pattern": r"^(string|integer|float|boolean|json)$"
                },
                "IS_ACTIVE": {
                    "dtype": "bool"
                }
            },
            allow_nulls=False,
            null_columns={"CONFIG_DESCRIPTION", "CONFIG_CATEGORY", "UPDATED_TIME", "UPDATED_BY"},
            severity=ValidationSeverity.ERROR
        )
    
    @staticmethod
    def get_telecom_validator() -> TelecomDataValidator:
        """Get CFG telecom-specific validator."""
        return TelecomDataValidator(
            name="cfg_telecom",
            data_source_type=DataSourceType.CFG,
            severity=ValidationSeverity.ERROR
        )
    
    @staticmethod
    def get_file_validator() -> FileValidator:
        """Get CFG file validator."""
        return FileValidator(
            name="cfg_file",
            allowed_extensions=[".csv", ".xlsx", ".xls", ".json"],
            max_size_mb=50,  # 50MB max for config files
            min_size_mb=0.001,  # 1KB min
            severity=ValidationSeverity.ERROR
        )


class SCOREValidationRules:
    """Validation rules for SCORE data."""
    
    @staticmethod
    def get_structure_validator() -> DataStructureValidator:
        """Get SCORE data structure validator."""
        return DataStructureValidator(
            name="score_structure",
            required_columns=[],  # No required columns for SCORE data
            optional_columns=[
                "SCORE_ID",
                "SCORE_VALUE", 
                "SCORE_TYPE",
                "MEASUREMENT_TIME",
                "SCORE_NAME",
                "SCORE_CATEGORY",
                "SCORE_UNIT"
            ],
            min_rows=1,
            severity=ValidationSeverity.ERROR
        )
    
    @staticmethod
    def get_value_validator() -> DataValueValidator:
        """Get SCORE data value validator."""
        return DataValueValidator(
            name="score_values",
            column_rules={
                "SCORE_VALUE": {
                    "dtype": "float64",
                    "min_value": -999999,
                    "max_value": 999999
                }
            },
            allow_nulls=True,  # Allow nulls for SCORE data
            null_columns={"SCORE_ID", "SCORE_TYPE", "MEASUREMENT_TIME", "SCORE_NAME", "SCORE_CATEGORY", "SCORE_UNIT"},
            severity=ValidationSeverity.WARNING  # Use warning instead of error
        )
    
    @staticmethod
    def get_telecom_validator() -> TelecomDataValidator:
        """Get SCORE telecom-specific validator."""
        return TelecomDataValidator(
            name="score_telecom",
            data_source_type=DataSourceType.SCORE,
            severity=ValidationSeverity.WARNING
        )
    
    @staticmethod
    def get_file_validator() -> FileValidator:
        """Get SCORE file validator."""
        return FileValidator(
            name="score_file",
            allowed_extensions=[".csv", ".xlsx", ".xls"],
            max_size_mb=100,  # 100MB max for SCORE files
            min_size_mb=0.001,  # 1KB min
            severity=ValidationSeverity.ERROR
        )


class DatabaseValidationRules:
    """Validation rules for database operations."""
    
    @staticmethod
    def get_naming_validator() -> DatabaseValidator:
        """Get database naming validator."""
        return DatabaseValidator(
            name="database_naming",
            max_name_length=63,  # PostgreSQL limit
            name_pattern=r"^[a-zA-Z][a-zA-Z0-9_]*$",
            severity=ValidationSeverity.ERROR
        )


class ValidationRuleFactory:
    """Factory for creating validation rule sets."""
    
    @staticmethod
    def create_cdr_validators() -> List:
        """Create complete CDR validation rule set."""
        return [
            CDRValidationRules.get_file_validator(),
            CDRValidationRules.get_structure_validator(),
            CDRValidationRules.get_value_validator(),
            CDRValidationRules.get_telecom_validator()
        ]
    
    @staticmethod
    def create_kpi_validators() -> List:
        """Create complete KPI validation rule set."""
        return [
            KPIValidationRules.get_file_validator(),
            KPIValidationRules.get_structure_validator(),
            KPIValidationRules.get_value_validator(),
            KPIValidationRules.get_telecom_validator()
        ]
    
    @staticmethod
    def create_cfg_validators() -> List:
        """Create complete CFG validation rule set."""
        return [
            CFGValidationRules.get_file_validator(),
            CFGValidationRules.get_structure_validator(),
            CFGValidationRules.get_value_validator(),
            CFGValidationRules.get_telecom_validator()
        ]
    
    @staticmethod
    def create_score_validators() -> List:
        """Create complete SCORE validation rule set."""
        return [
            SCOREValidationRules.get_file_validator(),
            SCOREValidationRules.get_structure_validator(),
            SCOREValidationRules.get_value_validator(),
            SCOREValidationRules.get_telecom_validator()
        ]
    
    @staticmethod
    def create_database_validators() -> List:
        """Create database validation rule set."""
        return [
            DatabaseValidationRules.get_naming_validator()
        ]
    
    @staticmethod
    def create_validators_for_data_type(data_type: str) -> List:
        """Create validators for specific data type.
        
        Args:
            data_type: Type of data ('cdr', 'kpi', 'cfg', 'score', 'database')
            
        Returns:
            List of validation rules
            
        Raises:
            ValueError: If data_type is not supported
        """
        data_type = data_type.lower()
        
        if data_type == "cdr":
            return ValidationRuleFactory.create_cdr_validators()
        elif data_type == "kpi":
            return ValidationRuleFactory.create_kpi_validators()
        elif data_type == "cfg":
            return ValidationRuleFactory.create_cfg_validators()
        elif data_type == "score":
            return ValidationRuleFactory.create_score_validators()
        elif data_type == "database":
            return ValidationRuleFactory.create_database_validators()
        else:
            raise ValueError(f"Unsupported data type: {data_type}")