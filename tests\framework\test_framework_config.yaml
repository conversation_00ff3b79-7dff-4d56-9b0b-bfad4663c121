# 综合测试框架配置文件
# 该文件定义了测试套件和质量门控的配置

# 测试套件配置
test_suites:
  # 单元测试套件
  unit_tests:
    name: "单元测试"
    description: "核心功能单元测试"
    test_paths:
      - "tests/unit"
    markers:
      - "unit"
    priority: "high"
    test_type: "unit"
    parallel: true
    timeout: 300
    retries: 2
    dependencies: []
    environment:
      setup_method: null
      teardown_method: null
    thresholds:
      coverage_threshold: 90.0
      performance_threshold: null
    custom_config:
      pytest_args:
        - "--strict-markers"
        - "--tb=short"
  
  # 集成测试套件
  integration_tests:
    name: "集成测试"
    description: "模块间集成测试"
    test_paths:
      - "tests/integration"
    markers:
      - "integration"
    priority: "high"
    test_type: "integration"
    parallel: false
    timeout: 600
    retries: 1
    dependencies:
      - "unit_tests"
    environment:
      setup_method: "setup_integration_environment"
      teardown_method: "teardown_integration_environment"
    thresholds:
      coverage_threshold: 80.0
      performance_threshold:
        max_response_time: 1000
        min_throughput: 100
    custom_config:
      database_config:
        host: "localhost"
        port: 5432
        database: "test_db"
  
  # 端到端测试套件
  e2e_tests:
    name: "端到端测试"
    description: "完整业务流程测试"
    test_paths:
      - "tests/e2e"
    markers:
      - "e2e"
    priority: "medium"
    test_type: "e2e"
    parallel: false
    timeout: 1200
    retries: 1
    dependencies:
      - "integration_tests"
    environment:
      setup_method: "setup_e2e_environment"
      teardown_method: "teardown_e2e_environment"
    thresholds:
      coverage_threshold: 70.0
      performance_threshold:
        max_response_time: 2000
        min_throughput: 50
    custom_config:
      browser_config:
        headless: true
        timeout: 30
  
  # 性能测试套件
  performance_tests:
    name: "性能测试"
    description: "系统性能基准测试"
    test_paths:
      - "tests/performance"
    markers:
      - "performance"
    priority: "medium"
    test_type: "performance"
    parallel: false
    timeout: 1800
    retries: 0
    dependencies:
      - "integration_tests"
    environment:
      setup_method: "setup_performance_environment"
      teardown_method: "teardown_performance_environment"
    thresholds:
      coverage_threshold: null
      performance_threshold:
        max_response_time: 500
        min_throughput: 1000
        max_cpu_usage: 80
        max_memory_usage: 1024
    custom_config:
      load_config:
        concurrent_users: 100
        duration: 300
        ramp_up: 60
  
  # 安全测试套件
  security_tests:
    name: "安全测试"
    description: "安全漏洞和威胁测试"
    test_paths:
      - "tests/security"
    markers:
      - "security"
    priority: "high"
    test_type: "security"
    parallel: false
    timeout: 900
    retries: 1
    dependencies:
      - "integration_tests"
    environment:
      setup_method: "setup_security_environment"
      teardown_method: "teardown_security_environment"
    thresholds:
      coverage_threshold: null
      performance_threshold: null
    custom_config:
      security_tools:
        - "bandit"
        - "safety"
        - "semgrep"
  
  # 数据库测试套件
  database_tests:
    name: "数据库测试"
    description: "数据库操作和性能测试"
    test_paths:
      - "tests/database"
    markers:
      - "database"
    priority: "high"
    test_type: "integration"
    parallel: false
    timeout: 600
    retries: 1
    dependencies:
      - "unit_tests"
    environment:
      setup_method: "setup_database_environment"
      teardown_method: "teardown_database_environment"
    thresholds:
      coverage_threshold: 85.0
      performance_threshold:
        max_query_time: 100
        max_connection_time: 10
    custom_config:
      database_config:
        test_data_size: "medium"
        isolation_level: "READ_COMMITTED"

# 质量门控配置
quality_gates:
  # 基础质量门控
  basic:
    name: "基础质量门控"
    description: "最低质量要求"
    coverage_threshold: 70.0
    success_rate_threshold: 90.0
    performance_thresholds:
      max_response_time: 2000
      min_throughput: 50
    security_checks:
      - "no_critical_vulnerabilities"
    custom_checks: []
    blocking: true
  
  # 生产就绪质量门控
  production_ready:
    name: "生产就绪质量门控"
    description: "生产环境部署要求"
    coverage_threshold: 85.0
    success_rate_threshold: 95.0
    performance_thresholds:
      max_response_time: 1000
      min_throughput: 100
      max_cpu_usage: 70
      max_memory_usage: 512
    security_checks:
      - "no_critical_vulnerabilities"
      - "no_high_vulnerabilities"
      - "secure_configurations"
    custom_checks:
      - "code_quality_check"
      - "documentation_check"
    blocking: true
  
  # 功能完整质量门控
  feature_complete:
    name: "功能完整质量门控"
    description: "功能开发完整性要求"
    coverage_threshold: 90.0
    success_rate_threshold: 98.0
    performance_thresholds:
      max_response_time: 500
      min_throughput: 200
      max_cpu_usage: 60
      max_memory_usage: 256
    security_checks:
      - "no_vulnerabilities"
      - "secure_configurations"
      - "access_control_check"
    custom_checks:
      - "code_quality_check"
      - "documentation_check"
      - "api_documentation_check"
      - "user_acceptance_test"
    blocking: false
  
  # 发布候选质量门控
  release_candidate:
    name: "发布候选质量门控"
    description: "发布前最终检查"
    coverage_threshold: 95.0
    success_rate_threshold: 99.0
    performance_thresholds:
      max_response_time: 300
      min_throughput: 500
      max_cpu_usage: 50
      max_memory_usage: 128
    security_checks:
      - "no_vulnerabilities"
      - "secure_configurations"
      - "access_control_check"
      - "data_protection_check"
    custom_checks:
      - "code_quality_check"
      - "documentation_check"
      - "api_documentation_check"
      - "user_acceptance_test"
      - "performance_benchmark"
      - "scalability_test"
    blocking: true

# 全局配置
global_config:
  # 工作目录
  work_directory: "tests/framework_workspace"
  
  # 报告配置
  reporting:
    formats:
      - "html"
      - "json"
      - "junit"
    output_directory: "test_reports"
    include_performance: true
    include_coverage: true
    
  # 并行执行配置
  parallel_execution:
    max_workers: 4
    timeout_multiplier: 1.5
    
  # 重试配置
  retry_config:
    max_retries: 3
    retry_delay: 5
    exponential_backoff: true
    
  # 监控配置
  monitoring:
    enable_performance_monitoring: true
    enable_memory_profiling: true
    enable_real_time_monitoring: true
    monitoring_interval: 1
    
  # 通知配置
  notifications:
    enable_email: false
    enable_slack: false
    enable_webhook: false
    
  # 清理配置
  cleanup:
    auto_cleanup: true
    keep_artifacts_days: 7
    cleanup_on_success: false
    cleanup_on_failure: false