# -*- coding: utf-8 -*-
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""Utilities package.

This package contains utility modules for the Connect platform,
including data validation, file handling, caching, and other common utilities.
"""

# Import from database.utils for backward compatibility
try:
    from src.database.utils import *
except ImportError:
    # Fallback for relative imports
    try:
        from ..database.utils import *
    except ImportError:
        pass  # Skip if database.utils is not available

# Additional utils specific to the main application
from .cache_manager import CacheManager, CacheEntry, CacheStats
from .data_validator import DataValidator, ValidationResult, ValidationRule
from .file_handler import <PERSON><PERSON>and<PERSON>, FileInfo, FileError

__all__ = [
    # From database.utils
    "InputValidator",
    "sanitize_string",
    "snake_to_camel",
    "camel_to_snake",
    "ensure_directory_exists",
    "get_file_extension",
    "flatten_dict",
    "chunk_list",
    "deep_merge_dicts",
    "format_bytes",
    "is_valid_email",
    "truncate_string",
    "SQLInjectionGuard",
    "PerformanceTimer",
    "PerformanceMonitor",
    "time_execution",
    "retry",
    "timeout",
    "validate_args",
    "cache_result",
    "deprecated",
    "singleton",
    "rate_limit",
    "generate_batches",
    "bulk_insert_batched",
    "bulk_insert_with_copy",
    "BatchProcessor",
    # Application-specific utils
    "CacheManager",
    "CacheEntry",
    "CacheStats",
    "DataValidator",
    "ValidationResult",
    "ValidationRule",
    "FileHandler",
    "FileInfo",
    "FileError",
]