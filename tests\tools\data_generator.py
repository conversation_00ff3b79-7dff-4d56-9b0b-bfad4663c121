"""Test data generator for comprehensive testing."""

import random
import string
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

import pandas as pd
from faker import Faker
from shapely.geometry import Point, Polygon


class TestDataGenerator:
    """Generate test data for various testing scenarios."""
    
    def __init__(self, seed: Optional[int] = None):
        """Initialize the test data generator.
        
        Args:
            seed: Random seed for reproducible data generation
        """
        self.fake = Faker()
        if seed:
            Faker.seed(seed)
            random.seed(seed)
    
    def generate_ep_data(self, num_rows: int = 100) -> pd.DataFrame:
        """Generate EP (Engineering Parameters) test data.
        
        Args:
            num_rows: Number of rows to generate
            
        Returns:
            DataFrame with EP test data
        """
        data = {
            'cell_id': [f"CELL_{i:06d}" for i in range(num_rows)],
            'site_name': [self.fake.company() for _ in range(num_rows)],
            'latitude': [self.fake.latitude() for _ in range(num_rows)],
            'longitude': [self.fake.longitude() for _ in range(num_rows)],
            'azimuth': [random.randint(0, 359) for _ in range(num_rows)],
            'height': [random.uniform(10, 100) for _ in range(num_rows)],
            'power': [random.uniform(10, 50) for _ in range(num_rows)],
            'frequency': [random.choice([900, 1800, 2100, 2600]) for _ in range(num_rows)],
            'technology': [random.choice(['2G', '3G', '4G', '5G']) for _ in range(num_rows)],
            'vendor': [random.choice(['Ericsson', 'Nokia', 'Huawei']) for _ in range(num_rows)],
            'status': [random.choice(['Active', 'Inactive', 'Maintenance']) for _ in range(num_rows)],
            'created_date': [self.fake.date_between(start_date='-2y', end_date='today') for _ in range(num_rows)],
            'updated_date': [datetime.now() for _ in range(num_rows)]
        }
        return pd.DataFrame(data)
    
    def generate_cdr_data(self, num_rows: int = 1000) -> pd.DataFrame:
        """Generate CDR (Call Detail Record) test data.
        
        Args:
            num_rows: Number of rows to generate
            
        Returns:
            DataFrame with CDR test data
        """
        data = {
            'call_id': [str(uuid4()) for _ in range(num_rows)],
            'calling_number': [self.fake.phone_number() for _ in range(num_rows)],
            'called_number': [self.fake.phone_number() for _ in range(num_rows)],
            'call_start_time': [self.fake.date_time_between(start_date='-30d', end_date='now') for _ in range(num_rows)],
            'call_duration': [random.randint(1, 3600) for _ in range(num_rows)],
            'call_type': [random.choice(['Voice', 'SMS', 'Data']) for _ in range(num_rows)],
            'cell_id': [f"CELL_{random.randint(1, 10000):06d}" for _ in range(num_rows)],
            'vendor': [random.choice(['Telefonica', 'Vodafone', 'Telekom']) for _ in range(num_rows)],
            'data_volume_mb': [random.uniform(0, 1000) if random.random() > 0.3 else 0 for _ in range(num_rows)],
            'revenue': [random.uniform(0.01, 5.0) for _ in range(num_rows)],
            'location_lat': [self.fake.latitude() for _ in range(num_rows)],
            'location_lon': [self.fake.longitude() for _ in range(num_rows)]
        }
        return pd.DataFrame(data)
    
    def generate_kpi_data(self, num_rows: int = 500) -> pd.DataFrame:
        """Generate KPI (Key Performance Indicator) test data.
        
        Args:
            num_rows: Number of rows to generate
            
        Returns:
            DataFrame with KPI test data
        """
        data = {
            'kpi_id': [f"KPI_{i:06d}" for i in range(num_rows)],
            'cell_id': [f"CELL_{random.randint(1, 1000):06d}" for _ in range(num_rows)],
            'measurement_time': [self.fake.date_time_between(start_date='-7d', end_date='now') for _ in range(num_rows)],
            'call_success_rate': [random.uniform(85, 99.9) for _ in range(num_rows)],
            'call_drop_rate': [random.uniform(0.1, 5.0) for _ in range(num_rows)],
            'handover_success_rate': [random.uniform(90, 99.5) for _ in range(num_rows)],
            'throughput_dl_mbps': [random.uniform(10, 100) for _ in range(num_rows)],
            'throughput_ul_mbps': [random.uniform(5, 50) for _ in range(num_rows)],
            'latency_ms': [random.uniform(10, 100) for _ in range(num_rows)],
            'availability': [random.uniform(95, 100) for _ in range(num_rows)],
            'traffic_volume_gb': [random.uniform(100, 10000) for _ in range(num_rows)]
        }
        return pd.DataFrame(data)
    
    def generate_geospatial_data(self, num_polygons: int = 50) -> List[Dict[str, Any]]:
        """Generate geospatial polygon test data.
        
        Args:
            num_polygons: Number of polygons to generate
            
        Returns:
            List of polygon dictionaries
        """
        polygons = []
        for i in range(num_polygons):
            # Generate a random polygon around a center point
            center_lat = self.fake.latitude()
            center_lon = self.fake.longitude()
            
            # Create a simple square polygon
            offset = 0.01  # Approximately 1km
            coords = [
                (float(center_lon) - offset, float(center_lat) - offset),
                (float(center_lon) + offset, float(center_lat) - offset),
                (float(center_lon) + offset, float(center_lat) + offset),
                (float(center_lon) - offset, float(center_lat) + offset),
                (float(center_lon) - offset, float(center_lat) - offset)  # Close the polygon
            ]
            
            polygon_data = {
                'polygon_id': f"POLY_{i:06d}",
                'name': f"Area_{self.fake.city()}",
                'geometry': Polygon(coords),
                'area_type': random.choice(['Urban', 'Suburban', 'Rural']),
                'population': random.randint(1000, 100000),
                'coverage_type': random.choice(['Indoor', 'Outdoor', 'Mixed'])
            }
            polygons.append(polygon_data)
        
        return polygons
    
    def generate_invalid_data(self, data_type: str, num_rows: int = 10) -> pd.DataFrame:
        """Generate invalid test data for validation testing.
        
        Args:
            data_type: Type of data to generate ('ep', 'cdr', 'kpi')
            num_rows: Number of rows to generate
            
        Returns:
            DataFrame with invalid test data
        """
        if data_type == 'ep':
            return self._generate_invalid_ep_data(num_rows)
        elif data_type == 'cdr':
            return self._generate_invalid_cdr_data(num_rows)
        elif data_type == 'kpi':
            return self._generate_invalid_kpi_data(num_rows)
        else:
            raise ValueError(f"Unknown data type: {data_type}")
    
    def _generate_invalid_ep_data(self, num_rows: int) -> pd.DataFrame:
        """Generate invalid EP data for testing validation."""
        data = {
            'cell_id': [None if i % 3 == 0 else f"INVALID_CELL_{i}" for i in range(num_rows)],
            'latitude': [random.uniform(-200, 200) for _ in range(num_rows)],  # Invalid range
            'longitude': [random.uniform(-200, 200) for _ in range(num_rows)],  # Invalid range
            'azimuth': [random.randint(-100, 500) for _ in range(num_rows)],  # Invalid range
            'height': [random.uniform(-50, 1000) for _ in range(num_rows)],  # Negative heights
            'power': [random.uniform(-10, 200) for _ in range(num_rows)],  # Invalid range
            'frequency': [random.choice([500, 3000, 'invalid']) for _ in range(num_rows)],  # Invalid frequencies
            'technology': [random.choice(['1G', '6G', 'Unknown', None]) for _ in range(num_rows)],
            'vendor': [''] * num_rows,  # Empty vendor names
            'status': [random.choice(['Invalid', None, 123]) for _ in range(num_rows)]
        }
        return pd.DataFrame(data)
    
    def _generate_invalid_cdr_data(self, num_rows: int) -> pd.DataFrame:
        """Generate invalid CDR data for testing validation."""
        data = {
            'call_id': [None if i % 2 == 0 else 'invalid_uuid' for i in range(num_rows)],
            'calling_number': ['invalid_number'] * num_rows,
            'called_number': [None] * num_rows,
            'call_start_time': ['invalid_date'] * num_rows,
            'call_duration': [random.randint(-100, -1) for _ in range(num_rows)],  # Negative duration
            'call_type': ['InvalidType'] * num_rows,
            'data_volume_mb': [random.uniform(-100, -1) for _ in range(num_rows)],  # Negative volume
            'revenue': ['not_a_number'] * num_rows
        }
        return pd.DataFrame(data)
    
    def _generate_invalid_kpi_data(self, num_rows: int) -> pd.DataFrame:
        """Generate invalid KPI data for testing validation."""
        data = {
            'kpi_id': [None] * num_rows,
            'call_success_rate': [random.uniform(100, 200) for _ in range(num_rows)],  # > 100%
            'call_drop_rate': [random.uniform(-10, -1) for _ in range(num_rows)],  # Negative rate
            'throughput_dl_mbps': ['invalid'] * num_rows,
            'latency_ms': [random.uniform(-50, -1) for _ in range(num_rows)],  # Negative latency
            'availability': [random.uniform(101, 200) for _ in range(num_rows)]  # > 100%
        }
        return pd.DataFrame(data)
    
    def generate_large_dataset(self, data_type: str, num_rows: int = 100000) -> pd.DataFrame:
        """Generate large datasets for performance testing.
        
        Args:
            data_type: Type of data to generate
            num_rows: Number of rows to generate
            
        Returns:
            Large DataFrame for performance testing
        """
        if data_type == 'ep':
            return self.generate_ep_data(num_rows)
        elif data_type == 'cdr':
            return self.generate_cdr_data(num_rows)
        elif data_type == 'kpi':
            return self.generate_kpi_data(num_rows)
        else:
            raise ValueError(f"Unknown data type: {data_type}")
    
    def generate_schema_test_data(self) -> Dict[str, pd.DataFrame]:
        """Generate test data for all schemas.
        
        Returns:
            Dictionary with schema names as keys and DataFrames as values
        """
        return {
            'ep': self.generate_ep_data(100),
            'cdr': self.generate_cdr_data(200),
            'kpi': self.generate_kpi_data(150),
            'score': self._generate_score_data(75),
            'cfg': self._generate_config_data(50)
        }
    
    def _generate_score_data(self, num_rows: int) -> pd.DataFrame:
        """Generate score data for testing."""
        data = {
            'score_id': [f"SCORE_{i:06d}" for i in range(num_rows)],
            'cell_id': [f"CELL_{random.randint(1, 1000):06d}" for _ in range(num_rows)],
            'score_type': [random.choice(['Quality', 'Coverage', 'Capacity']) for _ in range(num_rows)],
            'score_value': [random.uniform(0, 100) for _ in range(num_rows)],
            'measurement_date': [self.fake.date_between(start_date='-30d', end_date='today') for _ in range(num_rows)],
            'weight': [random.uniform(0.1, 1.0) for _ in range(num_rows)]
        }
        return pd.DataFrame(data)
    
    def _generate_config_data(self, num_rows: int) -> pd.DataFrame:
        """Generate configuration data for testing."""
        data = {
            'config_id': [f"CFG_{i:06d}" for i in range(num_rows)],
            'parameter_name': [f"param_{self.fake.word()}" for _ in range(num_rows)],
            'parameter_value': [str(random.randint(1, 1000)) for _ in range(num_rows)],
            'parameter_type': [random.choice(['Integer', 'String', 'Float', 'Boolean']) for _ in range(num_rows)],
            'description': [self.fake.sentence() for _ in range(num_rows)],
            'is_active': [random.choice([True, False]) for _ in range(num_rows)]
        }
        return pd.DataFrame(data)