"""统一验证框架集成测试

测试整个验证系统的端到端功能和集成场景。
"""

import pytest
import pandas as pd
import tempfile
import os
import json
from pathlib import Path
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from ..factory import ValidationFactory, get_validation_factory
from ..core import ValidationFramework, ValidationSeverity, ValidationType
from ..validators import (
    DataStructureValidator,
    DataValueValidator,
    TelecomDataValidator,
    FileValidator
)
from ..rules import CDRValidationRules, KPIValidationRules, CFGValidationRules
from ..exceptions import ValidationError, ValidationConfigError
from .conftest import performance_test, memory_test


class TestEndToEndValidation:
    """端到端验证测试类"""
    
    def test_complete_cdr_validation_workflow(self, temp_csv_file, sample_cdr_data):
        """测试完整的CDR验证工作流"""
        factory = get_validation_factory()
        
        # 1. 文件存在性验证
        file_result = factory.validate_file(temp_csv_file, 'cdr')
        assert file_result.is_valid, f"File validation failed: {file_result.issues}"
        
        # 2. 数据结构验证
        data_result = factory.validate_cdr(sample_cdr_data)
        assert data_result.is_valid, f"Data validation failed: {data_result.issues}"
        
        # 3. 验证报告生成
        report = {
            'file_validation': file_result.to_dict(),
            'data_validation': data_result.to_dict(),
            'timestamp': datetime.now().isoformat()
        }
        
        assert 'file_validation' in report
        assert 'data_validation' in report
        assert report['file_validation']['is_valid']
        assert report['data_validation']['is_valid']
    
    def test_complete_kpi_validation_workflow(self, sample_kpi_data):
        """测试完整的KPI验证工作流"""
        factory = get_validation_factory()
        
        # 创建临时KPI文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            sample_kpi_data.to_csv(f.name, index=False)
            temp_file = f.name
        
        try:
            # 1. 文件验证
            file_result = factory.validate_file(temp_file, 'kpi')
            assert file_result.is_valid
            
            # 2. 数据验证
            data_result = factory.validate_kpi(sample_kpi_data)
            assert data_result.is_valid
            
            # 3. 组合验证结果
            combined_result = {
                'overall_valid': file_result.is_valid and data_result.is_valid,
                'file_issues': len(file_result.issues),
                'data_issues': len(data_result.issues),
                'total_issues': len(file_result.issues) + len(data_result.issues)
            }
            
            assert combined_result['overall_valid']
            assert combined_result['total_issues'] == 0
        
        finally:
            os.unlink(temp_file)
    
    def test_multi_file_validation_batch(self, sample_cdr_data, sample_kpi_data):
        """测试多文件批量验证"""
        factory = get_validation_factory()
        
        # 创建多个临时文件
        temp_files = []
        try:
            # CDR文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                sample_cdr_data.to_csv(f.name, index=False)
                temp_files.append((f.name, 'cdr'))
            
            # KPI文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                sample_kpi_data.to_csv(f.name, index=False)
                temp_files.append((f.name, 'kpi'))
            
            # 批量验证
            results = []
            for file_path, data_type in temp_files:
                result = factory.validate_file(file_path, data_type)
                results.append({
                    'file': file_path,
                    'type': data_type,
                    'valid': result.is_valid,
                    'issues': len(result.issues)
                })
            
            # 验证结果
            assert len(results) == 2
            assert all(r['valid'] for r in results)
            assert sum(r['issues'] for r in results) == 0
        
        finally:
            for file_path, _ in temp_files:
                if os.path.exists(file_path):
                    os.unlink(file_path)
    
    def test_validation_with_context_propagation(self, sample_cdr_data):
        """测试带上下文传播的验证"""
        from ..core import ValidationContext
        
        factory = get_validation_factory()
        
        # 创建验证上下文
        context = ValidationContext(
            source_file="test_cdr.csv",
            data_type="cdr",
            user_id="test_user",
            session_id="test_session",
            metadata={
                "import_batch": "batch_001",
                "region": "north"
            }
        )
        
        # 使用上下文进行验证
        framework = factory.get_framework('cdr')
        result = framework.validate(sample_cdr_data, context)
        
        assert result.is_valid
        assert result.context is not None
        assert result.context.source_file == "test_cdr.csv"
        assert result.context.metadata["import_batch"] == "batch_001"
    
    def test_validation_pipeline_with_transformations(self, sample_cdr_data):
        """测试带数据转换的验证管道"""
        factory = get_validation_factory()
        
        # 模拟数据转换管道
        def transform_data(data):
            """简单的数据转换"""
            transformed = data.copy()
            # 添加计算列
            if 'CALL_START_TIME' in transformed.columns and 'CALL_END_TIME' in transformed.columns:
                transformed['CALL_DURATION_CALCULATED'] = (
                    pd.to_datetime(transformed['CALL_END_TIME']) - 
                    pd.to_datetime(transformed['CALL_START_TIME'])
                ).dt.total_seconds()
            return transformed
        
        # 1. 原始数据验证
        original_result = factory.validate_cdr(sample_cdr_data)
        assert original_result.is_valid
        
        # 2. 数据转换
        transformed_data = transform_data(sample_cdr_data)
        
        # 3. 转换后数据验证
        transformed_result = factory.validate_cdr(transformed_data)
        assert transformed_result.is_valid
        
        # 4. 验证新增列
        assert 'CALL_DURATION_CALCULATED' in transformed_data.columns
        assert not transformed_data['CALL_DURATION_CALCULATED'].isna().any()


class TestCrossValidatorIntegration:
    """跨验证器集成测试类"""
    
    def test_structure_and_value_validator_integration(self, sample_cdr_data):
        """测试结构和值验证器集成"""
        from ..core import ValidationFramework
        
        # 创建包含结构和值验证的框架
        structure_validator = DataStructureValidator()
        value_validator = DataValueValidator()
        
        framework = ValidationFramework([
            structure_validator,
            value_validator
        ])
        
        # 添加验证规则
        structure_rules = CDRValidationRules.get_structure_rules()
        value_rules = CDRValidationRules.get_value_rules()
        
        for rule in structure_rules + value_rules:
            framework.add_rule(rule)
        
        # 执行验证
        result = framework.validate(sample_cdr_data)
        assert result.is_valid
        
        # 验证两种类型的规则都被执行
        structure_issues = [i for i in result.issues if i.validation_type == ValidationType.STRUCTURE]
        value_issues = [i for i in result.issues if i.validation_type == ValidationType.VALUE]
        
        # 对于有效数据，应该没有问题
        assert len(structure_issues) == 0
        assert len(value_issues) == 0
    
    def test_telecom_and_file_validator_integration(self, temp_csv_file, sample_cdr_data):
        """测试电信和文件验证器集成"""
        telecom_validator = TelecomDataValidator()
        file_validator = FileValidator()
        
        framework = ValidationFramework([
            file_validator,
            telecom_validator
        ])
        
        # 添加验证规则
        telecom_rules = CDRValidationRules.get_telecom_rules()
        file_rules = CDRValidationRules.get_file_rules()
        
        for rule in telecom_rules + file_rules:
            framework.add_rule(rule)
        
        # 文件验证
        file_result = framework.validate(temp_csv_file)
        assert file_result.is_valid
        
        # 数据验证
        data_result = framework.validate(sample_cdr_data)
        assert data_result.is_valid
    
    def test_all_validators_integration(self, sample_cdr_data):
        """测试所有验证器集成"""
        factory = get_validation_factory()
        
        # 获取完整的CDR验证框架（包含所有验证器）
        framework = factory.get_framework('cdr')
        
        # 验证框架包含所有必要的验证器
        validator_types = [type(v).__name__ for v in framework.validators]
        expected_validators = [
            'DataStructureValidator',
            'DataValueValidator', 
            'TelecomDataValidator',
            'FileValidator'
        ]
        
        for expected in expected_validators:
            assert any(expected in vt for vt in validator_types), f"Missing validator: {expected}"
        
        # 执行完整验证
        result = framework.validate(sample_cdr_data)
        assert result.is_valid


class TestValidationPerformanceIntegration:
    """验证性能集成测试类"""
    
    @performance_test(max_time=5.0)
    def test_large_dataset_validation_performance(self, large_dataset):
        """测试大数据集验证性能"""
        factory = get_validation_factory()
        
        result = factory.validate_cdr(large_dataset)
        # 应该在时间限制内完成
        assert result is not None
    
    @performance_test(max_time=3.0)
    def test_parallel_validation_performance(self, sample_cdr_data, sample_kpi_data):
        """测试并行验证性能"""
        factory = ValidationFactory({
            'parallel_validation': True,
            'max_workers': 4
        })
        
        # 准备多个数据集
        datasets = [
            (sample_cdr_data, 'cdr'),
            (sample_kpi_data, 'kpi'),
            (sample_cdr_data, 'cdr'),
            (sample_kpi_data, 'kpi')
        ]
        
        results = factory.validate_parallel(datasets)
        assert len(results) == 4
        assert all(r.is_valid for r in results)
    
    @memory_test(max_memory_mb=200)
    def test_memory_efficient_validation(self, large_dataset):
        """测试内存高效验证"""
        factory = get_validation_factory()
        
        # 多次验证大数据集
        for _ in range(3):
            result = factory.validate_cdr(large_dataset)
            assert result is not None
        
        # 内存使用应该在限制内
    
    def test_validation_caching_performance(self, sample_cdr_data):
        """测试验证缓存性能"""
        factory = ValidationFactory({'cache_enabled': True})
        
        # 第一次验证（创建缓存）
        start_time = datetime.now()
        result1 = factory.validate_cdr(sample_cdr_data)
        first_time = (datetime.now() - start_time).total_seconds()
        
        # 第二次验证（使用缓存）
        start_time = datetime.now()
        result2 = factory.validate_cdr(sample_cdr_data)
        second_time = (datetime.now() - start_time).total_seconds()
        
        assert result1.is_valid
        assert result2.is_valid
        # 第二次应该更快（由于缓存）
        assert second_time <= first_time * 1.5  # 允许一些误差


class TestErrorHandlingIntegration:
    """错误处理集成测试类"""
    
    def test_cascading_validation_errors(self, missing_columns_data):
        """测试级联验证错误"""
        factory = get_validation_factory()
        
        result = factory.validate_cdr(missing_columns_data)
        assert not result.is_valid
        
        # 应该有多种类型的错误
        error_types = {issue.validation_type for issue in result.issues}
        assert ValidationType.STRUCTURE in error_types
        
        # 错误应该按严重程度排序
        severities = [issue.severity for issue in result.issues]
        critical_count = sum(1 for s in severities if s == ValidationSeverity.CRITICAL)
        assert critical_count > 0
    
    def test_partial_validation_recovery(self, invalid_cdr_data):
        """测试部分验证恢复"""
        factory = get_validation_factory()
        
        result = factory.validate_cdr(invalid_cdr_data)
        assert not result.is_valid
        
        # 即使有错误，也应该完成所有验证
        assert len(result.issues) > 0
        
        # 检查是否有不同严重程度的问题
        severities = {issue.severity for issue in result.issues}
        assert len(severities) > 0
    
    def test_validation_timeout_handling(self, large_dataset):
        """测试验证超时处理"""
        factory = ValidationFactory({
            'validation_timeout': 1.0  # 很短的超时
        })
        
        # 对于大数据集，可能会超时
        result = factory.validate_cdr(large_dataset)
        
        # 即使超时，也应该返回结果
        assert result is not None
    
    def test_file_access_error_handling(self):
        """测试文件访问错误处理"""
        factory = get_validation_factory()
        
        # 测试不存在的文件
        result = factory.validate_file('/non/existent/file.csv', 'cdr')
        assert not result.is_valid
        assert any("does not exist" in issue.message for issue in result.issues)
        
        # 测试无效路径
        result = factory.validate_file('', 'cdr')
        assert not result.is_valid
    
    def test_data_type_mismatch_handling(self, sample_cdr_data):
        """测试数据类型不匹配处理"""
        factory = get_validation_factory()
        
        # 用CDR数据验证KPI规则
        result = factory.validate_kpi(sample_cdr_data)
        assert not result.is_valid
        
        # 应该有结构相关的错误
        structure_errors = [i for i in result.issues if i.validation_type == ValidationType.STRUCTURE]
        assert len(structure_errors) > 0


class TestValidationReporting:
    """验证报告测试类"""
    
    def test_comprehensive_validation_report(self, sample_cdr_data, invalid_cdr_data):
        """测试综合验证报告"""
        factory = get_validation_factory()
        
        # 验证有效和无效数据
        valid_result = factory.validate_cdr(sample_cdr_data)
        invalid_result = factory.validate_cdr(invalid_cdr_data)
        
        # 生成综合报告
        report = {
            'validation_summary': {
                'total_validations': 2,
                'successful_validations': 1 if valid_result.is_valid else 0,
                'failed_validations': 1 if not invalid_result.is_valid else 0
            },
            'results': [
                {
                    'dataset': 'valid_cdr',
                    'result': valid_result.to_dict()
                },
                {
                    'dataset': 'invalid_cdr', 
                    'result': invalid_result.to_dict()
                }
            ],
            'timestamp': datetime.now().isoformat()
        }
        
        assert report['validation_summary']['total_validations'] == 2
        assert report['validation_summary']['successful_validations'] >= 0
        assert report['validation_summary']['failed_validations'] >= 0
        assert len(report['results']) == 2
    
    def test_validation_metrics_collection(self, sample_cdr_data):
        """测试验证指标收集"""
        factory = get_validation_factory()
        
        # 执行多次验证
        results = []
        for _ in range(5):
            result = factory.validate_cdr(sample_cdr_data)
            results.append(result)
        
        # 收集指标
        metrics = {
            'total_validations': len(results),
            'success_rate': sum(1 for r in results if r.is_valid) / len(results),
            'average_validation_time': sum(r.validation_time for r in results) / len(results),
            'total_issues': sum(len(r.issues) for r in results),
            'issue_breakdown': {}
        }
        
        # 按类型分组问题
        for result in results:
            for issue in result.issues:
                issue_type = issue.validation_type.value
                if issue_type not in metrics['issue_breakdown']:
                    metrics['issue_breakdown'][issue_type] = 0
                metrics['issue_breakdown'][issue_type] += 1
        
        assert metrics['total_validations'] == 5
        assert 0 <= metrics['success_rate'] <= 1
        assert metrics['average_validation_time'] >= 0
    
    def test_validation_audit_trail(self, sample_cdr_data):
        """测试验证审计跟踪"""
        from ..core import ValidationContext
        
        factory = get_validation_factory()
        
        # 创建带审计信息的上下文
        context = ValidationContext(
            source_file="audit_test.csv",
            data_type="cdr",
            user_id="audit_user",
            session_id="audit_session",
            metadata={
                "audit_id": "audit_001",
                "timestamp": datetime.now().isoformat()
            }
        )
        
        framework = factory.get_framework('cdr')
        result = framework.validate(sample_cdr_data, context)
        
        # 验证审计信息
        audit_trail = {
            'validation_id': result.context.session_id,
            'user_id': result.context.user_id,
            'source_file': result.context.source_file,
            'validation_time': result.validation_time,
            'is_valid': result.is_valid,
            'issue_count': len(result.issues),
            'audit_metadata': result.context.metadata
        }
        
        assert audit_trail['validation_id'] == "audit_session"
        assert audit_trail['user_id'] == "audit_user"
        assert audit_trail['source_file'] == "audit_test.csv"
        assert 'audit_id' in audit_trail['audit_metadata']


class TestValidationConfigurationIntegration:
    """验证配置集成测试类"""
    
    def test_custom_validation_configuration(self, sample_cdr_data):
        """测试自定义验证配置"""
        custom_config = {
            'cache_enabled': True,
            'max_cache_size': 10,
            'parallel_validation': True,
            'max_workers': 2,
            'validation_timeout': 30.0,
            'strict_mode': True
        }
        
        factory = ValidationFactory(custom_config)
        
        result = factory.validate_cdr(sample_cdr_data)
        assert result.is_valid
        
        # 验证配置生效
        cache_info = factory.get_cache_info()
        assert cache_info['max_size'] == 10
    
    def test_validation_rule_customization(self, sample_cdr_data):
        """测试验证规则自定义"""
        from ..core import ValidationRule, ValidationFramework
        from ..validators import DataValueValidator
        
        # 创建自定义规则
        class CustomCDRRule(ValidationRule):
            def __init__(self):
                super().__init__(
                    name="custom_cdr_rule",
                    description="Custom CDR validation rule",
                    validation_type=ValidationType.BUSINESS,
                    severity=ValidationSeverity.WARNING
                )
            
            def validate(self, data, context=None):
                issues = []
                if isinstance(data, pd.DataFrame):
                    # 自定义业务规则：通话时长不能超过24小时
                    if 'CALL_DURATION' in data.columns:
                        long_calls = data[data['CALL_DURATION'] > 86400]  # 24小时
                        if not long_calls.empty:
                            issues.append(self.create_issue(
                                f"Found {len(long_calls)} calls longer than 24 hours",
                                context
                            ))
                return issues
        
        # 创建包含自定义规则的框架
        validator = DataValueValidator()
        framework = ValidationFramework([validator])
        framework.add_rule(CustomCDRRule())
        
        result = framework.validate(sample_cdr_data)
        # 对于正常数据，自定义规则应该通过
        assert result.is_valid
    
    def test_environment_specific_configuration(self, sample_cdr_data):
        """测试环境特定配置"""
        # 开发环境配置
        dev_config = {
            'strict_mode': False,
            'validation_timeout': 60.0,
            'cache_enabled': True
        }
        
        # 生产环境配置
        prod_config = {
            'strict_mode': True,
            'validation_timeout': 30.0,
            'cache_enabled': True,
            'max_cache_size': 100
        }
        
        # 测试不同环境
        dev_factory = ValidationFactory(dev_config)
        prod_factory = ValidationFactory(prod_config)
        
        dev_result = dev_factory.validate_cdr(sample_cdr_data)
        prod_result = prod_factory.validate_cdr(sample_cdr_data)
        
        assert dev_result.is_valid
        assert prod_result.is_valid
        
        # 验证配置差异
        assert dev_factory._config['strict_mode'] != prod_factory._config['strict_mode']
        assert dev_factory._config['validation_timeout'] != prod_factory._config['validation_timeout']


if __name__ == "__main__":
    pytest.main([__file__])