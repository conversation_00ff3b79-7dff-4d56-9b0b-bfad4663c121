__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Geospatial data processing module.

This module provides functionality for processing geospatial data,
including polygon handling, coordinate transformations, and vendor tagging.

P2 Priority Features:
- Geospatial data processing
- MapInfo .TAB polygon processing
- Automatic CDR data vendor tag setting
- Coordinate validation and transformation
"""

from .polygon_handler import PolygonHandler
from .processor import GeospatialProcessor
from .vendor_tagger import VendorTagger

__all__ = ["GeospatialProcessor", "PolygonHandler", "VendorTagger"]
