# -*- coding: utf-8 -*-
"""
Batch Processor for Unified Data Processing

This module provides memory-optimized batch processing capabilities
with support for various batching strategies and processing modes.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import asyncio
import logging
import time
import gc
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable, Iterator, AsyncIterator, Tuple
from enum import Enum
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

import pandas as pd
import numpy as np
from .types import (
    ProcessingEngine, ProcessingMode, ProcessingStatus, QualityLevel,
    ProcessingMetrics, ProcessingResult, ProcessingConfig, ChunkInfo
)
from .adapters import create_adapter, BaseAdapter
from ..utils import MemoryMonitor


class BatchStrategy(Enum):
    """Batch processing strategies."""
    FIXED_SIZE = "fixed_size"  # Fixed number of records per batch
    MEMORY_BASED = "memory_based"  # Based on memory consumption
    TIME_BASED = "time_based"  # Based on processing time
    ADAPTIVE = "adaptive"  # Adaptive based on performance
    CUSTOM = "custom"  # Custom batching logic


class BatchMode(Enum):
    """Batch processing execution modes."""
    SEQUENTIAL = "sequential"  # Process batches one by one
    PARALLEL = "parallel"  # Process batches in parallel threads
    ASYNC = "async"  # Process batches asynchronously
    PIPELINE = "pipeline"  # Pipeline processing with overlap


@dataclass
class BatchConfig:
    """Configuration for batch processing."""
    strategy: BatchStrategy = BatchStrategy.ADAPTIVE
    mode: BatchMode = BatchMode.SEQUENTIAL
    
    # Size-based configuration
    batch_size: int = 10000
    min_batch_size: int = 1000
    max_batch_size: int = 100000
    
    # Memory-based configuration
    memory_limit_mb: int = 512
    memory_threshold: float = 0.8  # Trigger batch when 80% of limit reached
    
    # Time-based configuration
    time_limit_seconds: float = 30.0
    
    # Parallel processing configuration
    max_workers: int = 4
    worker_type: str = "thread"  # "thread" or "process"
    
    # Adaptive configuration
    performance_target_rps: float = 1000.0  # Records per second target
    adaptation_factor: float = 0.1  # How much to adjust batch size
    
    # Error handling
    max_retries: int = 3
    retry_delay: float = 1.0
    continue_on_error: bool = True
    
    # Progress tracking
    progress_callback: Optional[Callable[[int, int], None]] = None
    log_progress: bool = True
    progress_interval: int = 10  # Log every N batches


@dataclass
class BatchInfo:
    """Information about a data batch."""
    batch_id: int
    start_index: int
    end_index: int
    size: int
    estimated_memory_mb: float = 0.0
    processing_time: float = 0.0
    status: ProcessingStatus = ProcessingStatus.PENDING
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BatchResult:
    """Result of processing a single batch."""
    batch_info: BatchInfo
    data: Optional[Any] = None
    records_processed: int = 0
    processing_time: float = 0.0
    memory_used_mb: float = 0.0
    status: ProcessingStatus = ProcessingStatus.COMPLETED
    error_message: Optional[str] = None
    metrics: Optional[ProcessingMetrics] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BatchProcessingResult:
    """Result of complete batch processing operation."""
    total_batches: int
    successful_batches: int
    failed_batches: int
    total_records: int
    processed_records: int
    total_processing_time: float
    average_batch_time: float
    peak_memory_mb: float
    status: ProcessingStatus
    batch_results: List[BatchResult] = field(default_factory=list)
    error_summary: Dict[str, int] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


class BatchProcessor:
    """Advanced batch processing system with multiple strategies and modes.
    
    Features:
    - Multiple batching strategies (fixed, memory-based, adaptive)
    - Various execution modes (sequential, parallel, async, pipeline)
    - Memory monitoring and optimization
    - Performance tracking and adaptation
    - Error handling and retry logic
    - Progress tracking and callbacks
    - Telecommunications data optimization
    """
    
    def __init__(self, config: Optional[BatchConfig] = None):
        """Initialize batch processor.
        
        Args:
            config: Batch processing configuration
        """
        self.config = config or BatchConfig()
        self.logger = logging.getLogger(__name__)
        self.memory_monitor = MemoryMonitor()
        
        # Processing state
        self._current_adapter: Optional[BaseAdapter] = None
        self._performance_history: List[Dict[str, float]] = []
        self._adaptive_batch_size = self.config.batch_size
        
        # Execution resources
        self._thread_executor: Optional[ThreadPoolExecutor] = None
        self._process_executor: Optional[ProcessPoolExecutor] = None
    
    async def process_data(
        self,
        data: Union[pd.DataFrame, Any, Iterator, AsyncIterator],
        processor_func: Callable,
        engine: Optional[ProcessingEngine] = None,
        config: Optional[BatchConfig] = None
    ) -> BatchProcessingResult:
        """Process data in batches using specified processor function.
        
        Args:
            data: Data to process (DataFrame, iterator, or async iterator)
            processor_func: Function to apply to each batch
            engine: Processing engine to use
            config: Batch configuration (overrides instance config)
            
        Returns:
            Batch processing result
        """
        start_time = time.time()
        batch_config = config or self.config
        
        try:
            # Create adapter
            adapter = create_adapter(engine=engine)
            self._current_adapter = adapter
            
            # Initialize execution resources
            await self._initialize_executors(batch_config)
            
            # Convert data to iterable batches
            batch_iterator = await self._create_batch_iterator(data, batch_config)
            
            # Process batches based on mode
            if batch_config.mode == BatchMode.SEQUENTIAL:
                result = await self._process_sequential(batch_iterator, processor_func, batch_config)
            elif batch_config.mode == BatchMode.PARALLEL:
                result = await self._process_parallel(batch_iterator, processor_func, batch_config)
            elif batch_config.mode == BatchMode.ASYNC:
                result = await self._process_async(batch_iterator, processor_func, batch_config)
            elif batch_config.mode == BatchMode.PIPELINE:
                result = await self._process_pipeline(batch_iterator, processor_func, batch_config)
            else:
                raise ValueError(f"Unsupported batch mode: {batch_config.mode}")
            
            # Calculate final metrics
            result.total_processing_time = time.time() - start_time
            result.average_batch_time = (
                result.total_processing_time / result.total_batches if result.total_batches > 0 else 0
            )
            
            # Calculate performance metrics
            if result.total_processing_time > 0:
                result.performance_metrics = {
                    'records_per_second': result.processed_records / result.total_processing_time,
                    'batches_per_second': result.successful_batches / result.total_processing_time,
                    'memory_efficiency': result.processed_records / max(result.peak_memory_mb, 1),
                    'success_rate': result.successful_batches / max(result.total_batches, 1),
                    'average_batch_size': result.processed_records / max(result.successful_batches, 1)
                }
            
            self.logger.info(
                f"Batch processing completed: {result.successful_batches}/{result.total_batches} batches, "
                f"{result.processed_records:,} records, {result.total_processing_time:.2f}s"
            )
            
            return result
            
        except Exception as e:
            result = BatchProcessingResult(
                total_batches=0,
                successful_batches=0,
                failed_batches=0,
                total_records=0,
                processed_records=0,
                total_processing_time=time.time() - start_time,
                average_batch_time=0,
                peak_memory_mb=0,
                status=ProcessingStatus.FAILED,
                metadata={'error': str(e)}
            )
            self.logger.error(f"Batch processing failed: {e}")
            return result
        
        finally:
            await self._cleanup_executors()
    
    async def process_file(
        self,
        file_path: Union[str, Path],
        processor_func: Callable,
        engine: Optional[ProcessingEngine] = None,
        config: Optional[BatchConfig] = None,
        **read_kwargs
    ) -> BatchProcessingResult:
        """Process file in batches.
        
        Args:
            file_path: Path to file to process
            processor_func: Function to apply to each batch
            engine: Processing engine to use
            config: Batch configuration
            **read_kwargs: Additional arguments for file reading
            
        Returns:
            Batch processing result
        """
        batch_config = config or self.config
        
        # Create file iterator based on batch strategy
        if batch_config.strategy == BatchStrategy.MEMORY_BASED:
            # Read file in chunks based on memory limit
            chunk_size = await self._estimate_chunk_size_for_memory(
                file_path, batch_config.memory_limit_mb
            )
        else:
            chunk_size = batch_config.batch_size
        
        # Create chunked file reader
        async def file_batch_iterator():
            adapter = create_adapter(engine=engine)
            
            if str(file_path).endswith('.csv'):
                for chunk in adapter.read_csv_chunked(file_path, chunk_size=chunk_size, **read_kwargs):
                    yield chunk
            elif str(file_path).endswith(('.xlsx', '.xls')):
                # For Excel files, read in chunks if possible
                data = adapter.read_excel(file_path, **read_kwargs)
                for i in range(0, len(data), chunk_size):
                    yield data.iloc[i:i + chunk_size]
            else:
                raise ValueError(f"Unsupported file format: {file_path}")
        
        return await self.process_data(
            file_batch_iterator(),
            processor_func,
            engine=engine,
            config=batch_config
        )
    
    async def estimate_optimal_batch_size(
        self,
        sample_data: Union[pd.DataFrame, Any],
        processor_func: Callable,
        target_memory_mb: float = 256,
        target_time_seconds: float = 10.0
    ) -> int:
        """Estimate optimal batch size based on sample data.
        
        Args:
            sample_data: Sample data for estimation
            processor_func: Processor function to benchmark
            target_memory_mb: Target memory usage per batch
            target_time_seconds: Target processing time per batch
            
        Returns:
            Estimated optimal batch size
        """
        if isinstance(sample_data, pd.DataFrame):
            sample_size = len(sample_data)
        else:
            sample_size = 1000  # Default assumption
        
        # Test with small batch to estimate memory and time per record
        test_batch_size = min(1000, sample_size)
        
        if isinstance(sample_data, pd.DataFrame):
            test_data = sample_data.head(test_batch_size)
        else:
            test_data = sample_data
        
        # Measure memory usage
        memory_before = self.memory_monitor.get_memory_usage()
        
        # Measure processing time
        start_time = time.time()
        try:
            await processor_func(test_data)
            processing_time = time.time() - start_time
        except Exception as e:
            self.logger.warning(f"Failed to benchmark processor function: {e}")
            return self.config.batch_size
        
        memory_after = self.memory_monitor.get_memory_usage()
        memory_per_record = (memory_after - memory_before) / test_batch_size
        time_per_record = processing_time / test_batch_size
        
        # Calculate optimal batch size based on constraints
        memory_optimal = int(target_memory_mb / max(memory_per_record, 0.001))
        time_optimal = int(target_time_seconds / max(time_per_record, 0.0001))
        
        # Take the more conservative estimate
        optimal_size = min(memory_optimal, time_optimal)
        
        # Apply bounds
        optimal_size = max(self.config.min_batch_size, optimal_size)
        optimal_size = min(self.config.max_batch_size, optimal_size)
        
        self.logger.info(
            f"Estimated optimal batch size: {optimal_size} "
            f"(memory: {memory_optimal}, time: {time_optimal})"
        )
        
        return optimal_size
    
    # Batch creation and iteration methods
    async def _create_batch_iterator(self, data: Any, config: BatchConfig) -> AsyncIterator[Tuple[BatchInfo, Any]]:
        """Create batch iterator based on data type and strategy."""
        if isinstance(data, pd.DataFrame):
            async for batch_info, batch_data in self._create_dataframe_batches(data, config):
                yield batch_info, batch_data
        
        elif hasattr(data, '__iter__') or hasattr(data, '__aiter__'):
            async for batch_info, batch_data in self._create_iterator_batches(data, config):
                yield batch_info, batch_data
        
        else:
            # Single data item
            batch_info = BatchInfo(
                batch_id=0,
                start_index=0,
                end_index=1,
                size=1
            )
            yield batch_info, data
    
    async def _create_dataframe_batches(self, df: pd.DataFrame, config: BatchConfig) -> AsyncIterator[Tuple[BatchInfo, pd.DataFrame]]:
        """Create batches from DataFrame."""
        total_rows = len(df)
        batch_id = 0
        start_idx = 0
        
        while start_idx < total_rows:
            # Determine batch size based on strategy
            if config.strategy == BatchStrategy.FIXED_SIZE:
                batch_size = config.batch_size
            elif config.strategy == BatchStrategy.ADAPTIVE:
                batch_size = self._adaptive_batch_size
            elif config.strategy == BatchStrategy.MEMORY_BASED:
                batch_size = await self._calculate_memory_based_batch_size(df, start_idx, config)
            else:
                batch_size = config.batch_size
            
            end_idx = min(start_idx + batch_size, total_rows)
            batch_data = df.iloc[start_idx:end_idx]
            
            # Estimate memory usage
            estimated_memory = batch_data.memory_usage(deep=True).sum() / (1024 * 1024)  # MB
            
            batch_info = BatchInfo(
                batch_id=batch_id,
                start_index=start_idx,
                end_index=end_idx,
                size=len(batch_data),
                estimated_memory_mb=estimated_memory
            )
            
            yield batch_info, batch_data
            
            start_idx = end_idx
            batch_id += 1
    
    async def _create_iterator_batches(self, data_iter: Any, config: BatchConfig) -> AsyncIterator[Tuple[BatchInfo, List]]:
        """Create batches from iterator."""
        batch_id = 0
        batch_data = []
        total_processed = 0
        
        # Handle async iterator
        if hasattr(data_iter, '__aiter__'):
            async for item in data_iter:
                batch_data.append(item)
                
                if len(batch_data) >= config.batch_size:
                    batch_info = BatchInfo(
                        batch_id=batch_id,
                        start_index=total_processed,
                        end_index=total_processed + len(batch_data),
                        size=len(batch_data)
                    )
                    
                    yield batch_info, batch_data
                    
                    total_processed += len(batch_data)
                    batch_data = []
                    batch_id += 1
        
        # Handle regular iterator
        else:
            for item in data_iter:
                batch_data.append(item)
                
                if len(batch_data) >= config.batch_size:
                    batch_info = BatchInfo(
                        batch_id=batch_id,
                        start_index=total_processed,
                        end_index=total_processed + len(batch_data),
                        size=len(batch_data)
                    )
                    
                    yield batch_info, batch_data
                    
                    total_processed += len(batch_data)
                    batch_data = []
                    batch_id += 1
        
        # Yield remaining data
        if batch_data:
            batch_info = BatchInfo(
                batch_id=batch_id,
                start_index=total_processed,
                end_index=total_processed + len(batch_data),
                size=len(batch_data)
            )
            
            yield batch_info, batch_data
    
    # Processing mode implementations
    async def _process_sequential(
        self,
        batch_iterator: AsyncIterator[Tuple[BatchInfo, Any]],
        processor_func: Callable,
        config: BatchConfig
    ) -> BatchProcessingResult:
        """Process batches sequentially."""
        result = BatchProcessingResult(
            total_batches=0,
            successful_batches=0,
            failed_batches=0,
            total_records=0,
            processed_records=0,
            total_processing_time=0,
            average_batch_time=0,
            peak_memory_mb=0,
            status=ProcessingStatus.RUNNING
        )
        
        async for batch_info, batch_data in batch_iterator:
            batch_result = await self._process_single_batch(
                batch_info, batch_data, processor_func, config
            )
            
            result.batch_results.append(batch_result)
            result.total_batches += 1
            result.total_records += batch_info.size
            
            if batch_result.status == ProcessingStatus.COMPLETED:
                result.successful_batches += 1
                result.processed_records += batch_result.records_processed
            else:
                result.failed_batches += 1
                error_type = type(batch_result.error_message).__name__ if batch_result.error_message else "Unknown"
                result.error_summary[error_type] = result.error_summary.get(error_type, 0) + 1
            
            # Update peak memory
            result.peak_memory_mb = max(result.peak_memory_mb, batch_result.memory_used_mb)
            
            # Adaptive batch size adjustment
            if config.strategy == BatchStrategy.ADAPTIVE:
                await self._adjust_adaptive_batch_size(batch_result, config)
            
            # Progress callback
            if config.progress_callback:
                config.progress_callback(result.successful_batches, result.total_batches)
            
            # Log progress
            if config.log_progress and result.total_batches % config.progress_interval == 0:
                self.logger.info(
                    f"Processed {result.total_batches} batches, "
                    f"{result.processed_records:,} records, "
                    f"{result.successful_batches}/{result.total_batches} successful"
                )
            
            # Stop on error if configured
            if batch_result.status == ProcessingStatus.FAILED and not config.continue_on_error:
                break
        
        result.status = ProcessingStatus.COMPLETED if result.failed_batches == 0 else ProcessingStatus.PARTIAL
        return result
    
    async def _process_parallel(
        self,
        batch_iterator: AsyncIterator[Tuple[BatchInfo, Any]],
        processor_func: Callable,
        config: BatchConfig
    ) -> BatchProcessingResult:
        """Process batches in parallel."""
        result = BatchProcessingResult(
            total_batches=0,
            successful_batches=0,
            failed_batches=0,
            total_records=0,
            processed_records=0,
            total_processing_time=0,
            average_batch_time=0,
            peak_memory_mb=0,
            status=ProcessingStatus.RUNNING
        )
        
        # Collect batches for parallel processing
        batches = []
        async for batch_info, batch_data in batch_iterator:
            batches.append((batch_info, batch_data))
        
        # Process batches in parallel
        executor = self._thread_executor if config.worker_type == "thread" else self._process_executor
        
        # Create tasks for parallel execution
        tasks = []
        for batch_info, batch_data in batches:
            if config.worker_type == "thread":
                task = asyncio.create_task(
                    self._process_single_batch_async(batch_info, batch_data, processor_func, config)
                )
            else:
                # For process-based execution, we need to handle serialization
                task = asyncio.get_event_loop().run_in_executor(
                    executor,
                    self._process_single_batch_sync,
                    batch_info, batch_data, processor_func, config
                )
            tasks.append(task)
        
        # Wait for all tasks to complete
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for i, batch_result in enumerate(batch_results):
            if isinstance(batch_result, Exception):
                # Handle exception
                batch_info = batches[i][0]
                batch_result = BatchResult(
                    batch_info=batch_info,
                    status=ProcessingStatus.FAILED,
                    error_message=str(batch_result)
                )
            
            result.batch_results.append(batch_result)
            result.total_batches += 1
            result.total_records += batch_result.batch_info.size
            
            if batch_result.status == ProcessingStatus.COMPLETED:
                result.successful_batches += 1
                result.processed_records += batch_result.records_processed
            else:
                result.failed_batches += 1
            
            result.peak_memory_mb = max(result.peak_memory_mb, batch_result.memory_used_mb)
        
        result.status = ProcessingStatus.COMPLETED if result.failed_batches == 0 else ProcessingStatus.PARTIAL
        return result
    
    async def _process_async(
        self,
        batch_iterator: AsyncIterator[Tuple[BatchInfo, Any]],
        processor_func: Callable,
        config: BatchConfig
    ) -> BatchProcessingResult:
        """Process batches asynchronously with concurrency control."""
        result = BatchProcessingResult(
            total_batches=0,
            successful_batches=0,
            failed_batches=0,
            total_records=0,
            processed_records=0,
            total_processing_time=0,
            average_batch_time=0,
            peak_memory_mb=0,
            status=ProcessingStatus.RUNNING
        )
        
        # Use semaphore to control concurrency
        semaphore = asyncio.Semaphore(config.max_workers)
        
        async def process_with_semaphore(batch_info, batch_data):
            async with semaphore:
                return await self._process_single_batch(batch_info, batch_data, processor_func, config)
        
        # Create tasks for async execution
        tasks = []
        async for batch_info, batch_data in batch_iterator:
            task = asyncio.create_task(process_with_semaphore(batch_info, batch_data))
            tasks.append(task)
        
        # Process results as they complete
        for completed_task in asyncio.as_completed(tasks):
            batch_result = await completed_task
            
            result.batch_results.append(batch_result)
            result.total_batches += 1
            result.total_records += batch_result.batch_info.size
            
            if batch_result.status == ProcessingStatus.COMPLETED:
                result.successful_batches += 1
                result.processed_records += batch_result.records_processed
            else:
                result.failed_batches += 1
            
            result.peak_memory_mb = max(result.peak_memory_mb, batch_result.memory_used_mb)
            
            # Progress callback
            if config.progress_callback:
                config.progress_callback(result.successful_batches, result.total_batches)
        
        result.status = ProcessingStatus.COMPLETED if result.failed_batches == 0 else ProcessingStatus.PARTIAL
        return result
    
    async def _process_pipeline(
        self,
        batch_iterator: AsyncIterator[Tuple[BatchInfo, Any]],
        processor_func: Callable,
        config: BatchConfig
    ) -> BatchProcessingResult:
        """Process batches in pipeline mode with overlapping execution."""
        result = BatchProcessingResult(
            total_batches=0,
            successful_batches=0,
            failed_batches=0,
            total_records=0,
            processed_records=0,
            total_processing_time=0,
            average_batch_time=0,
            peak_memory_mb=0,
            status=ProcessingStatus.RUNNING
        )
        
        # Pipeline with producer-consumer pattern
        queue = asyncio.Queue(maxsize=config.max_workers * 2)
        
        # Producer task
        async def producer():
            async for batch_info, batch_data in batch_iterator:
                await queue.put((batch_info, batch_data))
            
            # Signal end of data
            for _ in range(config.max_workers):
                await queue.put(None)
        
        # Consumer task
        async def consumer():
            while True:
                item = await queue.get()
                if item is None:
                    break
                
                batch_info, batch_data = item
                batch_result = await self._process_single_batch(
                    batch_info, batch_data, processor_func, config
                )
                
                result.batch_results.append(batch_result)
                result.total_batches += 1
                result.total_records += batch_result.batch_info.size
                
                if batch_result.status == ProcessingStatus.COMPLETED:
                    result.successful_batches += 1
                    result.processed_records += batch_result.records_processed
                else:
                    result.failed_batches += 1
                
                result.peak_memory_mb = max(result.peak_memory_mb, batch_result.memory_used_mb)
                
                queue.task_done()
        
        # Start producer and consumers
        producer_task = asyncio.create_task(producer())
        consumer_tasks = [asyncio.create_task(consumer()) for _ in range(config.max_workers)]
        
        # Wait for completion
        await producer_task
        await queue.join()
        
        # Cancel consumer tasks
        for task in consumer_tasks:
            task.cancel()
        
        result.status = ProcessingStatus.COMPLETED if result.failed_batches == 0 else ProcessingStatus.PARTIAL
        return result
    
    # Single batch processing
    async def _process_single_batch(
        self,
        batch_info: BatchInfo,
        batch_data: Any,
        processor_func: Callable,
        config: BatchConfig
    ) -> BatchResult:
        """Process a single batch with error handling and retries."""
        start_time = time.time()
        memory_before = self.memory_monitor.get_memory_usage()
        
        for attempt in range(config.max_retries + 1):
            try:
                # Process the batch
                if asyncio.iscoroutinefunction(processor_func):
                    processed_data = await processor_func(batch_data)
                else:
                    processed_data = processor_func(batch_data)
                
                # Calculate metrics
                processing_time = time.time() - start_time
                memory_after = self.memory_monitor.get_memory_usage()
                memory_used = memory_after - memory_before
                
                # Force garbage collection
                gc.collect()
                
                return BatchResult(
                    batch_info=batch_info,
                    data=processed_data,
                    records_processed=batch_info.size,
                    processing_time=processing_time,
                    memory_used_mb=memory_used,
                    status=ProcessingStatus.COMPLETED,
                    metadata={
                        'attempt': attempt + 1,
                        'memory_before_mb': memory_before,
                        'memory_after_mb': memory_after
                    }
                )
                
            except Exception as e:
                if attempt < config.max_retries:
                    self.logger.warning(
                        f"Batch {batch_info.batch_id} failed on attempt {attempt + 1}, retrying: {e}"
                    )
                    await asyncio.sleep(config.retry_delay * (attempt + 1))  # Exponential backoff
                else:
                    self.logger.error(f"Batch {batch_info.batch_id} failed after {config.max_retries + 1} attempts: {e}")
                    
                    return BatchResult(
                        batch_info=batch_info,
                        processing_time=time.time() - start_time,
                        memory_used_mb=self.memory_monitor.get_memory_usage() - memory_before,
                        status=ProcessingStatus.FAILED,
                        error_message=str(e),
                        metadata={'attempts': attempt + 1}
                    )
    
    def _process_single_batch_sync(
        self,
        batch_info: BatchInfo,
        batch_data: Any,
        processor_func: Callable,
        config: BatchConfig
    ) -> BatchResult:
        """Synchronous version for process-based execution."""
        # This would be used for process-based parallel execution
        # Implementation would be similar to _process_single_batch but synchronous
        pass
    
    async def _process_single_batch_async(
        self,
        batch_info: BatchInfo,
        batch_data: Any,
        processor_func: Callable,
        config: BatchConfig
    ) -> BatchResult:
        """Async wrapper for thread-based execution."""
        return await self._process_single_batch(batch_info, batch_data, processor_func, config)
    
    # Adaptive batch size methods
    async def _adjust_adaptive_batch_size(self, batch_result: BatchResult, config: BatchConfig):
        """Adjust batch size based on performance feedback."""
        if batch_result.status != ProcessingStatus.COMPLETED:
            return
        
        # Calculate current performance
        records_per_second = batch_result.records_processed / max(batch_result.processing_time, 0.001)
        
        # Store performance history
        self._performance_history.append({
            'batch_size': batch_result.batch_info.size,
            'records_per_second': records_per_second,
            'memory_used_mb': batch_result.memory_used_mb,
            'processing_time': batch_result.processing_time
        })
        
        # Keep only recent history
        if len(self._performance_history) > 10:
            self._performance_history = self._performance_history[-10:]
        
        # Adjust batch size based on performance target
        if records_per_second < config.performance_target_rps:
            # Performance is below target, try smaller batches
            adjustment = -config.adaptation_factor
        else:
            # Performance is good, try larger batches
            adjustment = config.adaptation_factor
        
        new_batch_size = int(self._adaptive_batch_size * (1 + adjustment))
        
        # Apply bounds
        new_batch_size = max(config.min_batch_size, new_batch_size)
        new_batch_size = min(config.max_batch_size, new_batch_size)
        
        if new_batch_size != self._adaptive_batch_size:
            self.logger.debug(
                f"Adjusting adaptive batch size: {self._adaptive_batch_size} -> {new_batch_size} "
                f"(performance: {records_per_second:.1f} rps)"
            )
            self._adaptive_batch_size = new_batch_size
    
    async def _calculate_memory_based_batch_size(self, df: pd.DataFrame, start_idx: int, config: BatchConfig) -> int:
        """Calculate batch size based on memory constraints."""
        # Estimate memory per row
        sample_size = min(1000, len(df) - start_idx)
        if sample_size <= 0:
            return config.batch_size
        
        sample_data = df.iloc[start_idx:start_idx + sample_size]
        memory_per_row = sample_data.memory_usage(deep=True).sum() / len(sample_data)  # bytes
        
        # Calculate batch size for target memory usage
        target_memory_bytes = config.memory_limit_mb * 1024 * 1024 * config.memory_threshold
        batch_size = int(target_memory_bytes / memory_per_row)
        
        # Apply bounds
        batch_size = max(config.min_batch_size, batch_size)
        batch_size = min(config.max_batch_size, batch_size)
        
        return batch_size
    
    async def _estimate_chunk_size_for_memory(self, file_path: Union[str, Path], memory_limit_mb: float) -> int:
        """Estimate chunk size for file reading based on memory limit."""
        try:
            # Read a small sample to estimate memory usage
            if str(file_path).endswith('.csv'):
                sample = pd.read_csv(file_path, nrows=1000)
            elif str(file_path).endswith(('.xlsx', '.xls')):
                sample = pd.read_excel(file_path, nrows=1000)
            else:
                return self.config.batch_size
            
            # Calculate memory per row
            memory_per_row = sample.memory_usage(deep=True).sum() / len(sample)
            
            # Calculate chunk size
            target_memory_bytes = memory_limit_mb * 1024 * 1024
            chunk_size = int(target_memory_bytes / memory_per_row)
            
            # Apply bounds
            chunk_size = max(self.config.min_batch_size, chunk_size)
            chunk_size = min(self.config.max_batch_size, chunk_size)
            
            return chunk_size
            
        except Exception as e:
            self.logger.warning(f"Failed to estimate chunk size for {file_path}: {e}")
            return self.config.batch_size
    
    # Executor management
    async def _initialize_executors(self, config: BatchConfig):
        """Initialize thread/process executors for parallel processing."""
        if config.mode in [BatchMode.PARALLEL, BatchMode.ASYNC, BatchMode.PIPELINE]:
            if config.worker_type == "thread":
                self._thread_executor = ThreadPoolExecutor(max_workers=config.max_workers)
            else:
                self._process_executor = ProcessPoolExecutor(max_workers=config.max_workers)
    
    async def _cleanup_executors(self):
        """Clean up executors."""
        if self._thread_executor:
            self._thread_executor.shutdown(wait=True)
            self._thread_executor = None
        
        if self._process_executor:
            self._process_executor.shutdown(wait=True)
            self._process_executor = None
    
    def get_performance_history(self) -> List[Dict[str, float]]:
        """Get performance history for analysis."""
        return self._performance_history.copy()
    
    def reset_adaptive_batch_size(self):
        """Reset adaptive batch size to initial value."""
        self._adaptive_batch_size = self.config.batch_size
        self._performance_history.clear()