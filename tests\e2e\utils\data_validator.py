#!/usr/bin/env python3
"""
E2E测试数据验证工具

该模块提供:
1. 数据完整性验证
2. 数据格式验证
3. 业务规则验证
4. 地理空间数据验证
5. 性能数据验证
6. 数据质量报告生成

使用方法:
    from tests.e2e.utils.data_validator import DataValidator
    
    validator = DataValidator()
    result = validator.validate_ep_data(ep_dataframe)
    if not result.is_valid:
        print(f"验证失败: {result.errors}")
"""

import os
import sys
import re
import json
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    import pandas as pd
    import numpy as np
    import geopandas as gpd
    from shapely.geometry import Point, Polygon
    from shapely import wkt
except ImportError as e:
    print(f"警告: 缺少可选依赖包: {e}")
    pd = None
    np = None
    gpd = None
    Point = None
    Polygon = None
    wkt = None

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """验证级别"""
    STRICT = "strict"  # 严格验证
    NORMAL = "normal"  # 正常验证
    LOOSE = "loose"    # 宽松验证


class ValidationSeverity(Enum):
    """验证严重性"""
    ERROR = "error"      # 错误
    WARNING = "warning"  # 警告
    INFO = "info"        # 信息


@dataclass
class ValidationIssue:
    """验证问题"""
    severity: ValidationSeverity
    category: str
    message: str
    field: Optional[str] = None
    row_index: Optional[int] = None
    value: Optional[Any] = None
    expected: Optional[Any] = None
    suggestion: Optional[str] = None


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    issues: List[ValidationIssue] = field(default_factory=list)
    summary: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def errors(self) -> List[ValidationIssue]:
        """获取错误列表"""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.ERROR]
    
    @property
    def warnings(self) -> List[ValidationIssue]:
        """获取警告列表"""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.WARNING]
    
    @property
    def infos(self) -> List[ValidationIssue]:
        """获取信息列表"""
        return [issue for issue in self.issues if issue.severity == ValidationSeverity.INFO]


class DataValidator:
    """数据验证器"""
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.NORMAL):
        """初始化数据验证器
        
        Args:
            validation_level: 验证级别
        """
        self.validation_level = validation_level
        
        # 验证规则配置
        self.validation_rules = self._load_validation_rules()
        
        # 地理坐标系统
        self.coordinate_systems = {
            'WGS84': 4326,
            'GCJ02': None,  # 中国偏移坐标系
            'BD09': None    # 百度坐标系
        }
        
        logger.info(f"数据验证器初始化完成，验证级别: {validation_level.value}")
    
    def _load_validation_rules(self) -> Dict[str, Any]:
        """加载验证规则配置
        
        Returns:
            验证规则字典
        """
        return {
            'ep_data': {
                'required_columns': ['longitude', 'latitude', 'timestamp', 'cell_id'],
                'optional_columns': ['rsrp', 'rsrq', 'sinr', 'pci', 'earfcn'],
                'coordinate_range': {
                    'longitude': (-180.0, 180.0),
                    'latitude': (-90.0, 90.0)
                },
                'signal_range': {
                    'rsrp': (-150.0, -30.0),
                    'rsrq': (-30.0, 0.0),
                    'sinr': (-20.0, 40.0)
                },
                'data_types': {
                    'longitude': 'float',
                    'latitude': 'float',
                    'timestamp': 'datetime',
                    'cell_id': 'string',
                    'rsrp': 'float',
                    'rsrq': 'float',
                    'sinr': 'float'
                }
            },
            'cdr_data': {
                'required_columns': ['start_time', 'end_time', 'duration', 'call_type'],
                'optional_columns': ['caller_id', 'callee_id', 'cell_id', 'location'],
                'duration_range': (0, 86400),  # 0秒到24小时
                'call_types': ['voice', 'sms', 'data'],
                'data_types': {
                    'start_time': 'datetime',
                    'end_time': 'datetime',
                    'duration': 'int',
                    'call_type': 'string'
                }
            },
            'site_data': {
                'required_columns': ['site_id', 'longitude', 'latitude', 'site_type'],
                'optional_columns': ['height', 'azimuth', 'tilt', 'power'],
                'coordinate_range': {
                    'longitude': (-180.0, 180.0),
                    'latitude': (-90.0, 90.0)
                },
                'site_types': ['macro', 'micro', 'pico', 'femto'],
                'data_types': {
                    'site_id': 'string',
                    'longitude': 'float',
                    'latitude': 'float',
                    'site_type': 'string'
                }
            },
            'kpi_data': {
                'required_columns': ['timestamp', 'kpi_name', 'kpi_value'],
                'optional_columns': ['cell_id', 'site_id', 'region'],
                'kpi_names': ['throughput', 'latency', 'packet_loss', 'availability'],
                'value_ranges': {
                    'throughput': (0, 1000),  # Mbps
                    'latency': (0, 1000),     # ms
                    'packet_loss': (0, 100), # %
                    'availability': (0, 100) # %
                },
                'data_types': {
                    'timestamp': 'datetime',
                    'kpi_name': 'string',
                    'kpi_value': 'float'
                }
            }
        }
    
    def validate_ep_data(self, data: pd.DataFrame) -> ValidationResult:
        """验证EP数据
        
        Args:
            data: EP数据DataFrame
        
        Returns:
            验证结果
        """
        logger.info("开始验证EP数据")
        
        result = ValidationResult(is_valid=True)
        rules = self.validation_rules['ep_data']
        
        # 基础验证
        self._validate_basic_structure(data, rules, result, "EP")
        
        # 坐标验证
        self._validate_coordinates(data, rules, result)
        
        # 信号强度验证
        self._validate_signal_strength(data, rules, result)
        
        # 时间序列验证
        self._validate_timestamp_sequence(data, result)
        
        # 地理空间验证
        if Point and gpd:
            self._validate_geospatial_data(data, result)
        
        # 数据质量验证
        self._validate_data_quality(data, result, "EP")
        
        # 更新验证状态
        result.is_valid = len(result.errors) == 0
        
        # 生成摘要
        result.summary = self._generate_summary(data, result, "EP")
        
        logger.info(f"EP数据验证完成，有效性: {result.is_valid}")
        return result
    
    def validate_cdr_data(self, data: pd.DataFrame) -> ValidationResult:
        """验证CDR数据
        
        Args:
            data: CDR数据DataFrame
        
        Returns:
            验证结果
        """
        logger.info("开始验证CDR数据")
        
        result = ValidationResult(is_valid=True)
        rules = self.validation_rules['cdr_data']
        
        # 基础验证
        self._validate_basic_structure(data, rules, result, "CDR")
        
        # 通话时长验证
        self._validate_call_duration(data, rules, result)
        
        # 通话类型验证
        self._validate_call_types(data, rules, result)
        
        # 时间逻辑验证
        self._validate_time_logic(data, result)
        
        # 数据质量验证
        self._validate_data_quality(data, result, "CDR")
        
        # 更新验证状态
        result.is_valid = len(result.errors) == 0
        
        # 生成摘要
        result.summary = self._generate_summary(data, result, "CDR")
        
        logger.info(f"CDR数据验证完成，有效性: {result.is_valid}")
        return result
    
    def validate_site_data(self, data: pd.DataFrame) -> ValidationResult:
        """验证站点数据
        
        Args:
            data: 站点数据DataFrame
        
        Returns:
            验证结果
        """
        logger.info("开始验证站点数据")
        
        result = ValidationResult(is_valid=True)
        rules = self.validation_rules['site_data']
        
        # 基础验证
        self._validate_basic_structure(data, rules, result, "Site")
        
        # 坐标验证
        self._validate_coordinates(data, rules, result)
        
        # 站点类型验证
        self._validate_site_types(data, rules, result)
        
        # 站点唯一性验证
        self._validate_site_uniqueness(data, result)
        
        # 地理空间验证
        if Point and gpd:
            self._validate_geospatial_data(data, result)
        
        # 数据质量验证
        self._validate_data_quality(data, result, "Site")
        
        # 更新验证状态
        result.is_valid = len(result.errors) == 0
        
        # 生成摘要
        result.summary = self._generate_summary(data, result, "Site")
        
        logger.info(f"站点数据验证完成，有效性: {result.is_valid}")
        return result
    
    def validate_kpi_data(self, data: pd.DataFrame) -> ValidationResult:
        """验证KPI数据
        
        Args:
            data: KPI数据DataFrame
        
        Returns:
            验证结果
        """
        logger.info("开始验证KPI数据")
        
        result = ValidationResult(is_valid=True)
        rules = self.validation_rules['kpi_data']
        
        # 基础验证
        self._validate_basic_structure(data, rules, result, "KPI")
        
        # KPI值范围验证
        self._validate_kpi_values(data, rules, result)
        
        # KPI名称验证
        self._validate_kpi_names(data, rules, result)
        
        # 时间序列验证
        self._validate_timestamp_sequence(data, result)
        
        # 数据质量验证
        self._validate_data_quality(data, result, "KPI")
        
        # 更新验证状态
        result.is_valid = len(result.errors) == 0
        
        # 生成摘要
        result.summary = self._generate_summary(data, result, "KPI")
        
        logger.info(f"KPI数据验证完成，有效性: {result.is_valid}")
        return result
    
    def _validate_basic_structure(self, 
                                data: pd.DataFrame, 
                                rules: Dict[str, Any], 
                                result: ValidationResult,
                                data_type: str):
        """验证基础数据结构
        
        Args:
            data: 数据DataFrame
            rules: 验证规则
            result: 验证结果
            data_type: 数据类型
        """
        # 检查数据是否为空
        if data.empty:
            result.issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                category="structure",
                message=f"{data_type}数据为空",
                suggestion="请确保数据文件包含有效数据"
            ))
            return
        
        # 检查必需列
        required_columns = rules.get('required_columns', [])
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            result.issues.append(ValidationIssue(
                severity=ValidationSeverity.ERROR,
                category="structure",
                message=f"缺少必需列: {missing_columns}",
                expected=required_columns,
                suggestion="请确保数据包含所有必需的列"
            ))
        
        # 检查数据类型
        data_types = rules.get('data_types', {})
        for column, expected_type in data_types.items():
            if column in data.columns:
                self._validate_column_type(data, column, expected_type, result)
        
        # 检查数据行数
        if len(data) == 0:
            result.issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                category="structure",
                message=f"{data_type}数据行数为0",
                suggestion="请检查数据是否正确加载"
            ))
        elif len(data) > 1000000:  # 超过100万行
            result.issues.append(ValidationIssue(
                severity=ValidationSeverity.INFO,
                category="performance",
                message=f"{data_type}数据量较大: {len(data)}行",
                suggestion="大数据量可能影响处理性能"
            ))
    
    def _validate_column_type(self, 
                            data: pd.DataFrame, 
                            column: str, 
                            expected_type: str, 
                            result: ValidationResult):
        """验证列数据类型
        
        Args:
            data: 数据DataFrame
            column: 列名
            expected_type: 期望的数据类型
            result: 验证结果
        """
        if column not in data.columns:
            return
        
        series = data[column]
        
        # 检查空值
        null_count = series.isnull().sum()
        if null_count > 0:
            severity = ValidationSeverity.WARNING if null_count < len(data) * 0.1 else ValidationSeverity.ERROR
            result.issues.append(ValidationIssue(
                severity=severity,
                category="data_quality",
                message=f"列 '{column}' 包含 {null_count} 个空值",
                field=column,
                suggestion="请检查数据完整性"
            ))
        
        # 验证数据类型
        if expected_type == 'datetime':
            try:
                pd.to_datetime(series.dropna())
            except Exception as e:
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    category="data_type",
                    message=f"列 '{column}' 不是有效的日期时间格式: {str(e)}",
                    field=column,
                    expected="datetime",
                    suggestion="请确保日期时间格式正确"
                ))
        
        elif expected_type == 'float':
            try:
                pd.to_numeric(series.dropna(), errors='raise')
            except Exception:
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    category="data_type",
                    message=f"列 '{column}' 包含非数值数据",
                    field=column,
                    expected="numeric",
                    suggestion="请确保数值列只包含数字"
                ))
        
        elif expected_type == 'string':
            # 检查字符串长度
            if series.dtype == 'object':
                max_length = series.astype(str).str.len().max()
                if max_length > 1000:
                    result.issues.append(ValidationIssue(
                        severity=ValidationSeverity.WARNING,
                        category="data_quality",
                        message=f"列 '{column}' 包含过长的字符串 (最大长度: {max_length})",
                        field=column,
                        suggestion="请检查数据是否正确"
                    ))
    
    def _validate_coordinates(self, 
                            data: pd.DataFrame, 
                            rules: Dict[str, Any], 
                            result: ValidationResult):
        """验证地理坐标
        
        Args:
            data: 数据DataFrame
            rules: 验证规则
            result: 验证结果
        """
        coordinate_range = rules.get('coordinate_range', {})
        
        for coord_type, (min_val, max_val) in coordinate_range.items():
            if coord_type in data.columns:
                series = data[coord_type]
                
                # 检查坐标范围
                out_of_range = series[(series < min_val) | (series > max_val)]
                if not out_of_range.empty:
                    result.issues.append(ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        category="coordinate",
                        message=f"{coord_type}坐标超出有效范围: {len(out_of_range)}个点",
                        field=coord_type,
                        expected=f"[{min_val}, {max_val}]",
                        suggestion="请检查坐标系统和数据精度"
                    ))
                
                # 检查坐标精度
                if coord_type == 'longitude':
                    # 经度精度检查（小数点后6位约为0.1米精度）
                    precision = series.apply(lambda x: len(str(x).split('.')[-1]) if '.' in str(x) else 0)
                    low_precision = precision[precision < 4]
                    if not low_precision.empty:
                        result.issues.append(ValidationIssue(
                            severity=ValidationSeverity.WARNING,
                            category="coordinate",
                            message=f"经度精度较低: {len(low_precision)}个点精度不足4位小数",
                            field=coord_type,
                            suggestion="建议使用更高精度的坐标数据"
                        ))
    
    def _validate_signal_strength(self, 
                                data: pd.DataFrame, 
                                rules: Dict[str, Any], 
                                result: ValidationResult):
        """验证信号强度
        
        Args:
            data: 数据DataFrame
            rules: 验证规则
            result: 验证结果
        """
        signal_range = rules.get('signal_range', {})
        
        for signal_type, (min_val, max_val) in signal_range.items():
            if signal_type in data.columns:
                series = data[signal_type].dropna()
                
                if not series.empty:
                    # 检查信号强度范围
                    out_of_range = series[(series < min_val) | (series > max_val)]
                    if not out_of_range.empty:
                        result.issues.append(ValidationIssue(
                            severity=ValidationSeverity.WARNING,
                            category="signal",
                            message=f"{signal_type}信号强度异常: {len(out_of_range)}个值超出正常范围",
                            field=signal_type,
                            expected=f"[{min_val}, {max_val}]",
                            suggestion="请检查测量设备和环境条件"
                        ))
                    
                    # 检查信号强度分布
                    if np:
                        mean_val = np.mean(series)
                        std_val = np.std(series)
                        
                        if std_val < 1.0:  # 标准差过小，可能数据异常
                            result.issues.append(ValidationIssue(
                                severity=ValidationSeverity.WARNING,
                                category="signal",
                                message=f"{signal_type}信号强度变化过小 (标准差: {std_val:.2f})",
                                field=signal_type,
                                suggestion="请检查数据是否存在异常或测量环境过于单一"
                            ))
    
    def _validate_timestamp_sequence(self, data: pd.DataFrame, result: ValidationResult):
        """验证时间序列
        
        Args:
            data: 数据DataFrame
            result: 验证结果
        """
        timestamp_columns = ['timestamp', 'start_time', 'end_time']
        
        for col in timestamp_columns:
            if col in data.columns:
                try:
                    timestamps = pd.to_datetime(data[col].dropna())
                    
                    if not timestamps.empty:
                        # 检查时间顺序
                        if not timestamps.is_monotonic_increasing:
                            result.issues.append(ValidationIssue(
                                severity=ValidationSeverity.WARNING,
                                category="timestamp",
                                message=f"时间序列 '{col}' 不是单调递增的",
                                field=col,
                                suggestion="建议按时间顺序排序数据"
                            ))
                        
                        # 检查时间范围
                        min_time = timestamps.min()
                        max_time = timestamps.max()
                        now = datetime.now()
                        
                        if min_time > now:
                            result.issues.append(ValidationIssue(
                                severity=ValidationSeverity.WARNING,
                                category="timestamp",
                                message=f"时间序列 '{col}' 包含未来时间",
                                field=col,
                                value=min_time,
                                suggestion="请检查时间数据的正确性"
                            ))
                        
                        if max_time < now - timedelta(days=365*10):  # 超过10年前
                            result.issues.append(ValidationIssue(
                                severity=ValidationSeverity.INFO,
                                category="timestamp",
                                message=f"时间序列 '{col}' 包含较旧的数据",
                                field=col,
                                value=max_time,
                                suggestion="请确认历史数据的有效性"
                            ))
                        
                        # 检查时间间隔
                        if len(timestamps) > 1:
                            time_diffs = timestamps.diff().dropna()
                            if not time_diffs.empty:
                                median_diff = time_diffs.median()
                                
                                # 检查异常的时间间隔
                                outlier_diffs = time_diffs[time_diffs > median_diff * 10]
                                if not outlier_diffs.empty:
                                    result.issues.append(ValidationIssue(
                                        severity=ValidationSeverity.WARNING,
                                        category="timestamp",
                                        message=f"时间序列 '{col}' 存在异常的时间间隔: {len(outlier_diffs)}个",
                                        field=col,
                                        suggestion="请检查数据采集的连续性"
                                    ))
                
                except Exception as e:
                    result.issues.append(ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        category="timestamp",
                        message=f"时间序列 '{col}' 解析失败: {str(e)}",
                        field=col,
                        suggestion="请检查时间格式"
                    ))
    
    def _validate_geospatial_data(self, data: pd.DataFrame, result: ValidationResult):
        """验证地理空间数据
        
        Args:
            data: 数据DataFrame
            result: 验证结果
        """
        if 'longitude' in data.columns and 'latitude' in data.columns:
            try:
                # 创建地理点
                geometry = gpd.points_from_xy(data['longitude'], data['latitude'])
                gdf = gpd.GeoDataFrame(data, geometry=geometry)
                
                # 检查重复点
                duplicate_points = gdf.geometry.duplicated().sum()
                if duplicate_points > 0:
                    result.issues.append(ValidationIssue(
                        severity=ValidationSeverity.WARNING,
                        category="geospatial",
                        message=f"发现 {duplicate_points} 个重复的地理位置",
                        suggestion="请检查数据是否存在重复记录"
                    ))
                
                # 检查点的分布
                bounds = gdf.total_bounds
                area = (bounds[2] - bounds[0]) * (bounds[3] - bounds[1])
                
                if area < 0.001:  # 面积过小（约100米x100米）
                    result.issues.append(ValidationIssue(
                        severity=ValidationSeverity.INFO,
                        category="geospatial",
                        message="地理数据分布范围较小",
                        suggestion="请确认数据覆盖范围是否符合预期"
                    ))
                
                # 检查点密度
                if len(gdf) > 1000 and area > 0:
                    density = len(gdf) / area
                    if density > 10000:  # 密度过高
                        result.issues.append(ValidationIssue(
                            severity=ValidationSeverity.INFO,
                            category="geospatial",
                            message=f"地理数据点密度较高: {density:.0f} 点/平方度",
                            suggestion="高密度数据可能影响可视化性能"
                        ))
            
            except Exception as e:
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    category="geospatial",
                    message=f"地理空间数据验证失败: {str(e)}",
                    suggestion="请检查坐标数据的有效性"
                ))
    
    def _validate_data_quality(self, data: pd.DataFrame, result: ValidationResult, data_type: str):
        """验证数据质量
        
        Args:
            data: 数据DataFrame
            result: 验证结果
            data_type: 数据类型
        """
        # 检查重复行
        duplicate_rows = data.duplicated().sum()
        if duplicate_rows > 0:
            result.issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                category="data_quality",
                message=f"发现 {duplicate_rows} 行重复数据",
                suggestion="建议去除重复数据"
            ))
        
        # 检查数据完整性
        total_cells = data.size
        null_cells = data.isnull().sum().sum()
        null_percentage = (null_cells / total_cells) * 100
        
        if null_percentage > 10:
            result.issues.append(ValidationIssue(
                severity=ValidationSeverity.WARNING,
                category="data_quality",
                message=f"数据缺失率较高: {null_percentage:.1f}%",
                suggestion="请检查数据采集和处理流程"
            ))
        
        # 检查数据一致性
        for column in data.select_dtypes(include=['object']).columns:
            unique_values = data[column].nunique()
            total_values = len(data[column].dropna())
            
            if unique_values == total_values and total_values > 100:
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.INFO,
                    category="data_quality",
                    message=f"列 '{column}' 的所有值都是唯一的",
                    field=column,
                    suggestion="请确认这是否符合预期"
                ))
    
    def _validate_call_duration(self, 
                              data: pd.DataFrame, 
                              rules: Dict[str, Any], 
                              result: ValidationResult):
        """验证通话时长
        
        Args:
            data: CDR数据DataFrame
            rules: 验证规则
            result: 验证结果
        """
        if 'duration' in data.columns:
            duration_range = rules.get('duration_range', (0, 86400))
            min_duration, max_duration = duration_range
            
            durations = data['duration'].dropna()
            
            # 检查时长范围
            invalid_durations = durations[(durations < min_duration) | (durations > max_duration)]
            if not invalid_durations.empty:
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    category="business_rule",
                    message=f"通话时长超出有效范围: {len(invalid_durations)}条记录",
                    field="duration",
                    expected=f"[{min_duration}, {max_duration}]秒",
                    suggestion="请检查通话记录的准确性"
                ))
            
            # 检查异常短通话
            very_short_calls = durations[durations < 1]
            if not very_short_calls.empty and len(very_short_calls) > len(durations) * 0.1:
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.WARNING,
                    category="business_rule",
                    message=f"异常短通话较多: {len(very_short_calls)}条记录少于1秒",
                    field="duration",
                    suggestion="请检查是否存在未接通或异常通话"
                ))
    
    def _validate_call_types(self, 
                           data: pd.DataFrame, 
                           rules: Dict[str, Any], 
                           result: ValidationResult):
        """验证通话类型
        
        Args:
            data: CDR数据DataFrame
            rules: 验证规则
            result: 验证结果
        """
        if 'call_type' in data.columns:
            valid_types = rules.get('call_types', [])
            call_types = data['call_type'].dropna()
            
            invalid_types = call_types[~call_types.isin(valid_types)]
            if not invalid_types.empty:
                unique_invalid = invalid_types.unique()
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    category="business_rule",
                    message=f"无效的通话类型: {list(unique_invalid)}",
                    field="call_type",
                    expected=valid_types,
                    suggestion="请使用有效的通话类型"
                ))
    
    def _validate_time_logic(self, data: pd.DataFrame, result: ValidationResult):
        """验证时间逻辑
        
        Args:
            data: CDR数据DataFrame
            result: 验证结果
        """
        if 'start_time' in data.columns and 'end_time' in data.columns:
            try:
                start_times = pd.to_datetime(data['start_time'])
                end_times = pd.to_datetime(data['end_time'])
                
                # 检查结束时间是否晚于开始时间
                invalid_times = end_times <= start_times
                if invalid_times.any():
                    result.issues.append(ValidationIssue(
                        severity=ValidationSeverity.ERROR,
                        category="business_rule",
                        message=f"结束时间早于或等于开始时间: {invalid_times.sum()}条记录",
                        suggestion="请检查时间记录的逻辑性"
                    ))
                
                # 检查计算的时长与记录的时长是否一致
                if 'duration' in data.columns:
                    calculated_duration = (end_times - start_times).dt.total_seconds()
                    recorded_duration = data['duration']
                    
                    duration_diff = abs(calculated_duration - recorded_duration)
                    inconsistent = duration_diff > 1  # 允许1秒误差
                    
                    if inconsistent.any():
                        result.issues.append(ValidationIssue(
                            severity=ValidationSeverity.WARNING,
                            category="business_rule",
                            message=f"计算时长与记录时长不一致: {inconsistent.sum()}条记录",
                            suggestion="请检查时长计算的准确性"
                        ))
            
            except Exception as e:
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    category="business_rule",
                    message=f"时间逻辑验证失败: {str(e)}",
                    suggestion="请检查时间数据格式"
                ))
    
    def _validate_site_types(self, 
                           data: pd.DataFrame, 
                           rules: Dict[str, Any], 
                           result: ValidationResult):
        """验证站点类型
        
        Args:
            data: 站点数据DataFrame
            rules: 验证规则
            result: 验证结果
        """
        if 'site_type' in data.columns:
            valid_types = rules.get('site_types', [])
            site_types = data['site_type'].dropna()
            
            invalid_types = site_types[~site_types.isin(valid_types)]
            if not invalid_types.empty:
                unique_invalid = invalid_types.unique()
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    category="business_rule",
                    message=f"无效的站点类型: {list(unique_invalid)}",
                    field="site_type",
                    expected=valid_types,
                    suggestion="请使用有效的站点类型"
                ))
    
    def _validate_site_uniqueness(self, data: pd.DataFrame, result: ValidationResult):
        """验证站点唯一性
        
        Args:
            data: 站点数据DataFrame
            result: 验证结果
        """
        if 'site_id' in data.columns:
            duplicate_sites = data['site_id'].duplicated().sum()
            if duplicate_sites > 0:
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.ERROR,
                    category="business_rule",
                    message=f"发现重复的站点ID: {duplicate_sites}个",
                    field="site_id",
                    suggestion="站点ID应该是唯一的"
                ))
    
    def _validate_kpi_values(self, 
                           data: pd.DataFrame, 
                           rules: Dict[str, Any], 
                           result: ValidationResult):
        """验证KPI值
        
        Args:
            data: KPI数据DataFrame
            rules: 验证规则
            result: 验证结果
        """
        if 'kpi_name' in data.columns and 'kpi_value' in data.columns:
            value_ranges = rules.get('value_ranges', {})
            
            for kpi_name, (min_val, max_val) in value_ranges.items():
                kpi_data = data[data['kpi_name'] == kpi_name]['kpi_value']
                
                if not kpi_data.empty:
                    out_of_range = kpi_data[(kpi_data < min_val) | (kpi_data > max_val)]
                    if not out_of_range.empty:
                        result.issues.append(ValidationIssue(
                            severity=ValidationSeverity.WARNING,
                            category="business_rule",
                            message=f"KPI '{kpi_name}' 值超出正常范围: {len(out_of_range)}个值",
                            field="kpi_value",
                            expected=f"[{min_val}, {max_val}]",
                            suggestion="请检查KPI计算和测量方法"
                        ))
    
    def _validate_kpi_names(self, 
                          data: pd.DataFrame, 
                          rules: Dict[str, Any], 
                          result: ValidationResult):
        """验证KPI名称
        
        Args:
            data: KPI数据DataFrame
            rules: 验证规则
            result: 验证结果
        """
        if 'kpi_name' in data.columns:
            valid_names = rules.get('kpi_names', [])
            kpi_names = data['kpi_name'].dropna()
            
            invalid_names = kpi_names[~kpi_names.isin(valid_names)]
            if not invalid_names.empty:
                unique_invalid = invalid_names.unique()
                result.issues.append(ValidationIssue(
                    severity=ValidationSeverity.WARNING,
                    category="business_rule",
                    message=f"未知的KPI名称: {list(unique_invalid)}",
                    field="kpi_name",
                    expected=valid_names,
                    suggestion="请使用标准的KPI名称"
                ))
    
    def _generate_summary(self, 
                        data: pd.DataFrame, 
                        result: ValidationResult, 
                        data_type: str) -> Dict[str, Any]:
        """生成验证摘要
        
        Args:
            data: 数据DataFrame
            result: 验证结果
            data_type: 数据类型
        
        Returns:
            验证摘要字典
        """
        return {
            'data_type': data_type,
            'total_rows': len(data),
            'total_columns': len(data.columns),
            'validation_level': self.validation_level.value,
            'issues_count': {
                'errors': len(result.errors),
                'warnings': len(result.warnings),
                'infos': len(result.infos)
            },
            'data_quality_score': self._calculate_quality_score(data, result),
            'validation_timestamp': datetime.now().isoformat()
        }
    
    def _calculate_quality_score(self, data: pd.DataFrame, result: ValidationResult) -> float:
        """计算数据质量分数
        
        Args:
            data: 数据DataFrame
            result: 验证结果
        
        Returns:
            质量分数 (0-100)
        """
        base_score = 100.0
        
        # 错误扣分
        error_penalty = len(result.errors) * 10
        warning_penalty = len(result.warnings) * 3
        
        # 数据完整性加分
        if not data.empty:
            completeness = (1 - data.isnull().sum().sum() / data.size) * 10
        else:
            completeness = 0
        
        # 计算最终分数
        final_score = max(0, base_score - error_penalty - warning_penalty + completeness)
        return min(100.0, final_score)
    
    def generate_validation_report(self, 
                                 validation_results: List[ValidationResult], 
                                 output_file: Optional[str] = None) -> Dict[str, Any]:
        """生成验证报告
        
        Args:
            validation_results: 验证结果列表
            output_file: 输出文件路径（可选）
        
        Returns:
            验证报告字典
        """
        logger.info("生成数据验证报告")
        
        report = {
            'report_timestamp': datetime.now().isoformat(),
            'validation_level': self.validation_level.value,
            'total_validations': len(validation_results),
            'overall_status': all(result.is_valid for result in validation_results),
            'summary': {
                'total_errors': sum(len(result.errors) for result in validation_results),
                'total_warnings': sum(len(result.warnings) for result in validation_results),
                'total_infos': sum(len(result.infos) for result in validation_results)
            },
            'validation_results': [],
            'recommendations': []
        }
        
        # 添加每个验证结果
        for i, result in enumerate(validation_results):
            result_summary = {
                'validation_id': i + 1,
                'is_valid': result.is_valid,
                'summary': result.summary,
                'issues_count': {
                    'errors': len(result.errors),
                    'warnings': len(result.warnings),
                    'infos': len(result.infos)
                },
                'issues': [
                    {
                        'severity': issue.severity.value,
                        'category': issue.category,
                        'message': issue.message,
                        'field': issue.field,
                        'suggestion': issue.suggestion
                    }
                    for issue in result.issues
                ]
            }
            report['validation_results'].append(result_summary)
        
        # 生成总体建议
        report['recommendations'] = self._generate_overall_recommendations(validation_results)
        
        # 保存报告到文件
        if output_file:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"验证报告已保存到: {output_path}")
        
        return report
    
    def _generate_overall_recommendations(self, validation_results: List[ValidationResult]) -> List[str]:
        """生成总体建议
        
        Args:
            validation_results: 验证结果列表
        
        Returns:
            建议列表
        """
        recommendations = []
        
        # 统计问题类型
        error_categories = {}
        warning_categories = {}
        
        for result in validation_results:
            for error in result.errors:
                error_categories[error.category] = error_categories.get(error.category, 0) + 1
            for warning in result.warnings:
                warning_categories[warning.category] = warning_categories.get(warning.category, 0) + 1
        
        # 基于问题类型生成建议
        if error_categories.get('structure', 0) > 0:
            recommendations.append("数据结构存在问题，建议检查数据格式和必需字段")
        
        if error_categories.get('coordinate', 0) > 0:
            recommendations.append("地理坐标数据存在错误，建议验证坐标系统和数据精度")
        
        if warning_categories.get('data_quality', 0) > 0:
            recommendations.append("数据质量需要改善，建议加强数据清洗和验证流程")
        
        if error_categories.get('business_rule', 0) > 0:
            recommendations.append("业务规则验证失败，建议检查数据是否符合业务逻辑")
        
        if not recommendations:
            recommendations.append("数据验证通过，质量良好")
        
        return recommendations


if __name__ == "__main__":
    # 示例用法
    if pd is not None:
        # 创建示例数据
        sample_data = pd.DataFrame({
            'longitude': [116.3974, 116.3975, 116.3976],
            'latitude': [39.9093, 39.9094, 39.9095],
            'timestamp': ['2024-01-01 10:00:00', '2024-01-01 10:01:00', '2024-01-01 10:02:00'],
            'cell_id': ['cell_001', 'cell_002', 'cell_003'],
            'rsrp': [-85.5, -87.2, -89.1]
        })
        
        # 创建验证器
        validator = DataValidator(ValidationLevel.NORMAL)
        
        # 验证数据
        result = validator.validate_ep_data(sample_data)
        
        print(f"验证结果: {'通过' if result.is_valid else '失败'}")
        print(f"错误数量: {len(result.errors)}")
        print(f"警告数量: {len(result.warnings)}")
        
        # 生成报告
        report = validator.generate_validation_report([result], "validation_report.json")
        print("验证报告生成完成")
    else:
        print("pandas未安装，无法运行示例")