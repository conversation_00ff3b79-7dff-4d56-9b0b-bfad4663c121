#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信平台 - 高级安全扫描与渗透测试

本文件包含全面的安全测试套件，包括漏洞扫描、渗透测试、
安全配置检查和合规性验证，确保系统达到企业级安全标准。

安全目标:
- 零安全漏洞
- 数据泄露风险为零
- 访问控制100%有效
- GDPR合规性100%
- 安全事件响应时间 < 1小时

作者: Connect质量工程师
创建时间: 2024-12-19
"""

import asyncio
import json
import re
import ssl
import socket
import subprocess
import hashlib
import hmac
import base64
import time
import requests
import pytest
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from urllib.parse import urljoin, urlparse
import logging
import tempfile
import os
import yaml
from cryptography import x509
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
import jwt
from sqlalchemy import create_engine, text
import pandas as pd

logger = logging.getLogger(__name__)


@dataclass
class SecurityVulnerability:
    """安全漏洞数据类"""
    vulnerability_id: str
    title: str
    severity: str  # critical, high, medium, low
    category: str  # owasp_top10, injection, xss, etc.
    description: str
    affected_component: str
    proof_of_concept: str = ""
    remediation: str = ""
    cvss_score: float = 0.0
    cwe_id: str = ""
    discovered_at: datetime = field(default_factory=datetime.now)
    status: str = "open"  # open, fixed, false_positive, accepted_risk


@dataclass
class SecurityTestResult:
    """安全测试结果数据类"""
    test_name: str
    test_category: str
    success: bool
    vulnerabilities: List[SecurityVulnerability] = field(default_factory=list)
    execution_time: float = 0.0
    error_message: str = ""
    recommendations: List[str] = field(default_factory=list)
    compliance_status: Dict[str, bool] = field(default_factory=dict)


class AdvancedSecurityScanner:
    """高级安全扫描器"""
    
    def __init__(self, target_url: str = "http://localhost:8000", api_key: str = None):
        self.target_url = target_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.test_results = []
        self.vulnerabilities = []
        
        # 配置请求会话
        self.session.headers.update({
            'User-Agent': 'Connect-Security-Scanner/1.0',
            'Accept': 'application/json'
        })
        
        if api_key:
            self.session.headers['Authorization'] = f'Bearer {api_key}'
    
    def add_vulnerability(self, vuln: SecurityVulnerability):
        """添加发现的漏洞"""
        self.vulnerabilities.append(vuln)
        logger.warning(f"发现{vuln.severity}级漏洞: {vuln.title}")
    
    # ==================== OWASP Top 10 测试 ====================
    
    def test_injection_vulnerabilities(self) -> SecurityTestResult:
        """测试注入漏洞 (OWASP A03:2021)"""
        start_time = time.time()
        result = SecurityTestResult(
            test_name="注入漏洞测试",
            test_category="OWASP_A03_Injection",
            success=True
        )
        
        try:
            # SQL注入测试
            self._test_sql_injection(result)
            
            # NoSQL注入测试
            self._test_nosql_injection(result)
            
            # LDAP注入测试
            self._test_ldap_injection(result)
            
            # 命令注入测试
            self._test_command_injection(result)
            
            # XPath注入测试
            self._test_xpath_injection(result)
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            logger.error(f"注入漏洞测试异常: {e}")
        
        result.execution_time = time.time() - start_time
        self.test_results.append(result)
        return result
    
    def _test_sql_injection(self, result: SecurityTestResult):
        """SQL注入测试"""
        sql_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM information_schema.tables --",
            "1' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --",
            "' OR 1=1#",
            "admin'--",
            "' OR 'x'='x",
            "1; WAITFOR DELAY '00:00:05' --"
        ]
        
        test_endpoints = [
            "/api/auth/login",
            "/api/users/search",
            "/api/sites/filter",
            "/api/data/query"
        ]
        
        for endpoint in test_endpoints:
            for payload in sql_payloads:
                try:
                    # GET参数注入
                    response = self.session.get(
                        f"{self.target_url}{endpoint}",
                        params={'id': payload, 'search': payload},
                        timeout=10
                    )
                    
                    if self._detect_sql_injection_response(response):
                        vuln = SecurityVulnerability(
                            vulnerability_id=f"SQL_INJ_{endpoint}_{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                            title=f"SQL注入漏洞 - {endpoint}",
                            severity="critical",
                            category="injection",
                            description=f"端点 {endpoint} 存在SQL注入漏洞",
                            affected_component=endpoint,
                            proof_of_concept=f"Payload: {payload}\nResponse: {response.text[:200]}",
                            remediation="使用参数化查询和输入验证",
                            cvss_score=9.8,
                            cwe_id="CWE-89"
                        )
                        self.add_vulnerability(vuln)
                        result.vulnerabilities.append(vuln)
                    
                    # POST数据注入
                    if endpoint in ["/api/auth/login", "/api/users/search"]:
                        post_response = self.session.post(
                            f"{self.target_url}{endpoint}",
                            json={'username': payload, 'password': payload},
                            timeout=10
                        )
                        
                        if self._detect_sql_injection_response(post_response):
                            vuln = SecurityVulnerability(
                                vulnerability_id=f"SQL_INJ_POST_{endpoint}_{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                                title=f"POST SQL注入漏洞 - {endpoint}",
                                severity="critical",
                                category="injection",
                                description=f"端点 {endpoint} POST请求存在SQL注入漏洞",
                                affected_component=endpoint,
                                proof_of_concept=f"POST Payload: {payload}\nResponse: {post_response.text[:200]}",
                                remediation="使用参数化查询和输入验证",
                                cvss_score=9.8,
                                cwe_id="CWE-89"
                            )
                            self.add_vulnerability(vuln)
                            result.vulnerabilities.append(vuln)
                
                except requests.RequestException as e:
                    logger.debug(f"SQL注入测试请求异常 {endpoint}: {e}")
    
    def _detect_sql_injection_response(self, response: requests.Response) -> bool:
        """检测SQL注入响应特征"""
        if response.status_code == 500:
            return True
        
        error_patterns = [
            r"SQL syntax.*MySQL",
            r"Warning.*mysql_.*",
            r"valid MySQL result",
            r"PostgreSQL.*ERROR",
            r"Warning.*pg_.*",
            r"valid PostgreSQL result",
            r"SQLite.*error",
            r"sqlite3.OperationalError",
            r"ORA-\d{5}",
            r"Microsoft.*ODBC.*SQL Server",
            r"SQLServer JDBC Driver",
            r"SqlException"
        ]
        
        response_text = response.text.lower()
        for pattern in error_patterns:
            if re.search(pattern, response_text, re.IGNORECASE):
                return True
        
        return False
    
    def _test_nosql_injection(self, result: SecurityTestResult):
        """NoSQL注入测试"""
        nosql_payloads = [
            '{"$ne": null}',
            '{"$gt": ""}',
            '{"$where": "this.username == this.password"}',
            '{"$regex": ".*"}',
            '{"username": {"$ne": null}, "password": {"$ne": null}}'
        ]
        
        for payload in nosql_payloads:
            try:
                response = self.session.post(
                    f"{self.target_url}/api/auth/login",
                    json={'username': payload, 'password': payload},
                    timeout=10
                )
                
                if response.status_code == 200 and 'token' in response.text:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"NOSQL_INJ_{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                        title="NoSQL注入漏洞",
                        severity="high",
                        category="injection",
                        description="登录端点存在NoSQL注入漏洞",
                        affected_component="/api/auth/login",
                        proof_of_concept=f"Payload: {payload}",
                        remediation="使用参数化查询和输入验证",
                        cvss_score=8.5,
                        cwe_id="CWE-943"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
            
            except requests.RequestException as e:
                logger.debug(f"NoSQL注入测试异常: {e}")
    
    def _test_ldap_injection(self, result: SecurityTestResult):
        """LDAP注入测试"""
        ldap_payloads = [
            "*)(uid=*))(|(uid=*",
            "*)(|(password=*))",
            "admin)(&(password=*",
            "*))%00"
        ]
        
        for payload in ldap_payloads:
            try:
                response = self.session.post(
                    f"{self.target_url}/api/auth/ldap",
                    json={'username': payload, 'password': 'test'},
                    timeout=10
                )
                
                if response.status_code == 200:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"LDAP_INJ_{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                        title="LDAP注入漏洞",
                        severity="high",
                        category="injection",
                        description="LDAP认证存在注入漏洞",
                        affected_component="/api/auth/ldap",
                        proof_of_concept=f"Payload: {payload}",
                        remediation="使用LDAP转义和输入验证",
                        cvss_score=8.0,
                        cwe_id="CWE-90"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
            
            except requests.RequestException:
                pass  # LDAP端点可能不存在
    
    def _test_command_injection(self, result: SecurityTestResult):
        """命令注入测试"""
        cmd_payloads = [
            "; ls -la",
            "| whoami",
            "& ping -c 1 127.0.0.1",
            "`id`",
            "$(whoami)",
            "; cat /etc/passwd",
            "| type C:\\Windows\\System32\\drivers\\etc\\hosts"
        ]
        
        test_endpoints = [
            "/api/system/ping",
            "/api/tools/traceroute",
            "/api/admin/backup"
        ]
        
        for endpoint in test_endpoints:
            for payload in cmd_payloads:
                try:
                    response = self.session.post(
                        f"{self.target_url}{endpoint}",
                        json={'target': f"127.0.0.1{payload}", 'command': payload},
                        timeout=15
                    )
                    
                    if self._detect_command_injection_response(response):
                        vuln = SecurityVulnerability(
                            vulnerability_id=f"CMD_INJ_{endpoint}_{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                            title=f"命令注入漏洞 - {endpoint}",
                            severity="critical",
                            category="injection",
                            description=f"端点 {endpoint} 存在命令注入漏洞",
                            affected_component=endpoint,
                            proof_of_concept=f"Payload: {payload}\nResponse: {response.text[:200]}",
                            remediation="使用白名单验证和命令参数化",
                            cvss_score=9.5,
                            cwe_id="CWE-78"
                        )
                        self.add_vulnerability(vuln)
                        result.vulnerabilities.append(vuln)
                
                except requests.RequestException as e:
                    logger.debug(f"命令注入测试异常 {endpoint}: {e}")
    
    def _detect_command_injection_response(self, response: requests.Response) -> bool:
        """检测命令注入响应特征"""
        response_text = response.text.lower()
        
        # Unix/Linux命令输出特征
        unix_patterns = [
            r"uid=\d+\(\w+\)",  # id命令输出
            r"total \d+",  # ls -la输出
            r"root:x:0:0:",  # /etc/passwd内容
            r"ping statistics",  # ping命令输出
            r"packets transmitted"
        ]
        
        # Windows命令输出特征
        windows_patterns = [
            r"volume in drive",
            r"directory of",
            r"copyright.*microsoft",
            r"windows.*version"
        ]
        
        all_patterns = unix_patterns + windows_patterns
        
        for pattern in all_patterns:
            if re.search(pattern, response_text, re.IGNORECASE):
                return True
        
        return False
    
    def _test_xpath_injection(self, result: SecurityTestResult):
        """XPath注入测试"""
        xpath_payloads = [
            "' or '1'='1",
            "' or 1=1 or ''='",
            "x' or name()='username' or 'x'='y",
            "test' and count(/*)=1 and 'test'='test"
        ]
        
        for payload in xpath_payloads:
            try:
                response = self.session.get(
                    f"{self.target_url}/api/xml/search",
                    params={'query': payload},
                    timeout=10
                )
                
                if response.status_code == 200 and len(response.text) > 100:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"XPATH_INJ_{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                        title="XPath注入漏洞",
                        severity="medium",
                        category="injection",
                        description="XML搜索端点存在XPath注入漏洞",
                        affected_component="/api/xml/search",
                        proof_of_concept=f"Payload: {payload}",
                        remediation="使用参数化XPath查询",
                        cvss_score=6.5,
                        cwe_id="CWE-643"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
            
            except requests.RequestException:
                pass  # XML端点可能不存在
    
    # ==================== 认证和授权测试 ====================
    
    def test_authentication_vulnerabilities(self) -> SecurityTestResult:
        """测试认证漏洞 (OWASP A07:2021)"""
        start_time = time.time()
        result = SecurityTestResult(
            test_name="认证漏洞测试",
            test_category="OWASP_A07_Authentication",
            success=True
        )
        
        try:
            # 弱密码测试
            self._test_weak_passwords(result)
            
            # 暴力破解测试
            self._test_brute_force_protection(result)
            
            # JWT安全测试
            self._test_jwt_security(result)
            
            # 会话管理测试
            self._test_session_management(result)
            
            # 多因素认证测试
            self._test_mfa_bypass(result)
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            logger.error(f"认证漏洞测试异常: {e}")
        
        result.execution_time = time.time() - start_time
        self.test_results.append(result)
        return result
    
    def _test_weak_passwords(self, result: SecurityTestResult):
        """弱密码测试"""
        weak_passwords = [
            "123456", "password", "admin", "root", "test",
            "qwerty", "123123", "abc123", "password123",
            "admin123", "root123", "guest", "user"
        ]
        
        common_usernames = [
            "admin", "administrator", "root", "user", "test",
            "guest", "demo", "sa", "operator", "manager"
        ]
        
        for username in common_usernames:
            for password in weak_passwords:
                try:
                    response = self.session.post(
                        f"{self.target_url}/api/auth/login",
                        json={'username': username, 'password': password},
                        timeout=10
                    )
                    
                    if response.status_code == 200 and ('token' in response.text or 'success' in response.text):
                        vuln = SecurityVulnerability(
                            vulnerability_id=f"WEAK_PWD_{username}_{password}",
                            title=f"弱密码漏洞 - {username}/{password}",
                            severity="high",
                            category="authentication",
                            description=f"用户 {username} 使用弱密码 {password}",
                            affected_component="/api/auth/login",
                            proof_of_concept=f"Username: {username}, Password: {password}",
                            remediation="强制使用强密码策略",
                            cvss_score=8.0,
                            cwe_id="CWE-521"
                        )
                        self.add_vulnerability(vuln)
                        result.vulnerabilities.append(vuln)
                
                except requests.RequestException as e:
                    logger.debug(f"弱密码测试异常: {e}")
    
    def _test_brute_force_protection(self, result: SecurityTestResult):
        """暴力破解保护测试"""
        # 快速连续登录尝试
        failed_attempts = 0
        
        for i in range(10):
            try:
                response = self.session.post(
                    f"{self.target_url}/api/auth/login",
                    json={'username': 'testuser', 'password': f'wrongpass{i}'},
                    timeout=5
                )
                
                if response.status_code in [401, 403]:
                    failed_attempts += 1
                elif response.status_code == 429:  # Too Many Requests
                    # 有速率限制，这是好的
                    break
                
                time.sleep(0.1)  # 短暂延迟
            
            except requests.RequestException:
                break
        
        # 如果能够进行多次失败尝试而没有被限制，则存在漏洞
        if failed_attempts >= 5:
            vuln = SecurityVulnerability(
                vulnerability_id="BRUTE_FORCE_NO_PROTECTION",
                title="缺少暴力破解保护",
                severity="medium",
                category="authentication",
                description="登录端点缺少暴力破解保护机制",
                affected_component="/api/auth/login",
                proof_of_concept=f"成功进行了 {failed_attempts} 次失败登录尝试",
                remediation="实施账户锁定和速率限制",
                cvss_score=6.0,
                cwe_id="CWE-307"
            )
            self.add_vulnerability(vuln)
            result.vulnerabilities.append(vuln)
    
    def _test_jwt_security(self, result: SecurityTestResult):
        """JWT安全测试"""
        # 尝试获取有效的JWT token
        try:
            login_response = self.session.post(
                f"{self.target_url}/api/auth/login",
                json={'username': 'admin', 'password': 'admin123'},
                timeout=10
            )
            
            if login_response.status_code == 200:
                token_data = login_response.json()
                if 'token' in token_data:
                    token = token_data['token']
                    
                    # JWT算法混淆攻击
                    self._test_jwt_algorithm_confusion(result, token)
                    
                    # JWT密钥暴力破解
                    self._test_jwt_weak_secret(result, token)
                    
                    # JWT None算法攻击
                    self._test_jwt_none_algorithm(result, token)
        
        except Exception as e:
            logger.debug(f"JWT测试异常: {e}")
    
    def _test_jwt_algorithm_confusion(self, result: SecurityTestResult, token: str):
        """JWT算法混淆攻击测试"""
        try:
            # 解码JWT header
            header = jwt.get_unverified_header(token)
            payload = jwt.decode(token, options={"verify_signature": False})
            
            # 尝试将RS256改为HS256
            if header.get('alg') == 'RS256':
                # 创建恶意token
                malicious_payload = payload.copy()
                malicious_payload['username'] = 'admin'
                malicious_payload['role'] = 'administrator'
                
                # 使用公钥作为HMAC密钥
                public_key = "public_key_content"  # 这里应该是实际的公钥
                malicious_token = jwt.encode(
                    malicious_payload,
                    public_key,
                    algorithm='HS256'
                )
                
                # 测试恶意token
                test_response = self.session.get(
                    f"{self.target_url}/api/admin/users",
                    headers={'Authorization': f'Bearer {malicious_token}'},
                    timeout=10
                )
                
                if test_response.status_code == 200:
                    vuln = SecurityVulnerability(
                        vulnerability_id="JWT_ALG_CONFUSION",
                        title="JWT算法混淆漏洞",
                        severity="critical",
                        category="authentication",
                        description="JWT实现存在算法混淆漏洞",
                        affected_component="JWT验证",
                        proof_of_concept="成功使用HS256算法绕过RS256验证",
                        remediation="严格验证JWT算法类型",
                        cvss_score=9.0,
                        cwe_id="CWE-347"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
        
        except Exception as e:
            logger.debug(f"JWT算法混淆测试异常: {e}")
    
    def _test_jwt_weak_secret(self, result: SecurityTestResult, token: str):
        """JWT弱密钥测试"""
        weak_secrets = [
            "secret", "key", "password", "123456", "jwt_secret",
            "your-256-bit-secret", "mysecretkey", "secretkey"
        ]
        
        for secret in weak_secrets:
            try:
                decoded = jwt.decode(token, secret, algorithms=['HS256'])
                
                vuln = SecurityVulnerability(
                    vulnerability_id=f"JWT_WEAK_SECRET_{secret}",
                    title="JWT弱密钥漏洞",
                    severity="critical",
                    category="authentication",
                    description=f"JWT使用弱密钥: {secret}",
                    affected_component="JWT签名",
                    proof_of_concept=f"成功使用弱密钥 '{secret}' 解码JWT",
                    remediation="使用强随机密钥签名JWT",
                    cvss_score=9.5,
                    cwe_id="CWE-326"
                )
                self.add_vulnerability(vuln)
                result.vulnerabilities.append(vuln)
                break
            
            except jwt.InvalidSignatureError:
                continue
            except Exception as e:
                logger.debug(f"JWT弱密钥测试异常: {e}")
    
    def _test_jwt_none_algorithm(self, result: SecurityTestResult, token: str):
        """JWT None算法攻击测试"""
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            
            # 创建使用none算法的恶意token
            malicious_payload = payload.copy()
            malicious_payload['username'] = 'admin'
            malicious_payload['role'] = 'administrator'
            
            # 手动构造none算法token
            header = {"alg": "none", "typ": "JWT"}
            header_encoded = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
            payload_encoded = base64.urlsafe_b64encode(json.dumps(malicious_payload).encode()).decode().rstrip('=')
            
            malicious_token = f"{header_encoded}.{payload_encoded}."
            
            # 测试恶意token
            test_response = self.session.get(
                f"{self.target_url}/api/admin/users",
                headers={'Authorization': f'Bearer {malicious_token}'},
                timeout=10
            )
            
            if test_response.status_code == 200:
                vuln = SecurityVulnerability(
                    vulnerability_id="JWT_NONE_ALG",
                    title="JWT None算法漏洞",
                    severity="critical",
                    category="authentication",
                    description="JWT接受none算法，允许未签名token",
                    affected_component="JWT验证",
                    proof_of_concept="成功使用none算法绕过JWT签名验证",
                    remediation="禁止none算法，强制验证签名",
                    cvss_score=9.8,
                    cwe_id="CWE-347"
                )
                self.add_vulnerability(vuln)
                result.vulnerabilities.append(vuln)
        
        except Exception as e:
            logger.debug(f"JWT None算法测试异常: {e}")
    
    def _test_session_management(self, result: SecurityTestResult):
        """会话管理测试"""
        try:
            # 登录获取会话
            login_response = self.session.post(
                f"{self.target_url}/api/auth/login",
                json={'username': 'testuser', 'password': 'testpass'},
                timeout=10
            )
            
            if login_response.status_code == 200:
                # 检查会话cookie安全属性
                cookies = login_response.cookies
                
                for cookie in cookies:
                    if 'session' in cookie.name.lower() or 'token' in cookie.name.lower():
                        # 检查Secure标志
                        if not cookie.secure:
                            vuln = SecurityVulnerability(
                                vulnerability_id=f"SESSION_INSECURE_{cookie.name}",
                                title="会话Cookie缺少Secure标志",
                                severity="medium",
                                category="session_management",
                                description=f"Cookie {cookie.name} 缺少Secure标志",
                                affected_component="会话管理",
                                proof_of_concept=f"Cookie: {cookie.name}",
                                remediation="为会话Cookie设置Secure标志",
                                cvss_score=5.0,
                                cwe_id="CWE-614"
                            )
                            self.add_vulnerability(vuln)
                            result.vulnerabilities.append(vuln)
                        
                        # 检查HttpOnly标志
                        if not getattr(cookie, 'httponly', False):
                            vuln = SecurityVulnerability(
                                vulnerability_id=f"SESSION_NO_HTTPONLY_{cookie.name}",
                                title="会话Cookie缺少HttpOnly标志",
                                severity="medium",
                                category="session_management",
                                description=f"Cookie {cookie.name} 缺少HttpOnly标志",
                                affected_component="会话管理",
                                proof_of_concept=f"Cookie: {cookie.name}",
                                remediation="为会话Cookie设置HttpOnly标志",
                                cvss_score=5.5,
                                cwe_id="CWE-1004"
                            )
                            self.add_vulnerability(vuln)
                            result.vulnerabilities.append(vuln)
        
        except Exception as e:
            logger.debug(f"会话管理测试异常: {e}")
    
    def _test_mfa_bypass(self, result: SecurityTestResult):
        """多因素认证绕过测试"""
        try:
            # 尝试直接访问需要MFA的端点
            mfa_endpoints = [
                "/api/admin/settings",
                "/api/admin/users",
                "/api/sensitive/data"
            ]
            
            for endpoint in mfa_endpoints:
                response = self.session.get(
                    f"{self.target_url}{endpoint}",
                    timeout=10
                )
                
                if response.status_code == 200:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"MFA_BYPASS_{endpoint}",
                        title=f"MFA绕过漏洞 - {endpoint}",
                        severity="high",
                        category="authentication",
                        description=f"敏感端点 {endpoint} 可以绕过MFA访问",
                        affected_component=endpoint,
                        proof_of_concept=f"直接访问 {endpoint} 成功",
                        remediation="强制所有敏感操作使用MFA",
                        cvss_score=8.5,
                        cwe_id="CWE-287"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
        
        except Exception as e:
            logger.debug(f"MFA绕过测试异常: {e}")
    
    # ==================== XSS和CSRF测试 ====================
    
    def test_xss_vulnerabilities(self) -> SecurityTestResult:
        """测试XSS漏洞 (OWASP A03:2021)"""
        start_time = time.time()
        result = SecurityTestResult(
            test_name="XSS漏洞测试",
            test_category="OWASP_A03_XSS",
            success=True
        )
        
        try:
            # 反射型XSS测试
            self._test_reflected_xss(result)
            
            # 存储型XSS测试
            self._test_stored_xss(result)
            
            # DOM型XSS测试
            self._test_dom_xss(result)
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            logger.error(f"XSS漏洞测试异常: {e}")
        
        result.execution_time = time.time() - start_time
        self.test_results.append(result)
        return result
    
    def _test_reflected_xss(self, result: SecurityTestResult):
        """反射型XSS测试"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus><option>test</option></select>"
        ]
        
        test_endpoints = [
            "/api/search",
            "/api/users/search",
            "/api/sites/filter",
            "/api/error"
        ]
        
        for endpoint in test_endpoints:
            for payload in xss_payloads:
                try:
                    # GET参数XSS
                    response = self.session.get(
                        f"{self.target_url}{endpoint}",
                        params={'q': payload, 'search': payload, 'error': payload},
                        timeout=10
                    )
                    
                    if self._detect_xss_response(response, payload):
                        vuln = SecurityVulnerability(
                            vulnerability_id=f"REFLECTED_XSS_{endpoint}_{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                            title=f"反射型XSS漏洞 - {endpoint}",
                            severity="high",
                            category="xss",
                            description=f"端点 {endpoint} 存在反射型XSS漏洞",
                            affected_component=endpoint,
                            proof_of_concept=f"Payload: {payload}",
                            remediation="对用户输入进行HTML编码和输出过滤",
                            cvss_score=7.5,
                            cwe_id="CWE-79"
                        )
                        self.add_vulnerability(vuln)
                        result.vulnerabilities.append(vuln)
                
                except requests.RequestException as e:
                    logger.debug(f"反射型XSS测试异常 {endpoint}: {e}")
    
    def _test_stored_xss(self, result: SecurityTestResult):
        """存储型XSS测试"""
        xss_payloads = [
            "<script>alert('Stored XSS')</script>",
            "<img src=x onerror=alert('Stored XSS')>",
            "<svg onload=alert('Stored XSS')>"
        ]
        
        # 尝试在用户资料、评论等地方注入XSS
        for payload in xss_payloads:
            try:
                # 创建包含XSS的用户资料
                create_response = self.session.post(
                    f"{self.target_url}/api/users/profile",
                    json={
                        'name': payload,
                        'bio': payload,
                        'company': payload
                    },
                    timeout=10
                )
                
                if create_response.status_code in [200, 201]:
                    # 获取用户资料查看是否存在XSS
                    view_response = self.session.get(
                        f"{self.target_url}/api/users/profile",
                        timeout=10
                    )
                    
                    if self._detect_xss_response(view_response, payload):
                        vuln = SecurityVulnerability(
                            vulnerability_id=f"STORED_XSS_PROFILE_{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                            title="存储型XSS漏洞 - 用户资料",
                            severity="high",
                            category="xss",
                            description="用户资料存在存储型XSS漏洞",
                            affected_component="/api/users/profile",
                            proof_of_concept=f"Payload: {payload}",
                            remediation="对存储的用户输入进行HTML编码",
                            cvss_score=8.0,
                            cwe_id="CWE-79"
                        )
                        self.add_vulnerability(vuln)
                        result.vulnerabilities.append(vuln)
            
            except requests.RequestException as e:
                logger.debug(f"存储型XSS测试异常: {e}")
    
    def _test_dom_xss(self, result: SecurityTestResult):
        """DOM型XSS测试"""
        # DOM XSS通常需要浏览器环境，这里进行基础检测
        dom_xss_patterns = [
            "document.write",
            "innerHTML",
            "outerHTML",
            "document.location",
            "window.location",
            "eval(",
            "setTimeout(",
            "setInterval("
        ]
        
        try:
            # 获取前端JavaScript文件
            js_response = self.session.get(
                f"{self.target_url}/static/js/main.js",
                timeout=10
            )
            
            if js_response.status_code == 200:
                js_content = js_response.text
                
                for pattern in dom_xss_patterns:
                    if pattern in js_content:
                        # 进一步分析是否存在不安全的DOM操作
                        if self._analyze_dom_xss_pattern(js_content, pattern):
                            vuln = SecurityVulnerability(
                                vulnerability_id=f"DOM_XSS_{pattern}",
                                title=f"潜在DOM XSS漏洞 - {pattern}",
                                severity="medium",
                                category="xss",
                                description=f"JavaScript代码中发现潜在的DOM XSS模式: {pattern}",
                                affected_component="前端JavaScript",
                                proof_of_concept=f"发现不安全的DOM操作: {pattern}",
                                remediation="使用安全的DOM操作方法和输入验证",
                                cvss_score=6.0,
                                cwe_id="CWE-79"
                            )
                            self.add_vulnerability(vuln)
                            result.vulnerabilities.append(vuln)
        
        except requests.RequestException:
            pass  # JavaScript文件可能不存在
    
    def _detect_xss_response(self, response: requests.Response, payload: str) -> bool:
        """检测XSS响应特征"""
        # 检查payload是否未经编码直接出现在响应中
        if payload in response.text:
            return True
        
        # 检查常见的XSS标签是否存在
        xss_indicators = ['<script', '<img', '<svg', '<iframe', 'javascript:', 'onerror=', 'onload=']
        response_lower = response.text.lower()
        
        for indicator in xss_indicators:
            if indicator in response_lower and indicator in payload.lower():
                return True
        
        return False
    
    def _analyze_dom_xss_pattern(self, js_content: str, pattern: str) -> bool:
        """分析DOM XSS模式"""
        # 简单的静态分析，检查是否存在不安全的DOM操作
        lines = js_content.split('\n')
        
        for line in lines:
            if pattern in line:
                # 检查是否直接使用用户输入
                if any(keyword in line.lower() for keyword in ['location.', 'url', 'param', 'query', 'hash']):
                    return True
        
        return False
    
    # ==================== 数据泄露和隐私测试 ====================
    
    def test_data_exposure_vulnerabilities(self) -> SecurityTestResult:
        """测试数据泄露漏洞 (OWASP A01:2021)"""
        start_time = time.time()
        result = SecurityTestResult(
            test_name="数据泄露漏洞测试",
            test_category="OWASP_A01_Data_Exposure",
            success=True
        )
        
        try:
            # 敏感信息泄露测试
            self._test_sensitive_data_exposure(result)
            
            # 目录遍历测试
            self._test_directory_traversal(result)
            
            # 备份文件泄露测试
            self._test_backup_file_exposure(result)
            
            # API信息泄露测试
            self._test_api_information_disclosure(result)
            
            # 错误信息泄露测试
            self._test_error_information_disclosure(result)
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            logger.error(f"数据泄露漏洞测试异常: {e}")
        
        result.execution_time = time.time() - start_time
        self.test_results.append(result)
        return result
    
    def _test_sensitive_data_exposure(self, result: SecurityTestResult):
        """敏感数据泄露测试"""
        sensitive_endpoints = [
            "/api/config",
            "/api/admin/config",
            "/api/database/config",
            "/api/env",
            "/api/debug",
            "/api/logs",
            "/api/users/all",
            "/api/internal/status"
        ]
        
        for endpoint in sensitive_endpoints:
            try:
                response = self.session.get(
                    f"{self.target_url}{endpoint}",
                    timeout=10
                )
                
                if response.status_code == 200:
                    # 检查响应中是否包含敏感信息
                    sensitive_patterns = [
                        r"password[""'\s]*[:=][""'\s]*[^\s,}]+",
                        r"api[_-]?key[""'\s]*[:=][""'\s]*[^\s,}]+",
                        r"secret[""'\s]*[:=][""'\s]*[^\s,}]+",
                        r"token[""'\s]*[:=][""'\s]*[^\s,}]+",
                        r"database[_-]?url[""'\s]*[:=][""'\s]*[^\s,}]+",
                        r"connection[_-]?string[""'\s]*[:=][""'\s]*[^\s,}]+"
                    ]
                    
                    for pattern in sensitive_patterns:
                        matches = re.findall(pattern, response.text, re.IGNORECASE)
                        if matches:
                            vuln = SecurityVulnerability(
                                vulnerability_id=f"SENSITIVE_DATA_{endpoint}_{hashlib.md5(pattern.encode()).hexdigest()[:8]}",
                                title=f"敏感数据泄露 - {endpoint}",
                                severity="high",
                                category="data_exposure",
                                description=f"端点 {endpoint} 泄露敏感信息",
                                affected_component=endpoint,
                                proof_of_concept=f"发现敏感信息: {matches[0][:50]}...",
                                remediation="移除敏感信息或限制访问权限",
                                cvss_score=8.5,
                                cwe_id="CWE-200"
                            )
                            self.add_vulnerability(vuln)
                            result.vulnerabilities.append(vuln)
            
            except requests.RequestException as e:
                logger.debug(f"敏感数据测试异常 {endpoint}: {e}")
    
    def _test_directory_traversal(self, result: SecurityTestResult):
        """目录遍历测试"""
        traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd",
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd"
        ]
        
        test_endpoints = [
            "/api/files/download",
            "/api/static/file",
            "/api/export/file",
            "/api/logs/view"
        ]
        
        for endpoint in test_endpoints:
            for payload in traversal_payloads:
                try:
                    response = self.session.get(
                        f"{self.target_url}{endpoint}",
                        params={'file': payload, 'path': payload, 'filename': payload},
                        timeout=10
                    )
                    
                    if self._detect_directory_traversal_response(response):
                        vuln = SecurityVulnerability(
                            vulnerability_id=f"DIR_TRAVERSAL_{endpoint}_{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                            title=f"目录遍历漏洞 - {endpoint}",
                            severity="high",
                            category="data_exposure",
                            description=f"端点 {endpoint} 存在目录遍历漏洞",
                            affected_component=endpoint,
                            proof_of_concept=f"Payload: {payload}\nResponse: {response.text[:200]}",
                            remediation="验证和限制文件路径访问",
                            cvss_score=8.0,
                            cwe_id="CWE-22"
                        )
                        self.add_vulnerability(vuln)
                        result.vulnerabilities.append(vuln)
                
                except requests.RequestException as e:
                    logger.debug(f"目录遍历测试异常 {endpoint}: {e}")
    
    def _detect_directory_traversal_response(self, response: requests.Response) -> bool:
        """检测目录遍历响应特征"""
        response_text = response.text.lower()
        
        # Unix/Linux系统文件特征
        unix_patterns = [
            r"root:x:0:0:",  # /etc/passwd内容
            r"bin:x:1:1:",
            r"daemon:x:2:2:",
            r"localhost",  # hosts文件内容
            r"127.0.0.1"
        ]
        
        # Windows系统文件特征
        windows_patterns = [
            r"# copyright.*microsoft",
            r"# this is a sample hosts file",
            r"localhost.*127.0.0.1"
        ]
        
        all_patterns = unix_patterns + windows_patterns
        
        for pattern in all_patterns:
            if re.search(pattern, response_text, re.IGNORECASE):
                return True
        
        return False
    
    def _test_backup_file_exposure(self, result: SecurityTestResult):
        """备份文件泄露测试"""
        backup_files = [
            "/.env",
            "/.env.backup",
            "/config.php.bak",
            "/database.sql",
            "/backup.sql",
            "/dump.sql",
            "/web.config",
            "/.htaccess",
            "/robots.txt",
            "/sitemap.xml",
            "/.git/config",
            "/.svn/entries",
            "/package.json",
            "/composer.json",
            "/requirements.txt"
        ]
        
        for file_path in backup_files:
            try:
                response = self.session.get(
                    f"{self.target_url}{file_path}",
                    timeout=10
                )
                
                if response.status_code == 200 and len(response.text) > 10:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"BACKUP_EXPOSURE_{file_path.replace('/', '_')}",
                        title=f"备份文件泄露 - {file_path}",
                        severity="medium",
                        category="data_exposure",
                        description=f"敏感文件 {file_path} 可被公开访问",
                        affected_component=file_path,
                        proof_of_concept=f"文件内容: {response.text[:100]}...",
                        remediation="移除或保护敏感文件",
                        cvss_score=6.5,
                        cwe_id="CWE-200"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
            
            except requests.RequestException as e:
                logger.debug(f"备份文件测试异常 {file_path}: {e}")
    
    def _test_api_information_disclosure(self, result: SecurityTestResult):
        """API信息泄露测试"""
        info_endpoints = [
            "/api/version",
            "/api/info",
            "/api/health",
            "/api/status",
            "/api/metrics",
            "/api/swagger.json",
            "/api/openapi.json",
            "/api/docs",
            "/api/debug"
        ]
        
        for endpoint in info_endpoints:
            try:
                response = self.session.get(
                    f"{self.target_url}{endpoint}",
                    timeout=10
                )
                
                if response.status_code == 200:
                    # 检查是否泄露了过多的系统信息
                    sensitive_info_patterns = [
                        r"version[""'\s]*[:=][""'\s]*[\d\.]+",
                        r"database[""'\s]*[:=]",
                        r"server[""'\s]*[:=]",
                        r"os[""'\s]*[:=]",
                        r"python[""'\s]*[:=]",
                        r"node[""'\s]*[:=]",
                        r"memory[""'\s]*[:=]",
                        r"cpu[""'\s]*[:=]"
                    ]
                    
                    disclosed_info = []
                    for pattern in sensitive_info_patterns:
                        matches = re.findall(pattern, response.text, re.IGNORECASE)
                        disclosed_info.extend(matches)
                    
                    if disclosed_info:
                        vuln = SecurityVulnerability(
                            vulnerability_id=f"API_INFO_DISCLOSURE_{endpoint.replace('/', '_')}",
                            title=f"API信息泄露 - {endpoint}",
                            severity="low",
                            category="data_exposure",
                            description=f"端点 {endpoint} 泄露系统信息",
                            affected_component=endpoint,
                            proof_of_concept=f"泄露信息: {disclosed_info[:3]}",
                            remediation="限制系统信息的公开访问",
                            cvss_score=3.0,
                            cwe_id="CWE-200"
                        )
                        self.add_vulnerability(vuln)
                        result.vulnerabilities.append(vuln)
            
            except requests.RequestException as e:
                logger.debug(f"API信息泄露测试异常 {endpoint}: {e}")
    
    def _test_error_information_disclosure(self, result: SecurityTestResult):
        """错误信息泄露测试"""
        error_payloads = [
            "'""'""'",  # 引号错误
            "999999999999999999999",  # 数值溢出
            "../../../etc/passwd",  # 路径错误
            "<script>alert(1)</script>",  # 脚本注入
            "SELECT * FROM users",  # SQL语句
            "<?php phpinfo(); ?>",  # PHP代码
        ]
        
        test_endpoints = [
            "/api/users/1",
            "/api/sites/search",
            "/api/data/export",
            "/api/auth/login"
        ]
        
        for endpoint in test_endpoints:
            for payload in error_payloads:
                try:
                    response = self.session.get(
                        f"{self.target_url}{endpoint}",
                        params={'id': payload, 'data': payload},
                        timeout=10
                    )
                    
                    if self._detect_error_information_disclosure(response):
                        vuln = SecurityVulnerability(
                            vulnerability_id=f"ERROR_DISCLOSURE_{endpoint}_{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                            title=f"错误信息泄露 - {endpoint}",
                            severity="low",
                            category="data_exposure",
                            description=f"端点 {endpoint} 泄露详细错误信息",
                            affected_component=endpoint,
                            proof_of_concept=f"Payload: {payload}\nError: {response.text[:200]}",
                            remediation="使用通用错误消息，记录详细错误到日志",
                            cvss_score=3.5,
                            cwe_id="CWE-209"
                        )
                        self.add_vulnerability(vuln)
                        result.vulnerabilities.append(vuln)
                
                except requests.RequestException as e:
                    logger.debug(f"错误信息泄露测试异常 {endpoint}: {e}")
    
    def _detect_error_information_disclosure(self, response: requests.Response) -> bool:
        """检测错误信息泄露特征"""
        if response.status_code not in [400, 500]:
            return False
        
        error_patterns = [
            r"traceback",
            r"stack trace",
            r"file.*line \d+",
            r"exception.*at",
            r"error.*in.*line",
            r"mysql.*error",
            r"postgresql.*error",
            r"sqlite.*error",
            r"oracle.*error",
            r"sqlserver.*error",
            r"php.*error",
            r"python.*error",
            r"java.*exception",
            r"c#.*exception",
            r"node.*error"
        ]
        
        response_text = response.text.lower()
        for pattern in error_patterns:
            if re.search(pattern, response_text, re.IGNORECASE):
                return True
        
        return False
    
    # ==================== 安全配置测试 ====================
    
    def test_security_configuration(self) -> SecurityTestResult:
        """测试安全配置漏洞 (OWASP A05:2021)"""
        start_time = time.time()
        result = SecurityTestResult(
            test_name="安全配置测试",
            test_category="OWASP_A05_Security_Misconfiguration",
            success=True
        )
        
        try:
            # HTTP安全头测试
            self._test_security_headers(result)
            
            # SSL/TLS配置测试
            self._test_ssl_configuration(result)
            
            # CORS配置测试
            self._test_cors_configuration(result)
            
            # 默认凭据测试
            self._test_default_credentials(result)
            
            # 目录列表测试
            self._test_directory_listing(result)
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            logger.error(f"安全配置测试异常: {e}")
        
        result.execution_time = time.time() - start_time
        self.test_results.append(result)
        return result
    
    def _test_security_headers(self, result: SecurityTestResult):
        """HTTP安全头测试"""
        try:
            response = self.session.get(f"{self.target_url}/", timeout=10)
            headers = response.headers
            
            # 检查必需的安全头
            required_headers = {
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': ['DENY', 'SAMEORIGIN'],
                'X-XSS-Protection': '1; mode=block',
                'Strict-Transport-Security': None,  # 任何值都可以
                'Content-Security-Policy': None,
                'Referrer-Policy': None
            }
            
            for header_name, expected_values in required_headers.items():
                if header_name not in headers:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"MISSING_HEADER_{header_name}",
                        title=f"缺少安全头 - {header_name}",
                        severity="medium",
                        category="security_misconfiguration",
                        description=f"响应中缺少安全头 {header_name}",
                        affected_component="HTTP响应头",
                        proof_of_concept=f"缺少头: {header_name}",
                        remediation=f"添加 {header_name} 安全头",
                        cvss_score=5.0,
                        cwe_id="CWE-693"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
                elif expected_values and headers[header_name] not in expected_values:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"WEAK_HEADER_{header_name}",
                        title=f"弱安全头配置 - {header_name}",
                        severity="low",
                        category="security_misconfiguration",
                        description=f"安全头 {header_name} 配置不当",
                        affected_component="HTTP响应头",
                        proof_of_concept=f"当前值: {headers[header_name]}",
                        remediation=f"正确配置 {header_name} 安全头",
                        cvss_score=3.0,
                        cwe_id="CWE-693"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
            
            # 检查有害的头
            harmful_headers = ['Server', 'X-Powered-By', 'X-AspNet-Version']
            for header_name in harmful_headers:
                if header_name in headers:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"INFO_DISCLOSURE_HEADER_{header_name}",
                        title=f"信息泄露头 - {header_name}",
                        severity="low",
                        category="security_misconfiguration",
                        description=f"响应头 {header_name} 泄露服务器信息",
                        affected_component="HTTP响应头",
                        proof_of_concept=f"泄露信息: {headers[header_name]}",
                        remediation=f"移除或隐藏 {header_name} 头",
                        cvss_score=2.0,
                        cwe_id="CWE-200"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
        
        except requests.RequestException as e:
            logger.debug(f"安全头测试异常: {e}")
    
    def _test_ssl_configuration(self, result: SecurityTestResult):
        """SSL/TLS配置测试"""
        if not self.target_url.startswith('https'):
            vuln = SecurityVulnerability(
                vulnerability_id="NO_HTTPS",
                title="未使用HTTPS",
                severity="high",
                category="security_misconfiguration",
                description="应用程序未使用HTTPS加密传输",
                affected_component="传输层",
                proof_of_concept="URL使用HTTP协议",
                remediation="启用HTTPS并重定向HTTP流量",
                cvss_score=7.5,
                cwe_id="CWE-319"
            )
            self.add_vulnerability(vuln)
            result.vulnerabilities.append(vuln)
            return
        
        try:
            # 解析URL获取主机和端口
            parsed_url = urlparse(self.target_url)
            hostname = parsed_url.hostname
            port = parsed_url.port or 443
            
            # 创建SSL上下文
            context = ssl.create_default_context()
            
            # 连接并获取证书信息
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    cipher = ssock.cipher()
                    
                    # 检查证书有效期
                    not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                    if not_after < datetime.now() + timedelta(days=30):
                        vuln = SecurityVulnerability(
                            vulnerability_id="SSL_CERT_EXPIRING",
                            title="SSL证书即将过期",
                            severity="medium",
                            category="security_misconfiguration",
                            description="SSL证书将在30天内过期",
                            affected_component="SSL证书",
                            proof_of_concept=f"过期时间: {not_after}",
                            remediation="更新SSL证书",
                            cvss_score=5.0,
                            cwe_id="CWE-295"
                        )
                        self.add_vulnerability(vuln)
                        result.vulnerabilities.append(vuln)
                    
                    # 检查弱加密套件
                    if cipher and len(cipher) >= 3:
                        cipher_name = cipher[0]
                        weak_ciphers = ['RC4', 'DES', '3DES', 'MD5', 'SHA1']
                        
                        for weak_cipher in weak_ciphers:
                            if weak_cipher in cipher_name:
                                vuln = SecurityVulnerability(
                                    vulnerability_id=f"WEAK_CIPHER_{weak_cipher}",
                                    title=f"弱加密套件 - {weak_cipher}",
                                    severity="medium",
                                    category="security_misconfiguration",
                                    description=f"使用弱加密套件: {cipher_name}",
                                    affected_component="SSL/TLS配置",
                                    proof_of_concept=f"加密套件: {cipher_name}",
                                    remediation="禁用弱加密套件，使用强加密算法",
                                    cvss_score=6.0,
                                    cwe_id="CWE-327"
                                )
                                self.add_vulnerability(vuln)
                                result.vulnerabilities.append(vuln)
                                break
        
        except Exception as e:
            logger.debug(f"SSL配置测试异常: {e}")
    
    def _test_cors_configuration(self, result: SecurityTestResult):
        """CORS配置测试"""
        try:
            # 测试通配符CORS
            response = self.session.options(
                f"{self.target_url}/api/users",
                headers={'Origin': 'https://evil.com'},
                timeout=10
            )
            
            cors_headers = {
                'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials'),
                'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
            }
            
            # 检查危险的CORS配置
            if cors_headers['Access-Control-Allow-Origin'] == '*':
                if cors_headers['Access-Control-Allow-Credentials'] == 'true':
                    vuln = SecurityVulnerability(
                        vulnerability_id="CORS_WILDCARD_WITH_CREDENTIALS",
                        title="危险的CORS配置",
                        severity="high",
                        category="security_misconfiguration",
                        description="CORS配置允许任意域名且支持凭据",
                        affected_component="CORS配置",
                        proof_of_concept="Access-Control-Allow-Origin: * 且 Access-Control-Allow-Credentials: true",
                        remediation="限制CORS域名或禁用凭据支持",
                        cvss_score=8.0,
                        cwe_id="CWE-942"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
                else:
                    vuln = SecurityVulnerability(
                        vulnerability_id="CORS_WILDCARD",
                        title="宽松的CORS配置",
                        severity="medium",
                        category="security_misconfiguration",
                        description="CORS配置允许任意域名访问",
                        affected_component="CORS配置",
                        proof_of_concept="Access-Control-Allow-Origin: *",
                        remediation="限制CORS允许的域名",
                        cvss_score=5.0,
                        cwe_id="CWE-942"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
        
        except requests.RequestException as e:
            logger.debug(f"CORS配置测试异常: {e}")
    
    def _test_default_credentials(self, result: SecurityTestResult):
        """默认凭据测试"""
        default_creds = [
            ('admin', 'admin'),
            ('admin', 'password'),
            ('admin', '123456'),
            ('root', 'root'),
            ('root', 'password'),
            ('administrator', 'administrator'),
            ('guest', 'guest'),
            ('test', 'test'),
            ('demo', 'demo'),
            ('user', 'user')
        ]
        
        for username, password in default_creds:
            try:
                response = self.session.post(
                    f"{self.target_url}/api/auth/login",
                    json={'username': username, 'password': password},
                    timeout=10
                )
                
                if response.status_code == 200 and ('token' in response.text or 'success' in response.text):
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"DEFAULT_CREDS_{username}_{password}",
                        title=f"默认凭据 - {username}/{password}",
                        severity="critical",
                        category="security_misconfiguration",
                        description=f"系统使用默认凭据: {username}/{password}",
                        affected_component="认证系统",
                        proof_of_concept=f"成功使用默认凭据登录: {username}/{password}",
                        remediation="更改所有默认密码",
                        cvss_score=9.8,
                        cwe_id="CWE-798"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
            
            except requests.RequestException as e:
                logger.debug(f"默认凭据测试异常: {e}")
    
    def _test_directory_listing(self, result: SecurityTestResult):
        """目录列表测试"""
        test_directories = [
            "/static/",
            "/assets/",
            "/uploads/",
            "/files/",
            "/images/",
            "/docs/",
            "/backup/",
            "/admin/",
            "/api/"
        ]
        
        for directory in test_directories:
            try:
                response = self.session.get(
                    f"{self.target_url}{directory}",
                    timeout=10
                )
                
                if self._detect_directory_listing(response):
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"DIR_LISTING_{directory.replace('/', '_')}",
                        title=f"目录列表泄露 - {directory}",
                        severity="low",
                        category="security_misconfiguration",
                        description=f"目录 {directory} 允许列表浏览",
                        affected_component=directory,
                        proof_of_concept=f"目录列表可访问: {directory}",
                        remediation="禁用目录列表功能",
                        cvss_score=3.0,
                        cwe_id="CWE-548"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
            
            except requests.RequestException as e:
                logger.debug(f"目录列表测试异常 {directory}: {e}")
    
    def _detect_directory_listing(self, response: requests.Response) -> bool:
        """检测目录列表特征"""
        if response.status_code != 200:
            return False
        
        listing_patterns = [
            r"index of",
            r"directory listing",
            r"parent directory",
            r"\[dir\]",
            r"<a href=.*/>.*</a>",
            r"last modified.*size.*description"
        ]
        
        response_text = response.text.lower()
        for pattern in listing_patterns:
            if re.search(pattern, response_text, re.IGNORECASE):
                return True
        
        return False
    
    # ==================== 业务逻辑测试 ====================
    
    def test_business_logic_vulnerabilities(self) -> SecurityTestResult:
        """测试业务逻辑漏洞"""
        start_time = time.time()
        result = SecurityTestResult(
            test_name="业务逻辑漏洞测试",
            test_category="Business_Logic",
            success=True
        )
        
        try:
            # 权限提升测试
            self._test_privilege_escalation(result)
            
            # 业务流程绕过测试
            self._test_workflow_bypass(result)
            
            # 数据完整性测试
            self._test_data_integrity(result)
            
            # 竞态条件测试
            self._test_race_conditions(result)
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            logger.error(f"业务逻辑漏洞测试异常: {e}")
        
        result.execution_time = time.time() - start_time
        self.test_results.append(result)
        return result
    
    def _test_privilege_escalation(self, result: SecurityTestResult):
        """权限提升测试"""
        try:
            # 尝试访问管理员功能
            admin_endpoints = [
                "/api/admin/users",
                "/api/admin/settings",
                "/api/admin/logs",
                "/api/admin/backup"
            ]
            
            for endpoint in admin_endpoints:
                response = self.session.get(
                    f"{self.target_url}{endpoint}",
                    timeout=10
                )
                
                if response.status_code == 200:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"PRIVILEGE_ESCALATION_{endpoint}",
                        title=f"权限提升漏洞 - {endpoint}",
                        severity="high",
                        category="business_logic",
                        description=f"普通用户可访问管理员端点: {endpoint}",
                        affected_component=endpoint,
                        proof_of_concept=f"未授权访问成功: {endpoint}",
                        remediation="实施严格的权限控制",
                        cvss_score=8.5,
                        cwe_id="CWE-269"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
        
        except requests.RequestException as e:
            logger.debug(f"权限提升测试异常: {e}")
    
    def _test_workflow_bypass(self, result: SecurityTestResult):
        """业务流程绕过测试"""
        try:
            # 测试跳过验证步骤
            bypass_tests = [
                {
                    'name': '跳过邮箱验证',
                    'endpoint': '/api/users/activate',
                    'method': 'POST',
                    'data': {'user_id': 1, 'verified': True}
                },
                {
                    'name': '跳过支付验证',
                    'endpoint': '/api/orders/complete',
                    'method': 'POST',
                    'data': {'order_id': 1, 'paid': True}
                }
            ]
            
            for test in bypass_tests:
                if test['method'] == 'POST':
                    response = self.session.post(
                        f"{self.target_url}{test['endpoint']}",
                        json=test['data'],
                        timeout=10
                    )
                else:
                    response = self.session.get(
                        f"{self.target_url}{test['endpoint']}",
                        timeout=10
                    )
                
                if response.status_code == 200:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"WORKFLOW_BYPASS_{test['name']}",
                        title=f"业务流程绕过 - {test['name']}",
                        severity="high",
                        category="business_logic",
                        description=f"可以绕过业务流程: {test['name']}",
                        affected_component=test['endpoint'],
                        proof_of_concept=f"成功绕过: {test['name']}",
                        remediation="加强业务流程验证",
                        cvss_score=7.5,
                        cwe_id="CWE-840"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
        
        except requests.RequestException as e:
            logger.debug(f"业务流程绕过测试异常: {e}")
    
    def _test_data_integrity(self, result: SecurityTestResult):
        """数据完整性测试"""
        try:
            # 测试负数金额
            negative_amount_test = {
                'endpoint': '/api/transactions/create',
                'data': {'amount': -100, 'type': 'deposit'}
            }
            
            response = self.session.post(
                f"{self.target_url}{negative_amount_test['endpoint']}",
                json=negative_amount_test['data'],
                timeout=10
            )
            
            if response.status_code == 200:
                vuln = SecurityVulnerability(
                    vulnerability_id="NEGATIVE_AMOUNT_ACCEPTED",
                    title="接受负数金额",
                    severity="medium",
                    category="business_logic",
                    description="系统接受负数金额交易",
                    affected_component="/api/transactions/create",
                    proof_of_concept="成功创建负数金额交易",
                    remediation="添加金额验证逻辑",
                    cvss_score=6.0,
                    cwe_id="CWE-20"
                )
                self.add_vulnerability(vuln)
                result.vulnerabilities.append(vuln)
        
        except requests.RequestException as e:
            logger.debug(f"数据完整性测试异常: {e}")
    
    def _test_race_conditions(self, result: SecurityTestResult):
        """竞态条件测试"""
        try:
            # 并发请求测试
            import threading
            import queue
            
            results_queue = queue.Queue()
            
            def concurrent_request():
                try:
                    response = self.session.post(
                        f"{self.target_url}/api/users/credit",
                        json={'amount': 100},
                        timeout=10
                    )
                    results_queue.put(response.status_code)
                except:
                    results_queue.put(None)
            
            # 启动多个并发线程
            threads = []
            for _ in range(5):
                thread = threading.Thread(target=concurrent_request)
                threads.append(thread)
                thread.start()
            
            # 等待所有线程完成
            for thread in threads:
                thread.join()
            
            # 检查结果
            success_count = 0
            while not results_queue.empty():
                status_code = results_queue.get()
                if status_code == 200:
                    success_count += 1
            
            # 如果多个请求都成功，可能存在竞态条件
            if success_count > 1:
                vuln = SecurityVulnerability(
                    vulnerability_id="RACE_CONDITION_CREDIT",
                    title="竞态条件漏洞",
                    severity="medium",
                    category="business_logic",
                    description="并发请求可能导致竞态条件",
                    affected_component="/api/users/credit",
                    proof_of_concept=f"{success_count} 个并发请求成功",
                    remediation="实施事务锁定和原子操作",
                    cvss_score=5.5,
                    cwe_id="CWE-362"
                )
                self.add_vulnerability(vuln)
                result.vulnerabilities.append(vuln)
        
        except Exception as e:
            logger.debug(f"竞态条件测试异常: {e}")
    
    # ==================== 合规性测试 ====================
    
    def test_compliance_requirements(self) -> SecurityTestResult:
        """测试合规性要求"""
        start_time = time.time()
        result = SecurityTestResult(
            test_name="合规性测试",
            test_category="Compliance",
            success=True
        )
        
        try:
            # GDPR合规性测试
            self._test_gdpr_compliance(result)
            
            # 数据保护测试
            self._test_data_protection(result)
            
            # 审计日志测试
            self._test_audit_logging(result)
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            logger.error(f"合规性测试异常: {e}")
        
        result.execution_time = time.time() - start_time
        self.test_results.append(result)
        return result
    
    def _test_gdpr_compliance(self, result: SecurityTestResult):
        """GDPR合规性测试"""
        gdpr_endpoints = [
            '/api/privacy/policy',
            '/api/users/data-export',
            '/api/users/data-delete',
            '/api/consent/manage'
        ]
        
        for endpoint in gdpr_endpoints:
            try:
                response = self.session.get(
                    f"{self.target_url}{endpoint}",
                    timeout=10
                )
                
                if response.status_code == 404:
                    vuln = SecurityVulnerability(
                        vulnerability_id=f"GDPR_MISSING_{endpoint}",
                        title=f"GDPR功能缺失 - {endpoint}",
                        severity="medium",
                        category="compliance",
                        description=f"缺少GDPR要求的功能: {endpoint}",
                        affected_component=endpoint,
                        proof_of_concept=f"端点不存在: {endpoint}",
                        remediation="实施GDPR要求的数据主体权利",
                        cvss_score=5.0,
                        cwe_id="CWE-200"
                    )
                    self.add_vulnerability(vuln)
                    result.vulnerabilities.append(vuln)
            
            except requests.RequestException as e:
                logger.debug(f"GDPR合规性测试异常 {endpoint}: {e}")
    
    def _test_data_protection(self, result: SecurityTestResult):
        """数据保护测试"""
        try:
            # 测试个人数据是否加密存储
            response = self.session.get(
                f"{self.target_url}/api/users/profile",
                timeout=10
            )
            
            if response.status_code == 200:
                profile_data = response.json()
                
                # 检查敏感字段是否明文存储
                sensitive_fields = ['password', 'ssn', 'credit_card', 'phone', 'email']
                
                for field in sensitive_fields:
                    if field in profile_data:
                        field_value = str(profile_data[field])
                        
                        # 简单检查是否为明文（长度和格式）
                        if self._is_plaintext_sensitive_data(field, field_value):
                            vuln = SecurityVulnerability(
                                vulnerability_id=f"PLAINTEXT_DATA_{field}",
                                title=f"敏感数据明文存储 - {field}",
                                severity="high",
                                category="data_protection",
                                description=f"敏感字段 {field} 可能以明文形式存储",
                                affected_component="数据存储",
                                proof_of_concept=f"字段 {field} 疑似明文存储",
                                remediation="对敏感数据进行加密存储",
                                cvss_score=8.0,
                                cwe_id="CWE-312"
                            )
                            self.add_vulnerability(vuln)
                            result.vulnerabilities.append(vuln)
        
        except Exception as e:
            logger.debug(f"数据保护测试异常: {e}")
    
    def _is_plaintext_sensitive_data(self, field: str, value: str) -> bool:
        """检查是否为明文敏感数据"""
        if field == 'password':
            # 密码应该是哈希值，通常很长且包含特殊字符
            return len(value) < 20 or not any(c in value for c in '$#@!*')
        elif field == 'ssn':
            # SSN格式检查
            return re.match(r'^\d{3}-\d{2}-\d{4}$', value) is not None
        elif field == 'credit_card':
            # 信用卡号格式检查
            return re.match(r'^\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}$', value) is not None
        elif field in ['phone', 'email']:
            # 电话和邮箱通常不加密，但应该有访问控制
            return True
        
        return False
    
    def _test_audit_logging(self, result: SecurityTestResult):
        """审计日志测试"""
        try:
            # 执行一些需要审计的操作
            audit_actions = [
                {'method': 'POST', 'endpoint': '/api/auth/login', 'data': {'username': 'test', 'password': 'test'}},
                {'method': 'GET', 'endpoint': '/api/admin/users', 'data': None},
                {'method': 'POST', 'endpoint': '/api/users/delete', 'data': {'user_id': 999}}
            ]
            
            for action in audit_actions:
                if action['method'] == 'POST':
                    self.session.post(
                        f"{self.target_url}{action['endpoint']}",
                        json=action['data'],
                        timeout=10
                    )
                else:
                    self.session.get(
                        f"{self.target_url}{action['endpoint']}",
                        timeout=10
                    )
            
            # 检查是否有审计日志端点
            audit_response = self.session.get(
                f"{self.target_url}/api/admin/audit-logs",
                timeout=10
            )
            
            if audit_response.status_code == 404:
                vuln = SecurityVulnerability(
                    vulnerability_id="MISSING_AUDIT_LOGS",
                    title="缺少审计日志功能",
                    severity="medium",
                    category="compliance",
                    description="系统缺少审计日志记录功能",
                    affected_component="审计系统",
                    proof_of_concept="审计日志端点不存在",
                    remediation="实施完整的审计日志系统",
                    cvss_score=5.5,
                    cwe_id="CWE-778"
                )
                self.add_vulnerability(vuln)
                result.vulnerabilities.append(vuln)
        
        except Exception as e:
            logger.debug(f"审计日志测试异常: {e}")
    
    # ==================== 报告生成 ====================
    
    def generate_security_report(self) -> Dict[str, Any]:
        """生成安全测试报告"""
        total_vulnerabilities = len(self.vulnerabilities)
        
        # 按严重程度统计
        severity_counts = {
            'critical': len([v for v in self.vulnerabilities if v.severity == 'critical']),
            'high': len([v for v in self.vulnerabilities if v.severity == 'high']),
            'medium': len([v for v in self.vulnerabilities if v.severity == 'medium']),
            'low': len([v for v in self.vulnerabilities if v.severity == 'low'])
        }
        
        # 按类别统计
        category_counts = {}
        for vuln in self.vulnerabilities:
            category_counts[vuln.category] = category_counts.get(vuln.category, 0) + 1
        
        # 计算风险评分
        risk_score = self._calculate_risk_score()
        
        # 生成建议
        recommendations = self._generate_recommendations()
        
        report = {
            'scan_summary': {
                'target_url': self.target_url,
                'scan_date': datetime.now().isoformat(),
                'total_vulnerabilities': total_vulnerabilities,
                'risk_score': risk_score,
                'severity_distribution': severity_counts,
                'category_distribution': category_counts
            },
            'test_results': [
                {
                    'test_name': result.test_name,
                    'test_category': result.test_category,
                    'success': result.success,
                    'execution_time': result.execution_time,
                    'vulnerabilities_found': len(result.vulnerabilities),
                    'error_message': result.error_message
                }
                for result in self.test_results
            ],
            'vulnerabilities': [
                {
                    'id': vuln.vulnerability_id,
                    'title': vuln.title,
                    'severity': vuln.severity,
                    'category': vuln.category,
                    'description': vuln.description,
                    'affected_component': vuln.affected_component,
                    'proof_of_concept': vuln.proof_of_concept,
                    'remediation': vuln.remediation,
                    'cvss_score': vuln.cvss_score,
                    'cwe_id': vuln.cwe_id,
                    'discovered_at': vuln.discovered_at.isoformat(),
                    'status': vuln.status
                }
                for vuln in self.vulnerabilities
            ],
            'recommendations': recommendations,
            'compliance_status': self._check_compliance_status()
        }
        
        return report
    
    def _calculate_risk_score(self) -> float:
        """计算总体风险评分"""
        if not self.vulnerabilities:
            return 0.0
        
        severity_weights = {
            'critical': 10.0,
            'high': 7.5,
            'medium': 5.0,
            'low': 2.5
        }
        
        total_score = sum(
            severity_weights.get(vuln.severity, 0) for vuln in self.vulnerabilities
        )
        
        # 标准化到0-100分
        max_possible_score = len(self.vulnerabilities) * 10.0
        return min(100.0, (total_score / max_possible_score) * 100) if max_possible_score > 0 else 0.0
    
    def _generate_recommendations(self) -> List[str]:
        """生成安全建议"""
        recommendations = []
        
        # 基于发现的漏洞类型生成建议
        categories = set(vuln.category for vuln in self.vulnerabilities)
        
        if 'injection' in categories:
            recommendations.append("实施输入验证和参数化查询以防止注入攻击")
        
        if 'authentication' in categories:
            recommendations.append("加强认证机制，实施多因素认证和强密码策略")
        
        if 'xss' in categories:
            recommendations.append("对所有用户输入进行适当的编码和过滤")
        
        if 'security_misconfiguration' in categories:
            recommendations.append("审查和加强安全配置，移除默认凭据")
        
        if 'data_exposure' in categories:
            recommendations.append("限制敏感信息的访问，实施适当的访问控制")
        
        if not recommendations:
            recommendations.append("继续保持良好的安全实践")
        
        return recommendations
    
    def _check_compliance_status(self) -> Dict[str, bool]:
        """检查合规性状态"""
        compliance_status = {
            'gdpr_compliant': True,
            'security_headers_present': True,
            'encryption_in_transit': True,
            'audit_logging_enabled': True,
            'access_controls_implemented': True
        }
        
        # 基于发现的漏洞更新合规性状态
        for vuln in self.vulnerabilities:
            if 'gdpr' in vuln.vulnerability_id.lower():
                compliance_status['gdpr_compliant'] = False
            
            if 'header' in vuln.vulnerability_id.lower():
                compliance_status['security_headers_present'] = False
            
            if 'https' in vuln.vulnerability_id.lower() or 'ssl' in vuln.vulnerability_id.lower():
                compliance_status['encryption_in_transit'] = False
            
            if 'audit' in vuln.vulnerability_id.lower():
                compliance_status['audit_logging_enabled'] = False
            
            if 'privilege' in vuln.vulnerability_id.lower() or 'access' in vuln.vulnerability_id.lower():
                compliance_status['access_controls_implemented'] = False
        
        return compliance_status


class TestAdvancedSecurityScanning:
    """高级安全扫描测试类"""
    
    @pytest.fixture
    def security_scanner(self):
        """创建安全扫描器实例"""
        return AdvancedSecurityScanner(
            target_url=os.getenv('TEST_TARGET_URL', 'http://localhost:8000'),
            api_key=os.getenv('TEST_API_KEY')
        )
    
    @pytest.mark.security
    @pytest.mark.slow
    def test_injection_vulnerabilities(self, security_scanner):
        """测试注入漏洞"""
        result = security_scanner.test_injection_vulnerabilities()
        
        assert result.success, f"注入漏洞测试失败: {result.error_message}"
        
        # 记录发现的漏洞
        if result.vulnerabilities:
            logger.warning(f"发现 {len(result.vulnerabilities)} 个注入漏洞")
            for vuln in result.vulnerabilities:
                logger.warning(f"  - {vuln.title} ({vuln.severity})")
        
        # 在生产环境中，应该断言没有发现漏洞
        # assert len(result.vulnerabilities) == 0, "发现注入漏洞"
    
    @pytest.mark.security
    @pytest.mark.slow
    def test_authentication_vulnerabilities(self, security_scanner):
        """测试认证漏洞"""
        result = security_scanner.test_authentication_vulnerabilities()
        
        assert result.success, f"认证漏洞测试失败: {result.error_message}"
        
        if result.vulnerabilities:
            logger.warning(f"发现 {len(result.vulnerabilities)} 个认证漏洞")
            for vuln in result.vulnerabilities:
                logger.warning(f"  - {vuln.title} ({vuln.severity})")
    
    @pytest.mark.security
    def test_xss_vulnerabilities(self, security_scanner):
        """测试XSS漏洞"""
        result = security_scanner.test_xss_vulnerabilities()
        
        assert result.success, f"XSS漏洞测试失败: {result.error_message}"
        
        if result.vulnerabilities:
            logger.warning(f"发现 {len(result.vulnerabilities)} 个XSS漏洞")
            for vuln in result.vulnerabilities:
                logger.warning(f"  - {vuln.title} ({vuln.severity})")
    
    @pytest.mark.security
    def test_data_exposure_vulnerabilities(self, security_scanner):
        """测试数据泄露漏洞"""
        result = security_scanner.test_data_exposure_vulnerabilities()
        
        assert result.success, f"数据泄露漏洞测试失败: {result.error_message}"
        
        if result.vulnerabilities:
            logger.warning(f"发现 {len(result.vulnerabilities)} 个数据泄露漏洞")
            for vuln in result.vulnerabilities:
                logger.warning(f"  - {vuln.title} ({vuln.severity})")
    
    @pytest.mark.security
    def test_security_configuration(self, security_scanner):
        """测试安全配置"""
        result = security_scanner.test_security_configuration()
        
        assert result.success, f"安全配置测试失败: {result.error_message}"
        
        if result.vulnerabilities:
            logger.warning(f"发现 {len(result.vulnerabilities)} 个安全配置问题")
            for vuln in result.vulnerabilities:
                logger.warning(f"  - {vuln.title} ({vuln.severity})")
    
    @pytest.mark.security
    def test_business_logic_vulnerabilities(self, security_scanner):
        """测试业务逻辑漏洞"""
        result = security_scanner.test_business_logic_vulnerabilities()
        
        assert result.success, f"业务逻辑漏洞测试失败: {result.error_message}"
        
        if result.vulnerabilities:
            logger.warning(f"发现 {len(result.vulnerabilities)} 个业务逻辑漏洞")
            for vuln in result.vulnerabilities:
                logger.warning(f"  - {vuln.title} ({vuln.severity})")
    
    @pytest.mark.security
    @pytest.mark.compliance
    def test_compliance_requirements(self, security_scanner):
        """测试合规性要求"""
        result = security_scanner.test_compliance_requirements()
        
        assert result.success, f"合规性测试失败: {result.error_message}"
        
        if result.vulnerabilities:
            logger.warning(f"发现 {len(result.vulnerabilities)} 个合规性问题")
            for vuln in result.vulnerabilities:
                logger.warning(f"  - {vuln.title} ({vuln.severity})")
    
    @pytest.mark.security
    @pytest.mark.slow
    def test_comprehensive_security_scan(self, security_scanner):
        """综合安全扫描测试"""
        # 执行所有安全测试
        test_methods = [
            security_scanner.test_injection_vulnerabilities,
            security_scanner.test_authentication_vulnerabilities,
            security_scanner.test_xss_vulnerabilities,
            security_scanner.test_data_exposure_vulnerabilities,
            security_scanner.test_security_configuration,
            security_scanner.test_business_logic_vulnerabilities,
            security_scanner.test_compliance_requirements
        ]
        
        total_vulnerabilities = 0
        failed_tests = []
        
        for test_method in test_methods:
            try:
                result = test_method()
                total_vulnerabilities += len(result.vulnerabilities)
                
                if not result.success:
                    failed_tests.append(result.test_name)
            
            except Exception as e:
                failed_tests.append(f"{test_method.__name__}: {str(e)}")
        
        # 生成综合报告
        report = security_scanner.generate_security_report()
        
        # 保存报告到文件
        report_file = Path("security_scan_report.json")
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"安全扫描完成，发现 {total_vulnerabilities} 个漏洞")
        logger.info(f"详细报告已保存到: {report_file}")
        
        # 断言检查
        assert len(failed_tests) == 0, f"以下测试失败: {failed_tests}"
        
        # 在生产环境中，应该断言没有严重漏洞
        critical_vulns = [v for v in security_scanner.vulnerabilities if v.severity == 'critical']
        if critical_vulns:
            logger.error(f"发现 {len(critical_vulns)} 个严重漏洞，需要立即修复")
            # assert len(critical_vulns) == 0, "发现严重安全漏洞"


if __name__ == "__main__":
    # 命令行执行安全扫描
    import argparse
    
    parser = argparse.ArgumentParser(description='Connect电信平台安全扫描工具')
    parser.add_argument('--target', default='http://localhost:8000', help='目标URL')
    parser.add_argument('--api-key', help='API密钥')
    parser.add_argument('--output', default='security_report.json', help='报告输出文件')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建扫描器并执行扫描
    scanner = AdvancedSecurityScanner(target_url=args.target, api_key=args.api_key)
    
    print(f"开始安全扫描: {args.target}")
    print("=" * 50)
    
    # 执行所有测试
    test_methods = [
        ('注入漏洞测试', scanner.test_injection_vulnerabilities),
        ('认证漏洞测试', scanner.test_authentication_vulnerabilities),
        ('XSS漏洞测试', scanner.test_xss_vulnerabilities),
        ('数据泄露测试', scanner.test_data_exposure_vulnerabilities),
        ('安全配置测试', scanner.test_security_configuration),
        ('业务逻辑测试', scanner.test_business_logic_vulnerabilities),
        ('合规性测试', scanner.test_compliance_requirements)
    ]
    
    for test_name, test_method in test_methods:
        print(f"\n执行 {test_name}...")
        try:
            result = test_method()
            if result.success:
                print(f"✓ {test_name} 完成，发现 {len(result.vulnerabilities)} 个问题")
            else:
                print(f"✗ {test_name} 失败: {result.error_message}")
        except Exception as e:
            print(f"✗ {test_name} 异常: {str(e)}")
    
    # 生成并保存报告
    print("\n生成安全报告...")
    report = scanner.generate_security_report()
    
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 输出摘要
    print("\n" + "=" * 50)
    print("安全扫描摘要:")
    print(f"目标: {args.target}")
    print(f"总漏洞数: {report['scan_summary']['total_vulnerabilities']}")
    print(f"风险评分: {report['scan_summary']['risk_score']:.1f}/100")
    print("\n严重程度分布:")
    for severity, count in report['scan_summary']['severity_distribution'].items():
        if count > 0:
            print(f"  {severity.upper()}: {count}")
    
    print(f"\n详细报告已保存到: {args.output}")
    
    # 返回适当的退出码
    critical_count = report['scan_summary']['severity_distribution']['critical']
    high_count = report['scan_summary']['severity_distribution']['high']
    
    if critical_count > 0:
        print(f"\n⚠️  发现 {critical_count} 个严重漏洞，需要立即修复！")
        exit(2)
    elif high_count > 0:
        print(f"\n⚠️  发现 {high_count} 个高危漏洞，建议尽快修复。")
        exit(1)
    else:
        print("\n✓ 未发现严重安全问题。")
        exit(0)