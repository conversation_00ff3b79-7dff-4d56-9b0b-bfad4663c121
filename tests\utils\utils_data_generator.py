#!/usr/bin/env python3
"""
Connect测试数据生成器
用于生成各种测试场景的数据，包括CDR数据、EP数据、站点数据等

作者: Connect质量工程师
日期: 2024
"""

import random
import csv
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import uuid
import math
from faker import Faker
import numpy as np

class _TestDataGenerator:
    """测试数据生成器主类"""
    
    def __init__(self, seed: int = 42):
        """初始化数据生成器
        
        Args:
            seed: 随机种子，确保数据生成的可重复性
        """
        self.seed = seed
        random.seed(seed)
        np.random.seed(seed)
        self.fake = Faker(['zh_CN'])
        Faker.seed(seed)
        
        # 电信相关的常量
        self.cell_technologies = ['2G', '3G', '4G', '5G']
        self.frequency_bands = {
            '2G': [900, 1800],
            '3G': [900, 2100],
            '4G': [800, 900, 1800, 2100, 2600],
            '5G': [3500, 4900, 28000]
        }
        self.operators = ['中国移动', '中国联通', '中国电信']
        self.provinces = [
            '北京', '上海', '天津', '重庆', '河北', '山西', '辽宁', '吉林',
            '黑龙江', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',
            '湖北', '湖南', '广东', '海南', '四川', '贵州', '云南', '陕西',
            '甘肃', '青海', '台湾', '内蒙古', '广西', '西藏', '宁夏', '新疆'
        ]
        
        # 地理坐标范围（中国境内）
        self.lat_range = (18.0, 54.0)
        self.lon_range = (73.0, 135.0)
        
        # 性能指标范围
        self.rsrp_range = (-140, -44)  # dBm
        self.rsrq_range = (-20, -3)    # dB
        self.sinr_range = (-10, 30)    # dB
        self.throughput_range = (1, 1000)  # Mbps
    
    def generate_cdr_data(self, 
                         num_records: int = 1000,
                         start_date: datetime = None,
                         end_date: datetime = None,
                         output_format: str = 'csv') -> str:
        """生成CDR（呼叫详单记录）数据
        
        Args:
            num_records: 生成记录数量
            start_date: 开始日期
            end_date: 结束日期
            output_format: 输出格式 ('csv', 'json', 'excel')
            
        Returns:
            生成的数据文件路径
        """
        if start_date is None:
            start_date = datetime.now() - timedelta(days=30)
        if end_date is None:
            end_date = datetime.now()
        
        records = []
        
        for i in range(num_records):
            # 生成基本信息
            call_id = f"CDR_{uuid.uuid4().hex[:8]}"
            msisdn = self.fake.phone_number()
            imsi = f"460{random.randint(10000000000000, 99999999999999)}"
            imei = f"{random.randint(100000000000000, 999999999999999)}"
            
            # 生成时间信息
            call_time = self.fake.date_time_between(start_date=start_date, end_date=end_date)
            duration = random.randint(10, 3600)  # 通话时长（秒）
            
            # 生成位置信息
            latitude = random.uniform(*self.lat_range)
            longitude = random.uniform(*self.lon_range)
            cell_id = random.randint(100000, 999999)
            lac = random.randint(1000, 9999)
            
            # 生成网络信息
            technology = random.choice(self.cell_technologies)
            operator = random.choice(self.operators)
            frequency = random.choice(self.frequency_bands[technology])
            
            # 生成性能指标
            rsrp = random.uniform(*self.rsrp_range)
            rsrq = random.uniform(*self.rsrq_range)
            sinr = random.uniform(*self.sinr_range)
            
            # 生成业务信息
            call_type = random.choice(['语音', '视频', '数据'])
            service_type = random.choice(['通话', '短信', '上网', '视频通话'])
            data_volume = random.randint(0, 1024*1024) if call_type == '数据' else 0
            
            record = {
                'call_id': call_id,
                'msisdn': msisdn,
                'imsi': imsi,
                'imei': imei,
                'call_time': call_time.strftime('%Y-%m-%d %H:%M:%S'),
                'duration': duration,
                'latitude': round(latitude, 6),
                'longitude': round(longitude, 6),
                'cell_id': cell_id,
                'lac': lac,
                'technology': technology,
                'operator': operator,
                'frequency': frequency,
                'rsrp': round(rsrp, 2),
                'rsrq': round(rsrq, 2),
                'sinr': round(sinr, 2),
                'call_type': call_type,
                'service_type': service_type,
                'data_volume': data_volume,
                'province': random.choice(self.provinces),
                'city': self.fake.city(),
                'district': self.fake.district()
            }
            
            records.append(record)
        
        # 保存数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('test_data/cdr')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            filename = f"cdr_data_{timestamp}.csv"
            filepath = output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=records[0].keys())
                writer.writeheader()
                writer.writerows(records)
                
        elif output_format == 'json':
            filename = f"cdr_data_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'excel':
            filename = f"cdr_data_{timestamp}.xlsx"
            filepath = output_dir / filename
            
            df = pd.DataFrame(records)
            df.to_excel(filepath, index=False)
        
        return str(filepath)
    
    def generate_ep_data(self, 
                        num_records: int = 500,
                        start_date: datetime = None,
                        end_date: datetime = None,
                        output_format: str = 'csv') -> str:
        """生成EP（工程参数）数据
        
        Args:
            num_records: 生成记录数量
            start_date: 开始日期
            end_date: 结束日期
            output_format: 输出格式
            
        Returns:
            生成的数据文件路径
        """
        if start_date is None:
            start_date = datetime.now() - timedelta(days=7)
        if end_date is None:
            end_date = datetime.now()
        
        records = []
        
        for i in range(num_records):
            # 生成基本信息
            ep_id = f"EP_{uuid.uuid4().hex[:8]}"
            cell_id = random.randint(100000, 999999)
            site_id = random.randint(10000, 99999)
            
            # 生成时间信息
            measurement_time = self.fake.date_time_between(start_date=start_date, end_date=end_date)
            
            # 生成位置信息
            latitude = random.uniform(*self.lat_range)
            longitude = random.uniform(*self.lon_range)
            height = random.randint(10, 200)  # 天线高度
            
            # 生成网络配置
            technology = random.choice(self.cell_technologies)
            operator = random.choice(self.operators)
            frequency = random.choice(self.frequency_bands[technology])
            bandwidth = random.choice([5, 10, 15, 20]) if technology in ['4G', '5G'] else random.choice([5, 10])
            
            # 生成天线参数
            azimuth = random.randint(0, 359)  # 方位角
            tilt = random.randint(-10, 10)    # 下倾角
            antenna_gain = random.uniform(12, 21)  # 天线增益
            
            # 生成功率参数
            tx_power = random.uniform(20, 46)  # 发射功率 dBm
            max_power = random.uniform(40, 50)
            
            # 生成性能参数
            pci = random.randint(0, 503)  # 物理小区标识
            tac = random.randint(1, 65535)  # 跟踪区码
            
            # 生成配置参数
            handover_threshold = random.uniform(-110, -70)
            reselection_threshold = random.uniform(-110, -70)
            
            record = {
                'ep_id': ep_id,
                'cell_id': cell_id,
                'site_id': site_id,
                'measurement_time': measurement_time.strftime('%Y-%m-%d %H:%M:%S'),
                'latitude': round(latitude, 6),
                'longitude': round(longitude, 6),
                'height': height,
                'technology': technology,
                'operator': operator,
                'frequency': frequency,
                'bandwidth': bandwidth,
                'azimuth': azimuth,
                'tilt': tilt,
                'antenna_gain': round(antenna_gain, 2),
                'tx_power': round(tx_power, 2),
                'max_power': round(max_power, 2),
                'pci': pci,
                'tac': tac,
                'handover_threshold': round(handover_threshold, 2),
                'reselection_threshold': round(reselection_threshold, 2),
                'province': random.choice(self.provinces),
                'city': self.fake.city(),
                'district': self.fake.district(),
                'site_name': f"{self.fake.company()}_基站",
                'site_type': random.choice(['宏站', '微站', '室分', '小基站'])
            }
            
            records.append(record)
        
        # 保存数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('test_data/ep')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            filename = f"ep_data_{timestamp}.csv"
            filepath = output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=records[0].keys())
                writer.writeheader()
                writer.writerows(records)
                
        elif output_format == 'json':
            filename = f"ep_data_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'excel':
            filename = f"ep_data_{timestamp}.xlsx"
            filepath = output_dir / filename
            
            df = pd.DataFrame(records)
            df.to_excel(filepath, index=False)
        
        return str(filepath)
    
    def generate_site_data(self, 
                          num_records: int = 200,
                          output_format: str = 'csv') -> str:
        """生成站点数据
        
        Args:
            num_records: 生成记录数量
            output_format: 输出格式
            
        Returns:
            生成的数据文件路径
        """
        records = []
        
        for i in range(num_records):
            # 生成基本信息
            site_id = random.randint(10000, 99999)
            site_name = f"{self.fake.company()}_基站_{site_id}"
            
            # 生成位置信息
            latitude = random.uniform(*self.lat_range)
            longitude = random.uniform(*self.lon_range)
            
            # 生成站点属性
            site_type = random.choice(['宏站', '微站', '室分', '小基站'])
            operator = random.choice(self.operators)
            technologies = random.sample(self.cell_technologies, random.randint(1, 3))
            
            # 生成覆盖信息
            coverage_radius = random.randint(100, 5000)  # 覆盖半径（米）
            if site_type == '宏站':
                coverage_radius = random.randint(1000, 5000)
            elif site_type == '微站':
                coverage_radius = random.randint(200, 1000)
            elif site_type == '小基站':
                coverage_radius = random.randint(50, 200)
            
            # 生成容量信息
            max_users = random.randint(50, 1000)
            current_users = random.randint(0, max_users)
            
            # 生成状态信息
            status = random.choice(['正常', '告警', '故障', '维护'])
            online_time = random.uniform(95, 100)  # 在线率
            
            # 生成建设信息
            build_date = self.fake.date_between(start_date='-5y', end_date='today')
            vendor = random.choice(['华为', '中兴', '爱立信', '诺基亚', '大唐'])
            
            record = {
                'site_id': site_id,
                'site_name': site_name,
                'latitude': round(latitude, 6),
                'longitude': round(longitude, 6),
                'site_type': site_type,
                'operator': operator,
                'technologies': ','.join(technologies),
                'coverage_radius': coverage_radius,
                'max_users': max_users,
                'current_users': current_users,
                'utilization_rate': round(current_users / max_users * 100, 2),
                'status': status,
                'online_time': round(online_time, 2),
                'build_date': build_date.strftime('%Y-%m-%d'),
                'vendor': vendor,
                'province': random.choice(self.provinces),
                'city': self.fake.city(),
                'district': self.fake.district(),
                'address': self.fake.address(),
                'contact_person': self.fake.name(),
                'contact_phone': self.fake.phone_number()
            }
            
            records.append(record)
        
        # 保存数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('test_data/site')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            filename = f"site_data_{timestamp}.csv"
            filepath = output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=records[0].keys())
                writer.writeheader()
                writer.writerows(records)
                
        elif output_format == 'json':
            filename = f"site_data_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'excel':
            filename = f"site_data_{timestamp}.xlsx"
            filepath = output_dir / filename
            
            df = pd.DataFrame(records)
            df.to_excel(filepath, index=False)
        
        return str(filepath)
    
    def generate_drive_test_data(self, 
                               num_records: int = 2000,
                               route_length: float = 10.0,
                               output_format: str = 'csv') -> str:
        """生成路测数据
        
        Args:
            num_records: 生成记录数量
            route_length: 路测路线长度（公里）
            output_format: 输出格式
            
        Returns:
            生成的数据文件路径
        """
        records = []
        
        # 生成路测路线
        start_lat = random.uniform(*self.lat_range)
        start_lon = random.uniform(*self.lon_range)
        
        # 计算路线点
        points_per_km = num_records / route_length
        
        for i in range(num_records):
            # 生成GPS轨迹
            progress = i / num_records
            
            # 简单的直线路径，实际应用中可以更复杂
            lat_offset = (random.uniform(-0.01, 0.01) + progress * 0.1)
            lon_offset = (random.uniform(-0.01, 0.01) + progress * 0.1)
            
            latitude = start_lat + lat_offset
            longitude = start_lon + lon_offset
            
            # 生成时间戳
            test_time = datetime.now() - timedelta(hours=2) + timedelta(seconds=i*5)
            
            # 生成速度信息
            speed = random.uniform(0, 120)  # km/h
            
            # 生成网络信息
            technology = random.choice(self.cell_technologies)
            operator = random.choice(self.operators)
            cell_id = random.randint(100000, 999999)
            
            # 生成信号质量
            rsrp = random.uniform(*self.rsrp_range)
            rsrq = random.uniform(*self.rsrq_range)
            sinr = random.uniform(*self.sinr_range)
            
            # 添加一些真实的信号衰减模型
            if speed > 60:  # 高速移动时信号质量下降
                rsrp -= random.uniform(5, 15)
                rsrq -= random.uniform(2, 8)
                sinr -= random.uniform(3, 10)
            
            # 生成业务性能
            throughput_dl = random.uniform(*self.throughput_range)
            throughput_ul = throughput_dl * random.uniform(0.1, 0.5)  # 上行通常较低
            
            # 生成呼叫质量
            call_setup_time = random.uniform(1, 10)  # 秒
            call_drop_rate = random.uniform(0, 5)    # %
            handover_success_rate = random.uniform(90, 100)  # %
            
            record = {
                'test_id': f"DT_{uuid.uuid4().hex[:8]}",
                'test_time': test_time.strftime('%Y-%m-%d %H:%M:%S'),
                'latitude': round(latitude, 6),
                'longitude': round(longitude, 6),
                'speed': round(speed, 2),
                'technology': technology,
                'operator': operator,
                'cell_id': cell_id,
                'rsrp': round(rsrp, 2),
                'rsrq': round(rsrq, 2),
                'sinr': round(sinr, 2),
                'throughput_dl': round(throughput_dl, 2),
                'throughput_ul': round(throughput_ul, 2),
                'call_setup_time': round(call_setup_time, 2),
                'call_drop_rate': round(call_drop_rate, 2),
                'handover_success_rate': round(handover_success_rate, 2),
                'province': random.choice(self.provinces),
                'city': self.fake.city(),
                'road_type': random.choice(['高速公路', '城市道路', '乡村道路', '隧道', '桥梁']),
                'environment': random.choice(['城区', '郊区', '农村', '山区', '水域'])
            }
            
            records.append(record)
        
        # 保存数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('test_data/drive_test')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            filename = f"drive_test_data_{timestamp}.csv"
            filepath = output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=records[0].keys())
                writer.writeheader()
                writer.writerows(records)
                
        elif output_format == 'json':
            filename = f"drive_test_data_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'excel':
            filename = f"drive_test_data_{timestamp}.xlsx"
            filepath = output_dir / filename
            
            df = pd.DataFrame(records)
            df.to_excel(filepath, index=False)
        
        return str(filepath)
    
    def generate_kpi_data(self, 
                         num_days: int = 30,
                         output_format: str = 'csv') -> str:
        """生成KPI数据
        
        Args:
            num_days: 生成天数
            output_format: 输出格式
            
        Returns:
            生成的数据文件路径
        """
        records = []
        
        # KPI指标定义
        kpi_metrics = {
            '呼叫建立成功率': (95, 100),
            '呼叫掉话率': (0, 5),
            '切换成功率': (90, 100),
            '下载速率': (10, 100),
            '上传速率': (5, 50),
            '网络延迟': (10, 100),
            '网络可用性': (95, 100),
            '用户满意度': (80, 100),
            '流量利用率': (30, 90),
            '基站利用率': (40, 85)
        }
        
        start_date = datetime.now() - timedelta(days=num_days)
        
        for day in range(num_days):
            current_date = start_date + timedelta(days=day)
            
            # 每天生成24小时的数据
            for hour in range(24):
                timestamp = current_date.replace(hour=hour, minute=0, second=0)
                
                for kpi_name, (min_val, max_val) in kpi_metrics.items():
                    # 添加时间模式（工作时间vs非工作时间）
                    if 8 <= hour <= 22:  # 工作时间
                        if kpi_name in ['呼叫建立成功率', '切换成功率', '网络可用性']:
                            value = random.uniform(min_val * 0.95, max_val)
                        elif kpi_name in ['呼叫掉话率', '网络延迟']:
                            value = random.uniform(min_val, max_val * 1.2)
                        else:
                            value = random.uniform(min_val, max_val)
                    else:  # 非工作时间
                        if kpi_name in ['下载速率', '上传速率', '流量利用率']:
                            value = random.uniform(min_val * 0.5, max_val * 0.7)
                        else:
                            value = random.uniform(min_val, max_val)
                    
                    # 添加周末效应
                    if current_date.weekday() >= 5:  # 周末
                        if kpi_name in ['流量利用率', '基站利用率']:
                            value *= random.uniform(0.7, 0.9)
                    
                    record = {
                        'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                        'date': current_date.strftime('%Y-%m-%d'),
                        'hour': hour,
                        'kpi_name': kpi_name,
                        'kpi_value': round(value, 2),
                        'unit': self._get_kpi_unit(kpi_name),
                        'operator': random.choice(self.operators),
                        'technology': random.choice(self.cell_technologies),
                        'province': random.choice(self.provinces),
                        'city': self.fake.city(),
                        'measurement_type': random.choice(['实时', '统计', '预测']),
                        'data_source': random.choice(['网管系统', '路测', '用户反馈', 'OMC'])
                    }
                    
                    records.append(record)
        
        # 保存数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('test_data/kpi')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            filename = f"kpi_data_{timestamp}.csv"
            filepath = output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=records[0].keys())
                writer.writeheader()
                writer.writerows(records)
                
        elif output_format == 'json':
            filename = f"kpi_data_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'excel':
            filename = f"kpi_data_{timestamp}.xlsx"
            filepath = output_dir / filename
            
            df = pd.DataFrame(records)
            df.to_excel(filepath, index=False)
        
        return str(filepath)
    
    def _get_kpi_unit(self, kpi_name: str) -> str:
        """获取KPI指标单位"""
        unit_mapping = {
            '呼叫建立成功率': '%',
            '呼叫掉话率': '%',
            '切换成功率': '%',
            '下载速率': 'Mbps',
            '上传速率': 'Mbps',
            '网络延迟': 'ms',
            '网络可用性': '%',
            '用户满意度': '分',
            '流量利用率': '%',
            '基站利用率': '%'
        }
        return unit_mapping.get(kpi_name, '')
    
    def generate_large_dataset(self, 
                              data_type: str,
                              size_mb: int = 100,
                              output_format: str = 'csv') -> str:
        """生成大数据集用于性能测试
        
        Args:
            data_type: 数据类型 ('cdr', 'ep', 'site', 'drive_test', 'kpi')
            size_mb: 目标文件大小（MB）
            output_format: 输出格式
            
        Returns:
            生成的数据文件路径
        """
        # 估算记录数量（基于平均记录大小）
        avg_record_sizes = {
            'cdr': 500,      # 字节
            'ep': 400,
            'site': 300,
            'drive_test': 600,
            'kpi': 200
        }
        
        target_bytes = size_mb * 1024 * 1024
        avg_size = avg_record_sizes.get(data_type, 400)
        estimated_records = target_bytes // avg_size
        
        print(f"生成 {data_type} 数据，目标大小: {size_mb}MB，预估记录数: {estimated_records}")
        
        if data_type == 'cdr':
            return self.generate_cdr_data(estimated_records, output_format=output_format)
        elif data_type == 'ep':
            return self.generate_ep_data(estimated_records, output_format=output_format)
        elif data_type == 'site':
            return self.generate_site_data(estimated_records, output_format=output_format)
        elif data_type == 'drive_test':
            return self.generate_drive_test_data(estimated_records, output_format=output_format)
        elif data_type == 'kpi':
            # KPI数据按天数计算
            days = max(1, estimated_records // 240)  # 每天约240条记录
            return self.generate_kpi_data(days, output_format=output_format)
        else:
            raise ValueError(f"不支持的数据类型: {data_type}")
    
    def generate_test_suite(self, output_dir: str = 'test_data') -> Dict[str, str]:
        """生成完整的测试数据套件
        
        Args:
            output_dir: 输出目录
            
        Returns:
            生成的文件路径字典
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print("开始生成测试数据套件...")
        
        files = {}
        
        # 生成各类型数据
        print("生成CDR数据...")
        files['cdr'] = self.generate_cdr_data(1000)
        
        print("生成EP数据...")
        files['ep'] = self.generate_ep_data(500)
        
        print("生成站点数据...")
        files['site'] = self.generate_site_data(200)
        
        print("生成路测数据...")
        files['drive_test'] = self.generate_drive_test_data(2000)
        
        print("生成KPI数据...")
        files['kpi'] = self.generate_kpi_data(30)
        
        print("测试数据套件生成完成！")
        print("生成的文件:")
        for data_type, filepath in files.items():
            print(f"  {data_type}: {filepath}")
        
        return files

def main():
    """主函数，用于命令行调用"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Connect测试数据生成器')
    parser.add_argument('--type', choices=['cdr', 'ep', 'site', 'drive_test', 'kpi', 'all'],
                       default='all', help='数据类型')
    parser.add_argument('--records', type=int, default=1000, help='记录数量')
    parser.add_argument('--size', type=int, help='文件大小（MB），用于大数据集生成')
    parser.add_argument('--format', choices=['csv', 'json', 'excel'], 
                       default='csv', help='输出格式')
    parser.add_argument('--output', default='test_data', help='输出目录')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    args = parser.parse_args()
    
    generator = TestDataGenerator(seed=args.seed)
    
    if args.type == 'all':
        generator.generate_test_suite(args.output)
    else:
        if args.size:
            filepath = generator.generate_large_dataset(
                args.type, args.size, args.format
            )
        else:
            if args.type == 'cdr':
                filepath = generator.generate_cdr_data(args.records, output_format=args.format)
            elif args.type == 'ep':
                filepath = generator.generate_ep_data(args.records, output_format=args.format)
            elif args.type == 'site':
                filepath = generator.generate_site_data(args.records, output_format=args.format)
            elif args.type == 'drive_test':
                filepath = generator.generate_drive_test_data(args.records, output_format=args.format)
            elif args.type == 'kpi':
                days = max(1, args.records // 240)
                filepath = generator.generate_kpi_data(days, output_format=args.format)
        
        print(f"数据生成完成: {filepath}")

if __name__ == '__main__':
    main()