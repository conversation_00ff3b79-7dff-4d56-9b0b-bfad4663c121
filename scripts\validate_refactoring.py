#!/usr/bin/env python3
"""Validation script for Connect system refactoring

This script validates that the refactoring changes work correctly and
don't break existing functionality.
"""

import sys
import asyncio
import logging
from pathlib import Path
from datetime import datetime
import pandas as pd
import numpy as np

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RefactoringValidator:
    """Validates refactoring changes"""
    
    def __init__(self):
        self.test_results = {}
        self.errors = []
    
    async def run_all_validations(self) -> bool:
        """Run all validation tests"""
        logger.info("🚀 Starting Connect refactoring validation")
        
        validations = [
            ("Configuration Management", self.validate_config_management),
            ("Database Connection Pool", self.validate_database_pool),
            ("CDR Processing", self.validate_cdr_processing),
            ("Geospatial Analysis", self.validate_geospatial_analysis),
            ("KPI Calculation", self.validate_kpi_calculation),
            ("Error Handling", self.validate_error_handling),
            ("Type Safety", self.validate_type_safety),
        ]
        
        all_passed = True
        
        for test_name, test_func in validations:
            try:
                logger.info(f"🔍 Testing: {test_name}")
                result = await test_func()
                self.test_results[test_name] = result
                
                if result:
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED")
                    all_passed = False
                    
            except Exception as e:
                logger.error(f"💥 {test_name}: ERROR - {e}")
                self.test_results[test_name] = False
                self.errors.append(f"{test_name}: {e}")
                all_passed = False
        
        # Print summary
        self.print_summary()
        return all_passed
    
    async def validate_config_management(self) -> bool:
        """Validate unified configuration management"""
        try:
            from config.settings import SettingsManager, ConfigManager

            # Test settings manager initialization
            settings = SettingsManager()

            # Test telecommunications configuration access
            telecom_config = settings.get("telecom")
            if not telecom_config:
                return False

            # Validate CDR configuration
            cdr_config = telecom_config.get("cdr", {})
            required_cdr_keys = ["batch_size", "table_prefix", "validation_enabled"]
            if not all(key in cdr_config for key in required_cdr_keys):
                return False

            # Validate EP configuration
            ep_config = telecom_config.get("ep", {})
            required_ep_keys = ["batch_size", "table_prefix", "signal_analysis"]
            if not all(key in ep_config for key in required_ep_keys):
                return False

            # Test unified config manager with environment variable override
            unified_config = ConfigManager()
            import os
            os.environ["CONNECT_CDR_BATCH_SIZE"] = "5000"
            unified_config._apply_env_overrides()

            updated_batch_size = unified_config.get("telecom.cdr.batch_size")
            if updated_batch_size != 5000:
                return False

            logger.info("Configuration management validation passed")
            return True

        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    async def validate_database_pool(self) -> bool:
        """Validate enhanced database connection pool"""
        try:
            from database.connection.pool import DatabasePool
            
            # Test pool initialization (without actual database)
            pool = DatabasePool()
            
            # Test connection type optimization methods
            if not hasattr(pool, 'execute'):
                return False
            
            # Validate enhanced execute method signature
            import inspect
            execute_sig = inspect.signature(pool.execute)
            if 'connection_type' not in execute_sig.parameters:
                return False
            
            logger.info("Database pool validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Database pool validation failed: {e}")
            return False
    
    async def validate_cdr_processing(self) -> bool:
        """Validate CDR processing enhancements"""
        try:
            from importers.cdr_importer import CDRImporter
            
            # Create test CDR data
            test_data = pd.DataFrame({
                'call_id': ['call_001', 'call_002', 'call_003'],
                'caller_number': ['1234567890', '0987654321', '1122334455'],
                'called_number': ['5555555555', '6666666666', '7777777777'],
                'call_start_time': [datetime.now()] * 3,
                'call_duration': [120, 300, 45],
                'call_status': ['completed', 'completed', 'failed'],
                'cell_id': ['cell_001', 'cell_002', 'cell_001'],
                'cell_tower_lat': [40.7128, 40.7589, 40.7128],
                'cell_tower_lon': [-74.0060, -73.9851, -74.0060],
            })
            
            # Test CDR importer
            importer = CDRImporter({})
            
            # Validate async processing method exists
            if not hasattr(importer, 'process_batch_async'):
                return False
            
            # Test data validation
            if hasattr(importer, '_validate_cdr_data_async'):
                validated_data = await importer._validate_cdr_data_async(test_data)
                if len(validated_data) != 3:  # Should keep all valid records
                    return False
            
            logger.info("CDR processing validation passed")
            return True
            
        except Exception as e:
            logger.error(f"CDR processing validation failed: {e}")
            return False
    
    async def validate_geospatial_analysis(self) -> bool:
        """Validate geospatial analysis capabilities"""
        try:
            from geospatial.analyzer import SpatialAnalyzer
            from geospatial.indexer import SpatialIndexer
            from geospatial.processor import GeospatialProcessor
            import geopandas as gpd
            from shapely.geometry import Point

            # Create test geospatial data
            test_points = [
                Point(-74.0060, 40.7128),  # NYC
                Point(-73.9851, 40.7589),  # NYC
                Point(-74.0060, 40.7128),  # NYC (duplicate)
            ]

            test_gdf = gpd.GeoDataFrame({
                'measurement_id': ['m001', 'm002', 'm003'],
                'rsrp': [-85, -95, -105],
                'cell_id': ['cell_001', 'cell_002', 'cell_001'],
                'geometry': test_points
            })

            # Test spatial analyzer
            analyzer = SpatialAnalyzer()
            coverage_result = analyzer.analyze_coverage(test_gdf)
            if not isinstance(coverage_result, dict):
                return False

            required_keys = ['coverage_ratio', 'total_area', 'covered_area']
            if not all(key in coverage_result for key in required_keys):
                return False

            # Test signal quality analysis
            quality_result = analyzer.analyze_signal_quality(test_gdf)
            if not isinstance(quality_result, dict):
                return False

            # Test spatial indexer
            indexer = SpatialIndexer()
            indexer.build_index(test_gdf, 'measurement_id')
            if indexer._spatial_index is None:
                return False

            # Test point query
            query_point = Point(-74.0060, 40.7128)
            query_results = indexer.query_point(query_point, buffer_distance=0.001)
            if not isinstance(query_results, list):
                return False

            # Test geospatial processor
            processor = GeospatialProcessor()
            test_df = pd.DataFrame({
                'id': [1, 2, 3],
                'latitude': [40.7128, 40.7589, 40.7128],
                'longitude': [-74.0060, -73.9851, -74.0060]
            })
            points_gdf = processor.create_points_from_coordinates(test_df)
            if len(points_gdf) != 3:
                return False

            # Test CRS transformation
            transformed_gdf = processor.transform_crs(points_gdf, "EPSG:3857")
            if transformed_gdf.crs != "EPSG:3857":
                return False

            logger.info("Geospatial analysis validation passed")
            return True

        except Exception as e:
            logger.error(f"Geospatial analysis validation failed: {e}")
            return False
    
    async def validate_kpi_calculation(self) -> bool:
        """Validate KPI calculation functionality"""
        try:
            from telecom.kpi_calculator import TelecomKPICalculator, KPIType, AggregationLevel
            
            # Create test data for KPI calculation
            test_cdr_data = pd.DataFrame({
                'call_id': [f'call_{i:03d}' for i in range(100)],
                'call_status': ['completed'] * 95 + ['failed'] * 5,
                'cell_id': ['cell_001'] * 50 + ['cell_002'] * 50,
                'call_duration': np.random.randint(10, 3600, 100),
            })
            
            # Test KPI calculator
            calculator = TelecomKPICalculator()
            
            # Test call success rate calculation
            success_rate_results = await calculator.calculate_call_success_rate(
                test_cdr_data, AggregationLevel.CELL
            )
            
            if not success_rate_results:
                return False
            
            # Validate result structure
            first_result = success_rate_results[0]
            if first_result.kpi_type != KPIType.CALL_SUCCESS_RATE:
                return False
            
            if first_result.value < 0 or first_result.value > 100:
                return False
            
            logger.info("KPI calculation validation passed")
            return True
            
        except Exception as e:
            logger.error(f"KPI calculation validation failed: {e}")
            return False
    
    async def validate_error_handling(self) -> bool:
        """Validate enhanced error handling"""
        try:
            from database.exceptions import (
                TelecomDataError, CDRProcessingError, 
                EPProcessingError, KPICalculationError
            )
            
            # Test telecommunications-specific exceptions
            telecom_error = TelecomDataError(
                "Test error",
                data_type="cdr",
                record_id="test_001",
                cell_id="cell_001"
            )
            
            # Validate error structure
            if telecom_error.details.get("data_type") != "cdr":
                return False
            
            # Test CDR processing error
            cdr_error = CDRProcessingError(
                "CDR processing failed",
                call_id="call_001",
                processing_stage="validation"
            )
            
            if cdr_error.details.get("call_id") != "call_001":
                return False
            
            # Test error serialization
            error_dict = telecom_error.to_dict()
            if not isinstance(error_dict, dict):
                return False

            required_keys = ["type", "message", "error_code", "details"]
            if not all(key in error_dict for key in required_keys):
                return False
            
            logger.info("Error handling validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Error handling validation failed: {e}")
            return False
    
    async def validate_type_safety(self) -> bool:
        """Validate type definitions and safety"""
        try:
            # Import from our custom types module, not Python's built-in types
            import sys
            sys.path.insert(0, 'src')
            from types.telecom_types import (
                CDRRecord, EPRecord, KPIRecord,
                DataSourceType, ProcessingStatus,
                ImportResult, ValidationResult
            )
            
            # Test data structure creation
            cdr_record = CDRRecord(
                call_id="test_001",
                caller_number="1234567890",
                called_number="0987654321",
                call_start_time=datetime.now(),
                call_end_time=None,
                call_duration=None,
                call_status="completed",
                cell_id="cell_001",
                lac=None,
                imsi=None,
                cell_tower_lat=40.7128,
                cell_tower_lon=-74.0060,
                network_technology=None
            )
            
            if cdr_record.call_id != "test_001":
                return False
            
            # Test enum values
            if DataSourceType.CDR != "cdr":
                return False
            
            if ProcessingStatus.COMPLETED != "completed":
                return False
            
            # Test result structures
            import_result = ImportResult(
                success=True,
                records_imported=100,
                processing_time=1.5
            )
            
            if not import_result.success:
                return False
            
            logger.info("Type safety validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Type safety validation failed: {e}")
            return False
    
    def print_summary(self):
        """Print validation summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests
        
        print("\n" + "="*60)
        print("📊 CONNECT REFACTORING VALIDATION SUMMARY")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.errors:
            print("\n🚨 ERRORS:")
            for error in self.errors:
                print(f"  • {error}")
        
        print("\n📋 DETAILED RESULTS:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {status} {test_name}")
        
        print("="*60)


async def main():
    """Main validation function"""
    validator = RefactoringValidator()
    success = await validator.run_all_validations()
    
    if success:
        print("\n🎉 All validations passed! Refactoring is successful.")
        return 0
    else:
        print("\n⚠️ Some validations failed. Please review the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
