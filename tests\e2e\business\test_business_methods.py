#!/usr/bin/env python3
"""
E2E测试业务方法实现

该模块提供:
1. 数据导入业务方法 (import_ep_data, import_cdr_data等)
2. 数据分析业务方法 (analyze_coverage, analyze_performance等)
3. 报告生成业务方法 (generate_report, export_data等)
4. 地理空间分析方法 (spatial_analysis, gap_analysis等)
5. KPI计算和监控方法
6. 竞对分析方法

使用方法:
    from tests.e2e.business.test_business_methods import BusinessMethods
    
    business = BusinessMethods()
    result = await business.import_ep_data(file_path, options)
"""

import os
import sys
import asyncio
import json
import csv
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import logging
import tempfile
import shutil
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    import pandas as pd
    import numpy as np
    import geopandas as gpd
    from shapely.geometry import Point, Polygon, LineString
    from shapely.ops import unary_union
except ImportError as e:
    print(f"警告: 缺少地理空间处理依赖包: {e}")
    pd = None
    np = None
    gpd = None
    Point = None
    Polygon = None
    LineString = None
    unary_union = None

try:
    import psycopg2
    from sqlalchemy import create_engine, text
    from sqlalchemy.orm import sessionmaker
except ImportError as e:
    print(f"警告: 缺少数据库依赖包: {e}")
    psycopg2 = None
    create_engine = None
    text = None
    sessionmaker = None

try:
    import httpx
    import aiofiles
except ImportError as e:
    print(f"警告: 缺少异步HTTP依赖包: {e}")
    httpx = None
    aiofiles = None

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ImportResult:
    """数据导入结果"""
    success: bool
    total_records: int
    imported_records: int
    failed_records: int
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    processing_time: float = 0.0
    file_size: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AnalysisResult:
    """分析结果"""
    analysis_type: str
    success: bool
    results: Dict[str, Any] = field(default_factory=dict)
    metrics: Dict[str, float] = field(default_factory=dict)
    charts: List[str] = field(default_factory=list)
    processing_time: float = 0.0
    errors: List[str] = field(default_factory=list)


@dataclass
class ReportResult:
    """报告生成结果"""
    success: bool
    report_path: str
    report_type: str
    file_size: int = 0
    generation_time: float = 0.0
    errors: List[str] = field(default_factory=list)


class BusinessMethods:
    """业务方法实现类"""
    
    def __init__(self, 
                 db_url: str = None,
                 api_base_url: str = None,
                 temp_dir: str = None):
        """初始化业务方法
        
        Args:
            db_url: 数据库连接URL
            api_base_url: API基础URL
            temp_dir: 临时目录
        """
        self.db_url = db_url or os.getenv('DATABASE_URL', 'postgresql://test:test@localhost:5432/connect_test')
        self.api_base_url = api_base_url or os.getenv('API_BASE_URL', 'http://localhost:8000')
        self.temp_dir = Path(temp_dir or tempfile.gettempdir()) / "connect_e2e"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 数据库连接
        self.engine = None
        self.session_factory = None
        if create_engine:
            try:
                self.engine = create_engine(self.db_url)
                self.session_factory = sessionmaker(bind=self.engine)
                logger.info("数据库连接初始化成功")
            except Exception as e:
                logger.error(f"数据库连接初始化失败: {e}")
        
        # HTTP客户端
        self.http_client = None
        if httpx:
            self.http_client = httpx.AsyncClient(base_url=self.api_base_url, timeout=300.0)
        
        logger.info(f"业务方法初始化完成，临时目录: {self.temp_dir}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        if self.http_client:
            await self.http_client.aclose()
        
        # 清理临时文件
        try:
            if self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                logger.info("临时文件清理完成")
        except Exception as e:
            logger.warning(f"临时文件清理失败: {e}")
    
    # ==================== 数据导入方法 ====================
    
    async def import_ep_data(self, 
                           file_path: str, 
                           options: Dict[str, Any] = None) -> ImportResult:
        """导入EP数据
        
        Args:
            file_path: EP数据文件路径
            options: 导入选项
        
        Returns:
            导入结果
        """
        start_time = datetime.now()
        options = options or {}
        
        try:
            logger.info(f"开始导入EP数据: {file_path}")
            
            # 检查文件
            file_path = Path(file_path)
            if not file_path.exists():
                return ImportResult(
                    success=False,
                    total_records=0,
                    imported_records=0,
                    failed_records=0,
                    errors=[f"文件不存在: {file_path}"]
                )
            
            file_size = file_path.stat().st_size
            
            # 读取数据
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, encoding=options.get('encoding', 'utf-8'))
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                return ImportResult(
                    success=False,
                    total_records=0,
                    imported_records=0,
                    failed_records=0,
                    errors=[f"不支持的文件格式: {file_path.suffix}"]
                )
            
            total_records = len(df)
            logger.info(f"读取到 {total_records} 条EP记录")
            
            # 数据验证和清洗
            validation_result = self._validate_ep_data(df)
            if not validation_result['valid']:
                return ImportResult(
                    success=False,
                    total_records=total_records,
                    imported_records=0,
                    failed_records=total_records,
                    errors=validation_result['errors']
                )
            
            # 数据转换
            df_processed = self._process_ep_data(df, options)
            
            # 批量导入数据库
            imported_count = 0
            failed_count = 0
            errors = []
            
            if self.engine:
                try:
                    # 使用数据库导入
                    batch_size = options.get('batch_size', 1000)
                    for i in range(0, len(df_processed), batch_size):
                        batch = df_processed.iloc[i:i+batch_size]
                        try:
                            batch.to_sql('ep_data', self.engine, if_exists='append', index=False)
                            imported_count += len(batch)
                            logger.info(f"已导入 {imported_count}/{total_records} 条EP记录")
                        except Exception as e:
                            failed_count += len(batch)
                            errors.append(f"批次 {i//batch_size + 1} 导入失败: {str(e)}")
                            logger.error(f"EP数据批次导入失败: {e}")
                except Exception as e:
                    errors.append(f"数据库导入失败: {str(e)}")
                    failed_count = total_records
            else:
                # 使用API导入
                imported_count, failed_count, api_errors = await self._import_via_api(
                    'ep', df_processed, options
                )
                errors.extend(api_errors)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = ImportResult(
                success=imported_count > 0,
                total_records=total_records,
                imported_records=imported_count,
                failed_records=failed_count,
                errors=errors,
                processing_time=processing_time,
                file_size=file_size,
                metadata={
                    'file_name': file_path.name,
                    'columns': list(df.columns),
                    'data_types': df.dtypes.to_dict()
                }
            )
            
            logger.info(f"EP数据导入完成: {imported_count}/{total_records} 成功")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"EP数据导入异常: {e}")
            return ImportResult(
                success=False,
                total_records=0,
                imported_records=0,
                failed_records=0,
                errors=[f"导入异常: {str(e)}"],
                processing_time=processing_time
            )
    
    async def import_cdr_data(self, 
                            file_path: str, 
                            options: Dict[str, Any] = None) -> ImportResult:
        """导入CDR数据
        
        Args:
            file_path: CDR数据文件路径
            options: 导入选项
        
        Returns:
            导入结果
        """
        start_time = datetime.now()
        options = options or {}
        
        try:
            logger.info(f"开始导入CDR数据: {file_path}")
            
            # 检查文件
            file_path = Path(file_path)
            if not file_path.exists():
                return ImportResult(
                    success=False,
                    total_records=0,
                    imported_records=0,
                    failed_records=0,
                    errors=[f"文件不存在: {file_path}"]
                )
            
            file_size = file_path.stat().st_size
            
            # 读取数据
            df = pd.read_csv(file_path, encoding=options.get('encoding', 'utf-8'))
            total_records = len(df)
            logger.info(f"读取到 {total_records} 条CDR记录")
            
            # 数据验证和清洗
            validation_result = self._validate_cdr_data(df)
            if not validation_result['valid']:
                return ImportResult(
                    success=False,
                    total_records=total_records,
                    imported_records=0,
                    failed_records=total_records,
                    errors=validation_result['errors']
                )
            
            # 数据转换
            df_processed = self._process_cdr_data(df, options)
            
            # 批量导入
            imported_count = 0
            failed_count = 0
            errors = []
            
            if self.engine:
                try:
                    batch_size = options.get('batch_size', 1000)
                    for i in range(0, len(df_processed), batch_size):
                        batch = df_processed.iloc[i:i+batch_size]
                        try:
                            batch.to_sql('cdr_data', self.engine, if_exists='append', index=False)
                            imported_count += len(batch)
                            logger.info(f"已导入 {imported_count}/{total_records} 条CDR记录")
                        except Exception as e:
                            failed_count += len(batch)
                            errors.append(f"批次 {i//batch_size + 1} 导入失败: {str(e)}")
                except Exception as e:
                    failed_count = total_records
                    errors.append(f"数据库导入失败: {str(e)}")
            else:
                imported_count, failed_count, api_errors = await self._import_via_api(
                    'cdr', df_processed, options
                )
                errors.extend(api_errors)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = ImportResult(
                success=imported_count > 0,
                total_records=total_records,
                imported_records=imported_count,
                failed_records=failed_count,
                errors=errors,
                processing_time=processing_time,
                file_size=file_size,
                metadata={
                    'file_name': file_path.name,
                    'columns': list(df.columns)
                }
            )
            
            logger.info(f"CDR数据导入完成: {imported_count}/{total_records} 成功")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"CDR数据导入异常: {e}")
            return ImportResult(
                success=False,
                total_records=0,
                imported_records=0,
                failed_records=0,
                errors=[f"导入异常: {str(e)}"],
                processing_time=processing_time
            )
    
    async def import_site_data(self, 
                             file_path: str, 
                             options: Dict[str, Any] = None) -> ImportResult:
        """导入站点数据
        
        Args:
            file_path: 站点数据文件路径
            options: 导入选项
        
        Returns:
            导入结果
        """
        start_time = datetime.now()
        options = options or {}
        
        try:
            logger.info(f"开始导入站点数据: {file_path}")
            
            # 检查文件
            file_path = Path(file_path)
            if not file_path.exists():
                return ImportResult(
                    success=False,
                    total_records=0,
                    imported_records=0,
                    failed_records=0,
                    errors=[f"文件不存在: {file_path}"]
                )
            
            file_size = file_path.stat().st_size
            
            # 读取数据
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, encoding=options.get('encoding', 'utf-8'))
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                return ImportResult(
                    success=False,
                    total_records=0,
                    imported_records=0,
                    failed_records=0,
                    errors=[f"不支持的文件格式: {file_path.suffix}"]
                )
            
            total_records = len(df)
            logger.info(f"读取到 {total_records} 条站点记录")
            
            # 数据验证和清洗
            validation_result = self._validate_site_data(df)
            if not validation_result['valid']:
                return ImportResult(
                    success=False,
                    total_records=total_records,
                    imported_records=0,
                    failed_records=total_records,
                    errors=validation_result['errors']
                )
            
            # 数据转换
            df_processed = self._process_site_data(df, options)
            
            # 批量导入
            imported_count = 0
            failed_count = 0
            errors = []
            
            if self.engine:
                try:
                    batch_size = options.get('batch_size', 1000)
                    for i in range(0, len(df_processed), batch_size):
                        batch = df_processed.iloc[i:i+batch_size]
                        try:
                            batch.to_sql('site_data', self.engine, if_exists='append', index=False)
                            imported_count += len(batch)
                            logger.info(f"已导入 {imported_count}/{total_records} 条站点记录")
                        except Exception as e:
                            failed_count += len(batch)
                            errors.append(f"批次 {i//batch_size + 1} 导入失败: {str(e)}")
                except Exception as e:
                    failed_count = total_records
                    errors.append(f"数据库导入失败: {str(e)}")
            else:
                imported_count, failed_count, api_errors = await self._import_via_api(
                    'site', df_processed, options
                )
                errors.extend(api_errors)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = ImportResult(
                success=imported_count > 0,
                total_records=total_records,
                imported_records=imported_count,
                failed_records=failed_count,
                errors=errors,
                processing_time=processing_time,
                file_size=file_size,
                metadata={
                    'file_name': file_path.name,
                    'columns': list(df.columns)
                }
            )
            
            logger.info(f"站点数据导入完成: {imported_count}/{total_records} 成功")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"站点数据导入异常: {e}")
            return ImportResult(
                success=False,
                total_records=0,
                imported_records=0,
                failed_records=0,
                errors=[f"导入异常: {str(e)}"],
                processing_time=processing_time
            )
    
    async def import_kpi_data(self, 
                            file_path: str, 
                            options: Dict[str, Any] = None) -> ImportResult:
        """导入KPI数据
        
        Args:
            file_path: KPI数据文件路径
            options: 导入选项
        
        Returns:
            导入结果
        """
        start_time = datetime.now()
        options = options or {}
        
        try:
            logger.info(f"开始导入KPI数据: {file_path}")
            
            # 检查文件
            file_path = Path(file_path)
            if not file_path.exists():
                return ImportResult(
                    success=False,
                    total_records=0,
                    imported_records=0,
                    failed_records=0,
                    errors=[f"文件不存在: {file_path}"]
                )
            
            file_size = file_path.stat().st_size
            
            # 读取数据
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, encoding=options.get('encoding', 'utf-8'))
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            else:
                return ImportResult(
                    success=False,
                    total_records=0,
                    imported_records=0,
                    failed_records=0,
                    errors=[f"不支持的文件格式: {file_path.suffix}"]
                )
            
            total_records = len(df)
            logger.info(f"读取到 {total_records} 条KPI记录")
            
            # 数据验证和清洗
            validation_result = self._validate_kpi_data(df)
            if not validation_result['valid']:
                return ImportResult(
                    success=False,
                    total_records=total_records,
                    imported_records=0,
                    failed_records=total_records,
                    errors=validation_result['errors']
                )
            
            # 数据转换
            df_processed = self._process_kpi_data(df, options)
            
            # 批量导入
            imported_count = 0
            failed_count = 0
            errors = []
            
            if self.engine:
                try:
                    batch_size = options.get('batch_size', 1000)
                    for i in range(0, len(df_processed), batch_size):
                        batch = df_processed.iloc[i:i+batch_size]
                        try:
                            batch.to_sql('kpi_data', self.engine, if_exists='append', index=False)
                            imported_count += len(batch)
                            logger.info(f"已导入 {imported_count}/{total_records} 条KPI记录")
                        except Exception as e:
                            failed_count += len(batch)
                            errors.append(f"批次 {i//batch_size + 1} 导入失败: {str(e)}")
                except Exception as e:
                    failed_count = total_records
                    errors.append(f"数据库导入失败: {str(e)}")
            else:
                imported_count, failed_count, api_errors = await self._import_via_api(
                    'kpi', df_processed, options
                )
                errors.extend(api_errors)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = ImportResult(
                success=imported_count > 0,
                total_records=total_records,
                imported_records=imported_count,
                failed_records=failed_count,
                errors=errors,
                processing_time=processing_time,
                file_size=file_size,
                metadata={
                    'file_name': file_path.name,
                    'columns': list(df.columns)
                }
            )
            
            logger.info(f"KPI数据导入完成: {imported_count}/{total_records} 成功")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"KPI数据导入异常: {e}")
            return ImportResult(
                success=False,
                total_records=0,
                imported_records=0,
                failed_records=0,
                errors=[f"导入异常: {str(e)}"],
                processing_time=processing_time
            )
    
    # ==================== 数据验证方法 ====================
    
    def _validate_ep_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证EP数据"""
        errors = []
        warnings = []
        
        # 检查必需列
        required_columns = ['longitude', 'latitude', 'rsrp', 'rsrq', 'sinr']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        # 检查数据类型和范围
        if 'longitude' in df.columns:
            invalid_lon = df[(df['longitude'] < -180) | (df['longitude'] > 180)]
            if not invalid_lon.empty:
                errors.append(f"经度值超出范围 [-180, 180]: {len(invalid_lon)} 条记录")
        
        if 'latitude' in df.columns:
            invalid_lat = df[(df['latitude'] < -90) | (df['latitude'] > 90)]
            if not invalid_lat.empty:
                errors.append(f"纬度值超出范围 [-90, 90]: {len(invalid_lat)} 条记录")
        
        if 'rsrp' in df.columns:
            invalid_rsrp = df[(df['rsrp'] < -150) | (df['rsrp'] > -30)]
            if not invalid_rsrp.empty:
                warnings.append(f"RSRP值异常: {len(invalid_rsrp)} 条记录")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def _validate_cdr_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证CDR数据"""
        errors = []
        warnings = []
        
        # 检查必需列
        required_columns = ['start_time', 'end_time', 'calling_number', 'called_number']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        # 检查时间格式
        if 'start_time' in df.columns:
            try:
                pd.to_datetime(df['start_time'])
            except Exception:
                errors.append("start_time列时间格式无效")
        
        if 'end_time' in df.columns:
            try:
                pd.to_datetime(df['end_time'])
            except Exception:
                errors.append("end_time列时间格式无效")
        
        # 检查电话号码格式
        if 'calling_number' in df.columns:
            invalid_numbers = df[~df['calling_number'].astype(str).str.match(r'^\d{11}$')]
            if not invalid_numbers.empty:
                warnings.append(f"主叫号码格式异常: {len(invalid_numbers)} 条记录")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def _validate_site_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证站点数据"""
        errors = []
        warnings = []
        
        # 检查必需列
        required_columns = ['site_id', 'longitude', 'latitude', 'site_name']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        # 检查站点ID唯一性
        if 'site_id' in df.columns:
            duplicate_sites = df[df['site_id'].duplicated()]
            if not duplicate_sites.empty:
                errors.append(f"站点ID重复: {len(duplicate_sites)} 条记录")
        
        # 检查坐标范围
        if 'longitude' in df.columns:
            invalid_lon = df[(df['longitude'] < -180) | (df['longitude'] > 180)]
            if not invalid_lon.empty:
                errors.append(f"经度值超出范围: {len(invalid_lon)} 条记录")
        
        if 'latitude' in df.columns:
            invalid_lat = df[(df['latitude'] < -90) | (df['latitude'] > 90)]
            if not invalid_lat.empty:
                errors.append(f"纬度值超出范围: {len(invalid_lat)} 条记录")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    def _validate_kpi_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证KPI数据"""
        errors = []
        warnings = []
        
        # 检查必需列
        required_columns = ['timestamp', 'kpi_name', 'kpi_value']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        # 检查时间格式
        if 'timestamp' in df.columns:
            try:
                pd.to_datetime(df['timestamp'])
            except Exception:
                errors.append("timestamp列时间格式无效")
        
        # 检查KPI值
        if 'kpi_value' in df.columns:
            non_numeric = df[~pd.to_numeric(df['kpi_value'], errors='coerce').notna()]
            if not non_numeric.empty:
                warnings.append(f"KPI值非数值: {len(non_numeric)} 条记录")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }
    
    # ==================== 数据处理方法 ====================
    
    def _process_ep_data(self, df: pd.DataFrame, options: Dict[str, Any]) -> pd.DataFrame:
        """处理EP数据"""
        df_processed = df.copy()
        
        # 数据清洗
        if 'longitude' in df_processed.columns and 'latitude' in df_processed.columns:
            # 移除无效坐标
            df_processed = df_processed[
                (df_processed['longitude'].between(-180, 180)) &
                (df_processed['latitude'].between(-90, 90))
            ]
        
        # 添加地理位置点
        if Point and 'longitude' in df_processed.columns and 'latitude' in df_processed.columns:
            df_processed['geometry'] = df_processed.apply(
                lambda row: Point(row['longitude'], row['latitude']), axis=1
            )
        
        # 添加时间戳
        if 'timestamp' not in df_processed.columns:
            df_processed['timestamp'] = datetime.now()
        
        # 数据类型转换
        numeric_columns = ['rsrp', 'rsrq', 'sinr']
        for col in numeric_columns:
            if col in df_processed.columns:
                df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce')
        
        return df_processed
    
    def _process_cdr_data(self, df: pd.DataFrame, options: Dict[str, Any]) -> pd.DataFrame:
        """处理CDR数据"""
        df_processed = df.copy()
        
        # 时间格式转换
        time_columns = ['start_time', 'end_time']
        for col in time_columns:
            if col in df_processed.columns:
                df_processed[col] = pd.to_datetime(df_processed[col], errors='coerce')
        
        # 计算通话时长
        if 'start_time' in df_processed.columns and 'end_time' in df_processed.columns:
            df_processed['duration'] = (
                df_processed['end_time'] - df_processed['start_time']
            ).dt.total_seconds()
        
        # 电话号码标准化
        phone_columns = ['calling_number', 'called_number']
        for col in phone_columns:
            if col in df_processed.columns:
                df_processed[col] = df_processed[col].astype(str).str.replace(r'\D', '', regex=True)
        
        return df_processed
    
    def _process_site_data(self, df: pd.DataFrame, options: Dict[str, Any]) -> pd.DataFrame:
        """处理站点数据"""
        df_processed = df.copy()
        
        # 坐标验证和清洗
        if 'longitude' in df_processed.columns and 'latitude' in df_processed.columns:
            df_processed = df_processed[
                (df_processed['longitude'].between(-180, 180)) &
                (df_processed['latitude'].between(-90, 90))
            ]
        
        # 添加地理位置点
        if Point and 'longitude' in df_processed.columns and 'latitude' in df_processed.columns:
            df_processed['geometry'] = df_processed.apply(
                lambda row: Point(row['longitude'], row['latitude']), axis=1
            )
        
        # 站点名称标准化
        if 'site_name' in df_processed.columns:
            df_processed['site_name'] = df_processed['site_name'].str.strip()
        
        return df_processed
    
    def _process_kpi_data(self, df: pd.DataFrame, options: Dict[str, Any]) -> pd.DataFrame:
        """处理KPI数据"""
        df_processed = df.copy()
        
        # 时间格式转换
        if 'timestamp' in df_processed.columns:
            df_processed['timestamp'] = pd.to_datetime(df_processed['timestamp'], errors='coerce')
        
        # KPI值数值化
        if 'kpi_value' in df_processed.columns:
            df_processed['kpi_value'] = pd.to_numeric(df_processed['kpi_value'], errors='coerce')
        
        # 移除空值
        df_processed = df_processed.dropna(subset=['kpi_value'])
        
        return df_processed
    
    # ==================== API导入方法 ====================
    
    async def _import_via_api(self, 
                            data_type: str, 
                            df: pd.DataFrame, 
                            options: Dict[str, Any]) -> Tuple[int, int, List[str]]:
        """通过API导入数据"""
        if not self.http_client:
            return 0, len(df), ["HTTP客户端未初始化"]
        
        imported_count = 0
        failed_count = 0
        errors = []
        
        batch_size = options.get('batch_size', 100)
        endpoint = f"/api/v1/data/{data_type}/import"
        
        try:
            for i in range(0, len(df), batch_size):
                batch = df.iloc[i:i+batch_size]
                
                # 转换为JSON格式
                data = {
                    'records': batch.to_dict('records'),
                    'options': options
                }
                
                headers = {'Content-Type': 'application/json'}
                if self.api_key:
                    headers['Authorization'] = f'Bearer {self.api_key}'
                
                try:
                    response = await self.http_client.post(
                        endpoint,
                        json=data,
                        headers=headers,
                        timeout=60.0
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        imported_count += result.get('imported_count', 0)
                        batch_errors = result.get('errors', [])
                        if batch_errors:
                            errors.extend(batch_errors)
                            failed_count += len(batch_errors)
                    else:
                        failed_count += len(batch)
                        errors.append(f"批次 {i//batch_size + 1} API调用失败: {response.status_code}")
                        
                except Exception as e:
                    failed_count += len(batch)
                    errors.append(f"批次 {i//batch_size + 1} 请求异常: {str(e)}")
                    
                logger.info(f"API导入进度: {imported_count + failed_count}/{len(df)}")
                
        except Exception as e:
            errors.append(f"API导入异常: {str(e)}")
            failed_count = len(df)
        
        return imported_count, failed_count, errors
    
    async def import_kpi_data(self, 
                            file_path: str, 
                            options: Dict[str, Any] = None) -> ImportResult:
        """导入KPI数据
        
        Args:
            file_path: KPI数据文件路径
            options: 导入选项
        
        Returns:
            导入结果
        """
        start_time = datetime.now()
        options = options or {}
        
        try:
            logger.info(f"开始导入KPI数据: {file_path}")
            
            # 检查文件
            file_path = Path(file_path)
            if not file_path.exists():
                return ImportResult(
                    success=False,
                    total_records=0,
                    imported_records=0,
                    failed_records=0,
                    errors=[f"文件不存在: {file_path}"]
                )
            
            file_size = file_path.stat().st_size
            
            # 读取数据
            df = pd.read_csv(file_path, encoding=options.get('encoding', 'utf-8'))
            total_records = len(df)
            logger.info(f"读取到 {total_records} 条KPI记录")
            
            # 数据验证和清洗
            validation_result = self._validate_kpi_data(df)
            if not validation_result['valid']:
                return ImportResult(
                    success=False,
                    total_records=total_records,
                    imported_records=0,
                    failed_records=total_records,
                    errors=validation_result['errors']
                )
            
            # 数据转换
            df_processed = self._process_kpi_data(df, options)
            
            # 批量导入
            imported_count = 0
            failed_count = 0
            errors = []
            
            if self.engine:
                try:
                    batch_size = options.get('batch_size', 1000)
                    for i in range(0, len(df_processed), batch_size):
                        batch = df_processed.iloc[i:i+batch_size]
                        try:
                            batch.to_sql('kpi_data', self.engine, if_exists='append', index=False)
                            imported_count += len(batch)
                            logger.info(f"已导入 {imported_count}/{total_records} 条KPI记录")
                        except Exception as e:
                            failed_count += len(batch)
                            errors.append(f"批次 {i//batch_size + 1} 导入失败: {str(e)}")
                except Exception as e:
                    failed_count = total_records
                    errors.append(f"数据库导入失败: {str(e)}")
            else:
                imported_count, failed_count, api_errors = await self._import_via_api(
                    'kpi', df_processed, options
                )
                errors.extend(api_errors)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = ImportResult(
                success=imported_count > 0,
                total_records=total_records,
                imported_records=imported_count,
                failed_records=failed_count,
                errors=errors,
                processing_time=processing_time,
                file_size=file_size,
                metadata={
                    'file_name': file_path.name,
                    'columns': list(df.columns)
                }
            )
            
            logger.info(f"KPI数据导入完成: {imported_count}/{total_records} 成功")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"KPI数据导入异常: {e}")
            return ImportResult(
                success=False,
                total_records=0,
                imported_records=0,
                failed_records=0,
                errors=[f"导入异常: {str(e)}"],
                processing_time=processing_time
            )
    
    # ==================== 数据分析方法 ====================
    
    async def analyze_coverage(self, 
                             region: Dict[str, Any], 
                             options: Dict[str, Any] = None) -> AnalysisResult:
        """覆盖率分析
        
        Args:
            region: 分析区域
            options: 分析选项
        
        Returns:
            分析结果
        """
        start_time = datetime.now()
        options = options or {}
        
        try:
            logger.info(f"开始覆盖率分析: {region}")
            
            # 获取区域内的站点数据
            sites_data = await self._get_sites_in_region(region)
            if not sites_data:
                return AnalysisResult(
                    analysis_type="coverage",
                    success=False,
                    errors=["区域内没有找到站点数据"]
                )
            
            # 获取EP数据
            ep_data = await self._get_ep_data_in_region(region)
            
            # 计算覆盖率指标
            coverage_metrics = self._calculate_coverage_metrics(sites_data, ep_data, region)
            
            # 生成热力图
            heatmap_path = await self._generate_coverage_heatmap(sites_data, ep_data, region)
            
            # GAP分析
            gap_analysis = self._perform_gap_analysis(sites_data, ep_data, region)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = AnalysisResult(
                analysis_type="coverage",
                success=True,
                results={
                    'coverage_metrics': coverage_metrics,
                    'gap_analysis': gap_analysis,
                    'sites_count': len(sites_data),
                    'ep_points_count': len(ep_data) if ep_data else 0
                },
                metrics=coverage_metrics,
                charts=[heatmap_path] if heatmap_path else [],
                processing_time=processing_time
            )
            
            logger.info(f"覆盖率分析完成，覆盖率: {coverage_metrics.get('coverage_rate', 0):.2f}%")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"覆盖率分析异常: {e}")
            return AnalysisResult(
                analysis_type="coverage",
                success=False,
                errors=[f"分析异常: {str(e)}"],
                processing_time=processing_time
            )
    
    async def analyze_performance(self, 
                                time_range: Dict[str, str], 
                                options: Dict[str, Any] = None) -> AnalysisResult:
        """性能分析
        
        Args:
            time_range: 时间范围
            options: 分析选项
        
        Returns:
            分析结果
        """
        start_time = datetime.now()
        options = options or {}
        
        try:
            logger.info(f"开始性能分析: {time_range}")
            
            # 获取KPI数据
            kpi_data = await self._get_kpi_data_in_timerange(time_range)
            if not kpi_data:
                return AnalysisResult(
                    analysis_type="performance",
                    success=False,
                    errors=["时间范围内没有找到KPI数据"]
                )
            
            # 计算性能指标
            performance_metrics = self._calculate_performance_metrics(kpi_data)
            
            # 趋势分析
            trend_analysis = self._perform_trend_analysis(kpi_data)
            
            # 异常检测
            anomaly_detection = self._detect_anomalies(kpi_data)
            
            # 生成性能图表
            charts = await self._generate_performance_charts(kpi_data)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = AnalysisResult(
                analysis_type="performance",
                success=True,
                results={
                    'performance_metrics': performance_metrics,
                    'trend_analysis': trend_analysis,
                    'anomaly_detection': anomaly_detection,
                    'data_points': len(kpi_data)
                },
                metrics=performance_metrics,
                charts=charts,
                processing_time=processing_time
            )
            
            logger.info(f"性能分析完成，处理 {len(kpi_data)} 个数据点")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"性能分析异常: {e}")
            return AnalysisResult(
                analysis_type="performance",
                success=False,
                errors=[f"分析异常: {str(e)}"],
                processing_time=processing_time
            )
    
    async def analyze_competitor(self, 
                               competitor_data: Dict[str, Any], 
                               options: Dict[str, Any] = None) -> AnalysisResult:
        """竞对分析
        
        Args:
            competitor_data: 竞对数据
            options: 分析选项
        
        Returns:
            分析结果
        """
        start_time = datetime.now()
        options = options or {}
        
        try:
            logger.info("开始竞对分析")
            
            # 获取自有网络数据
            own_data = await self._get_own_network_data()
            
            # 对比分析
            comparison_results = self._compare_networks(own_data, competitor_data)
            
            # 差距分析
            gap_analysis = self._analyze_competitive_gaps(own_data, competitor_data)
            
            # 优势分析
            advantage_analysis = self._analyze_competitive_advantages(own_data, competitor_data)
            
            # 生成对比图表
            charts = await self._generate_competitor_charts(own_data, competitor_data)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = AnalysisResult(
                analysis_type="competitor",
                success=True,
                results={
                    'comparison_results': comparison_results,
                    'gap_analysis': gap_analysis,
                    'advantage_analysis': advantage_analysis
                },
                metrics=comparison_results.get('metrics', {}),
                charts=charts,
                processing_time=processing_time
            )
            
            logger.info("竞对分析完成")
            return result
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"竞对分析异常: {e}")
            return AnalysisResult(
                analysis_type="competitor",
                success=False,
                errors=[f"分析异常: {str(e)}"],
                processing_time=processing_time
            )
    
    # ==================== 报告生成方法 ====================
    
    async def generate_report(self, 
                            report_type: str, 
                            data: Dict[str, Any], 
                            options: Dict[str, Any] = None) -> ReportResult:
        """生成报告
        
        Args:
            report_type: 报告类型
            data: 报告数据
            options: 生成选项
        
        Returns:
            报告结果
        """
        start_time = datetime.now()
        options = options or {}
        
        try:
            logger.info(f"开始生成{report_type}报告")
            
            # 确定报告格式
            format_type = options.get('format', 'pdf')
            
            # 生成报告文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_filename = f"{report_type}_report_{timestamp}.{format_type}"
            report_path = self.temp_dir / report_filename
            
            # 根据报告类型生成内容
            if report_type == 'coverage':
                success = await self._generate_coverage_report(data, report_path, format_type)
            elif report_type == 'performance':
                success = await self._generate_performance_report(data, report_path, format_type)
            elif report_type == 'competitor':
                success = await self._generate_competitor_report(data, report_path, format_type)
            elif report_type == 'summary':
                success = await self._generate_summary_report(data, report_path, format_type)
            else:
                return ReportResult(
                    success=False,
                    report_path="",
                    report_type=report_type,
                    errors=[f"不支持的报告类型: {report_type}"]
                )
            
            if not success:
                return ReportResult(
                    success=False,
                    report_path="",
                    report_type=report_type,
                    errors=["报告生成失败"]
                )
            
            # 获取文件大小
            file_size = report_path.stat().st_size if report_path.exists() else 0
            generation_time = (datetime.now() - start_time).total_seconds()
            
            result = ReportResult(
                success=True,
                report_path=str(report_path),
                report_type=report_type,
                file_size=file_size,
                generation_time=generation_time
            )
            
            logger.info(f"{report_type}报告生成完成: {report_path}")
            return result
            
        except Exception as e:
            generation_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"报告生成异常: {e}")
            return ReportResult(
                success=False,
                report_path="",
                report_type=report_type,
                generation_time=generation_time,
                errors=[f"生成异常: {str(e)}"]
            )
    
    async def export_data(self, 
                        data_type: str, 
                        filters: Dict[str, Any] = None, 
                        options: Dict[str, Any] = None) -> ReportResult:
        """导出数据
        
        Args:
            data_type: 数据类型
            filters: 过滤条件
            options: 导出选项
        
        Returns:
            导出结果
        """
        start_time = datetime.now()
        filters = filters or {}
        options = options or {}
        
        try:
            logger.info(f"开始导出{data_type}数据")
            
            # 获取数据
            if data_type == 'ep':
                data = await self._get_ep_data_with_filters(filters)
            elif data_type == 'cdr':
                data = await self._get_cdr_data_with_filters(filters)
            elif data_type == 'site':
                data = await self._get_site_data_with_filters(filters)
            elif data_type == 'kpi':
                data = await self._get_kpi_data_with_filters(filters)
            else:
                return ReportResult(
                    success=False,
                    report_path="",
                    report_type=f"{data_type}_export",
                    errors=[f"不支持的数据类型: {data_type}"]
                )
            
            if not data:
                return ReportResult(
                    success=False,
                    report_path="",
                    report_type=f"{data_type}_export",
                    errors=["没有找到符合条件的数据"]
                )
            
            # 确定导出格式
            format_type = options.get('format', 'csv')
            
            # 生成导出文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            export_filename = f"{data_type}_export_{timestamp}.{format_type}"
            export_path = self.temp_dir / export_filename
            
            # 导出数据
            if format_type == 'csv':
                data.to_csv(export_path, index=False, encoding='utf-8-sig')
            elif format_type == 'excel':
                data.to_excel(export_path, index=False)
            elif format_type == 'json':
                data.to_json(export_path, orient='records', force_ascii=False, indent=2)
            else:
                return ReportResult(
                    success=False,
                    report_path="",
                    report_type=f"{data_type}_export",
                    errors=[f"不支持的导出格式: {format_type}"]
                )
            
            # 获取文件大小
            file_size = export_path.stat().st_size
            generation_time = (datetime.now() - start_time).total_seconds()
            
            result = ReportResult(
                success=True,
                report_path=str(export_path),
                report_type=f"{data_type}_export",
                file_size=file_size,
                generation_time=generation_time
            )
            
            logger.info(f"{data_type}数据导出完成: {export_path}, {len(data)}条记录")
            return result
            
        except Exception as e:
            generation_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"数据导出异常: {e}")
            return ReportResult(
                success=False,
                report_path="",
                report_type=f"{data_type}_export",
                generation_time=generation_time,
                errors=[f"导出异常: {str(e)}"]
            )
    
    # ==================== 私有辅助方法 ====================
    
    def _validate_ep_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证EP数据"""
        errors = []
        
        # 检查必需列
        required_columns = ['longitude', 'latitude', 'signal_strength']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        # 检查数据类型
        if 'longitude' in df.columns:
            if not pd.api.types.is_numeric_dtype(df['longitude']):
                errors.append("经度列必须是数值类型")
        
        if 'latitude' in df.columns:
            if not pd.api.types.is_numeric_dtype(df['latitude']):
                errors.append("纬度列必须是数值类型")
        
        # 检查数据范围
        if 'longitude' in df.columns and pd.api.types.is_numeric_dtype(df['longitude']):
            if df['longitude'].min() < -180 or df['longitude'].max() > 180:
                errors.append("经度值超出有效范围 (-180, 180)")
        
        if 'latitude' in df.columns and pd.api.types.is_numeric_dtype(df['latitude']):
            if df['latitude'].min() < -90 or df['latitude'].max() > 90:
                errors.append("纬度值超出有效范围 (-90, 90)")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def _validate_cdr_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证CDR数据"""
        errors = []
        
        # 检查必需列
        required_columns = ['call_time', 'duration', 'cell_id']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        # 检查时间格式
        if 'call_time' in df.columns:
            try:
                pd.to_datetime(df['call_time'])
            except:
                errors.append("call_time列时间格式无效")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def _validate_site_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证站点数据"""
        errors = []
        
        # 检查必需列
        required_columns = ['site_id', 'longitude', 'latitude']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        # 检查坐标
        if 'longitude' in df.columns and 'latitude' in df.columns:
            if not pd.api.types.is_numeric_dtype(df['longitude']) or not pd.api.types.is_numeric_dtype(df['latitude']):
                errors.append("经纬度列必须是数值类型")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def _validate_kpi_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """验证KPI数据"""
        errors = []
        
        # 检查必需列
        required_columns = ['timestamp', 'kpi_name', 'kpi_value']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            errors.append(f"缺少必需列: {missing_columns}")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def _process_ep_data(self, df: pd.DataFrame, options: Dict[str, Any]) -> pd.DataFrame:
        """处理EP数据"""
        df_processed = df.copy()
        
        # 数据清洗
        df_processed = df_processed.dropna(subset=['longitude', 'latitude'])
        
        # 坐标转换
        if options.get('coordinate_system') == 'wgs84':
            # 已经是WGS84，无需转换
            pass
        
        # 添加处理时间戳
        df_processed['processed_at'] = datetime.now()
        
        return df_processed
    
    def _process_cdr_data(self, df: pd.DataFrame, options: Dict[str, Any]) -> pd.DataFrame:
        """处理CDR数据"""
        df_processed = df.copy()
        
        # 时间格式标准化
        if 'call_time' in df_processed.columns:
            df_processed['call_time'] = pd.to_datetime(df_processed['call_time'])
        
        # 添加处理时间戳
        df_processed['processed_at'] = datetime.now()
        
        return df_processed
    
    def _process_site_data(self, df: pd.DataFrame, options: Dict[str, Any]) -> pd.DataFrame:
        """处理站点数据"""
        df_processed = df.copy()
        
        # 数据清洗
        df_processed = df_processed.dropna(subset=['longitude', 'latitude', 'site_id'])
        
        # 添加处理时间戳
        df_processed['processed_at'] = datetime.now()
        
        return df_processed
    
    def _process_kpi_data(self, df: pd.DataFrame, options: Dict[str, Any]) -> pd.DataFrame:
        """处理KPI数据"""
        df_processed = df.copy()
        
        # 时间格式标准化
        if 'timestamp' in df_processed.columns:
            df_processed['timestamp'] = pd.to_datetime(df_processed['timestamp'])
        
        # 添加处理时间戳
        df_processed['processed_at'] = datetime.now()
        
        return df_processed
    
    async def _import_via_api(self, 
                            data_type: str, 
                            df: pd.DataFrame, 
                            options: Dict[str, Any]) -> Tuple[int, int, List[str]]:
        """通过API导入数据"""
        if not self.http_client:
            return 0, len(df), ["HTTP客户端未初始化"]
        
        imported_count = 0
        failed_count = 0
        errors = []
        
        try:
            batch_size = options.get('batch_size', 100)
            
            for i in range(0, len(df), batch_size):
                batch = df.iloc[i:i+batch_size]
                batch_data = batch.to_dict('records')
                
                try:
                    response = await self.http_client.post(
                        f"/api/data/{data_type}/import",
                        json={'data': batch_data}
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        imported_count += result.get('imported_count', 0)
                        failed_count += result.get('failed_count', 0)
                    else:
                        failed_count += len(batch)
                        errors.append(f"API请求失败: {response.status_code}")
                        
                except Exception as e:
                    failed_count += len(batch)
                    errors.append(f"批次 {i//batch_size + 1} API请求异常: {str(e)}")
        
        except Exception as e:
            errors.append(f"API导入异常: {str(e)}")
            failed_count = len(df)
        
        return imported_count, failed_count, errors
    
    async def _get_sites_in_region(self, region: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取区域内的站点数据"""
        # 模拟数据获取
        return [
            {'site_id': 'S001', 'longitude': 116.4074, 'latitude': 39.9042, 'coverage_radius': 1000},
            {'site_id': 'S002', 'longitude': 116.4174, 'latitude': 39.9142, 'coverage_radius': 1200}
        ]
    
    async def _get_ep_data_in_region(self, region: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取区域内的EP数据"""
        # 模拟数据获取
        return [
            {'longitude': 116.4074, 'latitude': 39.9042, 'signal_strength': -85},
            {'longitude': 116.4174, 'latitude': 39.9142, 'signal_strength': -78}
        ]
    
    def _calculate_coverage_metrics(self, 
                                  sites_data: List[Dict[str, Any]], 
                                  ep_data: List[Dict[str, Any]], 
                                  region: Dict[str, Any]) -> Dict[str, float]:
        """计算覆盖率指标"""
        # 模拟覆盖率计算
        return {
            'coverage_rate': 85.6,
            'signal_strength_avg': -82.5,
            'weak_coverage_rate': 12.3,
            'no_coverage_rate': 2.1
        }
    
    async def _generate_coverage_heatmap(self, 
                                       sites_data: List[Dict[str, Any]], 
                                       ep_data: List[Dict[str, Any]], 
                                       region: Dict[str, Any]) -> Optional[str]:
        """生成覆盖热力图"""
        # 模拟热力图生成
        heatmap_path = self.temp_dir / f"coverage_heatmap_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        
        # 这里应该实际生成热力图
        # 暂时创建一个空文件
        heatmap_path.touch()
        
        return str(heatmap_path)
    
    def _perform_gap_analysis(self, 
                            sites_data: List[Dict[str, Any]], 
                            ep_data: List[Dict[str, Any]], 
                            region: Dict[str, Any]) -> Dict[str, Any]:
        """执行GAP分析"""
        return {
            'gap_areas': [
                {'area_id': 'GAP001', 'longitude': 116.4000, 'latitude': 39.9000, 'severity': 'high'},
                {'area_id': 'GAP002', 'longitude': 116.4200, 'latitude': 39.9200, 'severity': 'medium'}
            ],
            'total_gap_areas': 2,
            'high_priority_gaps': 1,
            'medium_priority_gaps': 1
        }
    
    async def _get_kpi_data_in_timerange(self, time_range: Dict[str, str]) -> List[Dict[str, Any]]:
        """获取时间范围内的KPI数据"""
        # 模拟KPI数据获取
        return [
            {'timestamp': '2024-01-01 00:00:00', 'kpi_name': 'throughput', 'kpi_value': 150.5},
            {'timestamp': '2024-01-01 01:00:00', 'kpi_name': 'throughput', 'kpi_value': 145.2}
        ]
    
    def _calculate_performance_metrics(self, kpi_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算性能指标"""
        return {
            'avg_throughput': 147.85,
            'max_throughput': 150.5,
            'min_throughput': 145.2,
            'throughput_variance': 2.65
        }
    
    def _perform_trend_analysis(self, kpi_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """执行趋势分析"""
        return {
            'trend_direction': 'decreasing',
            'trend_strength': 0.75,
            'seasonal_pattern': 'detected',
            'forecast': {
                'next_hour': 142.8,
                'confidence': 0.85
            }
        }
    
    def _detect_anomalies(self, kpi_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """检测异常"""
        return {
            'anomalies_detected': 0,
            'anomaly_threshold': 3.0,
            'anomaly_points': []
        }
    
    async def _generate_performance_charts(self, kpi_data: List[Dict[str, Any]]) -> List[str]:
        """生成性能图表"""
        charts = []
        
        # 模拟图表生成
        chart_path = self.temp_dir / f"performance_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        chart_path.touch()
        charts.append(str(chart_path))
        
        return charts
    
    async def _get_own_network_data(self) -> Dict[str, Any]:
        """获取自有网络数据"""
        return {
            'sites_count': 1500,
            'coverage_rate': 85.6,
            'avg_throughput': 147.85
        }
    
    def _compare_networks(self, own_data: Dict[str, Any], competitor_data: Dict[str, Any]) -> Dict[str, Any]:
        """对比网络"""
        return {
            'metrics': {
                'sites_ratio': own_data.get('sites_count', 0) / competitor_data.get('sites_count', 1),
                'coverage_diff': own_data.get('coverage_rate', 0) - competitor_data.get('coverage_rate', 0),
                'throughput_diff': own_data.get('avg_throughput', 0) - competitor_data.get('avg_throughput', 0)
            }
        }
    
    def _analyze_competitive_gaps(self, own_data: Dict[str, Any], competitor_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析竞争差距"""
        return {
            'coverage_gap': competitor_data.get('coverage_rate', 0) - own_data.get('coverage_rate', 0),
            'performance_gap': competitor_data.get('avg_throughput', 0) - own_data.get('avg_throughput', 0),
            'improvement_areas': ['rural_coverage', 'indoor_penetration']
        }
    
    def _analyze_competitive_advantages(self, own_data: Dict[str, Any], competitor_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析竞争优势"""
        return {
            'advantages': ['urban_coverage', 'network_latency'],
            'advantage_score': 7.5
        }
    
    async def _generate_competitor_charts(self, own_data: Dict[str, Any], competitor_data: Dict[str, Any]) -> List[str]:
        """生成竞对图表"""
        charts = []
        
        # 模拟图表生成
        chart_path = self.temp_dir / f"competitor_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        chart_path.touch()
        charts.append(str(chart_path))
        
        return charts
    
    async def _generate_coverage_report(self, data: Dict[str, Any], report_path: Path, format_type: str) -> bool:
        """生成覆盖率报告"""
        try:
            if format_type == 'pdf':
                # 模拟PDF报告生成
                with open(report_path, 'w') as f:
                    f.write("Coverage Report Content")
            elif format_type == 'html':
                # 模拟HTML报告生成
                with open(report_path, 'w') as f:
                    f.write("<html><body><h1>Coverage Report</h1></body></html>")
            return True
        except Exception as e:
            logger.error(f"覆盖率报告生成失败: {e}")
            return False
    
    async def _generate_performance_report(self, data: Dict[str, Any], report_path: Path, format_type: str) -> bool:
        """生成性能报告"""
        try:
            if format_type == 'pdf':
                with open(report_path, 'w') as f:
                    f.write("Performance Report Content")
            elif format_type == 'html':
                with open(report_path, 'w') as f:
                    f.write("<html><body><h1>Performance Report</h1></body></html>")
            return True
        except Exception as e:
            logger.error(f"性能报告生成失败: {e}")
            return False
    
    async def _generate_competitor_report(self, data: Dict[str, Any], report_path: Path, format_type: str) -> bool:
        """生成竞对报告"""
        try:
            if format_type == 'pdf':
                with open(report_path, 'w') as f:
                    f.write("Competitor Report Content")
            elif format_type == 'html':
                with open(report_path, 'w') as f:
                    f.write("<html><body><h1>Competitor Report</h1></body></html>")
            return True
        except Exception as e:
            logger.error(f"竞对报告生成失败: {e}")
            return False
    
    async def _generate_summary_report(self, data: Dict[str, Any], report_path: Path, format_type: str) -> bool:
        """生成汇总报告"""
        try:
            if format_type == 'pdf':
                with open(report_path, 'w') as f:
                    f.write("Summary Report Content")
            elif format_type == 'html':
                with open(report_path, 'w') as f:
                    f.write("<html><body><h1>Summary Report</h1></body></html>")
            return True
        except Exception as e:
            logger.error(f"汇总报告生成失败: {e}")
            return False
    
    async def _get_ep_data_with_filters(self, filters: Dict[str, Any]) -> pd.DataFrame:
        """根据过滤条件获取EP数据"""
        # 模拟数据获取
        data = {
            'longitude': [116.4074, 116.4174, 116.4274],
            'latitude': [39.9042, 39.9142, 39.9242],
            'signal_strength': [-85, -78, -92],
            'timestamp': ['2024-01-01 10:00:00', '2024-01-01 10:01:00', '2024-01-01 10:02:00']
        }
        return pd.DataFrame(data)
    
    async def _get_cdr_data_with_filters(self, filters: Dict[str, Any]) -> pd.DataFrame:
        """根据过滤条件获取CDR数据"""
        data = {
            'call_time': ['2024-01-01 10:00:00', '2024-01-01 10:01:00'],
            'duration': [120, 180],
            'cell_id': ['CELL001', 'CELL002'],
            'call_type': ['voice', 'data']
        }
        return pd.DataFrame(data)
    
    async def _get_site_data_with_filters(self, filters: Dict[str, Any]) -> pd.DataFrame:
        """根据过滤条件获取站点数据"""
        data = {
            'site_id': ['S001', 'S002', 'S003'],
            'longitude': [116.4074, 116.4174, 116.4274],
            'latitude': [39.9042, 39.9142, 39.9242],
            'site_type': ['macro', 'micro', 'pico'],
            'coverage_radius': [1000, 800, 500]
        }
        return pd.DataFrame(data)
    
    async def _get_kpi_data_with_filters(self, filters: Dict[str, Any]) -> pd.DataFrame:
        """根据过滤条件获取KPI数据"""
        data = {
            'timestamp': ['2024-01-01 10:00:00', '2024-01-01 11:00:00', '2024-01-01 12:00:00'],
            'kpi_name': ['throughput', 'throughput', 'throughput'],
            'kpi_value': [150.5, 145.2, 148.7],
            'site_id': ['S001', 'S002', 'S003']
        }
        return pd.DataFrame(data)


# ==================== 便捷函数 ====================

async def create_business_methods(db_url: str = None, 
                                api_base_url: str = None, 
                                temp_dir: str = None) -> BusinessMethods:
    """创建业务方法实例
    
    Args:
        db_url: 数据库连接URL
        api_base_url: API基础URL
        temp_dir: 临时目录
    
    Returns:
        业务方法实例
    """
    return BusinessMethods(db_url=db_url, api_base_url=api_base_url, temp_dir=temp_dir)


async def test_import_workflow(file_paths: Dict[str, str], 
                             options: Dict[str, Any] = None) -> Dict[str, ImportResult]:
    """测试完整的数据导入工作流
    
    Args:
        file_paths: 文件路径字典 {'ep': 'path/to/ep.csv', 'cdr': 'path/to/cdr.csv', ...}
        options: 导入选项
    
    Returns:
        导入结果字典
    """
    options = options or {}
    results = {}
    
    async with BusinessMethods() as business:
        # EP数据导入
        if 'ep' in file_paths:
            logger.info("开始EP数据导入测试")
            results['ep'] = await business.import_ep_data(file_paths['ep'], options.get('ep', {}))
        
        # CDR数据导入
        if 'cdr' in file_paths:
            logger.info("开始CDR数据导入测试")
            results['cdr'] = await business.import_cdr_data(file_paths['cdr'], options.get('cdr', {}))
        
        # 站点数据导入
        if 'site' in file_paths:
            logger.info("开始站点数据导入测试")
            results['site'] = await business.import_site_data(file_paths['site'], options.get('site', {}))
        
        # KPI数据导入
        if 'kpi' in file_paths:
            logger.info("开始KPI数据导入测试")
            results['kpi'] = await business.import_kpi_data(file_paths['kpi'], options.get('kpi', {}))
    
    return results


async def test_analysis_workflow(analysis_configs: List[Dict[str, Any]]) -> List[AnalysisResult]:
    """测试完整的分析工作流
    
    Args:
        analysis_configs: 分析配置列表
    
    Returns:
        分析结果列表
    """
    results = []
    
    async with BusinessMethods() as business:
        for config in analysis_configs:
            analysis_type = config.get('type')
            
            if analysis_type == 'coverage':
                result = await business.analyze_coverage(
                    region=config.get('region', {}),
                    options=config.get('options', {})
                )
            elif analysis_type == 'performance':
                result = await business.analyze_performance(
                    time_range=config.get('time_range', {}),
                    options=config.get('options', {})
                )
            elif analysis_type == 'competitor':
                result = await business.analyze_competitor(
                    competitor_data=config.get('competitor_data', {}),
                    options=config.get('options', {})
                )
            else:
                result = AnalysisResult(
                    analysis_type=analysis_type,
                    success=False,
                    errors=[f"不支持的分析类型: {analysis_type}"]
                )
            
            results.append(result)
    
    return results


async def test_report_workflow(report_configs: List[Dict[str, Any]]) -> List[ReportResult]:
    """测试完整的报告生成工作流
    
    Args:
        report_configs: 报告配置列表
    
    Returns:
        报告结果列表
    """
    results = []
    
    async with BusinessMethods() as business:
        for config in report_configs:
            result = await business.generate_report(
                report_type=config.get('type', 'summary'),
                data=config.get('data', {}),
                options=config.get('options', {})
            )
            results.append(result)
    
    return results


# ==================== 性能测试辅助函数 ====================

async def benchmark_import_performance(data_sizes: List[int], 
                                     data_type: str = 'ep') -> Dict[str, Any]:
    """基准测试数据导入性能
    
    Args:
        data_sizes: 数据大小列表（记录数）
        data_type: 数据类型
    
    Returns:
        性能测试结果
    """
    results = {
        'data_type': data_type,
        'benchmarks': [],
        'summary': {}
    }
    
    async with BusinessMethods() as business:
        for size in data_sizes:
            logger.info(f"开始{size}条记录的{data_type}数据导入性能测试")
            
            # 生成测试数据
            test_file = await _generate_test_data_file(data_type, size)
            
            # 执行导入
            start_time = datetime.now()
            
            if data_type == 'ep':
                result = await business.import_ep_data(test_file)
            elif data_type == 'cdr':
                result = await business.import_cdr_data(test_file)
            elif data_type == 'site':
                result = await business.import_site_data(test_file)
            elif data_type == 'kpi':
                result = await business.import_kpi_data(test_file)
            else:
                continue
            
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            
            benchmark = {
                'data_size': size,
                'processing_time': processing_time,
                'records_per_second': size / processing_time if processing_time > 0 else 0,
                'success_rate': result.imported_records / result.total_records if result.total_records > 0 else 0,
                'memory_usage': 0  # 可以添加内存监控
            }
            
            results['benchmarks'].append(benchmark)
            
            # 清理测试文件
            try:
                os.remove(test_file)
            except:
                pass
    
    # 计算汇总统计
    if results['benchmarks']:
        processing_times = [b['processing_time'] for b in results['benchmarks']]
        records_per_second = [b['records_per_second'] for b in results['benchmarks']]
        
        results['summary'] = {
            'avg_processing_time': sum(processing_times) / len(processing_times),
            'max_processing_time': max(processing_times),
            'min_processing_time': min(processing_times),
            'avg_records_per_second': sum(records_per_second) / len(records_per_second),
            'max_records_per_second': max(records_per_second),
            'min_records_per_second': min(records_per_second)
        }
    
    return results


async def _generate_test_data_file(data_type: str, size: int) -> str:
    """生成测试数据文件
    
    Args:
        data_type: 数据类型
        size: 记录数
    
    Returns:
        测试文件路径
    """
    temp_dir = Path(tempfile.gettempdir()) / "connect_e2e_test"
    temp_dir.mkdir(parents=True, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    test_file = temp_dir / f"test_{data_type}_{size}_{timestamp}.csv"
    
    if data_type == 'ep':
        data = {
            'longitude': np.random.uniform(116.0, 117.0, size),
            'latitude': np.random.uniform(39.0, 40.0, size),
            'signal_strength': np.random.uniform(-100, -50, size),
            'timestamp': [datetime.now() - timedelta(minutes=i) for i in range(size)]
        }
    elif data_type == 'cdr':
        data = {
            'call_time': [datetime.now() - timedelta(minutes=i) for i in range(size)],
            'duration': np.random.randint(10, 3600, size),
            'cell_id': [f'CELL{i:06d}' for i in range(size)],
            'call_type': np.random.choice(['voice', 'data'], size)
        }
    elif data_type == 'site':
        data = {
            'site_id': [f'S{i:06d}' for i in range(size)],
            'longitude': np.random.uniform(116.0, 117.0, size),
            'latitude': np.random.uniform(39.0, 40.0, size),
            'site_type': np.random.choice(['macro', 'micro', 'pico'], size),
            'coverage_radius': np.random.randint(200, 2000, size)
        }
    elif data_type == 'kpi':
        data = {
            'timestamp': [datetime.now() - timedelta(hours=i) for i in range(size)],
            'kpi_name': np.random.choice(['throughput', 'latency', 'packet_loss'], size),
            'kpi_value': np.random.uniform(0, 200, size),
            'site_id': [f'S{i%1000:06d}' for i in range(size)]
        }
    else:
        raise ValueError(f"不支持的数据类型: {data_type}")
    
    df = pd.DataFrame(data)
    df.to_csv(test_file, index=False)
    
    return str(test_file)


if __name__ == "__main__":
    # 示例用法
    async def main():
        # 测试数据导入
        file_paths = {
            'ep': 'test_data/ep_sample.csv',
            'cdr': 'test_data/cdr_sample.csv'
        }
        
        import_results = await test_import_workflow(file_paths)
        print("导入结果:", import_results)
        
        # 测试分析
        analysis_configs = [
            {
                'type': 'coverage',
                'region': {'bounds': [116.0, 39.0, 117.0, 40.0]},
                'options': {}
            },
            {
                'type': 'performance',
                'time_range': {'start': '2024-01-01', 'end': '2024-01-02'},
                'options': {}
            }
        ]
        
        analysis_results = await test_analysis_workflow(analysis_configs)
        print("分析结果:", analysis_results)
        
        # 性能基准测试
        benchmark_results = await benchmark_import_performance([1000, 5000, 10000], 'ep')
        print("性能基准测试结果:", benchmark_results)
    
    # 运行示例
    # asyncio.run(main())