# EP性能优化配置文件
# 解决"Disabled complex analysis due to resource constraints"问题

# EP导入器性能配置
telecom:
  ep:
    # 基础配置
    batch_size: 5000
    table_prefix: ep_
    coordinate_validation: true
    
    # 性能优化配置 - 关键参数调整
    large_dataset_threshold: 50000  # 提高阈值从默认10000到50000
    enable_performance_optimizations: true
    skip_analysis_for_large_datasets: false  # 强制启用复杂分析
    max_neighbor_search_records: 100000  # 提高从默认50000到100000
    use_optimized_algorithms: true
    
    # 分析功能启用配置
    enable_neighbor_detection: true
    enable_interference_analysis: true
    enable_topology_analysis: true
    enable_spatial_indexing: true
    enable_coverage_calculation: true
    
    # 内存和资源管理
    memory_threshold_gb: 1.0  # 降低内存阈值要求
    max_memory_usage_mb: 4096  # 增加最大内存使用限制
    processing_timeout_seconds: 7200  # 增加处理超时时间
    
    # 空间分析配置
    spatial_buffer_meters: 1000.0
    coordinate_system: "EPSG:4326"
    
    # 质量阈值
    max_error_rate: 0.01  # 稍微放宽错误率
    min_data_quality_score: 0.95  # 稍微降低质量要求

# 系统资源配置
performance:
  max_memory_usage_mb: 4096  # 增加系统内存限制
  processing_timeout_seconds: 7200
  parallel_workers: 6  # 增加并行工作线程
  enable_resource_monitoring: true
  
# 日志配置 - 用于调试性能问题
logging:
  level: INFO
  performance_logging: true
  resource_monitoring: true

# 使用说明:
# 1. 将此文件复制到config/environments/目录下
# 2. 在导入EP数据时使用: --config ep_performance_optimization.yaml
# 3. 或者将相关配置合并到现有的development.yaml中