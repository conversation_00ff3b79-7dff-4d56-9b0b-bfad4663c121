#!/usr/bin/env python3
"""综合测试框架命令行工具

该工具提供命令行接口来使用综合测试框架的各种功能。

使用示例:
    python cli.py run --suite unit_tests
    python cli.py run-all --parallel
    python cli.py check-gates --gate production_ready
    python cli.py report --format html
    python cli.py list-suites
    python cli.py validate-config
"""

import argparse
import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import List, Optional

from .comprehensive_test_framework import ComprehensiveTestFramework
from .comprehensive_test_framework_execution import TestExecutionEngine, QualityGateChecker
from .comprehensive_test_framework_reporting import TestReportGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestFrameworkCLI:
    """测试框架命令行接口"""
    
    def __init__(self, work_dir: str = "tests/framework_workspace"):
        """初始化CLI
        
        Args:
            work_dir: 工作目录路径
        """
        self.work_dir = Path(work_dir)
        self.framework = None
        self.execution_engine = None
        self.quality_checker = None
        self.report_generator = None
    
    async def initialize(self):
        """初始化测试框架组件"""
        logger.info("初始化测试框架...")
        
        self.framework = ComprehensiveTestFramework(str(self.work_dir))
        self.execution_engine = TestExecutionEngine(self.framework)
        self.quality_checker = QualityGateChecker(self.framework)
        self.report_generator = TestReportGenerator(self.framework)
        
        logger.info("测试框架初始化完成")
    
    async def run_test_suite(self, suite_name: str, verbose: bool = False) -> int:
        """运行单个测试套件
        
        Args:
            suite_name: 测试套件名称
            verbose: 是否显示详细信息
            
        Returns:
            退出代码 (0=成功, 1=失败)
        """
        try:
            logger.info(f"运行测试套件: {suite_name}")
            
            result = await self.execution_engine.execute_test_suite(suite_name)
            
            # 输出结果
            print(f"\n测试套件 '{suite_name}' 执行结果:")
            print(f"状态: {result.status.value}")
            print(f"总测试数: {result.test_count}")
            print(f"通过: {result.passed_count}")
            print(f"失败: {result.failed_count}")
            print(f"跳过: {result.skipped_count}")
            print(f"错误: {result.error_count}")
            print(f"成功率: {result.success_rate:.1f}%")
            print(f"耗时: {result.duration:.2f}秒")
            
            if result.coverage_percentage is not None:
                print(f"覆盖率: {result.coverage_percentage:.1f}%")
            
            if verbose and result.error_details:
                print(f"\n错误详情:")
                for error in result.error_details:
                    print(f"  - {error}")
            
            return 0 if result.is_success else 1
            
        except Exception as e:
            logger.error(f"运行测试套件失败: {e}")
            return 2
    
    async def run_test_suites(self, suite_names: List[str], parallel: bool = False, 
                            verbose: bool = False) -> int:
        """运行多个测试套件
        
        Args:
            suite_names: 测试套件名称列表
            parallel: 是否并行执行
            verbose: 是否显示详细信息
            
        Returns:
            退出代码 (0=成功, 1=失败)
        """
        try:
            logger.info(f"运行测试套件: {suite_names} (并行: {parallel})")
            
            results = await self.execution_engine.execute_test_suites(
                suite_names, parallel=parallel
            )
            
            # 计算总体统计
            total_tests = sum(r.test_count for r in results)
            total_passed = sum(r.passed_count for r in results)
            total_failed = sum(r.failed_count for r in results)
            total_duration = sum(r.duration for r in results)
            overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
            
            # 输出结果摘要
            print(f"\n多套件执行结果摘要:")
            print(f"执行套件数: {len(results)}")
            print(f"总测试数: {total_tests}")
            print(f"总通过数: {total_passed}")
            print(f"总失败数: {total_failed}")
            print(f"整体成功率: {overall_success_rate:.1f}%")
            print(f"总耗时: {total_duration:.2f}秒")
            
            # 输出各套件详情
            print(f"\n各套件详情:")
            for result in results:
                status_icon = "✓" if result.is_success else "✗"
                print(f"  {status_icon} {result.suite_name}: {result.status.value} "
                      f"({result.passed_count}/{result.test_count}, {result.success_rate:.1f}%)")
                
                if verbose and result.error_details:
                    for error in result.error_details:
                        print(f"    - {error}")
            
            # 判断整体是否成功
            all_success = all(r.is_success for r in results)
            return 0 if all_success else 1
            
        except Exception as e:
            logger.error(f"运行测试套件失败: {e}")
            return 2
    
    async def run_all_test_suites(self, parallel: bool = False, verbose: bool = False) -> int:
        """运行所有测试套件
        
        Args:
            parallel: 是否并行执行独立套件
            verbose: 是否显示详细信息
            
        Returns:
            退出代码 (0=成功, 1=失败)
        """
        try:
            logger.info("运行所有测试套件...")
            
            # 获取所有测试套件
            all_suites = self.framework.list_test_suites()
            print(f"发现 {len(all_suites)} 个测试套件")
            
            # 确定执行顺序
            execution_order = self.framework.get_execution_order(all_suites)
            print(f"执行顺序: {' -> '.join(execution_order)}")
            
            # 执行所有测试套件
            return await self.run_test_suites(execution_order, parallel=parallel, verbose=verbose)
            
        except Exception as e:
            logger.error(f"运行所有测试套件失败: {e}")
            return 2
    
    async def check_quality_gates(self, execution_results: List = None, 
                                gate_names: List[str] = None, verbose: bool = False) -> int:
        """检查质量门控
        
        Args:
            execution_results: 测试执行结果列表，None表示从最近的执行中获取
            gate_names: 要检查的质量门控名称列表，None表示检查所有
            verbose: 是否显示详细信息
            
        Returns:
            退出代码 (0=成功, 1=失败)
        """
        try:
            if execution_results is None:
                # 如果没有提供执行结果，尝试从框架中获取最近的结果
                execution_results = getattr(self.framework, '_last_execution_results', [])
                if not execution_results:
                    print("没有可用的测试执行结果，请先运行测试")
                    return 1
            
            logger.info("检查质量门控...")
            
            gate_results = await self.quality_checker.check_quality_gates(
                execution_results, gate_names
            )
            
            # 输出检查结果
            print(f"\n质量门控检查结果:")
            all_passed = True
            
            for gate_name, gate_result in gate_results.items():
                status_icon = "✓" if gate_result['overall_passed'] else "✗"
                status_text = "通过" if gate_result['overall_passed'] else "失败"
                print(f"  {status_icon} {gate_name}: {status_text}")
                
                if not gate_result['overall_passed']:
                    all_passed = False
                
                if verbose:
                    # 输出详细检查项
                    for check_name, check_result in gate_result['checks'].items():
                        check_icon = "✓" if check_result['passed'] else "✗"
                        message = check_result.get('message', '')
                        print(f"    {check_icon} {check_name}: {message}")
            
            if all_passed:
                print("\n🎉 所有质量门控检查通过!")
                return 0
            else:
                print("\n⚠️ 部分质量门控检查未通过")
                return 1
            
        except Exception as e:
            logger.error(f"检查质量门控失败: {e}")
            return 2
    
    async def generate_report(self, execution_results: List = None, 
                            format_type: str = "html", output_file: str = None) -> int:
        """生成测试报告
        
        Args:
            execution_results: 测试执行结果列表，None表示从最近的执行中获取
            format_type: 报告格式 (html, json, performance, coverage)
            output_file: 输出文件路径，None表示自动生成
            
        Returns:
            退出代码 (0=成功, 1=失败)
        """
        try:
            if execution_results is None:
                # 如果没有提供执行结果，尝试从框架中获取最近的结果
                execution_results = getattr(self.framework, '_last_execution_results', [])
                if not execution_results:
                    print("没有可用的测试执行结果，请先运行测试")
                    return 1
            
            logger.info(f"生成 {format_type} 格式报告...")
            
            if format_type == "html":
                report_file = self.report_generator.generate_html_report(
                    execution_results, output_file=output_file
                )
            elif format_type == "json":
                report_file = self.report_generator.generate_json_report(
                    execution_results, output_file=output_file
                )
            elif format_type == "performance":
                report_file = self.report_generator.generate_performance_report(
                    execution_results, output_file=output_file
                )
            elif format_type == "coverage":
                report_file = self.report_generator.generate_coverage_report(
                    execution_results, output_file=output_file
                )
            else:
                print(f"不支持的报告格式: {format_type}")
                return 1
            
            print(f"报告已生成: {report_file}")
            return 0
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return 2
    
    def list_test_suites(self, verbose: bool = False) -> int:
        """列出所有测试套件
        
        Args:
            verbose: 是否显示详细信息
            
        Returns:
            退出代码 (0=成功)
        """
        try:
            suites = self.framework.list_test_suites()
            
            print(f"\n发现 {len(suites)} 个测试套件:")
            
            for suite_name in suites:
                suite_config = self.framework.get_suite(suite_name)
                if suite_config:
                    print(f"  - {suite_name} ({suite_config.priority.value}, {suite_config.test_type.value})")
                    
                    if verbose:
                        print(f"    描述: {suite_config.description}")
                        print(f"    路径: {', '.join(suite_config.test_paths)}")
                        print(f"    标记: {', '.join(suite_config.markers)}")
                        print(f"    依赖: {', '.join(suite_config.dependencies) if suite_config.dependencies else '无'}")
                        print(f"    并行: {'是' if suite_config.parallel else '否'}")
                        print(f"    超时: {suite_config.timeout}秒")
                        print()
                else:
                    print(f"  - {suite_name} (配置未找到)")
            
            return 0
            
        except Exception as e:
            logger.error(f"列出测试套件失败: {e}")
            return 2
    
    def list_quality_gates(self, verbose: bool = False) -> int:
        """列出所有质量门控
        
        Args:
            verbose: 是否显示详细信息
            
        Returns:
            退出代码 (0=成功)
        """
        try:
            gates = self.framework.list_quality_gates()
            
            print(f"\n发现 {len(gates)} 个质量门控:")
            
            for gate_name in gates:
                gate_config = self.framework.get_quality_gate(gate_name)
                if gate_config:
                    blocking_text = "阻塞" if gate_config.blocking else "非阻塞"
                    print(f"  - {gate_name} ({blocking_text})")
                    
                    if verbose:
                        print(f"    描述: {gate_config.description}")
                        print(f"    覆盖率阈值: {gate_config.coverage_threshold}%")
                        print(f"    成功率阈值: {gate_config.success_rate_threshold}%")
                        if gate_config.performance_thresholds:
                            print(f"    性能阈值: {gate_config.performance_thresholds}")
                        if gate_config.security_checks:
                            print(f"    安全检查: {', '.join(gate_config.security_checks)}")
                        if gate_config.custom_checks:
                            print(f"    自定义检查: {', '.join(gate_config.custom_checks)}")
                        print()
                else:
                    print(f"  - {gate_name} (配置未找到)")
            
            return 0
            
        except Exception as e:
            logger.error(f"列出质量门控失败: {e}")
            return 2
    
    def validate_config(self) -> int:
        """验证配置文件
        
        Returns:
            退出代码 (0=成功, 1=失败)
        """
        try:
            logger.info("验证配置文件...")
            
            # 验证测试套件配置
            suites = self.framework.list_test_suites()
            print(f"✓ 发现 {len(suites)} 个测试套件")
            
            # 验证依赖关系
            for suite_name in suites:
                suite_config = self.framework.get_suite(suite_name)
                if suite_config and suite_config.dependencies:
                    for dep in suite_config.dependencies:
                        if dep not in suites:
                            print(f"✗ 测试套件 '{suite_name}' 依赖的套件 '{dep}' 不存在")
                            return 1
            
            print("✓ 测试套件依赖关系验证通过")
            
            # 验证质量门控配置
            gates = self.framework.list_quality_gates()
            print(f"✓ 发现 {len(gates)} 个质量门控")
            
            # 验证执行顺序
            try:
                execution_order = self.framework.get_execution_order(suites)
                print(f"✓ 执行顺序验证通过: {' -> '.join(execution_order)}")
            except Exception as e:
                print(f"✗ 执行顺序验证失败: {e}")
                return 1
            
            print("\n🎉 配置文件验证通过!")
            return 0
            
        except Exception as e:
            logger.error(f"验证配置文件失败: {e}")
            return 2
    
    def show_status(self) -> int:
        """显示框架状态
        
        Returns:
            退出代码 (0=成功)
        """
        try:
            print(f"\n综合测试框架状态:")
            print(f"工作目录: {self.work_dir}")
            print(f"配置文件: {self.work_dir / 'test_framework_config.yaml'}")
            
            # 检查目录结构
            required_dirs = ['test_reports', 'logs', 'artifacts']
            for dir_name in required_dirs:
                dir_path = self.work_dir / dir_name
                status = "存在" if dir_path.exists() else "不存在"
                print(f"{dir_name} 目录: {status}")
            
            # 显示测试套件和质量门控数量
            if self.framework:
                suites_count = len(self.framework.list_test_suites())
                gates_count = len(self.framework.list_quality_gates())
                print(f"测试套件数量: {suites_count}")
                print(f"质量门控数量: {gates_count}")
            
            return 0
            
        except Exception as e:
            logger.error(f"显示状态失败: {e}")
            return 2


def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器
    
    Returns:
        参数解析器
    """
    parser = argparse.ArgumentParser(
        description="综合测试框架命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s run --suite unit_tests                    # 运行单个测试套件
  %(prog)s run --suite unit_tests integration_tests # 运行多个测试套件
  %(prog)s run-all --parallel                       # 并行运行所有测试套件
  %(prog)s check-gates --gate production_ready      # 检查特定质量门控
  %(prog)s report --format html                     # 生成HTML报告
  %(prog)s list-suites --verbose                    # 列出所有测试套件（详细）
  %(prog)s validate-config                          # 验证配置文件
  %(prog)s status                                   # 显示框架状态
        """
    )
    
    parser.add_argument(
        "--work-dir", 
        default="tests/framework_workspace",
        help="工作目录路径 (默认: tests/framework_workspace)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细信息"
    )
    
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="静默模式"
    )
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # run 命令
    run_parser = subparsers.add_parser("run", help="运行测试套件")
    run_parser.add_argument(
        "--suite", "-s",
        nargs="+",
        required=True,
        help="要运行的测试套件名称"
    )
    run_parser.add_argument(
        "--parallel", "-p",
        action="store_true",
        help="并行执行独立的测试套件"
    )
    
    # run-all 命令
    run_all_parser = subparsers.add_parser("run-all", help="运行所有测试套件")
    run_all_parser.add_argument(
        "--parallel", "-p",
        action="store_true",
        help="并行执行独立的测试套件"
    )
    
    # check-gates 命令
    gates_parser = subparsers.add_parser("check-gates", help="检查质量门控")
    gates_parser.add_argument(
        "--gate", "-g",
        nargs="+",
        help="要检查的质量门控名称（不指定则检查所有）"
    )
    
    # report 命令
    report_parser = subparsers.add_parser("report", help="生成测试报告")
    report_parser.add_argument(
        "--format", "-f",
        choices=["html", "json", "performance", "coverage"],
        default="html",
        help="报告格式 (默认: html)"
    )
    report_parser.add_argument(
        "--output", "-o",
        help="输出文件路径（不指定则自动生成）"
    )
    
    # list-suites 命令
    subparsers.add_parser("list-suites", help="列出所有测试套件")
    
    # list-gates 命令
    subparsers.add_parser("list-gates", help="列出所有质量门控")
    
    # validate-config 命令
    subparsers.add_parser("validate-config", help="验证配置文件")
    
    # status 命令
    subparsers.add_parser("status", help="显示框架状态")
    
    return parser


async def main() -> int:
    """主函数
    
    Returns:
        退出代码
    """
    parser = create_parser()
    args = parser.parse_args()
    
    # 配置日志级别
    if args.quiet:
        logging.getLogger().setLevel(logging.ERROR)
    elif args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建CLI实例
    cli = TestFrameworkCLI(args.work_dir)
    
    try:
        # 对于需要框架初始化的命令，先初始化
        if args.command in ['run', 'run-all', 'check-gates', 'report']:
            await cli.initialize()
        elif args.command in ['list-suites', 'list-gates', 'validate-config']:
            # 这些命令只需要基础框架
            cli.framework = ComprehensiveTestFramework(args.work_dir)
        
        # 执行相应命令
        if args.command == "run":
            if len(args.suite) == 1:
                return await cli.run_test_suite(args.suite[0], args.verbose)
            else:
                return await cli.run_test_suites(args.suite, args.parallel, args.verbose)
        
        elif args.command == "run-all":
            return await cli.run_all_test_suites(args.parallel, args.verbose)
        
        elif args.command == "check-gates":
            return await cli.check_quality_gates(gate_names=args.gate, verbose=args.verbose)
        
        elif args.command == "report":
            return await cli.generate_report(format_type=args.format, output_file=args.output)
        
        elif args.command == "list-suites":
            return cli.list_test_suites(args.verbose)
        
        elif args.command == "list-gates":
            return cli.list_quality_gates(args.verbose)
        
        elif args.command == "validate-config":
            return cli.validate_config()
        
        elif args.command == "status":
            return cli.show_status()
        
        else:
            parser.print_help()
            return 1
    
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 130
    except Exception as e:
        logger.error(f"执行命令失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 2


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)