name: Connect测试监控集成

# Connect电信数据分析平台 - 测试监控和Dashboard集成工作流
# 自动运行全套测试并将结果集成到监控系统
# 支持性能基准测试、安全扫描、质量分析和实时监控

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # 每天凌晨2点运行完整测试套件
    - cron: '0 2 * * *'
    # 每周一凌晨3点运行深度安全扫描
    - cron: '0 3 * * 1'
  workflow_dispatch:
    inputs:
      test_suite:
        description: '要运行的测试套件'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - unit
          - integration
          - e2e
          - performance
          - security
          - quality
      environment:
        description: '测试环境'
        required: false
        default: 'development'
        type: choice
        options:
          - development
          - staging
          - production
      enable_monitoring:
        description: '启用监控集成'
        required: false
        default: true
        type: boolean
      deep_security_scan:
        description: '启用深度安全扫描'
        required: false
        default: false
        type: boolean

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'
  POSTGRES_VERSION: '13'
  REDIS_VERSION: '7'
  TEST_ENVIRONMENT: ${{ github.event.inputs.environment || 'development' }}
  ENABLE_MONITORING: ${{ github.event.inputs.enable_monitoring || 'true' }}
  # 监控系统配置
  PROMETHEUS_GATEWAY: ${{ secrets.PROMETHEUS_GATEWAY }}
  GRAFANA_URL: ${{ secrets.GRAFANA_URL }}
  GRAFANA_API_KEY: ${{ secrets.GRAFANA_API_KEY }}
  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
  # 邮件通知配置
  SMTP_SERVER: ${{ secrets.SMTP_SERVER }}
  SMTP_USERNAME: ${{ secrets.SMTP_USERNAME }}
  SMTP_PASSWORD: ${{ secrets.SMTP_PASSWORD }}
  FROM_EMAIL: ${{ secrets.FROM_EMAIL }}
  TO_EMAILS: ${{ secrets.TO_EMAILS }}
  # 自定义Webhook
  CUSTOM_WEBHOOKS: ${{ secrets.CUSTOM_WEBHOOKS }}
  # 告警阈值
  ALERT_TEST_FAILURE_RATE: '0.05'
  ALERT_RESPONSE_TIME: '3.0'
  ALERT_CRITICAL_VULNS: '0'
  ALERT_CODE_COVERAGE: '85.0'

jobs:
  # 环境准备
  setup:
    runs-on: ubuntu-latest
    outputs:
      test-matrix: ${{ steps.test-matrix.outputs.matrix }}
      cache-key: ${{ steps.cache-key.outputs.key }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Generate cache key
        id: cache-key
        run: |
          echo "key=test-deps-${{ hashFiles('requirements.txt', 'package.json', 'pyproject.toml') }}" >> $GITHUB_OUTPUT
          
      - name: Generate test matrix
        id: test-matrix
        run: |
          TEST_SUITE="${{ github.event.inputs.test_suite || 'all' }}"
          if [ "$TEST_SUITE" = "all" ]; then
            echo 'matrix={"test_type":["unit","integration","e2e","performance","security","quality"]}' >> $GITHUB_OUTPUT
          else
            echo "matrix={\"test_type\":[\"$TEST_SUITE\"]}" >> $GITHUB_OUTPUT
          fi
          
      - name: Setup monitoring configuration
        if: env.ENABLE_MONITORING == 'true'
        run: |
          mkdir -p monitoring/config
          echo "Setting up monitoring integration..."
          echo "Environment: ${{ env.TEST_ENVIRONMENT }}"
          echo "Prometheus Gateway: ${{ env.PROMETHEUS_GATEWAY }}"
          echo "Grafana URL: ${{ env.GRAFANA_URL }}"

  # 单元测试
  unit-tests:
    needs: setup
    if: contains(fromJson(needs.setup.outputs.test-matrix).test_type, 'unit')
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.10', '3.11', '3.12']
    
    services:
      postgres:
        image: postgis/postgis:13-3.1
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
          
      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/pip
            .venv
          key: ${{ needs.setup.outputs.cache-key }}-py${{ matrix.python-version }}
          restore-keys: |
            ${{ needs.setup.outputs.cache-key }}-
            
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          
      - name: Set up test environment
        run: |
          cp .env.test.example .env.test
          export DATABASE_URL="postgresql://testuser:testpass@localhost:5432/testdb"
          export REDIS_URL="redis://localhost:6379/0"
          
      - name: Run unit tests with coverage
        run: |
          pytest tests/unit/ \
            --cov=src \
            --cov-report=xml \
            --cov-report=html \
            --cov-report=term \
            --junitxml=test-results/unit-tests.xml \
            --html=test-results/unit-tests.html \
            --self-contained-html \
            -v
            
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: unit-test-results-py${{ matrix.python-version }}
          path: |
            test-results/
            htmlcov/
            coverage.xml
            
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unit-tests
          name: unit-tests-py${{ matrix.python-version }}

  # 集成测试
  integration-tests:
    needs: setup
    if: contains(fromJson(needs.setup.outputs.test-matrix).test_type, 'integration')
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgis/postgis:13-3.1
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          
      - name: Set up test database
        run: |
          export DATABASE_URL="postgresql://testuser:testpass@localhost:5432/testdb"
          python -m alembic upgrade head
          
      - name: Run integration tests
        run: |
          pytest tests/integration/ \
            --junitxml=test-results/integration-tests.xml \
            --html=test-results/integration-tests.html \
            --self-contained-html \
            -v
            
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: test-results/

  # E2E测试
  e2e-tests:
    needs: setup
    if: contains(fromJson(needs.setup.outputs.test-matrix).test_type, 'e2e')
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgis/postgis:13-3.1
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          npm ci
          
      - name: Build frontend
        run: |
          npm run build
          
      - name: Start application
        run: |
          export DATABASE_URL="postgresql://testuser:testpass@localhost:5432/testdb"
          export REDIS_URL="redis://localhost:6379/0"
          python -m alembic upgrade head
          python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 &
          sleep 10
          
      - name: Run E2E tests
        run: |
          pytest tests/e2e/ \
            --junitxml=test-results/e2e-tests.xml \
            --html=test-results/e2e-tests.html \
            --self-contained-html \
            -v
            
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-test-results
          path: test-results/

  # 性能测试 - 增强版
  performance-tests:
    needs: setup
    if: contains(fromJson(needs.setup.outputs.test-matrix).test_type, 'performance')
    runs-on: ubuntu-latest
    timeout-minutes: 60
    
    services:
      postgres:
        image: postgis/postgis:13-3.1
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
          # 性能测试优化配置
          POSTGRES_SHARED_BUFFERS: 256MB
          POSTGRES_EFFECTIVE_CACHE_SIZE: 1GB
          POSTGRES_WORK_MEM: 16MB
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          --shm-size=1g
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          --memory=512m
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          # 安装性能测试专用工具
          pip install memory-profiler psutil py-spy
          
      - name: Setup test database with optimizations
        run: |
          export DATABASE_URL="postgresql://testuser:testpass@localhost:5432/testdb"
          export REDIS_URL="redis://localhost:6379/0"
          python -m alembic upgrade head
          # 创建性能测试索引
          psql $DATABASE_URL -c "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ep_data_geom ON ep_data USING GIST (geom);"
          psql $DATABASE_URL -c "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_ep_data_timestamp ON ep_data (timestamp);"
          psql $DATABASE_URL -c "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cdr_data_geom ON cdr_data USING GIST (geom);"
          
      - name: Generate large test datasets
        run: |
          echo "Generating large test datasets for performance testing..."
          python -c "
          import pandas as pd
          import numpy as np
          from datetime import datetime, timedelta
          
          # 生成500万行EP数据
          print('Generating 5M EP records...')
          ep_data = pd.DataFrame({
              'timestamp': pd.date_range('2024-01-01', periods=5000000, freq='1min'),
              'longitude': np.random.uniform(116.0, 117.0, 5000000),
              'latitude': np.random.uniform(39.0, 40.0, 5000000),
              'rsrp': np.random.uniform(-120, -60, 5000000),
              'rsrq': np.random.uniform(-20, -5, 5000000),
              'sinr': np.random.uniform(-10, 30, 5000000),
              'cell_id': np.random.randint(1, 10000, 5000000)
          })
          ep_data.to_csv('test_data/large_ep_data.csv', index=False)
          
          # 生成100万行CDR数据
          print('Generating 1M CDR records...')
          cdr_data = pd.DataFrame({
              'start_time': pd.date_range('2024-01-01', periods=1000000, freq='5min'),
              'duration': np.random.randint(10, 3600, 1000000),
              'start_longitude': np.random.uniform(116.0, 117.0, 1000000),
              'start_latitude': np.random.uniform(39.0, 40.0, 1000000),
              'end_longitude': np.random.uniform(116.0, 117.0, 1000000),
              'end_latitude': np.random.uniform(39.0, 40.0, 1000000),
              'call_type': np.random.choice(['voice', 'data', 'sms'], 1000000)
          })
          cdr_data.to_csv('test_data/large_cdr_data.csv', index=False)
          print('Test data generation completed.')
          "
          
      - name: Run enhanced performance benchmarks
        run: |
          echo "Running enhanced performance benchmarks..."
          export DATABASE_URL="postgresql://testuser:testpass@localhost:5432/testdb"
          export REDIS_URL="redis://localhost:6379/0"
          
          # 运行增强性能基准测试
          python -m pytest tests/performance/test_enhanced_performance_benchmarks.py \
            --junitxml=test-results/performance-tests.xml \
            --html=test-results/performance-tests.html \
            --self-contained-html \
            --benchmark-json=test-results/benchmark.json \
            --tb=short \
            -v -s
            
          # 运行内存性能分析
          mprof run python -m pytest tests/performance/test_memory_performance.py -v
          mprof plot -o test-results/memory-profile.png
          
          # 生成性能报告
          python tests/performance/generate_performance_report.py \
            --input test-results/benchmark.json \
            --output test-results/performance-report.html
            
      - name: Analyze performance metrics
        run: |
          echo "Analyzing performance metrics..."
          python -c "
          import json
          import os
          
          # 读取基准测试结果
          with open('test-results/benchmark.json', 'r') as f:
              results = json.load(f)
          
          # 分析关键指标
          print('=== Performance Analysis ===')
          for test in results.get('tests', []):
              name = test.get('name', 'Unknown')
              stats = test.get('stats', {})
              mean_time = stats.get('mean', 0)
              print(f'{name}: {mean_time:.4f}s')
              
              # 检查是否超过阈值
              if 'data_import' in name and mean_time > 10.0:
                  print(f'WARNING: Data import time {mean_time:.2f}s exceeds 10s threshold')
              elif 'geospatial' in name and mean_time > 3.0:
                  print(f'WARNING: Geospatial query time {mean_time:.2f}s exceeds 3s threshold')
          "
          
      - name: Upload performance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-test-results
          path: |
            test-results/
            mprofile_*.dat
            test_data/large_*.csv

  # 安全测试 - 增强版
  security-tests:
    needs: setup
    if: contains(fromJson(needs.setup.outputs.test-matrix).test_type, 'security')
    runs-on: ubuntu-latest
    timeout-minutes: 45
    
    services:
      postgres:
        image: postgis/postgis:13-3.1
        env:
          POSTGRES_PASSWORD: testpass
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Set up Node.js for frontend security scanning
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          # 安装安全扫描工具
          pip install bandit safety semgrep sqlmap-python
          npm ci
          # 安装前端安全扫描工具
          npm install -g audit-ci retire
          
      - name: Static Code Security Analysis
        run: |
          echo "Running static code security analysis..."
          mkdir -p test-results/security
          
          # Bandit - Python安全扫描
          echo "Running Bandit security scan..."
          bandit -r src/ -f json -o test-results/security/bandit-report.json || true
          bandit -r src/ -f txt -o test-results/security/bandit-report.txt || true
          
          # Safety - 依赖漏洞扫描
          echo "Running Safety dependency scan..."
          safety check --json --output test-results/security/safety-report.json || true
          safety check --output test-results/security/safety-report.txt || true
          
          # Semgrep - 高级静态分析
          echo "Running Semgrep advanced static analysis..."
          semgrep --config=auto --json --output=test-results/security/semgrep-report.json src/ || true
          
      - name: Frontend Security Scanning
        run: |
          echo "Running frontend security scanning..."
          
          # NPM Audit - Node.js依赖漏洞扫描
          echo "Running NPM audit..."
          npm audit --json > test-results/security/npm-audit.json || true
          
          # Retire.js - JavaScript库漏洞扫描
          echo "Running Retire.js scan..."
          retire --outputformat json --outputpath test-results/security/retire-report.json || true
          
      - name: Database Security Assessment
        run: |
          echo "Running database security assessment..."
          export DATABASE_URL="postgresql://testuser:testpass@localhost:5432/testdb"
          
          # 数据库配置安全检查
          python -c "
          import psycopg2
          import json
          
          conn = psycopg2.connect('$DATABASE_URL')
          cur = conn.cursor()
          
          security_checks = []
          
          # 检查用户权限
          cur.execute(\"SELECT usename, usesuper, usecreatedb FROM pg_user;\")
          users = cur.fetchall()
          security_checks.append({'check': 'user_privileges', 'results': users})
          
          # 检查数据库配置
          cur.execute(\"SHOW all;\")
          config = cur.fetchall()
          security_checks.append({'check': 'database_config', 'count': len(config)})
          
          # 保存结果
          with open('test-results/security/db-security-check.json', 'w') as f:
              json.dump(security_checks, f, indent=2, default=str)
          
          conn.close()
          "
          
      - name: API Security Testing
        run: |
          echo "Running API security testing..."
          export DATABASE_URL="postgresql://testuser:testpass@localhost:5432/testdb"
          export REDIS_URL="redis://localhost:6379/0"
          
          # 启动应用进行安全测试
          python -m alembic upgrade head
          python -m uvicorn src.main:app --host 0.0.0.0 --port 8000 &
          APP_PID=$!
          sleep 15
          
          # API安全测试
          python -c "
          import requests
          import json
          import time
          
          base_url = 'http://localhost:8000'
          security_results = []
          
          # 测试SQL注入防护
          try:
              response = requests.get(f'{base_url}/api/v1/data?filter=1\' OR 1=1--')
              security_results.append({
                  'test': 'sql_injection_protection',
                  'status_code': response.status_code,
                  'protected': response.status_code != 200 or 'error' in response.text.lower()
              })
          except Exception as e:
              security_results.append({'test': 'sql_injection_protection', 'error': str(e)})
          
          # 测试XSS防护
          try:
              response = requests.post(f'{base_url}/api/v1/data', 
                  json={'name': '<script>alert(\"xss\")</script>'})
              security_results.append({
                  'test': 'xss_protection',
                  'status_code': response.status_code,
                  'protected': '<script>' not in response.text
              })
          except Exception as e:
              security_results.append({'test': 'xss_protection', 'error': str(e)})
          
          # 测试认证绕过
          try:
              response = requests.get(f'{base_url}/api/v1/admin/users')
              security_results.append({
                  'test': 'authentication_bypass',
                  'status_code': response.status_code,
                  'protected': response.status_code == 401 or response.status_code == 403
              })
          except Exception as e:
              security_results.append({'test': 'authentication_bypass', 'error': str(e)})
          
          # 保存结果
          with open('test-results/security/api-security-test.json', 'w') as f:
              json.dump(security_results, f, indent=2)
          "
          
          # 停止应用
          kill $APP_PID || true
          
      - name: Deep Security Scan (Optional)
        if: github.event.inputs.deep_security_scan == 'true' || github.event.schedule
        run: |
          echo "Running deep security scan..."
          
          # OWASP ZAP扫描 (如果可用)
          if command -v docker &> /dev/null; then
              echo "Running OWASP ZAP baseline scan..."
              docker run -v $(pwd):/zap/wrk/:rw \
                  -t owasp/zap2docker-stable zap-baseline.py \
                  -t http://host.docker.internal:8000 \
                  -J test-results/security/zap-report.json || true
          fi
          
      - name: Run comprehensive security tests
        run: |
          echo "Running comprehensive security test suite..."
          export DATABASE_URL="postgresql://testuser:testpass@localhost:5432/testdb"
          export REDIS_URL="redis://localhost:6379/0"
          
          # 运行扩展安全测试
          python -m pytest tests/security/test_security_extended.py \
            --junitxml=test-results/security/security-tests.xml \
            --html=test-results/security/security-tests.html \
            --self-contained-html \
            --tb=short \
            -v -s
            
      - name: Generate security summary report
        if: always()
        run: |
          echo "Generating security summary report..."
          python -c "
          import json
          import os
          from pathlib import Path
          
          security_dir = Path('test-results/security')
          summary = {
              'timestamp': '$(date -Iseconds)',
              'environment': '${{ env.TEST_ENVIRONMENT }}',
              'scans_completed': [],
              'critical_issues': 0,
              'high_issues': 0,
              'medium_issues': 0,
              'low_issues': 0
          }
          
          # 统计各种扫描结果
          for report_file in security_dir.glob('*.json'):
              try:
                  with open(report_file) as f:
                      data = json.load(f)
                  summary['scans_completed'].append(report_file.name)
                  
                  # 根据不同报告格式统计问题
                  if 'bandit' in report_file.name:
                      for result in data.get('results', []):
                          severity = result.get('issue_severity', '').lower()
                          if severity == 'high':
                              summary['high_issues'] += 1
                          elif severity == 'medium':
                              summary['medium_issues'] += 1
                          elif severity == 'low':
                              summary['low_issues'] += 1
                              
              except Exception as e:
                  print(f'Error processing {report_file}: {e}')
          
          # 保存汇总报告
          with open('test-results/security/security-summary.json', 'w') as f:
              json.dump(summary, f, indent=2)
          
          print(f'Security Summary:')
          print(f'Critical: {summary[\"critical_issues\"]}')
          print(f'High: {summary[\"high_issues\"]}')
          print(f'Medium: {summary[\"medium_issues\"]}')
          print(f'Low: {summary[\"low_issues\"]}')
          
          # 检查是否有严重安全问题
          if summary['critical_issues'] > 0:
              print('ERROR: Critical security issues found!')
              exit(1)
          "
          
      - name: Upload security results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-test-results
          path: test-results/security/

  # 质量测试
  quality-tests:
    needs: setup
    if: contains(fromJson(needs.setup.outputs.test-matrix).test_type, 'quality')
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt
          # 安装代码质量工具
          pip install pylint flake8 mypy black isort radon
          npm ci
          npm install -g eslint prettier typescript
          
      - name: Python Code Quality Analysis
        run: |
          echo "Running Python code quality analysis..."
          mkdir -p test-results/quality
          
          # Pylint - 代码质量检查
          echo "Running Pylint..."
          pylint src/ --output-format=json > test-results/quality/pylint-report.json || true
          pylint src/ --output-format=text > test-results/quality/pylint-report.txt || true
          
          # Flake8 - 代码风格检查
          echo "Running Flake8..."
          flake8 src/ --format=json --output-file=test-results/quality/flake8-report.json || true
          flake8 src/ --output-file=test-results/quality/flake8-report.txt || true
          
          # MyPy - 类型检查
          echo "Running MyPy..."
          mypy src/ --json-report test-results/quality/mypy-report || true
          
          # Radon - 复杂度分析
          echo "Running Radon complexity analysis..."
          radon cc src/ --json > test-results/quality/radon-cc.json || true
          radon mi src/ --json > test-results/quality/radon-mi.json || true
          
          # Black - 代码格式检查
          echo "Running Black format check..."
          black --check --diff src/ > test-results/quality/black-report.txt || true
          
          # isort - 导入排序检查
          echo "Running isort check..."
          isort --check-only --diff src/ > test-results/quality/isort-report.txt || true
          
      - name: Frontend Code Quality Analysis
        run: |
          echo "Running frontend code quality analysis..."
          
          # ESLint - JavaScript/TypeScript代码检查
          echo "Running ESLint..."
          npx eslint frontend/src --ext .js,.jsx,.ts,.tsx --format json --output-file test-results/quality/eslint-report.json || true
          
          # Prettier - 代码格式检查
          echo "Running Prettier check..."
          npx prettier --check frontend/src --write-file test-results/quality/prettier-report.txt || true
          
          # TypeScript编译检查
          echo "Running TypeScript check..."
          npx tsc --noEmit --project frontend/tsconfig.json > test-results/quality/typescript-report.txt || true
          
      - name: Generate quality metrics
        run: |
          echo "Generating quality metrics..."
          python -c "
          import json
          import os
          from pathlib import Path
          
          quality_dir = Path('test-results/quality')
          metrics = {
              'timestamp': '$(date -Iseconds)',
              'python_quality': {},
              'frontend_quality': {},
              'overall_score': 0
          }
          
          # 处理Pylint结果
          try:
              with open(quality_dir / 'pylint-report.json') as f:
                  pylint_data = json.load(f)
              metrics['python_quality']['pylint_score'] = 10.0  # 默认满分
              metrics['python_quality']['pylint_issues'] = len(pylint_data)
          except:
              metrics['python_quality']['pylint_score'] = 0
              metrics['python_quality']['pylint_issues'] = 0
          
          # 处理ESLint结果
          try:
              with open(quality_dir / 'eslint-report.json') as f:
                  eslint_data = json.load(f)
              total_errors = sum(len(file.get('messages', [])) for file in eslint_data)
              metrics['frontend_quality']['eslint_issues'] = total_errors
          except:
              metrics['frontend_quality']['eslint_issues'] = 0
          
          # 计算总体质量分数
          python_score = max(0, 100 - metrics['python_quality']['pylint_issues'] * 2)
          frontend_score = max(0, 100 - metrics['frontend_quality']['eslint_issues'] * 2)
          metrics['overall_score'] = (python_score + frontend_score) / 2
          
          # 保存质量指标
          with open('test-results/quality/quality-metrics.json', 'w') as f:
              json.dump(metrics, f, indent=2)
          
          print(f'Quality Score: {metrics[\"overall_score\"]:.1f}/100')
          print(f'Python Issues: {metrics[\"python_quality\"][\"pylint_issues\"]}')
          print(f'Frontend Issues: {metrics[\"frontend_quality\"][\"eslint_issues\"]}')
          "
          
      - name: Upload quality results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: quality-test-results
          path: test-results/quality/

  # 测试结果聚合和监控集成 - 增强版
  aggregate-and-monitor:
    needs: [unit-tests, integration-tests, e2e-tests, performance-tests, security-tests, quality-tests]
    if: always() && env.ENABLE_MONITORING == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download all test results
        uses: actions/download-artifact@v3
        with:
          path: all-test-results/
          
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install monitoring dependencies
        run: |
          pip install requests junitparser beautifulsoup4 lxml prometheus-client
          pip install slack-sdk smtplib email-validator
          
      - name: Aggregate test results
        run: |
          echo "Aggregating test results..."
          python -c "
          import json
          import os
          from pathlib import Path
          from datetime import datetime
          
          results_dir = Path('all-test-results')
          aggregated = {
              'timestamp': datetime.now().isoformat(),
              'environment': '${{ env.TEST_ENVIRONMENT }}',
              'commit_sha': '${{ github.sha }}',
              'branch': '${{ github.ref_name }}',
              'workflow_run_id': '${{ github.run_id }}',
              'test_suites': {},
              'summary': {
                  'total_tests': 0,
                  'passed_tests': 0,
                  'failed_tests': 0,
                  'skipped_tests': 0,
                  'success_rate': 0.0,
                  'total_duration': 0.0
              },
              'performance_metrics': {},
              'security_summary': {},
              'quality_metrics': {}
          }
          
          # 聚合各类测试结果
          for result_dir in results_dir.iterdir():
              if result_dir.is_dir():
                  suite_name = result_dir.name.replace('-test-results', '')
                  aggregated['test_suites'][suite_name] = {
                      'status': 'completed',
                      'artifacts': list(result_dir.glob('**/*'))
                  }
          
          # 保存聚合结果
          with open('aggregated-results.json', 'w') as f:
              json.dump(aggregated, f, indent=2, default=str)
          
          print('Test results aggregated successfully')
          "
          
      - name: Run monitoring integration
        run: |
          echo "Running monitoring integration..."
          export PROMETHEUS_GATEWAY="${{ env.PROMETHEUS_GATEWAY }}"
          export GRAFANA_URL="${{ env.GRAFANA_URL }}"
          export GRAFANA_API_KEY="${{ env.GRAFANA_API_KEY }}"
          export SLACK_WEBHOOK="${{ env.SLACK_WEBHOOK }}"
          
          # 运行监控集成脚本
          python tests/monitoring/test_monitoring_integration.py \
            --results-file aggregated-results.json \
            --test-results-dir all-test-results/ \
            --environment "${{ env.TEST_ENVIRONMENT }}" \
            --commit-sha "${{ github.sha }}" \
            --branch "${{ github.ref_name }}" \
            --workflow-id "${{ github.run_id }}" \
            --enable-prometheus \
            --enable-grafana \
            --enable-alerts \
            --verbose
            
      - name: Generate monitoring dashboard
        if: env.GRAFANA_URL != '' && env.GRAFANA_API_KEY != ''
        run: |
          echo "Generating Grafana dashboard..."
          python -c "
          import requests
          import json
          import os
          
          grafana_url = os.getenv('GRAFANA_URL')
          api_key = os.getenv('GRAFANA_API_KEY')
          
          if grafana_url and api_key:
              headers = {
                  'Authorization': f'Bearer {api_key}',
                  'Content-Type': 'application/json'
              }
              
              # 创建或更新Connect测试监控Dashboard
              dashboard_config = {
                  'dashboard': {
                      'title': 'Connect电信平台测试监控',
                      'tags': ['connect', 'testing', 'ci-cd'],
                      'timezone': 'browser',
                      'panels': [
                          {
                              'title': '测试成功率',
                              'type': 'stat',
                              'targets': [{
                                  'expr': 'connect_test_success_rate',
                                  'legendFormat': '成功率'
                              }]
                          },
                          {
                              'title': '性能指标',
                              'type': 'graph',
                              'targets': [{
                                  'expr': 'connect_performance_response_time',
                                  'legendFormat': '响应时间'
                              }]
                          },
                          {
                              'title': '安全扫描结果',
                              'type': 'table',
                              'targets': [{
                                  'expr': 'connect_security_issues',
                                  'legendFormat': '安全问题'
                              }]
                          }
                      ]
                  },
                  'overwrite': True
              }
              
              try:
                  response = requests.post(
                      f'{grafana_url}/api/dashboards/db',
                      headers=headers,
                      json=dashboard_config
                  )
                  if response.status_code == 200:
                      print('Dashboard created/updated successfully')
                  else:
                      print(f'Dashboard creation failed: {response.status_code}')
              except Exception as e:
                  print(f'Error creating dashboard: {e}')
          "
          
      - name: Send notifications
        if: always()
        run: |
          echo "Sending notifications..."
          python -c "
          import json
          import requests
          import os
          from datetime import datetime
          
          # 读取聚合结果
          with open('aggregated-results.json') as f:
              results = json.load(f)
          
          # 准备通知消息
          success_rate = results['summary'].get('success_rate', 0)
          total_tests = results['summary'].get('total_tests', 0)
          failed_tests = results['summary'].get('failed_tests', 0)
          
          status_emoji = '✅' if success_rate >= 95 else '⚠️' if success_rate >= 80 else '❌'
          
          message = f'''
          {status_emoji} Connect电信平台测试报告
          
          📊 测试概览:
          • 总测试数: {total_tests}
          • 成功率: {success_rate:.1f}%
          • 失败测试: {failed_tests}
          • 环境: ${{ env.TEST_ENVIRONMENT }}
          • 分支: ${{ github.ref_name }}
          • 提交: ${{ github.sha }}[:8]
          
          🔗 详细报告: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
          '''
          
          # 发送Slack通知
          slack_webhook = os.getenv('SLACK_WEBHOOK')
          if slack_webhook:
              try:
                  response = requests.post(slack_webhook, json={'text': message})
                  if response.status_code == 200:
                      print('Slack notification sent successfully')
                  else:
                      print(f'Slack notification failed: {response.status_code}')
              except Exception as e:
                  print(f'Error sending Slack notification: {e}')
          
          # 检查告警阈值
          if success_rate < float('${{ env.ALERT_TEST_FAILURE_RATE }}' or '95.0'):
              print(f'ALERT: Test success rate {success_rate:.1f}% below threshold')
              exit(1)
          "
          
      - name: Upload monitoring results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: monitoring-results
          path: |
            aggregated-results.json
            monitoring-report.json
            dashboard-config.json
            
      - name: Cleanup
        if: always()
        run: |
          echo "Cleaning up temporary files..."
          rm -rf all-test-results/
          echo "Monitoring integration completed"
          TEAMS_WEBHOOK_URL: ${{ secrets.TEAMS_WEBHOOK_URL }}
          DISCORD_WEBHOOK_URL: ${{ secrets.DISCORD_WEBHOOK_URL }}
          PROMETHEUS_PUSHGATEWAY_URL: ${{ secrets.PROMETHEUS_PUSHGATEWAY_URL }}
          GRAFANA_API_URL: ${{ secrets.GRAFANA_API_URL }}
          GRAFANA_API_TOKEN: ${{ secrets.GRAFANA_API_TOKEN }}
          DATADOG_API_URL: ${{ secrets.DATADOG_API_URL }}
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
            
      - name: Update test badges
        run: |
          python scripts/update_test_badges.py \
            --results-file aggregated-results.json \
            --output-dir badges/
            
      - name: Publish test report
        uses: peaceiris/actions-gh-pages@v3
        if: github.ref == 'refs/heads/main'
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./test-reports
          destination_dir: test-reports
          
      - name: Upload aggregated results
        uses: actions/upload-artifact@v3
        with:
          name: aggregated-test-results
          path: |
            aggregated-results.json
            dashboard-data.json
            badges/
            dashboard/
      
      - name: Deploy Dashboard to GitHub Pages
        if: github.ref == 'refs/heads/main' && always()
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dashboard
          destination_dir: test-dashboard
          keep_files: false
          commit_message: 'Deploy test dashboard: ${{ github.sha }}'
      
      - name: Upload Dashboard to S3
        if: always() && env.AWS_S3_BUCKET != ''
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_S3_BUCKET: ${{ secrets.AWS_S3_BUCKET }}
          AWS_REGION: ${{ secrets.AWS_REGION || 'us-east-1' }}
        run: |
          if [ -d "dashboard" ]; then
            aws s3 sync dashboard/ s3://$AWS_S3_BUCKET/test-dashboard/ \
              --region $AWS_REGION \
              --delete \
              --cache-control "max-age=300"
            echo "Dashboard uploaded to: https://$AWS_S3_BUCKET.s3.$AWS_REGION.amazonaws.com/test-dashboard/dashboard.html"
          fi
            
      - name: Comment PR with test results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const results = JSON.parse(fs.readFileSync('aggregated-results.json', 'utf8'));
            
            const comment = `## 🧪 测试结果报告
            
            | 测试类型 | 通过 | 失败 | 跳过 | 覆盖率 |
            |---------|------|------|------|--------|
            | 单元测试 | ${results.unit.passed} | ${results.unit.failed} | ${results.unit.skipped} | ${results.unit.coverage}% |
            | 集成测试 | ${results.integration.passed} | ${results.integration.failed} | ${results.integration.skipped} | - |
            | E2E测试 | ${results.e2e.passed} | ${results.e2e.failed} | ${results.e2e.skipped} | - |
            | 性能测试 | ${results.performance.passed} | ${results.performance.failed} | ${results.performance.skipped} | - |
            | 安全测试 | ${results.security.passed} | ${results.security.failed} | ${results.security.skipped} | - |
            
            **总体状态**: ${results.overall.status === 'success' ? '✅ 通过' : '❌ 失败'}
            **总测试数**: ${results.overall.total}
            **通过率**: ${results.overall.pass_rate}%
            
            ${results.overall.status === 'failure' ? '⚠️ 请检查失败的测试用例并修复相关问题。' : ''}
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  # 测试失败通知
  notify-failure:
    needs: [unit-tests, integration-tests, e2e-tests, performance-tests, security-tests]
    if: failure()
    runs-on: ubuntu-latest
    
    steps:
      - name: Send failure notification
        uses: 8398a7/action-slack@v3
        if: env.SLACK_WEBHOOK_URL != ''
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        with:
          status: failure
          text: |
            🚨 Connect电信数据分析平台测试失败
            
            **分支**: ${{ github.ref }}
            **提交**: ${{ github.sha }}
            **作者**: ${{ github.actor }}
            **工作流**: ${{ github.workflow }}
            
            请检查测试日志并修复相关问题。
            
            [查看详情](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})

  # 性能回归检测
  performance-regression:
    needs: performance-tests
    if: always() && needs.performance-tests.result == 'success'
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download performance results
        uses: actions/download-artifact@v3
        with:
          name: performance-test-results
          path: performance-results/
          
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Install analysis dependencies
        run: |
          pip install pandas numpy matplotlib seaborn
          
      - name: Analyze performance regression
        run: |
          python scripts/analyze_performance_regression.py \
            --current-results performance-results/benchmark.json \
            --baseline-branch main \
            --threshold 10 \
            --output-file performance-analysis.json
            
      - name: Comment performance analysis
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            if (fs.existsSync('performance-analysis.json')) {
              const analysis = JSON.parse(fs.readFileSync('performance-analysis.json', 'utf8'));
              
              if (analysis.regressions.length > 0) {
                const comment = `## ⚠️ 性能回归检测
                
                发现以下性能回归:
                
                ${analysis.regressions.map(r => 
                  `- **${r.test}**: ${r.change}% 性能下降 (${r.baseline}ms → ${r.current}ms)`
                ).join('\n')}
                
                请优化相关代码以避免性能回归。
                `;
                
                github.rest.issues.createComment({
                  issue_number: context.issue.number,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  body: comment
                });
              }
            }

  # 安全漏洞报告
  security-report:
    needs: security-tests
    if: always() && needs.security-tests.result == 'success'
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Download security results
        uses: actions/download-artifact@v3
        with:
          name: security-test-results
          path: security-results/
          
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          
      - name: Generate security report
        run: |
          python scripts/generate_security_report.py \
            --bandit-report security-results/bandit-report.json \
            --safety-report security-results/safety-report.json \
            --test-results security-results/security-tests.xml \
            --output-file security-report.json
            
      - name: Upload security report to dashboard
        if: github.event.inputs.send_to_dashboard != 'false'
        env:
          SECURITY_DASHBOARD_URL: ${{ secrets.SECURITY_DASHBOARD_URL }}
          SECURITY_API_KEY: ${{ secrets.SECURITY_API_KEY }}
        run: |
          python scripts/upload_security_report.py \
            --report-file security-report.json \
            --dashboard-url "$SECURITY_DASHBOARD_URL" \
            --api-key "$SECURITY_API_KEY"