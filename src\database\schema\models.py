__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Data model definitions using SQLAlchemy Table objects.

This module provides table structure definitions and utilities for creating
tables from pandas DataFrames with automatic column enhancement.
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Optional

import pandas as pd
from sqlalchemy import (
    BigInteger,
    Column,
    DateTime,
    MetaData,
    Table,
    Text,
    create_engine,
    inspect,
)
from sqlalchemy.schema import CreateTable
from sqlalchemy.sql import func

from ..exceptions import SchemaError, ValidationError
from ..utils.validators import InputValidator
from .table_schema import TableSchema, ColumnSchema


class TableDefinition:
    """Table definition utility using SQLAlchemy Table objects.

    This class provides methods to create table structures with automatic
    column enhancement including primary key and timestamp columns.
    """

    def __init__(self, validator: Optional[InputValidator] = None):
        """Initialize table definition utility.

        Args:
            validator: Input validator instance for name validation
        """
        self.validator = validator or InputValidator()
        self.metadata = MetaData()

    def create_table_definition(
        self,
        table_name: str,
        columns: Dict[str, str],
        schema_name: Optional[str] = None,
    ) -> Table:
        """Create SQLAlchemy Table object with enhanced columns.

        Args:
            table_name: Name of the table
            columns: Dictionary of column names and their types
            schema_name: Schema name for the table

        Returns:
            SQLAlchemy Table object

        Raises:
            ValidationError: If table or column names are invalid
        """
        # Validate table name
        if not self.validator.validate_identifier(table_name):
            raise ValidationError(f"Invalid table name: {table_name}")

        # Clean and validate column names
        cleaned_columns = self._clean_column_names(columns)

        # Create table columns with standard structure
        table_columns = self._create_standard_columns(cleaned_columns)

        # Create SQLAlchemy Table object
        table = Table(table_name, self.metadata, *table_columns, schema=schema_name)

        return table

    def _clean_column_names(self, columns: Dict[str, str]) -> Dict[str, str]:
        """Clean and standardize column names.

        Args:
            columns: Original column names and types

        Returns:
            Dictionary with cleaned column names
        """
        cleaned = {}
        used_names = set()

        for original_name, col_type in columns.items():
            # Clean column name
            clean_name = self._normalize_column_name(original_name)

            # Handle duplicates
            if clean_name in used_names:
                counter = 1
                while f"{clean_name}_{counter}" in used_names:
                    counter += 1
                clean_name = f"{clean_name}_{counter}"

            used_names.add(clean_name)
            cleaned[clean_name] = col_type

        return cleaned

    def _normalize_column_name(self, name: str) -> str:
        """Normalize column name according to PostgreSQL standards.

        Args:
            name: Original column name

        Returns:
            Normalized column name
        """
        # Convert to lowercase
        normalized = name.lower().strip()

        # Replace spaces and special characters with underscores
        normalized = re.sub(r"[^a-z0-9_]", "_", normalized)

        # Remove multiple consecutive underscores
        normalized = re.sub(r"_+", "_", normalized)

        # Remove leading/trailing underscores
        normalized = normalized.strip("_")

        # Ensure it starts with a letter
        if normalized and not normalized[0].isalpha():
            normalized = f"col_{normalized}"

        # Truncate if too long (PostgreSQL limit is 63 characters)
        if len(normalized) > 63:
            normalized = normalized[:60] + "_tr"

        # Handle empty names
        if not normalized:
            normalized = "unnamed_col"

        return normalized

    def _create_standard_columns(self, columns: Dict[str, str]) -> List[Column]:
        """Create column list with standard columns.

        Args:
            columns: Dictionary of column names and types

        Returns:
            List of SQLAlchemy Column objects
        """
        table_columns = []

        # Add primary key column (id BIGSERIAL PRIMARY KEY)
        if "id" not in columns:
            table_columns.append(
                Column("id", BigInteger, primary_key=True, autoincrement=True)
            )

        # Add user-defined columns (default to TEXT type as per PRD)
        for col_name, col_type in columns.items():
            if col_name == "id":
                # If user specified id column, use BIGINT
                table_columns.append(
                    Column("id", BigInteger, primary_key=True, autoincrement=True)
                )
            else:
                # Default all other columns to TEXT as per PRD requirements
                table_columns.append(Column(col_name, Text))

        # Add created_at timestamp column
        if "created_at" not in columns:
            table_columns.append(
                Column(
                    "created_at",
                    DateTime(timezone=False),
                    default=func.current_timestamp(),
                    nullable=False,
                )
            )

        return table_columns

    def generate_create_sql(self, table: Table, engine) -> str:
        """Generate CREATE TABLE SQL statement.

        Args:
            table: SQLAlchemy Table object
            engine: Database engine for SQL compilation

        Returns:
            CREATE TABLE SQL statement
        """
        create_table = CreateTable(table)
        return str(create_table.compile(engine, compile_kwargs={"literal_binds": True}))


def create_table_from_dataframe(
    df: pd.DataFrame,
    table_name: str,
    schema_name: Optional[str] = None,
    validator: Optional[InputValidator] = None,
) -> Table:
    """Create SQLAlchemy Table object from pandas DataFrame.

    Args:
        df: Pandas DataFrame to analyze
        table_name: Name for the new table
        schema_name: Schema name for the table
        validator: Input validator instance

    Returns:
        SQLAlchemy Table object

    Raises:
        ValidationError: If table name or column names are invalid
        SchemaError: If DataFrame is empty or invalid
    """
    if df.empty:
        raise SchemaError("Cannot create table from empty DataFrame")

    # Initialize table definition utility
    table_def = TableDefinition(validator)

    # Extract column information from DataFrame
    # As per PRD, default all columns to TEXT type except id
    columns = {}
    for col in df.columns:
        if str(col).lower() == "id":
            continue  # Will be handled as primary key
        columns[str(col)] = "TEXT"

    # Create table definition
    table = table_def.create_table_definition(
        table_name=table_name, columns=columns, schema_name=schema_name
    )

    return table


def infer_column_types(df: pd.DataFrame) -> Dict[str, str]:
    """Infer PostgreSQL column types from pandas DataFrame.

    Note: As per PRD requirements, this function defaults all columns
    to TEXT type for simplicity and stability.

    Args:
        df: Pandas DataFrame to analyze

    Returns:
        Dictionary mapping column names to PostgreSQL types
    """
    column_types = {}

    for col in df.columns:
        # As per PRD: default all columns to TEXT except id
        if str(col).lower() == "id":
            column_types[str(col)] = "BIGINT"
        else:
            column_types[str(col)] = "TEXT"

    return column_types


def validate_table_structure(table: Table) -> bool:
    """Validate table structure meets requirements.

    Args:
        table: SQLAlchemy Table object to validate

    Returns:
        True if table structure is valid

    Raises:
        SchemaError: If table structure is invalid
    """
    # Check for required columns
    column_names = [col.name for col in table.columns]

    if "id" not in column_names:
        raise SchemaError("Table must have 'id' primary key column")

    if "created_at" not in column_names:
        raise SchemaError("Table must have 'created_at' timestamp column")

    # Validate id column is primary key
    id_column = table.columns["id"]
    if not id_column.primary_key:
        raise SchemaError("'id' column must be primary key")

    # Validate created_at column type
    created_at_column = table.columns["created_at"]
    if not isinstance(created_at_column.type, DateTime):
        raise SchemaError("'created_at' column must be DateTime type")

    return True
