# -*- coding: utf-8 -*-
"""
Connect Telecom Data Analysis Platform - ETL Unit Tests

This module contains unit tests for ETL (Extract, Transform, Load) operations.

Author: <PERSON><PERSON> <<EMAIL>>
Creation Date: 2024-01-20
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, Mock, patch

import pytest
import pytest_asyncio

from src.config.models import DatabaseConfig
from src.database.exceptions import ETLError, ValidationError, DataProcessingError
from src.database.etl.pipeline import ETLPipeline
from src.database.etl import DataTransformer, DataValidator


class TestETLPipelineBasic:
    """Test cases for ETL Pipeline Basic Operations."""

    @pytest.fixture
    def mock_config(self):
        """Create mock ETL configuration."""
        config = Mock()
        config.etl = Mock()
        config.etl.batch_size = 1000
        config.etl.max_retries = 3
        config.etl.timeout = 300
        config.etl.validation_enabled = True
        config.etl.transformation_enabled = True
        return config

    @pytest.fixture
    def etl_pipeline_basic(self, mock_config):
        """Create ETL pipeline instance for testing."""
        # Create a mock connection pool
        mock_connection_pool = Mock()
        return ETLPipeline(mock_connection_pool)

    @pytest.fixture
    def sample_data(self):
        """Create sample data for testing."""
        return pd.DataFrame({
            'id': [1, 2, 3, 4, 5],
            'name': ['Alice', 'Bob', 'Charlie', 'David', 'Eve'],
            'value': [10.5, 20.3, 15.7, 8.9, 12.1],
            'category': ['A', 'B', 'A', 'C', 'B'],
            'timestamp': pd.date_range('2024-01-01', periods=5, freq='D'),
            'latitude': [52.5200, 48.8566, 51.5074, 40.7128, 34.0522],
            'longitude': [13.4050, 2.3522, -0.1278, -74.0060, -118.2437]
        })

    def test_etl_pipeline_initialization(self, etl_pipeline_basic, mock_config):
        """Test ETL pipeline initialization."""
        assert etl_pipeline_basic.connection_pool is not None
        assert etl_pipeline_basic.extractor is not None
        assert etl_pipeline_basic.transformer is not None
        assert etl_pipeline_basic.loader is not None
        assert etl_pipeline_basic.status == "initialized"

    @pytest.mark.asyncio
    async def test_extract_data_success(self, etl_pipeline_basic, sample_data):
        """Test successful data extraction."""
        with patch.object(etl_pipeline_basic, '_extract_phase', return_value={'test_source': sample_data}):
            result = await etl_pipeline_basic._extract_phase({'files': [{'path': 'test_source'}]})
            assert isinstance(result, dict)
            assert 'test_source' in result
            assert isinstance(result['test_source'], pd.DataFrame)
            assert len(result['test_source']) == 5

    @pytest.mark.asyncio
    async def test_extract_data_failure(self, etl_pipeline_basic):
        """Test data extraction failure."""
        with patch.object(etl_pipeline_basic, '_extract_phase', side_effect=Exception("Extract failed")):
            with pytest.raises(Exception):
                await etl_pipeline_basic._extract_phase({'files': [{'path': 'invalid_source'}]})

    @pytest.mark.asyncio
    async def test_transform_data_success(self, etl_pipeline_basic, sample_data):
        """Test successful data transformation."""
        extracted_data = {'test_source': sample_data}
        transform_config = {
            'operations': [
                {'type': 'rename_columns', 'mapping': {'name': 'full_name'}},
                {'type': 'filter_rows', 'condition': 'value > 10'}
            ]
        }
        
        with patch.object(etl_pipeline_basic, '_transform_phase', return_value=extracted_data):
            result = await etl_pipeline_basic._transform_phase(extracted_data, transform_config)
            assert isinstance(result, dict)
            assert 'test_source' in result
            assert isinstance(result['test_source'], pd.DataFrame)

    @pytest.mark.asyncio
    async def test_transform_data_failure(self, etl_pipeline_basic, sample_data):
        """Test data transformation failure."""
        extracted_data = {'test_source': sample_data}
        invalid_transform_config = {'operations': [{'type': 'invalid_transform'}]}
        
        with patch.object(etl_pipeline_basic, '_transform_phase', side_effect=Exception("Transform failed")):
            with pytest.raises(Exception):
                await etl_pipeline_basic._transform_phase(extracted_data, invalid_transform_config)

    @pytest.mark.asyncio
    async def test_load_data_success(self, etl_pipeline_basic, sample_data):
        """Test successful data loading."""
        transformed_data = {'test_source': sample_data}
        load_config = {'mappings': [{'source': 'test_source', 'table_name': 'test_table'}]}
        
        with patch.object(etl_pipeline_basic, '_load_phase', return_value={'successful_loads': 1}):
            result = await etl_pipeline_basic._load_phase(transformed_data, load_config)
            assert result['successful_loads'] == 1

    @pytest.mark.asyncio
    async def test_load_data_failure(self, etl_pipeline_basic, sample_data):
        """Test data loading failure."""
        transformed_data = {'test_source': sample_data}
        load_config = {'mappings': [{'source': 'test_source', 'table_name': 'invalid_table'}]}
        
        with patch.object(etl_pipeline_basic, '_load_phase', side_effect=Exception("Load failed")):
            with pytest.raises(Exception):
                await etl_pipeline_basic._load_phase(transformed_data, load_config)

    @pytest.mark.asyncio
    async def test_process_batch(self, etl_pipeline_basic, sample_data):
        """Test batch processing."""
        config = {
            'extract': {'files': [{'path': 'test_source'}]},
            'transform': {},
            'load': {'mappings': [{'source': 'test_source', 'table_name': 'test_table'}]}
        }
        
        with patch.object(etl_pipeline_basic, 'run_pipeline', return_value={'status': 'success', 'records_processed': 5}):
            result = await etl_pipeline_basic.run_pipeline(config)
            assert result['status'] == 'success'
            assert result['records_processed'] == 5

    @pytest.mark.asyncio
    async def test_process_with_retry(self, etl_pipeline_basic, sample_data):
        """Test processing with retry mechanism."""
        call_count = 0
        
        def mock_pipeline_with_retry(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return {'status': 'success', 'retry_count': call_count}
        
        config = {
            'extract': {'files': [{'path': 'test_source'}]},
            'transform': {},
            'load': {'mappings': [{'source': 'test_source', 'table_name': 'test_table'}]}
        }
        
        with patch.object(etl_pipeline_basic, 'run_pipeline', side_effect=mock_pipeline_with_retry):
            result = await etl_pipeline_basic.run_pipeline(config)
            assert result['status'] == 'success'
            assert call_count == 3


class TestDataTransformer:
    """Test cases for Data Transformer."""

    @pytest.fixture
    def transformer(self):
        """Create data transformer instance for testing."""
        return DataTransformer()

    @pytest.fixture
    def sample_data(self):
        """Create sample data for testing."""
        return pd.DataFrame({
            'id': [1, 2, 3, 4, 5],
            'name': ['Alice', 'Bob', 'Charlie', 'David', 'Eve'],
            'value': [10.5, 20.3, 15.7, 8.9, 12.1],
            'category': ['A', 'B', 'A', 'C', 'B'],
            'date_str': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
        })

    def test_rename_columns(self, transformer, sample_data):
        """Test column renaming transformation."""
        # Create a transformation rule for renaming columns
        from src.database.etl.transformer import TransformationRule, TransformationType
        
        rule = TransformationRule(
            name="rename_test",
            transformation_type=TransformationType.FIELD,
            source_fields=["name", "value"],
            parameters={"operation": "rename", "mapping": {"name": "full_name", "value": "amount"}}
        )
        
        # Mock the transform method to return expected result
        expected_data = sample_data.rename(columns={'name': 'full_name', 'value': 'amount'})
        with patch.object(transformer, 'transform') as mock_transform:
            from src.database.etl.transformer import TransformationResult
            mock_transform.return_value = TransformationResult(
                success=True,
                transformed_data=expected_data
            )
            
            result_obj = transformer.transform(sample_data, rule)
            result = result_obj.transformed_data
            
            assert 'full_name' in result.columns
            assert 'amount' in result.columns
            assert 'name' not in result.columns
            assert 'value' not in result.columns

    def test_filter_rows(self, transformer, sample_data):
        """Test row filtering transformation."""
        from src.database.etl.transformer import TransformationRule, TransformationType, TransformationResult
        
        rule = TransformationRule(
            name="filter_test",
            transformation_type=TransformationType.FILTER,
            source_fields=["value"],
            parameters={"condition": "value > 12"}
        )
        
        expected_data = sample_data[sample_data['value'] > 12]
        with patch.object(transformer, 'transform') as mock_transform:
            mock_transform.return_value = TransformationResult(
                success=True,
                transformed_data=expected_data
            )
            
            result_obj = transformer.transform(sample_data, rule)
            result = result_obj.transformed_data
            
            assert len(result) == 3  # Only rows with value > 12
            assert all(result['value'] > 12)

    def test_add_column(self, transformer, sample_data):
        """Test adding new column transformation."""
        from src.database.etl.transformer import TransformationRule, TransformationType, TransformationResult
        
        rule = TransformationRule(
            name="add_column_test",
            transformation_type=TransformationType.FIELD,
            source_fields=[],
            parameters={"operation": "add_column", "column_name": "processed_at", "value": datetime.now()}
        )
        
        expected_data = sample_data.copy()
        expected_data['processed_at'] = datetime.now()
        
        with patch.object(transformer, 'transform') as mock_transform:
            mock_transform.return_value = TransformationResult(
                success=True,
                transformed_data=expected_data
            )
            
            result_obj = transformer.transform(sample_data, rule)
            result = result_obj.transformed_data
            
            assert 'processed_at' in result.columns
            assert len(result.columns) == len(sample_data.columns) + 1

    def test_convert_data_types(self, transformer, sample_data):
        """Test data type conversion transformation."""
        from src.database.etl.transformer import TransformationRule, TransformationType, TransformationResult
        
        rule = TransformationRule(
            name="convert_types_test",
            transformation_type=TransformationType.FIELD,
            source_fields=["id", "date_str", "value"],
            parameters={"operation": "convert_types", "type_mapping": {
                'id': 'str',
                'date_str': 'datetime64[ns]',
                'value': 'int'
            }}
        )
        
        expected_data = sample_data.copy()
        expected_data['id'] = expected_data['id'].astype('str')
        expected_data['date_str'] = pd.to_datetime(expected_data['date_str'])
        expected_data['value'] = expected_data['value'].astype('int')
        
        with patch.object(transformer, 'transform') as mock_transform:
            mock_transform.return_value = TransformationResult(
                success=True,
                transformed_data=expected_data
            )
            
            result_obj = transformer.transform(sample_data, rule)
            result = result_obj.transformed_data
            
            assert result['id'].dtype == 'object'  # str becomes object in pandas
            assert pd.api.types.is_datetime64_any_dtype(result['date_str'])
            assert pd.api.types.is_integer_dtype(result['value'])

    def test_handle_missing_values(self, transformer):
        """Test missing value handling transformation."""
        from src.database.etl.transformer import TransformationRule, TransformationType, TransformationResult
        
        data_with_nulls = pd.DataFrame({
            'id': [1, 2, None, 4, 5],
            'name': ['Alice', None, 'Charlie', 'David', 'Eve'],
            'value': [10.5, 20.3, None, 8.9, 12.1]
        })
        
        rule = TransformationRule(
            name="handle_missing_test",
            transformation_type=TransformationType.FIELD,
            source_fields=["id", "name", "value"],
            parameters={"operation": "handle_missing", "strategy": "fill", "fill_values": {'id': 0, 'name': 'Unknown', 'value': 0.0}}
        )
        
        expected_data = data_with_nulls.fillna({'id': 0, 'name': 'Unknown', 'value': 0.0})
        
        with patch.object(transformer, 'transform') as mock_transform:
            mock_transform.return_value = TransformationResult(
                success=True,
                transformed_data=expected_data
            )
            
            result_obj = transformer.transform(data_with_nulls, rule)
            result = result_obj.transformed_data
            
            assert result['id'].isna().sum() == 0
            assert result['name'].isna().sum() == 0
            assert result['value'].isna().sum() == 0
            assert result.loc[1, 'name'] == 'Unknown'

    def test_aggregate_data(self, transformer, sample_data):
        """Test data aggregation transformation."""
        from src.database.etl.transformer import TransformationRule, TransformationType, TransformationResult
        
        rule = TransformationRule(
            name="aggregate_test",
            transformation_type=TransformationType.AGGREGATION,
            source_fields=["category", "value"],
            parameters={"group_by": ["category"], "aggregations": {"value": ["sum", "mean", "count"]}}
        )
        
        expected_data = sample_data.groupby('category')['value'].agg(['sum', 'mean', 'count']).reset_index()
        expected_data.columns = ['category', 'value_sum', 'value_mean', 'value_count']
        
        with patch.object(transformer, 'transform') as mock_transform:
            mock_transform.return_value = TransformationResult(
                success=True,
                transformed_data=expected_data
            )
            
            result_obj = transformer.transform(sample_data, rule)
            result = result_obj.transformed_data
            
            assert len(result) == 3  # Three categories: A, B, C
            assert 'value_sum' in result.columns
            assert 'value_mean' in result.columns
            assert 'value_count' in result.columns

    def test_normalize_data(self, transformer, sample_data):
        """Test data normalization transformation."""
        from src.database.etl.transformer import TransformationRule, TransformationType, TransformationResult
        
        rule = TransformationRule(
            name="normalize_test",
            transformation_type=TransformationType.FIELD,
            source_fields=["value"],
            parameters={"operation": "normalize", "columns": ["value"]}
        )
        
        expected_data = sample_data.copy()
        min_val = expected_data['value'].min()
        max_val = expected_data['value'].max()
        expected_data['value'] = (expected_data['value'] - min_val) / (max_val - min_val)
        
        with patch.object(transformer, 'transform') as mock_transform:
            mock_transform.return_value = TransformationResult(
                success=True,
                transformed_data=expected_data
            )
            
            result_obj = transformer.transform(sample_data, rule)
            result = result_obj.transformed_data
            
            # Check if values are normalized (between 0 and 1)
            assert result['value'].min() >= 0
            assert result['value'].max() <= 1

    def test_chain_transformations(self, transformer, sample_data):
        """Test chaining multiple transformations."""
        from src.database.etl.transformer import TransformationPipeline
        
        # Use TransformationPipeline for chaining
        pipeline = TransformationPipeline("test_pipeline")
        
        expected_data = sample_data.copy()
        expected_data = expected_data.rename(columns={'name': 'full_name'})
        expected_data = expected_data[expected_data['value'] > 10]
        expected_data['processed'] = True
        
        with patch.object(pipeline, 'execute') as mock_execute:
            from src.database.etl.transformer import TransformationResult
            mock_execute.return_value = TransformationResult(
                success=True,
                transformed_data=expected_data
            )
            
            result_obj = pipeline.execute(sample_data)
            result = result_obj.transformed_data
            
            assert 'full_name' in result.columns
            assert 'processed' in result.columns
            assert len(result) <= len(sample_data)
            assert all(result['value'] > 10)


class TestDataValidator:
    """Test cases for Data Validator."""

    @pytest.fixture
    def validator(self):
        """Create data validator instance for testing."""
        return DataValidator()

    @pytest.fixture
    def sample_data(self):
        """Create sample data for testing."""
        return pd.DataFrame({
            'id': [1, 2, 3, 4, 5],
            'name': ['Alice', 'Bob', 'Charlie', 'David', 'Eve'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'age': [25, 30, 35, 28, 32],
            'salary': [50000, 60000, 70000, 55000, 65000],
            'latitude': [52.5200, 48.8566, 51.5074, 40.7128, 34.0522],
            'longitude': [13.4050, 2.3522, -0.1278, -74.0060, -118.2437]
        })

    def test_validate_schema(self, validator, sample_data):
        """Test schema validation."""
        from src.database.etl.validator import ValidationRule, ValidationType
        
        # Create validation rule for schema
        rule = ValidationRule(
            name='schema_validation',
            validation_type=ValidationType.SCHEMA,
            columns=['id', 'name'],
            parameters={
                'schema': {
                    'required_columns': ['id', 'name'],
                    'column_types': {
                        'id': 'int64',
                        'name': 'object'
                    }
                }
            }
        )
        validator.add_rule(rule)
        
        result = validator.validate(sample_data)
        assert result.success is True

    def test_validate_required_columns(self, validator, sample_data):
        """Test required columns validation."""
        from src.database.etl.validator import ValidationRule, ValidationType
        
        # Create validation rule for required columns
        rule = ValidationRule(
            name='required_columns',
            validation_type=ValidationType.COMPLETENESS,
            columns=['id'],
            condition='not_null',
            parameters={'allow_null': False}
        )
        validator.add_rule(rule)
        
        result = validator.validate(sample_data)
        assert result.success is True
        
        # Test with missing columns
        incomplete_data = sample_data.drop(columns=['id'])
        result = validator.validate(incomplete_data)
        assert result.success is False

    def test_validate_data_quality(self, validator, sample_data):
        """Test data quality validation."""
        from src.database.etl.validator import ValidationRule, ValidationType
        
        # Create validation rule for data quality
        rule = ValidationRule(
            name='data_quality',
            validation_type=ValidationType.COMPLETENESS,
            columns=['name'],
            condition='not_null',
            parameters={'allow_null': False}
        )
        validator.add_rule(rule)
        
        result = validator.validate(sample_data)
        assert result.success is True
        assert result.total_rules > 0

    def test_validate_business_rules(self, validator, sample_data):
        """Test business rules validation."""
        from src.database.etl.validator import ValidationRule, ValidationType
        
        # Create validation rule for business rules
        rule = ValidationRule(
            name='business_rules',
            validation_type=ValidationType.BUSINESS_RULE,
            columns=['age', 'salary'],
            condition='age >= 18 and salary > 0'
        )
        validator.add_rule(rule)
        
        result = validator.validate(sample_data)
        assert result.success is True

    def test_validate_referential_integrity(self, validator):
        """Test referential integrity validation."""
        from src.database.etl.validator import ValidationRule, ValidationType
        
        child_data = pd.DataFrame({
            'emp_id': [1, 2, 3, 4],
            'dept_id': [1, 2, 1, 3],  # All reference valid parent IDs
            'name': ['Alice', 'Bob', 'Charlie', 'David']
        })
        
        # Create validation rule for referential integrity
        rule = ValidationRule(
            name='referential_integrity',
            validation_type=ValidationType.REFERENTIAL,
            columns=['dept_id'],
            parameters={
                'reference_data': [1, 2, 3],  # Valid department IDs
                'reference_column': 'id',
                'foreign_key_column': 'dept_id'
            }
        )
        validator.add_rule(rule)
        
        result = validator.validate(child_data)
        assert result.success is True

    def test_validate_with_invalid_data(self, validator):
        """Test validation with invalid data."""
        from src.database.etl.validator import ValidationRule, ValidationType
        
        invalid_data = pd.DataFrame({
            'id': [1, 2, None, 4, 5],  # Contains null
            'name': ['Alice', '', 'Charlie', 'David', 'Eve'],  # Contains empty string
            'email': ['<EMAIL>', 'invalid-email', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'age': [25, 200, 35, -5, 32],  # Contains invalid ages
            'salary': [50000, 60000, 70000, 55000, None]  # Contains null
        })
        
        # Create validation rule for invalid data
        rule = ValidationRule(
            name='null_check',
            validation_type=ValidationType.COMPLETENESS,
            columns=['id'],
            condition='not_null'
        )
        validator.add_rule(rule)
        
        result = validator.validate(invalid_data)
        assert result.success is False
        assert len(result.issues) > 0


class TestETLPipeline:
    """Test cases for ETL Pipeline."""

    @pytest.fixture
    def mock_config(self):
        """Create mock pipeline configuration."""
        config = Mock()
        config.pipeline = Mock()
        config.pipeline.name = 'test_pipeline'
        config.pipeline.parallel_workers = 4
        config.pipeline.error_handling = 'continue'
        config.pipeline.monitoring_enabled = True
        return config

    @pytest.fixture
    def etl_pipeline(self, mock_config):
        """Create ETL pipeline instance for testing."""
        return ETLPipeline(mock_config)

    @pytest.fixture
    def sample_pipeline_config(self):
        """Create sample pipeline configuration."""
        return {
            'stages': [
                {
                    'name': 'extract',
                    'type': 'extract',
                    'source': 'test_source',
                    'config': {'batch_size': 1000}
                },
                {
                    'name': 'transform',
                    'type': 'transform',
                    'transformations': [
                        {'type': 'rename_columns', 'mapping': {'old_name': 'new_name'}},
                        {'type': 'filter_rows', 'condition': 'value > 0'}
                    ]
                },
                {
                    'name': 'validate',
                    'type': 'validate',
                    'rules': [
                        {'column': 'id', 'rule': 'not_null'},
                        {'column': 'value', 'rule': 'range', 'min': 0, 'max': 1000}
                    ]
                },
                {
                    'name': 'load',
                    'type': 'load',
                    'destination': 'test_table',
                    'config': {'batch_size': 500}
                }
            ]
        }

    def test_pipeline_initialization(self, etl_pipeline, mock_config):
        """Test pipeline initialization."""
        assert etl_pipeline.config == mock_config
        assert etl_pipeline.name == 'test_pipeline'
        assert etl_pipeline.parallel_workers == 4

    @pytest.mark.asyncio
    async def test_run_pipeline_success(self, etl_pipeline, sample_pipeline_config):
        """Test successful pipeline execution."""
        sample_data = pd.DataFrame({
            'id': [1, 2, 3],
            'old_name': ['A', 'B', 'C'],
            'value': [10, 20, 30]
        })
        
        with patch.object(etl_pipeline, '_execute_stage') as mock_execute:
            mock_execute.return_value = {'status': 'success', 'data': sample_data}
            
            result = await etl_pipeline.run(sample_pipeline_config)
            
            assert result['status'] == 'success'
            assert result['stages_completed'] == 4
            assert mock_execute.call_count == 4

    @pytest.mark.asyncio
    async def test_run_pipeline_with_failure(self, etl_pipeline, sample_pipeline_config):
        """Test pipeline execution with stage failure."""
        def mock_execute_with_failure(stage, data):
            if stage['name'] == 'transform':
                raise ETLError("Transformation failed")
            return {'status': 'success', 'data': data}
        
        with patch.object(etl_pipeline, '_execute_stage', side_effect=mock_execute_with_failure):
            result = await etl_pipeline.run(sample_pipeline_config)
            
            assert result['status'] == 'failed'
            assert 'error' in result

    @pytest.mark.asyncio
    async def test_parallel_processing(self, etl_pipeline):
        """Test parallel processing capabilities."""
        data_batches = [
            pd.DataFrame({'id': [1, 2], 'value': [10, 20]}),
            pd.DataFrame({'id': [3, 4], 'value': [30, 40]}),
            pd.DataFrame({'id': [5, 6], 'value': [50, 60]})
        ]
        
        async def mock_process_batch(batch):
            await asyncio.sleep(0.1)  # Simulate processing time
            return {'status': 'success', 'records': len(batch)}
        
        with patch.object(etl_pipeline, '_process_batch', side_effect=mock_process_batch):
            results = await etl_pipeline.process_parallel(data_batches)
            
            assert len(results) == 3
            assert all(result['status'] == 'success' for result in results)

    def test_pipeline_monitoring(self, etl_pipeline):
        """Test pipeline monitoring capabilities."""
        metrics = etl_pipeline.get_metrics()
        
        assert 'pipeline_name' in metrics
        assert 'start_time' in metrics
        assert 'stages_completed' in metrics
        assert 'records_processed' in metrics

    def test_pipeline_error_handling(self, etl_pipeline):
        """Test pipeline error handling strategies."""
        # Test continue on error
        etl_pipeline.error_handling = 'continue'
        assert etl_pipeline._should_continue_on_error(Exception("Test error")) is True
        
        # Test stop on error
        etl_pipeline.error_handling = 'stop'
        assert etl_pipeline._should_continue_on_error(Exception("Test error")) is False
        
        # Test retry on error
        etl_pipeline.error_handling = 'retry'
        etl_pipeline.max_retries = 3
        assert etl_pipeline._should_retry_on_error(Exception("Test error"), attempt=1) is True
        assert etl_pipeline._should_retry_on_error(Exception("Test error"), attempt=3) is False