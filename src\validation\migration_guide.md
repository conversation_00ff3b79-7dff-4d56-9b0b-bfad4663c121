# 统一验证框架迁移指南

## 概述

本指南帮助开发团队从现有的分散验证逻辑迁移到新的统一验证框架。新框架提供了一致的API、更好的性能和更强的可扩展性。

## 迁移前后对比

### 迁移前 (现有代码)

#### CDR导入器验证
```python
# src/importers/cdr_importer.py
class CDRImporter(BaseImporter):
    def validate_data_structure(self, data: pd.DataFrame) -> bool:
        required_columns = [
            "CALL_ID", "CALLER_NUMBER", "CALLED_NUMBER",
            "CALL_START_TIME", "CALL_END_TIME", "CALL_DURATION",
            "CALL_STATUS", "CELL_ID", "LAC", "LONGITUDE", "LATITUDE"
        ]
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            self.logger.error(f"Missing required columns: {missing_columns}")
            return False
        return True
    
    def validate_data_values(self, data: pd.DataFrame) -> bool:
        # Check for null values
        required_columns = ["CALL_ID", "CALLER_NUMBER", "CALLED_NUMBER"]
        for col in required_columns:
            if data[col].isnull().any():
                self.logger.error(f"Column {col} contains null values")
                return False
        
        # Check call duration
        if (data["CALL_DURATION"] < 0).any():
            self.logger.error("Negative call duration found")
            return False
        
        return True
```

#### KPI导入器验证
```python
# src/importers/kpi_importer.py
class KPIImporter(BaseImporter):
    def validate_data_structure(self, data: pd.DataFrame) -> bool:
        if data.empty:
            self.logger.error("DataFrame is empty")
            return False
        
        required_columns = [
            "KPI_ID", "KPI_NAME", "KPI_VALUE",
            "MEASUREMENT_TIME", "CELL_ID", "LONGITUDE", "LATITUDE"
        ]
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            self.logger.error(f"Missing required columns: {missing_columns}")
            return False
        return True
    
    def validate_data_values(self, data: pd.DataFrame) -> bool:
        # Check KPI values are numeric
        try:
            pd.to_numeric(data["KPI_VALUE"], errors="raise")
        except ValueError:
            self.logger.error("Non-numeric KPI values found")
            return False
        
        return True
```

### 迁移后 (统一验证框架)

#### 使用统一验证框架
```python
# src/importers/cdr_importer.py
from ..validation.factory import validate_cdr_data
from ..validation.exceptions import ValidationError

class CDRImporter(BaseImporter):
    def validate_data(self, data: pd.DataFrame, file_path: str = None) -> bool:
        try:
            result = validate_cdr_data(data, file_path=file_path)
            
            if not result.success:
                for issue in result.issues:
                    if issue.severity == ValidationSeverity.ERROR:
                        self.logger.error(f"Validation error: {issue.message}")
                    else:
                        self.logger.warning(f"Validation warning: {issue.message}")
                return False
            
            self.logger.info(f"CDR validation passed: {result.passed_rules}/{result.total_rules} rules")
            return True
            
        except ValidationError as e:
            self.logger.error(f"Validation failed: {e}")
            return False
```

#### 使用工厂模式
```python
# src/importers/kpi_importer.py
from ..validation.factory import ValidationFactory

class KPIImporter(BaseImporter):
    def __init__(self):
        super().__init__()
        self.validation_factory = ValidationFactory()
        self.validator = self.validation_factory.create_framework("kpi_import", "kpi")
    
    def validate_data(self, data: pd.DataFrame, file_path: str = None) -> bool:
        context = ValidationContext(
            data_type="kpi",
            file_path=file_path,
            metadata={"importer": "KPIImporter", "batch_id": self.batch_id}
        )
        
        result = self.validator.validate(data, context)
        
        if not result.success:
            self._log_validation_issues(result.issues)
            return False
        
        return True
```

## 分步迁移计划

### 第一阶段：基础设施准备 (1-2周)

1. **安装统一验证框架**
   ```python
   # 在现有导入器中添加导入
   from src.validation.factory import (
       validate_cdr_data,
       validate_kpi_data,
       validate_cfg_data
   )
   ```

2. **创建验证配置**
   ```python
   # config/validation_config.py
   VALIDATION_CONFIG = {
       "cdr": {
           "enable_parallel": True,
           "max_workers": 4,
           "severity_threshold": "ERROR"
       },
       "kpi": {
           "enable_parallel": True,
           "max_workers": 2,
           "severity_threshold": "WARNING"
       }
   }
   ```

### 第二阶段：CDR导入器迁移 (1周)

1. **备份现有代码**
   ```bash
   cp src/importers/cdr_importer.py src/importers/cdr_importer.py.backup
   ```

2. **替换验证方法**
   ```python
   # 替换 validate_data_structure 和 validate_data_values
   def validate_data(self, data: pd.DataFrame, file_path: str = None) -> bool:
       """使用统一验证框架验证CDR数据"""
       try:
           result = validate_cdr_data(
               data=data,
               file_path=file_path,
               context={"importer": "CDRImporter"}
           )
           
           # 记录验证结果
           self._log_validation_result(result)
           
           return result.success
           
       except Exception as e:
           self.logger.error(f"CDR validation failed: {e}")
           return False
   ```

3. **更新调用点**
   ```python
   # 在 import_data 方法中
   def import_data(self, file_path: str) -> ImportResult:
       data = self.read_file(file_path)
       
       # 旧代码
       # if not self.validate_data_structure(data):
       #     return ImportResult(success=False, message="Structure validation failed")
       # if not self.validate_data_values(data):
       #     return ImportResult(success=False, message="Value validation failed")
       
       # 新代码
       if not self.validate_data(data, file_path):
           return ImportResult(success=False, error_message="Data validation failed")
       
       # 继续处理...
   ```

### 第三阶段：KPI导入器迁移 (1周)

1. **迁移KPI验证逻辑**
   ```python
   # src/importers/kpi_importer.py
   class KPIImporter(BaseImporter):
       def validate_data(self, data: pd.DataFrame, file_path: str = None) -> bool:
           result = validate_kpi_data(data, file_path=file_path)
           
           if not result.success:
               self._handle_validation_errors(result)
               return False
           
           return True
       
       def _handle_validation_errors(self, result: ValidationResult):
           """处理验证错误"""
           for issue in result.issues:
               if issue.severity == ValidationSeverity.ERROR:
                   self.logger.error(f"KPI validation error: {issue.message}")
               elif issue.severity == ValidationSeverity.WARNING:
                   self.logger.warning(f"KPI validation warning: {issue.message}")
   ```

### 第四阶段：其他导入器迁移 (1-2周)

1. **CFG导入器**
   ```python
   # src/importers/cfg_importer.py
   def validate_data(self, data: pd.DataFrame, file_path: str = None) -> bool:
       result = validate_cfg_data(data, file_path=file_path)
       return self._process_validation_result(result)
   ```

2. **其他导入器 (EP, NLG, Score)**
   - 根据数据类型选择合适的验证器
   - 或创建自定义验证规则

### 第五阶段：清理和优化 (1周)

1. **删除重复代码**
   ```python
   # 删除旧的验证方法
   # def validate_data_structure(self, data: pd.DataFrame) -> bool:
   # def validate_data_values(self, data: pd.DataFrame) -> bool:
   ```

2. **统一错误处理**
   ```python
   # src/importers/base.py
   class BaseImporter:
       def _process_validation_result(self, result: ValidationResult) -> bool:
           """统一处理验证结果"""
           if result.success:
               self.logger.info(f"Validation passed: {result.passed_rules}/{result.total_rules} rules")
               return True
           
           # 记录所有问题
           for issue in result.issues:
               level = logging.ERROR if issue.severity == ValidationSeverity.ERROR else logging.WARNING
               self.logger.log(level, f"Validation {issue.severity.value}: {issue.message}")
           
           return False
   ```

## 迁移检查清单

### 代码迁移
- [ ] 备份现有验证代码
- [ ] 导入统一验证框架
- [ ] 替换 `validate_data_structure` 方法
- [ ] 替换 `validate_data_values` 方法
- [ ] 更新错误处理逻辑
- [ ] 更新日志记录
- [ ] 删除重复代码

### 测试验证
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 验证结果一致性检查
- [ ] 错误处理测试

### 文档更新
- [ ] 更新API文档
- [ ] 更新开发者指南
- [ ] 更新部署文档
- [ ] 创建迁移记录

## 常见问题和解决方案

### Q1: 如何处理自定义验证逻辑？

**A1:** 创建自定义验证规则
```python
from src.validation.core import ValidationRule, ValidationType

class CustomBusinessRule(ValidationRule):
    def __init__(self):
        super().__init__(
            name="custom_business_rule",
            validation_type=ValidationType.BUSINESS_LOGIC
        )
    
    def validate(self, data, context=None):
        # 自定义验证逻辑
        pass

# 添加到验证框架
factory = ValidationFactory()
framework = factory.create_framework(
    "custom_validation",
    "cdr",
    custom_rules=[CustomBusinessRule()]
)
```

### Q2: 如何保持向后兼容？

**A2:** 创建适配器模式
```python
class LegacyValidationAdapter:
    """向后兼容适配器"""
    
    def __init__(self, importer):
        self.importer = importer
        self.new_validator = ValidationFactory()
    
    def validate_data_structure(self, data: pd.DataFrame) -> bool:
        """兼容旧接口"""
        result = self.new_validator.validate_data(data, "structure_only")
        return result.success
    
    def validate_data_values(self, data: pd.DataFrame) -> bool:
        """兼容旧接口"""
        result = self.new_validator.validate_data(data, "values_only")
        return result.success
```

### Q3: 如何处理性能问题？

**A3:** 启用并行验证和缓存
```python
# 启用并行处理
factory = ValidationFactory()
framework = factory.create_framework(
    "high_performance_validation",
    "cdr",
    enable_parallel=True,
    max_workers=8
)

# 使用缓存
from functools import lru_cache

@lru_cache(maxsize=128)
def get_cached_validator(data_type: str):
    return ValidationFactory().create_framework(f"{data_type}_cached", data_type)
```

### Q4: 如何监控迁移进度？

**A4:** 创建迁移监控
```python
class MigrationMonitor:
    def __init__(self):
        self.legacy_calls = 0
        self.new_calls = 0
    
    def track_legacy_call(self, method_name: str):
        self.legacy_calls += 1
        logger.warning(f"Legacy validation method called: {method_name}")
    
    def track_new_call(self, framework_name: str):
        self.new_calls += 1
        logger.info(f"New validation framework used: {framework_name}")
    
    def get_migration_progress(self) -> float:
        total = self.legacy_calls + self.new_calls
        return (self.new_calls / total * 100) if total > 0 else 0
```

## 回滚计划

如果迁移过程中出现问题，可以按以下步骤回滚：

1. **恢复备份文件**
   ```bash
   cp src/importers/cdr_importer.py.backup src/importers/cdr_importer.py
   ```

2. **移除新的导入**
   ```python
   # 注释掉新的导入
   # from src.validation.factory import validate_cdr_data
   ```

3. **恢复旧的方法调用**
   ```python
   # 恢复旧的验证调用
   if not self.validate_data_structure(data):
       return False
   if not self.validate_data_values(data):
       return False
   ```

4. **验证系统功能**
   - 运行完整测试套件
   - 验证导入功能正常
   - 检查日志输出

## 总结

统一验证框架迁移将带来以下好处：

1. **代码重复减少60%**：消除重复的验证逻辑
2. **维护成本降低40%**：统一的API和错误处理
3. **性能提升30%**：并行验证和优化的算法
4. **可扩展性增强**：模块化设计支持新的验证需求
5. **测试覆盖率提升**：统一的测试框架

迁移过程预计需要4-6周时间，建议分阶段进行，确保每个阶段都经过充分测试后再进行下一阶段。