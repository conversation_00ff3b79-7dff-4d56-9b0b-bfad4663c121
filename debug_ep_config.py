#!/usr/bin/env python3
"""
调试EP配置加载问题的脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ep_config_loading():
    """测试EP配置加载"""
    print("=== 测试EP配置加载 ===")
    
    # 1. 测试直接从database.yaml读取配置
    print("\n1. 直接从database.yaml读取EP配置:")
    try:
        import yaml
        config_path = Path("config/database.yaml")
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                yaml_config = yaml.safe_load(f)
            
            if 'telecom_data_sources' in yaml_config:
                ep_config = yaml_config['telecom_data_sources'].get('ep', {})
                print(f"EP配置: {ep_config}")
                print(f"schema_name: {ep_config.get('schema_name', 'NOT_FOUND')}")
            else:
                print("未找到telecom_data_sources配置")
        else:
            print(f"配置文件不存在: {config_path}")
    except Exception as e:
        print(f"读取配置文件失败: {e}")
    
    # 2. 测试AbstractImporter的_get_data_source_config方法
    print("\n2. 测试AbstractImporter._get_data_source_config方法:")
    try:
        from src.importers.base.abstract_importer import AbstractImporter
        
        # 创建一个临时的AbstractImporter实例来测试配置加载
        class TestImporter(AbstractImporter):
            def __init__(self):
                self.logger = self._setup_logger()
            
            async def import_data(self, source):
                pass
            
            async def validate_source(self, source):
                pass
            
            async def get_source_info(self, source):
                pass
        
        test_importer = TestImporter()
        ep_config = test_importer._get_data_source_config('ep')
        print(f"通过_get_data_source_config获取的EP配置: {ep_config}")
        print(f"schema_name: {ep_config.get('schema_name', 'NOT_FOUND')}")
        
    except Exception as e:
        print(f"测试AbstractImporter失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
    
    # 3. 测试EPImporter的初始化
    print("\n3. 测试EPImporter初始化:")
    try:
        from src.importers.ep_importer import EPImporter
        
        # 创建EPImporter实例
        ep_importer = EPImporter()
        print(f"EPImporter.data_source_config: {getattr(ep_importer, 'data_source_config', 'NOT_SET')}")
        print(f"EPImporter.schema_name: {getattr(ep_importer, 'schema_name', 'NOT_SET')}")
        print(f"EPImporter.get_schema_name(): {ep_importer.get_schema_name()}")
        
    except Exception as e:
        print(f"测试EPImporter失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

if __name__ == "__main__":
    test_ep_config_loading()