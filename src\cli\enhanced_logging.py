#!/usr/bin/env python3
"""
Enhanced logging configuration for Connect CLI
"""
import logging
import logging.handlers
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from rich.console import Console
from rich.logging import RichHandler

console = Console()


class StructuredFormatter(logging.Formatter):
    """Custom formatter that outputs structured JSON logs."""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add extra fields if present
        if hasattr(record, 'file_path'):
            log_entry['file_path'] = record.file_path
        if hasattr(record, 'data_type'):
            log_entry['data_type'] = record.data_type
        if hasattr(record, 'error_category'):
            log_entry['error_category'] = record.error_category
        if hasattr(record, 'records_processed'):
            log_entry['records_processed'] = record.records_processed
        if hasattr(record, 'processing_time'):
            log_entry['processing_time'] = record.processing_time
            
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
            
        return json.dumps(log_entry, ensure_ascii=False)


class ImportProgressHandler(logging.Handler):
    """Custom handler that shows import progress in console."""
    
    def __init__(self, console: Console):
        super().__init__()
        self.console = console
        self.error_count = 0
        self.warning_count = 0
        
    def emit(self, record):
        if record.levelno >= logging.ERROR:
            self.error_count += 1
            self.console.print(f"[red]❌ ERROR:[/red] {record.getMessage()}")
        elif record.levelno >= logging.WARNING:
            self.warning_count += 1
            self.console.print(f"[yellow]⚠️ WARNING:[/yellow] {record.getMessage()}")
        elif record.levelno >= logging.INFO and 'import' in record.getMessage().lower():
            self.console.print(f"[blue]ℹ️ INFO:[/blue] {record.getMessage()}")


def setup_enhanced_logging(
    log_level: str = "INFO",
    log_dir: str = "logs",
    enable_console: bool = True,
    enable_structured: bool = True,
    enable_progress: bool = True
) -> Dict[str, logging.Logger]:
    """Setup enhanced logging configuration for CLI."""
    
    # Create log directory
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # Configure loggers
    loggers = {}
    
    # Main CLI logger
    cli_logger = logging.getLogger('cli')
    cli_logger.setLevel(getattr(logging, log_level.upper()))
    cli_logger.handlers.clear()
    
    # Import operations logger
    import_logger = logging.getLogger('import')
    import_logger.setLevel(getattr(logging, log_level.upper()))
    import_logger.handlers.clear()
    
    # Database operations logger
    db_logger = logging.getLogger('database')
    db_logger.setLevel(getattr(logging, log_level.upper()))
    db_logger.handlers.clear()
    
    # File handlers with rotation
    if enable_structured:
        # Structured JSON logs
        json_handler = logging.handlers.RotatingFileHandler(
            log_path / 'cli_structured.log',
            maxBytes=50*1024*1024,  # 50MB
            backupCount=5
        )
        json_handler.setFormatter(StructuredFormatter())
        
        cli_logger.addHandler(json_handler)
        import_logger.addHandler(json_handler)
        db_logger.addHandler(json_handler)
    
    # Human-readable logs
    file_handler = logging.handlers.RotatingFileHandler(
        log_path / 'cli.log',
        maxBytes=50*1024*1024,  # 50MB
        backupCount=5
    )
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    
    cli_logger.addHandler(file_handler)
    import_logger.addHandler(file_handler)
    db_logger.addHandler(file_handler)
    
    # Console handlers
    if enable_console:
        if enable_progress:
            # Custom progress handler
            progress_handler = ImportProgressHandler(console)
            import_logger.addHandler(progress_handler)
        
        # Rich console handler for general logging
        rich_handler = RichHandler(
            console=console,
            show_time=False,
            show_path=False,
            markup=True
        )
        rich_handler.setLevel(logging.WARNING)  # Only show warnings and errors in console
        
        cli_logger.addHandler(rich_handler)
        db_logger.addHandler(rich_handler)
    
    # Error-specific logger
    error_logger = logging.getLogger('errors')
    error_logger.setLevel(logging.ERROR)
    error_logger.handlers.clear()
    
    error_handler = logging.handlers.RotatingFileHandler(
        log_path / 'errors.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=10
    )
    error_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(name)s:%(lineno)d - %(message)s\n'
        'Exception: %(exc_info)s\n'
        '---'
    )
    error_handler.setFormatter(error_formatter)
    error_logger.addHandler(error_handler)
    
    # Performance logger
    perf_logger = logging.getLogger('performance')
    perf_logger.setLevel(logging.INFO)
    perf_logger.handlers.clear()
    
    perf_handler = logging.FileHandler(log_path / 'performance.log')
    perf_formatter = logging.Formatter(
        '%(asctime)s - %(message)s'
    )
    perf_handler.setFormatter(perf_formatter)
    perf_logger.addHandler(perf_handler)
    
    loggers.update({
        'cli': cli_logger,
        'import': import_logger,
        'database': db_logger,
        'errors': error_logger,
        'performance': perf_logger
    })
    
    return loggers


def log_import_metrics(
    logger: logging.Logger,
    file_path: str,
    data_type: str,
    records_processed: int,
    processing_time: float,
    status: str,
    error_message: Optional[str] = None
):
    """Log import metrics in a structured way."""
    
    metrics = {
        'file_path': file_path,
        'data_type': data_type,
        'records_processed': records_processed,
        'processing_time': processing_time,
        'status': status,
        'records_per_second': records_processed / processing_time if processing_time > 0 else 0
    }
    
    if error_message:
        metrics['error_message'] = error_message
    
    logger.info(
        f"Import completed: {file_path} ({status})",
        extra=metrics
    )


def log_performance_summary(
    logger: logging.Logger,
    total_files: int,
    successful_imports: int,
    failed_imports: int,
    total_records: int,
    total_time: float
):
    """Log performance summary."""
    
    success_rate = (successful_imports / total_files * 100) if total_files > 0 else 0
    avg_records_per_second = total_records / total_time if total_time > 0 else 0
    
    summary = {
        'total_files': total_files,
        'successful_imports': successful_imports,
        'failed_imports': failed_imports,
        'success_rate_percent': success_rate,
        'total_records': total_records,
        'total_time_seconds': total_time,
        'avg_records_per_second': avg_records_per_second
    }
    
    logger.info(
        f"Import session completed: {success_rate:.1f}% success rate",
        extra=summary
    )
