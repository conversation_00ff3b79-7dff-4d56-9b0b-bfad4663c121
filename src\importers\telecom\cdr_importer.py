"""CDR (Call Detail Records) importer with enhanced telecommunications capabilities.

This module provides specialized CDR data import functionality with support for
multiple operator formats, advanced validation, and performance optimization.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import logging
from typing import Any, Dict, List, Optional, Union
from pathlib import Path
from datetime import datetime

import pandas as pd
import numpy as np
from pydantic import BaseModel, Field, ConfigDict

from ..base import (
    AbstractImporter,
    ValidationMixin,
    ProcessingMixin,
    PerformanceMixin,
    ImporterConfig,
    ImportResult,
    ImportMetrics,
)


class CDRConfig(BaseModel):
    """Configuration specific to CDR data import."""

    # Operator-specific settings
    operator: str = Field(
        default="generic",
        description="Telecom operator (telefonica, vodafone, telekom, etc.)",
    )
    country_code: str = Field(
        default="ES", description="Country code for phone number validation"
    )
    timezone: str = Field(
        default="Europe/Madrid", description="Timezone for timestamp processing"
    )

    # CDR-specific validation
    min_call_duration: float = Field(
        default=0.0, description="Minimum call duration in seconds"
    )
    max_call_duration: float = Field(
        default=86400.0, description="Maximum call duration in seconds"
    )
    validate_phone_numbers: bool = Field(
        default=True, description="Enable phone number validation"
    )
    validate_call_times: bool = Field(
        default=True, description="Enable call time validation"
    )

    # Data processing
    enable_geolocation: bool = Field(
        default=True, description="Enable geolocation processing"
    )
    enable_call_direction_detection: bool = Field(
        default=True, description="Auto-detect call direction"
    )
    enable_roaming_detection: bool = Field(
        default=True, description="Enable roaming detection"
    )

    # Performance optimization
    batch_size: int = Field(default=50000, description="Batch size for CDR processing")
    enable_time_partitioning: bool = Field(
        default=True, description="Enable time-based partitioning"
    )
    partition_column: str = Field(
        default="call_date", description="Column for partitioning"
    )

    # Quality thresholds
    max_error_rate: float = Field(
        default=0.02, description="Maximum acceptable error rate"
    )
    min_data_quality_score: float = Field(
        default=0.95, description="Minimum data quality score"
    )

    model_config = ConfigDict(
        extra="allow"
    )
class CDRImporter(AbstractImporter, ValidationMixin, ProcessingMixin, PerformanceMixin):
    """Enhanced CDR importer with telecommunications-specific capabilities.

    This importer provides comprehensive CDR data processing with support for:
    - Multiple operator formats (Telefonica, Vodafone, Telekom, etc.)
    - Advanced validation and quality checks
    - Geolocation and spatial processing
    - Performance optimization for large datasets
    - Async processing with memory optimization
    """

    # Standard CDR column mappings for different operators
    OPERATOR_SCHEMAS = {
        "telefonica": {
            "calling_number": "numero_origen",
            "called_number": "numero_destino",
            "call_start_time": "fecha_hora_inicio",
            "call_end_time": "fecha_hora_fin",
            "call_duration": "duracion_segundos",
            "cell_id": "celda_id",
            "latitude": "latitud",
            "longitude": "longitud",
        },
        "vodafone": {
            "calling_number": "a_number",
            "called_number": "b_number",
            "call_start_time": "start_time",
            "call_end_time": "end_time",
            "call_duration": "duration",
            "cell_id": "cell_id",
            "latitude": "lat",
            "longitude": "lon",
        },
        "telekom": {
            "calling_number": "rufende_nummer",
            "called_number": "gerufene_nummer",
            "call_start_time": "anruf_start",
            "call_end_time": "anruf_ende",
            "call_duration": "dauer_sekunden",
            "cell_id": "zellen_id",
            "latitude": "breitengrad",
            "longitude": "laengengrad",
        },
        "generic": {
            "calling_number": "calling_number",
            "called_number": "called_number",
            "call_start_time": "call_start_time",
            "call_end_time": "call_end_time",
            "call_duration": "call_duration",
            "cell_id": "cell_id",
            "latitude": "latitude",
            "longitude": "longitude",
        },
    }

    def __init__(
        self, config: Optional[Union[ImporterConfig, Dict[str, Any]]] = None, **kwargs
    ):
        """Initialize CDR importer.

        Args:
            config: Importer configuration
            **kwargs: Additional configuration parameters
        """
        super().__init__(config=config, **kwargs)

        # CDR-specific configuration
        cdr_config_dict = kwargs.get("cdr_config", {})
        if isinstance(cdr_config_dict, CDRConfig):
            self.cdr_config = cdr_config_dict
        else:
            self.cdr_config = CDRConfig(**cdr_config_dict)

        # Set data type
        self.data_type = "CDR"
        self.name = "CDR Importer"
        self.supported_formats = [".csv", ".xlsx", ".xls", ".json"]

        # Set operator (required for schema mapping)
        self.operator = kwargs.get('operator', 'telefonica')  # Default to telefonica

        # Required columns (will be mapped based on operator)
        self.required_columns = ["calling_number", "called_number", "call_start_time"]

        # Configure processing for CDR data
        self.configure_processing(
            {
                "batch_size": self.cdr_config.batch_size,
                "enable_parallel": True,
                "memory_limit_mb": 4096,  # Increased from 2048MB to match NLG importer
                "use_polars": False,  # Pandas for better telecom data handling
            }
        )

        # Configure validation for CDR data
        self.configure_validation(
            {
                "enable_strict_validation": True,
                "max_error_rate": self.cdr_config.max_error_rate,
                "enable_phone_validation": self.cdr_config.validate_phone_numbers,
                "enable_time_validation": self.cdr_config.validate_call_times,
                "country_code": self.cdr_config.country_code,
            }
        )

        # Configure performance monitoring
        self.configure_performance(
            {
                "enable_monitoring": True,
                "enable_telecom_optimizations": True,
                "cdr_batch_size": self.cdr_config.batch_size,
            }
        )

        self.logger.info(
            f"CDR Importer initialized for operator: {self.cdr_config.operator}"
        )

    def set_database_context(self, **kwargs):
        """Set database context components for unified manager integration."""
        self.pool_manager = kwargs.get("pool")
        self.db_manager = kwargs.get("db_manager")
        self.db_ops = kwargs.get("db_ops")
        if "schema_manager" in kwargs:
            self.schema_manager = kwargs["schema_manager"]
        self.logger.debug("Database context set for CDR importer")

    async def import_data(self, source: Union[str, Path], **kwargs) -> ImportResult:
        """Import CDR data with comprehensive processing, handling multi-operator Excel sheets.

        Args:
            source: Data source (file path or connection string)
            **kwargs: Additional import parameters

        Returns:
            Import result with metrics and status
        """
        start_time = datetime.now()
        total_metrics = ImportMetrics()
        all_warnings = []

        try:
            # Start performance monitoring
            await self.start_performance_monitoring()

            with self.track_operation("cdr_import", source=str(source)) as op_metrics:
                # Validate source
                if not await self.validate_source(source):
                    return ImportResult(
                        success=False,
                        records_imported=0,
                        records_failed=0,
                        source_path=str(source),
                        error_message="Source validation failed",
                        processing_time=0.0,
                        file_size_bytes=0,
                        metadata={
                            "validation_results": {
                                "errors": ["Invalid or inaccessible data source"]
                            }
                        }
                    )

                source_path = Path(source)
                if source_path.suffix.lower() in ['.xlsx', '.xls']:
                    excel_file = pd.ExcelFile(source)
                    sheets = excel_file.sheet_names
                    for sheet in sheets:
                        operator = self._detect_operator_from_sheet(sheet)
                        self.operator = operator  # Set for mapping and storage
                        df = pd.read_excel(excel_file, sheet_name=sheet)
                        if df.empty:
                            continue
                        processed = await self.process_data_async(df, self._process_cdr_batch, **kwargs)
                        validation_result = await self.validate_telecom_data(processed, "CDR")
                        if not validation_result.get("valid", True):
                            error_rate = validation_result.get("summary", {}).get("errors", 0) / len(processed)
                            if error_rate > self.cdr_config.max_error_rate:
                                all_warnings.append(f"Sheet {sheet} failed validation")
                                continue
                        await self._store_cdr_data(processed, table_name=self._generate_cdr_table_name(source_path.name))
                        total_metrics.records_processed += len(processed)
                        if 'warnings' in validation_result.get('results', {}):
                            all_warnings.extend([w['message'] for w in validation_result['results']['warnings'][:5]])
                else:
                    data = await self.process_data_async(source, self._process_cdr_batch, **kwargs)
                    if data is None or len(data) == 0:
                        return ImportResult(
                            success=False,
                            records_imported=0,
                            records_failed=0,
                            source_path=str(source),
                            error_message="No data loaded",
                            processing_time=(datetime.now() - start_time).total_seconds(),
                            file_size_bytes=source_path.stat().st_size if source_path.exists() else 0,
                            metadata={}
                        )
                    validation_result = await self.validate_telecom_data(data, "CDR")
                    if not validation_result.get("valid", True):
                        error_rate = validation_result.get("summary", {}).get("errors", 0) / len(data)
                        if error_rate > self.cdr_config.max_error_rate:
                            return ImportResult(
                                success=False,
                                records_imported=0,
                                records_failed=len(data),
                                source_path=str(source),
                                error_message="Data quality low",
                                processing_time=(datetime.now() - start_time).total_seconds(),
                                file_size_bytes=source_path.stat().st_size if source_path.exists() else 0,
                                metadata={
                                    "validation_result": validation_result,
                                    "error_rate": error_rate
                                }
                            )
                    await self._store_cdr_data(data, table_name=self._generate_cdr_table_name(source_path.name))
                    total_metrics.records_processed = len(data)
                    if 'warnings' in validation_result.get('results', {}):
                        all_warnings = [w['message'] for w in validation_result['results']['warnings'][:5]]

                processing_time = (datetime.now() - start_time).total_seconds()
                total_metrics.processing_time_seconds = processing_time

                return ImportResult(
                    success=True,
                    records_imported=total_metrics.records_processed,
                    records_failed=0,
                    source_path=str(source),
                    error_message=None,
                    processing_time=processing_time,
                    file_size_bytes=source_path.stat().st_size if source_path.exists() else 0,
                    metadata={
                        "warnings": all_warnings,
                        "source_info": {"path": str(source), "type": "CDR"},
                        "metrics": total_metrics.__dict__ if hasattr(total_metrics, '__dict__') else {}
                    }
                )

        except Exception as e:
            self.logger.error(f"CDR import failed: {e}")
            return ImportResult(
                success=False,
                records_imported=0,
                records_failed=0,
                source_path=str(source),
                error_message=str(e),
                processing_time=(datetime.now() - start_time).total_seconds() if 'start_time' in locals() else 0.0,
                file_size_bytes=Path(source).stat().st_size if Path(source).exists() else 0,
                metadata={}
            )

        finally:
            await self.stop_performance_monitoring()

    async def _process_cdr_batch(self, batch: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Process a batch of CDR data.

        Args:
            batch: Batch of CDR data
            **kwargs: Additional processing parameters

        Returns:
            Processed batch
        """
        try:
            # Apply operator-specific column mapping
            batch = self._map_operator_columns(batch)

            # Standardize data types
            batch = self._standardize_data_types(batch)

            # Process timestamps
            batch = self._process_timestamps(batch)

            # Calculate derived fields
            batch = self._calculate_derived_fields(batch)

            # Process geolocation if enabled
            if self.cdr_config.enable_geolocation:
                batch = self._process_geolocation(batch)

            # Detect call direction if enabled
            if self.cdr_config.enable_call_direction_detection:
                batch = self._detect_call_direction(batch)

            # Detect roaming if enabled
            if self.cdr_config.enable_roaming_detection:
                batch = self._detect_roaming(batch)

            # Add metadata - use .loc to avoid SettingWithCopyWarning
            batch = batch.copy()  # Ensure we're working with a copy
            batch.loc[:, "import_timestamp"] = datetime.now()
            batch.loc[:, "operator"] = self.cdr_config.operator
            batch.loc[:, "data_source"] = "cdr_import"

            return batch

        except Exception as e:
            self.logger.error(f"CDR batch processing failed: {e}")
            raise

    def _map_operator_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Map operator-specific column names to standard names."""
        schema = self.OPERATOR_SCHEMAS.get(
            self.cdr_config.operator, self.OPERATOR_SCHEMAS["generic"]
        )

        # Create reverse mapping (operator columns to standard columns)
        column_mapping = {v: k for k, v in schema.items() if v in df.columns}

        if column_mapping:
            df = df.rename(columns=column_mapping)
            self.logger.info(
                f"Mapped {len(column_mapping)} columns for operator {self.cdr_config.operator}"
            )

        # Handle duplicate column names by adding counter suffix
        clean_columns = []
        for col in df.columns:
            clean_name = str(col).strip().lower()
            
            # Ensure uniqueness by adding counter for duplicates
            original_clean_name = clean_name
            counter = 1
            while clean_name in clean_columns:
                clean_name = f'{original_clean_name}_{counter}'
                counter += 1
            
            clean_columns.append(clean_name)
        
        df.columns = clean_columns
        
        return df

    def _standardize_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize data types for CDR fields."""
        # Ensure we're working with a copy to avoid SettingWithCopyWarning
        df = df.copy()

        # Phone numbers as strings - explicitly cast column type first
        for col in ["calling_number", "called_number"]:
            if col in df.columns:
                # First convert to object dtype to avoid incompatible dtype warnings
                df[col] = df[col].astype('object')
                df.loc[:, col] = df[col].astype(str).str.strip()

        # Numeric fields
        if "call_duration" in df.columns:
            df.loc[:, "call_duration"] = pd.to_numeric(
                df["call_duration"], errors="coerce"
            )

        # Coordinates
        for col in ["latitude", "longitude"]:
            if col in df.columns:
                df.loc[:, col] = pd.to_numeric(df[col], errors="coerce")

        return df

    def _process_timestamps(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process and validate timestamps."""
        # Ensure we're working with a copy to avoid SettingWithCopyWarning
        df = df.copy()

        timestamp_columns = ["call_start_time", "call_end_time"]

        for col in timestamp_columns:
            if col in df.columns:
                try:
                    df.loc[:, col] = pd.to_datetime(df[col], errors="coerce")
                    # Localize to configured timezone if needed
                    if df[col].dt.tz is None:
                        try:
                            df.loc[:, col] = df[col].dt.tz_localize(
                                self.cdr_config.timezone,
                                ambiguous="infer",
                                nonexistent="shift_forward",
                            )
                        except TypeError:
                            # Fallback for older pandas versions
                            df.loc[:, col] = df[col].dt.tz_localize(
                                self.cdr_config.timezone
                            )
                except Exception as e:
                    self.logger.warning(f"Timestamp processing warning for {col}: {e}")

        # Create call_date for partitioning
        if "call_start_time" in df.columns:
            df.loc[:, "call_date"] = df["call_start_time"].dt.date

        return df

    def _calculate_derived_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate derived fields from CDR data."""
        # Ensure we're working with a copy to avoid SettingWithCopyWarning
        df = df.copy()

        # Calculate call duration if not present
        if (
            "call_start_time" in df.columns
            and "call_end_time" in df.columns
            and "call_duration" not in df.columns
        ):
            duration_seconds = (
                df["call_end_time"] - df["call_start_time"]
            ).dt.total_seconds()
            df.loc[:, "call_duration"] = duration_seconds.fillna(0)

        # Call hour for analysis
        if "call_start_time" in df.columns:
            df.loc[:, "call_hour"] = df["call_start_time"].dt.hour
            df.loc[:, "call_day_of_week"] = df["call_start_time"].dt.dayofweek

        # Call type classification
        if "call_duration" in df.columns:
            df.loc[:, "call_type"] = df["call_duration"].apply(self._classify_call_type)

        return df

    def _classify_call_type(self, duration: float) -> str:
        """Classify call type based on duration."""
        if pd.isna(duration) or duration <= 0:
            return "failed"
        elif duration < 5:
            return "short"
        elif duration < 60:
            return "normal"
        elif duration < 300:
            return "long"
        else:
            return "very_long"

    def _process_geolocation(self, df: pd.DataFrame) -> pd.DataFrame:
        """Process geolocation data and create geometry column."""
        if "latitude" in df.columns and "longitude" in df.columns:
            # Filter valid coordinates
            valid_coords = (
                df["latitude"].notna()
                & df["longitude"].notna()
                & (df["latitude"].between(-90, 90))
                & (df["longitude"].between(-180, 180))
            )

            # Create geometry column for valid coordinates
            if valid_coords.any():
                try:
                    from shapely.geometry import Point

                    df.loc[valid_coords, "geometry"] = df.loc[valid_coords].apply(
                        lambda row: Point(row["longitude"], row["latitude"]), axis=1
                    )
                except ImportError:
                    self.logger.warning(
                        "Shapely not available, skipping geometry creation"
                    )

            self.logger.info(f"Processed geolocation for {valid_coords.sum()} records")

        return df

    def _detect_call_direction(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect call direction (incoming/outgoing/internal)."""
        if "calling_number" in df.columns and "called_number" in df.columns:
            # This is a simplified implementation - would need operator-specific logic
            # Use .loc to avoid SettingWithCopyWarning
            df = df.copy()  # Ensure we're working with a copy
            df.loc[:, "call_direction"] = "unknown"

            # Example logic (would be customized per operator)
            country_prefix = "+34" if self.cdr_config.country_code == "ES" else "+49"

            # Outgoing calls (simplified)
            outgoing_mask = df["calling_number"].str.startswith(country_prefix)
            df.loc[outgoing_mask, "call_direction"] = "outgoing"

            # Incoming calls (simplified)
            incoming_mask = df["called_number"].str.startswith(country_prefix)
            df.loc[incoming_mask & ~outgoing_mask, "call_direction"] = "incoming"

        return df

    def _detect_roaming(self, df: pd.DataFrame) -> pd.DataFrame:
        """Detect roaming calls based on location and operator data."""
        # Simplified roaming detection - would need operator-specific logic
        # Use .loc to avoid SettingWithCopyWarning
        df = df.copy()  # Ensure we're working with a copy
        df.loc[:, "is_roaming"] = False

        # Example: detect based on country codes in phone numbers
        if "calling_number" in df.columns:
            home_country_prefix = (
                "+34" if self.cdr_config.country_code == "ES" else "+49"
            )

            roaming_mask = ~df["calling_number"].str.startswith(
                home_country_prefix
            ) | ~df["called_number"].str.startswith(home_country_prefix)

            df.loc[roaming_mask, "is_roaming"] = True

        return df

    def _detect_operator_from_sheet(self, sheet_name: str) -> str:
        sheet_lower = sheet_name.lower()
        if 'telefonica' in sheet_lower:
            return 'telefonica'
        if 'vodafone' in sheet_lower:
            return 'vodafone'
        if 'telekom' in sheet_lower:
            return 'telekom'
        return 'generic'

def _generate_cdr_table_name(self, filename: str) -> str:
        # Simple extraction: e.g., from 'Shared_Benchmark_Q2_DE_2024_HTTP_Browsing_...' -> '2024Q2_HTTP_Browsing'
        parts = filename.split('_')
        year = [p for p in parts if p.startswith('20') and len(p)==4][0]
        quarter = [p for p in parts if p.startswith('Q')][0]
        service = [p for p in parts if p in ['HTTP', 'Browsing', 'etc']][0]  # Adjust as needed
        return f'{year}{quarter}_{service}'

async def _store_cdr_data(self, data: pd.DataFrame, table_name: str = None) -> None:
        """Store CDR data in database with partitioning."""
        if not hasattr(self, "db_ops") or not self.db_ops:
            raise ValueError("Database operations not configured")

        table_name = "cdr_data"

        # Get target schema based on operator
        operator_schema_mapping = {
            'telefonica': 'cdr_to2',
            'vodafone': 'cdr_vdf',
            'telekom': 'cdr_tdg'
        }
        target_schema = operator_schema_mapping.get(self.operator, 'cdr_to2')

        # Apply time partitioning if enabled
        if self.cdr_config.enable_time_partitioning and "call_date" in data.columns:
            # Group by date and store in separate partitions
            for call_date, group in data.groupby("call_date"):
                partition_table = f"{table_name}_{call_date.strftime('%Y_%m')}"
                await self.db_ops.store_dataframe(
                    group, partition_table, if_exists="append", schema=target_schema
                )
        else:
            # Store in single table
            await self.db_ops.store_dataframe(data, table_name, if_exists="append", schema=target_schema)

async def validate_source(self, source: Union[str, Path]) -> bool:
    """Validate CDR data source.

    Args:
        source: Data source to validate

    Returns:
        True if source is valid
    """
    try:
        source_path = Path(source)

        # Check if file exists
        if not source_path.exists():
            self.logger.error(f"Source file does not exist: {source}")
            return False

        # Check file format
        if source_path.suffix.lower() not in self.supported_formats:
            self.logger.error(f"Unsupported file format: {source_path.suffix}")
            return False

        # Check file size (basic validation)
        file_size_mb = source_path.stat().st_size / 1024 / 1024
        if file_size_mb > 5000:  # 5GB limit
            self.logger.warning(f"Large file detected: {file_size_mb:.1f}MB")

        return True

    except Exception as e:
        self.logger.error(f"Source validation failed: {e}")
        return False

    async def get_source_info(self, source: Union[str, Path]) -> Dict[str, Any]:
        """Get information about CDR data source.

        Args:
            source: Data source

        Returns:
            Source information dictionary
        """
        try:
            source_path = Path(source)
            stat = source_path.stat()

            return {
                "file_path": str(source_path),
                "file_name": source_path.name,
                "file_size_bytes": stat.st_size,
                "file_size_mb": stat.st_size / 1024 / 1024,
                "file_format": source_path.suffix.lower(),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "is_supported": source_path.suffix.lower() in self.supported_formats,
                "estimated_records": self._estimate_record_count(source_path),
            }

        except Exception as e:
            self.logger.error(f"Failed to get source info: {e}")
            return {"error": str(e)}

    def _estimate_record_count(self, file_path: Path) -> Optional[int]:
        """Estimate number of records in file."""
        try:
            file_size_mb = file_path.stat().st_size / 1024 / 1024

            # Rough estimation based on file size and format
            if file_path.suffix.lower() == ".csv":
                # Assume ~200 bytes per CDR record on average
                return int(file_size_mb * 1024 * 1024 / 200)
            elif file_path.suffix.lower() in [".xlsx", ".xls"]:
                # Excel files are more compressed
                return int(file_size_mb * 1024 * 1024 / 150)
            else:
                return None

        except Exception:
            return None

    def get_operator_schema(self, operator: str) -> Dict[str, str]:
        """Get column schema for specific operator.

        Args:
            operator: Operator name

        Returns:
            Column mapping dictionary
        """
        return self.OPERATOR_SCHEMAS.get(operator, self.OPERATOR_SCHEMAS["generic"])

    def get_supported_operators(self) -> List[str]:
        """Get list of supported operators.

        Returns:
            List of supported operator names
        """
        return list(self.OPERATOR_SCHEMAS.keys())
