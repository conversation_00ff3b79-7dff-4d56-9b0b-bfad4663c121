#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unit tests for batch processing utilities.

This module contains comprehensive tests for the batch_processor module,
including tests for generate_batches and bulk_insert_batched functions.
"""

import asyncio
from typing import Any, Dict, List
from unittest.mock import AsyncMock, Mock, patch

import pytest

from src.database.exceptions import DatabaseError, ValidationError
from src.database.utils.batch_processor import (
    BatchProcessor,
    bulk_insert_batched,
    bulk_insert_with_copy,
    generate_batches,
)


class TestGenerateBatches:
    """Test cases for generate_batches function."""

    def test_generate_batches_basic(self):
        """Test basic batch generation functionality."""
        data = [
            {"id": 1, "name": "<PERSON>"},
            {"id": 2, "name": "<PERSON>"},
            {"id": 3, "name": "<PERSON>"},
            {"id": 4, "name": "<PERSON>"},
            {"id": 5, "name": "<PERSON>"},
        ]

        batches = list(generate_batches(data, batch_size=2))

        assert len(batches) == 3
        assert batches[0] == [{"id": 1, "name": "<PERSON>"}, {"id": 2, "name": "<PERSON>"}]
        assert batches[1] == [{"id": 3, "name": "<PERSON>"}, {"id": 4, "name": "David"}]
        assert batches[2] == [{"id": 5, "name": "Eve"}]

    def test_generate_batches_exact_division(self):
        """Test batch generation when data length is exactly divisible by batch size."""
        data = [
            {"id": 1, "name": "Alice"},
            {"id": 2, "name": "Bob"},
            {"id": 3, "name": "Charlie"},
            {"id": 4, "name": "David"},
        ]

        batches = list(generate_batches(data, batch_size=2))

        assert len(batches) == 2
        assert batches[0] == [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}]
        assert batches[1] == [{"id": 3, "name": "Charlie"}, {"id": 4, "name": "David"}]

    def test_generate_batches_single_batch(self):
        """Test batch generation when batch size is larger than data length."""
        data = [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}]

        batches = list(generate_batches(data, batch_size=5))

        assert len(batches) == 1
        assert batches[0] == data

    def test_generate_batches_empty_data(self):
        """Test batch generation with empty data list."""
        data = []

        batches = list(generate_batches(data, batch_size=2))

        assert len(batches) == 0

    def test_generate_batches_batch_size_one(self):
        """Test batch generation with batch size of 1."""
        data = [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}]

        batches = list(generate_batches(data, batch_size=1))

        assert len(batches) == 2
        assert batches[0] == [{"id": 1, "name": "Alice"}]
        assert batches[1] == [{"id": 2, "name": "Bob"}]

    def test_generate_batches_invalid_batch_size(self):
        """Test batch generation with invalid batch size."""
        data = [{"id": 1, "name": "Alice"}]

        with pytest.raises(
            ValidationError, match="Batch size must be a positive integer"
        ):
            list(generate_batches(data, batch_size=0))

        with pytest.raises(
            ValidationError, match="Batch size must be a positive integer"
        ):
            list(generate_batches(data, batch_size=-1))

        with pytest.raises(
            ValidationError, match="Batch size must be a positive integer"
        ):
            list(generate_batches(data, batch_size="invalid"))

    def test_generate_batches_invalid_data_type(self):
        """Test batch generation with invalid data type."""
        with pytest.raises(
            ValidationError, match="Data must be a list of dictionaries"
        ):
            list(generate_batches("not a list", batch_size=2))

        with pytest.raises(
            ValidationError, match="Data must be a list of dictionaries"
        ):
            list(generate_batches(None, batch_size=2))

    def test_generate_batches_invalid_data_items(self):
        """Test batch generation with invalid items in data list."""
        data = [{"id": 1, "name": "Alice"}, "not a dict", {"id": 3, "name": "Charlie"}]

        with pytest.raises(
            ValidationError, match="Item at index 1 is not a dictionary"
        ):
            list(generate_batches(data, batch_size=2))


class TestBulkInsertBatched:
    """Test cases for bulk_insert_batched function."""

    @pytest.fixture
    def mock_session_manager(self):
        """Create a mock session manager."""
        session_manager = AsyncMock()
        return session_manager

    @pytest.fixture
    def mock_crud_operations(self):
        """Create a mock CRUD operations instance."""
        crud_ops = AsyncMock()
        crud_ops.bulk_insert = AsyncMock(
            return_value=[{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}]
        )
        return crud_ops

    @pytest.mark.asyncio
    async def test_bulk_insert_batched_success(
        self, mock_session_manager, mock_crud_operations
    ):
        """Test successful bulk insert with batching."""
        records = [
            {"name": "Alice", "age": 30},
            {"name": "Bob", "age": 25},
            {"name": "Charlie", "age": 35},
            {"name": "David", "age": 28},
        ]

        result = await bulk_insert_batched(
            session_manager=mock_session_manager,
            records=records,
            table_name="users",
            schema_name="public",
            batch_size=2,
            crud_operations=mock_crud_operations,
        )

        assert result["success"] is True
        assert result["total_records"] == 4
        assert result["batches_processed"] == 2
        assert result["records_inserted"] == 4
        assert result["batch_size"] == 2
        assert len(result["errors"]) == 0

        # Verify CRUD operations were called correctly
        assert mock_crud_operations.bulk_insert.call_count == 2

    @pytest.mark.asyncio
    async def test_bulk_insert_batched_empty_records(self, mock_session_manager):
        """Test bulk insert with empty records list."""
        records = []

        result = await bulk_insert_batched(
            session_manager=mock_session_manager,
            records=records,
            table_name="users",
            schema_name="public",
            batch_size=2,
        )

        assert result["success"] is True
        assert result["total_records"] == 0
        assert result["batches_processed"] == 0
        assert result["records_inserted"] == 0
        assert len(result["errors"]) == 0

    @pytest.mark.asyncio
    async def test_bulk_insert_batched_invalid_table_name(self, mock_session_manager):
        """Test bulk insert with invalid table name."""
        records = [{"name": "Alice"}]

        with patch(
            "src.database.utils.batch_processor.SQLInjectionGuard.validate_identifier",
            return_value=False,
        ):
            with pytest.raises(ValidationError, match="Invalid table name"):
                await bulk_insert_batched(
                    session_manager=mock_session_manager,
                    records=records,
                    table_name="invalid; DROP TABLE users;",
                    schema_name="public",
                )

    @pytest.mark.asyncio
    async def test_bulk_insert_batched_invalid_schema_name(self, mock_session_manager):
        """Test bulk insert with invalid schema name."""
        records = [{"name": "Alice"}]

        with patch(
            "src.database.utils.batch_processor.SQLInjectionGuard.validate_identifier"
        ) as mock_validate:
            mock_validate.side_effect = lambda name, type_: type_ != "schema"

            with pytest.raises(ValidationError, match="Invalid schema name"):
                await bulk_insert_batched(
                    session_manager=mock_session_manager,
                    records=records,
                    table_name="users",
                    schema_name="invalid; DROP SCHEMA public;",
                )

    @pytest.mark.asyncio
    async def test_bulk_insert_batched_invalid_records_type(self, mock_session_manager):
        """Test bulk insert with invalid records type."""
        with pytest.raises(
            ValidationError, match="Records must be a list of dictionaries"
        ):
            await bulk_insert_batched(
                session_manager=mock_session_manager,
                records="not a list",
                table_name="users",
                schema_name="public",
            )

    @pytest.mark.asyncio
    async def test_bulk_insert_batched_invalid_batch_size(self, mock_session_manager):
        """Test bulk insert with invalid batch size."""
        records = [{"name": "Alice"}]

        with pytest.raises(
            ValidationError, match="Batch size must be a positive integer"
        ):
            await bulk_insert_batched(
                session_manager=mock_session_manager,
                records=records,
                table_name="users",
                schema_name="public",
                batch_size=0,
            )

    @pytest.mark.asyncio
    async def test_bulk_insert_batched_partial_failure(self, mock_session_manager):
        """Test bulk insert with partial batch failures."""
        records = [
            {"name": "Alice", "age": 30},
            {"name": "Bob", "age": 25},
            {"name": "Charlie", "age": 35},
            {"name": "David", "age": 28},
        ]

        # Mock CRUD operations to fail on second batch
        mock_crud_operations = AsyncMock()
        mock_crud_operations.bulk_insert.side_effect = [
            [
                {"id": 1, "name": "Alice"},
                {"id": 2, "name": "Bob"},
            ],  # First batch succeeds
            Exception("Database error"),  # Second batch fails
        ]

        result = await bulk_insert_batched(
            session_manager=mock_session_manager,
            records=records,
            table_name="users",
            schema_name="public",
            batch_size=2,
            crud_operations=mock_crud_operations,
        )

        assert result["success"] is False
        assert result["total_records"] == 4
        assert result["batches_processed"] == 1  # Only first batch succeeded
        assert result["records_inserted"] == 2
        assert len(result["errors"]) == 1
        assert "Error processing batch 2" in result["errors"][0]


class TestBulkInsertWithCopy:
    """Test cases for bulk_insert_with_copy function."""

    @pytest.fixture
    def mock_session_manager(self):
        """Create a mock session manager with connection context."""
        mock_conn = AsyncMock()
        mock_conn.fetch.return_value = [
            {"column_name": "id"},
            {"column_name": "name"},
            {"column_name": "age"},
        ]
        mock_conn.copy_records_to_table = AsyncMock()

        session_manager = AsyncMock()
        session_manager.__aenter__.return_value = mock_conn
        session_manager.__aexit__.return_value = None

        return session_manager

    @pytest.mark.asyncio
    async def test_bulk_insert_with_copy_success(self, mock_session_manager):
        """Test successful bulk insert using COPY method."""
        records = [
            {"id": 1, "name": "Alice", "age": 30},
            {"id": 2, "name": "Bob", "age": 25},
            {"id": 3, "name": "Charlie", "age": 35},
        ]

        result = await bulk_insert_with_copy(
            session_manager=mock_session_manager,
            records=records,
            table_name="users",
            schema_name="public",
            batch_size=2,
        )

        assert result["success"] is True
        assert result["total_records"] == 3
        assert result["batches_processed"] == 2
        assert result["records_inserted"] == 3
        assert len(result["errors"]) == 0

        # Verify copy_records_to_table was called
        mock_conn = await mock_session_manager.__aenter__()
        assert mock_conn.copy_records_to_table.call_count == 2

    @pytest.mark.asyncio
    async def test_bulk_insert_with_copy_table_not_found(self, mock_session_manager):
        """Test bulk insert with COPY when table is not found."""
        # Mock empty column result to simulate table not found
        mock_conn = await mock_session_manager.__aenter__()
        mock_conn.fetch.return_value = []

        records = [{"name": "Alice"}]

        with pytest.raises(
            DatabaseError, match="Table public.users not found or has no columns"
        ):
            await bulk_insert_with_copy(
                session_manager=mock_session_manager,
                records=records,
                table_name="users",
                schema_name="public",
            )


class TestBatchProcessor:
    """Test cases for BatchProcessor class."""

    @pytest.fixture
    def mock_session_manager(self):
        """Create a mock session manager."""
        return AsyncMock()

    @pytest.fixture
    def mock_crud_operations(self):
        """Create a mock CRUD operations instance."""
        crud_ops = AsyncMock()
        crud_ops.bulk_insert = AsyncMock(return_value=[{"id": 1, "name": "Alice"}])
        return crud_ops

    def test_batch_processor_init(self, mock_session_manager, mock_crud_operations):
        """Test BatchProcessor initialization."""
        processor = BatchProcessor(mock_session_manager, mock_crud_operations)

        assert processor.session_manager == mock_session_manager
        assert processor.crud_operations == mock_crud_operations

    def test_batch_processor_init_without_crud(self, mock_session_manager):
        """Test BatchProcessor initialization without CRUD operations."""
        with patch("src.database.operations.crud.CRUDOperations") as mock_crud_class:
            mock_crud_instance = Mock()
            mock_crud_class.return_value = mock_crud_instance

            processor = BatchProcessor(mock_session_manager)

            assert processor.session_manager == mock_session_manager
            assert processor.crud_operations == mock_crud_instance
            mock_crud_class.assert_called_once_with(mock_session_manager)

    @pytest.mark.asyncio
    async def test_batch_processor_process_batches_regular(
        self, mock_session_manager, mock_crud_operations
    ):
        """Test BatchProcessor process_batches with regular method."""
        processor = BatchProcessor(mock_session_manager, mock_crud_operations)
        records = [{"name": "Alice"}]

        with patch(
            "src.database.utils.batch_processor.bulk_insert_batched"
        ) as mock_bulk_insert:
            mock_bulk_insert.return_value = {"success": True, "records_inserted": 1}

            result = await processor.process_batches(
                records=records,
                table_name="users",
                schema_name="public",
                batch_size=1000,
                use_copy=False,
            )

            assert result["success"] is True
            mock_bulk_insert.assert_called_once_with(
                mock_session_manager,
                records,
                "users",
                "public",
                1000,
                mock_crud_operations,
            )

    @pytest.mark.asyncio
    async def test_batch_processor_process_batches_copy(
        self, mock_session_manager, mock_crud_operations
    ):
        """Test BatchProcessor process_batches with COPY method."""
        processor = BatchProcessor(mock_session_manager, mock_crud_operations)
        records = [{"name": "Alice"}]

        with patch(
            "src.database.utils.batch_processor.bulk_insert_with_copy"
        ) as mock_bulk_copy:
            mock_bulk_copy.return_value = {"success": True, "records_inserted": 1}

            result = await processor.process_batches(
                records=records,
                table_name="users",
                schema_name="public",
                batch_size=5000,
                use_copy=True,
            )

            assert result["success"] is True
            mock_bulk_copy.assert_called_once_with(
                mock_session_manager, records, "users", "public", 5000
            )

    def test_batch_processor_generate_batches(self, mock_session_manager):
        """Test BatchProcessor generate_batches method."""
        processor = BatchProcessor(mock_session_manager)
        data = [
            {"id": 1, "name": "Alice"},
            {"id": 2, "name": "Bob"},
            {"id": 3, "name": "Charlie"},
        ]

        batches = list(processor.generate_batches(data, batch_size=2))

        assert len(batches) == 2
        assert batches[0] == [{"id": 1, "name": "Alice"}, {"id": 2, "name": "Bob"}]
        assert batches[1] == [{"id": 3, "name": "Charlie"}]


if __name__ == "__main__":
    pytest.main([__file__])
