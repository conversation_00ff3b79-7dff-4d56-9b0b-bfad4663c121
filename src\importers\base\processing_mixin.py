"""Processing mixin for memory-optimized batch processing with async support.

This module provides comprehensive data processing capabilities with support
for both Pandas and Polars engines, async processing, and memory optimization.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import gc
import logging
import psutil
from typing import Any, Dict, List, Optional, Union, AsyncGenerator, Callable
from datetime import datetime
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from pathlib import Path

import pandas as pd
import numpy as np
from pydantic import BaseModel, Field, ConfigDict


@dataclass
class ProcessingMetrics:
    """Metrics for processing operations."""
    batches_processed: int = 0
    records_processed: int = 0
    processing_time_seconds: float = 0.0
    memory_peak_mb: float = 0.0
    memory_current_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    throughput_records_per_second: float = 0.0
    errors: List[str] = field(default_factory=list)
    
    def update_throughput(self) -> None:
        """Update throughput calculation."""
        if self.processing_time_seconds > 0:
            self.throughput_records_per_second = self.records_processed / self.processing_time_seconds


class ProcessingConfig(BaseModel):
    """Configuration for data processing operations."""
    batch_size: int = Field(default=10000, ge=1, le=1000000, description="Batch size for processing")
    max_workers: int = Field(default=4, ge=1, le=32, description="Maximum number of worker threads")
    memory_limit_mb: int = Field(default=1024, ge=128, le=8192, description="Memory limit in MB")
    enable_parallel: bool = Field(default=True, description="Enable parallel processing")
    enable_progress_tracking: bool = Field(default=True, description="Enable progress tracking")
    chunk_size: int = Field(default=1000, ge=100, le=100000, description="Chunk size for file reading")
    use_polars: bool = Field(default=False, description="Use Polars instead of Pandas")
    compression_level: int = Field(default=6, ge=1, le=9, description="Compression level for temporary files")
    temp_dir: Optional[str] = Field(default=None, description="Temporary directory for processing")
    
    # Async processing settings
    async_batch_size: int = Field(default=5000, description="Batch size for async processing")
    max_concurrent_batches: int = Field(default=3, description="Maximum concurrent batches")
    async_timeout_seconds: int = Field(default=300, description="Timeout for async operations")
    
    # Memory management
    gc_frequency: int = Field(default=10, description="Garbage collection frequency (batches)")
    memory_check_frequency: int = Field(default=5, description="Memory check frequency (batches)")
    memory_warning_threshold: float = Field(default=0.8, description="Memory warning threshold (0-1)")
    
    model_config = ConfigDict(
        extra="allow"
    )
class ProcessingMixin:
    """Mixin class providing memory-optimized batch processing capabilities.
    
    This mixin can be used with any importer to add comprehensive processing
    functionality with async support and memory optimization.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.processing_config = ProcessingConfig()
        self.processing_metrics = ProcessingMetrics()
        self.processing_logger = logging.getLogger(f"{self.__class__.__name__}.processing")
        self._executor = None
        self._process = psutil.Process()
        
        # Initialize data engine
        self._init_data_engine()
        
    def configure_processing(self, config: Union[ProcessingConfig, Dict[str, Any]]) -> None:
        """Configure processing settings.
        
        Args:
            config: Processing configuration
        """
        if isinstance(config, dict):
            self.processing_config = ProcessingConfig(**config)
        else:
            self.processing_config = config
            
        # Reinitialize data engine if needed
        self._init_data_engine()
        
    def _init_data_engine(self) -> None:
        """Initialize the data processing engine."""
        if self.processing_config.use_polars:
            try:
                import polars as pl
                self.pl = pl
                self.data_engine = 'polars'
                self.processing_logger.info("Initialized Polars data engine")
            except ImportError:
                self.processing_logger.warning("Polars not available, using Pandas")
                self.data_engine = 'pandas'
        else:
            self.data_engine = 'pandas'
            
    async def process_data_async(self, 
                                data: Union[pd.DataFrame, str, Path],
                                processor_func: Callable,
                                **kwargs) -> Any:
        """Process data asynchronously with memory optimization.
        
        Args:
            data: Data to process (DataFrame or file path)
            processor_func: Function to process each batch
            **kwargs: Additional arguments for processor function
            
        Returns:
            Processed data
        """
        start_time = datetime.now()
        self.processing_metrics = ProcessingMetrics()
        
        try:
            # Load data if it's a file path
            if isinstance(data, (str, Path)):
                data = await self._load_data_async(data)
                
            # Process data in batches
            results = []
            async for batch_result in self._process_batches_async(data, processor_func, **kwargs):
                results.append(batch_result)
                
            # Combine results
            final_result = await self._combine_results(results)
            
            # Update metrics
            self.processing_metrics.processing_time_seconds = (
                datetime.now() - start_time
            ).total_seconds()
            self.processing_metrics.update_throughput()
            
            return final_result
            
        except Exception as e:
            self.processing_logger.error(f"Async processing failed: {e}")
            self.processing_metrics.errors.append(str(e))
            raise
            
    async def _load_data_async(self, file_path: Union[str, Path], **kwargs) -> Union[pd.DataFrame, Any]:
        """Load data asynchronously from file."""
        file_path = Path(file_path)
        
        if self.data_engine == 'polars':
            return await self._load_with_polars(file_path)
        else:
            return await self._load_with_pandas(file_path, **kwargs)
            
    async def _load_with_pandas(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """Load data using Pandas with chunking."""
        suffix = file_path.suffix.lower()
        
        if suffix == '.csv':
            # Load CSV in chunks
            chunks = []
            chunk_reader = pd.read_csv(
                file_path,
                chunksize=self.processing_config.chunk_size,
                low_memory=False
            )
            
            for chunk in chunk_reader:
                chunks.append(chunk)
                await self._check_memory_usage()
                
            return pd.concat(chunks, ignore_index=True)
            
        elif suffix in ['.xlsx', '.xls', '.xlsb']:
            # Load Excel file with configuration support
            loop = asyncio.get_event_loop()
            # Get optimal engine for the file type
            engine = self._get_optimal_engine(suffix)
            
            # Prepare read_excel parameters
            read_params = {'engine': engine}
            
            # Add skip_rows from data source config if available
            if hasattr(self, 'data_source_config') and self.data_source_config:
                source_config = self.data_source_config
                if isinstance(source_config, dict) and 'skip_rows' in source_config:
                    read_params['skiprows'] = source_config['skip_rows']
                    if hasattr(self, 'logger'):
                        data_type = getattr(self, 'data_type', 'unknown')
                        self.logger.info(f"Using skip_rows={source_config['skip_rows']} from config for {data_type}")
                elif hasattr(source_config, 'skip_rows') and source_config.skip_rows is not None:
                    read_params['skiprows'] = source_config.skip_rows
                    if hasattr(self, 'logger'):
                        data_type = getattr(self, 'data_type', 'unknown')
                        self.logger.info(f"Using skip_rows={source_config.skip_rows} from config for {data_type}")
            
            # Override with kwargs if provided
            if 'skiprows' in kwargs:
                read_params['skiprows'] = kwargs['skiprows']
            if 'header' in kwargs:
                read_params['header'] = kwargs['header']
            
            return await loop.run_in_executor(
                self._get_executor(),
                lambda: pd.read_excel(file_path, **read_params)
            )
            
        elif suffix == '.json':
            # Load JSON file
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(
                self._get_executor(),
                pd.read_json,
                file_path
            )
            
        else:
            raise ValueError(f"Unsupported file format: {suffix}")
            
    async def _load_with_polars(self, file_path: Path) -> Any:
        """Load data using Polars."""
        suffix = file_path.suffix.lower()
        
        if suffix == '.csv':
            return self.pl.read_csv(file_path)
        elif suffix == '.json':
            return self.pl.read_json(file_path)
        else:
            # Convert to Pandas for unsupported formats
            df = await self._load_with_pandas(file_path)
            return self.pl.from_pandas(df)
            
    async def _process_batches_async(self, 
                                   data: Union[pd.DataFrame, Any],
                                   processor_func: Callable,
                                   **kwargs) -> AsyncGenerator[Any, None]:
        """Process data in batches asynchronously."""
        total_records = len(data)
        batch_size = self.processing_config.async_batch_size
        
        # Create semaphore to limit concurrent batches
        semaphore = asyncio.Semaphore(self.processing_config.max_concurrent_batches)
        
        # Process batches
        tasks = []
        for i in range(0, total_records, batch_size):
            batch = data.iloc[i:i + batch_size] if hasattr(data, 'iloc') else data[i:i + batch_size]
            task = self._process_single_batch(semaphore, batch, processor_func, **kwargs)
            tasks.append(task)
            
        # Execute batches with progress tracking
        for i, task in enumerate(asyncio.as_completed(tasks)):
            try:
                result = await asyncio.wait_for(
                    task,
                    timeout=self.processing_config.async_timeout_seconds
                )
                
                self.processing_metrics.batches_processed += 1
                self.processing_metrics.records_processed += len(result) if hasattr(result, '__len__') else batch_size
                
                # Memory management
                if i % self.processing_config.gc_frequency == 0:
                    gc.collect()
                    
                if i % self.processing_config.memory_check_frequency == 0:
                    await self._check_memory_usage()
                    
                yield result
                
            except asyncio.TimeoutError:
                self.processing_logger.error(f"Batch processing timeout after {self.processing_config.async_timeout_seconds}s")
                self.processing_metrics.errors.append("Batch processing timeout")
                
            except Exception as e:
                self.processing_logger.error(f"Batch processing error: {e}")
                self.processing_metrics.errors.append(str(e))
                
    async def _process_single_batch(self, 
                                  semaphore: asyncio.Semaphore,
                                  batch: Any,
                                  processor_func: Callable,
                                  **kwargs) -> Any:
        """Process a single batch with semaphore control."""
        async with semaphore:
            try:
                # Check if processor function is async
                if asyncio.iscoroutinefunction(processor_func):
                    return await processor_func(batch, **kwargs)
                else:
                    # Run synchronous function in executor
                    loop = asyncio.get_event_loop()
                    return await loop.run_in_executor(
                        self._get_executor(),
                        processor_func,
                        batch,
                        **kwargs
                    )
                    
            except Exception as e:
                self.processing_logger.error(f"Single batch processing error: {e}")
                raise
                
    async def _combine_results(self, results: List[Any]) -> Any:
        """Combine processing results."""
        if not results:
            return None
            
        # Handle different result types
        if isinstance(results[0], pd.DataFrame):
            return pd.concat(results, ignore_index=True)
        elif hasattr(results[0], 'concat'):  # Polars DataFrame
            return self.pl.concat(results)
        elif isinstance(results[0], list):
            # Flatten list of lists
            combined = []
            for result in results:
                combined.extend(result)
            return combined
        else:
            return results
            
    async def _check_memory_usage(self) -> None:
        """Check current memory usage and warn if threshold exceeded."""
        memory_info = self._process.memory_info()
        current_memory_mb = memory_info.rss / 1024 / 1024
        
        self.processing_metrics.memory_current_mb = current_memory_mb
        if current_memory_mb > self.processing_metrics.memory_peak_mb:
            self.processing_metrics.memory_peak_mb = current_memory_mb
            
        # Check against limit
        memory_usage_ratio = current_memory_mb / self.processing_config.memory_limit_mb
        if memory_usage_ratio > self.processing_config.memory_warning_threshold:
            self.processing_logger.warning(
                f"High memory usage: {current_memory_mb:.1f}MB "
                f"({memory_usage_ratio:.1%} of limit)"
            )
            
            # Force garbage collection
            gc.collect()
            
        # Update CPU usage
        self.processing_metrics.cpu_usage_percent = self._process.cpu_percent()
        
    def _get_executor(self) -> ThreadPoolExecutor:
        """Get or create thread pool executor."""
        if self._executor is None:
            self._executor = ThreadPoolExecutor(
                max_workers=self.processing_config.max_workers,
                thread_name_prefix="importer_worker"
            )
        return self._executor
        
    def process_data_sync(self, 
                         data: Union[pd.DataFrame, str, Path],
                         processor_func: Callable,
                         **kwargs) -> Any:
        """Process data synchronously with memory optimization.
        
        Args:
            data: Data to process
            processor_func: Function to process each batch
            **kwargs: Additional arguments
            
        Returns:
            Processed data
        """
        start_time = datetime.now()
        self.processing_metrics = ProcessingMetrics()
        
        try:
            # Load data if it's a file path
            if isinstance(data, (str, Path)):
                data = self._load_data_sync(data)
                
            # Process in batches
            results = []
            total_records = len(data)
            batch_size = self.processing_config.batch_size
            
            for i in range(0, total_records, batch_size):
                batch = data.iloc[i:i + batch_size] if hasattr(data, 'iloc') else data[i:i + batch_size]
                
                try:
                    result = processor_func(batch, **kwargs)
                    results.append(result)
                    
                    self.processing_metrics.batches_processed += 1
                    self.processing_metrics.records_processed += len(batch)
                    
                    # Memory management
                    if i % self.processing_config.gc_frequency == 0:
                        gc.collect()
                        
                    if i % self.processing_config.memory_check_frequency == 0:
                        self._check_memory_usage_sync()
                        
                except Exception as e:
                    self.processing_logger.error(f"Batch processing error: {e}")
                    self.processing_metrics.errors.append(str(e))
                    
            # Combine results
            final_result = self._combine_results_sync(results)
            
            # Update metrics
            self.processing_metrics.processing_time_seconds = (
                datetime.now() - start_time
            ).total_seconds()
            self.processing_metrics.update_throughput()
            
            return final_result
            
        except Exception as e:
            self.processing_logger.error(f"Sync processing failed: {e}")
            self.processing_metrics.errors.append(str(e))
            raise
            
    def _load_data_sync(self, file_path: Union[str, Path]) -> Union[pd.DataFrame, Any]:
        """Load data synchronously from file."""
        file_path = Path(file_path)
        suffix = file_path.suffix.lower()
        
        if self.data_engine == 'polars':
            if suffix == '.csv':
                return self.pl.read_csv(file_path)
            elif suffix == '.json':
                return self.pl.read_json(file_path)
            else:
                # Fallback to Pandas
                df = pd.read_excel(file_path) if suffix in ['.xlsx', '.xls'] else pd.read_csv(file_path)
                return self.pl.from_pandas(df)
        else:
            if suffix == '.csv':
                return pd.read_csv(file_path, chunksize=None)
            elif suffix in ['.xlsx', '.xls', '.xlsb']:
                engine = self._get_optimal_engine(suffix)
                return pd.read_excel(file_path, engine=engine)
            elif suffix == '.json':
                return pd.read_json(file_path)
            else:
                raise ValueError(f"Unsupported file format: {suffix}")

    def _get_optimal_engine(self, file_extension: str) -> str:
        """Get optimal pandas engine for file type.

        Args:
            file_extension: File extension (e.g., '.xlsb', '.xlsx')

        Returns:
            str: Engine name
        """
        engine_map = {
            '.xlsb': 'pyxlsb',  # Fastest for binary Excel files
            '.xlsx': 'openpyxl',  # Good for modern Excel files
            '.xls': 'xlrd'  # For legacy Excel files
        }
        return engine_map.get(file_extension, 'openpyxl')

    def _check_memory_usage_sync(self) -> None:
        """Check memory usage synchronously."""
        memory_info = self._process.memory_info()
        current_memory_mb = memory_info.rss / 1024 / 1024
        
        self.processing_metrics.memory_current_mb = current_memory_mb
        if current_memory_mb > self.processing_metrics.memory_peak_mb:
            self.processing_metrics.memory_peak_mb = current_memory_mb
            
        # Check against limit
        memory_usage_ratio = current_memory_mb / self.processing_config.memory_limit_mb
        if memory_usage_ratio > self.processing_config.memory_warning_threshold:
            self.processing_logger.warning(
                f"High memory usage: {current_memory_mb:.1f}MB "
                f"({memory_usage_ratio:.1%} of limit)"
            )
            gc.collect()
            
        self.processing_metrics.cpu_usage_percent = self._process.cpu_percent()
        
    def _combine_results_sync(self, results: List[Any]) -> Any:
        """Combine results synchronously."""
        if not results:
            return None
            
        if isinstance(results[0], pd.DataFrame):
            return pd.concat(results, ignore_index=True)
        elif hasattr(results[0], 'concat'):  # Polars
            return self.pl.concat(results)
        elif isinstance(results[0], list):
            combined = []
            for result in results:
                combined.extend(result)
            return combined
        else:
            return results
            
    def get_processing_metrics(self) -> ProcessingMetrics:
        """Get current processing metrics."""
        return self.processing_metrics
        
    def reset_processing_metrics(self) -> None:
        """Reset processing metrics."""
        self.processing_metrics = ProcessingMetrics()
        
    def cleanup(self) -> None:
        """Cleanup resources."""
        if self._executor:
            try:
                self._executor.shutdown(wait=False)  # Don't wait to avoid deadlock
                self._executor = None
            except Exception as e:
                self.processing_logger.warning(f"Error during executor shutdown: {e}")
                self._executor = None
            
        # Force garbage collection
        gc.collect()
        
    def __del__(self):
        """Destructor to cleanup resources."""
        try:
            # Use atexit to register cleanup instead of direct call
            import atexit
            atexit.register(self._safe_cleanup)
        except Exception:
            # If atexit fails, try direct cleanup but don't wait
            try:
                if self._executor:
                    self._executor.shutdown(wait=False)
            except Exception:
                pass  # Ignore errors during destruction
    
    def _safe_cleanup(self):
        """Safe cleanup for atexit registration."""
        try:
            if self._executor:
                self._executor.shutdown(wait=False)
                self._executor = None
        except Exception:
            pass  # Ignore all errors during safe cleanup