"""统一验证框架文档生成器

自动生成API文档、用户手册、配置指南等文档，
确保文档与代码保持同步。
"""

import os
import sys
import json
import inspect
import ast
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import re
from datetime import datetime
import subprocess

# 版本信息
VERSION = "1.0.0"


@dataclass
class DocConfig:
    """文档生成配置"""
    # 输出配置
    output_dir: str = 'docs'
    format: str = 'markdown'  # markdown, html, pdf
    
    # 内容配置
    include_api: bool = True
    include_examples: bool = True
    include_config: bool = True
    include_tutorials: bool = True
    
    # 样式配置
    theme: str = 'default'
    language: str = 'zh-cn'
    
    # 源码配置
    source_dir: str = '.'
    exclude_patterns: List[str] = None
    
    def __post_init__(self):
        if self.exclude_patterns is None:
            self.exclude_patterns = ['__pycache__', '*.pyc', 'tests', '.git']


@dataclass
class ClassInfo:
    """类信息"""
    name: str
    docstring: str
    methods: List[Dict[str, Any]]
    attributes: List[Dict[str, Any]]
    inheritance: List[str]
    module: str
    file_path: str
    line_number: int


@dataclass
class FunctionInfo:
    """函数信息"""
    name: str
    docstring: str
    parameters: List[Dict[str, Any]]
    return_type: str
    module: str
    file_path: str
    line_number: int
    is_async: bool = False


@dataclass
class ModuleInfo:
    """模块信息"""
    name: str
    docstring: str
    classes: List[ClassInfo]
    functions: List[FunctionInfo]
    file_path: str
    imports: List[str]


class CodeAnalyzer:
    """代码分析器"""
    
    def __init__(self):
        self.modules: Dict[str, ModuleInfo] = {}
    
    def analyze_directory(self, directory: str, exclude_patterns: List[str] = None) -> Dict[str, ModuleInfo]:
        """分析目录中的Python文件"""
        if exclude_patterns is None:
            exclude_patterns = ['__pycache__', '*.pyc', 'tests']
        
        python_files = self._find_python_files(directory, exclude_patterns)
        
        for file_path in python_files:
            try:
                module_info = self._analyze_file(file_path)
                if module_info:
                    self.modules[module_info.name] = module_info
            except Exception as e:
                print(f"分析文件 {file_path} 时出错: {e}")
        
        return self.modules
    
    def _find_python_files(self, directory: str, exclude_patterns: List[str]) -> List[str]:
        """查找Python文件"""
        python_files = []
        
        for root, dirs, files in os.walk(directory):
            # 排除目录
            dirs[:] = [d for d in dirs if not any(
                self._match_pattern(d, pattern) for pattern in exclude_patterns
            )]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    if not any(self._match_pattern(file_path, pattern) for pattern in exclude_patterns):
                        python_files.append(file_path)
        
        return python_files
    
    def _match_pattern(self, text: str, pattern: str) -> bool:
        """匹配模式"""
        if '*' in pattern:
            import fnmatch
            return fnmatch.fnmatch(text, pattern)
        return pattern in text
    
    def _analyze_file(self, file_path: str) -> Optional[ModuleInfo]:
        """分析单个Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            # 获取模块名
            module_name = self._get_module_name(file_path)
            
            # 获取模块文档字符串
            module_docstring = ast.get_docstring(tree) or ""
            
            # 分析类和函数
            classes = []
            functions = []
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = self._analyze_class(node, file_path)
                    if class_info:
                        classes.append(class_info)
                elif isinstance(node, ast.FunctionDef) or isinstance(node, ast.AsyncFunctionDef):
                    # 只分析模块级函数
                    if self._is_module_level(node, tree):
                        func_info = self._analyze_function(node, file_path)
                        if func_info:
                            functions.append(func_info)
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    import_info = self._analyze_import(node)
                    if import_info:
                        imports.extend(import_info)
            
            return ModuleInfo(
                name=module_name,
                docstring=module_docstring,
                classes=classes,
                functions=functions,
                file_path=file_path,
                imports=imports
            )
            
        except Exception as e:
            print(f"分析文件 {file_path} 时出错: {e}")
            return None
    
    def _get_module_name(self, file_path: str) -> str:
        """获取模块名"""
        path = Path(file_path)
        if path.name == '__init__.py':
            return path.parent.name
        return path.stem
    
    def _analyze_class(self, node: ast.ClassDef, file_path: str) -> Optional[ClassInfo]:
        """分析类"""
        try:
            # 获取类文档字符串
            docstring = ast.get_docstring(node) or ""
            
            # 获取继承关系
            inheritance = []
            for base in node.bases:
                if isinstance(base, ast.Name):
                    inheritance.append(base.id)
                elif isinstance(base, ast.Attribute):
                    inheritance.append(self._get_attribute_name(base))
            
            # 分析方法
            methods = []
            attributes = []
            
            for item in node.body:
                if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                    method_info = self._analyze_method(item)
                    if method_info:
                        methods.append(method_info)
                elif isinstance(item, ast.AnnAssign) and isinstance(item.target, ast.Name):
                    # 类属性
                    attr_info = {
                        'name': item.target.id,
                        'type': self._get_type_annotation(item.annotation) if item.annotation else 'Any',
                        'docstring': ''
                    }
                    attributes.append(attr_info)
            
            return ClassInfo(
                name=node.name,
                docstring=docstring,
                methods=methods,
                attributes=attributes,
                inheritance=inheritance,
                module=self._get_module_name(file_path),
                file_path=file_path,
                line_number=node.lineno
            )
            
        except Exception as e:
            print(f"分析类 {node.name} 时出错: {e}")
            return None
    
    def _analyze_function(self, node: ast.FunctionDef, file_path: str) -> Optional[FunctionInfo]:
        """分析函数"""
        try:
            # 获取函数文档字符串
            docstring = ast.get_docstring(node) or ""
            
            # 分析参数
            parameters = self._analyze_parameters(node.args)
            
            # 获取返回类型
            return_type = 'Any'
            if node.returns:
                return_type = self._get_type_annotation(node.returns)
            
            return FunctionInfo(
                name=node.name,
                docstring=docstring,
                parameters=parameters,
                return_type=return_type,
                module=self._get_module_name(file_path),
                file_path=file_path,
                line_number=node.lineno,
                is_async=isinstance(node, ast.AsyncFunctionDef)
            )
            
        except Exception as e:
            print(f"分析函数 {node.name} 时出错: {e}")
            return None
    
    def _analyze_method(self, node: ast.FunctionDef) -> Optional[Dict[str, Any]]:
        """分析方法"""
        try:
            docstring = ast.get_docstring(node) or ""
            parameters = self._analyze_parameters(node.args)
            
            return_type = 'Any'
            if node.returns:
                return_type = self._get_type_annotation(node.returns)
            
            return {
                'name': node.name,
                'docstring': docstring,
                'parameters': parameters,
                'return_type': return_type,
                'is_async': isinstance(node, ast.AsyncFunctionDef),
                'is_property': any(
                    isinstance(decorator, ast.Name) and decorator.id == 'property'
                    for decorator in node.decorator_list
                ),
                'is_classmethod': any(
                    isinstance(decorator, ast.Name) and decorator.id == 'classmethod'
                    for decorator in node.decorator_list
                ),
                'is_staticmethod': any(
                    isinstance(decorator, ast.Name) and decorator.id == 'staticmethod'
                    for decorator in node.decorator_list
                ),
                'line_number': node.lineno
            }
            
        except Exception as e:
            print(f"分析方法 {node.name} 时出错: {e}")
            return None
    
    def _analyze_parameters(self, args: ast.arguments) -> List[Dict[str, Any]]:
        """分析函数参数"""
        parameters = []
        
        # 普通参数
        for i, arg in enumerate(args.args):
            param_info = {
                'name': arg.arg,
                'type': 'Any',
                'default': None,
                'kind': 'positional'
            }
            
            # 类型注解
            if arg.annotation:
                param_info['type'] = self._get_type_annotation(arg.annotation)
            
            # 默认值
            default_offset = len(args.args) - len(args.defaults)
            if i >= default_offset:
                default_index = i - default_offset
                param_info['default'] = self._get_default_value(args.defaults[default_index])
            
            parameters.append(param_info)
        
        # *args
        if args.vararg:
            parameters.append({
                'name': args.vararg.arg,
                'type': 'Any',
                'default': None,
                'kind': 'var_positional'
            })
        
        # **kwargs
        if args.kwarg:
            parameters.append({
                'name': args.kwarg.arg,
                'type': 'Any',
                'default': None,
                'kind': 'var_keyword'
            })
        
        return parameters
    
    def _get_type_annotation(self, annotation: ast.AST) -> str:
        """获取类型注解字符串"""
        try:
            if isinstance(annotation, ast.Name):
                return annotation.id
            elif isinstance(annotation, ast.Constant):
                return repr(annotation.value)
            elif isinstance(annotation, ast.Attribute):
                return self._get_attribute_name(annotation)
            elif isinstance(annotation, ast.Subscript):
                value = self._get_type_annotation(annotation.value)
                slice_value = self._get_type_annotation(annotation.slice)
                return f"{value}[{slice_value}]"
            else:
                return ast.unparse(annotation)
        except:
            return 'Any'
    
    def _get_attribute_name(self, node: ast.Attribute) -> str:
        """获取属性名"""
        if isinstance(node.value, ast.Name):
            return f"{node.value.id}.{node.attr}"
        elif isinstance(node.value, ast.Attribute):
            return f"{self._get_attribute_name(node.value)}.{node.attr}"
        else:
            return node.attr
    
    def _get_default_value(self, node: ast.AST) -> str:
        """获取默认值"""
        try:
            if isinstance(node, ast.Constant):
                return repr(node.value)
            elif isinstance(node, ast.Name):
                return node.id
            else:
                return ast.unparse(node)
        except:
            return 'None'
    
    def _is_module_level(self, node: ast.FunctionDef, tree: ast.Module) -> bool:
        """检查是否为模块级函数"""
        for item in tree.body:
            if item == node:
                return True
        return False
    
    def _analyze_import(self, node: ast.AST) -> List[str]:
        """分析导入语句"""
        imports = []
        
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(alias.name)
        elif isinstance(node, ast.ImportFrom):
            module = node.module or ''
            for alias in node.names:
                if alias.name == '*':
                    imports.append(f"from {module} import *")
                else:
                    imports.append(f"from {module} import {alias.name}")
        
        return imports


class MarkdownGenerator:
    """Markdown文档生成器"""
    
    def __init__(self, config: DocConfig):
        self.config = config
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_documentation(self, modules: Dict[str, ModuleInfo]) -> bool:
        """生成完整文档"""
        try:
            # 生成主页
            self._generate_index(modules)
            
            # 生成API文档
            if self.config.include_api:
                self._generate_api_docs(modules)
            
            # 生成配置指南
            if self.config.include_config:
                self._generate_config_guide()
            
            # 生成教程
            if self.config.include_tutorials:
                self._generate_tutorials()
            
            # 生成示例
            if self.config.include_examples:
                self._generate_examples()
            
            print(f"文档已生成到: {self.output_dir}")
            return True
            
        except Exception as e:
            print(f"生成文档时出错: {e}")
            return False
    
    def _generate_index(self, modules: Dict[str, ModuleInfo]):
        """生成主页"""
        content = f"""# 统一验证框架文档

版本: {VERSION}  
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 概述

统一验证框架是一个高性能、可扩展的电信数据验证系统，专为Connect数据分析与可视化系统设计。

## 核心特性

- 🚀 **高性能**: 500万行数据处理 < 10秒
- 🔧 **可扩展**: 模块化设计，支持自定义验证规则
- 📊 **电信专业**: 深度集成CDR、KPI、路测等电信数据验证
- 🛡️ **企业级**: 完整的监控、日志、错误处理机制
- 🌐 **地理空间**: 优化的PostGIS空间数据验证

## 快速导航

### API文档
"""
        
        # 添加模块链接
        for module_name in sorted(modules.keys()):
            content += f"- [{module_name}](api/{module_name}.md)\n"
        
        content += """

### 指南

- [配置指南](config_guide.md)
- [快速开始](tutorials/quick_start.md)
- [最佳实践](tutorials/best_practices.md)
- [性能优化](tutorials/performance.md)

### 示例

- [基本用法](examples/basic_usage.md)
- [自定义验证](examples/custom_validation.md)
- [批量处理](examples/batch_processing.md)
- [监控集成](examples/monitoring.md)

## 架构概览

```
统一验证框架
├── 核心引擎 (core.py)
├── 验证器 (validators.py)
├── 验证规则 (rules.py)
├── 工厂模式 (factory.py)
├── 配置管理 (config.py)
├── 监控系统 (monitoring.py)
└── 工具函数 (utils.py)
```

## 支持的数据类型

| 数据类型 | 描述 | 验证规则 |
|---------|------|----------|
| CDR | 通话详单记录 | 结构、值域、电信规则 |
| KPI | 关键性能指标 | 时间序列、阈值、趋势 |
| CFG | 配置数据 | 格式、完整性、一致性 |
| 路测数据 | 网络测试数据 | 地理位置、信号强度 |
| 站点数据 | 基站信息 | 坐标、覆盖范围 |

## 许可证

MIT License

## 支持

如有问题或建议，请联系技术支持团队。
"""
        
        with open(self.output_dir / 'README.md', 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _generate_api_docs(self, modules: Dict[str, ModuleInfo]):
        """生成API文档"""
        api_dir = self.output_dir / 'api'
        api_dir.mkdir(exist_ok=True)
        
        # 生成API索引
        index_content = "# API 文档\n\n"
        
        for module_name, module_info in sorted(modules.items()):
            # 生成模块文档
            self._generate_module_doc(module_info, api_dir)
            
            # 添加到索引
            index_content += f"## [{module_name}]({module_name}.md)\n\n"
            if module_info.docstring:
                index_content += f"{module_info.docstring}\n\n"
        
        with open(api_dir / 'README.md', 'w', encoding='utf-8') as f:
            f.write(index_content)
    
    def _generate_module_doc(self, module_info: ModuleInfo, output_dir: Path):
        """生成模块文档"""
        content = f"# {module_info.name}\n\n"
        
        if module_info.docstring:
            content += f"{module_info.docstring}\n\n"
        
        # 导入信息
        if module_info.imports:
            content += "## 导入\n\n"
            for imp in module_info.imports[:10]:  # 只显示前10个
                content += f"- `{imp}`\n"
            if len(module_info.imports) > 10:
                content += f"- ... 还有 {len(module_info.imports) - 10} 个导入\n"
            content += "\n"
        
        # 类文档
        if module_info.classes:
            content += "## 类\n\n"
            for class_info in module_info.classes:
                content += self._generate_class_doc(class_info)
        
        # 函数文档
        if module_info.functions:
            content += "## 函数\n\n"
            for func_info in module_info.functions:
                content += self._generate_function_doc(func_info)
        
        with open(output_dir / f"{module_info.name}.md", 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _generate_class_doc(self, class_info: ClassInfo) -> str:
        """生成类文档"""
        content = f"### {class_info.name}\n\n"
        
        # 继承关系
        if class_info.inheritance:
            inheritance_str = ', '.join(class_info.inheritance)
            content += f"**继承**: {inheritance_str}\n\n"
        
        # 类文档字符串
        if class_info.docstring:
            content += f"{class_info.docstring}\n\n"
        
        # 属性
        if class_info.attributes:
            content += "#### 属性\n\n"
            for attr in class_info.attributes:
                content += f"- **{attr['name']}** (`{attr['type']}`): {attr['docstring']}\n"
            content += "\n"
        
        # 方法
        if class_info.methods:
            content += "#### 方法\n\n"
            for method in class_info.methods:
                content += self._generate_method_doc(method)
        
        return content
    
    def _generate_method_doc(self, method: Dict[str, Any]) -> str:
        """生成方法文档"""
        content = f"##### {method['name']}\n\n"
        
        # 方法签名
        params = []
        for param in method['parameters']:
            param_str = param['name']
            if param['type'] != 'Any':
                param_str += f": {param['type']}"
            if param['default']:
                param_str += f" = {param['default']}"
            params.append(param_str)
        
        signature = f"{method['name']}({', '.join(params)})"
        if method['return_type'] != 'Any':
            signature += f" -> {method['return_type']}"
        
        content += f"```python\n{signature}\n```\n\n"
        
        # 方法类型标识
        tags = []
        if method['is_async']:
            tags.append('async')
        if method['is_property']:
            tags.append('property')
        if method['is_classmethod']:
            tags.append('classmethod')
        if method['is_staticmethod']:
            tags.append('staticmethod')
        
        if tags:
            content += f"**标签**: {', '.join(tags)}\n\n"
        
        # 方法文档字符串
        if method['docstring']:
            content += f"{method['docstring']}\n\n"
        
        return content
    
    def _generate_function_doc(self, func_info: FunctionInfo) -> str:
        """生成函数文档"""
        content = f"### {func_info.name}\n\n"
        
        # 函数签名
        params = []
        for param in func_info.parameters:
            param_str = param['name']
            if param['type'] != 'Any':
                param_str += f": {param['type']}"
            if param['default']:
                param_str += f" = {param['default']}"
            params.append(param_str)
        
        signature = f"{func_info.name}({', '.join(params)})"
        if func_info.return_type != 'Any':
            signature += f" -> {func_info.return_type}"
        
        content += f"```python\n{signature}\n```\n\n"
        
        # 异步标识
        if func_info.is_async:
            content += "**类型**: 异步函数\n\n"
        
        # 函数文档字符串
        if func_info.docstring:
            content += f"{func_info.docstring}\n\n"
        
        return content
    
    def _generate_config_guide(self):
        """生成配置指南"""
        content = """# 配置指南

统一验证框架提供了灵活的配置系统，支持多环境配置和动态配置更新。

## 配置文件结构

```json
{
  "environment": "production",
  "version": "1.0.0",
  "validation": {
    "mode": "strict",
    "parallel_enabled": true,
    "max_workers": 4,
    "timeout_seconds": 300,
    "batch_size": 10000
  },
  "performance": {
    "enable_caching": true,
    "cache_size": 1000,
    "enable_monitoring": true,
    "memory_limit_mb": 1024
  },
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_enabled": true,
    "console_enabled": true
  },
  "database": {
    "url": "postgresql://user:password@localhost:5432/connect",
    "pool_size": 5,
    "max_overflow": 10,
    "echo": false
  },
  "redis": {
    "url": "redis://localhost:6379/0",
    "max_connections": 10,
    "socket_timeout": 30
  },
  "telecom": {
    "country_code": "86",
    "timezone": "Asia/Shanghai",
    "frequency_bands": ["900", "1800", "2100", "2600"]
  }
}
```

## 配置项说明

### 验证配置 (validation)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| mode | string | "strict" | 验证模式: strict/lenient |
| parallel_enabled | boolean | true | 是否启用并行验证 |
| max_workers | integer | 4 | 最大工作线程数 |
| timeout_seconds | integer | 300 | 验证超时时间(秒) |
| batch_size | integer | 10000 | 批处理大小 |

### 性能配置 (performance)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| enable_caching | boolean | true | 是否启用缓存 |
| cache_size | integer | 1000 | 缓存大小 |
| enable_monitoring | boolean | true | 是否启用监控 |
| memory_limit_mb | integer | 1024 | 内存限制(MB) |

### 日志配置 (logging)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| level | string | "INFO" | 日志级别 |
| format | string | - | 日志格式 |
| file_enabled | boolean | true | 是否启用文件日志 |
| console_enabled | boolean | true | 是否启用控制台日志 |

### 数据库配置 (database)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| url | string | - | 数据库连接URL |
| pool_size | integer | 5 | 连接池大小 |
| max_overflow | integer | 10 | 最大溢出连接数 |
| echo | boolean | false | 是否打印SQL语句 |

### Redis配置 (redis)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| url | string | - | Redis连接URL |
| max_connections | integer | 10 | 最大连接数 |
| socket_timeout | integer | 30 | 套接字超时时间(秒) |

### 电信配置 (telecom)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| country_code | string | "86" | 国家代码 |
| timezone | string | "Asia/Shanghai" | 时区 |
| frequency_bands | array | - | 支持的频段 |

## 环境配置

框架支持多环境配置，通过环境变量 `VALIDATION_ENV` 指定：

- `development`: 开发环境
- `testing`: 测试环境
- `production`: 生产环境

## 配置加载优先级

1. 环境变量
2. 命令行参数
3. 环境特定配置文件 (config_production.json)
4. 主配置文件 (config.json)
5. 默认配置

## 动态配置更新

```python
from validation.config import ConfigManager

# 获取配置管理器
config_manager = ConfigManager()

# 更新配置
config_manager.update_config({
    "validation": {
        "max_workers": 8
    }
})

# 保存配置
config_manager.save_config("config_updated.json")
```

## 最佳实践

1. **生产环境**: 使用严格模式，启用监控和缓存
2. **开发环境**: 使用宽松模式，启用详细日志
3. **测试环境**: 使用内存数据库，禁用缓存
4. **性能调优**: 根据硬件配置调整工作线程数和内存限制
5. **安全配置**: 不要在配置文件中硬编码敏感信息
"""
        
        with open(self.output_dir / 'config_guide.md', 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _generate_tutorials(self):
        """生成教程"""
        tutorials_dir = self.output_dir / 'tutorials'
        tutorials_dir.mkdir(exist_ok=True)
        
        # 快速开始教程
        quick_start = """# 快速开始

本教程将帮助您快速上手统一验证框架。

## 安装

### 自动安装

```bash
python install.py --environment production
```

### 手动安装

1. 安装依赖:
```bash
pip install -r requirements.txt
```

2. 配置环境:
```bash
export VALIDATION_CONFIG_PATH=/path/to/config.json
```

## 基本用法

### 1. 创建验证框架

```python
from validation.factory import ValidationFactory

# 创建工厂
factory = ValidationFactory()

# 创建CDR验证框架
cdr_framework = factory.create_framework('cdr')
```

### 2. 准备数据

```python
import pandas as pd
from validation.utils import generate_sample_data

# 生成示例数据
data = generate_sample_data('cdr', 1000)

# 或加载真实数据
# data = pd.read_csv('cdr_data.csv')
```

### 3. 执行验证

```python
# 执行验证
result = cdr_framework.validate(data)

# 检查结果
print(f"验证通过: {result.is_valid}")
print(f"问题数量: {len(result.issues)}")

# 查看问题详情
for issue in result.issues[:5]:  # 显示前5个问题
    print(f"- {issue.message} (行: {issue.row_index})")
```

### 4. 生成报告

```python
from validation.utils import ReportGenerator

# 创建报告生成器
report_gen = ReportGenerator()

# 生成HTML报告
report_gen.generate_html_report(
    result, 
    output_path='validation_report.html'
)
```

## 高级用法

### 自定义验证规则

```python
from validation.rules import ValidationRule
from validation.core import ValidationIssue

class CustomRule(ValidationRule):
    def validate(self, data, context=None):
        issues = []
        
        # 自定义验证逻辑
        for idx, row in data.iterrows():
            if row['custom_field'] < 0:
                issues.append(ValidationIssue(
                    rule_name=self.name,
                    message="自定义字段不能为负数",
                    severity="error",
                    row_index=idx,
                    column="custom_field",
                    value=row['custom_field']
                ))
        
        return issues

# 注册自定义规则
factory.register_rule('custom', CustomRule('custom_rule'))
```

### 批量处理

```python
from validation.utils import BatchProcessor

# 创建批处理器
processor = BatchProcessor(batch_size=10000)

# 批量验证大文件
results = processor.process_file(
    'large_data.csv',
    cdr_framework.validate
)

# 合并结果
final_result = processor.merge_results(results)
```

### 并行验证

```python
# 启用并行验证
factory.config.parallel_enabled = True
factory.config.max_workers = 4

# 并行验证多个数据集
datasets = [data1, data2, data3, data4]
results = factory.validate_parallel(datasets, 'cdr')
```

## 性能优化

### 1. 启用缓存

```python
factory.config.enable_caching = True
factory.config.cache_size = 2000
```

### 2. 调整批处理大小

```python
factory.config.batch_size = 50000  # 根据内存调整
```

### 3. 优化内存使用

```python
factory.config.memory_limit_mb = 2048
```

## 监控和调试

### 启用监控

```python
from validation.monitoring import ValidationMonitor

# 创建监控器
monitor = ValidationMonitor()

# 开始监控
monitor.start_monitoring()

# 执行验证
result = cdr_framework.validate(data)

# 获取性能指标
metrics = monitor.get_metrics()
print(f"验证耗时: {metrics.execution_time}秒")
print(f"内存使用: {metrics.memory_usage}MB")
```

### 调试模式

```python
import logging

# 启用调试日志
logging.getLogger('validation').setLevel(logging.DEBUG)

# 详细错误信息
factory.config.debug_mode = True
```

## 下一步

- 阅读 [最佳实践](best_practices.md)
- 学习 [性能优化](performance.md)
- 查看 [API文档](../api/README.md)
- 浏览 [示例代码](../examples/)
"""
        
        with open(tutorials_dir / 'quick_start.md', 'w', encoding='utf-8') as f:
            f.write(quick_start)
    
    def _generate_examples(self):
        """生成示例"""
        examples_dir = self.output_dir / 'examples'
        examples_dir.mkdir(exist_ok=True)
        
        # 基本用法示例
        basic_usage = """# 基本用法示例

本文档展示了统一验证框架的基本用法示例。

## 示例1: CDR数据验证

```python
from validation.factory import ValidationFactory
from validation.utils import generate_sample_data
import pandas as pd

# 创建验证工厂
factory = ValidationFactory()

# 生成CDR测试数据
cdr_data = generate_sample_data('cdr', 1000)
print(f"生成了 {len(cdr_data)} 条CDR记录")

# 创建CDR验证框架
cdr_framework = factory.create_framework('cdr')

# 执行验证
result = cdr_framework.validate(cdr_data)

# 输出结果
print(f"验证结果: {'通过' if result.is_valid else '失败'}")
print(f"发现问题: {len(result.issues)} 个")
print(f"验证耗时: {result.execution_time:.2f} 秒")

# 显示问题详情
if result.issues:
    print("\n问题详情:")
    for i, issue in enumerate(result.issues[:5]):
        print(f"{i+1}. {issue.message}")
        print(f"   位置: 第{issue.row_index}行, {issue.column}列")
        print(f"   值: {issue.value}")
        print(f"   严重性: {issue.severity}")
```

## 示例2: KPI数据验证

```python
# 生成KPI测试数据
kpi_data = generate_sample_data('kpi', 500)

# 创建KPI验证框架
kpi_framework = factory.create_framework('kpi')

# 执行验证
result = kpi_framework.validate(kpi_data)

# 生成验证报告
from validation.utils import ReportGenerator

report_gen = ReportGenerator()
report_gen.generate_html_report(
    result,
    output_path='kpi_validation_report.html',
    title='KPI数据验证报告'
)

print("验证报告已生成: kpi_validation_report.html")
```

## 示例3: 文件验证

```python
# 验证CSV文件
file_result = factory.validate_file(
    'data/cdr_data.csv',
    data_type='cdr'
)

print(f"文件验证结果: {'通过' if file_result.is_valid else '失败'}")

# 批量验证多个文件
file_paths = [
    'data/cdr_2023_01.csv',
    'data/cdr_2023_02.csv',
    'data/cdr_2023_03.csv'
]

batch_results = []
for file_path in file_paths:
    result = factory.validate_file(file_path, 'cdr')
    batch_results.append({
        'file': file_path,
        'valid': result.is_valid,
        'issues': len(result.issues)
    })

# 输出批量验证结果
print("\n批量验证结果:")
for result in batch_results:
    status = "✅" if result['valid'] else "❌"
    print(f"{status} {result['file']}: {result['issues']} 个问题")
```

## 示例4: 性能监控

```python
from validation.monitoring import ValidationMonitor
import time

# 创建监控器
monitor = ValidationMonitor()

# 开始监控
monitor.start_monitoring()

# 执行大数据量验证
large_data = generate_sample_data('cdr', 100000)
start_time = time.time()

result = cdr_framework.validate(large_data)

end_time = time.time()

# 获取性能指标
metrics = monitor.get_metrics()

print(f"\n性能指标:")
print(f"数据量: {len(large_data):,} 行")
print(f"验证耗时: {end_time - start_time:.2f} 秒")
print(f"吞吐量: {len(large_data) / (end_time - start_time):,.0f} 行/秒")
print(f"内存使用: {metrics.memory_usage:.1f} MB")
print(f"CPU使用: {metrics.cpu_usage:.1f}%")

# 停止监控
monitor.stop_monitoring()
```

## 示例5: 自定义配置

```python
from validation.config import ValidationConfig, ConfigManager

# 创建自定义配置
custom_config = ValidationConfig(
    mode='lenient',  # 宽松模式
    parallel_enabled=True,
    max_workers=8,
    timeout_seconds=600,
    batch_size=20000
)

# 使用自定义配置创建工厂
factory = ValidationFactory(config=custom_config)

# 或者动态更新配置
config_manager = ConfigManager()
config_manager.update_config({
    "validation": {
        "mode": "strict",
        "max_workers": 4
    },
    "performance": {
        "enable_caching": True,
        "cache_size": 2000
    }
})

# 重新加载配置
factory.reload_config()
```

## 示例6: 错误处理

```python
from validation.exceptions import (
    ValidationError,
    ConfigurationError,
    DataFormatError
)

try:
    # 尝试验证无效数据
    invalid_data = pd.DataFrame({'invalid_column': [1, 2, 3]})
    result = cdr_framework.validate(invalid_data)
    
except DataFormatError as e:
    print(f"数据格式错误: {e}")
    print("请检查数据结构是否符合CDR格式要求")
    
except ValidationError as e:
    print(f"验证错误: {e}")
    print("请检查验证规则配置")
    
except Exception as e:
    print(f"未知错误: {e}")
    print("请联系技术支持")
```

## 运行示例

将以上代码保存为Python文件并运行:

```bash
python basic_usage_example.py
```

确保已正确安装和配置统一验证框架。
"""
        
        with open(examples_dir / 'basic_usage.md', 'w', encoding='utf-8') as f:
            f.write(basic_usage)


class DocumentationGenerator:
    """文档生成器主类"""
    
    def __init__(self, config: DocConfig):
        self.config = config
        self.analyzer = CodeAnalyzer()
        self.markdown_generator = MarkdownGenerator(config)
    
    def generate(self) -> bool:
        """生成完整文档"""
        try:
            print(f"🚀 开始生成文档...")
            print(f"源码目录: {self.config.source_dir}")
            print(f"输出目录: {self.config.output_dir}")
            
            # 分析源码
            print("📊 分析源码...")
            modules = self.analyzer.analyze_directory(
                self.config.source_dir,
                self.config.exclude_patterns
            )
            
            print(f"发现 {len(modules)} 个模块")
            for module_name in modules.keys():
                print(f"  - {module_name}")
            
            # 生成文档
            print("📝 生成文档...")
            success = self.markdown_generator.generate_documentation(modules)
            
            if success:
                print(f"✅ 文档生成完成: {self.config.output_dir}")
                return True
            else:
                print("❌ 文档生成失败")
                return False
                
        except Exception as e:
            print(f"生成文档时出错: {e}")
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='统一验证框架文档生成器')
    parser.add_argument('--source-dir', default='.',
                       help='源码目录')
    parser.add_argument('--output-dir', default='docs',
                       help='输出目录')
    parser.add_argument('--format', choices=['markdown', 'html'],
                       default='markdown', help='输出格式')
    parser.add_argument('--exclude', nargs='*',
                       default=['__pycache__', '*.pyc', 'tests', '.git'],
                       help='排除模式')
    parser.add_argument('--no-api', action='store_true',
                       help='不生成API文档')
    parser.add_argument('--no-examples', action='store_true',
                       help='不生成示例')
    parser.add_argument('--no-config', action='store_true',
                       help='不生成配置指南')
    parser.add_argument('--no-tutorials', action='store_true',
                       help='不生成教程')
    
    args = parser.parse_args()
    
    # 创建配置
    config = DocConfig(
        source_dir=args.source_dir,
        output_dir=args.output_dir,
        format=args.format,
        exclude_patterns=args.exclude,
        include_api=not args.no_api,
        include_examples=not args.no_examples,
        include_config=not args.no_config,
        include_tutorials=not args.no_tutorials
    )
    
    # 生成文档
    generator = DocumentationGenerator(config)
    success = generator.generate()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()