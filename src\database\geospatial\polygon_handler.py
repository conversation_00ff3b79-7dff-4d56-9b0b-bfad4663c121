__author__ = "Vincent<PERSON>Li"
__email__ = "<EMAIL>"

"""Polygon handler for MapInfo .TAB files and polygon operations.

This module provides specialized functionality for handling polygon data,
particularly MapInfo .TAB files commonly used in telecommunications.
"""

import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import asyncpg
import geopandas as gpd
import pandas as pd
from loguru import logger
from shapely.geometry import MultiPolygon, Point, Polygon

# Handle relative imports with fallback
try:
    from ..config import settings
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    # Add parent directories to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(current_dir))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from config import settings
from ..exceptions import GeospatialError
from ..utils.performance import measure_performance
from ..utils.validators import validate_coordinates
from .processor import GeospatialProcessor


class PolygonHandler:
    """Specialized handler for polygon data operations.

    This class focuses on:
    - MapInfo .TAB file processing
    - Polygon validation and cleaning
    - Vendor region mapping
    - Performance optimization for large polygon datasets
    """

    def __init__(self, processor: Optional[GeospatialProcessor] = None):
        """Initialize the PolygonHandler.

        Args:
            processor: Optional GeospatialProcessor instance
        """
        self.processor = processor or GeospatialProcessor()
        self.vendor_regions = {}
        self.polygon_cache = {}

    async def load_vendor_polygons(
        self, polygon_dir: Union[str, Path]
    ) -> Dict[str, gpd.GeoDataFrame]:
        """Load all vendor polygon files from directory.

        Args:
            polygon_dir: Directory containing polygon files

        Returns:
            Dictionary mapping vendor names to GeoDataFrames
        """
        try:
            polygon_dir = Path(polygon_dir)
            if not polygon_dir.exists():
                raise GeospatialError(f"Polygon directory not found: {polygon_dir}")

            vendor_data = {}
            tab_files = list(polygon_dir.glob("*.TAB"))

            logger.info(f"Found {len(tab_files)} TAB files in {polygon_dir}")

            for tab_file in tab_files:
                try:
                    vendor_name = self._extract_vendor_name(tab_file.stem)
                    logger.info(f"Loading polygon data for vendor: {vendor_name}")

                    gdf = await self.processor.load_spatial_data(
                        tab_file, encoding="utf-8"
                    )

                    # Add vendor information
                    gdf["vendor"] = vendor_name
                    gdf["source_file"] = tab_file.name

                    # Process and validate polygons
                    gdf = await self._process_vendor_polygons(gdf, vendor_name)

                    vendor_data[vendor_name] = gdf
                    logger.info(f"Loaded {len(gdf)} polygons for {vendor_name}")

                except Exception as e:
                    logger.error(f"Failed to load {tab_file}: {e}")
                    continue

            self.vendor_regions = vendor_data
            logger.info(
                f"Successfully loaded polygon data for {len(vendor_data)} vendors"
            )
            return vendor_data

        except Exception as e:
            logger.error(f"Failed to load vendor polygons: {e}")
            raise GeospatialError(f"Failed to load vendor polygons: {e}")

    def _extract_vendor_name(self, filename: str) -> str:
        """Extract vendor name from filename.

        Args:
            filename: TAB filename without extension

        Returns:
            Extracted vendor name
        """
        # Handle specific naming patterns
        if "TO2" in filename:
            return "TO2"
        elif "TDG" in filename:
            return "TDG"
        elif "VDF" in filename:
            return "VDF"
        else:
            # Extract first part before space or underscore
            parts = filename.replace("_", " ").split(" ")
            return parts[0] if parts else filename

    @measure_performance
    async def _process_vendor_polygons(
        self, gdf: gpd.GeoDataFrame, vendor_name: str
    ) -> gpd.GeoDataFrame:
        """Process and validate vendor polygon data.

        Args:
            gdf: Input GeoDataFrame
            vendor_name: Vendor name

        Returns:
            Processed GeoDataFrame
        """
        try:
            processed_gdf = gdf.copy()

            # Validate and fix geometries
            validation_results = await self.processor.validate_geometries(processed_gdf)

            if validation_results["invalid_geometries"] > 0:
                logger.warning(
                    f"Fixing {validation_results['invalid_geometries']} invalid geometries for {vendor_name}"
                )
                processed_gdf["geometry"] = processed_gdf.geometry.buffer(0)

            # Remove empty or null geometries
            before_count = len(processed_gdf)
            processed_gdf = processed_gdf[~processed_gdf.geometry.is_empty]
            processed_gdf = processed_gdf[~processed_gdf.geometry.isnull()]
            after_count = len(processed_gdf)

            if before_count != after_count:
                logger.info(
                    f"Removed {before_count - after_count} empty/null geometries for {vendor_name}"
                )

            # Add polygon metadata
            processed_gdf["polygon_id"] = range(1, len(processed_gdf) + 1)
            processed_gdf["area_km2"] = processed_gdf.geometry.area / 1000000
            processed_gdf["perimeter_km"] = processed_gdf.geometry.length / 1000
            processed_gdf["centroid_lon"] = processed_gdf.geometry.centroid.x
            processed_gdf["centroid_lat"] = processed_gdf.geometry.centroid.y

            # Add bounding box information
            bounds = processed_gdf.geometry.bounds
            processed_gdf["bbox_minx"] = bounds["minx"]
            processed_gdf["bbox_miny"] = bounds["miny"]
            processed_gdf["bbox_maxx"] = bounds["maxx"]
            processed_gdf["bbox_maxy"] = bounds["maxy"]

            logger.info(f"Processed {len(processed_gdf)} polygons for {vendor_name}")
            return processed_gdf

        except Exception as e:
            logger.error(f"Failed to process vendor polygons for {vendor_name}: {e}")
            raise GeospatialError(f"Failed to process vendor polygons: {e}")

    async def find_containing_polygons(
        self,
        points: List[Tuple[float, float]],
        vendor_filter: Optional[List[str]] = None,
    ) -> pd.DataFrame:
        """Find which polygons contain the given points.

        Args:
            points: List of (longitude, latitude) tuples
            vendor_filter: Optional list of vendor names to filter

        Returns:
            DataFrame with point-polygon relationships
        """
        try:
            if not self.vendor_regions:
                raise GeospatialError("No vendor polygon data loaded")

            results = []
            vendors_to_check = vendor_filter or list(self.vendor_regions.keys())

            for vendor in vendors_to_check:
                if vendor not in self.vendor_regions:
                    logger.warning(f"Vendor {vendor} not found in loaded data")
                    continue

                vendor_gdf = self.vendor_regions[vendor]

                # Perform point-in-polygon query
                point_results = await self.processor.point_in_polygon_query(
                    points, vendor_gdf
                )

                # Add vendor information to results
                point_results["vendor"] = vendor
                results.append(point_results)

            if results:
                combined_results = pd.concat(results, ignore_index=True)
                logger.info(
                    f"Found polygon matches for {len(combined_results)} point-vendor combinations"
                )
                return combined_results
            else:
                logger.warning("No polygon matches found")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"Failed to find containing polygons: {e}")
            raise GeospatialError(f"Failed to find containing polygons: {e}")

    async def get_polygon_statistics(
        self, vendor_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get statistics about loaded polygon data.

        Args:
            vendor_name: Optional vendor name to get specific statistics

        Returns:
            Dictionary with polygon statistics
        """
        try:
            if not self.vendor_regions:
                return {"error": "No polygon data loaded"}

            if vendor_name:
                if vendor_name not in self.vendor_regions:
                    return {"error": f"Vendor {vendor_name} not found"}

                gdf = self.vendor_regions[vendor_name]
                stats = {
                    "vendor": vendor_name,
                    "polygon_count": len(gdf),
                    "total_area_km2": gdf["area_km2"].sum(),
                    "avg_area_km2": gdf["area_km2"].mean(),
                    "min_area_km2": gdf["area_km2"].min(),
                    "max_area_km2": gdf["area_km2"].max(),
                    "bounding_box": await self.processor.get_bounding_box(gdf),
                }
            else:
                # Overall statistics
                total_polygons = sum(len(gdf) for gdf in self.vendor_regions.values())
                total_area = sum(
                    gdf["area_km2"].sum() for gdf in self.vendor_regions.values()
                )

                stats = {
                    "vendors": list(self.vendor_regions.keys()),
                    "total_vendors": len(self.vendor_regions),
                    "total_polygons": total_polygons,
                    "total_area_km2": total_area,
                    "vendor_details": {},
                }

                for vendor, gdf in self.vendor_regions.items():
                    stats["vendor_details"][vendor] = {
                        "polygon_count": len(gdf),
                        "area_km2": gdf["area_km2"].sum(),
                    }

            logger.info(f"Generated polygon statistics: {stats}")
            return stats

        except Exception as e:
            logger.error(f"Failed to get polygon statistics: {e}")
            raise GeospatialError(f"Failed to get polygon statistics: {e}")

    async def save_vendor_polygons_to_db(
        self,
        schema: str = "geospatial",
        table_prefix: str = "vendor_polygons",
        if_exists: str = "replace",
    ) -> None:
        """Save all vendor polygon data to database.

        Args:
            schema: Target schema name
            table_prefix: Prefix for table names
            if_exists: How to behave if table exists
        """
        try:
            if not self.vendor_regions:
                raise GeospatialError("No vendor polygon data to save")

            for vendor, gdf in self.vendor_regions.items():
                table_name = f"{table_prefix}_{vendor.lower()}"

                logger.info(
                    f"Saving {len(gdf)} polygons for {vendor} to {schema}.{table_name}"
                )

                await self.processor.save_to_postgis(
                    gdf=gdf, table_name=table_name, schema=schema, if_exists=if_exists
                )

            logger.info(
                f"Successfully saved polygon data for {len(self.vendor_regions)} vendors"
            )

        except Exception as e:
            logger.error(f"Failed to save vendor polygons to database: {e}")
            raise GeospatialError(f"Failed to save vendor polygons to database: {e}")

    async def create_unified_polygon_table(
        self,
        schema: str = "geospatial",
        table_name: str = "all_vendor_polygons",
        if_exists: str = "replace",
    ) -> None:
        """Create a unified table with all vendor polygons.

        Args:
            schema: Target schema name
            table_name: Target table name
            if_exists: How to behave if table exists
        """
        try:
            if not self.vendor_regions:
                raise GeospatialError("No vendor polygon data to unify")

            # Combine all vendor data
            all_polygons = []
            for vendor, gdf in self.vendor_regions.items():
                vendor_gdf = gdf.copy()
                vendor_gdf["vendor_name"] = vendor
                all_polygons.append(vendor_gdf)

            unified_gdf = pd.concat(all_polygons, ignore_index=True)

            # Add global polygon ID
            unified_gdf["global_polygon_id"] = range(1, len(unified_gdf) + 1)

            logger.info(
                f"Creating unified table with {len(unified_gdf)} polygons from {len(self.vendor_regions)} vendors"
            )

            await self.processor.save_to_postgis(
                gdf=unified_gdf,
                table_name=table_name,
                schema=schema,
                if_exists=if_exists,
            )

            logger.info(
                f"Successfully created unified polygon table {schema}.{table_name}"
            )

        except Exception as e:
            logger.error(f"Failed to create unified polygon table: {e}")
            raise GeospatialError(f"Failed to create unified polygon table: {e}")

    async def optimize_polygons(
        self,
        simplify_tolerance: float = 0.001,
        remove_holes_smaller_than: Optional[float] = None,
    ) -> None:
        """Optimize polygon geometries for better performance.

        Args:
            simplify_tolerance: Tolerance for geometry simplification
            remove_holes_smaller_than: Remove holes smaller than this area (km²)
        """
        try:
            if not self.vendor_regions:
                raise GeospatialError("No vendor polygon data to optimize")

            for vendor, gdf in self.vendor_regions.items():
                logger.info(f"Optimizing polygons for {vendor}")

                original_count = len(gdf)

                # Simplify geometries
                gdf["geometry"] = gdf.geometry.simplify(
                    simplify_tolerance, preserve_topology=True
                )

                # Remove small holes if specified
                if remove_holes_smaller_than:

                    def remove_small_holes(geom):
                        if isinstance(geom, Polygon) and geom.interiors:
                            # Keep only holes larger than threshold
                            large_holes = [
                                hole
                                for hole in geom.interiors
                                if Polygon(hole).area / 1000000
                                >= remove_holes_smaller_than
                            ]
                            return Polygon(geom.exterior, large_holes)
                        return geom

                    gdf["geometry"] = gdf.geometry.apply(remove_small_holes)

                # Recalculate area after optimization
                gdf["area_km2"] = gdf.geometry.area / 1000000

                self.vendor_regions[vendor] = gdf

                logger.info(f"Optimized {original_count} polygons for {vendor}")

            logger.info("Polygon optimization completed for all vendors")

        except Exception as e:
            logger.error(f"Failed to optimize polygons: {e}")
            raise GeospatialError(f"Failed to optimize polygons: {e}")
