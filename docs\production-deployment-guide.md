# Connect 生产环境部署指南

## 概述

本指南提供了Connect电信数据导入系统在生产环境中的完整部署方案，包括系统要求、配置管理、监控设置和安全考虑。

## 系统要求

### 硬件要求

#### 最低配置
- **CPU**: 4核心 2.4GHz
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **网络**: 1Gbps

#### 推荐配置
- **CPU**: 8核心 3.0GHz+
- **内存**: 16GB+ RAM
- **存储**: 500GB+ NVMe SSD
- **网络**: 10Gbps

#### 大规模部署
- **CPU**: 16核心 3.2GHz+
- **内存**: 32GB+ RAM
- **存储**: 1TB+ NVMe SSD (数据) + 200GB SSD (系统)
- **网络**: 10Gbps+ 冗余连接

### 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Python**: 3.8+
- **PostgreSQL**: 13+
- **Redis**: 6.0+ (可选，用于缓存)
- **Nginx**: 1.18+ (反向代理)

## 部署架构

### 单机部署
```
┌─────────────────────────────────────┐
│           生产服务器                │
├─────────────────────────────────────┤
│  Nginx (反向代理)                   │
│  ├── Connect API (FastAPI)          │
│  ├── Connect CLI                    │
│  └── 静态文件服务                   │
├─────────────────────────────────────┤
│  PostgreSQL 数据库                  │
│  ├── ep_to2, cdr_to2, nlg_to2      │
│  ├── kpi_to2, score_to2, cfg_to2   │
│  └── 监控和日志表                   │
├─────────────────────────────────────┤
│  文件存储                           │
│  ├── 数据输入目录                   │
│  ├── 处理日志                       │
│  └── 备份文件                       │
└─────────────────────────────────────┘
```

### 高可用部署
```
┌─────────────────┐    ┌─────────────────┐
│   负载均衡器    │    │   负载均衡器    │
│   (Nginx/HAProxy)│    │   (Nginx/HAProxy)│
└─────────┬───────┘    └─────────┬───────┘
          │                      │
    ┌─────┴──────┐         ┌─────┴──────┐
    │ Connect    │         │ Connect    │
    │ 应用服务器1 │         │ 应用服务器2 │
    └─────┬──────┘         └─────┬──────┘
          │                      │
    ┌─────┴──────────────────────┴─────┐
    │      PostgreSQL 集群             │
    │   (主从复制 + 读写分离)          │
    └──────────────────────────────────┘
```

## 环境配置

### 1. 系统环境变量

创建 `/etc/environment` 或 `.env` 文件：

```bash
# 数据库配置
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=connect
DATABASE_USER=to2
DATABASE_PASSWORD=TO2

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# 安全配置
JWT_SECRET_KEY=your-super-secret-jwt-key-here
ALLOWED_HOSTS=your-domain.com,localhost

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/var/log/connect/app.log

# 文件存储
DATA_INPUT_DIR=/data/connect/input
DATA_BACKUP_DIR=/data/connect/backup
TEMP_DIR=/tmp/connect

# 性能配置
MAX_WORKERS=8
BATCH_SIZE=5000
MEMORY_LIMIT=2048
```

### 2. 数据库配置

#### PostgreSQL 优化配置 (`postgresql.conf`)

```sql
# 内存配置
shared_buffers = 2GB                    # 25% of RAM
effective_cache_size = 6GB              # 75% of RAM
work_mem = 64MB                         # Per connection
maintenance_work_mem = 512MB

# 连接配置
max_connections = 200
max_prepared_transactions = 200

# 写入优化
wal_buffers = 64MB
checkpoint_completion_target = 0.9
checkpoint_timeout = 15min

# 查询优化
random_page_cost = 1.1                  # For SSD
effective_io_concurrency = 200          # For SSD

# 日志配置
log_destination = 'csvlog'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d.log'
log_statement = 'mod'                   # Log modifications
log_min_duration_statement = 1000       # Log slow queries
```

#### 数据库用户和权限

```sql
-- 创建生产用户
CREATE USER connect_prod WITH PASSWORD 'secure_password_here';

-- 创建数据库
CREATE DATABASE connect_prod OWNER connect_prod;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE connect_prod TO connect_prod;

-- 创建模式
\c connect_prod
CREATE SCHEMA IF NOT EXISTS ep_to2 AUTHORIZATION connect_prod;
CREATE SCHEMA IF NOT EXISTS cdr_to2 AUTHORIZATION connect_prod;
CREATE SCHEMA IF NOT EXISTS cdr_vdf AUTHORIZATION connect_prod;
CREATE SCHEMA IF NOT EXISTS cdr_tdg AUTHORIZATION connect_prod;
CREATE SCHEMA IF NOT EXISTS nlg_to2 AUTHORIZATION connect_prod;
CREATE SCHEMA IF NOT EXISTS kpi_to2 AUTHORIZATION connect_prod;
CREATE SCHEMA IF NOT EXISTS score_to2 AUTHORIZATION connect_prod;
CREATE SCHEMA IF NOT EXISTS cfg_to2 AUTHORIZATION connect_prod;
```

### 3. Nginx 配置

创建 `/etc/nginx/sites-available/connect`:

```nginx
upstream connect_api {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;  # 如果有多个实例
    keepalive 32;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/ssl/certs/connect.crt;
    ssl_certificate_key /etc/ssl/private/connect.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # 文件上传限制
    client_max_body_size 500M;
    client_body_timeout 300s;
    
    # API代理
    location /api/ {
        proxy_pass http://connect_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 静态文件
    location /static/ {
        alias /opt/connect/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 健康检查
    location /health {
        proxy_pass http://connect_api/api/health;
        access_log off;
    }
}
```

## 服务管理

### 1. Systemd 服务配置

创建 `/etc/systemd/system/connect-api.service`:

```ini
[Unit]
Description=Connect Telecommunications Data Import API
After=network.target postgresql.service
Requires=postgresql.service

[Service]
Type=exec
User=connect
Group=connect
WorkingDirectory=/opt/connect
Environment=PATH=/opt/connect/.venv/bin
ExecStart=/opt/connect/.venv/bin/python -m src.api.import_api
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=connect-api

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/connect /var/log/connect /data/connect

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

### 2. 服务管理命令

```bash
# 启用服务
sudo systemctl enable connect-api

# 启动服务
sudo systemctl start connect-api

# 查看状态
sudo systemctl status connect-api

# 查看日志
sudo journalctl -u connect-api -f

# 重启服务
sudo systemctl restart connect-api

# 停止服务
sudo systemctl stop connect-api
```

## 监控和日志

### 1. 日志配置

#### 应用日志配置 (`config/logging.yaml`)

```yaml
version: 1
disable_existing_loggers: false

formatters:
  standard:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s'
  json:
    format: '{"timestamp": "%(asctime)s", "logger": "%(name)s", "level": "%(levelname)s", "message": "%(message)s", "module": "%(module)s", "function": "%(funcName)s"}'

handlers:
  console:
    class: logging.StreamHandler
    level: INFO
    formatter: standard
    stream: ext://sys.stdout
    
  file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: detailed
    filename: /var/log/connect/app.log
    maxBytes: 100MB
    backupCount: 10
    
  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: json
    filename: /var/log/connect/error.log
    maxBytes: 50MB
    backupCount: 5
    
  syslog:
    class: logging.handlers.SysLogHandler
    level: WARNING
    formatter: standard
    address: /dev/log

loggers:
  src:
    level: INFO
    handlers: [console, file, error_file]
    propagate: false
    
  uvicorn:
    level: INFO
    handlers: [console, file]
    propagate: false
    
  asyncpg:
    level: WARNING
    handlers: [file]
    propagate: false

root:
  level: INFO
  handlers: [console, file, syslog]
```

### 2. 监控脚本

创建 `/opt/connect/scripts/health_check.sh`:

```bash
#!/bin/bash

# Connect 健康检查脚本
LOG_FILE="/var/log/connect/health_check.log"
API_URL="http://localhost:8000/api/health"
DB_CHECK_SQL="SELECT 1"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# API健康检查
check_api() {
    if curl -s -f "$API_URL" > /dev/null; then
        log "API: OK"
        return 0
    else
        log "API: FAILED"
        return 1
    fi
}

# 数据库健康检查
check_database() {
    if psql -h localhost -U to2 -d connect -c "$DB_CHECK_SQL" > /dev/null 2>&1; then
        log "Database: OK"
        return 0
    else
        log "Database: FAILED"
        return 1
    fi
}

# 磁盘空间检查
check_disk_space() {
    USAGE=$(df /data/connect | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$USAGE" -lt 90 ]; then
        log "Disk Space: OK ($USAGE%)"
        return 0
    else
        log "Disk Space: WARNING ($USAGE%)"
        return 1
    fi
}

# 内存使用检查
check_memory() {
    USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$USAGE" -lt 90 ]; then
        log "Memory: OK ($USAGE%)"
        return 0
    else
        log "Memory: WARNING ($USAGE%)"
        return 1
    fi
}

# 主检查函数
main() {
    log "Starting health check"
    
    FAILED=0
    
    check_api || FAILED=$((FAILED + 1))
    check_database || FAILED=$((FAILED + 1))
    check_disk_space || FAILED=$((FAILED + 1))
    check_memory || FAILED=$((FAILED + 1))
    
    if [ $FAILED -eq 0 ]; then
        log "Health check: ALL OK"
        exit 0
    else
        log "Health check: $FAILED checks failed"
        exit 1
    fi
}

main "$@"
```

### 3. Cron 任务

添加到 `/etc/crontab`:

```bash
# Connect 监控任务
*/5 * * * * connect /opt/connect/scripts/health_check.sh
0 2 * * * connect /opt/connect/scripts/backup_database.sh
0 3 * * 0 connect /opt/connect/scripts/cleanup_logs.sh
```

## 安全配置

### 1. 防火墙设置

```bash
# UFW配置
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow from 10.0.0.0/8 to any port 5432  # 内网数据库访问
sudo ufw enable
```

### 2. SSL/TLS证书

```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 数据库安全

```sql
-- 限制连接
ALTER SYSTEM SET listen_addresses = 'localhost,**********';

-- 启用SSL
ALTER SYSTEM SET ssl = on;
ALTER SYSTEM SET ssl_cert_file = '/etc/ssl/certs/server.crt';
ALTER SYSTEM SET ssl_key_file = '/etc/ssl/private/server.key';

-- 重载配置
SELECT pg_reload_conf();
```

## 备份策略

### 1. 数据库备份

创建 `/opt/connect/scripts/backup_database.sh`:

```bash
#!/bin/bash

BACKUP_DIR="/data/connect/backup"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="connect"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 全量备份
pg_dump -h localhost -U to2 -d "$DB_NAME" | gzip > "$BACKUP_DIR/connect_full_$DATE.sql.gz"

# 保留30天的备份
find "$BACKUP_DIR" -name "connect_full_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: connect_full_$DATE.sql.gz"
```

### 2. 配置文件备份

```bash
#!/bin/bash

CONFIG_BACKUP_DIR="/data/connect/backup/config"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$CONFIG_BACKUP_DIR"

# 备份配置文件
tar -czf "$CONFIG_BACKUP_DIR/config_$DATE.tar.gz" \
    /opt/connect/config/ \
    /etc/nginx/sites-available/connect \
    /etc/systemd/system/connect-api.service

echo "Configuration backup completed: config_$DATE.tar.gz"
```

## 性能优化

### 1. 数据库优化

```sql
-- 创建索引
CREATE INDEX CONCURRENTLY idx_import_timestamp ON score_to2.score_table (import_timestamp);
CREATE INDEX CONCURRENTLY idx_source_file ON score_to2.score_table (source_file);

-- 分区表（对于大数据量）
CREATE TABLE cdr_to2.cdr_data_2024 PARTITION OF cdr_to2.cdr_data
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- 定期维护
VACUUM ANALYZE;
REINDEX DATABASE connect;
```

### 2. 应用优化

```python
# 连接池配置
DATABASE_POOL_SIZE = 20
DATABASE_MAX_OVERFLOW = 30
DATABASE_POOL_TIMEOUT = 30

# 异步处理配置
MAX_CONCURRENT_IMPORTS = 4
BATCH_SIZE = 5000
MEMORY_LIMIT = 2048  # MB
```

## 故障排除

### 常见问题和解决方案

1. **API服务无响应**
   ```bash
   sudo systemctl status connect-api
   sudo journalctl -u connect-api -n 50
   ```

2. **数据库连接失败**
   ```bash
   psql -h localhost -U to2 -d connect -c "SELECT version();"
   ```

3. **磁盘空间不足**
   ```bash
   df -h
   du -sh /data/connect/*
   ```

4. **内存不足**
   ```bash
   free -h
   ps aux --sort=-%mem | head -10
   ```

## 部署检查清单

### 部署前检查
- [ ] 系统要求满足
- [ ] 数据库配置完成
- [ ] 环境变量设置
- [ ] SSL证书配置
- [ ] 防火墙规则设置
- [ ] 备份策略配置

### 部署后验证
- [ ] API健康检查通过
- [ ] 数据库连接正常
- [ ] 文件导入功能测试
- [ ] 监控系统运行
- [ ] 日志记录正常
- [ ] 备份任务执行

### 性能测试
- [ ] 单文件导入测试
- [ ] 批量导入测试
- [ ] 并发处理测试
- [ ] 大文件处理测试
- [ ] 长时间运行测试

---

**版本**: v2.0.0  
**更新时间**: 2024-01-20  
**维护团队**: Connect开发团队
