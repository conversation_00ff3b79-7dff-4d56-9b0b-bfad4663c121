"""Unit tests for utility components."""

import hashlib
import json
import os
import tempfile
from datetime import date, datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from unittest.mock import MagicMock, Mock, mock_open, patch

import numpy as np
import pandas as pd
import pytest

from src.utils.cache_manager import CacheEntry, CacheManager, CacheStats
# from src.utils.compression import CompressionStats, CompressionUtils
# from src.utils.config_loader import Config<PERSON>rror, ConfigLoader, ConfigValidator

# Import utility components to test
from src.utils.data_validator import DataValidator, ValidationResult, ValidationRule
# from src.utils.date_utils import DateFormat, DateRange, DateUtils
# from src.utils.encryption import CryptoManager, EncryptionUtils, SecurityError
from src.utils.file_handler import FileError, <PERSON>Hand<PERSON>, FileInfo
# from src.utils.logger_utils import Log<PERSON>ilter, Log<PERSON>ormatter, LoggerUtils
# from src.utils.math_utils import MathUtils, NumericValidator, StatisticsCalculator
# from src.utils.retry_handler import RetryError, RetryHandler, RetryPolicy
# from src.utils.string_utils import StringUtils, StringValidator, TextProcessor


class TestDataValidator:
    """Test cases for DataValidator class."""

    @pytest.fixture
    def data_validator(self):
        """Create DataValidator instance."""
        return DataValidator()

    @pytest.fixture
    def sample_dataframe(self):
        """Create sample DataFrame for validation."""
        return pd.DataFrame(
            {
                "id": [1, 2, 3, 4, 5],
                "name": ["Alice", "Bob", "Charlie", "David", "Eve"],
                "email": [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
                "age": [25, 30, 35, 28, 32],
                "salary": [50000, 75000, 60000, 80000, 70000],
                "join_date": pd.to_datetime(
                    [
                        "2020-01-15",
                        "2019-03-20",
                        "2021-06-10",
                        "2018-11-05",
                        "2022-02-28",
                    ]
                ),
                "department": ["IT", "HR", "IT", "Finance", "HR"],
            }
        )

    def test_validator_initialization(self, data_validator):
        """Test validator initialization."""
        assert data_validator.rules == []
        assert data_validator.strict_mode is False
        assert data_validator.stop_on_first_error is False

    def test_add_validation_rule(self, data_validator):
        """Test adding validation rules."""
        rule = ValidationRule(
            column="age",
            rule_type="range",
            parameters={"min": 18, "max": 65},
            message="Age must be between 18 and 65",
        )

        data_validator.add_rule(rule)

        assert len(data_validator.rules) == 1
        assert data_validator.rules[0].column == "age"
        assert data_validator.rules[0].rule_type == "range"

    def test_validate_required_columns(self, data_validator, sample_dataframe):
        """Test required columns validation."""
        rule = ValidationRule(
            column="id", rule_type="required", message="ID column is required"
        )
        data_validator.add_rule(rule)

        result = data_validator.validate(sample_dataframe)

        assert result.is_valid
        assert len(result.errors) == 0

        # Test missing required column
        df_missing = sample_dataframe.drop("id", axis=1)
        result = data_validator.validate(df_missing)

        assert not result.is_valid
        assert len(result.errors) > 0

    def test_validate_data_types(self, data_validator, sample_dataframe):
        """Test data type validation."""
        rule = ValidationRule(
            column="age",
            rule_type="dtype",
            parameters={"expected_type": "int64"},
            message="Age must be integer",
        )
        data_validator.add_rule(rule)

        result = data_validator.validate(sample_dataframe)

        assert result.is_valid
        assert len(result.errors) == 0

    def test_validate_value_ranges(self, data_validator, sample_dataframe):
        """Test value range validation."""
        rule = ValidationRule(
            column="age",
            rule_type="range",
            parameters={"min": 18, "max": 65},
            message="Age must be between 18 and 65",
        )
        data_validator.add_rule(rule)

        result = data_validator.validate(sample_dataframe)

        assert result.is_valid
        assert len(result.errors) == 0

        # Test with invalid range
        invalid_df = sample_dataframe.copy()
        invalid_df.loc[0, "age"] = 100  # Invalid age

        result = data_validator.validate(invalid_df)

        assert not result.is_valid
        assert len(result.errors) > 0

    def test_validate_patterns(self, data_validator, sample_dataframe):
        """Test pattern validation (regex)."""
        rule = ValidationRule(
            column="email",
            rule_type="pattern",
            parameters={"pattern": r"^[\w\.-]+@[\w\.-]+\.\w+$"},
            message="Invalid email format",
        )
        data_validator.add_rule(rule)

        result = data_validator.validate(sample_dataframe)

        assert result.is_valid
        assert len(result.errors) == 0

        # Test with invalid email
        invalid_df = sample_dataframe.copy()
        invalid_df.loc[0, "email"] = "invalid-email"  # Invalid format

        result = data_validator.validate(invalid_df)

        assert not result.is_valid
        assert len(result.errors) > 0

    def test_validate_uniqueness(self, data_validator, sample_dataframe):
        """Test uniqueness validation."""
        rule = ValidationRule(
            column="id", rule_type="unique", message="ID values must be unique"
        )
        data_validator.add_rule(rule)

        result = data_validator.validate(sample_dataframe)

        assert result.is_valid
        assert len(result.errors) == 0

        # Test with duplicate values
        duplicate_df = sample_dataframe.copy()
        duplicate_df.loc[1, "id"] = 1  # Duplicate ID

        result = data_validator.validate(duplicate_df)

        assert not result.is_valid
        assert len(result.errors) > 0

    def test_validate_null_values(self, data_validator, sample_dataframe):
        """Test null value validation."""
        rule = ValidationRule(
            column="name", rule_type="not_null", message="Name cannot be null"
        )
        data_validator.add_rule(rule)

        result = data_validator.validate(sample_dataframe)

        assert result.is_valid
        assert len(result.errors) == 0

        # Test with null values
        null_df = sample_dataframe.copy()
        null_df.loc[0, "name"] = None

        result = data_validator.validate(null_df)

        assert not result.is_valid
        assert len(result.errors) > 0

    def test_custom_validation_function(self, data_validator, sample_dataframe):
        """Test custom validation function."""

        def validate_salary_department(row):
            """Custom validation: IT department should have higher salary."""
            if row["department"] == "IT" and row["salary"] < 55000:
                return False, "IT employees should have salary >= 55000"
            return True, None

        rule = ValidationRule(
            column=None,  # Row-level validation
            rule_type="custom",
            parameters={"function": validate_salary_department},
            message="Custom salary validation failed",
        )
        data_validator.add_rule(rule)

        result = data_validator.validate(sample_dataframe)

        assert result.is_valid
        assert len(result.errors) == 0

    def test_validation_summary(self, data_validator, sample_dataframe):
        """Test validation summary generation."""
        # Add multiple rules
        rules = [
            ValidationRule("id", "required", message="ID required"),
            ValidationRule("age", "range", {"min": 18, "max": 65}, "Age range"),
            ValidationRule(
                "email",
                "pattern",
                {"pattern": r"^[\w\.-]+@[\w\.-]+\.\w+$"},
                "Email format",
            ),
        ]

        for rule in rules:
            data_validator.add_rule(rule)

        result = data_validator.validate(sample_dataframe)
        summary = result.get_summary()

        assert "total_rules" in summary
        assert "passed_rules" in summary
        assert "failed_rules" in summary
        assert "total_records" in summary
        assert "valid_records" in summary
        assert "invalid_records" in summary
        assert summary["total_rules"] == 3

    def test_batch_validation(self, data_validator):
        """Test batch validation of multiple DataFrames."""
        df1 = pd.DataFrame({"id": [1, 2], "name": ["A", "B"]})
        df2 = pd.DataFrame({"id": [3, 4], "name": ["C", "D"]})
        df3 = pd.DataFrame({"id": [5], "name": [None]})  # Invalid

        rule = ValidationRule("name", "not_null", message="Name required")
        data_validator.add_rule(rule)

        results = data_validator.validate_batch([df1, df2, df3])

        assert len(results) == 3
        assert results[0].is_valid
        assert results[1].is_valid
        assert not results[2].is_valid


class TestFileHandler:
    """Test cases for FileHandler class."""

    @pytest.fixture
    def file_handler(self):
        """Create FileHandler instance."""
        return FileHandler()

    def test_file_handler_initialization(self, file_handler):
        """Test file handler initialization."""
        assert file_handler.supported_formats == ["csv", "xlsx", "json", "txt", "xml"]
        assert file_handler.max_file_size == 100 * 1024 * 1024  # 100MB
        assert file_handler.encoding == "utf-8"

    def test_get_file_info(self, file_handler, tmp_path):
        """Test file information retrieval."""
        test_file = tmp_path / "test.csv"
        test_content = "id,name\n1,Alice\n2,Bob"
        test_file.write_text(test_content)

        file_info = file_handler.get_file_info(str(test_file))

        assert file_info.path == str(test_file)
        assert file_info.size > 0
        assert file_info.extension == ".csv"
        assert file_info.exists is True
        assert file_info.is_readable is True

    def test_validate_file_format(self, file_handler, tmp_path):
        """Test file format validation."""
        # Valid format
        csv_file = tmp_path / "test.csv"
        csv_file.touch()

        is_valid, error = file_handler.validate_format(str(csv_file))
        assert is_valid
        assert error is None

        # Invalid format
        invalid_file = tmp_path / "test.invalid"
        invalid_file.touch()

        is_valid, error = file_handler.validate_format(str(invalid_file))
        assert not is_valid
        assert error is not None

    def test_validate_file_size(self, file_handler, tmp_path):
        """Test file size validation."""
        # Small file (valid)
        small_file = tmp_path / "small.csv"
        small_file.write_text("small content")

        is_valid, error = file_handler.validate_size(str(small_file))
        assert is_valid
        assert error is None

        # Mock large file
        with patch("pathlib.Path.stat") as mock_stat:
            mock_stat.return_value.st_size = 200 * 1024 * 1024  # 200MB

            is_valid, error = file_handler.validate_size(str(small_file))
            assert not is_valid
            assert error is not None

    def test_read_file_content(self, file_handler, tmp_path):
        """Test file content reading."""
        test_file = tmp_path / "test.txt"
        test_content = "Hello, World!\nThis is a test file."
        test_file.write_text(test_content, encoding="utf-8")

        content = file_handler.read_content(str(test_file))

        assert content == test_content

    def test_write_file_content(self, file_handler, tmp_path):
        """Test file content writing."""
        test_file = tmp_path / "output.txt"
        test_content = "This is test content."

        success = file_handler.write_content(str(test_file), test_content)

        assert success
        assert test_file.exists()
        assert test_file.read_text() == test_content

    def test_copy_file(self, file_handler, tmp_path):
        """Test file copying."""
        source_file = tmp_path / "source.txt"
        dest_file = tmp_path / "destination.txt"

        source_file.write_text("Source content")

        success = file_handler.copy_file(str(source_file), str(dest_file))

        assert success
        assert dest_file.exists()
        assert dest_file.read_text() == source_file.read_text()

    def test_move_file(self, file_handler, tmp_path):
        """Test file moving."""
        source_file = tmp_path / "source.txt"
        dest_file = tmp_path / "destination.txt"

        source_file.write_text("Source content")

        success = file_handler.move_file(str(source_file), str(dest_file))

        assert success
        assert not source_file.exists()
        assert dest_file.exists()

    def test_delete_file(self, file_handler, tmp_path):
        """Test file deletion."""
        test_file = tmp_path / "to_delete.txt"
        test_file.write_text("Content to delete")

        success = file_handler.delete_file(str(test_file))

        assert success
        assert not test_file.exists()

    def test_create_backup(self, file_handler, tmp_path):
        """Test backup file creation."""
        original_file = tmp_path / "original.txt"
        original_file.write_text("Original content")

        backup_path = file_handler.create_backup(str(original_file))

        assert backup_path is not None
        assert Path(backup_path).exists()
        assert ".backup." in backup_path or "_backup" in backup_path

    def test_calculate_file_hash(self, file_handler, tmp_path):
        """Test file hash calculation."""
        test_file = tmp_path / "test.txt"
        test_content = "Content for hashing"
        test_file.write_text(test_content)

        file_hash = file_handler.calculate_hash(str(test_file))

        # Calculate expected hash
        expected_hash = hashlib.md5(test_content.encode()).hexdigest()

        assert file_hash == expected_hash

    def test_batch_file_operations(self, file_handler, tmp_path):
        """Test batch file operations."""
        # Create multiple test files
        files = []
        for i in range(3):
            test_file = tmp_path / f"test_{i}.txt"
            test_file.write_text(f"Content {i}")
            files.append(str(test_file))

        # Test batch validation
        results = file_handler.validate_batch(files)

        assert len(results) == 3
        assert all(result.is_valid for result in results)


# class TestDateUtils:
#     """Test cases for DateUtils class."""

#     @pytest.fixture
#     def date_utils(self):
#         """Create DateUtils instance."""
#         return DateUtils()

#     def test_parse_date_string(self, date_utils):
#         """Test date string parsing."""
#         # Test various date formats
#         test_cases = [
#             ("2023-01-15", "%Y-%m-%d"),
#             ("15/01/2023", "%d/%m/%Y"),
#             ("Jan 15, 2023", "%b %d, %Y"),
#             ("2023-01-15 14:30:00", "%Y-%m-%d %H:%M:%S"),
#         ]

#         for date_str, format_str in test_cases:
#             parsed_date = date_utils.parse_date(date_str, format_str)
#             assert isinstance(parsed_date, datetime)
#             assert parsed_date.year == 2023
#             assert parsed_date.month == 1
#             assert parsed_date.day == 15

#     def test_format_date(self, date_utils):
#         """Test date formatting."""
#         test_date = datetime(2023, 1, 15, 14, 30, 0)

#         # Test various output formats
#         assert date_utils.format_date(test_date, "%Y-%m-%d") == "2023-01-15"
#         assert date_utils.format_date(test_date, "%d/%m/%Y") == "15/01/2023"
#         assert date_utils.format_date(test_date, "%B %d, %Y") == "January 15, 2023"

#     def test_date_arithmetic(self, date_utils):
#         """Test date arithmetic operations."""
#         base_date = datetime(2023, 1, 15)

#         # Add days
#         future_date = date_utils.add_days(base_date, 10)
#         assert future_date.day == 25

#         # Subtract days
#         past_date = date_utils.subtract_days(base_date, 5)
#         assert past_date.day == 10

#         # Add months
#         future_month = date_utils.add_months(base_date, 2)
#         assert future_month.month == 3

#         # Add years
#         future_year = date_utils.add_years(base_date, 1)
#         assert future_year.year == 2024

#     def test_date_difference(self, date_utils):
#         """Test date difference calculations."""
#         date1 = datetime(2023, 1, 15)
#         date2 = datetime(2023, 1, 25)

#         # Days difference
#         days_diff = date_utils.days_between(date1, date2)
#         assert days_diff == 10

#         # Hours difference
#         hours_diff = date_utils.hours_between(date1, date2)
#         assert hours_diff == 240  # 10 days * 24 hours

#         # Business days
#         business_days = date_utils.business_days_between(date1, date2)
#         assert business_days >= 0  # Should be positive

#     def test_date_validation(self, date_utils):
#         """Test date validation."""
#         # Valid dates
#         assert date_utils.is_valid_date("2023-01-15", "%Y-%m-%d")
#         assert date_utils.is_valid_date("15/01/2023", "%d/%m/%Y")

#         # Invalid dates
#         assert not date_utils.is_valid_date("2023-13-15", "%Y-%m-%d")  # Invalid month
#         assert not date_utils.is_valid_date("invalid-date", "%Y-%m-%d")
#         assert not date_utils.is_valid_date("2023-02-30", "%Y-%m-%d")  # Invalid day

#     def test_date_range_generation(self, date_utils):
#         """Test date range generation."""
#         start_date = datetime(2023, 1, 1)
#         end_date = datetime(2023, 1, 10)

#         # Daily range
#         daily_range = date_utils.generate_date_range(start_date, end_date, "daily")
#         assert len(daily_range) == 10
#         assert daily_range[0] == start_date
#         assert daily_range[-1] == end_date

#         # Weekly range
#         weekly_range = date_utils.generate_date_range(start_date, end_date, "weekly")
#         assert len(weekly_range) >= 1

#     def test_timezone_handling(self, date_utils):
#         """Test timezone operations."""
#         utc_date = datetime(2023, 1, 15, 12, 0, 0)

#         # Convert to timezone
#         local_date = date_utils.convert_timezone(utc_date, "UTC", "US/Eastern")
#         assert isinstance(local_date, datetime)

#         # Get current time in timezone
#         current_time = date_utils.now_in_timezone("US/Pacific")
#         assert isinstance(current_time, datetime)

#     def test_date_formatting_localization(self, date_utils):
#         """Test localized date formatting."""
#         test_date = datetime(2023, 1, 15)

#         # Test different locales (if available)
#         try:
#             formatted = date_utils.format_localized(test_date, "en_US", "%B %d, %Y")
#             assert "January" in formatted
#         except:
#             # Locale might not be available, skip test
#             pass


# class TestStringUtils:
#     """Test cases for StringUtils class."""
# 
#     @pytest.fixture
#     def string_utils(self):
#         """Create StringUtils instance."""
#         return StringUtils()
# 
#     def test_string_cleaning(self, string_utils):
#         """Test string cleaning operations."""
#         dirty_string = "  Hello, World!  \n\t  "
# 
#         # Clean whitespace
#         cleaned = string_utils.clean_whitespace(dirty_string)
#         assert cleaned == "Hello, World!"
# 
#         # Remove special characters
#         special_string = "Hello@#$%World!"
#         cleaned_special = string_utils.remove_special_chars(special_string)
#         assert cleaned_special == "HelloWorld"
# 
#         # Normalize case
#         mixed_case = "HeLLo WoRLd"
#         normalized = string_utils.normalize_case(mixed_case, "title")
#         assert normalized == "Hello World"
# 
#     def test_string_validation(self, string_utils):
#         """Test string validation."""
#         # Email validation
#         assert string_utils.is_valid_email("<EMAIL>")
#         assert not string_utils.is_valid_email("invalid-email")
# 
#         # Phone validation
#         assert string_utils.is_valid_phone("******-567-8900")
#         assert string_utils.is_valid_phone("(*************")
# 
#         # URL validation
#         assert string_utils.is_valid_url("https://www.example.com")
#         assert not string_utils.is_valid_url("not-a-url")
# 
#     def test_string_transformation(self, string_utils):
#         """Test string transformations."""
#         test_string = "Hello World Example"
# 
#         # Snake case
#         snake_case = string_utils.to_snake_case(test_string)
#         assert snake_case == "hello_world_example"
# 
#         # Camel case
#         camel_case = string_utils.to_camel_case(test_string)
#         assert camel_case == "helloWorldExample"
# 
#         # Kebab case
#         kebab_case = string_utils.to_kebab_case(test_string)
#         assert kebab_case == "hello-world-example"
# 
#     def test_string_similarity(self, string_utils):
#         """Test string similarity calculations."""
#         string1 = "Hello World"
#         string2 = "Hello Word"
#         string3 = "Goodbye World"
# 
#         # Levenshtein distance
#         distance = string_utils.levenshtein_distance(string1, string2)
#         assert distance == 1  # One character difference
# 
#         # Similarity ratio
#         similarity = string_utils.similarity_ratio(string1, string2)
#         assert similarity > 0.8  # Should be quite similar
# 
#         similarity_low = string_utils.similarity_ratio(string1, string3)
#         assert similarity_low < 0.5  # Should be less similar
# 
#     def test_text_processing(self, string_utils):
#         """Test text processing operations."""
#         text = "This is a sample text with multiple words and sentences. It contains various punctuation marks!"
# 
#         # Word count
#         word_count = string_utils.count_words(text)
#         assert word_count > 10
# 
#         # Extract words
#         words = string_utils.extract_words(text)
#         assert len(words) == word_count
#         assert "sample" in words
# 
#         # Extract sentences
#         sentences = string_utils.extract_sentences(text)
#         assert len(sentences) == 2
# 
#     def test_string_encoding(self, string_utils):
#         """Test string encoding/decoding."""
#         test_string = "Hello, 世界! 🌍"
# 
#         # UTF-8 encoding
#         encoded = string_utils.encode_string(test_string, "utf-8")
#         assert isinstance(encoded, bytes)
# 
#         # Decode back
#         decoded = string_utils.decode_string(encoded, "utf-8")
#         assert decoded == test_string
# 
#     def test_string_truncation(self, string_utils):
#         """Test string truncation."""
#         long_string = "This is a very long string that needs to be truncated"
# 
#         # Simple truncation
#         truncated = string_utils.truncate(long_string, 20)
#         assert len(truncated) <= 20
# 
#         # Truncation with ellipsis
#         truncated_ellipsis = string_utils.truncate(long_string, 20, add_ellipsis=True)
#         assert truncated_ellipsis.endswith("...")
#         assert len(truncated_ellipsis) <= 20
# 
#     def test_string_masking(self, string_utils):
#         """Test string masking for sensitive data."""
#         # Email masking
#         email = "<EMAIL>"
#         masked_email = string_utils.mask_email(email)
#         assert "*" in masked_email
#         assert "@example.com" in masked_email
# 
#         # Phone masking
#         phone = "************"
#         masked_phone = string_utils.mask_phone(phone)
#         assert "*" in masked_phone
# 
#         # Credit card masking
#         cc_number = "1234567890123456"
#         masked_cc = string_utils.mask_credit_card(cc_number)
#         assert masked_cc.endswith("3456")
#         assert "*" in masked_cc


# class TestMathUtils:
#     """Test cases for MathUtils class."""
# 
#     @pytest.fixture
#     def math_utils(self):
#         """Create MathUtils instance."""
#         return MathUtils()
# 
#     @pytest.fixture
#     def sample_numbers(self):
#         """Create sample numeric data."""
#         return [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
# 
#     def test_basic_statistics(self, math_utils, sample_numbers):
#         """Test basic statistical calculations."""
#         # Mean
#         mean = math_utils.calculate_mean(sample_numbers)
#         assert mean == 5.5
# 
#         # Median
#         median = math_utils.calculate_median(sample_numbers)
#         assert median == 5.5
# 
#         # Mode
#         mode_data = [1, 2, 2, 3, 3, 3, 4]
#         mode = math_utils.calculate_mode(mode_data)
#         assert mode == 3
# 
#         # Standard deviation
#         std_dev = math_utils.calculate_std_dev(sample_numbers)
#         assert std_dev > 0
# 
#     def test_percentile_calculations(self, math_utils, sample_numbers):
#         """Test percentile calculations."""
#         # 50th percentile (median)
#         p50 = math_utils.calculate_percentile(sample_numbers, 50)
#         assert p50 == 5.5
# 
#         # 25th percentile
#         p25 = math_utils.calculate_percentile(sample_numbers, 25)
#         assert p25 < 5.5
# 
#         # 75th percentile
#         p75 = math_utils.calculate_percentile(sample_numbers, 75)
#         assert p75 > 5.5
# 
#     def test_outlier_detection(self, math_utils):
#         """Test outlier detection."""
#         data_with_outliers = [1, 2, 3, 4, 5, 100]  # 100 is an outlier
# 
#         # IQR method
#         outliers_iqr = math_utils.detect_outliers_iqr(data_with_outliers)
#         assert 100 in outliers_iqr
# 
#         # Z-score method
#         outliers_zscore = math_utils.detect_outliers_zscore(
#             data_with_outliers, threshold=2
#         )
#         assert 100 in outliers_zscore
# 
#     def test_correlation_calculations(self, math_utils):
#         """Test correlation calculations."""
#         x = [1, 2, 3, 4, 5]
#         y = [2, 4, 6, 8, 10]  # Perfect positive correlation
# 
#         # Pearson correlation
#         correlation = math_utils.calculate_correlation(x, y)
#         assert abs(correlation - 1.0) < 0.001  # Should be close to 1
# 
#         # Spearman correlation
#         spearman = math_utils.calculate_spearman_correlation(x, y)
#         assert abs(spearman - 1.0) < 0.001
# 
#     def test_normalization(self, math_utils, sample_numbers):
#         """Test data normalization."""
#         # Min-max normalization
#         normalized_minmax = math_utils.normalize_minmax(sample_numbers)
#         assert min(normalized_minmax) == 0.0
#         assert max(normalized_minmax) == 1.0
# 
#         # Z-score normalization
#         normalized_zscore = math_utils.normalize_zscore(sample_numbers)
#         assert (
#             abs(math_utils.calculate_mean(normalized_zscore)) < 0.001
#         )  # Mean should be ~0
# 
#     def test_rounding_operations(self, math_utils):
#         """Test rounding operations."""
#         test_number = 3.14159
# 
#         # Round to decimal places
#         rounded = math_utils.round_to_decimal(test_number, 2)
#         assert rounded == 3.14
# 
#         # Round to significant figures
#         rounded_sig = math_utils.round_to_significant(test_number, 3)
#         assert rounded_sig == 3.14
# 
#     def test_numeric_validation(self, math_utils):
#         """Test numeric validation."""
#         # Integer validation
#         assert math_utils.is_integer("123")
#         assert not math_utils.is_integer("123.45")
#         assert not math_utils.is_integer("abc")
# 
#         # Float validation
#         assert math_utils.is_float("123.45")
#         assert math_utils.is_float("123")
#         assert not math_utils.is_float("abc")
# 
#         # Range validation
#         assert math_utils.is_in_range(5, 1, 10)
#         assert not math_utils.is_in_range(15, 1, 10)
# 
#     def test_mathematical_operations(self, math_utils):
#         """Test mathematical operations."""
#         # Factorial
#         assert math_utils.factorial(5) == 120
# 
#         # GCD
#         assert math_utils.gcd(48, 18) == 6
# 
#         # LCM
#         assert math_utils.lcm(4, 6) == 12
# 
#         # Power
#         assert math_utils.power(2, 3) == 8
# 
#         # Square root
#         assert math_utils.sqrt(16) == 4


class TestCacheManager:
    """Test cases for CacheManager class."""

    @pytest.fixture
    def cache_manager(self):
        """Create CacheManager instance."""
        return CacheManager(max_size=100, ttl=3600)  # 1 hour TTL

    def test_cache_initialization(self, cache_manager):
        """Test cache manager initialization."""
        assert cache_manager.max_size == 100
        assert cache_manager.ttl == 3600
        assert cache_manager.size == 0
        assert cache_manager.hit_count == 0
        assert cache_manager.miss_count == 0

    def test_cache_set_get(self, cache_manager):
        """Test basic cache set and get operations."""
        # Set value
        cache_manager.set("key1", "value1")
        assert cache_manager.size == 1

        # Get value
        value = cache_manager.get("key1")
        assert value == "value1"
        assert cache_manager.hit_count == 1

        # Get non-existent key
        value = cache_manager.get("nonexistent")
        assert value is None
        assert cache_manager.miss_count == 1

    def test_cache_expiration(self, cache_manager):
        """Test cache entry expiration."""
        # Set value with short TTL
        cache_manager.set("temp_key", "temp_value", ttl=1)  # 1 second

        # Should be available immediately
        assert cache_manager.get("temp_key") == "temp_value"

        # Mock time passage
        with patch("time.time", return_value=time.time() + 2):
            # Should be expired
            assert cache_manager.get("temp_key") is None

    def test_cache_size_limit(self, cache_manager):
        """Test cache size limit enforcement."""
        # Fill cache to capacity
        for i in range(cache_manager.max_size):
            cache_manager.set(f"key_{i}", f"value_{i}")

        assert cache_manager.size == cache_manager.max_size

        # Add one more item (should evict oldest)
        cache_manager.set("overflow_key", "overflow_value")

        # Size should still be at max
        assert cache_manager.size == cache_manager.max_size

        # First item should be evicted
        assert cache_manager.get("key_0") is None

        # New item should be present
        assert cache_manager.get("overflow_key") == "overflow_value"

    def test_cache_delete(self, cache_manager):
        """Test cache entry deletion."""
        cache_manager.set("delete_me", "value")
        assert cache_manager.get("delete_me") == "value"

        # Delete entry
        deleted = cache_manager.delete("delete_me")
        assert deleted is True
        assert cache_manager.get("delete_me") is None

        # Try to delete non-existent key
        deleted = cache_manager.delete("nonexistent")
        assert deleted is False

    def test_cache_clear(self, cache_manager):
        """Test cache clearing."""
        # Add some entries
        for i in range(5):
            cache_manager.set(f"key_{i}", f"value_{i}")

        assert cache_manager.size == 5

        # Clear cache
        cache_manager.clear()

        assert cache_manager.size == 0
        assert cache_manager.get("key_0") is None

    def test_cache_statistics(self, cache_manager):
        """Test cache statistics."""
        # Perform some operations
        cache_manager.set("key1", "value1")
        cache_manager.get("key1")  # Hit
        cache_manager.get("key2")  # Miss

        stats = cache_manager.get_stats()

        assert stats["size"] == 1
        assert stats["hit_count"] == 1
        assert stats["miss_count"] == 1
        assert stats["hit_rate"] == 0.5
        assert "memory_usage" in stats

    def test_cache_keys_values(self, cache_manager):
        """Test getting cache keys and values."""
        # Add some entries
        test_data = {"key1": "value1", "key2": "value2", "key3": "value3"}
        for key, value in test_data.items():
            cache_manager.set(key, value)

        # Get all keys
        keys = cache_manager.keys()
        assert set(keys) == set(test_data.keys())

        # Get all values
        values = cache_manager.values()
        assert set(values) == set(test_data.values())

        # Get all items
        items = cache_manager.items()
        assert dict(items) == test_data

    def test_cache_contains(self, cache_manager):
        """Test cache membership testing."""
        cache_manager.set("existing_key", "value")

        assert "existing_key" in cache_manager
        assert "nonexistent_key" not in cache_manager


class TestRetryHandler:
    """Test cases for RetryHandler class."""

    @pytest.fixture
    def retry_handler(self):
        """Create RetryHandler instance."""
        return RetryHandler(
            max_attempts=3, base_delay=1.0, max_delay=10.0, backoff_factor=2.0
        )

    def test_retry_handler_initialization(self, retry_handler):
        """Test retry handler initialization."""
        assert retry_handler.max_attempts == 3
        assert retry_handler.base_delay == 1.0
        assert retry_handler.max_delay == 10.0
        assert retry_handler.backoff_factor == 2.0

    def test_successful_operation(self, retry_handler):
        """Test operation that succeeds on first try."""

        def successful_operation():
            return "success"

        result = retry_handler.execute(successful_operation)
        assert result == "success"
        assert retry_handler.attempt_count == 1

    def test_operation_with_retries(self, retry_handler):
        """Test operation that fails then succeeds."""
        call_count = 0

        def flaky_operation():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return "success"

        with patch("time.sleep"):  # Mock sleep to speed up test
            result = retry_handler.execute(flaky_operation)

        assert result == "success"
        assert call_count == 3
        assert retry_handler.attempt_count == 3

    def test_operation_max_attempts_exceeded(self, retry_handler):
        """Test operation that always fails."""

        def failing_operation():
            raise Exception("Always fails")

        with patch("time.sleep"):  # Mock sleep to speed up test
            with pytest.raises(Exception, match="Always fails"):
                retry_handler.execute(failing_operation)

        assert retry_handler.attempt_count == 3  # Max attempts

    def test_exponential_backoff(self, retry_handler):
        """Test exponential backoff delay calculation."""
        # Test delay calculation for different attempts
        delay1 = retry_handler.calculate_delay(1)
        delay2 = retry_handler.calculate_delay(2)
        delay3 = retry_handler.calculate_delay(3)

        assert delay1 == 1.0  # Base delay
        assert delay2 == 2.0  # Base delay * backoff_factor
        assert delay3 == 4.0  # Base delay * backoff_factor^2

    def test_max_delay_limit(self, retry_handler):
        """Test that delay doesn't exceed max_delay."""
        # Calculate delay for high attempt number
        high_attempt_delay = retry_handler.calculate_delay(10)

        assert high_attempt_delay <= retry_handler.max_delay

    def test_retry_with_custom_exceptions(self, retry_handler):
        """Test retry with specific exception types."""
        call_count = 0

        def operation_with_different_exceptions():
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                raise ConnectionError("Network error")  # Should retry
            elif call_count == 2:
                raise ValueError("Invalid value")  # Should not retry
            return "success"

        # Configure to only retry on ConnectionError
        retry_handler.retryable_exceptions = [ConnectionError]

        with patch("time.sleep"):
            with pytest.raises(ValueError):
                retry_handler.execute(operation_with_different_exceptions)

        assert call_count == 2  # Should stop after ValueError

    def test_retry_callback(self, retry_handler):
        """Test retry callback functionality."""
        callback_calls = []

        def retry_callback(attempt, exception):
            callback_calls.append((attempt, str(exception)))

        def failing_operation():
            raise Exception("Test failure")

        retry_handler.on_retry = retry_callback

        with patch("time.sleep"):
            with pytest.raises(Exception):
                retry_handler.execute(failing_operation)

        # Should have called callback for each retry (not including final failure)
        assert len(callback_calls) == 2  # 2 retries before final failure

    def test_retry_statistics(self, retry_handler):
        """Test retry statistics collection."""

        def flaky_operation():
            if retry_handler.attempt_count < 2:
                raise Exception("Temporary failure")
            return "success"

        with patch("time.sleep"):
            result = retry_handler.execute(flaky_operation)

        stats = retry_handler.get_stats()

        assert stats["total_attempts"] == 2
        assert stats["success"] is True
        assert stats["total_delay"] >= 0
        assert "execution_time" in stats
