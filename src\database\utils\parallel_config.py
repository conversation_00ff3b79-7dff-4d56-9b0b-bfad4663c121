"""
Enhanced parallel processing configuration for telecommunications data import.

This module provides intelligent parallel worker configuration based on system
resources, file characteristics, and database configuration.
"""

import logging
import psutil
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class ParallelProcessingConfig:
    """Manages parallel processing configuration for telecommunications data import."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize with database configuration.
        
        Args:
            config: Database configuration dictionary
        """
        self.config = config
        self.telecom_config = config.get('telecom_data_sources', {})
        self.system_info = self._get_system_info()
        
    def _get_system_info(self) -> Dict[str, Any]:
        """Get current system resource information."""
        memory = psutil.virtual_memory()
        
        return {
            'cpu_count': psutil.cpu_count(logical=True),
            'cpu_percent': psutil.cpu_percent(interval=0.1),
            'memory_total_gb': memory.total / (1024**3),
            'memory_available_gb': memory.available / (1024**3),
            'memory_percent_used': memory.percent,
        }
    
    def get_optimal_workers_for_cdr(self, file_size_mb: float = 0, 
                                   total_files: int = 1) -> Dict[str, Any]:
        """Get optimal worker configuration for CDR data processing.
        
        Args:
            file_size_mb: Size of the file(s) being processed
            total_files: Total number of files to process
            
        Returns:
            Dictionary with optimal configuration
        """
        cdr_config = self.telecom_config.get('cdr', {})
        batch_config = cdr_config.get('batch_processing', {})
        
        # Get configuration values with defaults
        default_workers = batch_config.get('parallel_workers', 8)
        memory_threshold_mb = batch_config.get('memory_threshold_mb', 2048)
        memory_per_worker_mb = batch_config.get('memory_per_worker_mb', 300)
        enable_dynamic_scaling = batch_config.get('enable_dynamic_scaling', True)
        min_workers = batch_config.get('min_workers', 2)
        max_workers = batch_config.get('max_workers', 12)
        
        if not enable_dynamic_scaling:
            return {
                'parallel_workers': default_workers,
                'reason': 'Dynamic scaling disabled, using configured value',
                'memory_threshold_mb': memory_threshold_mb,
                'scaling_applied': False
            }
        
        # Calculate optimal workers based on system resources
        optimal_workers = self._calculate_optimal_workers(
            file_size_mb, total_files, memory_per_worker_mb, min_workers, max_workers
        )
        
        # Check if we need to scale down due to memory constraints
        estimated_memory_usage = self._estimate_memory_usage(file_size_mb, total_files)
        
        scaling_info = {
            'parallel_workers': optimal_workers,
            'reason': 'Optimized for system resources and file characteristics',
            'memory_threshold_mb': memory_threshold_mb,
            'estimated_memory_mb': estimated_memory_usage,
            'scaling_applied': optimal_workers != default_workers,
            'system_constraints': {
                'cpu_cores': self.system_info['cpu_count'],
                'available_memory_gb': self.system_info['memory_available_gb'],
                'memory_usage_percent': self.system_info['memory_percent_used']
            }
        }
        
        # Apply memory-based scaling if needed
        if estimated_memory_usage > memory_threshold_mb:
            memory_constrained_workers = max(min_workers, 
                                           int(memory_threshold_mb / memory_per_worker_mb))
            if memory_constrained_workers < optimal_workers:
                scaling_info['parallel_workers'] = memory_constrained_workers
                scaling_info['reason'] = f'Scaled down due to memory constraints ({estimated_memory_usage:.0f}MB > {memory_threshold_mb}MB)'
                scaling_info['scaling_applied'] = True
        
        return scaling_info
    
    def _calculate_optimal_workers(self, file_size_mb: float, total_files: int,
                                 memory_per_worker_mb: int, min_workers: int, 
                                 max_workers: int) -> int:
        """Calculate optimal number of workers based on system resources."""
        
        # CPU-based calculation (leave 2 cores for system)
        max_workers_by_cpu = max(min_workers, self.system_info['cpu_count'] - 2)
        
        # Memory-based calculation (use 70% of available memory)
        available_memory_mb = self.system_info['memory_available_gb'] * 1024 * 0.7
        max_workers_by_memory = max(min_workers, int(available_memory_mb / memory_per_worker_mb))
        
        # File size-based calculation
        avg_file_size = file_size_mb / max(1, total_files)
        if avg_file_size > 100:  # Large files
            recommended_workers = min(4, max_workers_by_cpu, max_workers_by_memory)
        elif avg_file_size > 50:  # Medium files
            recommended_workers = min(6, max_workers_by_cpu, max_workers_by_memory)
        else:  # Small files
            recommended_workers = min(8, max_workers_by_cpu, max_workers_by_memory)
        
        # Apply bounds
        optimal_workers = max(min_workers, min(max_workers, recommended_workers))
        
        logger.debug(f"Worker calculation: CPU={max_workers_by_cpu}, "
                    f"Memory={max_workers_by_memory}, FileSize={recommended_workers}, "
                    f"Final={optimal_workers}")
        
        return optimal_workers
    
    def _estimate_memory_usage(self, file_size_mb: float, total_files: int) -> float:
        """Estimate memory usage for processing files."""
        
        # More realistic memory estimation
        avg_file_size = file_size_mb / max(1, total_files)
        
        # Memory usage factors:
        # - File loading: 1.5x file size (Excel decompression)
        # - Data processing: 1x file size (DataFrame operations)
        # - Buffer/overhead: 0.5x file size
        memory_factor = 3.0  # Total factor
        
        # Estimate concurrent files (typically 2-4 files processed simultaneously)
        max_concurrent_files = min(4, total_files)
        
        estimated_memory = avg_file_size * memory_factor * max_concurrent_files
        
        return estimated_memory
    
    def get_batch_size_for_cdr(self, file_size_mb: float = 0, 
                              record_count: int = 0) -> int:
        """Get optimal batch size for CDR processing.
        
        Args:
            file_size_mb: Size of the file being processed
            record_count: Number of records (if known)
            
        Returns:
            Optimal batch size
        """
        cdr_config = self.telecom_config.get('cdr', {})
        batch_config = cdr_config.get('batch_processing', {})
        default_batch_size = batch_config.get('batch_size', 10000)
        
        # Adjust batch size based on file size and available memory
        if file_size_mb > 200:  # Very large files
            return min(default_batch_size, 5000)
        elif file_size_mb > 100:  # Large files
            return min(default_batch_size, 7500)
        elif file_size_mb < 10:  # Small files
            return min(default_batch_size, 15000)
        else:
            return default_batch_size
    
    def should_use_streaming(self, file_size_mb: float) -> bool:
        """Determine if streaming should be used for large files.
        
        Args:
            file_size_mb: Size of the file being processed
            
        Returns:
            True if streaming should be used
        """
        # Use streaming for files larger than 500MB or if memory is constrained
        memory_constrained = self.system_info['memory_available_gb'] < 4
        large_file = file_size_mb > 500
        
        return large_file or memory_constrained
    
    def get_processing_recommendations(self, file_path: Path) -> Dict[str, Any]:
        """Get comprehensive processing recommendations for a file.
        
        Args:
            file_path: Path to the file to be processed
            
        Returns:
            Dictionary with processing recommendations
        """
        try:
            file_size_mb = file_path.stat().st_size / (1024**2)
        except (OSError, FileNotFoundError):
            file_size_mb = 50  # Default assumption
        
        worker_config = self.get_optimal_workers_for_cdr(file_size_mb, 1)
        batch_size = self.get_batch_size_for_cdr(file_size_mb)
        use_streaming = self.should_use_streaming(file_size_mb)
        
        return {
            'file_size_mb': file_size_mb,
            'parallel_workers': worker_config['parallel_workers'],
            'batch_size': batch_size,
            'use_streaming': use_streaming,
            'memory_threshold_mb': worker_config['memory_threshold_mb'],
            'estimated_memory_mb': worker_config['estimated_memory_mb'],
            'scaling_reason': worker_config['reason'],
            'system_info': self.system_info
        }
