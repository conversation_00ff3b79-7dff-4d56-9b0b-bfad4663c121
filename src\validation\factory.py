"""Validation factory for creating and managing validators.

This module provides a high-level factory interface for creating
validation frameworks and managing validation workflows.
"""

from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd
from loguru import logger

from ..types.telecom_types import DataSourceType
from .core import ValidationContext, ValidationFramework, ValidationResult
from .exceptions import ValidationConfigError, ValidationError
from .rules import ValidationRuleFactory


class ValidationFactory:
    """Factory for creating and managing validation workflows."""
    
    def __init__(self):
        self._frameworks: Dict[str, ValidationFramework] = {}
        self._rule_factory = ValidationRuleFactory()
    
    def create_framework(
        self,
        name: str,
        data_type: str,
        custom_rules: Optional[List] = None,
        enable_parallel: bool = True,
        max_workers: Optional[int] = None
    ) -> ValidationFramework:
        """Create a validation framework for specific data type.
        
        Args:
            name: Framework name
            data_type: Type of data ('cdr', 'kpi', 'cfg', 'database')
            custom_rules: Additional custom validation rules
            enable_parallel: Enable parallel validation
            max_workers: Maximum number of worker threads
            
        Returns:
            Configured ValidationFramework
            
        Raises:
            ValidationConfigError: If configuration is invalid
        """
        try:
            # Get standard rules for data type
            rules = self._rule_factory.create_validators_for_data_type(data_type)
            
            # Add custom rules if provided
            if custom_rules:
                rules.extend(custom_rules)
            
            # Create framework
            framework = ValidationFramework(
                name=name,
                enable_parallel=enable_parallel,
                max_workers=max_workers
            )
            
            # Add rules to framework
            for rule in rules:
                framework.add_rule(rule)
            
            # Cache framework
            self._frameworks[name] = framework
            
            logger.info(f"Created validation framework '{name}' for {data_type} with {len(rules)} rules")
            return framework
            
        except Exception as e:
            raise ValidationConfigError(
                f"Failed to create validation framework '{name}' for {data_type}",
                details={"data_type": data_type, "error": str(e)}
            ) from e
    
    def get_framework(self, name: str) -> Optional[ValidationFramework]:
        """Get cached validation framework by name.
        
        Args:
            name: Framework name
            
        Returns:
            ValidationFramework if found, None otherwise
        """
        return self._frameworks.get(name)
    
    def validate_data(
        self,
        data: Any,
        data_type: str,
        file_path: Optional[Union[str, Path]] = None,
        framework_name: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Validate data using appropriate framework.
        
        Args:
            data: Data to validate
            data_type: Type of data ('cdr', 'kpi', 'cfg', 'database')
            file_path: Optional file path for context
            framework_name: Optional custom framework name
            context: Additional validation context
            
        Returns:
            ValidationResult
            
        Raises:
            ValidationError: If validation setup fails
        """
        try:
            # Determine framework name
            if not framework_name:
                framework_name = f"{data_type}_validation"
            
            # Get or create framework
            framework = self.get_framework(framework_name)
            if not framework:
                framework = self.create_framework(framework_name, data_type)
            
            # Create validation context
            validation_context = ValidationContext(
                data_type=data_type,
                file_path=str(file_path) if file_path else None,
                metadata=context or {}
            )
            
            # Run validation
            result = framework.validate(data, validation_context)
            
            logger.info(
                f"Validation completed for {data_type}: "
                f"{result.passed_rules}/{result.total_rules} rules passed, "
                f"{len(result.issues)} issues found"
            )
            
            return result
            
        except Exception as e:
            raise ValidationError(
                f"Failed to validate {data_type} data",
                details={
                    "data_type": data_type,
                    "framework_name": framework_name,
                    "error": str(e)
                }
            ) from e
    
    def validate_file(
        self,
        file_path: Union[str, Path],
        data_type: str,
        read_kwargs: Optional[Dict[str, Any]] = None,
        framework_name: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Validate data from file.
        
        Args:
            file_path: Path to data file
            data_type: Type of data ('cdr', 'kpi', 'cfg')
            read_kwargs: Additional arguments for pandas.read_csv/read_excel
            framework_name: Optional custom framework name
            context: Additional validation context
            
        Returns:
            ValidationResult
            
        Raises:
            ValidationError: If file reading or validation fails
        """
        file_path = Path(file_path)
        
        try:
            # Read data from file
            if file_path.suffix.lower() == '.csv':
                data = pd.read_csv(file_path, **(read_kwargs or {}))
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                data = pd.read_excel(file_path, **(read_kwargs or {}))
            else:
                raise ValidationError(
                    f"Unsupported file format: {file_path.suffix}",
                    details={"file_path": str(file_path), "suffix": file_path.suffix}
                )
            
            logger.info(f"Read {len(data)} rows from {file_path}")
            
            # Validate data
            return self.validate_data(
                data=data,
                data_type=data_type,
                file_path=file_path,
                framework_name=framework_name,
                context=context
            )
            
        except pd.errors.EmptyDataError:
            raise ValidationError(
                f"File is empty: {file_path}",
                details={"file_path": str(file_path)}
            )
        except pd.errors.ParserError as e:
            raise ValidationError(
                f"Failed to parse file: {file_path}",
                details={"file_path": str(file_path), "parser_error": str(e)}
            ) from e
        except Exception as e:
            raise ValidationError(
                f"Failed to validate file: {file_path}",
                details={"file_path": str(file_path), "error": str(e)}
            ) from e
    
    def validate_cdr_data(
        self,
        data: pd.DataFrame,
        file_path: Optional[Union[str, Path]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Validate CDR data with predefined rules.
        
        Args:
            data: CDR DataFrame
            file_path: Optional file path for context
            context: Additional validation context
            
        Returns:
            ValidationResult
        """
        return self.validate_data(
            data=data,
            data_type="cdr",
            file_path=file_path,
            context=context
        )
    
    def validate_kpi_data(
        self,
        data: pd.DataFrame,
        file_path: Optional[Union[str, Path]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Validate KPI data with predefined rules.
        
        Args:
            data: KPI DataFrame
            file_path: Optional file path for context
            context: Additional validation context
            
        Returns:
            ValidationResult
        """
        return self.validate_data(
            data=data,
            data_type="kpi",
            file_path=file_path,
            context=context
        )
    
    def validate_cfg_data(
        self,
        data: pd.DataFrame,
        file_path: Optional[Union[str, Path]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Validate configuration data with predefined rules.
        
        Args:
            data: Configuration DataFrame
            file_path: Optional file path for context
            context: Additional validation context
            
        Returns:
            ValidationResult
        """
        return self.validate_data(
            data=data,
            data_type="cfg",
            file_path=file_path,
            context=context
        )
    
    def validate_score_data(
        self,
        data: pd.DataFrame,
        file_path: Optional[Union[str, Path]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Validate SCORE data with predefined rules.
        
        Args:
            data: SCORE DataFrame
            file_path: Optional file path for context
            context: Additional validation context
            
        Returns:
            ValidationResult
        """
        return self.validate_data(
            data=data,
            data_type="score",
            file_path=file_path,
            context=context
        )
    
    def validate_cdr_file(
        self,
        file_path: Union[str, Path],
        read_kwargs: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Validate CDR file.
        
        Args:
            file_path: Path to CDR file
            read_kwargs: Additional arguments for file reading
            context: Additional validation context
            
        Returns:
            ValidationResult
        """
        return self.validate_file(
            file_path=file_path,
            data_type="cdr",
            read_kwargs=read_kwargs,
            context=context
        )
    
    def validate_kpi_file(
        self,
        file_path: Union[str, Path],
        read_kwargs: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Validate KPI file.
        
        Args:
            file_path: Path to KPI file
            read_kwargs: Additional arguments for file reading
            context: Additional validation context
            
        Returns:
            ValidationResult
        """
        return self.validate_file(
            file_path=file_path,
            data_type="kpi",
            read_kwargs=read_kwargs,
            context=context
        )
    
    def validate_cfg_file(
        self,
        file_path: Union[str, Path],
        read_kwargs: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Validate configuration file.
        
        Args:
            file_path: Path to configuration file
            read_kwargs: Additional arguments for file reading
            context: Additional validation context
            
        Returns:
            ValidationResult
        """
        return self.validate_file(
            file_path=file_path,
            data_type="cfg",
            read_kwargs=read_kwargs,
            context=context
        )
    
    def validate_score_file(
        self,
        file_path: Union[str, Path],
        read_kwargs: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> ValidationResult:
        """Validate SCORE file.
        
        Args:
            file_path: Path to SCORE file
            read_kwargs: Additional arguments for file reading
            context: Additional validation context
            
        Returns:
            ValidationResult
        """
        return self.validate_file(
            file_path=file_path,
            data_type="score",
            read_kwargs=read_kwargs,
            context=context
        )
    
    def clear_cache(self) -> None:
        """Clear cached validation frameworks."""
        self._frameworks.clear()
        logger.info("Cleared validation framework cache")
    
    def list_frameworks(self) -> List[str]:
        """List names of cached validation frameworks.
        
        Returns:
            List of framework names
        """
        return list(self._frameworks.keys())
    
    def get_framework_info(self, name: str) -> Optional[Dict[str, Any]]:
        """Get information about a cached framework.
        
        Args:
            name: Framework name
            
        Returns:
            Framework information dict or None if not found
        """
        framework = self.get_framework(name)
        if not framework:
            return None
        
        return {
            "name": framework.name,
            "rule_count": len(framework.rules),
            "parallel_enabled": framework.enable_parallel,
            "max_workers": framework.max_workers,
            "rules": [
                {
                    "name": rule.name,
                    "type": rule.validation_type.value,
                    "severity": rule.severity.value
                }
                for rule in framework.rules
            ]
        }


# Global validation factory instance
_validation_factory = ValidationFactory()


def get_validation_factory() -> ValidationFactory:
    """Get the global validation factory instance.
    
    Returns:
        ValidationFactory instance
    """
    return _validation_factory


# Convenience functions for common validation tasks
def validate_cdr_data(
    data: pd.DataFrame,
    file_path: Optional[Union[str, Path]] = None,
    context: Optional[Dict[str, Any]] = None
) -> ValidationResult:
    """Validate CDR data using global factory.
    
    Args:
        data: CDR DataFrame
        file_path: Optional file path for context
        context: Additional validation context
        
    Returns:
        ValidationResult
    """
    return _validation_factory.validate_cdr_data(data, file_path, context)


def validate_kpi_data(
    data: pd.DataFrame,
    file_path: Optional[Union[str, Path]] = None,
    context: Optional[Dict[str, Any]] = None
) -> ValidationResult:
    """Validate KPI data using global factory.
    
    Args:
        data: KPI DataFrame
        file_path: Optional file path for context
        context: Additional validation context
        
    Returns:
        ValidationResult
    """
    return _validation_factory.validate_kpi_data(data, file_path, context)


def validate_cfg_data(
    data: pd.DataFrame,
    file_path: Optional[Union[str, Path]] = None,
    context: Optional[Dict[str, Any]] = None
) -> ValidationResult:
    """Validate configuration data using global factory.
    
    Args:
        data: Configuration DataFrame
        file_path: Optional file path for context
        context: Additional validation context
        
    Returns:
        ValidationResult
    """
    return _validation_factory.validate_cfg_data(data, file_path, context)


def validate_cdr_file(
    file_path: Union[str, Path],
    read_kwargs: Optional[Dict[str, Any]] = None,
    context: Optional[Dict[str, Any]] = None
) -> ValidationResult:
    """Validate CDR file using global factory.
    
    Args:
        file_path: Path to CDR file
        read_kwargs: Additional arguments for file reading
        context: Additional validation context
        
    Returns:
        ValidationResult
    """
    return _validation_factory.validate_cdr_file(file_path, read_kwargs, context)


def validate_kpi_file(
    file_path: Union[str, Path],
    read_kwargs: Optional[Dict[str, Any]] = None,
    context: Optional[Dict[str, Any]] = None
) -> ValidationResult:
    """Validate KPI file using global factory.
    
    Args:
        file_path: Path to KPI file
        read_kwargs: Additional arguments for file reading
        context: Additional validation context
        
    Returns:
        ValidationResult
    """
    return _validation_factory.validate_kpi_file(file_path, read_kwargs, context)


def validate_cfg_file(
    file_path: Union[str, Path],
    read_kwargs: Optional[Dict[str, Any]] = None,
    context: Optional[Dict[str, Any]] = None
) -> ValidationResult:
    """Validate configuration file using global factory.
    
    Args:
        file_path: Path to configuration file
        read_kwargs: Additional arguments for file reading
        context: Additional validation context
        
    Returns:
        ValidationResult
    """
    return _validation_factory.validate_cfg_file(file_path, read_kwargs, context)


def validate_score_data(
    data: pd.DataFrame,
    file_path: Optional[Union[str, Path]] = None,
    context: Optional[Dict[str, Any]] = None
) -> ValidationResult:
    """Validate SCORE data using global factory.
    
    Args:
        data: SCORE DataFrame
        file_path: Optional file path for context
        context: Additional validation context
        
    Returns:
        ValidationResult
    """
    return _validation_factory.validate_score_data(data, file_path, context)


def validate_score_file(
    file_path: Union[str, Path],
    read_kwargs: Optional[Dict[str, Any]] = None,
    context: Optional[Dict[str, Any]] = None
) -> ValidationResult:
    """Validate SCORE file using global factory.
    
    Args:
        file_path: Path to SCORE file
        read_kwargs: Additional arguments for file reading
        context: Additional validation context
        
    Returns:
        ValidationResult
    """
    return _validation_factory.validate_score_file(file_path, read_kwargs, context)


def validate_data(
    data: Any,
    data_type: str,
    file_path: Optional[Union[str, Path]] = None,
    framework_name: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> ValidationResult:
    """Validate data using global factory.
    
    Args:
        data: Data to validate
        data_type: Type of data ('cdr', 'kpi', 'cfg', 'score', 'database')
        file_path: Optional file path for context
        framework_name: Optional custom framework name
        context: Additional validation context
        
    Returns:
        ValidationResult
    """
    return _validation_factory.validate_data(
        data=data,
        data_type=data_type,
        file_path=file_path,
        framework_name=framework_name,
        context=context
    )


def validate_file(
    file_path: Union[str, Path],
    data_type: str,
    read_kwargs: Optional[Dict[str, Any]] = None,
    framework_name: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None
) -> ValidationResult:
    """Validate file using global factory.
    
    Args:
        file_path: Path to data file
        data_type: Type of data ('cdr', 'kpi', 'cfg', 'score')
        read_kwargs: Additional arguments for pandas.read_csv/read_excel
        framework_name: Optional custom framework name
        context: Additional validation context
        
    Returns:
        ValidationResult
    """
    return _validation_factory.validate_file(
        file_path=file_path,
        data_type=data_type,
        read_kwargs=read_kwargs,
        framework_name=framework_name,
        context=context
    )