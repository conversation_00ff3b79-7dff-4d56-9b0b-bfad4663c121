#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect Telecom Data Analysis Platform - Test Helper Utilities

This module provides common test helper functionalities, including:
- Test data generation
- File operation assistance
- Database operation assistance
- API testing assistance
- Performance monitoring assistance
- Security testing assistance

Author: <PERSON><PERSON> <<EMAIL>>
Creation Date: 2024-01-20
"""

import os
import json
import time
import random
import string
import hashlib
import tempfile
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from contextlib import contextmanager

import pandas as pd
import numpy as np
import requests
import psutil
from faker import Faker
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker


# Initialize Faker
fake = Faker() # Using default English locale


@dataclass
class TestResult:
    """Test result data class"""
    test_name: str
    status: str  # 'passed', 'failed', 'skipped'
    duration: float
    error_message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class PerformanceMetrics:
    """Performance metrics data class"""
    cpu_usage: float
    memory_usage: float
    disk_io: float
    network_io: float
    response_time: float
    throughput: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class DataGenerator:
    """Test data generator"""
    
    @staticmethod
    def generate_ep_data(num_records: int = 1000) -> pd.DataFrame:
        """Generate EP (Engineering Parameters) data"""
        data = []
        for _ in range(num_records):
            record = {
                'cell_id': fake.random_int(min=1, max=99999),
                'longitude': fake.longitude(),
                'latitude': fake.latitude(),
                'rsrp': fake.random_int(min=-140, max=-44),
                'rsrq': fake.random_int(min=-20, max=-3),
                'sinr': fake.random_int(min=-20, max=30),
                'pci': fake.random_int(min=0, max=503),
                'earfcn': fake.random_int(min=0, max=65535),
                'ta': fake.random_int(min=0, max=1282),
                'timestamp': fake.date_time_between(start_date='-1y', end_date='now'),
                'operator': fake.random_element(elements=('OperatorA', 'OperatorB', 'OperatorC')),
                'technology': fake.random_element(elements=('4G', '5G')),
                'band': fake.random_element(elements=('B1', 'B3', 'B8', 'B20', 'B38', 'B41'))
            }
            data.append(record)
        
        return pd.DataFrame(data)
    
    @staticmethod
    def generate_cdr_data(num_records: int = 1000) -> pd.DataFrame:
        """Generate CDR (Call Detail Record) data"""
        data = []
        for _ in range(num_records):
            record = {
                'call_id': fake.uuid4(),
                'calling_number': fake.phone_number(),
                'called_number': fake.phone_number(),
                'start_time': fake.date_time_between(start_date='-1y', end_date='now'),
                'duration': fake.random_int(min=1, max=3600),
                'call_type': fake.random_element(elements=('voice', 'sms', 'data')),
                'cell_id_start': fake.random_int(min=1, max=99999),
                'cell_id_end': fake.random_int(min=1, max=99999),
                'longitude_start': fake.longitude(),
                'latitude_start': fake.latitude(),
                'longitude_end': fake.longitude(),
                'latitude_end': fake.latitude(),
                'data_volume': fake.random_int(min=0, max=1000000),  # bytes
                'charging_amount': fake.random_int(min=0, max=10000) / 100,  # currency unit
                'roaming_flag': fake.boolean(),
                'service_type': fake.random_element(elements=('voice', 'video', 'web', 'app'))
            }
            data.append(record)
        
        return pd.DataFrame(data)
    
    @staticmethod
    def generate_site_data(num_records: int = 100) -> pd.DataFrame:
        """Generate site data"""
        data = []
        for _ in range(num_records):
            record = {
                'site_id': fake.random_int(min=1, max=9999),
                'site_name': f"Site_{fake.city()}",
                'longitude': fake.longitude(),
                'latitude': fake.latitude(),
                'height': fake.random_int(min=10, max=200),
                'azimuth': fake.random_int(min=0, max=359),
                'tilt': fake.random_int(min=-10, max=10),
                'power': fake.random_int(min=10, max=60),
                'frequency': fake.random_int(min=800, max=2600),
                'technology': fake.random_element(elements=('4G', '5G')),
                'operator': fake.random_element(elements=('OperatorX', 'OperatorY', 'OperatorZ')),
                'coverage_radius': fake.random_int(min=500, max=5000),
                'capacity': fake.random_int(min=100, max=1000),
                'status': fake.random_element(elements=('active', 'inactive', 'maintenance')),
                'installation_date': fake.date_between(start_date='-5y', end_date='now')
            }
            data.append(record)
        
        return pd.DataFrame(data)
    
    @staticmethod
    def generate_kpi_data(num_records: int = 1000) -> pd.DataFrame:
        """Generate KPI (Key Performance Indicator) data"""
        data = []
        for _ in range(num_records):
            record = {
                'timestamp': fake.date_time_between(start_date='-1y', end_date='now'),
                'cell_id': fake.random_int(min=1, max=99999),
                'site_id': fake.random_int(min=1, max=9999),
                'accessibility': fake.random_int(min=90, max=100) / 100,
                'retainability': fake.random_int(min=95, max=100) / 100,
                'mobility': fake.random_int(min=90, max=100) / 100,
                'integrity': fake.random_int(min=95, max=100) / 100,
                'availability': fake.random_int(min=98, max=100) / 100,
                'throughput_dl': fake.random_int(min=10, max=1000),  # Mbps
                'throughput_ul': fake.random_int(min=5, max=100),    # Mbps
                'latency': fake.random_int(min=10, max=100),         # ms
                'packet_loss': fake.random_int(min=0, max=5) / 100,
                'jitter': fake.random_int(min=1, max=50),            # ms
                'active_users': fake.random_int(min=0, max=500),
                'data_volume': fake.random_int(min=0, max=10000),    # GB
                'voice_erlang': fake.random_int(min=0, max=100) / 10
            }
            data.append(record)
        
        return pd.DataFrame(data)
    
    @staticmethod
    def generate_large_dataset(num_records: int = 5000000, 
                             data_type: str = 'ep') -> pd.DataFrame:
        """Generate large dataset"""
        if data_type == 'ep':
            return DataGenerator.generate_ep_data(num_records)
        elif data_type == 'cdr':
            return DataGenerator.generate_cdr_data(num_records)
        elif data_type == 'site':
            return DataGenerator.generate_site_data(num_records)
        elif data_type == 'kpi':
            return DataGenerator.generate_kpi_data(num_records)
        else:
            raise ValueError(f"Unsupported data type: {data_type}")


class FileHelper:
    """File operation helper class"""
    
    @staticmethod
    def create_test_file(data: pd.DataFrame, 
                        file_format: str = 'csv',
                        file_path: Optional[str] = None) -> str:
        """Create test file"""
        if file_path is None:
            suffix = f'.{file_format}'
            with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, 
                                           delete=False) as f:
                file_path = f.name
        
        if file_format.lower() == 'csv':
            data.to_csv(file_path, index=False, encoding='utf-8')
        elif file_format.lower() in ['xlsx', 'excel']:
            data.to_excel(file_path, index=False)
        elif file_format.lower() == 'json':
            data.to_json(file_path, orient='records', force_ascii=False)
        elif file_format.lower() == 'parquet':
            data.to_parquet(file_path, index=False)
        else:
            raise ValueError(f"Unsupported file format: {file_format}")
        
        return file_path
    
    @staticmethod
    def create_corrupted_file(file_path: str, 
                            corruption_type: str = 'truncate') -> str:
        """Create corrupted file"""
        corrupted_path = file_path.replace('.', '_corrupted.')
        
        if corruption_type == 'truncate':
            # Truncate file
            with open(file_path, 'rb') as src:
                data = src.read()
            with open(corrupted_path, 'wb') as dst:
                dst.write(data[:len(data)//2])
        
        elif corruption_type == 'random_bytes':
            # Insert random bytes
            with open(file_path, 'rb') as src:
                data = bytearray(src.read())
            # Insert random bytes at random positions
            for _ in range(10):
                pos = random.randint(0, len(data)-1)
                data[pos] = random.randint(0, 255)
            with open(corrupted_path, 'wb') as dst:
                dst.write(data)
        
        elif corruption_type == 'empty':
            # Create empty file
            Path(corrupted_path).touch()
        
        return corrupted_path
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """Get file information"""
        path = Path(file_path)
        if not path.exists():
            return {'exists': False}
        
        stat = path.stat()
        return {
            'exists': True,
            'size': stat.st_size,
            'created': datetime.fromtimestamp(stat.st_ctime),
            'modified': datetime.fromtimestamp(stat.st_mtime),
            'extension': path.suffix,
            'name': path.name,
            'parent': str(path.parent)
        }
    
    @staticmethod
    @contextmanager
    def temporary_files(*file_paths):
        """Temporary files context manager"""
        try:
            yield
        finally:
            for file_path in file_paths:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception:
                    pass


class DatabaseHelper:
    """Database operation helper class"""
    
    def __init__(self, connection_string: str):
        self.engine = create_engine(connection_string)
        self.Session = sessionmaker(bind=self.engine)
    
    def execute_query(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """Execute query"""
        with self.Session() as session:
            result = session.execute(text(query), params or {})
            return [dict(row) for row in result]
    
    def execute_script(self, script: str) -> None:
        """Execute script"""
        with self.Session() as session:
            session.execute(text(script))
            session.commit()
    
    def table_exists(self, table_name: str) -> bool:
        """Check if table exists"""
        query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_name = :table_name
        )
        """
        result = self.execute_query(query, {'table_name': table_name})
        return result[0]['exists'] if result else False
    
    def get_table_count(self, table_name: str) -> int:
        """Get table record count"""
        query = f"SELECT COUNT(*) as count FROM {table_name}"
        result = self.execute_query(query)
        return result[0]['count'] if result else 0
    
    def truncate_table(self, table_name: str) -> None:
        """Truncate table"""
        self.execute_script(f"TRUNCATE TABLE {table_name}")
    
    def drop_table(self, table_name: str) -> None:
        """Drop table"""
        self.execute_script(f"DROP TABLE IF EXISTS {table_name}")
    
    @contextmanager
    def transaction(self):
        """Transaction context manager"""
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()


class APIHelper:
    """API testing helper class"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.token = None
    
    def login(self, username: str, password: str, 
              endpoint: str = '/api/auth/login') -> bool:
        """Login to get token"""
        try:
            response = self.post(endpoint, {
                'username': username,
                'password': password
            })
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('access_token')
                if self.token:
                    self.session.headers.update({
                        'Authorization': f'Bearer {self.token}'
                    })
                    return True
            return False
        except Exception:
            return False
    
    def logout(self, endpoint: str = '/api/auth/logout') -> bool:
        """Logout"""
        try:
            response = self.post(endpoint)
            if response.status_code in [200, 204]:
                self.token = None
                self.session.headers.pop('Authorization', None)
                return True
            return False
        except Exception:
            return False
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> requests.Response:
        """GET request"""
        url = f"{self.base_url}{endpoint}"
        return self.session.get(url, params=params, timeout=self.timeout)
    
    def post(self, endpoint: str, data: Optional[Dict] = None, 
             files: Optional[Dict] = None) -> requests.Response:
        """POST request"""
        url = f"{self.base_url}{endpoint}"
        if files:
            return self.session.post(url, data=data, files=files, timeout=self.timeout)
        else:
            return self.session.post(url, json=data, timeout=self.timeout)
    
    def put(self, endpoint: str, data: Optional[Dict] = None) -> requests.Response:
        """PUT request"""
        url = f"{self.base_url}{endpoint}"
        return self.session.put(url, json=data, timeout=self.timeout)
    
    def delete(self, endpoint: str) -> requests.Response:
        """DELETE request"""
        url = f"{self.base_url}{endpoint}"
        return self.session.delete(url, timeout=self.timeout)
    
    def upload_file(self, endpoint: str, file_path: str, 
                   field_name: str = 'file') -> requests.Response:
        """Upload file"""
        with open(file_path, 'rb') as f:
            files = {field_name: f}
            return self.post(endpoint, files=files)
    
    def download_file(self, endpoint: str, save_path: str) -> bool:
        """Download file"""
        try:
            response = self.get(endpoint)
            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    f.write(response.content)
                return True
            return False
        except Exception:
            return False
    
    def check_health(self, endpoint: str = '/api/health') -> bool:
        """Check service health status"""
        try:
            response = self.get(endpoint)
            return response.status_code == 200
        except Exception:
            return False


class PerformanceMonitor:
    """Performance monitor"""
    
    def __init__(self, interval: float = 1.0):
        self.interval = interval
        self.metrics = []
        self.monitoring = False
        self.start_time = None
    
    def start_monitoring(self):
        """Start monitoring"""
        self.monitoring = True
        self.start_time = time.time()
        self.metrics = []
    
    def stop_monitoring(self):
        """Stop monitoring"""
        self.monitoring = False
    
    def collect_metrics(self) -> PerformanceMetrics:
        """Collect performance metrics"""
        cpu_percent = psutil.cpu_percent(interval=None)
        memory = psutil.virtual_memory()
        disk_io = psutil.disk_io_counters()
        network_io = psutil.net_io_counters()
        
        return PerformanceMetrics(
            cpu_usage=cpu_percent,
            memory_usage=memory.percent,
            disk_io=disk_io.read_bytes + disk_io.write_bytes if disk_io else 0,
            network_io=network_io.bytes_sent + network_io.bytes_recv if network_io else 0,
            response_time=0.0,  # Needs to be set externally
            throughput=0.0,     # Needs to be set externally
            timestamp=datetime.now()
        )
    
    async def monitor_async(self):
        """Asynchronous monitoring"""
        while self.monitoring:
            metrics = self.collect_metrics()
            self.metrics.append(metrics)
            await asyncio.sleep(self.interval)
    
    def get_average_metrics(self) -> Optional[PerformanceMetrics]:
        """Get average performance metrics"""
        if not self.metrics:
            return None
        
        avg_cpu = sum(m.cpu_usage for m in self.metrics) / len(self.metrics)
        avg_memory = sum(m.memory_usage for m in self.metrics) / len(self.metrics)
        avg_disk_io = sum(m.disk_io for m in self.metrics) / len(self.metrics)
        avg_network_io = sum(m.network_io for m in self.metrics) / len(self.metrics)
        avg_response_time = sum(m.response_time for m in self.metrics) / len(self.metrics)
        avg_throughput = sum(m.throughput for m in self.metrics) / len(self.metrics)
        
        return PerformanceMetrics(
            cpu_usage=avg_cpu,
            memory_usage=avg_memory,
            disk_io=avg_disk_io,
            network_io=avg_network_io,
            response_time=avg_response_time,
            throughput=avg_throughput,
            timestamp=datetime.now()
        )
    
    def get_peak_metrics(self) -> Optional[PerformanceMetrics]:
        """Get peak performance metrics"""
        if not self.metrics:
            return None
        
        max_cpu = max(m.cpu_usage for m in self.metrics)
        max_memory = max(m.memory_usage for m in self.metrics)
        max_disk_io = max(m.disk_io for m in self.metrics)
        max_network_io = max(m.network_io for m in self.metrics)
        max_response_time = max(m.response_time for m in self.metrics)
        max_throughput = max(m.throughput for m in self.metrics)
        
        return PerformanceMetrics(
            cpu_usage=max_cpu,
            memory_usage=max_memory,
            disk_io=max_disk_io,
            network_io=max_network_io,
            response_time=max_response_time,
            throughput=max_throughput,
            timestamp=datetime.now()
        )


class SecurityHelper:
    """Security testing helper class"""
    
    @staticmethod
    def generate_sql_injection_payloads() -> List[str]:
        """Generate SQL injection test payloads"""
        return [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "'; SELECT * FROM users; --",
            "' UNION SELECT username, password FROM users --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' OR 1=1 --",
            "'; EXEC xp_cmdshell('dir'); --",
            "' AND (SELECT COUNT(*) FROM users) > 0 --"
        ]
    
    @staticmethod
    def generate_xss_payloads() -> List[str]:
        """Generate XSS test payloads"""
        return [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>"
        ]
    
    @staticmethod
    def generate_weak_passwords() -> List[str]:
        """Generate weak password list"""
        return [
            "password", "123456", "admin", "test", "guest",
            "password123", "admin123", "qwerty", "abc123",
            "111111", "000000", "root", "user", "login"
        ]
    
    @staticmethod
    def generate_random_string(length: int = 10) -> str:
        """Generate random string"""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))
    
    @staticmethod
    def hash_password(password: str, algorithm: str = 'sha256') -> str:
        """Password hashing"""
        if algorithm == 'md5':
            return hashlib.md5(password.encode()).hexdigest()
        elif algorithm == 'sha1':
            return hashlib.sha1(password.encode()).hexdigest()
        elif algorithm == 'sha256':
            return hashlib.sha256(password.encode()).hexdigest()
        else:
            raise ValueError(f"Unsupported hashing algorithm: {algorithm}")
    
    @staticmethod
    def check_password_strength(password: str) -> Dict[str, Any]:
        """Check password strength"""
        score = 0
        feedback = []
        
        # Length check
        if len(password) >= 8:
            score += 1
        else:
            feedback.append("Password must be at least 8 characters long")
        
        # Contains uppercase letter
        if any(c.isupper() for c in password):
            score += 1
        else:
            feedback.append("Should contain uppercase letters")
        
        # Contains lowercase letter
        if any(c.islower() for c in password):
            score += 1
        else:
            feedback.append("Should contain lowercase letters")
        
        # Contains digit
        if any(c.isdigit() for c in password):
            score += 1
        else:
            feedback.append("Should contain digits")
        
        # Contains special character
        if any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password):
            score += 1
        else:
            feedback.append("Should contain special characters")
        
        # Rating
        if score >= 4:
            strength = "Strong"
        elif score >= 3:
            strength = "Medium"
        elif score >= 2:
            strength = "Weak"
        else:
            strength = "Very Weak"
        
        return {
            'score': score,
            'strength': strength,
            'feedback': feedback
        }


class _TestReporter:
    """Test report generator"""
    
    def __init__(self, output_dir: str = "tests/reports"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.results = []
    
    def add_result(self, result: TestResult):
        """Add test result"""
        self.results.append(result)
    
    def generate_json_report(self, filename: str = None) -> str:
        """Generate JSON report"""
        if filename is None:
            filename = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_path = self.output_dir / filename
        
        report_data = {
            'summary': self._get_summary(),
            'results': [asdict(result) for result in self.results],
            'generated_at': datetime.now().isoformat()
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
        
        return str(report_path)
    
    def generate_html_report(self, filename: str = None) -> str:
        """Generate HTML report"""
        if filename is None:
            filename = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        report_path = self.output_dir / filename
        summary = self._get_summary()
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Connect Test Report</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .summary {{ background: #f5f5f5; padding: 15px; border-radius: 5px; }}
                .passed {{ color: green; }}
                .failed {{ color: red; }}
                .skipped {{ color: orange; }}
                table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <h1>Connect Telecom Data Analysis Platform - Test Report</h1>
            
            <div class="summary">
                <h2>Test Summary</h2>
                <p>Total Tests: {summary['total']}</p>
                <p class="passed">Passed: {summary['passed']}</p>
                <p class="failed">Failed: {summary['failed']}</p>
                <p class="skipped">Skipped: {summary['skipped']}</p>
                <p>Success Rate: {summary['success_rate']:.2f}%</p>
                <p>Total Duration: {summary['total_duration']:.2f}s</p>
            </div>
            
            <h2>Detailed Results</h2>
            <table>
                <tr>
                    <th>Test Name</th>
                    <th>Status</th>
                    <th>Duration (s)</th>
                    <th>Error Message</th>
                </tr>
        """
        
        for result in self.results:
            status_class = result.status
            error_msg = result.error_message or "-"
            html_content += f"""
                <tr>
                    <td>{result.test_name}</td>
                    <td class="{status_class}">{result.status}</td>
                    <td>{result.duration:.3f}</td>
                    <td>{error_msg}</td>
                </tr>
            """
        
        html_content += """
            </table>
        </body>
        </html>
        """
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return str(report_path)
    
    def _get_summary(self) -> Dict[str, Any]:
        """Get test summary"""
        total = len(self.results)
        passed = sum(1 for r in self.results if r.status == 'passed')
        failed = sum(1 for r in self.results if r.status == 'failed')
        skipped = sum(1 for r in self.results if r.status == 'skipped')
        
        success_rate = (passed / total * 100) if total > 0 else 0
        total_duration = sum(r.duration for r in self.results)
        
        return {
            'total': total,
            'passed': passed,
            'failed': failed,
            'skipped': skipped,
            'success_rate': success_rate,
            'total_duration': total_duration
        }


# Utility functions
def wait_for_condition(condition_func, timeout: int = 30, 
                      interval: float = 1.0) -> bool:
    """Wait for condition to be met"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if condition_func():
            return True
        time.sleep(interval)
    return False


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """Retry on failure decorator"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        time.sleep(delay)
                    continue
            raise last_exception
        return wrapper
    return decorator


def measure_execution_time(func):
    """Measure execution time decorator"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            return result, duration
        except Exception as e:
            duration = time.time() - start_time
            raise e
    return wrapper


if __name__ == "__main__":
    # Test data generator
    print("Generating test data...")
    ep_data = DataGenerator.generate_ep_data(100)
    print(f"Generated EP data: {len(ep_data)} records")
    
    # Create test file
    test_file = FileHelper.create_test_file(ep_data, 'csv')
    print(f"Created test file: {test_file}")
    
    # File information
    file_info = FileHelper.get_file_info(test_file)
    print(f"File information: {file_info}")
    
    # Cleanup
    os.remove(test_file)
    print("Testing complete")