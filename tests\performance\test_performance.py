"""Performance tests for the database framework."""

"""Performance tests for the Connect database framework.

This module contains comprehensive performance tests to ensure the system meets
performance requirements under various load conditions as specified in Task 22.
"""

import asyncio
import gc
import multiprocessing
import os
import tempfile
import threading
import time
from concurrent.futures import <PERSON><PERSON>oolExecutor, ThreadPoolExecutor
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import List, Dict, Any
from unittest.mock import MagicMock, Mock, patch, AsyncMock

import numpy as np
import pandas as pd
import psutil
import pytest
from memory_profiler import profile

# Import components for performance testing
from src.database.connection import (
    DatabasePoolManager,
    ReadWriteSplitter,
    SessionManager,
)
from src.database.monitoring import MetricsCollector, PerformanceLogger
from src.database.operations import BulkOperations, CRUDOperations, QueryBuilder
from src.exporters.csv_exporter import CSVExporter
from src.exporters.excel_exporter import ExcelExporter
# from src.geospatial.processor import GeospatialProcessor
from src.importers.cdr_importer import CDRImporter
from src.importers.ep_importer import EPImporter
from src.utils.cache_manager import <PERSON>ache<PERSON>anager


@pytest.mark.performance
class TestConnectionPoolPerformance:
    """Performance tests for connection pool."""

    @pytest.fixture
    def mock_engine(self):
        """Create mock database engine for performance testing."""
        engine = Mock()
        # Mock connection that can be used multiple times
        mock_conn = Mock()
        engine.connect.return_value.__enter__ = Mock(return_value=mock_conn)
        engine.connect.return_value.__exit__ = Mock(return_value=None)
        return engine

    @pytest.fixture
    async def connection_pool(self, mock_engine, test_config):
        """Create connection pool for performance testing."""
        with patch("asyncpg.create_pool", return_value=mock_engine):
            pool = DatabasePoolManager(test_config)
            await pool.initialize_pool()
            yield pool
            if hasattr(pool, "close"):
                await pool.close()

    @pytest.mark.asyncio
    async def test_connection_acquisition_speed(self, connection_pool):
        """Test connection acquisition speed."""
        num_acquisitions = 100  # Reduced for async testing
        start_time = time.time()

        for _ in range(num_acquisitions):
            conn = await connection_pool.acquire_connection()
            await connection_pool.return_connection(conn)

        end_time = time.time()
        total_time = end_time - start_time
        avg_time_per_acquisition = total_time / num_acquisitions

        # Should be able to acquire connections quickly (< 10ms per acquisition for async)
        assert (
            avg_time_per_acquisition < 0.01
        ), f"Average acquisition time: {avg_time_per_acquisition:.4f}s"

        print(f"Connection acquisition performance:")
        print(f"  Total acquisitions: {num_acquisitions}")
        print(f"  Total time: {total_time:.4f}s")
        print(f"  Average time per acquisition: {avg_time_per_acquisition:.6f}s")

    @pytest.mark.asyncio
    async def test_concurrent_connection_usage(self, connection_pool):
        """Test concurrent connection usage performance."""
        num_tasks = 10  # Reduced for async testing
        operations_per_task = 10
        results = []
        errors = []

        async def worker():
            """Worker function for concurrent connection testing."""
            task_start = time.time()
            try:
                for _ in range(operations_per_task):
                    conn = await connection_pool.acquire_connection()
                    # Simulate some work
                    await asyncio.sleep(0.001)  # 1ms work simulation
                    await connection_pool.return_connection(conn)

                task_end = time.time()
                results.append(task_end - task_start)
            except Exception as e:
                errors.append(str(e))

        # Start concurrent workers
        start_time = time.time()
        tasks = []
        for _ in range(num_tasks):
            task = asyncio.create_task(worker())
            tasks.append(task)

        # Wait for completion
        await asyncio.gather(*tasks)

        end_time = time.time()
        total_time = end_time - start_time

        # Verify no errors occurred
        assert len(errors) == 0, f"Errors occurred: {errors}"

        # Calculate statistics
        total_operations = num_tasks * operations_per_task
        avg_task_time = sum(results) / len(results) if results else 0
        operations_per_second = total_operations / total_time if total_time > 0 else 0

        print(f"Concurrent connection performance:")
        print(f"  Tasks: {num_tasks}")
        print(f"  Operations per task: {operations_per_task}")
        print(f"  Total operations: {total_operations}")
        print(f"  Total time: {total_time:.4f}s")
        print(f"  Average task time: {avg_task_time:.4f}s")
        print(f"  Operations per second: {operations_per_second:.2f}")

        # Should handle at least 100 operations per second for async
        assert operations_per_second > 100

    @pytest.mark.asyncio
    async def test_connection_pool_scaling(self, test_config):
        """Test connection pool scaling with different pool sizes."""
        pool_sizes = [5, 10]  # Reduced for async testing
        results = {}

        for pool_size in pool_sizes:
            # Create a new config with different pool size
            from src.config.models import ConnectConfig as Config
            config_dict = {
                "database": {
                    "host": "localhost",
                    "port": 5432,
                    "name": "test_db",
                    "user": "test_user",
                    "password": "test_pass"
                },
                "pool": {
                    "min_size": pool_size,
                    "max_size": pool_size * 2,
                    "timeout": 30
                }
            }
            config = Config(config_dict)

            with patch("asyncpg.create_pool") as mock_create:
                mock_pool = Mock()
                mock_pool.acquire = AsyncMock()
                mock_pool.release = AsyncMock()
                mock_create.return_value = mock_pool

                pool = DatabasePoolManager(config)
                await pool.initialize_pool()

                # Test performance with this pool size
                start_time = time.time()

                async def worker():
                    for _ in range(10):  # Reduced operations
                        conn = await pool.acquire_connection()
                        await asyncio.sleep(0.001)
                        await pool.return_connection(conn)

                tasks = []
                for _ in range(5):  # Reduced concurrent tasks
                    task = asyncio.create_task(worker())
                    tasks.append(task)

                await asyncio.gather(*tasks)

                end_time = time.time()
                results[pool_size] = end_time - start_time

                await pool.close()

        print(f"Connection pool scaling results:")
        for pool_size, time_taken in results.items():
            print(f"  Pool size {pool_size}: {time_taken:.4f}s")

        # Larger pools should generally perform better (or at least not much worse)
        if len(results) >= 2:
            pool_sizes_sorted = sorted(results.keys())
            assert results[pool_sizes_sorted[-1]] <= results[pool_sizes_sorted[0]] * 2  # Allow some overhead


@pytest.mark.performance
class TestBulkOperationsPerformance:
    """Performance tests for bulk operations."""

    @pytest.fixture
    def mock_connection(self):
        """Create mock connection for bulk operations."""
        conn = Mock()
        conn.execute.return_value = Mock()
        conn.executemany.return_value = Mock()
        return conn

    @pytest.fixture
    def bulk_operations(self, mock_connection):
        """Create bulk operations instance."""
        # Create a mock session manager that works synchronously
        mock_session_manager = Mock()

        # Create a proper context manager mock that doesn't return a coroutine
        class MockSyncContextManager:
            def __init__(self, connection):
                self.connection = connection

            def __enter__(self):
                return self.connection

            def __exit__(self, exc_type, exc_val, exc_tb):
                return None

        # Make get_connection return a sync context manager, not a coroutine
        mock_session_manager.get_connection.return_value = MockSyncContextManager(mock_connection)

        # Create BulkOperations with the properly mocked session manager
        bulk_ops = BulkOperations(mock_session_manager)

        # Mock pandas DataFrame.to_sql method to avoid actual database operations
        with patch('pandas.DataFrame.to_sql', return_value=None) as mock_to_sql:
            mock_to_sql.return_value = None
            yield bulk_ops

    def test_bulk_insert_performance(self, bulk_operations):
        """Test bulk insert performance with different data sizes."""
        data_sizes = [1000, 10000, 100000]
        results = {}

        for size in data_sizes:
            # Generate test data
            test_data = pd.DataFrame(
                {
                    "id": range(size),
                    "name": [f"Name_{i}" for i in range(size)],
                    "value": np.random.uniform(0, 1000, size),
                    "category": np.random.choice(["A", "B", "C", "D"], size),
                    "timestamp": pd.date_range("2023-01-01", periods=size, freq="min"),
                }
            )

            # Measure bulk insert performance
            start_time = time.time()

            success = bulk_operations.bulk_insert_dataframe("test_table", test_data)

            end_time = time.time()
            insert_time = end_time - start_time

            assert success
            results[size] = {
                "time": insert_time,
                "records_per_second": size / insert_time,
            }

        print(f"Bulk insert performance:")
        for size, metrics in results.items():
            print(
                f"  {size:,} records: {metrics['time']:.4f}s ({metrics['records_per_second']:.0f} records/sec)"
            )

        # Should handle at least 10,000 records per second for large datasets
        assert results[100000]["records_per_second"] > 10000

    def test_chunked_processing_performance(self, bulk_operations):
        """Test chunked processing performance."""
        chunk_sizes = [1000, 5000, 10000]
        total_records = 50000
        results = {}

        # Generate large test dataset
        large_dataset = pd.DataFrame(
            {
                "id": range(total_records),
                "data": ["x" * 100] * total_records,  # Larger data per record
                "value": np.random.uniform(0, 1000, total_records),
            }
        )

        for chunk_size in chunk_sizes:
            start_time = time.time()

            # Process in chunks
            chunks_processed = 0
            for chunk_start in range(0, total_records, chunk_size):
                chunk_end = min(chunk_start + chunk_size, total_records)
                chunk_data = large_dataset.iloc[chunk_start:chunk_end]

                success = bulk_operations.bulk_insert_dataframe(
                    "test_table", chunk_data
                )
                assert success
                chunks_processed += 1

            end_time = time.time()
            processing_time = end_time - start_time

            results[chunk_size] = {
                "time": processing_time,
                "chunks": chunks_processed,
                "records_per_second": total_records / processing_time,
            }

        print(f"Chunked processing performance:")
        for chunk_size, metrics in results.items():
            print(
                f"  Chunk size {chunk_size}: {metrics['time']:.4f}s, {metrics['chunks']} chunks, {metrics['records_per_second']:.0f} records/sec"
            )

        # Find optimal chunk size (should be somewhere in the middle)
        best_chunk_size = max(
            results.keys(), key=lambda x: results[x]["records_per_second"]
        )
        print(f"  Best performing chunk size: {best_chunk_size}")

    def test_memory_usage_during_bulk_operations(self, bulk_operations):
        """Test memory usage during bulk operations."""
        process = psutil.Process()
        initial_memory = process.memory_info().rss

        # Generate large dataset
        large_size = 100000
        test_data = pd.DataFrame(
            {
                "id": range(large_size),
                "large_text": ["x" * 1000] * large_size,  # 1KB per record
                "numbers": np.random.uniform(0, 1000, large_size),
            }
        )

        # Monitor memory during operation
        memory_samples = []

        def memory_monitor():
            """Monitor memory usage in background."""
            for _ in range(50):  # Sample for 5 seconds
                memory_samples.append(process.memory_info().rss)
                time.sleep(0.1)

        # Start memory monitoring
        monitor_thread = threading.Thread(target=memory_monitor)
        monitor_thread.start()

        # Perform bulk operation
        start_time = time.time()
        success = bulk_operations.bulk_insert_dataframe("test_table", test_data)
        end_time = time.time()

        # Wait for monitoring to complete
        monitor_thread.join()

        assert success

        # Analyze memory usage
        max_memory = max(memory_samples)
        avg_memory = sum(memory_samples) / len(memory_samples)
        memory_increase = max_memory - initial_memory

        print(f"Memory usage during bulk operations:")
        print(f"  Initial memory: {initial_memory / 1024 / 1024:.2f} MB")
        print(f"  Max memory: {max_memory / 1024 / 1024:.2f} MB")
        print(f"  Average memory: {avg_memory / 1024 / 1024:.2f} MB")
        print(f"  Memory increase: {memory_increase / 1024 / 1024:.2f} MB")
        print(f"  Processing time: {end_time - start_time:.4f}s")

        # Memory increase should be reasonable (less than 500MB for 100K records)
        assert memory_increase < 500 * 1024 * 1024


@pytest.mark.performance
class TestImporterPerformance:
    """Performance tests for data importers."""

    @pytest.fixture
    def large_ep_file(self, tmp_path):
        """Create large EP data file for performance testing."""
        # Generate 100,000 records
        num_records = 100000

        ep_data = pd.DataFrame(
            {
                "timestamp": pd.date_range(
                    "2023-01-01", periods=num_records, freq="min"
                ),
                "meter_id": [f"M{i:06d}" for i in range(num_records)],
                "energy_kwh": np.random.uniform(10, 100, num_records),
                "power_kw": np.random.uniform(5, 50, num_records),
                "voltage": np.random.uniform(220, 240, num_records),
                "current": np.random.uniform(10, 30, num_records),
                "power_factor": np.random.uniform(0.8, 1.0, num_records),
            }
        )

        file_path = tmp_path / "large_ep_data.csv"
        ep_data.to_csv(file_path, index=False)
        return str(file_path)

    @pytest.fixture
    def large_cdr_file(self, tmp_path):
        """Create large CDR data file for performance testing."""
        # Generate 50,000 records
        num_records = 50000

        cdr_data = pd.DataFrame(
            {
                "call_id": [f"CALL_{i:08d}" for i in range(num_records)],
                "caller_number": [f"+1555{i:07d}" for i in range(num_records)],
                "callee_number": [f"+1666{i:07d}" for i in range(num_records)],
                "start_time": pd.date_range(
                    "2023-01-01", periods=num_records, freq="30s"
                ),
                "end_time": pd.date_range(
                    "2023-01-01 00:01:00", periods=num_records, freq="30s"
                ),
                "duration_seconds": np.random.randint(10, 3600, num_records),
                "call_type": np.random.choice(
                    ["voice", "video", "conference"], num_records
                ),
                "status": np.random.choice(
                    ["completed", "failed", "busy"], num_records, p=[0.8, 0.1, 0.1]
                ),
                "bytes_transferred": np.random.randint(1000, 1000000, num_records),
            }
        )

        file_path = tmp_path / "large_cdr_data.csv"
        cdr_data.to_csv(file_path, index=False)
        return str(file_path)

    def test_ep_importer_performance(self, large_ep_file, test_config):
        """Test EP importer performance with large files."""
        # Mock database components
        with patch("src.database.connection.SessionManager") as mock_session:
            with patch("src.database.operations.BulkOperations") as mock_bulk:
                mock_session.return_value.get_connection.return_value.__enter__ = Mock()
                mock_session.return_value.get_connection.return_value.__exit__ = Mock()
                mock_bulk.return_value.bulk_insert_dataframe.return_value = True

                # Create importer with performance monitoring
                performance_logger = PerformanceLogger()
                ep_importer = EPImporter(
                    test_config, performance_logger=performance_logger
                )

                # Measure import performance
                start_time = time.time()
                result = ep_importer.import_file(large_ep_file)
                end_time = time.time()

                import_time = end_time - start_time
                records_per_second = result.records_processed / import_time

                print(f"EP Importer Performance:")
                print(f"  Records processed: {result.records_processed:,}")
                print(f"  Import time: {import_time:.4f}s")
                print(f"  Records per second: {records_per_second:.0f}")
                print(
                    f"  Memory usage: {psutil.Process().memory_info().rss / 1024 / 1024:.2f} MB"
                )

                assert result.success
                # Should process at least 5,000 records per second
                assert records_per_second > 5000

    def test_cdr_importer_performance(self, large_cdr_file, test_config):
        """Test CDR importer performance with large files."""
        with patch("src.database.connection.SessionManager") as mock_session:
            with patch("src.database.operations.BulkOperations") as mock_bulk:
                mock_session.return_value.get_connection.return_value.__enter__ = Mock()
                mock_session.return_value.get_connection.return_value.__exit__ = Mock()
                mock_bulk.return_value.bulk_insert_dataframe.return_value = True

                cdr_importer = CDRImporter(test_config)

                start_time = time.time()
                result = cdr_importer.import_file(large_cdr_file)
                end_time = time.time()

                import_time = end_time - start_time
                records_per_second = result.records_processed / import_time

                print(f"CDR Importer Performance:")
                print(f"  Records processed: {result.records_processed:,}")
                print(f"  Import time: {import_time:.4f}s")
                print(f"  Records per second: {records_per_second:.0f}")

                assert result.success
                assert records_per_second > 3000

    def test_concurrent_imports(self, large_ep_file, large_cdr_file, test_config):
        """Test concurrent import performance."""
        results = []
        errors = []

        def import_worker(file_path, importer_class):
            """Worker function for concurrent imports."""
            try:
                with patch("src.database.connection.SessionManager") as mock_session:
                    with patch("src.database.operations.BulkOperations") as mock_bulk:
                        mock_session.return_value.get_connection.return_value.__enter__ = (
                            Mock()
                        )
                        mock_session.return_value.get_connection.return_value.__exit__ = (
                            Mock()
                        )
                        mock_bulk.return_value.bulk_insert_dataframe.return_value = True

                        importer = importer_class(test_config)
                        start_time = time.time()
                        result = importer.import_file(file_path)
                        end_time = time.time()

                        results.append(
                            {
                                "importer": importer_class.__name__,
                                "records": result.records_processed,
                                "time": end_time - start_time,
                                "success": result.success,
                            }
                        )
            except Exception as e:
                errors.append(str(e))

        # Start concurrent imports
        start_time = time.time()
        threads = [
            threading.Thread(target=import_worker, args=(large_ep_file, EPImporter)),
            threading.Thread(target=import_worker, args=(large_cdr_file, CDRImporter)),
        ]

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        end_time = time.time()
        total_time = end_time - start_time

        assert len(errors) == 0, f"Errors occurred: {errors}"
        assert len(results) == 2

        total_records = sum(r["records"] for r in results)
        overall_records_per_second = total_records / total_time

        print(f"Concurrent Import Performance:")
        print(f"  Total time: {total_time:.4f}s")
        print(f"  Total records: {total_records:,}")
        print(f"  Overall records per second: {overall_records_per_second:.0f}")

        for result in results:
            print(
                f"  {result['importer']}: {result['records']:,} records in {result['time']:.4f}s"
            )

        # Concurrent processing should be efficient
        assert overall_records_per_second > 8000


@pytest.mark.performance
class TestExporterPerformance:
    """Performance tests for data exporters."""

    @pytest.fixture
    def large_dataset(self):
        """Create large dataset for export testing."""
        num_records = 100000
        return pd.DataFrame(
            {
                "id": range(num_records),
                "timestamp": pd.date_range(
                    "2023-01-01", periods=num_records, freq="min"
                ),
                "name": [f"Record_{i}" for i in range(num_records)],
                "value": np.random.uniform(0, 1000, num_records),
                "category": np.random.choice(["A", "B", "C", "D", "E"], num_records),
                "description": ["Description text " * 10]
                * num_records,  # Larger text fields
                "flag": np.random.choice([True, False], num_records),
            }
        )

    def test_csv_export_performance(self, large_dataset, tmp_path, test_config):
        """Test CSV export performance."""
        csv_exporter = CSVExporter(test_config)
        output_file = tmp_path / "large_export.csv"

        start_time = time.time()
        result = csv_exporter.export_dataframe(large_dataset, str(output_file))
        end_time = time.time()

        export_time = end_time - start_time
        records_per_second = len(large_dataset) / export_time
        file_size = output_file.stat().st_size

        print(f"CSV Export Performance:")
        print(f"  Records exported: {len(large_dataset):,}")
        print(f"  Export time: {export_time:.4f}s")
        print(f"  Records per second: {records_per_second:.0f}")
        print(f"  File size: {file_size / 1024 / 1024:.2f} MB")
        print(f"  Export rate: {file_size / 1024 / 1024 / export_time:.2f} MB/s")

        assert result.success
        assert records_per_second > 10000  # Should export at least 10K records/sec

    def test_excel_export_performance(self, large_dataset, tmp_path, test_config):
        """Test Excel export performance."""
        # Use smaller dataset for Excel (Excel is slower)
        small_dataset = large_dataset.head(10000)

        excel_exporter = ExcelExporter(test_config)
        output_file = tmp_path / "large_export.xlsx"

        start_time = time.time()
        result = excel_exporter.export_dataframe(small_dataset, str(output_file))
        end_time = time.time()

        export_time = end_time - start_time
        records_per_second = len(small_dataset) / export_time
        file_size = output_file.stat().st_size

        print(f"Excel Export Performance:")
        print(f"  Records exported: {len(small_dataset):,}")
        print(f"  Export time: {export_time:.4f}s")
        print(f"  Records per second: {records_per_second:.0f}")
        print(f"  File size: {file_size / 1024 / 1024:.2f} MB")

        assert result.success
        assert (
            records_per_second > 1000
        )  # Excel is slower, 1K records/sec is acceptable

    def test_chunked_export_performance(self, large_dataset, tmp_path, test_config):
        """Test chunked export performance."""
        csv_exporter = CSVExporter(test_config)
        output_file = tmp_path / "chunked_export.csv"

        chunk_size = 10000
        start_time = time.time()

        # Export in chunks
        first_chunk = True
        for chunk_start in range(0, len(large_dataset), chunk_size):
            chunk_end = min(chunk_start + chunk_size, len(large_dataset))
            chunk_data = large_dataset.iloc[chunk_start:chunk_end]

            # Append to file (except first chunk)
            mode = "w" if first_chunk else "a"
            header = first_chunk

            chunk_data.to_csv(output_file, mode=mode, header=header, index=False)
            first_chunk = False

        end_time = time.time()
        export_time = end_time - start_time
        records_per_second = len(large_dataset) / export_time

        print(f"Chunked Export Performance:")
        print(f"  Records exported: {len(large_dataset):,}")
        print(f"  Chunk size: {chunk_size:,}")
        print(f"  Export time: {export_time:.4f}s")
        print(f"  Records per second: {records_per_second:.0f}")

        # Verify file integrity
        exported_df = pd.read_csv(output_file)
        assert len(exported_df) == len(large_dataset)

        assert records_per_second > 8000


@pytest.mark.performance
class TestGeospatialPerformance:
    """Performance tests for geospatial operations."""

    @pytest.fixture
    def large_point_dataset(self):
        """Create large geospatial point dataset."""
        num_points = 10000
        return pd.DataFrame(
            {
                "id": range(num_points),
                "name": [f"Point_{i}" for i in range(num_points)],
                "latitude": np.random.uniform(-90, 90, num_points),
                "longitude": np.random.uniform(-180, 180, num_points),
                "elevation": np.random.uniform(0, 1000, num_points),
            }
        )

    def test_point_creation_performance(self, large_point_dataset, test_config):
        """Test point creation performance."""
        geo_processor = GeospatialProcessor(test_config)

        start_time = time.time()

        points = []
        for _, row in large_point_dataset.iterrows():
            point = geo_processor.create_point(row["longitude"], row["latitude"])
            points.append(point)

        end_time = time.time()
        creation_time = end_time - start_time
        points_per_second = len(large_point_dataset) / creation_time

        print(f"Point Creation Performance:")
        print(f"  Points created: {len(points):,}")
        print(f"  Creation time: {creation_time:.4f}s")
        print(f"  Points per second: {points_per_second:.0f}")

        assert len(points) == len(large_point_dataset)
        assert points_per_second > 5000  # Should create at least 5K points/sec

    def test_distance_calculation_performance(self, large_point_dataset, test_config):
        """Test distance calculation performance."""
        geo_processor = GeospatialProcessor(test_config)

        # Create points
        points = []
        for _, row in large_point_dataset.head(
            1000
        ).iterrows():  # Use subset for distance calc
            point = geo_processor.create_point(row["longitude"], row["latitude"])
            points.append(point)

        # Calculate distances from first point to all others
        reference_point = points[0]

        start_time = time.time()

        distances = []
        for point in points[1:]:
            distance = geo_processor.calculate_distance(reference_point, point)
            distances.append(distance)

        end_time = time.time()
        calculation_time = end_time - start_time
        calculations_per_second = len(distances) / calculation_time

        print(f"Distance Calculation Performance:")
        print(f"  Distance calculations: {len(distances):,}")
        print(f"  Calculation time: {calculation_time:.4f}s")
        print(f"  Calculations per second: {calculations_per_second:.0f}")

        assert calculations_per_second > 1000

    def test_spatial_indexing_performance(self, large_point_dataset, test_config):
        """Test spatial indexing performance."""
        from src.geospatial.indexer import SpatialIndexer

        spatial_indexer = SpatialIndexer(test_config)
        geo_processor = GeospatialProcessor(test_config)

        # Create geometries
        geometries = []
        for _, row in large_point_dataset.iterrows():
            point = geo_processor.create_point(row["longitude"], row["latitude"])
            geometries.append((row["id"], point))

        # Test index creation performance
        start_time = time.time()
        spatial_indexer.create_index(geometries)
        index_creation_time = time.time() - start_time

        # Test query performance
        query_point = geo_processor.create_point(0, 0)  # Center of world

        start_time = time.time()

        # Perform multiple queries
        num_queries = 100
        for _ in range(num_queries):
            results = spatial_indexer.query_nearest(query_point, k=10)

        query_time = time.time() - start_time
        queries_per_second = num_queries / query_time

        print(f"Spatial Indexing Performance:")
        print(f"  Geometries indexed: {len(geometries):,}")
        print(f"  Index creation time: {index_creation_time:.4f}s")
        print(f"  Queries performed: {num_queries}")
        print(f"  Query time: {query_time:.4f}s")
        print(f"  Queries per second: {queries_per_second:.0f}")

        assert queries_per_second > 50  # Should handle at least 50 queries/sec


@pytest.mark.performance
class TestCachePerformance:
    """Performance tests for caching system."""

    @pytest.fixture
    def cache_manager(self):
        """Create cache manager for performance testing."""
        return CacheManager(max_size=10000, ttl=3600)

    def test_cache_write_performance(self, cache_manager):
        """Test cache write performance."""
        num_operations = 10000

        start_time = time.time()

        for i in range(num_operations):
            key = f"key_{i}"
            value = f"value_{i}" * 100  # Larger values
            cache_manager.set(key, value)

        end_time = time.time()
        write_time = end_time - start_time
        writes_per_second = num_operations / write_time

        print(f"Cache Write Performance:")
        print(f"  Operations: {num_operations:,}")
        print(f"  Write time: {write_time:.4f}s")
        print(f"  Writes per second: {writes_per_second:.0f}")

        assert writes_per_second > 50000  # Should handle at least 50K writes/sec

    def test_cache_read_performance(self, cache_manager):
        """Test cache read performance."""
        # Populate cache
        num_items = 5000
        for i in range(num_items):
            cache_manager.set(f"key_{i}", f"value_{i}")

        # Test read performance
        num_reads = 20000

        start_time = time.time()

        hits = 0
        for i in range(num_reads):
            key = f"key_{i % num_items}"  # Cycle through existing keys
            value = cache_manager.get(key)
            if value is not None:
                hits += 1

        end_time = time.time()
        read_time = end_time - start_time
        reads_per_second = num_reads / read_time
        hit_rate = hits / num_reads

        print(f"Cache Read Performance:")
        print(f"  Read operations: {num_reads:,}")
        print(f"  Read time: {read_time:.4f}s")
        print(f"  Reads per second: {reads_per_second:.0f}")
        print(f"  Hit rate: {hit_rate:.2%}")

        assert reads_per_second > 100000  # Should handle at least 100K reads/sec
        assert hit_rate > 0.95  # Should have high hit rate

    def test_concurrent_cache_access(self, cache_manager):
        """Test concurrent cache access performance."""
        num_threads = 20
        operations_per_thread = 1000
        results = []
        errors = []

        def cache_worker(worker_id):
            """Worker function for concurrent cache testing."""
            try:
                start_time = time.time()

                for i in range(operations_per_thread):
                    key = f"worker_{worker_id}_key_{i}"
                    value = f"worker_{worker_id}_value_{i}"

                    # Mix of reads and writes
                    if i % 3 == 0:
                        cache_manager.set(key, value)
                    else:
                        cache_manager.get(key)

                end_time = time.time()
                results.append(end_time - start_time)

            except Exception as e:
                errors.append(str(e))

        # Start concurrent workers
        start_time = time.time()
        threads = []
        for i in range(num_threads):
            thread = threading.Thread(target=cache_worker, args=(i,))
            threads.append(thread)
            thread.start()

        for thread in threads:
            thread.join()

        end_time = time.time()
        total_time = end_time - start_time

        assert len(errors) == 0, f"Errors occurred: {errors}"

        total_operations = num_threads * operations_per_thread
        operations_per_second = total_operations / total_time
        avg_thread_time = sum(results) / len(results)

        print(f"Concurrent Cache Performance:")
        print(f"  Threads: {num_threads}")
        print(f"  Operations per thread: {operations_per_thread}")
        print(f"  Total operations: {total_operations:,}")
        print(f"  Total time: {total_time:.4f}s")
        print(f"  Operations per second: {operations_per_second:.0f}")
        print(f"  Average thread time: {avg_thread_time:.4f}s")

        assert operations_per_second > 20000


@pytest.mark.performance
@pytest.mark.slow
class TestSystemPerformance:
    """System-wide performance tests."""

    def test_memory_leak_detection(self, test_config):
        """Test for memory leaks during extended operations."""
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        memory_samples = []

        # Perform many operations that could potentially leak memory
        for iteration in range(100):
            # Create and destroy database connections
            with patch("sqlalchemy.create_engine") as mock_engine:
                mock_engine.return_value = Mock()
                pool = DatabasePoolManager(test_config)

                # Perform operations
                for _ in range(10):
                    conn = pool.get_connection()
                    pool.return_connection(conn)

                pool.close()

            # Create and process data
            test_data = pd.DataFrame({"id": range(1000), "data": ["x" * 100] * 1000})

            # Process data
            processed = test_data.copy()
            processed["processed"] = True

            # Clean up
            del test_data, processed

            # Sample memory every 10 iterations
            if iteration % 10 == 0:
                gc.collect()  # Force garbage collection
                current_memory = process.memory_info().rss
                memory_samples.append(current_memory)

        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory

        print(f"Memory Leak Detection:")
        print(f"  Initial memory: {initial_memory / 1024 / 1024:.2f} MB")
        print(f"  Final memory: {final_memory / 1024 / 1024:.2f} MB")
        print(f"  Memory increase: {memory_increase / 1024 / 1024:.2f} MB")
        print(f"  Memory samples: {len(memory_samples)}")

        # Memory increase should be minimal (less than 50MB)
        assert (
            memory_increase < 50 * 1024 * 1024
        ), f"Potential memory leak detected: {memory_increase / 1024 / 1024:.2f} MB increase"

    def test_cpu_usage_monitoring(self, test_config):
        """Test CPU usage during intensive operations."""
        process = psutil.Process()
        cpu_samples = []

        def cpu_monitor():
            """Monitor CPU usage in background."""
            for _ in range(100):  # Monitor for 10 seconds
                cpu_percent = process.cpu_percent()
                cpu_samples.append(cpu_percent)
                time.sleep(0.1)

        # Start CPU monitoring
        monitor_thread = threading.Thread(target=cpu_monitor)
        monitor_thread.start()

        # Perform CPU-intensive operations
        start_time = time.time()

        # Simulate heavy data processing
        for _ in range(10):
            large_data = pd.DataFrame(
                {"id": range(10000), "value": np.random.uniform(0, 1000, 10000)}
            )

            # Perform calculations
            large_data["squared"] = large_data["value"] ** 2
            large_data["sqrt"] = np.sqrt(large_data["value"])
            large_data["log"] = np.log(large_data["value"] + 1)

            # Aggregations
            summary = large_data.groupby(large_data["id"] % 10).agg(
                {"value": ["mean", "std", "min", "max"], "squared": "sum"}
            )

        end_time = time.time()
        processing_time = end_time - start_time

        # Wait for monitoring to complete
        monitor_thread.join()

        avg_cpu = sum(cpu_samples) / len(cpu_samples)
        max_cpu = max(cpu_samples)

        print(f"CPU Usage Monitoring:")
        print(f"  Processing time: {processing_time:.4f}s")
        print(f"  Average CPU usage: {avg_cpu:.2f}%")
        print(f"  Maximum CPU usage: {max_cpu:.2f}%")
        print(f"  CPU samples: {len(cpu_samples)}")

        # CPU usage should be reasonable (not constantly at 100%)
        assert avg_cpu < 90, f"High average CPU usage: {avg_cpu:.2f}%"

    def test_throughput_under_load(self, test_config, tmp_path):
        """Test system throughput under high load."""
        # Create multiple test files
        test_files = []
        for i in range(5):
            test_data = pd.DataFrame(
                {
                    "id": range(i * 1000, (i + 1) * 1000),
                    "value": np.random.uniform(0, 1000, 1000),
                    "category": np.random.choice(["A", "B", "C"], 1000),
                }
            )

            file_path = tmp_path / f"test_data_{i}.csv"
            test_data.to_csv(file_path, index=False)
            test_files.append(str(file_path))

        # Process files concurrently
        results = []
        errors = []

        def process_file(file_path):
            """Process a single file."""
            try:
                start_time = time.time()

                # Read file
                data = pd.read_csv(file_path)

                # Process data
                data["processed"] = True
                data["timestamp"] = datetime.now()

                # Simulate database operations
                with patch("src.database.operations.BulkOperations") as mock_bulk:
                    mock_bulk.return_value.bulk_insert_dataframe.return_value = True
                    bulk_ops = BulkOperations(Mock())
                    bulk_ops.bulk_insert_dataframe("test_table", data)

                end_time = time.time()
                processing_time = end_time - start_time

                results.append(
                    {"file": file_path, "records": len(data), "time": processing_time}
                )

            except Exception as e:
                errors.append(str(e))

        # Process files concurrently
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [
                executor.submit(process_file, file_path) for file_path in test_files
            ]

            # Wait for completion
            for future in futures:
                future.result()

        end_time = time.time()
        total_time = end_time - start_time

        assert len(errors) == 0, f"Errors occurred: {errors}"

        total_records = sum(r["records"] for r in results)
        throughput = total_records / total_time

        print(f"Throughput Under Load:")
        print(f"  Files processed: {len(test_files)}")
        print(f"  Total records: {total_records:,}")
        print(f"  Total time: {total_time:.4f}s")
        print(f"  Throughput: {throughput:.0f} records/sec")

        for result in results:
            file_name = Path(result["file"]).name
            records_per_sec = result["records"] / result["time"]
            print(
                f"    {file_name}: {result['records']} records in {result['time']:.4f}s ({records_per_sec:.0f} rec/sec)"
            )

        # Should maintain good throughput under load
        assert throughput > 5000
