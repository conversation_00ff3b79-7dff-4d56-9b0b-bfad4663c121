"""统一验证框架测试配置和夹具

提供测试所需的数据、配置和夹具函数。
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any
import tempfile
import os

from ..core import ValidationContext, ValidationSeverity
from ..factory import ValidationFactory


@pytest.fixture
def sample_cdr_data():
    """生成示例CDR数据"""
    base_time = datetime.now()
    return pd.DataFrame({
        'CALL_ID': ['CDR001', 'CDR002', 'CDR003', 'CDR004', 'CDR005'],
        'CALLER_NUMBER': ['13800138001', '13800138002', '13800138003', '13800138004', '13800138005'],
        'CALLED_NUMBER': ['13900139001', '13900139002', '13900139003', '13900139004', '13900139005'],
        'CALL_START_TIME': [
            base_time - timedelta(hours=1),
            base_time - timedelta(hours=2),
            base_time - timedelta(hours=3),
            base_time - timedelta(hours=4),
            base_time - timedelta(hours=5)
        ],
        'CALL_END_TIME': [
            base_time - timedelta(hours=1) + timedelta(minutes=5),
            base_time - timedelta(hours=2) + timedelta(minutes=10),
            base_time - timedelta(hours=3) + timedelta(minutes=3),
            base_time - timedelta(hours=4) + timedelta(minutes=15),
            base_time - timedelta(hours=5) + timedelta(minutes=8)
        ],
        'CALL_DURATION': [300, 600, 180, 900, 480],  # 秒
        'CALL_STATUS': ['COMPLETED', 'COMPLETED', 'FAILED', 'COMPLETED', 'COMPLETED'],
        'CELL_ID': ['CELL001', 'CELL002', 'CELL003', 'CELL004', 'CELL005'],
        'LAC': [1001, 1002, 1003, 1004, 1005],
        'LONGITUDE': [116.3974, 116.3975, 116.3976, 116.3977, 116.3978],
        'LATITUDE': [39.9093, 39.9094, 39.9095, 39.9096, 39.9097]
    })


@pytest.fixture
def sample_kpi_data():
    """生成示例KPI数据"""
    base_time = datetime.now()
    return pd.DataFrame({
        'KPI_ID': ['KPI001', 'KPI002', 'KPI003', 'KPI004', 'KPI005'],
        'KPI_NAME': ['RSRP', 'RSRQ', 'SINR', 'THROUGHPUT', 'LATENCY'],
        'KPI_VALUE': [-85.5, -12.3, 15.2, 50.8, 25.1],
        'MEASUREMENT_TIME': [
            base_time - timedelta(minutes=5),
            base_time - timedelta(minutes=10),
            base_time - timedelta(minutes=15),
            base_time - timedelta(minutes=20),
            base_time - timedelta(minutes=25)
        ],
        'CELL_ID': ['CELL001', 'CELL002', 'CELL003', 'CELL004', 'CELL005'],
        'LONGITUDE': [116.3974, 116.3975, 116.3976, 116.3977, 116.3978],
        'LATITUDE': [39.9093, 39.9094, 39.9095, 39.9096, 39.9097]
    })


@pytest.fixture
def sample_cfg_data():
    """生成示例配置数据"""
    return pd.DataFrame({
        'CONFIG_ID': ['CFG001', 'CFG002', 'CFG003', 'CFG004', 'CFG005'],
        'CONFIG_NAME': ['MAX_POWER', 'FREQUENCY', 'BANDWIDTH', 'ANTENNA_TILT', 'AZIMUTH'],
        'CONFIG_VALUE': ['43', '2100', '20', '5', '120'],
        'CELL_ID': ['CELL001', 'CELL002', 'CELL003', 'CELL004', 'CELL005'],
        'LAST_UPDATED': [
            datetime.now() - timedelta(days=1),
            datetime.now() - timedelta(days=2),
            datetime.now() - timedelta(days=3),
            datetime.now() - timedelta(days=4),
            datetime.now() - timedelta(days=5)
        ]
    })


@pytest.fixture
def invalid_cdr_data():
    """生成包含错误的CDR数据"""
    base_time = datetime.now()
    return pd.DataFrame({
        'CALL_ID': ['CDR001', None, 'CDR003', 'CDR004', ''],  # 包含空值
        'CALLER_NUMBER': ['13800138001', '13800138002', '13800138003', '13800138004', '13800138005'],
        'CALLED_NUMBER': ['13900139001', '13900139002', '13900139003', '13900139004', '13900139005'],
        'CALL_START_TIME': [
            base_time - timedelta(hours=1),
            base_time - timedelta(hours=2),
            base_time - timedelta(hours=3),
            base_time - timedelta(hours=4),
            base_time - timedelta(hours=5)
        ],
        'CALL_END_TIME': [
            base_time - timedelta(hours=1) + timedelta(minutes=5),
            base_time - timedelta(hours=2) - timedelta(minutes=10),  # 结束时间早于开始时间
            base_time - timedelta(hours=3) + timedelta(minutes=3),
            base_time - timedelta(hours=4) + timedelta(minutes=15),
            base_time - timedelta(hours=5) + timedelta(minutes=8)
        ],
        'CALL_DURATION': [300, -600, 180, 900, 480],  # 包含负值
        'CALL_STATUS': ['COMPLETED', 'COMPLETED', 'FAILED', 'COMPLETED', 'COMPLETED'],
        'CELL_ID': ['CELL001', 'CELL002', 'CELL003', 'CELL004', 'CELL005'],
        'LAC': [1001, 1002, 1003, 1004, 1005],
        'LONGITUDE': [116.3974, 116.3975, 116.3976, 116.3977, 116.3978],
        'LATITUDE': [39.9093, 39.9094, 39.9095, 39.9096, 39.9097]
    })


@pytest.fixture
def invalid_kpi_data():
    """生成包含错误的KPI数据"""
    base_time = datetime.now()
    return pd.DataFrame({
        'KPI_ID': ['KPI001', 'KPI002', 'KPI003', 'KPI004', 'KPI005'],
        'KPI_NAME': ['RSRP', 'RSRQ', 'SINR', 'THROUGHPUT', 'LATENCY'],
        'KPI_VALUE': [-85.5, 'invalid', 15.2, None, 25.1],  # 包含非数字值和空值
        'MEASUREMENT_TIME': [
            base_time - timedelta(minutes=5),
            base_time - timedelta(minutes=10),
            'invalid_date',  # 无效日期
            base_time - timedelta(minutes=20),
            base_time - timedelta(minutes=25)
        ],
        'CELL_ID': ['CELL001', 'CELL002', 'CELL003', 'CELL004', 'CELL005'],
        'LONGITUDE': [116.3974, 116.3975, 116.3976, 116.3977, 116.3978],
        'LATITUDE': [39.9093, 39.9094, 39.9095, 39.9096, 39.9097]
    })


@pytest.fixture
def empty_dataframe():
    """生成空的DataFrame"""
    return pd.DataFrame()


@pytest.fixture
def missing_columns_data():
    """生成缺少必需列的数据"""
    return pd.DataFrame({
        'CALL_ID': ['CDR001', 'CDR002'],
        'CALLER_NUMBER': ['13800138001', '13800138002']
        # 缺少其他必需列
    })


@pytest.fixture
def validation_context():
    """生成验证上下文"""
    return ValidationContext(
        data_type="cdr",
        file_path="/test/data/cdr_test.csv",
        metadata={
            "importer": "TestImporter",
            "batch_id": "TEST_BATCH_001",
            "timestamp": datetime.now().isoformat()
        }
    )


@pytest.fixture
def validation_factory():
    """生成验证工厂实例"""
    return ValidationFactory()


@pytest.fixture
def temp_csv_file(sample_cdr_data):
    """创建临时CSV文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        sample_cdr_data.to_csv(f.name, index=False)
        yield f.name
    
    # 清理临时文件
    if os.path.exists(f.name):
        os.unlink(f.name)


@pytest.fixture
def temp_invalid_csv_file(invalid_cdr_data):
    """创建包含错误数据的临时CSV文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        invalid_cdr_data.to_csv(f.name, index=False)
        yield f.name
    
    # 清理临时文件
    if os.path.exists(f.name):
        os.unlink(f.name)


@pytest.fixture
def large_dataset():
    """生成大型数据集用于性能测试"""
    import numpy as np
    
    size = 10000
    base_time = datetime.now()
    
    return pd.DataFrame({
        'CALL_ID': [f'CDR{i:06d}' for i in range(size)],
        'CALLER_NUMBER': [f'138{np.random.randint(10000000, 99999999)}' for _ in range(size)],
        'CALLED_NUMBER': [f'139{np.random.randint(10000000, 99999999)}' for _ in range(size)],
        'CALL_START_TIME': [
            base_time - timedelta(hours=np.random.randint(1, 24)) 
            for _ in range(size)
        ],
        'CALL_END_TIME': [
            base_time - timedelta(hours=np.random.randint(1, 24)) + timedelta(minutes=np.random.randint(1, 60))
            for _ in range(size)
        ],
        'CALL_DURATION': np.random.randint(10, 3600, size),
        'CALL_STATUS': np.random.choice(['COMPLETED', 'FAILED', 'BUSY'], size),
        'CELL_ID': [f'CELL{np.random.randint(1, 1000):03d}' for _ in range(size)],
        'LAC': np.random.randint(1000, 9999, size),
        'LONGITUDE': np.random.uniform(116.0, 117.0, size),
        'LATITUDE': np.random.uniform(39.0, 40.0, size)
    })


@pytest.fixture(scope="session")
def test_config():
    """测试配置"""
    return {
        'validation': {
            'enable_parallel': True,
            'max_workers': 2,
            'timeout': 30,
            'severity_threshold': ValidationSeverity.WARNING
        },
        'performance': {
            'max_execution_time': 5.0,  # 秒
            'max_memory_usage': 100 * 1024 * 1024,  # 100MB
            'benchmark_iterations': 10
        },
        'logging': {
            'level': 'DEBUG',
            'capture_warnings': True
        }
    }


@pytest.fixture
def mock_logger():
    """模拟日志记录器"""
    import logging
    from unittest.mock import Mock
    
    logger = Mock(spec=logging.Logger)
    logger.info = Mock()
    logger.warning = Mock()
    logger.error = Mock()
    logger.debug = Mock()
    
    return logger


class ValidationTestHelper:
    """验证测试辅助类"""
    
    @staticmethod
    def assert_validation_success(result):
        """断言验证成功"""
        assert result.success, f"Validation failed: {[issue.message for issue in result.issues]}"
        assert result.passed_rules > 0, "No rules were executed"
        assert len([issue for issue in result.issues if issue.severity == ValidationSeverity.ERROR]) == 0
    
    @staticmethod
    def assert_validation_failure(result, expected_error_count=None):
        """断言验证失败"""
        assert not result.success, "Validation should have failed"
        error_issues = [issue for issue in result.issues if issue.severity == ValidationSeverity.ERROR]
        assert len(error_issues) > 0, "No error issues found"
        
        if expected_error_count is not None:
            assert len(error_issues) == expected_error_count, f"Expected {expected_error_count} errors, got {len(error_issues)}"
    
    @staticmethod
    def assert_has_issue_type(result, issue_type: str):
        """断言包含特定类型的问题"""
        issue_types = [issue.rule_name for issue in result.issues]
        assert issue_type in issue_types, f"Issue type '{issue_type}' not found in {issue_types}"
    
    @staticmethod
    def get_issues_by_severity(result, severity: ValidationSeverity):
        """获取特定严重程度的问题"""
        return [issue for issue in result.issues if issue.severity == severity]


@pytest.fixture
def validation_helper():
    """验证测试辅助实例"""
    return ValidationTestHelper()


# 性能测试装饰器
def performance_test(max_time=5.0):
    """性能测试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            start_time = time.time()
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            assert execution_time <= max_time, f"Test took {execution_time:.2f}s, expected <= {max_time}s"
            return result
        return wrapper
    return decorator


# 内存测试装饰器
def memory_test(max_memory_mb=100):
    """内存测试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import psutil
            import os
            
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss
            
            result = func(*args, **kwargs)
            
            final_memory = process.memory_info().rss
            memory_used_mb = (final_memory - initial_memory) / 1024 / 1024
            
            assert memory_used_mb <= max_memory_mb, f"Test used {memory_used_mb:.2f}MB, expected <= {max_memory_mb}MB"
            return result
        return wrapper
    return decorator