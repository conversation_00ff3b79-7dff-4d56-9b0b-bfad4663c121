"""Tests for database connection health check module.

This module contains comprehensive tests for the health check functionality,
including successful checks, failed checks, periodic monitoring, and status changes.
"""

import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, Mock, patch

import asyncpg
import pytest

from src.database.connection.health_check import (
    HealthChecker,
    HealthCheckResult,
    HealthStats,
    HealthStatus,
    close_global_health_checker,
    get_global_health_status,
    get_health_checker,
    initialize_global_health_checker,
    perform_global_health_check,
)
from src.database.exceptions import ConnectionError, DatabaseError


class TestHealthCheckResult:
    """Test HealthCheckResult class."""

    def test_init(self):
        """Test HealthCheckResult initialization."""
        timestamp = datetime.now()
        result = HealthCheckResult(
            status=HealthStatus.HEALTHY,
            timestamp=timestamp,
            response_time_ms=50.5,
            error_message="Test error",
            error_code="TEST_ERROR",
            details={"key": "value"},
        )

        assert result.status == HealthStatus.HEALTHY
        assert result.timestamp == timestamp
        assert result.response_time_ms == 50.5
        assert result.error_message == "Test error"
        assert result.error_code == "TEST_ERROR"
        assert result.details == {"key": "value"}

    def test_to_dict(self):
        """Test HealthCheckResult to_dict method."""
        timestamp = datetime.now()
        result = HealthCheckResult(
            status=HealthStatus.UNHEALTHY,
            timestamp=timestamp,
            response_time_ms=100.0,
            error_message="Connection failed",
            error_code="CONNECTION_ERROR",
        )

        result_dict = result.to_dict()

        assert result_dict["status"] == "unhealthy"
        assert result_dict["timestamp"] == timestamp.isoformat()
        assert result_dict["response_time_ms"] == 100.0
        assert result_dict["error_message"] == "Connection failed"
        assert result_dict["error_code"] == "CONNECTION_ERROR"
        assert result_dict["details"] == {}


class TestHealthStats:
    """Test HealthStats class."""

    def test_init(self):
        """Test HealthStats initialization."""
        stats = HealthStats()

        assert stats.total_checks == 0
        assert stats.successful_checks == 0
        assert stats.failed_checks == 0
        assert stats.average_response_time_ms == 0.0
        assert stats.last_check_time is None
        assert stats.uptime_percentage == 100.0
        assert stats.consecutive_failures == 0
        assert stats.consecutive_successes == 0

    def test_update_success(self):
        """Test HealthStats update_success method."""
        stats = HealthStats()

        # First success
        stats.update_success(50.0)
        assert stats.total_checks == 1
        assert stats.successful_checks == 1
        assert stats.failed_checks == 0
        assert stats.average_response_time_ms == 50.0
        assert stats.uptime_percentage == 100.0
        assert stats.consecutive_successes == 1
        assert stats.consecutive_failures == 0
        assert stats.last_check_time is not None

        # Second success
        stats.update_success(100.0)
        assert stats.total_checks == 2
        assert stats.successful_checks == 2
        assert stats.average_response_time_ms == 75.0
        assert stats.uptime_percentage == 100.0
        assert stats.consecutive_successes == 2

    def test_update_failure(self):
        """Test HealthStats update_failure method."""
        stats = HealthStats()

        # Add some successes first
        stats.update_success(50.0)
        stats.update_success(60.0)

        # Add failure
        stats.update_failure()
        assert stats.total_checks == 3
        assert stats.successful_checks == 2
        assert stats.failed_checks == 1
        assert stats.uptime_percentage == pytest.approx(66.67, rel=1e-2)
        assert stats.consecutive_failures == 1
        assert stats.consecutive_successes == 0

    def test_to_dict(self):
        """Test HealthStats to_dict method."""
        stats = HealthStats()
        stats.update_success(50.0)

        stats_dict = stats.to_dict()

        assert stats_dict["total_checks"] == 1
        assert stats_dict["successful_checks"] == 1
        assert stats_dict["failed_checks"] == 0
        assert stats_dict["average_response_time_ms"] == 50.0
        assert stats_dict["uptime_percentage"] == 100.0
        assert stats_dict["consecutive_failures"] == 0
        assert stats_dict["consecutive_successes"] == 1
        assert "last_check_time" in stats_dict


class TestHealthChecker:
    """Test HealthChecker class."""

    @pytest.fixture
    def mock_pool(self):
        """Create mock asyncpg pool."""
        pool = AsyncMock(spec=asyncpg.Pool)
        return pool

    @pytest.fixture
    async def health_checker(self, mock_pool):
        """Create HealthChecker instance with mock pool."""
        checker = HealthChecker(mock_pool, check_interval=1.0, timeout=2.0, max_retries=2)
        yield checker
        # Ensure monitoring is stopped after each test
        if checker.is_monitoring():
            await checker.stop_monitoring()

    def test_init(self, mock_pool):
        """Test HealthChecker initialization."""
        checker = HealthChecker(
            mock_pool, check_interval=5.0, timeout=3.0, max_retries=1
        )

        assert checker.pool == mock_pool
        assert checker.check_interval == 5.0
        assert checker.timeout == 3.0
        assert checker.max_retries == 1
        assert checker.get_health_status() == HealthStatus.UNKNOWN
        assert not checker.is_monitoring()
        assert checker.get_last_result() is None

    @pytest.mark.asyncio
    async def test_check_health_success(self, health_checker, mock_pool):
        """Test successful health check."""
        # Mock successful connection and query
        mock_connection = AsyncMock()
        mock_connection.fetchval.return_value = 1

        # Create a proper async context manager
        class MockAsyncContextManager:
            async def __aenter__(self):
                return mock_connection

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        mock_pool.acquire.return_value = MockAsyncContextManager()

        # Perform health check
        result = await health_checker.check_health()

        # Verify result
        assert result.status == HealthStatus.HEALTHY
        assert result.error_message is None
        assert result.error_code is None
        assert result.response_time_ms > 0
        assert health_checker.get_health_status() == HealthStatus.HEALTHY

        # Verify stats
        stats = health_checker.get_stats()
        assert stats.total_checks == 1
        assert stats.successful_checks == 1
        assert stats.failed_checks == 0
        assert stats.consecutive_successes == 1

    @pytest.mark.asyncio
    async def test_check_health_timeout(self, health_checker, mock_pool):
        """Test health check timeout."""

        # Mock timeout with proper async context manager
        class SlowAsyncContextManager:
            async def __aenter__(self):
                await asyncio.sleep(10)  # Longer than timeout
                return AsyncMock()

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        mock_pool.acquire.return_value = SlowAsyncContextManager()

        # Perform health check
        result = await health_checker.check_health()

        # Verify result
        assert result.status == HealthStatus.UNHEALTHY
        assert "timeout" in result.error_message.lower()
        assert result.error_code == "HEALTH_CHECK_TIMEOUT"
        assert health_checker.get_health_status() == HealthStatus.UNHEALTHY

        # Verify stats
        stats = health_checker.get_stats()
        assert stats.total_checks == 1
        assert stats.successful_checks == 0
        assert stats.failed_checks == 1
        assert stats.consecutive_failures == 1

    @pytest.mark.asyncio
    async def test_check_health_connection_error(self, health_checker, mock_pool):
        """Test health check with connection error."""
        # Mock connection error
        mock_pool.acquire.side_effect = asyncpg.ConnectionDoesNotExistError(
            "Connection lost"
        )

        # Perform health check
        result = await health_checker.check_health()

        # Verify result
        assert result.status == HealthStatus.UNHEALTHY
        assert "Connection lost" in result.error_message
        assert result.error_code == "HEALTH_CHECK_NO_CONNECTION"
        assert health_checker.get_health_status() == HealthStatus.UNHEALTHY

    @pytest.mark.asyncio
    async def test_check_health_invalid_result(self, health_checker, mock_pool):
        """Test health check with invalid query result."""
        # Mock connection with invalid result
        mock_connection = AsyncMock()
        mock_connection.fetchval.return_value = 0  # Should be 1

        class MockAsyncContextManager:
            async def __aenter__(self):
                return mock_connection

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        mock_pool.acquire.return_value = MockAsyncContextManager()

        # Perform health check
        result = await health_checker.check_health()

        # Verify result
        assert result.status == HealthStatus.UNHEALTHY
        assert "unexpected result" in result.error_message.lower()
        assert result.error_code == "HEALTH_CHECK_FAILED"

    @pytest.mark.asyncio
    async def test_check_health_retries(self, health_checker, mock_pool):
        """Test health check retries on failure."""
        # Mock first two calls to fail, third to succeed
        call_count = 0

        class MockRetryContextManager:
            async def __aenter__(self):
                nonlocal call_count
                call_count += 1
                if call_count <= 2:
                    # First two calls fail
                    raise asyncpg.InterfaceError("Temporary error")

                # Third call succeeds
                mock_connection = AsyncMock()
                mock_connection.fetchval.return_value = 1
                return mock_connection

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        mock_pool.acquire.return_value = MockRetryContextManager()

        # Perform health check
        result = await health_checker.check_health()

        # Verify result (should succeed on third attempt)
        assert result.status == HealthStatus.HEALTHY
        assert result.details["attempt"] == 3
        assert call_count == 3

    @pytest.mark.asyncio
    async def test_start_stop_monitoring(self, health_checker):
        """Test starting and stopping monitoring."""
        # Initially not monitoring
        assert not health_checker.is_monitoring()

        # Start monitoring
        await health_checker.start_monitoring()
        assert health_checker.is_monitoring()

        # Wait a bit to ensure monitoring task is running
        await asyncio.sleep(0.1)

        # Stop monitoring
        await health_checker.stop_monitoring()
        assert not health_checker.is_monitoring()

    @pytest.mark.asyncio
    async def test_start_monitoring_already_running(self, health_checker):
        """Test starting monitoring when already running."""
        await health_checker.start_monitoring()

        # Should raise RuntimeError
        with pytest.raises(RuntimeError, match="already running"):
            await health_checker.start_monitoring()

        await health_checker.stop_monitoring()

    @pytest.mark.asyncio
    async def test_stop_monitoring_not_running(self, health_checker):
        """Test stopping monitoring when not running."""
        # Should not raise error
        await health_checker.stop_monitoring()
        assert not health_checker.is_monitoring()

    @pytest.mark.asyncio
    async def test_periodic_monitoring(self, health_checker, mock_pool):
        """Test periodic health monitoring."""
        # Mock successful health checks
        mock_connection = AsyncMock()
        mock_connection.fetchval.return_value = 1

        class MockAsyncContextManager:
            async def __aenter__(self):
                return mock_connection

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        mock_pool.acquire.return_value = MockAsyncContextManager()

        # Start monitoring with short interval
        health_checker.check_interval = 0.1
        await health_checker.start_monitoring()

        # Wait for a few checks
        await asyncio.sleep(0.3)

        # Stop monitoring
        await health_checker.stop_monitoring()

        # Verify multiple checks were performed
        stats = health_checker.get_stats()
        assert stats.total_checks >= 2
        assert stats.successful_checks >= 2

    def test_status_change_callbacks(self, health_checker):
        """Test status change callbacks."""
        callback_calls = []

        def status_callback(old_status, new_status):
            callback_calls.append((old_status, new_status))

        # Add callback
        health_checker.add_status_change_callback(status_callback)

        # Simulate status change
        health_checker._notify_status_change(HealthStatus.UNKNOWN, HealthStatus.HEALTHY)

        # Verify callback was called
        assert len(callback_calls) == 1
        assert callback_calls[0] == (HealthStatus.UNKNOWN, HealthStatus.HEALTHY)

        # Remove callback
        health_checker.remove_status_change_callback(status_callback)

        # Simulate another status change
        health_checker._notify_status_change(
            HealthStatus.HEALTHY, HealthStatus.UNHEALTHY
        )

        # Verify callback was not called again
        assert len(callback_calls) == 1

    def test_reset_stats(self, health_checker):
        """Test resetting statistics."""
        # Add some stats
        health_checker._stats.update_success(50.0)
        health_checker._stats.update_failure()

        # Verify stats exist
        assert health_checker._stats.total_checks == 2

        # Reset stats
        health_checker.reset_stats()

        # Verify stats are reset
        stats = health_checker.get_stats()
        assert stats.total_checks == 0
        assert stats.successful_checks == 0
        assert stats.failed_checks == 0

    @pytest.mark.asyncio
    async def test_async_context_manager(self, health_checker, mock_pool):
        """Test HealthChecker as async context manager."""
        # Mock successful health checks
        mock_connection = AsyncMock()
        mock_connection.fetchval.return_value = 1

        class MockAsyncContextManager:
            async def __aenter__(self):
                return mock_connection

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        mock_pool.acquire.return_value = MockAsyncContextManager()

        # Use as context manager
        async with health_checker as checker:
            assert checker.is_monitoring()
            await asyncio.sleep(0.1)  # Let it run briefly

        # Should be stopped after context exit
        assert not health_checker.is_monitoring()


class TestGlobalHealthChecker:
    """Test global health checker functions."""

    @pytest.fixture
    def mock_pool(self):
        """Create mock asyncpg pool."""
        return AsyncMock(spec=asyncpg.Pool)

    @pytest.mark.asyncio
    async def test_initialize_global_health_checker(self, mock_pool):
        """Test initializing global health checker."""
        # Initially no global checker
        assert get_health_checker() is None

        # Initialize global checker
        checker = initialize_global_health_checker(mock_pool, check_interval=5.0)

        # Verify global checker is set
        assert get_health_checker() is checker
        assert checker.check_interval == 5.0

        # Clean up
        await close_global_health_checker()

    @pytest.mark.asyncio
    async def test_initialize_global_health_checker_already_initialized(
        self, mock_pool
    ):
        """Test initializing global health checker when already initialized."""
        # Initialize first checker
        initialize_global_health_checker(mock_pool)

        # Should raise RuntimeError for second initialization
        with pytest.raises(RuntimeError, match="already initialized"):
            initialize_global_health_checker(mock_pool)

        # Clean up
        await close_global_health_checker()

    @pytest.mark.asyncio
    async def test_close_global_health_checker(self, mock_pool):
        """Test closing global health checker."""
        # Initialize and start monitoring
        checker = initialize_global_health_checker(mock_pool)
        await checker.start_monitoring()

        # Close global checker
        await close_global_health_checker()

        # Verify global checker is None and monitoring stopped
        assert get_health_checker() is None
        assert not checker.is_monitoring()

    @pytest.mark.asyncio
    async def test_close_global_health_checker_not_initialized(self):
        """Test closing global health checker when not initialized."""
        # Should not raise error
        await close_global_health_checker()
        assert get_health_checker() is None

    @pytest.mark.asyncio
    async def test_get_global_health_status(self, mock_pool):
        """Test getting global health status."""
        # Should raise RuntimeError when not initialized
        with pytest.raises(RuntimeError, match="not initialized"):
            await get_global_health_status()

        # Initialize global checker
        checker = initialize_global_health_checker(mock_pool)

        # Should return status
        status = await get_global_health_status()
        assert status == HealthStatus.UNKNOWN

        # Clean up
        await close_global_health_checker()

    @pytest.mark.asyncio
    async def test_perform_global_health_check(self, mock_pool):
        """Test performing global health check."""
        # Should raise RuntimeError when not initialized
        with pytest.raises(RuntimeError, match="not initialized"):
            await perform_global_health_check()

        # Initialize global checker
        initialize_global_health_checker(mock_pool)

        # Mock successful health check
        mock_connection = AsyncMock()
        mock_connection.fetchval.return_value = 1

        class MockAsyncContextManager:
            async def __aenter__(self):
                return mock_connection

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        mock_pool.acquire.return_value = MockAsyncContextManager()

        # Perform health check
        result = await perform_global_health_check()

        # Verify result
        assert result.status == HealthStatus.HEALTHY
        assert result.response_time_ms > 0

        # Clean up
        await close_global_health_checker()


class TestHealthCheckIntegration:
    """Integration tests for health check functionality."""

    @pytest.mark.asyncio
    async def test_health_check_with_real_pool_mock(self):
        """Test health check with more realistic pool mock."""
        # Create a more realistic pool mock
        pool = AsyncMock(spec=asyncpg.Pool)

        # Mock connection behavior
        mock_connection = AsyncMock()
        mock_connection.fetchval.return_value = 1

        class MockAsyncContextManager:
            async def __aenter__(self):
                return mock_connection

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        pool.acquire.return_value = MockAsyncContextManager()

        # Create health checker
        checker = HealthChecker(pool, check_interval=0.1, timeout=1.0)

        # Perform multiple health checks
        for _ in range(3):
            result = await checker.check_health()
            assert result.status == HealthStatus.HEALTHY
            await asyncio.sleep(0.05)

        # Verify stats
        stats = checker.get_stats()
        assert stats.total_checks == 3
        assert stats.successful_checks == 3
        assert stats.uptime_percentage == 100.0

    @pytest.mark.asyncio
    async def test_health_check_recovery_scenario(self):
        """Test health check recovery from failure to success."""
        pool = AsyncMock(spec=asyncpg.Pool)

        # Track call count for simulating recovery
        call_count = 0

        class MockRecoveryContextManager:
            async def __aenter__(self):
                nonlocal call_count
                call_count += 1

                if call_count <= 2:
                    # First two calls fail
                    raise asyncpg.ConnectionDoesNotExistError("Database down")
                else:
                    # Subsequent calls succeed
                    mock_connection = AsyncMock()
                    mock_connection.fetchval.return_value = 1
                    return mock_connection

            async def __aexit__(self, exc_type, exc_val, exc_tb):
                pass

        pool.acquire.return_value = MockRecoveryContextManager()

        # Create health checker
        checker = HealthChecker(pool, check_interval=0.1, timeout=1.0, max_retries=0)

        # Track status changes
        status_changes = []

        def status_callback(old_status, new_status):
            status_changes.append((old_status, new_status))

        checker.add_status_change_callback(status_callback)

        # First check should fail
        result1 = await checker.check_health()
        assert result1.status == HealthStatus.UNHEALTHY

        # Second check should fail
        result2 = await checker.check_health()
        assert result2.status == HealthStatus.UNHEALTHY

        # Third check should succeed (recovery)
        result3 = await checker.check_health()
        assert result3.status == HealthStatus.HEALTHY

        # Verify status changes
        assert len(status_changes) >= 2
        # Should have transitions to unhealthy and back to healthy
        assert any(change[1] == HealthStatus.UNHEALTHY for change in status_changes)
        assert any(change[1] == HealthStatus.HEALTHY for change in status_changes)

        # Verify final stats
        stats = checker.get_stats()
        assert stats.total_checks == 3
        assert stats.successful_checks == 1
        assert stats.failed_checks == 2
        assert stats.consecutive_successes == 1
        assert stats.consecutive_failures == 0
