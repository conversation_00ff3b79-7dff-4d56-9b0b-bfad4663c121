#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect平台增强性能测试

本模块提供全面的性能测试功能，包括：
- 大数据量处理性能测试
- 并发用户负载测试
- 内存和CPU使用率监控
- 数据库查询性能测试
- API响应时间测试
- 系统资源利用率分析

作者: Connect质量工程团队
创建时间: 2024-01-20
"""

import asyncio
import concurrent.futures
import json
import logging
import os
import psutil
import pytest
import requests
import statistics
import tempfile
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from unittest.mock import patch

import pandas as pd
import numpy as np
from locust import HttpUser, task, between
from locust.env import Environment
from locust.stats import stats_printer, stats_history
from locust.log import setup_logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'disk_io': [],
            'network_io': [],
            'response_times': [],
            'throughput': [],
            'error_rates': []
        }
        self.start_time = None
        self.monitoring = False
    
    def start_monitoring(self):
        """开始性能监控"""
        self.start_time = time.time()
        self.monitoring = True
        logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        logger.info("性能监控已停止")
    
    def collect_system_metrics(self):
        """收集系统性能指标"""
        if not self.monitoring:
            return
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        self.metrics['cpu_usage'].append({
            'timestamp': time.time(),
            'value': cpu_percent
        })
        
        # 内存使用率
        memory = psutil.virtual_memory()
        self.metrics['memory_usage'].append({
            'timestamp': time.time(),
            'percent': memory.percent,
            'used_gb': memory.used / (1024**3),
            'available_gb': memory.available / (1024**3)
        })
        
        # 磁盘IO
        disk_io = psutil.disk_io_counters()
        if disk_io:
            self.metrics['disk_io'].append({
                'timestamp': time.time(),
                'read_bytes': disk_io.read_bytes,
                'write_bytes': disk_io.write_bytes,
                'read_count': disk_io.read_count,
                'write_count': disk_io.write_count
            })
        
        # 网络IO
        network_io = psutil.net_io_counters()
        if network_io:
            self.metrics['network_io'].append({
                'timestamp': time.time(),
                'bytes_sent': network_io.bytes_sent,
                'bytes_recv': network_io.bytes_recv,
                'packets_sent': network_io.packets_sent,
                'packets_recv': network_io.packets_recv
            })
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {
            'monitoring_duration': time.time() - self.start_time if self.start_time else 0,
            'cpu_stats': {},
            'memory_stats': {},
            'disk_stats': {},
            'network_stats': {},
            'response_time_stats': {},
            'throughput_stats': {}
        }
        
        # CPU统计
        if self.metrics['cpu_usage']:
            cpu_values = [m['value'] for m in self.metrics['cpu_usage']]
            summary['cpu_stats'] = {
                'avg': statistics.mean(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values),
                'p95': np.percentile(cpu_values, 95),
                'p99': np.percentile(cpu_values, 99)
            }
        
        # 内存统计
        if self.metrics['memory_usage']:
            memory_values = [m['percent'] for m in self.metrics['memory_usage']]
            used_gb_values = [m['used_gb'] for m in self.metrics['memory_usage']]
            summary['memory_stats'] = {
                'avg_percent': statistics.mean(memory_values),
                'max_percent': max(memory_values),
                'avg_used_gb': statistics.mean(used_gb_values),
                'max_used_gb': max(used_gb_values)
            }
        
        # 响应时间统计
        if self.metrics['response_times']:
            response_times = [m['time'] for m in self.metrics['response_times']]
            summary['response_time_stats'] = {
                'avg': statistics.mean(response_times),
                'median': statistics.median(response_times),
                'p95': np.percentile(response_times, 95),
                'p99': np.percentile(response_times, 99),
                'max': max(response_times),
                'min': min(response_times)
            }
        
        return summary


class LargeDatasetGenerator:
    """大数据集生成器"""
    
    @staticmethod
    def generate_cdr_data(num_records: int, output_file: str) -> str:
        """生成CDR测试数据"""
        logger.info(f"生成CDR数据: {num_records} 条记录")
        
        # 生成随机CDR数据
        data = []
        for i in range(num_records):
            record = {
                'call_id': f'call_{i:010d}',
                'caller': f'1{np.random.randint(1000000000, 9999999999)}',
                'callee': f'1{np.random.randint(1000000000, 9999999999)}',
                'start_time': (datetime.now() - timedelta(days=np.random.randint(0, 30))).isoformat(),
                'duration': np.random.randint(1, 3600),
                'cell_id': f'cell_{np.random.randint(1, 1000):03d}',
                'lac': np.random.randint(1, 65535),
                'ci': np.random.randint(1, 65535),
                'imsi': f'{np.random.randint(100000000000000, 999999999999999)}',
                'imei': f'{np.random.randint(100000000000000, 999999999999999)}'
            }
            data.append(record)
        
        # 保存到CSV文件
        df = pd.DataFrame(data)
        df.to_csv(output_file, index=False)
        
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
        logger.info(f"CDR数据生成完成: {output_file}, 大小: {file_size:.2f}MB")
        
        return output_file
    
    @staticmethod
    def generate_ep_data(num_records: int, output_file: str) -> str:
        """生成EP测试数据"""
        logger.info(f"生成EP数据: {num_records} 条记录")
        
        # 生成随机EP数据
        data = []
        base_lat, base_lon = 39.9042, 116.4074  # 北京坐标
        
        for i in range(num_records):
            record = {
                'timestamp': (datetime.now() - timedelta(seconds=i)).isoformat(),
                'longitude': base_lon + np.random.uniform(-0.1, 0.1),
                'latitude': base_lat + np.random.uniform(-0.1, 0.1),
                'rsrp': np.random.uniform(-120, -60),
                'rsrq': np.random.uniform(-20, -3),
                'sinr': np.random.uniform(-10, 30),
                'pci': np.random.randint(0, 503),
                'earfcn': np.random.choice([1825, 1850, 1875, 1900]),
                'ta': np.random.randint(0, 1282),
                'speed': np.random.uniform(0, 120)
            }
            data.append(record)
        
        # 保存到CSV文件
        df = pd.DataFrame(data)
        df.to_csv(output_file, index=False)
        
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
        logger.info(f"EP数据生成完成: {output_file}, 大小: {file_size:.2f}MB")
        
        return output_file
    
    @staticmethod
    def generate_site_data(num_records: int, output_file: str) -> str:
        """生成站点测试数据"""
        logger.info(f"生成站点数据: {num_records} 条记录")
        
        # 生成随机站点数据
        data = []
        base_lat, base_lon = 39.9042, 116.4074  # 北京坐标
        
        for i in range(num_records):
            record = {
                'site_id': f'site_{i:06d}',
                'site_name': f'基站_{i:06d}',
                'longitude': base_lon + np.random.uniform(-0.5, 0.5),
                'latitude': base_lat + np.random.uniform(-0.5, 0.5),
                'height': np.random.uniform(20, 80),
                'azimuth': np.random.uniform(0, 360),
                'tilt': np.random.uniform(-10, 10),
                'power': np.random.uniform(20, 46),
                'frequency': np.random.choice([1800, 2100, 2600]),
                'technology': np.random.choice(['4G', '5G']),
                'vendor': np.random.choice(['华为', '爱立信', '诺基亚', '中兴']),
                'status': np.random.choice(['在线', '离线', '维护'])
            }
            data.append(record)
        
        # 保存到CSV文件
        df = pd.DataFrame(data)
        df.to_csv(output_file, index=False)
        
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
        logger.info(f"站点数据生成完成: {output_file}, 大小: {file_size:.2f}MB")
        
        return output_file


class ConnectLoadTestUser(HttpUser):
    """Connect平台负载测试用户"""
    
    wait_time = between(1, 3)
    
    def on_start(self):
        """用户开始时的初始化"""
        # 登录
        response = self.client.post("/api/auth/login", json={
            "username": "test_user",
            "password": "test_password"
        })
        
        if response.status_code == 200:
            self.token = response.json().get('token')
            self.client.headers.update({'Authorization': f'Bearer {self.token}'})
    
    @task(3)
    def view_dashboard(self):
        """查看仪表板"""
        self.client.get("/api/dashboard")
    
    @task(2)
    def query_cdr_data(self):
        """查询CDR数据"""
        params = {
            'start_date': '2024-01-01',
            'end_date': '2024-01-31',
            'limit': 100
        }
        self.client.get("/api/data/cdr", params=params)
    
    @task(2)
    def query_ep_data(self):
        """查询EP数据"""
        params = {
            'start_time': '2024-01-01T00:00:00',
            'end_time': '2024-01-01T23:59:59',
            'limit': 100
        }
        self.client.get("/api/data/ep", params=params)
    
    @task(1)
    def query_sites(self):
        """查询站点数据"""
        params = {
            'bounds': '116.3,39.8,116.5,40.0',
            'technology': '4G'
        }
        self.client.get("/api/sites", params=params)
    
    @task(1)
    def generate_report(self):
        """生成报告"""
        data = {
            'report_type': 'coverage',
            'date_range': {
                'start': '2024-01-01',
                'end': '2024-01-31'
            },
            'parameters': {
                'technology': '4G',
                'area': 'beijing'
            }
        }
        self.client.post("/api/reports/generate", json=data)


class TestPerformanceEnhanced:
    """增强性能测试类"""
    
    def setup_method(self):
        """测试方法设置"""
        self.monitor = PerformanceMonitor()
        self.data_generator = LargeDatasetGenerator()
        self.base_url = os.getenv('TEST_BASE_URL', 'http://localhost:8000')
        self.api_url = f"{self.base_url}/api"
        
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        logger.info(f"临时目录: {self.temp_dir}")
    
    def teardown_method(self):
        """测试方法清理"""
        # 清理临时文件
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    @pytest.mark.performance
    @pytest.mark.parametrize("data_size,expected_time", [
        (10000, 5),      # 1万条记录，期望5秒内完成
        (100000, 30),    # 10万条记录，期望30秒内完成
        (1000000, 180),  # 100万条记录，期望3分钟内完成
        (5000000, 600),  # 500万条记录，期望10分钟内完成
    ])
    def test_large_data_import_performance(self, data_size: int, expected_time: int):
        """测试大数据导入性能"""
        logger.info(f"测试大数据导入性能: {data_size} 条记录")
        
        # 开始性能监控
        self.monitor.start_monitoring()
        
        try:
            # 生成测试数据
            test_file = os.path.join(self.temp_dir, f'cdr_test_{data_size}.csv')
            self.data_generator.generate_cdr_data(data_size, test_file)
            
            # 记录开始时间
            start_time = time.time()
            
            # 模拟数据导入（实际应该调用真实的导入API）
            with patch('requests.post') as mock_post:
                mock_post.return_value.status_code = 200
                mock_post.return_value.json.return_value = {
                    'success': True,
                    'imported_records': data_size,
                    'import_id': f'import_{int(time.time())}'
                }
                
                # 执行导入
                with open(test_file, 'rb') as f:
                    files = {'file': f}
                    response = requests.post(f"{self.api_url}/data/import/cdr", files=files)
            
            # 记录结束时间
            end_time = time.time()
            import_time = end_time - start_time
            
            # 收集系统指标
            self.monitor.collect_system_metrics()
            
            # 验证性能
            assert import_time <= expected_time, f"导入时间 {import_time:.2f}s 超过期望时间 {expected_time}s"
            
            # 计算吞吐量
            throughput = data_size / import_time
            logger.info(f"导入性能: {data_size} 条记录, 耗时: {import_time:.2f}s, 吞吐量: {throughput:.2f} 记录/秒")
            
            # 验证吞吐量
            min_throughput = 1000  # 最小1000记录/秒
            assert throughput >= min_throughput, f"吞吐量 {throughput:.2f} 低于最小要求 {min_throughput}"
            
        finally:
            self.monitor.stop_monitoring()
            
            # 获取性能摘要
            summary = self.monitor.get_performance_summary()
            logger.info(f"性能摘要: {summary}")
            
            # 验证系统资源使用
            if summary['cpu_stats']:
                assert summary['cpu_stats']['max'] <= 90, f"CPU使用率过高: {summary['cpu_stats']['max']}%"
            
            if summary['memory_stats']:
                assert summary['memory_stats']['max_percent'] <= 85, f"内存使用率过高: {summary['memory_stats']['max_percent']}%"
    
    @pytest.mark.performance
    @pytest.mark.parametrize("query_type,data_size,expected_time", [
        ('simple', 'small', 1),      # 简单查询，小数据集，1秒内
        ('simple', 'medium', 3),     # 简单查询，中等数据集，3秒内
        ('complex', 'medium', 5),    # 复杂查询，中等数据集，5秒内
        ('complex', 'large', 10),    # 复杂查询，大数据集，10秒内
        ('aggregation', 'large', 15) # 聚合查询，大数据集，15秒内
    ])
    def test_query_performance(self, query_type: str, data_size: str, expected_time: int):
        """测试查询性能"""
        logger.info(f"测试查询性能: {query_type} 查询, {data_size} 数据集")
        
        # 开始性能监控
        self.monitor.start_monitoring()
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 模拟查询请求
            with patch('requests.get') as mock_get:
                # 根据查询类型和数据大小模拟不同的响应时间
                response_delay = {
                    ('simple', 'small'): 0.5,
                    ('simple', 'medium'): 1.5,
                    ('complex', 'medium'): 3.0,
                    ('complex', 'large'): 6.0,
                    ('aggregation', 'large'): 8.0
                }.get((query_type, data_size), 2.0)
                
                time.sleep(response_delay)  # 模拟查询时间
                
                mock_get.return_value.status_code = 200
                mock_get.return_value.json.return_value = {
                    'success': True,
                    'data': [{'id': i, 'value': f'test_{i}'} for i in range(100)],
                    'total': 100,
                    'query_time': response_delay
                }
                
                # 执行查询
                params = {
                    'type': query_type,
                    'size': data_size,
                    'limit': 100
                }
                response = requests.get(f"{self.api_url}/data/query", params=params)
            
            # 记录结束时间
            end_time = time.time()
            query_time = end_time - start_time
            
            # 收集系统指标
            self.monitor.collect_system_metrics()
            
            # 记录响应时间
            self.monitor.metrics['response_times'].append({
                'timestamp': time.time(),
                'time': query_time,
                'query_type': query_type,
                'data_size': data_size
            })
            
            # 验证性能
            assert query_time <= expected_time, f"查询时间 {query_time:.2f}s 超过期望时间 {expected_time}s"
            
            logger.info(f"查询性能: {query_type} 查询, {data_size} 数据集, 耗时: {query_time:.2f}s")
            
        finally:
            self.monitor.stop_monitoring()
    
    @pytest.mark.performance
    @pytest.mark.parametrize("user_count,duration", [
        (5, 30),    # 5个并发用户，30秒
        (10, 60),   # 10个并发用户，60秒
        (20, 120),  # 20个并发用户，120秒
    ])
    def test_concurrent_user_load(self, user_count: int, duration: int):
        """测试并发用户负载"""
        logger.info(f"测试并发用户负载: {user_count} 用户, {duration} 秒")
        
        # 设置Locust环境
        env = Environment(user_classes=[ConnectLoadTestUser])
        env.create_local_runner()
        
        # 开始性能监控
        self.monitor.start_monitoring()
        
        try:
            # 启动负载测试
            env.runner.start(user_count, spawn_rate=2)
            
            # 运行指定时间
            time.sleep(duration)
            
            # 停止测试
            env.runner.stop()
            
            # 收集测试结果
            stats = env.runner.stats
            
            # 验证性能指标
            total_requests = stats.total.num_requests
            total_failures = stats.total.num_failures
            avg_response_time = stats.total.avg_response_time
            
            logger.info(f"负载测试结果: 总请求: {total_requests}, 失败: {total_failures}, 平均响应时间: {avg_response_time:.2f}ms")
            
            # 验证错误率
            error_rate = (total_failures / total_requests) * 100 if total_requests > 0 else 0
            assert error_rate <= 5, f"错误率过高: {error_rate:.2f}%"
            
            # 验证平均响应时间
            assert avg_response_time <= 3000, f"平均响应时间过长: {avg_response_time:.2f}ms"
            
            # 验证吞吐量
            throughput = total_requests / duration
            min_throughput = user_count * 0.5  # 每个用户每秒至少0.5个请求
            assert throughput >= min_throughput, f"吞吐量过低: {throughput:.2f} 请求/秒"
            
        finally:
            self.monitor.stop_monitoring()
            
            # 获取性能摘要
            summary = self.monitor.get_performance_summary()
            logger.info(f"系统性能摘要: {summary}")
    
    @pytest.mark.performance
    def test_memory_usage_under_load(self):
        """测试负载下的内存使用情况"""
        logger.info("测试负载下的内存使用情况")
        
        # 开始性能监控
        self.monitor.start_monitoring()
        
        try:
            # 模拟内存密集型操作
            large_data_sets = []
            
            for i in range(10):
                # 创建大数据集
                data = np.random.rand(100000, 10)  # 100万个浮点数
                large_data_sets.append(data)
                
                # 收集内存指标
                self.monitor.collect_system_metrics()
                
                # 模拟数据处理
                processed_data = np.mean(data, axis=1)
                
                time.sleep(1)
            
            # 获取内存使用统计
            memory_stats = self.monitor.get_performance_summary()['memory_stats']
            
            logger.info(f"内存使用统计: {memory_stats}")
            
            # 验证内存使用不超过限制
            assert memory_stats['max_percent'] <= 90, f"内存使用率过高: {memory_stats['max_percent']}%"
            assert memory_stats['max_used_gb'] <= 15, f"内存使用量过高: {memory_stats['max_used_gb']:.2f}GB"
            
        finally:
            self.monitor.stop_monitoring()
            
            # 清理内存
            del large_data_sets
    
    @pytest.mark.performance
    def test_database_connection_pool(self):
        """测试数据库连接池性能"""
        logger.info("测试数据库连接池性能")
        
        # 模拟并发数据库连接
        def simulate_db_query(query_id: int) -> Dict[str, Any]:
            start_time = time.time()
            
            # 模拟数据库查询
            time.sleep(np.random.uniform(0.1, 0.5))
            
            end_time = time.time()
            
            return {
                'query_id': query_id,
                'duration': end_time - start_time,
                'success': True
            }
        
        # 使用线程池模拟并发查询
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            # 提交100个并发查询
            futures = [executor.submit(simulate_db_query, i) for i in range(100)]
            
            # 收集结果
            results = []
            for future in concurrent.futures.as_completed(futures):
                result = future.result()
                results.append(result)
        
        # 分析性能
        durations = [r['duration'] for r in results]
        avg_duration = statistics.mean(durations)
        max_duration = max(durations)
        p95_duration = np.percentile(durations, 95)
        
        logger.info(f"数据库连接池性能: 平均: {avg_duration:.3f}s, 最大: {max_duration:.3f}s, P95: {p95_duration:.3f}s")
        
        # 验证性能
        assert avg_duration <= 1.0, f"平均查询时间过长: {avg_duration:.3f}s"
        assert max_duration <= 2.0, f"最大查询时间过长: {max_duration:.3f}s"
        assert p95_duration <= 1.5, f"P95查询时间过长: {p95_duration:.3f}s"
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_async_api_performance(self):
        """测试异步API性能"""
        logger.info("测试异步API性能")
        
        import aiohttp
        
        async def make_request(session: aiohttp.ClientSession, url: str, request_id: int) -> Dict[str, Any]:
            start_time = time.time()
            
            try:
                async with session.get(url) as response:
                    await response.text()
                    end_time = time.time()
                    
                    return {
                        'request_id': request_id,
                        'status': response.status,
                        'duration': end_time - start_time,
                        'success': response.status == 200
                    }
            except Exception as e:
                end_time = time.time()
                return {
                    'request_id': request_id,
                    'status': 0,
                    'duration': end_time - start_time,
                    'success': False,
                    'error': str(e)
                }
        
        # 创建异步HTTP会话
        async with aiohttp.ClientSession() as session:
            # 并发发送100个请求
            tasks = [
                make_request(session, f"{self.api_url}/health", i)
                for i in range(100)
            ]
            
            # 等待所有请求完成
            results = await asyncio.gather(*tasks)
        
        # 分析结果
        successful_requests = [r for r in results if r['success']]
        failed_requests = [r for r in results if not r['success']]
        
        success_rate = len(successful_requests) / len(results) * 100
        
        if successful_requests:
            durations = [r['duration'] for r in successful_requests]
            avg_duration = statistics.mean(durations)
            p95_duration = np.percentile(durations, 95)
            
            logger.info(f"异步API性能: 成功率: {success_rate:.1f}%, 平均响应时间: {avg_duration:.3f}s, P95: {p95_duration:.3f}s")
            
            # 验证性能
            assert success_rate >= 95, f"成功率过低: {success_rate:.1f}%"
            assert avg_duration <= 1.0, f"平均响应时间过长: {avg_duration:.3f}s"
            assert p95_duration <= 2.0, f"P95响应时间过长: {p95_duration:.3f}s"
        else:
            pytest.fail("所有异步请求都失败了")
    
    def test_generate_performance_report(self):
        """生成性能测试报告"""
        logger.info("生成性能测试报告")
        
        # 收集所有性能数据
        performance_data = {
            'test_timestamp': datetime.now().isoformat(),
            'system_info': {
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'disk_total_gb': psutil.disk_usage('/').total / (1024**3)
            },
            'performance_metrics': self.monitor.get_performance_summary(),
            'test_results': {
                'data_import_tests': 'completed',
                'query_performance_tests': 'completed',
                'load_tests': 'completed',
                'memory_tests': 'completed',
                'database_tests': 'completed',
                'async_api_tests': 'completed'
            }
        }
        
        # 保存报告
        report_file = os.path.join(self.temp_dir, 'performance_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(performance_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"性能测试报告已生成: {report_file}")
        
        # 验证报告文件
        assert os.path.exists(report_file)
        assert os.path.getsize(report_file) > 0
        
        # 读取并验证报告内容
        with open(report_file, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        assert 'test_timestamp' in report_data
        assert 'system_info' in report_data
        assert 'performance_metrics' in report_data
        assert 'test_results' in report_data
        
        logger.info("性能测试报告验证通过")


if __name__ == '__main__':
    # 运行性能测试
    pytest.main([
        __file__,
        '-v',
        '-m', 'performance',
        '--tb=short',
        '--durations=10'
    ])