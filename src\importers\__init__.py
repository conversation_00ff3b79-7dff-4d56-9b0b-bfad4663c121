"""Data importers for Connect telecommunications system.

This module provides a comprehensive importer architecture with specialized importers
for telecommunications data formats and generic data processing capabilities.

Architecture:
- base/: Abstract base classes and mixins for common functionality
- telecom/: Telecommunications-specific importers (CDR, KPI, EP, NLG)
- generic/: Generic format importers (CSV, Excel, JSON)

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

# Import base classes and mixins
from .base import (
    AbstractImporter,
    ValidationMixin,
    ProcessingMixin,
    PerformanceMixin,
    ImporterConfig,
    ImportResult,
    ImportStatus,
    TelecomImportError,
    create_importer_config
)

# Import telecommunications importers
from .telecom import (
    CDRImporter,
    KPIImporter,
    EPImporter,
    NLGImporter,
    CDRConfig,
    KPIConfig,
    EPConfig,
    NLGConfig,
    create_importer as create_telecom_importer,
    SUPPORTED_DATA_TYPES as TELECOM_DATA_TYPES
)

# Import generic importers
from .generic import (
    CSVImporter,
    ExcelImporter,
    JSONImporter,
    CSVConfig,
    ExcelConfig,
    JSONConfig,
    create_importer as create_generic_importer,
    SUPPORTED_FORMATS as GENERIC_FORMATS
)

# Legacy imports for backward compatibility
from .score_importer import ScoreImporter
from .cfg_importer import CFGImporter
from .batch_processor import BatchProcessor
from .data_transformer import DataTransformer

# Module metadata
__version__ = "2.0.0"
__description__ = "Refactored importer architecture for Connect telecommunications system"

# All supported importers
ALL_IMPORTERS = {
    # Telecommunications importers
    'cdr': CDRImporter,
    'kpi': KPIImporter,
    'ep': EPImporter,
    'nlg': NLGImporter,
    
    # Generic importers
    'csv': CSVImporter,
    'excel': ExcelImporter,
    'json': JSONImporter,
    
    # Legacy importers
    'score': ScoreImporter,
    'cfg': CFGImporter
}

# All supported configurations
ALL_CONFIGS = {
    # Telecommunications configs
    'cdr': CDRConfig,
    'kpi': KPIConfig,
    'ep': EPConfig,
    'nlg': NLGConfig,
    
    # Generic configs
    'csv': CSVConfig,
    'excel': ExcelConfig,
    'json': JSONConfig
}


def create_importer(importer_type: str, config: dict = None, **kwargs):
    """Universal importer factory function.
    
    Args:
        importer_type: Type of importer ('cdr', 'kpi', 'ep', 'nlg', 'csv', 'excel', 'json')
        config: Configuration dictionary
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured importer instance
        
    Raises:
        ValueError: If importer type is not supported
        
    Examples:
        >>> # Create CDR importer
        >>> cdr_importer = create_importer('cdr', {'operator': 'telefonica'})
        
        >>> # Create CSV importer
        >>> csv_importer = create_importer('csv', {'delimiter': ';', 'encoding': 'utf-8'})
        
        >>> # Create Excel importer with sheet selection
        >>> excel_importer = create_importer('excel', {'sheet_name': 'Data', 'header': 0})
    """
    if importer_type not in ALL_IMPORTERS:
        raise ValueError(f"Unsupported importer type: {importer_type}. Supported: {list(ALL_IMPORTERS.keys())}")
        
    # Use specialized factory functions for telecom and generic importers
    if importer_type in TELECOM_DATA_TYPES:
        return create_telecom_importer(importer_type, config, **kwargs)
    elif importer_type in GENERIC_FORMATS:
        return create_generic_importer(importer_type, config, **kwargs)
    else:
        # Legacy importers - create directly
        importer_class = ALL_IMPORTERS[importer_type]
        return importer_class(config=config, **kwargs)


def get_supported_importers():
    """Get all supported importer types.
    
    Returns:
        Dictionary of importer types and their descriptions
    """
    return {
        # Telecommunications importers
        'cdr': 'Call Detail Records importer',
        'kpi': 'Key Performance Indicators importer', 
        'ep': 'Engineering Parameters importer',
        'nlg': 'Network Location Geography importer',
        
        # Generic importers
        'csv': 'CSV and text file importer',
        'excel': 'Microsoft Excel file importer',
        'json': 'JSON and line-delimited JSON importer',
        
        # Legacy importers
        'score': 'Score data importer',
        'cfg': 'Configuration file importer'
    }


def get_importer_info(importer_type: str):
    """Get detailed information about a specific importer.
    
    Args:
        importer_type: Type of importer
        
    Returns:
        Dictionary with importer information
    """
    if importer_type not in ALL_IMPORTERS:
        return None
        
    importer_class = ALL_IMPORTERS[importer_type]
    config_class = ALL_CONFIGS.get(importer_type)
    
    info = {
        'type': importer_type,
        'class': importer_class.__name__,
        'module': importer_class.__module__,
        'description': importer_class.__doc__.split('\n')[0] if importer_class.__doc__ else 'No description',
        'has_config': config_class is not None
    }
    
    # Add category information
    if importer_type in TELECOM_DATA_TYPES:
        info['category'] = 'telecommunications'
        info['supported_operators'] = getattr(importer_class, 'SUPPORTED_OPERATORS', [])
    elif importer_type in GENERIC_FORMATS:
        info['category'] = 'generic'
        info['supported_extensions'] = GENERIC_FORMATS[importer_type]['extensions']
    else:
        info['category'] = 'legacy'
        
    return info


def validate_importer_config(importer_type: str, config: dict):
    """Validate configuration for a specific importer type.
    
    Args:
        importer_type: Type of importer
        config: Configuration to validate
        
    Returns:
        Tuple of (is_valid, errors)
    """
    if importer_type not in ALL_CONFIGS:
        return False, [f"No configuration validation available for {importer_type}"]
        
    config_class = ALL_CONFIGS[importer_type]
    
    try:
        config_class(**config)
        return True, []
    except Exception as e:
        return False, [str(e)]


# Export all classes and functions
__all__ = [
    # Base classes and mixins
    'AbstractImporter',
    'ValidationMixin',
    'ProcessingMixin', 
    'PerformanceMixin',
    'ImporterConfig',
    'ImportResult',
    'ImportStatus',
    'TelecomImportError',
    
    # Telecommunications importers
    'CDRImporter',
    'KPIImporter',
    'EPImporter', 
    'NLGImporter',
    'CDRConfig',
    'KPIConfig',
    'EPConfig',
    'NLGConfig',
    
    # Generic importers
    'CSVImporter',
    'ExcelImporter',
    'JSONImporter',
    'CSVConfig',
    'ExcelConfig',
    'JSONConfig',
    
    # Legacy importers
    'ScoreImporter',
    'CFGImporter',
    'BatchProcessor',
    'DataTransformer',
    
    # Constants
    'ALL_IMPORTERS',
    'ALL_CONFIGS',
    'TELECOM_DATA_TYPES',
    'GENERIC_FORMATS',
    
    # Factory functions
    'create_importer',
    'create_telecom_importer',
    'create_generic_importer',
    'create_importer_config',
    
    # Utility functions
    'get_supported_importers',
    'get_importer_info',
    'validate_importer_config'
]
