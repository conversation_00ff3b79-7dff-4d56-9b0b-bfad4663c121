"""统一验证框架配置管理

提供验证框架的配置管理功能，包括默认配置、环境特定配置和配置验证。
"""

import os
import json
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum


class ValidationMode(Enum):
    """验证模式枚举"""
    STRICT = "strict"          # 严格模式，所有错误都会导致验证失败
    LENIENT = "lenient"        # 宽松模式，只有严重错误才会导致验证失败
    WARNING_ONLY = "warning"   # 仅警告模式，不会导致验证失败


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class PerformanceConfig:
    """性能配置"""
    parallel_validation: bool = True
    max_workers: int = 8
    batch_size: int = 10000
    timeout: int = 300
    memory_limit_mb: int = 1024
    chunk_size: int = 50000
    use_multiprocessing: bool = False


@dataclass
class CacheConfig:
    """缓存配置"""
    enabled: bool = True
    max_cache_size: int = 100
    ttl_seconds: int = 3600
    cleanup_interval: int = 300
    cache_validation_results: bool = False


@dataclass
class ValidationConfig:
    """验证配置"""
    mode: ValidationMode = ValidationMode.STRICT
    fail_fast: bool = False
    max_errors_per_rule: int = 1000
    continue_on_error: bool = True
    error_sampling_rate: float = 1.0
    skip_empty_data: bool = True
    validate_schema: bool = True


@dataclass
class LoggingConfig:
    """日志配置"""
    level: LogLevel = LogLevel.INFO
    log_validation_details: bool = False
    log_performance_metrics: bool = True
    log_to_file: bool = False
    log_file_path: Optional[str] = None
    max_log_file_size_mb: int = 100
    log_rotation_count: int = 5


@dataclass
class MonitoringConfig:
    """监控配置"""
    enabled: bool = True
    collect_metrics: bool = True
    metrics_interval: int = 60
    export_metrics: bool = False
    metrics_export_path: Optional[str] = None
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        'validation_time_seconds': 30.0,
        'error_rate_percent': 10.0,
        'memory_usage_mb': 2048.0,
        'throughput_rows_per_second': 1000.0
    })


@dataclass
class SecurityConfig:
    """安全配置"""
    validate_file_paths: bool = True
    allowed_file_extensions: list = field(default_factory=lambda: ['.csv', '.xlsx', '.json'])
    max_file_size_mb: int = 1024
    sanitize_error_messages: bool = True
    mask_sensitive_data: bool = True
    sensitive_columns: list = field(default_factory=lambda: [
        'CALLING_NUMBER', 'CALLED_NUMBER', 'IMSI', 'IMEI'
    ])


@dataclass
class TelecomConfig:
    """电信业务配置"""
    # CDR配置
    cdr_required_columns: list = field(default_factory=lambda: [
        'CALLING_NUMBER', 'CALLED_NUMBER', 'CALL_START_TIME', 
        'CALL_END_TIME', 'CALL_DURATION', 'CELL_ID', 'LAC', 'IMSI'
    ])
    cdr_optional_columns: list = field(default_factory=lambda: [
        'IMEI', 'MSC_ID', 'CALL_TYPE', 'SERVICE_TYPE'
    ])
    
    # KPI配置
    kpi_required_columns: list = field(default_factory=lambda: [
        'CELL_ID', 'KPI_NAME', 'KPI_VALUE', 'MEASUREMENT_TIME', 'LAC'
    ])
    kpi_optional_columns: list = field(default_factory=lambda: [
        'SECTOR_ID', 'FREQUENCY_BAND', 'TECHNOLOGY'
    ])
    
    # 业务规则配置
    phone_number_pattern: str = r'^1[3-9]\d{9}$'
    imsi_pattern: str = r'^\d{15}$'
    cell_id_range: tuple = (1, 999999)
    lac_range: tuple = (1, 65535)
    max_call_duration: int = 86400  # 24小时
    
    # KPI值范围
    kpi_value_ranges: Dict[str, tuple] = field(default_factory=lambda: {
        'RSRP': (-140, -40),
        'RSRQ': (-20, 0),
        'SINR': (-10, 30),
        'CQI': (0, 15),
        'THROUGHPUT_DL': (0, 1000),
        'THROUGHPUT_UL': (0, 100)
    })


@dataclass
class ValidationFrameworkConfig:
    """验证框架主配置"""
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    validation: ValidationConfig = field(default_factory=ValidationConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    telecom: TelecomConfig = field(default_factory=TelecomConfig)
    
    # 自定义配置
    custom_validators: Dict[str, Any] = field(default_factory=dict)
    custom_rules: Dict[str, list] = field(default_factory=dict)
    custom_settings: Dict[str, Any] = field(default_factory=dict)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path
        self._config: Optional[ValidationFrameworkConfig] = None
        self._environment = os.getenv('CONNECT_ENV', 'development')
    
    def load_config(self, config_dict: Optional[Dict[str, Any]] = None) -> ValidationFrameworkConfig:
        """加载配置
        
        Args:
            config_dict: 配置字典，如果提供则使用该配置，否则从文件加载
            
        Returns:
            ValidationFrameworkConfig: 验证框架配置
        """
        if config_dict:
            self._config = self._create_config_from_dict(config_dict)
        elif self.config_path and os.path.exists(self.config_path):
            self._config = self._load_config_from_file()
        else:
            self._config = self._get_default_config()
        
        # 应用环境特定配置
        self._apply_environment_config()
        
        # 验证配置
        self._validate_config()
        
        return self._config
    
    def get_config(self) -> ValidationFrameworkConfig:
        """获取当前配置"""
        if self._config is None:
            self._config = self.load_config()
        return self._config
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """更新配置
        
        Args:
            updates: 配置更新字典
        """
        if self._config is None:
            self._config = self.load_config()
        
        # 递归更新配置
        self._recursive_update(self._config, updates)
        
        # 重新验证配置
        self._validate_config()
    
    def save_config(self, file_path: Optional[str] = None) -> None:
        """保存配置到文件
        
        Args:
            file_path: 保存路径，如果不提供则使用默认路径
        """
        if self._config is None:
            raise ValueError("No config to save")
        
        save_path = file_path or self.config_path or 'validation_config.json'
        
        config_dict = self._config_to_dict(self._config)
        
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    def _create_config_from_dict(self, config_dict: Dict[str, Any]) -> ValidationFrameworkConfig:
        """从字典创建配置"""
        # 获取默认配置
        config = self._get_default_config()
        
        # 递归更新配置
        self._recursive_update(config, config_dict)
        
        return config
    
    def _load_config_from_file(self) -> ValidationFrameworkConfig:
        """从文件加载配置"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        return self._create_config_from_dict(config_dict)
    
    def _get_default_config(self) -> ValidationFrameworkConfig:
        """获取默认配置"""
        return ValidationFrameworkConfig()
    
    def _apply_environment_config(self) -> None:
        """应用环境特定配置"""
        if self._environment == 'production':
            # 生产环境配置
            self._config.logging.level = LogLevel.WARNING
            self._config.logging.log_validation_details = False
            self._config.monitoring.enabled = True
            self._config.performance.parallel_validation = True
            self._config.cache.enabled = True
            self._config.security.validate_file_paths = True
            
        elif self._environment == 'testing':
            # 测试环境配置
            self._config.logging.level = LogLevel.DEBUG
            self._config.logging.log_validation_details = True
            self._config.monitoring.enabled = False
            self._config.cache.enabled = False
            self._config.validation.fail_fast = True
            
        elif self._environment == 'development':
            # 开发环境配置
            self._config.logging.level = LogLevel.INFO
            self._config.logging.log_validation_details = True
            self._config.monitoring.enabled = True
            self._config.performance.parallel_validation = False
            self._config.cache.enabled = True
    
    def _validate_config(self) -> None:
        """验证配置有效性"""
        if not self._config:
            raise ValueError("Config is None")
        
        # 验证性能配置
        if self._config.performance.max_workers <= 0:
            raise ValueError("max_workers must be positive")
        
        if self._config.performance.batch_size <= 0:
            raise ValueError("batch_size must be positive")
        
        if self._config.performance.timeout <= 0:
            raise ValueError("timeout must be positive")
        
        # 验证缓存配置
        if self._config.cache.max_cache_size <= 0:
            raise ValueError("max_cache_size must be positive")
        
        if self._config.cache.ttl_seconds <= 0:
            raise ValueError("ttl_seconds must be positive")
        
        # 验证验证配置
        if self._config.validation.max_errors_per_rule <= 0:
            raise ValueError("max_errors_per_rule must be positive")
        
        if not (0 <= self._config.validation.error_sampling_rate <= 1):
            raise ValueError("error_sampling_rate must be between 0 and 1")
        
        # 验证安全配置
        if self._config.security.max_file_size_mb <= 0:
            raise ValueError("max_file_size_mb must be positive")
        
        # 验证电信配置
        if not self._config.telecom.cdr_required_columns:
            raise ValueError("cdr_required_columns cannot be empty")
        
        if not self._config.telecom.kpi_required_columns:
            raise ValueError("kpi_required_columns cannot be empty")
    
    def _recursive_update(self, target: Any, source: Dict[str, Any]) -> None:
        """递归更新配置"""
        for key, value in source.items():
            if hasattr(target, key):
                current_value = getattr(target, key)
                if isinstance(current_value, dict) and isinstance(value, dict):
                    current_value.update(value)
                elif hasattr(current_value, '__dict__') and isinstance(value, dict):
                    self._recursive_update(current_value, value)
                else:
                    setattr(target, key, value)
    
    def _config_to_dict(self, config: ValidationFrameworkConfig) -> Dict[str, Any]:
        """将配置转换为字典"""
        result = {}
        
        for field_name, field_value in config.__dict__.items():
            if hasattr(field_value, '__dict__'):
                result[field_name] = self._dataclass_to_dict(field_value)
            else:
                result[field_name] = field_value
        
        return result
    
    def _dataclass_to_dict(self, obj: Any) -> Dict[str, Any]:
        """将数据类转换为字典"""
        result = {}
        
        for field_name, field_value in obj.__dict__.items():
            if isinstance(field_value, Enum):
                result[field_name] = field_value.value
            elif hasattr(field_value, '__dict__'):
                result[field_name] = self._dataclass_to_dict(field_value)
            else:
                result[field_name] = field_value
        
        return result


# 全局配置管理器实例
_config_manager = ConfigManager()


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器"""
    return _config_manager


def load_config(config_path: Optional[str] = None, 
                config_dict: Optional[Dict[str, Any]] = None) -> ValidationFrameworkConfig:
    """加载配置
    
    Args:
        config_path: 配置文件路径
        config_dict: 配置字典
        
    Returns:
        ValidationFrameworkConfig: 验证框架配置
    """
    if config_path:
        _config_manager.config_path = config_path
    
    return _config_manager.load_config(config_dict)


def get_config() -> ValidationFrameworkConfig:
    """获取当前配置"""
    return _config_manager.get_config()


def update_config(updates: Dict[str, Any]) -> None:
    """更新配置"""
    _config_manager.update_config(updates)


def save_config(file_path: Optional[str] = None) -> None:
    """保存配置到文件
    
    Args:
        file_path: 保存路径，如果不提供则使用默认路径
    """
    _config_manager.save_config(file_path)


# 预定义配置模板
DEVELOPMENT_CONFIG = {
    'performance': {
        'parallel_validation': False,
        'max_workers': 2,
        'batch_size': 5000
    },
    'logging': {
        'level': 'DEBUG',
        'log_validation_details': True
    },
    'cache': {
        'enabled': True,
        'max_cache_size': 10
    },
    'monitoring': {
        'enabled': True,
        'collect_metrics': True
    }
}

TESTING_CONFIG = {
    'performance': {
        'parallel_validation': False,
        'max_workers': 1,
        'batch_size': 1000,
        'timeout': 60
    },
    'logging': {
        'level': 'DEBUG',
        'log_validation_details': True
    },
    'cache': {
        'enabled': False
    },
    'validation': {
        'fail_fast': True,
        'max_errors_per_rule': 10
    },
    'monitoring': {
        'enabled': False
    }
}

PRODUCTION_CONFIG = {
    'performance': {
        'parallel_validation': True,
        'max_workers': 8,
        'batch_size': 50000,
        'timeout': 600
    },
    'logging': {
        'level': 'WARNING',
        'log_validation_details': False,
        'log_to_file': True
    },
    'cache': {
        'enabled': True,
        'max_cache_size': 1000,
        'ttl_seconds': 7200
    },
    'validation': {
        'mode': 'strict',
        'fail_fast': False,
        'max_errors_per_rule': 10000
    },
    'monitoring': {
        'enabled': True,
        'collect_metrics': True,
        'export_metrics': True
    },
    'security': {
        'validate_file_paths': True,
        'max_file_size_mb': 2048,
        'sanitize_error_messages': True,
        'mask_sensitive_data': True
    }
}

HIGH_PERFORMANCE_CONFIG = {
    'performance': {
        'parallel_validation': True,
        'max_workers': 16,
        'batch_size': 100000,
        'timeout': 1800,
        'memory_limit_mb': 4096,
        'chunk_size': 200000,
        'use_multiprocessing': True
    },
    'cache': {
        'enabled': True,
        'max_cache_size': 2000,
        'ttl_seconds': 3600
    },
    'validation': {
        'mode': 'lenient',
        'fail_fast': False,
        'error_sampling_rate': 0.1
    },
    'logging': {
        'level': 'ERROR',
        'log_validation_details': False
    }
}

MEMORY_EFFICIENT_CONFIG = {
    'performance': {
        'parallel_validation': False,
        'max_workers': 1,
        'batch_size': 5000,
        'memory_limit_mb': 256,
        'chunk_size': 10000
    },
    'cache': {
        'enabled': False
    },
    'validation': {
        'fail_fast': True,
        'max_errors_per_rule': 100
    },
    'logging': {
        'level': 'WARNING',
        'log_validation_details': False
    },
    'monitoring': {
        'enabled': False
    }
}


def get_preset_config(preset_name: str) -> Dict[str, Any]:
    """获取预设配置
    
    Args:
        preset_name: 预设名称 ('development', 'testing', 'production', 
                    'high_performance', 'memory_efficient')
                    
    Returns:
        Dict[str, Any]: 预设配置字典
        
    Raises:
        ValueError: 如果预设名称不存在
    """
    presets = {
        'development': DEVELOPMENT_CONFIG,
        'testing': TESTING_CONFIG,
        'production': PRODUCTION_CONFIG,
        'high_performance': HIGH_PERFORMANCE_CONFIG,
        'memory_efficient': MEMORY_EFFICIENT_CONFIG
    }
    
    if preset_name not in presets:
        raise ValueError(f"Unknown preset: {preset_name}. Available presets: {list(presets.keys())}")
    
    return presets[preset_name]


def create_config_from_preset(preset_name: str, 
                             overrides: Optional[Dict[str, Any]] = None) -> ValidationFrameworkConfig:
    """从预设创建配置
    
    Args:
        preset_name: 预设名称
        overrides: 覆盖配置
        
    Returns:
        ValidationFrameworkConfig: 验证框架配置
    """
    preset_config = get_preset_config(preset_name)
    
    if overrides:
        # 深度合并配置
        def deep_merge(base: dict, override: dict) -> dict:
            result = base.copy()
            for key, value in override.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result
        
        preset_config = deep_merge(preset_config, overrides)
    
    return load_config(config_dict=preset_config)