#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 测试配置管理

本模块提供统一的测试配置管理，包括：
- 测试环境配置
- 数据库连接配置
- API端点配置
- 性能测试参数
- 安全测试配置
- E2E测试配置

作者: Connect质量工程团队
创建时间: 2024-01-20
"""

import os
from dataclasses import dataclass
from typing import Dict, List, Optional
from pathlib import Path


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: int = 5432
    database: str = "connect_test"
    username: str = "test_user"
    password: str = "test_password"
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600


@dataclass
class APIConfig:
    """API配置"""
    base_url: str = "http://localhost:8000"
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    auth_endpoint: str = "/api/auth/login"
    refresh_endpoint: str = "/api/auth/refresh"
    
    # API端点
    endpoints: Dict[str, str] = None
    
    def __post_init__(self):
        if self.endpoints is None:
            self.endpoints = {
                'login': '/api/auth/login',
                'logout': '/api/auth/logout',
                'refresh': '/api/auth/refresh',
                'users': '/api/users',
                'data_import': '/api/data/import',
                'ep_data': '/api/data/ep',
                'cdr_data': '/api/data/cdr',
                'site_data': '/api/data/sites',
                'kpi_data': '/api/data/kpi',
                'drive_test': '/api/analysis/drive-test',
                'coverage_analysis': '/api/analysis/coverage',
                'gap_analysis': '/api/analysis/gap',
                'kpi_monitoring': '/api/monitoring/kpi',
                'alerts': '/api/monitoring/alerts',
                'reports': '/api/reports',
                'export': '/api/export',
                'health': '/api/health',
                'metrics': '/api/metrics'
            }


@dataclass
class PerformanceConfig:
    """性能测试配置"""
    # 负载测试参数
    max_users: int = 20
    spawn_rate: float = 2.0
    test_duration: int = 300  # 秒
    
    # 数据处理性能基准
    max_data_processing_time: int = 10  # 秒，500万行数据
    max_query_response_time: int = 3    # 秒，地理查询
    max_api_response_time: float = 0.5  # 秒，API响应
    max_page_load_time: int = 3         # 秒，页面加载
    
    # 资源使用限制
    max_cpu_usage: float = 80.0         # 百分比
    max_memory_usage: int = 16          # GB
    max_disk_io: int = 1000             # IOPS
    
    # 大数据测试参数
    large_dataset_size: int = 5000000   # 行数
    concurrent_operations: int = 10
    batch_size: int = 10000
    
    # 性能监控间隔
    monitoring_interval: float = 1.0    # 秒
    

@dataclass
class SecurityConfig:
    """安全测试配置"""
    # OWASP测试配置
    injection_payloads: List[str] = None
    xss_payloads: List[str] = None
    csrf_test_enabled: bool = True
    
    # 认证测试
    weak_passwords: List[str] = None
    brute_force_attempts: int = 10
    session_timeout: int = 1800  # 秒
    
    # 数据保护测试
    sensitive_data_patterns: List[str] = None
    encryption_algorithms: List[str] = None
    
    # 漏洞扫描配置
    scan_timeout: int = 300
    max_scan_depth: int = 3
    
    def __post_init__(self):
        if self.injection_payloads is None:
            self.injection_payloads = [
                "'; DROP TABLE users; --",
                "' OR '1'='1",
                "'; SELECT * FROM users; --",
                "' UNION SELECT username, password FROM users --",
                "'; INSERT INTO users VALUES ('hacker', 'password'); --"
            ]
        
        if self.xss_payloads is None:
            self.xss_payloads = [
                "<script>alert('XSS')</script>",
                "<img src=x onerror=alert('XSS')>",
                "javascript:alert('XSS')",
                "<svg onload=alert('XSS')>",
                "<iframe src=javascript:alert('XSS')></iframe>"
            ]
        
        if self.weak_passwords is None:
            self.weak_passwords = [
                "password", "123456", "admin", "test", "guest",
                "password123", "admin123", "qwerty", "abc123"
            ]
        
        if self.sensitive_data_patterns is None:
            self.sensitive_data_patterns = [
                r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b',  # 信用卡号
                r'\b\d{3}-\d{2}-\d{4}\b',                        # SSN
                r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # 邮箱
                r'\b1[3-9]\d{9}\b',                             # 手机号
                r'\b\d{15,19}\b'                                # 银行卡号
            ]
        
        if self.encryption_algorithms is None:
            self.encryption_algorithms = [
                'AES-256-GCM', 'AES-256-CBC', 'RSA-2048', 'RSA-4096'
            ]


@dataclass
class E2EConfig:
    """E2E测试配置"""
    # WebDriver配置
    browser: str = "chrome"
    headless: bool = True
    window_size: tuple = (1920, 1080)
    implicit_wait: int = 10
    page_load_timeout: int = 30
    script_timeout: int = 30
    
    # 测试用户配置
    test_users: Dict[str, Dict[str, str]] = None
    
    # 测试数据配置
    test_data_dir: str = "tests/data"
    download_dir: str = "tests/downloads"
    upload_dir: str = "tests/uploads"
    
    # 截图配置
    screenshot_on_failure: bool = True
    screenshot_dir: str = "tests/screenshots"
    
    # 等待配置
    default_wait_timeout: int = 10
    long_wait_timeout: int = 60
    processing_wait_timeout: int = 180
    
    def __post_init__(self):
        if self.test_users is None:
            self.test_users = {
                'admin': {
                    'username': '<EMAIL>',
                    'password': 'Admin123!',
                    'role': 'administrator'
                },
                'analyst': {
                    'username': '<EMAIL>',
                    'password': 'Analyst123!',
                    'role': 'analyst'
                },
                'viewer': {
                    'username': '<EMAIL>',
                    'password': 'Viewer123!',
                    'role': 'viewer'
                }
            }


@dataclass
class MonitoringConfig:
    """监控配置"""
    # 指标收集
    metrics_enabled: bool = True
    metrics_interval: int = 5  # 秒
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    log_file: str = "tests/logs/test.log"
    
    # 报告配置
    report_format: str = "html"
    report_dir: str = "tests/reports"
    
    # 告警配置
    alert_thresholds: Dict[str, float] = None
    
    def __post_init__(self):
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                'cpu_usage': 80.0,
                'memory_usage': 85.0,
                'disk_usage': 90.0,
                'response_time': 5.0,
                'error_rate': 5.0
            }


class _TestConfig:
    """测试配置管理器"""
    
    def __init__(self, env: str = None):
        self.env = env or os.getenv('TEST_ENV', 'development')
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        # 基础配置
        self.database = DatabaseConfig()
        self.api = APIConfig()
        self.performance = PerformanceConfig()
        self.security = SecurityConfig()
        self.e2e = E2EConfig()
        self.monitoring = MonitoringConfig()
        
        # 根据环境调整配置
        self._adjust_for_environment()
        
        # 创建必要的目录
        self._create_directories()
    
    def _adjust_for_environment(self):
        """根据环境调整配置"""
        if self.env == 'production':
            self.api.base_url = "https://connect.prod.com"
            self.database.host = "prod-db.connect.com"
            self.e2e.headless = True
            self.performance.max_users = 50
            
        elif self.env == 'staging':
            self.api.base_url = "https://connect.staging.com"
            self.database.host = "staging-db.connect.com"
            self.e2e.headless = True
            self.performance.max_users = 30
            
        elif self.env == 'testing':
            self.api.base_url = "http://test.connect.com"
            self.database.host = "test-db.connect.com"
            self.e2e.headless = True
            self.performance.max_users = 20
            
        else:  # development
            self.api.base_url = "http://localhost:8000"
            self.database.host = "localhost"
            self.e2e.headless = False
            self.performance.max_users = 10
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.e2e.test_data_dir,
            self.e2e.download_dir,
            self.e2e.upload_dir,
            self.e2e.screenshot_dir,
            self.monitoring.report_dir,
            os.path.dirname(self.monitoring.log_file)
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        return (
            f"postgresql://{self.database.username}:{self.database.password}"
            f"@{self.database.host}:{self.database.port}/{self.database.database}"
        )
    
    def get_api_url(self, endpoint: str) -> str:
        """获取API完整URL"""
        endpoint_path = self.api.endpoints.get(endpoint, endpoint)
        return f"{self.api.base_url}{endpoint_path}"
    
    def get_test_user(self, role: str) -> Dict[str, str]:
        """获取测试用户信息"""
        return self.e2e.test_users.get(role, {})
    
    def is_performance_test_enabled(self) -> bool:
        """检查是否启用性能测试"""
        return os.getenv('ENABLE_PERFORMANCE_TESTS', 'false').lower() == 'true'
    
    def is_security_test_enabled(self) -> bool:
        """检查是否启用安全测试"""
        return os.getenv('ENABLE_SECURITY_TESTS', 'false').lower() == 'true'
    
    def is_e2e_test_enabled(self) -> bool:
        """检查是否启用E2E测试"""
        return os.getenv('ENABLE_E2E_TESTS', 'true').lower() == 'true'
    
    def get_parallel_workers(self) -> int:
        """获取并行测试工作进程数"""
        return int(os.getenv('TEST_PARALLEL_WORKERS', '4'))
    
    def get_test_timeout(self) -> int:
        """获取测试超时时间"""
        return int(os.getenv('TEST_TIMEOUT', '300'))
    
    def should_generate_coverage(self) -> bool:
        """检查是否生成覆盖率报告"""
        return os.getenv('GENERATE_COVERAGE', 'true').lower() == 'true'
    
    def should_send_notifications(self) -> bool:
        """检查是否发送测试通知"""
        return os.getenv('SEND_TEST_NOTIFICATIONS', 'false').lower() == 'true'
    
    def get_notification_webhook(self) -> Optional[str]:
        """获取通知webhook URL"""
        return os.getenv('NOTIFICATION_WEBHOOK_URL')
    
    def export_config(self) -> Dict:
        """导出配置为字典"""
        return {
            'environment': self.env,
            'database': {
                'host': self.database.host,
                'port': self.database.port,
                'database': self.database.database
            },
            'api': {
                'base_url': self.api.base_url,
                'timeout': self.api.timeout
            },
            'performance': {
                'max_users': self.performance.max_users,
                'test_duration': self.performance.test_duration
            },
            'e2e': {
                'browser': self.e2e.browser,
                'headless': self.e2e.headless
            }
        }


# 全局配置实例
config = _TestConfig()


# 配置验证函数
def validate_config() -> List[str]:
    """验证配置有效性"""
    errors = []
    
    # 验证数据库配置
    if not config.database.host:
        errors.append("数据库主机地址不能为空")
    
    if not (1 <= config.database.port <= 65535):
        errors.append("数据库端口必须在1-65535范围内")
    
    # 验证API配置
    if not config.api.base_url:
        errors.append("API基础URL不能为空")
    
    if config.api.timeout <= 0:
        errors.append("API超时时间必须大于0")
    
    # 验证性能配置
    if config.performance.max_users <= 0:
        errors.append("最大用户数必须大于0")
    
    if config.performance.test_duration <= 0:
        errors.append("测试持续时间必须大于0")
    
    # 验证E2E配置
    if config.e2e.browser not in ['chrome', 'firefox', 'safari', 'edge']:
        errors.append("不支持的浏览器类型")
    
    return errors


if __name__ == "__main__":
    # 验证配置
    validation_errors = validate_config()
    
    if validation_errors:
        print("配置验证失败:")
        for error in validation_errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
        print(f"当前环境: {config.env}")
        print(f"API地址: {config.api.base_url}")
        print(f"数据库: {config.database.host}:{config.database.port}")