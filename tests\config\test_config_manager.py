"""Test configuration manager for comprehensive testing framework."""

import os
import yaml
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
import logging
from contextlib import contextmanager


logger = logging.getLogger(__name__)


@dataclass
class DatabaseTestConfig:
    """Database test configuration."""
    host: str
    port: int
    database: str
    username: str
    password: str
    schema: str
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600
    echo: bool = False
    ssl_mode: str = 'prefer'
    connection_timeout: int = 30
    command_timeout: int = 300
    

@dataclass
class PerformanceTestConfig:
    """Performance test configuration."""
    max_execution_time: float = 30.0
    max_memory_usage_mb: float = 512.0
    max_cpu_usage_percent: float = 80.0
    concurrent_users: int = 10
    ramp_up_time: int = 60
    test_duration: int = 300
    think_time: float = 1.0
    error_threshold_percent: float = 5.0
    response_time_percentiles: List[float] = field(default_factory=lambda: [50, 90, 95, 99])
    

@dataclass
class SecurityTestConfig:
    """Security test configuration."""
    enable_sql_injection_tests: bool = True
    enable_xss_tests: bool = True
    enable_csrf_tests: bool = True
    enable_auth_tests: bool = True
    enable_encryption_tests: bool = True
    test_user_credentials: Dict[str, str] = field(default_factory=dict)
    api_keys: Dict[str, str] = field(default_factory=dict)
    jwt_secret: str = 'test-secret-key'
    session_timeout: int = 3600
    

@dataclass
class IntegrationTestConfig:
    """Integration test configuration."""
    external_services: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    mock_services: List[str] = field(default_factory=list)
    test_data_sources: List[str] = field(default_factory=list)
    cleanup_after_tests: bool = True
    parallel_execution: bool = False
    max_parallel_workers: int = 4
    retry_attempts: int = 3
    retry_delay: float = 1.0
    

@dataclass
class CoverageTestConfig:
    """Code coverage test configuration."""
    target_coverage_percent: float = 80.0
    fail_under_percent: float = 75.0
    include_patterns: List[str] = field(default_factory=lambda: ['src/*'])
    exclude_patterns: List[str] = field(default_factory=lambda: ['tests/*', '*/migrations/*'])
    report_formats: List[str] = field(default_factory=lambda: ['html', 'xml', 'term'])
    report_directory: str = 'htmlcov'
    show_missing: bool = True
    skip_covered: bool = False
    

@dataclass
class TestEnvironmentConfig:
    """Test environment configuration."""
    name: str
    description: str
    database: DatabaseTestConfig
    performance: PerformanceTestConfig
    security: SecurityTestConfig
    integration: IntegrationTestConfig
    coverage: CoverageTestConfig
    logging_level: str = 'INFO'
    debug_mode: bool = False
    test_data_path: str = 'tests/fixtures'
    temp_directory: str = '/tmp/test_data'
    cleanup_temp_files: bool = True
    parallel_test_execution: bool = False
    test_timeout: int = 300
    

class TestConfigManager:
    """Manages test configurations for different environments and scenarios."""
    
    def __init__(self, config_dir: Optional[str] = None):
        """Initialize test configuration manager.
        
        Args:
            config_dir: Directory containing configuration files
        """
        self.config_dir = Path(config_dir) if config_dir else Path(__file__).parent
        self.configs: Dict[str, TestEnvironmentConfig] = {}
        self.current_config: Optional[TestEnvironmentConfig] = None
        self.config_history: List[Dict[str, Any]] = []
        
        # Load default configurations
        self._load_default_configs()
        
    def _load_default_configs(self):
        """Load default test configurations."""
        # Development environment
        dev_config = TestEnvironmentConfig(
            name='development',
            description='Development environment for local testing',
            database=DatabaseTestConfig(
                host='localhost',
                port=5432,
                database='connect_test',
                username='test_user',
                password='test_password',
                schema='public',
                echo=True
            ),
            performance=PerformanceTestConfig(
                max_execution_time=60.0,
                max_memory_usage_mb=1024.0,
                concurrent_users=5
            ),
            security=SecurityTestConfig(
                test_user_credentials={'test_user': 'test_password'},
                api_keys={'test_api': 'test_key_123'}
            ),
            integration=IntegrationTestConfig(
                mock_services=['external_api', 'payment_service'],
                parallel_execution=False
            ),
            coverage=CoverageTestConfig(
                target_coverage_percent=75.0,
                fail_under_percent=70.0
            ),
            debug_mode=True
        )
        
        # CI/CD environment
        ci_config = TestEnvironmentConfig(
            name='ci',
            description='CI/CD environment for automated testing',
            database=DatabaseTestConfig(
                host=os.getenv('TEST_DB_HOST', 'localhost'),
                port=int(os.getenv('TEST_DB_PORT', '5432')),
                database=os.getenv('TEST_DB_NAME', 'connect_ci_test'),
                username=os.getenv('TEST_DB_USER', 'ci_user'),
                password=os.getenv('TEST_DB_PASSWORD', 'ci_password'),
                schema='public',
                echo=False
            ),
            performance=PerformanceTestConfig(
                max_execution_time=30.0,
                max_memory_usage_mb=512.0,
                concurrent_users=10
            ),
            security=SecurityTestConfig(
                test_user_credentials={
                    'ci_user': os.getenv('CI_TEST_PASSWORD', 'ci_password')
                },
                api_keys={
                    'ci_api': os.getenv('CI_API_KEY', 'ci_key_123')
                }
            ),
            integration=IntegrationTestConfig(
                mock_services=['external_api', 'payment_service', 'notification_service'],
                parallel_execution=True,
                max_parallel_workers=4
            ),
            coverage=CoverageTestConfig(
                target_coverage_percent=80.0,
                fail_under_percent=75.0
            ),
            parallel_test_execution=True,
            debug_mode=False
        )
        
        # Production-like environment
        prod_config = TestEnvironmentConfig(
            name='production',
            description='Production-like environment for integration testing',
            database=DatabaseTestConfig(
                host=os.getenv('PROD_TEST_DB_HOST', 'prod-test-db'),
                port=int(os.getenv('PROD_TEST_DB_PORT', '5432')),
                database=os.getenv('PROD_TEST_DB_NAME', 'connect_prod_test'),
                username=os.getenv('PROD_TEST_DB_USER', 'prod_test_user'),
                password=os.getenv('PROD_TEST_DB_PASSWORD', 'prod_test_password'),
                schema='public',
                pool_size=20,
                max_overflow=40,
                echo=False
            ),
            performance=PerformanceTestConfig(
                max_execution_time=15.0,
                max_memory_usage_mb=256.0,
                concurrent_users=50,
                test_duration=600
            ),
            security=SecurityTestConfig(
                enable_sql_injection_tests=True,
                enable_xss_tests=True,
                enable_csrf_tests=True,
                enable_auth_tests=True,
                enable_encryption_tests=True
            ),
            integration=IntegrationTestConfig(
                mock_services=[],  # Use real services in prod-like environment
                parallel_execution=True,
                max_parallel_workers=8,
                cleanup_after_tests=True
            ),
            coverage=CoverageTestConfig(
                target_coverage_percent=85.0,
                fail_under_percent=80.0
            ),
            parallel_test_execution=True,
            test_timeout=600
        )
        
        self.configs = {
            'development': dev_config,
            'ci': ci_config,
            'production': prod_config
        }
        
        # Set default environment
        env_name = os.getenv('TEST_ENVIRONMENT', 'development')
        self.set_environment(env_name)
        
    def load_config_from_file(self, filepath: str) -> TestEnvironmentConfig:
        """Load configuration from file.
        
        Args:
            filepath: Path to configuration file
            
        Returns:
            TestEnvironmentConfig object
        """
        filepath = Path(filepath)
        
        if not filepath.exists():
            raise FileNotFoundError(f"Configuration file not found: {filepath}")
            
        with open(filepath, 'r', encoding='utf-8') as f:
            if filepath.suffix.lower() in ['.yml', '.yaml']:
                config_data = yaml.safe_load(f)
            elif filepath.suffix.lower() == '.json':
                config_data = json.load(f)
            else:
                raise ValueError(f"Unsupported configuration file format: {filepath.suffix}")
                
        return self._parse_config_data(config_data)
        
    def _parse_config_data(self, config_data: Dict[str, Any]) -> TestEnvironmentConfig:
        """Parse configuration data into TestEnvironmentConfig object.
        
        Args:
            config_data: Configuration data dictionary
            
        Returns:
            TestEnvironmentConfig object
        """
        # Parse database config
        db_data = config_data.get('database', {})
        database_config = DatabaseTestConfig(**db_data)
        
        # Parse performance config
        perf_data = config_data.get('performance', {})
        performance_config = PerformanceTestConfig(**perf_data)
        
        # Parse security config
        sec_data = config_data.get('security', {})
        security_config = SecurityTestConfig(**sec_data)
        
        # Parse integration config
        int_data = config_data.get('integration', {})
        integration_config = IntegrationTestConfig(**int_data)
        
        # Parse coverage config
        cov_data = config_data.get('coverage', {})
        coverage_config = CoverageTestConfig(**cov_data)
        
        # Create environment config
        env_config = TestEnvironmentConfig(
            name=config_data.get('name', 'custom'),
            description=config_data.get('description', 'Custom configuration'),
            database=database_config,
            performance=performance_config,
            security=security_config,
            integration=integration_config,
            coverage=coverage_config,
            logging_level=config_data.get('logging_level', 'INFO'),
            debug_mode=config_data.get('debug_mode', False),
            test_data_path=config_data.get('test_data_path', 'tests/fixtures'),
            temp_directory=config_data.get('temp_directory', '/tmp/test_data'),
            cleanup_temp_files=config_data.get('cleanup_temp_files', True),
            parallel_test_execution=config_data.get('parallel_test_execution', False),
            test_timeout=config_data.get('test_timeout', 300)
        )
        
        return env_config
        
    def save_config_to_file(self, config: TestEnvironmentConfig, filepath: str):
        """Save configuration to file.
        
        Args:
            config: Configuration to save
            filepath: Output file path
        """
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        # Convert config to dictionary
        config_dict = {
            'name': config.name,
            'description': config.description,
            'database': {
                'host': config.database.host,
                'port': config.database.port,
                'database': config.database.database,
                'username': config.database.username,
                'password': config.database.password,
                'schema': config.database.schema,
                'pool_size': config.database.pool_size,
                'max_overflow': config.database.max_overflow,
                'pool_timeout': config.database.pool_timeout,
                'pool_recycle': config.database.pool_recycle,
                'echo': config.database.echo,
                'ssl_mode': config.database.ssl_mode,
                'connection_timeout': config.database.connection_timeout,
                'command_timeout': config.database.command_timeout
            },
            'performance': {
                'max_execution_time': config.performance.max_execution_time,
                'max_memory_usage_mb': config.performance.max_memory_usage_mb,
                'max_cpu_usage_percent': config.performance.max_cpu_usage_percent,
                'concurrent_users': config.performance.concurrent_users,
                'ramp_up_time': config.performance.ramp_up_time,
                'test_duration': config.performance.test_duration,
                'think_time': config.performance.think_time,
                'error_threshold_percent': config.performance.error_threshold_percent,
                'response_time_percentiles': config.performance.response_time_percentiles
            },
            'security': {
                'enable_sql_injection_tests': config.security.enable_sql_injection_tests,
                'enable_xss_tests': config.security.enable_xss_tests,
                'enable_csrf_tests': config.security.enable_csrf_tests,
                'enable_auth_tests': config.security.enable_auth_tests,
                'enable_encryption_tests': config.security.enable_encryption_tests,
                'test_user_credentials': config.security.test_user_credentials,
                'api_keys': config.security.api_keys,
                'jwt_secret': config.security.jwt_secret,
                'session_timeout': config.security.session_timeout
            },
            'integration': {
                'external_services': config.integration.external_services,
                'mock_services': config.integration.mock_services,
                'test_data_sources': config.integration.test_data_sources,
                'cleanup_after_tests': config.integration.cleanup_after_tests,
                'parallel_execution': config.integration.parallel_execution,
                'max_parallel_workers': config.integration.max_parallel_workers,
                'retry_attempts': config.integration.retry_attempts,
                'retry_delay': config.integration.retry_delay
            },
            'coverage': {
                'target_coverage_percent': config.coverage.target_coverage_percent,
                'fail_under_percent': config.coverage.fail_under_percent,
                'include_patterns': config.coverage.include_patterns,
                'exclude_patterns': config.coverage.exclude_patterns,
                'report_formats': config.coverage.report_formats,
                'report_directory': config.coverage.report_directory,
                'show_missing': config.coverage.show_missing,
                'skip_covered': config.coverage.skip_covered
            },
            'logging_level': config.logging_level,
            'debug_mode': config.debug_mode,
            'test_data_path': config.test_data_path,
            'temp_directory': config.temp_directory,
            'cleanup_temp_files': config.cleanup_temp_files,
            'parallel_test_execution': config.parallel_test_execution,
            'test_timeout': config.test_timeout
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            if filepath.suffix.lower() in ['.yml', '.yaml']:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
            elif filepath.suffix.lower() == '.json':
                json.dump(config_dict, f, indent=2)
            else:
                raise ValueError(f"Unsupported configuration file format: {filepath.suffix}")
                
    def set_environment(self, env_name: str):
        """Set current test environment.
        
        Args:
            env_name: Name of the environment to set
        """
        if env_name not in self.configs:
            raise ValueError(f"Unknown environment: {env_name}. Available: {list(self.configs.keys())}")
            
        self.current_config = self.configs[env_name]
        
        # Record configuration change
        self.config_history.append({
            'timestamp': datetime.now(),
            'environment': env_name,
            'action': 'set_environment'
        })
        
        logger.info(f"Test environment set to: {env_name}")
        
    def get_current_config(self) -> Optional[TestEnvironmentConfig]:
        """Get current test configuration.
        
        Returns:
            Current TestEnvironmentConfig or None
        """
        return self.current_config
        
    def get_config(self, env_name: str) -> Optional[TestEnvironmentConfig]:
        """Get configuration for specific environment.
        
        Args:
            env_name: Name of the environment
            
        Returns:
            TestEnvironmentConfig or None
        """
        return self.configs.get(env_name)
        
    def add_config(self, config: TestEnvironmentConfig):
        """Add new test configuration.
        
        Args:
            config: TestEnvironmentConfig to add
        """
        self.configs[config.name] = config
        
        # Record configuration addition
        self.config_history.append({
            'timestamp': datetime.now(),
            'environment': config.name,
            'action': 'add_config'
        })
        
        logger.info(f"Added test configuration: {config.name}")
        
    def remove_config(self, env_name: str):
        """Remove test configuration.
        
        Args:
            env_name: Name of the environment to remove
        """
        if env_name in self.configs:
            del self.configs[env_name]
            
            # Record configuration removal
            self.config_history.append({
                'timestamp': datetime.now(),
                'environment': env_name,
                'action': 'remove_config'
            })
            
            logger.info(f"Removed test configuration: {env_name}")
            
            # Reset current config if it was removed
            if self.current_config and self.current_config.name == env_name:
                self.current_config = None
                
    def list_environments(self) -> List[str]:
        """List available test environments.
        
        Returns:
            List of environment names
        """
        return list(self.configs.keys())
        
    def validate_config(self, config: TestEnvironmentConfig) -> List[str]:
        """Validate test configuration.
        
        Args:
            config: Configuration to validate
            
        Returns:
            List of validation errors
        """
        errors = []
        
        # Validate database config
        if not config.database.host:
            errors.append("Database host is required")
        if not config.database.database:
            errors.append("Database name is required")
        if not config.database.username:
            errors.append("Database username is required")
            
        # Validate performance config
        if config.performance.max_execution_time <= 0:
            errors.append("Max execution time must be positive")
        if config.performance.max_memory_usage_mb <= 0:
            errors.append("Max memory usage must be positive")
        if config.performance.concurrent_users <= 0:
            errors.append("Concurrent users must be positive")
            
        # Validate coverage config
        if not (0 <= config.coverage.target_coverage_percent <= 100):
            errors.append("Target coverage percent must be between 0 and 100")
        if not (0 <= config.coverage.fail_under_percent <= 100):
            errors.append("Fail under percent must be between 0 and 100")
            
        return errors
        
    @contextmanager
    def temporary_config(self, config: TestEnvironmentConfig):
        """Temporarily use a different configuration.
        
        Args:
            config: Temporary configuration to use
        """
        original_config = self.current_config
        
        try:
            self.current_config = config
            yield config
        finally:
            self.current_config = original_config
            
    def get_database_url(self, config: Optional[TestEnvironmentConfig] = None) -> str:
        """Get database URL for current or specified configuration.
        
        Args:
            config: Optional specific configuration to use
            
        Returns:
            Database URL string
        """
        cfg = config or self.current_config
        if not cfg:
            raise ValueError("No configuration available")
            
        db = cfg.database
        return f"postgresql://{db.username}:{db.password}@{db.host}:{db.port}/{db.database}"
        
    def get_config_summary(self) -> Dict[str, Any]:
        """Get summary of current configuration.
        
        Returns:
            Configuration summary dictionary
        """
        if not self.current_config:
            return {'error': 'No configuration set'}
            
        config = self.current_config
        return {
            'environment': config.name,
            'description': config.description,
            'database_host': config.database.host,
            'database_name': config.database.database,
            'debug_mode': config.debug_mode,
            'parallel_execution': config.parallel_test_execution,
            'coverage_target': config.coverage.target_coverage_percent,
            'performance_max_time': config.performance.max_execution_time,
            'performance_max_memory': config.performance.max_memory_usage_mb
        }


# Global configuration manager instance
config_manager = TestConfigManager()


# Convenience functions
def get_current_config() -> Optional[TestEnvironmentConfig]:
    """Get current test configuration."""
    return config_manager.get_current_config()


def set_test_environment(env_name: str):
    """Set test environment."""
    config_manager.set_environment(env_name)


def get_database_url() -> str:
    """Get database URL for current configuration."""
    return config_manager.get_database_url()