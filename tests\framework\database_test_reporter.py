#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 数据库测试报告生成器
基于docs/database/database-framework.md需求的测试报告生成工具

本模块提供：
- 多格式测试报告生成
- 详细的测试结果分析
- 性能指标统计
- 质量门控报告
- CI/CD集成支持
"""

import json
import logging
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
import xml.etree.ElementTree as ET
from xml.dom import minidom
import base64
import io

# 导入绘图库
try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    import seaborn as sns
    import pandas as pd
    import numpy as np
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    plt = None
    sns = None
    pd = None
    np = None

from tests.framework.comprehensive_test_framework import (
    TestExecutionResult,
    QualityGate,
    TestPriority,
    TestStatus
)


@dataclass
class ReportConfig:
    """报告配置"""
    title: str = "Connect数据库测试报告"
    subtitle: str = "电信数据分析平台测试执行报告"
    author: str = "Database Test Framework"
    version: str = "1.0.0"
    include_charts: bool = True
    include_details: bool = True
    include_logs: bool = False
    chart_style: str = "seaborn"  # seaborn, ggplot, classic
    color_scheme: str = "default"  # default, colorblind, dark
    language: str = "zh_CN"
    timezone: str = "Asia/Shanghai"
    

@dataclass
class TestMetrics:
    """测试指标统计"""
    total_tests: int = 0
    passed_tests: int = 0
    failed_tests: int = 0
    skipped_tests: int = 0
    error_tests: int = 0
    success_rate: float = 0.0
    total_duration: float = 0.0
    average_duration: float = 0.0
    min_duration: float = 0.0
    max_duration: float = 0.0
    coverage_percentage: float = 0.0
    
    def __post_init__(self):
        if self.total_tests > 0:
            self.success_rate = (self.passed_tests / self.total_tests) * 100
            self.average_duration = self.total_duration / self.total_tests


@dataclass
class PerformanceMetrics:
    """性能指标统计"""
    avg_response_time: float = 0.0
    max_response_time: float = 0.0
    min_response_time: float = 0.0
    p95_response_time: float = 0.0
    p99_response_time: float = 0.0
    throughput: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    database_connections: int = 0
    query_count: int = 0
    slow_queries: int = 0
    

class DatabaseTestReporter:
    """数据库测试报告生成器"""
    
    def __init__(self, config: ReportConfig = None):
        self.config = config or ReportConfig()
        self.logger = logging.getLogger(__name__)
        
        # 设置绘图样式
        if PLOTTING_AVAILABLE and self.config.include_charts:
            self._setup_plotting_style()
        
        # 报告数据
        self.test_results: Dict[TestPriority, List[TestExecutionResult]] = {}
        self.quality_gates: Dict[str, bool] = {}
        self.test_metrics: TestMetrics = TestMetrics()
        self.performance_metrics: PerformanceMetrics = PerformanceMetrics()
        self.execution_metadata: Dict[str, Any] = {}
        
    def _setup_plotting_style(self):
        """设置绘图样式"""
        if not PLOTTING_AVAILABLE:
            return
        
        # 设置样式
        if self.config.chart_style == "seaborn":
            sns.set_style("whitegrid")
            plt.style.use('seaborn-v0_8')
        elif self.config.chart_style == "ggplot":
            plt.style.use('ggplot')
        else:
            plt.style.use('classic')
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置颜色方案
        if self.config.color_scheme == "colorblind":
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
            plt.rcParams['axes.prop_cycle'] = plt.cycler(color=colors)
    
    def add_test_results(self, results: Dict[TestPriority, List[TestExecutionResult]]):
        """添加测试结果"""
        self.test_results = results
        self._calculate_test_metrics()
        self._calculate_performance_metrics()
    
    def add_quality_gates(self, quality_gates: Dict[str, bool]):
        """添加质量门控结果"""
        self.quality_gates = quality_gates
    
    def add_execution_metadata(self, metadata: Dict[str, Any]):
        """添加执行元数据"""
        self.execution_metadata = metadata
    
    def _calculate_test_metrics(self):
        """计算测试指标"""
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        skipped_tests = 0
        error_tests = 0
        total_duration = 0.0
        durations = []
        
        for priority, results in self.test_results.items():
            for result in results:
                total_tests += result.tests_total
                passed_tests += result.tests_passed
                failed_tests += result.tests_failed
                skipped_tests += result.tests_skipped
                
                if result.status == TestStatus.FAILED:
                    error_tests += 1
                
                total_duration += result.execution_time
                durations.append(result.execution_time)
        
        self.test_metrics = TestMetrics(
            total_tests=total_tests,
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            skipped_tests=skipped_tests,
            error_tests=error_tests,
            total_duration=total_duration,
            min_duration=min(durations) if durations else 0.0,
            max_duration=max(durations) if durations else 0.0,
            coverage_percentage=85.0  # 模拟覆盖率
        )
    
    def _calculate_performance_metrics(self):
        """计算性能指标"""
        response_times = []
        memory_usage = []
        cpu_usage = []
        
        for priority, results in self.test_results.items():
            for result in results:
                if result.performance_metrics:
                    # 提取响应时间
                    if 'response_time_ms' in result.performance_metrics:
                        response_times.append(result.performance_metrics['response_time_ms'])
                    
                    # 提取内存使用
                    if 'memory_usage_mb' in result.performance_metrics:
                        memory_usage.append(result.performance_metrics['memory_usage_mb'])
                    
                    # 提取CPU使用
                    if 'cpu_usage_percent' in result.performance_metrics:
                        cpu_usage.append(result.performance_metrics['cpu_usage_percent'])
        
        if response_times:
            response_times_sorted = sorted(response_times)
            n = len(response_times_sorted)
            
            self.performance_metrics = PerformanceMetrics(
                avg_response_time=sum(response_times) / len(response_times),
                max_response_time=max(response_times),
                min_response_time=min(response_times),
                p95_response_time=response_times_sorted[int(n * 0.95)] if n > 0 else 0,
                p99_response_time=response_times_sorted[int(n * 0.99)] if n > 0 else 0,
                memory_usage_mb=sum(memory_usage) / len(memory_usage) if memory_usage else 0,
                cpu_usage_percent=sum(cpu_usage) / len(cpu_usage) if cpu_usage else 0
            )
    
    def _generate_charts(self, output_dir: Path) -> Dict[str, str]:
        """生成图表"""
        if not PLOTTING_AVAILABLE or not self.config.include_charts:
            return {}
        
        charts = {}
        chart_dir = output_dir / 'charts'
        chart_dir.mkdir(exist_ok=True)
        
        try:
            # 1. 测试结果饼图
            fig, ax = plt.subplots(figsize=(10, 8))
            
            labels = ['通过', '失败', '跳过']
            sizes = [self.test_metrics.passed_tests, 
                    self.test_metrics.failed_tests, 
                    self.test_metrics.skipped_tests]
            colors = ['#2ecc71', '#e74c3c', '#f39c12']
            
            # 过滤掉为0的数据
            filtered_data = [(label, size, color) for label, size, color in zip(labels, sizes, colors) if size > 0]
            if filtered_data:
                labels, sizes, colors = zip(*filtered_data)
                
                wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
                ax.set_title('测试结果分布', fontsize=16, fontweight='bold')
                
                # 美化文本
                for autotext in autotexts:
                    autotext.set_color('white')
                    autotext.set_fontweight('bold')
            
            plt.tight_layout()
            chart_path = chart_dir / 'test_results_pie.png'
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            charts['test_results_pie'] = str(chart_path)
            
            # 2. 优先级测试结果柱状图
            fig, ax = plt.subplots(figsize=(12, 8))
            
            priorities = []
            passed_counts = []
            failed_counts = []
            
            for priority in [TestPriority.P0, TestPriority.P1, TestPriority.P2, TestPriority.P3]:
                if priority in self.test_results:
                    priorities.append(priority.value)
                    
                    total_passed = sum(r.tests_passed for r in self.test_results[priority])
                    total_failed = sum(r.tests_failed for r in self.test_results[priority])
                    
                    passed_counts.append(total_passed)
                    failed_counts.append(total_failed)
            
            if priorities:
                x = np.arange(len(priorities))
                width = 0.35
                
                bars1 = ax.bar(x - width/2, passed_counts, width, label='通过', color='#2ecc71')
                bars2 = ax.bar(x + width/2, failed_counts, width, label='失败', color='#e74c3c')
                
                ax.set_xlabel('测试优先级', fontsize=12)
                ax.set_ylabel('测试数量', fontsize=12)
                ax.set_title('各优先级测试结果', fontsize=16, fontweight='bold')
                ax.set_xticks(x)
                ax.set_xticklabels(priorities)
                ax.legend()
                
                # 添加数值标签
                for bar in bars1:
                    height = bar.get_height()
                    if height > 0:
                        ax.annotate(f'{int(height)}',
                                  xy=(bar.get_x() + bar.get_width() / 2, height),
                                  xytext=(0, 3),
                                  textcoords="offset points",
                                  ha='center', va='bottom')
                
                for bar in bars2:
                    height = bar.get_height()
                    if height > 0:
                        ax.annotate(f'{int(height)}',
                                  xy=(bar.get_x() + bar.get_width() / 2, height),
                                  xytext=(0, 3),
                                  textcoords="offset points",
                                  ha='center', va='bottom')
            
            plt.tight_layout()
            chart_path = chart_dir / 'priority_results_bar.png'
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            charts['priority_results_bar'] = str(chart_path)
            
            # 3. 执行时间趋势图
            if len(self.test_results) > 1:
                fig, ax = plt.subplots(figsize=(12, 6))
                
                suite_names = []
                execution_times = []
                
                for priority, results in self.test_results.items():
                    for result in results:
                        suite_names.append(f"{priority.value}_{result.suite_name}")
                        execution_times.append(result.execution_time)
                
                if suite_names and execution_times:
                    ax.plot(range(len(execution_times)), execution_times, marker='o', linewidth=2, markersize=6)
                    ax.set_xlabel('测试套件', fontsize=12)
                    ax.set_ylabel('执行时间 (秒)', fontsize=12)
                    ax.set_title('测试执行时间趋势', fontsize=16, fontweight='bold')
                    ax.grid(True, alpha=0.3)
                    
                    # 设置x轴标签（旋转以避免重叠）
                    ax.set_xticks(range(len(suite_names)))
                    ax.set_xticklabels(suite_names, rotation=45, ha='right')
                
                plt.tight_layout()
                chart_path = chart_dir / 'execution_time_trend.png'
                plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                plt.close()
                charts['execution_time_trend'] = str(chart_path)
            
            # 4. 性能指标雷达图
            if any([self.performance_metrics.avg_response_time,
                   self.performance_metrics.memory_usage_mb,
                   self.performance_metrics.cpu_usage_percent]):
                
                fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
                
                # 性能指标（标准化到0-100）
                metrics = {
                    '响应时间': min(100, self.performance_metrics.avg_response_time / 10),  # 假设10ms为满分
                    '内存使用': min(100, self.performance_metrics.memory_usage_mb / 10),    # 假设1GB为满分
                    'CPU使用': self.performance_metrics.cpu_usage_percent,
                    '成功率': self.test_metrics.success_rate,
                    '覆盖率': self.test_metrics.coverage_percentage
                }
                
                # 反转某些指标（越低越好）
                normalized_metrics = {
                    '响应时间': 100 - metrics['响应时间'],
                    '内存使用': 100 - metrics['内存使用'],
                    'CPU使用': 100 - metrics['CPU使用'],
                    '成功率': metrics['成功率'],
                    '覆盖率': metrics['覆盖率']
                }
                
                labels = list(normalized_metrics.keys())
                values = list(normalized_metrics.values())
                
                # 计算角度
                angles = np.linspace(0, 2 * np.pi, len(labels), endpoint=False).tolist()
                values += values[:1]  # 闭合图形
                angles += angles[:1]
                
                # 绘制雷达图
                ax.plot(angles, values, 'o-', linewidth=2, color='#3498db')
                ax.fill(angles, values, alpha=0.25, color='#3498db')
                ax.set_xticks(angles[:-1])
                ax.set_xticklabels(labels)
                ax.set_ylim(0, 100)
                ax.set_title('性能指标雷达图', fontsize=16, fontweight='bold', pad=20)
                ax.grid(True)
                
                plt.tight_layout()
                chart_path = chart_dir / 'performance_radar.png'
                plt.savefig(chart_path, dpi=300, bbox_inches='tight')
                plt.close()
                charts['performance_radar'] = str(chart_path)
            
            self.logger.info(f"生成了 {len(charts)} 个图表")
            
        except Exception as e:
            self.logger.error(f"生成图表时出错: {e}")
        
        return charts
    
    def _encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64字符串"""
        try:
            with open(image_path, 'rb') as img_file:
                return base64.b64encode(img_file.read()).decode('utf-8')
        except Exception as e:
            self.logger.error(f"编码图片失败 {image_path}: {e}")
            return ""
    
    def generate_html_report(self, output_path: Path) -> Path:
        """生成HTML格式报告"""
        self.logger.info("生成HTML报告")
        
        # 生成图表
        charts = self._generate_charts(output_path.parent)
        
        # 计算统计信息
        quality_gates_passed = sum(1 for passed in self.quality_gates.values() if passed)
        quality_gates_total = len(self.quality_gates)
        
        # 生成HTML内容
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{self.config.title}</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }}
        
        .header p {{
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .metric-card {{
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }}
        
        .metric-card:hover {{
            transform: translateY(-5px);
        }}
        
        .metric-card h3 {{
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }}
        
        .metric-value {{
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        
        .metric-value.success {{ color: #27ae60; }}
        .metric-value.warning {{ color: #f39c12; }}
        .metric-value.danger {{ color: #e74c3c; }}
        .metric-value.info {{ color: #3498db; }}
        
        .section {{
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }}
        
        .section-header {{
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
        }}
        
        .section-header h2 {{
            color: #495057;
            font-size: 1.5em;
            font-weight: 600;
        }}
        
        .section-content {{
            padding: 20px;
        }}
        
        .chart-container {{
            text-align: center;
            margin: 20px 0;
        }}
        
        .chart-container img {{
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }}
        
        .test-results-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        
        .test-results-table th,
        .test-results-table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }}
        
        .test-results-table th {{
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }}
        
        .test-results-table tr:hover {{
            background-color: #f8f9fa;
        }}
        
        .status-badge {{
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }}
        
        .status-passed {{ background-color: #d4edda; color: #155724; }}
        .status-failed {{ background-color: #f8d7da; color: #721c24; }}
        .status-skipped {{ background-color: #fff3cd; color: #856404; }}
        
        .quality-gates {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }}
        
        .quality-gate {{
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid;
        }}
        
        .quality-gate.passed {{
            background-color: #d4edda;
            border-left-color: #28a745;
        }}
        
        .quality-gate.failed {{
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }}
        
        .quality-gate h4 {{
            margin-bottom: 10px;
            font-size: 1.1em;
        }}
        
        .performance-metrics {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }}
        
        .performance-metric {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }}
        
        .performance-metric .label {{
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }}
        
        .performance-metric .value {{
            font-size: 1.5em;
            font-weight: bold;
            color: #495057;
        }}
        
        .footer {{
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }}
        
        @media (max-width: 768px) {{
            .container {{
                padding: 10px;
            }}
            
            .header h1 {{
                font-size: 2em;
            }}
            
            .summary-grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>{self.config.title}</h1>
            <p>{self.config.subtitle}</p>
            <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>测试环境: {self.execution_metadata.get('environment', 'development')}</p>
        </div>
        
        <!-- 概览指标 -->
        <div class="summary-grid">
            <div class="metric-card">
                <h3>总测试数</h3>
                <div class="metric-value info">{self.test_metrics.total_tests}</div>
            </div>
            <div class="metric-card">
                <h3>通过测试</h3>
                <div class="metric-value success">{self.test_metrics.passed_tests}</div>
            </div>
            <div class="metric-card">
                <h3>失败测试</h3>
                <div class="metric-value danger">{self.test_metrics.failed_tests}</div>
            </div>
            <div class="metric-card">
                <h3>成功率</h3>
                <div class="metric-value {'success' if self.test_metrics.success_rate >= 95 else 'warning' if self.test_metrics.success_rate >= 80 else 'danger'}">{self.test_metrics.success_rate:.1f}%</div>
            </div>
            <div class="metric-card">
                <h3>执行时间</h3>
                <div class="metric-value info">{self.test_metrics.total_duration:.2f}s</div>
            </div>
            <div class="metric-card">
                <h3>代码覆盖率</h3>
                <div class="metric-value {'success' if self.test_metrics.coverage_percentage >= 80 else 'warning'}">{self.test_metrics.coverage_percentage:.1f}%</div>
            </div>
        </div>
"""
        
        # 添加图表部分
        if charts:
            html_content += """
        <!-- 图表分析 -->
        <div class="section">
            <div class="section-header">
                <h2>📊 图表分析</h2>
            </div>
            <div class="section-content">
"""
            
            for chart_name, chart_path in charts.items():
                chart_title = {
                    'test_results_pie': '测试结果分布',
                    'priority_results_bar': '各优先级测试结果',
                    'execution_time_trend': '测试执行时间趋势',
                    'performance_radar': '性能指标雷达图'
                }.get(chart_name, chart_name)
                
                # 将图片编码为base64
                img_base64 = self._encode_image_to_base64(chart_path)
                if img_base64:
                    html_content += f"""
                <div class="chart-container">
                    <h3>{chart_title}</h3>
                    <img src="data:image/png;base64,{img_base64}" alt="{chart_title}">
                </div>
"""
            
            html_content += """
            </div>
        </div>
"""
        
        # 添加质量门控部分
        if self.quality_gates:
            html_content += """
        <!-- 质量门控 -->
        <div class="section">
            <div class="section-header">
                <h2>🚪 质量门控</h2>
            </div>
            <div class="section-content">
                <div class="quality-gates">
"""
            
            for gate_name, passed in self.quality_gates.items():
                status_class = "passed" if passed else "failed"
                status_text = "✅ 通过" if passed else "❌ 失败"
                
                html_content += f"""
                    <div class="quality-gate {status_class}">
                        <h4>{gate_name}</h4>
                        <p><strong>状态:</strong> {status_text}</p>
                    </div>
"""
            
            html_content += """
                </div>
            </div>
        </div>
"""
        
        # 添加性能指标部分
        if any([self.performance_metrics.avg_response_time,
               self.performance_metrics.memory_usage_mb,
               self.performance_metrics.cpu_usage_percent]):
            html_content += """
        <!-- 性能指标 -->
        <div class="section">
            <div class="section-header">
                <h2>⚡ 性能指标</h2>
            </div>
            <div class="section-content">
                <div class="performance-metrics">
"""
            
            if self.performance_metrics.avg_response_time > 0:
                html_content += f"""
                    <div class="performance-metric">
                        <div class="label">平均响应时间</div>
                        <div class="value">{self.performance_metrics.avg_response_time:.2f}ms</div>
                    </div>
                    <div class="performance-metric">
                        <div class="label">P95响应时间</div>
                        <div class="value">{self.performance_metrics.p95_response_time:.2f}ms</div>
                    </div>
                    <div class="performance-metric">
                        <div class="label">最大响应时间</div>
                        <div class="value">{self.performance_metrics.max_response_time:.2f}ms</div>
                    </div>
"""
            
            if self.performance_metrics.memory_usage_mb > 0:
                html_content += f"""
                    <div class="performance-metric">
                        <div class="label">内存使用</div>
                        <div class="value">{self.performance_metrics.memory_usage_mb:.1f}MB</div>
                    </div>
"""
            
            if self.performance_metrics.cpu_usage_percent > 0:
                html_content += f"""
                    <div class="performance-metric">
                        <div class="label">CPU使用率</div>
                        <div class="value">{self.performance_metrics.cpu_usage_percent:.1f}%</div>
                    </div>
"""
            
            html_content += """
                </div>
            </div>
        </div>
"""
        
        # 添加详细测试结果
        if self.config.include_details:
            html_content += """
        <!-- 详细测试结果 -->
        <div class="section">
            <div class="section-header">
                <h2>📋 详细测试结果</h2>
            </div>
            <div class="section-content">
                <table class="test-results-table">
                    <thead>
                        <tr>
                            <th>优先级</th>
                            <th>测试套件</th>
                            <th>状态</th>
                            <th>通过/总数</th>
                            <th>执行时间</th>
                            <th>错误信息</th>
                        </tr>
                    </thead>
                    <tbody>
"""
            
            for priority, results in self.test_results.items():
                for result in results:
                    status_class = "passed" if result.status == TestStatus.PASSED else "failed"
                    status_text = "通过" if result.status == TestStatus.PASSED else "失败"
                    error_msg = result.error_message[:100] + "..." if result.error_message and len(result.error_message) > 100 else (result.error_message or "")
                    
                    html_content += f"""
                        <tr>
                            <td>{priority.value}</td>
                            <td>{result.suite_name}</td>
                            <td><span class="status-badge status-{status_class}">{status_text}</span></td>
                            <td>{result.tests_passed}/{result.tests_total}</td>
                            <td>{result.execution_time:.2f}s</td>
                            <td>{error_msg}</td>
                        </tr>
"""
            
            html_content += """
                    </tbody>
                </table>
            </div>
        </div>
"""
        
        # 添加页脚
        html_content += f"""
        <!-- 页脚 -->
        <div class="footer">
            <p>报告由 {self.config.author} 生成 | 版本 {self.config.version}</p>
            <p>框架版本: Database Test Framework v1.0.0</p>
        </div>
    </div>
</body>
</html>
"""
        
        # 保存HTML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        self.logger.info(f"HTML报告已生成: {output_path}")
        return output_path
    
    def generate_json_report(self, output_path: Path) -> Path:
        """生成JSON格式报告"""
        self.logger.info("生成JSON报告")
        
        report_data = {
            'metadata': {
                'title': self.config.title,
                'generated_at': datetime.now().isoformat(),
                'generator': self.config.author,
                'version': self.config.version,
                'execution_metadata': self.execution_metadata
            },
            'summary': {
                'test_metrics': asdict(self.test_metrics),
                'performance_metrics': asdict(self.performance_metrics),
                'quality_gates': {
                    'total': len(self.quality_gates),
                    'passed': sum(1 for passed in self.quality_gates.values() if passed),
                    'failed': sum(1 for passed in self.quality_gates.values() if not passed),
                    'results': self.quality_gates
                }
            },
            'test_results': {},
            'detailed_results': []
        }
        
        # 添加按优先级分组的结果
        for priority, results in self.test_results.items():
            report_data['test_results'][priority.value] = {
                'total_suites': len(results),
                'total_tests': sum(r.tests_total for r in results),
                'passed_tests': sum(r.tests_passed for r in results),
                'failed_tests': sum(r.tests_failed for r in results),
                'skipped_tests': sum(r.tests_skipped for r in results),
                'total_duration': sum(r.execution_time for r in results),
                'suites': [asdict(result) for result in results]
            }
            
            # 添加详细结果
            for result in results:
                report_data['detailed_results'].append({
                    'priority': priority.value,
                    'suite_name': result.suite_name,
                    'status': result.status.value,
                    'tests_total': result.tests_total,
                    'tests_passed': result.tests_passed,
                    'tests_failed': result.tests_failed,
                    'tests_skipped': result.tests_skipped,
                    'execution_time': result.execution_time,
                    'start_time': result.start_time,
                    'end_time': result.end_time,
                    'coverage_percentage': result.coverage_percentage,
                    'performance_metrics': result.performance_metrics,
                    'error_message': result.error_message
                })
        
        # 保存JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        self.logger.info(f"JSON报告已生成: {output_path}")
        return output_path
    
    def generate_junit_report(self, output_path: Path) -> Path:
        """生成JUnit XML格式报告"""
        self.logger.info("生成JUnit XML报告")
        
        # 创建根元素
        testsuites = ET.Element('testsuites')
        testsuites.set('name', 'Database Test Framework')
        testsuites.set('tests', str(self.test_metrics.total_tests))
        testsuites.set('failures', str(self.test_metrics.failed_tests))
        testsuites.set('skipped', str(self.test_metrics.skipped_tests))
        testsuites.set('time', str(self.test_metrics.total_duration))
        testsuites.set('timestamp', datetime.now().isoformat())
        
        # 为每个优先级创建测试套件
        for priority, results in self.test_results.items():
            for result in results:
                testsuite = ET.SubElement(testsuites, 'testsuite')
                testsuite.set('name', f"{priority.value}_{result.suite_name}")
                testsuite.set('tests', str(result.tests_total))
                testsuite.set('failures', str(result.tests_failed))
                testsuite.set('skipped', str(result.tests_skipped))
                testsuite.set('time', str(result.execution_time))
                testsuite.set('timestamp', str(result.start_time))
                
                # 添加属性
                properties = ET.SubElement(testsuite, 'properties')
                
                prop = ET.SubElement(properties, 'property')
                prop.set('name', 'priority')
                prop.set('value', priority.value)
                
                prop = ET.SubElement(properties, 'property')
                prop.set('name', 'coverage')
                prop.set('value', str(result.coverage_percentage))
                
                if result.performance_metrics:
                    for key, value in result.performance_metrics.items():
                        prop = ET.SubElement(properties, 'property')
                        prop.set('name', f'performance.{key}')
                        prop.set('value', str(value))
                
                # 创建测试用例（简化版本）
                for i in range(result.tests_total):
                    testcase = ET.SubElement(testsuite, 'testcase')
                    testcase.set('name', f'test_{i+1}')
                    testcase.set('classname', result.suite_name)
                    testcase.set('time', str(result.execution_time / result.tests_total))
                    
                    # 如果有失败的测试，添加失败信息
                    if i < result.tests_failed and result.error_message:
                        failure = ET.SubElement(testcase, 'failure')
                        failure.set('message', 'Test failed')
                        failure.text = result.error_message
                
                # 添加系统输出
                if result.error_message:
                    system_err = ET.SubElement(testsuite, 'system-err')
                    system_err.text = result.error_message
        
        # 格式化XML
        rough_string = ET.tostring(testsuites, 'utf-8')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent='  ')
        
        # 保存XML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(pretty_xml)
        
        self.logger.info(f"JUnit XML报告已生成: {output_path}")
        return output_path
    
    def generate_all_reports(self, output_dir: Path, formats: List[str] = None) -> Dict[str, Path]:
        """生成所有格式的报告"""
        if formats is None:
            formats = ['html', 'json', 'junit']
        
        output_dir.mkdir(parents=True, exist_ok=True)
        generated_reports = {}
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        for format_type in formats:
            try:
                if format_type.lower() == 'html':
                    report_path = output_dir / f'test_report_{timestamp}.html'
                    generated_reports['html'] = self.generate_html_report(report_path)
                
                elif format_type.lower() == 'json':
                    report_path = output_dir / f'test_report_{timestamp}.json'
                    generated_reports['json'] = self.generate_json_report(report_path)
                
                elif format_type.lower() == 'junit':
                    report_path = output_dir / f'test_report_{timestamp}.xml'
                    generated_reports['junit'] = self.generate_junit_report(report_path)
                
                else:
                    self.logger.warning(f"不支持的报告格式: {format_type}")
            
            except Exception as e:
                self.logger.error(f"生成 {format_type} 报告时出错: {e}")
        
        self.logger.info(f"报告生成完成，共生成 {len(generated_reports)} 个报告文件")
        return generated_reports


def create_test_reporter(config: Dict[str, Any] = None) -> DatabaseTestReporter:
    """创建测试报告生成器的工厂函数"""
    if config:
        report_config = ReportConfig(**config)
    else:
        report_config = ReportConfig()
    
    return DatabaseTestReporter(report_config)


if __name__ == "__main__":
    # 示例用法
    import argparse
    
    parser = argparse.ArgumentParser(description="数据库测试报告生成器")
    parser.add_argument('--input', required=True, help='测试结果JSON文件路径')
    parser.add_argument('--output', default='./reports', help='报告输出目录')
    parser.add_argument('--formats', nargs='+', choices=['html', 'json', 'junit'], 
                       default=['html', 'json'], help='生成的报告格式')
    parser.add_argument('--title', default='Connect数据库测试报告', help='报告标题')
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 创建报告生成器
    config = ReportConfig(title=args.title)
    reporter = DatabaseTestReporter(config)
    
    # 加载测试结果（这里需要根据实际的数据格式进行调整）
    try:
        with open(args.input, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
        
        # 这里需要根据实际的数据结构来解析测试结果
        # reporter.add_test_results(parsed_results)
        # reporter.add_quality_gates(quality_gates)
        # reporter.add_execution_metadata(metadata)
        
        # 生成报告
        output_dir = Path(args.output)
        reports = reporter.generate_all_reports(output_dir, args.formats)
        
        print(f"\n报告生成完成！")
        for format_type, path in reports.items():
            print(f"  {format_type.upper()}: {path}")
    
    except Exception as e:
        print(f"生成报告时出错: {e}")