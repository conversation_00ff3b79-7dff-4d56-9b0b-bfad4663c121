#!/usr/bin/env python3
"""
Database logging framework.

This module provides logging configuration and utilities for the database framework,
following the LOGGING_CONFIG structure defined in the PRD.
"""

import logging
import logging.config
import os
import time
from collections import defaultdict
from contextlib import contextmanager
from pathlib import Path
from typing import Any, Dict, Generator, Optional

import psutil

# Handle relative imports with fallback
try:
    from ...config import get_config
    from ...config.models import ConnectConfig, LoggingConfig
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    # Add parent directories to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from config import get_config
    from config.models import ConnectConfig, LoggingConfig


class DatabaseLogger:
    """Logger for database operations with structured logging support."""

    def __init__(self, name: str = "database") -> None:
        """Initialize database logger.

        Args:
            name: Logger name prefix
        """
        self.name = name
        self.logger = logging.getLogger(name)

    def info(self, message: str, **kwargs) -> None:
        """Log info level message with optional metadata."""
        self.logger.info(message, extra=kwargs)

    def error(self, message: str, error: Optional[Exception] = None, **kwargs) -> None:
        """Log error level message with optional error details."""
        if error:
            kwargs["error"] = str(error)
        self.logger.error(message, extra=kwargs)

    def warning(self, message: str, **kwargs) -> None:
        """Log warning level message with optional metadata."""
        self.logger.warning(message, extra=kwargs)

    def debug(self, message: str, **kwargs) -> None:
        """Log debug level message with optional metadata."""
        self.logger.debug(message, extra=kwargs)
    
    def critical(self, message: str, **kwargs) -> None:
        """Log critical level message with optional metadata."""
        self.logger.critical(message, extra=kwargs)

    def log_structured(
        self, level: str, message: str, metadata: Dict[str, Any]
    ) -> None:
        """Log message with structured metadata."""
        getattr(self.logger, level)(message, extra=metadata)

    @contextmanager
    def operation_context(
        self, operation_name: str, **kwargs
    ) -> Generator[None, None, None]:
        """Context manager for tracking database operations."""
        start_time = time.time()
        self.info(f"Starting {operation_name}", **kwargs)
        try:
            yield
        finally:
            duration = time.time() - start_time
            self.info(f"Completed {operation_name}", duration=duration, **kwargs)


class QueryLogger:
    """Logger for database query operations with performance tracking."""

    def __init__(self, name: str = "query") -> None:
        """Initialize query logger.

        Args:
            name: Logger name prefix
        """
        self.name = name
        self.logger = logging.getLogger(name)
        self.slow_query_threshold = 1.0  # seconds
        self.sensitive_fields = [
            "password",
            "passwd",
            "pwd",
            "secret",
            "token",
            "key",
            "api_key",
            "ssn",
            "social_security",
            "credit_card",
            "card_number",
            "cvv",
            "cvc",
            "email",
            "phone",
            "address",
            "birth_date",
            "salary",
            "income",
        ]
        self.query_stats = defaultdict(int)
        self.total_duration = 0.0
        self.query_count = 0

    def log_query(self, query: str, params: list, duration: float) -> None:
        """Log a database query with parameters and execution time."""
        self.query_count += 1
        self.total_duration += duration

        # Sanitize sensitive parameters
        sanitized_params = self._sanitize_params(params)

        # Extract query type
        query_type = query.strip().split()[0].upper()
        self.query_stats[query_type] += 1

        if duration > self.slow_query_threshold:
            self.logger.warning(
                f"Slow query detected: {query}",
                extra={
                    "query": query,
                    "params": sanitized_params,
                    "duration": duration,
                    "query_type": query_type,
                },
            )
        else:
            self.logger.info(
                f"Query executed: {query_type}",
                extra={
                    "query": query,
                    "params": sanitized_params,
                    "duration": duration,
                    "query_type": query_type,
                },
            )

    def log_query_error(self, query: str, params: list, error: Exception) -> None:
        """Log a query error."""
        sanitized_params = self._sanitize_params(params)
        self.logger.error(
            f"Query error: {str(error)}",
            extra={"query": query, "params": sanitized_params, "error": str(error)},
        )

    def get_statistics(self) -> Dict[str, Any]:
        """Get query execution statistics."""
        return {
            "total_queries": self.query_count,
            "average_duration": self.total_duration / max(self.query_count, 1),
            "query_types": dict(self.query_stats),
        }

    def _sanitize_params(self, params: list) -> list:
        """Sanitize sensitive parameters for logging."""
        sanitized = []
        for param in params:
            if isinstance(param, str) and any(
                field in param.lower() for field in self.sensitive_fields
            ):
                sanitized.append("[REDACTED]")
            else:
                sanitized.append(param)
        return sanitized


class PerformanceLogger:
    """Logger for tracking and monitoring performance metrics."""

    def __init__(self, name: str = "performance") -> None:
        """Initialize performance logger.

        Args:
            name: Logger name prefix
        """
        self.name = name
        self.logger = logging.getLogger(name)
        self.metrics = defaultdict(lambda: {"count": 0, "total_time": 0.0})
        self.slow_operation_threshold = 1.0  # seconds
        self._operation_start_times: Dict[str, float] = {}

    def start_operation(self, operation_name: str) -> None:
        """Start timing an operation."""
        self._operation_start_times[operation_name] = time.time()
        self.logger.info(
            f"Operation {operation_name} started",
            extra={"operation": operation_name},
        )

    def end_operation(self, operation_name: str) -> None:
        """End timing an operation and log its duration."""
        start_time = self._operation_start_times.pop(operation_name, None)
        if start_time is None:
            self.logger.warning(
                f"end_operation called for {operation_name} without a start_operation call.",
                extra={"operation": operation_name},
            )
            return

        duration = time.time() - start_time
        self.metrics[operation_name]["count"] += 1
        self.metrics[operation_name]["total_time"] += duration

        log_level = "warning" if duration > self.slow_operation_threshold else "info"
        getattr(self.logger, log_level)(
            f"Operation {operation_name} completed",
            extra={"duration": duration, "operation": operation_name},
        )

    @contextmanager
    def time_operation(self, operation_name: str) -> Generator[None, None, None]:
        """Context manager for timing operations."""
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            self.metrics[operation_name]["count"] += 1
            self.metrics[operation_name]["total_time"] += duration

            self.logger.info(
                f"Operation {operation_name} completed",
                extra={"duration": duration, "operation": operation_name},
            )

            if duration > self.slow_operation_threshold:
                self.logger.warning(
                    f"Slow operation detected: {operation_name}",
                    extra={
                        "duration": duration,
                        "threshold": self.slow_operation_threshold,
                    },
                )

    def get_performance_metrics(self) -> Dict[str, Dict[str, float]]:
        """Get collected performance metrics."""
        return dict(self.metrics)

    def get_memory_usage(self) -> int:
        """Get current process memory usage in bytes."""
        return psutil.Process().memory_info().rss

    def get_cpu_usage(self) -> float:
        """Get current CPU usage percentage."""
        return psutil.cpu_percent()


def setup_logging(config: Optional[ConnectConfig] = None) -> None:
    """Set up logging configuration based on PRD LOGGING_CONFIG structure.

    Args:
        config: Configuration object containing logging settings.
                If None, uses default configuration.
    """
    if config is None:
        config = get_config()

    logging_config = config.logging

    # Ensure logs directory exists
    log_file = logging_config.file or "logs/database.log"
    log_dir = Path(log_file).parent
    log_dir.mkdir(parents=True, exist_ok=True)

    # Build logging configuration following PRD LOGGING_CONFIG structure
    logging_dict_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {"standard": {"format": logging_config.format}},
        "handlers": {
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": log_file,
                "formatter": "standard",
                "maxBytes": logging_config.max_bytes,
                "backupCount": logging_config.backup_count,
                "encoding": "utf-8",
            },
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "standard",
                "stream": "ext://sys.stdout",
            },
        },
        "loggers": {
            "database": {
                "handlers": ["file", "console"],
                "level": logging_config.level,
                "propagate": False,
            },
            "src.database": {
                "handlers": ["file", "console"],
                "level": logging_config.level,
                "propagate": False,
            },
        },
        "root": {"level": logging_config.level, "handlers": ["console"]},
    }

    # Apply the logging configuration
    logging.config.dictConfig(logging_dict_config)

    # Log successful setup
    logger = get_logger("database.setup")
    logger.info(f"Logging configured successfully. Log file: {log_file}")
    logger.info(f"Log level: {logging_config.level}")


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the specified name.

    Args:
        name: Logger name. Will be prefixed with 'database.' if not already.

    Returns:
        logging.Logger: Configured logger instance.
    """
    # Ensure logger name starts with 'database.' for consistency
    if not name.startswith("database."):
        if name == "database":
            logger_name = name
        else:
            logger_name = f"database.{name}"
    else:
        logger_name = name

    return logging.getLogger(logger_name)


def configure_module_logger(
    module_name: str, level: Optional[str] = None
) -> logging.Logger:
    """Configure a logger for a specific module.

    Args:
        module_name: Name of the module (e.g., 'connection', 'monitoring').
        level: Optional log level override.

    Returns:
        logging.Logger: Configured logger for the module.
    """
    logger_name = f"database.{module_name}"
    logger = logging.getLogger(logger_name)

    if level:
        logger.setLevel(getattr(logging, level.upper()))

    return logger


# Pre-configured loggers for common modules
def get_connection_logger() -> logging.Logger:
    """Get logger for database connection operations."""
    return get_logger("connection")


def get_monitoring_logger() -> logging.Logger:
    """Get logger for monitoring operations."""
    return get_logger("monitoring")


def get_config_logger() -> logging.Logger:
    """Get logger for configuration operations."""
    return get_logger("config")


def get_exception_logger() -> logging.Logger:
    """Get logger for exception handling."""
    return get_logger("exceptions")
