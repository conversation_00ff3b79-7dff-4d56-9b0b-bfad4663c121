"""Tests for Schema Router functionality."""

import pytest
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

from src.database.schema.router import (
    SchemaRouter,
    RoutingStrategy,
    RoutingRule,
    SchemaRoutingError,
)
from src.database.schema.manager import SchemaManager
from src.config import get_config
from src.config.models import ConnectConfig as Config


class TestSchemaRouter:
    """Test cases for SchemaRouter class."""
    
    @pytest.fixture
    def mock_schema_manager(self):
        """Create a mock schema manager."""
        manager = AsyncMock(spec=SchemaManager)
        manager.schema_exists.return_value = True
        manager.list_schemas.return_value = ["public", "ep_to2", "cdr_to2", "nlg_to2"]
        return manager
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration."""
        config = Mock(spec=Config)
        config.data_sources = {
            "ep": {"schema_name": "ep_to2", "table_name_pattern": "ep_*"},
            "cdr": {"schema_name": "cdr_to2", "table_name_pattern": "cdr_*"},
            "nlg": {"schema_name": "nlg_to2", "table_name_pattern": "nlg_*"},
            "kpi": {"schema_name": "kpi_to2", "table_name_pattern": "kpi_*"},
        }
        return config
    
    @pytest.fixture
    def schema_router(self, mock_schema_manager, mock_config):
        """Create a SchemaRouter instance for testing."""
        with patch('src.database.schema.router.get_config', return_value=mock_config):
            return SchemaRouter(mock_schema_manager)
    
    def test_initialization(self, schema_router, mock_config):
        """Test SchemaRouter initialization."""
        assert schema_router.default_schema == "public"
        assert len(schema_router.routing_rules) == len(mock_config.data_sources)
        
        # Check that data source rules were created
        rule_names = [rule.name for rule in schema_router.routing_rules]
        assert "data_source_ep" in rule_names
        assert "data_source_cdr" in rule_names
    
    def test_add_routing_rule(self, schema_router):
        """Test adding a new routing rule."""
        rule = RoutingRule(
            name="test_rule",
            strategy=RoutingStrategy.TENANT,
            condition={"tenant_id": "tenant1"},
            target_schema="tenant1_schema",
            priority=20
        )
        
        initial_count = len(schema_router.routing_rules)
        schema_router.add_routing_rule(rule)
        
        assert len(schema_router.routing_rules) == initial_count + 1
        assert rule in schema_router.routing_rules
        
        # Check that rules are sorted by priority
        priorities = [rule.priority for rule in schema_router.routing_rules]
        assert priorities == sorted(priorities, reverse=True)
    
    def test_add_duplicate_rule(self, schema_router):
        """Test adding a duplicate routing rule."""
        rule = RoutingRule(
            name="data_source_ep",  # This already exists
            strategy=RoutingStrategy.TENANT,
            condition={"tenant_id": "tenant1"},
            target_schema="tenant1_schema"
        )
        
        with pytest.raises(SchemaRoutingError, match="already exists"):
            schema_router.add_routing_rule(rule)
    
    def test_add_invalid_rule(self, schema_router):
        """Test adding an invalid routing rule."""
        # Rule with invalid schema name
        rule = RoutingRule(
            name="invalid_rule",
            strategy=RoutingStrategy.TENANT,
            condition={"tenant_id": "tenant1"},
            target_schema="invalid-schema-name!"
        )
        
        with pytest.raises(SchemaRoutingError, match="Invalid target schema name"):
            schema_router.add_routing_rule(rule)
    
    def test_remove_routing_rule(self, schema_router):
        """Test removing a routing rule."""
        initial_count = len(schema_router.routing_rules)
        
        # Remove existing rule
        result = schema_router.remove_routing_rule("data_source_ep")
        assert result is True
        assert len(schema_router.routing_rules) == initial_count - 1
        
        # Try to remove non-existent rule
        result = schema_router.remove_routing_rule("non_existent_rule")
        assert result is False
    
    def test_route_schema_data_source(self, schema_router):
        """Test schema routing based on data source."""
        context = {"data_source": "ep"}
        schema = schema_router.route_schema(context)
        assert schema == "ep_to2"
        
        context = {"data_source": "cdr"}
        schema = schema_router.route_schema(context)
        assert schema == "cdr_to2"
    
    def test_route_schema_tenant(self, schema_router):
        """Test schema routing based on tenant."""
        # Add tenant routing rule
        rule = RoutingRule(
            name="tenant_rule",
            strategy=RoutingStrategy.TENANT,
            condition={"tenant_id": "tenant1"},
            target_schema="tenant1_schema",
            priority=30
        )
        schema_router.add_routing_rule(rule)
        
        context = {"tenant_id": "tenant1"}
        schema = schema_router.route_schema(context)
        assert schema == "tenant1_schema"
    
    def test_route_schema_custom(self, schema_router):
        """Test custom schema routing."""
        # Add custom routing rule
        rule = RoutingRule(
            name="custom_rule",
            strategy=RoutingStrategy.CUSTOM,
            condition={
                "table_name": {"operator": "startswith", "value": "special_"}
            },
            target_schema="special_schema",
            priority=25
        )
        schema_router.add_routing_rule(rule)
        
        context = {"table_name": "special_table_1"}
        schema = schema_router.route_schema(context)
        assert schema == "special_schema"
        
        context = {"table_name": "normal_table"}
        schema = schema_router.route_schema(context)
        # Should fall back to default or other rules
        assert schema != "special_schema"
    
    def test_route_schema_default(self, schema_router):
        """Test default schema routing."""
        # Empty context should return default schema
        schema = schema_router.route_schema({})
        assert schema == "public"
        
        # Context with no matching rules should return default schema
        context = {"unknown_key": "unknown_value"}
        schema = schema_router.route_schema(context)
        assert schema == "public"
    
    @pytest.mark.asyncio
    async def test_switch_schema(self, schema_router, mock_schema_manager):
        """Test schema switching."""
        # Switch to existing schema
        result = await schema_router.switch_schema("ep_to2")
        assert result is True
        mock_schema_manager.schema_exists.assert_called_with("ep_to2")
        
        # Switch to non-existing schema without creation
        mock_schema_manager.schema_exists.return_value = False
        with pytest.raises(SchemaRoutingError, match="Schema does not exist"):
            await schema_router.switch_schema("non_existent_schema")
        
        # Switch to non-existing schema with creation
        result = await schema_router.switch_schema("new_schema", create_if_not_exists=True)
        assert result is True
        mock_schema_manager.create_schema.assert_called_with("new_schema")
    
    @pytest.mark.asyncio
    async def test_switch_schema_invalid_name(self, schema_router):
        """Test switching to schema with invalid name."""
        with pytest.raises(SchemaRoutingError, match="Invalid schema name"):
            await schema_router.switch_schema("invalid-schema-name!")
    
    def test_get_schema_for_table(self, schema_router):
        """Test getting schema for a specific table."""
        # Table name that matches data source pattern
        schema = schema_router.get_schema_for_table("ep_cell_2024_cw01")
        assert schema == "ep_to2"  # Should infer ep data source
        
        # Table name with explicit context
        context = {"data_source": "cdr"}
        schema = schema_router.get_schema_for_table("some_table", context)
        assert schema == "cdr_to2"
    
    def test_get_tenant_schema(self, schema_router):
        """Test getting schema for a tenant."""
        # Add tenant rule first
        rule = RoutingRule(
            name="tenant_rule",
            strategy=RoutingStrategy.TENANT,
            condition={"tenant_id": "tenant1"},
            target_schema="tenant1_schema",
            priority=30
        )
        schema_router.add_routing_rule(rule)
        
        schema = schema_router.get_tenant_schema("tenant1")
        assert schema == "tenant1_schema"
        
        # Non-existent tenant should return default
        schema = schema_router.get_tenant_schema("non_existent_tenant")
        assert schema == "public"
    
    @pytest.mark.asyncio
    async def test_list_available_schemas(self, schema_router, mock_schema_manager):
        """Test listing available schemas."""
        schemas = await schema_router.list_available_schemas()
        assert schemas == ["public", "ep_to2", "cdr_to2", "nlg_to2"]
        mock_schema_manager.list_schemas.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_validate_routing_configuration(self, schema_router, mock_schema_manager):
        """Test routing configuration validation."""
        # All schemas exist
        mock_schema_manager.schema_exists.return_value = True
        report = await schema_router.validate_routing_configuration()
        
        assert report["valid"] is True
        assert len(report["issues"]) == 0
        assert report["rules_count"] > 0
        assert len(report["schemas_referenced"]) > 0
    
    @pytest.mark.asyncio
    async def test_validate_routing_configuration_with_issues(self, schema_router, mock_schema_manager):
        """Test routing configuration validation with issues."""
        # Add rule with invalid schema name
        rule = RoutingRule(
            name="invalid_rule",
            strategy=RoutingStrategy.TENANT,
            condition={"tenant_id": "tenant1"},
            target_schema="invalid-schema!"
        )
        schema_router.routing_rules.append(rule)
        
        # Some schemas don't exist
        mock_schema_manager.schema_exists.side_effect = lambda name: name != "ep_to2"
        
        report = await schema_router.validate_routing_configuration()
        
        assert report["valid"] is False
        assert len(report["issues"]) > 0
    
    def test_set_get_default_schema(self, schema_router):
        """Test setting and getting default schema."""
        # Set valid default schema
        schema_router.set_default_schema("new_default")
        assert schema_router.get_default_schema() == "new_default"
        
        # Try to set invalid default schema
        with pytest.raises(SchemaRoutingError, match="Invalid default schema name"):
            schema_router.set_default_schema("invalid-schema!")
    
    def test_matches_tenant_rule_list(self, schema_router):
        """Test tenant rule matching with list of tenant IDs."""
        rule = RoutingRule(
            name="multi_tenant_rule",
            strategy=RoutingStrategy.TENANT,
            condition={"tenant_id": ["tenant1", "tenant2", "tenant3"]},
            target_schema="multi_tenant_schema"
        )
        schema_router.add_routing_rule(rule)
        
        # Should match any tenant in the list
        context = {"tenant_id": "tenant2"}
        schema = schema_router.route_schema(context)
        assert schema == "multi_tenant_schema"
        
        # Should not match tenant not in the list
        context = {"tenant_id": "tenant4"}
        schema = schema_router.route_schema(context)
        assert schema != "multi_tenant_schema"
    
    def test_custom_rule_operators(self, schema_router):
        """Test custom rule with different operators."""
        # Test endswith operator
        rule1 = RoutingRule(
            name="endswith_rule",
            strategy=RoutingStrategy.CUSTOM,
            condition={"table_name": {"operator": "endswith", "value": "_log"}},
            target_schema="log_schema",
            priority=30
        )
        schema_router.add_routing_rule(rule1)
        
        context = {"table_name": "application_log"}
        schema = schema_router.route_schema(context)
        assert schema == "log_schema"
        
        # Test contains operator
        rule2 = RoutingRule(
            name="contains_rule",
            strategy=RoutingStrategy.CUSTOM,
            condition={"table_name": {"operator": "contains", "value": "temp"}},
            target_schema="temp_schema",
            priority=25
        )
        schema_router.add_routing_rule(rule2)
        
        context = {"table_name": "my_temp_table"}
        schema = schema_router.route_schema(context)
        assert schema == "temp_schema"
    
    def test_custom_rule_regex(self, schema_router):
        """Test custom rule with regex operator."""
        rule = RoutingRule(
            name="regex_rule",
            strategy=RoutingStrategy.CUSTOM,
            condition={"table_name": {"operator": "regex", "value": r"^test_\d+_.*"}},
            target_schema="test_schema",
            priority=30
        )
        schema_router.add_routing_rule(rule)
        
        # Should match regex pattern
        context = {"table_name": "test_123_data"}
        schema = schema_router.route_schema(context)
        assert schema == "test_schema"
        
        # Should not match regex pattern
        context = {"table_name": "test_abc_data"}
        schema = schema_router.route_schema(context)
        assert schema != "test_schema"
    
    def test_rule_priority_ordering(self, schema_router):
        """Test that rules are applied in priority order."""
        # Add high priority rule
        high_priority_rule = RoutingRule(
            name="high_priority",
            strategy=RoutingStrategy.CUSTOM,
            condition={"priority": "high"},
            target_schema="high_priority_schema",
            priority=100
        )
        schema_router.add_routing_rule(high_priority_rule)
        
        # Add low priority rule that would also match
        low_priority_rule = RoutingRule(
            name="low_priority",
            strategy=RoutingStrategy.CUSTOM,
            condition={"priority": "high"},  # Same condition
            target_schema="low_priority_schema",
            priority=1
        )
        schema_router.add_routing_rule(low_priority_rule)
        
        # High priority rule should be matched first
        context = {"priority": "high"}
        schema = schema_router.route_schema(context)
        assert schema == "high_priority_schema"
    
    def test_disabled_rule(self, schema_router):
        """Test that disabled rules are not applied."""
        rule = RoutingRule(
            name="disabled_rule",
            strategy=RoutingStrategy.CUSTOM,
            condition={"test": "value"},
            target_schema="disabled_schema",
            priority=100,
            enabled=False  # Disabled rule
        )
        schema_router.add_routing_rule(rule)
        
        context = {"test": "value"}
        schema = schema_router.route_schema(context)
        assert schema != "disabled_schema"  # Should not match disabled rule
        assert schema == "public"  # Should fall back to default