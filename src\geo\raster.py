"""Raster Data Processing Module

Provides raster data processing functionality based on rasterio.
"""

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import geopandas as gpd
import numpy as np
import rasterio
from rasterio import features, mask, warp, windows
from rasterio.crs import CRS
from rasterio.enums import Resampling
from rasterio.transform import from_bounds
from shapely.geometry import box

logger = logging.getLogger(__name__)


class RasterProcessor:
    """Raster Data Processor

    Provides raster data reading, processing, analysis and output functionality.
    """

    def __init__(self, default_crs: str = "EPSG:4326"):
        """
        Initialize raster processor

        Args:
            default_crs: Default coordinate reference system
        """
        self.default_crs = default_crs
        self.logger = logging.getLogger(self.__class__.__name__)

    def read_raster(
        self, file_path: Union[str, Path], band: Optional[int] = None
    ) -> Tuple[np.ndarray, dict]:
        """
        Read raster file

        Args:
            file_path: Raster file path
            band: Specified band to read, None means read all bands

        Returns:
            tuple: (data array, metadata dictionary)

        Raises:
            FileNotFoundError: File does not exist
            ValueError: File format not supported
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"File does not exist: {file_path}")

        try:
            with rasterio.open(file_path) as src:
                if band is not None:
                    data = src.read(band)
                else:
                    data = src.read()

                metadata = {
                    "crs": src.crs,
                    "transform": src.transform,
                    "width": src.width,
                    "height": src.height,
                    "count": src.count,
                    "dtype": src.dtypes[0] if src.count > 0 else None,
                    "nodata": src.nodata,
                    "bounds": src.bounds,
                }

            self.logger.info(
                f"Successfully read raster file: {file_path}, shape: {data.shape}"
            )
            return data, metadata

        except Exception as e:
            self.logger.error(f"Failed to read raster file: {file_path}, error: {e}")
            raise ValueError(f"Cannot read raster file: {e}")

    def create_raster_from_array(
        self,
        data: np.ndarray,
        transform: rasterio.Affine,
        crs: Union[str, CRS],
        nodata: Optional[float] = None,
    ) -> Dict[str, Any]:
        """
        Create raster data structure from array

        Args:
            data: Raster data array
            transform: Affine transformation matrix
            crs: Coordinate reference system
            nodata: No data value

        Returns:
            dict: Raster data structure
        """
        if data.ndim == 2:
            data = data[np.newaxis, :, :]

        raster_data = {
            "data": data,
            "metadata": {
                "crs": CRS.from_string(crs) if isinstance(crs, str) else crs,
                "transform": transform,
                "width": data.shape[2],
                "height": data.shape[1],
                "count": data.shape[0],
                "dtype": data.dtype,
                "nodata": nodata,
            },
        }

        self.logger.info(f"Created raster data structure, shape: {data.shape}")
        return raster_data

    def reproject_raster(
        self,
        data: np.ndarray,
        src_metadata: dict,
        target_crs: Union[str, CRS],
        resampling: Resampling = Resampling.nearest,
    ) -> Tuple[np.ndarray, dict]:
        """
        Raster reprojection

        Args:
            data: Source raster data
            src_metadata: Source raster metadata
            target_crs: Target coordinate system
            resampling: Resampling method

        Returns:
            tuple: (reprojected data, new metadata)
        """
        try:
            target_crs = (
                CRS.from_string(target_crs)
                if isinstance(target_crs, str)
                else target_crs
            )

            # Calculate target transformation and dimensions
            transform, width, height = warp.calculate_default_transform(
                src_metadata["crs"],
                target_crs,
                src_metadata["width"],
                src_metadata["height"],
                *rasterio.transform.array_bounds(
                    src_metadata["height"],
                    src_metadata["width"],
                    src_metadata["transform"],
                ),
            )

            # Create target array
            if data.ndim == 2:
                data = data[np.newaxis, :, :]

            dst_data = np.zeros((data.shape[0], height, width), dtype=data.dtype)

            # Execute reprojection
            warp.reproject(
                source=data,
                destination=dst_data,
                src_transform=src_metadata["transform"],
                src_crs=src_metadata["crs"],
                dst_transform=transform,
                dst_crs=target_crs,
                resampling=resampling,
                src_nodata=src_metadata.get("nodata"),
                dst_nodata=src_metadata.get("nodata"),
            )

            # Update metadata
            new_metadata = src_metadata.copy()
            new_metadata.update(
                {
                    "crs": target_crs,
                    "transform": transform,
                    "width": width,
                    "height": height,
                }
            )

            self.logger.info(
                f"Raster reprojection completed: {src_metadata['crs']} -> {target_crs}"
            )
            return dst_data, new_metadata

        except Exception as e:
            self.logger.error(f"Raster reprojection failed: {e}")
            raise ValueError(f"Raster reprojection failed: {e}")

    def clip_raster_by_geometry(
        self,
        data: np.ndarray,
        metadata: dict,
        geometries: List[dict],
        crop: bool = True,
    ) -> Tuple[np.ndarray, dict]:
        """
        Clip raster using geometry objects

        Args:
            data: Raster data
            metadata: Raster metadata
            geometries: List of clipping geometry objects
            crop: Whether to crop to geometry bounds

        Returns:
            tuple: (clipped data, new metadata)
        """
        try:
            if data.ndim == 2:
                data = data[np.newaxis, :, :]

            # Perform clipping
            clipped_data, clipped_transform = mask.mask(
                dataset=rasterio.MemoryFile()
                .open(
                    driver="GTiff",
                    height=data.shape[1],
                    width=data.shape[2],
                    count=data.shape[0],
                    dtype=data.dtype,
                    crs=metadata["crs"],
                    transform=metadata["transform"],
                    nodata=metadata.get("nodata"),
                )
                .write(data, range(1, data.shape[0] + 1)),
                shapes=geometries,
                crop=crop,
                nodata=metadata.get("nodata"),
            )

            # Update metadata
            new_metadata = metadata.copy()
            new_metadata.update(
                {
                    "transform": clipped_transform,
                    "width": clipped_data.shape[2],
                    "height": clipped_data.shape[1],
                }
            )

            self.logger.info(
                f"Raster clipping completed, new dimensions: {clipped_data.shape}"
            )
            return clipped_data, new_metadata

        except Exception as e:
            self.logger.error(f"Raster clipping failed: {e}")
            raise ValueError(f"Raster clipping failed: {e}")

    def resample_raster(
        self,
        data: np.ndarray,
        metadata: dict,
        scale_factor: float,
        resampling: Resampling = Resampling.bilinear,
    ) -> Tuple[np.ndarray, dict]:
        """
        Raster resampling

        Args:
            data: Raster data
            metadata: Raster metadata
            scale_factor: Scale factor (>1 enlarge, <1 shrink)
            resampling: Resampling method

        Returns:
            tuple: (resampled data, new metadata)
        """
        try:
            if data.ndim == 2:
                data = data[np.newaxis, :, :]

            # Calculate new dimensions
            new_height = int(data.shape[1] * scale_factor)
            new_width = int(data.shape[2] * scale_factor)

            # Calculate new transform matrix
            new_transform = metadata["transform"] * metadata["transform"].scale(
                (metadata["width"] / new_width), (metadata["height"] / new_height)
            )

            # Create target array
            resampled_data = np.zeros(
                (data.shape[0], new_height, new_width), dtype=data.dtype
            )

            # Perform resampling
            warp.reproject(
                source=data,
                destination=resampled_data,
                src_transform=metadata["transform"],
                src_crs=metadata["crs"],
                dst_transform=new_transform,
                dst_crs=metadata["crs"],
                resampling=resampling,
                src_nodata=metadata.get("nodata"),
                dst_nodata=metadata.get("nodata"),
            )

            # Update metadata
            new_metadata = metadata.copy()
            new_metadata.update(
                {"transform": new_transform, "width": new_width, "height": new_height}
            )

            self.logger.info(
                f"Raster resampling completed, scale factor: {scale_factor}"
            )
            return resampled_data, new_metadata

        except Exception as e:
            self.logger.error(f"Raster resampling failed: {e}")
            raise ValueError(f"Raster resampling failed: {e}")

    def calculate_statistics(
        self, data: np.ndarray, nodata: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Calculate raster statistics

        Args:
            data: Raster data
            nodata: No data value

        Returns:
            dict: Statistics information
        """
        if data.ndim == 3:
            # Multi-band data, calculate statistics for each band
            stats = {}
            for i in range(data.shape[0]):
                band_data = data[i]
                if nodata is not None:
                    valid_data = band_data[band_data != nodata]
                else:
                    valid_data = band_data[~np.isnan(band_data)]

                if len(valid_data) > 0:
                    stats[f"band_{i+1}"] = {
                        "min": float(np.min(valid_data)),
                        "max": float(np.max(valid_data)),
                        "mean": float(np.mean(valid_data)),
                        "std": float(np.std(valid_data)),
                        "valid_pixels": int(len(valid_data)),
                        "total_pixels": int(band_data.size),
                    }
        else:
            # Single band data
            if nodata is not None:
                valid_data = data[data != nodata]
            else:
                valid_data = data[~np.isnan(data)]

            if len(valid_data) > 0:
                stats = {
                    "min": float(np.min(valid_data)),
                    "max": float(np.max(valid_data)),
                    "mean": float(np.mean(valid_data)),
                    "std": float(np.std(valid_data)),
                    "valid_pixels": int(len(valid_data)),
                    "total_pixels": int(data.size),
                }
            else:
                stats = {"error": "No valid data"}

        return stats

    def save_raster(
        self,
        data: np.ndarray,
        metadata: dict,
        file_path: Union[str, Path],
        compress: str = "lzw",
    ) -> None:
        """
        Save raster file

        Args:
            data: Raster data
            metadata: Raster metadata
            file_path: Output file path
            compress: Compression method
        """
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            if data.ndim == 2:
                data = data[np.newaxis, :, :]

            with rasterio.open(
                file_path,
                "w",
                driver="GTiff",
                height=data.shape[1],
                width=data.shape[2],
                count=data.shape[0],
                dtype=data.dtype,
                crs=metadata["crs"],
                transform=metadata["transform"],
                nodata=metadata.get("nodata"),
                compress=compress,
            ) as dst:
                dst.write(data)

            self.logger.info(f"Raster file saved successfully: {file_path}")

        except Exception as e:
            self.logger.error(f"Failed to save raster file: {file_path}, error: {e}")
            raise ValueError(f"Failed to save raster file: {e}")
