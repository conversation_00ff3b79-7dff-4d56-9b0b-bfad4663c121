#!/usr/bin/env python3
"""
Connect平台测试数据生成器

本模块提供测试数据的生成、管理和清理功能，支持各种测试场景。
包括用户数据、站点数据、CDR数据、EP数据等的生成。

作者: Connect质量工程团队
日期: 2024-01-20
"""

import os
import sys
import json
import csv
import random
import string
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging
from pathlib import Path
import sqlite3
import pandas as pd
from faker import Faker
import numpy as np

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 初始化Faker
fake = Faker(['zh_CN', 'en_US'])
Faker.seed(42)  # 确保可重现的测试数据

@dataclass
class UserData:
    """用户数据结构"""
    id: int
    username: str
    email: str
    full_name: str
    role: str
    department: str
    phone: str
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime] = None

@dataclass
class SiteData:
    """站点数据结构"""
    id: int
    site_id: str
    site_name: str
    site_type: str
    latitude: float
    longitude: float
    address: str
    city: str
    province: str
    operator: str
    technology: str
    frequency_band: str
    antenna_height: float
    coverage_radius: float
    capacity: int
    status: str
    created_at: datetime

@dataclass
class CDRData:
    """CDR数据结构"""
    id: int
    call_id: str
    caller_number: str
    called_number: str
    call_type: str
    start_time: datetime
    end_time: datetime
    duration: int
    site_id: str
    cell_id: str
    latitude: float
    longitude: float
    signal_strength: float
    quality_score: float
    data_volume: int
    status: str

@dataclass
class EPData:
    """EP数据结构"""
    id: int
    measurement_id: str
    device_id: str
    test_type: str
    start_time: datetime
    end_time: datetime
    latitude: float
    longitude: float
    speed: float
    direction: float
    rsrp: float
    rsrq: float
    sinr: float
    throughput_dl: float
    throughput_ul: float
    latency: float
    packet_loss: float
    site_id: str
    technology: str

class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self, output_dir: str = "tests/fixtures/data"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 预定义的数据选项
        self.roles = ['admin', 'analyst', 'viewer', 'operator']
        self.departments = ['网络优化', '网络规划', '运维', '质量管理', '数据分析']
        self.site_types = ['宏站', '微站', '室分', '小基站']
        self.operators = ['中国移动', '中国联通', '中国电信']
        self.technologies = ['4G', '5G', '3G']
        self.frequency_bands = ['1800MHz', '2100MHz', '2600MHz', '3500MHz', '4900MHz']
        self.call_types = ['语音', '数据', '短信', '视频']
        self.test_types = ['DT测试', 'CQT测试', '专项测试', '优化测试']
        self.cities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉']
        self.provinces = ['北京市', '上海市', '广东省', '浙江省', '江苏省', '四川省', '湖北省']
    
    def generate_users(self, count: int = 100) -> List[UserData]:
        """生成用户数据"""
        logger.info(f"生成 {count} 个用户数据...")
        
        users = []
        for i in range(count):
            user = UserData(
                id=i + 1,
                username=f"user_{i+1:04d}",
                email=fake.email(),
                full_name=fake.name(),
                role=random.choice(self.roles),
                department=random.choice(self.departments),
                phone=fake.phone_number(),
                is_active=random.choice([True, True, True, False]),  # 75%活跃
                created_at=fake.date_time_between(start_date='-2y', end_date='now'),
                last_login=fake.date_time_between(start_date='-30d', end_date='now') if random.random() > 0.2 else None
            )
            users.append(user)
        
        return users
    
    def generate_sites(self, count: int = 1000) -> List[SiteData]:
        """生成站点数据"""
        logger.info(f"生成 {count} 个站点数据...")
        
        sites = []
        for i in range(count):
            # 生成中国境内的坐标
            latitude = random.uniform(18.0, 54.0)  # 中国纬度范围
            longitude = random.uniform(73.0, 135.0)  # 中国经度范围
            
            site = SiteData(
                id=i + 1,
                site_id=f"SITE_{i+1:06d}",
                site_name=f"{random.choice(self.cities)}{fake.street_name()}站",
                site_type=random.choice(self.site_types),
                latitude=latitude,
                longitude=longitude,
                address=fake.address(),
                city=random.choice(self.cities),
                province=random.choice(self.provinces),
                operator=random.choice(self.operators),
                technology=random.choice(self.technologies),
                frequency_band=random.choice(self.frequency_bands),
                antenna_height=random.uniform(20.0, 80.0),
                coverage_radius=random.uniform(0.5, 5.0),
                capacity=random.randint(100, 2000),
                status=random.choice(['正常', '维护', '故障', '停用']),
                created_at=fake.date_time_between(start_date='-3y', end_date='now')
            )
            sites.append(site)
        
        return sites
    
    def generate_cdr_data(self, count: int = 10000, sites: List[SiteData] = None) -> List[CDRData]:
        """生成CDR数据"""
        logger.info(f"生成 {count} 个CDR数据...")
        
        if sites is None:
            sites = self.generate_sites(100)
        
        cdr_records = []
        for i in range(count):
            site = random.choice(sites)
            start_time = fake.date_time_between(start_date='-30d', end_date='now')
            duration = random.randint(10, 3600)  # 10秒到1小时
            end_time = start_time + timedelta(seconds=duration)
            
            # 在站点附近生成坐标
            lat_offset = random.uniform(-0.01, 0.01)
            lon_offset = random.uniform(-0.01, 0.01)
            
            cdr = CDRData(
                id=i + 1,
                call_id=f"CALL_{uuid.uuid4().hex[:12].upper()}",
                caller_number=f"1{random.randint(3000000000, 9999999999)}",
                called_number=f"1{random.randint(3000000000, 9999999999)}",
                call_type=random.choice(self.call_types),
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                site_id=site.site_id,
                cell_id=f"{site.site_id}_CELL_{random.randint(1, 6)}",
                latitude=site.latitude + lat_offset,
                longitude=site.longitude + lon_offset,
                signal_strength=random.uniform(-120, -60),
                quality_score=random.uniform(0.5, 1.0),
                data_volume=random.randint(1024, 1024*1024*100),  # 1KB到100MB
                status=random.choice(['成功', '失败', '中断'])
            )
            cdr_records.append(cdr)
        
        return cdr_records
    
    def generate_ep_data(self, count: int = 5000, sites: List[SiteData] = None) -> List[EPData]:
        """生成EP数据"""
        logger.info(f"生成 {count} 个EP数据...")
        
        if sites is None:
            sites = self.generate_sites(100)
        
        ep_records = []
        for i in range(count):
            site = random.choice(sites)
            start_time = fake.date_time_between(start_date='-30d', end_date='now')
            duration = random.randint(60, 1800)  # 1分钟到30分钟
            end_time = start_time + timedelta(seconds=duration)
            
            # 生成测试路径
            lat_offset = random.uniform(-0.02, 0.02)
            lon_offset = random.uniform(-0.02, 0.02)
            
            ep = EPData(
                id=i + 1,
                measurement_id=f"EP_{uuid.uuid4().hex[:12].upper()}",
                device_id=f"DEVICE_{random.randint(1000, 9999)}",
                test_type=random.choice(self.test_types),
                start_time=start_time,
                end_time=end_time,
                latitude=site.latitude + lat_offset,
                longitude=site.longitude + lon_offset,
                speed=random.uniform(0, 120),  # 0-120 km/h
                direction=random.uniform(0, 360),
                rsrp=random.uniform(-140, -44),
                rsrq=random.uniform(-20, -3),
                sinr=random.uniform(-10, 30),
                throughput_dl=random.uniform(1, 1000),  # 1-1000 Mbps
                throughput_ul=random.uniform(1, 100),   # 1-100 Mbps
                latency=random.uniform(10, 100),
                packet_loss=random.uniform(0, 5),
                site_id=site.site_id,
                technology=site.technology
            )
            ep_records.append(ep)
        
        return ep_records
    
    def generate_large_dataset(self, 
                             users_count: int = 1000,
                             sites_count: int = 10000,
                             cdr_count: int = 1000000,
                             ep_count: int = 100000) -> Dict[str, List]:
        """生成大数据集"""
        logger.info("开始生成大数据集...")
        
        # 生成基础数据
        users = self.generate_users(users_count)
        sites = self.generate_sites(sites_count)
        
        # 生成关联数据
        cdr_data = self.generate_cdr_data(cdr_count, sites)
        ep_data = self.generate_ep_data(ep_count, sites)
        
        return {
            'users': users,
            'sites': sites,
            'cdr_data': cdr_data,
            'ep_data': ep_data
        }
    
    def save_to_json(self, data: List, filename: str):
        """保存数据到JSON文件"""
        filepath = self.output_dir / f"{filename}.json"
        
        # 转换数据类为字典
        json_data = [asdict(item) if hasattr(item, '__dict__') else item for item in data]
        
        # 处理datetime对象
        def json_serializer(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False, default=json_serializer)
        
        logger.info(f"数据已保存到: {filepath}")
    
    def save_to_csv(self, data: List, filename: str):
        """保存数据到CSV文件"""
        filepath = self.output_dir / f"{filename}.csv"
        
        if not data:
            logger.warning(f"没有数据可保存到 {filename}")
            return
        
        # 转换为DataFrame
        df_data = [asdict(item) if hasattr(item, '__dict__') else item for item in data]
        df = pd.DataFrame(df_data)
        
        # 保存到CSV
        df.to_csv(filepath, index=False, encoding='utf-8')
        logger.info(f"数据已保存到: {filepath}")
    
    def save_to_sqlite(self, datasets: Dict[str, List], db_filename: str = "test_data.db"):
        """保存数据到SQLite数据库"""
        db_path = self.output_dir / db_filename
        
        conn = sqlite3.connect(db_path)
        
        try:
            for table_name, data in datasets.items():
                if not data:
                    continue
                
                # 转换为DataFrame
                df_data = [asdict(item) if hasattr(item, '__dict__') else item for item in data]
                df = pd.DataFrame(df_data)
                
                # 保存到SQLite
                df.to_sql(table_name, conn, if_exists='replace', index=False)
                logger.info(f"表 {table_name} 已保存到数据库 ({len(data)} 条记录)")
            
            conn.commit()
            logger.info(f"数据库已保存到: {db_path}")
            
        finally:
            conn.close()
    
    def generate_performance_test_data(self, size: str = "medium") -> Dict[str, List]:
        """生成性能测试数据"""
        sizes = {
            "small": {
                "users": 100,
                "sites": 1000,
                "cdr": 10000,
                "ep": 1000
            },
            "medium": {
                "users": 1000,
                "sites": 10000,
                "cdr": 100000,
                "ep": 10000
            },
            "large": {
                "users": 10000,
                "sites": 100000,
                "cdr": 1000000,
                "ep": 100000
            },
            "xlarge": {
                "users": 50000,
                "sites": 500000,
                "cdr": 5000000,
                "ep": 500000
            }
        }
        
        if size not in sizes:
            raise ValueError(f"不支持的数据大小: {size}")
        
        config = sizes[size]
        logger.info(f"生成 {size} 规模的性能测试数据...")
        
        return self.generate_large_dataset(
            users_count=config["users"],
            sites_count=config["sites"],
            cdr_count=config["cdr"],
            ep_count=config["ep"]
        )
    
    def generate_corrupted_data(self, count: int = 100) -> List[Dict[str, Any]]:
        """生成损坏的测试数据"""
        logger.info(f"生成 {count} 个损坏的数据记录...")
        
        corrupted_data = []
        
        for i in range(count):
            # 随机选择损坏类型
            corruption_type = random.choice([
                'missing_required_field',
                'invalid_data_type',
                'out_of_range_value',
                'invalid_format',
                'null_value',
                'duplicate_key'
            ])
            
            if corruption_type == 'missing_required_field':
                data = {
                    'id': i + 1,
                    # 缺少必需字段
                    'name': fake.name()
                }
            elif corruption_type == 'invalid_data_type':
                data = {
                    'id': f"invalid_id_{i}",  # ID应该是数字
                    'name': fake.name(),
                    'latitude': "not_a_number",  # 纬度应该是数字
                    'longitude': fake.longitude()
                }
            elif corruption_type == 'out_of_range_value':
                data = {
                    'id': i + 1,
                    'name': fake.name(),
                    'latitude': 999.0,  # 超出有效范围
                    'longitude': -999.0  # 超出有效范围
                }
            elif corruption_type == 'invalid_format':
                data = {
                    'id': i + 1,
                    'name': fake.name(),
                    'email': "invalid_email_format",  # 无效邮箱格式
                    'phone': "123"  # 无效电话格式
                }
            elif corruption_type == 'null_value':
                data = {
                    'id': i + 1,
                    'name': None,  # 不应为空的字段
                    'latitude': None,
                    'longitude': None
                }
            else:  # duplicate_key
                data = {
                    'id': 1,  # 重复的ID
                    'name': fake.name(),
                    'latitude': fake.latitude(),
                    'longitude': fake.longitude()
                }
            
            data['corruption_type'] = corruption_type
            corrupted_data.append(data)
        
        return corrupted_data
    
    def cleanup_test_data(self):
        """清理测试数据"""
        logger.info("清理测试数据...")
        
        if self.output_dir.exists():
            import shutil
            shutil.rmtree(self.output_dir)
            logger.info(f"已删除目录: {self.output_dir}")
        
        # 重新创建目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_test_fixtures(self):
        """生成测试夹具数据"""
        logger.info("生成测试夹具数据...")
        
        # 生成小规模的测试数据
        users = self.generate_users(20)
        sites = self.generate_sites(50)
        cdr_data = self.generate_cdr_data(200, sites)
        ep_data = self.generate_ep_data(100, sites)
        corrupted_data = self.generate_corrupted_data(50)
        
        # 保存为不同格式
        self.save_to_json(users, "test_users")
        self.save_to_json(sites, "test_sites")
        self.save_to_json(cdr_data, "test_cdr_data")
        self.save_to_json(ep_data, "test_ep_data")
        self.save_to_json(corrupted_data, "test_corrupted_data")
        
        self.save_to_csv(users, "test_users")
        self.save_to_csv(sites, "test_sites")
        self.save_to_csv(cdr_data, "test_cdr_data")
        self.save_to_csv(ep_data, "test_ep_data")
        
        # 保存到SQLite
        datasets = {
            'users': users,
            'sites': sites,
            'cdr_data': cdr_data,
            'ep_data': ep_data
        }
        self.save_to_sqlite(datasets, "test_fixtures.db")
        
        logger.info("测试夹具数据生成完成")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Connect测试数据生成器')
    parser.add_argument('--output-dir', '-o',
                       default='tests/fixtures/data',
                       help='输出目录')
    parser.add_argument('--size', '-s',
                       choices=['small', 'medium', 'large', 'xlarge'],
                       default='medium',
                       help='数据规模')
    parser.add_argument('--format', '-f',
                       choices=['json', 'csv', 'sqlite', 'all'],
                       default='all',
                       help='输出格式')
    parser.add_argument('--fixtures-only', action='store_true',
                       help='只生成测试夹具数据')
    parser.add_argument('--performance-data', action='store_true',
                       help='生成性能测试数据')
    parser.add_argument('--cleanup', action='store_true',
                       help='清理现有数据')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建生成器
    generator = TestDataGenerator(args.output_dir)
    
    # 清理数据
    if args.cleanup:
        generator.cleanup_test_data()
        return
    
    # 生成测试夹具
    if args.fixtures_only:
        generator.generate_test_fixtures()
        return
    
    # 生成性能测试数据
    if args.performance_data:
        datasets = generator.generate_performance_test_data(args.size)
    else:
        # 生成标准测试数据
        datasets = generator.generate_large_dataset()
    
    # 保存数据
    if args.format in ['json', 'all']:
        for name, data in datasets.items():
            generator.save_to_json(data, name)
    
    if args.format in ['csv', 'all']:
        for name, data in datasets.items():
            generator.save_to_csv(data, name)
    
    if args.format in ['sqlite', 'all']:
        generator.save_to_sqlite(datasets)
    
    logger.info("数据生成完成!")

if __name__ == '__main__':
    main()