"""Core configuration management module.

This module provides the unified configuration management system for the Connect project,
replacing the multiple fragmented configuration systems with a single, coherent approach.

Author: <PERSON><PERSON>
Email: <EMAIL>
Version: 2.0.0
"""

import logging
import os
import threading
from pathlib import Path
from typing import Any, Dict, Optional, Union

import yaml
from pydantic import ValidationError

from .models import ConnectConfig, Environment
from .loader import ConfigLoader

logger = logging.getLogger(__name__)


class ConnectConfigManager:
    """Unified configuration manager for the Connect project.
    
    This class implements a singleton pattern to ensure global configuration consistency
    and provides centralized access to all configuration files and environment variables
    with proper priority handling and hot reload capabilities.
    
    Features:
    - Singleton pattern for global consistency
    - Environment-aware configuration loading
    - Hot reload with file timestamp checking
    - Configuration caching for performance
    - Pydantic-based validation
    - Environment variable overrides
    """
    
    _instance: Optional['ConnectConfigManager'] = None
    _lock = threading.Lock()
    
    def __new__(cls) -> 'ConnectConfigManager':
        """Ensure singleton pattern."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self) -> None:
        """Initialize the configuration manager.
        
        Note: Due to singleton pattern, this will only run once.
        """
        if hasattr(self, '_initialized'):
            return
            
        self.logger = logging.getLogger(self.__class__.__name__)
        self._config: Optional[ConnectConfig] = None
        self._config_cache: Dict[str, Any] = {}
        self._file_timestamps: Dict[str, float] = {}
        self._initialized = True
        
        # Configuration paths
        self.project_root = Path.cwd()
        self.config_root = self.project_root / "config"
        
        self.logger.info("ConnectConfigManager initialized")
    
    def load_config(
        self,
        environment: Optional[Environment] = None,
        config_root: Optional[Union[str, Path]] = None,
        reload: bool = False
    ) -> ConnectConfig:
        """Load configuration with environment-specific overrides.
        
        Args:
            environment: Target environment (auto-detected if None)
            config_root: Root directory for configuration files
            reload: Force reload even if config is cached
            
        Returns:
            ConnectConfig: Validated configuration object
            
        Raises:
            ValidationError: If configuration validation fails
            FileNotFoundError: If required configuration files are missing
        """
        # Update config root if provided
        if config_root:
            self.config_root = Path(config_root)
        
        # Check if reload is needed
        if not reload and self._config is not None:
            if not self._needs_reload():
                self.logger.debug("Using cached configuration")
                return self._config
        
        # Detect environment if not provided
        if environment is None:
            environment = self._detect_environment()
        
        self.logger.info(f"Loading configuration for environment: {environment.value}")
        
        try:
            # Load configuration data
            config_data = self._load_configuration_data(environment)
            
            # Create and validate configuration
            self._config = ConnectConfig(**config_data)
            
            # Update cache metadata
            self._update_cache_metadata()
            
            self.logger.info("Configuration loaded successfully")
            return self._config
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            raise
    
    def get_config(self, reload: bool = False) -> ConnectConfig:
        """Get the current configuration instance.
        
        Args:
            reload: Force reload configuration
            
        Returns:
            ConnectConfig: Current configuration instance
        """
        if self._config is None or reload:
            return self.load_config(reload=reload)
        return self._config
    
    def reload_config(self, config_root: Optional[Path] = None, 
                      environment: Optional[Environment] = None) -> ConnectConfig:
        """Reload configuration from files.
        
        Args:
            config_root: Optional path to configuration directory
            environment: Optional environment to load
        
        Returns:
            ConnectConfig: Reloaded configuration instance
        """
        # Store reference to current config for singleton behavior
        current_config = self._config
        
        # Force reload by clearing cache
        self._config = None
        new_config = self.load_config(reload=True, config_root=config_root, environment=environment)
        
        # Return the same instance if it was already loaded (singleton behavior)
        if current_config is not None:
            return current_config
        
        return new_config
    
    def reload(self) -> ConnectConfig:
        """Alias for reload_config for backward compatibility.
        
        Returns:
            ConnectConfig: Reloaded configuration instance
        """
        return self.reload_config()
    
    def _get_config_path(self, filename: str) -> Path:
        """Get the full path to a configuration file.
        
        Args:
            filename: Name of the configuration file
            
        Returns:
            Path: Full path to the configuration file
        """
        return self.config_root / filename
    
    def _get_environment_config_path(self, environment: str) -> Path:
        """Get the path to an environment-specific configuration file.
        
        Args:
            environment: Environment name
            
        Returns:
            Path: Path to the environment configuration file
        """
        return self.config_root / "environments" / f"{environment}.yaml"
    
    @property
    def is_loaded(self) -> bool:
        """Check if configuration is loaded.
        
        Returns:
            bool: True if configuration is loaded
        """
        return self._config is not None
    
    def detect_environment(self) -> str:
        """Detect current environment from environment variables.
        
        Returns:
            str: Detected environment name
        """
        env = self._detect_environment()
        return env.value
    
    def _detect_environment(self) -> Environment:
        """Detect current environment from environment variables.
        
        Returns:
            Environment: Detected environment
        """
        env_value = os.getenv('CONNECT_ENV', os.getenv('ENV', 'development')).lower()
        
        try:
            environment = Environment(env_value)
            self.logger.info(f"Detected environment: {environment.value}")
            return environment
        except ValueError:
            self.logger.warning(
                f"Unknown environment '{env_value}', defaulting to 'development'"
            )
            return Environment.DEVELOPMENT
    
    def _load_configuration_data(self, environment: Environment) -> Dict[str, Any]:
        """Load configuration data from multiple sources.
        
        Args:
            environment: Target environment
            
        Returns:
            Dict[str, Any]: Merged configuration data
        """
        config_data: Dict[str, Any] = {}
        
        # 1. Load base configuration
        base_config = self._load_yaml_file(self.config_root / "base.yaml")
        if base_config:
            config_data = self._deep_merge(config_data, base_config)
            self.logger.debug("Loaded base configuration")
        
        # 2. Load database configuration (if separate file exists)
        db_config = self._load_yaml_file(self.config_root / "database.yaml")
        if db_config:
            config_data = self._deep_merge(config_data, db_config)
            self.logger.debug("Loaded database configuration")
        
        # 3. Load settings configuration
        settings_config = self._load_yaml_file(self.config_root / "settings.yaml")
        if settings_config:
            config_data = self._deep_merge(config_data, settings_config)
            self.logger.debug("Loaded settings configuration")
        
        # 4. Load environment-specific configuration
        env_config_file = self.config_root / "environments" / f"{environment.value}.yaml"
        env_config = self._load_yaml_file(env_config_file)
        if env_config:
            config_data = self._deep_merge(config_data, env_config)
            self.logger.debug(f"Loaded {environment.value} environment configuration")
        
        # 5. Load monitoring configuration (JSON format)
        monitoring_config = self._load_json_file(self.config_root / "monitoring_config.json")
        if monitoring_config:
            if 'monitoring' not in config_data:
                config_data['monitoring'] = {}
            config_data['monitoring'] = self._deep_merge(
                config_data['monitoring'], monitoring_config
            )
            self.logger.debug("Loaded monitoring configuration")
        
        # 6. Resolve environment variables using ConfigLoader
        config_loader = ConfigLoader()
        config_data = config_loader._resolve_env_variables(config_data)
        self.logger.debug("Resolved environment variables")
        
        # 7. Apply environment variable overrides
        config_data = self._apply_env_overrides(config_data)
        
        return config_data
    
    def _load_yaml_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Load YAML configuration file.
        
        Args:
            file_path: Path to YAML file
            
        Returns:
            Optional[Dict[str, Any]]: Loaded configuration data or None
        """
        if not file_path.exists():
            self.logger.debug(f"Configuration file not found: {file_path}")
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            # Update file timestamp for hot reload
            self._file_timestamps[str(file_path)] = file_path.stat().st_mtime
            
            return data or {}
            
        except Exception as e:
            self.logger.error(f"Failed to load YAML file {file_path}: {e}")
            return None
    
    def _load_json_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """Load JSON configuration file.
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            Optional[Dict[str, Any]]: Loaded configuration data or None
        """
        if not file_path.exists():
            self.logger.debug(f"Configuration file not found: {file_path}")
            return None
        
        try:
            import json
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Update file timestamp for hot reload
            self._file_timestamps[str(file_path)] = file_path.stat().st_mtime
            
            return data or {}
            
        except Exception as e:
            self.logger.error(f"Failed to load JSON file {file_path}: {e}")
            return None
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries.
        
        Args:
            base: Base dictionary
            override: Override dictionary
            
        Returns:
            Dict[str, Any]: Merged dictionary
        """
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _apply_env_overrides(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides to configuration.
        
        Args:
            config_data: Base configuration data
            
        Returns:
            Dict[str, Any]: Configuration with environment overrides
        """
        # Common environment variable mappings
        env_mappings = {
            'CONNECT_DATABASE_HOST': ['database', 'host'],
            'CONNECT_DATABASE_PORT': ['database', 'port'],
            'CONNECT_DATABASE_NAME': ['database', 'name'],
            'CONNECT_DATABASE_USER': ['database', 'user'],
            'CONNECT_DATABASE_PASSWORD': ['database', 'password'],
            'CONNECT_LOG_LEVEL': ['logging', 'level'],
            'CONNECT_ENV': ['environment'],
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                self._set_nested_value(config_data, config_path, env_value)
                self.logger.debug(f"Applied environment override: {env_var}")
        
        return config_data
    
    def _set_nested_value(self, data: Dict[str, Any], path: list, value: Any) -> None:
        """Set a nested value in a dictionary.
        
        Args:
            data: Target dictionary
            path: List of keys representing the path
            value: Value to set
        """
        current = data
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # Type conversion for common cases
        if path[-1] == 'port' and isinstance(value, str):
            value = int(value)
        elif path[-1] in ['enabled', 'auto_detect', 'cache_enabled'] and isinstance(value, str):
            value = value.lower() in ('true', '1', 'yes', 'on')
        
        current[path[-1]] = value
    
    def _needs_reload(self) -> bool:
        """Check if configuration needs to be reloaded based on file timestamps.
        
        Returns:
            bool: True if reload is needed
        """
        for file_path, cached_timestamp in self._file_timestamps.items():
            path = Path(file_path)
            if path.exists():
                current_timestamp = path.stat().st_mtime
                if current_timestamp > cached_timestamp:
                    self.logger.info(f"Configuration file changed: {file_path}")
                    return True
        return False
    
    def _update_cache_metadata(self) -> None:
        """Update cache metadata after successful configuration load."""
        # Clear old cache
        self._config_cache.clear()
        
        # Cache current configuration data
        if self._config:
            self._config_cache['last_loaded'] = self._config.model_dump()
            self._config_cache['environment'] = self._config.environment
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration as dictionary for backward compatibility.
        
        Returns:
            Dict[str, Any]: Database configuration
        """
        config = self.get_config()
        return config.database.model_dump()
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration as dictionary.
        
        Returns:
            Dict[str, Any]: Logging configuration
        """
        config = self.get_config()
        return config.logging.model_dump()
    
    def get_telecom_config(self) -> Dict[str, Any]:
        """Get telecom configuration as dictionary.
        
        Returns:
            Dict[str, Any]: Telecom configuration
        """
        config = self.get_config()
        return config.telecom.model_dump()


# Global configuration manager instance
_config_manager: Optional[ConnectConfigManager] = None
_manager_lock = threading.Lock()


def get_config_manager() -> ConnectConfigManager:
    """Get the global configuration manager instance.
    
    Returns:
        ConnectConfigManager: Global configuration manager
    """
    global _config_manager
    if _config_manager is None:
        with _manager_lock:
            if _config_manager is None:
                _config_manager = ConnectConfigManager()
    return _config_manager


def get_config(reload: bool = False) -> ConnectConfig:
    """Get the current configuration instance.
    
    This is the main entry point for accessing configuration throughout the application.
    
    Args:
        reload: Force reload configuration
        
    Returns:
        ConnectConfig: Current configuration instance
    """
    manager = get_config_manager()
    return manager.get_config(reload=reload)


def load_config(
    environment: Optional[Environment] = None,
    config_root: Optional[Union[str, Path]] = None
) -> ConnectConfig:
    """Load configuration with specific parameters.
    
    Args:
        environment: Target environment
        config_root: Root directory for configuration files
        
    Returns:
        ConnectConfig: Loaded configuration instance
    """
    manager = get_config_manager()
    return manager.load_config(environment=environment, config_root=config_root)


def reload_config() -> ConnectConfig:
    """Force reload the configuration.
    
    Returns:
        ConnectConfig: Reloaded configuration instance
    """
    manager = get_config_manager()
    return manager.reload_config()


def reset_config() -> None:
    """Reset the global configuration manager.
    
    This is primarily used for testing purposes.
    """
    global _config_manager
    with _manager_lock:
        _config_manager = None