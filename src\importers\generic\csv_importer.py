"""Generic CSV importer with high-performance processing capabilities.

This module provides a specialized CSV importer that can handle large CSV files
with memory optimization, encoding detection, and flexible schema handling.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import logging
from typing import Any, Dict, List, Optional, Union, Iterator
from pathlib import Path
from datetime import datetime
import io

import pandas as pd
import numpy as np
from pydantic import BaseModel, Field, ConfigDict

from ..base import (
    AbstractImporter,
    ValidationMixin,
    ProcessingMixin,
    PerformanceMixin,
    ImporterConfig,
    ImportResult,
    ImportStatus
)


class CSVConfig(BaseModel):
    """Configuration specific to CSV import."""
    # File reading settings
    encoding: str = Field(default="utf-8", description="File encoding (auto-detect if 'auto')")
    delimiter: str = Field(default=",", description="Field delimiter")
    quotechar: str = Field(default='"', description="Quote character")
    escapechar: Optional[str] = Field(default=None, description="Escape character")
    skipinitialspace: bool = Field(default=True, description="Skip whitespace after delimiter")
    
    # Header and data settings
    header_row: int = Field(default=0, description="Header row index (0-based)")
    skip_rows: int = Field(default=0, description="Number of rows to skip at start")
    skip_footer: int = Field(default=0, description="Number of rows to skip at end")
    max_rows: Optional[int] = Field(default=None, description="Maximum rows to read")
    
    # Data type settings
    auto_detect_types: bool = Field(default=True, description="Auto-detect column data types")
    date_columns: List[str] = Field(default_factory=list, description="Columns to parse as dates")
    numeric_columns: List[str] = Field(default_factory=list, description="Columns to parse as numeric")
    string_columns: List[str] = Field(default_factory=list, description="Columns to keep as strings")
    
    # Performance settings
    chunk_size: int = Field(default=50000, description="Chunk size for reading large files")
    low_memory: bool = Field(default=True, description="Use low memory mode")
    engine: str = Field(default="c", description="Parser engine ('c', 'python')")
    
    # Data cleaning settings
    remove_empty_rows: bool = Field(default=True, description="Remove completely empty rows")
    remove_empty_columns: bool = Field(default=True, description="Remove completely empty columns")
    strip_whitespace: bool = Field(default=True, description="Strip whitespace from string columns")
    replace_na_values: List[str] = Field(
        default_factory=lambda: ['', 'NULL', 'null', 'N/A', 'n/a', '#N/A', 'NA'],
        description="Values to treat as NaN"
    )
    
    # Validation settings
    validate_headers: bool = Field(default=True, description="Validate column headers")
    required_columns: List[str] = Field(default_factory=list, description="Required column names")
    max_error_rate: float = Field(default=0.05, description="Maximum acceptable error rate")
    
    # Output settings
    preserve_dtypes: bool = Field(default=True, description="Preserve detected data types")
    add_row_numbers: bool = Field(default=True, description="Add row number column")
    add_file_metadata: bool = Field(default=True, description="Add file metadata columns")
    
    model_config = ConfigDict(
        extra="allow"
    )
class CSVImporter(AbstractImporter, ValidationMixin, ProcessingMixin, PerformanceMixin):
    """High-performance CSV importer with advanced processing capabilities.
    
    This importer provides comprehensive CSV processing with support for:
    - Automatic encoding detection and handling
    - Memory-optimized chunked reading for large files
    - Flexible data type detection and conversion
    - Data cleaning and validation
    - Performance monitoring and optimization
    """
    
    # Common CSV delimiters and their detection patterns
    DELIMITER_PATTERNS = {
        ',': 'comma',
        ';': 'semicolon', 
        '\t': 'tab',
        '|': 'pipe',
        ' ': 'space'
    }
    
    # Common encoding patterns
    ENCODING_CANDIDATES = [
        'utf-8', 'utf-8-sig', 'latin1', 'iso-8859-1', 
        'cp1252', 'ascii', 'utf-16', 'utf-32'
    ]
    
    def __init__(self, config: Optional[Union[ImporterConfig, Dict[str, Any]]] = None, **kwargs):
        """Initialize CSV importer.
        
        Args:
            config: Importer configuration
            **kwargs: Additional configuration parameters
        """
        super().__init__(config=config, **kwargs)
        
        # CSV-specific configuration
        csv_config_dict = kwargs.get('csv_config', {})
        if isinstance(csv_config_dict, CSVConfig):
            self.csv_config = csv_config_dict
        else:
            self.csv_config = CSVConfig(**csv_config_dict)
            
        # Set data type
        self.data_type = 'CSV'
        self.name = 'CSV Importer'
        self.supported_formats = ['.csv', '.tsv', '.txt']
        
        # Configure processing for CSV data
        self.configure_processing({
            'batch_size': self.csv_config.chunk_size,
            'enable_parallel': True,
            'memory_limit_mb': 1024,
            'use_polars': False  # Pandas for better CSV handling
        })
        
        # Configure validation
        self.configure_validation({
            'enable_strict_validation': self.csv_config.validate_headers,
            'max_error_rate': self.csv_config.max_error_rate
        })
        
        # Configure performance monitoring
        self.configure_performance({
            'enable_monitoring': True,
            'csv_chunk_size': self.csv_config.chunk_size
        })
        
        self.logger.info(f"CSV Importer initialized with chunk size: {self.csv_config.chunk_size}")
        
    async def import_data(self, source: Union[str, Path], **kwargs) -> ImportResult:
        """Import CSV data with comprehensive processing.
        
        Args:
            source: CSV file path
            **kwargs: Additional import parameters
            
        Returns:
            Import result with metrics and status
        """
        start_time = datetime.now()
        
        try:
            # Start performance monitoring
            await self.start_performance_monitoring()
            
            with self.track_operation("csv_import", source=str(source)) as op_metrics:
                # Validate source
                if not await self.validate_source(source):
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        message="Source validation failed",
                        records_processed=0,
                        errors=["Invalid or inaccessible CSV file"]
                    )
                    
                # Detect file characteristics
                file_info = await self._analyze_csv_file(source)
                
                # Load and process data
                data = await self._load_csv_data(source, file_info, **kwargs)
                
                if data is None or len(data) == 0:
                    return ImportResult(
                        status=ImportStatus.FAILED,
                        message="No data loaded from CSV file",
                        records_processed=0
                    )
                    
                # Update operation metrics
                op_metrics.records_processed = len(data)
                op_metrics.bytes_processed = data.memory_usage(deep=True).sum()
                
                # Clean and validate data
                data = await self._clean_csv_data(data)
                validation_result = await self._validate_csv_data(data)
                
                if not validation_result['is_valid']:
                    if validation_result['error_rate'] > self.csv_config.max_error_rate:
                        return ImportResult(
                            status=ImportStatus.FAILED,
                            message=f"Data quality below threshold: {validation_result['error_rate']:.2%}",
                            records_processed=len(data),
                            errors=validation_result['errors'][:10]
                        )
                    else:
                        self.logger.warning(f"Data quality issues detected: {validation_result['error_rate']:.2%}")
                        
                # Store data if database operations are available
                if hasattr(self, 'db_ops') and self.db_ops:
                    try:
                        await self._store_csv_data(data, source)
                    except Exception as e:
                        self.logger.error(f"Database storage failed: {e}")
                        return ImportResult(
                            status=ImportStatus.FAILED,
                            message=f"Database storage failed: {e}",
                            records_processed=len(data),
                            errors=[str(e)]
                        )
                        
                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                
                return ImportResult(
                    status=ImportStatus.SUCCESS,
                    message=f"Successfully imported {len(data)} records from CSV",
                    records_processed=len(data),
                    processing_time_seconds=processing_time,
                    data_quality_score=validation_result['quality_score'],
                    warnings=validation_result.get('warnings', [])[:5],
                    metadata={
                        'file_info': file_info,
                        'columns': list(data.columns),
                        'data_types': data.dtypes.to_dict(),
                        'memory_usage_mb': data.memory_usage(deep=True).sum() / 1024 / 1024
                    }
                )
                
        except Exception as e:
            self.logger.error(f"CSV import failed: {e}")
            return ImportResult(
                status=ImportStatus.FAILED,
                message=f"Import failed: {e}",
                records_processed=0,
                errors=[str(e)]
            )
            
        finally:
            await self.stop_performance_monitoring()
            
    async def _analyze_csv_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Analyze CSV file to detect characteristics.
        
        Args:
            file_path: Path to CSV file
            
        Returns:
            Dictionary with file analysis results
        """
        file_path = Path(file_path)
        
        # Basic file info
        stat = file_path.stat()
        file_info = {
            'file_size_bytes': stat.st_size,
            'file_size_mb': stat.st_size / 1024 / 1024,
            'encoding': self.csv_config.encoding,
            'delimiter': self.csv_config.delimiter,
            'estimated_rows': 0,
            'sample_data': None
        }
        
        try:
            # Detect encoding if set to auto
            if self.csv_config.encoding == 'auto':
                file_info['encoding'] = await self._detect_encoding(file_path)
                
            # Read sample for analysis
            sample_size = min(1000, stat.st_size // 100)  # 1% of file or 1000 bytes
            
            with open(file_path, 'r', encoding=file_info['encoding'], errors='ignore') as f:
                sample_lines = []
                for i, line in enumerate(f):
                    if i >= 10:  # Read first 10 lines for analysis
                        break
                    sample_lines.append(line.strip())
                    
            file_info['sample_data'] = sample_lines
            
            # Detect delimiter if not specified
            if self.csv_config.delimiter == 'auto':
                file_info['delimiter'] = self._detect_delimiter(sample_lines)
            else:
                file_info['delimiter'] = self.csv_config.delimiter
                
            # Estimate row count
            if sample_lines:
                avg_line_length = sum(len(line) for line in sample_lines) / len(sample_lines)
                if avg_line_length > 0:
                    file_info['estimated_rows'] = int(stat.st_size / avg_line_length)
                    
        except Exception as e:
            self.logger.warning(f"File analysis failed: {e}")
            
        return file_info
        
    async def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding.
        
        Args:
            file_path: Path to file
            
        Returns:
            Detected encoding
        """
        try:
            import chardet
            
            # Read sample for encoding detection
            sample_size = min(10000, file_path.stat().st_size)
            
            with open(file_path, 'rb') as f:
                sample = f.read(sample_size)
                
            result = chardet.detect(sample)
            detected_encoding = result.get('encoding', 'utf-8')
            confidence = result.get('confidence', 0)
            
            if confidence > 0.8:
                self.logger.info(f"Detected encoding: {detected_encoding} (confidence: {confidence:.2f})")
                return detected_encoding
            else:
                self.logger.warning(f"Low confidence encoding detection: {detected_encoding} ({confidence:.2f})")
                
        except ImportError:
            self.logger.warning("chardet not available, trying common encodings")
        except Exception as e:
            self.logger.warning(f"Encoding detection failed: {e}")
            
        # Fallback: try common encodings
        for encoding in self.ENCODING_CANDIDATES:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read(1000)  # Try to read sample
                self.logger.info(f"Using encoding: {encoding}")
                return encoding
            except UnicodeDecodeError:
                continue
                
        # Final fallback
        return 'utf-8'
        
    def _detect_delimiter(self, sample_lines: List[str]) -> str:
        """Detect CSV delimiter from sample lines.
        
        Args:
            sample_lines: Sample lines from CSV file
            
        Returns:
            Detected delimiter
        """
        if not sample_lines:
            return ','
            
        # Count occurrences of each potential delimiter
        delimiter_counts = {}
        
        for delimiter in self.DELIMITER_PATTERNS.keys():
            counts = []
            for line in sample_lines[1:6]:  # Skip header, check next 5 lines
                if line.strip():
                    counts.append(line.count(delimiter))
                    
            if counts:
                # Check consistency (same count across lines)
                if len(set(counts)) == 1 and counts[0] > 0:
                    delimiter_counts[delimiter] = counts[0]
                    
        if delimiter_counts:
            # Return delimiter with highest consistent count
            best_delimiter = max(delimiter_counts.items(), key=lambda x: x[1])[0]
            delimiter_name = self.DELIMITER_PATTERNS[best_delimiter]
            self.logger.info(f"Detected delimiter: {delimiter_name} ('{best_delimiter}')")
            return best_delimiter
        else:
            self.logger.warning("Could not detect delimiter, using comma")
            return ','
            
    async def _load_csv_data(self, file_path: Union[str, Path], file_info: Dict[str, Any], **kwargs) -> pd.DataFrame:
        """Load CSV data with optimized settings.
        
        Args:
            file_path: Path to CSV file
            file_info: File analysis results
            **kwargs: Additional parameters
            
        Returns:
            Loaded DataFrame
        """
        file_path = Path(file_path)
        
        # Prepare pandas read_csv parameters
        read_params = {
            'filepath_or_buffer': file_path,
            'encoding': file_info['encoding'],
            'delimiter': file_info['delimiter'],
            'quotechar': self.csv_config.quotechar,
            'escapechar': self.csv_config.escapechar,
            'skipinitialspace': self.csv_config.skipinitialspace,
            'header': self.csv_config.header_row,
            'skiprows': self.csv_config.skip_rows,
            'skipfooter': self.csv_config.skip_footer,
            'nrows': self.csv_config.max_rows,
            'low_memory': self.csv_config.low_memory,
            'engine': self.csv_config.engine,
            'na_values': self.csv_config.replace_na_values,
            'keep_default_na': True,
            'na_filter': True
        }
        
        # Add date parsing if specified
        if self.csv_config.date_columns:
            read_params['parse_dates'] = self.csv_config.date_columns
            read_params['date_parser'] = pd.to_datetime
            
        # Add data type specifications
        if not self.csv_config.auto_detect_types:
            dtype_dict = {}
            
            # Set string columns
            for col in self.csv_config.string_columns:
                dtype_dict[col] = 'str'
                
            # Set numeric columns
            for col in self.csv_config.numeric_columns:
                dtype_dict[col] = 'float64'
                
            if dtype_dict:
                read_params['dtype'] = dtype_dict
                
        try:
            # Check if file is large and needs chunked reading
            if file_info['file_size_mb'] > 100 or file_info['estimated_rows'] > 500000:
                self.logger.info(f"Large file detected ({file_info['file_size_mb']:.1f}MB), using chunked reading")
                return await self._load_csv_chunked(read_params)
            else:
                # Load entire file
                self.logger.info("Loading CSV file in single operation")
                data = pd.read_csv(**read_params)
                
                # Add metadata if requested
                if self.csv_config.add_file_metadata:
                    data = self._add_file_metadata(data, file_path)
                    
                return data
                
        except Exception as e:
            self.logger.error(f"Failed to load CSV data: {e}")
            raise
            
    async def _load_csv_chunked(self, read_params: Dict[str, Any]) -> pd.DataFrame:
        """Load large CSV file in chunks.
        
        Args:
            read_params: Parameters for pandas read_csv
            
        Returns:
            Combined DataFrame
        """
        read_params['chunksize'] = self.csv_config.chunk_size
        
        chunks = []
        total_rows = 0
        
        try:
            chunk_reader = pd.read_csv(**read_params)
            
            for i, chunk in enumerate(chunk_reader):
                # Process chunk if needed
                if self.csv_config.remove_empty_rows:
                    chunk = chunk.dropna(how='all')
                    
                chunks.append(chunk)
                total_rows += len(chunk)
                
                if i % 10 == 0:  # Log progress every 10 chunks
                    self.logger.info(f"Processed {i+1} chunks, {total_rows} rows")
                    
                # Memory management
                if len(chunks) > 50:  # Combine chunks periodically
                    combined_chunk = pd.concat(chunks, ignore_index=True)
                    chunks = [combined_chunk]
                    
            # Combine all chunks
            if chunks:
                data = pd.concat(chunks, ignore_index=True)
                self.logger.info(f"Successfully loaded {len(data)} rows from {len(chunks)} chunks")
                return data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            self.logger.error(f"Chunked reading failed: {e}")
            raise
            
    def _add_file_metadata(self, data: pd.DataFrame, file_path: Path) -> pd.DataFrame:
        """Add file metadata columns to DataFrame.
        
        Args:
            data: DataFrame to enhance
            file_path: Source file path
            
        Returns:
            Enhanced DataFrame
        """
        if self.csv_config.add_row_numbers:
            data['_row_number'] = range(1, len(data) + 1)
            
        if self.csv_config.add_file_metadata:
            data['_source_file'] = file_path.name
            data['_import_timestamp'] = datetime.now()
            data['_file_size_mb'] = file_path.stat().st_size / 1024 / 1024
            
        return data
        
    async def _clean_csv_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Clean CSV data according to configuration.
        
        Args:
            data: DataFrame to clean
            
        Returns:
            Cleaned DataFrame
        """
        original_rows = len(data)
        original_cols = len(data.columns)
        
        # Remove duplicate columns and standardize column names
        from src.utils.column_deduplicator import ColumnDeduplicator
        
        # Remove duplicate columns, keeping the most complete one
        data, dedup_report = ColumnDeduplicator.remove_duplicate_columns(
            data, keep_strategy='best'
        )
        
        if dedup_report['total_removed'] > 0:
            self.logger.info(f"Removed {dedup_report['total_removed']} duplicate columns in CSV data")
        
        # Standardize column names to lowercase
        clean_columns = []
        for col in data.columns:
            clean_name = str(col).strip().lower()
            clean_columns.append(clean_name)
        
        data.columns = clean_columns
        
        # Remove empty rows
        if self.csv_config.remove_empty_rows:
            data = data.dropna(how='all')
            
        # Remove empty columns
        if self.csv_config.remove_empty_columns:
            data = data.dropna(axis=1, how='all')
            
        # Strip whitespace from string columns
        if self.csv_config.strip_whitespace:
            string_columns = data.select_dtypes(include=['object']).columns
            for col in string_columns:
                data[col] = data[col].astype(str).str.strip()
                # Convert back to NaN if empty after stripping
                data[col] = data[col].replace('', np.nan)
                
        # Log cleaning results
        rows_removed = original_rows - len(data)
        cols_removed = original_cols - len(data.columns)
        
        if rows_removed > 0 or cols_removed > 0:
            self.logger.info(f"Data cleaning: removed {rows_removed} rows, {cols_removed} columns")
            
        return data
        
    async def _validate_csv_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Validate CSV data quality.
        
        Args:
            data: DataFrame to validate
            
        Returns:
            Validation results
        """
        validation_result = {
            'is_valid': True,
            'error_rate': 0.0,
            'quality_score': 1.0,
            'errors': [],
            'warnings': []
        }
        
        total_cells = data.size
        error_count = 0
        
        # Check required columns
        if self.csv_config.required_columns:
            missing_columns = set(self.csv_config.required_columns) - set(data.columns)
            if missing_columns:
                validation_result['errors'].append(f"Missing required columns: {missing_columns}")
                validation_result['is_valid'] = False
                
        # Check for completely empty DataFrame
        if len(data) == 0:
            validation_result['errors'].append("No data rows found")
            validation_result['is_valid'] = False
            return validation_result
            
        # Calculate missing data rate
        missing_cells = data.isnull().sum().sum()
        missing_rate = missing_cells / total_cells if total_cells > 0 else 0
        
        if missing_rate > 0.5:
            validation_result['warnings'].append(f"High missing data rate: {missing_rate:.1%}")
            
        # Check for duplicate rows
        duplicate_rows = data.duplicated().sum()
        if duplicate_rows > 0:
            duplicate_rate = duplicate_rows / len(data)
            if duplicate_rate > 0.1:
                validation_result['warnings'].append(f"High duplicate rate: {duplicate_rate:.1%}")
            else:
                validation_result['warnings'].append(f"Found {duplicate_rows} duplicate rows")
                
        # Check data type consistency
        for col in data.columns:
            if col.startswith('_'):  # Skip metadata columns
                continue
                
            # Check for mixed types in object columns
            if data[col].dtype == 'object':
                sample_values = data[col].dropna().head(100)
                if len(sample_values) > 0:
                    type_counts = sample_values.apply(type).value_counts()
                    if len(type_counts) > 1:
                        validation_result['warnings'].append(f"Mixed data types in column '{col}'")
                        
        # Calculate overall quality score
        error_rate = error_count / total_cells if total_cells > 0 else 0
        quality_score = max(0, 1 - error_rate - missing_rate * 0.5)
        
        validation_result['error_rate'] = error_rate
        validation_result['quality_score'] = quality_score
        
        return validation_result
        
    async def _store_csv_data(self, data: pd.DataFrame, source_path: Union[str, Path]) -> None:
        """Store CSV data in database.
        
        Args:
            data: DataFrame to store
            source_path: Source file path
        """
        if not hasattr(self, 'db_ops') or not self.db_ops:
            raise ValueError("Database operations not configured")
            
        # Generate table name from file name
        file_name = Path(source_path).stem
        table_name = f'csv_import_{file_name.lower().replace(" ", "_")}'
        
        # Store in database
        await self.db_ops.store_dataframe(
            data, 
            table_name,
            if_exists='replace'  # Replace for CSV imports
        )
        
        # Create indexes on common columns
        if hasattr(self.db_ops, 'create_index'):
            # Index on timestamp columns
            timestamp_cols = [col for col in data.columns if 'timestamp' in col.lower() or 'date' in col.lower()]
            for col in timestamp_cols:
                try:
                    await self.db_ops.create_index(table_name, col)
                except Exception as e:
                    self.logger.warning(f"Failed to create index on {col}: {e}")
                    
    async def validate_source(self, source: Union[str, Path]) -> bool:
        """Validate CSV data source.
        
        Args:
            source: Data source to validate
            
        Returns:
            True if source is valid
        """
        try:
            source_path = Path(source)
            
            # Check if file exists
            if not source_path.exists():
                self.logger.error(f"Source file does not exist: {source}")
                return False
                
            # Check file format
            if source_path.suffix.lower() not in self.supported_formats:
                self.logger.error(f"Unsupported file format: {source_path.suffix}")
                return False
                
            # Check file size
            file_size_mb = source_path.stat().st_size / 1024 / 1024
            if file_size_mb > 5000:  # 5GB limit
                self.logger.error(f"File too large: {file_size_mb:.1f}MB")
                return False
                
            # Try to read first few lines
            try:
                with open(source_path, 'r', encoding='utf-8', errors='ignore') as f:
                    first_line = f.readline()
                    if not first_line.strip():
                        self.logger.error("File appears to be empty")
                        return False
            except Exception as e:
                self.logger.error(f"Cannot read file: {e}")
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"Source validation failed: {e}")
            return False
            
    async def get_source_info(self, source: Union[str, Path]) -> Dict[str, Any]:
        """Get information about CSV data source.
        
        Args:
            source: Data source
            
        Returns:
            Source information dictionary
        """
        try:
            source_path = Path(source)
            stat = source_path.stat()
            
            # Analyze file
            file_info = await self._analyze_csv_file(source_path)
            
            return {
                'file_path': str(source_path),
                'file_name': source_path.name,
                'file_size_bytes': stat.st_size,
                'file_size_mb': stat.st_size / 1024 / 1024,
                'file_format': source_path.suffix.lower(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'is_supported': source_path.suffix.lower() in self.supported_formats,
                'estimated_records': file_info['estimated_rows'],
                'detected_encoding': file_info['encoding'],
                'detected_delimiter': file_info['delimiter'],
                'sample_lines': file_info['sample_data'][:3] if file_info['sample_data'] else [],
                'requires_chunked_reading': file_info['file_size_mb'] > 100
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get source info: {e}")
            return {'error': str(e)}
            
    def get_column_info(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get detailed information about DataFrame columns.
        
        Args:
            data: DataFrame to analyze
            
        Returns:
            Column information dictionary
        """
        column_info = {}
        
        for col in data.columns:
            col_data = data[col]
            
            info = {
                'dtype': str(col_data.dtype),
                'non_null_count': int(col_data.count()),
                'null_count': int(col_data.isnull().sum()),
                'null_percentage': float(col_data.isnull().mean() * 100),
                'unique_count': int(col_data.nunique()),
                'memory_usage_bytes': int(col_data.memory_usage(deep=True))
            }
            
            # Add type-specific statistics
            if pd.api.types.is_numeric_dtype(col_data):
                info.update({
                    'min_value': float(col_data.min()) if col_data.notna().any() else None,
                    'max_value': float(col_data.max()) if col_data.notna().any() else None,
                    'mean_value': float(col_data.mean()) if col_data.notna().any() else None,
                    'std_value': float(col_data.std()) if col_data.notna().any() else None
                })
            elif pd.api.types.is_string_dtype(col_data) or col_data.dtype == 'object':
                non_null_data = col_data.dropna()
                if len(non_null_data) > 0:
                    info.update({
                        'avg_length': float(non_null_data.astype(str).str.len().mean()),
                        'max_length': int(non_null_data.astype(str).str.len().max()),
                        'min_length': int(non_null_data.astype(str).str.len().min())
                    })
                    
            column_info[col] = info
            
        return column_info
        
    def suggest_data_types(self, data: pd.DataFrame) -> Dict[str, str]:
        """Suggest optimal data types for DataFrame columns.
        
        Args:
            data: DataFrame to analyze
            
        Returns:
            Dictionary mapping column names to suggested data types
        """
        suggestions = {}
        
        for col in data.columns:
            col_data = data[col].dropna()
            
            if len(col_data) == 0:
                suggestions[col] = 'object'
                continue
                
            # Check if column can be converted to numeric
            try:
                pd.to_numeric(col_data)
                # Check if integers
                if col_data.astype(str).str.match(r'^-?\d+$').all():
                    suggestions[col] = 'int64'
                else:
                    suggestions[col] = 'float64'
                continue
            except (ValueError, TypeError):
                pass
                
            # Check if column can be converted to datetime
            try:
                pd.to_datetime(col_data)
                suggestions[col] = 'datetime64[ns]'
                continue
            except (ValueError, TypeError):
                pass
                
            # Check if column is boolean
            unique_values = set(col_data.astype(str).str.lower())
            if unique_values.issubset({'true', 'false', '1', '0', 'yes', 'no'}):
                suggestions[col] = 'bool'
                continue
                
            # Check if column should be categorical
            if col_data.nunique() / len(col_data) < 0.1 and col_data.nunique() < 100:
                suggestions[col] = 'category'
                continue
                
            # Default to string
            suggestions[col] = 'object'
            
        return suggestions