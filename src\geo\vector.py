"""Vector Data Processing Module

Provides vector data processing functionality based on geopandas.
"""

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import geopandas as gpd
import pandas as pd
import pyproj
from pyproj import CRS, Transformer
from shapely.geometry import LineString, Point, Polygon

logger = logging.getLogger(__name__)


class VectorProcessor:
    """Vector Data Processor

    Provides vector data reading, processing, analysis and output functionality.
    """

    def __init__(self, default_crs: str = "EPSG:4326"):
        """
        Initialize vector processor

        Args:
            default_crs: Default coordinate reference system
        """
        self.default_crs = default_crs
        self.logger = logging.getLogger(self.__class__.__name__)

    def read_file(self, file_path: Union[str, Path], **kwargs) -> gpd.GeoDataFrame:
        """
        Read vector file

        Args:
            file_path: File path
            **kwargs: Additional parameters for geopandas.read_file

        Returns:
            GeoDataFrame: Read vector data

        Raises:
            FileNotFoundError: File does not exist
            ValueError: File format not supported
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"File does not exist: {file_path}")

        try:
            gdf = gpd.read_file(file_path, **kwargs)
            self.logger.info(
                f"Successfully read vector file: {file_path}, records: {len(gdf)}"
            )
            return gdf
        except Exception as e:
            self.logger.error(f"Failed to read vector file: {file_path}, error: {e}")
            raise ValueError(f"Cannot read file: {e}")

    def create_points_from_coordinates(
        self, coordinates: List[tuple], crs: Optional[str] = None
    ) -> gpd.GeoDataFrame:
        """
        Create point features from coordinate list

        Args:
            coordinates: List of coordinate tuples [(x, y), ...]
            crs: Coordinate reference system

        Returns:
            GeoDataFrame: Point feature data
        """
        if not coordinates:
            raise ValueError("Coordinate list cannot be empty")

        points = [Point(coord) for coord in coordinates]
        gdf = gpd.GeoDataFrame(geometry=points, crs=crs or self.default_crs)

        self.logger.info(f"Created {len(points)} point features")
        return gdf

    def transform_crs(self, gdf: gpd.GeoDataFrame, target_crs: str) -> gpd.GeoDataFrame:
        """
        Coordinate reference system transformation

        Args:
            gdf: Input GeoDataFrame
            target_crs: Target coordinate reference system

        Returns:
            GeoDataFrame: Transformed data
        """
        if gdf.crs is None:
            self.logger.warning("Input data has no CRS information, using default CRS")
            gdf = gdf.set_crs(self.default_crs)

        try:
            transformed_gdf = gdf.to_crs(target_crs)
            self.logger.info(
                f"CRS transformation successful: {gdf.crs} -> {target_crs}"
            )
            return transformed_gdf
        except Exception as e:
            self.logger.error(f"CRS transformation failed: {e}")
            raise ValueError(f"CRS transformation failed: {e}")

    def spatial_join(
        self,
        left_gdf: gpd.GeoDataFrame,
        right_gdf: gpd.GeoDataFrame,
        how: str = "inner",
        predicate: str = "intersects",
    ) -> gpd.GeoDataFrame:
        """
        Spatial join

        Args:
            left_gdf: Left GeoDataFrame
            right_gdf: Right GeoDataFrame
            how: Join method ('left', 'right', 'outer', 'inner')
            predicate: Spatial relationship predicate

        Returns:
            GeoDataFrame: Join result
        """
        try:
            # Ensure both datasets use the same coordinate system
            if left_gdf.crs != right_gdf.crs:
                self.logger.info("Unifying CRS for spatial join")
                right_gdf = right_gdf.to_crs(left_gdf.crs)

            result = gpd.sjoin(left_gdf, right_gdf, how=how, predicate=predicate)
            self.logger.info(f"Spatial join completed, result records: {len(result)}")
            return result
        except Exception as e:
            self.logger.error(f"Spatial join failed: {e}")
            raise ValueError(f"Spatial join failed: {e}")

    def buffer_analysis(
        self, gdf: gpd.GeoDataFrame, distance: float, resolution: int = 16
    ) -> gpd.GeoDataFrame:
        """
        Buffer analysis

        Args:
            gdf: Input GeoDataFrame
            distance: Buffer distance
            resolution: Buffer resolution

        Returns:
            GeoDataFrame: Buffer result
        """
        try:
            buffered = gdf.copy()
            buffered["geometry"] = gdf.geometry.buffer(distance, resolution=resolution)
            self.logger.info(f"Buffer analysis completed, distance: {distance}")
            return buffered
        except Exception as e:
            self.logger.error(f"Buffer analysis failed: {e}")
            raise ValueError(f"Buffer analysis failed: {e}")

    def dissolve_by_attribute(
        self, gdf: gpd.GeoDataFrame, by: Union[str, List[str]], aggfunc: str = "first"
    ) -> gpd.GeoDataFrame:
        """
        Dissolve features by attribute

        Args:
            gdf: Input GeoDataFrame
            by: Field to dissolve by
            aggfunc: Aggregation function

        Returns:
            GeoDataFrame: Dissolved result
        """
        try:
            dissolved = gdf.dissolve(by=by, aggfunc=aggfunc)
            self.logger.info(f"Feature dissolve completed, dissolve field: {by}")
            return dissolved
        except Exception as e:
            self.logger.error(f"Feature dissolve failed: {e}")
            raise ValueError(f"Feature dissolve failed: {e}")

    def save_file(
        self,
        gdf: gpd.GeoDataFrame,
        file_path: Union[str, Path],
        driver: Optional[str] = None,
        **kwargs,
    ) -> None:
        """
        Save vector file

        Args:
            gdf: GeoDataFrame to save
            file_path: Output file path
            driver: Output format driver
            **kwargs: Additional save parameters
        """
        file_path = Path(file_path)

        # Create output directory
        file_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            if driver:
                gdf.to_file(file_path, driver=driver, **kwargs)
            else:
                gdf.to_file(file_path, **kwargs)
            self.logger.info(f"Vector file saved successfully: {file_path}")
        except Exception as e:
            self.logger.error(f"Failed to save vector file: {file_path}, error: {e}")
            raise ValueError(f"Failed to save file: {e}")

    def get_statistics(self, gdf: gpd.GeoDataFrame) -> Dict[str, Any]:
        """
        Get vector data statistics

        Args:
            gdf: Input GeoDataFrame

        Returns:
            dict: Statistics information
        """
        stats = {
            "record_count": len(gdf),
            "field_count": len(gdf.columns),
            "crs": str(gdf.crs),
            "bounds": gdf.total_bounds.tolist() if not gdf.empty else None,
            "geometry_types": gdf.geometry.geom_type.value_counts().to_dict(),
            "null_geometry_count": gdf.geometry.isna().sum(),
            "invalid_geometry_count": (~gdf.geometry.is_valid).sum(),
        }

        return stats
