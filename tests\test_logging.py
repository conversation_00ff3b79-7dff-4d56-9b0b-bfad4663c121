#!/usr/bin/env python3
"""
Tests for the database logging framework.

This module tests the logging configuration and functionality
to ensure it follows the PRD LOGGING_CONFIG structure.
"""

import logging
import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import MagicMock, patch

from src.config import get_config
from src.database.monitoring.logger import (
    configure_module_logger,
    get_config_logger,
    get_connection_logger,
    get_exception_logger,
    get_logger,
    get_monitoring_logger,
    setup_logging,
)


class TestLoggingFramework(unittest.TestCase):
    """Test cases for the logging framework."""

    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for test logs
        self.temp_dir = tempfile.mkdtemp()
        self.log_file = os.path.join(self.temp_dir, "test_database.log")

        # Reset logging configuration
        logging.getLogger().handlers.clear()
        logging.getLogger().setLevel(logging.WARNING)

    def tearDown(self):
        """Clean up test environment."""
        # Reset logging first to release file handles
        for handler in logging.getLogger().handlers[:]:
            handler.close()
            logging.getLogger().removeHandler(handler)

        # Clear all loggers
        for name in list(logging.Logger.manager.loggerDict.keys()):
            if name.startswith("database"):
                logger = logging.getLogger(name)
                for handler in logger.handlers[:]:
                    handler.close()
                    logger.removeHandler(handler)

        # Clean up log files
        try:
            if os.path.exists(self.log_file):
                os.remove(self.log_file)
        except (OSError, PermissionError):
            pass  # File might be locked, ignore

        try:
            if os.path.exists(self.temp_dir):
                import shutil

                shutil.rmtree(self.temp_dir, ignore_errors=True)
        except (OSError, PermissionError):
            pass  # Directory might be locked, ignore

    def test_setup_logging_with_config(self):
        """Test setup_logging with custom configuration."""
        config = Config(
            database=DatabaseConfig(
                host="localhost",
                port=5432,
                name="test_db",
                user="test_user",
                password=os.getenv("TEST_DB_PASSWORD", "secure_test_password_123!"),
            ),
            logging=LoggingConfig(
                level="DEBUG",
                format="%(levelname)s - %(message)s",
                file=self.log_file,
                max_bytes=1024,
                backup_count=2,
            ),
        )

        setup_logging(config)

        # Verify log file was created
        self.assertTrue(os.path.exists(self.log_file))

        # Test logging
        logger = get_logger("test")
        logger.info("Test message")

        # Verify log content
        with open(self.log_file, "r", encoding="utf-8") as f:
            content = f.read()
            self.assertIn("Test message", content)
            self.assertIn("INFO", content)

    def test_setup_logging_without_config(self):
        """Test setup_logging with default configuration."""
        # Create a config with a valid temp file path
        temp_log_file = os.path.join(self.temp_dir, "default_test.log")

        with patch("src.database.monitoring.logger.Config") as mock_config_class:
            mock_config = Config(
                database=DatabaseConfig(
                    host="localhost",
                    port=5432,
                    name="test_db",
                    user="test_user",
                    password=os.getenv("TEST_DB_PASSWORD", "secure_test_password_123!"),
                ),
                logging=LoggingConfig(file=temp_log_file),
            )
            mock_config_class.return_value = mock_config

            setup_logging()

        # Verify logger is configured
        logger = get_logger("database")
        self.assertIsInstance(logger, logging.Logger)
        self.assertEqual(logger.name, "database")

    def test_get_logger_with_prefix(self):
        """Test get_logger adds database prefix correctly."""
        logger1 = get_logger("connection")
        logger2 = get_logger("database.monitoring")
        logger3 = get_logger("database")

        self.assertEqual(logger1.name, "database.connection")
        self.assertEqual(logger2.name, "database.monitoring")
        self.assertEqual(logger3.name, "database")

    def test_configure_module_logger(self):
        """Test configure_module_logger functionality."""
        logger = configure_module_logger("connection", "ERROR")

        self.assertEqual(logger.name, "database.connection")
        self.assertEqual(logger.level, logging.ERROR)

    def test_predefined_loggers(self):
        """Test predefined logger functions."""
        connection_logger = get_connection_logger()
        monitoring_logger = get_monitoring_logger()
        config_logger = get_config_logger()
        exception_logger = get_exception_logger()

        self.assertEqual(connection_logger.name, "database.connection")
        self.assertEqual(monitoring_logger.name, "database.monitoring")
        self.assertEqual(config_logger.name, "database.config")
        self.assertEqual(exception_logger.name, "database.exceptions")

    def test_logging_config_to_dict_config(self):
        """Test LoggingConfig.to_dict_config method."""
        logging_config = LoggingConfig(
            level="WARNING",
            format="%(name)s - %(message)s",
            file="test.log",
            max_bytes=2048,
            backup_count=3,
            disable_existing_loggers=True,
        )

        dict_config = logging_config.to_dict_config()

        # Verify structure follows PRD LOGGING_CONFIG
        self.assertEqual(dict_config["version"], 1)
        self.assertTrue(dict_config["disable_existing_loggers"])

        # Verify formatters
        self.assertIn("standard", dict_config["formatters"])
        self.assertEqual(
            dict_config["formatters"]["standard"]["format"], "%(name)s - %(message)s"
        )

        # Verify handlers
        self.assertIn("file", dict_config["handlers"])
        self.assertIn("console", dict_config["handlers"])

        file_handler = dict_config["handlers"]["file"]
        self.assertEqual(file_handler["class"], "logging.handlers.RotatingFileHandler")
        self.assertEqual(file_handler["filename"], "test.log")
        self.assertEqual(file_handler["maxBytes"], 2048)
        self.assertEqual(file_handler["backupCount"], 3)

        # Verify loggers
        self.assertIn("database", dict_config["loggers"])
        db_logger = dict_config["loggers"]["database"]
        self.assertEqual(db_logger["level"], "WARNING")
        self.assertEqual(db_logger["handlers"], ["file", "console"])
        self.assertFalse(db_logger["propagate"])

    def test_log_directory_creation(self):
        """Test that log directory is created automatically."""
        log_dir = os.path.join(self.temp_dir, "nested", "logs")
        log_file = os.path.join(log_dir, "test.log")

        config = Config(
            database=DatabaseConfig(
                host="localhost",
                port=5432,
                name="test_db",
                user="test_user",
                password=os.getenv("TEST_DB_PASSWORD", "secure_test_password_123!"),
            ),
            logging=LoggingConfig(file=log_file),
        )

        setup_logging(config)

        # Verify directory was created
        self.assertTrue(os.path.exists(log_dir))

        # Clean up - close handlers first
        for handler in logging.getLogger().handlers[:]:
            handler.close()
            logging.getLogger().removeHandler(handler)

        # Clear database loggers
        for name in list(logging.Logger.manager.loggerDict.keys()):
            if name.startswith("database"):
                logger = logging.getLogger(name)
                for handler in logger.handlers[:]:
                    handler.close()
                    logger.removeHandler(handler)

        # Clean up directories
        try:
            import shutil

            nested_dir = os.path.join(self.temp_dir, "nested")
            if os.path.exists(nested_dir):
                shutil.rmtree(nested_dir, ignore_errors=True)
        except (OSError, PermissionError):
            pass  # Ignore cleanup errors

    def test_logging_integration(self):
        """Test complete logging integration."""
        config = Config(
            database=DatabaseConfig(
                host="localhost",
                port=5432,
                name="test_db",
                user="test_user",
                password=os.getenv("TEST_DB_PASSWORD", "secure_test_password_123!"),
            ),
            logging=LoggingConfig(level="INFO", file=self.log_file),
        )

        setup_logging(config)

        # Test different loggers
        loggers = {
            "connection": get_connection_logger(),
            "monitoring": get_monitoring_logger(),
            "config": get_config_logger(),
            "exceptions": get_exception_logger(),
        }

        # Log messages from different modules
        for module, logger in loggers.items():
            logger.info(f"Test message from {module} module")
            logger.warning(f"Warning from {module} module")
            logger.error(f"Error from {module} module")

        # Verify all messages were logged
        with open(self.log_file, "r", encoding="utf-8") as f:
            content = f.read()

        for module in loggers.keys():
            self.assertIn(f"Test message from {module} module", content)
            self.assertIn(f"Warning from {module} module", content)
            self.assertIn(f"Error from {module} module", content)

    def test_default_logging_config_values(self):
        """Test default values in LoggingConfig."""
        config = LoggingConfig()

        self.assertEqual(config.level, "INFO")
        self.assertEqual(config.file, "logs/database.log")
        self.assertEqual(config.max_bytes, 10485760)  # 10MB
        self.assertEqual(config.backup_count, 5)
        self.assertFalse(config.disable_existing_loggers)
        self.assertEqual(
            config.format, "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )


if __name__ == "__main__":
    unittest.main()
