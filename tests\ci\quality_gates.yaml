# Connect电信数据分析平台 - 质量门禁配置
# 定义各种质量标准、规则和阈值

# 全局配置
global:
  version: "1.0"
  description: "Connect平台质量门禁配置"
  last_updated: "2024-01-20"
  
# 质量门禁规则
quality_gates:
  # 功能质量规则
  functional:
    - name: "test_success_rate_critical"
      metric: "test_success_rate"
      operator: ">="
      threshold: 95.0
      severity: "critical"
      description: "测试成功率必须达到95%以上"
      category: "functional"
      enabled: true
      
    - name: "test_success_rate_warning"
      metric: "test_success_rate"
      operator: ">="
      threshold: 90.0
      severity: "major"
      description: "测试成功率建议达到90%以上"
      category: "functional"
      enabled: true
      
    - name: "unit_test_coverage"
      metric: "unit_test_coverage"
      operator: ">="
      threshold: 85.0
      severity: "critical"
      description: "单元测试覆盖率必须达到85%以上"
      category: "functional"
      enabled: true
      
    - name: "integration_test_coverage"
      metric: "integration_test_coverage"
      operator: ">="
      threshold: 70.0
      severity: "major"
      description: "集成测试覆盖率建议达到70%以上"
      category: "functional"
      enabled: true
      
  # 性能质量规则
  performance:
    - name: "api_response_time_critical"
      metric: "avg_api_response_time"
      operator: "<="
      threshold: 500.0
      severity: "critical"
      description: "API平均响应时间必须小于500ms"
      category: "performance"
      enabled: true
      
    - name: "api_response_time_warning"
      metric: "avg_api_response_time"
      operator: "<="
      threshold: 300.0
      severity: "minor"
      description: "API平均响应时间建议小于300ms"
      category: "performance"
      enabled: true
      
    - name: "page_load_time"
      metric: "avg_page_load_time"
      operator: "<="
      threshold: 3000.0
      severity: "critical"
      description: "页面加载时间必须小于3秒"
      category: "performance"
      enabled: true
      
    - name: "throughput_minimum"
      metric: "requests_per_second"
      operator: ">="
      threshold: 100.0
      severity: "critical"
      description: "系统吞吐量必须达到100 req/s以上"
      category: "performance"
      enabled: true
      
    - name: "cpu_usage_limit"
      metric: "avg_cpu_usage"
      operator: "<="
      threshold: 80.0
      severity: "major"
      description: "CPU使用率应保持在80%以下"
      category: "performance"
      enabled: true
      
    - name: "memory_usage_limit"
      metric: "avg_memory_usage"
      operator: "<="
      threshold: 80.0
      severity: "major"
      description: "内存使用率应保持在80%以下"
      category: "performance"
      enabled: true
      
    - name: "concurrent_users"
      metric: "max_concurrent_users"
      operator: ">="
      threshold: 20.0
      severity: "critical"
      description: "系统必须支持20个并发用户"
      category: "performance"
      enabled: true
      
    - name: "large_data_processing"
      metric: "data_processing_time_5m_rows"
      operator: "<="
      threshold: 10.0
      severity: "critical"
      description: "500万行数据处理时间必须小于10秒"
      category: "performance"
      enabled: true
      
  # 安全质量规则
  security:
    - name: "critical_vulnerabilities"
      metric: "critical_vulnerabilities_count"
      operator: "=="
      threshold: 0.0
      severity: "critical"
      description: "不允许存在严重安全漏洞"
      category: "security"
      enabled: true
      
    - name: "high_vulnerabilities"
      metric: "high_vulnerabilities_count"
      operator: "<="
      threshold: 0.0
      severity: "critical"
      description: "不允许存在高危安全漏洞"
      category: "security"
      enabled: true
      
    - name: "medium_vulnerabilities"
      metric: "medium_vulnerabilities_count"
      operator: "<="
      threshold: 2.0
      severity: "major"
      description: "中危漏洞数量应控制在2个以内"
      category: "security"
      enabled: true
      
    - name: "authentication_security"
      metric: "auth_security_score"
      operator: ">="
      threshold: 90.0
      severity: "critical"
      description: "认证安全分数必须达到90分以上"
      category: "security"
      enabled: true
      
    - name: "data_encryption"
      metric: "data_encryption_score"
      operator: ">="
      threshold: 95.0
      severity: "critical"
      description: "数据加密分数必须达到95分以上"
      category: "security"
      enabled: true
      
    - name: "access_control"
      metric: "access_control_score"
      operator: ">="
      threshold: 90.0
      severity: "critical"
      description: "访问控制分数必须达到90分以上"
      category: "security"
      enabled: true
      
    - name: "dependency_vulnerabilities"
      metric: "dependency_vulnerabilities_count"
      operator: "<="
      threshold: 0.0
      severity: "major"
      description: "依赖库不应存在已知漏洞"
      category: "security"
      enabled: true
      
  # 可靠性质量规则
  reliability:
    - name: "error_rate"
      metric: "error_rate"
      operator: "<="
      threshold: 0.1
      severity: "critical"
      description: "系统错误率必须小于0.1%"
      category: "reliability"
      enabled: true
      
    - name: "availability"
      metric: "system_availability"
      operator: ">="
      threshold: 99.5
      severity: "critical"
      description: "系统可用性必须达到99.5%以上"
      category: "reliability"
      enabled: true
      
    - name: "mttr"
      metric: "mean_time_to_recovery"
      operator: "<="
      threshold: 300.0
      severity: "major"
      description: "平均恢复时间应小于5分钟"
      category: "reliability"
      enabled: true
      
    - name: "data_consistency"
      metric: "data_consistency_score"
      operator: ">="
      threshold: 99.9
      severity: "critical"
      description: "数据一致性分数必须达到99.9%以上"
      category: "reliability"
      enabled: true
      
  # 可维护性质量规则
  maintainability:
    - name: "code_complexity"
      metric: "cyclomatic_complexity"
      operator: "<="
      threshold: 10.0
      severity: "major"
      description: "代码圈复杂度应小于10"
      category: "maintainability"
      enabled: true
      
    - name: "code_duplication"
      metric: "code_duplication_rate"
      operator: "<="
      threshold: 5.0
      severity: "major"
      description: "代码重复率应小于5%"
      category: "maintainability"
      enabled: true
      
    - name: "technical_debt"
      metric: "technical_debt_ratio"
      operator: "<="
      threshold: 5.0
      severity: "major"
      description: "技术债务比率应小于5%"
      category: "maintainability"
      enabled: true
      
    - name: "documentation_coverage"
      metric: "documentation_coverage"
      operator: ">="
      threshold: 80.0
      severity: "minor"
      description: "文档覆盖率建议达到80%以上"
      category: "maintainability"
      enabled: true

# 部署决策矩阵
deployment_decisions:
  # 自动批准条件
  auto_approve:
    conditions:
      - all_critical_rules_passed: true
      - critical_failures: 0
      - major_failures: 0
      - overall_score: ">= 90"
    decision: "approve"
    description: "所有关键规则通过，可自动部署"
    
  # 条件批准
  conditional_approve:
    conditions:
      - all_critical_rules_passed: true
      - critical_failures: 0
      - major_failures: "<= 2"
      - overall_score: ">= 80"
    decision: "conditional"
    description: "关键规则通过，存在少量非关键问题，可条件部署"
    
  # 人工审核
  manual_review:
    conditions:
      - all_critical_rules_passed: true
      - critical_failures: 0
      - major_failures: "> 2"
      - overall_score: ">= 70"
    decision: "manual_review"
    description: "存在较多非关键问题，需要人工审核"
    
  # 拒绝部署
  reject:
    conditions:
      - critical_failures: "> 0"
    decision: "reject"
    description: "存在关键问题，拒绝部署"

# 通知配置
notifications:
  # 通知触发条件
  triggers:
    - condition: "status == 'failed'"
      channels: ["slack", "email", "teams"]
      urgency: "high"
      
    - condition: "status == 'warning'"
      channels: ["slack"]
      urgency: "medium"
      
    - condition: "deployment_decision == 'reject'"
      channels: ["slack", "email"]
      urgency: "high"
      
    - condition: "deployment_decision == 'manual_review'"
      channels: ["slack"]
      urgency: "medium"
      
  # 通知模板
  templates:
    slack:
      success:
        title: "✅ Connect质量门禁检查通过"
        color: "good"
        
      warning:
        title: "⚠️ Connect质量门禁检查警告"
        color: "warning"
        
      failure:
        title: "❌ Connect质量门禁检查失败"
        color: "danger"
        
    email:
      subject_template: "Connect质量门禁检查 - {status} - {branch}"
      
# 监控集成配置
monitoring:
  # 指标推送配置
  metrics:
    prometheus:
      enabled: true
      job_name: "connect_ci"
      metrics:
        - name: "connect_test_success_rate"
          type: "gauge"
          description: "测试成功率"
          
        - name: "connect_code_coverage"
          type: "gauge"
          description: "代码覆盖率"
          
        - name: "connect_performance_score"
          type: "gauge"
          description: "性能分数"
          
        - name: "connect_security_score"
          type: "gauge"
          description: "安全分数"
          
        - name: "connect_overall_quality_score"
          type: "gauge"
          description: "综合质量分数"
          
        - name: "connect_quality_gate_status"
          type: "gauge"
          description: "质量门禁状态 (1=通过, 0=失败)"
          
        - name: "connect_deployment_decision"
          type: "gauge"
          description: "部署决策 (1=批准, 0=拒绝)"
          
    datadog:
      enabled: false
      tags:
        - "environment:ci"
        - "service:connect"
        - "team:quality"
        
    newrelic:
      enabled: false
      event_type: "ConnectCIQualityGate"
      
  # 告警配置
  alerts:
    - name: "quality_gate_failure"
      condition: "connect_quality_gate_status == 0"
      severity: "critical"
      description: "质量门禁检查失败"
      
    - name: "low_test_coverage"
      condition: "connect_code_coverage < 80"
      severity: "warning"
      description: "代码覆盖率过低"
      
    - name: "poor_performance"
      condition: "connect_performance_score < 70"
      severity: "warning"
      description: "性能分数过低"
      
    - name: "security_issues"
      condition: "connect_security_score < 90"
      severity: "critical"
      description: "安全分数过低"

# 报告配置
reporting:
  # 报告格式
  formats:
    - "json"
    - "html"
    - "junit"
    
  # 报告内容
  sections:
    - "summary"
    - "metrics"
    - "quality_gates"
    - "recommendations"
    - "trends"
    
  # 历史趋势
  trends:
    enabled: true
    retention_days: 90
    metrics:
      - "test_success_rate"
      - "code_coverage"
      - "performance_score"
      - "security_score"
      - "overall_score"

# 环境特定配置
environments:
  development:
    quality_gates:
      # 开发环境相对宽松的标准
      test_success_rate_critical: 85.0
      code_coverage_critical: 70.0
      
  staging:
    quality_gates:
      # 预发布环境标准
      test_success_rate_critical: 90.0
      code_coverage_critical: 75.0
      
  production:
    quality_gates:
      # 生产环境严格标准
      test_success_rate_critical: 95.0
      code_coverage_critical: 80.0
      security_score_critical: 95.0

# 集成配置
integrations:
  # CI/CD平台
  ci_platforms:
    github_actions:
      enabled: true
      status_checks: true
      
    gitlab_ci:
      enabled: false
      
    jenkins:
      enabled: false
      
  # 代码质量工具
  quality_tools:
    sonarqube:
      enabled: true
      url: "http://localhost:9000"
      
    codecov:
      enabled: false
      
  # 安全扫描工具
  security_tools:
    snyk:
      enabled: false
      
    owasp_zap:
      enabled: true
      
    bandit:
      enabled: true