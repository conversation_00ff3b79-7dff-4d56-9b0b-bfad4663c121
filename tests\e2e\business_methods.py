#!/usr/bin/env python3
"""
Connect平台E2E测试业务方法实现

本模块提供E2E测试中使用的具体业务方法实现，包括数据导入、
路测分析、站点管理、KPI监控等核心业务功能的自动化测试方法。

作者: Connect质量工程团队
日期: 2024-01-20
"""

import os
import time
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BusinessMethods:
    """E2E测试业务方法实现"""
    
    def __init__(self, driver: webdriver.Chrome, base_url: str, api_base_url: str):
        self.driver = driver
        self.base_url = base_url
        self.api_base_url = api_base_url
        self.wait = WebDriverWait(driver, 10)
        self.session = requests.Session()
        
    def wait_for_element(self, by: By, value: str, timeout: int = 10):
        """等待元素出现"""
        return WebDriverWait(self.driver, timeout).until(
            EC.presence_of_element_located((by, value))
        )
    
    def wait_for_clickable(self, by: By, value: str, timeout: int = 10):
        """等待元素可点击"""
        return WebDriverWait(self.driver, timeout).until(
            EC.element_to_be_clickable((by, value))
        )
    
    def wait_for_page_load(self, timeout: int = 30):
        """等待页面加载完成"""
        WebDriverWait(self.driver, timeout).until(
            lambda driver: driver.execute_script("return document.readyState") == "complete"
        )
    
    def scroll_to_element(self, element):
        """滚动到元素位置"""
        self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(0.5)
    
    # ==================== 数据管理业务方法 ====================
    
    def import_ep_data(self, file_path: str, expected_records: int = None) -> bool:
        """导入EP数据"""
        logger.info(f"开始导入EP数据: {file_path}")
        
        try:
            # 导航到数据管理页面
            self.driver.get(f"{self.base_url}/data-management")
            self.wait_for_page_load()
            
            # 点击EP数据导入标签
            ep_tab = self.wait_for_clickable(By.XPATH, "//a[contains(text(), 'EP数据')]")
            ep_tab.click()
            time.sleep(1)
            
            # 点击导入按钮
            import_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '导入数据')]")
            import_btn.click()
            time.sleep(1)
            
            # 上传文件
            file_input = self.wait_for_element(By.XPATH, "//input[@type='file']")
            file_input.send_keys(os.path.abspath(file_path))
            time.sleep(2)
            
            # 确认上传
            confirm_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '确认导入')]")
            confirm_btn.click()
            
            # 等待导入完成
            success_msg = self.wait_for_element(
                By.XPATH, 
                "//div[contains(@class, 'success') or contains(text(), '导入成功')]",
                timeout=60
            )
            
            # 验证导入记录数
            if expected_records:
                records_text = self.wait_for_element(
                    By.XPATH, 
                    "//span[contains(text(), '条记录')]"
                ).text
                actual_records = int(''.join(filter(str.isdigit, records_text)))
                assert actual_records == expected_records, f"期望导入{expected_records}条记录，实际导入{actual_records}条"
            
            logger.info("EP数据导入成功")
            return True
            
        except Exception as e:
            logger.error(f"EP数据导入失败: {str(e)}")
            return False
    
    def import_cdr_data(self, file_path: str, expected_records: int = None) -> bool:
        """导入CDR数据"""
        logger.info(f"开始导入CDR数据: {file_path}")
        
        try:
            # 导航到数据管理页面
            self.driver.get(f"{self.base_url}/data-management")
            self.wait_for_page_load()
            
            # 点击CDR数据导入标签
            cdr_tab = self.wait_for_clickable(By.XPATH, "//a[contains(text(), 'CDR数据')]")
            cdr_tab.click()
            time.sleep(1)
            
            # 点击导入按钮
            import_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '导入数据')]")
            import_btn.click()
            time.sleep(1)
            
            # 上传文件
            file_input = self.wait_for_element(By.XPATH, "//input[@type='file']")
            file_input.send_keys(os.path.abspath(file_path))
            time.sleep(2)
            
            # 选择数据格式（如果需要）
            format_select = self.driver.find_elements(By.XPATH, "//select[@name='format']")
            if format_select:
                Select(format_select[0]).select_by_value("csv")
            
            # 确认上传
            confirm_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '确认导入')]")
            confirm_btn.click()
            
            # 等待导入完成
            success_msg = self.wait_for_element(
                By.XPATH, 
                "//div[contains(@class, 'success') or contains(text(), '导入成功')]",
                timeout=120  # CDR数据可能较大，等待时间更长
            )
            
            # 验证导入记录数
            if expected_records:
                records_text = self.wait_for_element(
                    By.XPATH, 
                    "//span[contains(text(), '条记录')]"
                ).text
                actual_records = int(''.join(filter(str.isdigit, records_text)))
                assert actual_records == expected_records, f"期望导入{expected_records}条记录，实际导入{actual_records}条"
            
            logger.info("CDR数据导入成功")
            return True
            
        except Exception as e:
            logger.error(f"CDR数据导入失败: {str(e)}")
            return False
    
    def import_site_data(self, file_path: str, expected_records: int = None) -> bool:
        """导入站点数据"""
        logger.info(f"开始导入站点数据: {file_path}")
        
        try:
            # 导航到站点管理页面
            self.driver.get(f"{self.base_url}/site-management")
            self.wait_for_page_load()
            
            # 点击导入站点按钮
            import_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '导入站点')]")
            import_btn.click()
            time.sleep(1)
            
            # 上传文件
            file_input = self.wait_for_element(By.XPATH, "//input[@type='file']")
            file_input.send_keys(os.path.abspath(file_path))
            time.sleep(2)
            
            # 确认上传
            confirm_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '确认导入')]")
            confirm_btn.click()
            
            # 等待导入完成
            success_msg = self.wait_for_element(
                By.XPATH, 
                "//div[contains(@class, 'success') or contains(text(), '导入成功')]",
                timeout=60
            )
            
            # 验证导入记录数
            if expected_records:
                records_text = self.wait_for_element(
                    By.XPATH, 
                    "//span[contains(text(), '个站点')]"
                ).text
                actual_records = int(''.join(filter(str.isdigit, records_text)))
                assert actual_records == expected_records, f"期望导入{expected_records}个站点，实际导入{actual_records}个"
            
            logger.info("站点数据导入成功")
            return True
            
        except Exception as e:
            logger.error(f"站点数据导入失败: {str(e)}")
            return False
    
    def validate_data_format(self, data_type: str, file_path: str) -> Dict[str, Any]:
        """验证数据格式"""
        logger.info(f"验证{data_type}数据格式: {file_path}")
        
        try:
            # 使用API验证数据格式
            with open(file_path, 'rb') as f:
                files = {'file': f}
                response = self.session.post(
                    f"{self.api_base_url}/data/validate",
                    files=files,
                    data={'data_type': data_type}
                )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"数据格式验证结果: {result}")
                return result
            else:
                logger.error(f"数据格式验证失败: {response.status_code}")
                return {'valid': False, 'errors': [f"HTTP {response.status_code}"]}
                
        except Exception as e:
            logger.error(f"数据格式验证异常: {str(e)}")
            return {'valid': False, 'errors': [str(e)]}
    
    def check_data_quality(self, data_type: str) -> Dict[str, Any]:
        """检查数据质量"""
        logger.info(f"检查{data_type}数据质量")
        
        try:
            # 导航到数据质量页面
            self.driver.get(f"{self.base_url}/data-quality")
            self.wait_for_page_load()
            
            # 选择数据类型
            data_type_select = Select(self.wait_for_element(By.NAME, "dataType"))
            data_type_select.select_by_value(data_type)
            
            # 点击检查按钮
            check_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '开始检查')]")
            check_btn.click()
            
            # 等待检查完成
            result_div = self.wait_for_element(
                By.XPATH, 
                "//div[@class='quality-result']",
                timeout=60
            )
            
            # 提取质量指标
            completeness = float(self.driver.find_element(
                By.XPATH, "//span[@data-metric='completeness']"
            ).text.replace('%', ''))
            
            accuracy = float(self.driver.find_element(
                By.XPATH, "//span[@data-metric='accuracy']"
            ).text.replace('%', ''))
            
            consistency = float(self.driver.find_element(
                By.XPATH, "//span[@data-metric='consistency']"
            ).text.replace('%', ''))
            
            result = {
                'completeness': completeness,
                'accuracy': accuracy,
                'consistency': consistency,
                'overall_score': (completeness + accuracy + consistency) / 3
            }
            
            logger.info(f"数据质量检查结果: {result}")
            return result
            
        except Exception as e:
            logger.error(f"数据质量检查失败: {str(e)}")
            return {'error': str(e)}
    
    # ==================== 路测分析业务方法 ====================
    
    def create_drive_test_project(self, project_name: str, description: str = "") -> str:
        """创建路测项目"""
        logger.info(f"创建路测项目: {project_name}")
        
        try:
            # 导航到路测分析页面
            self.driver.get(f"{self.base_url}/drive-test")
            self.wait_for_page_load()
            
            # 点击新建项目按钮
            new_project_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '新建项目')]")
            new_project_btn.click()
            time.sleep(1)
            
            # 填写项目信息
            name_input = self.wait_for_element(By.NAME, "projectName")
            name_input.clear()
            name_input.send_keys(project_name)
            
            if description:
                desc_input = self.driver.find_element(By.NAME, "description")
                desc_input.clear()
                desc_input.send_keys(description)
            
            # 确认创建
            create_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '创建')]")
            create_btn.click()
            
            # 等待创建成功并获取项目ID
            success_msg = self.wait_for_element(
                By.XPATH, 
                "//div[contains(@class, 'success')]",
                timeout=30
            )
            
            # 从URL或页面元素获取项目ID
            time.sleep(2)
            current_url = self.driver.current_url
            project_id = current_url.split('/')[-1] if '/project/' in current_url else None
            
            if not project_id:
                # 尝试从页面元素获取
                project_id_element = self.driver.find_elements(
                    By.XPATH, "//span[@data-field='project-id']"
                )
                if project_id_element:
                    project_id = project_id_element[0].text
            
            logger.info(f"路测项目创建成功，项目ID: {project_id}")
            return project_id
            
        except Exception as e:
            logger.error(f"创建路测项目失败: {str(e)}")
            return None
    
    def upload_drive_test_data(self, project_id: str, file_path: str) -> bool:
        """上传路测数据"""
        logger.info(f"上传路测数据到项目 {project_id}: {file_path}")
        
        try:
            # 导航到项目页面
            self.driver.get(f"{self.base_url}/drive-test/project/{project_id}")
            self.wait_for_page_load()
            
            # 点击上传数据按钮
            upload_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '上传数据')]")
            upload_btn.click()
            time.sleep(1)
            
            # 上传文件
            file_input = self.wait_for_element(By.XPATH, "//input[@type='file']")
            file_input.send_keys(os.path.abspath(file_path))
            time.sleep(2)
            
            # 确认上传
            confirm_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '确认上传')]")
            confirm_btn.click()
            
            # 等待上传完成
            success_msg = self.wait_for_element(
                By.XPATH, 
                "//div[contains(@class, 'success') or contains(text(), '上传成功')]",
                timeout=120
            )
            
            logger.info("路测数据上传成功")
            return True
            
        except Exception as e:
            logger.error(f"路测数据上传失败: {str(e)}")
            return False
    
    def run_drive_test_analysis(self, project_id: str, analysis_type: str = "coverage") -> bool:
        """运行路测分析"""
        logger.info(f"运行路测分析，项目: {project_id}, 类型: {analysis_type}")
        
        try:
            # 导航到项目分析页面
            self.driver.get(f"{self.base_url}/drive-test/project/{project_id}/analysis")
            self.wait_for_page_load()
            
            # 选择分析类型
            analysis_select = Select(self.wait_for_element(By.NAME, "analysisType"))
            analysis_select.select_by_value(analysis_type)
            
            # 点击开始分析按钮
            start_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '开始分析')]")
            start_btn.click()
            
            # 等待分析完成
            result_div = self.wait_for_element(
                By.XPATH, 
                "//div[@class='analysis-result'] | //div[contains(@class, 'analysis-complete')]",
                timeout=300  # 分析可能需要较长时间
            )
            
            # 检查是否有错误
            error_elements = self.driver.find_elements(
                By.XPATH, "//div[contains(@class, 'error') or contains(@class, 'failed')]"
            )
            
            if error_elements:
                error_text = error_elements[0].text
                logger.error(f"路测分析失败: {error_text}")
                return False
            
            logger.info("路测分析完成")
            return True
            
        except Exception as e:
            logger.error(f"路测分析失败: {str(e)}")
            return False
    
    def generate_coverage_heatmap(self, project_id: str, signal_type: str = "rsrp") -> bool:
        """生成覆盖热力图"""
        logger.info(f"生成覆盖热力图，项目: {project_id}, 信号类型: {signal_type}")
        
        try:
            # 导航到热力图页面
            self.driver.get(f"{self.base_url}/drive-test/project/{project_id}/heatmap")
            self.wait_for_page_load()
            
            # 选择信号类型
            signal_select = Select(self.wait_for_element(By.NAME, "signalType"))
            signal_select.select_by_value(signal_type)
            
            # 点击生成按钮
            generate_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '生成热力图')]")
            generate_btn.click()
            
            # 等待热力图生成
            heatmap_canvas = self.wait_for_element(
                By.XPATH, 
                "//canvas[@class='heatmap'] | //div[@class='heatmap-container']",
                timeout=60
            )
            
            # 验证热力图是否正确显示
            if heatmap_canvas.is_displayed():
                logger.info("覆盖热力图生成成功")
                return True
            else:
                logger.error("热力图未正确显示")
                return False
            
        except Exception as e:
            logger.error(f"生成覆盖热力图失败: {str(e)}")
            return False
    
    def export_analysis_report(self, project_id: str, report_format: str = "pdf") -> str:
        """导出分析报告"""
        logger.info(f"导出分析报告，项目: {project_id}, 格式: {report_format}")
        
        try:
            # 导航到报告页面
            self.driver.get(f"{self.base_url}/drive-test/project/{project_id}/report")
            self.wait_for_page_load()
            
            # 选择报告格式
            format_select = Select(self.wait_for_element(By.NAME, "reportFormat"))
            format_select.select_by_value(report_format)
            
            # 点击导出按钮
            export_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '导出报告')]")
            export_btn.click()
            
            # 等待下载链接出现
            download_link = self.wait_for_element(
                By.XPATH, 
                "//a[contains(@href, 'download') or contains(text(), '下载')]",
                timeout=60
            )
            
            download_url = download_link.get_attribute('href')
            logger.info(f"分析报告导出成功，下载链接: {download_url}")
            return download_url
            
        except Exception as e:
            logger.error(f"导出分析报告失败: {str(e)}")
            return None
    
    # ==================== 站点管理业务方法 ====================
    
    def create_site(self, site_data: Dict[str, Any]) -> str:
        """创建站点"""
        logger.info(f"创建站点: {site_data.get('site_name', 'Unknown')}")
        
        try:
            # 导航到站点管理页面
            self.driver.get(f"{self.base_url}/site-management")
            self.wait_for_page_load()
            
            # 点击新建站点按钮
            new_site_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '新建站点')]")
            new_site_btn.click()
            time.sleep(1)
            
            # 填写站点信息
            for field, value in site_data.items():
                if field in ['site_name', 'site_id', 'address', 'latitude', 'longitude']:
                    input_element = self.wait_for_element(By.NAME, field)
                    input_element.clear()
                    input_element.send_keys(str(value))
            
            # 选择站点类型
            if 'site_type' in site_data:
                site_type_select = Select(self.wait_for_element(By.NAME, "site_type"))
                site_type_select.select_by_value(site_data['site_type'])
            
            # 确认创建
            create_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '创建')]")
            create_btn.click()
            
            # 等待创建成功并获取站点ID
            success_msg = self.wait_for_element(
                By.XPATH, 
                "//div[contains(@class, 'success')]",
                timeout=30
            )
            
            # 获取新创建的站点ID
            site_id_element = self.wait_for_element(
                By.XPATH, "//span[@data-field='site-id']"
            )
            site_id = site_id_element.text
            
            logger.info(f"站点创建成功，站点ID: {site_id}")
            return site_id
            
        except Exception as e:
            logger.error(f"创建站点失败: {str(e)}")
            return None
    
    def update_site(self, site_id: str, update_data: Dict[str, Any]) -> bool:
        """更新站点信息"""
        logger.info(f"更新站点 {site_id}: {update_data}")
        
        try:
            # 导航到站点详情页面
            self.driver.get(f"{self.base_url}/site-management/site/{site_id}")
            self.wait_for_page_load()
            
            # 点击编辑按钮
            edit_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '编辑')]")
            edit_btn.click()
            time.sleep(1)
            
            # 更新字段
            for field, value in update_data.items():
                input_element = self.wait_for_element(By.NAME, field)
                input_element.clear()
                input_element.send_keys(str(value))
            
            # 保存更改
            save_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '保存')]")
            save_btn.click()
            
            # 等待保存成功
            success_msg = self.wait_for_element(
                By.XPATH, 
                "//div[contains(@class, 'success') or contains(text(), '保存成功')]",
                timeout=30
            )
            
            logger.info("站点信息更新成功")
            return True
            
        except Exception as e:
            logger.error(f"更新站点信息失败: {str(e)}")
            return False
    
    def run_gap_analysis(self, area_bounds: Dict[str, float]) -> Dict[str, Any]:
        """运行GAP分析"""
        logger.info(f"运行GAP分析，区域: {area_bounds}")
        
        try:
            # 导航到GAP分析页面
            self.driver.get(f"{self.base_url}/site-management/gap-analysis")
            self.wait_for_page_load()
            
            # 设置分析区域
            for bound, value in area_bounds.items():
                input_element = self.wait_for_element(By.NAME, bound)
                input_element.clear()
                input_element.send_keys(str(value))
            
            # 开始分析
            analyze_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '开始分析')]")
            analyze_btn.click()
            
            # 等待分析完成
            result_div = self.wait_for_element(
                By.XPATH, 
                "//div[@class='gap-analysis-result']",
                timeout=120
            )
            
            # 提取分析结果
            coverage_rate = float(self.driver.find_element(
                By.XPATH, "//span[@data-metric='coverage-rate']"
            ).text.replace('%', ''))
            
            gap_count = int(self.driver.find_element(
                By.XPATH, "//span[@data-metric='gap-count']"
            ).text)
            
            recommended_sites = int(self.driver.find_element(
                By.XPATH, "//span[@data-metric='recommended-sites']"
            ).text)
            
            result = {
                'coverage_rate': coverage_rate,
                'gap_count': gap_count,
                'recommended_sites': recommended_sites,
                'analysis_time': datetime.now().isoformat()
            }
            
            logger.info(f"GAP分析完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"GAP分析失败: {str(e)}")
            return {'error': str(e)}
    
    def calculate_coverage_rate(self, area_id: str) -> float:
        """计算覆盖率"""
        logger.info(f"计算区域 {area_id} 的覆盖率")
        
        try:
            # 使用API计算覆盖率
            response = self.session.get(
                f"{self.api_base_url}/site/coverage-rate",
                params={'area_id': area_id}
            )
            
            if response.status_code == 200:
                result = response.json()
                coverage_rate = result.get('coverage_rate', 0.0)
                logger.info(f"覆盖率计算结果: {coverage_rate}%")
                return coverage_rate
            else:
                logger.error(f"覆盖率计算失败: {response.status_code}")
                return 0.0
                
        except Exception as e:
            logger.error(f"覆盖率计算异常: {str(e)}")
            return 0.0
    
    # ==================== KPI监控业务方法 ====================
    
    def setup_kpi_alert(self, kpi_name: str, threshold: float, condition: str = ">") -> str:
        """设置KPI告警"""
        logger.info(f"设置KPI告警: {kpi_name} {condition} {threshold}")
        
        try:
            # 导航到KPI监控页面
            self.driver.get(f"{self.base_url}/kpi-monitoring")
            self.wait_for_page_load()
            
            # 点击设置告警按钮
            alert_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '设置告警')]")
            alert_btn.click()
            time.sleep(1)
            
            # 选择KPI
            kpi_select = Select(self.wait_for_element(By.NAME, "kpiName"))
            kpi_select.select_by_value(kpi_name)
            
            # 设置条件
            condition_select = Select(self.wait_for_element(By.NAME, "condition"))
            condition_select.select_by_value(condition)
            
            # 设置阈值
            threshold_input = self.wait_for_element(By.NAME, "threshold")
            threshold_input.clear()
            threshold_input.send_keys(str(threshold))
            
            # 确认设置
            confirm_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '确认')]")
            confirm_btn.click()
            
            # 获取告警ID
            success_msg = self.wait_for_element(
                By.XPATH, 
                "//div[contains(@class, 'success')]",
                timeout=30
            )
            
            alert_id_element = self.wait_for_element(
                By.XPATH, "//span[@data-field='alert-id']"
            )
            alert_id = alert_id_element.text
            
            logger.info(f"KPI告警设置成功，告警ID: {alert_id}")
            return alert_id
            
        except Exception as e:
            logger.error(f"设置KPI告警失败: {str(e)}")
            return None
    
    def generate_kpi_report(self, start_date: str, end_date: str, kpi_list: List[str]) -> str:
        """生成KPI报告"""
        logger.info(f"生成KPI报告: {start_date} 到 {end_date}")
        
        try:
            # 导航到KPI报告页面
            self.driver.get(f"{self.base_url}/kpi-monitoring/report")
            self.wait_for_page_load()
            
            # 设置日期范围
            start_date_input = self.wait_for_element(By.NAME, "startDate")
            start_date_input.clear()
            start_date_input.send_keys(start_date)
            
            end_date_input = self.wait_for_element(By.NAME, "endDate")
            end_date_input.clear()
            end_date_input.send_keys(end_date)
            
            # 选择KPI
            for kpi in kpi_list:
                kpi_checkbox = self.wait_for_element(
                    By.XPATH, f"//input[@type='checkbox'][@value='{kpi}']"
                )
                if not kpi_checkbox.is_selected():
                    kpi_checkbox.click()
            
            # 生成报告
            generate_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '生成报告')]")
            generate_btn.click()
            
            # 等待报告生成完成
            download_link = self.wait_for_element(
                By.XPATH, 
                "//a[contains(@href, 'download') or contains(text(), '下载')]",
                timeout=60
            )
            
            report_url = download_link.get_attribute('href')
            logger.info(f"KPI报告生成成功，下载链接: {report_url}")
            return report_url
            
        except Exception as e:
            logger.error(f"生成KPI报告失败: {str(e)}")
            return None
    
    def check_alert_status(self, alert_id: str) -> Dict[str, Any]:
        """检查告警状态"""
        logger.info(f"检查告警状态: {alert_id}")
        
        try:
            # 使用API检查告警状态
            response = self.session.get(
                f"{self.api_base_url}/kpi/alert/{alert_id}/status"
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"告警状态: {result}")
                return result
            else:
                logger.error(f"检查告警状态失败: {response.status_code}")
                return {'error': f"HTTP {response.status_code}"}
                
        except Exception as e:
            logger.error(f"检查告警状态异常: {str(e)}")
            return {'error': str(e)}
    
    # ==================== 用户权限业务方法 ====================
    
    def test_admin_permissions(self) -> Dict[str, bool]:
        """测试管理员权限"""
        logger.info("测试管理员权限")
        
        permissions = {
            'user_management': False,
            'system_config': False,
            'data_export': False,
            'site_management': False,
            'kpi_config': False
        }
        
        try:
            # 测试用户管理权限
            self.driver.get(f"{self.base_url}/admin/users")
            self.wait_for_page_load()
            if "用户管理" in self.driver.title or self.driver.find_elements(By.XPATH, "//h1[contains(text(), '用户管理')]"):
                permissions['user_management'] = True
            
            # 测试系统配置权限
            self.driver.get(f"{self.base_url}/admin/config")
            self.wait_for_page_load()
            if "系统配置" in self.driver.title or self.driver.find_elements(By.XPATH, "//h1[contains(text(), '系统配置')]"):
                permissions['system_config'] = True
            
            # 测试数据导出权限
            self.driver.get(f"{self.base_url}/data-export")
            self.wait_for_page_load()
            export_btn = self.driver.find_elements(By.XPATH, "//button[contains(text(), '导出')]")
            if export_btn and export_btn[0].is_enabled():
                permissions['data_export'] = True
            
            # 测试站点管理权限
            self.driver.get(f"{self.base_url}/site-management")
            self.wait_for_page_load()
            create_btn = self.driver.find_elements(By.XPATH, "//button[contains(text(), '新建')]")
            if create_btn and create_btn[0].is_enabled():
                permissions['site_management'] = True
            
            # 测试KPI配置权限
            self.driver.get(f"{self.base_url}/kpi-monitoring/config")
            self.wait_for_page_load()
            config_btn = self.driver.find_elements(By.XPATH, "//button[contains(text(), '配置')]")
            if config_btn and config_btn[0].is_enabled():
                permissions['kpi_config'] = True
            
            logger.info(f"管理员权限测试结果: {permissions}")
            return permissions
            
        except Exception as e:
            logger.error(f"测试管理员权限失败: {str(e)}")
            return permissions
    
    def test_viewer_restrictions(self) -> Dict[str, bool]:
        """测试查看者权限限制"""
        logger.info("测试查看者权限限制")
        
        restrictions = {
            'cannot_create_site': False,
            'cannot_delete_data': False,
            'cannot_modify_config': False,
            'cannot_export_sensitive': False,
            'can_view_reports': False
        }
        
        try:
            # 测试不能创建站点
            self.driver.get(f"{self.base_url}/site-management")
            self.wait_for_page_load()
            create_btns = self.driver.find_elements(By.XPATH, "//button[contains(text(), '新建') or contains(text(), '创建')]")
            restrictions['cannot_create_site'] = len(create_btns) == 0 or not create_btns[0].is_enabled()
            
            # 测试不能删除数据
            delete_btns = self.driver.find_elements(By.XPATH, "//button[contains(text(), '删除')]")
            restrictions['cannot_delete_data'] = len(delete_btns) == 0 or not delete_btns[0].is_enabled()
            
            # 测试不能修改配置
            self.driver.get(f"{self.base_url}/admin/config")
            time.sleep(2)
            if "403" in self.driver.page_source or "权限不足" in self.driver.page_source:
                restrictions['cannot_modify_config'] = True
            
            # 测试不能导出敏感数据
            self.driver.get(f"{self.base_url}/data-export")
            self.wait_for_page_load()
            sensitive_export = self.driver.find_elements(By.XPATH, "//button[@data-type='sensitive']")
            restrictions['cannot_export_sensitive'] = len(sensitive_export) == 0 or not sensitive_export[0].is_enabled()
            
            # 测试可以查看报告
            self.driver.get(f"{self.base_url}/reports")
            self.wait_for_page_load()
            if "报告" in self.driver.title or self.driver.find_elements(By.XPATH, "//h1[contains(text(), '报告')]"):
                restrictions['can_view_reports'] = True
            
            logger.info(f"查看者权限限制测试结果: {restrictions}")
            return restrictions
            
        except Exception as e:
            logger.error(f"测试查看者权限限制失败: {str(e)}")
            return restrictions
    
    # ==================== 数据导出业务方法 ====================
    
    def export_data(self, data_type: str, export_format: str, filters: Dict[str, Any] = None) -> str:
        """导出数据"""
        logger.info(f"导出数据: {data_type}, 格式: {export_format}")
        
        try:
            # 导航到数据导出页面
            self.driver.get(f"{self.base_url}/data-export")
            self.wait_for_page_load()
            
            # 选择数据类型
            data_type_select = Select(self.wait_for_element(By.NAME, "dataType"))
            data_type_select.select_by_value(data_type)
            
            # 选择导出格式
            format_select = Select(self.wait_for_element(By.NAME, "exportFormat"))
            format_select.select_by_value(export_format)
            
            # 设置过滤条件
            if filters:
                for filter_name, filter_value in filters.items():
                    filter_input = self.driver.find_elements(By.NAME, filter_name)
                    if filter_input:
                        filter_input[0].clear()
                        filter_input[0].send_keys(str(filter_value))
            
            # 开始导出
            export_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '导出')]")
            export_btn.click()
            
            # 等待导出完成
            download_link = self.wait_for_element(
                By.XPATH, 
                "//a[contains(@href, 'download') or contains(text(), '下载')]",
                timeout=120
            )
            
            download_url = download_link.get_attribute('href')
            logger.info(f"数据导出成功，下载链接: {download_url}")
            return download_url
            
        except Exception as e:
            logger.error(f"数据导出失败: {str(e)}")
            return None
    
    def validate_export_format(self, file_path: str, expected_format: str) -> bool:
        """验证导出格式"""
        logger.info(f"验证导出格式: {file_path}, 期望格式: {expected_format}")
        
        try:
            file_extension = Path(file_path).suffix.lower()
            
            format_mapping = {
                'csv': '.csv',
                'excel': '.xlsx',
                'json': '.json',
                'pdf': '.pdf'
            }
            
            expected_extension = format_mapping.get(expected_format.lower())
            
            if file_extension == expected_extension:
                logger.info("导出格式验证通过")
                return True
            else:
                logger.error(f"导出格式不匹配: 期望 {expected_extension}, 实际 {file_extension}")
                return False
                
        except Exception as e:
            logger.error(f"验证导出格式失败: {str(e)}")
            return False
    
    def check_export_permissions(self, data_type: str, user_role: str) -> bool:
        """检查导出权限"""
        logger.info(f"检查导出权限: {data_type}, 用户角色: {user_role}")
        
        try:
            # 使用API检查权限
            response = self.session.get(
                f"{self.api_base_url}/export/permissions",
                params={'data_type': data_type, 'user_role': user_role}
            )
            
            if response.status_code == 200:
                result = response.json()
                has_permission = result.get('has_permission', False)
                logger.info(f"导出权限检查结果: {has_permission}")
                return has_permission
            else:
                logger.error(f"检查导出权限失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"检查导出权限异常: {str(e)}")
            return False
    
    # ==================== 高级业务方法 ====================
    
    def batch_import_data(self, file_list: List[Dict[str, str]], data_type: str) -> Dict[str, Any]:
        """批量导入数据"""
        logger.info(f"批量导入{data_type}数据，文件数量: {len(file_list)}")
        
        results = {
            'total_files': len(file_list),
            'successful': 0,
            'failed': 0,
            'errors': [],
            'import_ids': []
        }
        
        try:
            # 导航到批量导入页面
            self.driver.get(f"{self.base_url}/data-management/batch-import")
            self.wait_for_page_load()
            
            # 选择数据类型
            data_type_select = Select(self.wait_for_element(By.NAME, "dataType"))
            data_type_select.select_by_value(data_type)
            
            # 批量上传文件
            file_input = self.wait_for_element(By.XPATH, "//input[@type='file'][@multiple]")
            
            for file_info in file_list:
                file_path = file_info.get('path')
                if file_path and os.path.exists(file_path):
                    file_input.send_keys(file_path)
                    time.sleep(1)  # 等待文件加载
            
            # 开始批量导入
            import_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '开始导入')]")
            import_btn.click()
            
            # 等待导入完成并收集结果
            progress_bar = self.wait_for_element(By.CLASS_NAME, "progress-bar", timeout=300)
            
            # 等待进度条完成
            while True:
                progress_value = progress_bar.get_attribute('aria-valuenow')
                if progress_value and int(progress_value) >= 100:
                    break
                time.sleep(5)
            
            # 获取导入结果
            result_table = self.wait_for_element(By.CLASS_NAME, "import-results-table")
            rows = result_table.find_elements(By.TAG_NAME, "tr")[1:]  # 跳过表头
            
            for row in rows:
                cells = row.find_elements(By.TAG_NAME, "td")
                if len(cells) >= 3:
                    file_name = cells[0].text
                    status = cells[1].text
                    import_id = cells[2].text if len(cells) > 2 else None
                    
                    if status == "成功":
                        results['successful'] += 1
                        if import_id:
                            results['import_ids'].append(import_id)
                    else:
                        results['failed'] += 1
                        error_msg = cells[3].text if len(cells) > 3 else "未知错误"
                        results['errors'].append(f"{file_name}: {error_msg}")
            
            logger.info(f"批量导入完成: {results}")
            return results
            
        except Exception as e:
            logger.error(f"批量导入数据失败: {str(e)}")
            results['errors'].append(str(e))
            return results
    
    def advanced_data_analysis(self, analysis_type: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """高级数据分析"""
        logger.info(f"执行高级数据分析: {analysis_type}")
        
        try:
            # 导航到高级分析页面
            self.driver.get(f"{self.base_url}/analysis/advanced")
            self.wait_for_page_load()
            
            # 选择分析类型
            analysis_select = Select(self.wait_for_element(By.NAME, "analysisType"))
            analysis_select.select_by_value(analysis_type)
            
            # 设置分析参数
            for param_name, param_value in parameters.items():
                param_input = self.driver.find_elements(By.NAME, param_name)
                if param_input:
                    if param_input[0].tag_name == 'select':
                        Select(param_input[0]).select_by_value(str(param_value))
                    else:
                        param_input[0].clear()
                        param_input[0].send_keys(str(param_value))
            
            # 开始分析
            analyze_btn = self.wait_for_clickable(By.XPATH, "//button[contains(text(), '开始分析')]")
            analyze_btn.click()
            
            # 等待分析完成
            result_container = self.wait_for_element(
                By.CLASS_NAME, 
                "analysis-results",
                timeout=600  # 10分钟超时
            )
            
            # 提取分析结果
            results = {
                'analysis_type': analysis_type,
                'status': 'completed',
                'results': {},
                'charts': [],
                'reports': []
            }
            
            # 获取数值结果
            metric_elements = result_container.find_elements(By.CLASS_NAME, "metric-value")
            for element in metric_elements:
                metric_name = element.get_attribute('data-metric')
                metric_value = element.text
                if metric_name:
                    results['results'][metric_name] = metric_value
            
            # 获取图表链接
            chart_elements = result_container.find_elements(By.CLASS_NAME, "chart-link")
            for element in chart_elements:
                chart_url = element.get_attribute('href')
                chart_title = element.text
                results['charts'].append({
                    'title': chart_title,
                    'url': chart_url
                })
            
            # 获取报告链接
            report_elements = result_container.find_elements(By.CLASS_NAME, "report-download")
            for element in report_elements:
                report_url = element.get_attribute('href')
                report_name = element.text
                results['reports'].append({
                    'name': report_name,
                    'url': report_url
                })
            
            logger.info(f"高级数据分析完成: {analysis_type}")
            return results
            
        except Exception as e:
            logger.error(f"高级数据分析失败: {str(e)}")
            return {
                'analysis_type': analysis_type,
                'status': 'failed',
                'error': str(e)
            }
    
    def performance_benchmark_test(self, test_scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """性能基准测试"""
        logger.info(f"执行性能基准测试，场景数量: {len(test_scenarios)}")
        
        benchmark_results = {
            'total_scenarios': len(test_scenarios),
            'completed': 0,
            'failed': 0,
            'results': [],
            'summary': {}
        }
        
        try:
            for scenario in test_scenarios:
                scenario_name = scenario.get('name', 'Unknown')
                logger.info(f"执行性能测试场景: {scenario_name}")
                
                try:
                    # 记录开始时间
                    start_time = time.time()
                    
                    # 根据场景类型执行不同的测试
                    scenario_type = scenario.get('type')
                    
                    if scenario_type == 'data_import':
                        result = self._benchmark_data_import(scenario)
                    elif scenario_type == 'query_performance':
                        result = self._benchmark_query_performance(scenario)
                    elif scenario_type == 'concurrent_users':
                        result = self._benchmark_concurrent_users(scenario)
                    elif scenario_type == 'large_dataset':
                        result = self._benchmark_large_dataset(scenario)
                    else:
                        result = {'error': f'Unknown scenario type: {scenario_type}'}
                    
                    # 记录结束时间
                    end_time = time.time()
                    execution_time = end_time - start_time
                    
                    result.update({
                        'scenario_name': scenario_name,
                        'execution_time': execution_time,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    benchmark_results['results'].append(result)
                    benchmark_results['completed'] += 1
                    
                except Exception as scenario_error:
                    logger.error(f"场景 {scenario_name} 执行失败: {str(scenario_error)}")
                    benchmark_results['failed'] += 1
                    benchmark_results['results'].append({
                        'scenario_name': scenario_name,
                        'error': str(scenario_error),
                        'timestamp': datetime.now().isoformat()
                    })
            
            # 生成性能摘要
            benchmark_results['summary'] = self._generate_performance_summary(
                benchmark_results['results']
            )
            
            logger.info(f"性能基准测试完成: {benchmark_results['summary']}")
            return benchmark_results
            
        except Exception as e:
            logger.error(f"性能基准测试失败: {str(e)}")
            benchmark_results['error'] = str(e)
            return benchmark_results
    
    def _benchmark_data_import(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """数据导入性能测试"""
        file_size = scenario.get('file_size', '1MB')
        data_type = scenario.get('data_type', 'cdr')
        
        # 生成测试数据文件
        test_file = self._generate_test_file(file_size, data_type)
        
        # 执行导入并测量性能
        start_time = time.time()
        
        if data_type == 'cdr':
            import_result = self.import_cdr_data(test_file)
        elif data_type == 'ep':
            import_result = self.import_ep_data(test_file)
        else:
            import_result = None
        
        end_time = time.time()
        import_time = end_time - start_time
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return {
            'type': 'data_import',
            'file_size': file_size,
            'data_type': data_type,
            'import_time': import_time,
            'success': import_result is not None,
            'throughput': self._calculate_throughput(file_size, import_time)
        }
    
    def _benchmark_query_performance(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """查询性能测试"""
        query_type = scenario.get('query_type', 'simple')
        data_size = scenario.get('data_size', 'medium')
        
        # 执行查询性能测试
        start_time = time.time()
        
        try:
            # 使用API执行查询
            response = self.session.get(
                f"{self.api_base_url}/performance/query",
                params={
                    'type': query_type,
                    'size': data_size
                }
            )
            
            end_time = time.time()
            query_time = end_time - start_time
            
            if response.status_code == 200:
                result_data = response.json()
                return {
                    'type': 'query_performance',
                    'query_type': query_type,
                    'data_size': data_size,
                    'query_time': query_time,
                    'result_count': result_data.get('count', 0),
                    'success': True
                }
            else:
                return {
                    'type': 'query_performance',
                    'query_type': query_type,
                    'data_size': data_size,
                    'query_time': query_time,
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                }
                
        except Exception as e:
            end_time = time.time()
            query_time = end_time - start_time
            
            return {
                'type': 'query_performance',
                'query_type': query_type,
                'data_size': data_size,
                'query_time': query_time,
                'success': False,
                'error': str(e)
            }
    
    def _benchmark_concurrent_users(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """并发用户性能测试"""
        user_count = scenario.get('user_count', 5)
        duration = scenario.get('duration', 60)  # 秒
        
        # 模拟并发用户访问
        start_time = time.time()
        
        # 使用多个浏览器实例模拟并发
        concurrent_results = []
        
        for i in range(min(user_count, 3)):  # 限制实际浏览器实例数量
            try:
                # 创建新的浏览器实例
                from selenium import webdriver
                from selenium.webdriver.chrome.options import Options
                
                options = Options()
                options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                options.add_argument('--disable-dev-shm-usage')
                
                concurrent_driver = webdriver.Chrome(options=options)
                
                # 执行并发操作
                concurrent_driver.get(f"{self.base_url}/dashboard")
                time.sleep(2)
                
                # 执行一些基本操作
                page_load_time = concurrent_driver.execute_script(
                    "return window.performance.timing.loadEventEnd - window.performance.timing.navigationStart"
                )
                
                concurrent_results.append({
                    'user_id': i + 1,
                    'page_load_time': page_load_time,
                    'success': True
                })
                
                concurrent_driver.quit()
                
            except Exception as e:
                concurrent_results.append({
                    'user_id': i + 1,
                    'success': False,
                    'error': str(e)
                })
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 计算平均响应时间
        successful_results = [r for r in concurrent_results if r.get('success')]
        avg_response_time = 0
        if successful_results:
            avg_response_time = sum(r.get('page_load_time', 0) for r in successful_results) / len(successful_results)
        
        return {
            'type': 'concurrent_users',
            'user_count': user_count,
            'duration': duration,
            'total_time': total_time,
            'successful_users': len(successful_results),
            'failed_users': len(concurrent_results) - len(successful_results),
            'avg_response_time': avg_response_time,
            'results': concurrent_results
        }
    
    def _benchmark_large_dataset(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """大数据集性能测试"""
        dataset_size = scenario.get('dataset_size', '1M')  # 1M records
        operation = scenario.get('operation', 'query')
        
        start_time = time.time()
        
        try:
            # 使用API测试大数据集操作
            response = self.session.post(
                f"{self.api_base_url}/performance/large-dataset",
                json={
                    'size': dataset_size,
                    'operation': operation
                }
            )
            
            end_time = time.time()
            operation_time = end_time - start_time
            
            if response.status_code == 200:
                result_data = response.json()
                return {
                    'type': 'large_dataset',
                    'dataset_size': dataset_size,
                    'operation': operation,
                    'operation_time': operation_time,
                    'memory_usage': result_data.get('memory_usage'),
                    'cpu_usage': result_data.get('cpu_usage'),
                    'success': True
                }
            else:
                return {
                    'type': 'large_dataset',
                    'dataset_size': dataset_size,
                    'operation': operation,
                    'operation_time': operation_time,
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                }
                
        except Exception as e:
            end_time = time.time()
            operation_time = end_time - start_time
            
            return {
                'type': 'large_dataset',
                'dataset_size': dataset_size,
                'operation': operation,
                'operation_time': operation_time,
                'success': False,
                'error': str(e)
            }
    
    def _generate_test_file(self, file_size: str, data_type: str) -> str:
        """生成测试文件"""
        # 解析文件大小
        size_map = {
            '1KB': 1024,
            '1MB': 1024 * 1024,
            '10MB': 10 * 1024 * 1024,
            '100MB': 100 * 1024 * 1024
        }
        
        target_size = size_map.get(file_size, 1024 * 1024)
        
        # 生成临时文件
        import tempfile
        import csv
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        
        if data_type == 'cdr':
            # 生成CDR测试数据
            headers = ['call_id', 'caller', 'callee', 'start_time', 'duration', 'cell_id']
            writer = csv.writer(temp_file)
            writer.writerow(headers)
            
            current_size = temp_file.tell()
            row_count = 0
            
            while current_size < target_size:
                row = [
                    f'call_{row_count:06d}',
                    f'1{row_count % 10000000000:010d}',
                    f'1{(row_count + 1) % 10000000000:010d}',
                    f'2024-01-01 {row_count % 24:02d}:{row_count % 60:02d}:{row_count % 60:02d}',
                    row_count % 3600,
                    f'cell_{row_count % 1000:03d}'
                ]
                writer.writerow(row)
                row_count += 1
                current_size = temp_file.tell()
        
        elif data_type == 'ep':
            # 生成EP测试数据
            headers = ['timestamp', 'longitude', 'latitude', 'rsrp', 'rsrq', 'sinr']
            writer = csv.writer(temp_file)
            writer.writerow(headers)
            
            current_size = temp_file.tell()
            row_count = 0
            
            while current_size < target_size:
                row = [
                    f'2024-01-01 {row_count % 24:02d}:{row_count % 60:02d}:{row_count % 60:02d}',
                    116.3 + (row_count % 1000) * 0.001,
                    39.9 + (row_count % 1000) * 0.001,
                    -80 - (row_count % 40),
                    -10 - (row_count % 20),
                    10 + (row_count % 30)
                ]
                writer.writerow(row)
                row_count += 1
                current_size = temp_file.tell()
        
        temp_file.close()
        return temp_file.name
    
    def _calculate_throughput(self, file_size: str, time_taken: float) -> float:
        """计算吞吐量"""
        size_map = {
            '1KB': 1,
            '1MB': 1024,
            '10MB': 10 * 1024,
            '100MB': 100 * 1024
        }
        
        size_kb = size_map.get(file_size, 1024)
        return size_kb / time_taken if time_taken > 0 else 0
    
    def _generate_performance_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成性能测试摘要"""
        summary = {
            'total_tests': len(results),
            'successful_tests': 0,
            'failed_tests': 0,
            'avg_execution_time': 0,
            'performance_metrics': {}
        }
        
        successful_results = [r for r in results if not r.get('error')]
        summary['successful_tests'] = len(successful_results)
        summary['failed_tests'] = len(results) - len(successful_results)
        
        if successful_results:
            execution_times = [r.get('execution_time', 0) for r in successful_results]
            summary['avg_execution_time'] = sum(execution_times) / len(execution_times)
            
            # 按测试类型分组统计
            by_type = {}
            for result in successful_results:
                test_type = result.get('type', 'unknown')
                if test_type not in by_type:
                    by_type[test_type] = []
                by_type[test_type].append(result)
            
            for test_type, type_results in by_type.items():
                type_times = [r.get('execution_time', 0) for r in type_results]
                summary['performance_metrics'][test_type] = {
                    'count': len(type_results),
                    'avg_time': sum(type_times) / len(type_times),
                    'min_time': min(type_times),
                    'max_time': max(type_times)
                }
        
        return summary