"""Tests for performance monitoring and decorators.

This module contains comprehensive tests for the MetricsCollector,
performance decorators, and general-purpose decorators.
"""

import asyncio
import time
from typing import Any, Dict
from unittest.mock import AsyncMock, Mock, patch

import pytest

from src.database.exceptions import ConnectionError, TimeoutError
from src.database.monitoring.metrics import (
    MetricsCollector,
    MetricsSummary,
    PoolMetrics,
    QueryMetrics,
    get_metrics_collector,
)
from src.database.utils.decorators import (
    cache_result,
    deprecated,
    log_calls,
    rate_limit,
    retry,
    singleton,
    timeout,
    validate_args,
)
from src.database.utils.performance import (
    PerformanceMonitor,
    PerformanceTimer,
    time_execution,
)


class _TestMetricsCollector:
    """Test cases for MetricsCollector class."""

    def test_metrics_collector_initialization(self):
        """Test MetricsCollector initialization."""
        collector = MetricsCollector()

        assert len(collector.query_metrics) == 0
        assert len(collector.pool_metrics) == 0
        assert collector._collection_task is None

    def test_record_query_execution(self):
        """Test recording query execution metrics."""
        collector = MetricsCollector()

        collector.record_query_execution(
            query="SELECT * FROM users",
            execution_time=0.5,
            rows_affected=10,
            success=True,
        )

        assert len(collector.query_metrics) == 1
        metric = collector.query_metrics[0]
        assert metric.query_hash is not None  # query_hash is generated from query
        assert metric.execution_time == 0.5
        assert metric.rows_affected == 10
        assert metric.success is True
        assert metric.timestamp is not None

    def test_collect_pool_metrics(self):
        """Test collecting pool metrics."""
        # Mock pool manager
        mock_pool_manager = Mock()
        mock_pool_manager.is_initialized = Mock(return_value=True)
        mock_pool_manager.get_pool_stats = Mock(
            return_value={
                "pool_size": 10,
                "pool_active": 3,
                "pool_idle": 7,
                "pool_acquisitions": 100,
                "pool_releases": 98,
                "total_connections_created": 10,
                "total_connections_closed": 0,
            }
        )

        collector = MetricsCollector(pool_manager=mock_pool_manager)

        # Collect metrics
        result = collector.collect_pool_metrics()

        assert result is not None
        assert len(collector.pool_metrics) == 1
        metric = collector.pool_metrics[0]
        assert metric.pool_size == 10
        assert metric.active_connections == 3
        assert metric.idle_connections == 7
        assert metric.timestamp is not None

    def test_get_metrics_summary(self):
        """Test getting metrics summary."""
        # Mock pool manager
        mock_pool_manager = Mock()
        mock_pool_manager.is_initialized = Mock(return_value=True)
        mock_pool_manager.get_pool_stats = Mock(
            return_value={
                "pool_size": 10,
                "pool_active": 3,
                "pool_idle": 7,
                "pool_acquisitions": 100,
                "pool_releases": 98,
                "total_connections_created": 10,
                "total_connections_closed": 0,
            }
        )

        collector = MetricsCollector(pool_manager=mock_pool_manager)

        # Add some test data
        collector.record_query_execution("SELECT 1", 0.1, 1, True)
        collector.record_query_execution("SELECT 2", 0.2, 2, True)
        collector.record_query_execution("SELECT 3", 0.3, 0, False)

        collector.collect_pool_metrics()

        summary = collector.get_metrics_summary()

        assert summary.total_queries == 3
        assert summary.successful_queries == 2
        assert summary.failed_queries == 1
        assert (
            abs(summary.avg_execution_time - 0.15) < 0.01
        )  # Average of successful queries (0.1, 0.2)
        assert summary.current_pool_metrics is not None

    @pytest.mark.asyncio
    async def test_start_stop_collection(self):
        """Test starting and stopping automatic collection."""
        # Mock pool manager
        mock_pool_manager = Mock()
        mock_pool_manager.is_initialized = Mock(return_value=True)
        mock_pool_manager.get_pool_stats = Mock(
            return_value={
                "pool_size": 10,
                "pool_active": 2,
                "pool_idle": 8,
                "pool_acquisitions": 100,
                "pool_releases": 98,
                "total_connections_created": 10,
                "total_connections_closed": 0,
            }
        )

        collector = MetricsCollector(pool_manager=mock_pool_manager)

        # Start collection
        await collector.start_collection(interval=0.1)

        assert collector._collection_task is not None
        assert not collector._collection_task.done()

        # Wait a bit for collection to happen
        await asyncio.sleep(0.15)

        # Stop collection
        await collector.stop_collection()

        # After stop_collection, the task should be None
        assert collector._collection_task is None
        assert len(collector.pool_metrics) > 0

    def test_get_metrics_collector_singleton(self):
        """Test that get_metrics_collector returns singleton."""
        collector1 = get_metrics_collector()
        collector2 = get_metrics_collector()

        assert collector1 is collector2


class _TestPerformanceDecorator:
    """Test cases for @time_execution decorator."""

    def test_sync_function_timing(self):
        """Test timing synchronous functions."""

        @time_execution
        def slow_function():
            time.sleep(0.1)
            return "result"

        with patch("src.database.utils.performance.logger") as mock_logger:
            result = slow_function()

            assert result == "result"
            mock_logger.info.assert_called()
            call_args = mock_logger.info.call_args[0][0]
            assert "slow_function" in call_args
            assert "executed in" in call_args

    @pytest.mark.asyncio
    async def test_async_function_timing(self):
        """Test timing asynchronous functions."""

        @time_execution
        async def async_slow_function():
            await asyncio.sleep(0.1)
            return "async_result"

        with patch("src.database.utils.performance.logger") as mock_logger:
            result = await async_slow_function()

            assert result == "async_result"
            mock_logger.info.assert_called()
            call_args = mock_logger.info.call_args[0][0]
            assert "async_slow_function" in call_args
            assert "executed in" in call_args

    def test_performance_timer_context_manager(self):
        """Test PerformanceTimer context manager."""
        with PerformanceTimer("test_operation") as timer:
            time.sleep(0.05)

        assert timer.elapsed_time > 0.04
        assert timer.elapsed_time < 0.1

    def test_performance_monitor(self):
        """Test PerformanceMonitor class."""
        monitor = PerformanceMonitor()

        monitor.start_operation("op1")
        time.sleep(0.05)
        monitor.end_operation("op1")

        monitor.start_operation("op2")
        time.sleep(0.03)
        monitor.end_operation("op2")

        stats = monitor.get_stats()

        assert "op1" in stats
        assert "op2" in stats
        assert stats["op1"]["count"] == 1
        assert stats["op1"]["total_time"] > 0.04
        assert stats["op2"]["total_time"] > 0.02


class _TestRetryDecorator:
    """Test cases for @retry decorator."""

    def test_sync_retry_success_on_first_attempt(self):
        """Test retry decorator with immediate success."""
        call_count = 0

        @retry(max_attempts=3)
        def successful_function():
            nonlocal call_count
            call_count += 1
            return "success"

        result = successful_function()

        assert result == "success"
        assert call_count == 1

    def test_sync_retry_success_after_failures(self):
        """Test retry decorator with success after failures."""
        call_count = 0

        @retry(max_attempts=3, delay=0.01, exceptions=(ValueError,))
        def flaky_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ValueError("Temporary failure")
            return "success"

        result = flaky_function()

        assert result == "success"
        assert call_count == 3

    def test_sync_retry_max_attempts_exceeded(self):
        """Test retry decorator when max attempts exceeded."""
        call_count = 0

        @retry(max_attempts=2, delay=0.01, exceptions=(ValueError,))
        def always_failing_function():
            nonlocal call_count
            call_count += 1
            raise ValueError("Always fails")

        with pytest.raises(ValueError, match="Always fails"):
            always_failing_function()

        assert call_count == 2

    @pytest.mark.asyncio
    async def test_async_retry_success(self):
        """Test retry decorator with async function."""
        call_count = 0

        @retry(max_attempts=3, delay=0.01, exceptions=(ConnectionError,))
        async def async_flaky_function():
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise ConnectionError("Connection failed")
            return "async_success"

        result = await async_flaky_function()

        assert result == "async_success"
        assert call_count == 2

    def test_retry_with_callback(self):
        """Test retry decorator with retry callback."""
        retry_calls = []

        def on_retry_callback(attempt, exception):
            retry_calls.append((attempt, str(exception)))

        @retry(
            max_attempts=3,
            delay=0.01,
            on_retry=on_retry_callback,
            exceptions=(ValueError,),
        )
        def failing_function():
            if len(retry_calls) < 1:
                raise ValueError("First failure")
            return "success"

        result = failing_function()

        assert result == "success"
        assert len(retry_calls) == 1
        assert retry_calls[0][0] == 1
        assert "First failure" in retry_calls[0][1]


class _TestTimeoutDecorator:
    """Test cases for @timeout decorator."""

    @pytest.mark.asyncio
    async def test_timeout_success(self):
        """Test timeout decorator with fast function."""

        @timeout(1.0)
        async def fast_function():
            await asyncio.sleep(0.1)
            return "completed"

        result = await fast_function()
        assert result == "completed"

    @pytest.mark.asyncio
    async def test_timeout_exceeded(self):
        """Test timeout decorator with slow function."""

        @timeout(0.1)
        async def slow_function():
            await asyncio.sleep(0.5)
            return "should_not_complete"

        with pytest.raises(TimeoutError, match="timed out after 0.1s"):
            await slow_function()

    def test_timeout_sync_function_error(self):
        """Test timeout decorator raises error for sync functions."""
        with pytest.raises(ValueError, match="can only be applied to async functions"):

            @timeout(1.0)
            def sync_function():
                pass


class _TestValidateArgsDecorator:
    """Test cases for @validate_args decorator."""

    def test_validation_success(self):
        """Test argument validation with valid inputs."""

        @validate_args(lambda x: x > 0, lambda y: isinstance(y, str))
        def validated_function(x, y):
            return f"{x}: {y}"

        result = validated_function(5, "test")
        assert result == "5: test"

    def test_validation_failure(self):
        """Test argument validation with invalid inputs."""

        @validate_args(lambda x: x > 0, lambda y: isinstance(y, str))
        def validated_function(x, y):
            return f"{x}: {y}"

        with pytest.raises(ValueError, match="Validation failed for argument 0"):
            validated_function(-1, "test")

        with pytest.raises(ValueError, match="Validation failed for argument 1"):
            validated_function(5, 123)


class _TestCacheResultDecorator:
    """Test cases for @cache_result decorator."""

    def test_cache_hit(self):
        """Test cache hit scenario."""
        call_count = 0

        @cache_result(ttl=1.0, maxsize=10)
        def expensive_function(x):
            nonlocal call_count
            call_count += 1
            return x * 2

        # First call
        result1 = expensive_function(5)
        assert result1 == 10
        assert call_count == 1

        # Second call (should hit cache)
        result2 = expensive_function(5)
        assert result2 == 10
        assert call_count == 1  # Should not increment

    def test_cache_expiration(self):
        """Test cache expiration."""
        call_count = 0

        @cache_result(ttl=0.1, maxsize=10)
        def expensive_function(x):
            nonlocal call_count
            call_count += 1
            return x * 2

        # First call
        result1 = expensive_function(5)
        assert result1 == 10
        assert call_count == 1

        # Wait for expiration
        time.sleep(0.15)

        # Second call (should miss cache due to expiration)
        result2 = expensive_function(5)
        assert result2 == 10
        assert call_count == 2

    def test_cache_management(self):
        """Test cache management methods."""

        @cache_result(maxsize=2)
        def cached_function(x):
            return x * 2

        cached_function(1)
        cached_function(2)

        info = cached_function.cache_info()
        assert info["size"] == 2
        assert info["maxsize"] == 2

        cached_function.cache_clear()
        info = cached_function.cache_info()
        assert info["size"] == 0


class _TestSingletonDecorator:
    """Test cases for @singleton decorator."""

    def test_singleton_behavior(self):
        """Test singleton decorator creates only one instance."""

        @singleton
        class DemoClass:
            def __init__(self, value=None):
                self.value = value

        instance1 = DemoClass("first")
        instance2 = DemoClass("second")

        assert instance1 is instance2
        assert instance1.value == "first"  # First initialization wins


class _TestRateLimitDecorator:
    """Test cases for @rate_limit decorator."""

    def test_sync_rate_limiting(self):
        """Test rate limiting for sync functions."""
        call_times = []

        @rate_limit(calls_per_second=10.0)  # Max 10 calls per second
        def rate_limited_function():
            call_times.append(time.time())
            return "called"

        # Make multiple calls
        for _ in range(3):
            rate_limited_function()

        # Check that calls were spaced appropriately
        assert len(call_times) == 3
        if len(call_times) >= 2:
            time_diff = call_times[1] - call_times[0]
            assert (
                time_diff >= 0.09
            )  # Should be at least 0.1s apart (with some tolerance)

    @pytest.mark.asyncio
    async def test_async_rate_limiting(self):
        """Test rate limiting for async functions."""
        call_times = []

        @rate_limit(calls_per_second=10.0)
        async def async_rate_limited_function():
            call_times.append(time.time())
            return "async_called"

        # Make multiple calls
        for _ in range(3):
            await async_rate_limited_function()

        # Check that calls were spaced appropriately
        assert len(call_times) == 3
        if len(call_times) >= 2:
            time_diff = call_times[1] - call_times[0]
            assert (
                time_diff >= 0.09
            )  # Should be at least 0.1s apart (with some tolerance)


class _TestLogCallsDecorator:
    """Test cases for @log_calls decorator."""

    def test_log_calls_basic(self):
        """Test basic call logging."""

        @log_calls(include_args=True, include_result=True)
        def logged_function(x, y=None):
            return x + (y or 0)

        with patch("src.database.utils.decorators.logger") as mock_logger:
            result = logged_function(5, y=3)

            assert result == 8
            assert mock_logger.log.call_count == 2  # Start and end calls


class _TestDeprecatedDecorator:
    """Test cases for @deprecated decorator."""

    def test_deprecated_warning(self):
        """Test deprecated decorator logs warning."""

        @deprecated(reason="Use new_function instead", version="2.0")
        def old_function():
            return "old_result"

        with patch("src.database.utils.decorators.logger") as mock_logger:
            result = old_function()

            assert result == "old_result"
            mock_logger.warning.assert_called_once()
            warning_msg = mock_logger.warning.call_args[0][0]
            assert "deprecated" in warning_msg
            assert "2.0" in warning_msg
            assert "Use new_function instead" in warning_msg


if __name__ == "__main__":
    pytest.main([__file__])
