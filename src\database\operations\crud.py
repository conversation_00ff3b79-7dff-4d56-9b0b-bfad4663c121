"""Database CRUD operations module.

This module provides comprehensive CRUD (Create, Read, Update, Delete) operations
for database interactions with support for multiple database backends.
"""

import logging
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime
import pandas as pd
from sqlalchemy import text, select, insert, update, delete, Column, Integer, String, MetaData, Table
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from sqlalchemy.engine import Result

from ..exceptions import DatabaseError, ValidationError, TableNotFoundError
from ..types import QueryResult, BulkOperationResult
from ..utils.query_optimizer import QueryOptimizer
from ..utils.performance import PerformanceMonitor
from ..utils.security import SQLInjectionGuard

logger = logging.getLogger(__name__)


class CRUDOperations:
    """Comprehensive CRUD operations for database interactions.
    
    This class provides a high-level interface for performing CRUD operations
    with built-in error handling, logging, and performance monitoring.
    """
    
    def __init__(self, session_manager, performance_monitor: Optional[PerformanceMonitor] = None):
        """Initialize CRUD operations.
        
        Args:
            session_manager: Database session manager
            performance_monitor: Optional performance monitoring instance
        """
        self.session_manager = session_manager
        self.performance_monitor = performance_monitor or PerformanceMonitor()
        self.query_optimizer = QueryOptimizer()
    
    @property
    def pool(self):
        """Backward compatibility property for accessing session manager."""
        return self.session_manager
        
    async def _get_table_metadata(self, table_name: str, schema_name: str = "public") -> Table:
        """Get table metadata including columns and their types.
        
        Args:
            table_name: Name of the table
            schema_name: Schema name (default: public)
            
        Returns:
            SQLAlchemy Table object with metadata
            
        Raises:
            ValidationError: If table/schema names are invalid
            DatabaseError: If table doesn't exist or metadata retrieval fails
        """
        # Validate identifiers
        if not SQLInjectionGuard.validate_identifier(table_name):
            raise ValidationError(f"Invalid table name: {table_name}")
        if not SQLInjectionGuard.validate_identifier(schema_name):
            raise ValidationError(f"Invalid schema name: {schema_name}")
            
        async with self.session_manager as connection:
            # Check if table exists
            exists_query = """
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = $1 AND table_name = $2
                )
            """
            table_exists = await connection.fetchval(exists_query, schema_name, table_name)
            
            if not table_exists:
                raise DatabaseError(f"Table {schema_name}.{table_name} does not exist")
            
            # Get column information
            columns_query = """
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = $1 AND table_name = $2
                ORDER BY ordinal_position
            """
            
            columns_data = await connection.fetch(columns_query, schema_name, table_name)
            
            if not columns_data:
                raise DatabaseError(f"No columns found for table {schema_name}.{table_name}")
            
            # Create SQLAlchemy Table object
            metadata = MetaData()
            columns = []
            
            for col_data in columns_data:
                col_name = col_data['column_name']
                data_type = col_data['data_type']
                is_nullable = col_data['is_nullable'] == 'YES'
                
                # Map PostgreSQL types to SQLAlchemy types (simplified)
                if 'integer' in data_type:
                    col_type = Integer
                else:
                    col_type = String
                    
                columns.append(Column(col_name, col_type, nullable=is_nullable))
            
            return Table(table_name, metadata, *columns, schema=schema_name)
    
    async def create(self, table: str, data: Dict[str, Any], schema_name: Optional[str] = "public") -> Dict[str, Any]:
        """Create a new record in the specified table.
        
        Args:
            table: Name of the table
            data: Dictionary containing the data to insert
            schema_name: Optional schema name
            
        Returns:
            Dictionary containing the created record with any generated fields
            
        Raises:
            DatabaseError: If the creation operation fails
            ValidationError: If the data validation fails
        """
        try:
            # Validate input data
            if data is None:
                raise ValidationError("Data cannot be None")

            # Check if data is empty based on its type
            if isinstance(data, (list, tuple, dict)):
                if len(data) == 0:
                    raise ValidationError("Data cannot be empty")
            elif hasattr(data, 'empty'):  # DataFrame or Series
                if data.empty:
                    raise ValidationError("Data cannot be empty")
            elif hasattr(data, '__len__'):  # Other iterable types
                if len(data) == 0:
                    raise ValidationError("Data cannot be empty")
            
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table):
                raise ValidationError(f"Invalid table name: {table}")
            if schema_name and not SQLInjectionGuard.validate_identifier(schema_name):
                raise ValidationError(f"Invalid schema name: {schema_name}")
                
            # Get table metadata to validate columns
            table_metadata = await self._get_table_metadata(table, schema_name)
            
            # Validate column names
            table_columns = {col.name for col in table_metadata.columns}
            for column in data.keys():
                if not SQLInjectionGuard.validate_identifier(column):
                    raise ValidationError(f"Invalid column name: {column}")
                if column not in table_columns:
                    raise ValidationError(f"Column '{column}' does not exist in table '{table}'")
                
            # Build table reference
            table_ref = f"{schema_name}.{table}" if schema_name else table
            
            # Prepare insert statement
            columns = ", ".join(data.keys())
            placeholders = ", ".join([f"${i+1}" for i in range(len(data))])
            query = f"INSERT INTO {table_ref} ({columns}) VALUES ({placeholders}) RETURNING *"
            
            # Execute query with performance monitoring
            async with self.session_manager as connection:
                with self.performance_monitor.measure_query_time(query):
                    result = await connection.fetchrow(query, *data.values())
                    
                if result:
                    return dict(result)
                else:
                    raise DatabaseError("Failed to retrieve created record")
                
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during create operation: {e}")
            raise DatabaseError(f"Unexpected error: {e}") from e
            
    async def read(self, table_name: str, filters: Optional[Dict[str, Any]] = None, 
                  schema: Optional[str] = None, limit: Optional[int] = None,
                  offset: Optional[int] = None, order_by: Optional[str] = None) -> List[Dict[str, Any]]:
        """Read records from the specified table.
        
        Args:
            table_name: Name of the table
            filters: Optional dictionary of filter conditions
            schema: Optional schema name
            limit: Optional limit for number of records
            offset: Optional offset for pagination
            order_by: Optional column name for ordering
            
        Returns:
            List of dictionaries containing the matching records
            
        Raises:
            DatabaseError: If the read operation fails
        """
        try:
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table_name):
                raise ValidationError(f"Invalid table name: {table_name}")
            if schema and not SQLInjectionGuard.validate_identifier(schema):
                raise ValidationError(f"Invalid schema name: {schema}")
            
            # Validate limit and offset
            if limit is not None or offset is not None:
                SQLInjectionGuard.validate_limit_offset(limit, offset)
            
            # Build table reference
            table_ref = f"{schema}.{table_name}" if schema else table_name
            
            # Build base query
            query = f"SELECT * FROM {table_ref}"
            params = []
            
            # Add filters
            if filters:
                conditions = []
                param_index = 1
                for key, value in filters.items():
                    if not SQLInjectionGuard.validate_identifier(key):
                        raise ValidationError(f"Invalid column name: {key}")
                    conditions.append(f"{key} = ${param_index}")
                    params.append(value)
                    param_index += 1
                query += f" WHERE {' AND '.join(conditions)}"
                
            # Add ordering
            if order_by:
                if not SQLInjectionGuard.validate_identifier(order_by):
                    raise ValidationError(f"Invalid order by column: {order_by}")
                query += f" ORDER BY {order_by}"
                
            # Add pagination
            if limit:
                query += f" LIMIT {limit}"
            if offset:
                query += f" OFFSET {offset}"
                
            # Execute query with performance monitoring
            async with self.session_manager as connection:
                with self.performance_monitor.measure_query_time(query):
                    result = await connection.fetch(query, *params)
                    
            # Return results
            return [dict(row) for row in result]
            
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during read operation: {e}")
            raise DatabaseError(f"Unexpected error: {e}") from e
            
    async def update(self, table_name: str, data: Dict[str, Any], 
                    filters: Dict[str, Any], schema: Optional[str] = None) -> List[Dict[str, Any]]:
        """Update records in the specified table.
        
        Args:
            table_name: Name of the table
            data: Dictionary containing the data to update
            filters: Dictionary of filter conditions for records to update
            schema: Optional schema name
            
        Returns:
            List of updated records
            
        Raises:
            DatabaseError: If the update operation fails
            ValidationError: If the data validation fails
        """
        try:
            # Validate input
            if data is None:
                raise ValidationError("Update data cannot be None")

            # Check if data is empty based on its type
            if isinstance(data, (list, tuple, dict)):
                if len(data) == 0:
                    raise ValidationError("Update data cannot be empty")
            elif hasattr(data, 'empty'):  # DataFrame or Series
                if data.empty:
                    raise ValidationError("Update data cannot be empty")
            elif hasattr(data, '__len__'):  # Other iterable types
                if len(data) == 0:
                    raise ValidationError("Update data cannot be empty")
            if not filters:
                raise ValidationError("Filters cannot be empty for update operation")
            
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table_name):
                raise ValidationError(f"Invalid table name: {table_name}")
            if schema and not SQLInjectionGuard.validate_identifier(schema):
                raise ValidationError(f"Invalid schema name: {schema}")
                
            # Build table reference
            table_ref = f"{schema}.{table_name}" if schema else table_name
            
            # Build update statement
            params = []
            param_index = 1
            
            # Validate and build SET clauses
            set_clauses = []
            for key in data.keys():
                if not SQLInjectionGuard.validate_identifier(key):
                    raise ValidationError(f"Invalid column name: {key}")
                set_clauses.append(f"{key} = ${param_index}")
                params.append(data[key])
                param_index += 1
            
            # Validate and build WHERE clauses
            where_clauses = []
            for key in filters.keys():
                if not SQLInjectionGuard.validate_identifier(key):
                    raise ValidationError(f"Invalid filter column name: {key}")
                where_clauses.append(f"{key} = ${param_index}")
                params.append(filters[key])
                param_index += 1
            
            query = f"UPDATE {table_ref} SET {', '.join(set_clauses)} WHERE {' AND '.join(where_clauses)} RETURNING *"
                
            # Execute query with performance monitoring
            async with self.session_manager as connection:
                with self.performance_monitor.measure_query_time(query):
                    result = await connection.fetch(query, *params)
                    
            return [dict(row) for row in result]
            
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during update operation: {e}")
            raise DatabaseError(f"Unexpected error: {e}") from e
            
    async def delete(self, table_name: str, filters: Dict[str, Any], 
                    schema: Optional[str] = None) -> List[Dict[str, Any]]:
        """Delete records from the specified table.
        
        Args:
            table_name: Name of the table
            filters: Dictionary of filter conditions for records to delete
            schema: Optional schema name
            
        Returns:
            List of deleted records
            
        Raises:
            DatabaseError: If the delete operation fails
            ValidationError: If the filters are invalid
        """
        try:
            # Validate input
            if not filters:
                raise ValidationError("Filters cannot be empty for delete operation")
            
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table_name):
                raise ValidationError(f"Invalid table name: {table_name}")
            if schema and not SQLInjectionGuard.validate_identifier(schema):
                raise ValidationError(f"Invalid schema name: {schema}")
                
            # Build table reference
            table_ref = f"{schema}.{table_name}" if schema else table_name
            
            # Build delete statement
            params = []
            param_index = 1
            where_clauses = []
            
            for key in filters.keys():
                if not SQLInjectionGuard.validate_identifier(key):
                    raise ValidationError(f"Invalid filter column name: {key}")
                where_clauses.append(f"{key} = ${param_index}")
                params.append(filters[key])
                param_index += 1
                
            query = f"DELETE FROM {table_ref} WHERE {' AND '.join(where_clauses)} RETURNING *"
            
            # Execute query with performance monitoring
            async with self.session_manager as connection:
                with self.performance_monitor.measure_query_time(query):
                    result = await connection.fetch(query, *params)
                    
            return [dict(row) for row in result]
            
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during delete operation: {e}")
            raise DatabaseError(f"Unexpected error: {e}") from e
            
    async def bulk_insert(self, table_name: str, data: List[Dict[str, Any]], 
                         schema: Optional[str] = None, batch_size: int = 1000) -> BulkOperationResult:
        """Perform bulk insert operation.
        
        Args:
            table_name: Name of the table
            data: List of dictionaries containing the data to insert
            schema: Optional schema name
            batch_size: Number of records to insert per batch
            
        Returns:
            BulkOperationResult containing operation statistics
            
        Raises:
            DatabaseError: If the bulk insert operation fails
            ValidationError: If the data validation fails
        """
        try:
            # Handle different data types properly
            if data is None:
                raise ValidationError("Data cannot be None")

            # Check if data is empty based on its type
            if isinstance(data, (list, tuple)):
                if len(data) == 0:
                    raise ValidationError("Data list cannot be empty")
            elif hasattr(data, 'empty'):  # DataFrame or Series
                if data.empty:
                    raise ValidationError("Data cannot be empty")
            elif hasattr(data, '__len__'):  # Other iterable types
                if len(data) == 0:
                    raise ValidationError("Data cannot be empty")
            
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table_name):
                raise ValidationError(f"Invalid table name: {table_name}")
            if schema and not SQLInjectionGuard.validate_identifier(schema):
                raise ValidationError(f"Invalid schema name: {schema}")
                
            # Build table reference
            table_ref = f"{schema}.{table_name}" if schema else table_name
            
            total_inserted = 0
            failed_records = []
            
            async with self.session_manager as connection:
                # Process data in batches
                for i in range(0, len(data), batch_size):
                    batch = data[i:i + batch_size]
                    
                    try:
                        # Prepare batch insert
                        if batch:
                            # Validate column names
                            for key in batch[0].keys():
                                if not SQLInjectionGuard.validate_identifier(key):
                                    raise ValidationError(f"Invalid column name: {key}")
                            
                            columns = ", ".join(batch[0].keys())
                            placeholders = ", ".join([f"${i+1}" for i in range(len(batch[0]))])
                            query = f"INSERT INTO {table_ref} ({columns}) VALUES ({placeholders})"
                            
                            # Execute batch with performance monitoring
                            with self.performance_monitor.measure_query_time(query):
                                for record in batch:
                                    params = list(record.values())
                                    await connection.execute(query, *params)
                                    
                            total_inserted += len(batch)
                            
                    except Exception as e:
                        logger.warning(f"Failed to insert batch {i//batch_size + 1}: {e}")
                        failed_records.extend(batch)
            
            return BulkOperationResult(
                total_processed=len(data),
                successful=total_inserted,
                failed=len(failed_records),
                failed_records=failed_records
            )
            
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during bulk insert: {e}")
            raise DatabaseError(f"Bulk insert failed: {e}") from e
            
    async def bulk_update(self, table_name: str, data: List[Dict[str, Any]], 
                         key_columns: List[str], schema: Optional[str] = None,
                         batch_size: int = 1000) -> BulkOperationResult:
        """Perform bulk update operation.
        
        Args:
            table_name: Name of the table
            data: List of dictionaries containing the data to update
            key_columns: List of column names to use as keys for matching records
            schema: Optional schema name
            batch_size: Number of records to update per batch
            
        Returns:
            BulkOperationResult containing operation statistics
            
        Raises:
            DatabaseError: If the bulk update operation fails
            ValidationError: If the data validation fails
        """
        try:
            # Validate input data
            if data is None:
                raise ValidationError("Data cannot be None")

            # Check if data is empty based on its type
            if isinstance(data, (list, tuple)):
                if len(data) == 0:
                    raise ValidationError("Data list cannot be empty")
            elif hasattr(data, 'empty'):  # DataFrame or Series
                if data.empty:
                    raise ValidationError("Data cannot be empty")
            elif hasattr(data, '__len__'):  # Other iterable types
                if len(data) == 0:
                    raise ValidationError("Data cannot be empty")
            if not key_columns:
                raise ValidationError("Key columns cannot be empty")
            
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table_name):
                raise ValidationError(f"Invalid table name: {table_name}")
            if schema and not SQLInjectionGuard.validate_identifier(schema):
                raise ValidationError(f"Invalid schema name: {schema}")
            
            for col in key_columns:
                if not SQLInjectionGuard.validate_identifier(col):
                    raise ValidationError(f"Invalid key column name: {col}")
                
            total_updated = 0
            failed_records = []
            
            # Process data in batches
            for i in range(0, len(data), batch_size):
                batch = data[i:i + batch_size]
                
                for record in batch:
                    try:
                        # Separate key columns from update columns
                        key_data = {k: v for k, v in record.items() if k in key_columns}
                        update_data = {k: v for k, v in record.items() if k not in key_columns}
                        
                        if update_data:  # Only update if there's data to update
                            updated_count = await self.update(table_name, update_data, key_data, schema)
                            total_updated += updated_count
                        
                    except Exception as e:
                        logger.warning(f"Failed to update record {record}: {e}")
                        failed_records.append(record)
                        
            return BulkOperationResult(
                total_processed=len(data),
                successful=total_updated,
                failed=len(failed_records),
                failed_records=failed_records
            )
            
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during bulk update: {e}")
            raise DatabaseError(f"Bulk update failed: {e}") from e
            
    async def upsert(self, table_name: str, data: Dict[str, Any], 
                    key_columns: List[str], schema: Optional[str] = None) -> Dict[str, Any]:
        """Perform upsert (insert or update) operation.
        
        Args:
            table_name: Name of the table
            data: Dictionary containing the data to upsert
            key_columns: List of column names to use as keys for matching
            schema: Optional schema name
            
        Returns:
            Dictionary containing the upserted record
            
        Raises:
            DatabaseError: If the upsert operation fails
            ValidationError: If the data validation fails
        """
        try:
            # Validate input data
            if data is None:
                raise ValidationError("Data cannot be None")

            # Check if data is empty based on its type
            if isinstance(data, (list, tuple)):
                if len(data) == 0:
                    raise ValidationError("Data cannot be empty")
            elif hasattr(data, 'empty'):  # DataFrame or Series
                if data.empty:
                    raise ValidationError("Data cannot be empty")
            elif hasattr(data, '__len__'):  # Other iterable types
                if len(data) == 0:
                    raise ValidationError("Data cannot be empty")
            if not key_columns:
                raise ValidationError("Key columns cannot be empty")
            
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table_name):
                raise ValidationError(f"Invalid table name: {table_name}")
            if schema and not SQLInjectionGuard.validate_identifier(schema):
                raise ValidationError(f"Invalid schema name: {schema}")
            
            for col in key_columns:
                if not SQLInjectionGuard.validate_identifier(col):
                    raise ValidationError(f"Invalid key column name: {col}")
            
            for col in data.keys():
                if not SQLInjectionGuard.validate_identifier(col):
                    raise ValidationError(f"Invalid column name: {col}")
                
            # Extract key data for checking existence
            key_data = {k: v for k, v in data.items() if k in key_columns}
            
            # Check if record exists
            existing_records = await self.read(table_name, key_data, schema, limit=1)
            
            if existing_records:
                # Update existing record
                update_data = {k: v for k, v in data.items() if k not in key_columns}
                if update_data:
                    await self.update(table_name, update_data, key_data, schema)
                # Return updated record
                updated_records = await self.read(table_name, key_data, schema, limit=1)
                return updated_records[0] if updated_records else data
            else:
                # Insert new record
                return await self.create(table_name, data, schema)
                
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during upsert: {e}")
            raise DatabaseError(f"Upsert failed: {e}") from e
            
    async def count(self, table_name: str, filters: Optional[Dict[str, Any]] = None,
                   schema: Optional[str] = None) -> int:
        """Count records in the specified table.
        
        Args:
            table_name: Name of the table
            filters: Optional dictionary of filter conditions
            schema: Optional schema name
            
        Returns:
            Number of matching records
            
        Raises:
            DatabaseError: If the count operation fails
        """
        try:
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table_name):
                raise ValidationError(f"Invalid table name: {table_name}")
            if schema and not SQLInjectionGuard.validate_identifier(schema):
                raise ValidationError(f"Invalid schema name: {schema}")
            
            # Build table reference
            table_ref = f"{schema}.{table_name}" if schema else table_name
            
            # Build count query
            query = f"SELECT COUNT(*) as count FROM {table_ref}"
            params = []
            param_index = 1
            
            # Add filters
            if filters:
                conditions = []
                for key, value in filters.items():
                    if not SQLInjectionGuard.validate_identifier(key):
                        raise ValidationError(f"Invalid filter column name: {key}")
                    conditions.append(f"{key} = ${param_index}")
                    params.append(value)
                    param_index += 1
                query += f" WHERE {' AND '.join(conditions)}"
                
            # Execute query with performance monitoring
            async with self.session_manager as connection:
                with self.performance_monitor.measure_query_time(query):
                    result = await connection.fetchrow(query, *params)
                    
            return result['count'] if result else 0
            
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during count operation: {e}")
            raise DatabaseError(f"Unexpected error: {e}") from e
            
    async def exists(self, table_name: str, filters: Dict[str, Any],
                    schema: Optional[str] = None) -> bool:
        """Check if records exist in the specified table.
        
        Args:
            table_name: Name of the table
            filters: Dictionary of filter conditions
            schema: Optional schema name
            
        Returns:
            True if matching records exist, False otherwise
            
        Raises:
            DatabaseError: If the existence check fails
        """
        try:
            # Validate input
            if not filters:
                raise ValidationError("Filters cannot be empty for existence check")
                
            count = await self.count(table_name, filters, schema)
            return count > 0
            
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during existence check: {e}")
            raise DatabaseError(f"Existence check failed: {e}") from e
            
    async def execute_raw_query(self, query: str, params: Optional[List[Any]] = None) -> QueryResult:
        """Execute a raw SQL query.
        
        Args:
            query: Raw SQL query string
            params: Optional parameters for the query
            
        Returns:
            QueryResult containing the query results
            
        Raises:
            DatabaseError: If the query execution fails
        """
        try:
            # Validate query is not empty
            if not query or not query.strip():
                raise ValidationError("Query cannot be empty")
                
            # Execute query with performance monitoring
            async with self.session_manager as connection:
                with self.performance_monitor.measure_query_time(query):
                    if query.strip().upper().startswith('SELECT'):
                        # For SELECT queries
                        rows = await connection.fetch(query, *(params or []))
                        data = [dict(row) for row in rows]
                        columns = list(rows[0].keys()) if rows else []
                        return QueryResult(
                            data=data,
                            row_count=len(data),
                            columns=columns
                        )
                    else:
                        # For INSERT, UPDATE, DELETE operations
                        result = await connection.execute(query, *(params or []))
                        return QueryResult(
                            data=[],
                            row_count=result,
                            columns=[]
                        )
                
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during raw query execution: {e}")
            raise DatabaseError(f"Unexpected error: {e}") from e
            
    async def insert_dataframe(self, table_name: str, dataframe: pd.DataFrame,
                              schema: Optional[str] = None, if_exists: str = 'append') -> bool:
        """Insert a pandas DataFrame into the specified table.
        
        Args:
            table_name: Name of the table
            dataframe: Pandas DataFrame to insert
            schema: Optional schema name
            if_exists: How to behave if the table exists ('fail', 'replace', 'append')
            
        Returns:
            True if successful, False otherwise
            
        Raises:
            DatabaseError: If the DataFrame insertion fails
        """
        try:
            # Validate input
            if dataframe is None:
                raise ValidationError("DataFrame cannot be None")
            
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table_name):
                raise ValidationError(f"Invalid table name: {table_name}")
            if schema and not SQLInjectionGuard.validate_identifier(schema):
                raise ValidationError(f"Invalid schema name: {schema}")
            
            # Convert DataFrame to list of dictionaries for bulk insert
            if dataframe.empty:
                logger.info(f"Empty DataFrame provided for table '{schema}.{table_name}'")
                return True

            # Convert DataFrame to records (list of dicts)
            records = dataframe.to_dict('records')
            
            # Use existing bulk_insert method
            await self.bulk_insert(table_name, records, schema)
            
            logger.info(
                f"Successfully inserted {len(records)} records from DataFrame into table '{schema}.{table_name}'"
            )
            return True

        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(
                f"Error inserting DataFrame into '{schema}.{table_name}': {e}"
            )
            raise DatabaseError(f"Failed to insert DataFrame: {e}") from e
            
    async def update_data(self, table_name: str, schema: str, data: Dict[str, Any], 
                         where_clause: str) -> str:
        """Update data in the specified table with a WHERE clause.
        
        Args:
            table_name: Name of the table
            schema: Schema name
            data: Dictionary containing the data to update
            where_clause: WHERE clause for the update
            
        Returns:
            String indicating the result of the update operation
            
        Raises:
            DatabaseError: If the update operation fails
        """
        try:
            # Validate input
            if data is None:
                raise ValidationError("Update data cannot be None")

            # Check if data is empty based on its type
            if isinstance(data, (list, tuple, dict)):
                if len(data) == 0:
                    raise ValidationError("Update data cannot be empty")
            elif hasattr(data, 'empty'):  # DataFrame or Series
                if data.empty:
                    raise ValidationError("Update data cannot be empty")
            elif hasattr(data, '__len__'):  # Other iterable types
                if len(data) == 0:
                    raise ValidationError("Update data cannot be empty")
            if not where_clause:
                raise ValidationError("WHERE clause cannot be empty")
            
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table_name):
                raise ValidationError(f"Invalid table name: {table_name}")
            if not SQLInjectionGuard.validate_identifier(schema):
                raise ValidationError(f"Invalid schema name: {schema}")
                
            # Build table reference
            table_ref = f"{schema}.{table_name}"
            
            # Build update statement
            params = []
            param_index = 1
            
            # Validate and build SET clauses
            set_clauses = []
            for key in data.keys():
                if not SQLInjectionGuard.validate_identifier(key):
                    raise ValidationError(f"Invalid column name: {key}")
                set_clauses.append(f"{key} = ${param_index}")
                params.append(data[key])
                param_index += 1
            
            query = f"UPDATE {table_ref} SET {', '.join(set_clauses)} WHERE {where_clause}"
                
            # Execute query with performance monitoring
            async with self.session_manager as connection:
                with self.performance_monitor.measure_query_time(query):
                    result = await connection.execute(query, *params)
                    
            return f"UPDATE {result}"
            
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during update_data operation: {e}")
            raise DatabaseError(f"Unexpected error: {e}") from e
            
    async def delete_data(self, table_name: str, schema: str, where_clause: str) -> str:
        """Delete data from the specified table with a WHERE clause.
        
        Args:
            table_name: Name of the table
            schema: Schema name
            where_clause: WHERE clause for the delete
            
        Returns:
            String indicating the result of the delete operation
            
        Raises:
            DatabaseError: If the delete operation fails
        """
        try:
            # Validate input
            if not where_clause:
                raise ValidationError("WHERE clause cannot be empty")
            
            # Validate identifiers
            if not SQLInjectionGuard.validate_identifier(table_name):
                raise ValidationError(f"Invalid table name: {table_name}")
            if not SQLInjectionGuard.validate_identifier(schema):
                raise ValidationError(f"Invalid schema name: {schema}")
                
            # Build table reference
            table_ref = f"{schema}.{table_name}"
            
            query = f"DELETE FROM {table_ref} WHERE {where_clause}"
            
            # Execute query with performance monitoring
            async with self.session_manager as connection:
                with self.performance_monitor.measure_query_time(query):
                    result = await connection.execute(query)
                    
            return f"DELETE {result}"
            
        except (ValidationError, DatabaseError):
            raise
        except Exception as e:
            logger.error(f"Unexpected error during delete_data operation: {e}")
            raise DatabaseError(f"Unexpected error: {e}") from e
