# -*- coding: utf-8 -*-
"""
Core Types for Unified Data Processing Layer

This module defines the fundamental types, enums, and data structures used
throughout the unified data processing framework.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, AsyncGenerator, Callable

import pandas as pd
from pydantic import BaseModel, Field, validator, field_validator, ConfigDict


class ProcessingEngine(Enum):
    """Supported data processing engines."""
    AUTO = "auto"  # Automatic selection based on data size
    PANDAS = "pandas"  # Pandas DataFrame engine
    POLARS = "polars"  # Polars DataFrame engine (high performance)
    DASK = "dask"  # Dask for distributed processing (future)


class ProcessingMode(Enum):
    """Processing execution modes."""
    SYNC = "sync"  # Synchronous processing
    ASYNC = "async"  # Asynchronous processing
    PARALLEL = "parallel"  # Parallel processing with threads
    DISTRIBUTED = "distributed"  # Distributed processing (future)


class DataFormat(Enum):
    """Supported data formats."""
    CSV = "csv"
    TSV = "tsv"
    EXCEL = "excel"
    JSON = "json"
    JSONL = "jsonl"
    PARQUET = "parquet"
    FEATHER = "feather"
    HDF5 = "hdf5"


class ProcessingStatus(Enum):
    """Processing operation status."""
    PENDING = "pending"
    INITIALIZING = "initializing"
    PROCESSING = "processing"
    VALIDATING = "validating"
    TRANSFORMING = "transforming"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class QualityLevel(Enum):
    """Data quality levels."""
    EXCELLENT = "excellent"  # >95% quality
    GOOD = "good"  # 90-95% quality
    ACCEPTABLE = "acceptable"  # 80-90% quality
    POOR = "poor"  # 70-80% quality
    UNACCEPTABLE = "unacceptable"  # <70% quality


@dataclass
class ProcessingMetrics:
    """Comprehensive metrics for processing operations."""
    # Timing metrics
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    processing_time_seconds: float = 0.0
    validation_time_seconds: float = 0.0
    transformation_time_seconds: float = 0.0
    
    # Data metrics
    records_processed: int = 0
    records_validated: int = 0
    records_transformed: int = 0
    records_failed: int = 0
    batches_processed: int = 0
    
    # Performance metrics
    throughput_records_per_second: float = 0.0
    memory_usage_mb: float = 0.0
    memory_peak_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    
    # Quality metrics
    data_quality_score: float = 0.0
    error_rate: float = 0.0
    completeness_rate: float = 0.0
    
    # Error tracking
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # Engine-specific metrics
    engine_used: Optional[ProcessingEngine] = None
    chunks_processed: int = 0
    parallel_workers: int = 0
    
    def calculate_throughput(self) -> float:
        """Calculate processing throughput."""
        if self.processing_time_seconds > 0:
            self.throughput_records_per_second = self.records_processed / self.processing_time_seconds
        return self.throughput_records_per_second
    
    def calculate_error_rate(self) -> float:
        """Calculate error rate."""
        if self.records_processed > 0:
            self.error_rate = self.records_failed / self.records_processed
        return self.error_rate
    
    def get_quality_level(self) -> QualityLevel:
        """Get data quality level based on score."""
        if self.data_quality_score >= 0.95:
            return QualityLevel.EXCELLENT
        elif self.data_quality_score >= 0.90:
            return QualityLevel.GOOD
        elif self.data_quality_score >= 0.80:
            return QualityLevel.ACCEPTABLE
        elif self.data_quality_score >= 0.70:
            return QualityLevel.POOR
        else:
            return QualityLevel.UNACCEPTABLE


@dataclass
class ProcessingResult:
    """Comprehensive result object for processing operations."""
    status: ProcessingStatus
    data: Optional[Union[pd.DataFrame, Any]] = None
    metrics: ProcessingMetrics = field(default_factory=ProcessingMetrics)
    source_info: Dict[str, Any] = field(default_factory=dict)
    validation_results: Dict[str, Any] = field(default_factory=dict)
    transformation_results: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def success(self) -> bool:
        """Check if processing was successful."""
        return self.status == ProcessingStatus.COMPLETED
    
    @property
    def has_warnings(self) -> bool:
        """Check if processing has warnings."""
        return len(self.warnings) > 0 or len(self.metrics.warnings) > 0
    
    @property
    def quality_level(self) -> QualityLevel:
        """Get data quality level."""
        return self.metrics.get_quality_level()


class ProcessingConfig(BaseModel):
    """Base configuration for data processing operations."""
    # Engine settings
    engine: ProcessingEngine = Field(default=ProcessingEngine.AUTO, description="Processing engine")
    mode: ProcessingMode = Field(default=ProcessingMode.ASYNC, description="Processing mode")
    
    # Performance settings
    chunk_size: int = Field(default=50000, ge=1000, le=1000000, description="Chunk size for processing")
    batch_size: int = Field(default=10000, ge=1000, le=100000, description="Batch size for operations")
    max_workers: int = Field(default=4, ge=1, le=32, description="Maximum worker threads")
    memory_limit_mb: int = Field(default=1024, ge=128, le=8192, description="Memory limit in MB")
    
    # Quality settings
    enable_validation: bool = Field(default=True, description="Enable data validation")
    enable_transformation: bool = Field(default=True, description="Enable data transformation")
    quality_threshold: float = Field(default=0.8, ge=0.0, le=1.0, description="Minimum quality threshold")
    max_error_rate: float = Field(default=0.05, ge=0.0, le=1.0, description="Maximum error rate")
    
    # Async settings
    async_timeout_seconds: int = Field(default=300, ge=30, le=3600, description="Async operation timeout")
    max_concurrent_operations: int = Field(default=3, ge=1, le=10, description="Max concurrent operations")
    
    # Memory management
    enable_gc: bool = Field(default=True, description="Enable garbage collection")
    gc_frequency: int = Field(default=10, ge=1, le=100, description="GC frequency (batches)")
    memory_check_frequency: int = Field(default=5, ge=1, le=50, description="Memory check frequency")
    memory_warning_threshold: float = Field(default=0.8, ge=0.5, le=0.95, description="Memory warning threshold")
    
    # Engine selection thresholds
    pandas_threshold_records: int = Field(default=1_000_000, description="Pandas engine threshold")
    polars_threshold_records: int = Field(default=1_000_000, description="Polars engine threshold")
    
    # Monitoring
    enable_monitoring: bool = Field(default=True, description="Enable performance monitoring")
    enable_progress_tracking: bool = Field(default=True, description="Enable progress tracking")
    log_level: str = Field(default="INFO", description="Logging level")
    
    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of {valid_levels}")
        return v.upper()
    
    model_config = ConfigDict(
        extra="allow",
        use_enum_values=True
    )
@dataclass
class ChunkInfo:
    """Information about a data chunk."""
    chunk_id: int
    start_row: int
    end_row: int
    size: int
    memory_usage_mb: float = 0.0
    processing_time: float = 0.0
    status: ProcessingStatus = ProcessingStatus.PENDING
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FileInfo:
    """Information about a source file."""
    path: Path
    size_bytes: int
    format: DataFormat
    encoding: Optional[str] = None
    delimiter: Optional[str] = None
    header_row: Optional[int] = None
    estimated_rows: Optional[int] = None
    columns: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def size_mb(self) -> float:
        """Get file size in MB."""
        return self.size_bytes / (1024 * 1024)
    
    @property
    def size_gb(self) -> float:
        """Get file size in GB."""
        return self.size_bytes / (1024 * 1024 * 1024)


# Type aliases for common use cases
ProcessorFunction = Callable[[pd.DataFrame], pd.DataFrame]
ValidatorFunction = Callable[[pd.DataFrame], bool]
TransformerFunction = Callable[[pd.DataFrame], pd.DataFrame]
AsyncProcessorFunction = Callable[[pd.DataFrame], AsyncGenerator[pd.DataFrame, None]]

# Common data type mappings
DATA_TYPE_MAPPINGS = {
    'string': 'object',
    'integer': 'int64',
    'float': 'float64',
    'boolean': 'bool',
    'datetime': 'datetime64[ns]',
    'date': 'datetime64[ns]',
    'category': 'category'
}

# Engine selection criteria
ENGINE_SELECTION_CRITERIA = {
    ProcessingEngine.PANDAS: {
        'max_records': 1_000_000,
        'max_memory_mb': 1024,
        'best_for': ['small_datasets', 'complex_operations', 'compatibility']
    },
    ProcessingEngine.POLARS: {
        'max_records': 100_000_000,
        'max_memory_mb': 8192,
        'best_for': ['large_datasets', 'performance', 'memory_efficiency']
    }
}