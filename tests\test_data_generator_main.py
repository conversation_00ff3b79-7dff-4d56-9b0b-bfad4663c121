#!/usr/bin/env python3
"""
Test data generator for the Connect database framework.

This module provides utilities to generate realistic test data
for various components of the system.
"""

import csv
import json
import random
import string
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd


class DataGenerator:
    """Generate test data for various components."""

    def __init__(self, seed: int = 42):
        """Initialize the test data generator.

        Args:
            seed: Random seed for reproducible data generation
        """
        random.seed(seed)
        np.random.seed(seed)
        self.seed = seed

    def generate_ep_data(self, num_records: int = 1000) -> pd.DataFrame:
        """Generate EP (Electronic Prescription) test data.

        Args:
            num_records: Number of records to generate

        Returns:
            DataFrame with EP test data
        """
        data = []

        for i in range(num_records):
            record = {
                "prescription_id": f"EP{i+1:06d}",
                "patient_id": f"P{random.randint(1000, 9999)}",
                "doctor_id": f"D{random.randint(100, 999)}",
                "pharmacy_id": f"PH{random.randint(10, 99)}",
                "medication_name": random.choice(
                    [
                        "Amoxicillin",
                        "Ibuprofen",
                        "Paracetamol",
                        "Aspirin",
                        "Metformin",
                        "Lisinopril",
                        "Atorvastatin",
                        "Omeprazole",
                    ]
                ),
                "dosage": f"{random.randint(5, 500)}mg",
                "quantity": random.randint(10, 100),
                "prescribed_date": self._random_date(
                    datetime.now() - timedelta(days=365), datetime.now()
                ),
                "dispensed_date": self._random_date(
                    datetime.now() - timedelta(days=30), datetime.now()
                )
                if random.random() > 0.3
                else None,
                "status": random.choice(["prescribed", "dispensed", "cancelled"]),
                "cost": round(random.uniform(5.0, 200.0), 2),
                "insurance_covered": random.choice([True, False]),
                "notes": self._random_text(50) if random.random() > 0.7 else None,
            }
            data.append(record)

        return pd.DataFrame(data)

    def generate_cdr_data(self, num_records: int = 1000) -> pd.DataFrame:
        """Generate CDR (Call Detail Record) test data.

        Args:
            num_records: Number of records to generate

        Returns:
            DataFrame with CDR test data
        """
        data = []

        for i in range(num_records):
            call_start = self._random_date(
                datetime.now() - timedelta(days=30), datetime.now()
            )
            duration = random.randint(10, 3600)  # 10 seconds to 1 hour
            call_end = call_start + timedelta(seconds=duration)

            record = {
                "call_id": f"CDR{i+1:08d}",
                "caller_number": self._random_phone_number(),
                "callee_number": self._random_phone_number(),
                "call_start_time": call_start,
                "call_end_time": call_end,
                "duration_seconds": duration,
                "call_type": random.choice(["voice", "video", "conference"]),
                "call_status": random.choice(
                    ["completed", "busy", "no_answer", "failed"]
                ),
                "caller_location": self._random_location(),
                "callee_location": self._random_location(),
                "cost": round(random.uniform(0.01, 5.0), 4),
                "network_type": random.choice(["4G", "5G", "WiFi", "3G"]),
                "quality_score": round(random.uniform(1.0, 5.0), 2),
                "data_usage_mb": round(random.uniform(0.1, 50.0), 2)
                if random.random() > 0.5
                else 0,
            }
            data.append(record)

        return pd.DataFrame(data)

    def generate_nlg_data(self, num_records: int = 1000) -> pd.DataFrame:
        """Generate NLG (Natural Language Generation) test data.

        Args:
            num_records: Number of records to generate

        Returns:
            DataFrame with NLG test data
        """
        data = []

        templates = [
            "The patient {patient_name} was prescribed {medication} for {condition}.",
            "Dr. {doctor_name} recommended {treatment} for patient {patient_name}.",
            "Patient {patient_name} reported {symptom} during the consultation.",
            "The test results for {patient_name} showed {result}.",
            "Follow-up appointment scheduled for {patient_name} on {date}.",
        ]

        for i in range(num_records):
            template = random.choice(templates)

            record = {
                "record_id": f"NLG{i+1:06d}",
                "template": template,
                "generated_text": self._fill_template(template),
                "language": random.choice(["en", "es", "fr", "de", "zh"]),
                "confidence_score": round(random.uniform(0.7, 1.0), 3),
                "generation_time": self._random_date(
                    datetime.now() - timedelta(days=7), datetime.now()
                ),
                "model_version": f"v{random.randint(1, 5)}.{random.randint(0, 9)}",
                "context_length": random.randint(50, 500),
                "quality_rating": random.randint(1, 5),
                "human_reviewed": random.choice([True, False]),
            }
            data.append(record)

        return pd.DataFrame(data)

    def generate_geospatial_data(self, num_records: int = 1000) -> pd.DataFrame:
        """Generate geospatial test data.

        Args:
            num_records: Number of records to generate

        Returns:
            DataFrame with geospatial test data
        """
        data = []

        # Define some city centers for realistic coordinates
        city_centers = [
            {"name": "New York", "lat": 40.7128, "lon": -74.0060},
            {"name": "London", "lat": 51.5074, "lon": -0.1278},
            {"name": "Tokyo", "lat": 35.6762, "lon": 139.6503},
            {"name": "Sydney", "lat": -33.8688, "lon": 151.2093},
            {"name": "Berlin", "lat": 52.5200, "lon": 13.4050},
        ]

        for i in range(num_records):
            city = random.choice(city_centers)
            # Add some random offset to create points around the city
            lat_offset = random.uniform(-0.5, 0.5)
            lon_offset = random.uniform(-0.5, 0.5)

            record = {
                "point_id": f"GEO{i+1:06d}",
                "name": f"Location {i+1}",
                "latitude": round(city["lat"] + lat_offset, 6),
                "longitude": round(city["lon"] + lon_offset, 6),
                "altitude": round(random.uniform(0, 1000), 2),
                "city": city["name"],
                "country": self._get_country_for_city(city["name"]),
                "category": random.choice(
                    [
                        "hospital",
                        "pharmacy",
                        "clinic",
                        "laboratory",
                        "office",
                        "residential",
                        "commercial",
                        "industrial",
                    ]
                ),
                "accuracy_meters": random.randint(1, 100),
                "timestamp": self._random_date(
                    datetime.now() - timedelta(days=30), datetime.now()
                ),
                "elevation": round(random.uniform(0, 500), 2),
                "address": self._random_address(),
            }
            data.append(record)

        return pd.DataFrame(data)

    def generate_performance_data(self, num_records: int = 1000) -> pd.DataFrame:
        """Generate performance monitoring test data.

        Args:
            num_records: Number of records to generate

        Returns:
            DataFrame with performance test data
        """
        data = []

        operations = [
            "database_query",
            "file_import",
            "data_export",
            "geospatial_analysis",
            "data_validation",
            "cache_operation",
        ]

        for i in range(num_records):
            operation = random.choice(operations)
            base_duration = {
                "database_query": 0.1,
                "file_import": 5.0,
                "data_export": 3.0,
                "geospatial_analysis": 2.0,
                "data_validation": 1.0,
                "cache_operation": 0.01,
            }[operation]

            record = {
                "metric_id": f"PERF{i+1:06d}",
                "operation": operation,
                "duration_seconds": round(
                    random.uniform(base_duration * 0.5, base_duration * 2.0), 4
                ),
                "memory_usage_mb": round(random.uniform(10, 500), 2),
                "cpu_usage_percent": round(random.uniform(5, 95), 2),
                "disk_io_mb": round(random.uniform(0, 100), 2),
                "network_io_mb": round(random.uniform(0, 50), 2),
                "records_processed": random.randint(1, 10000),
                "success": random.choice([True, False], p=[0.95, 0.05]),
                "error_message": self._random_error()
                if random.random() < 0.05
                else None,
                "timestamp": self._random_date(
                    datetime.now() - timedelta(hours=24), datetime.now()
                ),
                "server_id": f"SRV{random.randint(1, 10):02d}",
                "thread_id": random.randint(1, 100),
            }
            data.append(record)

        return pd.DataFrame(data)

    def _random_date(self, start_date: datetime, end_date: datetime) -> datetime:
        """Generate a random date between start and end dates."""
        time_between = end_date - start_date
        days_between = time_between.days
        random_days = random.randrange(days_between)
        random_seconds = random.randrange(24 * 60 * 60)
        return start_date + timedelta(days=random_days, seconds=random_seconds)

    def _random_text(self, max_length: int = 100) -> str:
        """Generate random text."""
        words = [
            "lorem",
            "ipsum",
            "dolor",
            "sit",
            "amet",
            "consectetur",
            "adipiscing",
            "elit",
            "sed",
            "do",
            "eiusmod",
            "tempor",
            "incididunt",
            "ut",
            "labore",
            "et",
            "dolore",
            "magna",
            "aliqua",
            "enim",
            "ad",
            "minim",
            "veniam",
            "quis",
        ]

        text = " ".join(random.choices(words, k=random.randint(5, 15)))
        return text[:max_length]

    def _random_phone_number(self) -> str:
        """Generate a random phone number."""
        return f"+1{random.randint(200, 999)}{random.randint(200, 999)}{random.randint(1000, 9999)}"

    def _random_location(self) -> str:
        """Generate a random location string."""
        cities = ["New York", "Los Angeles", "Chicago", "Houston", "Phoenix"]
        states = ["NY", "CA", "IL", "TX", "AZ"]
        return f"{random.choice(cities)}, {random.choice(states)}"

    def _random_address(self) -> str:
        """Generate a random address."""
        street_names = ["Main St", "Oak Ave", "First St", "Second St", "Park Rd"]
        return f"{random.randint(1, 9999)} {random.choice(street_names)}"

    def _get_country_for_city(self, city: str) -> str:
        """Get country for a given city."""
        city_country_map = {
            "New York": "USA",
            "London": "UK",
            "Tokyo": "Japan",
            "Sydney": "Australia",
            "Berlin": "Germany",
        }
        return city_country_map.get(city, "Unknown")

    def _fill_template(self, template: str) -> str:
        """Fill a template with random data."""
        replacements = {
            "{patient_name}": random.choice(["John Doe", "Jane Smith", "Bob Johnson"]),
            "{doctor_name}": random.choice(["Dr. Wilson", "Dr. Brown", "Dr. Davis"]),
            "{medication}": random.choice(["Aspirin", "Ibuprofen", "Paracetamol"]),
            "{condition}": random.choice(["headache", "fever", "pain"]),
            "{treatment}": random.choice(["rest", "medication", "therapy"]),
            "{symptom}": random.choice(["fatigue", "nausea", "dizziness"]),
            "{result}": random.choice(["normal", "elevated", "low"]),
            "{date}": self._random_date(
                datetime.now(), datetime.now() + timedelta(days=30)
            ).strftime("%Y-%m-%d"),
        }

        text = template
        for placeholder, value in replacements.items():
            text = text.replace(placeholder, value)

        return text

    def _random_error(self) -> str:
        """Generate a random error message."""
        errors = [
            "Connection timeout",
            "Invalid data format",
            "Permission denied",
            "Resource not found",
            "Memory allocation failed",
            "Network unreachable",
            "Database connection lost",
            "Invalid parameter value",
        ]
        return random.choice(errors)

    def save_test_data(self, output_dir: str = "test_data"):
        """Generate and save all test data to files.

        Args:
            output_dir: Directory to save test data files
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # Generate different datasets
        datasets = {
            "ep_data.csv": self.generate_ep_data(1000),
            "cdr_data.csv": self.generate_cdr_data(1000),
            "nlg_data.csv": self.generate_nlg_data(500),
            "geospatial_data.csv": self.generate_geospatial_data(500),
            "performance_data.csv": self.generate_performance_data(2000),
        }

        # Save datasets
        for filename, df in datasets.items():
            filepath = output_path / filename
            df.to_csv(filepath, index=False)
            print(f"Saved {len(df)} records to {filepath}")

        # Generate smaller datasets for unit tests
        small_datasets = {
            "small_ep_data.csv": self.generate_ep_data(50),
            "small_cdr_data.csv": self.generate_cdr_data(50),
            "small_geospatial_data.csv": self.generate_geospatial_data(25),
        }

        for filename, df in small_datasets.items():
            filepath = output_path / filename
            df.to_csv(filepath, index=False)
            print(f"Saved {len(df)} records to {filepath}")

        # Generate JSON test data
        json_data = {
            "config": {
                "database": {"host": "localhost", "port": 5432, "name": "test_db"},
                "cache": {"type": "redis", "ttl": 3600},
            },
            "test_cases": [
                {"id": i, "name": f"test_case_{i}", "expected": True}
                for i in range(1, 11)
            ],
        }

        json_filepath = output_path / "test_config.json"
        with open(json_filepath, "w") as f:
            json.dump(json_data, f, indent=2)
        print(f"Saved JSON test data to {json_filepath}")

        print(f"\nAll test data generated successfully in {output_path}")


if __name__ == "__main__":
    generator = TestDataGenerator()
    generator.save_test_data()