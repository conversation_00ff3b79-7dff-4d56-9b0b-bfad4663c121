__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""CSV data exporter.

This module provides CSV export functionality for various data formats.
"""

import csv
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from .base import BaseExporter, ExportError, ExportResult

# Configure logging
logger = logging.getLogger(__name__)


class CSVExporter(BaseExporter):
    """CSV data exporter."""

    def __init__(
        self,
        output_path: Union[str, Path],
        delimiter: str = ",",
        encoding: str = "utf-8",
        **kwargs,
    ):
        """Initialize CSV exporter.

        Args:
            output_path: Path where CSV file will be saved
            delimiter: CSV delimiter character
            encoding: File encoding
            **kwargs: Additional configuration options
        """
        super().__init__(output_path, **kwargs)
        self.delimiter = delimiter
        self.encoding = encoding

        # Ensure output file has .csv extension
        if not self.output_path.suffix:
            self.output_path = self.output_path.with_suffix(".csv")
        elif self.output_path.suffix.lower() != ".csv":
            self.output_path = self.output_path.with_suffix(".csv")

    async def export(self, data: Any, **kwargs) -> ExportResult:
        """Export data to CSV format.

        Args:
            data: Data to export (DataFrame, list of dicts, or list of lists)
            **kwargs: Additional export options
                - include_header: Whether to include header row (default: True)
                - index: Whether to include index column (default: False)

        Returns:
            ExportResult: Result of the export operation

        Raises:
            ExportError: If export fails
        """
        try:
            self.validate_data(data)
            self.prepare_output_directory()

            include_header = kwargs.get("include_header", True)
            include_index = kwargs.get("index", False)

            records_exported = 0

            if isinstance(data, pd.DataFrame):
                records_exported = await self._export_dataframe(
                    data, include_header, include_index
                )
            elif isinstance(data, list):
                records_exported = await self._export_list(data, include_header)
            else:
                raise ExportError(f"Unsupported data type: {type(data)}")

            file_size = (
                self.output_path.stat().st_size if self.output_path.exists() else 0
            )

            logger.info(
                f"Successfully exported {records_exported} records to {self.output_path}"
            )

            return ExportResult(
                success=True,
                file_path=self.output_path,
                records_exported=records_exported,
                file_size_bytes=file_size,
                metadata={
                    "delimiter": self.delimiter,
                    "encoding": self.encoding,
                    "include_header": include_header,
                    "include_index": include_index,
                },
            )

        except Exception as e:
            error_msg = f"Failed to export CSV: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ExportResult(success=False, error_message=error_msg)

    async def _export_dataframe(
        self, df: pd.DataFrame, include_header: bool, include_index: bool
    ) -> int:
        """Export pandas DataFrame to CSV."""
        df.to_csv(
            self.output_path,
            sep=self.delimiter,
            encoding=self.encoding,
            header=include_header,
            index=include_index,
        )
        return len(df)

    async def _export_list(self, data: List[Any], include_header: bool) -> int:
        """Export list data to CSV."""
        with open(self.output_path, "w", newline="", encoding=self.encoding) as csvfile:
            if not data:
                return 0

            if isinstance(data[0], dict):
                # List of dictionaries
                fieldnames = data[0].keys()
                writer = csv.DictWriter(
                    csvfile, fieldnames=fieldnames, delimiter=self.delimiter
                )

                if include_header:
                    writer.writeheader()

                for row in data:
                    writer.writerow(row)

            elif isinstance(data[0], (list, tuple)):
                # List of lists/tuples
                writer = csv.writer(csvfile, delimiter=self.delimiter)

                for row in data:
                    writer.writerow(row)
            else:
                # List of simple values
                writer = csv.writer(csvfile, delimiter=self.delimiter)

                for value in data:
                    writer.writerow([value])

            return len(data)
