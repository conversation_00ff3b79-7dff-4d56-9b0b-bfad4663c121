"""Example usage of ReadWriteSplitter for database read-write splitting.

This example demonstrates how to configure and use the ReadWriteSplitter
to route database queries to appropriate read replicas or the primary database.
"""

import asyncio
import logging
from typing import List

from src.database.config import DatabaseConfig, ReadWriteConfig, load_config
from src.database.connection import LoadBalancingStrategy, ReadWriteSplitter
from src.database.exceptions import (
    NoAvailableReplicasError,
    PrimaryDatabaseUnavailableError,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def example_basic_usage():
    """Basic usage example of ReadWriteSplitter."""
    logger.info("=== Basic ReadWriteSplitter Usage Example ===")

    # Configure primary database
    primary_config = DatabaseConfig(
        host="localhost",
        port=5432,
        name="connect_db",
        user="connect_user",
        password="connect_password",
    )

    # Configure read replicas
    replica_configs = [
        DatabaseConfig(
            host="replica1.example.com",
            port=5432,
            name="connect_db",
            user="connect_readonly",
            password="readonly_password",
        ),
        DatabaseConfig(
            host="replica2.example.com",
            port=5432,
            name="connect_db",
            user="connect_readonly",
            password="readonly_password",
        ),
    ]

    # Initialize ReadWriteSplitter
    splitter = ReadWriteSplitter(
        primary_config=primary_config,
        replica_configs=replica_configs,
        load_balancing_strategy=LoadBalancingStrategy.ROUND_ROBIN,
        fallback_to_primary=True,
        health_check_interval=30,
    )

    try:
        # Initialize the splitter
        await splitter.initialize()
        logger.info("ReadWriteSplitter initialized successfully")

        # Example 1: Read operation (uses replica)
        logger.info("\n--- Example 1: Read Operation ---")
        async with splitter.get_connection(read_only=True) as conn:
            result = await conn.fetchrow("SELECT version()")
            logger.info(f"Database version from replica: {result['version']}")

        # Example 2: Write operation (uses primary)
        logger.info("\n--- Example 2: Write Operation ---")
        async with splitter.get_connection(read_only=False) as conn:
            await conn.execute(
                "CREATE TABLE IF NOT EXISTS test_table (id SERIAL PRIMARY KEY, name TEXT)"
            )
            await conn.execute(
                "INSERT INTO test_table (name) VALUES ($1)", "test_record"
            )
            logger.info("Write operation completed on primary database")

        # Example 3: Multiple read operations (demonstrates load balancing)
        logger.info("\n--- Example 3: Multiple Read Operations (Load Balancing) ---")
        for i in range(5):
            async with splitter.get_connection(read_only=True) as conn:
                result = await conn.fetchval("SELECT COUNT(*) FROM test_table")
                logger.info(f"Read {i+1}: Record count = {result}")

        # Example 4: Health status check
        logger.info("\n--- Example 4: Health Status Check ---")
        health_status = await splitter.get_health_status()
        logger.info(f"Primary healthy: {health_status['summary']['primary_healthy']}")
        logger.info(f"Healthy replicas: {health_status['summary']['healthy_replicas']}")

        # Example 5: Connection statistics
        logger.info("\n--- Example 5: Connection Statistics ---")
        stats = splitter.get_stats()
        logger.info(f"Load balancing strategy: {stats['load_balancing_strategy']}")
        logger.info(f"Replica connection counts: {stats['replica_connection_counts']}")

    except Exception as e:
        logger.error(f"Error in basic usage example: {str(e)}")

    finally:
        # Clean up
        await splitter.close()
        logger.info("ReadWriteSplitter closed")


async def example_error_handling():
    """Example demonstrating error handling scenarios."""
    logger.info("\n=== Error Handling Example ===")

    # Configure with invalid replica (for demonstration)
    primary_config = DatabaseConfig(
        host="localhost",
        port=5432,
        name="connect_db",
        user="connect_user",
        password="connect_password",
    )

    # Invalid replica configuration
    replica_configs = [
        DatabaseConfig(
            host="invalid-replica.example.com",
            port=5432,
            name="connect_db",
            user="invalid_user",
            password="invalid_password",
        )
    ]

    splitter = ReadWriteSplitter(
        primary_config=primary_config,
        replica_configs=replica_configs,
        fallback_to_primary=True,  # Enable fallback for demonstration
    )

    try:
        await splitter.initialize()

        # This should fallback to primary when replica is unavailable
        logger.info("Attempting read operation with unavailable replica...")
        async with splitter.get_connection(read_only=True) as conn:
            result = await conn.fetchrow("SELECT 'Fallback to primary' as message")
            logger.info(f"Result: {result['message']}")

    except NoAvailableReplicasError as e:
        logger.error(f"No replicas available: {e}")
    except PrimaryDatabaseUnavailableError as e:
        logger.error(f"Primary database unavailable: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")

    finally:
        await splitter.close()


async def example_config_file_usage():
    """Example using configuration from YAML file."""
    logger.info("\n=== Configuration File Usage Example ===")

    try:
        # Load configuration from file
        # Note: This assumes you have a valid database_read_write_example.yaml file
        config = load_config("config/database_read_write_example.yaml")

        if config.read_write and config.read_write.enabled:
            logger.info("Read-write splitting is enabled in configuration")

            # Create splitter from configuration
            splitter = ReadWriteSplitter(
                primary_config=config.read_write.primary,
                replica_configs=config.read_write.replicas,
                load_balancing_strategy=LoadBalancingStrategy(
                    config.read_write.load_balancing_strategy
                ),
                fallback_to_primary=config.read_write.fallback_to_primary,
                health_check_interval=config.read_write.health_check_interval,
            )

            await splitter.initialize()
            logger.info("ReadWriteSplitter initialized from configuration file")

            # Use the splitter...
            health_status = await splitter.get_health_status()
            logger.info(
                f"Configuration-based splitter health: {health_status['summary']}"
            )

            await splitter.close()
        else:
            logger.info("Read-write splitting is not enabled in configuration")

    except FileNotFoundError:
        logger.warning("Configuration file not found, using default configuration")
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")


async def example_different_strategies():
    """Example demonstrating different load balancing strategies."""
    logger.info("\n=== Load Balancing Strategies Example ===")

    primary_config = DatabaseConfig(
        host="localhost",
        port=5432,
        name="connect_db",
        user="connect_user",
        password="connect_password",
    )

    replica_configs = [
        DatabaseConfig(
            host="replica1.example.com",
            port=5432,
            name="connect_db",
            user="readonly",
            password="pass",
        ),
        DatabaseConfig(
            host="replica2.example.com",
            port=5432,
            name="connect_db",
            user="readonly",
            password="pass",
        ),
        DatabaseConfig(
            host="replica3.example.com",
            port=5432,
            name="connect_db",
            user="readonly",
            password="pass",
        ),
    ]

    strategies = [
        LoadBalancingStrategy.ROUND_ROBIN,
        LoadBalancingStrategy.LEAST_CONNECTIONS,
        LoadBalancingStrategy.RANDOM,
    ]

    for strategy in strategies:
        logger.info(f"\n--- Testing {strategy.value} strategy ---")

        splitter = ReadWriteSplitter(
            primary_config=primary_config,
            replica_configs=replica_configs,
            load_balancing_strategy=strategy,
            fallback_to_primary=True,
        )

        try:
            await splitter.initialize()

            # Simulate multiple read operations
            for i in range(3):
                try:
                    connection, pool = await splitter.acquire_connection(read_only=True)
                    logger.info(f"Connection {i+1} acquired using {strategy.value}")
                    await pool.release_connection(connection)
                except Exception as e:
                    logger.warning(f"Connection {i+1} failed: {e}")

            stats = splitter.get_stats()
            logger.info(f"Strategy: {stats['load_balancing_strategy']}")
            logger.info(f"Connection counts: {stats['replica_connection_counts']}")

        except Exception as e:
            logger.error(f"Error with {strategy.value} strategy: {e}")

        finally:
            await splitter.close()


async def main():
    """Main function to run all examples."""
    logger.info("Starting ReadWriteSplitter examples...")

    # Run examples
    await example_basic_usage()
    await example_error_handling()
    await example_config_file_usage()
    await example_different_strategies()

    logger.info("\nAll examples completed!")


if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
