"""Spatial Indexing Module for Telecommunications Data

This module provides spatial indexing capabilities for efficient spatial queries
on telecommunications data including R-tree indexing and spatial hashing.
"""

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

import logging
import numpy as np
import pandas as pd
import geopandas as gpd
from typing import Dict, List, Optional, Tuple, Union, Any
from shapely.geometry import Point, Polygon, box
from shapely.strtree import STRtree
import hashlib

logger = logging.getLogger(__name__)


class SpatialIndexer:
    """Spatial indexer for telecommunications data with optimized query performance"""
    
    def __init__(self, crs: str = "EPSG:4326"):
        """Initialize spatial indexer
        
        Args:
            crs: Coordinate reference system for spatial operations
        """
        self.crs = crs
        self.logger = logging.getLogger(self.__class__.__name__)
        self._spatial_index = None
        self._geometries = []
        self._data_mapping = {}
        self._geohash_index = {}
        
    def build_index(self, data: gpd.GeoDataFrame, id_column: str = None) -> None:
        """Build spatial index from GeoDataFrame
        
        Args:
            data: GeoDataFrame containing geometries to index
            id_column: Column name to use as unique identifier
        """
        try:
            if data.empty:
                self.logger.warning("Cannot build index from empty data")
                return
            
            # Prepare geometries and mapping
            self._geometries = data.geometry.tolist()
            
            # Create ID mapping
            if id_column and id_column in data.columns:
                ids = data[id_column].tolist()
            else:
                ids = list(range(len(data)))
            
            # Build data mapping
            for i, (geom, data_id) in enumerate(zip(self._geometries, ids)):
                self._data_mapping[i] = {
                    'id': data_id,
                    'geometry': geom,
                    'data_index': i
                }
            
            # Build R-tree spatial index
            self._spatial_index = STRtree(self._geometries)
            
            # Build geohash index for telecommunications optimization
            self._build_geohash_index(data)
            
            self.logger.info(f"Built spatial index for {len(self._geometries)} geometries")
            
        except Exception as e:
            self.logger.error(f"Failed to build spatial index: {e}")
            raise
    
    def _build_geohash_index(self, data: gpd.GeoDataFrame, precision: int = 8) -> None:
        """Build geohash index for fast approximate spatial queries
        
        Args:
            data: GeoDataFrame to index
            precision: Geohash precision level
        """
        try:
            self._geohash_index = {}
            
            for idx, row in data.iterrows():
                geom = row.geometry
                if geom and hasattr(geom, 'x') and hasattr(geom, 'y'):
                    # For points, use direct coordinates
                    lat, lon = geom.y, geom.x
                elif geom and hasattr(geom, 'centroid'):
                    # For polygons, use centroid
                    centroid = geom.centroid
                    lat, lon = centroid.y, centroid.x
                else:
                    continue
                
                # Generate geohash
                geohash = self._generate_geohash(lat, lon, precision)
                
                if geohash not in self._geohash_index:
                    self._geohash_index[geohash] = []
                
                self._geohash_index[geohash].append({
                    'data_index': idx,
                    'geometry': geom,
                    'coordinates': (lat, lon)
                })
            
            self.logger.debug(f"Built geohash index with {len(self._geohash_index)} cells")
            
        except Exception as e:
            self.logger.error(f"Failed to build geohash index: {e}")
    
    def _generate_geohash(self, lat: float, lon: float, precision: int = 8) -> str:
        """Generate geohash for coordinates
        
        Args:
            lat: Latitude
            lon: Longitude
            precision: Hash precision
            
        Returns:
            Geohash string
        """
        # Simple geohash implementation for telecommunications data
        coord_str = f"{lat:.6f},{lon:.6f}"
        hash_obj = hashlib.md5(coord_str.encode())
        return hash_obj.hexdigest()[:precision]
    
    def query_point(self, point: Point, buffer_distance: float = 0.001) -> List[Dict[str, Any]]:
        """Query spatial index for geometries near a point
        
        Args:
            point: Query point
            buffer_distance: Buffer distance around point
            
        Returns:
            List of matching geometries with metadata
        """
        try:
            if not self._spatial_index:
                self.logger.warning("Spatial index not built")
                return []
            
            # Create buffer around point
            query_geom = point.buffer(buffer_distance)
            
            # Query spatial index
            possible_matches = list(self._spatial_index.query(query_geom))
            
            # Filter for actual intersections
            results = []
            for geom in possible_matches:
                try:
                    geom_idx = self._geometries.index(geom)
                    if geom_idx in self._data_mapping:
                        if geom.intersects(query_geom):
                            results.append(self._data_mapping[geom_idx])
                except ValueError:
                    # Geometry not found in list, skip
                    continue
            
            self.logger.debug(f"Point query returned {len(results)} results")
            return results
            
        except Exception as e:
            self.logger.error(f"Point query failed: {e}")
            return []
    
    def query_bbox(self, bbox: Tuple[float, float, float, float]) -> List[Dict[str, Any]]:
        """Query spatial index for geometries within bounding box
        
        Args:
            bbox: Bounding box as (min_x, min_y, max_x, max_y)
            
        Returns:
            List of matching geometries with metadata
        """
        try:
            if not self._spatial_index:
                self.logger.warning("Spatial index not built")
                return []
            
            # Create bounding box geometry
            query_geom = box(*bbox)
            
            # Query spatial index
            possible_matches = list(self._spatial_index.query(query_geom))
            
            # Filter for actual intersections
            results = []
            for geom in possible_matches:
                try:
                    geom_idx = self._geometries.index(geom)
                    if geom_idx in self._data_mapping:
                        if geom.intersects(query_geom):
                            results.append(self._data_mapping[geom_idx])
                except ValueError:
                    # Geometry not found in list, skip
                    continue
            
            self.logger.debug(f"Bounding box query returned {len(results)} results")
            return results
            
        except Exception as e:
            self.logger.error(f"Bounding box query failed: {e}")
            return []
    
    def query_polygon(self, polygon: Polygon) -> List[Dict[str, Any]]:
        """Query spatial index for geometries within polygon
        
        Args:
            polygon: Query polygon
            
        Returns:
            List of matching geometries with metadata
        """
        try:
            if not self._spatial_index:
                self.logger.warning("Spatial index not built")
                return []
            
            # Query spatial index
            possible_matches = list(self._spatial_index.query(polygon))
            
            # Filter for actual intersections
            results = []
            for geom in possible_matches:
                try:
                    geom_idx = self._geometries.index(geom)
                    if geom_idx in self._data_mapping:
                        if geom.intersects(polygon):
                            results.append(self._data_mapping[geom_idx])
                except ValueError:
                    # Geometry not found in list, skip
                    continue
            
            self.logger.debug(f"Polygon query returned {len(results)} results")
            return results
            
        except Exception as e:
            self.logger.error(f"Polygon query failed: {e}")
            return []
    
    def nearest_neighbors(self, point: Point, k: int = 5, max_distance: float = None) -> List[Dict[str, Any]]:
        """Find k nearest neighbors to a point
        
        Args:
            point: Query point
            k: Number of neighbors to find
            max_distance: Maximum search distance
            
        Returns:
            List of nearest neighbors with distances
        """
        try:
            if not self._spatial_index:
                self.logger.warning("Spatial index not built")
                return []
            
            # Calculate distances to all geometries
            distances = []
            for i, geom in enumerate(self._geometries):
                if i in self._data_mapping:
                    distance = point.distance(geom)
                    if max_distance is None or distance <= max_distance:
                        distances.append((distance, i))
            
            # Sort by distance and take k nearest
            distances.sort(key=lambda x: x[0])
            nearest = distances[:k]
            
            # Build results with distance information
            results = []
            for distance, geom_idx in nearest:
                result = self._data_mapping[geom_idx].copy()
                result['distance'] = distance
                results.append(result)
            
            self.logger.debug(f"Found {len(results)} nearest neighbors")
            return results
            
        except Exception as e:
            self.logger.error(f"Nearest neighbor query failed: {e}")
            return []
    
    def query_by_geohash(self, lat: float, lon: float, precision: int = 8) -> List[Dict[str, Any]]:
        """Fast approximate spatial query using geohash
        
        Args:
            lat: Latitude
            lon: Longitude
            precision: Geohash precision
            
        Returns:
            List of geometries in the same geohash cell
        """
        try:
            if not self._geohash_index:
                self.logger.warning("Geohash index not built")
                return []
            
            # Generate geohash for query point
            geohash = self._generate_geohash(lat, lon, precision)
            
            # Return all geometries in the same geohash cell
            results = self._geohash_index.get(geohash, [])
            
            self.logger.debug(f"Geohash query returned {len(results)} results")
            return results
            
        except Exception as e:
            self.logger.error(f"Geohash query failed: {e}")
            return []
    
    def get_index_stats(self) -> Dict[str, Any]:
        """Get spatial index statistics
        
        Returns:
            Dictionary containing index statistics
        """
        return {
            'total_geometries': len(self._geometries),
            'index_built': self._spatial_index is not None,
            'geohash_cells': len(self._geohash_index),
            'memory_usage_estimate': len(self._geometries) * 64,  # Rough estimate in bytes
        }
    
    def clear_index(self) -> None:
        """Clear spatial index and free memory"""
        self._spatial_index = None
        self._geometries.clear()
        self._data_mapping.clear()
        self._geohash_index.clear()
        self.logger.info("Spatial index cleared")
    
    def rebuild_index(self, data: gpd.GeoDataFrame, id_column: str = None) -> None:
        """Rebuild spatial index with new data
        
        Args:
            data: New GeoDataFrame to index
            id_column: Column name to use as unique identifier
        """
        self.clear_index()
        self.build_index(data, id_column)
        self.logger.info("Spatial index rebuilt")
