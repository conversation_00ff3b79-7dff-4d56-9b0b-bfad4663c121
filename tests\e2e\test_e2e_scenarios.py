"""End-to-end test scenarios for database framework.

This module contains comprehensive end-to-end tests that validate complete
business workflows and user scenarios as specified in Task 22.
Integrates with the E2E testing framework for structured scenario execution.
"""

import asyncio
import os
import tempfile
import time
import json
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch
import logging

import numpy as np
import pandas as pd
import pytest
from shapely.geometry import Point, Polygon
from sqlalchemy import text

from src.config.models import DatabaseConfig
from src.database.connection.session import SessionManager
from src.database.connection.pool import DatabasePoolManager
from src.database.operations.crud import CRUDOperations
from src.database.operations.importer import DataImporter
from src.database.operations.exporter import DataExporter
from src.database.schema.manager import SchemaManager
from src.database.geospatial.processor import GeospatialProcessor
from src.database.monitoring.logger import DatabaseLogger

# Import E2E framework
from tests.e2e.test_e2e_framework import (
    E2ETestFramework,
    E2ETestStep,
    E2ETestScenario,
    create_test_step,
    create_e2e_scenario,
    e2e_framework
)

# Import test tools
from tests.tools.data_generator import TestDataGenerator
from tests.fixtures.enhanced_test_data import EnhancedTestDataGenerator
from tests.tools.performance_monitor import PerformanceMonitor

logger = logging.getLogger(__name__)


@pytest.mark.e2e
class TestCompleteDataWorkflow:
    """End-to-end tests for complete data processing workflows."""

    @pytest.fixture
    async def e2e_system(self, test_config):
        """Set up complete system for end-to-end testing."""
        # Initialize all components
        config = test_config  # Use full config, not just database config
        pool_manager = DatabasePoolManager(config)
        session_manager = SessionManager(config)  # Pass config, not pool_manager
        crud_manager = CRUDOperations(session_manager)
        schema_manager = SchemaManager(session_manager)
        data_importer = DataImporter(session_manager)  # Pass session_manager directly
        data_exporter = DataExporter(crud_manager)
        geospatial_processor = GeospatialProcessor()
        logger = DatabaseLogger("test_database")
        
        system = {
            'config': config,
            'pool_manager': pool_manager,
            'session_manager': session_manager,
            'crud_manager': crud_manager,
            'schema_manager': schema_manager,
            'data_importer': data_importer,
            'data_exporter': data_exporter,
            'geospatial_processor': geospatial_processor,
            'logger': logger
        }
        
        yield system
        
        # Cleanup
        await pool_manager.close()

    @pytest.mark.asyncio
    async def test_complete_ep_data_workflow(self, e2e_system, tmp_path):
        """Test complete EP data processing workflow."""
        # Create test EP data file
        ep_data = pd.DataFrame({
            'cell_id': ['CELL_001', 'CELL_002', 'CELL_003'],
            'site_name': ['Site_A', 'Site_B', 'Site_C'],
            'latitude': [52.5200, 52.5300, 52.5400],
            'longitude': [13.4050, 13.4150, 13.4250],
            'technology': ['LTE', 'LTE', '5G'],
            'frequency_band': ['B20', 'B3', 'n78'],
            'power_dbm': [43.0, 43.5, 44.0],
            'azimuth': [90, 180, 270],
            'tilt': [3, 4, 5],
            'antenna_height': [30, 35, 40]
        })
        
        ep_file = tmp_path / "test_ep_data.csv"
        ep_data.to_csv(ep_file, index=False)

        # Clean up any existing data from previous test runs
        try:
            await e2e_system['crud_manager'].execute_query("DROP TABLE IF EXISTS ep_to2.ep_sites")
        except Exception:
            pass  # Table might not exist

        # Step 1: Import EP data
        import_result = await e2e_system['data_importer'].import_ep_data(
            str(ep_file), table_name="ep_sites"
        )
        assert import_result.status.value in ['completed', 'success']
        # EP config has skip_rows=1, so we expect len(ep_data) - 1 records (excluding header)
        expected_records = len(ep_data) - 1  # Skip header row
        assert import_result.metrics.records_processed == expected_records
        
        # Step 2: Validate data integrity
        imported_data = await e2e_system['crud_manager'].read("ep_sites", schema="ep_to2")
        assert len(imported_data) == expected_records
        
        # Step 3: Perform geospatial analysis
        site_points = []
        for _, row in imported_data.iterrows():
            point = Point(row['longitude'], row['latitude'])
            site_points.append({
                'cell_id': row['cell_id'],
                'geometry': point,
                'technology': row['technology']
            })
        
        # Calculate distances between sites
        distances = []
        for i, site1 in enumerate(site_points):
            for j, site2 in enumerate(site_points[i+1:], i+1):
                distance = e2e_system['geospatial_processor'].calculate_distance(
                    site1['geometry'], site2['geometry']
                )
                distances.append({
                    'site1': site1['cell_id'],
                    'site2': site2['cell_id'],
                    'distance_m': distance
                })
        
        assert len(distances) == 3  # 3 sites = 3 pairs
        assert all(d['distance_m'] > 0 for d in distances)
        
        # Step 4: Create coverage analysis
        coverage_areas = []
        for site in site_points:
            # Create 1km buffer around each site
            buffer = e2e_system['geospatial_processor'].create_buffer(
                site['geometry'], 1000
            )
            coverage_areas.append({
                'cell_id': site['cell_id'],
                'coverage_area': buffer,
                'technology': site['technology']
            })
        
        assert len(coverage_areas) == len(site_points)
        
        # Step 5: Export processed data
        export_file = tmp_path / "processed_ep_data.csv"
        export_result = await e2e_system['data_exporter'].export_to_csv(
            "ep_sites", str(export_file)
        )
        assert export_result.success
        
        # Verify exported file
        exported_data = pd.read_csv(export_file)
        assert len(exported_data) == len(ep_data)
        
        # Step 6: Log workflow completion
        e2e_system['logger'].info(
            f"EP data workflow completed: {len(ep_data)} sites processed"
        )

    @pytest.mark.asyncio
    async def test_complete_cdr_data_workflow(self, e2e_system, tmp_path):
        """Test complete CDR data processing workflow."""
        # Create test CDR data
        cdr_data = pd.DataFrame({
            'call_id': ['CALL_001', 'CALL_002', 'CALL_003', 'CALL_004'],
            'msisdn': ['491701234567', '491701234568', '491701234569', '491701234570'],
            'cell_id': ['CELL_001', 'CELL_002', 'CELL_001', 'CELL_003'],
            'start_time': pd.to_datetime([
                '2024-01-01 10:00:00', '2024-01-01 10:05:00',
                '2024-01-01 10:10:00', '2024-01-01 10:15:00'
            ]),
            'end_time': pd.to_datetime([
                '2024-01-01 10:03:00', '2024-01-01 10:08:00',
                '2024-01-01 10:12:00', '2024-01-01 10:18:00'
            ]),
            'call_type': ['voice', 'data', 'voice', 'data'],
            'data_volume_mb': [0, 15.5, 0, 8.2],
            'latitude': [52.5200, 52.5300, 52.5200, 52.5400],
            'longitude': [13.4050, 13.4150, 13.4050, 13.4250]
        })
        
        cdr_file = tmp_path / "test_cdr_data.csv"
        cdr_data.to_csv(cdr_file, index=False)
        
        # Step 1: Import CDR data
        import_result = await e2e_system['data_importer'].import_cdr_data(
            str(cdr_file), table_name="cdr_records"
        )
        assert import_result.status.value in ['completed', 'success']
        # CDR config has skip_rows=2, so we expect len(cdr_data) - 2 records (excluding header rows)
        expected_records = len(cdr_data) - 2  # Skip header rows
        assert import_result.metrics.records_processed == expected_records
        
        # Step 2: Validate data quality
        imported_cdr = await e2e_system['crud_manager'].read("cdr_records", schema="cdr_to2")
        assert len(imported_cdr) == expected_records
        
        # Check data types and constraints
        for _, record in imported_cdr.iterrows():
            assert record['start_time'] < record['end_time']
            assert record['latitude'] >= -90 and record['latitude'] <= 90
            assert record['longitude'] >= -180 and record['longitude'] <= 180
        
        # Step 3: Perform traffic analysis
        traffic_analysis = {
            'total_calls': len(imported_cdr),
            'voice_calls': len(imported_cdr[imported_cdr['call_type'] == 'voice']),
            'data_sessions': len(imported_cdr[imported_cdr['call_type'] == 'data']),
            'total_data_mb': imported_cdr['data_volume_mb'].sum(),
            'avg_call_duration': (imported_cdr['end_time'] - imported_cdr['start_time']).mean()
        }
        
        assert traffic_analysis['total_calls'] == 4
        assert traffic_analysis['voice_calls'] == 2
        assert traffic_analysis['data_sessions'] == 2
        assert traffic_analysis['total_data_mb'] > 0
        
        # Step 4: Geospatial analysis of call locations
        call_locations = []
        for _, record in imported_cdr.iterrows():
            point = Point(record['longitude'], record['latitude'])
            call_locations.append({
                'call_id': record['call_id'],
                'location': point,
                'cell_id': record['cell_id']
            })
        
        # Find calls in same location
        same_location_calls = {}
        for call in call_locations:
            location_key = f"{call['location'].x:.4f},{call['location'].y:.4f}"
            if location_key not in same_location_calls:
                same_location_calls[location_key] = []
            same_location_calls[location_key].append(call['call_id'])
        
        # Step 5: Cell utilization analysis
        cell_utilization = imported_cdr.groupby('cell_id').agg({
            'call_id': 'count',
            'data_volume_mb': 'sum',
            'start_time': 'min',
            'end_time': 'max'
        }).rename(columns={'call_id': 'call_count'})
        
        assert len(cell_utilization) <= len(imported_cdr['cell_id'].unique())
        
        # Step 6: Export analysis results
        analysis_file = tmp_path / "cdr_analysis.csv"
        cell_utilization.to_csv(analysis_file)
        
        # Verify analysis export
        assert analysis_file.exists()
        analysis_data = pd.read_csv(analysis_file)
        assert len(analysis_data) > 0

    @pytest.mark.asyncio
    async def test_multi_user_concurrent_workflow(self, e2e_system, tmp_path):
        """Test concurrent multi-user workflows."""
        num_users = 5
        operations_per_user = 10
        
        async def user_workflow(user_id: int):
            """Simulate a user workflow."""
            results = []
            
            for operation in range(operations_per_user):
                try:
                    # Create user-specific test data
                    user_data = pd.DataFrame({
                        'id': [f"user_{user_id}_record_{operation}"],
                        'user_id': [user_id],
                        'operation': [operation],
                        'timestamp': [pd.Timestamp.now()],
                        'data': [f"test_data_{user_id}_{operation}"]
                    })
                    
                    # Insert data
                    table_name = f"user_{user_id}_data"
                    insert_result = await e2e_system['crud_manager'].bulk_insert(
                        table_name, user_data
                    )
                    
                    # Query data
                    query_result = await e2e_system['crud_manager'].select_all(table_name)
                    
                    # Update data
                    update_query = f"UPDATE {table_name} SET data = data || '_updated' WHERE operation = {operation}"
                    update_result = await e2e_system['crud_manager'].execute(update_query)
                    
                    results.append({
                        'user_id': user_id,
                        'operation': operation,
                        'insert_success': insert_result.success,
                        'query_records': len(query_result),
                        'update_success': update_result is not None
                    })
                    
                except Exception as e:
                    results.append({
                        'user_id': user_id,
                        'operation': operation,
                        'error': str(e)
                    })
            
            return results
        
        # Execute concurrent user workflows
        start_time = time.time()
        
        tasks = [user_workflow(user_id) for user_id in range(num_users)]
        user_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results
        successful_users = 0
        total_operations = 0
        successful_operations = 0
        
        for user_id, results in enumerate(user_results):
            if isinstance(results, Exception):
                continue
                
            user_successful = True
            for result in results:
                total_operations += 1
                if 'error' not in result:
                    successful_operations += 1
                else:
                    user_successful = False
            
            if user_successful:
                successful_users += 1
        
        # Performance assertions
        success_rate = successful_operations / total_operations if total_operations > 0 else 0
        operations_per_second = total_operations / total_time
        
        assert success_rate >= 0.95, f"Success rate too low: {success_rate:.2%}"
        assert successful_users >= num_users * 0.8, f"Too many user failures: {successful_users}/{num_users}"
        assert operations_per_second >= 10, f"Operations per second too low: {operations_per_second:.2f}"
        
        print(f"Multi-user workflow results:")
        print(f"  Users: {num_users}")
        print(f"  Total operations: {total_operations}")
        print(f"  Successful operations: {successful_operations}")
        print(f"  Success rate: {success_rate:.2%}")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Operations per second: {operations_per_second:.2f}")

    @pytest.mark.asyncio
    async def test_data_pipeline_integrity(self, e2e_system, tmp_path):
        """Test end-to-end data pipeline integrity."""
        # Step 1: Create source data with known characteristics
        source_data = pd.DataFrame({
            'id': range(1000),
            'value': np.random.uniform(0, 100, 1000),
            'category': np.random.choice(['A', 'B', 'C'], 1000),
            'timestamp': pd.date_range('2024-01-01', periods=1000, freq='1min')
        })
        
        # Calculate source statistics
        source_stats = {
            'count': len(source_data),
            'value_sum': source_data['value'].sum(),
            'value_mean': source_data['value'].mean(),
            'categories': source_data['category'].value_counts().to_dict(),
            'date_range': (source_data['timestamp'].min(), source_data['timestamp'].max())
        }
        
        source_file = tmp_path / "source_data.csv"
        source_data.to_csv(source_file, index=False)
        
        # Step 2: Import data through pipeline
        import_result = await e2e_system['data_importer'].import_csv_data(
            str(source_file), table_name="pipeline_test"
        )
        assert import_result.status.value in ['completed', 'success']
        assert import_result.metrics.records_processed == len(source_data)
        
        # Step 3: Verify data integrity after import
        imported_data = await e2e_system['crud_manager'].select_all("pipeline_test")
        
        imported_stats = {
            'count': len(imported_data),
            'value_sum': imported_data['value'].sum(),
            'value_mean': imported_data['value'].mean(),
            'categories': imported_data['category'].value_counts().to_dict(),
            'date_range': (imported_data['timestamp'].min(), imported_data['timestamp'].max())
        }
        
        # Verify data integrity
        assert imported_stats['count'] == source_stats['count']
        assert abs(imported_stats['value_sum'] - source_stats['value_sum']) < 0.01
        assert abs(imported_stats['value_mean'] - source_stats['value_mean']) < 0.01
        assert imported_stats['categories'] == source_stats['categories']
        
        # Step 4: Perform data transformations
        transform_query = """
        UPDATE pipeline_test 
        SET value = value * 2 
        WHERE category = 'A'
        """
        
        transform_result = await e2e_system['crud_manager'].execute(transform_query)
        assert transform_result is not None
        
        # Step 5: Verify transformation integrity
        transformed_data = await e2e_system['crud_manager'].select_all("pipeline_test")
        
        # Check that category A values were doubled
        category_a_data = transformed_data[transformed_data['category'] == 'A']
        original_a_data = source_data[source_data['category'] == 'A']
        
        for idx, (_, transformed_row) in enumerate(category_a_data.iterrows()):
            original_row = original_a_data.iloc[idx]
            expected_value = original_row['value'] * 2
            assert abs(transformed_row['value'] - expected_value) < 0.01
        
        # Step 6: Export and verify final data
        export_file = tmp_path / "final_data.csv"
        export_result = await e2e_system['data_exporter'].export_to_csv(
            "pipeline_test", str(export_file)
        )
        assert export_result.success
        
        # Verify exported data matches database
        exported_data = pd.read_csv(export_file)
        assert len(exported_data) == len(transformed_data)
        
        # Step 7: Cleanup and verify
        cleanup_result = await e2e_system['crud_manager'].execute(
            "DROP TABLE IF EXISTS pipeline_test"
        )
        assert cleanup_result is not None

    @pytest.mark.asyncio
    async def test_system_recovery_workflow(self, e2e_system, tmp_path):
        """Test system recovery and error handling workflows."""
        # Step 1: Normal operation
        test_data = pd.DataFrame({
            'id': range(100),
            'data': [f"record_{i}" for i in range(100)]
        })
        
        normal_result = await e2e_system['crud_manager'].bulk_insert(
            "recovery_test", test_data
        )
        assert normal_result.success
        
        # Step 2: Simulate connection failure and recovery
        with patch.object(e2e_system['session_manager'], 'get_session') as mock_session:
            # First call fails
            mock_session.side_effect = [Exception("Connection failed"), 
                                      e2e_system['session_manager'].get_session()]
            
            # Should handle failure gracefully
            try:
                failed_result = await e2e_system['crud_manager'].select_all("recovery_test")
                # If it doesn't raise an exception, it should return empty or handle gracefully
            except Exception as e:
                assert "Connection failed" in str(e)
        
        # Step 3: Verify system recovery
        # Reset mock to normal behavior
        recovery_data = await e2e_system['crud_manager'].select_all("recovery_test")
        assert len(recovery_data) == len(test_data)
        
        # Step 4: Test transaction rollback
        try:
            async with e2e_system['crud_manager'].transaction() as tx:
                # Insert some data
                await tx.bulk_insert("recovery_test_temp", test_data[:10])
                
                # Verify data is there (within transaction)
                temp_data = await tx.select_all("recovery_test_temp")
                assert len(temp_data) == 10
                
                # Force rollback
                raise Exception("Intentional rollback")
                
        except Exception as e:
            assert "Intentional rollback" in str(e)
        
        # Step 5: Verify rollback worked
        # Table should not exist or be empty
        try:
            rollback_check = await e2e_system['crud_manager'].select_all("recovery_test_temp")
            # If table exists, it should be empty
            assert len(rollback_check) == 0
        except Exception:
            # Table doesn't exist, which is also correct
            pass
        
        # Step 6: Test graceful degradation
        # Simulate high load scenario
        concurrent_tasks = []
        for i in range(20):
            task_data = pd.DataFrame({
                'id': [i],
                'task_id': [f"task_{i}"],
                'data': [f"concurrent_data_{i}"]
            })
            
            task = e2e_system['crud_manager'].bulk_insert(
                f"concurrent_test_{i}", task_data
            )
            concurrent_tasks.append(task)
        
        # Execute concurrent operations
        results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
        
        # Analyze results - system should handle graceful degradation
        successful_tasks = sum(1 for r in results if not isinstance(r, Exception) and r.success)
        failed_tasks = len(results) - successful_tasks
        
        # Should have high success rate even under load
        success_rate = successful_tasks / len(results)
        assert success_rate >= 0.8, f"Success rate too low under load: {success_rate:.2%}"
        
        print(f"System recovery test results:")
        print(f"  Concurrent tasks: {len(results)}")
        print(f"  Successful: {successful_tasks}")
        print(f"  Failed: {failed_tasks}")
        print(f"  Success rate: {success_rate:.2%}")


@pytest.mark.e2e
class TestBusinessScenarios:
    """End-to-end tests for specific business scenarios."""

    @pytest.mark.asyncio
    async def test_network_coverage_analysis_scenario(self, e2e_system, tmp_path):
        """Test complete network coverage analysis business scenario."""
        # Scenario: Telecom operator wants to analyze network coverage gaps
        
        # Step 1: Import site data
        site_data = pd.DataFrame({
            'site_id': ['SITE_001', 'SITE_002', 'SITE_003', 'SITE_004'],
            'site_name': ['Downtown_A', 'Downtown_B', 'Suburb_A', 'Rural_A'],
            'latitude': [52.5200, 52.5250, 52.4800, 52.4500],
            'longitude': [13.4050, 13.4100, 13.3500, 13.3000],
            'technology': ['LTE', 'LTE', '5G', 'LTE'],
            'coverage_radius_m': [2000, 1800, 1500, 3000],
            'status': ['active', 'active', 'active', 'maintenance']
        })
        
        site_file = tmp_path / "sites.csv"
        site_data.to_csv(site_file, index=False)
        
        import_result = await e2e_system['data_importer'].import_csv_data(
            str(site_file), table_name="network_sites"
        )
        assert import_result.status.value in ['completed', 'success']
        
        # Step 2: Import traffic data
        traffic_data = pd.DataFrame({
            'measurement_id': range(1, 13),
            'site_id': ['SITE_001'] * 4 + ['SITE_002'] * 4 + ['SITE_003'] * 4,
            'timestamp': pd.date_range('2024-01-01 08:00', periods=12, freq='2H'),
            'connected_users': [150, 200, 180, 120, 80, 120, 100, 90, 50, 70, 60, 45],
            'data_throughput_mbps': [45.2, 52.1, 48.7, 38.9, 25.3, 35.7, 28.9, 26.1, 15.8, 18.9, 16.2, 12.4],
            'signal_quality': [85, 88, 86, 82, 78, 81, 79, 77, 72, 75, 73, 70]
        })
        
        traffic_file = tmp_path / "traffic.csv"
        traffic_data.to_csv(traffic_file, index=False)
        
        traffic_import = await e2e_system['data_importer'].import_csv_data(
            str(traffic_file), table_name="traffic_data"
        )
        assert traffic_import.success
        
        # Step 3: Perform coverage analysis
        active_sites = await e2e_system['crud_manager'].execute_query(
            "SELECT * FROM network_sites WHERE status = 'active'"
        )
        assert len(active_sites) == 3
        
        # Calculate coverage areas
        coverage_areas = []
        for _, site in active_sites.iterrows():
            center = Point(site['longitude'], site['latitude'])
            coverage = e2e_system['geospatial_processor'].create_buffer(
                center, site['coverage_radius_m']
            )
            coverage_areas.append({
                'site_id': site['site_id'],
                'coverage_area': coverage,
                'technology': site['technology']
            })
        
        # Step 4: Identify coverage gaps
        # Define area of interest (bounding box)
        area_bounds = {
            'min_lat': 52.4400,
            'max_lat': 52.5300,
            'min_lon': 13.2900,
            'max_lon': 13.4200
        }
        
        # Create grid points for coverage testing
        lat_points = np.linspace(area_bounds['min_lat'], area_bounds['max_lat'], 20)
        lon_points = np.linspace(area_bounds['min_lon'], area_bounds['max_lon'], 20)
        
        coverage_gaps = []
        for lat in lat_points:
            for lon in lon_points:
                test_point = Point(lon, lat)
                covered = False
                
                for coverage in coverage_areas:
                    if coverage['coverage_area'].contains(test_point):
                        covered = True
                        break
                
                if not covered:
                    coverage_gaps.append({'latitude': lat, 'longitude': lon})
        
        # Step 5: Analyze traffic patterns
        traffic_analysis = await e2e_system['crud_manager'].execute_query("""
            SELECT 
                site_id,
                AVG(connected_users) as avg_users,
                MAX(connected_users) as peak_users,
                AVG(data_throughput_mbps) as avg_throughput,
                AVG(signal_quality) as avg_signal_quality
            FROM traffic_data 
            GROUP BY site_id
        """)
        
        assert len(traffic_analysis) == 3
        
        # Step 6: Generate coverage report
        coverage_report = {
            'total_sites': len(site_data),
            'active_sites': len(active_sites),
            'coverage_gaps_count': len(coverage_gaps),
            'technologies': active_sites['technology'].value_counts().to_dict(),
            'traffic_summary': traffic_analysis.to_dict('records'),
            'coverage_percentage': (1 - len(coverage_gaps) / (len(lat_points) * len(lon_points))) * 100
        }
        
        # Assertions for business requirements
        assert coverage_report['coverage_percentage'] > 70  # At least 70% coverage
        assert coverage_report['active_sites'] >= 3  # Minimum active sites
        assert len(coverage_report['traffic_summary']) > 0  # Traffic data available
        
        # Step 7: Export results
        gaps_df = pd.DataFrame(coverage_gaps)
        if len(gaps_df) > 0:
            gaps_file = tmp_path / "coverage_gaps.csv"
            gaps_df.to_csv(gaps_file, index=False)
        
        report_file = tmp_path / "coverage_report.json"
        import json
        with open(report_file, 'w') as f:
            json.dump(coverage_report, f, indent=2, default=str)
        
        print(f"Coverage Analysis Results:")
        print(f"  Coverage percentage: {coverage_report['coverage_percentage']:.1f}%")
        print(f"  Coverage gaps found: {coverage_report['coverage_gaps_count']}")
        print(f"  Active sites: {coverage_report['active_sites']}")

    @pytest.mark.asyncio
    async def test_performance_monitoring_scenario(self, e2e_system, tmp_path):
        """Test performance monitoring and alerting scenario."""
        # Scenario: Monitor system performance and generate alerts
        
        # Step 1: Set up performance monitoring
        monitoring_start = time.time()
        performance_metrics = []
        
        # Step 2: Simulate various operations and collect metrics
        operations = [
            ('bulk_insert', 1000),
            ('select_query', 500),
            ('update_query', 200),
            ('complex_join', 100)
        ]
        
        for operation_type, record_count in operations:
            operation_start = time.time()
            
            if operation_type == 'bulk_insert':
                test_data = pd.DataFrame({
                    'id': range(record_count),
                    'data': [f"test_{i}" for i in range(record_count)]
                })
                result = await e2e_system['crud_manager'].bulk_insert(
                    "performance_test", test_data
                )
                success = result.success
                
            elif operation_type == 'select_query':
                result = await e2e_system['crud_manager'].execute_query(
                    "SELECT * FROM performance_test LIMIT 500"
                )
                success = len(result) > 0
                
            elif operation_type == 'update_query':
                result = await e2e_system['crud_manager'].execute(
                    "UPDATE performance_test SET data = data || '_updated' WHERE id < 200"
                )
                success = result is not None
                
            elif operation_type == 'complex_join':
                # Simulate complex query
                result = await e2e_system['crud_manager'].execute_query(
                    "SELECT COUNT(*) FROM performance_test WHERE id % 10 = 0"
                )
                success = len(result) > 0
            
            operation_end = time.time()
            operation_time = operation_end - operation_start
            
            metrics = {
                'operation': operation_type,
                'record_count': record_count,
                'execution_time': operation_time,
                'records_per_second': record_count / operation_time if operation_time > 0 else 0,
                'success': success,
                'timestamp': pd.Timestamp.now()
            }
            
            performance_metrics.append(metrics)
        
        # Step 3: Analyze performance metrics
        metrics_df = pd.DataFrame(performance_metrics)
        
        # Performance thresholds (business requirements)
        thresholds = {
            'bulk_insert': {'min_rps': 1000, 'max_time': 5.0},
            'select_query': {'min_rps': 500, 'max_time': 2.0},
            'update_query': {'min_rps': 100, 'max_time': 3.0},
            'complex_join': {'min_rps': 50, 'max_time': 4.0}
        }
        
        alerts = []
        for _, metric in metrics_df.iterrows():
            operation = metric['operation']
            if operation in thresholds:
                threshold = thresholds[operation]
                
                if metric['records_per_second'] < threshold['min_rps']:
                    alerts.append({
                        'type': 'performance_degradation',
                        'operation': operation,
                        'metric': 'records_per_second',
                        'value': metric['records_per_second'],
                        'threshold': threshold['min_rps'],
                        'severity': 'warning'
                    })
                
                if metric['execution_time'] > threshold['max_time']:
                    alerts.append({
                        'type': 'slow_operation',
                        'operation': operation,
                        'metric': 'execution_time',
                        'value': metric['execution_time'],
                        'threshold': threshold['max_time'],
                        'severity': 'critical'
                    })
        
        # Step 4: Generate performance report
        monitoring_end = time.time()
        total_monitoring_time = monitoring_end - monitoring_start
        
        performance_report = {
            'monitoring_duration': total_monitoring_time,
            'total_operations': len(performance_metrics),
            'successful_operations': sum(1 for m in performance_metrics if m['success']),
            'alerts_generated': len(alerts),
            'average_performance': metrics_df.groupby('operation').agg({
                'execution_time': 'mean',
                'records_per_second': 'mean'
            }).to_dict(),
            'alerts': alerts
        }
        
        # Step 5: Export monitoring data
        metrics_file = tmp_path / "performance_metrics.csv"
        metrics_df.to_csv(metrics_file, index=False)
        
        if alerts:
            alerts_file = tmp_path / "performance_alerts.json"
            import json
            with open(alerts_file, 'w') as f:
                json.dump(alerts, f, indent=2, default=str)
        
        # Assertions
        success_rate = performance_report['successful_operations'] / performance_report['total_operations']
        assert success_rate >= 0.95, f"Success rate too low: {success_rate:.2%}"
        
        # Should complete monitoring within reasonable time
        assert total_monitoring_time < 30, f"Monitoring took too long: {total_monitoring_time:.2f}s"
        
        print(f"Performance Monitoring Results:")
        print(f"  Total operations: {performance_report['total_operations']}")
        print(f"  Success rate: {success_rate:.2%}")
        print(f"  Alerts generated: {performance_report['alerts_generated']}")
        print(f"  Monitoring duration: {total_monitoring_time:.2f}s")
        
        if alerts:
            print(f"  Alerts:")
            for alert in alerts:
                print(f"    - {alert['type']}: {alert['operation']} {alert['metric']} = {alert['value']:.2f} (threshold: {alert['threshold']})")

    @pytest.mark.asyncio
    async def test_data_quality_validation_scenario(self, e2e_system, tmp_path):
        """Test data quality validation and cleansing scenario."""
        # Scenario: Import data with quality issues and validate/clean it
        
        # Step 1: Create data with various quality issues
        problematic_data = pd.DataFrame({
            'id': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            'msisdn': [
                '491701234567',  # Valid
                '49170123456',   # Too short
                '4917012345678', # Too long
                '491701234567',  # Duplicate
                '',              # Empty
                None,            # Null
                '491701234568',  # Valid
                'invalid_msisdn', # Invalid format
                '491701234569',  # Valid
                '491701234567'   # Duplicate
            ],
            'latitude': [52.5200, 52.5300, 91.0, 52.5400, 52.5500, None, 52.5600, 52.5700, -91.0, 52.5800],
            'longitude': [13.4050, 13.4150, 13.4250, 181.0, 13.4350, 13.4450, None, 13.4550, 13.4650, -181.0],
            'signal_strength': [-50, -60, -200, -70, 0, -80, -90, 150, -100, -110],
            'timestamp': [
                '2024-01-01 10:00:00',
                '2024-01-01 10:05:00',
                'invalid_date',
                '2024-01-01 10:15:00',
                '2024-01-01 10:20:00',
                None,
                '2024-01-01 10:30:00',
                '2024-01-01 10:35:00',
                '2024-01-01 10:40:00',
                '2024-01-01 10:45:00'
            ]
        })
        
        data_file = tmp_path / "problematic_data.csv"
        problematic_data.to_csv(data_file, index=False)
        
        # Step 2: Import data and track quality issues
        quality_issues = []
        
        # Validate MSISDN format
        import re
        msisdn_pattern = re.compile(r'^49\d{10,11}$')
        
        for idx, row in problematic_data.iterrows():
            row_issues = []
            
            # Check MSISDN
            msisdn = row['msisdn']
            if pd.isna(msisdn) or msisdn == '':
                row_issues.append('missing_msisdn')
            elif not msisdn_pattern.match(str(msisdn)):
                row_issues.append('invalid_msisdn_format')
            
            # Check coordinates
            lat, lon = row['latitude'], row['longitude']
            if pd.isna(lat) or pd.isna(lon):
                row_issues.append('missing_coordinates')
            elif not (-90 <= lat <= 90) or not (-180 <= lon <= 180):
                row_issues.append('invalid_coordinates')
            
            # Check signal strength
            signal = row['signal_strength']
            if pd.isna(signal) or not (-150 <= signal <= 0):
                row_issues.append('invalid_signal_strength')
            
            # Check timestamp
            timestamp = row['timestamp']
            if pd.isna(timestamp):
                row_issues.append('missing_timestamp')
            else:
                try:
                    pd.to_datetime(timestamp)
                except:
                    row_issues.append('invalid_timestamp_format')
            
            if row_issues:
                quality_issues.append({
                    'row_id': idx,
                    'issues': row_issues,
                    'severity': 'critical' if len(row_issues) > 2 else 'warning'
                })
        
        # Step 3: Clean and validate data
        cleaned_data = problematic_data.copy()
        
        # Remove rows with critical issues
        critical_rows = [issue['row_id'] for issue in quality_issues if issue['severity'] == 'critical']
        cleaned_data = cleaned_data.drop(critical_rows)
        
        # Fix fixable issues
        # Remove duplicates
        cleaned_data = cleaned_data.drop_duplicates(subset=['msisdn'], keep='first')
        
        # Fix coordinate bounds
        cleaned_data.loc[cleaned_data['latitude'] > 90, 'latitude'] = 90
        cleaned_data.loc[cleaned_data['latitude'] < -90, 'latitude'] = -90
        cleaned_data.loc[cleaned_data['longitude'] > 180, 'longitude'] = 180
        cleaned_data.loc[cleaned_data['longitude'] < -180, 'longitude'] = -180
        
        # Fix signal strength bounds
        cleaned_data.loc[cleaned_data['signal_strength'] > 0, 'signal_strength'] = -50
        cleaned_data.loc[cleaned_data['signal_strength'] < -150, 'signal_strength'] = -150
        
        # Step 4: Import cleaned data
        cleaned_file = tmp_path / "cleaned_data.csv"
        cleaned_data.to_csv(cleaned_file, index=False)
        
        import_result = await e2e_system['data_importer'].import_csv_data(
            str(cleaned_file), table_name="quality_validated_data"
        )
        assert import_result.status.value in ['completed', 'success']
        
        # Step 5: Validate imported data quality
        imported_data = await e2e_system['crud_manager'].select_all("quality_validated_data")
        
        # Quality checks on imported data
        quality_metrics = {
            'total_records': len(imported_data),
            'original_records': len(problematic_data),
            'records_removed': len(problematic_data) - len(imported_data),
            'data_completeness': {
                'msisdn': imported_data['msisdn'].notna().sum() / len(imported_data),
                'coordinates': (imported_data['latitude'].notna() & imported_data['longitude'].notna()).sum() / len(imported_data),
                'signal_strength': imported_data['signal_strength'].notna().sum() / len(imported_data)
            },
            'data_validity': {
                'valid_coordinates': ((imported_data['latitude'] >= -90) & (imported_data['latitude'] <= 90) & 
                                    (imported_data['longitude'] >= -180) & (imported_data['longitude'] <= 180)).sum() / len(imported_data),
                'valid_signal_strength': ((imported_data['signal_strength'] >= -150) & (imported_data['signal_strength'] <= 0)).sum() / len(imported_data)
            },
            'duplicates_removed': len(problematic_data) - len(problematic_data.drop_duplicates(subset=['msisdn']))
        }
        
        # Step 6: Generate quality report
        quality_report = {
            'data_quality_metrics': quality_metrics,
            'quality_issues_found': len(quality_issues),
            'critical_issues': len([i for i in quality_issues if i['severity'] == 'critical']),
            'warning_issues': len([i for i in quality_issues if i['severity'] == 'warning']),
            'data_retention_rate': len(imported_data) / len(problematic_data),
            'quality_score': sum(quality_metrics['data_completeness'].values()) / len(quality_metrics['data_completeness'])
        }
        
        # Export quality report
        quality_file = tmp_path / "data_quality_report.json"
        import json
        with open(quality_file, 'w') as f:
            json.dump(quality_report, f, indent=2, default=str)
        
        # Assertions
        assert quality_report['data_retention_rate'] >= 0.5, "Too much data lost during cleaning"
        assert quality_report['quality_score'] >= 0.8, "Data quality score too low"
        assert quality_metrics['data_validity']['valid_coordinates'] == 1.0, "Invalid coordinates remain"
        assert quality_metrics['data_validity']['valid_signal_strength'] == 1.0, "Invalid signal strength values remain"
        
        print(f"Data Quality Validation Results:")
        print(f"  Original records: {quality_metrics['original_records']}")
        print(f"  Final records: {quality_metrics['total_records']}")
        print(f"  Data retention rate: {quality_report['data_retention_rate']:.2%}")
        print(f"  Quality score: {quality_report['quality_score']:.2%}")
        print(f"  Issues found: {quality_report['quality_issues_found']} (Critical: {quality_report['critical_issues']}, Warning: {quality_report['warning_issues']})")