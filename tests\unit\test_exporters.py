"""Unit tests for data exporter components."""

import json
import os
import tempfile
from datetime import date, datetime
from pathlib import Path
from typing import Any, Dict, List, Optional
from unittest.mock import MagicMock, Mock, mock_open, patch

import numpy as np
import pandas as pd
import pytest

# Import components to test
from src.exporters.base import BaseExporter, ExportError, ExportResult
from src.exporters.csv_exporter import CSVExporter
from src.exporters.data_formatter import DataFormatter
from src.exporters.excel_exporter import ExcelExporter
from src.exporters.file_manager import FileManager
from src.exporters.geojson_exporter import GeoJSONExporter
from src.exporters.json_exporter import JSONExporter
from src.exporters.report_generator import ReportGenerator


class TestBaseExporter:
    """Test cases for BaseExporter class."""

    @pytest.fixture
    def base_exporter(self):
        """Create BaseExporter instance."""
        return BaseExporter()

    @pytest.fixture
    def sample_dataframe(self):
        """Create sample DataFrame for testing."""
        return pd.DataFrame(
            {
                "id": [1, 2, 3, 4, 5],
                "name": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"],
                "value": [10.5, 20.3, 15.7, 30.2, 25.8],
                "date": pd.to_datetime(
                    [
                        "2023-01-01",
                        "2023-01-02",
                        "2023-01-03",
                        "2023-01-04",
                        "2023-01-05",
                    ]
                ),
                "category": ["A", "B", "A", "C", "B"],
            }
        )

    def test_exporter_initialization(self, base_exporter):
        """Test exporter initialization."""
        assert base_exporter.name == "BaseExporter"
        assert base_exporter.supported_formats == []
        assert base_exporter.default_options == {}
        assert base_exporter.stats == {
            "total_records": 0,
            "exported_records": 0,
            "failed_records": 0,
            "start_time": None,
            "end_time": None,
            "file_size": 0,
        }

    def test_validate_data_not_implemented(self, base_exporter, sample_dataframe):
        """Test that validate_data raises NotImplementedError."""
        with pytest.raises(NotImplementedError):
            base_exporter.validate_data(sample_dataframe)

    def test_export_data_not_implemented(self, base_exporter, sample_dataframe):
        """Test that export_data raises NotImplementedError."""
        with pytest.raises(NotImplementedError):
            base_exporter.export_data(sample_dataframe, "output.csv")

    def test_format_data_not_implemented(self, base_exporter, sample_dataframe):
        """Test that format_data raises NotImplementedError."""
        with pytest.raises(NotImplementedError):
            base_exporter.format_data(sample_dataframe)

    def test_reset_stats(self, base_exporter):
        """Test statistics reset functionality."""
        # Modify stats
        base_exporter.stats["total_records"] = 100
        base_exporter.stats["exported_records"] = 80
        base_exporter.stats["failed_records"] = 20
        base_exporter.stats["start_time"] = datetime.now()

        # Reset stats
        base_exporter.reset_stats()

        assert base_exporter.stats["total_records"] == 0
        assert base_exporter.stats["exported_records"] == 0
        assert base_exporter.stats["failed_records"] == 0
        assert base_exporter.stats["start_time"] is None
        assert base_exporter.stats["end_time"] is None
        assert base_exporter.stats["file_size"] == 0

    def test_update_stats(self, base_exporter):
        """Test statistics update functionality."""
        base_exporter.update_stats(total=100, exported=80, failed=20, file_size=1024)

        assert base_exporter.stats["total_records"] == 100
        assert base_exporter.stats["exported_records"] == 80
        assert base_exporter.stats["failed_records"] == 20
        assert base_exporter.stats["file_size"] == 1024

    def test_get_export_summary(self, base_exporter):
        """Test export summary generation."""
        start_time = datetime.now()
        base_exporter.stats.update(
            {
                "total_records": 100,
                "exported_records": 85,
                "failed_records": 15,
                "start_time": start_time,
                "end_time": datetime.now(),
                "file_size": 2048,
            }
        )

        summary = base_exporter.get_export_summary()

        assert summary["total_records"] == 100
        assert summary["exported_records"] == 85
        assert summary["failed_records"] == 15
        assert summary["success_rate"] == 85.0
        assert summary["file_size"] == 2048
        assert "duration" in summary
        assert summary["status"] == "completed_with_errors"

    def test_configure_exporter(self, base_exporter):
        """Test exporter configuration."""
        config = {
            "chunk_size": 1000,
            "compression": True,
            "encoding": "utf-8",
            "date_format": "%Y-%m-%d",
        }

        base_exporter.configure(config)

        assert base_exporter.chunk_size == 1000
        assert base_exporter.compression is True
        assert base_exporter.encoding == "utf-8"
        assert base_exporter.date_format == "%Y-%m-%d"

    def test_validate_output_path(self, base_exporter):
        """Test output path validation."""
        # Valid path
        with patch("pathlib.Path.parent.exists", return_value=True):
            with patch("pathlib.Path.parent.is_dir", return_value=True):
                is_valid, error = base_exporter.validate_output_path(
                    "/valid/path/output.csv"
                )
                assert is_valid
                assert error is None

        # Invalid directory
        with patch("pathlib.Path.parent.exists", return_value=False):
            is_valid, error = base_exporter.validate_output_path(
                "/invalid/path/output.csv"
            )
            assert not is_valid
            assert error is not None

    def test_create_backup(self, base_exporter):
        """Test backup file creation."""
        with patch("pathlib.Path.exists", return_value=True):
            with patch("shutil.copy2") as mock_copy:
                backup_path = base_exporter.create_backup("/path/to/existing_file.csv")

                assert backup_path is not None
                assert ".backup." in backup_path
                mock_copy.assert_called_once()


class TestCSVExporter:
    """Test cases for CSVExporter class."""

    @pytest.fixture
    def csv_exporter(self):
        """Create CSVExporter instance."""
        return CSVExporter()

    @pytest.fixture
    def sample_data(self):
        """Create sample data for CSV export."""
        return pd.DataFrame(
            {
                "id": [1, 2, 3, 4, 5],
                "name": ["Alice", "Bob", "Charlie", "David", "Eve"],
                "value": [10.5, 20.3, 15.7, 30.2, 25.8],
                "date": pd.to_datetime(
                    [
                        "2023-01-01",
                        "2023-01-02",
                        "2023-01-03",
                        "2023-01-04",
                        "2023-01-05",
                    ]
                ),
                "category": ["A", "B", "A", "C", "B"],
                "description": [
                    "Text with, comma",
                    'Text with "quotes"',
                    "Normal text",
                    "Text\nwith\nnewlines",
                    "Special chars: àáâã",
                ],
            }
        )

    def test_csv_exporter_initialization(self, csv_exporter):
        """Test CSV exporter initialization."""
        assert csv_exporter.name == "CSVExporter"
        assert "csv" in csv_exporter.supported_formats
        assert csv_exporter.default_options["delimiter"] == ","
        assert csv_exporter.default_options["quoting"] == "minimal"
        assert csv_exporter.default_options["encoding"] == "utf-8"

    def test_validate_csv_data_valid(self, csv_exporter, sample_data):
        """Test validation of valid CSV data."""
        is_valid, errors = csv_exporter.validate_data(sample_data)

        assert is_valid
        assert len(errors) == 0

    def test_validate_csv_data_empty(self, csv_exporter):
        """Test validation of empty CSV data."""
        empty_data = pd.DataFrame()

        is_valid, errors = csv_exporter.validate_data(empty_data)

        assert not is_valid
        assert len(errors) > 0
        assert any("empty" in error.lower() for error in errors)

    def test_format_csv_data(self, csv_exporter, sample_data):
        """Test CSV data formatting."""
        formatted_data = csv_exporter.format_data(sample_data)

        # Check that data types are properly formatted
        assert formatted_data["date"].dtype == "object"  # Should be string
        assert all(isinstance(val, str) for val in formatted_data["date"])

        # Check date formatting
        assert "2023-01-01" in formatted_data["date"].iloc[0]

    def test_export_csv_basic(self, csv_exporter, sample_data, tmp_path):
        """Test basic CSV export functionality."""
        output_file = tmp_path / "test_output.csv"

        with patch("pandas.DataFrame.to_csv") as mock_to_csv:
            result = csv_exporter.export_data(sample_data, str(output_file))

            assert result.success
            assert result.exported_count == len(sample_data)
            assert result.output_file == str(output_file)
            mock_to_csv.assert_called_once()

    def test_export_csv_with_options(self, csv_exporter, sample_data, tmp_path):
        """Test CSV export with custom options."""
        output_file = tmp_path / "test_output.csv"
        options = {
            "delimiter": ";",
            "quoting": "all",
            "encoding": "latin-1",
            "include_index": False,
        }

        with patch("pandas.DataFrame.to_csv") as mock_to_csv:
            result = csv_exporter.export_data(sample_data, str(output_file), options)

            assert result.success
            mock_to_csv.assert_called_once()

            # Check that options were passed correctly
            call_kwargs = mock_to_csv.call_args[1]
            assert call_kwargs["sep"] == ";"
            assert call_kwargs["encoding"] == "latin-1"
            assert call_kwargs["index"] is False

    def test_export_csv_chunked(self, csv_exporter, tmp_path):
        """Test chunked CSV export for large datasets."""
        # Create large dataset
        large_data = pd.DataFrame(
            {"id": range(1, 10001), "value": np.random.rand(10000)}
        )

        output_file = tmp_path / "large_output.csv"

        with patch("pandas.DataFrame.to_csv") as mock_to_csv:
            result = csv_exporter.export_data_chunked(
                large_data, str(output_file), chunk_size=1000
            )

            assert result.success
            assert result.exported_count == len(large_data)
            # Should be called multiple times for chunks
            assert mock_to_csv.call_count > 1

    def test_export_csv_with_compression(self, csv_exporter, sample_data, tmp_path):
        """Test CSV export with compression."""
        output_file = tmp_path / "compressed_output.csv.gz"
        options = {"compression": "gzip"}

        with patch("pandas.DataFrame.to_csv") as mock_to_csv:
            result = csv_exporter.export_data(sample_data, str(output_file), options)

            assert result.success
            mock_to_csv.assert_called_once()

            # Check compression option
            call_kwargs = mock_to_csv.call_args[1]
            assert call_kwargs["compression"] == "gzip"

    def test_handle_csv_special_characters(self, csv_exporter, tmp_path):
        """Test handling of special characters in CSV export."""
        special_data = pd.DataFrame(
            {
                "text": [
                    "Normal text",
                    "Text with, comma",
                    'Text with "quotes"',
                    "Text\nwith\nnewlines",
                ],
                "unicode": ["àáâã", "ñóöø", "中文", "🚀🌟"],
            }
        )

        output_file = tmp_path / "special_chars.csv"

        with patch("pandas.DataFrame.to_csv") as mock_to_csv:
            result = csv_exporter.export_data(special_data, str(output_file))

            assert result.success
            mock_to_csv.assert_called_once()

            # Check encoding
            call_kwargs = mock_to_csv.call_args[1]
            assert call_kwargs["encoding"] == "utf-8"

    def test_csv_export_error_handling(self, csv_exporter, sample_data):
        """Test CSV export error handling."""
        invalid_path = "/invalid/path/output.csv"

        with patch(
            "pandas.DataFrame.to_csv", side_effect=PermissionError("Access denied")
        ):
            result = csv_exporter.export_data(sample_data, invalid_path)

            assert not result.success
            assert result.error is not None
            assert "Access denied" in str(result.error)


class TestExcelExporter:
    """Test cases for ExcelExporter class."""

    @pytest.fixture
    def excel_exporter(self):
        """Create ExcelExporter instance."""
        return ExcelExporter()

    @pytest.fixture
    def sample_data(self):
        """Create sample data for Excel export."""
        return pd.DataFrame(
            {
                "id": [1, 2, 3, 4, 5],
                "name": ["Alice", "Bob", "Charlie", "David", "Eve"],
                "value": [10.5, 20.3, 15.7, 30.2, 25.8],
                "date": pd.to_datetime(
                    [
                        "2023-01-01",
                        "2023-01-02",
                        "2023-01-03",
                        "2023-01-04",
                        "2023-01-05",
                    ]
                ),
                "percentage": [0.105, 0.203, 0.157, 0.302, 0.258],
            }
        )

    @pytest.fixture
    def multi_sheet_data(self):
        """Create multi-sheet data for Excel export."""
        return {
            "Summary": pd.DataFrame(
                {
                    "metric": ["Total Records", "Average Value", "Max Value"],
                    "value": [5, 20.5, 30.2],
                }
            ),
            "Details": pd.DataFrame(
                {
                    "id": [1, 2, 3],
                    "name": ["Alice", "Bob", "Charlie"],
                    "value": [10.5, 20.3, 15.7],
                }
            ),
            "Analysis": pd.DataFrame(
                {
                    "category": ["A", "B", "C"],
                    "count": [2, 2, 1],
                    "avg_value": [13.1, 23.05, 30.2],
                }
            ),
        }

    def test_excel_exporter_initialization(self, excel_exporter):
        """Test Excel exporter initialization."""
        assert excel_exporter.name == "ExcelExporter"
        assert "xlsx" in excel_exporter.supported_formats
        assert "xls" in excel_exporter.supported_formats
        assert excel_exporter.default_options["engine"] == "openpyxl"
        assert excel_exporter.default_options["index"] is False

    def test_validate_excel_data_valid(self, excel_exporter, sample_data):
        """Test validation of valid Excel data."""
        is_valid, errors = excel_exporter.validate_data(sample_data)

        assert is_valid
        assert len(errors) == 0

    def test_format_excel_data(self, excel_exporter, sample_data):
        """Test Excel data formatting."""
        formatted_data = excel_exporter.format_data(sample_data)

        # Check that data is properly formatted for Excel
        assert "id" in formatted_data.columns
        assert "name" in formatted_data.columns
        assert "date" in formatted_data.columns

        # Check that datetime is preserved
        assert pd.api.types.is_datetime64_any_dtype(formatted_data["date"])

    def test_export_excel_single_sheet(self, excel_exporter, sample_data, tmp_path):
        """Test Excel export to single sheet."""
        output_file = tmp_path / "test_output.xlsx"

        with patch("pandas.DataFrame.to_excel") as mock_to_excel:
            result = excel_exporter.export_data(sample_data, str(output_file))

            assert result.success
            assert result.exported_count == len(sample_data)
            assert result.output_file == str(output_file)
            mock_to_excel.assert_called_once()

    def test_export_excel_multi_sheet(self, excel_exporter, multi_sheet_data, tmp_path):
        """Test Excel export to multiple sheets."""
        output_file = tmp_path / "multi_sheet_output.xlsx"

        with patch("pandas.ExcelWriter") as mock_writer:
            mock_writer_instance = MagicMock()
            mock_writer.return_value.__enter__.return_value = mock_writer_instance

            result = excel_exporter.export_multi_sheet(
                multi_sheet_data, str(output_file)
            )

            assert result.success
            assert len(result.sheets) == 3
            assert "Summary" in result.sheets
            assert "Details" in result.sheets
            assert "Analysis" in result.sheets

    def test_export_excel_with_formatting(self, excel_exporter, sample_data, tmp_path):
        """Test Excel export with custom formatting."""
        output_file = tmp_path / "formatted_output.xlsx"

        formatting_options = {
            "header_format": {"bold": True, "bg_color": "#D7E4BC"},
            "number_format": {"value": "#,##0.00", "percentage": "0.00%"},
            "date_format": "yyyy-mm-dd",
            "auto_adjust_width": True,
        }

        with patch("pandas.ExcelWriter") as mock_writer:
            mock_writer_instance = MagicMock()
            mock_writer.return_value.__enter__.return_value = mock_writer_instance

            result = excel_exporter.export_with_formatting(
                sample_data, str(output_file), formatting_options
            )

            assert result.success
            mock_writer.assert_called_once()

    def test_export_excel_with_charts(self, excel_exporter, sample_data, tmp_path):
        """Test Excel export with charts."""
        output_file = tmp_path / "chart_output.xlsx"

        chart_config = {
            "chart_type": "column",
            "data_range": "B2:C6",
            "title": "Value by Name",
            "x_axis_title": "Name",
            "y_axis_title": "Value",
        }

        with patch("pandas.ExcelWriter") as mock_writer:
            mock_writer_instance = MagicMock()
            mock_writer.return_value.__enter__.return_value = mock_writer_instance

            result = excel_exporter.export_with_charts(
                sample_data, str(output_file), [chart_config]
            )

            assert result.success
            mock_writer.assert_called_once()

    def test_excel_data_validation(self, excel_exporter, tmp_path):
        """Test Excel data validation features."""
        data_with_validation = pd.DataFrame(
            {
                "id": [1, 2, 3],
                "status": ["Active", "Inactive", "Pending"],
                "score": [85, 92, 78],
            }
        )

        output_file = tmp_path / "validation_output.xlsx"

        validation_rules = {
            "status": {"type": "list", "source": ["Active", "Inactive", "Pending"]},
            "score": {"type": "whole", "minimum": 0, "maximum": 100},
        }

        with patch("pandas.ExcelWriter") as mock_writer:
            mock_writer_instance = MagicMock()
            mock_writer.return_value.__enter__.return_value = mock_writer_instance

            result = excel_exporter.export_with_validation(
                data_with_validation, str(output_file), validation_rules
            )

            assert result.success
            mock_writer.assert_called_once()

    def test_excel_export_large_dataset(self, excel_exporter, tmp_path):
        """Test Excel export with large dataset."""
        # Create large dataset (Excel has row limits)
        large_data = pd.DataFrame(
            {"id": range(1, 100001), "value": np.random.rand(100000)}  # 100k rows
        )

        output_file = tmp_path / "large_output.xlsx"

        with patch("pandas.ExcelWriter") as mock_writer:
            mock_writer_instance = MagicMock()
            mock_writer.return_value.__enter__.return_value = mock_writer_instance

            result = excel_exporter.export_large_dataset(
                large_data, str(output_file), max_rows_per_sheet=50000
            )

            assert result.success
            assert len(result.sheets) == 2  # Should split into 2 sheets
            mock_writer.assert_called_once()


class TestJSONExporter:
    """Test cases for JSONExporter class."""

    @pytest.fixture
    def json_exporter(self):
        """Create JSONExporter instance."""
        return JSONExporter()

    @pytest.fixture
    def sample_data(self):
        """Create sample data for JSON export."""
        return pd.DataFrame(
            {
                "id": [1, 2, 3],
                "name": ["Alice", "Bob", "Charlie"],
                "value": [10.5, 20.3, 15.7],
                "date": pd.to_datetime(["2023-01-01", "2023-01-02", "2023-01-03"]),
                "active": [True, False, True],
                "metadata": [{"key": "value1"}, {"key": "value2"}, {"key": "value3"}],
            }
        )

    def test_json_exporter_initialization(self, json_exporter):
        """Test JSON exporter initialization."""
        assert json_exporter.name == "JSONExporter"
        assert "json" in json_exporter.supported_formats
        assert json_exporter.default_options["orient"] == "records"
        assert json_exporter.default_options["date_format"] == "iso"
        assert json_exporter.default_options["indent"] == 2

    def test_format_json_data(self, json_exporter, sample_data):
        """Test JSON data formatting."""
        formatted_data = json_exporter.format_data(sample_data)

        # Check that data is properly formatted for JSON
        assert isinstance(formatted_data, pd.DataFrame)

        # Check date formatting
        assert formatted_data["date"].dtype == "object"  # Should be string

    def test_export_json_records(self, json_exporter, sample_data, tmp_path):
        """Test JSON export in records format."""
        output_file = tmp_path / "records_output.json"

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("json.dump") as mock_json_dump:
                result = json_exporter.export_data(
                    sample_data, str(output_file), {"orient": "records"}
                )

                assert result.success
                assert result.exported_count == len(sample_data)
                mock_json_dump.assert_called_once()

    def test_export_json_index(self, json_exporter, sample_data, tmp_path):
        """Test JSON export in index format."""
        output_file = tmp_path / "index_output.json"

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("json.dump") as mock_json_dump:
                result = json_exporter.export_data(
                    sample_data, str(output_file), {"orient": "index"}
                )

                assert result.success
                mock_json_dump.assert_called_once()

    def test_export_json_nested_structure(self, json_exporter, tmp_path):
        """Test JSON export with nested data structures."""
        nested_data = pd.DataFrame(
            {
                "id": [1, 2],
                "profile": [
                    {"name": "Alice", "age": 30, "skills": ["Python", "SQL"]},
                    {"name": "Bob", "age": 25, "skills": ["Java", "JavaScript"]},
                ],
                "contacts": [
                    [{"type": "email", "value": "<EMAIL>"}],
                    [{"type": "phone", "value": "+1234567890"}],
                ],
            }
        )

        output_file = tmp_path / "nested_output.json"

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("json.dump") as mock_json_dump:
                result = json_exporter.export_data(nested_data, str(output_file))

                assert result.success
                mock_json_dump.assert_called_once()

    def test_export_json_streaming(self, json_exporter, tmp_path):
        """Test streaming JSON export for large datasets."""
        # Create large dataset
        large_data = pd.DataFrame(
            {"id": range(1, 10001), "value": np.random.rand(10000)}
        )

        output_file = tmp_path / "streaming_output.json"

        with patch("builtins.open", mock_open()) as mock_file:
            result = json_exporter.export_streaming(
                large_data, str(output_file), chunk_size=1000
            )

            assert result.success
            assert result.exported_count == len(large_data)

    def test_json_custom_serialization(self, json_exporter, tmp_path):
        """Test JSON export with custom serialization."""
        data_with_custom_types = pd.DataFrame(
            {
                "id": [1, 2, 3],
                "timestamp": pd.to_datetime(["2023-01-01", "2023-01-02", "2023-01-03"]),
                "decimal_value": [10.123456789, 20.987654321, 15.555555555],
            }
        )

        output_file = tmp_path / "custom_output.json"

        custom_serializer = {
            "timestamp": lambda x: x.strftime("%Y-%m-%d %H:%M:%S"),
            "decimal_value": lambda x: round(x, 2),
        }

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("json.dump") as mock_json_dump:
                result = json_exporter.export_with_custom_serializer(
                    data_with_custom_types, str(output_file), custom_serializer
                )

                assert result.success
                mock_json_dump.assert_called_once()


class TestGeoJSONExporter:
    """Test cases for GeoJSONExporter class."""

    @pytest.fixture
    def geojson_exporter(self):
        """Create GeoJSONExporter instance."""
        return GeoJSONExporter()

    @pytest.fixture
    def sample_geo_data(self):
        """Create sample geospatial data."""
        return pd.DataFrame(
            {
                "id": [1, 2, 3],
                "name": ["Point A", "Point B", "Point C"],
                "latitude": [40.7128, 34.0522, 41.8781],
                "longitude": [-74.0060, -118.2437, -87.6298],
                "geometry": [
                    "POINT(-74.0060 40.7128)",
                    "POINT(-118.2437 34.0522)",
                    "POINT(-87.6298 41.8781)",
                ],
                "category": ["Type1", "Type2", "Type1"],
            }
        )

    @pytest.fixture
    def sample_polygon_data(self):
        """Create sample polygon data."""
        return pd.DataFrame(
            {
                "id": [1, 2],
                "name": ["Area A", "Area B"],
                "geometry": [
                    "POLYGON((0 0, 1 0, 1 1, 0 1, 0 0))",
                    "POLYGON((1 0, 2 0, 2 1, 1 1, 1 0))",
                ],
                "area_type": ["Residential", "Commercial"],
            }
        )

    def test_geojson_exporter_initialization(self, geojson_exporter):
        """Test GeoJSON exporter initialization."""
        assert geojson_exporter.name == "GeoJSONExporter"
        assert "geojson" in geojson_exporter.supported_formats
        assert geojson_exporter.default_options["crs"] == "EPSG:4326"
        assert geojson_exporter.default_options["precision"] == 6

    def test_validate_geospatial_data_valid(self, geojson_exporter, sample_geo_data):
        """Test validation of valid geospatial data."""
        is_valid, errors = geojson_exporter.validate_data(sample_geo_data)

        assert is_valid
        assert len(errors) == 0

    def test_validate_geospatial_data_missing_geometry(self, geojson_exporter):
        """Test validation with missing geometry column."""
        invalid_data = pd.DataFrame(
            {
                "id": [1, 2, 3],
                "name": ["Point A", "Point B", "Point C"]
                # Missing geometry column
            }
        )

        is_valid, errors = geojson_exporter.validate_data(invalid_data)

        assert not is_valid
        assert len(errors) > 0
        assert any("geometry" in error.lower() for error in errors)

    def test_format_geojson_data(self, geojson_exporter, sample_geo_data):
        """Test GeoJSON data formatting."""
        formatted_data = geojson_exporter.format_data(sample_geo_data)

        # Check GeoJSON structure
        assert "type" in formatted_data
        assert formatted_data["type"] == "FeatureCollection"
        assert "features" in formatted_data
        assert len(formatted_data["features"]) == len(sample_geo_data)

        # Check feature structure
        feature = formatted_data["features"][0]
        assert "type" in feature
        assert feature["type"] == "Feature"
        assert "geometry" in feature
        assert "properties" in feature

    def test_export_geojson_points(self, geojson_exporter, sample_geo_data, tmp_path):
        """Test GeoJSON export for point data."""
        output_file = tmp_path / "points_output.geojson"

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("json.dump") as mock_json_dump:
                result = geojson_exporter.export_data(sample_geo_data, str(output_file))

                assert result.success
                assert result.exported_count == len(sample_geo_data)
                mock_json_dump.assert_called_once()

    def test_export_geojson_polygons(
        self, geojson_exporter, sample_polygon_data, tmp_path
    ):
        """Test GeoJSON export for polygon data."""
        output_file = tmp_path / "polygons_output.geojson"

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("json.dump") as mock_json_dump:
                result = geojson_exporter.export_data(
                    sample_polygon_data, str(output_file)
                )

                assert result.success
                assert result.exported_count == len(sample_polygon_data)
                mock_json_dump.assert_called_once()

    def test_geojson_coordinate_transformation(
        self, geojson_exporter, sample_geo_data, tmp_path
    ):
        """Test coordinate transformation in GeoJSON export."""
        output_file = tmp_path / "transformed_output.geojson"

        options = {
            "source_crs": "EPSG:4326",
            "target_crs": "EPSG:3857",  # Web Mercator
            "transform_coordinates": True,
        }

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("json.dump") as mock_json_dump:
                result = geojson_exporter.export_data(
                    sample_geo_data, str(output_file), options
                )

                assert result.success
                mock_json_dump.assert_called_once()

    def test_geojson_precision_control(
        self, geojson_exporter, sample_geo_data, tmp_path
    ):
        """Test coordinate precision control in GeoJSON export."""
        output_file = tmp_path / "precision_output.geojson"

        options = {"precision": 2}  # 2 decimal places

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("json.dump") as mock_json_dump:
                result = geojson_exporter.export_data(
                    sample_geo_data, str(output_file), options
                )

                assert result.success
                mock_json_dump.assert_called_once()

    def test_geojson_feature_filtering(
        self, geojson_exporter, sample_geo_data, tmp_path
    ):
        """Test feature filtering in GeoJSON export."""
        output_file = tmp_path / "filtered_output.geojson"

        filter_condition = lambda row: row["category"] == "Type1"

        with patch("builtins.open", mock_open()) as mock_file:
            with patch("json.dump") as mock_json_dump:
                result = geojson_exporter.export_filtered(
                    sample_geo_data, str(output_file), filter_condition
                )

                assert result.success
                assert result.exported_count == 2  # Only Type1 features
                mock_json_dump.assert_called_once()


class TestReportGenerator:
    """Test cases for ReportGenerator class."""

    @pytest.fixture
    def report_generator(self):
        """Create ReportGenerator instance."""
        return ReportGenerator()

    @pytest.fixture
    def sample_data(self):
        """Create sample data for report generation."""
        return pd.DataFrame(
            {
                "id": range(1, 101),
                "category": ["A"] * 40 + ["B"] * 35 + ["C"] * 25,
                "value": np.random.normal(100, 20, 100),
                "date": pd.date_range("2023-01-01", periods=100, freq="D"),
                "status": ["Active"] * 80 + ["Inactive"] * 20,
            }
        )

    def test_generate_summary_report(self, report_generator, sample_data):
        """Test summary report generation."""
        summary = report_generator.generate_summary(sample_data)

        assert "total_records" in summary
        assert "column_info" in summary
        assert "data_types" in summary
        assert "missing_values" in summary
        assert "basic_statistics" in summary

        assert summary["total_records"] == len(sample_data)
        assert len(summary["column_info"]) == len(sample_data.columns)

    def test_generate_statistical_report(self, report_generator, sample_data):
        """Test statistical report generation."""
        stats_report = report_generator.generate_statistics(sample_data)

        assert "descriptive_statistics" in stats_report
        assert "correlation_matrix" in stats_report
        assert "distribution_analysis" in stats_report
        assert "outlier_detection" in stats_report

        # Check descriptive statistics
        desc_stats = stats_report["descriptive_statistics"]
        assert "value" in desc_stats.columns
        assert "mean" in desc_stats.index
        assert "std" in desc_stats.index

    def test_generate_data_quality_report(self, report_generator, sample_data):
        """Test data quality report generation."""
        quality_report = report_generator.generate_data_quality_report(sample_data)

        assert "completeness" in quality_report
        assert "consistency" in quality_report
        assert "validity" in quality_report
        assert "uniqueness" in quality_report
        assert "accuracy" in quality_report

        # Check completeness metrics
        completeness = quality_report["completeness"]
        assert "missing_percentage" in completeness
        assert "complete_records" in completeness

    def test_generate_export_report(self, report_generator, tmp_path):
        """Test export report generation."""
        export_stats = {
            "total_records": 1000,
            "exported_records": 950,
            "failed_records": 50,
            "export_time": 45.5,
            "file_size": 2048,
            "format": "CSV",
        }

        output_file = tmp_path / "export_report.html"

        with patch("builtins.open", mock_open()) as mock_file:
            result = report_generator.generate_export_report(
                export_stats, str(output_file)
            )

            assert result.success
            mock_file.assert_called_once()

    def test_generate_html_report(self, report_generator, sample_data, tmp_path):
        """Test HTML report generation."""
        output_file = tmp_path / "data_report.html"

        with patch("builtins.open", mock_open()) as mock_file:
            result = report_generator.generate_html_report(
                sample_data, str(output_file), include_charts=True
            )

            assert result.success
            mock_file.assert_called_once()

    def test_generate_pdf_report(self, report_generator, sample_data, tmp_path):
        """Test PDF report generation."""
        output_file = tmp_path / "data_report.pdf"

        with patch("weasyprint.HTML") as mock_html:
            mock_html.return_value.write_pdf = Mock()

            result = report_generator.generate_pdf_report(sample_data, str(output_file))

            assert result.success
            mock_html.assert_called_once()


class TestDataFormatter:
    """Test cases for DataFormatter class."""

    @pytest.fixture
    def data_formatter(self):
        """Create DataFormatter instance."""
        return DataFormatter()

    @pytest.fixture
    def sample_data(self):
        """Create sample data for formatting."""
        return pd.DataFrame(
            {
                "id": [1, 2, 3, 4, 5],
                "name": ["Alice", "Bob", "Charlie", "David", "Eve"],
                "value": [10.123456, 20.987654, 15.555555, 30.111111, 25.999999],
                "percentage": [0.1234, 0.5678, 0.9012, 0.3456, 0.7890],
                "date": pd.to_datetime(
                    [
                        "2023-01-01",
                        "2023-01-02",
                        "2023-01-03",
                        "2023-01-04",
                        "2023-01-05",
                    ]
                ),
                "currency": [1000.50, 2500.75, 750.25, 3200.00, 1800.99],
            }
        )

    def test_format_numbers(self, data_formatter, sample_data):
        """Test number formatting."""
        formatted_data = data_formatter.format_numbers(
            sample_data, {"value": 2, "currency": 2}  # 2 decimal places
        )

        # Check that numbers are properly formatted
        assert all(len(str(val).split(".")[-1]) <= 2 for val in formatted_data["value"])
        assert all(
            len(str(val).split(".")[-1]) <= 2 for val in formatted_data["currency"]
        )

    def test_format_percentages(self, data_formatter, sample_data):
        """Test percentage formatting."""
        formatted_data = data_formatter.format_percentages(
            sample_data, ["percentage"], decimal_places=1
        )

        # Check percentage formatting
        assert all(
            isinstance(val, str) and val.endswith("%")
            for val in formatted_data["percentage"]
        )

    def test_format_dates(self, data_formatter, sample_data):
        """Test date formatting."""
        formatted_data = data_formatter.format_dates(
            sample_data, ["date"], date_format="%Y-%m-%d"
        )

        # Check date formatting
        assert all(isinstance(val, str) for val in formatted_data["date"])
        assert "2023-01-01" in formatted_data["date"].iloc[0]

    def test_format_currency(self, data_formatter, sample_data):
        """Test currency formatting."""
        formatted_data = data_formatter.format_currency(
            sample_data, ["currency"], currency_symbol="$", decimal_places=2
        )

        # Check currency formatting
        assert all(
            isinstance(val, str) and val.startswith("$")
            for val in formatted_data["currency"]
        )

    def test_apply_conditional_formatting(self, data_formatter, sample_data):
        """Test conditional formatting."""
        conditions = {
            "value": {
                "high": lambda x: x > 20,
                "medium": lambda x: 10 <= x <= 20,
                "low": lambda x: x < 10,
            }
        }

        formatted_data = data_formatter.apply_conditional_formatting(
            sample_data, conditions
        )

        # Check that formatting categories are applied
        assert "value_category" in formatted_data.columns
        assert set(formatted_data["value_category"].unique()).issubset(
            {"high", "medium", "low"}
        )

    def test_format_for_display(self, data_formatter, sample_data):
        """Test comprehensive display formatting."""
        display_options = {
            "number_format": {"value": 2, "currency": 2},
            "date_format": "%Y-%m-%d",
            "percentage_format": {"percentage": 1},
            "currency_format": {"currency": "$"},
            "text_case": {"name": "title"},
        }

        formatted_data = data_formatter.format_for_display(sample_data, display_options)

        # Check various formatting applied
        assert all(name.istitle() for name in formatted_data["name"])
        assert all(isinstance(val, str) for val in formatted_data["date"])


class TestFileManager:
    """Test cases for FileManager class."""

    @pytest.fixture
    def file_manager(self):
        """Create FileManager instance."""
        return FileManager()

    def test_create_directory_structure(self, file_manager, tmp_path):
        """Test directory structure creation."""
        export_path = tmp_path / "exports" / "2023" / "01"

        file_manager.create_directory_structure(str(export_path))

        assert export_path.exists()
        assert export_path.is_dir()

    def test_generate_filename(self, file_manager):
        """Test filename generation."""
        # Test with timestamp
        filename = file_manager.generate_filename(
            "data_export", "csv", include_timestamp=True
        )

        assert filename.startswith("data_export_")
        assert filename.endswith(".csv")
        assert len(filename.split("_")) >= 3  # name + timestamp parts

        # Test without timestamp
        filename = file_manager.generate_filename(
            "simple_export", "xlsx", include_timestamp=False
        )

        assert filename == "simple_export.xlsx"

    def test_manage_file_versions(self, file_manager, tmp_path):
        """Test file version management."""
        base_file = tmp_path / "export.csv"
        base_file.touch()  # Create file

        # Create versioned filename
        versioned_file = file_manager.create_versioned_filename(str(base_file))

        assert versioned_file != str(base_file)
        assert "v1" in versioned_file or "_1" in versioned_file

    def test_cleanup_old_files(self, file_manager, tmp_path):
        """Test cleanup of old export files."""
        # Create test files with different ages
        old_file = tmp_path / "old_export.csv"
        recent_file = tmp_path / "recent_export.csv"

        old_file.touch()
        recent_file.touch()

        # Mock file modification times
        with patch("pathlib.Path.stat") as mock_stat:
            mock_stat.return_value.st_mtime = 1640995200  # Old timestamp

            cleaned_files = file_manager.cleanup_old_files(
                str(tmp_path), max_age_days=30, pattern="*_export.csv"
            )

            assert len(cleaned_files) >= 0  # Should identify files for cleanup

    def test_compress_files(self, file_manager, tmp_path):
        """Test file compression."""
        test_file = tmp_path / "test_export.csv"
        test_file.write_text("id,name,value\n1,Alice,10\n2,Bob,20")

        with patch("gzip.open", mock_open()) as mock_gzip:
            compressed_file = file_manager.compress_file(str(test_file))

            assert compressed_file.endswith(".gz")
            mock_gzip.assert_called_once()

    def test_validate_export_integrity(self, file_manager, tmp_path):
        """Test export file integrity validation."""
        test_file = tmp_path / "test_export.csv"
        test_content = "id,name,value\n1,Alice,10\n2,Bob,20"
        test_file.write_text(test_content)

        # Test file exists and is readable
        is_valid, errors = file_manager.validate_file_integrity(str(test_file))

        assert is_valid
        assert len(errors) == 0

        # Test non-existent file
        is_valid, errors = file_manager.validate_file_integrity(
            str(tmp_path / "nonexistent.csv")
        )

        assert not is_valid
        assert len(errors) > 0
