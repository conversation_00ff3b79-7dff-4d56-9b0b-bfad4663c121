__author__ = "<PERSON><PERSON>Li"
__email__ = "<EMAIL>"

"""Geospatial data processor for handling spatial data operations.

This module provides the main GeospatialProcessor class for processing
geospatial data including coordinate transformations, spatial queries,
and integration with PostGIS.
"""

import asyncio
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import asyncpg
import geopandas as gpd
import pandas as pd
from loguru import logger
from shapely import wkt
from shapely.geometry import MultiPolygon, Point, Polygon

# Handle relative imports with fallback
try:
    from ..config import settings
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    # Add parent directories to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(current_dir))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from config import settings
from ..connection.session import get_connection
from ..exceptions import DatabaseError, GeospatialError
from ..utils.performance import measure_performance
from ..utils.validators import validate_coordinates


class GeospatialError(Exception):
    """Custom exception for geospatial operations."""

    pass


class GeospatialProcessor:
    """Main processor for geospatial data operations.

    This class handles:
    - Loading and processing geospatial data files
    - Coordinate system transformations
    - Spatial queries and operations
    - Integration with PostGIS database
    - Performance optimization for large datasets
    """

    def __init__(self, connection_pool=None):
        """Initialize the GeospatialProcessor.

        Args:
            connection_pool: Optional database connection pool
        """
        self.connection_pool = connection_pool
        self.supported_formats = [".shp", ".geojson", ".tab", ".kml", ".gpx"]
        self.default_crs = "EPSG:4326"  # WGS84

    async def load_spatial_data(
        self,
        file_path: Union[str, Path],
        encoding: str = "utf-8",
        crs: Optional[str] = None,
    ) -> gpd.GeoDataFrame:
        """Load spatial data from various file formats.

        Args:
            file_path: Path to the spatial data file
            encoding: File encoding (default: utf-8)
            crs: Coordinate reference system to use

        Returns:
            GeoDataFrame containing the spatial data

        Raises:
            GeospatialError: If file format is not supported or loading fails
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                raise GeospatialError(f"File not found: {file_path}")

            file_ext = file_path.suffix.lower()
            if file_ext not in self.supported_formats:
                raise GeospatialError(f"Unsupported file format: {file_ext}")

            logger.info(f"Loading spatial data from {file_path}")

            # Load the spatial data
            if file_ext == ".tab":
                # Special handling for MapInfo TAB files
                gdf = gpd.read_file(str(file_path), encoding=encoding)
            else:
                gdf = gpd.read_file(str(file_path), encoding=encoding)

            # Set or transform CRS if specified
            if crs:
                if gdf.crs is None:
                    gdf.set_crs(crs, inplace=True)
                elif gdf.crs != crs:
                    gdf = gdf.to_crs(crs)
            elif gdf.crs is None:
                gdf.set_crs(self.default_crs, inplace=True)

            logger.info(f"Loaded {len(gdf)} features with CRS: {gdf.crs}")
            return gdf

        except Exception as e:
            logger.error(f"Failed to load spatial data from {file_path}: {e}")
            raise GeospatialError(f"Failed to load spatial data: {e}")

    @measure_performance
    async def process_polygon_data(
        self,
        gdf: gpd.GeoDataFrame,
        simplify_tolerance: Optional[float] = None,
        buffer_distance: Optional[float] = None,
    ) -> gpd.GeoDataFrame:
        """Process polygon data with optional simplification and buffering.

        Args:
            gdf: Input GeoDataFrame
            simplify_tolerance: Tolerance for geometry simplification
            buffer_distance: Distance for buffering geometries

        Returns:
            Processed GeoDataFrame
        """
        try:
            processed_gdf = gdf.copy()

            # Validate geometries
            invalid_geoms = ~processed_gdf.geometry.is_valid
            if invalid_geoms.any():
                logger.warning(
                    f"Found {invalid_geoms.sum()} invalid geometries, attempting to fix"
                )
                processed_gdf.loc[invalid_geoms, "geometry"] = processed_gdf.loc[
                    invalid_geoms, "geometry"
                ].buffer(0)

            # Simplify geometries if requested
            if simplify_tolerance:
                logger.info(
                    f"Simplifying geometries with tolerance {simplify_tolerance}"
                )
                processed_gdf["geometry"] = processed_gdf.geometry.simplify(
                    simplify_tolerance, preserve_topology=True
                )

            # Buffer geometries if requested
            if buffer_distance:
                logger.info(f"Buffering geometries with distance {buffer_distance}")
                processed_gdf["geometry"] = processed_gdf.geometry.buffer(
                    buffer_distance
                )

            # Add area and centroid calculations
            processed_gdf["area_km2"] = (
                processed_gdf.geometry.area / 1000000
            )  # Convert to km²
            processed_gdf["centroid_lon"] = processed_gdf.geometry.centroid.x
            processed_gdf["centroid_lat"] = processed_gdf.geometry.centroid.y

            logger.info(f"Processed {len(processed_gdf)} polygon features")
            return processed_gdf

        except Exception as e:
            logger.error(f"Failed to process polygon data: {e}")
            raise GeospatialError(f"Failed to process polygon data: {e}")

    async def point_in_polygon_query(
        self,
        points: List[Tuple[float, float]],
        polygons_gdf: gpd.GeoDataFrame,
        point_crs: str = "EPSG:4326",
    ) -> pd.DataFrame:
        """Perform point-in-polygon queries for multiple points.

        Args:
            points: List of (longitude, latitude) tuples
            polygons_gdf: GeoDataFrame containing polygon geometries
            point_crs: CRS of the input points

        Returns:
            DataFrame with point coordinates and matching polygon information
        """
        try:
            # Create GeoDataFrame from points
            points_gdf = gpd.GeoDataFrame(
                {"lon": [p[0] for p in points], "lat": [p[1] for p in points]},
                geometry=[Point(p[0], p[1]) for p in points],
                crs=point_crs,
            )

            # Ensure both GeoDataFrames have the same CRS
            if points_gdf.crs != polygons_gdf.crs:
                points_gdf = points_gdf.to_crs(polygons_gdf.crs)

            # Perform spatial join
            result = gpd.sjoin(points_gdf, polygons_gdf, how="left", predicate="within")

            logger.info(
                f"Processed {len(points)} points against {len(polygons_gdf)} polygons"
            )
            return result

        except Exception as e:
            logger.error(f"Failed to perform point-in-polygon query: {e}")
            raise GeospatialError(f"Failed to perform point-in-polygon query: {e}")

    async def save_to_postgis(
        self,
        gdf: gpd.GeoDataFrame,
        table_name: str,
        schema: str = "public",
        if_exists: str = "replace",
        chunksize: int = 10000,
    ) -> None:
        """Save GeoDataFrame to PostGIS database.

        Args:
            gdf: GeoDataFrame to save
            table_name: Target table name
            schema: Target schema name
            if_exists: How to behave if table exists ('fail', 'replace', 'append')
            chunksize: Number of rows to write at a time
        """
        try:
            if self.connection_pool is None:
                raise GeospatialError("No database connection available")

            # Ensure geometry column is in WKT format for PostGIS
            gdf_copy = gdf.copy()
            gdf_copy["geometry"] = gdf_copy.geometry.apply(lambda x: x.wkt)

            # Use SQLAlchemy engine for pandas to_sql
            from sqlalchemy import create_engine

            engine = create_engine(settings.db_url.replace("asyncpg", "psycopg2"))

            logger.info(f"Saving {len(gdf_copy)} features to {schema}.{table_name}")

            # Save to database
            gdf_copy.to_sql(
                name=table_name,
                con=engine,
                schema=schema,
                if_exists=if_exists,
                index=False,
                chunksize=chunksize,
                method="multi",
            )

            # Create spatial index if table was created/replaced
            if if_exists in ["replace", "fail"]:
                await self._create_spatial_index(table_name, schema)

            logger.info(f"Successfully saved data to {schema}.{table_name}")

        except Exception as e:
            logger.error(f"Failed to save to PostGIS: {e}")
            raise GeospatialError(f"Failed to save to PostGIS: {e}")

    async def _create_spatial_index(self, table_name: str, schema: str) -> None:
        """Create spatial index on geometry column.

        Args:
            table_name: Table name
            schema: Schema name
        """
        try:
            async with get_connection() as conn:
                # Convert geometry column to PostGIS geometry type
                await conn.execute(
                    f"""
                    ALTER TABLE {schema}.{table_name}
                    ALTER COLUMN geometry TYPE geometry
                    USING ST_GeomFromText(geometry)
                """
                )

                # Create spatial index
                index_name = f"idx_{table_name}_geometry"
                await conn.execute(
                    f"""
                    CREATE INDEX IF NOT EXISTS {index_name}
                    ON {schema}.{table_name}
                    USING GIST (geometry)
                """
                )

                logger.info(f"Created spatial index {index_name}")

        except Exception as e:
            logger.warning(f"Failed to create spatial index: {e}")

    async def calculate_distance(
        self, point1: Tuple[float, float], point2: Tuple[float, float], unit: str = "km"
    ) -> float:
        """Calculate distance between two points.

        Args:
            point1: First point (longitude, latitude)
            point2: Second point (longitude, latitude)
            unit: Distance unit ('km', 'm', 'miles')

        Returns:
            Distance in specified unit
        """
        try:
            from geopy.distance import geodesic

            # Note: geopy expects (latitude, longitude)
            dist = geodesic((point1[1], point1[0]), (point2[1], point2[0]))

            if unit == "km":
                return dist.kilometers
            elif unit == "m":
                return dist.meters
            elif unit == "miles":
                return dist.miles
            else:
                raise ValueError(f"Unsupported unit: {unit}")

        except Exception as e:
            logger.error(f"Failed to calculate distance: {e}")
            raise GeospatialError(f"Failed to calculate distance: {e}")

    async def get_bounding_box(self, gdf: gpd.GeoDataFrame) -> Dict[str, float]:
        """Get bounding box of GeoDataFrame.

        Args:
            gdf: Input GeoDataFrame

        Returns:
            Dictionary with min/max longitude and latitude
        """
        try:
            bounds = gdf.total_bounds
            return {
                "min_lon": bounds[0],
                "min_lat": bounds[1],
                "max_lon": bounds[2],
                "max_lat": bounds[3],
            }

        except Exception as e:
            logger.error(f"Failed to get bounding box: {e}")
            raise GeospatialError(f"Failed to get bounding box: {e}")

    async def validate_geometries(self, gdf: gpd.GeoDataFrame) -> Dict[str, Any]:
        """Validate geometries in GeoDataFrame.

        Args:
            gdf: Input GeoDataFrame

        Returns:
            Dictionary with validation results
        """
        try:
            results = {
                "total_features": len(gdf),
                "valid_geometries": gdf.geometry.is_valid.sum(),
                "invalid_geometries": (~gdf.geometry.is_valid).sum(),
                "empty_geometries": gdf.geometry.is_empty.sum(),
                "null_geometries": gdf.geometry.isnull().sum(),
            }

            results["validation_passed"] = results["invalid_geometries"] == 0

            logger.info(f"Geometry validation: {results}")
            return results

        except Exception as e:
            logger.error(f"Failed to validate geometries: {e}")
            raise GeospatialError(f"Failed to validate geometries: {e}")
    
    def transform_coordinates(self, geometry, source_crs: str, target_crs: str):
        """Transform geometry coordinates from source CRS to target CRS.
        
        Args:
            geometry: Shapely geometry object
            source_crs: Source coordinate reference system
            target_crs: Target coordinate reference system
            
        Returns:
            Transformed geometry
        """
        try:
            import pyproj
            from shapely.ops import transform
            
            # Create transformer
            transformer = pyproj.Transformer.from_crs(source_crs, target_crs, always_xy=True)
            
            # Transform geometry
            transformed_geom = transform(transformer.transform, geometry)
            return transformed_geom
            
        except Exception as e:
            logger.error(f"Failed to transform coordinates: {e}")
            raise GeospatialError(f"Failed to transform coordinates: {e}")
    
    def buffer_geometry(self, geometry, distance: float):
        """Create a buffer around a geometry.
        
        Args:
            geometry: Shapely geometry object
            distance: Buffer distance
            
        Returns:
            Buffered geometry
        """
        try:
            return geometry.buffer(distance)
        except Exception as e:
            logger.error(f"Failed to buffer geometry: {e}")
            raise GeospatialError(f"Failed to buffer geometry: {e}")
    
    def intersect_geometries(self, geom1, geom2):
        """Find intersection between two geometries.
        
        Args:
            geom1: First geometry
            geom2: Second geometry
            
        Returns:
            Intersection geometry or None if no intersection
        """
        try:
            intersection = geom1.intersection(geom2)
            return intersection if not intersection.is_empty else None
        except Exception as e:
            logger.error(f"Failed to intersect geometries: {e}")
            raise GeospatialError(f"Failed to intersect geometries: {e}")
