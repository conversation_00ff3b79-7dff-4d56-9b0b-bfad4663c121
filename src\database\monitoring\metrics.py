"""Performance metrics collection for database operations.

This module provides the MetricsCollector class for collecting and tracking
performance metrics including connection pool statistics and query execution times.
"""

import asyncio
import logging
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from statistics import mean, median
from typing import Any, Dict, List, Optional, Union

from ..connection.pool import DatabasePoolManager, get_pool_manager
from ..exceptions import DatabaseError

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class QueryMetrics:
    """Metrics for a single query execution."""

    query_hash: str
    execution_time: float
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None
    rows_affected: Optional[int] = None


@dataclass
class PoolMetrics:
    """Connection pool metrics snapshot."""

    timestamp: datetime
    pool_size: int
    active_connections: int
    idle_connections: int
    total_acquisitions: int
    total_releases: int
    total_connections_created: int
    total_connections_closed: int


@dataclass
class PerformanceMetrics:
    """Aggregated performance metrics over a period."""
    cpu_usage: float  # Percentage
    memory_usage: float  # MB
    disk_io_read: float  # MB/s
    disk_io_write: float  # MB/s
    network_sent: float  # MB/s
    network_received: float  # MB/s
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class MetricsSummary:
    """Summary of collected metrics."""

    total_queries: int = 0
    successful_queries: int = 0
    failed_queries: int = 0
    avg_execution_time: float = 0.0
    median_execution_time: float = 0.0
    min_execution_time: float = 0.0
    max_execution_time: float = 0.0
    queries_per_second: float = 0.0
    current_pool_metrics: Optional[PoolMetrics] = None
    top_slow_queries: List[QueryMetrics] = field(default_factory=list)


class MetricsCollector:
    """Collects and manages performance metrics for database operations."""

    def __init__(
        self,
        pool_manager: Optional[DatabasePoolManager] = None,
        max_query_history: int = 1000,
        max_pool_history: int = 100,
    ):
        """Initialize metrics collector.

        Args:
            pool_manager: Database pool manager instance. If None, uses global instance.
            max_query_history: Maximum number of query metrics to keep in memory.
            max_pool_history: Maximum number of pool snapshots to keep in memory.
        """
        self.pool_manager = pool_manager or get_pool_manager()
        self.max_query_history = max_query_history
        self.max_pool_history = max_pool_history

        # Query metrics storage
        self.query_metrics: deque[QueryMetrics] = deque(maxlen=max_query_history)
        self.query_times_by_hash: Dict[str, deque[float]] = defaultdict(
            lambda: deque(maxlen=100)
        )

        # Pool metrics storage
        self.pool_metrics: deque[PoolMetrics] = deque(maxlen=max_pool_history)

        # Metrics collection state
        self._collecting = False
        self._collection_task: Optional[asyncio.Task] = None
        self._collection_interval = 30.0  # seconds

        logger.info("MetricsCollector initialized")

    def record_query_execution(
        self,
        query: str,
        execution_time: float,
        success: bool = True,
        error_message: Optional[str] = None,
        rows_affected: Optional[int] = None,
    ) -> None:
        """Record metrics for a query execution.

        Args:
            query: SQL query string (will be hashed for privacy).
            execution_time: Query execution time in seconds.
            success: Whether the query executed successfully.
            error_message: Error message if query failed.
            rows_affected: Number of rows affected by the query.
        """
        try:
            # Create a hash of the query for privacy and grouping
            query_hash = self._hash_query(query)

            # Create query metrics
            metrics = QueryMetrics(
                query_hash=query_hash,
                execution_time=execution_time,
                timestamp=datetime.now(),
                success=success,
                error_message=error_message,
                rows_affected=rows_affected,
            )

            # Store metrics
            self.query_metrics.append(metrics)

            # Store execution time for this query type
            if success:
                self.query_times_by_hash[query_hash].append(execution_time)

            logger.debug(
                f"Recorded query metrics: hash={query_hash}, "
                f"time={execution_time:.4f}s, success={success}"
            )

        except Exception as e:
            logger.error(f"Error recording query metrics: {str(e)}", exc_info=True)

    def collect_pool_metrics(self) -> Optional[PoolMetrics]:
        """Collect current connection pool metrics.

        Returns:
            PoolMetrics: Current pool metrics snapshot, or None if collection fails.
        """
        try:
            if not self.pool_manager.is_initialized():
                logger.warning("Pool manager not initialized, cannot collect metrics")
                return None

            # Get pool statistics
            stats = self.pool_manager.get_pool_stats()

            # Create pool metrics snapshot
            metrics = PoolMetrics(
                timestamp=datetime.now(),
                pool_size=stats.get("pool_size", 0),
                active_connections=stats.get("pool_active", 0),
                idle_connections=stats.get("pool_idle", 0),
                total_acquisitions=stats.get("pool_acquisitions", 0),
                total_releases=stats.get("pool_releases", 0),
                total_connections_created=stats.get("total_connections_created", 0),
                total_connections_closed=stats.get("total_connections_closed", 0),
            )

            # Store metrics
            self.pool_metrics.append(metrics)

            logger.debug(f"Collected pool metrics: {metrics}")
            return metrics

        except Exception as e:
            logger.error(f"Error collecting pool metrics: {str(e)}", exc_info=True)
            return None

    def get_metrics_summary(
        self, time_window: Optional[timedelta] = None
    ) -> MetricsSummary:
        """Get a summary of collected metrics.

        Args:
            time_window: Time window to consider for metrics. If None, uses all data.

        Returns:
            MetricsSummary: Summary of metrics.
        """
        try:
            # Filter metrics by time window if specified
            if time_window:
                cutoff_time = datetime.now() - time_window
                filtered_queries = [
                    m for m in self.query_metrics if m.timestamp >= cutoff_time
                ]
            else:
                filtered_queries = list(self.query_metrics)

            if not filtered_queries:
                return MetricsSummary(
                    current_pool_metrics=self._get_latest_pool_metrics()
                )

            # Calculate query statistics
            total_queries = len(filtered_queries)
            successful_queries = sum(1 for m in filtered_queries if m.success)
            failed_queries = total_queries - successful_queries

            # Calculate execution time statistics
            execution_times = [m.execution_time for m in filtered_queries if m.success]

            if execution_times:
                avg_time = mean(execution_times)
                median_time = median(execution_times)
                min_time = min(execution_times)
                max_time = max(execution_times)
            else:
                avg_time = median_time = min_time = max_time = 0.0

            # Calculate queries per second
            if time_window and filtered_queries:
                time_span = time_window.total_seconds()
                queries_per_second = total_queries / time_span if time_span > 0 else 0.0
            else:
                queries_per_second = 0.0

            # Get top slow queries
            slow_queries = sorted(
                [m for m in filtered_queries if m.success],
                key=lambda x: x.execution_time,
                reverse=True,
            )[:10]

            return MetricsSummary(
                total_queries=total_queries,
                successful_queries=successful_queries,
                failed_queries=failed_queries,
                avg_execution_time=avg_time,
                median_execution_time=median_time,
                min_execution_time=min_time,
                max_execution_time=max_time,
                queries_per_second=queries_per_second,
                current_pool_metrics=self._get_latest_pool_metrics(),
                top_slow_queries=slow_queries,
            )

        except Exception as e:
            logger.error(f"Error generating metrics summary: {str(e)}", exc_info=True)
            return MetricsSummary(current_pool_metrics=self._get_latest_pool_metrics())

    async def start_collection(self, interval: float = 30.0) -> None:
        """Start automatic metrics collection.

        Args:
            interval: Collection interval in seconds.
        """
        if self._collecting:
            logger.warning("Metrics collection already started")
            return

        self._collection_interval = interval
        self._collecting = True
        self._collection_task = asyncio.create_task(self._collection_loop())

        logger.info(f"Started metrics collection with {interval}s interval")

    async def stop_collection(self) -> None:
        """Stop automatic metrics collection."""
        if not self._collecting:
            logger.warning("Metrics collection not started")
            return

        self._collecting = False

        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
            self._collection_task = None

        logger.info("Stopped metrics collection")

    def clear_metrics(self) -> None:
        """Clear all collected metrics."""
        self.query_metrics.clear()
        self.query_times_by_hash.clear()
        self.pool_metrics.clear()

        logger.info("Cleared all metrics")

    def get_query_stats_by_type(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics grouped by query type (hash).

        Returns:
            Dict mapping query hashes to their statistics.
        """
        stats = {}

        for query_hash, times in self.query_times_by_hash.items():
            if times:
                stats[query_hash] = {
                    "count": len(times),
                    "avg_time": mean(times),
                    "median_time": median(times),
                    "min_time": min(times),
                    "max_time": max(times),
                }

        return stats

    async def _collection_loop(self) -> None:
        """Background task for automatic metrics collection."""
        while self._collecting:
            try:
                # Collect pool metrics
                self.collect_pool_metrics()

                # Wait for next collection interval
                await asyncio.sleep(self._collection_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(
                    f"Error in metrics collection loop: {str(e)}", exc_info=True
                )
                await asyncio.sleep(5)  # Brief pause before retrying

    def _hash_query(self, query: str) -> str:
        """Create a hash of the query for privacy and grouping.

        Args:
            query: SQL query string.

        Returns:
            str: Hash of the query.
        """
        import hashlib

        # Normalize query for consistent hashing
        normalized = " ".join(query.strip().split())
        return hashlib.md5(normalized.encode(), usedforsecurity=False).hexdigest()[:12]

    def _get_latest_pool_metrics(self) -> Optional[PoolMetrics]:
        """Get the most recent pool metrics.

        Returns:
            PoolMetrics: Latest pool metrics, or None if no metrics available.
        """
        return self.pool_metrics[-1] if self.pool_metrics else None


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector(
    pool_manager: Optional[DatabasePoolManager] = None,
) -> MetricsCollector:
    """Get or create global metrics collector instance.

    Args:
        pool_manager: Database pool manager instance.

    Returns:
        MetricsCollector: Global metrics collector instance.
    """
    global _metrics_collector

    if _metrics_collector is None:
        _metrics_collector = MetricsCollector(pool_manager)

    return _metrics_collector


async def start_global_metrics_collection(interval: float = 30.0) -> None:
    """Start global metrics collection.

    Args:
        interval: Collection interval in seconds.
    """
    collector = get_metrics_collector()
    await collector.start_collection(interval)


async def stop_global_metrics_collection() -> None:
    """Stop global metrics collection."""
    global _metrics_collector
    if _metrics_collector:
        await _metrics_collector.stop_collection()
