"""综合测试框架 - 报告生成模块

该模块提供测试报告生成功能，包括：
- HTML报告生成
- JSON报告生成
- 性能报告生成
- 覆盖率报告生成
- 质量门控报告生成
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from jinja2 import Template, Environment, FileSystemLoader
import base64
import io

from .comprehensive_test_framework import (
    ComprehensiveTestFramework,
    TestExecutionResult,
    TestStatus,
    TestPriority
)

logger = logging.getLogger(__name__)


class TestReportGenerator:
    """测试报告生成器"""
    
    def __init__(self, framework: ComprehensiveTestFramework):
        """初始化报告生成器
        
        Args:
            framework: 综合测试框架实例
        """
        self.framework = framework
        self.report_dir = framework.work_dir / "test_reports"
        self.report_dir.mkdir(exist_ok=True)
        
        # 设置模板环境
        template_dir = Path(__file__).parent / "templates"
        template_dir.mkdir(exist_ok=True)
        self.jinja_env = Environment(loader=FileSystemLoader(str(template_dir)))
    
    def generate_html_report(self, execution_results: List[TestExecutionResult], 
                           quality_gate_results: Optional[Dict[str, Any]] = None,
                           output_file: Optional[str] = None) -> str:
        """生成HTML测试报告
        
        Args:
            execution_results: 测试执行结果列表
            quality_gate_results: 质量门控检查结果
            output_file: 输出文件路径，None表示自动生成
            
        Returns:
            生成的报告文件路径
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = self.report_dir / f"test_report_{timestamp}.html"
        else:
            output_file = Path(output_file)
        
        # 准备报告数据
        report_data = self._prepare_report_data(execution_results, quality_gate_results)
        
        # 生成HTML内容
        html_content = self._generate_html_content(report_data)
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML report generated: {output_file}")
        return str(output_file)
    
    def generate_json_report(self, execution_results: List[TestExecutionResult],
                           quality_gate_results: Optional[Dict[str, Any]] = None,
                           output_file: Optional[str] = None) -> str:
        """生成JSON测试报告
        
        Args:
            execution_results: 测试执行结果列表
            quality_gate_results: 质量门控检查结果
            output_file: 输出文件路径，None表示自动生成
            
        Returns:
            生成的报告文件路径
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = self.report_dir / f"test_report_{timestamp}.json"
        else:
            output_file = Path(output_file)
        
        # 准备报告数据
        report_data = self._prepare_report_data(execution_results, quality_gate_results)
        
        # 写入JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"JSON report generated: {output_file}")
        return str(output_file)
    
    def generate_performance_report(self, execution_results: List[TestExecutionResult],
                                  output_file: Optional[str] = None) -> str:
        """生成性能测试报告
        
        Args:
            execution_results: 测试执行结果列表
            output_file: 输出文件路径，None表示自动生成
            
        Returns:
            生成的报告文件路径
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = self.report_dir / f"performance_report_{timestamp}.html"
        else:
            output_file = Path(output_file)
        
        # 准备性能数据
        performance_data = self._prepare_performance_data(execution_results)
        
        # 生成性能报告HTML
        html_content = self._generate_performance_html(performance_data)
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"Performance report generated: {output_file}")
        return str(output_file)
    
    def generate_coverage_report(self, execution_results: List[TestExecutionResult],
                               output_file: Optional[str] = None) -> str:
        """生成覆盖率报告
        
        Args:
            execution_results: 测试执行结果列表
            output_file: 输出文件路径，None表示自动生成
            
        Returns:
            生成的报告文件路径
        """
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = self.report_dir / f"coverage_report_{timestamp}.html"
        else:
            output_file = Path(output_file)
        
        # 准备覆盖率数据
        coverage_data = self._prepare_coverage_data(execution_results)
        
        # 生成覆盖率报告HTML
        html_content = self._generate_coverage_html(coverage_data)
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"Coverage report generated: {output_file}")
        return str(output_file)
    
    def _prepare_report_data(self, execution_results: List[TestExecutionResult],
                           quality_gate_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """准备报告数据
        
        Args:
            execution_results: 测试执行结果列表
            quality_gate_results: 质量门控检查结果
            
        Returns:
            报告数据字典
        """
        # 计算总体统计
        total_tests = sum(r.test_count for r in execution_results)
        total_passed = sum(r.passed_count for r in execution_results)
        total_failed = sum(r.failed_count for r in execution_results)
        total_skipped = sum(r.skipped_count for r in execution_results)
        total_errors = sum(r.error_count for r in execution_results)
        total_duration = sum(r.duration for r in execution_results)
        
        # 计算成功率
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # 计算平均覆盖率
        coverages = [r.coverage_percentage for r in execution_results if r.coverage_percentage is not None]
        avg_coverage = sum(coverages) / len(coverages) if coverages else 0
        
        # 按状态分组
        results_by_status = {
            'passed': [r for r in execution_results if r.status == TestStatus.PASSED],
            'failed': [r for r in execution_results if r.status == TestStatus.FAILED],
            'error': [r for r in execution_results if r.status == TestStatus.ERROR],
            'skipped': [r for r in execution_results if r.status == TestStatus.SKIPPED]
        }
        
        # 按优先级分组
        results_by_priority = {}
        for result in execution_results:
            suite_config = self.framework.get_suite(result.suite_name)
            if suite_config:
                priority = suite_config.priority.value
                if priority not in results_by_priority:
                    results_by_priority[priority] = []
                results_by_priority[priority].append(result)
        
        return {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'framework_version': '1.0.0',
                'total_suites': len(execution_results),
                'execution_duration': total_duration
            },
            'summary': {
                'total_tests': total_tests,
                'passed': total_passed,
                'failed': total_failed,
                'skipped': total_skipped,
                'errors': total_errors,
                'success_rate': success_rate,
                'average_coverage': avg_coverage
            },
            'execution_results': [self._serialize_execution_result(r) for r in execution_results],
            'results_by_status': {
                status: [self._serialize_execution_result(r) for r in results]
                for status, results in results_by_status.items()
            },
            'results_by_priority': {
                priority: [self._serialize_execution_result(r) for r in results]
                for priority, results in results_by_priority.items()
            },
            'quality_gates': quality_gate_results,
            'performance_summary': self._get_performance_summary(execution_results),
            'coverage_summary': self._get_coverage_summary(execution_results)
        }
    
    def _serialize_execution_result(self, result: TestExecutionResult) -> Dict[str, Any]:
        """序列化测试执行结果
        
        Args:
            result: 测试执行结果
            
        Returns:
            序列化后的结果字典
        """
        return {
            'suite_name': result.suite_name,
            'status': result.status.value,
            'start_time': result.start_time.isoformat() if result.start_time else None,
            'end_time': result.end_time.isoformat() if result.end_time else None,
            'duration': result.duration,
            'test_count': result.test_count,
            'passed_count': result.passed_count,
            'failed_count': result.failed_count,
            'skipped_count': result.skipped_count,
            'error_count': result.error_count,
            'coverage_percentage': result.coverage_percentage,
            'error_details': result.error_details,
            'log_file': result.log_file,
            'performance_metrics': self._serialize_performance_metrics(result.performance_metrics),
            'memory_usage': result.memory_usage
        }
    
    def _serialize_performance_metrics(self, metrics) -> Optional[Dict[str, Any]]:
        """序列化性能指标
        
        Args:
            metrics: 性能指标对象
            
        Returns:
            序列化后的性能指标字典
        """
        if not metrics:
            return None
        
        return {
            'avg_response_time': getattr(metrics, 'avg_response_time', None),
            'max_response_time': getattr(metrics, 'max_response_time', None),
            'min_response_time': getattr(metrics, 'min_response_time', None),
            'throughput': getattr(metrics, 'throughput', None),
            'cpu_usage': getattr(metrics, 'cpu_usage', None),
            'memory_usage': getattr(metrics, 'memory_usage', None),
            'disk_io': getattr(metrics, 'disk_io', None),
            'network_io': getattr(metrics, 'network_io', None)
        }
    
    def _get_performance_summary(self, execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """获取性能摘要
        
        Args:
            execution_results: 测试执行结果列表
            
        Returns:
            性能摘要字典
        """
        performance_results = [r for r in execution_results if r.performance_metrics]
        
        if not performance_results:
            return {'available': False}
        
        # 计算平均性能指标
        avg_response_times = []
        max_response_times = []
        throughputs = []
        cpu_usages = []
        memory_usages = []
        
        for result in performance_results:
            metrics = result.performance_metrics
            if hasattr(metrics, 'avg_response_time') and metrics.avg_response_time:
                avg_response_times.append(metrics.avg_response_time)
            if hasattr(metrics, 'max_response_time') and metrics.max_response_time:
                max_response_times.append(metrics.max_response_time)
            if hasattr(metrics, 'throughput') and metrics.throughput:
                throughputs.append(metrics.throughput)
            if hasattr(metrics, 'cpu_usage') and metrics.cpu_usage:
                cpu_usages.append(metrics.cpu_usage)
            if hasattr(metrics, 'memory_usage') and metrics.memory_usage:
                memory_usages.append(metrics.memory_usage)
        
        return {
            'available': True,
            'avg_response_time': sum(avg_response_times) / len(avg_response_times) if avg_response_times else None,
            'max_response_time': max(max_response_times) if max_response_times else None,
            'avg_throughput': sum(throughputs) / len(throughputs) if throughputs else None,
            'avg_cpu_usage': sum(cpu_usages) / len(cpu_usages) if cpu_usages else None,
            'avg_memory_usage': sum(memory_usages) / len(memory_usages) if memory_usages else None
        }
    
    def _get_coverage_summary(self, execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """获取覆盖率摘要
        
        Args:
            execution_results: 测试执行结果列表
            
        Returns:
            覆盖率摘要字典
        """
        coverages = [r.coverage_percentage for r in execution_results if r.coverage_percentage is not None]
        
        if not coverages:
            return {'available': False}
        
        return {
            'available': True,
            'average': sum(coverages) / len(coverages),
            'minimum': min(coverages),
            'maximum': max(coverages),
            'suites_with_coverage': len(coverages),
            'total_suites': len(execution_results)
        }
    
    def _prepare_performance_data(self, execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """准备性能数据
        
        Args:
            execution_results: 测试执行结果列表
            
        Returns:
            性能数据字典
        """
        performance_results = [r for r in execution_results if r.performance_metrics]
        
        # 按时间排序的性能数据
        time_series_data = []
        for result in sorted(performance_results, key=lambda x: x.start_time or datetime.min):
            metrics = result.performance_metrics
            time_series_data.append({
                'timestamp': result.start_time.isoformat() if result.start_time else None,
                'suite_name': result.suite_name,
                'response_time': getattr(metrics, 'avg_response_time', None),
                'throughput': getattr(metrics, 'throughput', None),
                'cpu_usage': getattr(metrics, 'cpu_usage', None),
                'memory_usage': getattr(metrics, 'memory_usage', None)
            })
        
        # 性能趋势分析
        trends = self._analyze_performance_trends(time_series_data)
        
        return {
            'time_series': time_series_data,
            'trends': trends,
            'summary': self._get_performance_summary(execution_results)
        }
    
    def _analyze_performance_trends(self, time_series_data: List[Dict[str, Any]]) -> Dict[str, str]:
        """分析性能趋势
        
        Args:
            time_series_data: 时间序列数据
            
        Returns:
            趋势分析结果
        """
        if len(time_series_data) < 2:
            return {'response_time': 'insufficient_data', 'throughput': 'insufficient_data'}
        
        # 简单的趋势分析（比较首尾数据）
        first = time_series_data[0]
        last = time_series_data[-1]
        
        trends = {}
        
        # 响应时间趋势
        if first.get('response_time') and last.get('response_time'):
            if last['response_time'] > first['response_time'] * 1.1:
                trends['response_time'] = 'degrading'
            elif last['response_time'] < first['response_time'] * 0.9:
                trends['response_time'] = 'improving'
            else:
                trends['response_time'] = 'stable'
        else:
            trends['response_time'] = 'no_data'
        
        # 吞吐量趋势
        if first.get('throughput') and last.get('throughput'):
            if last['throughput'] > first['throughput'] * 1.1:
                trends['throughput'] = 'improving'
            elif last['throughput'] < first['throughput'] * 0.9:
                trends['throughput'] = 'degrading'
            else:
                trends['throughput'] = 'stable'
        else:
            trends['throughput'] = 'no_data'
        
        return trends
    
    def _prepare_coverage_data(self, execution_results: List[TestExecutionResult]) -> Dict[str, Any]:
        """准备覆盖率数据
        
        Args:
            execution_results: 测试执行结果列表
            
        Returns:
            覆盖率数据字典
        """
        coverage_results = [r for r in execution_results if r.coverage_percentage is not None]
        
        # 按覆盖率分组
        coverage_ranges = {
            'excellent': [r for r in coverage_results if r.coverage_percentage >= 90],
            'good': [r for r in coverage_results if 80 <= r.coverage_percentage < 90],
            'fair': [r for r in coverage_results if 70 <= r.coverage_percentage < 80],
            'poor': [r for r in coverage_results if r.coverage_percentage < 70]
        }
        
        return {
            'summary': self._get_coverage_summary(execution_results),
            'by_range': {
                range_name: {
                    'count': len(results),
                    'suites': [r.suite_name for r in results]
                }
                for range_name, results in coverage_ranges.items()
            },
            'detailed': [
                {
                    'suite_name': r.suite_name,
                    'coverage_percentage': r.coverage_percentage,
                    'test_count': r.test_count
                }
                for r in sorted(coverage_results, key=lambda x: x.coverage_percentage, reverse=True)
            ]
        }
    
    def _generate_html_content(self, report_data: Dict[str, Any]) -> str:
        """生成HTML报告内容
        
        Args:
            report_data: 报告数据
            
        Returns:
            HTML内容字符串
        """
        # 使用内置模板或创建简单的HTML
        template_content = self._get_html_template()
        template = Template(template_content)
        
        return template.render(**report_data)
    
    def _generate_performance_html(self, performance_data: Dict[str, Any]) -> str:
        """生成性能报告HTML
        
        Args:
            performance_data: 性能数据
            
        Returns:
            HTML内容字符串
        """
        template_content = self._get_performance_template()
        template = Template(template_content)
        
        return template.render(performance_data=performance_data)
    
    def _generate_coverage_html(self, coverage_data: Dict[str, Any]) -> str:
        """生成覆盖率报告HTML
        
        Args:
            coverage_data: 覆盖率数据
            
        Returns:
            HTML内容字符串
        """
        template_content = self._get_coverage_template()
        template = Template(template_content)
        
        return template.render(coverage_data=coverage_data)
    
    def _get_html_template(self) -> str:
        """获取HTML报告模板
        
        Returns:
            HTML模板字符串
        """
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 24px; font-weight: bold; }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .error { color: #fd7e14; }
        .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: bold; }
        .status-badge { padding: 4px 8px; border-radius: 4px; color: white; font-size: 12px; }
        .status-passed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .status-error { background-color: #fd7e14; }
        .status-skipped { background-color: #6c757d; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .progress-bar { width: 100%; height: 20px; background-color: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background-color: #28a745; transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>综合测试框架报告</h1>
            <p>生成时间: {{ metadata.generated_at }}</p>
        </div>
        
        <div class="section">
            <h2>测试摘要</h2>
            <div class="summary">
                <div class="summary-card">
                    <h3>总测试数</h3>
                    <div class="value">{{ summary.total_tests }}</div>
                </div>
                <div class="summary-card">
                    <h3>通过</h3>
                    <div class="value passed">{{ summary.passed }}</div>
                </div>
                <div class="summary-card">
                    <h3>失败</h3>
                    <div class="value failed">{{ summary.failed }}</div>
                </div>
                <div class="summary-card">
                    <h3>跳过</h3>
                    <div class="value skipped">{{ summary.skipped }}</div>
                </div>
                <div class="summary-card">
                    <h3>错误</h3>
                    <div class="value error">{{ summary.errors }}</div>
                </div>
                <div class="summary-card">
                    <h3>成功率</h3>
                    <div class="value">{{ "%.1f" | format(summary.success_rate) }}%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {{ summary.success_rate }}%"></div>
                    </div>
                </div>
                <div class="summary-card">
                    <h3>平均覆盖率</h3>
                    <div class="value">{{ "%.1f" | format(summary.average_coverage) }}%</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {{ summary.average_coverage }}%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>测试套件详情</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>套件名称</th>
                        <th>状态</th>
                        <th>测试数</th>
                        <th>通过</th>
                        <th>失败</th>
                        <th>跳过</th>
                        <th>错误</th>
                        <th>覆盖率</th>
                        <th>耗时(秒)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for result in execution_results %}
                    <tr>
                        <td>{{ result.suite_name }}</td>
                        <td>
                            <span class="status-badge status-{{ result.status }}">{{ result.status.upper() }}</span>
                        </td>
                        <td>{{ result.test_count }}</td>
                        <td class="passed">{{ result.passed_count }}</td>
                        <td class="failed">{{ result.failed_count }}</td>
                        <td class="skipped">{{ result.skipped_count }}</td>
                        <td class="error">{{ result.error_count }}</td>
                        <td>
                            {% if result.coverage_percentage %}
                                {{ "%.1f" | format(result.coverage_percentage) }}%
                            {% else %}
                                N/A
                            {% endif %}
                        </td>
                        <td>{{ "%.2f" | format(result.duration) }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% if quality_gates %}
        <div class="section">
            <h2>质量门控</h2>
            <div class="summary">
                {% for gate_name, gate_result in quality_gates.items() %}
                <div class="summary-card">
                    <h3>{{ gate_name }}</h3>
                    <div class="value {{ 'passed' if gate_result.overall_passed else 'failed' }}">
                        {{ '通过' if gate_result.overall_passed else '失败' }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        {% if performance_summary.available %}
        <div class="section">
            <h2>性能摘要</h2>
            <div class="summary">
                {% if performance_summary.avg_response_time %}
                <div class="summary-card">
                    <h3>平均响应时间</h3>
                    <div class="value">{{ "%.2f" | format(performance_summary.avg_response_time) }}ms</div>
                </div>
                {% endif %}
                {% if performance_summary.avg_throughput %}
                <div class="summary-card">
                    <h3>平均吞吐量</h3>
                    <div class="value">{{ "%.2f" | format(performance_summary.avg_throughput) }}</div>
                </div>
                {% endif %}
                {% if performance_summary.avg_cpu_usage %}
                <div class="summary-card">
                    <h3>平均CPU使用率</h3>
                    <div class="value">{{ "%.1f" | format(performance_summary.avg_cpu_usage) }}%</div>
                </div>
                {% endif %}
                {% if performance_summary.avg_memory_usage %}
                <div class="summary-card">
                    <h3>平均内存使用</h3>
                    <div class="value">{{ "%.1f" | format(performance_summary.avg_memory_usage) }}MB</div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</body>
</html>
        """
    
    def _get_performance_template(self) -> str:
        """获取性能报告模板
        
        Returns:
            性能报告HTML模板字符串
        """
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 24px; font-weight: bold; }
        .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: bold; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .trend-improving { color: #28a745; }
        .trend-degrading { color: #dc3545; }
        .trend-stable { color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>性能测试报告</h1>
        </div>
        
        {% if performance_data.summary.available %}
        <div class="section">
            <h2>性能摘要</h2>
            <div class="summary">
                {% if performance_data.summary.avg_response_time %}
                <div class="summary-card">
                    <h3>平均响应时间</h3>
                    <div class="value">{{ "%.2f" | format(performance_data.summary.avg_response_time) }}ms</div>
                </div>
                {% endif %}
                {% if performance_data.summary.max_response_time %}
                <div class="summary-card">
                    <h3>最大响应时间</h3>
                    <div class="value">{{ "%.2f" | format(performance_data.summary.max_response_time) }}ms</div>
                </div>
                {% endif %}
                {% if performance_data.summary.avg_throughput %}
                <div class="summary-card">
                    <h3>平均吞吐量</h3>
                    <div class="value">{{ "%.2f" | format(performance_data.summary.avg_throughput) }}</div>
                </div>
                {% endif %}
                {% if performance_data.summary.avg_cpu_usage %}
                <div class="summary-card">
                    <h3>平均CPU使用率</h3>
                    <div class="value">{{ "%.1f" | format(performance_data.summary.avg_cpu_usage) }}%</div>
                </div>
                {% endif %}
                {% if performance_data.summary.avg_memory_usage %}
                <div class="summary-card">
                    <h3>平均内存使用</h3>
                    <div class="value">{{ "%.1f" | format(performance_data.summary.avg_memory_usage) }}MB</div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="section">
            <h2>性能趋势</h2>
            <div class="summary">
                <div class="summary-card">
                    <h3>响应时间趋势</h3>
                    <div class="value trend-{{ performance_data.trends.response_time }}">
                        {% if performance_data.trends.response_time == 'improving' %}
                            改善
                        {% elif performance_data.trends.response_time == 'degrading' %}
                            恶化
                        {% elif performance_data.trends.response_time == 'stable' %}
                            稳定
                        {% else %}
                            无数据
                        {% endif %}
                    </div>
                </div>
                <div class="summary-card">
                    <h3>吞吐量趋势</h3>
                    <div class="value trend-{{ performance_data.trends.throughput }}">
                        {% if performance_data.trends.throughput == 'improving' %}
                            改善
                        {% elif performance_data.trends.throughput == 'degrading' %}
                            恶化
                        {% elif performance_data.trends.throughput == 'stable' %}
                            稳定
                        {% else %}
                            无数据
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>详细性能数据</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>套件名称</th>
                        <th>响应时间(ms)</th>
                        <th>吞吐量</th>
                        <th>CPU使用率(%)</th>
                        <th>内存使用(MB)</th>
                    </tr>
                </thead>
                <tbody>
                    {% for data in performance_data.time_series %}
                    <tr>
                        <td>{{ data.timestamp or 'N/A' }}</td>
                        <td>{{ data.suite_name }}</td>
                        <td>{{ "%.2f" | format(data.response_time) if data.response_time else 'N/A' }}</td>
                        <td>{{ "%.2f" | format(data.throughput) if data.throughput else 'N/A' }}</td>
                        <td>{{ "%.1f" | format(data.cpu_usage) if data.cpu_usage else 'N/A' }}</td>
                        <td>{{ "%.1f" | format(data.memory_usage) if data.memory_usage else 'N/A' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="section">
            <p>没有可用的性能数据。</p>
        </div>
        {% endif %}
    </div>
</body>
</html>
        """
    
    def _get_coverage_template(self) -> str:
        """获取覆盖率报告模板
        
        Returns:
            覆盖率报告HTML模板字符串
        """
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>覆盖率报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 24px; font-weight: bold; }
        .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .table th, .table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: bold; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .excellent { color: #28a745; }
        .good { color: #17a2b8; }
        .fair { color: #ffc107; }
        .poor { color: #dc3545; }
        .progress-bar { width: 100%; height: 20px; background-color: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; transition: width 0.3s ease; }
        .progress-excellent { background-color: #28a745; }
        .progress-good { background-color: #17a2b8; }
        .progress-fair { background-color: #ffc107; }
        .progress-poor { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>代码覆盖率报告</h1>
        </div>
        
        {% if coverage_data.summary.available %}
        <div class="section">
            <h2>覆盖率摘要</h2>
            <div class="summary">
                <div class="summary-card">
                    <h3>平均覆盖率</h3>
                    <div class="value">{{ "%.1f" | format(coverage_data.summary.average) }}%</div>
                    <div class="progress-bar">
                        <div class="progress-fill progress-{% if coverage_data.summary.average >= 90 %}excellent{% elif coverage_data.summary.average >= 80 %}good{% elif coverage_data.summary.average >= 70 %}fair{% else %}poor{% endif %}" style="width: {{ coverage_data.summary.average }}%"></div>
                    </div>
                </div>
                <div class="summary-card">
                    <h3>最高覆盖率</h3>
                    <div class="value excellent">{{ "%.1f" | format(coverage_data.summary.maximum) }}%</div>
                </div>
                <div class="summary-card">
                    <h3>最低覆盖率</h3>
                    <div class="value poor">{{ "%.1f" | format(coverage_data.summary.minimum) }}%</div>
                </div>
                <div class="summary-card">
                    <h3>有覆盖率数据的套件</h3>
                    <div class="value">{{ coverage_data.summary.suites_with_coverage }}/{{ coverage_data.summary.total_suites }}</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>覆盖率分布</h2>
            <div class="summary">
                <div class="summary-card">
                    <h3>优秀 (≥90%)</h3>
                    <div class="value excellent">{{ coverage_data.by_range.excellent.count }}</div>
                </div>
                <div class="summary-card">
                    <h3>良好 (80-89%)</h3>
                    <div class="value good">{{ coverage_data.by_range.good.count }}</div>
                </div>
                <div class="summary-card">
                    <h3>一般 (70-79%)</h3>
                    <div class="value fair">{{ coverage_data.by_range.fair.count }}</div>
                </div>
                <div class="summary-card">
                    <h3>较差 (<70%)</h3>
                    <div class="value poor">{{ coverage_data.by_range.poor.count }}</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>详细覆盖率数据</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>套件名称</th>
                        <th>覆盖率</th>
                        <th>测试数量</th>
                        <th>等级</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in coverage_data.detailed %}
                    <tr>
                        <td>{{ item.suite_name }}</td>
                        <td>
                            <div class="progress-bar">
                                <div class="progress-fill progress-{% if item.coverage_percentage >= 90 %}excellent{% elif item.coverage_percentage >= 80 %}good{% elif item.coverage_percentage >= 70 %}fair{% else %}poor{% endif %}" style="width: {{ item.coverage_percentage }}%"></div>
                            </div>
                            {{ "%.1f" | format(item.coverage_percentage) }}%
                        </td>
                        <td>{{ item.test_count }}</td>
                        <td>
                            <span class="{% if item.coverage_percentage >= 90 %}excellent{% elif item.coverage_percentage >= 80 %}good{% elif item.coverage_percentage >= 70 %}fair{% else %}poor{% endif %}">
                                {% if item.coverage_percentage >= 90 %}
                                    优秀
                                {% elif item.coverage_percentage >= 80 %}
                                    良好
                                {% elif item.coverage_percentage >= 70 %}
                                    一般
                                {% else %}
                                    较差
                                {% endif %}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="section">
            <p>没有可用的覆盖率数据。</p>
        </div>
        {% endif %}
    </div>
</body>
</html>
        """