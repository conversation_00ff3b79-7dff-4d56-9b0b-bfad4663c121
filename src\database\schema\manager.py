"""Schema management module for PostgreSQL database operations.

This module provides comprehensive schema and table management functionality
including creation, deletion, validation, and dynamic table creation from DataFrames.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Union

import asyncpg
import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.schema import CreateTable

from ..exceptions import (
    DatabaseError,
    SchemaError,
    SchemaNotFoundError,
    TableExistsError,
    ValidationError,
)
from ..utils.validators import InputValidator
from .models import (
    TableDefinition,
    create_table_from_dataframe,
    validate_table_structure,
)

logger = logging.getLogger(__name__)


class SchemaManager:
    """Comprehensive schema and table management for PostgreSQL.

    This class provides async methods for managing schemas and tables,
    including dynamic table creation from pandas DataFrames.
    """

    def __init__(
        self, connection_pool: asyncpg.Pool, validator: Optional[InputValidator] = None
    ):
        """Initialize schema manager.

        Args:
            connection_pool: AsyncPG connection pool
            validator: Input validator instance for name validation
        """
        self.pool = connection_pool
        self.validator = validator or InputValidator()
        self.table_def = TableDefinition(self.validator)

        # Create SQLAlchemy engine for SQL compilation
        # Note: This is only used for SQL generation, not actual connections
        self._engine = create_engine(
            "postgresql://", strategy="mock", executor=lambda sql, *_: None
        )

    async def create_schema(self, schema_name: str) -> bool:
        """Create a new schema.

        Args:
            schema_name: Name of the schema to create

        Returns:
            True if schema was created successfully

        Raises:
            ValidationError: If schema name is invalid
            DatabaseError: If schema creation fails
        """
        # Validate schema name
        if not self.validator.validate_identifier(schema_name):
            raise ValidationError(f"Invalid schema name: {schema_name}")

        # Check permissions before attempting creation
        await self._check_schema_creation_permissions()

        retry_count = 0
        max_retries = 3
        retry_delay = 1.0

        while retry_count <= max_retries:
            try:
                async with self.pool.acquire() as conn:
                    # Check if schema already exists
                    if await self.schema_exists(schema_name):
                        logger.info(f"Schema '{schema_name}' already exists")
                        return True

                    # Validate connection before schema creation
                    await self._validate_connection_for_schema_ops(conn)

                    # Create schema with explicit error handling
                    await conn.execute(f'CREATE SCHEMA IF NOT EXISTS "{schema_name}"')

                    # Verify schema was created
                    if await self.schema_exists(schema_name):
                        logger.info(f"Schema '{schema_name}' created successfully")
                        return True
                    else:
                        raise DatabaseError(
                            f"Schema '{schema_name}' creation appeared to succeed but schema does not exist"
                        )

            except asyncpg.InsufficientPrivilegeError as e:
                error_msg = (
                    f"Insufficient privileges to create schema '{schema_name}': {e}"
                )
                logger.error(error_msg)
                raise DatabaseError(error_msg) from e
            except asyncpg.DuplicateSchemaError as e:
                # Schema was created by another process, this is OK
                logger.info(
                    f"Schema '{schema_name}' already exists (created concurrently)"
                )
                return True
            except asyncpg.PostgresError as e:
                if retry_count < max_retries and "connection" in str(e).lower():
                    retry_count += 1
                    logger.warning(
                        f"Connection error creating schema '{schema_name}', retrying ({retry_count}/{max_retries}): {e}"
                    )
                    await asyncio.sleep(retry_delay * retry_count)
                    continue
                error_msg = f"Failed to create schema '{schema_name}': {e}"
                logger.error(error_msg)
                raise DatabaseError(error_msg) from e
            except Exception as e:
                error_msg = f"Unexpected error creating schema '{schema_name}': {e}"
                logger.error(error_msg)
                raise DatabaseError(error_msg) from e

        # This should never be reached due to the exception handling above
        raise DatabaseError(
            f"Failed to create schema '{schema_name}': Maximum retries exceeded"
        )

    async def drop_schema(self, schema_name: str, cascade: bool = False) -> bool:
        """Drop an existing schema.

        Args:
            schema_name: Name of the schema to drop
            cascade: Whether to drop all objects in the schema

        Returns:
            True if schema was dropped successfully

        Raises:
            ValidationError: If schema name is invalid
            SchemaNotFoundError: If schema doesn't exist
            DatabaseError: If schema deletion fails
        """
        # Validate schema name
        if not self.validator.validate_identifier(schema_name):
            raise ValidationError(f"Invalid schema name: {schema_name}")

        try:
            async with self.pool.acquire() as conn:
                # Check if schema exists
                if not await self.schema_exists(schema_name):
                    raise SchemaNotFoundError(f"Schema '{schema_name}' does not exist")

                # Drop schema
                cascade_clause = "CASCADE" if cascade else "RESTRICT"
                await conn.execute(f'DROP SCHEMA "{schema_name}" {cascade_clause}')
                logger.info(f"Schema '{schema_name}' dropped successfully")
                return True

        except SchemaNotFoundError:
            raise
        except asyncpg.PostgresError as e:
            logger.error(f"Failed to drop schema '{schema_name}': {e}")
            raise DatabaseError(f"Failed to drop schema '{schema_name}': {e}") from e
        except Exception as e:
            logger.error(f"Unexpected error dropping schema '{schema_name}': {e}")
            raise DatabaseError(
                f"Unexpected error dropping schema '{schema_name}': {e}"
            ) from e

    async def schema_exists(self, schema_name: str) -> bool:
        """Check if a schema exists.

        Args:
            schema_name: Name of the schema to check

        Returns:
            True if schema exists, False otherwise

        Raises:
            ValidationError: If schema name is invalid
            DatabaseError: If check operation fails
        """
        # Validate schema name
        if not self.validator.validate_identifier(schema_name):
            raise ValidationError(f"Invalid schema name: {schema_name}")

        try:
            async with self.pool.acquire() as conn:
                result = await conn.fetchval(
                    "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = $1)",
                    schema_name,
                )
                return bool(result)

        except asyncpg.PostgresError as e:
            logger.error(f"Failed to check schema existence '{schema_name}': {e}")
            raise DatabaseError(
                f"Failed to check schema existence '{schema_name}': {e}"
            ) from e
        except Exception as e:
            logger.error(f"Unexpected error checking schema '{schema_name}': {e}")
            raise DatabaseError(
                f"Unexpected error checking schema '{schema_name}': {e}"
            ) from e

    async def ensure_schema_exists(self, schema_name: str) -> bool:
        """Ensure a schema exists, creating it if necessary.

        Args:
            schema_name: Name of the schema to ensure exists

        Returns:
            True if schema exists or was created successfully

        Raises:
            ValidationError: If schema name is invalid
            DatabaseError: If schema operations fail
        """
        if await self.schema_exists(schema_name):
            logger.debug(f"Schema '{schema_name}' already exists")
            return True

        return await self.create_schema(schema_name)

    async def list_schemas(self) -> List[str]:
        """List all schemas in the database.

        Returns:
            List of schema names

        Raises:
            DatabaseError: If listing operation fails
        """
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(
                    "SELECT schema_name FROM information_schema.schemata "
                    "WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast') "
                    "ORDER BY schema_name"
                )
                return [row["schema_name"] for row in rows]

        except asyncpg.PostgresError as e:
            logger.error(f"Failed to list schemas: {e}")
            raise DatabaseError(f"Failed to list schemas: {e}") from e
        except Exception as e:
            logger.error(f"Unexpected error listing schemas: {e}")
            raise DatabaseError(f"Unexpected error listing schemas: {e}") from e

    async def _check_schema_creation_permissions(self) -> None:
        """Check if current user has permissions to create schemas.

        Raises:
            DatabaseError: If user lacks schema creation permissions
        """
        try:
            async with self.pool.acquire() as conn:
                # Check if user has CREATE privilege on database
                result = await conn.fetchval(
                    "SELECT has_database_privilege(current_user, current_database(), 'CREATE')"
                )
                if not result:
                    raise DatabaseError(
                        "Current user lacks CREATE privilege on database"
                    )
                logger.debug("Schema creation permissions verified")
        except Exception as e:
            error_msg = f"Failed to check schema creation permissions: {e}"
            logger.error(error_msg)
            raise DatabaseError(error_msg) from e

    async def _validate_connection_for_schema_ops(self, conn) -> None:
        """Validate connection is suitable for schema operations.

        Args:
            conn: Database connection to validate

        Raises:
            DatabaseError: If connection is not suitable for schema operations
        """
        try:
            # Test basic connectivity and permissions
            await conn.fetchval("SELECT current_user, current_database()")

            # Test if we can query system catalogs (needed for schema operations)
            await conn.fetchval(
                "SELECT count(*) FROM information_schema.schemata WHERE schema_name = 'public'"
            )

            logger.debug("Connection validated for schema operations")
        except Exception as e:
            error_msg = f"Connection validation failed for schema operations: {e}"
            logger.error(error_msg)
            raise DatabaseError(error_msg) from e

    async def table_exists(self, table_name: str, schema_name: str) -> bool:
        """Check if a table exists in the specified schema.

        Args:
            table_name: Name of the table to check
            schema_name: Name of the schema containing the table

        Returns:
            True if table exists, False otherwise

        Raises:
            ValidationError: If table or schema name is invalid
            DatabaseError: If check operation fails
        """
        # Validate names
        if not self.validator.validate_identifier(table_name):
            raise ValidationError(f"Invalid table name: {table_name}")
        if not self.validator.validate_identifier(schema_name):
            raise ValidationError(f"Invalid schema name: {schema_name}")

        try:
            async with self.pool.acquire() as conn:
                result = await conn.fetchval(
                    "SELECT EXISTS(SELECT 1 FROM information_schema.tables "
                    "WHERE table_schema = $1 AND table_name = $2)",
                    schema_name,
                    table_name,
                )
                return bool(result)

        except asyncpg.PostgresError as e:
            logger.error(
                f"Failed to check table existence '{schema_name}.{table_name}': {e}"
            )
            raise DatabaseError(
                f"Failed to check table existence '{schema_name}.{table_name}': {e}"
            ) from e
        except Exception as e:
            logger.error(
                f"Unexpected error checking table '{schema_name}.{table_name}': {e}"
            )
            raise DatabaseError(
                f"Unexpected error checking table '{schema_name}.{table_name}': {e}"
            ) from e

    async def create_table(self, table_schema) -> bool:
        """Create a table from TableSchema object.

        Args:
            table_schema: TableSchema object containing table definition

        Returns:
            True if table was created successfully

        Raises:
            ValidationError: If table schema is invalid
            DatabaseError: If table creation fails
        """
        # Validate table schema
        if not table_schema.is_valid():
            raise ValidationError(f"Invalid table schema for '{table_schema.name}'")

        # Validate table name
        if not self.validator.validate_identifier(table_schema.name):
            raise ValidationError(f"Invalid table name: {table_schema.name}")

        # Validate schema name if provided
        schema_name = table_schema.schema or "public"
        if not self.validator.validate_identifier(schema_name):
            raise ValidationError(f"Invalid schema name: {schema_name}")

        try:
            # Ensure schema exists
            await self.ensure_schema_exists(schema_name)

            # Check if table already exists
            if await self.table_exists(table_schema.name, schema_name):
                logger.info(f"Table '{schema_name}.{table_schema.name}' already exists")
                return True

            # Build CREATE TABLE SQL
            columns_sql = []
            for column in table_schema.columns:
                col_def = f'"{column.name}" {column.data_type}'

                if not column.nullable:
                    col_def += " NOT NULL"

                if column.primary_key:
                    col_def += " PRIMARY KEY"

                if column.unique:
                    col_def += " UNIQUE"

                if column.default_value:
                    col_def += f" DEFAULT {column.default_value}"

                columns_sql.append(col_def)

            # Create table SQL
            create_sql = f'CREATE TABLE "{schema_name}"."{table_schema.name}" ({", ".join(columns_sql)})'

            async with self.pool.acquire() as conn:
                await conn.execute(create_sql)
                logger.info(
                    f"Table '{schema_name}.{table_schema.name}' created successfully"
                )

            # Create indexes if any
            for index in table_schema.indexes:
                await self._create_index(table_schema.name, schema_name, index)

            return True

        except asyncpg.PostgresError as e:
            logger.error(
                f"Failed to create table '{schema_name}.{table_schema.name}': {e}"
            )
            raise DatabaseError(
                f"Failed to create table '{schema_name}.{table_schema.name}': {e}"
            ) from e
        except Exception as e:
            logger.error(
                f"Unexpected error creating table '{schema_name}.{table_schema.name}': {e}"
            )
            raise DatabaseError(
                f"Unexpected error creating table '{schema_name}.{table_schema.name}': {e}"
            ) from e

    async def _create_index(
        self, table_name: str, schema_name: str, index_schema
    ) -> bool:
        """Create an index on a table.

        Args:
            table_name: Name of the table
            schema_name: Name of the schema
            index_schema: IndexSchema object

        Returns:
            True if index was created successfully
        """
        try:
            # Build CREATE INDEX SQL
            unique_clause = "UNIQUE " if index_schema.unique else ""
            columns_clause = ", ".join(f'"{col}"' for col in index_schema.columns)

            create_index_sql = (
                f'CREATE {unique_clause}INDEX "{index_schema.name}" '
                f'ON "{schema_name}"."{table_name}" ({columns_clause})'
            )

            async with self.pool.acquire() as conn:
                await conn.execute(create_index_sql)
                logger.info(f"Index '{index_schema.name}' created successfully")

            return True

        except asyncpg.PostgresError as e:
            logger.error(f"Failed to create index '{index_schema.name}': {e}")
            raise DatabaseError(
                f"Failed to create index '{index_schema.name}': {e}"
            ) from e

    async def create_table_from_dataframe(
        self,
        df: pd.DataFrame,
        table_name: str,
        schema_name: str,
        if_exists: str = "fail",
    ) -> bool:
        """Create table from pandas DataFrame with enhanced structure.

        Args:
            df: Pandas DataFrame to create table from
            table_name: Name of the table to create
            schema_name: Name of the schema to create table in
            if_exists: Action if table exists ('fail', 'replace', 'append', 'skip')

        Returns:
            True if table was created successfully

        Raises:
            ValidationError: If names are invalid
            TableExistsError: If table exists and if_exists='fail'
            SchemaNotFoundError: If schema doesn't exist
            DatabaseError: If table creation fails
        """
        # Validate parameters
        if df.empty:
            raise ValidationError("Cannot create table from empty DataFrame")

        if not self.validator.validate_identifier(table_name):
            raise ValidationError(f"Invalid table name: {table_name}")
        if not self.validator.validate_identifier(schema_name):
            raise ValidationError(f"Invalid schema name: {schema_name}")

        if if_exists not in ("fail", "replace", "append", "skip"):
            raise ValidationError(f"Invalid if_exists value: {if_exists}")

        try:
            # Ensure schema exists
            await self.ensure_schema_exists(schema_name)

            # Check if table exists
            table_exists = await self.table_exists(table_name, schema_name)

            if table_exists:
                if if_exists == "fail":
                    raise TableExistsError(
                        f"Table '{schema_name}.{table_name}' already exists"
                    )
                elif if_exists == "skip":
                    logger.info(
                        f"Table '{schema_name}.{table_name}' exists, skipping creation"
                    )
                    return True
                elif if_exists == "replace":
                    await self._drop_table(table_name, schema_name)
                    logger.info(
                        f"Table '{schema_name}.{table_name}' dropped for replacement"
                    )
                elif if_exists == "append":
                    logger.info(
                        f"Table '{schema_name}.{table_name}' exists, will append data"
                    )
                    return True

            # Create SQLAlchemy table definition
            table = create_table_from_dataframe(
                df=df,
                table_name=table_name,
                schema_name=schema_name,
                validator=self.validator,
            )

            # Validate table structure
            validate_table_structure(table)

            # Generate CREATE TABLE SQL
            create_sql = self.table_def.generate_create_sql(table, self._engine)

            # Execute CREATE TABLE
            async with self.pool.acquire() as conn:
                await conn.execute(create_sql)
                logger.info(f"Table '{schema_name}.{table_name}' created successfully")
                return True

        except (ValidationError, TableExistsError, SchemaNotFoundError):
            raise
        except asyncpg.PostgresError as e:
            logger.error(f"Failed to create table '{schema_name}.{table_name}': {e}")
            raise DatabaseError(
                f"Failed to create table '{schema_name}.{table_name}': {e}"
            ) from e
        except Exception as e:
            logger.error(
                f"Unexpected error creating table '{schema_name}.{table_name}': {e}"
            )
            raise DatabaseError(
                f"Unexpected error creating table '{schema_name}.{table_name}': {e}"
            ) from e

    async def handle_table_operation(
        self, table_name: str, schema_name: str, operation: str
    ) -> bool:
        """Handle various table operations.

        Args:
            table_name: Name of the table
            schema_name: Name of the schema
            operation: Operation to perform ('create', 'drop', 'truncate', 'exists')

        Returns:
            Result of the operation

        Raises:
            ValidationError: If parameters are invalid
            DatabaseError: If operation fails
        """
        # Validate parameters
        if not self.validator.validate_identifier(table_name):
            raise ValidationError(f"Invalid table name: {table_name}")
        if not self.validator.validate_identifier(schema_name):
            raise ValidationError(f"Invalid schema name: {schema_name}")

        valid_operations = ("create", "drop", "truncate", "exists")
        if operation not in valid_operations:
            raise ValidationError(
                f"Invalid operation: {operation}. Must be one of {valid_operations}"
            )

        try:
            if operation == "exists":
                return await self.table_exists(table_name, schema_name)
            elif operation == "drop":
                return await self._drop_table(table_name, schema_name)
            elif operation == "truncate":
                return await self._truncate_table(table_name, schema_name)
            elif operation == "create":
                # For create operation, we need a DataFrame - this is a simplified version
                raise ValidationError(
                    "Create operation requires DataFrame. Use create_table_from_dataframe instead."
                )

        except ValidationError:
            raise
        except Exception as e:
            logger.error(
                f"Failed to perform operation '{operation}' on table '{schema_name}.{table_name}': {e}"
            )
            raise DatabaseError(
                f"Failed to perform operation '{operation}' on table '{schema_name}.{table_name}': {e}"
            ) from e

    def get_target_schema(self, data_type: str, environment: str = "to2") -> str:
        """Get target schema name based on data type and environment.

        Args:
            data_type: Type of data (ep, kpi, score, cfg, nlg, cdr)
            environment: Environment identifier (default: 'to2')

        Returns:
            Target schema name

        Raises:
            ValidationError: If data_type is invalid
        """
        valid_data_types = ("ep", "kpi", "score", "cfg", "nlg", "cdr")
        if data_type not in valid_data_types:
            raise ValidationError(
                f"Invalid data_type: {data_type}. Must be one of {valid_data_types}"
            )

        # Special handling for CDR data types
        if data_type == "cdr":
            # Default CDR schema mapping
            return f"cdr_{environment}"

        return f"{data_type}_{environment}"

    async def _drop_table(self, table_name: str, schema_name: str) -> bool:
        """Drop a table from the specified schema.

        Args:
            table_name: Name of the table to drop
            schema_name: Name of the schema containing the table

        Returns:
            True if table was dropped successfully

        Raises:
            DatabaseError: If drop operation fails
        """
        try:
            async with self.pool.acquire() as conn:
                await conn.execute(
                    f'DROP TABLE IF EXISTS "{schema_name}"."{table_name}"'
                )
                logger.info(f"Table '{schema_name}.{table_name}' dropped successfully")
                return True

        except asyncpg.PostgresError as e:
            logger.error(f"Failed to drop table '{schema_name}.{table_name}': {e}")
            raise DatabaseError(
                f"Failed to drop table '{schema_name}.{table_name}': {e}"
            ) from e

    async def _truncate_table(self, table_name: str, schema_name: str) -> bool:
        """Truncate a table in the specified schema.

        Args:
            table_name: Name of the table to truncate
            schema_name: Name of the schema containing the table

        Returns:
            True if table was truncated successfully

        Raises:
            DatabaseError: If truncate operation fails
        """
        try:
            async with self.pool.acquire() as conn:
                await conn.execute(
                    f'TRUNCATE TABLE "{schema_name}"."{table_name}" RESTART IDENTITY'
                )
                logger.info(
                    f"Table '{schema_name}.{table_name}' truncated successfully"
                )
                return True

        except asyncpg.PostgresError as e:
            logger.error(f"Failed to truncate table '{schema_name}.{table_name}': {e}")
            raise DatabaseError(
                f"Failed to truncate table '{schema_name}.{table_name}': {e}"
            ) from e

    async def get_table_info(self, table_name: str, schema_name: str) -> Dict[str, Any]:
        """Get detailed information about a table.

        Args:
            table_name: Name of the table
            schema_name: Name of the schema

        Returns:
            Dictionary containing table information

        Raises:
            ValidationError: If names are invalid
            DatabaseError: If operation fails
        """
        # Validate names
        if not self.validator.validate_identifier(table_name):
            raise ValidationError(f"Invalid table name: {table_name}")
        if not self.validator.validate_identifier(schema_name):
            raise ValidationError(f"Invalid schema name: {schema_name}")

        try:
            async with self.pool.acquire() as conn:
                # Get table columns
                columns = await conn.fetch(
                    "SELECT column_name, data_type, is_nullable, column_default "
                    "FROM information_schema.columns "
                    "WHERE table_schema = $1 AND table_name = $2 "
                    "ORDER BY ordinal_position",
                    schema_name,
                    table_name,
                )

                # Get table size
                size_result = await conn.fetchrow(
                    "SELECT pg_total_relation_size($1) as total_size, "
                    "pg_relation_size($1) as table_size",
                    f"{schema_name}.{table_name}",
                )

                # Get row count
                row_count = await conn.fetchval(
                    f'SELECT COUNT(*) FROM "{schema_name}"."{table_name}"'
                )

                return {
                    "schema_name": schema_name,
                    "table_name": table_name,
                    "columns": [dict(row) for row in columns],
                    "row_count": row_count,
                    "total_size_bytes": size_result["total_size"] if size_result else 0,
                    "table_size_bytes": size_result["table_size"] if size_result else 0,
                }

        except asyncpg.PostgresError as e:
            logger.error(
                f"Failed to get table info for '{schema_name}.{table_name}': {e}"
            )
            raise DatabaseError(
                f"Failed to get table info for '{schema_name}.{table_name}': {e}"
            ) from e
        except Exception as e:
            logger.error(
                f"Unexpected error getting table info for '{schema_name}.{table_name}': {e}"
            )
            raise DatabaseError(
                f"Unexpected error getting table info for '{schema_name}.{table_name}': {e}"
            ) from e
