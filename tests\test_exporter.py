#!/usr/bin/env python3
"""
Test suite for DataExporter class.

This module contains comprehensive tests for the CSV data export functionality,
including table export, query export, and batch export operations.
"""

import asyncio
import csv
import os
import shutil
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.database.connection.session import SessionManager
from src.database.exceptions import (
    DatabaseError,
    FileOperationError,
    TableNotFoundError,
    ValidationError,
)
from src.database.operations.exporter import DataExporter


class TestDataExporter:
    """Test cases for DataExporter class."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for test files."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def mock_session_manager(self):
        """Create a mock session manager."""
        session_manager = AsyncMock(spec=SessionManager)
        return session_manager

    @pytest.fixture
    def mock_connection(self):
        """Create a mock database connection."""
        connection = AsyncMock()
        return connection

    @pytest.fixture
    def exporter(self, mock_session_manager):
        """Create a DataExporter instance with mocked dependencies."""
        return DataExporter(mock_session_manager)

    @pytest.fixture
    def sample_table_data(self):
        """Sample table data for testing."""
        return [
            {"id": 1, "name": "Alice", "age": 30, "email": "<EMAIL>"},
            {"id": 2, "name": "Bob", "age": 25, "email": "<EMAIL>"},
            {"id": 3, "name": "Charlie", "age": 35, "email": "<EMAIL>"},
        ]

    @pytest.fixture
    def sample_table_info(self):
        """Sample table information for testing."""
        return {
            "columns": [
                {"name": "id", "type": "integer"},
                {"name": "name", "type": "varchar"},
                {"name": "age", "type": "integer"},
                {"name": "email", "type": "varchar"},
            ],
            "row_count": 3,
        }

    @pytest.mark.asyncio
    async def test_export_table_to_csv_success(
        self, exporter, mock_connection, temp_dir, sample_table_data, sample_table_info
    ):
        """Test successful table export to CSV."""
        # Setup
        table_name = "users"
        output_file = os.path.join(temp_dir, "users.csv")

        # Setup session manager as async context manager
        exporter.session_manager.__aenter__.return_value = mock_connection
        exporter.session_manager.__aexit__ = AsyncMock(return_value=None)

        # Mock get_table_info
        mock_gti = AsyncMock(return_value=sample_table_info)
        with patch.object(exporter, "get_table_info", new=mock_gti):
            # Mock connection.fetch to return sample data (async) and then empty to terminate loop
            mock_fetch_data = [
                (1, "Alice", 30, "<EMAIL>"),
                (2, "Bob", 25, "<EMAIL>"),
                (3, "Charlie", 35, "<EMAIL>"),
            ]
            mock_connection.fetch = AsyncMock(side_effect=[mock_fetch_data, []])

            # Execute
            result = await exporter.export_table_to_csv(table_name, output_file)

            # Verify
            assert result["success"] is True
            assert result["file_path"] == output_file
            assert result["rows_exported"] == 3
            assert os.path.exists(output_file)

            # Verify CSV content
            with open(output_file, "r", newline="", encoding="utf-8") as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                assert len(rows) == 3
                assert rows[0]["id"] == "1"
                assert rows[0]["name"] == "Alice"
                assert rows[0]["age"] == "30"
                assert rows[0]["email"] == "<EMAIL>"

    @pytest.mark.asyncio
    async def test_export_table_to_csv_table_not_found(self, exporter, mock_connection):
        """Test export with non-existent table."""
        # Setup
        table_name = "non_existent_table"
        output_file = "/tmp/test.csv"

        # Setup session manager as async context manager
        exporter.session_manager.__aenter__.return_value = mock_connection
        exporter.session_manager.__aexit__ = AsyncMock(return_value=None)

        # Mock get_table_info to raise TableNotFoundError
        with patch.object(
            exporter,
            "get_table_info",
            side_effect=TableNotFoundError("Table not found"),
        ):
            # Execute and verify
            with pytest.raises(TableNotFoundError):
                await exporter.export_table_to_csv(table_name, output_file)

    @pytest.mark.asyncio
    async def test_export_table_to_csv_file_operation_error(
        self, exporter, mock_connection, sample_table_info
    ):
        """Test export with file operation error."""
        # Setup
        table_name = "users"
        output_file = "/invalid/path/users.csv"  # Invalid path

        # Setup session manager as async context manager
        exporter.session_manager.__aenter__.return_value = mock_connection
        exporter.session_manager.__aexit__ = AsyncMock(return_value=None)

        # Mock get_table_info
        mock_get_table_info = AsyncMock(return_value=sample_table_info)
        exporter.get_table_info = mock_get_table_info

        # Mock open to raise an IOError when trying to write to the invalid path
        # This simulates the scenario where file operation fails.
        mock_open = MagicMock(side_effect=IOError("File operation failed"))

        # We only expect one call to fetch if the file operation error occurs before/during the first batch write.
        # If the error is expected before any data is fetched, this mock might not even be strictly necessary,
        # but it's safer to have it defined.
        mock_connection.fetch = AsyncMock(
            return_value=[(1, "Alice", 30, "<EMAIL>")]
        )

        with patch("builtins.open", mock_open):
            # Execute and verify
            with pytest.raises(FileOperationError):
                await exporter.export_table_to_csv(table_name, output_file)

        # Assert that open was called with the correct path
        mock_open.assert_called_once_with(
            output_file, "w", newline="", encoding="utf-8"
        )

    @pytest.mark.asyncio
    async def test_export_query_to_csv_success(
        self, exporter, mock_connection, temp_dir
    ):
        """Test successful query export to CSV."""
        # Setup
        query = "SELECT id, name FROM users WHERE age > 25"
        output_file = os.path.join(temp_dir, "query_result.csv")

        # Setup session manager as async context manager
        exporter.session_manager.__aenter__.return_value = mock_connection
        exporter.session_manager.__aexit__ = AsyncMock(return_value=None)

        # Mock connection.fetch to return sample data as AsyncMock
        mock_connection.fetch = AsyncMock(return_value=[(1, "Alice"), (3, "Charlie")])

        # Mock connection.fetchrow to return column info
        mock_connection.fetchrow = AsyncMock(
            return_value=None
        )  # No result for column info query

        # Execute
        result = await exporter.export_query_to_csv(query, output_file)

        # Verify
        assert result["success"] is True
        assert result["file_path"] == output_file
        assert result["rows_exported"] == 2
        assert os.path.exists(output_file)

        # Verify CSV content
        with open(output_file, "r", newline="", encoding="utf-8") as f:
            reader = csv.reader(f)
            rows = list(reader)
            assert len(rows) == 3  # Header + 2 data rows
            assert rows[0] == ["column_1", "column_2"]  # Default column names
            assert rows[1] == ["1", "Alice"]
            assert rows[2] == ["3", "Charlie"]

    @pytest.mark.asyncio
    async def test_export_query_to_csv_sql_injection_protection(self, exporter):
        """Test SQL injection protection in query export."""
        # Setup
        malicious_query = "SELECT * FROM users; DROP TABLE users; --"
        output_file = "/tmp/test.csv"

        # Execute and verify
        with pytest.raises(ValidationError, match="SQL injection detected"):
            await exporter.export_query_to_csv(malicious_query, output_file)

    @pytest.mark.asyncio
    async def test_batch_export_tables_success(
        self, exporter, mock_connection, temp_dir, sample_table_info
    ):
        """Test successful batch export of multiple tables."""
        # Setup
        table_names = ["users", "orders"]

        # Setup session manager as async context manager
        exporter.session_manager.__aenter__.return_value = mock_connection
        exporter.session_manager.__aexit__ = AsyncMock(return_value=None)

        # Mock get_table_info for both tables
        with patch.object(
            exporter,
            "get_table_info",
            new_callable=AsyncMock,
            return_value=sample_table_info,
        ):
            # Mock connection.fetch to return sample data with side_effect to terminate loop
            mock_connection.fetch = AsyncMock(
                side_effect=[
                    [(1, "Alice", 30, "<EMAIL>")],  # First call returns data
                    [],  # Second call returns empty to terminate loop
                    [
                        (1, "Alice", 30, "<EMAIL>")
                    ],  # Third call for second table
                    [],  # Fourth call returns empty to terminate loop
                ]
            )

            # Execute
            results = await exporter.batch_export_tables(table_names, temp_dir)

            # Verify
            assert len(results) == 2
            for result in results:
                assert result["success"] is True
                assert result["rows_exported"] == 1
                assert os.path.exists(result["file_path"])

    @pytest.mark.asyncio
    async def test_batch_export_tables_partial_failure(
        self, exporter, mock_connection, temp_dir, sample_table_info
    ):
        """Test batch export with some tables failing."""
        # Setup
        table_names = ["users", "non_existent_table"]

        # Setup session manager as async context manager
        exporter.session_manager.__aenter__.return_value = mock_connection
        exporter.session_manager.__aexit__ = AsyncMock(return_value=None)

        # Mock get_table_info - success for first table, failure for second
        async def mock_get_table_info_side_effect(table_name):
            if table_name == "users":
                return sample_table_info
            else:
                raise TableNotFoundError(f"Table {table_name} not found")

        with patch.object(
            exporter, "get_table_info", side_effect=mock_get_table_info_side_effect
        ):
            # Simulate fetching one batch of data then an empty batch to terminate the loop
            mock_connection.fetch.side_effect = [
                [(1, "Alice", 30, "<EMAIL>")],  # First call returns data
                [],  # Second call returns empty, stopping the batch fetch loop
            ]

            # Execute
            results = await exporter.batch_export_tables(table_names, temp_dir)

            # Verify
            assert len(results) == 2
            assert results[0]["success"] is True
            assert results[1]["success"] is False
            assert "not found" in results[1]["error"]

    @pytest.mark.asyncio
    async def test_get_table_info_success(self, exporter, mock_connection):
        """Test successful table info retrieval."""
        # Setup
        table_name = "users"

        # Setup async context manager mock
        exporter.session_manager.__aenter__.return_value = mock_connection
        exporter.session_manager.__aexit__ = AsyncMock(return_value=None)

        # Mock connection responses
        mock_connection.fetchval.side_effect = [
            True,  # Table exists query
            100,  # Row count query
        ]

        mock_connection.fetch.return_value = [
            {
                "column_name": "id",
                "data_type": "integer",
                "is_nullable": "NO",
                "column_default": None,
            },
            {
                "column_name": "name",
                "data_type": "varchar",
                "is_nullable": "YES",
                "column_default": None,
            },
            {
                "column_name": "age",
                "data_type": "integer",
                "is_nullable": "YES",
                "column_default": None,
            },
        ]

        # Execute
        result = await exporter.get_table_info(table_name)

        # Verify
        assert result["row_count"] == 100
        assert len(result["columns"]) == 3
        assert result["columns"][0]["name"] == "id"
        assert result["columns"][0]["type"] == "integer"

    @pytest.mark.asyncio
    async def test_get_table_info_table_not_found(self, exporter, mock_connection):
        """Test table info retrieval for non-existent table."""
        # Setup
        table_name = "non_existent_table"

        # Setup session manager as async context manager
        exporter.session_manager.__aenter__.return_value = mock_connection
        exporter.session_manager.__aexit__ = AsyncMock(return_value=None)

        # Mock connection to return False for table existence check
        mock_connection.fetchval.return_value = False

        # Execute and verify
        with pytest.raises(TableNotFoundError):
            await exporter.get_table_info(table_name)

    def test_validate_sql_query_safe(self, exporter):
        """Test SQL query validation with safe queries."""
        safe_queries = [
            "SELECT * FROM users",
            "SELECT id, name FROM users WHERE age > 25",
            "SELECT COUNT(*) FROM orders",
        ]

        for query in safe_queries:
            # Should not raise any exception
            exporter._validate_sql_query(query)

    def test_validate_sql_query_unsafe(self, exporter):
        """Test SQL query validation with unsafe queries."""
        unsafe_queries = [
            "SELECT * FROM users; DROP TABLE users;",
            "SELECT * FROM users; DELETE FROM users;",
            "SELECT * FROM users; INSERT INTO users VALUES (1, 'hacker');",
            "SELECT * FROM users; UPDATE users SET name = 'hacked';",
        ]

        for query in unsafe_queries:
            with pytest.raises(ValidationError, match="SQL injection detected"):
                exporter._validate_sql_query(query)

    def test_sanitize_filename(self, exporter):
        """Test filename sanitization."""
        test_cases = [
            ("normal_file.csv", "normal_file.csv"),
            ("file with spaces.csv", "file_with_spaces.csv"),
            ("file/with\\slashes.csv", "file_with_slashes.csv"),
            ("file:with*special?chars.csv", "file_with_special_chars.csv"),
            ("file<with>pipes|.csv", "file_with_pipes_.csv"),
        ]

        for input_name, expected_output in test_cases:
            result = exporter._sanitize_filename(input_name)
            assert result == expected_output

    @pytest.mark.asyncio
    async def test_export_with_custom_batch_size(
        self, exporter, mock_connection, temp_dir, sample_table_info
    ):
        """Test export with custom batch size."""
        # Setup
        table_name = "large_table"
        output_file = os.path.join(temp_dir, "large_table.csv")
        batch_size = 2

        # Setup session manager as async context manager
        exporter.session_manager.__aenter__.return_value = mock_connection
        exporter.session_manager.__aexit__ = AsyncMock(return_value=None)

        # Mock get_table_info
        with patch.object(exporter, "get_table_info", return_value=sample_table_info):
            # Mock connection.fetch to return data in batches
            mock_connection.fetch.side_effect = [
                [
                    (1, "Alice", 30, "<EMAIL>"),
                    (2, "Bob", 25, "<EMAIL>"),
                ],
                [(3, "Charlie", 35, "<EMAIL>")],
                [],  # End of data
            ]

            # Execute
            result = await exporter.export_table_to_csv(
                table_name, output_file, batch_size=batch_size
            )

            # Verify
            assert result["success"] is True
            assert result["rows_exported"] == 3
            assert os.path.exists(output_file)

            # Verify multiple fetch calls were made
            assert mock_connection.fetch.call_count >= 2


if __name__ == "__main__":
    pytest.main([__file__])
