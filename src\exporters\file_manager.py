__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""File management module.

This module provides file management functionality for export operations.
"""

import logging
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Configure logging
logger = logging.getLogger(__name__)


class FileManager:
    """File manager for handling export file operations."""

    def __init__(self, base_path: Union[str, Path], **kwargs):
        """Initialize file manager.

        Args:
            base_path: Base directory for file operations
            **kwargs: Additional configuration options
        """
        self.base_path = Path(base_path)
        self.config = kwargs
        self.create_directories = kwargs.get("create_directories", True)

        if self.create_directories:
            self.base_path.mkdir(parents=True, exist_ok=True)

        logger.info(f"Initialized FileManager with base path: {self.base_path}")

    def ensure_directory_exists(self, path: Union[str, Path]) -> Path:
        """Ensure directory exists.

        Args:
            path: Directory path to create

        Returns:
            Path: Created directory path
        """
        dir_path = Path(path)
        dir_path.mkdir(parents=True, exist_ok=True)
        return dir_path

    def get_unique_filename(
        self, filename: str, directory: Optional[Union[str, Path]] = None
    ) -> Path:
        """Get unique filename by adding suffix if file exists.

        Args:
            filename: Original filename
            directory: Directory to check (defaults to base_path)

        Returns:
            Path: Unique file path
        """
        if directory is None:
            directory = self.base_path

        file_path = Path(directory) / filename

        if not file_path.exists():
            return file_path

        # Add numeric suffix to make unique
        stem = file_path.stem
        suffix = file_path.suffix
        counter = 1

        while file_path.exists():
            new_name = f"{stem}_{counter}{suffix}"
            file_path = Path(directory) / new_name
            counter += 1

        return file_path

    def copy_file(
        self, source: Union[str, Path], destination: Union[str, Path]
    ) -> Path:
        """Copy file to destination.

        Args:
            source: Source file path
            destination: Destination file path

        Returns:
            Path: Destination file path
        """
        source_path = Path(source)
        dest_path = Path(destination)

        # Ensure destination directory exists
        dest_path.parent.mkdir(parents=True, exist_ok=True)

        shutil.copy2(source_path, dest_path)
        logger.info(f"Copied file from {source_path} to {dest_path}")

        return dest_path

    def move_file(
        self, source: Union[str, Path], destination: Union[str, Path]
    ) -> Path:
        """Move file to destination.

        Args:
            source: Source file path
            destination: Destination file path

        Returns:
            Path: Destination file path
        """
        source_path = Path(source)
        dest_path = Path(destination)

        # Ensure destination directory exists
        dest_path.parent.mkdir(parents=True, exist_ok=True)

        shutil.move(str(source_path), str(dest_path))
        logger.info(f"Moved file from {source_path} to {dest_path}")

        return dest_path

    def delete_file(self, file_path: Union[str, Path]) -> bool:
        """Delete file.

        Args:
            file_path: File path to delete

        Returns:
            bool: True if file was deleted, False otherwise
        """
        path = Path(file_path)

        if path.exists() and path.is_file():
            path.unlink()
            logger.info(f"Deleted file: {path}")
            return True

        return False

    def get_file_info(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """Get file information.

        Args:
            file_path: File path to analyze

        Returns:
            Dict: File information
        """
        path = Path(file_path)

        if not path.exists():
            return {"exists": False}

        stat = path.stat()

        return {
            "exists": True,
            "size_bytes": stat.st_size,
            "created": stat.st_ctime,
            "modified": stat.st_mtime,
            "is_file": path.is_file(),
            "is_directory": path.is_dir(),
            "extension": path.suffix,
            "name": path.name,
            "stem": path.stem,
        }

    def list_files(
        self, directory: Optional[Union[str, Path]] = None, pattern: str = "*"
    ) -> List[Path]:
        """List files in directory.

        Args:
            directory: Directory to list (defaults to base_path)
            pattern: File pattern to match

        Returns:
            List[Path]: List of file paths
        """
        if directory is None:
            directory = self.base_path

        dir_path = Path(directory)

        if not dir_path.exists() or not dir_path.is_dir():
            return []

        return list(dir_path.glob(pattern))

    def cleanup_old_files(
        self, directory: Optional[Union[str, Path]] = None, max_age_days: int = 30
    ) -> int:
        """Clean up old files.

        Args:
            directory: Directory to clean (defaults to base_path)
            max_age_days: Maximum age in days

        Returns:
            int: Number of files deleted
        """
        import time

        if directory is None:
            directory = self.base_path

        dir_path = Path(directory)
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 60 * 60
        deleted_count = 0

        for file_path in dir_path.glob("*"):
            if file_path.is_file():
                file_age = current_time - file_path.stat().st_mtime
                if file_age > max_age_seconds:
                    file_path.unlink()
                    deleted_count += 1
                    logger.info(f"Deleted old file: {file_path}")

        return deleted_count
