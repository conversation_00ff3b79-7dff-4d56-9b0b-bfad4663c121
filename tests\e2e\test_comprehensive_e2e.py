#!/usr/bin/env python3
"""
Comprehensive End-to-End Tests

This module provides comprehensive end-to-end tests that simulate real-world
usage scenarios of the database framework:
- Complete data processing workflows
- Multi-user concurrent scenarios
- Real-time data ingestion and processing
- Geospatial analysis pipelines
- System integration scenarios
- Performance under realistic loads

Author: Connect Database Framework Team
Version: 1.0.0
Date: 2024-01-01
"""

import asyncio
import json
import tempfile
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List

import pandas as pd
import pytest
from shapely.geometry import Point, Polygon

from src.config import get_config as load_config
from src.config.models import ConnectConfig as Config, DatabaseConfig
from src.database.connection.pool import DatabasePoolManager
from src.database.connection.session import SessionManager
from src.database.exceptions import (
    ConfigurationError,
    ConnectionError,
    DatabaseError,
    SchemaError,
    ValidationError,
)
from src.database.geospatial.processor import GeospatialProcessor
from src.database.geospatial.validator import GeometryValidator
from src.database.monitoring.logger import DatabaseLogger
from src.database.operations.crud import CRUDOperations
from src.database.operations.exporter import DataExporter
from src.database.operations.importer import DataImporter
from src.database.schema.manager import SchemaManager
from src.database.schema.models import TableSchema, ColumnSchema
from src.database.schema.router import SchemaRouter
from tests.e2e.scripts.setup_test_environment import TestEnvironmentManager
from tests.test_infrastructure import (
    PerformanceBenchmark,
    TestDataGenerator,
)


class TestTelecommunicationsDataWorkflow:
    """Test complete telecommunications data processing workflows."""
    
    @pytest.mark.asyncio
    async def test_ep_data_processing_workflow(self, test_database_pool):
        """Test complete EP (Engineering Parameters) data processing workflow."""
        # Initialize components
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        importer = DataImporter(test_database_pool)
        exporter = DataExporter(test_database_pool)
        logger = DatabaseLogger("ep_workflow")
        
        schema_name = "ep_data_production"
        
        try:
            logger.info("Starting EP data processing workflow")
            
            # Step 1: Setup EP data schema
            await schema_manager.create_schema(schema_name)
            
            # Create EP measurements table
            ep_table = TableSchema(
                name="ep_measurements",
                schema_name=schema_name,
                columns=[
                    ColumnSchema(name="measurement_id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="cell_id", data_type="VARCHAR(50)", nullable=False),
                    ColumnSchema(name="site_id", data_type="VARCHAR(50)", nullable=False),
                    ColumnSchema(name="measurement_time", data_type="TIMESTAMP", nullable=False),
                    ColumnSchema(name="rsrp", data_type="DECIMAL(8,2)"),  # Reference Signal Received Power
                    ColumnSchema(name="rsrq", data_type="DECIMAL(8,2)"),  # Reference Signal Received Quality
                    ColumnSchema(name="sinr", data_type="DECIMAL(8,2)"),  # Signal-to-Interference-plus-Noise Ratio
                    ColumnSchema(name="throughput_dl", data_type="DECIMAL(12,2)"),  # Downlink throughput
                    ColumnSchema(name="throughput_ul", data_type="DECIMAL(12,2)"),  # Uplink throughput
                    ColumnSchema(name="latitude", data_type="DECIMAL(10,8)"),
                    ColumnSchema(name="longitude", data_type="DECIMAL(11,8)"),
                    ColumnSchema(name="technology", data_type="VARCHAR(10)"),  # 4G, 5G, etc.
                    ColumnSchema(name="band", data_type="VARCHAR(20)"),
                    ColumnSchema(name="quality_score", data_type="DECIMAL(5,2)"),
                ],
            )
            
            await schema_manager.create_table(ep_table)
            
            # Create indexes for performance
            await schema_manager.create_index(
                table_name="ep_measurements",
                schema_name=schema_name,
                index_name="idx_ep_cell_time",
                columns=["cell_id", "measurement_time"],
            )
            
            await schema_manager.create_index(
                table_name="ep_measurements",
                schema=schema_name,
                index_name="idx_ep_location",
                columns=["latitude", "longitude"],
            )
            
            # Step 2: Generate and import EP data
            generator = TestDataGenerator()
            
            # Simulate 24 hours of EP measurements (every 15 minutes)
            measurement_interval = timedelta(minutes=15)
            start_time = datetime.now() - timedelta(days=1)
            
            ep_data = []
            cell_ids = [f"CELL_{i:04d}" for i in range(1, 101)]  # 100 cells
            site_ids = [f"SITE_{i:03d}" for i in range(1, 21)]   # 20 sites
            
            current_time = start_time
            while current_time <= datetime.now():
                for cell_id in cell_ids[:20]:  # Process subset for performance
                    site_id = generator.fake.random_element(site_ids)
                    
                    # Generate realistic EP measurements
                    rsrp = generator.fake.pydecimal(left_digits=3, right_digits=2, min_value=-140, max_value=-40)
                    rsrq = generator.fake.pydecimal(left_digits=2, right_digits=2, min_value=-20, max_value=-3)
                    sinr = generator.fake.pydecimal(left_digits=2, right_digits=2, min_value=-10, max_value=30)
                    
                    # Calculate quality score based on measurements
                    quality_score = min(100, max(0, 
                        (float(rsrp) + 140) * 0.5 + 
                        (float(rsrq) + 20) * 2 + 
                        (float(sinr) + 10) * 1.5
                    ))
                    
                    ep_data.append({
                        "cell_id": cell_id,
                        "site_id": site_id,
                        "measurement_time": current_time,
                        "rsrp": rsrp,
                        "rsrq": rsrq,
                        "sinr": sinr,
                        "throughput_dl": generator.fake.pydecimal(left_digits=5, right_digits=2, min_value=1000, max_value=100000),
                        "throughput_ul": generator.fake.pydecimal(left_digits=5, right_digits=2, min_value=500, max_value=50000),
                        "latitude": generator.fake.latitude(),
                        "longitude": generator.fake.longitude(),
                        "technology": generator.fake.random_element(["4G", "5G"]),
                        "band": generator.fake.random_element(["B1", "B3", "B7", "B20", "N78", "N258"]),
                        "quality_score": quality_score,
                    })
                
                current_time += measurement_interval
            
            logger.info(f"Generated {len(ep_data)} EP measurements")
            
            # Import data in batches
            batch_size = 1000
            with PerformanceBenchmark("ep_data_import", threshold_ms=30000.0) as benchmark:
                for i in range(0, len(ep_data), batch_size):
                    batch = ep_data[i:i + batch_size]
                    batch_df = pd.DataFrame(batch)
                    
                    await crud.insert_dataframe(
                        dataframe=batch_df,
                        table_name="ep_measurements",
                        schema=schema_name,
                    )
                    
                    logger.info(f"Imported batch {i//batch_size + 1}/{(len(ep_data) + batch_size - 1)//batch_size}")
            
            # Step 3: Perform EP data analysis
            logger.info("Starting EP data analysis")
            
            # Analysis 1: Cell performance summary
            cell_performance_query = """
                SELECT 
                    cell_id,
                    site_id,
                    technology,
                    COUNT(*) as measurement_count,
                    AVG(rsrp) as avg_rsrp,
                    AVG(rsrq) as avg_rsrq,
                    AVG(sinr) as avg_sinr,
                    AVG(throughput_dl) as avg_throughput_dl,
                    AVG(throughput_ul) as avg_throughput_ul,
                    AVG(quality_score) as avg_quality_score,
                    MIN(quality_score) as min_quality_score,
                    MAX(quality_score) as max_quality_score
                FROM {}.ep_measurements
                WHERE measurement_time >= NOW() - INTERVAL '24 hours'
                GROUP BY cell_id, site_id, technology
                ORDER BY avg_quality_score DESC
            """.format(schema_name)
            
            cell_performance = await crud.execute_query(cell_performance_query)
            assert len(cell_performance) > 0
            
            # Analysis 2: Hourly performance trends
            hourly_trends_query = """
                SELECT 
                    DATE_TRUNC('hour', measurement_time) as hour,
                    technology,
                    COUNT(*) as measurement_count,
                    AVG(quality_score) as avg_quality_score,
                    STDDEV(quality_score) as quality_score_stddev
                FROM {}.ep_measurements
                WHERE measurement_time >= NOW() - INTERVAL '24 hours'
                GROUP BY DATE_TRUNC('hour', measurement_time), technology
                ORDER BY hour, technology
            """.format(schema_name)
            
            hourly_trends = await crud.execute_query(hourly_trends_query)
            assert len(hourly_trends) > 0
            
            # Analysis 3: Poor performance cells identification
            poor_performance_query = """
                SELECT 
                    cell_id,
                    site_id,
                    AVG(quality_score) as avg_quality_score,
                    COUNT(*) as poor_measurements
                FROM {}.ep_measurements
                WHERE quality_score < 30
                    AND measurement_time >= NOW() - INTERVAL '24 hours'
                GROUP BY cell_id, site_id
                HAVING COUNT(*) > 5
                ORDER BY avg_quality_score ASC
            """.format(schema_name)
            
            poor_performance = await crud.execute_query(poor_performance_query)
            
            # Step 4: Export analysis results
            with tempfile.TemporaryDirectory() as temp_dir:
                # Export cell performance summary
                cell_perf_file = Path(temp_dir) / "cell_performance_summary.csv"
                cell_perf_df = pd.DataFrame(cell_performance)
                cell_perf_df.to_csv(cell_perf_file, index=False)
                
                # Export hourly trends
                trends_file = Path(temp_dir) / "hourly_performance_trends.csv"
                trends_df = pd.DataFrame(hourly_trends)
                trends_df.to_csv(trends_file, index=False)
                
                # Export poor performance cells
                if poor_performance:
                    poor_perf_file = Path(temp_dir) / "poor_performance_cells.csv"
                    poor_perf_df = pd.DataFrame(poor_performance)
                    poor_perf_df.to_csv(poor_perf_file, index=False)
                
                logger.info(f"Analysis results exported to {temp_dir}")
                
                # Verify exports
                assert cell_perf_file.exists()
                assert trends_file.exists()
                assert len(cell_perf_df) > 0
                assert len(trends_df) > 0
            
            logger.info("EP data processing workflow completed successfully")
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)
    
    @pytest.mark.asyncio
    async def test_cdr_data_processing_workflow(self, test_database_pool):
        """Test complete CDR (Call Detail Record) data processing workflow."""
        # Initialize components
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        logger = DatabaseLogger("cdr_workflow")
        
        schema_name = "cdr_data_production"
        
        try:
            logger.info("Starting CDR data processing workflow")
            
            # Step 1: Setup CDR data schema
            await schema_manager.create_schema(schema_name)
            
            # Create CDR table
            cdr_table = TableSchema(
                name="call_detail_records",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="cdr_id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="call_id", data_type="VARCHAR(50)", nullable=False),
                    ColumnSchema(name="calling_number", data_type="VARCHAR(20)", nullable=False),
                    ColumnSchema(name="called_number", data_type="VARCHAR(20)", nullable=False),
                    ColumnSchema(name="call_start_time", data_type="TIMESTAMP", nullable=False),
                    ColumnSchema(name="call_end_time", data_type="TIMESTAMP"),
                    ColumnSchema(name="duration_seconds", data_type="INTEGER"),
                    ColumnSchema(name="call_type", data_type="VARCHAR(20)"),  # voice, sms, data
                    ColumnSchema(name="originating_cell", data_type="VARCHAR(50)"),
                    ColumnSchema(name="terminating_cell", data_type="VARCHAR(50)"),
                    ColumnSchema(name="data_volume_mb", data_type="DECIMAL(12,2)"),
                    ColumnSchema(name="charging_amount", data_type="DECIMAL(10,4)"),
                    ColumnSchema(name="service_type", data_type="VARCHAR(30)"),
                    ColumnSchema(name="roaming_flag", data_type="BOOLEAN", default="FALSE"),
                    ColumnSchema(name="quality_indicator", data_type="VARCHAR(20)"),
                ],
            )
            
            await schema_manager.create_table(cdr_table)
            
            # Create indexes
            await schema_manager.create_index(
                table_name="call_detail_records",
                schema=schema_name,
                index_name="idx_cdr_start_time",
                columns=["call_start_time"],
            )
            
            await schema_manager.create_index(
                table_name="call_detail_records",
                schema=schema_name,
                index_name="idx_cdr_calling_number",
                columns=["calling_number"],
            )
            
            # Step 2: Generate and import CDR data
            generator = TestDataGenerator()
            
            # Generate CDR data for the last 24 hours
            cdr_data = []
            start_time = datetime.now() - timedelta(days=1)
            
            # Generate realistic call patterns
            for _ in range(5000):  # 5000 call records
                call_start = generator.fake.date_time_between(
                    start_date=start_time,
                    end_date=datetime.now()
                )
                
                call_type = generator.fake.random_element(["voice", "sms", "data"])
                
                if call_type == "voice":
                    duration = generator.fake.random_int(min=10, max=3600)  # 10 seconds to 1 hour
                    call_end = call_start + timedelta(seconds=duration)
                    data_volume = None
                    charging_amount = duration * 0.01  # $0.01 per second
                elif call_type == "sms":
                    duration = 0
                    call_end = call_start
                    data_volume = None
                    charging_amount = 0.05  # $0.05 per SMS
                else:  # data
                    duration = generator.fake.random_int(min=60, max=7200)  # 1 minute to 2 hours
                    call_end = call_start + timedelta(seconds=duration)
                    data_volume = generator.fake.pydecimal(left_digits=4, right_digits=2, min_value=0.1, max_value=1000)
                    charging_amount = float(data_volume) * 0.02  # $0.02 per MB
                
                cdr_data.append({
                    "call_id": f"CALL_{generator.fake.uuid4()}",
                    "calling_number": generator.fake.phone_number()[:20],
                    "called_number": generator.fake.phone_number()[:20],
                    "call_start_time": call_start,
                    "call_end_time": call_end,
                    "duration_seconds": duration,
                    "call_type": call_type,
                    "originating_cell": f"CELL_{generator.fake.random_int(min=1, max=100):04d}",
                    "terminating_cell": f"CELL_{generator.fake.random_int(min=1, max=100):04d}",
                    "data_volume_mb": data_volume,
                    "charging_amount": charging_amount,
                    "service_type": generator.fake.random_element(["prepaid", "postpaid", "enterprise"]),
                    "roaming_flag": generator.fake.boolean(chance_of_getting_true=10),
                    "quality_indicator": generator.fake.random_element(["excellent", "good", "fair", "poor"]),
                })
            
            logger.info(f"Generated {len(cdr_data)} CDR records")
            
            # Import CDR data
            with PerformanceBenchmark("cdr_data_import", threshold_ms=20000.0) as benchmark:
                cdr_df = pd.DataFrame(cdr_data)
                await crud.insert_dataframe(
                    dataframe=cdr_df,
                    table_name="call_detail_records",
                    schema=schema_name,
                )
            
            # Step 3: Perform CDR analysis
            logger.info("Starting CDR data analysis")
            
            # Analysis 1: Call volume and revenue by hour
            hourly_stats_query = """
                SELECT 
                    DATE_TRUNC('hour', call_start_time) as hour,
                    call_type,
                    COUNT(*) as call_count,
                    SUM(duration_seconds) as total_duration,
                    SUM(charging_amount) as total_revenue,
                    AVG(duration_seconds) as avg_duration
                FROM {}.call_detail_records
                WHERE call_start_time >= NOW() - INTERVAL '24 hours'
                GROUP BY DATE_TRUNC('hour', call_start_time), call_type
                ORDER BY hour, call_type
            """.format(schema_name)
            
            hourly_stats = await crud.execute_query(hourly_stats_query)
            assert len(hourly_stats) > 0
            
            # Analysis 2: Top users by usage
            top_users_query = """
                SELECT 
                    calling_number,
                    COUNT(*) as total_calls,
                    SUM(duration_seconds) as total_duration,
                    SUM(charging_amount) as total_charges,
                    SUM(CASE WHEN data_volume_mb IS NOT NULL THEN data_volume_mb ELSE 0 END) as total_data_mb
                FROM {}.call_detail_records
                WHERE call_start_time >= NOW() - INTERVAL '24 hours'
                GROUP BY calling_number
                ORDER BY total_charges DESC
                LIMIT 100
            """.format(schema_name)
            
            top_users = await crud.execute_query(top_users_query)
            assert len(top_users) > 0
            
            # Analysis 3: Cell utilization
            cell_utilization_query = """
                SELECT 
                    originating_cell,
                    COUNT(*) as call_count,
                    SUM(duration_seconds) as total_duration,
                    COUNT(DISTINCT calling_number) as unique_users,
                    AVG(CASE WHEN quality_indicator = 'poor' THEN 1.0 ELSE 0.0 END) as poor_quality_rate
                FROM {}.call_detail_records
                WHERE call_start_time >= NOW() - INTERVAL '24 hours'
                    AND originating_cell IS NOT NULL
                GROUP BY originating_cell
                ORDER BY call_count DESC
            """.format(schema_name)
            
            cell_utilization = await crud.execute_query(cell_utilization_query)
            assert len(cell_utilization) > 0
            
            # Step 4: Generate reports
            total_revenue = sum(float(record["total_revenue"]) for record in hourly_stats)
            total_calls = sum(record["call_count"] for record in hourly_stats)
            
            logger.info(f"CDR Analysis Summary:")
            logger.info(f"- Total calls: {total_calls}")
            logger.info(f"- Total revenue: ${total_revenue:.2f}")
            logger.info(f"- Top user charges: ${float(top_users[0]['total_charges']):.2f}")
            logger.info(f"- Busiest cell: {cell_utilization[0]['originating_cell']} ({cell_utilization[0]['call_count']} calls)")
            
            # Verify analysis results
            assert total_calls > 0
            assert total_revenue > 0
            assert len(top_users) > 0
            assert len(cell_utilization) > 0
            
            logger.info("CDR data processing workflow completed successfully")
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)


class TestGeospatialAnalysisWorkflow:
    """Test complete geospatial analysis workflows."""
    
    @pytest.mark.asyncio
    async def test_network_coverage_analysis(self, test_database_pool):
        """Test network coverage analysis workflow."""
        # Initialize components
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        processor = GeospatialProcessor()
        validator = GeometryValidator()
        logger = DatabaseLogger("coverage_analysis")
        
        schema_name = "network_coverage_analysis"
        
        try:
            logger.info("Starting network coverage analysis workflow")
            
            # Step 1: Setup geospatial schema
            await schema_manager.create_schema(schema_name)
            
            # Enable PostGIS
            async with test_database_pool.acquire() as connection:
                await connection.execute("CREATE EXTENSION IF NOT EXISTS postgis")
            
            # Create cell sites table
            cell_sites_table = TableSchema(
                name="cell_sites",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="site_id", data_type="VARCHAR(50)", primary_key=True),
                    ColumnSchema(name="site_name", data_type="VARCHAR(255)"),
                    ColumnSchema(name="location", data_type="GEOMETRY(POINT, 4326)"),
                    ColumnSchema(name="technology", data_type="VARCHAR(10)"),
                    ColumnSchema(name="frequency_band", data_type="VARCHAR(20)"),
                    ColumnSchema(name="max_power_dbm", data_type="DECIMAL(6,2)"),
                    ColumnSchema(name="antenna_height_m", data_type="DECIMAL(6,2)"),
                    ColumnSchema(name="coverage_radius_m", data_type="INTEGER"),
                    ColumnSchema(name="status", data_type="VARCHAR(20)", default="'active'"),
                ],
            )
            
            await schema_manager.create_table(cell_sites_table)
            
            # Create coverage areas table
            coverage_areas_table = TableSchema(
                name="coverage_areas",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="coverage_id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="site_id", data_type="VARCHAR(50)"),
                    ColumnSchema(name="coverage_polygon", data_type="GEOMETRY(POLYGON, 4326)"),
                    ColumnSchema(name="signal_strength_category", data_type="VARCHAR(20)"),
                    ColumnSchema(name="estimated_users", data_type="INTEGER"),
                ],
            )
            
            await schema_manager.create_table(coverage_areas_table)
            
            # Create spatial indexes
            await schema_manager.create_spatial_index(
                table_name="cell_sites",
                schema=schema_name,
                column_name="location",
            )
            
            await schema_manager.create_spatial_index(
                table_name="coverage_areas",
                schema=schema_name,
                column_name="coverage_polygon",
            )
            
            # Step 2: Generate cell site data
            generator = TestDataGenerator()
            
            # Generate cell sites in a specific region (e.g., city area)
            city_center_lat = 52.5200  # Berlin
            city_center_lon = 13.4050
            
            cell_sites_data = []
            for i in range(50):  # 50 cell sites
                # Generate locations within ~20km radius of city center
                lat_offset = generator.fake.pyfloat(min_value=-0.2, max_value=0.2)
                lon_offset = generator.fake.pyfloat(min_value=-0.3, max_value=0.3)
                
                site_location = Point(
                    city_center_lon + lon_offset,
                    city_center_lat + lat_offset
                )
                
                # Validate geometry
                assert validator.validate_geometry(site_location)
                
                technology = generator.fake.random_element(["4G", "5G"])
                coverage_radius = 2000 if technology == "4G" else 1500  # 5G has smaller cells
                
                cell_sites_data.append({
                    "site_id": f"SITE_{i+1:03d}",
                    "site_name": f"Cell Site {i+1}",
                    "location": site_location.wkt,
                    "technology": technology,
                    "frequency_band": generator.fake.random_element(["B1", "B3", "B7", "B20", "N78", "N258"]),
                    "max_power_dbm": generator.fake.pydecimal(left_digits=2, right_digits=2, min_value=20, max_value=46),
                    "antenna_height_m": generator.fake.pydecimal(left_digits=2, right_digits=2, min_value=15, max_value=60),
                    "coverage_radius_m": coverage_radius,
                    "status": "active",
                })
            
            # Import cell sites
            sites_df = pd.DataFrame(cell_sites_data)
            await crud.insert_dataframe(
                dataframe=sites_df,
                table_name="cell_sites",
                schema=schema_name,
            )
            
            logger.info(f"Imported {len(cell_sites_data)} cell sites")
            
            # Step 3: Generate coverage areas
            coverage_areas_data = []
            
            for site_data in cell_sites_data:
                site_location = Point(*[float(coord) for coord in site_data["location"].replace("POINT(", "").replace(")", "").split()])
                coverage_radius_m = site_data["coverage_radius_m"]
                
                # Create coverage polygons for different signal strength categories
                for category, radius_factor in [("strong", 0.4), ("medium", 0.7), ("weak", 1.0)]:
                    radius_deg = (coverage_radius_m * radius_factor) / 111000  # Approximate conversion to degrees
                    
                    # Create circular coverage area
                    coverage_polygon = processor.buffer_geometry(site_location, radius_deg)
                    
                    # Estimate users based on area and signal strength
                    area_km2 = coverage_polygon.area * 111000 * 111000 / 1000000  # Rough conversion
                    user_density = {"strong": 1000, "medium": 500, "weak": 200}[category]  # users per km2
                    estimated_users = int(area_km2 * user_density)
                    
                    coverage_areas_data.append({
                        "site_id": site_data["site_id"],
                        "coverage_polygon": coverage_polygon.wkt,
                        "signal_strength_category": category,
                        "estimated_users": estimated_users,
                    })
            
            # Import coverage areas
            coverage_df = pd.DataFrame(coverage_areas_data)
            await crud.insert_dataframe(
                dataframe=coverage_df,
                table_name="coverage_areas",
                schema=schema_name,
            )
            
            logger.info(f"Generated {len(coverage_areas_data)} coverage areas")
            
            # Step 4: Perform coverage analysis
            logger.info("Performing coverage analysis")
            
            # Analysis 1: Coverage overlap analysis
            overlap_analysis_query = """
                SELECT 
                    a.site_id as site_a,
                    b.site_id as site_b,
                    a.signal_strength_category,
                    ST_Area(ST_Intersection(a.coverage_polygon, b.coverage_polygon)) as overlap_area,
                    ST_Area(a.coverage_polygon) as area_a,
                    ST_Area(b.coverage_polygon) as area_b
                FROM {}.coverage_areas a
                JOIN {}.coverage_areas b ON a.coverage_id < b.coverage_id
                WHERE ST_Intersects(a.coverage_polygon, b.coverage_polygon)
                    AND a.signal_strength_category = b.signal_strength_category
                ORDER BY overlap_area DESC
                LIMIT 20
            """.format(schema_name, schema_name)
            
            overlap_results = await crud.execute_query(overlap_analysis_query)
            
            # Analysis 2: Coverage gaps analysis
            total_coverage_query = """
                SELECT 
                    signal_strength_category,
                    COUNT(*) as area_count,
                    SUM(estimated_users) as total_estimated_users,
                    SUM(ST_Area(coverage_polygon)) as total_coverage_area
                FROM {}.coverage_areas
                GROUP BY signal_strength_category
                ORDER BY signal_strength_category
            """.format(schema_name)
            
            coverage_summary = await crud.execute_query(total_coverage_query)
            
            # Analysis 3: Site density analysis
            site_density_query = """
                SELECT 
                    technology,
                    COUNT(*) as site_count,
                    AVG(coverage_radius_m) as avg_coverage_radius,
                    AVG(max_power_dbm) as avg_power,
                    AVG(antenna_height_m) as avg_antenna_height
                FROM {}.cell_sites
                WHERE status = 'active'
                GROUP BY technology
            """.format(schema_name)
            
            site_density = await crud.execute_query(site_density_query)
            
            # Step 5: Generate coverage report
            logger.info("Coverage Analysis Results:")
            
            for summary in coverage_summary:
                logger.info(f"- {summary['signal_strength_category']} coverage: {summary['area_count']} areas, {summary['total_estimated_users']} estimated users")
            
            for density in site_density:
                logger.info(f"- {density['technology']} sites: {density['site_count']} sites, avg radius: {float(density['avg_coverage_radius']):.0f}m")
            
            if overlap_results:
                logger.info(f"- Found {len(overlap_results)} coverage overlaps")
                logger.info(f"- Largest overlap: {float(overlap_results[0]['overlap_area']):.6f} square degrees")
            
            # Verify analysis results
            assert len(coverage_summary) == 3  # strong, medium, weak
            assert len(site_density) > 0
            assert sum(summary["total_estimated_users"] for summary in coverage_summary) > 0
            
            logger.info("Network coverage analysis completed successfully")
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)


class TestMultiUserConcurrencyScenarios:
    """Test multi-user concurrency scenarios."""
    
    @pytest.mark.asyncio
    async def test_concurrent_data_processing(self, test_database_pool):
        """Test concurrent data processing by multiple users/processes."""
        schema_manager = SchemaManager(test_database_pool)
        logger = DatabaseLogger("concurrent_processing")
        
        schema_name = "concurrent_test_schema"
        
        try:
            logger.info("Starting concurrent data processing test")
            
            # Setup shared schema
            await schema_manager.create_schema(schema_name)
            
            # Create shared data table
            shared_table = TableSchema(
                name="shared_data",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="user_id", data_type="VARCHAR(50)"),
                    ColumnSchema(name="process_id", data_type="VARCHAR(50)"),
                    ColumnSchema(name="data_batch", data_type="INTEGER"),
                    ColumnSchema(name="processing_time", data_type="TIMESTAMP", default="NOW()"),
                    ColumnSchema(name="data_payload", data_type="JSONB"),
                    ColumnSchema(name="status", data_type="VARCHAR(20)", default="'processing'"),
                ],
            )
            
            await schema_manager.create_table(shared_table)
            
            # Simulate multiple concurrent users
            async def simulate_user_processing(user_id: str, num_batches: int):
                """Simulate a user processing multiple data batches."""
                user_crud = CRUDOperations(test_database_pool)
                generator = TestDataGenerator()
                
                results = []
                
                for batch_id in range(num_batches):
                    process_id = f"{user_id}_batch_{batch_id}"
                    
                    # Generate batch data
                    batch_data = {
                        "user_id": user_id,
                        "process_id": process_id,
                        "data_batch": batch_id,
                        "data_payload": json.dumps({
                            "records": [
                                {
                                    "id": i,
                                    "value": generator.fake.pydecimal(left_digits=5, right_digits=2),
                                    "category": generator.fake.word(),
                                }
                                for i in range(100)  # 100 records per batch
                            ]
                        }),
                        "status": "processing",
                    }
                    
                    # Insert batch
                    batch_df = pd.DataFrame([batch_data])
                    insert_result = await user_crud.insert_dataframe(
                        dataframe=batch_df,
                        table_name="shared_data",
                        schema=schema_name,
                    )
                    
                    # Simulate processing time
                    await asyncio.sleep(0.1)
                    
                    # Update status to completed
                    await user_crud.update_data(
                        table_name="shared_data",
                        schema=schema_name,
                        data={"status": "completed"},
                        where_clause=f"process_id = '{process_id}'",
                    )
                    
                    results.append({
                        "user_id": user_id,
                        "batch_id": batch_id,
                        "status": "completed",
                    })
                
                return results
            
            # Run concurrent user simulations
            num_users = 5
            batches_per_user = 10
            
            with PerformanceBenchmark("concurrent_users", threshold_ms=30000.0) as benchmark:
                user_tasks = [
                    asyncio.create_task(simulate_user_processing(f"user_{i}", batches_per_user))
                    for i in range(num_users)
                ]
                
                user_results = await asyncio.gather(*user_tasks)
            
            # Verify results
            total_batches = sum(len(result) for result in user_results)
            expected_batches = num_users * batches_per_user
            
            assert total_batches == expected_batches
            
            # Verify data integrity
            crud = CRUDOperations(test_database_pool)
            
            # Check total records
            count_result = await crud.read(
                table_name="shared_data",
                schema=schema_name,
                select_clause="COUNT(*) as total_count",
            )
            
            total_count = count_result[0]["total_count"]
            assert total_count == expected_batches
            
            # Check completion status
            completed_result = await crud.read(
                table_name="shared_data",
                schema=schema_name,
                select_clause="COUNT(*) as completed_count",
                where_clause="status = 'completed'",
            )
            
            completed_count = completed_result[0]["completed_count"]
            assert completed_count == expected_batches
            
            # Check user distribution
            user_distribution = await crud.read(
                table_name="shared_data",
                schema=schema_name,
                select_clause="user_id, COUNT(*) as batch_count",
                group_by="user_id",
                order_by="user_id",
            )
            
            assert len(user_distribution) == num_users
            assert all(record["batch_count"] == batches_per_user for record in user_distribution)
            
            logger.info(f"Concurrent processing test completed: {total_batches} batches processed by {num_users} users")
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)
    
    @pytest.mark.asyncio
    async def test_resource_contention_handling(self, test_database_pool):
        """Test handling of resource contention scenarios."""
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        logger = DatabaseLogger("resource_contention")
        
        schema_name = "contention_test_schema"
        
        try:
            logger.info("Starting resource contention test")
            
            # Setup
            await schema_manager.create_schema(schema_name)
            
            # Create a table with limited resources (single counter)
            counter_table = TableSchema(
                name="shared_counter",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="INTEGER", primary_key=True),
                    ColumnSchema(name="counter_value", data_type="INTEGER", default="0"),
                    ColumnSchema(name="last_updated", data_type="TIMESTAMP", default="NOW()"),
                ],
            )
            
            await schema_manager.create_table(counter_table)
            
            # Initialize counter
            initial_data = pd.DataFrame([{"id": 1, "counter_value": 0}])
            await crud.insert_dataframe(
                dataframe=initial_data,
                table_name="shared_counter",
                schema=schema_name,
            )
            
            # Simulate concurrent counter increments
            async def increment_counter(worker_id: int, increments: int):
                """Simulate a worker incrementing the shared counter."""
                worker_crud = CRUDOperations(test_database_pool)
                successful_increments = 0
                
                for i in range(increments):
                    try:
                        # Use transaction to ensure atomicity
                        async with test_database_pool.acquire() as connection:
                            async with connection.transaction():
                                # Read current value
                                current_result = await connection.fetchrow(
                                    f"SELECT counter_value FROM {schema_name}.shared_counter WHERE id = 1 FOR UPDATE"
                                )
                                current_value = current_result["counter_value"]
                                
                                # Simulate some processing time
                                await asyncio.sleep(0.01)
                                
                                # Increment and update
                                new_value = current_value + 1
                                await connection.execute(
                                    f"UPDATE {schema_name}.shared_counter SET counter_value = $1, last_updated = NOW() WHERE id = 1",
                                    new_value
                                )
                                
                                successful_increments += 1
                    
                    except Exception as e:
                        logger.warning(f"Worker {worker_id} increment {i} failed: {e}")
                        # Retry after short delay
                        await asyncio.sleep(0.05)
                
                return {
                    "worker_id": worker_id,
                    "successful_increments": successful_increments,
                }
            
            # Run concurrent workers
            num_workers = 10
            increments_per_worker = 20
            
            with PerformanceBenchmark("resource_contention", threshold_ms=20000.0) as benchmark:
                worker_tasks = [
                    asyncio.create_task(increment_counter(worker_id, increments_per_worker))
                    for worker_id in range(num_workers)
                ]
                
                worker_results = await asyncio.gather(*worker_tasks, return_exceptions=True)
            
            # Verify results
            successful_results = [r for r in worker_results if not isinstance(r, Exception)]
            total_successful_increments = sum(r["successful_increments"] for r in successful_results)
            
            # Check final counter value
            final_result = await crud.read(
                table_name="shared_counter",
                schema=schema_name,
                where_clause="id = 1",
            )
            
            final_counter_value = final_result[0]["counter_value"]
            
            logger.info(f"Resource contention test results:")
            logger.info(f"- Expected increments: {num_workers * increments_per_worker}")
            logger.info(f"- Successful increments: {total_successful_increments}")
            logger.info(f"- Final counter value: {final_counter_value}")
            logger.info(f"- Successful workers: {len(successful_results)}/{num_workers}")
            
            # Verify data consistency
            assert final_counter_value == total_successful_increments
            assert final_counter_value > 0
            assert len(successful_results) > 0
            
            logger.info("Resource contention test completed successfully")
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)


class TestSystemIntegrationScenarios:
    """Test complete system integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_full_system_workflow(self, test_database_pool):
        """Test complete system workflow from data ingestion to reporting."""
        # Initialize all components
        env_manager = TestEnvironmentManager()
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        importer = DataImporter(test_database_pool)
        exporter = DataExporter(test_database_pool)
        processor = GeospatialProcessor()
        validator = GeometryValidator()
        logger = DatabaseLogger("full_system_workflow")
        
        # Setup test environment
        await env_manager.setup_test_environment()
        
        try:
            logger.info("Starting full system integration workflow")
            
            # Phase 1: System initialization
            logger.info("Phase 1: System initialization")
            
            # Create production-like schema structure
            schemas = ["raw_data", "processed_data", "analytics", "reporting"]
            for schema in schemas:
                await schema_manager.create_schema(schema)
            
            # Phase 2: Data ingestion
            logger.info("Phase 2: Data ingestion")
            
            # Create raw data tables
            raw_ep_table = TableSchema(
                name="raw_ep_measurements",
                schema_name="raw_data",
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="source_file", data_type="VARCHAR(255)"),
                    ColumnSchema(name="ingestion_time", data_type="TIMESTAMP", default="NOW()"),
                    ColumnSchema(name="raw_data", data_type="JSONB"),
                    ColumnSchema(name="validation_status", data_type="VARCHAR(20)", default="'pending'"),
                ],
            )
            
            await schema_manager.create_table(raw_ep_table)
            
            # Simulate data ingestion from multiple sources
            generator = TestDataGenerator()
            ingestion_batches = []
            
            for source_id in range(5):  # 5 data sources
                source_file = f"ep_data_source_{source_id}.json"
                
                # Generate raw EP data
                raw_ep_data = []
                for _ in range(200):  # 200 measurements per source
                    raw_measurement = {
                        "cell_id": f"CELL_{generator.fake.random_int(min=1, max=100):04d}",
                        "timestamp": generator.fake.date_time_between(
                            start_date=datetime.now() - timedelta(hours=1),
                            end_date=datetime.now()
                        ).isoformat(),
                        "measurements": {
                            "rsrp": float(generator.fake.pydecimal(left_digits=3, right_digits=2, min_value=-140, max_value=-40)),
                            "rsrq": float(generator.fake.pydecimal(left_digits=2, right_digits=2, min_value=-20, max_value=-3)),
                            "sinr": float(generator.fake.pydecimal(left_digits=2, right_digits=2, min_value=-10, max_value=30)),
                        },
                        "location": {
                            "latitude": float(generator.fake.latitude()),
                            "longitude": float(generator.fake.longitude()),
                        },
                        "metadata": {
                            "technology": generator.fake.random_element(["4G", "5G"]),
                            "band": generator.fake.random_element(["B1", "B3", "B7", "B20", "N78"]),
                        }
                    }
                    raw_ep_data.append(raw_measurement)
                
                # Ingest batch
                batch_data = pd.DataFrame([{
                    "source_file": source_file,
                    "raw_data": json.dumps(raw_ep_data),
                    "validation_status": "pending",
                }])
                
                await crud.insert_dataframe(
                    dataframe=batch_data,
                    table_name="raw_ep_measurements",
                    schema="raw_data",
                )
                
                ingestion_batches.append(source_file)
            
            logger.info(f"Ingested {len(ingestion_batches)} data batches")
            
            # Phase 3: Data processing and validation
            logger.info("Phase 3: Data processing and validation")
            
            # Create processed data table
            processed_ep_table = TableSchema(
                name="processed_ep_measurements",
                schema_name="processed_data",
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="source_id", data_type="INTEGER"),
                    ColumnSchema(name="cell_id", data_type="VARCHAR(50)"),
                    ColumnSchema(name="measurement_time", data_type="TIMESTAMP"),
                    ColumnSchema(name="rsrp", data_type="DECIMAL(8,2)"),
                    ColumnSchema(name="rsrq", data_type="DECIMAL(8,2)"),
                    ColumnSchema(name="sinr", data_type="DECIMAL(8,2)"),
                    ColumnSchema(name="location", data_type="GEOMETRY(POINT, 4326)"),
                    ColumnSchema(name="technology", data_type="VARCHAR(10)"),
                    ColumnSchema(name="band", data_type="VARCHAR(20)"),
                    ColumnSchema(name="quality_score", data_type="DECIMAL(5,2)"),
                    ColumnSchema(name="processing_time", data_type="TIMESTAMP", default="NOW()"),
                ],
            )
            
            await schema_manager.create_table(processed_ep_table)
            
            # Process raw data
            raw_data_records = await crud.read(
                table_name="raw_ep_measurements",
                schema="raw_data",
                where_clause="validation_status = 'pending'",
            )
            
            processed_records = []
            
            for raw_record in raw_data_records:
                raw_data = json.loads(raw_record["raw_data"])
                source_id = raw_record["id"]
                
                for measurement in raw_data:
                    # Validate and process measurement
                    try:
                        # Create geometry
                        location = Point(
                            measurement["location"]["longitude"],
                            measurement["location"]["latitude"]
                        )
                        
                        # Validate geometry
                        if not validator.validate_geometry(location):
                            continue
                        
                        # Calculate quality score
                        rsrp = measurement["measurements"]["rsrp"]
                        rsrq = measurement["measurements"]["rsrq"]
                        sinr = measurement["measurements"]["sinr"]
                        
                        quality_score = min(100, max(0, 
                            (rsrp + 140) * 0.5 + 
                            (rsrq + 20) * 2 + 
                            (sinr + 10) * 1.5
                        ))
                        
                        processed_record = {
                            "source_id": source_id,
                            "cell_id": measurement["cell_id"],
                            "measurement_time": measurement["timestamp"],
                            "rsrp": rsrp,
                            "rsrq": rsrq,
                            "sinr": sinr,
                            "location": location.wkt,
                            "technology": measurement["metadata"]["technology"],
                            "band": measurement["metadata"]["band"],
                            "quality_score": quality_score,
                        }
                        
                        processed_records.append(processed_record)
                    
                    except Exception as e:
                        logger.warning(f"Failed to process measurement: {e}")
                        continue
                
                # Update validation status
                await crud.update_data(
                    table_name="raw_ep_measurements",
                    schema="raw_data",
                    data={"validation_status": "processed"},
                    where_clause=f"id = {source_id}",
                )
            
            # Insert processed data
            if processed_records:
                processed_df = pd.DataFrame(processed_records)
                await crud.insert_dataframe(
                    dataframe=processed_df,
                    table_name="processed_ep_measurements",
                    schema="processed_data",
                )
            
            logger.info(f"Processed {len(processed_records)} measurements")
            
            # Phase 4: Analytics and aggregation
            logger.info("Phase 4: Analytics and aggregation")
            
            # Create analytics tables
            cell_analytics_table = TableSchema(
                name="cell_performance_analytics",
                schema_name="analytics",
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="cell_id", data_type="VARCHAR(50)"),
                    ColumnSchema(name="analysis_period", data_type="VARCHAR(20)"),
                    ColumnSchema(name="measurement_count", data_type="INTEGER"),
                    ColumnSchema(name="avg_rsrp", data_type="DECIMAL(8,2)"),
                    ColumnSchema(name="avg_rsrq", data_type="DECIMAL(8,2)"),
                    ColumnSchema(name="avg_sinr", data_type="DECIMAL(8,2)"),
                    ColumnSchema(name="avg_quality_score", data_type="DECIMAL(5,2)"),
                    ColumnSchema(name="technology", data_type="VARCHAR(10)"),
                    ColumnSchema(name="analysis_time", data_type="TIMESTAMP", default="NOW()"),
                ],
            )
            
            await schema_manager.create_table(cell_analytics_table)
            
            # Perform analytics
            analytics_query = """
                SELECT 
                    cell_id,
                    'hourly' as analysis_period,
                    COUNT(*) as measurement_count,
                    AVG(rsrp) as avg_rsrp,
                    AVG(rsrq) as avg_rsrq,
                    AVG(sinr) as avg_sinr,
                    AVG(quality_score) as avg_quality_score,
                    technology
                FROM processed_data.processed_ep_measurements
                WHERE measurement_time >= NOW() - INTERVAL '1 hour'
                GROUP BY cell_id, technology
                HAVING COUNT(*) >= 5
            """
            
            analytics_results = await crud.execute_query(analytics_query)
            
            if analytics_results:
                analytics_df = pd.DataFrame(analytics_results)
                await crud.insert_dataframe(
                    dataframe=analytics_df,
                    table_name="cell_performance_analytics",
                    schema="analytics",
                )
            
            logger.info(f"Generated analytics for {len(analytics_results)} cells")
            
            # Phase 5: Reporting
            logger.info("Phase 5: Reporting")
            
            # Create reporting views and summaries
            reporting_summary_table = TableSchema(
                name="system_performance_summary",
                schema_name="reporting",
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="report_date", data_type="DATE", default="CURRENT_DATE"),
                    ColumnSchema(name="total_measurements", data_type="INTEGER"),
                    ColumnSchema(name="total_cells", data_type="INTEGER"),
                    ColumnSchema(name="avg_system_quality", data_type="DECIMAL(5,2)"),
                    ColumnSchema(name="technology_distribution", data_type="JSONB"),
                    ColumnSchema(name="performance_categories", data_type="JSONB"),
                    ColumnSchema(name="report_generation_time", data_type="TIMESTAMP", default="NOW()"),
                ],
            )
            
            await schema_manager.create_table(reporting_summary_table)
            
            # Generate system summary report
            summary_query = """
                SELECT 
                    COUNT(*) as total_measurements,
                    COUNT(DISTINCT cell_id) as total_cells,
                    AVG(avg_quality_score) as avg_system_quality
                FROM analytics.cell_performance_analytics
            """
            
            summary_result = await crud.execute_query(summary_query)
            
            technology_dist_query = """
                SELECT 
                    technology,
                    COUNT(*) as cell_count
                FROM analytics.cell_performance_analytics
                GROUP BY technology
            """
            
            tech_dist_result = await crud.execute_query(technology_dist_query)
            
            performance_categories_query = """
                SELECT 
                    CASE 
                        WHEN avg_quality_score >= 80 THEN 'excellent'
                        WHEN avg_quality_score >= 60 THEN 'good'
                        WHEN avg_quality_score >= 40 THEN 'fair'
                        ELSE 'poor'
                    END as performance_category,
                    COUNT(*) as cell_count
                FROM analytics.cell_performance_analytics
                GROUP BY 
                    CASE 
                        WHEN avg_quality_score >= 80 THEN 'excellent'
                        WHEN avg_quality_score >= 60 THEN 'good'
                        WHEN avg_quality_score >= 40 THEN 'fair'
                        ELSE 'poor'
                    END
            """
            
            perf_categories_result = await crud.execute_query(performance_categories_query)
            
            # Create summary report
            if summary_result:
                technology_distribution = {record["technology"]: record["cell_count"] for record in tech_dist_result}
                performance_categories = {record["performance_category"]: record["cell_count"] for record in perf_categories_result}
                
                summary_report = {
                    "total_measurements": summary_result[0]["total_measurements"],
                    "total_cells": summary_result[0]["total_cells"],
                    "avg_system_quality": float(summary_result[0]["avg_system_quality"]),
                    "technology_distribution": json.dumps(technology_distribution),
                    "performance_categories": json.dumps(performance_categories),
                }
                
                summary_df = pd.DataFrame([summary_report])
                await crud.insert_dataframe(
                    dataframe=summary_df,
                    table_name="system_performance_summary",
                    schema="reporting",
                )
            
            # Phase 6: Data export and archival
            logger.info("Phase 6: Data export and archival")
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Export processed data
                processed_data_query = """
                    SELECT 
                        cell_id,
                        measurement_time,
                        rsrp,
                        rsrq,
                        sinr,
                        ST_X(location) as longitude,
                        ST_Y(location) as latitude,
                        technology,
                        band,
                        quality_score
                    FROM processed_data.processed_ep_measurements
                    ORDER BY measurement_time DESC
                    LIMIT 1000
                """
                
                export_data = await crud.execute_query(processed_data_query)
                
                if export_data:
                    export_file = Path(temp_dir) / "processed_measurements_export.csv"
                    export_df = pd.DataFrame(export_data)
                    export_df.to_csv(export_file, index=False)
                    
                    # Verify export
                    assert export_file.exists()
                    assert len(export_df) > 0
                    
                    logger.info(f"Exported {len(export_df)} processed measurements")
                
                # Export analytics summary
                analytics_export_file = Path(temp_dir) / "analytics_summary.json"
                with open(analytics_export_file, 'w') as f:
                    json.dump({
                        "summary": summary_report if summary_result else {},
                        "technology_distribution": technology_distribution if tech_dist_result else {},
                        "performance_categories": performance_categories if perf_categories_result else {},
                        "export_timestamp": datetime.now().isoformat(),
                    }, f, indent=2)
                
                assert analytics_export_file.exists()
                logger.info(f"Exported analytics summary to {analytics_export_file}")
            
            # Phase 7: System verification
            logger.info("Phase 7: System verification")
            
            # Verify data flow integrity
            raw_count_result = await crud.read(
                table_name="raw_ep_measurements",
                schema="raw_data",
                select_clause="COUNT(*) as count",
            )
            raw_count = raw_count_result[0]["count"]
            
            processed_count_result = await crud.read(
                table_name="processed_ep_measurements",
                schema="processed_data",
                select_clause="COUNT(*) as count",
            )
            processed_count = processed_count_result[0]["count"]
            
            analytics_count_result = await crud.read(
                table_name="cell_performance_analytics",
                schema="analytics",
                select_clause="COUNT(*) as count",
            )
            analytics_count = analytics_count_result[0]["count"]
            
            reporting_count_result = await crud.read(
                table_name="system_performance_summary",
                schema="reporting",
                select_clause="COUNT(*) as count",
            )
            reporting_count = reporting_count_result[0]["count"]
            
            # Verify system integrity
            assert raw_count > 0, "No raw data found"
            assert processed_count > 0, "No processed data found"
            assert analytics_count > 0, "No analytics data found"
            assert reporting_count > 0, "No reporting data found"
            
            # Verify data quality
            quality_check_query = """
                SELECT 
                    AVG(quality_score) as avg_quality,
                    MIN(quality_score) as min_quality,
                    MAX(quality_score) as max_quality,
                    COUNT(*) as total_measurements
                FROM processed_data.processed_ep_measurements
                WHERE quality_score IS NOT NULL
            """
            
            quality_result = await crud.execute_query(quality_check_query)
            
            if quality_result:
                avg_quality = float(quality_result[0]["avg_quality"])
                min_quality = float(quality_result[0]["min_quality"])
                max_quality = float(quality_result[0]["max_quality"])
                
                assert 0 <= min_quality <= 100, f"Invalid min quality score: {min_quality}"
                assert 0 <= max_quality <= 100, f"Invalid max quality score: {max_quality}"
                assert 0 <= avg_quality <= 100, f"Invalid avg quality score: {avg_quality}"
            
            logger.info("Full system integration workflow completed successfully")
            logger.info(f"Data flow: {raw_count} raw -> {processed_count} processed -> {analytics_count} analytics -> {reporting_count} reports")
            
            if quality_result:
                logger.info(f"Quality metrics: avg={avg_quality:.2f}, min={min_quality:.2f}, max={max_quality:.2f}")
            
        finally:
            # Cleanup
            for schema in schemas:
                await schema_manager.drop_schema(schema, cascade=True)
            
            await env_manager.cleanup_test_environment()


class TestPerformanceUnderLoad:
    """Test system performance under realistic loads."""
    
    @pytest.mark.asyncio
    async def test_high_volume_data_processing(self, test_database_pool):
        """Test system performance with high volume data processing."""
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        logger = DatabaseLogger("high_volume_test")
        
        schema_name = "high_volume_test_schema"
        
        try:
            logger.info("Starting high volume data processing test")
            
            # Setup
            await schema_manager.create_schema(schema_name)
            
            # Create high-performance table
            high_volume_table = TableSchema(
                name="high_volume_measurements",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="BIGSERIAL", primary_key=True),
                    ColumnSchema(name="batch_id", data_type="INTEGER"),
                    ColumnSchema(name="measurement_time", data_type="TIMESTAMP"),
                    ColumnSchema(name="device_id", data_type="VARCHAR(50)"),
                    ColumnSchema(name="metric_value", data_type="DECIMAL(12,4)"),
                    ColumnSchema(name="metric_type", data_type="VARCHAR(20)"),
                    ColumnSchema(name="location_hash", data_type="VARCHAR(32)"),
                    ColumnSchema(name="quality_flag", data_type="BOOLEAN"),
                ],
            )
            
            await schema_manager.create_table(high_volume_table)
            
            # Create optimized indexes
            await schema_manager.create_index(
                table_name="high_volume_measurements",
                schema=schema_name,
                index_name="idx_hv_batch_time",
                columns=["batch_id", "measurement_time"],
            )
            
            await schema_manager.create_index(
                table_name="high_volume_measurements",
                schema=schema_name,
                index_name="idx_hv_device_type",
                columns=["device_id", "metric_type"],
            )
            
            # Generate and process high volume data
            generator = TestDataGenerator()
            total_records = 100000  # 100K records
            batch_size = 5000
            num_batches = total_records // batch_size
            
            logger.info(f"Processing {total_records} records in {num_batches} batches")
            
            with PerformanceBenchmark("high_volume_processing", threshold_ms=60000.0) as benchmark:
                for batch_id in range(num_batches):
                    # Generate batch data
                    batch_data = []
                    
                    for _ in range(batch_size):
                        record = {
                            "batch_id": batch_id,
                            "measurement_time": generator.fake.date_time_between(
                                start_date=datetime.now() - timedelta(hours=1),
                                end_date=datetime.now()
                            ),
                            "device_id": f"DEVICE_{generator.fake.random_int(min=1, max=1000):04d}",
                            "metric_value": generator.fake.pydecimal(left_digits=8, right_digits=4),
                            "metric_type": generator.fake.random_element(["temperature", "humidity", "pressure", "signal"]),
                            "location_hash": generator.fake.md5(),
                            "quality_flag": generator.fake.boolean(chance_of_getting_true=85),
                        }
                        batch_data.append(record)
                    
                    # Insert batch
                    batch_df = pd.DataFrame(batch_data)
                    await crud.insert_dataframe(
                        dataframe=batch_df,
                        table_name="high_volume_measurements",
                        schema=schema_name,
                    )
                    
                    if (batch_id + 1) % 5 == 0:
                        logger.info(f"Processed batch {batch_id + 1}/{num_batches}")
            
            # Verify data integrity
            count_result = await crud.select_data(
                table_name="high_volume_measurements",
                schema=schema_name,
                select_clause="COUNT(*) as total_count",
            )
            
            total_count = count_result[0]["total_count"]
            assert total_count == total_records
            
            # Performance analysis
            analysis_query = """
                SELECT 
                    batch_id,
                    COUNT(*) as record_count,
                    AVG(metric_value) as avg_value,
                    COUNT(DISTINCT device_id) as unique_devices,
                    SUM(CASE WHEN quality_flag THEN 1 ELSE 0 END) as quality_records
                FROM {}.high_volume_measurements
                GROUP BY batch_id
                ORDER BY batch_id
            """.format(schema_name)
            
            analysis_results = await crud.execute_query(analysis_query)
            
            assert len(analysis_results) == num_batches
            assert all(record["record_count"] == batch_size for record in analysis_results)
            
            logger.info(f"High volume test completed: {total_count} records processed successfully")
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)


if __name__ == "__main__":
    # Run specific test scenarios
    import sys
    
    if len(sys.argv) > 1:
        scenario = sys.argv[1]
        if scenario == "telecom":
            pytest.main(["-v", "test_comprehensive_e2e.py::TestTelecommunicationsDataWorkflow"])
        elif scenario == "geospatial":
            pytest.main(["-v", "test_comprehensive_e2e.py::TestGeospatialAnalysisWorkflow"])
        elif scenario == "concurrency":
            pytest.main(["-v", "test_comprehensive_e2e.py::TestMultiUserConcurrencyScenarios"])
        elif scenario == "integration":
            pytest.main(["-v", "test_comprehensive_e2e.py::TestSystemIntegrationScenarios"])
        elif scenario == "performance":
            pytest.main(["-v", "test_comprehensive_e2e.py::TestPerformanceUnderLoad"])
        else:
            print("Available scenarios: telecom, geospatial, concurrency, integration, performance")
    else:
        # Run all tests
        pytest.main(["-v", "test_comprehensive_e2e.py"])