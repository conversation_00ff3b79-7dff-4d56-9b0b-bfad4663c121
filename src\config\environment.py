__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Environment Management Module

Provides environment variable management and configuration functionality.
"""

import logging
import os
import shutil
import subprocess
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class EnvironmentManager:
    """Environment Manager

    Responsible for managing application environment variables and configuration.
    """

    def __init__(self, project_root: Optional[Union[str, Path]] = None):
        """
        Initialize environment manager

        Args:
            project_root: Project root directory path
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.env_vars = {}

    def detect_python_environments(self) -> Dict[str, Any]:
        """
        Detect Python environments in the system

        Returns:
            dict: Python environment information
        """
        environments = {
            "system_python": None,
            "virtual_envs": [],
            "conda_envs": [],
            "pyenv_versions": [],
            "qgis_python": None,
        }

        # Detect system Python
        try:
            result = subprocess.run(
                [sys.executable, "--version"], capture_output=True, text=True
            )
            if result.returncode == 0:
                environments["system_python"] = {
                    "path": sys.executable,
                    "version": result.stdout.strip(),
                }
        except Exception as e:
            self.logger.warning(f"Failed to detect system Python: {e}")

        # Detect virtual environment
        venv_path = self.project_root / ".venv"
        if venv_path.exists():
            python_exe = venv_path / "Scripts" / "python.exe"
            if python_exe.exists():
                try:
                    result = subprocess.run(
                        [str(python_exe), "--version"], capture_output=True, text=True
                    )
                    if result.returncode == 0:
                        environments["virtual_envs"].append(
                            {
                                "name": ".venv",
                                "path": str(venv_path),
                                "python_path": str(python_exe),
                                "version": result.stdout.strip(),
                            }
                        )
                except Exception as e:
                    self.logger.warning(f"Failed to detect virtual environment: {e}")

        # Detect pyenv versions
        pyenv_paths = [
            Path.home() / ".pyenv" / "pyenv-win" / "bin" / "pyenv.bat",
            Path("C:/Users")
            / os.getenv("USERNAME", "")
            / ".pyenv"
            / "pyenv-win"
            / "bin"
            / "pyenv.bat",
        ]

        for pyenv_path in pyenv_paths:
            if pyenv_path.exists():
                try:
                    result = subprocess.run(
                        [str(pyenv_path), "versions"], capture_output=True, text=True
                    )
                    if result.returncode == 0 and result.stdout.strip():
                        versions = []
                        for line in result.stdout.strip().split("\n"):
                            line = line.strip()
                            if line and not line.startswith("*"):
                                versions.append(line.replace("*", "").strip())
                        environments["pyenv_versions"] = versions
                        break
                except Exception as e:
                    self.logger.warning(f"Failed to detect pyenv versions: {e}")

        # Detect QGIS Python
        qgis_paths = [
            Path("C:/OSGeo4W/apps/Python312/python.exe"),
            Path("C:/OSGeo4W/bin/python3.exe"),
            Path("C:/Program Files/QGIS 3.34/bin/python.exe"),
        ]

        for qgis_python in qgis_paths:
            if qgis_python.exists():
                try:
                    result = subprocess.run(
                        [str(qgis_python), "--version"], capture_output=True, text=True
                    )
                    if result.returncode == 0:
                        environments["qgis_python"] = {
                            "path": str(qgis_python),
                            "version": result.stdout.strip(),
                        }
                        break
                except Exception as e:
                    self.logger.warning(f"Failed to detect QGIS Python: {e}")

        # Detect Conda environment
        try:
            result = subprocess.run(
                ["conda", "env", "list"], capture_output=True, text=True
            )
            if result.returncode == 0:
                conda_envs = []
                for line in result.stdout.split("\n"):
                    if line.strip() and not line.startswith("#"):
                        parts = line.split()
                        if len(parts) >= 2:
                            env_name = parts[0]
                            env_path = parts[-1]
                            conda_envs.append({"name": env_name, "path": env_path})
                environments["conda_envs"] = conda_envs
        except Exception as e:
            self.logger.warning(f"Failed to detect Conda environment: {e}")

        return environments

    def setup_environment_variables(self, variables: Dict[str, str]) -> None:
        """
        Set environment variables

        Args:
            variables: Environment variables dictionary
        """
        for key, value in variables.items():
            os.environ[key] = value
            self.env_vars[key] = value
            self.logger.info(f"Setting environment variable: {key}={value}")

    def setup_qgis_environment(self, qgis_path: Optional[str] = None) -> bool:
        """
        Set QGIS environment variables

        Args:
            qgis_path: QGIS installation path

        Returns:
            bool: Whether setup was successful
        """
        if not qgis_path:
            # Auto-detect QGIS path
            possible_paths = [
                "C:/OSGeo4W",
                "C:/Program Files/QGIS 3.34",
                "C:/Program Files/QGIS 3.32",
            ]

            for path in possible_paths:
                if Path(path).exists():
                    qgis_path = path
                    break

        if not qgis_path or not Path(qgis_path).exists():
            self.logger.error("QGIS installation path not found")
            return False

        qgis_env_vars = {
            "QGIS_PREFIX_PATH": qgis_path,
            "QT_QPA_PLATFORM_PLUGIN_PATH": f"{qgis_path}/apps/Qt5/plugins",
            "GDAL_DATA": f"{qgis_path}/share/gdal",
            "PROJ_LIB": f"{qgis_path}/share/proj",
        }

        self.setup_environment_variables(qgis_env_vars)

        # Add QGIS Python paths to sys.path
        qgis_python_paths = [
            f"{qgis_path}/apps/qgis/python",
            f"{qgis_path}/apps/Python312/Lib/site-packages",
            f"{qgis_path}/apps/qgis/python/plugins",
        ]

        for path in qgis_python_paths:
            if Path(path).exists() and path not in sys.path:
                sys.path.insert(0, path)
                self.logger.info(f"Added Python path: {path}")

        return True

    def create_virtual_environment(
        self, env_name: str = ".venv", python_version: Optional[str] = None
    ) -> bool:
        """
        Create virtual environment

        Args:
            env_name: Environment name
            python_version: Python version

        Returns:
            bool: Whether creation was successful
        """
        try:
            env_path = self.project_root / env_name

            if env_path.exists():
                self.logger.warning(f"Virtual environment already exists: {env_path}")
            return True

            # Create virtual environment using venv
            cmd = [sys.executable, "-m", "venv", str(env_path)]
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                self.logger.info(
                    f"Virtual environment created successfully: {env_path}"
                )
                return True
            else:
                self.logger.error(
                    f"Failed to create virtual environment: {result.stderr}"
                )
                return False

        except Exception as e:
            self.logger.error(f"Failed to create virtual environment: {e}")
            return False

    def activate_virtual_environment(self, env_path: Union[str, Path]) -> bool:
        """
        Activate virtual environment

        Args:
            env_path: Virtual environment path

        Returns:
            bool: Whether activation was successful
        """
        try:
            env_path = Path(env_path)
            activate_script = env_path / "Scripts" / "activate.bat"

            if not activate_script.exists():
                self.logger.error(
                    f"Activation script does not exist: {activate_script}"
                )
                return False

            # Set virtual environment related environment variables
            self.setup_environment_variables(
                {
                    "VIRTUAL_ENV": str(env_path),
                    "PATH": f"{env_path / 'Scripts'};{os.environ.get('PATH', '')}",
                }
            )

            self.logger.info(f"Virtual environment activated successfully: {env_path}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to activate virtual environment: {e}")
            return False

    def install_poetry_dependencies(self) -> bool:
        """
        Install Poetry dependencies

        Returns:
            bool: Whether installation was successful
        """
        try:
            pyproject_path = self.project_root / "pyproject.toml"

            if not pyproject_path.exists():
                self.logger.error("pyproject.toml file does not exist")
                return False

            # Run poetry install
            poetry_path = shutil.which("poetry")
            if not poetry_path:
                self.logger.error("Poetry executable not found in PATH")
                return False

            result = subprocess.run(
                [poetry_path, "install"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                shell=False,
            )

            if result.returncode == 0:
                self.logger.info("Poetry dependencies installed successfully")
                return True
            else:
                self.logger.error(
                    f"Poetry dependency installation failed: {result.stderr}"
                )
                return False

        except Exception as e:
            self.logger.error(f"Failed to install Poetry dependencies: {e}")
            return False

    def check_poetry_status(self) -> Dict[str, Any]:
        """
        Check Poetry status

        Returns:
            dict: Poetry status information
        """
        status = {
            "poetry_available": False,
            "virtual_env": None,
            "dependencies": [],
            "python_version": None,
        }

        try:
            # Check if Poetry is available
            poetry_path = shutil.which("poetry")
            if not poetry_path:
                return status

            result = subprocess.run(
                [poetry_path, "--version"], capture_output=True, text=True, shell=False
            )
            if result.returncode == 0:
                status["poetry_available"] = True

            # Check virtual environment information
            result = subprocess.run(
                [poetry_path, "env", "info"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                shell=False,
            )
            if result.returncode == 0:
                for line in result.stdout.split("\n"):
                    if "Path:" in line:
                        status["virtual_env"] = line.split(":", 1)[1].strip()
                    elif "Python:" in line:
                        status["python_version"] = line.split(":", 1)[1].strip()

            # Check installed dependencies
            result = subprocess.run(
                [poetry_path, "show"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                shell=False,
            )
            if result.returncode == 0:
                dependencies = []
                for line in result.stdout.split("\n"):
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 2:
                            dependencies.append({"name": parts[0], "version": parts[1]})
                status["dependencies"] = dependencies

        except Exception as e:
            self.logger.warning(f"Failed to check Poetry status: {e}")

        return status

    def get_environment_info(self) -> Dict[str, Any]:
        """
        Get environment information

        Returns:
            dict: Environment information dictionary
        """
        info = {
            "project_root": str(self.project_root),
            "current_python": sys.executable,
            "python_version": sys.version,
            "environment_variables": dict(self.env_vars),
            "python_environments": self.detect_python_environments(),
            "poetry_status": self.check_poetry_status(),
        }

        return info

    def export_environment_script(
        self, output_path: Optional[Union[str, Path]] = None
    ) -> str:
        """
        Export environment setup script

        Args:
            output_path: Output file path

        Returns:
            str: Script content
        """
        script_lines = [
            "@echo off",
            "REM Environment variable setup script",
            "REM Automatically generated by EnvironmentManager",
            "",
        ]

        # Add environment variable settings
        for key, value in self.env_vars.items():
            script_lines.append(f"set {key}={value}")

        script_lines.extend(
            [
                "",
                "REM Activate virtual environment",
                "if exist .venv\\Scripts\\activate.bat (",
                "    call .venv\\Scripts\\activate.bat",
                "    echo Virtual environment activated",
                ") else (",
                "    echo Virtual environment does not exist",
                ")",
                "",
                "echo Environment setup complete",
            ]
        )

        script_content = "\n".join(script_lines)

        if output_path:
            output_path = Path(output_path)
            output_path.write_text(script_content, encoding="utf-8")
            self.logger.info(f"Environment script exported: {output_path}")

        return script_content
