#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控集成模块

本模块提供测试结果到CI/CD监控dashboard的集成功能，包括：
1. 测试结果收集和聚合
2. 性能指标监控
3. 安全扫描结果监控
4. 质量指标追踪
5. 告警和通知
6. 仪表板数据推送
7. 历史趋势分析
8. 自动化报告生成
"""

import asyncio
import json
import logging
import os
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import aiofiles
import aiohttp
import pytest
from prometheus_client import CollectorRegistry, Counter, Gauge, Histogram, push_to_gateway

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TestMetrics:
    """测试指标"""
    test_suite: str
    test_name: str
    status: str  # passed, failed, skipped, error
    duration: float
    timestamp: datetime
    error_message: Optional[str] = None
    tags: Dict[str, str] = field(default_factory=dict)
    custom_metrics: Dict[str, Union[int, float]] = field(default_factory=dict)


@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation: str
    response_time: float
    throughput: float
    memory_usage: float
    cpu_usage: float
    error_rate: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class SecurityMetrics:
    """安全指标"""
    scan_type: str
    vulnerabilities_found: int
    critical_count: int
    high_count: int
    medium_count: int
    low_count: int
    security_score: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class QualityMetrics:
    """质量指标"""
    code_coverage: float
    test_coverage: float
    complexity_score: float
    maintainability_index: float
    technical_debt_ratio: float
    bug_density: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class MonitoringConfig:
    """监控配置"""
    prometheus_gateway: Optional[str] = None
    grafana_url: Optional[str] = None
    grafana_api_key: Optional[str] = None
    slack_webhook: Optional[str] = None
    email_config: Optional[Dict[str, str]] = None
    custom_webhooks: List[str] = field(default_factory=list)
    alert_thresholds: Dict[str, float] = field(default_factory=dict)
    dashboard_config: Dict[str, Any] = field(default_factory=dict)


class MonitoringIntegration:
    """监控集成类"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.registry = CollectorRegistry()
        self.session = None
        
        # 初始化Prometheus指标
        self._init_prometheus_metrics()
        
        # 测试结果存储
        self.test_results: List[TestMetrics] = []
        self.performance_results: List[PerformanceMetrics] = []
        self.security_results: List[SecurityMetrics] = []
        self.quality_results: List[QualityMetrics] = []
    
    def _init_prometheus_metrics(self):
        """初始化Prometheus指标"""
        # 测试指标
        self.test_counter = Counter(
            'tests_total',
            'Total number of tests',
            ['suite', 'status', 'environment'],
            registry=self.registry
        )
        
        self.test_duration = Histogram(
            'test_duration_seconds',
            'Test execution duration',
            ['suite', 'test_name', 'environment'],
            registry=self.registry
        )
        
        # 性能指标
        self.response_time = Histogram(
            'response_time_seconds',
            'API response time',
            ['operation', 'environment'],
            registry=self.registry
        )
        
        self.throughput = Gauge(
            'throughput_ops_per_second',
            'Operations per second',
            ['operation', 'environment'],
            registry=self.registry
        )
        
        self.memory_usage = Gauge(
            'memory_usage_bytes',
            'Memory usage in bytes',
            ['component', 'environment'],
            registry=self.registry
        )
        
        self.cpu_usage = Gauge(
            'cpu_usage_percent',
            'CPU usage percentage',
            ['component', 'environment'],
            registry=self.registry
        )
        
        # 安全指标
        self.vulnerabilities = Gauge(
            'security_vulnerabilities_total',
            'Total security vulnerabilities',
            ['severity', 'scan_type', 'environment'],
            registry=self.registry
        )
        
        self.security_score = Gauge(
            'security_score',
            'Overall security score',
            ['scan_type', 'environment'],
            registry=self.registry
        )
        
        # 质量指标
        self.code_coverage = Gauge(
            'code_coverage_percent',
            'Code coverage percentage',
            ['component', 'environment'],
            registry=self.registry
        )
        
        self.technical_debt = Gauge(
            'technical_debt_ratio',
            'Technical debt ratio',
            ['component', 'environment'],
            registry=self.registry
        )
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def record_test_result(self, metrics: TestMetrics):
        """记录测试结果"""
        self.test_results.append(metrics)
        
        # 更新Prometheus指标
        environment = metrics.tags.get('environment', 'unknown')
        self.test_counter.labels(
            suite=metrics.test_suite,
            status=metrics.status,
            environment=environment
        ).inc()
        
        self.test_duration.labels(
            suite=metrics.test_suite,
            test_name=metrics.test_name,
            environment=environment
        ).observe(metrics.duration)
        
        logger.info(f"记录测试结果: {metrics.test_suite}.{metrics.test_name} - {metrics.status}")
    
    def record_performance_metrics(self, metrics: PerformanceMetrics):
        """记录性能指标"""
        self.performance_results.append(metrics)
        
        # 更新Prometheus指标
        environment = metrics.tags.get('environment', 'unknown')
        
        self.response_time.labels(
            operation=metrics.operation,
            environment=environment
        ).observe(metrics.response_time)
        
        self.throughput.labels(
            operation=metrics.operation,
            environment=environment
        ).set(metrics.throughput)
        
        self.memory_usage.labels(
            component=metrics.operation,
            environment=environment
        ).set(metrics.memory_usage)
        
        self.cpu_usage.labels(
            component=metrics.operation,
            environment=environment
        ).set(metrics.cpu_usage)
        
        logger.info(f"记录性能指标: {metrics.operation} - 响应时间: {metrics.response_time}s")
    
    def record_security_metrics(self, metrics: SecurityMetrics):
        """记录安全指标"""
        self.security_results.append(metrics)
        
        # 更新Prometheus指标
        environment = metrics.tags.get('environment', 'unknown')
        
        self.vulnerabilities.labels(
            severity='critical',
            scan_type=metrics.scan_type,
            environment=environment
        ).set(metrics.critical_count)
        
        self.vulnerabilities.labels(
            severity='high',
            scan_type=metrics.scan_type,
            environment=environment
        ).set(metrics.high_count)
        
        self.vulnerabilities.labels(
            severity='medium',
            scan_type=metrics.scan_type,
            environment=environment
        ).set(metrics.medium_count)
        
        self.vulnerabilities.labels(
            severity='low',
            scan_type=metrics.scan_type,
            environment=environment
        ).set(metrics.low_count)
        
        self.security_score.labels(
            scan_type=metrics.scan_type,
            environment=environment
        ).set(metrics.security_score)
        
        logger.info(f"记录安全指标: {metrics.scan_type} - 安全评分: {metrics.security_score}")
    
    def record_quality_metrics(self, metrics: QualityMetrics):
        """记录质量指标"""
        self.quality_results.append(metrics)
        
        # 更新Prometheus指标
        environment = metrics.tags.get('environment', 'unknown')
        
        self.code_coverage.labels(
            component='overall',
            environment=environment
        ).set(metrics.code_coverage)
        
        self.technical_debt.labels(
            component='overall',
            environment=environment
        ).set(metrics.technical_debt_ratio)
        
        logger.info(f"记录质量指标: 代码覆盖率: {metrics.code_coverage}%")
    
    async def push_metrics_to_prometheus(self):
        """推送指标到Prometheus Gateway"""
        if not self.config.prometheus_gateway:
            logger.warning("未配置Prometheus Gateway，跳过指标推送")
            return
        
        try:
            job_name = 'connect_tests'
            push_to_gateway(
                self.config.prometheus_gateway,
                job=job_name,
                registry=self.registry
            )
            logger.info(f"成功推送指标到Prometheus Gateway: {self.config.prometheus_gateway}")
        
        except Exception as e:
            logger.error(f"推送指标到Prometheus失败: {e}")
    
    async def create_grafana_dashboard(self):
        """创建Grafana仪表板"""
        if not self.config.grafana_url or not self.config.grafana_api_key:
            logger.warning("未配置Grafana，跳过仪表板创建")
            return
        
        dashboard_config = {
            "dashboard": {
                "id": None,
                "title": "Connect测试监控仪表板",
                "tags": ["testing", "monitoring", "connect"],
                "timezone": "browser",
                "panels": [
                    self._create_test_results_panel(),
                    self._create_performance_panel(),
                    self._create_security_panel(),
                    self._create_quality_panel()
                ],
                "time": {
                    "from": "now-24h",
                    "to": "now"
                },
                "refresh": "5m"
            },
            "overwrite": True
        }
        
        try:
            headers = {
                "Authorization": f"Bearer {self.config.grafana_api_key}",
                "Content-Type": "application/json"
            }
            
            async with self.session.post(
                f"{self.config.grafana_url}/api/dashboards/db",
                json=dashboard_config,
                headers=headers
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"成功创建Grafana仪表板: {result.get('url')}")
                else:
                    logger.error(f"创建Grafana仪表板失败: {response.status}")
        
        except Exception as e:
            logger.error(f"创建Grafana仪表板异常: {e}")
    
    def _create_test_results_panel(self) -> Dict[str, Any]:
        """创建测试结果面板"""
        return {
            "id": 1,
            "title": "测试结果统计",
            "type": "stat",
            "targets": [
                {
                    "expr": "sum(tests_total) by (status)",
                    "legendFormat": "{{status}}"
                }
            ],
            "fieldConfig": {
                "defaults": {
                    "color": {
                        "mode": "palette-classic"
                    },
                    "custom": {
                        "displayMode": "list",
                        "orientation": "horizontal"
                    }
                }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
        }
    
    def _create_performance_panel(self) -> Dict[str, Any]:
        """创建性能监控面板"""
        return {
            "id": 2,
            "title": "性能指标",
            "type": "timeseries",
            "targets": [
                {
                    "expr": "histogram_quantile(0.95, response_time_seconds)",
                    "legendFormat": "95th percentile response time"
                },
                {
                    "expr": "avg(throughput_ops_per_second)",
                    "legendFormat": "Average throughput"
                }
            ],
            "fieldConfig": {
                "defaults": {
                    "color": {
                        "mode": "palette-classic"
                    }
                }
            },
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
        }
    
    def _create_security_panel(self) -> Dict[str, Any]:
        """创建安全监控面板"""
        return {
            "id": 3,
            "title": "安全漏洞统计",
            "type": "piechart",
            "targets": [
                {
                    "expr": "sum(security_vulnerabilities_total) by (severity)",
                    "legendFormat": "{{severity}}"
                }
            ],
            "fieldConfig": {
                "defaults": {
                    "color": {
                        "mode": "palette-classic"
                    }
                }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
        }
    
    def _create_quality_panel(self) -> Dict[str, Any]:
        """创建质量监控面板"""
        return {
            "id": 4,
            "title": "代码质量指标",
            "type": "gauge",
            "targets": [
                {
                    "expr": "code_coverage_percent",
                    "legendFormat": "Code Coverage"
                },
                {
                    "expr": "(1 - technical_debt_ratio) * 100",
                    "legendFormat": "Code Quality"
                }
            ],
            "fieldConfig": {
                "defaults": {
                    "min": 0,
                    "max": 100,
                    "unit": "percent",
                    "thresholds": {
                        "steps": [
                            {"color": "red", "value": 0},
                            {"color": "yellow", "value": 70},
                            {"color": "green", "value": 85}
                        ]
                    }
                }
            },
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
        }
    
    async def send_alerts(self):
        """发送告警"""
        alerts = self._check_alert_conditions()
        
        for alert in alerts:
            await self._send_slack_alert(alert)
            await self._send_email_alert(alert)
            await self._send_webhook_alert(alert)
    
    def _check_alert_conditions(self) -> List[Dict[str, Any]]:
        """检查告警条件"""
        alerts = []
        
        # 检查测试失败率
        if self.test_results:
            failed_tests = len([t for t in self.test_results if t.status == 'failed'])
            total_tests = len(self.test_results)
            failure_rate = failed_tests / total_tests if total_tests > 0 else 0
            
            threshold = self.config.alert_thresholds.get('test_failure_rate', 0.1)
            if failure_rate > threshold:
                alerts.append({
                    "type": "test_failure",
                    "severity": "high",
                    "message": f"测试失败率过高: {failure_rate:.2%} (阈值: {threshold:.2%})",
                    "details": {
                        "failed_tests": failed_tests,
                        "total_tests": total_tests,
                        "failure_rate": failure_rate
                    }
                })
        
        # 检查性能指标
        if self.performance_results:
            avg_response_time = sum(p.response_time for p in self.performance_results) / len(self.performance_results)
            threshold = self.config.alert_thresholds.get('response_time', 5.0)
            
            if avg_response_time > threshold:
                alerts.append({
                    "type": "performance_degradation",
                    "severity": "medium",
                    "message": f"平均响应时间过长: {avg_response_time:.2f}s (阈值: {threshold}s)",
                    "details": {
                        "avg_response_time": avg_response_time,
                        "threshold": threshold
                    }
                })
        
        # 检查安全漏洞
        if self.security_results:
            latest_security = self.security_results[-1]
            critical_threshold = self.config.alert_thresholds.get('critical_vulnerabilities', 0)
            
            if latest_security.critical_count > critical_threshold:
                alerts.append({
                    "type": "security_vulnerability",
                    "severity": "critical",
                    "message": f"发现严重安全漏洞: {latest_security.critical_count}个",
                    "details": {
                        "critical_count": latest_security.critical_count,
                        "high_count": latest_security.high_count,
                        "security_score": latest_security.security_score
                    }
                })
        
        # 检查代码覆盖率
        if self.quality_results:
            latest_quality = self.quality_results[-1]
            coverage_threshold = self.config.alert_thresholds.get('code_coverage', 80.0)
            
            if latest_quality.code_coverage < coverage_threshold:
                alerts.append({
                    "type": "code_coverage",
                    "severity": "medium",
                    "message": f"代码覆盖率过低: {latest_quality.code_coverage:.1f}% (阈值: {coverage_threshold}%)",
                    "details": {
                        "code_coverage": latest_quality.code_coverage,
                        "threshold": coverage_threshold
                    }
                })
        
        return alerts
    
    async def _send_slack_alert(self, alert: Dict[str, Any]):
        """发送Slack告警"""
        if not self.config.slack_webhook:
            return
        
        color_map = {
            "critical": "danger",
            "high": "warning",
            "medium": "warning",
            "low": "good"
        }
        
        payload = {
            "attachments": [
                {
                    "color": color_map.get(alert['severity'], 'warning'),
                    "title": f"Connect测试告警 - {alert['type']}",
                    "text": alert['message'],
                    "fields": [
                        {
                            "title": "严重程度",
                            "value": alert['severity'],
                            "short": True
                        },
                        {
                            "title": "时间",
                            "value": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            "short": True
                        }
                    ],
                    "footer": "Connect监控系统",
                    "ts": int(time.time())
                }
            ]
        }
        
        try:
            async with self.session.post(
                self.config.slack_webhook,
                json=payload
            ) as response:
                if response.status == 200:
                    logger.info("Slack告警发送成功")
                else:
                    logger.error(f"Slack告警发送失败: {response.status}")
        
        except Exception as e:
            logger.error(f"发送Slack告警异常: {e}")
    
    async def _send_email_alert(self, alert: Dict[str, Any]):
        """发送邮件告警"""
        if not self.config.email_config:
            return
        
        # 这里可以集成SMTP邮件发送
        # 为了简化，这里只记录日志
        logger.info(f"邮件告警: {alert['message']}")
    
    async def _send_webhook_alert(self, alert: Dict[str, Any]):
        """发送Webhook告警"""
        for webhook_url in self.config.custom_webhooks:
            try:
                payload = {
                    "timestamp": datetime.now().isoformat(),
                    "alert": alert
                }
                
                async with self.session.post(
                    webhook_url,
                    json=payload
                ) as response:
                    if response.status == 200:
                        logger.info(f"Webhook告警发送成功: {webhook_url}")
                    else:
                        logger.error(f"Webhook告警发送失败: {webhook_url} - {response.status}")
            
            except Exception as e:
                logger.error(f"发送Webhook告警异常: {webhook_url} - {e}")
    
    async def generate_daily_report(self) -> Dict[str, Any]:
        """生成日报"""
        end_time = datetime.now()
        start_time = end_time - timedelta(days=1)
        
        # 过滤最近24小时的数据
        recent_tests = [t for t in self.test_results if t.timestamp >= start_time]
        recent_performance = [p for p in self.performance_results if p.timestamp >= start_time]
        recent_security = [s for s in self.security_results if s.timestamp >= start_time]
        recent_quality = [q for q in self.quality_results if q.timestamp >= start_time]
        
        report = {
            "report_date": end_time.strftime('%Y-%m-%d'),
            "period": f"{start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%Y-%m-%d %H:%M')}",
            "summary": {
                "total_tests": len(recent_tests),
                "passed_tests": len([t for t in recent_tests if t.status == 'passed']),
                "failed_tests": len([t for t in recent_tests if t.status == 'failed']),
                "test_success_rate": 0
            },
            "performance": {
                "avg_response_time": 0,
                "max_response_time": 0,
                "avg_throughput": 0,
                "avg_memory_usage": 0,
                "avg_cpu_usage": 0
            },
            "security": {
                "scans_performed": len(recent_security),
                "total_vulnerabilities": 0,
                "critical_vulnerabilities": 0,
                "avg_security_score": 0
            },
            "quality": {
                "avg_code_coverage": 0,
                "avg_technical_debt": 0,
                "quality_trend": "stable"
            }
        }
        
        # 计算测试统计
        if recent_tests:
            report["summary"]["test_success_rate"] = (
                report["summary"]["passed_tests"] / report["summary"]["total_tests"] * 100
            )
        
        # 计算性能统计
        if recent_performance:
            response_times = [p.response_time for p in recent_performance]
            report["performance"]["avg_response_time"] = sum(response_times) / len(response_times)
            report["performance"]["max_response_time"] = max(response_times)
            report["performance"]["avg_throughput"] = sum(p.throughput for p in recent_performance) / len(recent_performance)
            report["performance"]["avg_memory_usage"] = sum(p.memory_usage for p in recent_performance) / len(recent_performance)
            report["performance"]["avg_cpu_usage"] = sum(p.cpu_usage for p in recent_performance) / len(recent_performance)
        
        # 计算安全统计
        if recent_security:
            report["security"]["total_vulnerabilities"] = sum(s.vulnerabilities_found for s in recent_security)
            report["security"]["critical_vulnerabilities"] = sum(s.critical_count for s in recent_security)
            report["security"]["avg_security_score"] = sum(s.security_score for s in recent_security) / len(recent_security)
        
        # 计算质量统计
        if recent_quality:
            report["quality"]["avg_code_coverage"] = sum(q.code_coverage for q in recent_quality) / len(recent_quality)
            report["quality"]["avg_technical_debt"] = sum(q.technical_debt_ratio for q in recent_quality) / len(recent_quality)
        
        return report
    
    async def save_report(self, report: Dict[str, Any], output_file: str = None):
        """保存报告"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d')
            output_file = f"monitoring_report_{timestamp}.json"
        
        async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(report, indent=2, ensure_ascii=False, default=str))
        
        logger.info(f"监控报告已保存到: {output_file}")
        return output_file


# ==================== 集成函数 ====================

def create_monitoring_config() -> MonitoringConfig:
    """创建监控配置"""
    return MonitoringConfig(
        prometheus_gateway=os.getenv('PROMETHEUS_GATEWAY', 'http://localhost:9091'),
        grafana_url=os.getenv('GRAFANA_URL', 'http://localhost:3000'),
        grafana_api_key=os.getenv('GRAFANA_API_KEY'),
        slack_webhook=os.getenv('SLACK_WEBHOOK'),
        email_config={
            'smtp_server': os.getenv('SMTP_SERVER'),
            'smtp_port': os.getenv('SMTP_PORT', '587'),
            'username': os.getenv('SMTP_USERNAME'),
            'password': os.getenv('SMTP_PASSWORD'),
            'from_email': os.getenv('FROM_EMAIL'),
            'to_emails': os.getenv('TO_EMAILS', '').split(',')
        } if os.getenv('SMTP_SERVER') else None,
        custom_webhooks=os.getenv('CUSTOM_WEBHOOKS', '').split(',') if os.getenv('CUSTOM_WEBHOOKS') else [],
        alert_thresholds={
            'test_failure_rate': float(os.getenv('ALERT_TEST_FAILURE_RATE', '0.1')),
            'response_time': float(os.getenv('ALERT_RESPONSE_TIME', '5.0')),
            'critical_vulnerabilities': int(os.getenv('ALERT_CRITICAL_VULNS', '0')),
            'code_coverage': float(os.getenv('ALERT_CODE_COVERAGE', '80.0'))
        }
    )


async def integrate_test_results(test_results_file: str, monitoring: MonitoringIntegration):
    """集成测试结果"""
    try:
        async with aiofiles.open(test_results_file, 'r', encoding='utf-8') as f:
            content = await f.read()
            results = json.loads(content)
        
        for result in results.get('tests', []):
            metrics = TestMetrics(
                test_suite=result.get('suite', 'unknown'),
                test_name=result.get('name', 'unknown'),
                status=result.get('status', 'unknown'),
                duration=result.get('duration', 0.0),
                timestamp=datetime.fromisoformat(result.get('timestamp', datetime.now().isoformat())),
                error_message=result.get('error'),
                tags={'environment': os.getenv('TEST_ENVIRONMENT', 'development')}
            )
            monitoring.record_test_result(metrics)
        
        logger.info(f"成功集成测试结果: {len(results.get('tests', []))}个测试")
    
    except Exception as e:
        logger.error(f"集成测试结果失败: {e}")


async def integrate_performance_results(performance_file: str, monitoring: MonitoringIntegration):
    """集成性能测试结果"""
    try:
        async with aiofiles.open(performance_file, 'r', encoding='utf-8') as f:
            content = await f.read()
            results = json.loads(content)
        
        for result in results.get('benchmarks', []):
            metrics = PerformanceMetrics(
                operation=result.get('operation', 'unknown'),
                response_time=result.get('response_time', 0.0),
                throughput=result.get('throughput', 0.0),
                memory_usage=result.get('memory_usage', 0.0),
                cpu_usage=result.get('cpu_usage', 0.0),
                error_rate=result.get('error_rate', 0.0),
                timestamp=datetime.fromisoformat(result.get('timestamp', datetime.now().isoformat())),
                tags={'environment': os.getenv('TEST_ENVIRONMENT', 'development')}
            )
            monitoring.record_performance_metrics(metrics)
        
        logger.info(f"成功集成性能结果: {len(results.get('benchmarks', []))}个基准测试")
    
    except Exception as e:
        logger.error(f"集成性能结果失败: {e}")


async def integrate_security_results(security_file: str, monitoring: MonitoringIntegration):
    """集成安全扫描结果"""
    try:
        async with aiofiles.open(security_file, 'r', encoding='utf-8') as f:
            content = await f.read()
            results = json.loads(content)
        
        metrics = SecurityMetrics(
            scan_type=results.get('scan_type', 'unknown'),
            vulnerabilities_found=results.get('vulnerabilities_found', 0),
            critical_count=results.get('critical_count', 0),
            high_count=results.get('high_count', 0),
            medium_count=results.get('medium_count', 0),
            low_count=results.get('low_count', 0),
            security_score=results.get('overall_score', 0.0),
            timestamp=datetime.fromisoformat(results.get('timestamp', datetime.now().isoformat())),
            tags={'environment': os.getenv('TEST_ENVIRONMENT', 'development')}
        )
        monitoring.record_security_metrics(metrics)
        
        logger.info(f"成功集成安全扫描结果: 发现{metrics.vulnerabilities_found}个漏洞")
    
    except Exception as e:
        logger.error(f"集成安全结果失败: {e}")


if __name__ == "__main__":
    async def main():
        # 创建监控配置
        config = create_monitoring_config()
        
        # 初始化监控集成
        async with MonitoringIntegration(config) as monitoring:
            # 模拟一些测试数据
            test_metrics = TestMetrics(
                test_suite="unit_tests",
                test_name="test_data_import",
                status="passed",
                duration=2.5,
                timestamp=datetime.now(),
                tags={'environment': 'development'}
            )
            monitoring.record_test_result(test_metrics)
            
            performance_metrics = PerformanceMetrics(
                operation="data_import",
                response_time=1.2,
                throughput=1000.0,
                memory_usage=512 * 1024 * 1024,  # 512MB
                cpu_usage=45.0,
                error_rate=0.01,
                timestamp=datetime.now(),
                tags={'environment': 'development'}
            )
            monitoring.record_performance_metrics(performance_metrics)
            
            security_metrics = SecurityMetrics(
                scan_type="web_security",
                vulnerabilities_found=3,
                critical_count=0,
                high_count=1,
                medium_count=2,
                low_count=0,
                security_score=85.0,
                timestamp=datetime.now(),
                tags={'environment': 'development'}
            )
            monitoring.record_security_metrics(security_metrics)
            
            quality_metrics = QualityMetrics(
                code_coverage=87.5,
                test_coverage=92.0,
                complexity_score=6.2,
                maintainability_index=78.0,
                technical_debt_ratio=0.15,
                bug_density=0.02,
                timestamp=datetime.now(),
                tags={'environment': 'development'}
            )
            monitoring.record_quality_metrics(quality_metrics)
            
            # 推送指标
            await monitoring.push_metrics_to_prometheus()
            
            # 创建仪表板
            await monitoring.create_grafana_dashboard()
            
            # 检查告警
            await monitoring.send_alerts()
            
            # 生成日报
            report = await monitoring.generate_daily_report()
            await monitoring.save_report(report)
            
            print("监控集成测试完成")
            print(f"测试结果: {len(monitoring.test_results)}个")
            print(f"性能指标: {len(monitoring.performance_results)}个")
            print(f"安全指标: {len(monitoring.security_results)}个")
            print(f"质量指标: {len(monitoring.quality_results)}个")
    
    # 运行测试
    asyncio.run(main())