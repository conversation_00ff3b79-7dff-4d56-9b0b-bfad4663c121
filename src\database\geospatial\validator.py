__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Geospatial validation module.

This module provides validation capabilities for geospatial data including
coordinate validation, geometry validation, and spatial data quality checks.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import geopandas as gpd
import pandas as pd
from shapely.geometry import Point, Polygon, LineString, MultiPoint, MultiPolygon, MultiLineString
from shapely.geometry.base import BaseGeometry
from shapely import validation

logger = logging.getLogger(__name__)


class CoordinateValidator:
    """Validator for geographic coordinates."""
    
    @staticmethod
    def validate_latitude(lat: float) -> bool:
        """Validate latitude coordinate.
        
        Args:
            lat: Latitude value to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(lat, (int, float)):
            return False
        return -90.0 <= lat <= 90.0
    
    @staticmethod
    def validate_longitude(lon: float) -> bool:
        """Validate longitude coordinate.
        
        Args:
            lon: Longitude value to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(lon, (int, float)):
            return False
        return -180.0 <= lon <= 180.0
    
    @staticmethod
    def validate_coordinates(lat: float, lon: float) -> bool:
        """Validate latitude and longitude coordinates.
        
        Args:
            lat: Latitude value
            lon: Longitude value
            
        Returns:
            bool: True if both coordinates are valid, False otherwise
        """
        return (CoordinateValidator.validate_latitude(lat) and 
                CoordinateValidator.validate_longitude(lon))
    
    @staticmethod
    def validate_coordinate_pair(coords: Tuple[float, float]) -> bool:
        """Validate a coordinate pair (lon, lat).
        
        Args:
            coords: Tuple of (longitude, latitude)
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(coords, (tuple, list)) or len(coords) != 2:
            return False
        
        lon, lat = coords
        return CoordinateValidator.validate_coordinates(lat, lon)
    
    @staticmethod
    def validate_coordinate_list(coords_list: List[Tuple[float, float]]) -> bool:
        """Validate a list of coordinate pairs.
        
        Args:
            coords_list: List of coordinate tuples
            
        Returns:
            bool: True if all coordinates are valid, False otherwise
        """
        if not isinstance(coords_list, list) or len(coords_list) == 0:
            return False
        
        return all(CoordinateValidator.validate_coordinate_pair(coords) 
                  for coords in coords_list)


class GeometryValidator:
    """Validator for geospatial geometries."""

    def __init__(self):
        """Initialize the geometry validator."""
        self.custom_rules = {}
    
    def validate_geometry(self, geometry):
        """Validate a geometry object.
        
        Args:
            geometry: Shapely geometry object to validate
            
        Returns:
            tuple: (is_valid, errors) where is_valid is bool and errors is list
        """
        errors = []
        
        if geometry is None:
            return False, ["Geometry is None"]
        
        # Check if geometry is empty
        if hasattr(geometry, 'is_empty') and geometry.is_empty:
            errors.append("Empty geometry")
        
        # Check if geometry has is_valid attribute (Shapely geometries)
        if hasattr(geometry, 'is_valid'):
            if not geometry.is_valid:
                errors.append("Invalid geometry")
        
        # Validate coordinate ranges
        coord_valid, coord_errors = self.validate_coordinate_range(geometry)
        if not coord_valid:
            errors.extend(coord_errors)
        
        # Run custom validation rules
        for rule_name, rule_func in self.custom_rules.items():
            try:
                rule_valid, rule_errors = rule_func(geometry)
                if not rule_valid:
                    errors.extend(rule_errors)
            except Exception as e:
                errors.append(f"Custom rule '{rule_name}' failed: {str(e)}")
        
        # Return overall result
        is_valid = len(errors) == 0
        return is_valid, errors
    
    def validate_coordinate_range(self, geometry):
        """Validate coordinate ranges for a geometry.
        
        Args:
            geometry: The geometry object to validate
            
        Returns:
            tuple: (is_valid, errors) where is_valid is bool and errors is list
        """
        try:
            errors = []
            
            # Check if geometry is empty or None
            if geometry is None or (hasattr(geometry, 'is_empty') and geometry.is_empty):
                return True, []
            
            # Use bounds for all geometries - this is more reliable
            if hasattr(geometry, 'bounds'):
                try:
                    bounds = geometry.bounds
                    if bounds:  # bounds is not empty
                        min_lon, min_lat, max_lon, max_lat = bounds
                        
                        if min_lon < -180 or max_lon > 180:
                            errors.append(f"Longitude range [{min_lon}, {max_lon}] is out of valid range [-180, 180]")
                        if min_lat < -90 or max_lat > 90:
                            errors.append(f"Latitude range [{min_lat}, {max_lat}] is out of valid range [-90, 90]")
                except Exception:
                    # If bounds fails, try coords for Point geometries
                    if hasattr(geometry, 'coords'):
                        coords = list(geometry.coords)[0]
                        lon, lat = coords[0], coords[1]
                        
                        if lon < -180 or lon > 180:
                            errors.append(f"Longitude {lon} is out of valid range [-180, 180]")
                        if lat < -90 or lat > 90:
                            errors.append(f"Latitude {lat} is out of valid range [-90, 90]")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            return False, [f"Coordinate validation error: {str(e)}"]
    
    def validate_complexity(self, geometry, max_vertices=1000):
        """Validate geometry complexity.
        
        Args:
            geometry: The geometry object to validate
            max_vertices: Maximum allowed vertices
            
        Returns:
            tuple: (is_valid, errors) where is_valid is bool and errors is list
        """
        try:
            errors = []
            
            if hasattr(geometry, 'exterior'):
                # Polygon
                vertex_count = len(list(geometry.exterior.coords))
                if vertex_count > max_vertices:
                    errors.append(f"Polygon has {vertex_count} vertices, exceeding maximum of {max_vertices}")
            
            elif hasattr(geometry, 'coords'):
                # LineString or Point
                vertex_count = len(list(geometry.coords))
                if vertex_count > max_vertices:
                    errors.append(f"Geometry has {vertex_count} vertices, exceeding maximum of {max_vertices}")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            return False, [f"Complexity validation error: {str(e)}"]
    
    def validate_area(self, geometry, min_area=0.0, max_area=float('inf')):
        """Validate geometry area.
        
        Args:
            geometry: The geometry object to validate
            min_area: Minimum allowed area
            max_area: Maximum allowed area
            
        Returns:
            tuple: (is_valid, errors) where is_valid is bool and errors is list
        """
        try:
            errors = []
            
            if hasattr(geometry, 'area'):
                area = geometry.area
                
                if area < min_area:
                    errors.append(f"Geometry area {area} is below minimum of {min_area}")
                if area > max_area:
                    errors.append(f"Geometry area {area} exceeds maximum of {max_area}")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            return False, [f"Area validation error: {str(e)}"]
    
    def validate_topology(self, geometry):
        """Validate geometry topology.
        
        Args:
            geometry: The geometry object to validate
            
        Returns:
            tuple: (is_valid, errors) where is_valid is bool and errors is list
        """
        try:
            errors = []
            
            if hasattr(geometry, 'is_valid'):
                if not geometry.is_valid:
                    errors.append("Geometry has invalid topology")
                    
                    # Check for self-intersection
                    if hasattr(geometry, 'exterior') and hasattr(geometry.exterior, 'is_simple'):
                        if not geometry.exterior.is_simple:
                            errors.append("Geometry has self-intersecting exterior")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            return False, [f"Topology validation error: {str(e)}"]
    
    def validate_batch(self, geometries):
        """Validate a batch of geometries.
        
        Args:
            geometries: List of geometries to validate
            
        Returns:
            List of validation results as dictionaries
        """
        results = []
        for geometry in geometries:
            is_valid, errors = self.validate_geometry(geometry)
            results.append({
                "is_valid": is_valid,
                "errors": errors
            })
        return results
    
    def add_custom_rule(self, name, rule_func):
        """Add a custom validation rule.
        
        Args:
            name: Name of the custom rule
            rule_func: Function that takes a geometry and returns (is_valid, errors)
        """
        self.custom_rules[name] = rule_func
    
    @staticmethod
    def is_valid_geometry(geometry: BaseGeometry) -> bool:
        """Check if geometry is valid.
        
        Args:
            geometry: Shapely geometry object
            
        Returns:
            bool: True if geometry is valid, False otherwise
        """
        if geometry is None:
            return False
        
        if not isinstance(geometry, BaseGeometry):
            return False
        
        return geometry.is_valid
    
    @staticmethod
    def validate_point(point: Point) -> bool:
        """Validate a Point geometry.
        
        Args:
            point: Point geometry to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(point, Point):
            return False
        
        if point.is_empty:
            return False
        
        # Validate coordinates
        x, y = point.x, point.y
        return CoordinateValidator.validate_coordinates(y, x)
    
    @staticmethod
    def validate_polygon(polygon: Polygon) -> bool:
        """Validate a Polygon geometry.
        
        Args:
            polygon: Polygon geometry to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(polygon, Polygon):
            return False
        
        if polygon.is_empty:
            return False
        
        # Check if polygon is valid
        if not polygon.is_valid:
            return False
        
        # Check exterior ring
        if polygon.exterior is None:
            return False
        
        # Validate exterior coordinates
        exterior_coords = list(polygon.exterior.coords)
        if len(exterior_coords) < 4:  # Minimum for a closed polygon
            return False
        
        # Check if first and last coordinates are the same (closed)
        if exterior_coords[0] != exterior_coords[-1]:
            return False
        
        # Validate all coordinates
        for lon, lat in exterior_coords:
            if not CoordinateValidator.validate_coordinates(lat, lon):
                return False
        
        return True
    
    @staticmethod
    def validate_linestring(linestring: LineString) -> bool:
        """Validate a LineString geometry.
        
        Args:
            linestring: LineString geometry to validate
            
        Returns:
            bool: True if valid, False otherwise
        """
        if not isinstance(linestring, LineString):
            return False
        
        if linestring.is_empty:
            return False
        
        if not linestring.is_valid:
            return False
        
        # LineString must have at least 2 points
        coords = list(linestring.coords)
        if len(coords) < 2:
            return False
        
        # Validate all coordinates
        for lon, lat in coords:
            if not CoordinateValidator.validate_coordinates(lat, lon):
                return False
        
        return True
    

    
    @staticmethod
    def get_validation_errors(geometry: BaseGeometry) -> List[str]:
        """Get detailed validation errors for a geometry.
        
        Args:
            geometry: Geometry to validate
            
        Returns:
            List of validation error messages
        """
        errors = []
        
        if geometry is None:
            errors.append("Geometry is None")
            return errors
        
        if not isinstance(geometry, BaseGeometry):
            errors.append(f"Object is not a valid geometry type: {type(geometry)}")
            return errors
        
        if geometry.is_empty:
            errors.append("Geometry is empty")
        
        if not geometry.is_valid:
            try:
                # Try to get detailed validation error
                explain = validation.explain_validity(geometry)
                errors.append(f"Invalid geometry: {explain}")
            except Exception:
                errors.append("Geometry is invalid")
        
        # Type-specific validation
        if isinstance(geometry, Point):
            if not geometry.is_empty:
                x, y = geometry.x, geometry.y
                if not CoordinateValidator.validate_coordinates(y, x):
                    errors.append(f"Invalid coordinates: lat={y}, lon={x}")
        
        elif isinstance(geometry, Polygon):
            if not geometry.is_empty and geometry.exterior is not None:
                exterior_coords = list(geometry.exterior.coords)
                if len(exterior_coords) < 4:
                    errors.append("Polygon has fewer than 4 coordinates")
                elif exterior_coords[0] != exterior_coords[-1]:
                    errors.append("Polygon is not closed")
                else:
                    for i, (lon, lat) in enumerate(exterior_coords):
                        if not CoordinateValidator.validate_coordinates(lat, lon):
                            errors.append(f"Invalid coordinate at position {i}: lat={lat}, lon={lon}")
        
        elif isinstance(geometry, LineString):
            if not geometry.is_empty:
                coords = list(geometry.coords)
                if len(coords) < 2:
                    errors.append("LineString has fewer than 2 coordinates")
                else:
                    for i, (lon, lat) in enumerate(coords):
                        if not CoordinateValidator.validate_coordinates(lat, lon):
                            errors.append(f"Invalid coordinate at position {i}: lat={lat}, lon={lon}")
        
        return errors
    
    @staticmethod
    def validate_geodataframe(gdf: gpd.GeoDataFrame) -> Dict[str, Any]:
        """Validate all geometries in a GeoDataFrame.
        
        Args:
            gdf: GeoDataFrame to validate
            
        Returns:
            Dictionary with validation results
        """
        if not isinstance(gdf, gpd.GeoDataFrame):
            return {
                'valid': False,
                'error': 'Input is not a GeoDataFrame',
                'total_geometries': 0,
                'valid_geometries': 0,
                'invalid_geometries': 0,
                'validation_errors': []
            }
        
        total = len(gdf)
        valid_count = 0
        invalid_count = 0
        validation_errors = []
        
        for idx, row in gdf.iterrows():
            geometry = row.geometry
            if GeometryValidator.validate_geometry(geometry):
                valid_count += 1
            else:
                invalid_count += 1
                errors = GeometryValidator.get_validation_errors(geometry)
                validation_errors.append({
                    'index': idx,
                    'errors': errors
                })
        
        return {
            'valid': invalid_count == 0,
            'total_geometries': total,
            'valid_geometries': valid_count,
            'invalid_geometries': invalid_count,
            'validation_errors': validation_errors
        }