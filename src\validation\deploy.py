"""统一验证框架部署脚本

提供自动化部署、环境检查、依赖安装和配置管理功能。
专为Connect系统的生产环境部署优化。
"""

import os
import sys
import json
import shutil
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class DeploymentConfig:
    """部署配置"""
    environment: str = 'production'  # development, testing, production
    install_dependencies: bool = True
    run_tests: bool = True
    backup_existing: bool = True
    create_config: bool = True
    setup_monitoring: bool = True
    setup_logging: bool = True
    validate_installation: bool = True
    
    # 路径配置
    source_path: str = ''
    target_path: str = ''
    backup_path: str = ''
    config_path: str = ''
    log_path: str = ''
    
    # 性能配置
    max_workers: int = 4
    memory_limit_gb: int = 8
    enable_parallel: bool = True
    
    # 监控配置
    monitoring_enabled: bool = True
    metrics_retention_days: int = 30
    alert_email: str = ''
    
    # 安全配置
    enable_ssl: bool = True
    data_encryption: bool = True
    access_control: bool = True


class DeploymentValidator:
    """部署验证器"""
    
    def __init__(self, config: DeploymentConfig):
        self.config = config
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('validation_deployment')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def validate_environment(self) -> Tuple[bool, List[str]]:
        """验证部署环境"""
        issues = []
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            issues.append(f"Python版本过低: {sys.version}, 需要3.8+")
        
        # 检查必要的系统包
        required_packages = ['pandas', 'numpy', 'psycopg2-binary', 'redis']
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                issues.append(f"缺少必要包: {package}")
        
        # 检查磁盘空间
        if self.config.target_path:
            try:
                stat = shutil.disk_usage(self.config.target_path)
                free_gb = stat.free / (1024**3)
                if free_gb < 5:
                    issues.append(f"磁盘空间不足: {free_gb:.1f}GB, 需要至少5GB")
            except Exception as e:
                issues.append(f"无法检查磁盘空间: {e}")
        
        # 检查内存
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            if memory_gb < self.config.memory_limit_gb:
                issues.append(f"内存不足: {memory_gb:.1f}GB, 推荐{self.config.memory_limit_gb}GB+")
        except ImportError:
            self.logger.warning("无法检查内存使用情况 (psutil未安装)")
        
        # 检查数据库连接
        if self.config.environment == 'production':
            db_issues = self._validate_database_connection()
            issues.extend(db_issues)
        
        return len(issues) == 0, issues
    
    def _validate_database_connection(self) -> List[str]:
        """验证数据库连接"""
        issues = []
        
        try:
            import psycopg2
            # 这里应该使用实际的数据库配置
            # conn = psycopg2.connect(
            #     host='localhost',
            #     database='connect',
            #     user='connect_user',
            #     password='password'
            # )
            # conn.close()
            self.logger.info("数据库连接检查跳过 (需要配置实际连接参数)")
        except Exception as e:
            issues.append(f"数据库连接失败: {e}")
        
        return issues
    
    def validate_installation(self, installation_path: str) -> Tuple[bool, List[str]]:
        """验证安装结果"""
        issues = []
        
        # 检查核心文件
        core_files = [
            '__init__.py',
            'core.py',
            'validators.py',
            'rules.py',
            'factory.py',
            'config.py',
            'monitoring.py',
            'utils.py',
            'exceptions.py'
        ]
        
        for file in core_files:
            file_path = Path(installation_path) / file
            if not file_path.exists():
                issues.append(f"缺少核心文件: {file}")
        
        # 检查测试文件
        test_dir = Path(installation_path) / 'tests'
        if not test_dir.exists():
            issues.append("缺少测试目录")
        
        # 尝试导入验证框架
        try:
            sys.path.insert(0, str(Path(installation_path).parent))
            import validation
            
            # 检查版本信息
            version_info = validation.get_version_info()
            self.logger.info(f"验证框架版本: {version_info['version']}")
            
            # 运行健康检查
            health_status = validation.health_check()
            if health_status['status'] != 'healthy':
                issues.append(f"健康检查失败: {health_status.get('error', '未知错误')}")
            
        except Exception as e:
            issues.append(f"无法导入验证框架: {e}")
        finally:
            if str(Path(installation_path).parent) in sys.path:
                sys.path.remove(str(Path(installation_path).parent))
        
        return len(issues) == 0, issues


class DeploymentManager:
    """部署管理器"""
    
    def __init__(self, config: DeploymentConfig):
        self.config = config
        self.validator = DeploymentValidator(config)
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('validation_deployment_manager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def deploy(self) -> bool:
        """执行完整部署流程"""
        self.logger.info("开始部署统一验证框架...")
        
        try:
            # 1. 环境验证
            if not self._validate_environment():
                return False
            
            # 2. 备份现有安装
            if self.config.backup_existing:
                if not self._backup_existing_installation():
                    return False
            
            # 3. 安装依赖
            if self.config.install_dependencies:
                if not self._install_dependencies():
                    return False
            
            # 4. 复制文件
            if not self._copy_files():
                return False
            
            # 5. 创建配置
            if self.config.create_config:
                if not self._create_configuration():
                    return False
            
            # 6. 设置监控
            if self.config.setup_monitoring:
                if not self._setup_monitoring():
                    return False
            
            # 7. 设置日志
            if self.config.setup_logging:
                if not self._setup_logging():
                    return False
            
            # 8. 运行测试
            if self.config.run_tests:
                if not self._run_tests():
                    return False
            
            # 9. 验证安装
            if self.config.validate_installation:
                if not self._validate_installation():
                    return False
            
            self.logger.info("部署完成!")
            return True
            
        except Exception as e:
            self.logger.error(f"部署失败: {e}")
            return False
    
    def _validate_environment(self) -> bool:
        """验证环境"""
        self.logger.info("验证部署环境...")
        
        is_valid, issues = self.validator.validate_environment()
        
        if not is_valid:
            self.logger.error("环境验证失败:")
            for issue in issues:
                self.logger.error(f"  - {issue}")
            return False
        
        self.logger.info("环境验证通过")
        return True
    
    def _backup_existing_installation(self) -> bool:
        """备份现有安装"""
        if not self.config.target_path or not Path(self.config.target_path).exists():
            self.logger.info("无需备份 (目标路径不存在)")
            return True
        
        self.logger.info("备份现有安装...")
        
        try:
            backup_path = self.config.backup_path or f"{self.config.target_path}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copytree(self.config.target_path, backup_path)
            self.logger.info(f"备份完成: {backup_path}")
            return True
        except Exception as e:
            self.logger.error(f"备份失败: {e}")
            return False
    
    def _install_dependencies(self) -> bool:
        """安装依赖"""
        self.logger.info("安装依赖包...")
        
        requirements = [
            'pandas>=1.3.0',
            'numpy>=1.21.0',
            'psycopg2-binary>=2.9.0',
            'redis>=4.0.0',
            'pydantic>=1.8.0',
            'fastapi>=0.70.0',
            'uvicorn>=0.15.0',
            'prometheus-client>=0.12.0',
            'psutil>=5.8.0'
        ]
        
        try:
            for requirement in requirements:
                self.logger.info(f"安装: {requirement}")
                result = subprocess.run(
                    [sys.executable, '-m', 'pip', 'install', requirement],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode != 0:
                    self.logger.error(f"安装失败: {requirement}")
                    self.logger.error(result.stderr)
                    return False
            
            self.logger.info("依赖安装完成")
            return True
            
        except Exception as e:
            self.logger.error(f"依赖安装失败: {e}")
            return False
    
    def _copy_files(self) -> bool:
        """复制文件"""
        self.logger.info("复制验证框架文件...")
        
        try:
            source_path = Path(self.config.source_path)
            target_path = Path(self.config.target_path)
            
            # 创建目标目录
            target_path.mkdir(parents=True, exist_ok=True)
            
            # 复制Python文件
            python_files = list(source_path.glob('*.py'))
            for file in python_files:
                shutil.copy2(file, target_path / file.name)
                self.logger.debug(f"复制: {file.name}")
            
            # 复制测试目录
            tests_source = source_path / 'tests'
            if tests_source.exists():
                tests_target = target_path / 'tests'
                if tests_target.exists():
                    shutil.rmtree(tests_target)
                shutil.copytree(tests_source, tests_target)
                self.logger.debug("复制: tests/")
            
            # 复制文档
            docs = ['README.md', 'migration_guide.md']
            for doc in docs:
                doc_file = source_path / doc
                if doc_file.exists():
                    shutil.copy2(doc_file, target_path / doc)
                    self.logger.debug(f"复制: {doc}")
            
            self.logger.info("文件复制完成")
            return True
            
        except Exception as e:
            self.logger.error(f"文件复制失败: {e}")
            return False
    
    def _create_configuration(self) -> bool:
        """创建配置文件"""
        self.logger.info("创建配置文件...")
        
        try:
            config_data = {
                'validation': {
                    'mode': 'strict' if self.config.environment == 'production' else 'lenient',
                    'timeout_seconds': 300,
                    'max_errors': 1000
                },
                'performance': {
                    'parallel_enabled': self.config.enable_parallel,
                    'max_workers': self.config.max_workers,
                    'chunk_size': 10000,
                    'memory_limit_gb': self.config.memory_limit_gb
                },
                'monitoring': {
                    'enabled': self.config.monitoring_enabled,
                    'metrics_retention_days': self.config.metrics_retention_days,
                    'alert_email': self.config.alert_email,
                    'performance_tracking': True
                },
                'logging': {
                    'level': 'INFO' if self.config.environment == 'production' else 'DEBUG',
                    'file_path': self.config.log_path,
                    'max_file_size_mb': 100,
                    'backup_count': 5
                },
                'security': {
                    'enable_ssl': self.config.enable_ssl,
                    'data_encryption': self.config.data_encryption,
                    'access_control': self.config.access_control
                },
                'telecom': {
                    'country_code': 'CN',
                    'operator_codes': ['460-00', '460-01', '460-02'],
                    'frequency_bands': ['900', '1800', '2100', '2600']
                }
            }
            
            config_file = Path(self.config.config_path) / 'validation_config.json'
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置文件创建完成: {config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"配置文件创建失败: {e}")
            return False
    
    def _setup_monitoring(self) -> bool:
        """设置监控"""
        self.logger.info("设置监控系统...")
        
        try:
            # 创建监控目录
            monitoring_dir = Path(self.config.target_path) / 'monitoring'
            monitoring_dir.mkdir(exist_ok=True)
            
            # 创建Prometheus配置
            prometheus_config = {
                'global': {
                    'scrape_interval': '15s'
                },
                'scrape_configs': [{
                    'job_name': 'validation-framework',
                    'static_configs': [{
                        'targets': ['localhost:8000']
                    }]
                }]
            }
            
            with open(monitoring_dir / 'prometheus.yml', 'w') as f:
                import yaml
                yaml.dump(prometheus_config, f)
            
            self.logger.info("监控设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"监控设置失败: {e}")
            return False
    
    def _setup_logging(self) -> bool:
        """设置日志"""
        self.logger.info("设置日志系统...")
        
        try:
            log_dir = Path(self.config.log_path)
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建日志配置文件
            log_config = {
                'version': 1,
                'disable_existing_loggers': False,
                'formatters': {
                    'standard': {
                        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                    }
                },
                'handlers': {
                    'file': {
                        'level': 'INFO',
                        'class': 'logging.handlers.RotatingFileHandler',
                        'filename': str(log_dir / 'validation.log'),
                        'maxBytes': 100 * 1024 * 1024,  # 100MB
                        'backupCount': 5,
                        'formatter': 'standard'
                    },
                    'console': {
                        'level': 'INFO',
                        'class': 'logging.StreamHandler',
                        'formatter': 'standard'
                    }
                },
                'loggers': {
                    'validation': {
                        'handlers': ['file', 'console'],
                        'level': 'INFO',
                        'propagate': False
                    }
                }
            }
            
            with open(log_dir / 'logging_config.json', 'w') as f:
                json.dump(log_config, f, indent=2)
            
            self.logger.info("日志设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"日志设置失败: {e}")
            return False
    
    def _run_tests(self) -> bool:
        """运行测试"""
        self.logger.info("运行测试套件...")
        
        try:
            test_dir = Path(self.config.target_path) / 'tests'
            if not test_dir.exists():
                self.logger.warning("测试目录不存在，跳过测试")
                return True
            
            # 运行pytest
            result = subprocess.run(
                [sys.executable, '-m', 'pytest', str(test_dir), '-v'],
                capture_output=True,
                text=True,
                cwd=self.config.target_path
            )
            
            if result.returncode == 0:
                self.logger.info("所有测试通过")
                return True
            else:
                self.logger.error("测试失败:")
                self.logger.error(result.stdout)
                self.logger.error(result.stderr)
                return False
                
        except Exception as e:
            self.logger.error(f"测试运行失败: {e}")
            return False
    
    def _validate_installation(self) -> bool:
        """验证安装"""
        self.logger.info("验证安装结果...")
        
        is_valid, issues = self.validator.validate_installation(self.config.target_path)
        
        if not is_valid:
            self.logger.error("安装验证失败:")
            for issue in issues:
                self.logger.error(f"  - {issue}")
            return False
        
        self.logger.info("安装验证通过")
        return True


def create_deployment_config(
    environment: str = 'production',
    source_path: str = '',
    target_path: str = '',
    **kwargs
) -> DeploymentConfig:
    """创建部署配置"""
    config = DeploymentConfig(
        environment=environment,
        source_path=source_path or os.path.dirname(__file__),
        target_path=target_path or f'/opt/connect/validation',
        backup_path=kwargs.get('backup_path', ''),
        config_path=kwargs.get('config_path', '/etc/connect'),
        log_path=kwargs.get('log_path', '/var/log/connect')
    )
    
    # 更新其他配置
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
    
    return config


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='统一验证框架部署脚本')
    parser.add_argument('--environment', choices=['development', 'testing', 'production'], 
                       default='production', help='部署环境')
    parser.add_argument('--source-path', help='源代码路径')
    parser.add_argument('--target-path', help='目标安装路径')
    parser.add_argument('--config-file', help='配置文件路径')
    parser.add_argument('--skip-tests', action='store_true', help='跳过测试')
    parser.add_argument('--skip-backup', action='store_true', help='跳过备份')
    parser.add_argument('--dry-run', action='store_true', help='仅验证环境，不执行部署')
    
    args = parser.parse_args()
    
    # 创建部署配置
    config = create_deployment_config(
        environment=args.environment,
        source_path=args.source_path,
        target_path=args.target_path,
        run_tests=not args.skip_tests,
        backup_existing=not args.skip_backup
    )
    
    # 如果提供了配置文件，加载配置
    if args.config_file:
        try:
            with open(args.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                for key, value in config_data.items():
                    if hasattr(config, key):
                        setattr(config, key, value)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return False
    
    # 创建部署管理器
    manager = DeploymentManager(config)
    
    if args.dry_run:
        print("执行环境验证 (dry-run模式)...")
        is_valid, issues = manager.validator.validate_environment()
        if is_valid:
            print("✅ 环境验证通过")
            return True
        else:
            print("❌ 环境验证失败:")
            for issue in issues:
                print(f"  - {issue}")
            return False
    
    # 执行部署
    success = manager.deploy()
    
    if success:
        print("🎉 部署成功!")
        print(f"验证框架已安装到: {config.target_path}")
        print(f"配置文件位置: {config.config_path}")
        print(f"日志文件位置: {config.log_path}")
    else:
        print("❌ 部署失败")
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)