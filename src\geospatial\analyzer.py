"""Spatial Analysis Module for Telecommunications Data

This module provides spatial analysis capabilities specifically designed for
telecommunications data including coverage analysis, signal strength analysis,
and network performance spatial patterns.
"""

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

import logging
import numpy as np
import pandas as pd
import geopandas as gpd
from typing import Dict, List, Optional, Tuple, Union, Any
from shapely.geometry import Point, Polygon, MultiPolygon
from shapely.ops import unary_union
import warnings

logger = logging.getLogger(__name__)


class SpatialAnalyzer:
    """Spatial analyzer for telecommunications data"""
    
    def __init__(self, crs: str = "EPSG:4326"):
        """Initialize spatial analyzer
        
        Args:
            crs: Coordinate reference system for spatial operations
        """
        self.crs = crs
        self.logger = logging.getLogger(self.__class__.__name__)
        
    def analyze_coverage(self, 
                        data: gpd.GeoDataFrame, 
                        signal_column: str = "rsrp",
                        threshold: float = -110.0,
                        grid_size: float = 0.001) -> Dict[str, Any]:
        """Analyze network coverage based on signal strength measurements
        
        Args:
            data: GeoDataFrame with signal measurements
            signal_column: Column name containing signal strength values
            threshold: Signal strength threshold for coverage (dBm)
            grid_size: Grid size for coverage analysis (degrees)
            
        Returns:
            Dictionary containing coverage analysis results
        """
        try:
            if data.empty:
                return {"coverage_ratio": 0.0, "total_area": 0.0, "covered_area": 0.0}
            
            # Ensure data has the required column
            if signal_column not in data.columns:
                raise ValueError(f"Signal column '{signal_column}' not found in data")
            
            # Filter data above threshold
            covered_data = data[data[signal_column] >= threshold].copy()
            
            # Create coverage polygons using buffer analysis
            if not covered_data.empty:
                # Buffer points to create coverage areas
                buffer_distance = grid_size * 0.5  # Half grid size as buffer
                covered_data['coverage_area'] = covered_data.geometry.buffer(buffer_distance)
                
                # Union all coverage areas
                coverage_union = unary_union(covered_data['coverage_area'].tolist())
                covered_area = coverage_union.area if hasattr(coverage_union, 'area') else 0.0
            else:
                covered_area = 0.0
            
            # Calculate total analysis area
            total_bounds = data.total_bounds
            total_area = (total_bounds[2] - total_bounds[0]) * (total_bounds[3] - total_bounds[1])
            
            # Calculate coverage ratio
            coverage_ratio = (covered_area / total_area) * 100 if total_area > 0 else 0.0
            
            result = {
                "coverage_ratio": coverage_ratio,
                "total_area": total_area,
                "covered_area": covered_area,
                "threshold_used": threshold,
                "total_measurements": len(data),
                "covered_measurements": len(covered_data),
                "signal_statistics": {
                    "mean": data[signal_column].mean(),
                    "std": data[signal_column].std(),
                    "min": data[signal_column].min(),
                    "max": data[signal_column].max(),
                }
            }
            
            self.logger.info(f"Coverage analysis completed: {coverage_ratio:.2f}% coverage")
            return result
            
        except Exception as e:
            self.logger.error(f"Coverage analysis failed: {e}")
            raise
    
    def analyze_signal_quality(self, 
                              data: gpd.GeoDataFrame,
                              signal_column: str = "rsrp") -> Dict[str, Any]:
        """Analyze signal quality distribution and patterns
        
        Args:
            data: GeoDataFrame with signal measurements
            signal_column: Column name containing signal strength values
            
        Returns:
            Dictionary containing signal quality analysis results
        """
        try:
            if data.empty or signal_column not in data.columns:
                return {"quality_distribution": {}, "spatial_patterns": {}}
            
            # Define signal quality categories (for LTE RSRP)
            quality_bins = [-140, -110, -90, -70, -40]
            quality_labels = ['Poor', 'Fair', 'Good', 'Excellent']
            
            # Categorize signal quality
            data_copy = data.copy()
            data_copy['signal_quality'] = pd.cut(
                data_copy[signal_column], 
                bins=quality_bins, 
                labels=quality_labels,
                include_lowest=True
            )
            
            # Calculate quality distribution
            quality_counts = data_copy['signal_quality'].value_counts()
            quality_distribution = {
                str(quality): {
                    "count": int(count),
                    "percentage": (count / len(data_copy)) * 100
                }
                for quality, count in quality_counts.items()
            }
            
            # Analyze spatial patterns
            spatial_patterns = {}
            for quality in quality_labels:
                quality_data = data_copy[data_copy['signal_quality'] == quality]
                if not quality_data.empty:
                    # Calculate spatial statistics
                    bounds = quality_data.total_bounds
                    centroid = quality_data.geometry.centroid.unary_union.centroid
                    
                    spatial_patterns[quality] = {
                        "count": len(quality_data),
                        "bounds": bounds.tolist(),
                        "centroid": [centroid.x, centroid.y],
                        "signal_stats": {
                            "mean": quality_data[signal_column].mean(),
                            "std": quality_data[signal_column].std(),
                        }
                    }
            
            result = {
                "quality_distribution": quality_distribution,
                "spatial_patterns": spatial_patterns,
                "overall_statistics": {
                    "total_measurements": len(data),
                    "signal_mean": data[signal_column].mean(),
                    "signal_std": data[signal_column].std(),
                    "signal_range": [data[signal_column].min(), data[signal_column].max()],
                }
            }
            
            self.logger.info(f"Signal quality analysis completed for {len(data)} measurements")
            return result
            
        except Exception as e:
            self.logger.error(f"Signal quality analysis failed: {e}")
            raise
    
    def find_coverage_gaps(self,
                          data: gpd.GeoDataFrame,
                          signal_column: str = "rsrp",
                          threshold: float = -110.0,
                          gap_size_threshold: float = 0.001) -> gpd.GeoDataFrame:
        """Identify coverage gaps in the network
        
        Args:
            data: GeoDataFrame with signal measurements
            signal_column: Column name containing signal strength values
            threshold: Signal strength threshold for coverage
            gap_size_threshold: Minimum gap size to report (square degrees)
            
        Returns:
            GeoDataFrame containing coverage gap polygons
        """
        try:
            if data.empty:
                return gpd.GeoDataFrame(columns=['geometry', 'gap_area', 'severity'])
            
            # Create coverage areas
            covered_data = data[data[signal_column] >= threshold].copy()
            
            if covered_data.empty:
                # No coverage at all
                total_bounds = data.total_bounds
                gap_polygon = Polygon([
                    (total_bounds[0], total_bounds[1]),
                    (total_bounds[2], total_bounds[1]),
                    (total_bounds[2], total_bounds[3]),
                    (total_bounds[0], total_bounds[3])
                ])
                
                gaps_gdf = gpd.GeoDataFrame({
                    'geometry': [gap_polygon],
                    'gap_area': [gap_polygon.area],
                    'severity': ['Critical']
                }, crs=self.crs)
                
                return gaps_gdf
            
            # Buffer coverage points to create coverage polygons
            buffer_distance = 0.0005  # Approximately 50m at equator
            coverage_polygons = covered_data.geometry.buffer(buffer_distance)
            coverage_union = unary_union(coverage_polygons.tolist())
            
            # Create analysis boundary
            total_bounds = data.total_bounds
            boundary_polygon = Polygon([
                (total_bounds[0], total_bounds[1]),
                (total_bounds[2], total_bounds[1]),
                (total_bounds[2], total_bounds[3]),
                (total_bounds[0], total_bounds[3])
            ])
            
            # Find gaps (areas not covered)
            if hasattr(coverage_union, 'geoms'):
                # MultiPolygon case
                gaps = boundary_polygon.difference(coverage_union)
            else:
                # Single polygon case
                gaps = boundary_polygon.difference(coverage_union)
            
            # Process gaps
            gap_list = []
            if hasattr(gaps, 'geoms'):
                # Multiple gaps
                for gap in gaps.geoms:
                    if gap.area >= gap_size_threshold:
                        severity = self._classify_gap_severity(gap.area)
                        gap_list.append({
                            'geometry': gap,
                            'gap_area': gap.area,
                            'severity': severity
                        })
            else:
                # Single gap
                if gaps.area >= gap_size_threshold:
                    severity = self._classify_gap_severity(gaps.area)
                    gap_list.append({
                        'geometry': gaps,
                        'gap_area': gaps.area,
                        'severity': severity
                    })
            
            # Create GeoDataFrame
            if gap_list:
                gaps_gdf = gpd.GeoDataFrame(gap_list, crs=self.crs)
            else:
                gaps_gdf = gpd.GeoDataFrame(columns=['geometry', 'gap_area', 'severity'], crs=self.crs)
            
            self.logger.info(f"Found {len(gaps_gdf)} coverage gaps")
            return gaps_gdf
            
        except Exception as e:
            self.logger.error(f"Coverage gap analysis failed: {e}")
            raise
    
    def _classify_gap_severity(self, gap_area: float) -> str:
        """Classify gap severity based on area
        
        Args:
            gap_area: Area of the coverage gap
            
        Returns:
            Severity classification string
        """
        if gap_area > 0.01:  # Large gap
            return "Critical"
        elif gap_area > 0.005:  # Medium gap
            return "High"
        elif gap_area > 0.001:  # Small gap
            return "Medium"
        else:
            return "Low"
    
    def analyze_handover_patterns(self,
                                 data: gpd.GeoDataFrame,
                                 source_cell_col: str = "source_cell",
                                 target_cell_col: str = "target_cell",
                                 success_col: str = "handover_success") -> Dict[str, Any]:
        """Analyze handover patterns and success rates
        
        Args:
            data: GeoDataFrame with handover data
            source_cell_col: Column name for source cell ID
            target_cell_col: Column name for target cell ID
            success_col: Column name for handover success indicator
            
        Returns:
            Dictionary containing handover analysis results
        """
        try:
            if data.empty:
                return {"handover_success_rate": 0.0, "patterns": {}}
            
            required_cols = [source_cell_col, target_cell_col, success_col]
            missing_cols = [col for col in required_cols if col not in data.columns]
            if missing_cols:
                raise ValueError(f"Missing required columns: {missing_cols}")
            
            # Calculate overall success rate
            total_handovers = len(data)
            successful_handovers = len(data[data[success_col] == True])
            success_rate = (successful_handovers / total_handovers) * 100 if total_handovers > 0 else 0.0
            
            # Analyze handover patterns by cell pair
            handover_patterns = data.groupby([source_cell_col, target_cell_col]).agg({
                success_col: ['count', 'sum', 'mean'],
                'geometry': lambda x: x.iloc[0] if len(x) > 0 else None
            }).reset_index()
            
            # Flatten column names
            handover_patterns.columns = [
                source_cell_col, target_cell_col, 'total_attempts', 
                'successful_attempts', 'success_rate', 'location'
            ]
            
            # Convert to dictionary format
            patterns = {}
            for _, row in handover_patterns.iterrows():
                cell_pair = f"{row[source_cell_col]}->{row[target_cell_col]}"
                patterns[cell_pair] = {
                    "total_attempts": int(row['total_attempts']),
                    "successful_attempts": int(row['successful_attempts']),
                    "success_rate": float(row['success_rate']) * 100,
                    "location": [row['location'].x, row['location'].y] if row['location'] else None
                }
            
            result = {
                "handover_success_rate": success_rate,
                "total_handovers": total_handovers,
                "successful_handovers": successful_handovers,
                "patterns": patterns,
                "statistics": {
                    "unique_source_cells": data[source_cell_col].nunique(),
                    "unique_target_cells": data[target_cell_col].nunique(),
                    "total_cell_pairs": len(patterns),
                }
            }
            
            self.logger.info(f"Handover analysis completed: {success_rate:.2f}% success rate")
            return result
            
        except Exception as e:
            self.logger.error(f"Handover analysis failed: {e}")
            raise
    
    def create_heat_map_data(self,
                           data: gpd.GeoDataFrame,
                           value_column: str,
                           grid_size: float = 0.001,
                           aggregation: str = "mean") -> gpd.GeoDataFrame:
        """Create heat map data for visualization
        
        Args:
            data: GeoDataFrame with measurement data
            value_column: Column name containing values to aggregate
            grid_size: Grid cell size for heat map (degrees)
            aggregation: Aggregation method (mean, max, min, count)
            
        Returns:
            GeoDataFrame containing heat map grid with aggregated values
        """
        try:
            if data.empty or value_column not in data.columns:
                return gpd.GeoDataFrame(columns=['geometry', 'value', 'count'])
            
            # Get data bounds
            bounds = data.total_bounds
            
            # Create grid
            x_coords = np.arange(bounds[0], bounds[2] + grid_size, grid_size)
            y_coords = np.arange(bounds[1], bounds[3] + grid_size, grid_size)
            
            grid_cells = []
            grid_values = []
            grid_counts = []
            
            for i in range(len(x_coords) - 1):
                for j in range(len(y_coords) - 1):
                    # Create grid cell polygon
                    cell_polygon = Polygon([
                        (x_coords[i], y_coords[j]),
                        (x_coords[i+1], y_coords[j]),
                        (x_coords[i+1], y_coords[j+1]),
                        (x_coords[i], y_coords[j+1])
                    ])
                    
                    # Find points within this cell
                    cell_data = data[data.geometry.within(cell_polygon)]
                    
                    if not cell_data.empty:
                        # Aggregate values
                        if aggregation == "mean":
                            agg_value = cell_data[value_column].mean()
                        elif aggregation == "max":
                            agg_value = cell_data[value_column].max()
                        elif aggregation == "min":
                            agg_value = cell_data[value_column].min()
                        elif aggregation == "count":
                            agg_value = len(cell_data)
                        else:
                            agg_value = cell_data[value_column].mean()  # Default to mean
                        
                        grid_cells.append(cell_polygon)
                        grid_values.append(agg_value)
                        grid_counts.append(len(cell_data))
            
            # Create heat map GeoDataFrame
            heat_map_gdf = gpd.GeoDataFrame({
                'geometry': grid_cells,
                'value': grid_values,
                'count': grid_counts
            }, crs=self.crs)
            
            self.logger.info(f"Created heat map with {len(heat_map_gdf)} grid cells")
            return heat_map_gdf
            
        except Exception as e:
            self.logger.error(f"Heat map creation failed: {e}")
            raise
