"""Progress tracking utilities for database operations.

This module provides progress tracking functionality for long-running
database operations like ETL processes, data imports, and bulk operations.
"""

import time
from typing import Optional, Callable, Any
from loguru import logger


class ProgressTracker:
    """A simple progress tracker for monitoring operation progress.
    
    This class provides basic progress tracking functionality with
    optional callback support for custom progress handling.
    """
    
    def __init__(
        self,
        total: int,
        description: str = "Processing",
        callback: Optional[Callable[[int, int, float], None]] = None
    ):
        """Initialize the progress tracker.
        
        Args:
            total: Total number of items to process
            description: Description of the operation being tracked
            callback: Optional callback function called on progress updates
        """
        self.total = total
        self.description = description
        self.callback = callback
        self.current = 0
        self.start_time = time.time()
        
    def update(self, increment: int = 1) -> None:
        """Update the progress by the specified increment.
        
        Args:
            increment: Number of items processed since last update
        """
        self.current += increment
        
        # Calculate progress percentage
        progress_pct = (self.current / self.total) * 100 if self.total > 0 else 0
        
        # Log progress at certain intervals
        if self.current % max(1, self.total // 10) == 0 or self.current == self.total:
            elapsed = time.time() - self.start_time
            logger.info(
                f"{self.description}: {self.current}/{self.total} "
                f"({progress_pct:.1f}%) - {elapsed:.2f}s elapsed"
            )
        
        # Call callback if provided
        if self.callback:
            self.callback(self.current, self.total, progress_pct)
    
    def finish(self) -> None:
        """Mark the operation as finished."""
        elapsed = time.time() - self.start_time
        logger.info(
            f"{self.description} completed: {self.current}/{self.total} "
            f"in {elapsed:.2f}s"
        )
    
    @property
    def progress_percentage(self) -> float:
        """Get the current progress as a percentage.
        
        Returns:
            Progress percentage (0-100)
        """
        return (self.current / self.total) * 100 if self.total > 0 else 0
    
    @property
    def is_complete(self) -> bool:
        """Check if the operation is complete.
        
        Returns:
            True if current >= total
        """
        return self.current >= self.total