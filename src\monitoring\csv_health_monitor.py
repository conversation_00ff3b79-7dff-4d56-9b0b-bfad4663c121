#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV Processing Health Monitor

Proactive monitoring system to detect CSV processing issues before they cause
system-wide blocking. This monitor tracks file processing times, memory usage,
and identifies potential hanging conditions.
"""

import asyncio
import logging
import os
import psutil
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import aiofiles

# Setup logging
logger = logging.getLogger(__name__)


@dataclass
class ProcessingMetrics:
    """Metrics for CSV file processing."""
    file_path: str
    file_size: int
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    memory_peak: Optional[float] = None
    success: bool = False
    error_message: Optional[str] = None
    strategy_used: Optional[str] = None


@dataclass
class HealthStatus:
    """Overall health status of CSV processing."""
    is_healthy: bool
    avg_processing_time: float
    memory_usage_mb: float
    active_processes: int
    recent_failures: int
    warning_messages: List[str]
    last_check: datetime


class CSVHealthMonitor:
    """Monitor for CSV processing health and performance."""
    
    def __init__(self, max_history: int = 1000, warning_threshold: float = 30.0):
        self.max_history = max_history
        self.warning_threshold = warning_threshold  # seconds
        self.processing_history: List[ProcessingMetrics] = []
        self.active_processes: Dict[str, ProcessingMetrics] = {}
        self.is_monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
    
    def start_processing(self, file_path: str, file_size: int) -> str:
        """Register the start of CSV file processing."""
        process_id = f"{file_path}_{int(time.time() * 1000)}"
        
        metrics = ProcessingMetrics(
            file_path=file_path,
            file_size=file_size,
            start_time=datetime.now()
        )
        
        self.active_processes[process_id] = metrics
        logger.debug(f"Started monitoring CSV processing: {process_id}")
        
        return process_id
    
    def end_processing(self, process_id: str, success: bool = True, 
                      error_message: Optional[str] = None,
                      strategy_used: Optional[str] = None) -> None:
        """Register the completion of CSV file processing."""
        if process_id not in self.active_processes:
            logger.warning(f"Unknown process ID: {process_id}")
            return
        
        metrics = self.active_processes.pop(process_id)
        metrics.end_time = datetime.now()
        metrics.duration = (metrics.end_time - metrics.start_time).total_seconds()
        metrics.success = success
        metrics.error_message = error_message
        metrics.strategy_used = strategy_used
        
        # Record peak memory usage
        try:
            process = psutil.Process()
            metrics.memory_peak = process.memory_info().rss / 1024 / 1024  # MB
        except Exception:
            pass
        
        # Add to history
        self.processing_history.append(metrics)
        
        # Trim history if needed
        if len(self.processing_history) > self.max_history:
            self.processing_history = self.processing_history[-self.max_history:]
        
        # Log performance metrics
        if success:
            logger.info(
                f"CSV processing completed: {metrics.file_path} "
                f"({metrics.file_size} bytes) in {metrics.duration:.2f}s "
                f"using {metrics.strategy_used or 'default'} strategy"
            )
        else:
            logger.error(
                f"CSV processing failed: {metrics.file_path} "
                f"after {metrics.duration:.2f}s - {error_message}"
            )
    
    def check_hanging_processes(self) -> List[Tuple[str, float]]:
        """Check for processes that may be hanging."""
        hanging_processes = []
        current_time = datetime.now()
        
        for process_id, metrics in self.active_processes.items():
            duration = (current_time - metrics.start_time).total_seconds()
            
            # Check if process has been running too long
            expected_time = self._estimate_processing_time(metrics.file_size)
            if duration > max(expected_time * 3, self.warning_threshold):
                hanging_processes.append((process_id, duration))
                logger.warning(
                    f"Potential hanging process detected: {process_id} "
                    f"running for {duration:.1f}s (expected ~{expected_time:.1f}s)"
                )
        
        return hanging_processes
    
    def _estimate_processing_time(self, file_size: int) -> float:
        """Estimate expected processing time based on file size and history."""
        if not self.processing_history:
            # Default estimate: 1MB per second
            return max(file_size / (1024 * 1024), 1.0)
        
        # Calculate average processing rate from recent successful operations
        recent_successful = [
            m for m in self.processing_history[-50:] 
            if m.success and m.duration and m.duration > 0
        ]
        
        if not recent_successful:
            return max(file_size / (1024 * 1024), 1.0)
        
        # Calculate average MB/s processing rate
        total_mb = sum(m.file_size / (1024 * 1024) for m in recent_successful)
        total_time = sum(m.duration for m in recent_successful)
        
        if total_time > 0:
            avg_rate = total_mb / total_time  # MB/s
            return max((file_size / (1024 * 1024)) / avg_rate, 1.0)
        
        return max(file_size / (1024 * 1024), 1.0)
    
    def get_health_status(self) -> HealthStatus:
        """Get current health status of CSV processing."""
        current_time = datetime.now()
        recent_cutoff = current_time - timedelta(hours=1)
        
        # Get recent operations
        recent_ops = [
            m for m in self.processing_history 
            if m.start_time >= recent_cutoff
        ]
        
        # Calculate metrics
        successful_ops = [m for m in recent_ops if m.success and m.duration]
        failed_ops = [m for m in recent_ops if not m.success]
        
        avg_processing_time = 0.0
        if successful_ops:
            avg_processing_time = sum(m.duration for m in successful_ops) / len(successful_ops)
        
        # Get current memory usage
        try:
            process = psutil.Process()
            memory_usage_mb = process.memory_info().rss / 1024 / 1024
        except Exception:
            memory_usage_mb = 0.0
        
        # Check for hanging processes
        hanging_processes = self.check_hanging_processes()
        
        # Generate warnings
        warnings = []
        
        if hanging_processes:
            warnings.append(f"{len(hanging_processes)} potentially hanging processes detected")
        
        if len(failed_ops) > len(successful_ops) and len(recent_ops) > 5:
            warnings.append("High failure rate in recent operations")
        
        if avg_processing_time > self.warning_threshold:
            warnings.append(f"Average processing time ({avg_processing_time:.1f}s) exceeds threshold")
        
        if memory_usage_mb > 1024:  # 1GB
            warnings.append(f"High memory usage: {memory_usage_mb:.1f}MB")
        
        # Determine overall health
        is_healthy = (
            len(hanging_processes) == 0 and
            len(warnings) == 0 and
            (len(failed_ops) <= len(successful_ops) or len(recent_ops) <= 5)
        )
        
        return HealthStatus(
            is_healthy=is_healthy,
            avg_processing_time=avg_processing_time,
            memory_usage_mb=memory_usage_mb,
            active_processes=len(self.active_processes),
            recent_failures=len(failed_ops),
            warning_messages=warnings,
            last_check=current_time
        )
    
    async def start_monitoring(self, check_interval: float = 30.0) -> None:
        """Start continuous health monitoring."""
        if self.is_monitoring:
            logger.warning("Monitoring is already running")
            return
        
        self.is_monitoring = True
        logger.info(f"Starting CSV health monitoring (check interval: {check_interval}s)")
        
        try:
            while self.is_monitoring:
                # Check for hanging processes
                hanging_processes = self.check_hanging_processes()
                
                # Get health status
                health = self.get_health_status()
                
                # Log health status
                if not health.is_healthy:
                    logger.warning(
                        f"CSV processing health check: UNHEALTHY - "
                        f"Active: {health.active_processes}, "
                        f"Avg time: {health.avg_processing_time:.1f}s, "
                        f"Memory: {health.memory_usage_mb:.1f}MB, "
                        f"Warnings: {', '.join(health.warning_messages)}"
                    )
                else:
                    logger.debug(
                        f"CSV processing health check: HEALTHY - "
                        f"Active: {health.active_processes}, "
                        f"Avg time: {health.avg_processing_time:.1f}s, "
                        f"Memory: {health.memory_usage_mb:.1f}MB"
                    )
                
                # Wait for next check
                await asyncio.sleep(check_interval)
                
        except asyncio.CancelledError:
            logger.info("CSV health monitoring cancelled")
        except Exception as e:
            logger.error(f"Error in CSV health monitoring: {e}")
        finally:
            self.is_monitoring = False
    
    def stop_monitoring(self) -> None:
        """Stop continuous health monitoring."""
        if self._monitor_task and not self._monitor_task.done():
            self._monitor_task.cancel()
        self.is_monitoring = False
        logger.info("CSV health monitoring stopped")
    
    async def export_metrics(self, output_file: Path) -> None:
        """Export processing metrics to a file."""
        try:
            metrics_data = {
                'export_time': datetime.now().isoformat(),
                'total_operations': len(self.processing_history),
                'active_processes': len(self.active_processes),
                'health_status': self.get_health_status().__dict__,
                'recent_operations': [
                    {
                        'file_path': m.file_path,
                        'file_size': m.file_size,
                        'duration': m.duration,
                        'success': m.success,
                        'strategy_used': m.strategy_used,
                        'start_time': m.start_time.isoformat(),
                        'error_message': m.error_message
                    }
                    for m in self.processing_history[-100:]  # Last 100 operations
                ]
            }
            
            import json
            async with aiofiles.open(output_file, 'w') as f:
                await f.write(json.dumps(metrics_data, indent=2, default=str))
            
            logger.info(f"Metrics exported to {output_file}")
            
        except Exception as e:
            logger.error(f"Failed to export metrics: {e}")


# Global monitor instance
_global_monitor: Optional[CSVHealthMonitor] = None


def get_monitor() -> CSVHealthMonitor:
    """Get the global CSV health monitor instance."""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = CSVHealthMonitor()
    return _global_monitor


def start_csv_processing(file_path: str, file_size: int) -> str:
    """Convenience function to start monitoring CSV processing."""
    return get_monitor().start_processing(file_path, file_size)


def end_csv_processing(process_id: str, success: bool = True, 
                      error_message: Optional[str] = None,
                      strategy_used: Optional[str] = None) -> None:
    """Convenience function to end monitoring CSV processing."""
    get_monitor().end_processing(process_id, success, error_message, strategy_used)


async def start_health_monitoring(check_interval: float = 30.0) -> None:
    """Start the global health monitoring."""
    await get_monitor().start_monitoring(check_interval)


def stop_health_monitoring() -> None:
    """Stop the global health monitoring."""
    get_monitor().stop_monitoring()


if __name__ == "__main__":
    # Example usage
    async def main():
        monitor = CSVHealthMonitor()
        
        # Start monitoring
        monitor_task = asyncio.create_task(monitor.start_monitoring(10.0))
        
        # Simulate some CSV processing
        process_id = monitor.start_processing("/tmp/test.csv", 1024000)
        await asyncio.sleep(2)  # Simulate processing time
        monitor.end_processing(process_id, success=True, strategy_used="async")
        
        # Check health
        health = monitor.get_health_status()
        print(f"Health Status: {'HEALTHY' if health.is_healthy else 'UNHEALTHY'}")
        print(f"Active Processes: {health.active_processes}")
        print(f"Average Processing Time: {health.avg_processing_time:.2f}s")
        
        # Stop monitoring
        monitor.stop_monitoring()
        await monitor_task
    
    asyncio.run(main())