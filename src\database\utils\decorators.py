"""General-purpose decorators for database operations.

This module provides various decorators for common patterns like retry logic,
caching, validation, and error handling.
"""

import asyncio
import functools
import logging
import secrets
import time
from typing import Any, Callable, List, Optional, Tuple, Type, Union

from ..exceptions import ConnectionError, DatabaseError, TimeoutError

# Configure logging
logger = logging.getLogger(__name__)


def retry(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    jitter: bool = True,
    exceptions: Tuple[Type[Exception], ...] = (ConnectionError, TimeoutError),
    on_retry: Optional[Callable[[int, Exception], None]] = None,
) -> Callable:
    """Decorator to retry function execution on specific exceptions.

    Args:
        max_attempts: Maximum number of retry attempts.
        delay: Initial delay between retries in seconds.
        backoff: Multiplier for delay after each retry.
        jitter: Whether to add random jitter to delay.
        exceptions: Tuple of exception types to retry on.
        on_retry: Optional callback function called on each retry.

    Returns:
        Decorated function with retry logic.

    Examples:
        >>> @retry(max_attempts=3, delay=1.0, exceptions=(ConnectionError,))
        ... async def connect_to_database():
        ...     # Database connection logic
        ...     pass

        >>> @retry(max_attempts=5, backoff=1.5, jitter=True)
        ... def query_database():
        ...     # Database query logic
        ...     pass
    """

    def decorator(func: Callable) -> Callable:
        if asyncio.iscoroutinefunction(func):
            return _async_retry(
                func, max_attempts, delay, backoff, jitter, exceptions, on_retry
            )
        else:
            return _sync_retry(
                func, max_attempts, delay, backoff, jitter, exceptions, on_retry
            )

    return decorator


def _async_retry(
    func: Callable,
    max_attempts: int,
    delay: float,
    backoff: float,
    jitter: bool,
    exceptions: Tuple[Type[Exception], ...],
    on_retry: Optional[Callable[[int, Exception], None]],
) -> Callable:
    """Internal async retry decorator."""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        last_exception = None
        current_delay = delay

        for attempt in range(max_attempts):
            try:
                return await func(*args, **kwargs)
            except exceptions as e:
                last_exception = e

                if attempt == max_attempts - 1:
                    # Last attempt failed, re-raise the exception
                    logger.error(
                        f"Function {func.__name__} failed after {max_attempts} attempts: {str(e)}"
                    )
                    raise

                # Calculate delay with optional jitter
                actual_delay = current_delay
                if jitter:
                    # Use secrets for cryptographically secure random numbers
                    actual_delay *= (
                        0.5 + secrets.SystemRandom().random()
                    )  # 50-150% of base delay

                logger.warning(
                    f"Function {func.__name__} failed on attempt {attempt + 1}/{max_attempts}: {str(e)}. "
                    f"Retrying in {actual_delay:.2f}s"
                )

                # Call retry callback if provided
                if on_retry:
                    try:
                        on_retry(attempt + 1, e)
                    except Exception as callback_error:
                        logger.warning(f"Retry callback failed: {str(callback_error)}")

                # Wait before retry
                await asyncio.sleep(actual_delay)

                # Increase delay for next attempt
                current_delay *= backoff

            except Exception as e:
                # Non-retryable exception, re-raise immediately
                logger.error(
                    f"Function {func.__name__} failed with non-retryable exception: {str(e)}"
                )
                raise

        # This should never be reached, but just in case
        if last_exception:
            raise last_exception

    return wrapper


def _sync_retry(
    func: Callable,
    max_attempts: int,
    delay: float,
    backoff: float,
    jitter: bool,
    exceptions: Tuple[Type[Exception], ...],
    on_retry: Optional[Callable[[int, Exception], None]],
) -> Callable:
    """Internal sync retry decorator."""

    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        last_exception = None
        current_delay = delay

        for attempt in range(max_attempts):
            try:
                return func(*args, **kwargs)
            except exceptions as e:
                last_exception = e

                if attempt == max_attempts - 1:
                    # Last attempt failed, re-raise the exception
                    logger.error(
                        f"Function {func.__name__} failed after {max_attempts} attempts: {str(e)}"
                    )
                    raise

                # Calculate delay with optional jitter
                actual_delay = current_delay
                if jitter:
                    # Use secrets for cryptographically secure random numbers
                    actual_delay *= (
                        0.5 + secrets.SystemRandom().random()
                    )  # 50-150% of base delay

                logger.warning(
                    f"Function {func.__name__} failed on attempt {attempt + 1}/{max_attempts}: {str(e)}. "
                    f"Retrying in {actual_delay:.2f}s"
                )

                # Call retry callback if provided
                if on_retry:
                    try:
                        on_retry(attempt + 1, e)
                    except Exception as callback_error:
                        logger.warning(f"Retry callback failed: {str(callback_error)}")

                # Wait before retry
                time.sleep(actual_delay)

                # Increase delay for next attempt
                current_delay *= backoff

            except Exception as e:
                # Non-retryable exception, re-raise immediately
                logger.error(
                    f"Function {func.__name__} failed with non-retryable exception: {str(e)}"
                )
                raise

        # This should never be reached, but just in case
        if last_exception:
            raise last_exception

    return wrapper


def timeout(seconds: float) -> Callable:
    """Decorator to add timeout to async functions.

    Args:
        seconds: Timeout in seconds.

    Returns:
        Decorated function with timeout.

    Examples:
        >>> @timeout(30.0)
        ... async def long_running_query():
        ...     # Long running database operation
        ...     pass
    """

    def decorator(func: Callable) -> Callable:
        if not asyncio.iscoroutinefunction(func):
            raise ValueError("timeout decorator can only be applied to async functions")

        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=seconds)
            except asyncio.TimeoutError:
                error_msg = f"Function {func.__name__} timed out after {seconds}s"
                logger.error(error_msg)
                raise TimeoutError(
                    error_msg,
                    error_code="FUNCTION_TIMEOUT",
                    details={"function": func.__name__, "timeout": seconds},
                )

        return wrapper

    return decorator


def validate_args(*validators: Callable[[Any], bool]) -> Callable:
    """Decorator to validate function arguments.

    Args:
        *validators: Validation functions that take an argument and return bool.

    Returns:
        Decorated function with argument validation.

    Examples:
        >>> def is_positive(x):
        ...     return x > 0

        >>> @validate_args(is_positive, lambda y: isinstance(y, str))
        ... def process_data(x, y):
        ...     return x, y
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # Validate positional arguments
            if len(args) > len(validators):
                logger.warning(f"More arguments than validators for {func.__name__}")

            for i, (arg, validator) in enumerate(zip(args, validators)):
                try:
                    if not validator(arg):
                        raise ValueError(
                            f"Validation failed for argument {i} in {func.__name__}"
                        )
                except Exception as e:
                    error_msg = f"Validation error for argument {i} in {func.__name__}: {str(e)}"
                    logger.error(error_msg)
                    raise ValueError(error_msg) from e

            return func(*args, **kwargs)

        return wrapper

    return decorator


def cache_result(
    ttl: Optional[float] = None,
    maxsize: int = 128,
    key_func: Optional[Callable[..., str]] = None,
) -> Callable:
    """Decorator to cache function results.

    Args:
        ttl: Time-to-live for cached results in seconds. None for no expiration.
        maxsize: Maximum number of cached results.
        key_func: Function to generate cache keys from arguments.

    Returns:
        Decorated function with result caching.

    Examples:
        >>> @cache_result(ttl=300, maxsize=100)
        ... def expensive_query(query_id):
        ...     # Expensive database query
        ...     return result
    """

    def decorator(func: Callable) -> Callable:
        cache = {}
        cache_times = {}

        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = str(args) + str(sorted(kwargs.items()))

            # Check if result is cached and not expired
            if cache_key in cache:
                if ttl is None or (time.time() - cache_times[cache_key]) < ttl:
                    logger.debug(f"Cache hit for {func.__name__} with key {cache_key}")
                    return cache[cache_key]
                else:
                    # Expired, remove from cache
                    del cache[cache_key]
                    del cache_times[cache_key]

            # Execute function and cache result
            result = func(*args, **kwargs)

            # Manage cache size
            if len(cache) >= maxsize:
                # Remove oldest entry
                oldest_key = min(cache_times.keys(), key=lambda k: cache_times[k])
                del cache[oldest_key]
                del cache_times[oldest_key]

            # Store result
            cache[cache_key] = result
            cache_times[cache_key] = time.time()

            logger.debug(f"Cached result for {func.__name__} with key {cache_key}")
            return result

        # Add cache management methods
        wrapper.cache_clear = lambda: (cache.clear(), cache_times.clear())
        wrapper.cache_info = lambda: {
            "hits": len([k for k in cache.keys()]),
            "size": len(cache),
            "maxsize": maxsize,
        }

        return wrapper

    return decorator


def log_calls(
    log_level: int = logging.INFO,
    include_args: bool = True,
    include_result: bool = False,
    max_arg_length: int = 100,
) -> Callable:
    """Decorator to log function calls.

    Args:
        log_level: Logging level for call messages.
        include_args: Whether to include arguments in log messages.
        include_result: Whether to include return value in log messages.
        max_arg_length: Maximum length for argument strings.

    Returns:
        Decorated function with call logging.

    Examples:
        >>> @log_calls(log_level=logging.DEBUG, include_result=True)
        ... def database_query(sql):
        ...     # Database query logic
        ...     return result
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            function_name = f"{func.__module__}.{func.__qualname__}"

            # Prepare arguments string
            if include_args:
                args_str = str(args)[:max_arg_length]
                kwargs_str = str(kwargs)[:max_arg_length]
                if len(str(args)) > max_arg_length:
                    args_str += "..."
                if len(str(kwargs)) > max_arg_length:
                    kwargs_str += "..."
                call_info = f"args={args_str}, kwargs={kwargs_str}"
            else:
                call_info = "<args hidden>"

            logger.log(log_level, f"Calling {function_name} with {call_info}")

            try:
                result = func(*args, **kwargs)

                if include_result:
                    result_str = str(result)[:max_arg_length]
                    if len(str(result)) > max_arg_length:
                        result_str += "..."
                    logger.log(log_level, f"Completed {function_name} -> {result_str}")
                else:
                    logger.log(log_level, f"Completed {function_name}")

                return result

            except Exception as e:
                logger.log(log_level, f"Failed {function_name}: {str(e)}")
                raise

        return wrapper

    return decorator


def deprecated(reason: str = "", version: str = "") -> Callable:
    """Decorator to mark functions as deprecated.

    Args:
        reason: Reason for deprecation.
        version: Version when function was deprecated.

    Returns:
        Decorated function that logs deprecation warnings.

    Examples:
        >>> @deprecated(reason="Use new_function instead", version="2.0")
        ... def old_function():
        ...     pass
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            function_name = f"{func.__module__}.{func.__qualname__}"
            warning_msg = f"Function {function_name} is deprecated"

            if version:
                warning_msg += f" since version {version}"
            if reason:
                warning_msg += f": {reason}"

            logger.warning(warning_msg)

            return func(*args, **kwargs)

        return wrapper

    return decorator


def singleton(cls: Type) -> Type:
    """Decorator to make a class a singleton.

    Args:
        cls: Class to make singleton.

    Returns:
        Singleton class.

    Examples:
        >>> @singleton
        ... class DatabaseManager:
        ...     pass
    """
    instances = {}

    @functools.wraps(cls)
    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return get_instance


def rate_limit(calls_per_second: float) -> Callable:
    """Decorator to rate limit function calls.

    Args:
        calls_per_second: Maximum number of calls per second.

    Returns:
        Decorated function with rate limiting.

    Examples:
        >>> @rate_limit(10.0)  # Max 10 calls per second
        ... def api_call():
        ...     pass
    """
    min_interval = 1.0 / calls_per_second
    last_called = [0.0]

    def decorator(func: Callable) -> Callable:
        if asyncio.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs) -> Any:
                now = time.time()
                time_since_last = now - last_called[0]

                if time_since_last < min_interval:
                    sleep_time = min_interval - time_since_last
                    await asyncio.sleep(sleep_time)

                last_called[0] = time.time()
                return await func(*args, **kwargs)

            return async_wrapper
        else:

            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs) -> Any:
                now = time.time()
                time_since_last = now - last_called[0]

                if time_since_last < min_interval:
                    sleep_time = min_interval - time_since_last
                    time.sleep(sleep_time)

                last_called[0] = time.time()
                return func(*args, **kwargs)

            return sync_wrapper

    return decorator
