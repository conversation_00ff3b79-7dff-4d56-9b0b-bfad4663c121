"""Database configuration management.

This module provides database configuration loading and validation
functionalities for the Connect project using the unified configuration system.

Author: Vincent.Li
Email: <EMAIL>
Version: 2.0.0
"""

import logging
import os
from typing import Dict, Any, Optional

# Use the new unified configuration system
try:
    from ..config import get_config, reload_config as reload_global_config
    from ..config.models import ConnectConfig, DatabaseConfig
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    # Add parent directory to path
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from config import get_config, reload_config as reload_global_config
    from config.models import ConnectConfig, DatabaseConfig

logger = logging.getLogger(__name__)


def get_config(reload: bool = False) -> ConnectConfig:
    """Get the application configuration.
    
    This function now uses the unified configuration manager for consistency.
    
    Args:
        reload: Force reload configuration from files
        
    Returns:
        ConnectConfig: Application configuration
    """
    # Import here to avoid circular imports
    from ..config import get_config as get_unified_config
    return get_unified_config(reload=reload)


def reload_config() -> ConnectConfig:
    """Force reload the configuration.
    
    Returns:
        ConnectConfig: Reloaded configuration
    """
    return reload_global_config()


def validate_config(config: Optional[ConnectConfig] = None) -> bool:
    """Validate the configuration.
    
    Args:
        config: Configuration to validate (uses current if None)
        
    Returns:
        bool: True if configuration is valid
    """
    if config is None:
        config = get_config()
    
    try:
        # Validate database configuration
        db_config = config.database
        
        # Check required fields
        required_fields = ['host', 'port', 'name', 'user']
        for field in required_fields:
            if not hasattr(db_config, field) or getattr(db_config, field) is None:
                logger.error(f"Missing required database field: {field}")
                return False
        
        # Validate port range
        if not (1 <= db_config.port <= 65535):
            logger.error(f"Invalid database port: {db_config.port}")
            return False
        
        # Validate pool configuration if present
        if hasattr(db_config, 'pool') and db_config.pool:
            pool_config = db_config.pool
            if hasattr(pool_config, 'min_size') and hasattr(pool_config, 'max_size'):
                if pool_config.min_size > pool_config.max_size:
                    logger.error("Pool min_size cannot be greater than max_size")
                    return False
        
        logger.info("Configuration validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        return False


def get_database_url(config: Optional[ConnectConfig] = None) -> str:
    """Get the database URL from configuration.
    
    Args:
        config: Configuration to use (uses current if None)
        
    Returns:
        str: Database URL
    """
    if config is None:
        config = get_config()
    
    db_config = config.database
    
    # Build database URL
    url_parts = [
        f"postgresql://{db_config.user}"
    ]
    
    if db_config.password:
        url_parts[0] += f":{db_config.password}"
    
    url_parts.extend([
        f"@{db_config.host}:{db_config.port}",
        f"/{db_config.name}"
    ])
    
    return "".join(url_parts)


def get_database_config(config: Optional[ConnectConfig] = None) -> DatabaseConfig:
    """Get the database configuration.
    
    Args:
        config: Configuration to use (uses current if None)
        
    Returns:
        DatabaseConfig: Database configuration
    """
    if config is None:
        config = get_config()
    
    return config.database


# Backward compatibility functions
def get_legacy_database_config() -> Dict[str, Any]:
    """Get database configuration as dictionary for legacy compatibility.
    
    Returns:
        Dict[str, Any]: Database configuration as dictionary
    """
    config = get_config()
    return config.database.model_dump()


def get_connection_params() -> Dict[str, Any]:
    """Get database connection parameters.
    
    Returns:
        Dict[str, Any]: Connection parameters
    """
    config = get_config()
    db_config = config.database
    
    params = {
        'host': db_config.host,
        'port': db_config.port,
        'database': db_config.name,
        'user': db_config.user,
    }
    
    if db_config.password:
        params['password'] = db_config.password
    
    return params


# Global settings instance for backward compatibility
# Note: This will be initialized when first accessed to avoid circular imports
_settings = None

def get_settings():
    """Get global settings instance for backward compatibility."""
    global _settings
    if _settings is None:
        _settings = get_config()
    return _settings

# Create a settings object that can be imported directly
class SettingsProxy:
    """Proxy object for backward compatibility with direct settings access."""
    
    def __getattr__(self, name):
        config = get_config()
        if hasattr(config, name):
            return getattr(config, name)
        elif hasattr(config.database, name):
            return getattr(config.database, name)
        else:
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

# Create global settings instance for backward compatibility
settings = SettingsProxy()
