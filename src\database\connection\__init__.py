__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Database connection management module.

This module provides connection pooling, session management,
health checking capabilities, and read-write splitting for database operations.
"""

from .health_check import <PERSON><PERSON>hecker, HealthCheckResult, HealthStats, HealthStatus
from .pool import DatabasePoolManager
from .read_write_splitter import (
    LoadBalancingStrategy,
    NoAvailableReplicasError,
    PrimaryDatabaseUnavailableError,
    ReadWriteSplitter,
)
from .session import SessionManager, get_session_manager

__all__ = [
    "DatabasePoolManager",
    "SessionManager",
    "get_session_manager",
    "HealthChecker",
    "HealthCheckResult",
    "HealthStats",
    "HealthStatus",
    "LoadBalancingStrategy",
    "NoAvailableReplicasError",
    "PrimaryDatabaseUnavailableError",
    "ReadWriteSplitter",
]
