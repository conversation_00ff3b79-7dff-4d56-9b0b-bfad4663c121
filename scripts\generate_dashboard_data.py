#!/usr/bin/env python3
"""
Connect电信平台Dashboard数据生成脚本
用于将聚合的测试结果转换为监控dashboard所需的数据格式
"""

import json
import argparse
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DashboardDataGenerator:
    """Dashboard数据生成器"""
    
    def __init__(self, results_file: str, output_file: str):
        self.results_file = Path(results_file)
        self.output_file = Path(output_file)
        self.dashboard_data = {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'version': '1.0.0',
                'platform': 'Connect电信数据分析平台'
            },
            'overview': {},
            'test_metrics': {},
            'performance_metrics': {},
            'security_metrics': {},
            'quality_metrics': {},
            'coverage_metrics': {},
            'trend_data': {},
            'alerts': [],
            'charts': {},
            'tables': {}
        }
    
    def load_test_results(self) -> Dict[str, Any]:
        """加载测试结果数据"""
        try:
            with open(self.results_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading test results: {e}")
            return {}
    
    def generate_overview_metrics(self, results: Dict[str, Any]):
        """生成概览指标"""
        summary = results.get('summary', {})
        
        self.dashboard_data['overview'] = {
            'test_summary': {
                'total_tests': summary.get('total_tests', 0),
                'success_rate': round(summary.get('success_rate', 0), 2),
                'failed_tests': summary.get('failed_tests', 0),
                'duration': round(summary.get('total_duration', 0), 2),
                'status': self._get_overall_status(summary.get('success_rate', 0))
            },
            'environment_info': {
                'environment': results.get('environment', 'unknown'),
                'branch': results.get('branch', 'unknown'),
                'commit': results.get('commit_sha', 'unknown')[:8],
                'timestamp': results.get('timestamp', '')
            },
            'health_indicators': self._generate_health_indicators(results)
        }
    
    def _get_overall_status(self, success_rate: float) -> str:
        """根据成功率确定整体状态"""
        if success_rate >= 95:
            return 'healthy'
        elif success_rate >= 80:
            return 'warning'
        else:
            return 'critical'
    
    def _generate_health_indicators(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成健康指标"""
        indicators = []
        
        # 测试健康度
        success_rate = results.get('summary', {}).get('success_rate', 0)
        indicators.append({
            'name': '测试健康度',
            'value': success_rate,
            'unit': '%',
            'status': self._get_overall_status(success_rate),
            'threshold': 95.0
        })
        
        # 代码覆盖率
        coverage = results.get('coverage_data', {}).get('line_coverage', 0)
        indicators.append({
            'name': '代码覆盖率',
            'value': coverage,
            'unit': '%',
            'status': 'healthy' if coverage >= 80 else 'warning' if coverage >= 60 else 'critical',
            'threshold': 80.0
        })
        
        # 安全问题
        security = results.get('security_summary', {})
        critical_issues = security.get('critical_issues', 0)
        indicators.append({
            'name': '安全状态',
            'value': critical_issues,
            'unit': '个严重问题',
            'status': 'healthy' if critical_issues == 0 else 'critical',
            'threshold': 0
        })
        
        # 代码质量
        quality_score = results.get('quality_metrics', {}).get('overall_score', 0)
        indicators.append({
            'name': '代码质量',
            'value': quality_score,
            'unit': '分',
            'status': 'healthy' if quality_score >= 80 else 'warning' if quality_score >= 60 else 'critical',
            'threshold': 80.0
        })
        
        return indicators
    
    def generate_test_metrics(self, results: Dict[str, Any]):
        """生成测试指标数据"""
        test_suites = results.get('test_suites', {})
        
        # 测试套件概览
        suite_summary = []
        for suite_name, suite_data in test_suites.items():
            test_results = suite_data.get('test_results', {})
            total_tests = sum(tr.get('tests', 0) for tr in test_results.values())
            total_failures = sum(tr.get('failures', 0) for tr in test_results.values())
            total_errors = sum(tr.get('errors', 0) for tr in test_results.values())
            total_time = sum(tr.get('time', 0) for tr in test_results.values())
            
            success_rate = 0
            if total_tests > 0:
                success_rate = ((total_tests - total_failures - total_errors) / total_tests) * 100
            
            suite_summary.append({
                'name': suite_name,
                'total_tests': total_tests,
                'success_rate': round(success_rate, 2),
                'failures': total_failures,
                'errors': total_errors,
                'duration': round(total_time, 2),
                'status': self._get_overall_status(success_rate)
            })
        
        self.dashboard_data['test_metrics'] = {
            'suite_summary': suite_summary,
            'test_distribution': self._generate_test_distribution(results),
            'failure_analysis': self._generate_failure_analysis(test_suites)
        }
    
    def _generate_test_distribution(self, results: Dict[str, Any]) -> Dict[str, int]:
        """生成测试分布数据"""
        summary = results.get('summary', {})
        return {
            'passed': summary.get('passed_tests', 0),
            'failed': summary.get('failed_tests', 0),
            'skipped': summary.get('skipped_tests', 0),
            'errors': summary.get('error_tests', 0)
        }
    
    def _generate_failure_analysis(self, test_suites: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成失败分析数据"""
        failures = []
        
        for suite_name, suite_data in test_suites.items():
            test_results = suite_data.get('test_results', {})
            for test_name, test_data in test_results.items():
                test_cases = test_data.get('test_cases', [])
                for case in test_cases:
                    if case.get('status') in ['failed', 'error']:
                        failures.append({
                            'suite': suite_name,
                            'test': test_name,
                            'case': case.get('name', ''),
                            'status': case.get('status', ''),
                            'message': case.get('failure', case.get('error', '')),
                            'duration': case.get('time', 0)
                        })
        
        return failures[:20]  # 限制显示前20个失败
    
    def generate_performance_metrics(self, results: Dict[str, Any]):
        """生成性能指标数据"""
        perf_data = results.get('performance_metrics', {})
        
        # 性能基准数据
        benchmarks = []
        for benchmark in perf_data.get('benchmarks', []):
            bench_data = benchmark.get('data', {})
            if isinstance(bench_data, dict):
                benchmarks.append({
                    'name': benchmark.get('file', '').split('/')[-1],
                    'metrics': bench_data
                })
        
        # 响应时间分析
        response_times = self._extract_response_times(perf_data)
        
        # 内存使用分析
        memory_usage = self._extract_memory_usage(perf_data)
        
        self.dashboard_data['performance_metrics'] = {
            'benchmarks': benchmarks,
            'response_times': response_times,
            'memory_usage': memory_usage,
            'throughput': perf_data.get('throughput', {})
        }
    
    def _extract_response_times(self, perf_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取响应时间数据"""
        # 这里可以根据实际的性能测试数据格式进行解析
        return {
            'api_endpoints': [],
            'database_queries': [],
            'file_operations': []
        }
    
    def _extract_memory_usage(self, perf_data: Dict[str, Any]) -> Dict[str, Any]:
        """提取内存使用数据"""
        memory_files = perf_data.get('memory_usage', {})
        return {
            'peak_usage': 0,
            'average_usage': 0,
            'memory_leaks': [],
            'files': list(memory_files.keys())
        }
    
    def generate_security_metrics(self, results: Dict[str, Any]):
        """生成安全指标数据"""
        security = results.get('security_summary', {})
        
        # 安全问题分布
        issue_distribution = {
            'critical': security.get('critical_issues', 0),
            'high': security.get('high_issues', 0),
            'medium': security.get('medium_issues', 0),
            'low': security.get('low_issues', 0)
        }
        
        # 漏洞扫描结果
        vulnerability_scans = security.get('vulnerability_scans', [])
        
        # 静态分析结果
        static_analysis = security.get('static_analysis', {})
        
        self.dashboard_data['security_metrics'] = {
            'issue_distribution': issue_distribution,
            'total_issues': security.get('total_issues', 0),
            'vulnerability_scans': vulnerability_scans,
            'static_analysis_summary': self._summarize_static_analysis(static_analysis),
            'dependency_check': security.get('dependency_check', {})
        }
    
    def _summarize_static_analysis(self, static_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """汇总静态分析结果"""
        summary = {
            'bandit': {'issues': 0, 'confidence': 'unknown'},
            'semgrep': {'rules_matched': 0, 'findings': 0}
        }
        
        if 'bandit' in static_analysis:
            bandit_data = static_analysis['bandit']
            if isinstance(bandit_data, list):
                summary['bandit']['issues'] = len(bandit_data)
        
        if 'semgrep' in static_analysis:
            semgrep_data = static_analysis['semgrep']
            if isinstance(semgrep_data, dict):
                summary['semgrep']['findings'] = len(semgrep_data.get('results', []))
        
        return summary
    
    def generate_quality_metrics(self, results: Dict[str, Any]):
        """生成质量指标数据"""
        quality = results.get('quality_metrics', {})
        
        # 代码分析结果
        code_analysis = quality.get('code_analysis', {})
        
        # 复杂度指标
        complexity = quality.get('complexity_metrics', {})
        
        # 风格检查
        style_checks = quality.get('style_checks', {})
        
        self.dashboard_data['quality_metrics'] = {
            'overall_score': quality.get('overall_score', 0),
            'code_analysis': self._summarize_code_analysis(code_analysis),
            'complexity_metrics': self._summarize_complexity(complexity),
            'style_checks': self._summarize_style_checks(style_checks),
            'type_checks': quality.get('type_checks', {})
        }
    
    def _summarize_code_analysis(self, code_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """汇总代码分析结果"""
        summary = {
            'pylint': {'score': 0, 'issues': 0},
            'eslint': {'errors': 0, 'warnings': 0}
        }
        
        if 'pylint' in code_analysis:
            pylint_data = code_analysis['pylint']
            if isinstance(pylint_data, list):
                summary['pylint']['issues'] = len(pylint_data)
        
        if 'eslint' in code_analysis:
            eslint_data = code_analysis['eslint']
            if isinstance(eslint_data, list):
                total_errors = sum(len(file.get('messages', [])) for file in eslint_data)
                summary['eslint']['errors'] = total_errors
        
        return summary
    
    def _summarize_complexity(self, complexity: Dict[str, Any]) -> Dict[str, Any]:
        """汇总复杂度指标"""
        return {
            'cyclomatic_complexity': 0,
            'maintainability_index': 0,
            'halstead_metrics': {}
        }
    
    def _summarize_style_checks(self, style_checks: Dict[str, Any]) -> Dict[str, Any]:
        """汇总风格检查结果"""
        return {
            'flake8': {'violations': 0},
            'black': {'format_issues': 0},
            'prettier': {'format_issues': 0}
        }
    
    def generate_coverage_metrics(self, results: Dict[str, Any]):
        """生成覆盖率指标数据"""
        coverage = results.get('coverage_data', {})
        
        self.dashboard_data['coverage_metrics'] = {
            'overall': {
                'line_coverage': coverage.get('line_coverage', 0),
                'branch_coverage': coverage.get('branch_coverage', 0),
                'function_coverage': coverage.get('function_coverage', 0),
                'statement_coverage': coverage.get('statement_coverage', 0)
            },
            'by_package': [],
            'uncovered_lines': [],
            'trend': []
        }
    
    def generate_chart_configs(self):
        """生成图表配置"""
        self.dashboard_data['charts'] = {
            'test_success_rate': {
                'type': 'gauge',
                'title': '测试成功率',
                'data': self.dashboard_data['overview']['test_summary']['success_rate'],
                'config': {
                    'min': 0,
                    'max': 100,
                    'thresholds': [80, 95]
                }
            },
            'test_distribution': {
                'type': 'pie',
                'title': '测试结果分布',
                'data': self.dashboard_data['test_metrics']['test_distribution']
            },
            'security_issues': {
                'type': 'bar',
                'title': '安全问题分布',
                'data': self.dashboard_data['security_metrics']['issue_distribution']
            },
            'coverage_trend': {
                'type': 'line',
                'title': '覆盖率趋势',
                'data': self.dashboard_data['coverage_metrics']['trend']
            }
        }
    
    def generate_alerts(self, results: Dict[str, Any]):
        """生成告警信息"""
        alerts = []
        
        # 测试失败告警
        success_rate = results.get('summary', {}).get('success_rate', 0)
        if success_rate < 95:
            alerts.append({
                'level': 'warning' if success_rate >= 80 else 'critical',
                'type': 'test_failure',
                'message': f'测试成功率 {success_rate:.1f}% 低于预期阈值 95%',
                'timestamp': datetime.now().isoformat()
            })
        
        # 安全问题告警
        security = results.get('security_summary', {})
        critical_issues = security.get('critical_issues', 0)
        if critical_issues > 0:
            alerts.append({
                'level': 'critical',
                'type': 'security_issue',
                'message': f'发现 {critical_issues} 个严重安全问题',
                'timestamp': datetime.now().isoformat()
            })
        
        # 覆盖率告警
        coverage = results.get('coverage_data', {}).get('line_coverage', 0)
        if coverage < 80:
            alerts.append({
                'level': 'warning',
                'type': 'coverage_low',
                'message': f'代码覆盖率 {coverage:.1f}% 低于预期阈值 80%',
                'timestamp': datetime.now().isoformat()
            })
        
        self.dashboard_data['alerts'] = alerts
    
    def generate(self) -> bool:
        """生成dashboard数据"""
        logger.info(f"Generating dashboard data from {self.results_file}")
        
        # 加载测试结果
        results = self.load_test_results()
        if not results:
            logger.error("No test results found")
            return False
        
        # 生成各类指标数据
        self.generate_overview_metrics(results)
        self.generate_test_metrics(results)
        self.generate_performance_metrics(results)
        self.generate_security_metrics(results)
        self.generate_quality_metrics(results)
        self.generate_coverage_metrics(results)
        self.generate_chart_configs()
        self.generate_alerts(results)
        
        # 保存dashboard数据
        self.output_file.parent.mkdir(parents=True, exist_ok=True)
        with open(self.output_file, 'w', encoding='utf-8') as f:
            json.dump(self.dashboard_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Dashboard data generated successfully: {self.output_file}")
        return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='生成Connect电信平台Dashboard数据'
    )
    parser.add_argument(
        '--results-file',
        required=True,
        help='聚合测试结果文件'
    )
    parser.add_argument(
        '--output-file',
        required=True,
        help='Dashboard数据输出文件'
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建生成器并执行生成
    generator = DashboardDataGenerator(args.results_file, args.output_file)
    success = generator.generate()
    
    if success:
        logger.info("Dashboard data generation completed successfully")
        return 0
    else:
        logger.error("Dashboard data generation failed")
        return 1

if __name__ == '__main__':
    exit(main())