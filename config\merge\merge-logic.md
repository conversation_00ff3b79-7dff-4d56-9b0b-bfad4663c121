# System Requirements Overview
Please create a modern CDR data processing system based on the following core business logic, implementing a complete processing flow from Excel configuration files to a unified data table.

## Core Business Logic

### 1. Configuration-Driven Data Mapping System
**Goal:** Define column mapping from multiple source data tables to a unified target table through an Excel configuration file.
**Key Requirements:**
- Read the Excel configuration file (must include the `Unique_cdr_name` column as the target column name).
- Each column in the configuration file represents a column mapping rule for a source table.
- Support flexible column name matching (case-insensitive, interchangeable underscores and spaces).
- Maintain the original column order from the Excel file in the final output.

### 2. Smart Source Table Discovery Mechanism
**Goal:** Automatically discover the source data tables that need to be processed in the database.
**Key Requirements:**
- Scan all tables in the specified database schema.
- Exclude tables from specific schemas (containing keywords like `20xx`, `Merge`, `TRACE_ROUTE`, etc.).
- Perform intelligent matching based on table names and configured column names.
- Support fuzzy matching and variant matching (handling of underscores and spaces).

### 3. Data Transformation and Enhancement Processing
**Goal:** Convert data from various source tables into a unified format and add metadata.
**Key Requirements:**
- Convert source table column names to target column names according to the configuration mapping.
- Extract benchmark information (e.g., `2024Q3` format) from the source table name and add it to the `u_Benchmark` field.
- Add the `u_Benchmark_service` field to record the source data table name.
- Handle missing columns (fill with `None` values).
- Maintain data type consistency.

### 4. Multi-dimensional Data Classification and Export
**Goal:** Classify the merged data according to business rules.
**Key Requirements:**
- **Main Table:** The complete merged data table.
- **Unqualified Data:** Filter records containing "NOT QUALIFIED".
- **P10 Performance Data:** Classify based on transfer rate thresholds:
    - `HTTP_FDTT_DL`: ≤20Mbps, ≤100Mbps
    - `HTTP_FDTT_UL`: ≤2Mbps, ≤5Mbps
    - `HTTP_FILE_DL`: ≤25Mbps
    - `HTTP_FILE_UL`: ≤15Mbps
- **Clean Data:** Exclude data related to Ping/DNS/TRACE_ROUTE.

### 5. Multi-Operator Support Architecture
**Goal:** Support independent data processing for multiple operators.
**Key Requirements:**
- **Operator Configuration:** Telefonica(`TO2_CDR`/`to2`), Vodafone(`VDF_CDR`/`vdf`), Telekom(`TDG_CDR`/`tdg`).
- Each operator uses an independent database schema.
- Support parallel processing to improve efficiency.
- Unified error handling and logging.

### 6. Performance and Scalability Requirements
**Key Requirements:**
- Use a connection pool to manage database connections.
- Use a thread pool to process multiple source tables in parallel.
- Cache configuration data to avoid repeated reads.
- Support large volume data processing (batch processing).
- Complete exception handling and recovery mechanisms.

### 7. Data Quality and Sorting
**Key Requirements:**
- **Complex Sorting Logic:** First by year/quarter (`u_Benchmark`), then by service type (`u_Benchmark_service`).
- **Year/Quarter Sorting:** Convert `2024Q3` to `20243` for numerical sorting.
- **Service Type Priority:** `HTTP_FDTT_DL`(1) > `HTTP_FDTT_UL`(2) > `Ping`(3) > `DNS`(4).
- **Output Format:** CSV file + Database table.

## Technical Implementation Requirements

### Programming Language and Frameworks
- Python 3.8+
- `pandas` for data processing.
- `SQLAlchemy` for database operations.
- `concurrent.futures` for parallel processing.
- `openpyxl` for handling Excel files.

### Database
- PostgreSQL as the main database.
- Multi-tenant architecture with schema separation.
- Transaction processing to ensure data consistency.

### Code Structure
- Object-oriented design with clear separation of responsibilities.
- Configuration-driven to reduce hardcoding.
- Complete error handling and logging.
- Unit tests covering core logic.

### Output Requirements
- Create an output directory structure based on the operator code.
- **CSV File Naming Convention:** `{table_name}.csv`
- **Database Table Naming Convention:** `{prefix}_{table_name}`
- Processing logs should include detailed processing statistics.

### Desired Code Features
- **Modular Design:** Clear separation of classes and methods for easy maintenance and extension.
- **Configuration Flexibility:** Support for adjusting parameters like operators, table names, paths, etc., through configuration files.
- **Error Recovery:** Failure in processing a single table should not affect the overall process.
- **Performance Monitoring:** Statistics on processing time, record counts, file sizes, etc.
- **Comprehensive Documentation:** Clear comments and usage instructions.
