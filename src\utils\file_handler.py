# -*- coding: utf-8 -*-
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""File handling utilities.

This module provides comprehensive file handling functionality
for the Connect platform, including file operations, metadata
extraction, and error handling.
"""

import os
import shutil
import hashlib
import mimetypes
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from datetime import datetime


class FileError(Exception):
    """Custom exception for file handling errors."""
    pass


@dataclass
class FileInfo:
    """File information and metadata."""
    path: str
    name: str
    size: int
    extension: str
    mime_type: Optional[str]
    created_at: datetime
    modified_at: datetime
    is_directory: bool
    permissions: str
    checksum: Optional[str] = None
    
    @classmethod
    def from_path(cls, file_path: Union[str, Path], calculate_checksum: bool = False) -> 'FileInfo':
        """Create FileInfo from file path.
        
        Args:
            file_path: Path to the file
            calculate_checksum: Whether to calculate file checksum
            
        Returns:
            FileInfo instance
            
        Raises:
            FileError: If file doesn't exist or can't be accessed
        """
        path = Path(file_path)
        
        if not path.exists():
            raise FileError(f"File does not exist: {file_path}")
        
        try:
            stat = path.stat()
            
            # Get MIME type
            mime_type, _ = mimetypes.guess_type(str(path))
            
            # Calculate checksum if requested
            checksum = None
            if calculate_checksum and path.is_file():
                checksum = FileHandler.calculate_checksum(path)
            
            return cls(
                path=str(path.absolute()),
                name=path.name,
                size=stat.st_size,
                extension=path.suffix.lower(),
                mime_type=mime_type,
                created_at=datetime.fromtimestamp(stat.st_ctime),
                modified_at=datetime.fromtimestamp(stat.st_mtime),
                is_directory=path.is_dir(),
                permissions=oct(stat.st_mode)[-3:],
                checksum=checksum
            )
        except Exception as e:
            raise FileError(f"Error accessing file {file_path}: {str(e)}")


class FileHandler:
    """Comprehensive file handling utility class."""
    
    @staticmethod
    def read_file(file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """Read text file content.
        
        Args:
            file_path: Path to the file
            encoding: File encoding
            
        Returns:
            File content as string
            
        Raises:
            FileError: If file can't be read
        """
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except Exception as e:
            raise FileError(f"Error reading file {file_path}: {str(e)}")
    
    @staticmethod
    def write_file(file_path: Union[str, Path], content: str, encoding: str = 'utf-8', 
                   create_dirs: bool = True) -> None:
        """Write content to text file.
        
        Args:
            file_path: Path to the file
            content: Content to write
            encoding: File encoding
            create_dirs: Whether to create parent directories
            
        Raises:
            FileError: If file can't be written
        """
        path = Path(file_path)
        
        try:
            if create_dirs:
                path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(path, 'w', encoding=encoding) as f:
                f.write(content)
        except Exception as e:
            raise FileError(f"Error writing file {file_path}: {str(e)}")
    
    @staticmethod
    def read_binary(file_path: Union[str, Path]) -> bytes:
        """Read binary file content.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File content as bytes
            
        Raises:
            FileError: If file can't be read
        """
        try:
            with open(file_path, 'rb') as f:
                return f.read()
        except Exception as e:
            raise FileError(f"Error reading binary file {file_path}: {str(e)}")
    
    @staticmethod
    def write_binary(file_path: Union[str, Path], content: bytes, 
                     create_dirs: bool = True) -> None:
        """Write binary content to file.
        
        Args:
            file_path: Path to the file
            content: Binary content to write
            create_dirs: Whether to create parent directories
            
        Raises:
            FileError: If file can't be written
        """
        path = Path(file_path)
        
        try:
            if create_dirs:
                path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(path, 'wb') as f:
                f.write(content)
        except Exception as e:
            raise FileError(f"Error writing binary file {file_path}: {str(e)}")
    
    @staticmethod
    def copy_file(src: Union[str, Path], dst: Union[str, Path], 
                  create_dirs: bool = True) -> None:
        """Copy file from source to destination.
        
        Args:
            src: Source file path
            dst: Destination file path
            create_dirs: Whether to create parent directories
            
        Raises:
            FileError: If file can't be copied
        """
        src_path = Path(src)
        dst_path = Path(dst)
        
        if not src_path.exists():
            raise FileError(f"Source file does not exist: {src}")
        
        try:
            if create_dirs:
                dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(src_path, dst_path)
        except Exception as e:
            raise FileError(f"Error copying file from {src} to {dst}: {str(e)}")
    
    @staticmethod
    def move_file(src: Union[str, Path], dst: Union[str, Path], 
                  create_dirs: bool = True) -> None:
        """Move file from source to destination.
        
        Args:
            src: Source file path
            dst: Destination file path
            create_dirs: Whether to create parent directories
            
        Raises:
            FileError: If file can't be moved
        """
        src_path = Path(src)
        dst_path = Path(dst)
        
        if not src_path.exists():
            raise FileError(f"Source file does not exist: {src}")
        
        try:
            if create_dirs:
                dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(src_path), str(dst_path))
        except Exception as e:
            raise FileError(f"Error moving file from {src} to {dst}: {str(e)}")
    
    @staticmethod
    def delete_file(file_path: Union[str, Path]) -> None:
        """Delete a file.
        
        Args:
            file_path: Path to the file
            
        Raises:
            FileError: If file can't be deleted
        """
        path = Path(file_path)
        
        if not path.exists():
            return  # File doesn't exist, nothing to delete
        
        try:
            if path.is_file():
                path.unlink()
            elif path.is_dir():
                shutil.rmtree(path)
        except Exception as e:
            raise FileError(f"Error deleting file {file_path}: {str(e)}")
    
    @staticmethod
    def create_directory(dir_path: Union[str, Path], parents: bool = True) -> None:
        """Create directory.
        
        Args:
            dir_path: Directory path
            parents: Whether to create parent directories
            
        Raises:
            FileError: If directory can't be created
        """
        path = Path(dir_path)
        
        try:
            path.mkdir(parents=parents, exist_ok=True)
        except Exception as e:
            raise FileError(f"Error creating directory {dir_path}: {str(e)}")
    
    @staticmethod
    def list_files(dir_path: Union[str, Path], pattern: str = '*', 
                   recursive: bool = False) -> List[FileInfo]:
        """List files in directory.
        
        Args:
            dir_path: Directory path
            pattern: File pattern (glob)
            recursive: Whether to search recursively
            
        Returns:
            List of FileInfo objects
            
        Raises:
            FileError: If directory can't be accessed
        """
        path = Path(dir_path)
        
        if not path.exists():
            raise FileError(f"Directory does not exist: {dir_path}")
        
        if not path.is_dir():
            raise FileError(f"Path is not a directory: {dir_path}")
        
        try:
            files = []
            glob_method = path.rglob if recursive else path.glob
            
            for file_path in glob_method(pattern):
                try:
                    file_info = FileInfo.from_path(file_path)
                    files.append(file_info)
                except FileError:
                    # Skip files that can't be accessed
                    continue
            
            return files
        except Exception as e:
            raise FileError(f"Error listing files in {dir_path}: {str(e)}")
    
    @staticmethod
    def calculate_checksum(file_path: Union[str, Path], algorithm: str = 'md5') -> str:
        """Calculate file checksum.
        
        Args:
            file_path: Path to the file
            algorithm: Hash algorithm (md5, sha1, sha256)
            
        Returns:
            Hexadecimal checksum string
            
        Raises:
            FileError: If checksum can't be calculated
        """
        path = Path(file_path)
        
        if not path.exists() or not path.is_file():
            raise FileError(f"File does not exist: {file_path}")
        
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
        except Exception as e:
            raise FileError(f"Error calculating checksum for {file_path}: {str(e)}")
    
    @staticmethod
    def get_file_info(file_path: Union[str, Path], calculate_checksum: bool = False) -> FileInfo:
        """Get comprehensive file information.
        
        Args:
            file_path: Path to the file
            calculate_checksum: Whether to calculate checksum
            
        Returns:
            FileInfo object
            
        Raises:
            FileError: If file info can't be retrieved
        """
        return FileInfo.from_path(file_path, calculate_checksum)
    
    @staticmethod
    def ensure_directory_exists(file_path: Union[str, Path]) -> None:
        """Ensure the parent directory of a file exists.
        
        Args:
            file_path: Path to the file
            
        Raises:
            FileError: If directory can't be created
        """
        path = Path(file_path)
        parent_dir = path.parent
        
        if not parent_dir.exists():
            FileHandler.create_directory(parent_dir)
    
    @staticmethod
    def is_safe_path(file_path: Union[str, Path], base_path: Union[str, Path]) -> bool:
        """Check if file path is safe (within base directory).
        
        Args:
            file_path: File path to check
            base_path: Base directory path
            
        Returns:
            True if path is safe, False otherwise
        """
        try:
            file_path = Path(file_path).resolve()
            base_path = Path(base_path).resolve()
            
            return str(file_path).startswith(str(base_path))
        except Exception:
            return False