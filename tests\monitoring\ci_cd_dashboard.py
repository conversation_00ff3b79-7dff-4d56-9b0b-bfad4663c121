#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CI/CD监控Dashboard

集成E2E测试、性能测试和安全测试结果到统一的监控dashboard
支持实时监控、历史趋势分析、告警通知等功能

作者: Connect电信数据分析平台开发团队
创建时间: 2024-01-20
"""

import asyncio
import json
import logging
import os
import sqlite3
import time
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin

import aiofiles
import aiohttp
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from starlette.requests import Request
from starlette.websockets import WebSocket, WebSocketDisconnect
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ci_cd_dashboard.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ==================== 数据模型 ====================

@dataclass
class TestResult:
    """测试结果数据模型"""
    test_id: str
    test_type: str  # e2e, performance, security
    test_name: str
    status: str  # passed, failed, error, skipped
    duration: float
    timestamp: str
    details: Dict[str, Any]
    metrics: Dict[str, Union[int, float]]
    errors: List[str]
    warnings: List[str]
    
@dataclass
class BuildInfo:
    """构建信息数据模型"""
    build_id: str
    branch: str
    commit_hash: str
    commit_message: str
    author: str
    timestamp: str
    status: str  # running, success, failed, cancelled
    duration: Optional[float] = None
    
@dataclass
class QualityMetrics:
    """质量指标数据模型"""
    build_id: str
    timestamp: str
    test_coverage: float
    code_quality_score: float
    security_score: float
    performance_score: float
    reliability_score: float
    maintainability_score: float
    
@dataclass
class AlertRule:
    """告警规则数据模型"""
    rule_id: str
    name: str
    description: str
    condition: str
    threshold: Union[int, float]
    severity: str  # critical, high, medium, low
    enabled: bool
    notification_channels: List[str]
    
# ==================== 数据存储层 ====================

class DashboardDatabase:
    """Dashboard数据库管理"""
    
    def __init__(self, db_path: str = "ci_cd_dashboard.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库表结构"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 测试结果表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    test_id TEXT UNIQUE NOT NULL,
                    test_type TEXT NOT NULL,
                    test_name TEXT NOT NULL,
                    status TEXT NOT NULL,
                    duration REAL NOT NULL,
                    timestamp TEXT NOT NULL,
                    details TEXT,
                    metrics TEXT,
                    errors TEXT,
                    warnings TEXT,
                    build_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 构建信息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS builds (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    build_id TEXT UNIQUE NOT NULL,
                    branch TEXT NOT NULL,
                    commit_hash TEXT NOT NULL,
                    commit_message TEXT,
                    author TEXT,
                    timestamp TEXT NOT NULL,
                    status TEXT NOT NULL,
                    duration REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 质量指标表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS quality_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    build_id TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    test_coverage REAL,
                    code_quality_score REAL,
                    security_score REAL,
                    performance_score REAL,
                    reliability_score REAL,
                    maintainability_score REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 告警规则表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS alert_rules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    rule_id TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    condition TEXT NOT NULL,
                    threshold REAL NOT NULL,
                    severity TEXT NOT NULL,
                    enabled BOOLEAN DEFAULT 1,
                    notification_channels TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 告警历史表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS alert_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    rule_id TEXT NOT NULL,
                    message TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    resolved BOOLEAN DEFAULT 0,
                    resolved_at TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            logger.info("数据库初始化完成")
    
    def save_test_result(self, result: TestResult, build_id: str = None):
        """保存测试结果"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO test_results 
                (test_id, test_type, test_name, status, duration, timestamp, 
                 details, metrics, errors, warnings, build_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                result.test_id,
                result.test_type,
                result.test_name,
                result.status,
                result.duration,
                result.timestamp,
                json.dumps(result.details),
                json.dumps(result.metrics),
                json.dumps(result.errors),
                json.dumps(result.warnings),
                build_id
            ))
            conn.commit()
    
    def save_build_info(self, build: BuildInfo):
        """保存构建信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO builds 
                (build_id, branch, commit_hash, commit_message, author, timestamp, status, duration)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                build.build_id,
                build.branch,
                build.commit_hash,
                build.commit_message,
                build.author,
                build.timestamp,
                build.status,
                build.duration
            ))
            conn.commit()
    
    def save_quality_metrics(self, metrics: QualityMetrics):
        """保存质量指标"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO quality_metrics 
                (build_id, timestamp, test_coverage, code_quality_score, 
                 security_score, performance_score, reliability_score, maintainability_score)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                metrics.build_id,
                metrics.timestamp,
                metrics.test_coverage,
                metrics.code_quality_score,
                metrics.security_score,
                metrics.performance_score,
                metrics.reliability_score,
                metrics.maintainability_score
            ))
            conn.commit()
    
    def get_recent_test_results(self, limit: int = 100) -> List[Dict]:
        """获取最近的测试结果"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM test_results 
                ORDER BY created_at DESC 
                LIMIT ?
            """, (limit,))
            
            columns = [desc[0] for desc in cursor.description]
            results = []
            for row in cursor.fetchall():
                result = dict(zip(columns, row))
                # 解析JSON字段
                for field in ['details', 'metrics', 'errors', 'warnings']:
                    if result[field]:
                        try:
                            result[field] = json.loads(result[field])
                        except json.JSONDecodeError:
                            result[field] = {}
                results.append(result)
            
            return results
    
    def get_build_statistics(self, days: int = 30) -> Dict:
        """获取构建统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取指定天数内的构建统计
            since_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_builds,
                    SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_builds,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_builds,
                    AVG(duration) as avg_duration
                FROM builds 
                WHERE created_at >= ?
            """, (since_date,))
            
            result = cursor.fetchone()
            return {
                'total_builds': result[0] or 0,
                'successful_builds': result[1] or 0,
                'failed_builds': result[2] or 0,
                'success_rate': (result[1] or 0) / max(result[0] or 1, 1) * 100,
                'avg_duration': result[3] or 0
            }
    
    def get_test_statistics(self, days: int = 30) -> Dict:
        """获取测试统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            since_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            cursor.execute("""
                SELECT 
                    test_type,
                    COUNT(*) as total_tests,
                    SUM(CASE WHEN status = 'passed' THEN 1 ELSE 0 END) as passed_tests,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_tests,
                    AVG(duration) as avg_duration
                FROM test_results 
                WHERE created_at >= ?
                GROUP BY test_type
            """, (since_date,))
            
            results = {}
            for row in cursor.fetchall():
                test_type = row[0]
                results[test_type] = {
                    'total_tests': row[1],
                    'passed_tests': row[2],
                    'failed_tests': row[3],
                    'success_rate': (row[2] / max(row[1], 1)) * 100,
                    'avg_duration': row[4] or 0
                }
            
            return results

# ==================== 数据收集器 ====================

class TestResultCollector:
    """测试结果收集器"""
    
    def __init__(self, db: DashboardDatabase):
        self.db = db
        self.test_runners = {
            'e2e': self._collect_e2e_results,
            'performance': self._collect_performance_results,
            'security': self._collect_security_results
        }
    
    async def collect_all_results(self, build_id: str) -> Dict[str, List[TestResult]]:
        """收集所有类型的测试结果"""
        all_results = {}
        
        for test_type, collector in self.test_runners.items():
            try:
                logger.info(f"收集 {test_type} 测试结果")
                results = await collector(build_id)
                all_results[test_type] = results
                
                # 保存到数据库
                for result in results:
                    self.db.save_test_result(result, build_id)
                    
                logger.info(f"{test_type} 测试结果收集完成: {len(results)} 个测试")
                
            except Exception as e:
                logger.error(f"收集 {test_type} 测试结果失败: {e}")
                all_results[test_type] = []
        
        return all_results
    
    async def _collect_e2e_results(self, build_id: str) -> List[TestResult]:
        """收集E2E测试结果"""
        results = []
        
        # 从E2E测试报告文件读取结果
        e2e_report_path = Path("tests/e2e/reports/test_results.json")
        if e2e_report_path.exists():
            async with aiofiles.open(e2e_report_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                data = json.loads(content)
                
                for test_data in data.get('tests', []):
                    result = TestResult(
                        test_id=f"e2e_{test_data['name']}_{build_id}",
                        test_type="e2e",
                        test_name=test_data['name'],
                        status=test_data['status'],
                        duration=test_data.get('duration', 0),
                        timestamp=test_data.get('timestamp', datetime.now().isoformat()),
                        details=test_data.get('details', {}),
                        metrics=test_data.get('metrics', {}),
                        errors=test_data.get('errors', []),
                        warnings=test_data.get('warnings', [])
                    )
                    results.append(result)
        
        return results
    
    async def _collect_performance_results(self, build_id: str) -> List[TestResult]:
        """收集性能测试结果"""
        results = []
        
        # 从性能测试报告文件读取结果
        perf_report_path = Path("tests/performance/reports/benchmark_results.json")
        if perf_report_path.exists():
            async with aiofiles.open(perf_report_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                data = json.loads(content)
                
                for benchmark in data.get('benchmarks', []):
                    result = TestResult(
                        test_id=f"perf_{benchmark['name']}_{build_id}",
                        test_type="performance",
                        test_name=benchmark['name'],
                        status="passed" if benchmark.get('passed', True) else "failed",
                        duration=benchmark.get('duration', 0),
                        timestamp=benchmark.get('timestamp', datetime.now().isoformat()),
                        details=benchmark.get('details', {}),
                        metrics={
                            'execution_time': benchmark.get('execution_time', 0),
                            'memory_usage': benchmark.get('memory_usage', 0),
                            'cpu_usage': benchmark.get('cpu_usage', 0),
                            'throughput': benchmark.get('throughput', 0)
                        },
                        errors=benchmark.get('errors', []),
                        warnings=benchmark.get('warnings', [])
                    )
                    results.append(result)
        
        return results
    
    async def _collect_security_results(self, build_id: str) -> List[TestResult]:
        """收集安全测试结果"""
        results = []
        
        # 从安全测试报告文件读取结果
        security_report_path = Path("tests/security/reports/security_scan_results.json")
        if security_report_path.exists():
            async with aiofiles.open(security_report_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                data = json.loads(content)
                
                for scan in data.get('scans', []):
                    vulnerabilities = scan.get('vulnerabilities', [])
                    critical_count = len([v for v in vulnerabilities if v.get('severity') == 'critical'])
                    high_count = len([v for v in vulnerabilities if v.get('severity') == 'high'])
                    
                    status = "failed" if critical_count > 0 or high_count > 5 else "passed"
                    
                    result = TestResult(
                        test_id=f"sec_{scan['scan_type']}_{build_id}",
                        test_type="security",
                        test_name=scan['scan_type'],
                        status=status,
                        duration=scan.get('duration', 0),
                        timestamp=scan.get('start_time', datetime.now().isoformat()),
                        details=scan.get('summary', {}),
                        metrics={
                            'total_vulnerabilities': len(vulnerabilities),
                            'critical_vulnerabilities': critical_count,
                            'high_vulnerabilities': high_count,
                            'risk_score': scan.get('risk_score', 0)
                        },
                        errors=[],
                        warnings=[f"发现 {len(vulnerabilities)} 个安全漏洞"]
                    )
                    results.append(result)
        
        return results

# ==================== 质量指标计算器 ====================

class QualityMetricsCalculator:
    """质量指标计算器"""
    
    def __init__(self, db: DashboardDatabase):
        self.db = db
    
    def calculate_metrics(self, build_id: str, test_results: Dict[str, List[TestResult]]) -> QualityMetrics:
        """计算质量指标"""
        # 测试覆盖率
        test_coverage = self._calculate_test_coverage(test_results)
        
        # 代码质量评分
        code_quality_score = self._calculate_code_quality_score(test_results)
        
        # 安全评分
        security_score = self._calculate_security_score(test_results.get('security', []))
        
        # 性能评分
        performance_score = self._calculate_performance_score(test_results.get('performance', []))
        
        # 可靠性评分
        reliability_score = self._calculate_reliability_score(test_results)
        
        # 可维护性评分
        maintainability_score = self._calculate_maintainability_score(test_results)
        
        return QualityMetrics(
            build_id=build_id,
            timestamp=datetime.now().isoformat(),
            test_coverage=test_coverage,
            code_quality_score=code_quality_score,
            security_score=security_score,
            performance_score=performance_score,
            reliability_score=reliability_score,
            maintainability_score=maintainability_score
        )
    
    def _calculate_test_coverage(self, test_results: Dict[str, List[TestResult]]) -> float:
        """计算测试覆盖率"""
        # 简化计算：基于E2E测试通过率
        e2e_results = test_results.get('e2e', [])
        if not e2e_results:
            return 0.0
        
        passed_tests = len([r for r in e2e_results if r.status == 'passed'])
        total_tests = len(e2e_results)
        
        return (passed_tests / max(total_tests, 1)) * 100
    
    def _calculate_code_quality_score(self, test_results: Dict[str, List[TestResult]]) -> float:
        """计算代码质量评分"""
        # 基于所有测试的通过率
        all_results = []
        for results in test_results.values():
            all_results.extend(results)
        
        if not all_results:
            return 0.0
        
        passed_tests = len([r for r in all_results if r.status == 'passed'])
        total_tests = len(all_results)
        
        return (passed_tests / max(total_tests, 1)) * 100
    
    def _calculate_security_score(self, security_results: List[TestResult]) -> float:
        """计算安全评分"""
        if not security_results:
            return 100.0  # 没有安全测试时给满分
        
        total_vulnerabilities = 0
        critical_vulnerabilities = 0
        high_vulnerabilities = 0
        
        for result in security_results:
            metrics = result.metrics
            total_vulnerabilities += metrics.get('total_vulnerabilities', 0)
            critical_vulnerabilities += metrics.get('critical_vulnerabilities', 0)
            high_vulnerabilities += metrics.get('high_vulnerabilities', 0)
        
        # 安全评分计算：严重漏洞权重最高
        penalty = critical_vulnerabilities * 20 + high_vulnerabilities * 10 + (total_vulnerabilities - critical_vulnerabilities - high_vulnerabilities) * 2
        
        return max(0, 100 - penalty)
    
    def _calculate_performance_score(self, performance_results: List[TestResult]) -> float:
        """计算性能评分"""
        if not performance_results:
            return 100.0
        
        passed_tests = len([r for r in performance_results if r.status == 'passed'])
        total_tests = len(performance_results)
        
        return (passed_tests / max(total_tests, 1)) * 100
    
    def _calculate_reliability_score(self, test_results: Dict[str, List[TestResult]]) -> float:
        """计算可靠性评分"""
        # 基于测试稳定性和错误率
        all_results = []
        for results in test_results.values():
            all_results.extend(results)
        
        if not all_results:
            return 100.0
        
        error_tests = len([r for r in all_results if r.status == 'error'])
        total_tests = len(all_results)
        
        error_rate = error_tests / max(total_tests, 1)
        return max(0, 100 - error_rate * 100)
    
    def _calculate_maintainability_score(self, test_results: Dict[str, List[TestResult]]) -> float:
        """计算可维护性评分"""
        # 基于测试执行时间和复杂度
        all_results = []
        for results in test_results.values():
            all_results.extend(results)
        
        if not all_results:
            return 100.0
        
        avg_duration = sum(r.duration for r in all_results) / len(all_results)
        
        # 执行时间越长，可维护性评分越低
        if avg_duration < 10:  # 10秒以内
            return 100.0
        elif avg_duration < 60:  # 1分钟以内
            return 80.0
        elif avg_duration < 300:  # 5分钟以内
            return 60.0
        else:
            return 40.0

# ==================== Web Dashboard ====================

app = FastAPI(title="CI/CD监控Dashboard", version="1.0.0")

# 静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 全局变量
db = DashboardDatabase()
collector = TestResultCollector(db)
calculator = QualityMetricsCalculator(db)
connected_websockets: List[WebSocket] = []

@app.get("/", response_class=HTMLResponse)
async def dashboard_home(request: Request):
    """Dashboard主页"""
    return templates.TemplateResponse("dashboard.html", {"request": request})

@app.get("/api/overview")
async def get_overview():
    """获取概览数据"""
    build_stats = db.get_build_statistics()
    test_stats = db.get_test_statistics()
    recent_results = db.get_recent_test_results(20)
    
    return {
        "build_statistics": build_stats,
        "test_statistics": test_stats,
        "recent_results": recent_results
    }

@app.get("/api/trends")
async def get_trends(days: int = 30):
    """获取趋势数据"""
    # 这里应该实现趋势数据查询
    # 简化实现，返回模拟数据
    return {
        "build_success_rate": [85, 87, 90, 88, 92, 89, 91],
        "test_pass_rate": [92, 94, 91, 95, 93, 96, 94],
        "security_score": [78, 82, 85, 83, 87, 89, 91],
        "performance_score": [88, 85, 90, 87, 89, 92, 90],
        "dates": ["2024-01-14", "2024-01-15", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-20"]
    }

@app.post("/api/collect-results")
async def collect_test_results(build_id: str, background_tasks: BackgroundTasks):
    """收集测试结果"""
    background_tasks.add_task(run_collection_task, build_id)
    return {"message": "测试结果收集任务已启动", "build_id": build_id}

async def run_collection_task(build_id: str):
    """运行收集任务"""
    try:
        # 收集测试结果
        test_results = await collector.collect_all_results(build_id)
        
        # 计算质量指标
        metrics = calculator.calculate_metrics(build_id, test_results)
        db.save_quality_metrics(metrics)
        
        # 通知WebSocket客户端
        await notify_websocket_clients({
            "type": "collection_complete",
            "build_id": build_id,
            "results": {
                test_type: len(results) for test_type, results in test_results.items()
            },
            "metrics": asdict(metrics)
        })
        
        logger.info(f"构建 {build_id} 的测试结果收集完成")
        
    except Exception as e:
        logger.error(f"收集测试结果失败: {e}")
        await notify_websocket_clients({
            "type": "collection_error",
            "build_id": build_id,
            "error": str(e)
        })

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    await websocket.accept()
    connected_websockets.append(websocket)
    
    try:
        while True:
            # 保持连接活跃
            await websocket.receive_text()
    except WebSocketDisconnect:
        connected_websockets.remove(websocket)

async def notify_websocket_clients(message: Dict):
    """通知所有WebSocket客户端"""
    if connected_websockets:
        disconnected = []
        for websocket in connected_websockets:
            try:
                await websocket.send_json(message)
            except:
                disconnected.append(websocket)
        
        # 清理断开的连接
        for ws in disconnected:
            if ws in connected_websockets:
                connected_websockets.remove(ws)

if __name__ == "__main__":
    # 创建必要的目录
    os.makedirs("static", exist_ok=True)
    os.makedirs("templates", exist_ok=True)
    
    # 启动服务器
    uvicorn.run(
        "ci_cd_dashboard:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )