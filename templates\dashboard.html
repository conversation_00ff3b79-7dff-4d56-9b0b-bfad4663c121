<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connect电信数据分析平台 - CI/CD监控Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/plotly.js-dist@2.26.0/plotly.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-success { background-color: #10b981; }
        .status-failed { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }
        .status-running { background-color: #3b82f6; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 100%);
        }
        
        .nav-item {
            transition: background-color 0.3s ease;
        }
        
        .nav-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .nav-item.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-left: 4px solid #fbbf24;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 侧边栏 -->
    <div class="flex h-screen">
        <div class="sidebar w-64 text-white p-6">
            <div class="mb-8">
                <h1 class="text-xl font-bold">CI/CD监控</h1>
                <p class="text-blue-200 text-sm">Connect电信数据分析平台</p>
            </div>
            
            <nav class="space-y-2">
                <a href="#overview" class="nav-item active flex items-center p-3 rounded-lg" onclick="showSection('overview')">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    概览
                </a>
                <a href="#builds" class="nav-item flex items-center p-3 rounded-lg" onclick="showSection('builds')">
                    <i class="fas fa-hammer mr-3"></i>
                    构建状态
                </a>
                <a href="#tests" class="nav-item flex items-center p-3 rounded-lg" onclick="showSection('tests')">
                    <i class="fas fa-vial mr-3"></i>
                    测试结果
                </a>
                <a href="#quality" class="nav-item flex items-center p-3 rounded-lg" onclick="showSection('quality')">
                    <i class="fas fa-chart-line mr-3"></i>
                    质量指标
                </a>
                <a href="#security" class="nav-item flex items-center p-3 rounded-lg" onclick="showSection('security')">
                    <i class="fas fa-shield-alt mr-3"></i>
                    安全扫描
                </a>
                <a href="#performance" class="nav-item flex items-center p-3 rounded-lg" onclick="showSection('performance')">
                    <i class="fas fa-rocket mr-3"></i>
                    性能监控
                </a>
            </nav>
            
            <div class="mt-8 p-4 bg-blue-800 rounded-lg">
                <h3 class="font-semibold mb-2">系统状态</h3>
                <div class="flex items-center mb-2">
                    <span class="status-indicator status-success"></span>
                    <span class="text-sm">API服务正常</span>
                </div>
                <div class="flex items-center mb-2">
                    <span class="status-indicator status-success"></span>
                    <span class="text-sm">数据库连接正常</span>
                </div>
                <div class="flex items-center">
                    <span class="status-indicator status-running"></span>
                    <span class="text-sm">监控服务运行中</span>
                </div>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="flex-1 overflow-auto">
            <!-- 头部 -->
            <header class="bg-white shadow-sm p-6 border-b">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 id="page-title" class="text-2xl font-bold text-gray-800">概览</h2>
                        <p class="text-gray-600">实时监控CI/CD流水线状态和质量指标</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button onclick="refreshData()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-sync-alt mr-2"></i>刷新数据
                        </button>
                        <div class="text-sm text-gray-500">
                            最后更新: <span id="last-update">--</span>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 概览页面 -->
            <div id="overview-section" class="p-6">
                <!-- 关键指标卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="metric-card text-white p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">构建成功率</p>
                                <p id="build-success-rate" class="text-3xl font-bold">--</p>
                            </div>
                            <i class="fas fa-check-circle text-4xl text-blue-200"></i>
                        </div>
                    </div>
                    
                    <div class="metric-card text-white p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">测试通过率</p>
                                <p id="test-pass-rate" class="text-3xl font-bold">--</p>
                            </div>
                            <i class="fas fa-vial text-4xl text-blue-200"></i>
                        </div>
                    </div>
                    
                    <div class="metric-card text-white p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">安全评分</p>
                                <p id="security-score" class="text-3xl font-bold">--</p>
                            </div>
                            <i class="fas fa-shield-alt text-4xl text-blue-200"></i>
                        </div>
                    </div>
                    
                    <div class="metric-card text-white p-6 rounded-xl">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100 text-sm">性能评分</p>
                                <p id="performance-score" class="text-3xl font-bold">--</p>
                            </div>
                            <i class="fas fa-rocket text-4xl text-blue-200"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 趋势图表 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">构建和测试趋势</h3>
                        <div id="trend-chart" style="height: 300px;"></div>
                    </div>
                    
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">质量指标趋势</h3>
                        <div id="quality-chart" style="height: 300px;"></div>
                    </div>
                </div>
                
                <!-- 最近测试结果 -->
                <div class="chart-container">
                    <h3 class="text-lg font-semibold mb-4">最近测试结果</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full table-auto">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left">测试名称</th>
                                    <th class="px-4 py-2 text-left">类型</th>
                                    <th class="px-4 py-2 text-left">状态</th>
                                    <th class="px-4 py-2 text-left">执行时间</th>
                                    <th class="px-4 py-2 text-left">时间戳</th>
                                </tr>
                            </thead>
                            <tbody id="recent-tests-table">
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- 构建状态页面 -->
            <div id="builds-section" class="p-6 hidden">
                <div class="chart-container">
                    <h3 class="text-lg font-semibold mb-4">构建历史</h3>
                    <div id="builds-chart" style="height: 400px;"></div>
                </div>
            </div>
            
            <!-- 测试结果页面 -->
            <div id="tests-section" class="p-6 hidden">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">E2E测试</h3>
                        <div id="e2e-chart" style="height: 200px;"></div>
                    </div>
                    
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">性能测试</h3>
                        <div id="perf-chart" style="height: 200px;"></div>
                    </div>
                    
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">安全测试</h3>
                        <div id="sec-chart" style="height: 200px;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 质量指标页面 -->
            <div id="quality-section" class="p-6 hidden">
                <div class="chart-container">
                    <h3 class="text-lg font-semibold mb-4">质量指标雷达图</h3>
                    <div id="quality-radar-chart" style="height: 500px;"></div>
                </div>
            </div>
            
            <!-- 安全扫描页面 -->
            <div id="security-section" class="p-6 hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">漏洞分布</h3>
                        <div id="vulnerability-chart" style="height: 300px;"></div>
                    </div>
                    
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">安全趋势</h3>
                        <div id="security-trend-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
            
            <!-- 性能监控页面 -->
            <div id="performance-section" class="p-6 hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">响应时间分布</h3>
                        <div id="response-time-chart" style="height: 300px;"></div>
                    </div>
                    
                    <div class="chart-container">
                        <h3 class="text-lg font-semibold mb-4">资源使用情况</h3>
                        <div id="resource-usage-chart" style="height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- WebSocket连接状态指示器 -->
    <div id="ws-status" class="fixed bottom-4 right-4 px-4 py-2 rounded-lg text-white text-sm hidden">
        <i class="fas fa-wifi mr-2"></i>
        <span id="ws-status-text">连接中...</span>
    </div>
    
    <script>
        // 全局变量
        let currentSection = 'overview';
        let websocket = null;
        let dashboardData = {};
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            loadDashboardData();
            setInterval(updateTimestamp, 1000);
        });
        
        // WebSocket连接
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = function() {
                console.log('WebSocket连接已建立');
                updateWSStatus('connected', '已连接');
            };
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            websocket.onclose = function() {
                console.log('WebSocket连接已断开');
                updateWSStatus('disconnected', '连接断开');
                // 5秒后重连
                setTimeout(initWebSocket, 5000);
            };
            
            websocket.onerror = function(error) {
                console.error('WebSocket错误:', error);
                updateWSStatus('error', '连接错误');
            };
        }
        
        function updateWSStatus(status, text) {
            const statusEl = document.getElementById('ws-status');
            const textEl = document.getElementById('ws-status-text');
            
            statusEl.className = 'fixed bottom-4 right-4 px-4 py-2 rounded-lg text-white text-sm';
            
            switch(status) {
                case 'connected':
                    statusEl.classList.add('bg-green-500');
                    setTimeout(() => statusEl.classList.add('hidden'), 3000);
                    break;
                case 'disconnected':
                    statusEl.classList.add('bg-yellow-500');
                    statusEl.classList.remove('hidden');
                    break;
                case 'error':
                    statusEl.classList.add('bg-red-500');
                    statusEl.classList.remove('hidden');
                    break;
            }
            
            textEl.textContent = text;
        }
        
        function handleWebSocketMessage(data) {
            console.log('收到WebSocket消息:', data);
            
            switch(data.type) {
                case 'collection_complete':
                    showNotification('测试结果收集完成', 'success');
                    loadDashboardData();
                    break;
                case 'collection_error':
                    showNotification('测试结果收集失败: ' + data.error, 'error');
                    break;
            }
        }
        
        // 加载Dashboard数据
        async function loadDashboardData() {
            try {
                const response = await fetch('/api/overview');
                const data = await response.json();
                dashboardData = data;
                
                updateOverviewMetrics(data);
                updateRecentTestsTable(data.recent_results);
                
                // 加载趋势数据
                const trendsResponse = await fetch('/api/trends');
                const trendsData = await trendsResponse.json();
                
                updateTrendCharts(trendsData);
                
            } catch (error) {
                console.error('加载数据失败:', error);
                showNotification('数据加载失败', 'error');
            }
        }
        
        // 更新概览指标
        function updateOverviewMetrics(data) {
            const buildStats = data.build_statistics;
            const testStats = data.test_statistics;
            
            document.getElementById('build-success-rate').textContent = 
                buildStats.success_rate ? buildStats.success_rate.toFixed(1) + '%' : '--';
            
            // 计算总体测试通过率
            let totalTests = 0;
            let passedTests = 0;
            
            Object.values(testStats).forEach(stats => {
                totalTests += stats.total_tests;
                passedTests += stats.passed_tests;
            });
            
            const testPassRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : '--';
            document.getElementById('test-pass-rate').textContent = testPassRate + '%';
            
            // 模拟安全和性能评分
            document.getElementById('security-score').textContent = '85';
            document.getElementById('performance-score').textContent = '92';
        }
        
        // 更新最近测试结果表格
        function updateRecentTestsTable(results) {
            const tbody = document.getElementById('recent-tests-table');
            tbody.innerHTML = '';
            
            results.slice(0, 10).forEach(result => {
                const row = document.createElement('tr');
                row.className = 'border-b hover:bg-gray-50';
                
                const statusClass = {
                    'passed': 'status-success',
                    'failed': 'status-failed',
                    'error': 'status-failed',
                    'skipped': 'status-warning'
                }[result.status] || 'status-warning';
                
                row.innerHTML = `
                    <td class="px-4 py-2">${result.test_name}</td>
                    <td class="px-4 py-2">
                        <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                            ${result.test_type.toUpperCase()}
                        </span>
                    </td>
                    <td class="px-4 py-2">
                        <span class="status-indicator ${statusClass}"></span>
                        ${result.status}
                    </td>
                    <td class="px-4 py-2">${result.duration.toFixed(2)}s</td>
                    <td class="px-4 py-2">${new Date(result.timestamp).toLocaleString()}</td>
                `;
                
                tbody.appendChild(row);
            });
        }
        
        // 更新趋势图表
        function updateTrendCharts(data) {
            // 构建和测试趋势图
            const trendTrace1 = {
                x: data.dates,
                y: data.build_success_rate,
                type: 'scatter',
                mode: 'lines+markers',
                name: '构建成功率',
                line: { color: '#3b82f6' }
            };
            
            const trendTrace2 = {
                x: data.dates,
                y: data.test_pass_rate,
                type: 'scatter',
                mode: 'lines+markers',
                name: '测试通过率',
                line: { color: '#10b981' }
            };
            
            Plotly.newPlot('trend-chart', [trendTrace1, trendTrace2], {
                title: '',
                xaxis: { title: '日期' },
                yaxis: { title: '百分比 (%)' },
                margin: { t: 20 }
            });
            
            // 质量指标趋势图
            const qualityTrace1 = {
                x: data.dates,
                y: data.security_score,
                type: 'scatter',
                mode: 'lines+markers',
                name: '安全评分',
                line: { color: '#f59e0b' }
            };
            
            const qualityTrace2 = {
                x: data.dates,
                y: data.performance_score,
                type: 'scatter',
                mode: 'lines+markers',
                name: '性能评分',
                line: { color: '#8b5cf6' }
            };
            
            Plotly.newPlot('quality-chart', [qualityTrace1, qualityTrace2], {
                title: '',
                xaxis: { title: '日期' },
                yaxis: { title: '评分' },
                margin: { t: 20 }
            });
        }
        
        // 页面切换
        function showSection(sectionName) {
            // 隐藏所有页面
            document.querySelectorAll('[id$="-section"]').forEach(section => {
                section.classList.add('hidden');
            });
            
            // 显示目标页面
            document.getElementById(sectionName + '-section').classList.remove('hidden');
            
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            event.target.closest('.nav-item').classList.add('active');
            
            // 更新页面标题
            const titles = {
                'overview': '概览',
                'builds': '构建状态',
                'tests': '测试结果',
                'quality': '质量指标',
                'security': '安全扫描',
                'performance': '性能监控'
            };
            
            document.getElementById('page-title').textContent = titles[sectionName] || '概览';
            currentSection = sectionName;
        }
        
        // 刷新数据
        function refreshData() {
            loadDashboardData();
            showNotification('数据已刷新', 'success');
        }
        
        // 更新时间戳
        function updateTimestamp() {
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
        }
        
        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
            }`;
            
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${
                        type === 'success' ? 'fa-check-circle' :
                        type === 'error' ? 'fa-exclamation-circle' :
                        type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                    } mr-2"></i>
                    ${message}
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
        
        // 收集测试结果
        async function collectTestResults() {
            const buildId = 'build_' + Date.now();
            
            try {
                const response = await fetch('/api/collect-results', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ build_id: buildId })
                });
                
                const result = await response.json();
                showNotification(result.message, 'success');
                
            } catch (error) {
                console.error('收集测试结果失败:', error);
                showNotification('收集测试结果失败', 'error');
            }
        }
    </script>
</body>
</html>