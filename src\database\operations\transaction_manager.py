"""Transaction management module.

This module provides functionality for managing database transactions,
including transaction creation, commit, rollback, and savepoint operations.
"""

from contextlib import contextmanager
from typing import Any, Generator, Optional

from sqlalchemy.exc import SQLAlchemyError

from src.database.connection import SessionManager


class TransactionManager:
    """Class for managing database transactions."""

    def __init__(self, config):
        """Initialize TransactionManager with configuration.

        Args:
            config: Configuration object containing database settings.
        """
        self.config = config
        self.session_manager = SessionManager(config)

    @contextmanager
    def transaction(self) -> Generator[Any, None, None]:
        """Create a transaction context manager.

        This context manager automatically handles commit and rollback operations.

        Yields:
            Connection: Database connection with active transaction.

        Raises:
            SQLAlchemyError: If any database error occurs.
        """
        with self.session_manager.get_connection() as conn:
            try:
                # Start transaction
                transaction = conn.begin()
                yield conn
                # Commit transaction if no exceptions
                transaction.commit()
            except Exception as e:
                # Rollback transaction on exception
                transaction.rollback()
                raise

    @contextmanager
    def savepoint(self, conn) -> Generator[Any, None, None]:
        """Create a savepoint within an existing transaction.

        Args:
            conn: Active database connection with transaction.

        Yields:
            Savepoint: Database savepoint object.

        Raises:
            SQLAlchemyError: If any database error occurs.
        """
        try:
            # Create savepoint
            savepoint = conn.begin_nested()
            yield savepoint
            # Release savepoint if no exceptions
            savepoint.commit()
        except Exception as e:
            # Rollback to savepoint on exception
            savepoint.rollback()
            raise

    def commit(self, transaction) -> bool:
        """Commit a transaction.

        Args:
            transaction: Active transaction object.

        Returns:
            bool: True if commit successful, False otherwise.
        """
        try:
            transaction.commit()
            return True
        except SQLAlchemyError as e:
            # Log the error
            print(f"Transaction commit error: {str(e)}")
            return False

    def rollback(self, transaction) -> bool:
        """Rollback a transaction.

        Args:
            transaction: Active transaction object.

        Returns:
            bool: True if rollback successful, False otherwise.
        """
        try:
            transaction.rollback()
            return True
        except SQLAlchemyError as e:
            # Log the error
            print(f"Transaction rollback error: {str(e)}")
            return False

    def execute_in_transaction(self, statements, params=None) -> bool:
        """Execute multiple SQL statements in a single transaction.

        Args:
            statements: List of SQL statements to execute.
            params: List of parameters for each statement (optional).

        Returns:
            bool: True if all statements executed successfully, False otherwise.
        """
        with self.transaction() as conn:
            try:
                if params:
                    for stmt, param in zip(statements, params):
                        conn.execute(stmt, param)
                else:
                    for stmt in statements:
                        conn.execute(stmt)
                return True
            except SQLAlchemyError as e:
                # Transaction will be automatically rolled back by context manager
                print(f"Transaction execution error: {str(e)}")
                return False
