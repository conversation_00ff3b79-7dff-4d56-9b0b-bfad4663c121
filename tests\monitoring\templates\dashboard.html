<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connect质量监控仪表板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.2s;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-danger { background-color: #dc3545; }
        .status-info { background-color: #17a2b8; }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .alert-item {
            border-left: 4px solid #dc3545;
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 0 5px 5px 0;
        }
        
        .test-result-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }
        
        .test-result-item:hover {
            background-color: #f8f9fa;
        }
        
        .test-result-item:last-child {
            border-bottom: none;
        }
        
        .real-time-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .sidebar {
            background: #343a40;
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 10px 20px;
            border-radius: 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: #495057;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .quality-gate-status {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .quality-gate-passed {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .quality-gate-failed {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- 实时连接状态指示器 -->
    <div class="real-time-indicator">
        <span id="connectionStatus" class="badge bg-secondary">
            <i class="fas fa-circle pulse"></i> 连接中...
        </span>
    </div>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <div class="text-center mb-4">
                    <h5 class="text-white">Connect</h5>
                    <small class="text-muted">质量监控</small>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="#overview" data-tab="overview">
                            <i class="fas fa-tachometer-alt me-2"></i> 概览
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#tests" data-tab="tests">
                            <i class="fas fa-vial me-2"></i> 测试结果
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#performance" data-tab="performance">
                            <i class="fas fa-chart-line me-2"></i> 性能监控
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#security" data-tab="security">
                            <i class="fas fa-shield-alt me-2"></i> 安全扫描
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#quality" data-tab="quality">
                            <i class="fas fa-award me-2"></i> 质量门禁
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#trends" data-tab="trends">
                            <i class="fas fa-chart-area me-2"></i> 趋势分析
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <!-- 头部 -->
                <div class="dashboard-header text-center">
                    <h1><i class="fas fa-chart-bar me-3"></i>Connect质量监控仪表板</h1>
                    <p class="mb-0">实时监控系统质量指标和测试结果</p>
                </div>
                
                <!-- 概览标签页 -->
                <div id="overview" class="tab-content active">
                    <!-- 质量门禁状态 -->
                    <div id="qualityGateStatus" class="quality-gate-status">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-hourglass-half fa-2x me-3"></i>
                            <div>
                                <h5 class="mb-1">质量门禁检查中...</h5>
                                <small>正在获取最新的质量指标</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 关键指标卡片 -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value text-primary" id="testCoverage">--</div>
                                <div class="metric-label">测试覆盖率</div>
                                <small class="text-muted">目标: ≥80%</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value text-success" id="codeCoverage">--</div>
                                <div class="metric-label">代码覆盖率</div>
                                <small class="text-muted">目标: ≥85%</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value text-warning" id="performanceScore">--</div>
                                <div class="metric-label">性能得分</div>
                                <small class="text-muted">目标: ≥80</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value text-info" id="securityScore">--</div>
                                <div class="metric-label">安全得分</div>
                                <small class="text-muted">目标: ≥80</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 测试摘要 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h5><i class="fas fa-chart-pie me-2"></i>测试结果分布</h5>
                                <div id="testResultsPie"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container">
                                <h5><i class="fas fa-clock me-2"></i>最近测试活动</h5>
                                <div id="recentTestActivity"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 测试结果标签页 -->
                <div id="tests" class="tab-content">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="chart-container">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5><i class="fas fa-list me-2"></i>最新测试结果</h5>
                                    <div>
                                        <select id="testTypeFilter" class="form-select form-select-sm">
                                            <option value="">所有类型</option>
                                            <option value="unit">单元测试</option>
                                            <option value="integration">集成测试</option>
                                            <option value="e2e">端到端测试</option>
                                            <option value="performance">性能测试</option>
                                            <option value="security">安全测试</option>
                                        </select>
                                    </div>
                                </div>
                                <div id="testResultsList" style="max-height: 500px; overflow-y: auto;"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="chart-container">
                                <h5><i class="fas fa-chart-bar me-2"></i>测试类型统计</h5>
                                <div id="testTypeChart"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 性能监控标签页 -->
                <div id="performance" class="tab-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="chart-container">
                                <h5><i class="fas fa-chart-line me-2"></i>性能趋势</h5>
                                <div id="performanceTrends"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="metric-card text-center">
                                <div class="metric-value text-primary" id="avgResponseTime">--</div>
                                <div class="metric-label">平均响应时间 (ms)</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-card text-center">
                                <div class="metric-value text-success" id="throughput">--</div>
                                <div class="metric-label">吞吐量 (req/s)</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metric-card text-center">
                                <div class="metric-value text-warning" id="errorRate">--</div>
                                <div class="metric-label">错误率 (%)</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 安全扫描标签页 -->
                <div id="security" class="tab-content">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="chart-container">
                                <h5><i class="fas fa-shield-alt me-2"></i>安全漏洞分布</h5>
                                <div id="securityVulnerabilities"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="chart-container">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>安全告警</h5>
                                <div id="securityAlerts"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 质量门禁标签页 -->
                <div id="quality" class="tab-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="chart-container">
                                <h5><i class="fas fa-award me-2"></i>质量门禁检查结果</h5>
                                <div id="qualityGateDetails"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 趋势分析标签页 -->
                <div id="trends" class="tab-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="chart-container">
                                <h5><i class="fas fa-chart-area me-2"></i>质量趋势分析</h5>
                                <div id="qualityTrends"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // WebSocket连接
        const socket = io();
        
        // 连接状态管理
        const connectionStatus = document.getElementById('connectionStatus');
        
        socket.on('connect', function() {
            connectionStatus.innerHTML = '<i class="fas fa-circle text-success"></i> 已连接';
            connectionStatus.className = 'badge bg-success';
            console.log('已连接到服务器');
        });
        
        socket.on('disconnect', function() {
            connectionStatus.innerHTML = '<i class="fas fa-circle text-danger"></i> 连接断开';
            connectionStatus.className = 'badge bg-danger';
            console.log('与服务器连接断开');
        });
        
        // 实时测试结果
        socket.on('test_result', function(data) {
            console.log('收到测试结果:', data);
            updateTestResults();
            showNotification('新测试结果', `${data.test_name}: ${data.status}`, data.status === 'passed' ? 'success' : 'danger');
        });
        
        // 质量告警
        socket.on('quality_alert', function(data) {
            console.log('收到质量告警:', data);
            showNotification('质量告警', data.message, 'warning');
            updateQualityGateStatus();
        });
        
        // 标签页切换
        document.querySelectorAll('[data-tab]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 更新导航状态
                document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
                
                // 显示对应内容
                const tabId = this.getAttribute('data-tab');
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.style.display = 'none';
                });
                document.getElementById(tabId).style.display = 'block';
                
                // 加载对应数据
                loadTabData(tabId);
            });
        });
        
        // 加载标签页数据
        function loadTabData(tabId) {
            switch(tabId) {
                case 'overview':
                    loadOverviewData();
                    break;
                case 'tests':
                    loadTestResults();
                    break;
                case 'performance':
                    loadPerformanceData();
                    break;
                case 'security':
                    loadSecurityData();
                    break;
                case 'quality':
                    loadQualityData();
                    break;
                case 'trends':
                    loadTrendsData();
                    break;
            }
        }
        
        // 加载概览数据
        function loadOverviewData() {
            // 加载质量指标
            fetch('/api/quality-metrics?days=1')
                .then(response => response.json())
                .then(data => {
                    if (data.length > 0) {
                        const latest = data[0];
                        document.getElementById('testCoverage').textContent = latest.test_coverage?.toFixed(1) + '%' || '--';
                        document.getElementById('codeCoverage').textContent = latest.code_coverage?.toFixed(1) + '%' || '--';
                        document.getElementById('performanceScore').textContent = latest.performance_score?.toFixed(0) || '--';
                        document.getElementById('securityScore').textContent = latest.security_score?.toFixed(0) || '--';
                    }
                })
                .catch(error => console.error('加载质量指标失败:', error));
            
            // 加载测试摘要
            loadTestSummary();
            updateQualityGateStatus();
        }
        
        // 加载测试摘要
        function loadTestSummary() {
            fetch('/api/test-summary')
                .then(response => response.json())
                .then(data => {
                    // 创建测试结果饼图
                    const pieData = [];
                    const pieLabels = [];
                    const pieValues = [];
                    
                    Object.keys(data).forEach(testType => {
                        const summary = data[testType];
                        pieLabels.push(`${testType} (${summary.total})`);
                        pieValues.push(summary.total);
                    });
                    
                    const pieTrace = {
                        type: 'pie',
                        labels: pieLabels,
                        values: pieValues,
                        hole: 0.4,
                        marker: {
                            colors: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8']
                        }
                    };
                    
                    Plotly.newPlot('testResultsPie', [pieTrace], {
                        title: '测试类型分布',
                        showlegend: true,
                        height: 300
                    });
                })
                .catch(error => console.error('加载测试摘要失败:', error));
        }
        
        // 更新质量门禁状态
        function updateQualityGateStatus() {
            // 这里应该调用质量门禁检查API
            // 暂时使用模拟数据
            const status = document.getElementById('qualityGateStatus');
            status.className = 'quality-gate-status quality-gate-passed';
            status.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle fa-2x me-3 text-success"></i>
                    <div>
                        <h5 class="mb-1">质量门禁: 通过</h5>
                        <small>所有质量检查项均已通过</small>
                    </div>
                </div>
            `;
        }
        
        // 加载测试结果
        function loadTestResults() {
            updateTestResults();
        }
        
        // 更新测试结果
        function updateTestResults() {
            const testType = document.getElementById('testTypeFilter')?.value || '';
            const url = testType ? `/api/test-results?type=${testType}` : '/api/test-results';
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('testResultsList');
                    if (!container) return;
                    
                    container.innerHTML = '';
                    
                    data.slice(0, 50).forEach(result => {
                        const statusClass = result.status === 'passed' ? 'success' : 
                                          result.status === 'failed' ? 'danger' : 
                                          result.status === 'skipped' ? 'warning' : 'secondary';
                        
                        const item = document.createElement('div');
                        item.className = 'test-result-item';
                        item.innerHTML = `
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="status-indicator status-${statusClass === 'success' ? 'success' : statusClass === 'danger' ? 'danger' : 'warning'}"></span>
                                    <strong>${result.test_name}</strong>
                                    <small class="text-muted ms-2">${result.test_type}</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-${statusClass}">${result.status}</span>
                                    <small class="text-muted d-block">${new Date(result.timestamp).toLocaleString()}</small>
                                </div>
                            </div>
                            ${result.error_message ? `<div class="mt-2"><small class="text-danger">${result.error_message}</small></div>` : ''}
                        `;
                        
                        container.appendChild(item);
                    });
                })
                .catch(error => console.error('加载测试结果失败:', error));
        }
        
        // 加载性能数据
        function loadPerformanceData() {
            fetch('/api/performance-trends')
                .then(response => response.json())
                .then(data => {
                    if (data.length === 0) return;
                    
                    const dates = data.map(d => d.date);
                    const avgDurations = data.map(d => d.avg_duration);
                    const maxDurations = data.map(d => d.max_duration);
                    
                    const traces = [
                        {
                            x: dates,
                            y: avgDurations,
                            type: 'scatter',
                            mode: 'lines+markers',
                            name: '平均响应时间',
                            line: { color: '#007bff' }
                        },
                        {
                            x: dates,
                            y: maxDurations,
                            type: 'scatter',
                            mode: 'lines+markers',
                            name: '最大响应时间',
                            line: { color: '#dc3545' }
                        }
                    ];
                    
                    Plotly.newPlot('performanceTrends', traces, {
                        title: '性能趋势 (最近30天)',
                        xaxis: { title: '日期' },
                        yaxis: { title: '响应时间 (ms)' },
                        height: 400
                    });
                    
                    // 更新性能指标
                    const latest = data[data.length - 1];
                    if (latest) {
                        document.getElementById('avgResponseTime').textContent = latest.avg_duration?.toFixed(0) || '--';
                    }
                })
                .catch(error => console.error('加载性能数据失败:', error));
        }
        
        // 加载安全数据
        function loadSecurityData() {
            // 模拟安全数据
            const vulnerabilities = {
                'Critical': 0,
                'High': 1,
                'Medium': 3,
                'Low': 5
            };
            
            const secTrace = {
                x: Object.keys(vulnerabilities),
                y: Object.values(vulnerabilities),
                type: 'bar',
                marker: {
                    color: ['#dc3545', '#fd7e14', '#ffc107', '#28a745']
                }
            };
            
            Plotly.newPlot('securityVulnerabilities', [secTrace], {
                title: '安全漏洞分布',
                xaxis: { title: '严重程度' },
                yaxis: { title: '数量' },
                height: 300
            });
        }
        
        // 加载质量数据
        function loadQualityData() {
            // 模拟质量门禁详情
            const qualityDetails = document.getElementById('qualityGateDetails');
            qualityDetails.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>通过的检查项</h6>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                测试覆盖率
                                <span class="badge bg-success rounded-pill">85.2% ≥ 80%</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                代码覆盖率
                                <span class="badge bg-success rounded-pill">92.1% ≥ 85%</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                安全得分
                                <span class="badge bg-success rounded-pill">95 ≥ 80</span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>需要关注的项目</h6>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                技术债务比率
                                <span class="badge bg-warning rounded-pill">4.8% < 5%</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                缺陷逃逸率
                                <span class="badge bg-info rounded-pill">1.2% < 5%</span>
                            </li>
                        </ul>
                    </div>
                </div>
            `;
        }
        
        // 加载趋势数据
        function loadTrendsData() {
            fetch('/api/trends')
                .then(response => response.json())
                .then(data => {
                    if (data.length === 0) return;
                    
                    // 按测试类型分组
                    const groupedData = {};
                    data.forEach(item => {
                        if (!groupedData[item.test_type]) {
                            groupedData[item.test_type] = { dates: [], passRates: [] };
                        }
                        groupedData[item.test_type].dates.push(item.date);
                        groupedData[item.test_type].passRates.push(item.pass_rate);
                    });
                    
                    const traces = Object.keys(groupedData).map(testType => ({
                        x: groupedData[testType].dates,
                        y: groupedData[testType].passRates,
                        type: 'scatter',
                        mode: 'lines+markers',
                        name: testType
                    }));
                    
                    Plotly.newPlot('qualityTrends', traces, {
                        title: '测试通过率趋势',
                        xaxis: { title: '日期' },
                        yaxis: { title: '通过率 (%)' },
                        height: 400
                    });
                })
                .catch(error => console.error('加载趋势数据失败:', error));
        }
        
        // 显示通知
        function showNotification(title, message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 80px; right: 20px; z-index: 1050; min-width: 300px;';
            notification.innerHTML = `
                <strong>${title}</strong><br>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // 自动移除通知
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }
        
        // 测试类型过滤器事件
        document.addEventListener('DOMContentLoaded', function() {
            const testTypeFilter = document.getElementById('testTypeFilter');
            if (testTypeFilter) {
                testTypeFilter.addEventListener('change', updateTestResults);
            }
        });
        
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.style.display = 'none';
            });
            
            // 显示概览标签页
            document.getElementById('overview').style.display = 'block';
            
            // 加载初始数据
            loadOverviewData();
            
            // 定期刷新数据
            setInterval(() => {
                const activeTab = document.querySelector('.nav-link.active')?.getAttribute('data-tab');
                if (activeTab) {
                    loadTabData(activeTab);
                }
            }, 30000); // 每30秒刷新一次
        });
    </script>
</body>
</html>