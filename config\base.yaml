# Connect项目基础配置
# 与新Pydantic配置系统兼容的统一配置文件

project:
  name: Connect
  version: 1.0.0
  description: 电信数据处理与分析平台

# 数据库配置 - 简化版本，与Pydantic模型兼容
database:
  host: ${DATABASE_HOST:localhost}
  port: ${DATABASE_PORT:5432}
  name: ${DATABASE_NAME:connect}
  user: ${DATABASE_USER:to2}
  password: ${DATABASE_PASSWORD:TO2}
  ssl_mode: prefer
  pool:
    min_size: 5
    max_size: 20
    command_timeout: 60
    server_settings:
      jit: 'off'
  connection:
    retry_attempts: 3
    retry_delay: 1.0
    health_check_interval: 30

# 电信域配置
telecom:
  cdr:
    batch_size: 10000
    table_prefix: cdr_
    validation_enabled: true
    geospatial_enhancement: true
  ep:
    batch_size: 5000
    table_prefix: ep_
    coordinate_validation: true
  kpi:
    calculation_interval: 300
    retention_days: 90
    real_time_enabled: true
    alert_thresholds:
      call_success_rate: 95.0
      signal_strength_min: -110.0
  performance:
    max_memory_usage_mb: 2048
    processing_timeout_seconds: 3600
    parallel_workers: 4

# 日志配置
logging:
  level: INFO
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  handlers:
    console:
      enabled: true
      level: INFO
    file:
      enabled: true
      level: DEBUG
      filename: logs/connect.log
      max_bytes: 10485760
      backup_count: 5

# 安全配置
security:
  jwt:
    algorithm: HS256
    secret_key: ${JWT_SECRET_KEY:}
    access_token_expire_minutes: 30
    refresh_token_expire_days: 7
  password:
    min_length: 8
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_special_chars: true

# 监控配置
monitoring:
  metrics:
    enabled: true
    collection_interval: 60
  health_checks:
    enabled: true
    interval: 30
