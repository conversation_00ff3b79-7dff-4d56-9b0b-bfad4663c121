#!/usr/bin/env python3
"""
Connect测试报告生成器
用于生成各种测试报告，包括功能测试、性能测试、安全测试等报告

作者: Connect质量工程师
日期: 2024
"""

import json
import csv
import xml.etree.ElementTree as ET
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from jinja2 import Template
import base64
from io import BytesIO
import numpy as np
from dataclasses import dataclass, asdict
from enum import Enum

class TestStatus(Enum):
    """测试状态枚举"""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"
    BLOCKED = "blocked"

class TestPriority(Enum):
    """测试优先级枚举"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class TestType(Enum):
    """测试类型枚举"""
    UNIT = "unit"
    INTEGRATION = "integration"
    E2E = "e2e"
    PERFORMANCE = "performance"
    SECURITY = "security"
    API = "api"
    UI = "ui"

@dataclass
class ReportTestCase:
    """测试用例数据类"""
    id: str
    name: str
    description: str
    test_type: TestType
    priority: TestPriority
    status: TestStatus
    duration: float  # 执行时间（秒）
    start_time: datetime
    end_time: datetime
    error_message: Optional[str] = None
    stack_trace: Optional[str] = None
    tags: List[str] = None
    module: str = ""
    file_path: str = ""
    line_number: int = 0
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

@dataclass
class ReportTestSuite:
    """测试套件数据类"""
    name: str
    description: str
    test_cases: List[ReportTestCase]
    start_time: datetime
    end_time: datetime
    environment: str = "test"
    version: str = "1.0.0"
    
    @property
    def total_tests(self) -> int:
        return len(self.test_cases)
    
    @property
    def passed_tests(self) -> int:
        return len([tc for tc in self.test_cases if tc.status == TestStatus.PASSED])
    
    @property
    def failed_tests(self) -> int:
        return len([tc for tc in self.test_cases if tc.status == TestStatus.FAILED])
    
    @property
    def skipped_tests(self) -> int:
        return len([tc for tc in self.test_cases if tc.status == TestStatus.SKIPPED])
    
    @property
    def error_tests(self) -> int:
        return len([tc for tc in self.test_cases if tc.status == TestStatus.ERROR])
    
    @property
    def pass_rate(self) -> float:
        if self.total_tests == 0:
            return 0.0
        return (self.passed_tests / self.total_tests) * 100
    
    @property
    def total_duration(self) -> float:
        return sum(tc.duration for tc in self.test_cases)

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    name: str
    value: float
    unit: str
    threshold: Optional[float] = None
    status: str = "pass"  # pass, warning, fail
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

@dataclass
class SecurityVulnerability:
    """安全漏洞数据类"""
    id: str
    title: str
    description: str
    severity: str  # critical, high, medium, low
    cwe_id: Optional[str] = None
    cvss_score: Optional[float] = None
    location: str = ""
    recommendation: str = ""
    status: str = "open"  # open, fixed, ignored
    found_date: datetime = None
    
    def __post_init__(self):
        if self.found_date is None:
            self.found_date = datetime.now()

class _TestReportGenerator:
    """测试报告生成器主类"""
    
    def __init__(self, output_dir: str = "reports"):
        """初始化报告生成器
        
        Args:
            output_dir: 报告输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置seaborn样式
        sns.set_style("whitegrid")
        sns.set_palette("husl")
    
    def parse_junit_xml(self, xml_file: str) -> ReportTestSuite:
        """解析JUnit XML格式的测试结果
        
        Args:
            xml_file: JUnit XML文件路径
            
        Returns:
            ReportTestSuite对象
        """
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # 解析测试套件信息
        suite_name = root.get('name', 'Unknown')
        suite_time = float(root.get('time', '0'))
        
        test_cases = []
        
        for testcase in root.findall('testcase'):
            case_name = testcase.get('name')
            case_classname = testcase.get('classname', '')
            case_time = float(testcase.get('time', '0'))
            
            # 确定测试状态
            status = TestStatus.PASSED
            error_message = None
            stack_trace = None
            
            failure = testcase.find('failure')
            error = testcase.find('error')
            skipped = testcase.find('skipped')
            
            if failure is not None:
                status = TestStatus.FAILED
                error_message = failure.get('message', '')
                stack_trace = failure.text
            elif error is not None:
                status = TestStatus.ERROR
                error_message = error.get('message', '')
                stack_trace = error.text
            elif skipped is not None:
                status = TestStatus.SKIPPED
                error_message = skipped.get('message', '')
            
            # 创建测试用例
            test_case = ReportTestCase(
                id=f"{case_classname}.{case_name}",
                name=case_name,
                description=f"Test case: {case_name}",
                test_type=TestType.UNIT,  # 默认为单元测试
                priority=TestPriority.MEDIUM,
                status=status,
                duration=case_time,
                start_time=datetime.now() - timedelta(seconds=case_time),
                end_time=datetime.now(),
                error_message=error_message,
                stack_trace=stack_trace,
                module=case_classname
            )
            
            test_cases.append(test_case)
        
        # 创建测试套件
        test_suite = ReportTestSuite(
            name=suite_name,
            description=f"Test suite: {suite_name}",
            test_cases=test_cases,
            start_time=datetime.now() - timedelta(seconds=suite_time),
            end_time=datetime.now()
        )
        
        return test_suite
    
    def parse_pytest_json(self, json_file: str) -> ReportTestSuite:
        """解析pytest JSON格式的测试结果
        
        Args:
            json_file: pytest JSON文件路径
            
        Returns:
            ReportTestSuite对象
        """
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        test_cases = []
        
        for test in data.get('tests', []):
            # 解析测试状态
            outcome = test.get('outcome', 'unknown')
            status_mapping = {
                'passed': TestStatus.PASSED,
                'failed': TestStatus.FAILED,
                'skipped': TestStatus.SKIPPED,
                'error': TestStatus.ERROR
            }
            status = status_mapping.get(outcome, TestStatus.ERROR)
            
            # 解析错误信息
            error_message = None
            stack_trace = None
            if 'call' in test and 'longrepr' in test['call']:
                error_message = test['call']['longrepr']
                stack_trace = test['call'].get('traceback', '')
            
            # 创建测试用例
            test_case = ReportTestCase(
                id=test.get('nodeid', ''),
                name=test.get('name', ''),
                description=test.get('doc', ''),
                test_type=TestType.UNIT,
                priority=TestPriority.MEDIUM,
                status=status,
                duration=test.get('duration', 0),
                start_time=datetime.now(),
                end_time=datetime.now(),
                error_message=error_message,
                stack_trace=stack_trace,
                file_path=test.get('file', ''),
                line_number=test.get('line', 0)
            )
            
            test_cases.append(test_case)
        
        # 创建测试套件
        summary = data.get('summary', {})
        test_suite = ReportTestSuite(
            name="Pytest Test Suite",
            description="Pytest test execution results",
            test_cases=test_cases,
            start_time=datetime.now(),
            end_time=datetime.now()
        )
        
        return test_suite
    
    def generate_html_report(self, 
                           test_suite: ReportTestSuite,
                           performance_metrics: List[PerformanceMetric] = None,
                           security_vulnerabilities: List[SecurityVulnerability] = None,
                           template_file: str = None) -> str:
        """生成HTML格式的测试报告
        
        Args:
            test_suite: 测试套件数据
            performance_metrics: 性能指标列表
            security_vulnerabilities: 安全漏洞列表
            template_file: 自定义模板文件路径
            
        Returns:
            生成的HTML报告文件路径
        """
        if performance_metrics is None:
            performance_metrics = []
        if security_vulnerabilities is None:
            security_vulnerabilities = []
        
        # 生成图表
        charts = self._generate_charts(test_suite, performance_metrics)
        
        # 准备模板数据
        template_data = {
            'test_suite': test_suite,
            'performance_metrics': performance_metrics,
            'security_vulnerabilities': security_vulnerabilities,
            'charts': charts,
            'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'summary': self._generate_summary(test_suite, performance_metrics, security_vulnerabilities)
        }
        
        # 使用默认模板或自定义模板
        if template_file and Path(template_file).exists():
            with open(template_file, 'r', encoding='utf-8') as f:
                template_content = f.read()
        else:
            template_content = self._get_default_html_template()
        
        template = Template(template_content)
        html_content = template.render(**template_data)
        
        # 保存HTML报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        html_file = self.output_dir / f"test_report_{timestamp}.html"
        
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return str(html_file)
    
    def generate_pdf_report(self, test_suite: ReportTestSuite) -> str:
        """生成PDF格式的测试报告
        
        Args:
            test_suite: 测试套件数据
            
        Returns:
            生成的PDF报告文件路径
        """
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
        except ImportError:
            raise ImportError("需要安装reportlab库: pip install reportlab")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        pdf_file = self.output_dir / f"test_report_{timestamp}.pdf"
        
        # 创建PDF文档
        doc = SimpleDocTemplate(str(pdf_file), pagesize=A4)
        story = []
        
        # 获取样式
        styles = getSampleStyleSheet()
        title_style = styles['Title']
        heading_style = styles['Heading1']
        normal_style = styles['Normal']
        
        # 标题
        story.append(Paragraph("Connect测试报告", title_style))
        story.append(Spacer(1, 12))
        
        # 测试概要
        story.append(Paragraph("测试概要", heading_style))
        summary_data = [
            ['测试套件', test_suite.name],
            ['总测试数', str(test_suite.total_tests)],
            ['通过数', str(test_suite.passed_tests)],
            ['失败数', str(test_suite.failed_tests)],
            ['跳过数', str(test_suite.skipped_tests)],
            ['通过率', f"{test_suite.pass_rate:.2f}%"],
            ['总耗时', f"{test_suite.total_duration:.2f}秒"]
        ]
        
        summary_table = Table(summary_data)
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 12))
        
        # 失败测试详情
        if test_suite.failed_tests > 0:
            story.append(Paragraph("失败测试详情", heading_style))
            
            failed_cases = [tc for tc in test_suite.test_cases if tc.status == TestStatus.FAILED]
            for case in failed_cases:
                story.append(Paragraph(f"测试: {case.name}", styles['Heading2']))
                story.append(Paragraph(f"错误: {case.error_message or 'Unknown error'}", normal_style))
                story.append(Spacer(1, 6))
        
        # 构建PDF
        doc.build(story)
        
        return str(pdf_file)
    
    def generate_excel_report(self, 
                            test_suite: ReportTestSuite,
                            performance_metrics: List[PerformanceMetric] = None) -> str:
        """生成Excel格式的测试报告
        
        Args:
            test_suite: 测试套件数据
            performance_metrics: 性能指标列表
            
        Returns:
            生成的Excel报告文件路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        excel_file = self.output_dir / f"test_report_{timestamp}.xlsx"
        
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            # 测试概要
            summary_data = {
                '指标': ['测试套件', '总测试数', '通过数', '失败数', '跳过数', '错误数', '通过率(%)', '总耗时(秒)'],
                '值': [
                    test_suite.name,
                    test_suite.total_tests,
                    test_suite.passed_tests,
                    test_suite.failed_tests,
                    test_suite.skipped_tests,
                    test_suite.error_tests,
                    round(test_suite.pass_rate, 2),
                    round(test_suite.total_duration, 2)
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='测试概要', index=False)
            
            # 测试用例详情
            cases_data = []
            for case in test_suite.test_cases:
                cases_data.append({
                    '测试ID': case.id,
                    '测试名称': case.name,
                    '测试类型': case.test_type.value,
                    '优先级': case.priority.value,
                    '状态': case.status.value,
                    '耗时(秒)': case.duration,
                    '模块': case.module,
                    '错误信息': case.error_message or ''
                })
            
            cases_df = pd.DataFrame(cases_data)
            cases_df.to_excel(writer, sheet_name='测试用例', index=False)
            
            # 性能指标
            if performance_metrics:
                perf_data = []
                for metric in performance_metrics:
                    perf_data.append({
                        '指标名称': metric.name,
                        '值': metric.value,
                        '单位': metric.unit,
                        '阈值': metric.threshold or '',
                        '状态': metric.status,
                        '时间': metric.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                    })
                
                perf_df = pd.DataFrame(perf_data)
                perf_df.to_excel(writer, sheet_name='性能指标', index=False)
        
        return str(excel_file)
    
    def generate_json_report(self, 
                           test_suite: ReportTestSuite,
                           performance_metrics: List[PerformanceMetric] = None,
                           security_vulnerabilities: List[SecurityVulnerability] = None) -> str:
        """生成JSON格式的测试报告
        
        Args:
            test_suite: 测试套件数据
            performance_metrics: 性能指标列表
            security_vulnerabilities: 安全漏洞列表
            
        Returns:
            生成的JSON报告文件路径
        """
        if performance_metrics is None:
            performance_metrics = []
        if security_vulnerabilities is None:
            security_vulnerabilities = []
        
        # 转换数据为字典格式
        report_data = {
            'metadata': {
                'generation_time': datetime.now().isoformat(),
                'generator': 'Connect Test Report Generator',
                'version': '1.0.0'
            },
            'test_suite': {
                'name': test_suite.name,
                'description': test_suite.description,
                'environment': test_suite.environment,
                'version': test_suite.version,
                'start_time': test_suite.start_time.isoformat(),
                'end_time': test_suite.end_time.isoformat(),
                'summary': {
                    'total_tests': test_suite.total_tests,
                    'passed_tests': test_suite.passed_tests,
                    'failed_tests': test_suite.failed_tests,
                    'skipped_tests': test_suite.skipped_tests,
                    'error_tests': test_suite.error_tests,
                    'pass_rate': test_suite.pass_rate,
                    'total_duration': test_suite.total_duration
                },
                'test_cases': []
            },
            'performance_metrics': [],
            'security_vulnerabilities': []
        }
        
        # 添加测试用例
        for case in test_suite.test_cases:
            case_data = asdict(case)
            case_data['start_time'] = case.start_time.isoformat()
            case_data['end_time'] = case.end_time.isoformat()
            case_data['test_type'] = case.test_type.value
            case_data['priority'] = case.priority.value
            case_data['status'] = case.status.value
            report_data['test_suite']['test_cases'].append(case_data)
        
        # 添加性能指标
        for metric in performance_metrics:
            metric_data = asdict(metric)
            metric_data['timestamp'] = metric.timestamp.isoformat()
            report_data['performance_metrics'].append(metric_data)
        
        # 添加安全漏洞
        for vuln in security_vulnerabilities:
            vuln_data = asdict(vuln)
            vuln_data['found_date'] = vuln.found_date.isoformat()
            report_data['security_vulnerabilities'].append(vuln_data)
        
        # 保存JSON报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        json_file = self.output_dir / f"test_report_{timestamp}.json"
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        return str(json_file)
    
    def _generate_charts(self, 
                        test_suite: ReportTestSuite,
                        performance_metrics: List[PerformanceMetric]) -> Dict[str, str]:
        """生成图表并返回base64编码的图片
        
        Args:
            test_suite: 测试套件数据
            performance_metrics: 性能指标列表
            
        Returns:
            图表字典，键为图表名称，值为base64编码的图片
        """
        charts = {}
        
        # 测试结果饼图
        fig, ax = plt.subplots(figsize=(8, 6))
        labels = ['通过', '失败', '跳过', '错误']
        sizes = [test_suite.passed_tests, test_suite.failed_tests, 
                test_suite.skipped_tests, test_suite.error_tests]
        colors = ['#2ecc71', '#e74c3c', '#f39c12', '#9b59b6']
        
        # 只显示非零的部分
        non_zero_data = [(label, size, color) for label, size, color in zip(labels, sizes, colors) if size > 0]
        if non_zero_data:
            labels, sizes, colors = zip(*non_zero_data)
            ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        
        ax.set_title('测试结果分布', fontsize=14, fontweight='bold')
        charts['test_results_pie'] = self._fig_to_base64(fig)
        plt.close(fig)
        
        # 测试类型分布柱状图
        if test_suite.test_cases:
            fig, ax = plt.subplots(figsize=(10, 6))
            type_counts = {}
            for case in test_suite.test_cases:
                test_type = case.test_type.value
                type_counts[test_type] = type_counts.get(test_type, 0) + 1
            
            types = list(type_counts.keys())
            counts = list(type_counts.values())
            
            bars = ax.bar(types, counts, color='#3498db')
            ax.set_title('测试类型分布', fontsize=14, fontweight='bold')
            ax.set_xlabel('测试类型')
            ax.set_ylabel('测试数量')
            
            # 在柱状图上显示数值
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{int(height)}', ha='center', va='bottom')
            
            plt.xticks(rotation=45)
            plt.tight_layout()
            charts['test_types_bar'] = self._fig_to_base64(fig)
            plt.close(fig)
        
        # 性能指标趋势图
        if performance_metrics:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            # 按指标名称分组
            metric_groups = {}
            for metric in performance_metrics:
                if metric.name not in metric_groups:
                    metric_groups[metric.name] = []
                metric_groups[metric.name].append(metric)
            
            # 绘制每个指标的趋势
            for metric_name, metrics in metric_groups.items():
                timestamps = [m.timestamp for m in metrics]
                values = [m.value for m in metrics]
                ax.plot(timestamps, values, marker='o', label=metric_name)
            
            ax.set_title('性能指标趋势', fontsize=14, fontweight='bold')
            ax.set_xlabel('时间')
            ax.set_ylabel('指标值')
            ax.legend()
            plt.xticks(rotation=45)
            plt.tight_layout()
            charts['performance_trend'] = self._fig_to_base64(fig)
            plt.close(fig)
        
        return charts
    
    def _fig_to_base64(self, fig) -> str:
        """将matplotlib图表转换为base64编码的字符串
        
        Args:
            fig: matplotlib图表对象
            
        Returns:
            base64编码的图片字符串
        """
        buffer = BytesIO()
        fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        buffer.close()
        return f"data:image/png;base64,{image_base64}"
    
    def _generate_summary(self, 
                         test_suite: ReportTestSuite,
                         performance_metrics: List[PerformanceMetric],
                         security_vulnerabilities: List[SecurityVulnerability]) -> Dict[str, Any]:
        """生成测试总结
        
        Args:
            test_suite: 测试套件数据
            performance_metrics: 性能指标列表
            security_vulnerabilities: 安全漏洞列表
            
        Returns:
            测试总结字典
        """
        summary = {
            'overall_status': 'PASS' if test_suite.failed_tests == 0 and test_suite.error_tests == 0 else 'FAIL',
            'quality_score': self._calculate_quality_score(test_suite, performance_metrics, security_vulnerabilities),
            'recommendations': self._generate_recommendations(test_suite, performance_metrics, security_vulnerabilities),
            'risk_assessment': self._assess_risks(test_suite, security_vulnerabilities)
        }
        
        return summary
    
    def _calculate_quality_score(self, 
                               test_suite: ReportTestSuite,
                               performance_metrics: List[PerformanceMetric],
                               security_vulnerabilities: List[SecurityVulnerability]) -> float:
        """计算质量分数
        
        Args:
            test_suite: 测试套件数据
            performance_metrics: 性能指标列表
            security_vulnerabilities: 安全漏洞列表
            
        Returns:
            质量分数（0-100）
        """
        # 基础分数基于测试通过率
        base_score = test_suite.pass_rate
        
        # 性能指标扣分
        perf_penalty = 0
        if performance_metrics:
            failed_metrics = [m for m in performance_metrics if m.status == 'fail']
            perf_penalty = len(failed_metrics) * 5  # 每个失败指标扣5分
        
        # 安全漏洞扣分
        security_penalty = 0
        if security_vulnerabilities:
            for vuln in security_vulnerabilities:
                if vuln.severity == 'critical':
                    security_penalty += 20
                elif vuln.severity == 'high':
                    security_penalty += 10
                elif vuln.severity == 'medium':
                    security_penalty += 5
                elif vuln.severity == 'low':
                    security_penalty += 2
        
        final_score = max(0, base_score - perf_penalty - security_penalty)
        return round(final_score, 2)
    
    def _generate_recommendations(self, 
                                test_suite: ReportTestSuite,
                                performance_metrics: List[PerformanceMetric],
                                security_vulnerabilities: List[SecurityVulnerability]) -> List[str]:
        """生成改进建议
        
        Args:
            test_suite: 测试套件数据
            performance_metrics: 性能指标列表
            security_vulnerabilities: 安全漏洞列表
            
        Returns:
            改进建议列表
        """
        recommendations = []
        
        # 测试相关建议
        if test_suite.failed_tests > 0:
            recommendations.append(f"修复 {test_suite.failed_tests} 个失败的测试用例")
        
        if test_suite.pass_rate < 90:
            recommendations.append("提高测试通过率至90%以上")
        
        # 性能相关建议
        if performance_metrics:
            failed_metrics = [m for m in performance_metrics if m.status == 'fail']
            if failed_metrics:
                recommendations.append(f"优化 {len(failed_metrics)} 个性能指标")
        
        # 安全相关建议
        if security_vulnerabilities:
            critical_vulns = [v for v in security_vulnerabilities if v.severity == 'critical']
            high_vulns = [v for v in security_vulnerabilities if v.severity == 'high']
            
            if critical_vulns:
                recommendations.append(f"立即修复 {len(critical_vulns)} 个严重安全漏洞")
            if high_vulns:
                recommendations.append(f"优先修复 {len(high_vulns)} 个高危安全漏洞")
        
        if not recommendations:
            recommendations.append("所有测试通过，质量良好！")
        
        return recommendations
    
    def _assess_risks(self, 
                     test_suite: ReportTestSuite,
                     security_vulnerabilities: List[SecurityVulnerability]) -> str:
        """评估风险等级
        
        Args:
            test_suite: 测试套件数据
            security_vulnerabilities: 安全漏洞列表
            
        Returns:
            风险等级字符串
        """
        # 检查严重安全漏洞
        critical_vulns = [v for v in security_vulnerabilities if v.severity == 'critical']
        if critical_vulns:
            return "高风险 - 存在严重安全漏洞"
        
        # 检查测试失败率
        if test_suite.pass_rate < 80:
            return "高风险 - 测试通过率过低"
        
        # 检查高危安全漏洞
        high_vulns = [v for v in security_vulnerabilities if v.severity == 'high']
        if high_vulns:
            return "中风险 - 存在高危安全漏洞"
        
        if test_suite.pass_rate < 95:
            return "中风险 - 测试通过率需要改进"
        
        return "低风险 - 质量状况良好"
    
    def _get_default_html_template(self) -> str:
        """获取默认HTML模板
        
        Returns:
            HTML模板字符串
        """
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connect测试报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #7f8c8d;
            margin-top: 10px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .summary-card.pass {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        }
        .summary-card.fail {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 2em;
        }
        .summary-card p {
            margin: 0;
            opacity: 0.9;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2c3e50;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-bottom: 20px;
        }
        .chart-container {
            text-align: center;
            margin: 20px 0;
        }
        .chart-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-case {
            background-color: #f8f9fa;
            border-left: 4px solid #dee2e6;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .test-case.passed {
            border-left-color: #28a745;
        }
        .test-case.failed {
            border-left-color: #dc3545;
        }
        .test-case.skipped {
            border-left-color: #ffc107;
        }
        .test-case.error {
            border-left-color: #6f42c1;
        }
        .vulnerability {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .vulnerability.critical {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .vulnerability.high {
            background-color: #ffeaa7;
            border-color: #fdd835;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
        }
        .recommendations {
            background-color: #e8f5e8;
            border: 1px solid #c3e6c3;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .recommendations ul {
            margin: 0;
            padding-left: 20px;
        }
        .risk-assessment {
            padding: 15px;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
        }
        .risk-low {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .risk-medium {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .risk-high {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Connect测试报告</h1>
            <div class="subtitle">
                测试套件: {{ test_suite.name }}<br>
                生成时间: {{ generation_time }}
            </div>
        </div>
        
        <div class="section">
            <h2>测试概要</h2>
            <div class="summary">
                <div class="summary-card">
                    <h3>{{ test_suite.total_tests }}</h3>
                    <p>总测试数</p>
                </div>
                <div class="summary-card pass">
                    <h3>{{ test_suite.passed_tests }}</h3>
                    <p>通过</p>
                </div>
                <div class="summary-card fail">
                    <h3>{{ test_suite.failed_tests }}</h3>
                    <p>失败</p>
                </div>
                <div class="summary-card">
                    <h3>{{ "%.2f"|format(test_suite.pass_rate) }}%</h3>
                    <p>通过率</p>
                </div>
                <div class="summary-card">
                    <h3>{{ "%.2f"|format(summary.quality_score) }}</h3>
                    <p>质量分数</p>
                </div>
            </div>
        </div>
        
        {% if summary.risk_assessment %}
        <div class="section">
            <h2>风险评估</h2>
            <div class="risk-assessment {% if '高风险' in summary.risk_assessment %}risk-high{% elif '中风险' in summary.risk_assessment %}risk-medium{% else %}risk-low{% endif %}">
                {{ summary.risk_assessment }}
            </div>
        </div>
        {% endif %}
        
        {% if summary.recommendations %}
        <div class="section">
            <h2>改进建议</h2>
            <div class="recommendations">
                <ul>
                {% for recommendation in summary.recommendations %}
                    <li>{{ recommendation }}</li>
                {% endfor %}
                </ul>
            </div>
        </div>
        {% endif %}
        
        {% if charts %}
        <div class="section">
            <h2>测试结果图表</h2>
            {% if charts.test_results_pie %}
            <div class="chart-container">
                <h3>测试结果分布</h3>
                <img src="{{ charts.test_results_pie }}" alt="测试结果饼图">
            </div>
            {% endif %}
            
            {% if charts.test_types_bar %}
            <div class="chart-container">
                <h3>测试类型分布</h3>
                <img src="{{ charts.test_types_bar }}" alt="测试类型柱状图">
            </div>
            {% endif %}
            
            {% if charts.performance_trend %}
            <div class="chart-container">
                <h3>性能指标趋势</h3>
                <img src="{{ charts.performance_trend }}" alt="性能趋势图">
            </div>
            {% endif %}
        </div>
        {% endif %}
        
        {% if test_suite.failed_tests > 0 %}
        <div class="section">
            <h2>失败测试详情</h2>
            {% for case in test_suite.test_cases %}
                {% if case.status.value == 'failed' %}
                <div class="test-case failed">
                    <h4>{{ case.name }}</h4>
                    <p><strong>模块:</strong> {{ case.module }}</p>
                    <p><strong>错误信息:</strong> {{ case.error_message or '无' }}</p>
                    <p><strong>耗时:</strong> {{ "%.2f"|format(case.duration) }}秒</p>
                </div>
                {% endif %}
            {% endfor %}
        </div>
        {% endif %}
        
        {% if security_vulnerabilities %}
        <div class="section">
            <h2>安全漏洞</h2>
            {% for vuln in security_vulnerabilities %}
            <div class="vulnerability {{ vuln.severity }}">
                <h4>{{ vuln.title }}</h4>
                <p><strong>严重程度:</strong> {{ vuln.severity }}</p>
                <p><strong>描述:</strong> {{ vuln.description }}</p>
                {% if vuln.recommendation %}
                <p><strong>建议:</strong> {{ vuln.recommendation }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="footer">
            <p>报告由Connect质量工程师生成 | {{ generation_time }}</p>
        </div>
    </div>
</body>
</html>
        """

def main():
    """主函数，用于命令行调用"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Connect测试报告生成器')
    parser.add_argument('--input', required=True, help='输入文件路径（JUnit XML或pytest JSON）')
    parser.add_argument('--format', choices=['html', 'pdf', 'excel', 'json'], 
                       default='html', help='输出格式')
    parser.add_argument('--output', default='reports', help='输出目录')
    parser.add_argument('--template', help='自定义HTML模板文件路径')
    
    args = parser.parse_args()
    
    generator = TestReportGenerator(args.output)
    
    # 解析输入文件
    input_path = Path(args.input)
    if input_path.suffix.lower() == '.xml':
        test_suite = generator.parse_junit_xml(args.input)
    elif input_path.suffix.lower() == '.json':
        test_suite = generator.parse_pytest_json(args.input)
    else:
        raise ValueError(f"不支持的文件格式: {input_path.suffix}")
    
    # 生成报告
    if args.format == 'html':
        report_file = generator.generate_html_report(test_suite, template_file=args.template)
    elif args.format == 'pdf':
        report_file = generator.generate_pdf_report(test_suite)
    elif args.format == 'excel':
        report_file = generator.generate_excel_report(test_suite)
    elif args.format == 'json':
        report_file = generator.generate_json_report(test_suite)
    
    print(f"测试报告已生成: {report_file}")

if __name__ == '__main__':
    main()