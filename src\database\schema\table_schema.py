"""Table schema models for database schema management.

This module provides schema model classes that represent database table structures
with columns, indexes, and validation capabilities.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any


@dataclass
class ColumnSchema:
    """Column schema definition."""
    
    name: str
    data_type: str
    nullable: bool = True
    primary_key: bool = False
    foreign_key: Optional[str] = None
    default: Optional[str] = None
    default_value: Optional[str] = None
    max_length: Optional[int] = None
    unique: bool = False
    
    def __post_init__(self):
        """Handle backward compatibility for default parameter and validation."""
        if self.default is not None and self.default_value is None:
            self.default_value = self.default
            
        # Validate name is not empty
        if not self.name or not self.name.strip():
            from pydantic_core import ValidationError
            raise ValidationError.from_exception_data(
                "ColumnSchema",
                [
                    {
                        "type": "value_error",
                        "loc": ("name",),
                        "msg": "Column name cannot be empty",
                        "input": self.name,
                        "ctx": {"error": "empty_name"}
                    }
                ],
            )


@dataclass
class IndexSchema:
    """Index schema definition."""
    
    name: str
    columns: List[str]
    unique: bool = False
    index_type: str = "btree"


class TableSchema:
    """Table schema definition with columns and indexes."""
    
    def __init__(self, name: str, columns: Optional[List[ColumnSchema]] = None, 
                 schema: Optional[str] = None, indexes: Optional[List[IndexSchema]] = None):
        """Initialize table schema.
        
        Args:
            name: Table name
            columns: List of column schemas
            schema: Schema name (optional)
            indexes: List of index schemas (optional)
        """
        self.name = name
        self.columns = columns or []
        self.schema = schema
        self.indexes = indexes or []
    
    def add_column(self, column: ColumnSchema) -> None:
        """Add a column to the table schema.
        
        Args:
            column: Column schema to add
        """
        self.columns.append(column)
    
    def remove_column(self, column_name: str) -> None:
        """Remove a column from the table schema.
        
        Args:
            column_name: Name of column to remove
        """
        self.columns = [col for col in self.columns if col.name != column_name]
    
    def get_column(self, column_name: str) -> Optional[ColumnSchema]:
        """Get a column by name.
        
        Args:
            column_name: Name of column to get
            
        Returns:
            Column schema if found, None otherwise
        """
        for column in self.columns:
            if column.name == column_name:
                return column
        return None
    
    def has_column(self, column_name: str) -> bool:
        """Check if table has a column.
        
        Args:
            column_name: Name of column to check
            
        Returns:
            True if column exists, False otherwise
        """
        return self.get_column(column_name) is not None
    
    def add_index(self, index: IndexSchema) -> None:
        """Add an index to the table schema.
        
        Args:
            index: Index schema to add
        """
        self.indexes.append(index)
    
    def remove_index(self, index_name: str) -> None:
        """Remove an index from the table schema.
        
        Args:
            index_name: Name of index to remove
        """
        self.indexes = [idx for idx in self.indexes if idx.name != index_name]
    
    def has_index(self, index_name: str) -> bool:
        """Check if table has an index.
        
        Args:
            index_name: Name of index to check
            
        Returns:
            True if index exists, False otherwise
        """
        for index in self.indexes:
            if index.name == index_name:
                return True
        return False
    
    def is_valid(self) -> bool:
        """Validate table schema.
        
        Returns:
            True if schema is valid, False otherwise
        """
        # Check if table has at least one primary key column
        has_primary_key = any(col.primary_key for col in self.columns)
        return has_primary_key
    
    def get_primary_key_columns(self) -> List[ColumnSchema]:
        """Get all primary key columns.
        
        Returns:
            List of primary key columns
        """
        return [col for col in self.columns if col.primary_key]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert table schema to dictionary.
        
        Returns:
            Dictionary representation of table schema
        """
        return {
            'name': self.name,
            'schema': self.schema,
            'columns': [{
                'name': col.name,
                'data_type': col.data_type,
                'nullable': col.nullable,
                'primary_key': col.primary_key,
                'foreign_key': col.foreign_key,
                'default_value': col.default_value,
                'max_length': col.max_length,
                'unique': col.unique
            } for col in self.columns],
            'indexes': [{
                'name': idx.name,
                'columns': idx.columns,
                'unique': idx.unique,
                'index_type': idx.index_type
            } for idx in self.indexes]
        }