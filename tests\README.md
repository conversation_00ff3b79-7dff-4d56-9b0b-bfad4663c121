# Connect 电信数据分析平台 - 测试框架文档

## 📋 概述

本测试框架为Connect电信数据分析与可视化平台提供全面的质量保证体系，包括功能测试、性能测试、安全测试、集成测试和端到端测试。框架遵循测试金字塔原则，确保系统的功能正确性、性能稳定性和安全可靠性。

## 🏗️ 测试架构

```
tests/
├── unit/                    # 单元测试 (70%)
│   ├── test_database.py     # 数据库组件测试
│   ├── test_models.py       # 数据模型测试
│   ├── test_utils.py        # 工具函数测试
│   └── test_validators.py   # 验证器测试
├── integration/             # 集成测试 (20%)
│   ├── test_integration.py  # 组件集成测试
│   ├── test_api.py         # API集成测试
│   └── test_workflows.py   # 工作流集成测试
├── e2e/                    # 端到端测试 (10%)
│   ├── test_e2e_scenarios.py # 业务场景测试
│   ├── test_user_journeys.py # 用户旅程测试
│   └── test_system_flows.py  # 系统流程测试
├── performance/            # 性能测试
│   ├── test_performance.py # 性能基准测试
│   ├── test_load.py        # 负载测试
│   └── test_stress.py      # 压力测试
├── security/               # 安全测试
│   ├── test_security.py    # 安全漏洞测试
│   ├── test_auth.py        # 认证授权测试
│   └── test_compliance.py  # 合规性测试
├── fixtures/               # 测试数据
│   ├── sample_data/        # 样本数据文件
│   └── mock_data/          # 模拟数据
├── conftest.py            # 测试配置和夹具
├── test_settings.py       # 测试设置
├── test_utils.py          # 测试工具函数
└── run_tests.py           # 测试运行器
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装测试依赖
pip install -r requirements-dev.txt

# 设置环境变量
export TEST_DATABASE_URL="postgresql://test_user:test_pass@localhost:5432/test_connect"
export TEST_REDIS_URL="redis://localhost:6379/1"
```

### 2. 运行测试

```bash
# 运行所有测试
python run_tests.py all

# 运行特定类型的测试
python run_tests.py unit          # 单元测试
python run_tests.py integration   # 集成测试
python run_tests.py e2e           # 端到端测试
python run_tests.py performance   # 性能测试
python run_tests.py security      # 安全测试

# 快速测试（跳过慢速测试）
python run_tests.py fast

# 带覆盖率报告
python run_tests.py all --coverage

# 并行执行
python run_tests.py all --parallel

# 详细输出
python run_tests.py all --verbose
```

### 3. 使用pytest直接运行

```bash
# 运行所有测试
pytest

# 运行特定目录
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# 运行特定文件
pytest tests/unit/test_database.py

# 运行特定测试
pytest tests/unit/test_database.py::TestDatabaseConnection::test_connection_success

# 带标记的测试
pytest -m "not slow"           # 跳过慢速测试
pytest -m "security"           # 只运行安全测试
pytest -m "performance"        # 只运行性能测试

# 覆盖率报告
pytest --cov=src --cov-report=html --cov-report=term

# 并行执行
pytest -n auto

# 失败时停止
pytest -x

# 重新运行失败的测试
pytest --lf
```

## 📊 质量标准

### 功能质量指标
- **测试覆盖率**: >85%
- **代码覆盖率**: >90%
- **缺陷密度**: <0.1个/KLOC
- **缺陷逃逸率**: <5%

### 性能质量指标
- **API响应时间**: <500ms
- **页面加载时间**: <3s
- **地理查询响应**: <3s
- **大数据处理**: 500万行<10s
- **并发用户支持**: 20用户

### 安全质量指标
- **安全漏洞**: 0个
- **认证强度**: 强密码+MFA
- **数据加密**: 传输+存储
- **访问控制**: RBAC+最小权限

## 🔧 测试工具和框架

### 核心测试框架
- **pytest**: 主要测试框架
- **pytest-asyncio**: 异步测试支持
- **pytest-cov**: 覆盖率分析
- **pytest-xdist**: 并行测试执行
- **pytest-mock**: 模拟对象

### 性能测试工具
- **locust**: 负载测试
- **memory_profiler**: 内存分析
- **cProfile**: 性能分析
- **pytest-benchmark**: 基准测试

### 安全测试工具
- **bandit**: 代码安全扫描
- **safety**: 依赖库漏洞检测
- **pytest-security**: 安全测试插件

### 数据库测试
- **pytest-postgresql**: PostgreSQL测试
- **fakeredis**: Redis模拟
- **factory_boy**: 测试数据工厂

## 📝 编写测试指南

### 1. 单元测试

```python
import pytest
from tests.test_utils import DatabaseTestHelper, DataTestHelper

class TestDataProcessor:
    """数据处理器单元测试"""
    
    def test_process_ep_data(self):
        """测试EP数据处理"""
        # Arrange
        processor = DataProcessor()
        sample_data = DataTestHelper.create_sample_dataframe("ep", 100)
        
        # Act
        result = processor.process_ep_data(sample_data)
        
        # Assert
        assert result is not None
        DataTestHelper.assert_dataframe_shape(result, (100, 8))
        DataTestHelper.assert_no_null_values(result)
    
    @pytest.mark.parametrize("data_size", [10, 100, 1000])
    def test_process_different_sizes(self, data_size):
        """测试不同数据量的处理"""
        processor = DataProcessor()
        sample_data = DataTestHelper.create_sample_dataframe("cdr", data_size)
        
        result = processor.process_cdr_data(sample_data)
        
        assert len(result) == data_size
```

### 2. 集成测试

```python
import pytest
from tests.test_utils import DatabaseTestHelper, APITestHelper

class TestDataWorkflow:
    """数据工作流集成测试"""
    
    @pytest.mark.asyncio
    async def test_complete_data_pipeline(self, db_session, redis_client):
        """测试完整数据管道"""
        # 数据导入
        importer = DataImporter(db_session)
        import_result = await importer.import_file("test_data.csv")
        assert import_result.success
        
        # 数据处理
        processor = DataProcessor(db_session, redis_client)
        process_result = await processor.process_data(import_result.data_id)
        assert process_result.success
        
        # 数据导出
        exporter = DataExporter(db_session)
        export_result = await exporter.export_data(process_result.data_id)
        assert export_result.success
```

### 3. 性能测试

```python
import pytest
from tests.test_utils import PerformanceTestHelper, QualityTestHelper

class TestPerformance:
    """性能测试"""
    
    @pytest.mark.performance
    def test_large_data_processing_performance(self):
        """测试大数据处理性能"""
        # 生成测试数据
        test_data = QualityTestHelper.generate_load_test_data(5000000, "cdr")
        
        # 性能测试
        with PerformanceTestHelper.measure_time() as timer:
            processor = DataProcessor()
            result = processor.process_bulk_data(test_data)
        
        # 性能断言
        PerformanceTestHelper.assert_execution_time(timer.elapsed, max_seconds=10)
        assert len(result) == 5000000
    
    @pytest.mark.benchmark
    def test_query_performance(self, benchmark):
        """基准测试查询性能"""
        def query_function():
            return database.execute_complex_query()
        
        result = benchmark(query_function)
        assert result is not None
```

### 4. 安全测试

```python
import pytest
from tests.test_utils import SecurityTestHelper, APITestHelper

class TestSecurity:
    """安全测试"""
    
    @pytest.mark.security
    def test_sql_injection_prevention(self):
        """测试SQL注入防护"""
        payloads = SecurityTestHelper.generate_sql_injection_payloads()
        
        for payload in payloads:
            with pytest.raises(SecurityError):
                database.execute_user_query(payload)
    
    @pytest.mark.security
    def test_password_strength_validation(self):
        """测试密码强度验证"""
        passwords = SecurityTestHelper.generate_test_passwords()
        
        # 弱密码应该被拒绝
        with pytest.raises(ValidationError):
            auth_service.validate_password(passwords["weak"])
        
        # 强密码应该被接受
        auth_service.validate_password(passwords["strong"])
        SecurityTestHelper.assert_password_strength(passwords["strong"], "strong")
```

## 🎯 测试标记 (Markers)

```python
# 在pytest.ini中定义的标记
markers =
    slow: 标记慢速测试
    fast: 标记快速测试
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    performance: 性能测试
    security: 安全测试
    database: 需要数据库的测试
    redis: 需要Redis的测试
    geospatial: 地理空间测试
    api: API测试
    auth: 认证测试
    compliance: 合规性测试
```

使用示例：
```python
@pytest.mark.slow
@pytest.mark.performance
def test_large_dataset_processing():
    pass

@pytest.mark.security
@pytest.mark.auth
def test_authentication_security():
    pass
```

## 📈 持续集成

### GitHub Actions工作流

测试在每次推送和PR时自动运行：

```yaml
# .github/workflows/tests.yml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          pip install -r requirements-dev.txt
      - name: Run tests
        run: |
          python run_tests.py all --coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

### 测试阶段

1. **快速测试**: 单元测试和快速集成测试
2. **完整测试**: 所有测试类型
3. **性能测试**: 性能基准和负载测试
4. **安全扫描**: 安全漏洞和合规性检查

## 🔍 测试数据管理

### 测试数据策略

1. **隔离性**: 每个测试使用独立的数据
2. **可重复性**: 测试数据可重复生成
3. **真实性**: 模拟真实业务数据
4. **安全性**: 不包含敏感信息

### 数据工厂

```python
from tests.test_utils import DataTestHelper, QualityTestHelper

# 生成EP数据
ep_data = QualityTestHelper.generate_load_test_data(1000, "ep")

# 生成CDR数据
cdr_data = QualityTestHelper.generate_load_test_data(5000, "cdr")

# 创建DataFrame
df = DataTestHelper.create_sample_dataframe("ep", 100)
```

## 🚨 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库服务
   pg_isready -h localhost -p 5432
   
   # 检查环境变量
   echo $TEST_DATABASE_URL
   ```

2. **Redis连接失败**
   ```bash
   # 检查Redis服务
   redis-cli ping
   
   # 检查环境变量
   echo $TEST_REDIS_URL
   ```

3. **测试超时**
   ```bash
   # 增加超时时间
   pytest --timeout=300
   
   # 跳过慢速测试
   pytest -m "not slow"
   ```

4. **内存不足**
   ```bash
   # 减少并行进程
   pytest -n 2
   
   # 运行特定测试
   pytest tests/unit/
   ```

### 调试技巧

```python
# 使用pytest调试
pytest --pdb                    # 失败时进入调试器
pytest --pdb-trace             # 每个测试都进入调试器
pytest -s                      # 显示print输出
pytest -v                      # 详细输出
pytest --tb=short              # 简短的错误信息

# 使用日志调试
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📚 最佳实践

### 1. 测试命名
- 使用描述性的测试名称
- 遵循 `test_<功能>_<场景>_<期望结果>` 格式
- 使用中文注释说明测试目的

### 2. 测试结构
- 遵循 AAA 模式 (Arrange, Act, Assert)
- 每个测试只验证一个功能点
- 使用参数化测试减少重复代码

### 3. 测试数据
- 使用工厂模式生成测试数据
- 避免硬编码测试数据
- 确保测试数据的隔离性

### 4. 性能考虑
- 标记慢速测试
- 使用模拟对象减少外部依赖
- 合理使用并行测试

### 5. 安全测试
- 定期更新安全测试用例
- 测试所有输入验证
- 验证敏感数据处理

## 📞 支持和贡献

### 获取帮助
- 查看测试文档和示例
- 检查常见问题解答
- 联系质量工程团队

### 贡献指南
1. 添加新测试时更新文档
2. 确保测试覆盖率不降低
3. 遵循代码风格指南
4. 提交前运行完整测试套件

---

**Connect质量工程团队** - 确保系统质量，支撑业务成功 🚀