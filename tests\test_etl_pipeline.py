# -*- coding: utf-8 -*-
"""
Tests for ETL Pipeline Components

This module contains tests for the DataExtractor, DataLoader, and ETLPipeline classes.
"""

import asyncio
import json
import os
import sys
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pandas as pd
import pytest

# Add project root to Python path
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from src.database.connection.pool import DatabasePoolManager as ConnectionPool
from src.database.etl.extractor import DataExtractor
from src.database.etl.loader import DataLoader
from src.database.etl.pipeline import ETLPipeline
# DataCleaner is now integrated into the ETL pipeline module


class TestDataExtractor:
    """Test cases for DataExtractor class."""

    @pytest.fixture
    def extractor(self):
        """Create a DataExtractor instance for testing."""
        return DataExtractor()

    @pytest.fixture
    def sample_csv_file(self):
        """Create a temporary CSV file for testing."""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False) as f:
            f.write("name,age,city\n")
            f.write("Alice,25,New York\n")
            f.write("Bob,30,San Francisco\n")
            f.write("Charlie,35,Chicago\n")
            temp_file = f.name
        # File is now closed, yield the filename
        yield temp_file
        Path(temp_file).unlink(missing_ok=True)

    @pytest.fixture
    def sample_json_file(self):
        """Create a temporary JSON file for testing."""
        data = [
            {"name": "Alice", "age": 25, "city": "New York"},
            {"name": "Bob", "age": 30, "city": "San Francisco"},
            {"name": "Charlie", "age": 35, "city": "Chicago"},
        ]
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            json.dump(data, f)
            temp_file = f.name
        # File is now closed, yield the filename
        yield temp_file
        Path(temp_file).unlink(missing_ok=True)

    @pytest.mark.asyncio
    async def test_extract_csv_file(self, extractor, sample_csv_file):
        """Test extracting data from CSV file."""
        df = await extractor.extract_from_file(sample_csv_file)

        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert list(df.columns) == ["name", "age", "city"]
        assert df.iloc[0]["name"] == "Alice"

    @pytest.mark.asyncio
    async def test_extract_json_file(self, extractor, sample_json_file):
        """Test extracting data from JSON file."""
        df = await extractor.extract_from_file(sample_json_file)

        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert "name" in df.columns
        assert "age" in df.columns
        assert "city" in df.columns

    @pytest.mark.asyncio
    async def test_extract_nonexistent_file(self, extractor):
        """Test extracting from non-existent file raises FileNotFoundError."""
        with pytest.raises(FileNotFoundError):
            await extractor.extract_from_file("nonexistent.csv")

    @pytest.mark.asyncio
    async def test_extract_multiple_files(
        self, extractor, sample_csv_file, sample_json_file
    ):
        """Test extracting data from multiple files."""
        files = [sample_csv_file, sample_json_file]
        results = await extractor.extract_multiple_files(files)

        assert len(results) == 2
        assert sample_csv_file in results
        assert sample_json_file in results
        assert isinstance(results[sample_csv_file], pd.DataFrame)
        assert isinstance(results[sample_json_file], pd.DataFrame)

    def test_detect_file_type(self, extractor):
        """Test file type detection."""
        assert extractor._detect_file_type(Path("test.csv")) == "csv"
        assert extractor._detect_file_type(Path("test.tsv")) == "tsv"
        assert extractor._detect_file_type(Path("test.json")) == "json"
        assert extractor._detect_file_type(Path("test.txt")) == "txt"

    def test_get_file_info(self, extractor, sample_csv_file):
        """Test getting file information."""
        info = extractor.get_file_info(sample_csv_file)

        assert info["exists"] is True
        assert info["size"] > 0
        assert info["extension"] == ".csv"
        assert info["detected_type"] == "csv"
        assert info["is_supported"] is True


class TestDataLoader:
    """Test cases for DataLoader class."""

    @pytest.fixture
    def mock_connection_pool(self):
        """Create a mock connection pool."""
        pool = MagicMock(spec=ConnectionPool)
        mock_conn = AsyncMock()
        pool.acquire_connection = AsyncMock(return_value=mock_conn)
        pool.release_connection = AsyncMock(return_value=None)
        return pool, mock_conn

    @pytest.fixture
    def loader(self, mock_connection_pool):
        """Create a DataLoader instance for testing."""
        pool, _ = mock_connection_pool
        return DataLoader(pool)

    @pytest.fixture
    def sample_dataframe(self):
        """Create a sample DataFrame for testing."""
        return pd.DataFrame(
            {"id": [1, 2, 3], "name": ["Alice", "Bob", "Charlie"], "age": [25, 30, 35]}
        )

    @pytest.mark.asyncio
    async def test_validate_load_requirements(self, loader, sample_dataframe):
        """Test validation of load requirements."""
        with patch.object(
            loader.schema_manager, "schema_exists", return_value=True
        ), patch.object(loader.schema_manager, "table_exists", return_value=False):
            result = await loader.validate_load_requirements(
                sample_dataframe, "test_table", "public"
            )

            assert result["valid"] is True
            assert len(result["errors"]) == 0

    @pytest.mark.asyncio
    async def test_validate_empty_dataframe(self, loader):
        """Test validation with empty DataFrame."""
        empty_df = pd.DataFrame()
        result = await loader.validate_load_requirements(empty_df, "test_table")

        assert "DataFrame is empty" in result["warnings"]

    @pytest.mark.asyncio
    async def test_validate_duplicate_columns(self, loader):
        """Test validation with duplicate column names."""
        df = pd.DataFrame([[1, 2], [3, 4]], columns=["col1", "col1"])
        result = await loader.validate_load_requirements(df, "test_table")

        assert result["valid"] is False
        assert any("duplicate column names" in error for error in result["errors"])


class TestETLPipeline:
    """Test cases for ETLPipeline class."""

    @pytest.fixture
    def mock_connection_pool(self):
        """Create a mock connection pool."""
        pool = MagicMock(spec=ConnectionPool)
        mock_conn = AsyncMock()
        pool.acquire_connection = AsyncMock(return_value=mock_conn)
        pool.release_connection = AsyncMock(return_value=None)
        return pool, mock_conn

    @pytest.fixture
    def pipeline(self, mock_connection_pool):
        """Create an ETLPipeline instance for testing."""
        pool, _ = mock_connection_pool
        return ETLPipeline(pool)

    @pytest.fixture
    def sample_csv_file(self):
        """Create a temporary CSV file for testing."""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False) as f:
            f.write("User Name,Email Address,Age\n")
            f.write("Alice Smith,<EMAIL>,25\n")
            f.write("Bob Jones,<EMAIL>,30\n")
            yield f.name
        Path(f.name).unlink(missing_ok=True)

    @pytest.fixture
    def valid_config(self, sample_csv_file):
        """Create a valid pipeline configuration."""
        return {
            "extract": {"files": [{"path": sample_csv_file}]},
            "transform": {
                "clean_columns": True,
                "add_metadata": True,
                "validate_types": True,
                "null_strategy": "default",
            },
            "load": {
                "mappings": [
                    {
                        "source": sample_csv_file,
                        "table_name": "test_table",
                        "schema_name": "public",
                        "if_exists": "replace",
                        "create_table": True,
                    }
                ],
                "use_transaction": True,
            },
        }

    def test_validate_config_valid(self, pipeline, valid_config):
        """Test validation of valid configuration."""
        # Should not raise an exception
        pipeline._validate_config(valid_config)

    def test_validate_config_missing_extract(self, pipeline):
        """Test validation with missing extract section."""
        config = {"load": {"mappings": [{"table_name": "test"}]}}

        with pytest.raises(
            ValueError, match="Missing required configuration section: extract"
        ):
            pipeline._validate_config(config)

    def test_validate_config_missing_load(self, pipeline):
        """Test validation with missing load section."""
        config = {"extract": {"files": [{"path": "test.csv"}]}}

        with pytest.raises(
            ValueError, match="Missing required configuration section: load"
        ):
            pipeline._validate_config(config)

    def test_validate_config_no_files(self, pipeline):
        """Test validation with no files in extract."""
        config = {
            "extract": {"files": []},
            "load": {"mappings": [{"table_name": "test"}]},
        }

        with pytest.raises(
            ValueError, match="Extract configuration must specify files"
        ):
            pipeline._validate_config(config)

    def test_validate_config_no_mappings(self, pipeline):
        """Test validation with no mappings in load."""
        config = {
            "extract": {"files": [{"path": "test.csv"}]},
            "load": {"mappings": []},
        }

        with pytest.raises(
            ValueError, match="Load configuration must specify mappings"
        ):
            pipeline._validate_config(config)

    def test_validate_config_missing_table_name(self, pipeline):
        """Test validation with missing table_name in mapping."""
        config = {
            "extract": {"files": [{"path": "test.csv"}]},
            "load": {"mappings": [{"schema_name": "public"}]},
        }

        with pytest.raises(
            ValueError, match="Each load mapping must specify table_name"
        ):
            pipeline._validate_config(config)

    @pytest.mark.asyncio
    async def test_validate_pipeline_file_not_found(self, pipeline):
        """Test pipeline validation with non-existent file."""
        config = {
            "extract": {"files": [{"path": "nonexistent.csv"}]},
            "load": {"mappings": [{"table_name": "test"}]},
        }

        result = await pipeline.validate_pipeline(config)

        assert result["valid"] is False
        assert any("File not found" in error for error in result["errors"])

    @pytest.mark.asyncio
    async def test_run_file_pipeline_success(self, pipeline, sample_csv_file):
        """Test running pipeline for a single file."""
        with patch.object(
            pipeline.extractor, "extract_multiple_files", new=AsyncMock()
        ) as mock_extract, patch.object(
            pipeline.transformer, "clean_dataframe"
        ) as mock_transform, patch.object(
            pipeline.loader, "load_dataframe", new=AsyncMock()
        ) as mock_load, patch.object(
            pipeline, "_load_with_transaction", new=AsyncMock()
        ) as mock_load_transaction:
            # Mock successful extraction
            sample_df = pd.DataFrame(
                {
                    "User Name": ["Alice", "Bob"],
                    "Email Address": ["<EMAIL>", "<EMAIL>"],
                    "Age": [25, 30],
                }
            )
            mock_extract.return_value = {sample_csv_file: sample_df}

            # Mock successful transformation
            cleaned_df = pd.DataFrame(
                {
                    "id": [1, 2],
                    "user_name": ["Alice", "Bob"],
                    "email_address": ["<EMAIL>", "<EMAIL>"],
                    "age": [25, 30],
                    "created_at": ["2023-01-01", "2023-01-01"],
                }
            )
            mock_transform.return_value = cleaned_df

            # Mock successful loading
            mock_load.return_value = {
                "success": True,
                "rows_loaded": 2,
                "duration_seconds": 0.5,
            }

            # Mock transaction loading
            mock_load_transaction.return_value = [
                {
                    "source": sample_csv_file,
                    "table": "public.users",
                    "success": True,
                    "rows_loaded": 2,
                    "duration_seconds": 0.5,
                }
            ]

            result = await pipeline.run_file_pipeline(
                file_path=sample_csv_file, table_name="users", schema_name="public"
            )

            assert result["success"] is True
            assert result["status"] == "completed"
            assert "pipeline_id" in result
            assert "duration_seconds" in result

    @pytest.mark.asyncio
    async def test_run_pipeline_extraction_failure(self, pipeline, valid_config):
        """Test pipeline with extraction failure."""
        with patch.object(pipeline.extractor, "extract_multiple_files") as mock_extract:
            # Mock extraction failure
            mock_extract.return_value = {}  # No successful extractions

            result = await pipeline.run_pipeline(valid_config)

            assert result["success"] is False
            assert result["status"] == "failed"
            assert "No files were successfully extracted" in result["error"]

    def test_get_pipeline_status_initial(self, pipeline):
        """Test getting initial pipeline status."""
        status = pipeline.get_pipeline_status()

        assert status["pipeline_id"] is None
        assert status["status"] == "initialized"
        assert status["start_time"] is None
        assert status["end_time"] is None

    @pytest.mark.asyncio
    async def test_cleanup_pipeline(self, pipeline):
        """Test pipeline cleanup."""
        pipeline.pipeline_id = "test_pipeline"
        await pipeline.cleanup_pipeline()

        assert pipeline.status == "cleaned_up"


class TestETLIntegration:
    """Integration tests for the complete ETL process."""

    @pytest.fixture
    def sample_data_files(self):
        """Create multiple sample data files for testing."""
        files = []

        # CSV file
        with tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False) as f:
            f.write("Product Name,Price,Category\n")
            f.write("Laptop,999.99,Electronics\n")
            f.write("Book,19.99,Education\n")
            files.append(f.name)

        # JSON file
        data = [
            {"customer_name": "John Doe", "order_total": 150.00, "status": "completed"},
            {"customer_name": "Jane Smith", "order_total": 75.50, "status": "pending"},
        ]
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            json.dump(data, f)
            files.append(f.name)

        yield files

        # Cleanup
        for file_path in files:
            Path(file_path).unlink(missing_ok=True)

    @pytest.mark.asyncio
    async def test_complete_etl_flow(self, sample_data_files):
        """Test complete ETL flow with data cleaning and transformation."""
        # Create extractor and transformer
        extractor = DataExtractor()
        # Use ETLPipeline's built-in transformer
        pipeline = ETLPipeline()
        transformer = pipeline.transformer

        # Extract data
        extracted_data = await extractor.extract_multiple_files(sample_data_files)

        assert len(extracted_data) == 2

        # Transform data
        for file_path, df in extracted_data.items():
            if df is not None:
                cleaned_df = transformer.clean_dataframe(
                    df, clean_columns=True, add_metadata=True, validate_types=True
                )

                # Verify transformation results
                assert "id" in cleaned_df.columns
                assert "created_at" in cleaned_df.columns

                # Check column name cleaning
                original_columns = df.columns.tolist()
                cleaned_columns = cleaned_df.columns.tolist()

                # Should have more columns due to metadata
                assert len(cleaned_columns) >= len(original_columns)

                # All column names should be lowercase and snake_case
                for col in cleaned_columns:
                    if col not in ["id", "created_at"]:  # Skip metadata columns
                        assert col.islower()
                        assert " " not in col  # No spaces

    @pytest.mark.asyncio
    async def test_data_integrity_through_pipeline(self, sample_data_files):
        """Test that data integrity is maintained through the pipeline."""
        extractor = DataExtractor()
        # Use ETLPipeline's built-in transformer
        pipeline = ETLPipeline()
        transformer = pipeline.transformer

        csv_file = [f for f in sample_data_files if f.endswith(".csv")][0]

        # Extract original data
        original_df = await extractor.extract_from_file(csv_file)
        original_row_count = len(original_df)

        # Transform data
        cleaned_df = transformer.clean_dataframe(
            original_df, clean_columns=True, add_metadata=True, null_strategy="default"
        )

        # Verify row count is preserved
        assert len(cleaned_df) == original_row_count

        # Verify data values are preserved (excluding metadata columns)
        original_data_columns = original_df.columns.tolist()
        for i, orig_col in enumerate(original_data_columns):
            # Find corresponding cleaned column
            cleaned_col = transformer.clean_column_names([orig_col])[0]

            if cleaned_col in cleaned_df.columns:
                # Compare data values (as strings since we convert to TEXT)
                original_values = original_df[orig_col].astype(str).tolist()
                cleaned_values = cleaned_df[cleaned_col].astype(str).tolist()

                assert original_values == cleaned_values
