"""Table Operation Manager Module.

This module implements the TableOperationManager class for handling various strategies
when a table already exists during data loading or creation operations.
"""

import logging
from typing import Any, Dict, List, Optional, Union

import asyncpg
import pandas as pd
from sqlalchemy import MetaData, Table, text

from ..exceptions import DatabaseError, TableExistsError, ValidationError
from ..schema.manager import SchemaManager
from ..utils.security import SQLInjectionGuard
from ..utils.validators import InputValidator
from .crud import CRUDOperations

logger = logging.getLogger(__name__)


class TableOperationManager:
    """Manager for handling table operations when tables already exist.

    This class provides strategies for handling existing tables during data
    loading operations, including replace, append, update, skip, and fail strategies.
    """

    def __init__(
        self,
        session_manager,
        schema_manager: Optional[SchemaManager] = None,
        crud_operations: Optional[CRUDOperations] = None,
    ):
        """Initialize table operation manager.

        Args:
            session_manager: Database session manager instance
            schema_manager: Optional schema manager instance
            crud_operations: Optional CRUD operations instance
        """
        self.session_manager = session_manager
        self.schema_manager = schema_manager or SchemaManager(
            connection_pool=session_manager.pool
        )
        self.crud_operations = crud_operations or CRUDOperations(session_manager)
        self.validator = InputValidator()
        self.security_guard = SQLInjectionGuard()
        self.metadata = MetaData()

    async def handle_table_exists(
        self,
        df: pd.DataFrame,
        table_name: str,
        schema_name: str,
        operation: str = "fail",
        key_columns: Optional[List[str]] = None,
        batch_size: int = 1000,
    ) -> Dict[str, Any]:
        """Handle table existence with specified operation strategy.

        Args:
            df: DataFrame containing data to process
            table_name: Name of the target table
            schema_name: Name of the target schema
            operation: Operation strategy ('replace', 'append', 'update', 'skip', 'fail')
            key_columns: List of key columns for update operations
            batch_size: Number of rows to process in each batch

        Returns:
            Dict containing operation results

        Raises:
            ValidationError: If parameters are invalid
            TableExistsError: If table exists and operation='fail'
            DatabaseError: If operation fails
        """
        # Validate parameters
        await self._validate_parameters(
            df, table_name, schema_name, operation, key_columns
        )

        # Check if table exists
        table_exists = await self.schema_manager.table_exists(table_name, schema_name)

        if not table_exists:
            raise ValidationError(
                f"Table '{schema_name}.{table_name}' does not exist. "
                "Use schema_manager.create_table_from_dataframe() to create it first."
            )

        logger.info(
            f"Handling table '{schema_name}.{table_name}' with operation: {operation}"
        )

        # Execute operation based on strategy
        if operation == "fail":
            return await self._handle_fail(table_name, schema_name)
        elif operation == "replace":
            return await self._handle_replace(df, table_name, schema_name)
        elif operation == "append":
            return await self._handle_append(df, table_name, schema_name, batch_size)
        elif operation == "update":
            return await self._handle_update(
                df, table_name, schema_name, key_columns, batch_size
            )
        elif operation == "skip":
            return await self._handle_skip(table_name, schema_name)
        else:
            raise ValidationError(f"Unsupported operation: {operation}")

    async def _validate_parameters(
        self,
        df: pd.DataFrame,
        table_name: str,
        schema_name: str,
        operation: str,
        key_columns: Optional[List[str]],
    ) -> None:
        """Validate input parameters.

        Args:
            df: DataFrame to validate
            table_name: Table name to validate
            schema_name: Schema name to validate
            operation: Operation to validate
            key_columns: Key columns to validate

        Raises:
            ValidationError: If any parameter is invalid
        """
        # Validate DataFrame
        if df.empty:
            raise ValidationError("DataFrame cannot be empty")

        # Validate identifiers
        if not self.validator.validate_identifier(table_name, "table name")[0]:
            raise ValidationError(f"Invalid table name: {table_name}")
        if not self.validator.validate_identifier(schema_name, "schema name")[0]:
            raise ValidationError(f"Invalid schema name: {schema_name}")

        # Validate operation
        valid_operations = ["fail", "replace", "append", "update", "skip"]
        if operation not in valid_operations:
            raise ValidationError(
                f"Invalid operation '{operation}'. Must be one of: {valid_operations}"
            )

        # Validate key columns for update operation
        if operation == "update":
            if not key_columns:
                raise ValidationError(
                    "key_columns must be provided for 'update' operation"
                )
            if not isinstance(key_columns, list):
                raise ValidationError("key_columns must be a list")

            # Check if key columns exist in DataFrame
            missing_columns = set(key_columns) - set(df.columns)
            if missing_columns:
                raise ValidationError(
                    f"Key columns not found in DataFrame: {missing_columns}"
                )

    async def _handle_fail(self, table_name: str, schema_name: str) -> Dict[str, Any]:
        """Handle fail operation - raise error if table exists.

        Args:
            table_name: Name of the table
            schema_name: Name of the schema

        Returns:
            Dict containing operation results

        Raises:
            TableExistsError: Always raised for fail operation
        """
        raise TableExistsError(
            f"Table '{schema_name}.{table_name}' already exists and operation is 'fail'"
        )

    async def _handle_replace(
        self, df: pd.DataFrame, table_name: str, schema_name: str
    ) -> Dict[str, Any]:
        """Handle replace operation - drop and recreate table.

        Args:
            df: DataFrame containing new data
            table_name: Name of the table
            schema_name: Name of the schema

        Returns:
            Dict containing operation results
        """
        try:
            # Drop existing table
            async with self.session_manager as conn:
                drop_sql = f'DROP TABLE IF EXISTS "{schema_name}"."{table_name}"'
                await conn.execute(drop_sql)
                logger.info(f"Dropped table '{schema_name}.{table_name}'")

            # Create new table from DataFrame
            await self.schema_manager.create_table_from_dataframe(
                df=df,
                table_name=table_name,
                schema_name=schema_name,
                if_exists="fail",  # Should not exist after drop
            )

            # Insert data using bulk insert
            data_list = df.to_dict("records")
            table = await self.crud_operations._get_table_metadata(
                table_name, schema_name
            )
            inserted_records = await self.crud_operations.bulk_insert(
                table=table, data_list=data_list
            )

            logger.info(
                f"Replaced table '{schema_name}.{table_name}' with {len(inserted_records)} records"
            )

            return {
                "success": True,
                "operation": "replace",
                "table_name": table_name,
                "schema_name": schema_name,
                "rows_affected": len(inserted_records),
                "table_recreated": True,
                "message": f"Table replaced successfully with {len(inserted_records)} records",
            }

        except Exception as e:
            logger.error(f"Failed to replace table '{schema_name}.{table_name}': {e}")
            raise DatabaseError(f"Replace operation failed: {e}") from e

    async def _handle_append(
        self, df: pd.DataFrame, table_name: str, schema_name: str, batch_size: int
    ) -> Dict[str, Any]:
        """Handle append operation - insert new data into existing table.

        Args:
            df: DataFrame containing data to append
            table_name: Name of the table
            schema_name: Name of the schema
            batch_size: Number of rows to insert in each batch

        Returns:
            Dict containing operation results
        """
        try:
            # Get table metadata
            table = await self.crud_operations._get_table_metadata(
                table_name, schema_name
            )

            # Validate DataFrame columns against table structure
            await self._validate_dataframe_columns(df, table)

            # Convert DataFrame to list of dictionaries
            data_list = df.to_dict("records")

            # Process in batches
            total_inserted = 0
            for i in range(0, len(data_list), batch_size):
                batch = data_list[i : i + batch_size]
                inserted_records = await self.crud_operations.bulk_insert(
                    table=table, data_list=batch
                )
                total_inserted += len(inserted_records)
                logger.debug(
                    f"Inserted batch {i//batch_size + 1}: {len(inserted_records)} records"
                )

            logger.info(
                f"Appended {total_inserted} records to table '{schema_name}.{table_name}'"
            )

            return {
                "success": True,
                "operation": "append",
                "table_name": table_name,
                "schema_name": schema_name,
                "rows_affected": total_inserted,
                "batches_processed": (len(data_list) + batch_size - 1) // batch_size,
                "message": f"Successfully appended {total_inserted} records",
            }

        except Exception as e:
            logger.error(f"Failed to append to table '{schema_name}.{table_name}': {e}")
            raise DatabaseError(f"Append operation failed: {e}") from e

    async def _handle_update(
        self,
        df: pd.DataFrame,
        table_name: str,
        schema_name: str,
        key_columns: List[str],
        batch_size: int,
    ) -> Dict[str, Any]:
        """Handle update operation - update existing records, insert new ones.

        This operation uses a staging table approach for better performance
        and data integrity.

        Args:
            df: DataFrame containing data to update/insert
            table_name: Name of the target table
            schema_name: Name of the schema
            key_columns: List of key columns for matching records
            batch_size: Number of rows to process in each batch

        Returns:
            Dict containing operation results
        """
        try:
            staging_table_name = (
                f"{table_name}_staging_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}"
            )

            # Create staging table from DataFrame
            await self.schema_manager.create_table_from_dataframe(
                df=df,
                table_name=staging_table_name,
                schema_name=schema_name,
                if_exists="replace",
            )

            # Insert data into staging table
            staging_table = await self.crud_operations._get_table_metadata(
                staging_table_name, schema_name
            )
            data_list = df.to_dict("records")
            await self.crud_operations.bulk_insert(
                table=staging_table, data_list=data_list
            )

            # Perform upsert operation
            key_columns_str = ", ".join([f'"{col}"' for col in key_columns])
            target_table = f'"{schema_name}"."{table_name}"'
            staging_table_full = f'"{schema_name}"."{staging_table_name}"'

            # Get all columns except key columns for update
            all_columns = list(df.columns)
            update_columns = [col for col in all_columns if col not in key_columns]

            # Build update SET clause
            update_set = ", ".join(
                [f'"{col}" = EXCLUDED."{col}"' for col in update_columns]
            )

            # Build column list for INSERT
            columns_str = ", ".join([f'"{col}"' for col in all_columns])

            # Perform UPSERT using ON CONFLICT with safe identifiers
            # Note: target_table, staging_table_full, columns_str, key_columns_str, update_set
            # should be pre-validated and properly quoted
            upsert_sql = f"""
                INSERT INTO {target_table} ({columns_str})
                SELECT {columns_str} FROM {staging_table_full}
                ON CONFLICT ({key_columns_str})
                DO UPDATE SET {update_set}
            """

            async with self.session_manager as conn:
                # Execute upsert
                result = await conn.execute(upsert_sql)

                # Get affected rows count
                rows_affected = int(result.split()[-1]) if result else len(data_list)

                # Clean up staging table
                await conn.execute(f"DROP TABLE {staging_table_full}")

            logger.info(
                f"Updated/inserted {rows_affected} records in table '{schema_name}.{table_name}'"
            )

            return {
                "success": True,
                "operation": "update",
                "table_name": table_name,
                "schema_name": schema_name,
                "rows_affected": rows_affected,
                "key_columns": key_columns,
                "staging_table_used": staging_table_name,
                "message": f"Successfully updated/inserted {rows_affected} records",
            }

        except Exception as e:
            # Clean up staging table if it exists
            try:
                async with self.session_manager as conn:
                    await conn.execute(
                        f'DROP TABLE IF EXISTS "{schema_name}"."{staging_table_name}"'
                    )
            except Exception as cleanup_error:
                logger.warning(f"Failed to cleanup staging table: {cleanup_error}")

            logger.error(f"Failed to update table '{schema_name}.{table_name}': {e}")
            raise DatabaseError(f"Update operation failed: {e}") from e

    async def _handle_skip(self, table_name: str, schema_name: str) -> Dict[str, Any]:
        """Handle skip operation - do nothing if table exists.

        Args:
            table_name: Name of the table
            schema_name: Name of the schema

        Returns:
            Dict containing operation results
        """
        logger.info(
            f"Skipping operation for existing table '{schema_name}.{table_name}'"
        )

        return {
            "success": True,
            "operation": "skip",
            "table_name": table_name,
            "schema_name": schema_name,
            "rows_affected": 0,
            "message": f"Operation skipped - table '{schema_name}.{table_name}' already exists",
        }

    async def _validate_dataframe_columns(self, df: pd.DataFrame, table: Table) -> None:
        """Validate that DataFrame columns match table structure.

        Args:
            df: DataFrame to validate
            table: SQLAlchemy Table object

        Raises:
            ValidationError: If columns don't match
        """
        df_columns = set(df.columns)
        table_columns = set(table.columns.keys())

        # Check for missing required columns (columns that exist in table but not in DataFrame)
        missing_columns = table_columns - df_columns
        if missing_columns:
            # Filter out columns that have default values or are nullable
            required_missing = []
            for col_name in missing_columns:
                col = table.columns[col_name]
                if col.default is None and not col.nullable:
                    required_missing.append(col_name)

            if required_missing:
                raise ValidationError(
                    f"DataFrame is missing required columns: {required_missing}"
                )

        # Check for extra columns (columns that exist in DataFrame but not in table)
        extra_columns = df_columns - table_columns
        if extra_columns:
            logger.warning(
                f"DataFrame contains extra columns that will be ignored: {extra_columns}"
            )
            # Remove extra columns from DataFrame
            df.drop(columns=list(extra_columns), inplace=True)

    async def get_table_info(self, table_name: str, schema_name: str) -> Dict[str, Any]:
        """Get information about a table.

        Args:
            table_name: Name of the table
            schema_name: Name of the schema

        Returns:
            Dict containing table information

        Raises:
            ValidationError: If table or schema name is invalid
            DatabaseError: If operation fails
        """
        # Validate identifiers
        if not self.validator.validate_identifier(table_name, "table name")[0]:
            raise ValidationError(f"Invalid table name: {table_name}")
        if not self.validator.validate_identifier(schema_name, "schema name")[0]:
            raise ValidationError(f"Invalid schema name: {schema_name}")

        try:
            async with self.session_manager as conn:
                # Get table information
                table_info_query = """
                    SELECT
                        column_name,
                        data_type,
                        is_nullable,
                        column_default,
                        ordinal_position
                    FROM information_schema.columns
                    WHERE table_schema = $1 AND table_name = $2
                    ORDER BY ordinal_position
                """

                columns_info = await conn.fetch(
                    table_info_query, schema_name, table_name
                )

                if not columns_info:
                    raise ValidationError(
                        f"Table '{schema_name}.{table_name}' does not exist"
                    )

                # Get row count
                count_query = f'SELECT COUNT(*) FROM "{schema_name}"."{table_name}"'
                row_count = await conn.fetchval(count_query)

                # Format column information
                columns = []
                for col in columns_info:
                    columns.append(
                        {
                            "name": col["column_name"],
                            "type": col["data_type"],
                            "nullable": col["is_nullable"] == "YES",
                            "default": col["column_default"],
                            "position": col["ordinal_position"],
                        }
                    )

                return {
                    "table_name": table_name,
                    "schema_name": schema_name,
                    "row_count": row_count,
                    "columns": columns,
                    "column_count": len(columns),
                }

        except Exception as e:
            logger.error(
                f"Failed to get table info for '{schema_name}.{table_name}': {e}"
            )
            raise DatabaseError(f"Failed to get table information: {e}") from e
