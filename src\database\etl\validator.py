__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Data validation for ETL operations.

This module provides comprehensive data validation capabilities including
schema validation, data quality checks, and custom validation rules.
"""

import re
from dataclasses import dataclass, field
from datetime import date, datetime
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from loguru import logger

try:
    import jsonschema

    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False
    logger.warning("jsonschema not available. JSON schema validation will be limited.")

# Cerberus dependency removed - using pydantic validation instead
CERBERUS_AVAILABLE = False

from ..exceptions import ValidationError
from ..utils.progress_tracker import ProgressTracker


class ValidationType(Enum):
    """Types of validation."""

    SCHEMA = "schema"
    DATA_TYPE = "data_type"
    RANGE = "range"
    PATTERN = "pattern"
    UNIQUENESS = "uniqueness"
    COMPLETENESS = "completeness"
    CONSISTENCY = "consistency"
    CUSTOM = "custom"
    REFERENTIAL = "referential"
    BUSINESS_RULE = "business_rule"


class ValidationSeverity(Enum):
    """Validation severity levels."""

    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


@dataclass
class ValidationRule:
    """Validation rule definition."""

    name: str
    validation_type: ValidationType
    severity: ValidationSeverity = ValidationSeverity.ERROR
    description: str = ""
    columns: Optional[List[str]] = None
    condition: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    custom_function: Optional[Callable] = None
    enabled: bool = True


@dataclass
class ValidationIssue:
    """Validation issue details."""

    rule_name: str
    validation_type: ValidationType
    severity: ValidationSeverity
    message: str
    column: Optional[str] = None
    row_index: Optional[int] = None
    value: Optional[Any] = None
    expected: Optional[Any] = None
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ValidationResult:
    """Result of validation operation."""

    success: bool
    total_rules: int = 0
    passed_rules: int = 0
    failed_rules: int = 0
    issues: List[ValidationIssue] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)
    execution_time: Optional[float] = None
    data_summary: Dict[str, Any] = field(default_factory=dict)

    @property
    def error_count(self) -> int:
        """Count of error-level issues."""
        return len(
            [
                issue
                for issue in self.issues
                if issue.severity == ValidationSeverity.ERROR
            ]
        )

    @property
    def warning_count(self) -> int:
        """Count of warning-level issues."""
        return len(
            [
                issue
                for issue in self.issues
                if issue.severity == ValidationSeverity.WARNING
            ]
        )

    @property
    def info_count(self) -> int:
        """Count of info-level issues."""
        return len(
            [
                issue
                for issue in self.issues
                if issue.severity == ValidationSeverity.INFO
            ]
        )


class DataValidator:
    """Data validator for ETL operations."""

    def __init__(self):
        """Initialize data validator."""
        self.rules: List[ValidationRule] = []
        self.custom_validators: Dict[str, Callable] = {}

    def add_rule(self, rule: ValidationRule) -> None:
        """Add validation rule.

        Args:
            rule: Validation rule to add
        """
        self.rules.append(rule)
        logger.debug(f"Added validation rule: {rule.name}")

    def add_rules(self, rules: List[ValidationRule]) -> None:
        """Add multiple validation rules.

        Args:
            rules: List of validation rules
        """
        for rule in rules:
            self.add_rule(rule)

    def remove_rule(self, rule_name: str) -> bool:
        """Remove validation rule by name.

        Args:
            rule_name: Name of rule to remove

        Returns:
            True if rule was removed
        """
        for i, rule in enumerate(self.rules):
            if rule.name == rule_name:
                del self.rules[i]
                logger.debug(f"Removed validation rule: {rule_name}")
                return True
        return False

    def get_rule(self, rule_name: str) -> Optional[ValidationRule]:
        """Get validation rule by name.

        Args:
            rule_name: Name of rule to get

        Returns:
            ValidationRule if found
        """
        for rule in self.rules:
            if rule.name == rule_name:
                return rule
        return None

    def register_custom_validator(self, name: str, validator_func: Callable) -> None:
        """Register custom validator function.

        Args:
            name: Name of validator
            validator_func: Validator function
        """
        self.custom_validators[name] = validator_func
        logger.debug(f"Registered custom validator: {name}")

    def validate(
        self, data: pd.DataFrame, progress_tracker: Optional[ProgressTracker] = None
    ) -> ValidationResult:
        """Validate data against all rules.

        Args:
            data: DataFrame to validate
            progress_tracker: Optional progress tracker

        Returns:
            ValidationResult
        """
        try:
            start_time = datetime.now()

            if data.empty:
                logger.warning("Empty DataFrame provided for validation")
                return ValidationResult(
                    success=False,
                    issues=[
                        ValidationIssue(
                            rule_name="data_check",
                            validation_type=ValidationType.COMPLETENESS,
                            severity=ValidationSeverity.ERROR,
                            message="DataFrame is empty",
                        )
                    ],
                )

            enabled_rules = [rule for rule in self.rules if rule.enabled]

            if not enabled_rules:
                logger.warning("No enabled validation rules found")
                return ValidationResult(success=True, total_rules=0, passed_rules=0)

            logger.info(f"Validating data with {len(enabled_rules)} rules")

            if progress_tracker:
                progress_tracker.start_task("Validating data", len(enabled_rules))

            issues = []
            passed_rules = 0
            failed_rules = 0

            for i, rule in enumerate(enabled_rules):
                try:
                    logger.debug(f"Applying rule: {rule.name}")

                    rule_issues = self._apply_rule(data, rule)

                    if rule_issues:
                        issues.extend(rule_issues)
                        failed_rules += 1
                        logger.debug(
                            f"Rule {rule.name} failed with {len(rule_issues)} issues"
                        )
                    else:
                        passed_rules += 1
                        logger.debug(f"Rule {rule.name} passed")

                except Exception as e:
                    logger.error(f"Error applying rule {rule.name}: {e}")
                    issues.append(
                        ValidationIssue(
                            rule_name=rule.name,
                            validation_type=rule.validation_type,
                            severity=ValidationSeverity.ERROR,
                            message=f"Rule execution failed: {e}",
                        )
                    )
                    failed_rules += 1

                if progress_tracker:
                    progress_tracker.update_progress(i + 1)

            if progress_tracker:
                progress_tracker.complete_task()

            execution_time = (datetime.now() - start_time).total_seconds()

            # Calculate statistics
            statistics = self._calculate_statistics(data, issues)
            data_summary = self._generate_data_summary(data)

            # Determine overall success
            error_issues = [
                issue for issue in issues if issue.severity == ValidationSeverity.ERROR
            ]
            success = len(error_issues) == 0

            logger.info(
                f"Validation completed: {passed_rules} passed, {failed_rules} failed"
            )

            return ValidationResult(
                success=success,
                total_rules=len(enabled_rules),
                passed_rules=passed_rules,
                failed_rules=failed_rules,
                issues=issues,
                statistics=statistics,
                execution_time=execution_time,
                data_summary=data_summary,
            )

        except Exception as e:
            logger.error(f"Validation failed: {e}")
            return ValidationResult(
                success=False,
                issues=[
                    ValidationIssue(
                        rule_name="validation_error",
                        validation_type=ValidationType.CUSTOM,
                        severity=ValidationSeverity.ERROR,
                        message=f"Validation process failed: {e}",
                    )
                ],
            )

    def _apply_rule(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Apply single validation rule.

        Args:
            data: DataFrame to validate
            rule: Validation rule to apply

        Returns:
            List of validation issues
        """
        issues = []

        try:
            if rule.validation_type == ValidationType.SCHEMA:
                issues.extend(self._validate_schema(data, rule))
            elif rule.validation_type == ValidationType.DATA_TYPE:
                issues.extend(self._validate_data_type(data, rule))
            elif rule.validation_type == ValidationType.RANGE:
                issues.extend(self._validate_range(data, rule))
            elif rule.validation_type == ValidationType.PATTERN:
                issues.extend(self._validate_pattern(data, rule))
            elif rule.validation_type == ValidationType.UNIQUENESS:
                issues.extend(self._validate_uniqueness(data, rule))
            elif rule.validation_type == ValidationType.COMPLETENESS:
                issues.extend(self._validate_completeness(data, rule))
            elif rule.validation_type == ValidationType.CONSISTENCY:
                issues.extend(self._validate_consistency(data, rule))
            elif rule.validation_type == ValidationType.REFERENTIAL:
                issues.extend(self._validate_referential(data, rule))
            elif rule.validation_type == ValidationType.BUSINESS_RULE:
                issues.extend(self._validate_business_rule(data, rule))
            elif rule.validation_type == ValidationType.CUSTOM:
                issues.extend(self._validate_custom(data, rule))
            else:
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=ValidationSeverity.ERROR,
                        message=f"Unknown validation type: {rule.validation_type}",
                    )
                )

        except Exception as e:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=ValidationSeverity.ERROR,
                    message=f"Rule execution error: {e}",
                )
            )

        return issues

    def _validate_schema(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Validate data schema.

        Args:
            data: DataFrame to validate
            rule: Schema validation rule

        Returns:
            List of validation issues
        """
        issues = []

        schema = rule.parameters.get("schema", {})

        if not schema:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message="No schema provided for validation",
                )
            )
            return issues

        # Check required columns
        required_columns = schema.get("required_columns", [])
        missing_columns = [col for col in required_columns if col not in data.columns]

        for col in missing_columns:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message=f"Required column missing: {col}",
                    column=col,
                )
            )

        # Check column types
        column_types = schema.get("column_types", {})
        for col, expected_type in column_types.items():
            if col in data.columns:
                actual_type = str(data[col].dtype)
                if not self._is_compatible_type(actual_type, expected_type):
                    issues.append(
                        ValidationIssue(
                            rule_name=rule.name,
                            validation_type=rule.validation_type,
                            severity=rule.severity,
                            message=f"Column {col} type mismatch",
                            column=col,
                            expected=expected_type,
                            value=actual_type,
                        )
                    )

        return issues

    def _validate_data_type(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Validate data types.

        Args:
            data: DataFrame to validate
            rule: Data type validation rule

        Returns:
            List of validation issues
        """
        issues = []

        columns = rule.columns or data.columns
        expected_type = rule.parameters.get("expected_type")

        if not expected_type:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message="No expected type specified",
                )
            )
            return issues

        for col in columns:
            if col not in data.columns:
                # Report missing column as an issue
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Required column missing: {col}",
                        column=col,
                    )
                )
                continue

            actual_type = str(data[col].dtype)
            if not self._is_compatible_type(actual_type, expected_type):
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Column {col} has incorrect data type",
                        column=col,
                        expected=expected_type,
                        value=actual_type,
                    )
                )

        return issues

    def _validate_range(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Validate value ranges.

        Args:
            data: DataFrame to validate
            rule: Range validation rule

        Returns:
            List of validation issues
        """
        issues = []

        columns = rule.columns or []
        min_value = rule.parameters.get("min_value")
        max_value = rule.parameters.get("max_value")

        if min_value is None and max_value is None:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message="No range limits specified",
                )
            )
            return issues

        for col in columns:
            if col not in data.columns:
                # Report missing column as an issue
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Required column '{col}' is missing from the data",
                        column=col,
                        details={"missing_column": col}
                    )
                )
                continue

            # Check numeric columns only
            if not pd.api.types.is_numeric_dtype(data[col]):
                continue

            if min_value is not None:
                invalid_rows = data[data[col] < min_value]
                for idx, row in invalid_rows.iterrows():
                    issues.append(
                        ValidationIssue(
                            rule_name=rule.name,
                            validation_type=rule.validation_type,
                            severity=rule.severity,
                            message=f"Value below minimum: {row[col]} < {min_value}",
                            column=col,
                            row_index=idx,
                            value=row[col],
                            expected=f">= {min_value}",
                        )
                    )

            if max_value is not None:
                invalid_rows = data[data[col] > max_value]
                for idx, row in invalid_rows.iterrows():
                    issues.append(
                        ValidationIssue(
                            rule_name=rule.name,
                            validation_type=rule.validation_type,
                            severity=rule.severity,
                            message=f"Value above maximum: {row[col]} > {max_value}",
                            column=col,
                            row_index=idx,
                            value=row[col],
                            expected=f"<= {max_value}",
                        )
                    )

        return issues

    def _validate_pattern(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Validate patterns using regex.

        Args:
            data: DataFrame to validate
            rule: Pattern validation rule

        Returns:
            List of validation issues
        """
        issues = []

        columns = rule.columns or []
        pattern = rule.parameters.get("pattern")

        if not pattern:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message="No pattern specified",
                )
            )
            return issues

        try:
            regex = re.compile(pattern)
        except re.error as e:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message=f"Invalid regex pattern: {e}",
                )
            )
            return issues

        for col in columns:
            if col not in data.columns:
                # Report missing column as an issue
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Required column '{col}' is missing from the data",
                        column=col,
                        details={"missing_column": col}
                    )
                )
                continue

            # Convert to string for pattern matching
            str_series = data[col].astype(str)

            for idx, value in str_series.items():
                if pd.isna(data.loc[idx, col]):  # Skip NaN values
                    continue

                if not regex.match(value):
                    issues.append(
                        ValidationIssue(
                            rule_name=rule.name,
                            validation_type=rule.validation_type,
                            severity=rule.severity,
                            message=f"Value does not match pattern: {value}",
                            column=col,
                            row_index=idx,
                            value=value,
                            expected=pattern,
                        )
                    )

        return issues

    def _validate_uniqueness(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Validate uniqueness constraints.

        Args:
            data: DataFrame to validate
            rule: Uniqueness validation rule

        Returns:
            List of validation issues
        """
        issues = []

        columns = rule.columns or []

        if not columns:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message="No columns specified for uniqueness check",
                )
            )
            return issues

        # Check single column uniqueness
        if len(columns) == 1:
            col = columns[0]
            if col not in data.columns:
                return issues

            duplicates = data[data.duplicated(subset=[col], keep=False)]

            for idx, row in duplicates.iterrows():
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Duplicate value found: {row[col]}",
                        column=col,
                        row_index=idx,
                        value=row[col],
                    )
                )
        else:
            # Check composite uniqueness
            valid_columns = [col for col in columns if col in data.columns]

            if valid_columns:
                duplicates = data[data.duplicated(subset=valid_columns, keep=False)]

                for idx, row in duplicates.iterrows():
                    values = [str(row[col]) for col in valid_columns]
                    issues.append(
                        ValidationIssue(
                            rule_name=rule.name,
                            validation_type=rule.validation_type,
                            severity=rule.severity,
                            message=f"Duplicate combination found: {', '.join(values)}",
                            row_index=idx,
                            details={"columns": valid_columns, "values": values},
                        )
                    )

        return issues

    def _validate_completeness(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Validate data completeness (null checks).

        Args:
            data: DataFrame to validate
            rule: Completeness validation rule

        Returns:
            List of validation issues
        """
        issues = []

        columns = rule.columns or data.columns
        allow_null = rule.parameters.get("allow_null", False)
        max_null_percentage = rule.parameters.get("max_null_percentage", 0)

        for col in columns:
            if col not in data.columns:
                # Report missing column as an issue
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Required column '{col}' is missing from the data",
                        column=col,
                        details={"missing_column": col}
                    )
                )
                continue

            null_count = data[col].isnull().sum()
            total_count = len(data)
            null_percentage = (null_count / total_count) * 100 if total_count > 0 else 0

            if not allow_null and null_count > 0:
                # Report individual null values
                null_indices = data[data[col].isnull()].index
                for idx in null_indices:
                    issues.append(
                        ValidationIssue(
                            rule_name=rule.name,
                            validation_type=rule.validation_type,
                            severity=rule.severity,
                            message=f"Null value found in required column: {col}",
                            column=col,
                            row_index=idx,
                        )
                    )
            elif null_percentage > max_null_percentage:
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Column {col} has {null_percentage:.2f}% null values (max allowed: {max_null_percentage}%)",
                        column=col,
                        details={
                            "null_count": null_count,
                            "total_count": total_count,
                            "null_percentage": null_percentage,
                        },
                    )
                )

        return issues

    def _validate_consistency(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Validate data consistency.

        Args:
            data: DataFrame to validate
            rule: Consistency validation rule

        Returns:
            List of validation issues
        """
        issues = []

        condition = rule.condition

        if not condition:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message="No condition specified for consistency check",
                )
            )
            return issues

        try:
            # Evaluate condition
            invalid_rows = data.query(f"not ({condition})")

            for idx, row in invalid_rows.iterrows():
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Consistency check failed: {condition}",
                        row_index=idx,
                        details={"condition": condition},
                    )
                )

        except Exception as e:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message=f"Error evaluating condition: {e}",
                    details={"condition": condition},
                )
            )

        return issues

    def _validate_referential(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Validate referential integrity.

        Args:
            data: DataFrame to validate
            rule: Referential validation rule

        Returns:
            List of validation issues
        """
        issues = []

        reference_data = rule.parameters.get("reference_data")
        reference_column = rule.parameters.get("reference_column")
        foreign_key_column = rule.parameters.get("foreign_key_column")

        if not all([reference_data, reference_column, foreign_key_column]):
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message="Missing parameters for referential integrity check",
                )
            )
            return issues

        if foreign_key_column not in data.columns:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message=f"Foreign key column not found: {foreign_key_column}",
                )
            )
            return issues

        # Get reference values
        if isinstance(reference_data, pd.DataFrame):
            if reference_column not in reference_data.columns:
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Reference column not found: {reference_column}",
                    )
                )
                return issues
            reference_values = set(reference_data[reference_column].dropna())
        elif isinstance(reference_data, (list, set)):
            reference_values = set(reference_data)
        else:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message="Invalid reference data format",
                )
            )
            return issues

        # Check foreign key values
        foreign_values = data[foreign_key_column].dropna()
        invalid_values = foreign_values[~foreign_values.isin(reference_values)]

        for idx, value in invalid_values.items():
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message=f"Invalid foreign key value: {value}",
                    column=foreign_key_column,
                    row_index=idx,
                    value=value,
                )
            )

        return issues

    def _validate_business_rule(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Validate business rules.

        Args:
            data: DataFrame to validate
            rule: Business rule validation rule

        Returns:
            List of validation issues
        """
        issues = []

        # Business rules are similar to consistency checks but more complex
        condition = rule.condition

        if not condition:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message="No business rule condition specified",
                )
            )
            return issues

        try:
            # Evaluate business rule condition
            invalid_rows = data.query(f"not ({condition})")

            for idx, row in invalid_rows.iterrows():
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Business rule violation: {rule.description or condition}",
                        row_index=idx,
                        details={
                            "condition": condition,
                            "rule_description": rule.description,
                        },
                    )
                )

        except Exception as e:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message=f"Error evaluating business rule: {e}",
                    details={"condition": condition},
                )
            )

        return issues

    def _validate_custom(
        self, data: pd.DataFrame, rule: ValidationRule
    ) -> List[ValidationIssue]:
        """Validate using custom function.

        Args:
            data: DataFrame to validate
            rule: Custom validation rule

        Returns:
            List of validation issues
        """
        issues = []

        custom_function = rule.custom_function

        if not custom_function:
            # Try to get from registered validators
            validator_name = rule.parameters.get("validator_name")
            if validator_name and validator_name in self.custom_validators:
                custom_function = self.custom_validators[validator_name]
            else:
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message="No custom function specified",
                    )
                )
                return issues

        try:
            # Call custom function
            result = custom_function(data, rule.parameters)

            # Handle different return types
            if isinstance(result, bool):
                if not result:
                    issues.append(
                        ValidationIssue(
                            rule_name=rule.name,
                            validation_type=rule.validation_type,
                            severity=rule.severity,
                            message="Custom validation failed",
                        )
                    )
            elif isinstance(result, list):
                # Assume list of ValidationIssue objects
                issues.extend(result)
            elif isinstance(result, dict):
                # Convert dict to ValidationIssue
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=result.get("message", "Custom validation failed"),
                        column=result.get("column"),
                        row_index=result.get("row_index"),
                        value=result.get("value"),
                        details=result.get("details", {}),
                    )
                )
            else:
                issues.append(
                    ValidationIssue(
                        rule_name=rule.name,
                        validation_type=rule.validation_type,
                        severity=rule.severity,
                        message=f"Custom validation returned unexpected result: {result}",
                    )
                )

        except Exception as e:
            issues.append(
                ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=rule.severity,
                    message=f"Custom validation error: {e}",
                )
            )

        return issues

    def _is_compatible_type(self, actual_type: str, expected_type: str) -> bool:
        """Check if actual type is compatible with expected type.

        Args:
            actual_type: Actual data type
            expected_type: Expected data type

        Returns:
            True if compatible
        """
        # Type mapping for compatibility
        type_groups = {
            "integer": [
                "int8",
                "int16",
                "int32",
                "int64",
                "uint8",
                "uint16",
                "uint32",
                "uint64",
            ],
            "float": ["float16", "float32", "float64"],
            "string": ["object", "string", "category"],
            "datetime": ["datetime64", "timedelta64"],
            "boolean": ["bool"],
        }

        # Direct match
        if actual_type == expected_type:
            return True

        # Group match
        for group, types in type_groups.items():
            if expected_type == group and any(t in actual_type for t in types):
                return True

        return False

    def _calculate_statistics(
        self, data: pd.DataFrame, issues: List[ValidationIssue]
    ) -> Dict[str, Any]:
        """Calculate validation statistics.

        Args:
            data: DataFrame that was validated
            issues: List of validation issues

        Returns:
            Statistics dictionary
        """
        stats = {
            "total_rows": len(data),
            "total_columns": len(data.columns),
            "total_issues": len(issues),
            "error_issues": len(
                [i for i in issues if i.severity == ValidationSeverity.ERROR]
            ),
            "warning_issues": len(
                [i for i in issues if i.severity == ValidationSeverity.WARNING]
            ),
            "info_issues": len(
                [i for i in issues if i.severity == ValidationSeverity.INFO]
            ),
            "issues_by_type": {},
            "issues_by_column": {},
            "data_quality_score": 0.0,
        }

        # Group issues by type
        for issue in issues:
            issue_type = issue.validation_type.value
            stats["issues_by_type"][issue_type] = (
                stats["issues_by_type"].get(issue_type, 0) + 1
            )

            if issue.column:
                stats["issues_by_column"][issue.column] = (
                    stats["issues_by_column"].get(issue.column, 0) + 1
                )

        # Calculate data quality score (0-100)
        total_cells = len(data) * len(data.columns)
        if total_cells > 0:
            error_weight = 1.0
            warning_weight = 0.5
            info_weight = 0.1

            weighted_issues = (
                stats["error_issues"] * error_weight
                + stats["warning_issues"] * warning_weight
                + stats["info_issues"] * info_weight
            )

            stats["data_quality_score"] = max(
                0, 100 - (weighted_issues / total_cells * 100)
            )

        return stats

    def _generate_data_summary(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Generate data summary.

        Args:
            data: DataFrame to summarize

        Returns:
            Data summary dictionary
        """
        summary = {
            "shape": data.shape,
            "columns": list(data.columns),
            "dtypes": data.dtypes.to_dict(),
            "null_counts": data.isnull().sum().to_dict(),
            "memory_usage": data.memory_usage(deep=True).sum(),
        }

        # Add basic statistics for numeric columns
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        if len(numeric_columns) > 0:
            summary["numeric_stats"] = data[numeric_columns].describe().to_dict()

        return summary

    def create_validation_report(
        self, result: ValidationResult, output_path: Optional[Union[str, Path]] = None
    ) -> str:
        """Create validation report.

        Args:
            result: Validation result
            output_path: Optional output file path

        Returns:
            Report content as string
        """
        report_lines = []

        # Header
        report_lines.append("DATA VALIDATION REPORT")
        report_lines.append("=" * 50)
        report_lines.append(
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )
        report_lines.append("")

        # Summary
        report_lines.append("SUMMARY")
        report_lines.append("-" * 20)
        report_lines.append(
            f"Overall Status: {'PASSED' if result.success else 'FAILED'}"
        )
        report_lines.append(f"Total Rules: {result.total_rules}")
        report_lines.append(f"Passed Rules: {result.passed_rules}")
        report_lines.append(f"Failed Rules: {result.failed_rules}")
        report_lines.append(f"Total Issues: {len(result.issues)}")
        report_lines.append(f"  - Errors: {result.error_count}")
        report_lines.append(f"  - Warnings: {result.warning_count}")
        report_lines.append(f"  - Info: {result.info_count}")

        if result.execution_time:
            report_lines.append(f"Execution Time: {result.execution_time:.2f} seconds")

        if "data_quality_score" in result.statistics:
            report_lines.append(
                f"Data Quality Score: {result.statistics['data_quality_score']:.2f}/100"
            )

        report_lines.append("")

        # Data Summary
        if result.data_summary:
            report_lines.append("DATA SUMMARY")
            report_lines.append("-" * 20)
            shape = result.data_summary.get("shape", (0, 0))
            report_lines.append(f"Rows: {shape[0]:,}")
            report_lines.append(f"Columns: {shape[1]:,}")

            if "memory_usage" in result.data_summary:
                memory_mb = result.data_summary["memory_usage"] / (1024 * 1024)
                report_lines.append(f"Memory Usage: {memory_mb:.2f} MB")

            report_lines.append("")

        # Issues by Type
        if result.statistics.get("issues_by_type"):
            report_lines.append("ISSUES BY TYPE")
            report_lines.append("-" * 20)
            for issue_type, count in result.statistics["issues_by_type"].items():
                report_lines.append(f"{issue_type}: {count}")
            report_lines.append("")

        # Issues by Column
        if result.statistics.get("issues_by_column"):
            report_lines.append("ISSUES BY COLUMN")
            report_lines.append("-" * 20)
            for column, count in sorted(result.statistics["issues_by_column"].items()):
                report_lines.append(f"{column}: {count}")
            report_lines.append("")

        # Detailed Issues
        if result.issues:
            report_lines.append("DETAILED ISSUES")
            report_lines.append("-" * 20)

            for i, issue in enumerate(result.issues, 1):
                report_lines.append(
                    f"{i}. [{issue.severity.value.upper()}] {issue.rule_name}"
                )
                report_lines.append(f"   Type: {issue.validation_type.value}")
                report_lines.append(f"   Message: {issue.message}")

                if issue.column:
                    report_lines.append(f"   Column: {issue.column}")
                if issue.row_index is not None:
                    report_lines.append(f"   Row: {issue.row_index}")
                if issue.value is not None:
                    report_lines.append(f"   Value: {issue.value}")
                if issue.expected is not None:
                    report_lines.append(f"   Expected: {issue.expected}")

                report_lines.append("")

        report_content = "\n".join(report_lines)

        # Save to file if path provided
        if output_path:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, "w", encoding="utf-8") as f:
                f.write(report_content)

            logger.info(f"Validation report saved to: {output_path}")

        return report_content


# Convenience functions for common validation rules
def create_schema_rule(
    name: str,
    schema: Dict[str, Any],
    severity: ValidationSeverity = ValidationSeverity.ERROR,
) -> ValidationRule:
    """Create schema validation rule.

    Args:
        name: Rule name
        schema: Schema definition
        severity: Validation severity

    Returns:
        ValidationRule
    """
    return ValidationRule(
        name=name,
        validation_type=ValidationType.SCHEMA,
        severity=severity,
        description=f"Schema validation: {name}",
        parameters={"schema": schema},
    )


def create_not_null_rule(
    name: str,
    columns: List[str],
    severity: ValidationSeverity = ValidationSeverity.ERROR,
) -> ValidationRule:
    """Create not null validation rule.

    Args:
        name: Rule name
        columns: Columns to check
        severity: Validation severity

    Returns:
        ValidationRule
    """
    return ValidationRule(
        name=name,
        validation_type=ValidationType.COMPLETENESS,
        severity=severity,
        description=f"Not null check for columns: {', '.join(columns)}",
        columns=columns,
        parameters={"allow_null": False},
    )


def create_range_rule(
    name: str,
    columns: List[str],
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    severity: ValidationSeverity = ValidationSeverity.ERROR,
) -> ValidationRule:
    """Create range validation rule.

    Args:
        name: Rule name
        columns: Columns to check
        min_value: Minimum allowed value
        max_value: Maximum allowed value
        severity: Validation severity

    Returns:
        ValidationRule
    """
    return ValidationRule(
        name=name,
        validation_type=ValidationType.RANGE,
        severity=severity,
        description=f"Range check for columns: {', '.join(columns)}",
        columns=columns,
        parameters={"min_value": min_value, "max_value": max_value},
    )


def create_pattern_rule(
    name: str,
    columns: List[str],
    pattern: str,
    severity: ValidationSeverity = ValidationSeverity.ERROR,
) -> ValidationRule:
    """Create pattern validation rule.

    Args:
        name: Rule name
        columns: Columns to check
        pattern: Regex pattern
        severity: Validation severity

    Returns:
        ValidationRule
    """
    return ValidationRule(
        name=name,
        validation_type=ValidationType.PATTERN,
        severity=severity,
        description=f"Pattern check for columns: {', '.join(columns)}",
        columns=columns,
        parameters={"pattern": pattern},
    )


def create_unique_rule(
    name: str,
    columns: List[str],
    severity: ValidationSeverity = ValidationSeverity.ERROR,
) -> ValidationRule:
    """Create uniqueness validation rule.

    Args:
        name: Rule name
        columns: Columns to check for uniqueness
        severity: Validation severity

    Returns:
        ValidationRule
    """
    return ValidationRule(
        name=name,
        validation_type=ValidationType.UNIQUENESS,
        severity=severity,
        description=f"Uniqueness check for columns: {', '.join(columns)}",
        columns=columns,
    )
