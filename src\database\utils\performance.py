"""Performance monitoring utilities and decorators.

This module provides decorators and utilities for monitoring and measuring
performance of database operations and other functions.
"""

import asyncio
import functools
import logging
import time
from typing import Any, Callable, Dict, Optional, TypeVar, Union

from ..monitoring.metrics import get_metrics_collector

# Configure logging
logger = logging.getLogger(__name__)

# Type variables for generic decorators
F = TypeVar("F", bound=Callable[..., Any])
AsyncF = TypeVar("AsyncF", bound=Callable[..., Any])


def time_execution(
    func: Optional[Callable] = None,
    *,
    log_level: int = logging.INFO,
    include_args: bool = False,
    record_metrics: bool = True,
) -> Callable:
    """Decorator to measure and log execution time of functions.

    Can be used as @time_execution or @time_execution(log_level=logging.DEBUG)

    Args:
        func: Function to decorate (when used without parentheses).
        log_level: Logging level for execution time messages.
        include_args: Whether to include function arguments in log messages.
        record_metrics: Whether to record metrics using MetricsCollector.

    Returns:
        Decorated function that logs execution time.

    Examples:
        >>> @time_execution
        ... async def my_async_function():
        ...     await asyncio.sleep(1)

        >>> @time_execution(log_level=logging.DEBUG, include_args=True)
        ... def my_function(x, y):
        ...     return x + y
    """

    def decorator(f: Callable) -> Callable:
        if asyncio.iscoroutinefunction(f):
            return _async_time_execution(f, log_level, include_args, record_metrics)
        else:
            return _sync_time_execution(f, log_level, include_args, record_metrics)

    if func is None:
        # Called with arguments: @time_execution(log_level=...)
        return decorator
    else:
        # Called without arguments: @time_execution
        return decorator(func)


def _async_time_execution(
    func: Callable, log_level: int, include_args: bool, record_metrics: bool
) -> Callable:
    """Internal decorator for async functions."""

    @functools.wraps(func)
    async def wrapper(*args, **kwargs) -> Any:
        start_time = time.perf_counter()
        function_name = f"{func.__module__}.{func.__qualname__}"

        try:
            # Log function start
            if include_args:
                logger.log(
                    log_level,
                    f"Starting {function_name} with args={args}, kwargs={kwargs}",
                )
            else:
                logger.log(log_level, f"Starting {function_name}")

            # Execute function
            result = await func(*args, **kwargs)

            # Calculate execution time
            execution_time = time.perf_counter() - start_time

            # Log completion
            logger.info(f"{function_name} executed in {execution_time:.4f}s")

            # Record metrics if enabled
            if record_metrics:
                try:
                    collector = get_metrics_collector()
                    collector.record_query_execution(
                        query=function_name, execution_time=execution_time, success=True
                    )
                except Exception as e:
                    logger.warning(
                        f"Failed to record metrics for {function_name}: {str(e)}"
                    )

            return result

        except Exception as e:
            # Calculate execution time for failed operations
            execution_time = time.perf_counter() - start_time

            # Log error
            logger.log(
                log_level, f"Failed {function_name} in {execution_time:.4f}s: {str(e)}"
            )

            # Record metrics for failed operations if enabled
            if record_metrics:
                try:
                    collector = get_metrics_collector()
                    collector.record_query_execution(
                        query=function_name,
                        execution_time=execution_time,
                        success=False,
                        error_message=str(e),
                    )
                except Exception as metrics_error:
                    logger.warning(
                        f"Failed to record error metrics for {function_name}: {str(metrics_error)}"
                    )

            # Re-raise the original exception
            raise

    return wrapper


def _sync_time_execution(
    func: Callable, log_level: int, include_args: bool, record_metrics: bool
) -> Callable:
    """Internal decorator for sync functions."""

    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time.perf_counter()
        function_name = f"{func.__module__}.{func.__qualname__}"

        try:
            # Log function start
            if include_args:
                logger.log(
                    log_level,
                    f"Starting {function_name} with args={args}, kwargs={kwargs}",
                )
            else:
                logger.log(log_level, f"Starting {function_name}")

            # Execute function
            result = func(*args, **kwargs)

            # Calculate execution time
            execution_time = time.perf_counter() - start_time

            # Log completion
            logger.info(f"{function_name} executed in {execution_time:.4f}s")

            # Record metrics if enabled
            if record_metrics:
                try:
                    collector = get_metrics_collector()
                    collector.record_query_execution(
                        query=function_name, execution_time=execution_time, success=True
                    )
                except Exception as e:
                    logger.warning(
                        f"Failed to record metrics for {function_name}: {str(e)}"
                    )

            return result

        except Exception as e:
            # Calculate execution time for failed operations
            execution_time = time.perf_counter() - start_time

            # Log error
            logger.log(
                log_level, f"Failed {function_name} in {execution_time:.4f}s: {str(e)}"
            )

            # Record metrics for failed operations if enabled
            if record_metrics:
                try:
                    collector = get_metrics_collector()
                    collector.record_query_execution(
                        query=function_name,
                        execution_time=execution_time,
                        success=False,
                        error_message=str(e),
                    )
                except Exception as metrics_error:
                    logger.warning(
                        f"Failed to record error metrics for {function_name}: {str(metrics_error)}"
                    )

            # Re-raise the original exception
            raise

    return wrapper


class PerformanceTimer:
    """Context manager for measuring execution time.

    Examples:
        >>> with PerformanceTimer() as timer:
        ...     # Some operation
        ...     time.sleep(1)
        >>> print(f"Operation took {timer.elapsed:.4f} seconds")

        >>> async with PerformanceTimer("database_query") as timer:
        ...     await some_database_operation()
        >>> print(f"Query took {timer.elapsed:.4f} seconds")
    """

    def __init__(
        self,
        name: Optional[str] = None,
        log_level: int = logging.INFO,
        record_metrics: bool = False,
    ):
        """Initialize performance timer.

        Args:
            name: Optional name for the operation being timed.
            log_level: Logging level for timing messages.
            record_metrics: Whether to record metrics using MetricsCollector.
        """
        self.name = name or "operation"
        self.log_level = log_level
        self.record_metrics = record_metrics
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.elapsed: float = 0.0
        self.elapsed_time: float = 0.0  # Alias for backward compatibility

    def __enter__(self) -> "PerformanceTimer":
        """Start timing."""
        self.start_time = time.perf_counter()
        logger.log(self.log_level, f"Starting {self.name}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Stop timing and log results."""
        self.end_time = time.perf_counter()
        self.elapsed = self.end_time - (self.start_time or 0)
        self.elapsed_time = self.elapsed  # Update alias

        if exc_type is None:
            logger.log(self.log_level, f"Completed {self.name} in {self.elapsed:.4f}s")
            success = True
            error_message = None
        else:
            logger.log(
                self.log_level,
                f"Failed {self.name} in {self.elapsed:.4f}s: {str(exc_val)}",
            )
            success = False
            error_message = str(exc_val)

        # Record metrics if enabled
        if self.record_metrics:
            try:
                collector = get_metrics_collector()
                collector.record_query_execution(
                    query=self.name,
                    execution_time=self.elapsed,
                    success=success,
                    error_message=error_message,
                )
            except Exception as e:
                logger.warning(f"Failed to record metrics for {self.name}: {str(e)}")

    async def __aenter__(self) -> "PerformanceTimer":
        """Async context manager entry."""
        return self.__enter__()

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        self.__exit__(exc_type, exc_val, exc_tb)


def measure_memory_usage(
    func: Optional[Callable] = None, *, log_level: int = logging.INFO
) -> Callable:
    """Decorator to measure memory usage of functions.

    Note: Requires psutil package for memory monitoring.

    Args:
        func: Function to decorate.
        log_level: Logging level for memory usage messages.

    Returns:
        Decorated function that logs memory usage.
    """

    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def wrapper(*args, **kwargs) -> Any:
            try:
                import psutil

                process = psutil.Process()

                # Get initial memory usage
                initial_memory = process.memory_info().rss / 1024 / 1024  # MB

                # Execute function
                result = f(*args, **kwargs)

                # Get final memory usage
                final_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_diff = final_memory - initial_memory

                function_name = f"{f.__module__}.{f.__qualname__}"
                logger.log(
                    log_level,
                    f"Memory usage for {function_name}: "
                    f"initial={initial_memory:.2f}MB, "
                    f"final={final_memory:.2f}MB, "
                    f"diff={memory_diff:+.2f}MB",
                )

                return result

            except ImportError:
                logger.warning("psutil not available, memory monitoring disabled")
                return f(*args, **kwargs)
            except Exception as e:
                logger.warning(f"Error monitoring memory usage: {str(e)}")
                return f(*args, **kwargs)

        return wrapper

    if func is None:
        return decorator
    else:
        return decorator(func)


def profile_function(
    func: Optional[Callable] = None,
    *,
    sort_by: str = "cumulative",
    lines_to_print: int = 10,
) -> Callable:
    """Decorator to profile function execution using cProfile.

    Args:
        func: Function to decorate.
        sort_by: Sort criteria for profiling results.
        lines_to_print: Number of lines to print in profile output.

    Returns:
        Decorated function that profiles execution.
    """

    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def wrapper(*args, **kwargs) -> Any:
            try:
                import cProfile
                import io
                import pstats

                # Create profiler
                profiler = cProfile.Profile()

                # Profile function execution
                profiler.enable()
                result = f(*args, **kwargs)
                profiler.disable()

                # Generate profile report
                s = io.StringIO()
                ps = pstats.Stats(profiler, stream=s).sort_stats(sort_by)
                ps.print_stats(lines_to_print)

                function_name = f"{f.__module__}.{f.__qualname__}"
                logger.info(f"Profile for {function_name}:\n{s.getvalue()}")

                return result

            except Exception as e:
                logger.warning(f"Error profiling function: {str(e)}")
                return f(*args, **kwargs)

        return wrapper

    if func is None:
        return decorator
    else:
        return decorator(func)


class PerformanceMonitor:
    """Performance monitoring utility for tracking multiple operations."""

    def __init__(self):
        """Initialize performance monitor."""
        self.timers: dict[str, list[float]] = {}
        self.active_timers: dict[str, float] = {}

    def start_operation(self, name: str) -> None:
        """Start timing an operation.

        Args:
            name: Name of the operation.
        """
        self.active_timers[name] = time.perf_counter()

    def start_timer(self, name: str) -> None:
        """Start a named timer (alias for start_operation).

        Args:
            name: Name of the timer.
        """
        self.start_operation(name)

    def end_operation(self, name: str) -> float:
        """Stop timing an operation and record the elapsed time.

        Args:
            name: Name of the operation.

        Returns:
            float: Elapsed time in seconds.

        Raises:
            ValueError: If operation was not started.
        """
        if name not in self.active_timers:
            raise ValueError(f"Operation '{name}' was not started")

        elapsed = time.perf_counter() - self.active_timers[name]
        del self.active_timers[name]

        if name not in self.timers:
            self.timers[name] = []
        self.timers[name].append(elapsed)

        return elapsed

    def stop_timer(self, name: str) -> float:
        """Stop a named timer and record the elapsed time (alias for end_operation).

        Args:
            name: Name of the timer.

        Returns:
            float: Elapsed time in seconds.

        Raises:
            ValueError: If timer was not started.
        """
        return self.end_operation(name)

    def get_stats(self, name: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """Get statistics for operations.

        Args:
            name: Name of specific operation. If None, returns all operations.

        Returns:
            Dict containing statistics for operations.
        """
        if name is not None:
            if name not in self.timers or not self.timers[name]:
                return {
                    name: {
                        "count": 0,
                        "total_time": 0.0,
                        "min_time": 0.0,
                        "max_time": 0.0,
                        "avg_time": 0.0,
                    }
                }

            times = self.timers[name]
            total_time = sum(times)
            return {
                name: {
                    "count": len(times),
                    "total_time": total_time,
                    "min_time": min(times),
                    "max_time": max(times),
                    "avg_time": total_time / len(times),
                }
            }
        else:
            # Return stats for all operations
            result = {}
            for op_name in self.timers:
                if self.timers[op_name]:
                    times = self.timers[op_name]
                    total_time = sum(times)
                    result[op_name] = {
                        "count": len(times),
                        "total_time": total_time,
                        "min_time": min(times),
                        "max_time": max(times),
                        "avg_time": total_time / len(times),
                    }
                else:
                    result[op_name] = {
                        "count": 0,
                        "total_time": 0.0,
                        "min_time": 0.0,
                        "max_time": 0.0,
                        "avg_time": 0.0,
                    }
            return result

    def get_all_stats(self) -> dict[str, dict[str, float]]:
        """Get statistics for all timers.

        Returns:
            Dict mapping timer names to their statistics.
        """
        return {name: self.get_stats(name) for name in self.timers}

    def reset(self, name: Optional[str] = None) -> None:
        """Reset timer data.

        Args:
            name: Name of specific timer to reset. If None, resets all timers.
        """
        if name is None:
            self.timers.clear()
            self.active_timers.clear()
        else:
            self.timers.pop(name, None)
            self.active_timers.pop(name, None)
    
    def measure_query_time(self, query: str):
        """Context manager for measuring query execution time.
        
        Args:
            query: The SQL query being executed
            
        Returns:
            Context manager that measures execution time
        """
        from contextlib import contextmanager
        
        @contextmanager
        def timer_context():
            start_time = time.perf_counter()
            try:
                yield
            finally:
                elapsed = time.perf_counter() - start_time
                if "query_times" not in self.timers:
                    self.timers["query_times"] = []
                self.timers["query_times"].append(elapsed)
                logger.debug(f"Query executed in {elapsed:.4f}s: {query[:100]}...")
        
        return timer_context()


# Alias for backward compatibility
measure_performance = time_execution
