#!/usr/bin/env python3
"""测试用例生成器

该模块提供自动生成测试用例的功能，支持单元测试、集成测试、端到端测试等。

主要功能:
- 根据代码结构自动生成测试用例
- 生成数据库测试用例
- 生成API测试用例
- 生成性能测试用例
- 生成安全测试用例
- 支持多种测试框架（pytest、unittest等）
"""

import ast
import inspect
import json
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from jinja2 import Template


class TestFramework(Enum):
    """测试框架枚举"""
    PYTEST = "pytest"
    UNITTEST = "unittest"
    NOSE = "nose"
    DOCTEST = "doctest"


class TestType(Enum):
    """测试类型枚举"""
    UNIT = "unit"
    INTEGRATION = "integration"
    E2E = "e2e"
    PERFORMANCE = "performance"
    SECURITY = "security"
    API = "api"
    DATABASE = "database"
    UI = "ui"


class AssertionType(Enum):
    """断言类型枚举"""
    EQUAL = "equal"
    NOT_EQUAL = "not_equal"
    TRUE = "true"
    FALSE = "false"
    NONE = "none"
    NOT_NONE = "not_none"
    IN = "in"
    NOT_IN = "not_in"
    GREATER = "greater"
    LESS = "less"
    GREATER_EQUAL = "greater_equal"
    LESS_EQUAL = "less_equal"
    RAISES = "raises"
    REGEX_MATCH = "regex_match"
    INSTANCE_OF = "instance_of"


@dataclass
class TestCase:
    """测试用例数据结构"""
    name: str
    description: str
    test_type: TestType
    function_name: str
    setup_code: List[str] = field(default_factory=list)
    test_code: List[str] = field(default_factory=list)
    teardown_code: List[str] = field(default_factory=list)
    imports: Set[str] = field(default_factory=set)
    fixtures: List[str] = field(default_factory=list)
    parameters: List[Dict[str, Any]] = field(default_factory=list)
    expected_result: Any = None
    expected_exception: Optional[str] = None
    assertions: List[Dict[str, Any]] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    priority: str = "medium"
    timeout: Optional[int] = None
    skip_reason: Optional[str] = None


@dataclass
class FunctionInfo:
    """函数信息"""
    name: str
    module: str
    file_path: str
    line_number: int
    docstring: Optional[str]
    parameters: List[Dict[str, Any]]
    return_type: Optional[str]
    decorators: List[str]
    is_async: bool
    is_method: bool
    class_name: Optional[str]
    complexity: int


@dataclass
class ClassInfo:
    """类信息"""
    name: str
    module: str
    file_path: str
    line_number: int
    docstring: Optional[str]
    methods: List[FunctionInfo]
    properties: List[str]
    base_classes: List[str]
    decorators: List[str]


class CodeAnalyzer:
    """代码分析器"""
    
    def __init__(self):
        self.functions: List[FunctionInfo] = []
        self.classes: List[ClassInfo] = []
    
    def analyze_file(self, file_path: str) -> Tuple[List[FunctionInfo], List[ClassInfo]]:
        """分析Python文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            函数信息列表和类信息列表
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        functions = []
        classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                func_info = self._extract_function_info(node, file_path)
                functions.append(func_info)
            elif isinstance(node, ast.ClassDef):
                class_info = self._extract_class_info(node, file_path)
                classes.append(class_info)
        
        return functions, classes
    
    def _extract_function_info(self, node: ast.FunctionDef, file_path: str) -> FunctionInfo:
        """提取函数信息"""
        # 获取参数信息
        parameters = []
        for arg in node.args.args:
            param_info = {
                'name': arg.arg,
                'type': self._get_type_annotation(arg.annotation) if arg.annotation else None,
                'default': None
            }
            parameters.append(param_info)
        
        # 获取默认参数
        defaults = node.args.defaults
        if defaults:
            for i, default in enumerate(defaults):
                param_index = len(parameters) - len(defaults) + i
                if param_index >= 0:
                    parameters[param_index]['default'] = ast.unparse(default)
        
        # 获取装饰器
        decorators = [ast.unparse(decorator) for decorator in node.decorator_list]
        
        # 计算复杂度（简单的行数统计）
        complexity = len([n for n in ast.walk(node) if isinstance(n, (ast.If, ast.For, ast.While, ast.Try))])
        
        return FunctionInfo(
            name=node.name,
            module=Path(file_path).stem,
            file_path=file_path,
            line_number=node.lineno,
            docstring=ast.get_docstring(node),
            parameters=parameters,
            return_type=self._get_type_annotation(node.returns) if node.returns else None,
            decorators=decorators,
            is_async=isinstance(node, ast.AsyncFunctionDef),
            is_method=False,  # 需要在类上下文中确定
            class_name=None,
            complexity=complexity
        )
    
    def _extract_class_info(self, node: ast.ClassDef, file_path: str) -> ClassInfo:
        """提取类信息"""
        methods = []
        properties = []
        
        for item in node.body:
            if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                method_info = self._extract_function_info(item, file_path)
                method_info.is_method = True
                method_info.class_name = node.name
                methods.append(method_info)
            elif isinstance(item, ast.AnnAssign) and isinstance(item.target, ast.Name):
                properties.append(item.target.id)
        
        # 获取基类
        base_classes = [ast.unparse(base) for base in node.bases]
        
        # 获取装饰器
        decorators = [ast.unparse(decorator) for decorator in node.decorator_list]
        
        return ClassInfo(
            name=node.name,
            module=Path(file_path).stem,
            file_path=file_path,
            line_number=node.lineno,
            docstring=ast.get_docstring(node),
            methods=methods,
            properties=properties,
            base_classes=base_classes,
            decorators=decorators
        )
    
    def _get_type_annotation(self, annotation) -> Optional[str]:
        """获取类型注解字符串"""
        if annotation:
            return ast.unparse(annotation)
        return None


class TestCaseGenerator:
    """测试用例生成器"""
    
    def __init__(self, framework: TestFramework = TestFramework.PYTEST):
        """初始化测试用例生成器
        
        Args:
            framework: 测试框架
        """
        self.framework = framework
        self.analyzer = CodeAnalyzer()
        self.templates = self._load_templates()
    
    def _load_templates(self) -> Dict[str, Template]:
        """加载测试模板"""
        templates = {}
        
        # pytest单元测试模板
        templates['pytest_unit'] = Template("""
{%- for import in imports %}
{{ import }}
{%- endfor %}

{% if fixtures %}
{%- for fixture in fixtures %}
{{ fixture }}
{%- endfor %}

{% endif %}
{%- if parameters %}
@pytest.mark.parametrize("{{ param_names }}", [
{%- for param_set in parameters %}
    {{ param_set }},
{%- endfor %}
])
{% endif %}
{%- if tags %}
{%- for tag in tags %}
@pytest.mark.{{ tag }}
{%- endfor %}
{% endif %}
{%- if timeout %}
@pytest.mark.timeout({{ timeout }})
{% endif %}
{%- if skip_reason %}
@pytest.mark.skip(reason="{{ skip_reason }}")
{% endif %}
def {{ function_name }}({{ function_params }}):
    \"\"\"{{ description }}\"\"\"
{%- if setup_code %}
    # Setup
{%- for line in setup_code %}
    {{ line }}
{%- endfor %}
{% endif %}
    
    # Test
{%- for line in test_code %}
    {{ line }}
{%- endfor %}
    
{%- if assertions %}
    # Assertions
{%- for assertion in assertions %}
    {{ assertion }}
{%- endfor %}
{% endif %}
{%- if teardown_code %}
    
    # Teardown
{%- for line in teardown_code %}
    {{ line }}
{%- endfor %}
{% endif %}
""")
        
        # unittest模板
        templates['unittest'] = Template("""
{%- for import in imports %}
{{ import }}
{%- endfor %}

class {{ class_name }}(unittest.TestCase):
    \"\"\"{{ description }}\"\"\"
    
    def setUp(self):
        \"\"\"测试前置设置\"\"\"
{%- for line in setup_code %}
        {{ line }}
{%- endfor %}
    
    def tearDown(self):
        \"\"\"测试后置清理\"\"\"
{%- for line in teardown_code %}
        {{ line }}
{%- endfor %}
    
{%- if skip_reason %}
    @unittest.skip("{{ skip_reason }}")
{% endif %}
    def {{ function_name }}(self):
        \"\"\"{{ description }}\"\"\"
{%- for line in test_code %}
        {{ line }}
{%- endfor %}
        
{%- for assertion in assertions %}
        {{ assertion }}
{%- endfor %}
""")
        
        # API测试模板
        templates['api_test'] = Template("""
{%- for import in imports %}
{{ import }}
{%- endfor %}

@pytest.mark.api
def {{ function_name }}(client):
    \"\"\"{{ description }}\"\"\"
    # API请求
    response = client.{{ method.lower() }}(
        "{{ endpoint }}",
{%- if request_data %}
        json={{ request_data }},
{% endif %}
{%- if headers %}
        headers={{ headers }},
{% endif %}
    )
    
    # 状态码检查
    assert response.status_code == {{ expected_status }}
    
{%- if response_schema %}
    # 响应格式检查
    data = response.json()
{%- for field, field_type in response_schema.items() %}
    assert "{{ field }}" in data
    assert isinstance(data["{{ field }}"], {{ field_type }})
{%- endfor %}
{% endif %}
    
{%- for assertion in assertions %}
    {{ assertion }}
{%- endfor %}
""")
        
        # 数据库测试模板
        templates['database_test'] = Template("""
{%- for import in imports %}
{{ import }}
{%- endfor %}

@pytest.mark.database
def {{ function_name }}(db_session):
    \"\"\"{{ description }}\"\"\"
{%- if setup_code %}
    # 数据准备
{%- for line in setup_code %}
    {{ line }}
{%- endfor %}
{% endif %}
    
    # 执行数据库操作
{%- for line in test_code %}
    {{ line }}
{%- endfor %}
    
{%- for assertion in assertions %}
    {{ assertion }}
{%- endfor %}
    
{%- if teardown_code %}
    # 数据清理
{%- for line in teardown_code %}
    {{ line }}
{%- endfor %}
{% endif %}
""")
        
        # 性能测试模板
        templates['performance_test'] = Template("""
{%- for import in imports %}
{{ import }}
{%- endfor %}

@pytest.mark.performance
def {{ function_name }}():
    \"\"\"{{ description }}\"\"\"
    import time
    
{%- if setup_code %}
    # 性能测试准备
{%- for line in setup_code %}
    {{ line }}
{%- endfor %}
{% endif %}
    
    # 性能测试执行
    start_time = time.time()
    
{%- for line in test_code %}
    {{ line }}
{%- endfor %}
    
    end_time = time.time()
    execution_time = end_time - start_time
    
    # 性能断言
    assert execution_time < {{ max_execution_time }}, f"执行时间 {execution_time:.3f}s 超过阈值 {{ max_execution_time }}s"
    
{%- for assertion in assertions %}
    {{ assertion }}
{%- endfor %}
""")
        
        return templates
    
    def generate_unit_tests(self, source_file: str, output_dir: str) -> List[str]:
        """为源文件生成单元测试
        
        Args:
            source_file: 源文件路径
            output_dir: 输出目录
            
        Returns:
            生成的测试文件路径列表
        """
        functions, classes = self.analyzer.analyze_file(source_file)
        
        generated_files = []
        
        # 为每个函数生成测试
        for func in functions:
            if not func.name.startswith('_'):  # 跳过私有函数
                test_cases = self._generate_function_tests(func)
                test_file = self._write_test_file(test_cases, output_dir, f"test_{func.name}")
                generated_files.append(test_file)
        
        # 为每个类生成测试
        for cls in classes:
            test_cases = self._generate_class_tests(cls)
            test_file = self._write_test_file(test_cases, output_dir, f"test_{cls.name.lower()}")
            generated_files.append(test_file)
        
        return generated_files
    
    def _generate_function_tests(self, func: FunctionInfo) -> List[TestCase]:
        """为函数生成测试用例"""
        test_cases = []
        
        # 基本功能测试
        basic_test = TestCase(
            name=f"test_{func.name}_basic",
            description=f"测试 {func.name} 基本功能",
            test_type=TestType.UNIT,
            function_name=f"test_{func.name}_basic",
            imports={f"from {func.module} import {func.name}"},
            test_code=self._generate_basic_test_code(func),
            assertions=self._generate_basic_assertions(func)
        )
        test_cases.append(basic_test)
        
        # 边界值测试
        if func.parameters:
            boundary_test = TestCase(
                name=f"test_{func.name}_boundary",
                description=f"测试 {func.name} 边界值",
                test_type=TestType.UNIT,
                function_name=f"test_{func.name}_boundary",
                imports={f"from {func.module} import {func.name}"},
                test_code=self._generate_boundary_test_code(func),
                assertions=self._generate_boundary_assertions(func)
            )
            test_cases.append(boundary_test)
        
        # 异常测试
        exception_test = TestCase(
            name=f"test_{func.name}_exceptions",
            description=f"测试 {func.name} 异常处理",
            test_type=TestType.UNIT,
            function_name=f"test_{func.name}_exceptions",
            imports={f"from {func.module} import {func.name}", "import pytest"},
            test_code=self._generate_exception_test_code(func),
            assertions=self._generate_exception_assertions(func)
        )
        test_cases.append(exception_test)
        
        return test_cases
    
    def _generate_class_tests(self, cls: ClassInfo) -> List[TestCase]:
        """为类生成测试用例"""
        test_cases = []
        
        # 类初始化测试
        init_test = TestCase(
            name=f"test_{cls.name.lower()}_init",
            description=f"测试 {cls.name} 初始化",
            test_type=TestType.UNIT,
            function_name=f"test_{cls.name.lower()}_init",
            imports={f"from {cls.module} import {cls.name}"},
            test_code=[f"instance = {cls.name}()", "assert instance is not None"],
            assertions=["assert isinstance(instance, {cls.name})"]
        )
        test_cases.append(init_test)
        
        # 为每个公共方法生成测试
        for method in cls.methods:
            if not method.name.startswith('_') or method.name in ['__str__', '__repr__']:
                method_tests = self._generate_method_tests(method, cls)
                test_cases.extend(method_tests)
        
        return test_cases
    
    def _generate_method_tests(self, method: FunctionInfo, cls: ClassInfo) -> List[TestCase]:
        """为方法生成测试用例"""
        test_cases = []
        
        # 方法基本测试
        method_test = TestCase(
            name=f"test_{cls.name.lower()}_{method.name}",
            description=f"测试 {cls.name}.{method.name} 方法",
            test_type=TestType.UNIT,
            function_name=f"test_{cls.name.lower()}_{method.name}",
            imports={f"from {cls.module} import {cls.name}"},
            setup_code=[f"instance = {cls.name}()"],
            test_code=self._generate_method_test_code(method),
            assertions=self._generate_method_assertions(method)
        )
        test_cases.append(method_test)
        
        return test_cases
    
    def _generate_basic_test_code(self, func: FunctionInfo) -> List[str]:
        """生成基本测试代码"""
        code = []
        
        # 生成参数
        if func.parameters:
            args = []
            for param in func.parameters:
                if param['default']:
                    continue  # 跳过有默认值的参数
                
                # 根据类型生成测试值
                if param['type']:
                    if 'str' in param['type']:
                        args.append('"test_value"')
                    elif 'int' in param['type']:
                        args.append('42')
                    elif 'float' in param['type']:
                        args.append('3.14')
                    elif 'bool' in param['type']:
                        args.append('True')
                    elif 'list' in param['type']:
                        args.append('[1, 2, 3]')
                    elif 'dict' in param['type']:
                        args.append('{"key": "value"}')
                    else:
                        args.append('None')
                else:
                    args.append('"test_value"')  # 默认字符串
            
            if args:
                code.append(f"result = {func.name}({', '.join(args)})")
            else:
                code.append(f"result = {func.name}()")
        else:
            code.append(f"result = {func.name}()")
        
        return code
    
    def _generate_basic_assertions(self, func: FunctionInfo) -> List[str]:
        """生成基本断言"""
        assertions = []
        
        if func.return_type:
            if 'None' not in func.return_type:
                assertions.append("assert result is not None")
            
            if 'str' in func.return_type:
                assertions.append("assert isinstance(result, str)")
            elif 'int' in func.return_type:
                assertions.append("assert isinstance(result, int)")
            elif 'float' in func.return_type:
                assertions.append("assert isinstance(result, float)")
            elif 'bool' in func.return_type:
                assertions.append("assert isinstance(result, bool)")
            elif 'list' in func.return_type:
                assertions.append("assert isinstance(result, list)")
            elif 'dict' in func.return_type:
                assertions.append("assert isinstance(result, dict)")
        else:
            assertions.append("# TODO: 添加具体的断言")
        
        return assertions
    
    def _generate_boundary_test_code(self, func: FunctionInfo) -> List[str]:
        """生成边界值测试代码"""
        code = []
        
        for param in func.parameters:
            if param['type']:
                if 'int' in param['type']:
                    code.append(f"# 测试 {param['name']} 边界值")
                    code.append(f"result_min = {func.name}(0)")
                    code.append(f"result_max = {func.name}(999999)")
                elif 'str' in param['type']:
                    code.append(f"# 测试 {param['name']} 边界值")
                    code.append(f"result_empty = {func.name}('')")
                    code.append(f"result_long = {func.name}('a' * 1000)")
        
        if not code:
            code.append("# TODO: 添加边界值测试")
        
        return code
    
    def _generate_boundary_assertions(self, func: FunctionInfo) -> List[str]:
        """生成边界值断言"""
        return ["# TODO: 添加边界值断言"]
    
    def _generate_exception_test_code(self, func: FunctionInfo) -> List[str]:
        """生成异常测试代码"""
        code = []
        
        # 测试None参数
        if func.parameters:
            code.append("# 测试None参数")
            code.append(f"with pytest.raises(Exception):")
            code.append(f"    {func.name}(None)")
        
        # 测试类型错误
        for param in func.parameters:
            if param['type'] and 'str' in param['type']:
                code.append(f"# 测试 {param['name']} 类型错误")
                code.append(f"with pytest.raises(TypeError):")
                code.append(f"    {func.name}(123)")
                break
        
        if not code:
            code.append("# TODO: 添加异常测试")
        
        return code
    
    def _generate_exception_assertions(self, func: FunctionInfo) -> List[str]:
        """生成异常断言"""
        return []  # 异常测试通常使用pytest.raises，不需要额外断言
    
    def _generate_method_test_code(self, method: FunctionInfo) -> List[str]:
        """生成方法测试代码"""
        code = []
        
        if method.name == '__str__':
            code.append("result = str(instance)")
        elif method.name == '__repr__':
            code.append("result = repr(instance)")
        else:
            # 生成方法调用
            if method.parameters:
                # 跳过self参数
                params = method.parameters[1:] if method.parameters[0]['name'] == 'self' else method.parameters
                if params:
                    args = ['"test"' if not p['default'] else '' for p in params]
                    args = [arg for arg in args if arg]  # 移除空字符串
                    if args:
                        code.append(f"result = instance.{method.name}({', '.join(args)})")
                    else:
                        code.append(f"result = instance.{method.name}()")
                else:
                    code.append(f"result = instance.{method.name}()")
            else:
                code.append(f"result = instance.{method.name}()")
        
        return code
    
    def _generate_method_assertions(self, method: FunctionInfo) -> List[str]:
        """生成方法断言"""
        assertions = []
        
        if method.name in ['__str__', '__repr__']:
            assertions.append("assert isinstance(result, str)")
            assertions.append("assert len(result) > 0")
        elif method.return_type:
            if 'None' not in method.return_type:
                assertions.append("assert result is not None")
        else:
            assertions.append("# TODO: 添加具体的断言")
        
        return assertions
    
    def generate_api_tests(self, api_spec: Dict[str, Any], output_dir: str) -> str:
        """生成API测试
        
        Args:
            api_spec: API规范（OpenAPI/Swagger格式）
            output_dir: 输出目录
            
        Returns:
            生成的测试文件路径
        """
        test_cases = []
        
        # 解析API规范
        paths = api_spec.get('paths', {})
        
        for endpoint, methods in paths.items():
            for method, spec in methods.items():
                if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                    test_case = self._generate_api_test_case(endpoint, method.upper(), spec)
                    test_cases.append(test_case)
        
        return self._write_test_file(test_cases, output_dir, "test_api")
    
    def _generate_api_test_case(self, endpoint: str, method: str, spec: Dict[str, Any]) -> TestCase:
        """生成API测试用例"""
        operation_id = spec.get('operationId', f"{method.lower()}_{endpoint.replace('/', '_')}")
        summary = spec.get('summary', f"{method} {endpoint}")
        
        # 生成测试数据
        request_data = None
        if method in ['POST', 'PUT', 'PATCH']:
            request_body = spec.get('requestBody', {})
            if request_body:
                request_data = self._generate_request_data(request_body)
        
        # 获取期望状态码
        responses = spec.get('responses', {})
        expected_status = 200
        if '200' in responses:
            expected_status = 200
        elif '201' in responses:
            expected_status = 201
        elif responses:
            expected_status = int(list(responses.keys())[0])
        
        # 生成响应模式
        response_schema = None
        if str(expected_status) in responses:
            response_spec = responses[str(expected_status)]
            content = response_spec.get('content', {})
            if 'application/json' in content:
                schema = content['application/json'].get('schema', {})
                response_schema = self._extract_response_schema(schema)
        
        return TestCase(
            name=f"test_{operation_id}",
            description=f"测试 {summary}",
            test_type=TestType.API,
            function_name=f"test_{operation_id}",
            imports={"import pytest", "import requests"},
            tags=["api"],
            test_code=[],  # 将在模板中生成
            assertions=[]
        )
    
    def _generate_request_data(self, request_body: Dict[str, Any]) -> Dict[str, Any]:
        """生成请求数据"""
        content = request_body.get('content', {})
        if 'application/json' in content:
            schema = content['application/json'].get('schema', {})
            return self._generate_data_from_schema(schema)
        return {}
    
    def _generate_data_from_schema(self, schema: Dict[str, Any]) -> Any:
        """根据JSON Schema生成数据"""
        schema_type = schema.get('type', 'object')
        
        if schema_type == 'object':
            properties = schema.get('properties', {})
            data = {}
            for prop_name, prop_schema in properties.items():
                data[prop_name] = self._generate_data_from_schema(prop_schema)
            return data
        elif schema_type == 'array':
            items = schema.get('items', {})
            return [self._generate_data_from_schema(items)]
        elif schema_type == 'string':
            return "test_string"
        elif schema_type == 'integer':
            return 42
        elif schema_type == 'number':
            return 3.14
        elif schema_type == 'boolean':
            return True
        else:
            return None
    
    def _extract_response_schema(self, schema: Dict[str, Any]) -> Dict[str, str]:
        """提取响应模式"""
        result = {}
        properties = schema.get('properties', {})
        
        for prop_name, prop_schema in properties.items():
            prop_type = prop_schema.get('type', 'str')
            if prop_type == 'integer':
                result[prop_name] = 'int'
            elif prop_type == 'number':
                result[prop_name] = 'float'
            elif prop_type == 'boolean':
                result[prop_name] = 'bool'
            elif prop_type == 'array':
                result[prop_name] = 'list'
            elif prop_type == 'object':
                result[prop_name] = 'dict'
            else:
                result[prop_name] = 'str'
        
        return result
    
    def generate_database_tests(self, models: List[Dict[str, Any]], output_dir: str) -> str:
        """生成数据库测试
        
        Args:
            models: 数据模型列表
            output_dir: 输出目录
            
        Returns:
            生成的测试文件路径
        """
        test_cases = []
        
        for model in models:
            # CRUD测试
            crud_tests = self._generate_crud_tests(model)
            test_cases.extend(crud_tests)
            
            # 约束测试
            constraint_tests = self._generate_constraint_tests(model)
            test_cases.extend(constraint_tests)
        
        return self._write_test_file(test_cases, output_dir, "test_database")
    
    def _generate_crud_tests(self, model: Dict[str, Any]) -> List[TestCase]:
        """生成CRUD测试用例"""
        model_name = model['name']
        test_cases = []
        
        # Create测试
        create_test = TestCase(
            name=f"test_{model_name.lower()}_create",
            description=f"测试创建{model_name}",
            test_type=TestType.DATABASE,
            function_name=f"test_{model_name.lower()}_create",
            imports={f"from models import {model_name}"},
            tags=["database", "crud"],
            setup_code=["# 准备测试数据"],
            test_code=[
                f"# 创建{model_name}实例",
                f"instance = {model_name}(**test_data)",
                "db_session.add(instance)",
                "db_session.commit()"
            ],
            assertions=[
                "assert instance.id is not None",
                "assert instance in db_session"
            ]
        )
        test_cases.append(create_test)
        
        # Read测试
        read_test = TestCase(
            name=f"test_{model_name.lower()}_read",
            description=f"测试读取{model_name}",
            test_type=TestType.DATABASE,
            function_name=f"test_{model_name.lower()}_read",
            imports={f"from models import {model_name}"},
            tags=["database", "crud"],
            test_code=[
                f"# 查询{model_name}",
                f"instance = db_session.query({model_name}).first()"
            ],
            assertions=[
                "assert instance is not None",
                f"assert isinstance(instance, {model_name})"
            ]
        )
        test_cases.append(read_test)
        
        return test_cases
    
    def _generate_constraint_tests(self, model: Dict[str, Any]) -> List[TestCase]:
        """生成约束测试用例"""
        # 这里可以根据模型的约束生成相应的测试
        return []
    
    def generate_performance_tests(self, functions: List[str], output_dir: str, 
                                 max_execution_time: float = 1.0) -> str:
        """生成性能测试
        
        Args:
            functions: 要测试的函数列表
            output_dir: 输出目录
            max_execution_time: 最大执行时间（秒）
            
        Returns:
            生成的测试文件路径
        """
        test_cases = []
        
        for func_name in functions:
            perf_test = TestCase(
                name=f"test_{func_name}_performance",
                description=f"测试{func_name}性能",
                test_type=TestType.PERFORMANCE,
                function_name=f"test_{func_name}_performance",
                imports={f"from module import {func_name}", "import time"},
                tags=["performance"],
                test_code=[
                    "# 性能测试",
                    f"result = {func_name}(test_data)"
                ],
                assertions=[]
            )
            test_cases.append(perf_test)
        
        return self._write_test_file(test_cases, output_dir, "test_performance")
    
    def _write_test_file(self, test_cases: List[TestCase], output_dir: str, filename: str) -> str:
        """写入测试文件
        
        Args:
            test_cases: 测试用例列表
            output_dir: 输出目录
            filename: 文件名（不含扩展名）
            
        Returns:
            生成的文件路径
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        file_path = output_path / f"{filename}.py"
        
        # 收集所有导入
        all_imports = set()
        for test_case in test_cases:
            all_imports.update(test_case.imports)
        
        # 生成文件内容
        content = []
        content.append('#!/usr/bin/env python3')
        content.append(f'"""自动生成的测试文件: {filename}"""')
        content.append('')
        
        # 添加导入
        for import_stmt in sorted(all_imports):
            content.append(import_stmt)
        content.append('')
        
        # 添加测试用例
        for test_case in test_cases:
            if self.framework == TestFramework.PYTEST:
                test_content = self._render_pytest_test(test_case)
            elif self.framework == TestFramework.UNITTEST:
                test_content = self._render_unittest_test(test_case)
            else:
                test_content = self._render_pytest_test(test_case)  # 默认使用pytest
            
            content.append(test_content)
            content.append('')
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
        
        return str(file_path)
    
    def _render_pytest_test(self, test_case: TestCase) -> str:
        """渲染pytest测试"""
        template_name = 'pytest_unit'
        
        if test_case.test_type == TestType.API:
            template_name = 'api_test'
        elif test_case.test_type == TestType.DATABASE:
            template_name = 'database_test'
        elif test_case.test_type == TestType.PERFORMANCE:
            template_name = 'performance_test'
        
        template = self.templates[template_name]
        
        # 准备模板参数
        params = {
            'imports': sorted(test_case.imports),
            'function_name': test_case.function_name,
            'description': test_case.description,
            'setup_code': test_case.setup_code,
            'test_code': test_case.test_code,
            'teardown_code': test_case.teardown_code,
            'assertions': test_case.assertions,
            'tags': test_case.tags,
            'timeout': test_case.timeout,
            'skip_reason': test_case.skip_reason,
            'fixtures': test_case.fixtures,
            'parameters': test_case.parameters
        }
        
        # 处理参数化测试
        if test_case.parameters:
            param_names = ', '.join(test_case.parameters[0].keys()) if test_case.parameters else ''
            params['param_names'] = param_names
            params['function_params'] = param_names
        else:
            params['function_params'] = ''
        
        return template.render(**params)
    
    def _render_unittest_test(self, test_case: TestCase) -> str:
        """渲染unittest测试"""
        template = self.templates['unittest']
        
        class_name = f"Test{test_case.function_name.replace('test_', '').title()}"
        
        params = {
            'imports': sorted(test_case.imports),
            'class_name': class_name,
            'function_name': test_case.function_name,
            'description': test_case.description,
            'setup_code': test_case.setup_code,
            'test_code': test_case.test_code,
            'teardown_code': test_case.teardown_code,
            'assertions': test_case.assertions,
            'skip_reason': test_case.skip_reason
        }
        
        return template.render(**params)


# 使用示例
if __name__ == "__main__":
    # 创建测试用例生成器
    generator = TestCaseGenerator(TestFramework.PYTEST)
    
    # 为源文件生成单元测试
    source_file = "example_module.py"
    output_dir = "tests/generated"
    
    try:
        test_files = generator.generate_unit_tests(source_file, output_dir)
        print(f"生成的测试文件: {test_files}")
    except FileNotFoundError:
        print(f"源文件 {source_file} 不存在")
    
    # 生成API测试示例
    api_spec = {
        "paths": {
            "/users": {
                "get": {
                    "operationId": "get_users",
                    "summary": "获取用户列表",
                    "responses": {
                        "200": {
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "id": {"type": "integer"},
                                                "name": {"type": "string"},
                                                "email": {"type": "string"}
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                "post": {
                    "operationId": "create_user",
                    "summary": "创建用户",
                    "requestBody": {
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "name": {"type": "string"},
                                        "email": {"type": "string"}
                                    }
                                }
                            }
                        }
                    },
                    "responses": {
                        "201": {
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "id": {"type": "integer"},
                                            "name": {"type": "string"},
                                            "email": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    api_test_file = generator.generate_api_tests(api_spec, output_dir)
    print(f"生成的API测试文件: {api_test_file}")