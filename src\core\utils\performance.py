# -*- coding: utf-8 -*-
"""
Performance Monitoring and Metrics Utilities

This module provides comprehensive performance monitoring capabilities
for the Connect telecommunications data processing system.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import time
import threading
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from contextlib import contextmanager
from collections import defaultdict, deque
import statistics


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    operation_name: str
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    records_processed: int = 0
    bytes_processed: int = 0
    memory_used_mb: float = 0.0
    cpu_percent: float = 0.0
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def records_per_second(self) -> float:
        """Calculate records per second."""
        return self.records_processed / max(self.duration_seconds, 0.001)
    
    @property
    def bytes_per_second(self) -> float:
        """Calculate bytes per second."""
        return self.bytes_processed / max(self.duration_seconds, 0.001)
    
    @property
    def mb_per_second(self) -> float:
        """Calculate MB per second."""
        return (self.bytes_processed / (1024 * 1024)) / max(self.duration_seconds, 0.001)


@dataclass
class PerformanceStats:
    """Aggregated performance statistics."""
    operation_name: str
    total_operations: int
    successful_operations: int
    failed_operations: int
    total_duration_seconds: float
    avg_duration_seconds: float
    min_duration_seconds: float
    max_duration_seconds: float
    total_records_processed: int
    avg_records_per_second: float
    peak_records_per_second: float
    total_bytes_processed: int
    avg_mb_per_second: float
    peak_mb_per_second: float
    avg_memory_used_mb: float
    peak_memory_used_mb: float
    success_rate: float
    error_summary: Dict[str, int] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


class PerformanceMonitor:
    """Advanced performance monitoring system.
    
    Features:
    - Operation timing and metrics collection
    - Throughput measurement (records/sec, bytes/sec)
    - Memory usage tracking
    - Success/failure rate monitoring
    - Statistical analysis and reporting
    - Context-based performance tracking
    - Real-time performance alerts
    """
    
    def __init__(
        self,
        max_metrics: int = 10000,
        enable_real_time: bool = True,
        alert_thresholds: Optional[Dict[str, float]] = None
    ):
        """Initialize performance monitor.
        
        Args:
            max_metrics: Maximum number of metrics to keep in memory
            enable_real_time: Enable real-time monitoring
            alert_thresholds: Performance alert thresholds
        """
        self.max_metrics = max_metrics
        self.enable_real_time = enable_real_time
        self.alert_thresholds = alert_thresholds or {}
        
        self.logger = logging.getLogger(__name__)
        
        # Metrics storage
        self._metrics: List[PerformanceMetrics] = []
        self._metrics_by_operation: Dict[str, List[PerformanceMetrics]] = defaultdict(list)
        self._lock = threading.Lock()
        
        # Real-time monitoring
        self._active_operations: Dict[str, Dict[str, Any]] = {}
        self._operation_counter = 0
        
        # Alert callbacks
        self._alert_callbacks: List[Callable[[str, PerformanceMetrics], None]] = []
    
    def start_operation(
        self,
        operation_name: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Start tracking a performance operation.
        
        Args:
            operation_name: Name of the operation
            metadata: Additional metadata
            
        Returns:
            Operation ID for tracking
        """
        operation_id = f"{operation_name}_{self._operation_counter}_{int(time.time() * 1000)}"
        self._operation_counter += 1
        
        operation_info = {
            'operation_name': operation_name,
            'start_time': datetime.now(),
            'start_timestamp': time.time(),
            'metadata': metadata or {},
            'records_processed': 0,
            'bytes_processed': 0
        }
        
        with self._lock:
            self._active_operations[operation_id] = operation_info
        
        self.logger.debug(f"Started operation tracking: {operation_name} ({operation_id})")
        return operation_id
    
    def update_operation(
        self,
        operation_id: str,
        records_processed: Optional[int] = None,
        bytes_processed: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Update operation progress.
        
        Args:
            operation_id: Operation ID
            records_processed: Number of records processed
            bytes_processed: Number of bytes processed
            metadata: Additional metadata
        """
        with self._lock:
            if operation_id not in self._active_operations:
                self.logger.warning(f"Operation not found: {operation_id}")
                return
            
            operation = self._active_operations[operation_id]
            
            if records_processed is not None:
                operation['records_processed'] = records_processed
            
            if bytes_processed is not None:
                operation['bytes_processed'] = bytes_processed
            
            if metadata:
                operation['metadata'].update(metadata)
    
    def end_operation(
        self,
        operation_id: str,
        success: bool = True,
        error_message: Optional[str] = None,
        memory_used_mb: Optional[float] = None,
        cpu_percent: Optional[float] = None,
        final_metadata: Optional[Dict[str, Any]] = None
    ) -> PerformanceMetrics:
        """End operation tracking and record metrics.
        
        Args:
            operation_id: Operation ID
            success: Whether operation was successful
            error_message: Error message if failed
            memory_used_mb: Memory used in MB
            cpu_percent: CPU usage percentage
            final_metadata: Final metadata
            
        Returns:
            Performance metrics for the operation
        """
        end_time = datetime.now()
        end_timestamp = time.time()
        
        with self._lock:
            if operation_id not in self._active_operations:
                self.logger.warning(f"Operation not found: {operation_id}")
                # Create dummy metrics
                return PerformanceMetrics(
                    operation_name="unknown",
                    start_time=end_time,
                    end_time=end_time,
                    duration_seconds=0,
                    success=False,
                    error_message="Operation not found"
                )
            
            operation = self._active_operations.pop(operation_id)
        
        # Calculate duration
        duration = end_timestamp - operation['start_timestamp']
        
        # Merge metadata
        metadata = operation['metadata'].copy()
        if final_metadata:
            metadata.update(final_metadata)
        
        # Create metrics
        metrics = PerformanceMetrics(
            operation_name=operation['operation_name'],
            start_time=operation['start_time'],
            end_time=end_time,
            duration_seconds=duration,
            records_processed=operation['records_processed'],
            bytes_processed=operation['bytes_processed'],
            memory_used_mb=memory_used_mb or 0.0,
            cpu_percent=cpu_percent or 0.0,
            success=success,
            error_message=error_message,
            metadata=metadata
        )
        
        # Store metrics
        self._store_metrics(metrics)
        
        # Check alerts
        self._check_alerts(metrics)
        
        self.logger.debug(
            f"Completed operation: {metrics.operation_name} "
            f"({duration:.3f}s, {metrics.records_per_second:.1f} rps)"
        )
        
        return metrics
    
    def record_operation(
        self,
        operation_name: str,
        duration_seconds: float,
        records_processed: int = 0,
        bytes_processed: int = 0,
        memory_used_mb: float = 0.0,
        cpu_percent: float = 0.0,
        success: bool = True,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> PerformanceMetrics:
        """Record a completed operation directly.
        
        Args:
            operation_name: Name of the operation
            duration_seconds: Operation duration
            records_processed: Number of records processed
            bytes_processed: Number of bytes processed
            memory_used_mb: Memory used in MB
            cpu_percent: CPU usage percentage
            success: Whether operation was successful
            error_message: Error message if failed
            metadata: Additional metadata
            
        Returns:
            Performance metrics for the operation
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(seconds=duration_seconds)
        
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            start_time=start_time,
            end_time=end_time,
            duration_seconds=duration_seconds,
            records_processed=records_processed,
            bytes_processed=bytes_processed,
            memory_used_mb=memory_used_mb,
            cpu_percent=cpu_percent,
            success=success,
            error_message=error_message,
            metadata=metadata or {}
        )
        
        # Store metrics
        self._store_metrics(metrics)
        
        # Check alerts
        self._check_alerts(metrics)
        
        return metrics
    
    def _store_metrics(self, metrics: PerformanceMetrics) -> None:
        """Store metrics in memory.
        
        Args:
            metrics: Performance metrics to store
        """
        with self._lock:
            self._metrics.append(metrics)
            self._metrics_by_operation[metrics.operation_name].append(metrics)
            
            # Limit memory usage
            if len(self._metrics) > self.max_metrics:
                # Remove oldest metrics
                removed_metrics = self._metrics[:len(self._metrics) - self.max_metrics]
                self._metrics = self._metrics[-self.max_metrics:]
                
                # Update operation-specific lists
                for removed in removed_metrics:
                    op_metrics = self._metrics_by_operation[removed.operation_name]
                    if removed in op_metrics:
                        op_metrics.remove(removed)
    
    def _check_alerts(self, metrics: PerformanceMetrics) -> None:
        """Check performance alerts.
        
        Args:
            metrics: Performance metrics to check
        """
        alerts = []
        
        # Check duration threshold
        duration_threshold = self.alert_thresholds.get(f"{metrics.operation_name}_duration", 
                                                       self.alert_thresholds.get("default_duration"))
        if duration_threshold and metrics.duration_seconds > duration_threshold:
            alerts.append(f"Duration exceeded: {metrics.duration_seconds:.2f}s > {duration_threshold:.2f}s")
        
        # Check throughput threshold
        rps_threshold = self.alert_thresholds.get(f"{metrics.operation_name}_min_rps",
                                                  self.alert_thresholds.get("default_min_rps"))
        if rps_threshold and metrics.records_per_second < rps_threshold:
            alerts.append(f"Low throughput: {metrics.records_per_second:.1f} rps < {rps_threshold:.1f} rps")
        
        # Check memory threshold
        memory_threshold = self.alert_thresholds.get(f"{metrics.operation_name}_max_memory",
                                                     self.alert_thresholds.get("default_max_memory"))
        if memory_threshold and metrics.memory_used_mb > memory_threshold:
            alerts.append(f"High memory usage: {metrics.memory_used_mb:.1f}MB > {memory_threshold:.1f}MB")
        
        # Check failure
        if not metrics.success:
            alerts.append(f"Operation failed: {metrics.error_message}")
        
        # Trigger alert callbacks
        if alerts:
            alert_message = "; ".join(alerts)
            self.logger.warning(f"Performance alert for {metrics.operation_name}: {alert_message}")
            
            for callback in self._alert_callbacks:
                try:
                    callback(alert_message, metrics)
                except Exception as e:
                    self.logger.error(f"Error in alert callback: {e}")
    
    def get_operation_stats(self, operation_name: str) -> Optional[PerformanceStats]:
        """Get statistics for a specific operation.
        
        Args:
            operation_name: Name of the operation
            
        Returns:
            Performance statistics or None if no data
        """
        with self._lock:
            metrics_list = self._metrics_by_operation.get(operation_name, [])
        
        if not metrics_list:
            return None
        
        # Calculate statistics
        successful_ops = [m for m in metrics_list if m.success]
        failed_ops = [m for m in metrics_list if not m.success]
        
        durations = [m.duration_seconds for m in metrics_list]
        records_per_sec = [m.records_per_second for m in successful_ops if m.records_processed > 0]
        mb_per_sec = [m.mb_per_second for m in successful_ops if m.bytes_processed > 0]
        memory_usage = [m.memory_used_mb for m in metrics_list if m.memory_used_mb > 0]
        
        # Error summary
        error_summary = defaultdict(int)
        for op in failed_ops:
            error_type = type(op.error_message).__name__ if op.error_message else "Unknown"
            error_summary[error_type] += 1
        
        return PerformanceStats(
            operation_name=operation_name,
            total_operations=len(metrics_list),
            successful_operations=len(successful_ops),
            failed_operations=len(failed_ops),
            total_duration_seconds=sum(durations),
            avg_duration_seconds=statistics.mean(durations) if durations else 0,
            min_duration_seconds=min(durations) if durations else 0,
            max_duration_seconds=max(durations) if durations else 0,
            total_records_processed=sum(m.records_processed for m in successful_ops),
            avg_records_per_second=statistics.mean(records_per_sec) if records_per_sec else 0,
            peak_records_per_second=max(records_per_sec) if records_per_sec else 0,
            total_bytes_processed=sum(m.bytes_processed for m in successful_ops),
            avg_mb_per_second=statistics.mean(mb_per_sec) if mb_per_sec else 0,
            peak_mb_per_second=max(mb_per_sec) if mb_per_sec else 0,
            avg_memory_used_mb=statistics.mean(memory_usage) if memory_usage else 0,
            peak_memory_used_mb=max(memory_usage) if memory_usage else 0,
            success_rate=len(successful_ops) / len(metrics_list) if metrics_list else 0,
            error_summary=dict(error_summary)
        )
    
    def get_all_stats(self) -> Dict[str, PerformanceStats]:
        """Get statistics for all operations.
        
        Returns:
            Dictionary mapping operation names to their statistics
        """
        with self._lock:
            operation_names = list(self._metrics_by_operation.keys())
        
        return {
            name: self.get_operation_stats(name)
            for name in operation_names
            if self.get_operation_stats(name) is not None
        }
    
    def get_recent_metrics(
        self,
        operation_name: Optional[str] = None,
        limit: int = 100,
        since: Optional[datetime] = None
    ) -> List[PerformanceMetrics]:
        """Get recent performance metrics.
        
        Args:
            operation_name: Filter by operation name
            limit: Maximum number of metrics to return
            since: Only return metrics since this time
            
        Returns:
            List of performance metrics
        """
        with self._lock:
            if operation_name:
                metrics_list = self._metrics_by_operation.get(operation_name, [])
            else:
                metrics_list = self._metrics
        
        # Apply time filter
        if since:
            metrics_list = [m for m in metrics_list if m.start_time >= since]
        
        # Sort by start time and apply limit
        metrics_list = sorted(metrics_list, key=lambda m: m.start_time, reverse=True)
        return metrics_list[:limit]
    
    def clear_metrics(self, operation_name: Optional[str] = None) -> None:
        """Clear stored metrics.
        
        Args:
            operation_name: Clear metrics for specific operation (None for all)
        """
        with self._lock:
            if operation_name:
                # Clear specific operation
                if operation_name in self._metrics_by_operation:
                    # Remove from main list
                    self._metrics = [
                        m for m in self._metrics 
                        if m.operation_name != operation_name
                    ]
                    # Clear operation-specific list
                    del self._metrics_by_operation[operation_name]
            else:
                # Clear all metrics
                self._metrics.clear()
                self._metrics_by_operation.clear()
    
    def add_alert_callback(self, callback: Callable[[str, PerformanceMetrics], None]) -> None:
        """Add performance alert callback.
        
        Args:
            callback: Function to call when performance alert is triggered
        """
        self._alert_callbacks.append(callback)
    
    @contextmanager
    def operation_context(
        self,
        operation_name: str,
        metadata: Optional[Dict[str, Any]] = None,
        auto_update_records: bool = False,
        auto_update_bytes: bool = False
    ):
        """Context manager for operation tracking.
        
        Args:
            operation_name: Name of the operation
            metadata: Additional metadata
            auto_update_records: Automatically track records processed
            auto_update_bytes: Automatically track bytes processed
            
        Yields:
            Operation context with update methods
        """
        operation_id = self.start_operation(operation_name, metadata)
        
        context = {
            'operation_id': operation_id,
            'records_processed': 0,
            'bytes_processed': 0,
            'update_records': lambda count: self.update_operation(
                operation_id, records_processed=count
            ),
            'update_bytes': lambda count: self.update_operation(
                operation_id, bytes_processed=count
            ),
            'update_metadata': lambda meta: self.update_operation(
                operation_id, metadata=meta
            )
        }
        
        try:
            yield context
            
            # End operation successfully
            metrics = self.end_operation(
                operation_id,
                success=True,
                final_metadata={
                    'final_records': context['records_processed'],
                    'final_bytes': context['bytes_processed']
                }
            )
            
        except Exception as e:
            # End operation with error
            metrics = self.end_operation(
                operation_id,
                success=False,
                error_message=str(e)
            )
            raise
    
    def export_metrics_csv(self, file_path: str, operation_name: Optional[str] = None) -> None:
        """Export metrics to CSV file.
        
        Args:
            file_path: Path to CSV file
            operation_name: Filter by operation name
        """
        import csv
        
        metrics_list = self.get_recent_metrics(operation_name, limit=None)
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'operation_name', 'start_time', 'end_time', 'duration_seconds',
                'records_processed', 'bytes_processed', 'memory_used_mb',
                'cpu_percent', 'success', 'error_message', 'records_per_second',
                'bytes_per_second', 'mb_per_second'
            ]
            
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for metrics in metrics_list:
                writer.writerow({
                    'operation_name': metrics.operation_name,
                    'start_time': metrics.start_time.isoformat(),
                    'end_time': metrics.end_time.isoformat(),
                    'duration_seconds': metrics.duration_seconds,
                    'records_processed': metrics.records_processed,
                    'bytes_processed': metrics.bytes_processed,
                    'memory_used_mb': metrics.memory_used_mb,
                    'cpu_percent': metrics.cpu_percent,
                    'success': metrics.success,
                    'error_message': metrics.error_message or '',
                    'records_per_second': metrics.records_per_second,
                    'bytes_per_second': metrics.bytes_per_second,
                    'mb_per_second': metrics.mb_per_second
                })
        
        self.logger.info(f"Exported {len(metrics_list)} metrics to {file_path}")