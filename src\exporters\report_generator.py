__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Report generation module.

This module provides report generation functionality for various data formats.
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from .base import BaseExporter, ExportError, ExportResult

# Configure logging
logger = logging.getLogger(__name__)


class ReportGenerator(BaseExporter):
    """Report generator for creating formatted reports."""

    def __init__(
        self, output_path: Union[str, Path], template: Optional[str] = None, **kwargs
    ):
        """Initialize report generator.

        Args:
            output_path: Path where report will be saved
            template: Report template to use
            **kwargs: Additional configuration options
        """
        super().__init__(output_path, **kwargs)
        self.template = template

        # Default to HTML if no extension specified
        if not self.output_path.suffix:
            self.output_path = self.output_path.with_suffix(".html")

    async def export(self, data: Any, **kwargs) -> ExportResult:
        """Generate and export report.

        Args:
            data: Data to include in report
            **kwargs: Additional report options
                - title: Report title
                - description: Report description
                - format: Output format (html, pdf, etc.)

        Returns:
            ExportResult: Result of the report generation

        Raises:
            ExportError: If report generation fails
        """
        try:
            self.validate_data(data)
            self.prepare_output_directory()

            title = kwargs.get("title", "Data Report")
            description = kwargs.get("description", "Generated data report")
            format_type = kwargs.get("format", "html")

            # Generate report content
            report_content = self._generate_report_content(
                data, title, description, format_type
            )

            # Write report to file
            with open(self.output_path, "w", encoding="utf-8") as f:
                f.write(report_content)

            # Calculate records included
            records_exported = self._count_data_records(data)
            file_size = (
                self.output_path.stat().st_size if self.output_path.exists() else 0
            )

            logger.info(
                f"Successfully generated report with {records_exported} records at {self.output_path}"
            )

            return ExportResult(
                success=True,
                file_path=self.output_path,
                records_exported=records_exported,
                file_size_bytes=file_size,
                metadata={
                    "title": title,
                    "description": description,
                    "format": format_type,
                    "template": self.template,
                },
            )

        except Exception as e:
            error_msg = f"Failed to generate report: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ExportResult(success=False, error_message=error_msg)

    def _generate_report_content(
        self, data: Any, title: str, description: str, format_type: str
    ) -> str:
        """Generate report content based on format."""
        if format_type.lower() == "html":
            return self._generate_html_report(data, title, description)
        else:
            # Default to simple text format
            return self._generate_text_report(data, title, description)

    def _generate_html_report(self, data: Any, title: str, description: str) -> str:
        """Generate HTML report."""
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .summary {{ background-color: #f9f9f9; padding: 10px; margin: 10px 0; }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    <p>{description}</p>

    <div class="summary">
        <h2>Data Summary</h2>
        <p>Records: {self._count_data_records(data)}</p>
        <p>Data Type: {type(data).__name__}</p>
    </div>

    <h2>Data Preview</h2>
    {self._data_to_html_table(data)}
</body>
</html>
        """
        return html_content

    def _generate_text_report(self, data: Any, title: str, description: str) -> str:
        """Generate text report."""
        text_content = f"""
{title}
{'=' * len(title)}

{description}

Data Summary:
- Records: {self._count_data_records(data)}
- Data Type: {type(data).__name__}

Data Preview:
{str(data)[:1000]}{'...' if len(str(data)) > 1000 else ''}
        """
        return text_content

    def _data_to_html_table(self, data: Any) -> str:
        """Convert data to HTML table."""
        if isinstance(data, pd.DataFrame):
            return data.head(10).to_html(classes="data-table")
        elif isinstance(data, list) and data:
            if isinstance(data[0], dict):
                df = pd.DataFrame(data[:10])
                return df.to_html(classes="data-table")
            else:
                return f"<pre>{str(data[:10])}</pre>"
        else:
            return f"<pre>{str(data)}</pre>"

    def _count_data_records(self, data: Any) -> int:
        """Count records in data."""
        if isinstance(data, pd.DataFrame):
            return len(data)
        elif isinstance(data, list):
            return len(data)
        elif isinstance(data, dict):
            return 1
        else:
            return 1
