#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect平台访问控制模块

本模块提供数据库访问控制功能，包括用户权限管理、角色控制等。

作者: Connect质量工程团队
日期: 2024-01-20
"""

import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class Permission(Enum):
    """权限枚举"""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"


class Role(Enum):
    """角色枚举"""
    USER = "user"
    ANALYST = "analyst"
    ADMIN = "admin"
    SUPER_ADMIN = "super_admin"


@dataclass
class AccessRule:
    """访问规则"""
    resource: str
    permissions: List[Permission]
    roles: List[Role]
    conditions: Optional[Dict[str, Any]] = None


class AccessController:
    """访问控制器"""
    
    def __init__(self):
        """初始化访问控制器"""
        self.rules: List[AccessRule] = []
        self._setup_default_rules()
    
    def _setup_default_rules(self) -> None:
        """设置默认访问规则"""
        # 默认规则
        default_rules = [
            AccessRule(
                resource="database",
                permissions=[Permission.READ],
                roles=[Role.USER, Role.ANALYST, Role.ADMIN, Role.SUPER_ADMIN]
            ),
            AccessRule(
                resource="database",
                permissions=[Permission.WRITE],
                roles=[Role.ANALYST, Role.ADMIN, Role.SUPER_ADMIN]
            ),
            AccessRule(
                resource="database",
                permissions=[Permission.DELETE],
                roles=[Role.ADMIN, Role.SUPER_ADMIN]
            ),
            AccessRule(
                resource="system",
                permissions=[Permission.ADMIN],
                roles=[Role.SUPER_ADMIN]
            )
        ]
        self.rules.extend(default_rules)
    
    def check_permission(self, user_role: str, resource: str, permission: str) -> bool:
        """检查用户权限
        
        Args:
            user_role: 用户角色
            resource: 资源名称
            permission: 权限类型
            
        Returns:
            bool: 是否有权限
        """
        try:
            role = Role(user_role)
            perm = Permission(permission)
            
            for rule in self.rules:
                if (rule.resource == resource and 
                    perm in rule.permissions and 
                    role in rule.roles):
                    return True
            
            return False
            
        except ValueError:
            logger.warning(f"Invalid role or permission: {user_role}, {permission}")
            return False
    
    def add_rule(self, rule: AccessRule) -> None:
        """添加访问规则
        
        Args:
            rule: 访问规则
        """
        self.rules.append(rule)
        logger.info(f"Added access rule for resource: {rule.resource}")
    
    def remove_rule(self, resource: str, role: str) -> bool:
        """移除访问规则
        
        Args:
            resource: 资源名称
            role: 角色名称
            
        Returns:
            bool: 是否成功移除
        """
        try:
            role_enum = Role(role)
            initial_count = len(self.rules)
            
            self.rules = [
                rule for rule in self.rules 
                if not (rule.resource == resource and role_enum in rule.roles)
            ]
            
            removed = len(self.rules) < initial_count
            if removed:
                logger.info(f"Removed access rule for resource: {resource}, role: {role}")
            
            return removed
            
        except ValueError:
            logger.warning(f"Invalid role: {role}")
            return False
    
    def get_user_permissions(self, user_role: str, resource: str) -> List[str]:
        """获取用户对资源的权限列表
        
        Args:
            user_role: 用户角色
            resource: 资源名称
            
        Returns:
            List[str]: 权限列表
        """
        try:
            role = Role(user_role)
            permissions = set()
            
            for rule in self.rules:
                if rule.resource == resource and role in rule.roles:
                    permissions.update([perm.value for perm in rule.permissions])
            
            return list(permissions)
            
        except ValueError:
            logger.warning(f"Invalid role: {user_role}")
            return []
    
    def validate_access(self, user_id: str, user_role: str, resource: str, 
                       action: str) -> Dict[str, Any]:
        """验证访问权限
        
        Args:
            user_id: 用户ID
            user_role: 用户角色
            resource: 资源名称
            action: 操作类型
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        has_permission = self.check_permission(user_role, resource, action)
        
        result = {
            "user_id": user_id,
            "user_role": user_role,
            "resource": resource,
            "action": action,
            "allowed": has_permission,
            "timestamp": logging.Formatter().formatTime(logging.LogRecord(
                name="access_control", level=logging.INFO, pathname="", lineno=0,
                msg="", args=(), exc_info=None
            ))
        }
        
        if not has_permission:
            logger.warning(
                f"Access denied for user {user_id} (role: {user_role}) "
                f"to {action} on {resource}"
            )
        
        return result