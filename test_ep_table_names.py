#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EP数据表名生成的完整流程
验证TableNamingManager与EPImporter的集成
"""

import sys
import os
import yaml
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database.utils.table_naming import TableNamingManager
from src.importers.ep_importer import EPImporter
from src.config.core import ConnectConfigManager

def test_ep_table_names():
    """测试EP数据的表名生成流程"""
    
    print("🔍 测试EP表名生成流程...")
    
    # 1. 加载database.yaml配置
    try:
        config_path = Path(__file__).parent / 'config' / 'database.yaml'
        with open(config_path, 'r', encoding='utf-8') as f:
            database_config = yaml.safe_load(f)
        print(f"✅ Database config loaded from: {config_path}")
        
        ep_config = database_config['telecom_data_sources']['ep']
        print(f"📊 EP配置: schema={ep_config['schema_name']}, pattern={ep_config['table_name_pattern']}")
        
    except Exception as e:
        print(f"❌ Error loading database config: {e}")
        return
    
    # 2. 测试TableNamingManager
    try:
        naming_manager = TableNamingManager(database_config)
        print(f"✅ TableNamingManager initialized")
    except Exception as e:
        print(f"❌ Error initializing TableNamingManager: {e}")
        return
    
    # 3. 测试实际文件的表名生成
    test_files = [
        "D:/connect/data/input/ep/2025/CW03/GSMCELL_CW03.xlsx",
        "D:/connect/data/input/ep/2025/CW07/LTECELL_CW07.xlsx", 
        "D:/connect/data/input/ep/2025/CW10/NRCELL_CW10.xlsx",
        "D:/connect/data/input/ep/2024/CW48/TEF_SITE_CW48.xlsx",
        "D:/connect/data/input/ep/2024/CW01/TEF_Sites_CW01.xlsx"
    ]
    
    print("\n📋 实际文件表名生成测试:")
    for file_path_str in test_files:
        file_path = Path(file_path_str)
        if file_path.exists():
            table_name = naming_manager.generate_table_name('ep', file_path)
            print(f"  ✅ {file_path.name} → {table_name}")
        else:
            # 即使文件不存在，也测试表名生成逻辑
            table_name = naming_manager.generate_table_name('ep', file_path)
            print(f"  📝 {file_path.name} → {table_name} (文件不存在，仅测试逻辑)")
    
    # 4. 测试EPImporter的集成
    try:
        print("\n🔧 测试EPImporter集成...")
        
        # 创建EPImporter实例，使用database_config而不是ConnectConfig
        ep_importer = EPImporter(database_config)
        print(f"✅ EPImporter initialized with database config")
        
        # 测试get_table_name方法
        for file_path_str in test_files[:3]:  # 只测试前3个
            file_path = Path(file_path_str)
            try:
                table_name = ep_importer.get_table_name(file_path)
                print(f"  📊 EPImporter: {file_path.name} → {table_name}")
            except Exception as e:
                print(f"  ❌ EPImporter error for {file_path.name}: {e}")
                
    except Exception as e:
        print(f"❌ Error testing EPImporter: {e}")
    
    # 5. 验证配置一致性
    print("\n🔍 配置一致性验证:")
    expected_pattern = "ep_{cell_type}_{year}_cw{week}"
    actual_pattern = ep_config.get('table_name_pattern')
    if actual_pattern == expected_pattern:
        print(f"  ✅ 表名模式匹配: {actual_pattern}")
    else:
        print(f"  ❌ 表名模式不匹配: 期望={expected_pattern}, 实际={actual_pattern}")
    
    expected_schema = "ep_to2"
    actual_schema = ep_config.get('schema_name')
    if actual_schema == expected_schema:
        print(f"  ✅ Schema名称匹配: {actual_schema}")
    else:
        print(f"  ❌ Schema名称不匹配: 期望={expected_schema}, 实际={actual_schema}")

if __name__ == "__main__":
    test_ep_table_names()