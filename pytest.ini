# Pytest configuration file
[pytest]

# Test discovery
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Test directories
testpaths = tests

# Minimum version
minversion = 6.0

# Add current directory to Python path
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --asyncio-mode=auto
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --durations=10
    --maxfail=5

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    performance: Performance tests
    load: Load testing
    slow: Slow running tests (deselect with '-m "not slow"')
    database: Tests requiring database connection
    file_io: Tests involving file I/O operations
    geospatial: Geospatial related tests
    import_export: Import/export related tests
    security: Security tests
    compliance: Compliance and regulatory tests
    skip_ci: Skip in CI environment
    requires_network: Tests requiring network access
    etl: ETL pipeline tests
    monitoring: Monitoring and logging tests
    schema: Schema management tests
    connection: Connection management tests
    crud: CRUD operations tests
    validation: Data validation tests
    p0: Critical core features (MVP)
    p1: Important features (Production ready)
    p2: Valuable features (Feature enhancement)
    p3: Optional features (Advanced features)
    batch: Batch processing tests
    async_test: Asynchronous operation tests
    memory: Memory usage tests
    concurrency: Concurrency tests

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    error::FutureWarning

# Test timeout (in seconds)
# timeout = 300  # Requires pytest-timeout plugin

# Parallel execution
# Uncomment to enable parallel test execution
# -n auto

# JUnit XML output for CI
# --junitxml=test-results.xml