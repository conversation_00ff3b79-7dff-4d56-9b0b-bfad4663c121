"""统一验证框架安装脚本

提供自动化安装、配置和部署功能，确保验证框架
能够在不同环境中正确安装和运行。
"""

import os
import sys
import subprocess
import shutil
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import platform
import tempfile
from datetime import datetime

# 版本信息
VERSION = "1.0.0"
MIN_PYTHON_VERSION = (3, 8)


@dataclass
class InstallConfig:
    """安装配置"""
    # 安装路径
    install_path: str = None
    config_path: str = None
    log_path: str = None
    
    # 安装选项
    install_dependencies: bool = True
    create_config: bool = True
    setup_logging: bool = True
    run_tests: bool = True
    
    # 环境配置
    environment: str = 'production'  # development, testing, production
    database_url: str = None
    redis_url: str = None
    
    # 性能配置
    enable_monitoring: bool = True
    enable_caching: bool = True
    parallel_workers: int = None
    
    def __post_init__(self):
        if self.install_path is None:
            self.install_path = str(Path.cwd() / 'validation_framework')
        if self.config_path is None:
            self.config_path = str(Path(self.install_path) / 'config')
        if self.log_path is None:
            self.log_path = str(Path(self.install_path) / 'logs')
        if self.parallel_workers is None:
            self.parallel_workers = min(4, os.cpu_count() or 1)


class SystemChecker:
    """系统环境检查器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('installer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def check_system_requirements(self) -> Tuple[bool, List[str]]:
        """检查系统要求"""
        issues = []
        
        # 检查Python版本
        if sys.version_info < MIN_PYTHON_VERSION:
            issues.append(
                f"Python版本过低: {sys.version_info[:2]}, "
                f"需要 >= {MIN_PYTHON_VERSION}"
            )
        
        # 检查操作系统
        os_name = platform.system()
        if os_name not in ['Windows', 'Linux', 'Darwin']:
            issues.append(f"不支持的操作系统: {os_name}")
        
        # 检查内存
        try:
            import psutil
            memory_gb = psutil.virtual_memory().total / (1024**3)
            if memory_gb < 4:
                issues.append(f"内存不足: {memory_gb:.1f}GB, 建议 >= 4GB")
        except ImportError:
            issues.append("无法检查内存，请确保有足够内存 (>= 4GB)")
        
        # 检查磁盘空间
        try:
            disk_usage = shutil.disk_usage('.')
            free_gb = disk_usage.free / (1024**3)
            if free_gb < 1:
                issues.append(f"磁盘空间不足: {free_gb:.1f}GB, 需要 >= 1GB")
        except Exception:
            issues.append("无法检查磁盘空间")
        
        return len(issues) == 0, issues
    
    def check_dependencies(self) -> Tuple[bool, List[str]]:
        """检查依赖项"""
        required_packages = [
            'pandas>=1.3.0',
            'numpy>=1.20.0',
            'psutil>=5.8.0',
            'pydantic>=1.8.0',
            'sqlalchemy>=1.4.0',
            'redis>=4.0.0'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            package_name = package.split('>=')[0]
            try:
                __import__(package_name)
            except ImportError:
                missing_packages.append(package)
        
        return len(missing_packages) == 0, missing_packages
    
    def check_database_connection(self, database_url: str) -> Tuple[bool, str]:
        """检查数据库连接"""
        if not database_url:
            return True, "未配置数据库连接"
        
        try:
            from sqlalchemy import create_engine
            engine = create_engine(database_url)
            with engine.connect() as conn:
                conn.execute('SELECT 1')
            return True, "数据库连接正常"
        except Exception as e:
            return False, f"数据库连接失败: {e}"
    
    def check_redis_connection(self, redis_url: str) -> Tuple[bool, str]:
        """检查Redis连接"""
        if not redis_url:
            return True, "未配置Redis连接"
        
        try:
            import redis
            r = redis.from_url(redis_url)
            r.ping()
            return True, "Redis连接正常"
        except Exception as e:
            return False, f"Redis连接失败: {e}"


class DependencyInstaller:
    """依赖安装器"""
    
    def __init__(self):
        self.logger = logging.getLogger('installer')
    
    def install_requirements(self, requirements_file: str = None) -> bool:
        """安装依赖包"""
        try:
            if requirements_file and Path(requirements_file).exists():
                cmd = [sys.executable, '-m', 'pip', 'install', '-r', requirements_file]
            else:
                # 默认依赖包
                packages = [
                    'pandas>=1.3.0',
                    'numpy>=1.20.0',
                    'psutil>=5.8.0',
                    'pydantic>=1.8.0',
                    'sqlalchemy>=1.4.0',
                    'redis>=4.0.0',
                    'pytest>=6.0.0',
                    'pytest-cov>=2.12.0'
                ]
                cmd = [sys.executable, '-m', 'pip', 'install'] + packages
            
            self.logger.info("安装依赖包...")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info("依赖包安装成功")
                return True
            else:
                self.logger.error(f"依赖包安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"安装依赖包时出错: {e}")
            return False
    
    def create_requirements_file(self, output_path: str) -> bool:
        """创建requirements.txt文件"""
        requirements = [
            "# 统一验证框架依赖包",
            "pandas>=1.3.0",
            "numpy>=1.20.0",
            "psutil>=5.8.0",
            "pydantic>=1.8.0",
            "sqlalchemy>=1.4.0",
            "redis>=4.0.0",
            "",
            "# 开发和测试依赖",
            "pytest>=6.0.0",
            "pytest-cov>=2.12.0",
            "pytest-asyncio>=0.18.0",
            "black>=21.0.0",
            "flake8>=3.9.0",
            "mypy>=0.910",
            "",
            "# 可选依赖",
            "jupyter>=1.0.0",
            "matplotlib>=3.3.0",
            "seaborn>=0.11.0"
        ]
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(requirements))
            self.logger.info(f"requirements.txt已创建: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"创建requirements.txt失败: {e}")
            return False


class ConfigGenerator:
    """配置文件生成器"""
    
    def __init__(self):
        self.logger = logging.getLogger('installer')
    
    def create_default_config(self, config_path: str, environment: str = 'production') -> bool:
        """创建默认配置文件"""
        try:
            config_dir = Path(config_path)
            config_dir.mkdir(parents=True, exist_ok=True)
            
            # 主配置文件
            main_config = {
                "environment": environment,
                "version": VERSION,
                "validation": {
                    "mode": "strict" if environment == 'production' else "lenient",
                    "parallel_enabled": True,
                    "max_workers": min(4, os.cpu_count() or 1),
                    "timeout_seconds": 300,
                    "batch_size": 10000
                },
                "performance": {
                    "enable_caching": True,
                    "cache_size": 1000,
                    "enable_monitoring": True,
                    "memory_limit_mb": 1024
                },
                "logging": {
                    "level": "INFO" if environment == 'production' else "DEBUG",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    "file_enabled": True,
                    "console_enabled": True
                },
                "database": {
                    "url": "postgresql://user:password@localhost:5432/connect",
                    "pool_size": 5,
                    "max_overflow": 10,
                    "echo": environment != 'production'
                },
                "redis": {
                    "url": "redis://localhost:6379/0",
                    "max_connections": 10,
                    "socket_timeout": 30
                },
                "telecom": {
                    "country_code": "86",
                    "timezone": "Asia/Shanghai",
                    "frequency_bands": ["900", "1800", "2100", "2600"]
                }
            }
            
            config_file = config_dir / 'config.json'
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(main_config, f, indent=2, ensure_ascii=False)
            
            # 环境特定配置
            env_configs = {
                'development': {
                    "validation": {"mode": "lenient"},
                    "logging": {"level": "DEBUG"},
                    "performance": {"enable_monitoring": False}
                },
                'testing': {
                    "validation": {"mode": "strict", "timeout_seconds": 60},
                    "logging": {"level": "WARNING"},
                    "database": {"url": "sqlite:///test.db"}
                },
                'production': {
                    "validation": {"mode": "strict"},
                    "logging": {"level": "INFO"},
                    "performance": {"enable_monitoring": True}
                }
            }
            
            for env, config in env_configs.items():
                env_file = config_dir / f'config_{env}.json'
                with open(env_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置文件已创建: {config_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建配置文件失败: {e}")
            return False
    
    def create_logging_config(self, log_path: str) -> bool:
        """创建日志配置"""
        try:
            log_dir = Path(log_path)
            log_dir.mkdir(parents=True, exist_ok=True)
            
            logging_config = {
                "version": 1,
                "disable_existing_loggers": False,
                "formatters": {
                    "standard": {
                        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                    },
                    "detailed": {
                        "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s"
                    }
                },
                "handlers": {
                    "console": {
                        "class": "logging.StreamHandler",
                        "level": "INFO",
                        "formatter": "standard",
                        "stream": "ext://sys.stdout"
                    },
                    "file": {
                        "class": "logging.handlers.RotatingFileHandler",
                        "level": "DEBUG",
                        "formatter": "detailed",
                        "filename": str(log_dir / "validation.log"),
                        "maxBytes": 10485760,
                        "backupCount": 5
                    },
                    "error_file": {
                        "class": "logging.handlers.RotatingFileHandler",
                        "level": "ERROR",
                        "formatter": "detailed",
                        "filename": str(log_dir / "error.log"),
                        "maxBytes": 10485760,
                        "backupCount": 5
                    }
                },
                "loggers": {
                    "validation": {
                        "level": "DEBUG",
                        "handlers": ["console", "file", "error_file"],
                        "propagate": False
                    }
                },
                "root": {
                    "level": "INFO",
                    "handlers": ["console", "file"]
                }
            }
            
            config_file = log_dir / 'logging.json'
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(logging_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"日志配置已创建: {config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建日志配置失败: {e}")
            return False


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.logger = logging.getLogger('installer')
    
    def run_installation_tests(self, install_path: str) -> Tuple[bool, List[str]]:
        """运行安装测试"""
        test_results = []
        all_passed = True
        
        # 测试模块导入
        try:
            sys.path.insert(0, install_path)
            
            # 测试核心模块
            modules_to_test = [
                'validation.core',
                'validation.validators',
                'validation.rules',
                'validation.factory',
                'validation.config',
                'validation.monitoring',
                'validation.utils'
            ]
            
            for module_name in modules_to_test:
                try:
                    __import__(module_name)
                    test_results.append(f"✅ {module_name} 导入成功")
                except ImportError as e:
                    test_results.append(f"❌ {module_name} 导入失败: {e}")
                    all_passed = False
            
            # 测试基本功能
            try:
                from validation.factory import ValidationFactory
                from validation.utils import generate_sample_data
                
                # 创建测试数据
                data = generate_sample_data('cdr', 100)
                
                # 创建验证框架
                factory = ValidationFactory()
                framework = factory.create_framework('cdr')
                
                # 执行验证
                result = framework.validate(data)
                
                test_results.append("✅ 基本验证功能测试通过")
                
            except Exception as e:
                test_results.append(f"❌ 基本验证功能测试失败: {e}")
                all_passed = False
            
        except Exception as e:
            test_results.append(f"❌ 测试环境设置失败: {e}")
            all_passed = False
        finally:
            if install_path in sys.path:
                sys.path.remove(install_path)
        
        return all_passed, test_results
    
    def run_unit_tests(self, test_path: str) -> Tuple[bool, str]:
        """运行单元测试"""
        try:
            if not Path(test_path).exists():
                return True, "未找到测试文件，跳过单元测试"
            
            cmd = [sys.executable, '-m', 'pytest', test_path, '-v', '--tb=short']
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=test_path)
            
            if result.returncode == 0:
                return True, "单元测试全部通过"
            else:
                return False, f"单元测试失败:\n{result.stdout}\n{result.stderr}"
                
        except Exception as e:
            return False, f"运行单元测试时出错: {e}"


class ValidationFrameworkInstaller:
    """统一验证框架安装器"""
    
    def __init__(self, config: InstallConfig):
        self.config = config
        self.logger = self._setup_logger()
        self.system_checker = SystemChecker()
        self.dependency_installer = DependencyInstaller()
        self.config_generator = ConfigGenerator()
        self.test_runner = TestRunner()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('installer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def install(self) -> bool:
        """执行完整安装流程"""
        self.logger.info(f"🚀 开始安装统一验证框架 v{VERSION}")
        
        try:
            # 1. 系统环境检查
            if not self._check_system_requirements():
                return False
            
            # 2. 创建安装目录
            if not self._create_directories():
                return False
            
            # 3. 安装依赖
            if self.config.install_dependencies:
                if not self._install_dependencies():
                    return False
            
            # 4. 复制框架文件
            if not self._copy_framework_files():
                return False
            
            # 5. 创建配置文件
            if self.config.create_config:
                if not self._create_configuration():
                    return False
            
            # 6. 设置日志
            if self.config.setup_logging:
                if not self._setup_logging():
                    return False
            
            # 7. 运行测试
            if self.config.run_tests:
                if not self._run_tests():
                    self.logger.warning("测试失败，但安装继续")
            
            # 8. 创建启动脚本
            if not self._create_startup_scripts():
                return False
            
            # 9. 验证安装
            if not self._verify_installation():
                return False
            
            self.logger.info("✅ 统一验证框架安装成功!")
            self._print_installation_summary()
            return True
            
        except Exception as e:
            self.logger.error(f"安装过程中出现错误: {e}")
            return False
    
    def _check_system_requirements(self) -> bool:
        """检查系统要求"""
        self.logger.info("检查系统要求...")
        
        # 检查基本系统要求
        system_ok, system_issues = self.system_checker.check_system_requirements()
        if not system_ok:
            self.logger.error("系统要求检查失败:")
            for issue in system_issues:
                self.logger.error(f"  - {issue}")
            return False
        
        # 检查依赖项
        if self.config.install_dependencies:
            deps_ok, missing_deps = self.system_checker.check_dependencies()
            if not deps_ok:
                self.logger.info(f"将安装缺失的依赖项: {missing_deps}")
        
        # 检查数据库连接
        if self.config.database_url:
            db_ok, db_msg = self.system_checker.check_database_connection(self.config.database_url)
            if not db_ok:
                self.logger.warning(f"数据库连接检查: {db_msg}")
        
        # 检查Redis连接
        if self.config.redis_url:
            redis_ok, redis_msg = self.system_checker.check_redis_connection(self.config.redis_url)
            if not redis_ok:
                self.logger.warning(f"Redis连接检查: {redis_msg}")
        
        self.logger.info("✅ 系统要求检查通过")
        return True
    
    def _create_directories(self) -> bool:
        """创建安装目录"""
        self.logger.info("创建安装目录...")
        
        try:
            directories = [
                self.config.install_path,
                self.config.config_path,
                self.config.log_path,
                os.path.join(self.config.install_path, 'validation'),
                os.path.join(self.config.install_path, 'tests'),
                os.path.join(self.config.install_path, 'scripts'),
                os.path.join(self.config.install_path, 'docs')
            ]
            
            for directory in directories:
                Path(directory).mkdir(parents=True, exist_ok=True)
                self.logger.debug(f"创建目录: {directory}")
            
            self.logger.info(f"✅ 安装目录已创建: {self.config.install_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建安装目录失败: {e}")
            return False
    
    def _install_dependencies(self) -> bool:
        """安装依赖项"""
        self.logger.info("安装依赖项...")
        
        # 创建requirements.txt
        requirements_file = os.path.join(self.config.install_path, 'requirements.txt')
        if not self.dependency_installer.create_requirements_file(requirements_file):
            return False
        
        # 安装依赖
        if not self.dependency_installer.install_requirements(requirements_file):
            return False
        
        self.logger.info("✅ 依赖项安装完成")
        return True
    
    def _copy_framework_files(self) -> bool:
        """复制框架文件"""
        self.logger.info("复制框架文件...")
        
        try:
            # 获取当前脚本所在目录
            source_dir = Path(__file__).parent
            target_dir = Path(self.config.install_path) / 'validation'
            
            # 复制Python文件
            python_files = [
                '__init__.py',
                'core.py',
                'validators.py',
                'rules.py',
                'factory.py',
                'exceptions.py',
                'config.py',
                'monitoring.py',
                'utils.py',
                'deploy.py'
            ]
            
            for file_name in python_files:
                source_file = source_dir / file_name
                target_file = target_dir / file_name
                
                if source_file.exists():
                    shutil.copy2(source_file, target_file)
                    self.logger.debug(f"复制文件: {file_name}")
                else:
                    self.logger.warning(f"源文件不存在: {file_name}")
            
            # 复制测试文件
            tests_source = source_dir / 'tests'
            tests_target = Path(self.config.install_path) / 'tests'
            
            if tests_source.exists():
                shutil.copytree(tests_source, tests_target, dirs_exist_ok=True)
                self.logger.debug("复制测试文件")
            
            # 复制文档文件
            docs_files = ['README.md']
            for file_name in docs_files:
                source_file = source_dir / file_name
                target_file = Path(self.config.install_path) / 'docs' / file_name
                
                if source_file.exists():
                    shutil.copy2(source_file, target_file)
                    self.logger.debug(f"复制文档: {file_name}")
            
            self.logger.info("✅ 框架文件复制完成")
            return True
            
        except Exception as e:
            self.logger.error(f"复制框架文件失败: {e}")
            return False
    
    def _create_configuration(self) -> bool:
        """创建配置文件"""
        self.logger.info("创建配置文件...")
        
        if not self.config_generator.create_default_config(
            self.config.config_path, 
            self.config.environment
        ):
            return False
        
        self.logger.info("✅ 配置文件创建完成")
        return True
    
    def _setup_logging(self) -> bool:
        """设置日志配置"""
        self.logger.info("设置日志配置...")
        
        if not self.config_generator.create_logging_config(self.config.log_path):
            return False
        
        self.logger.info("✅ 日志配置设置完成")
        return True
    
    def _run_tests(self) -> bool:
        """运行测试"""
        self.logger.info("运行安装测试...")
        
        # 运行安装测试
        tests_passed, test_results = self.test_runner.run_installation_tests(
            self.config.install_path
        )
        
        for result in test_results:
            self.logger.info(result)
        
        if not tests_passed:
            self.logger.error("安装测试失败")
            return False
        
        # 运行单元测试
        test_path = os.path.join(self.config.install_path, 'tests')
        unit_tests_passed, unit_test_msg = self.test_runner.run_unit_tests(test_path)
        
        if unit_tests_passed:
            self.logger.info(f"✅ {unit_test_msg}")
        else:
            self.logger.warning(f"⚠️ {unit_test_msg}")
        
        return True
    
    def _create_startup_scripts(self) -> bool:
        """创建启动脚本"""
        self.logger.info("创建启动脚本...")
        
        try:
            scripts_dir = Path(self.config.install_path) / 'scripts'
            
            # Python启动脚本
            python_script = scripts_dir / 'start_validation.py'
            python_content = f'''
#!/usr/bin/env python3
"""统一验证框架启动脚本"""

import sys
import os
from pathlib import Path

# 添加框架路径
framework_path = Path(__file__).parent.parent / 'validation'
sys.path.insert(0, str(framework_path))

# 设置配置路径
config_path = Path(__file__).parent.parent / 'config' / 'config.json'
os.environ['VALIDATION_CONFIG_PATH'] = str(config_path)

if __name__ == '__main__':
    from validation.factory import ValidationFactory
    from validation.utils import generate_sample_data
    
    print("🚀 统一验证框架启动")
    print(f"版本: {VERSION}")
    print(f"配置: {{config_path}}")
    
    # 创建验证工厂
    factory = ValidationFactory()
    print("✅ 验证工厂创建成功")
    
    # 测试CDR验证
    print("\n测试CDR验证...")
    cdr_data = generate_sample_data('cdr', 100)
    cdr_framework = factory.create_framework('cdr')
    cdr_result = cdr_framework.validate(cdr_data)
    print(f"CDR验证结果: {{cdr_result.is_valid}}, 问题数: {{len(cdr_result.issues)}}")
    
    # 测试KPI验证
    print("\n测试KPI验证...")
    kpi_data = generate_sample_data('kpi', 100)
    kpi_framework = factory.create_framework('kpi')
    kpi_result = kpi_framework.validate(kpi_data)
    print(f"KPI验证结果: {{kpi_result.is_valid}}, 问题数: {{len(kpi_result.issues)}}")
    
    print("\n✅ 验证框架运行正常")
'''
            
            with open(python_script, 'w', encoding='utf-8') as f:
                f.write(python_content)
            
            # 使脚本可执行
            if platform.system() != 'Windows':
                os.chmod(python_script, 0o755)
            
            # Windows批处理脚本
            if platform.system() == 'Windows':
                batch_script = scripts_dir / 'start_validation.bat'
                batch_content = f'''
@echo off
cd /d "{self.config.install_path}"
python scripts\start_validation.py
pause
'''
                with open(batch_script, 'w', encoding='utf-8') as f:
                    f.write(batch_content)
            
            # Unix shell脚本
            else:
                shell_script = scripts_dir / 'start_validation.sh'
                shell_content = f'''
#!/bin/bash
cd "{self.config.install_path}"
python3 scripts/start_validation.py
'''
                with open(shell_script, 'w', encoding='utf-8') as f:
                    f.write(shell_content)
                os.chmod(shell_script, 0o755)
            
            self.logger.info("✅ 启动脚本创建完成")
            return True
            
        except Exception as e:
            self.logger.error(f"创建启动脚本失败: {e}")
            return False
    
    def _verify_installation(self) -> bool:
        """验证安装"""
        self.logger.info("验证安装...")
        
        try:
            # 检查关键文件
            required_files = [
                'validation/__init__.py',
                'validation/core.py',
                'validation/factory.py',
                'config/config.json',
                'scripts/start_validation.py'
            ]
            
            for file_path in required_files:
                full_path = Path(self.config.install_path) / file_path
                if not full_path.exists():
                    self.logger.error(f"缺少关键文件: {file_path}")
                    return False
            
            # 测试导入
            sys.path.insert(0, os.path.join(self.config.install_path, 'validation'))
            try:
                import core
                import factory
                self.logger.debug("模块导入测试通过")
            except ImportError as e:
                self.logger.error(f"模块导入失败: {e}")
                return False
            finally:
                if os.path.join(self.config.install_path, 'validation') in sys.path:
                    sys.path.remove(os.path.join(self.config.install_path, 'validation'))
            
            self.logger.info("✅ 安装验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"安装验证失败: {e}")
            return False
    
    def _print_installation_summary(self):
        """打印安装摘要"""
        summary = f"""

🎉 统一验证框架安装完成!

📁 安装路径: {self.config.install_path}
📁 配置路径: {self.config.config_path}
📁 日志路径: {self.config.log_path}

🚀 快速开始:
  1. 运行启动脚本: python {self.config.install_path}/scripts/start_validation.py
  2. 查看配置文件: {self.config.config_path}/config.json
  3. 查看日志文件: {self.config.log_path}/validation.log

📚 文档:
  - README: {self.config.install_path}/docs/README.md
  - 测试: {self.config.install_path}/tests/

🔧 环境: {self.config.environment}
📦 版本: {VERSION}

如有问题，请查看日志文件或联系技术支持。
        """
        
        print(summary)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='统一验证框架安装器')
    parser.add_argument('--install-path', default=None,
                       help='安装路径')
    parser.add_argument('--environment', choices=['development', 'testing', 'production'],
                       default='production', help='环境类型')
    parser.add_argument('--skip-deps', action='store_true',
                       help='跳过依赖安装')
    parser.add_argument('--skip-tests', action='store_true',
                       help='跳过测试')
    parser.add_argument('--database-url',
                       help='数据库连接URL')
    parser.add_argument('--redis-url',
                       help='Redis连接URL')
    parser.add_argument('--workers', type=int,
                       help='并行工作进程数')
    
    args = parser.parse_args()
    
    # 创建安装配置
    config = InstallConfig(
        install_path=args.install_path,
        environment=args.environment,
        install_dependencies=not args.skip_deps,
        run_tests=not args.skip_tests,
        database_url=args.database_url,
        redis_url=args.redis_url,
        parallel_workers=args.workers
    )
    
    # 执行安装
    installer = ValidationFrameworkInstaller(config)
    success = installer.install()
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()