"""Unit tests for database operations components."""

import asyncio
from datetime import date, datetime
from decimal import Decimal
from typing import Any, Dict, List
from unittest.mock import Async<PERSON>ock, <PERSON><PERSON>ock, Mock, patch

import pandas as pd
import pytest

from src.config.models import DatabaseConfig
from src.database.exceptions import OperationError, ValidationError
from src.database.operations.bulk_operations import BulkOperations

# Import components to test
from src.database.operations.crud import CRUDOperations
from src.database.query_builder.builder import QueryBuilder
from src.database.operations.transaction_manager import TransactionManager


class TestCRUDOperations:
    """Test cases for CRUDOperations class."""

    @pytest.fixture
    def mock_connection(self):
        """Create mock database connection."""
        connection = AsyncMock()
        connection.fetch.return_value = []
        connection.fetchrow.return_value = None
        connection.execute.return_value = None
        
        # Mock for table existence check and count operations
        def mock_fetchval_side_effect(*args, **kwargs):
            query = args[0] if args else ""
            query_lower = query.lower()
            if "count(*)" in query_lower or "count()" in query_lower:
                if "age" in query_lower and "25" in query:
                    return 15  # Count with age condition
                return 42  # Count without conditions
            elif "information_schema.tables" in query_lower:
                return True  # Table exists check
            elif "select 1" in query_lower and "limit 1" in query_lower:
                return 1  # Record exists
            return True  # Default for other checks
        
        connection.fetchval.side_effect = mock_fetchval_side_effect
        
        # Mock table columns for users table
        mock_columns = [
            {"column_name": "id", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('users_id_seq'::regclass)"},
            {"column_name": "name", "data_type": "character varying", "is_nullable": "YES", "column_default": None},
            {"column_name": "email", "data_type": "character varying", "is_nullable": "YES", "column_default": None},
            {"column_name": "age", "data_type": "integer", "is_nullable": "YES", "column_default": None}
        ]
        
        def mock_fetch_side_effect(query, *args):
            if "information_schema.columns" in query:
                return mock_columns
            elif "UPDATE" in query.upper() and "users" in query:
                # Return the updated record for UPDATE queries
                return [{"id": 1, "name": "John Smith", "email": "<EMAIL>", "age": 30}]
            elif "DELETE FROM" in query.upper():
                # Return the deleted record for DELETE queries
                return [{"id": 1, "name": "John Doe"}]
            elif "SELECT" in query.upper() and "users" in query:
                # Return sample records for SELECT queries
                # Check for pagination (LIMIT and OFFSET)
                if "LIMIT" in query.upper() and "OFFSET" in query.upper():
                    return [
                        {"id": 11, "name": "User 11"},
                        {"id": 12, "name": "User 12"},
                    ]
                # Check for ordering (ORDER BY)
                elif "ORDER BY" in query.upper():
                    return [
                        {"id": 1, "name": "Alice", "age": 25},
                        {"id": 2, "name": "Bob", "age": 30},
                    ]
                elif "age = 25" in query or "age=25" in query:
                    return [
                        {"id": 1, "name": "John Doe", "age": 30},
                        {"id": 2, "name": "Jane Doe", "age": 25},
                    ]
                else:
                    return [
                        {"id": 1, "name": "Alice", "age": 25},
                        {"id": 2, "name": "Bob", "age": 30},
                    ]
            return []
        
        connection.fetch.side_effect = mock_fetch_side_effect
        return connection

    @pytest.fixture
    def mock_session_manager(self, mock_connection):
        """Create mock session manager."""
        session_manager = AsyncMock()
        session_manager.__aenter__.return_value = mock_connection
        session_manager.__aexit__.return_value = None
        return session_manager

    @pytest.fixture
    def crud_ops(self, mock_session_manager):
        """Create CRUDOperations instance."""
        return CRUDOperations(mock_session_manager)

    @pytest.mark.asyncio
    async def test_create_record(self, crud_ops, mock_connection):
        """Test creating a new record."""
        data = {"name": "John Doe", "email": "<EMAIL>", "age": 30}
        mock_connection.fetchrow.return_value = {"id": 1, **data}

        result = await crud_ops.create("users", data)

        assert result["id"] == 1
        assert result["name"] == "John Doe"
        mock_connection.fetchrow.assert_called_once()

    @pytest.mark.asyncio
    async def test_read_record_by_id(self, crud_ops, mock_connection):
        """Test reading a record by ID."""
        expected_record = {"id": 1, "name": "John Doe", "email": "<EMAIL>"}
        mock_connection.fetchrow.return_value = expected_record

        result = await crud_ops.read("users", 1)

        assert result == expected_record
        mock_connection.fetchrow.assert_called_once()

    @pytest.mark.asyncio
    async def test_read_records_with_conditions(self, crud_ops, mock_connection):
        """Test reading records with WHERE conditions."""
        expected_records = [
            {"id": 1, "name": "John Doe", "age": 30},
            {"id": 2, "name": "Jane Doe", "age": 25},
        ]
        mock_connection.fetch.return_value = expected_records

        conditions = {"age": 25}  # Simple equality condition
        result = await crud_ops.read_many("users", conditions)

        assert len(result) == 2
        assert result == expected_records
        assert mock_connection.fetch.call_count >= 1

    @pytest.mark.asyncio
    async def test_update_record(self, crud_ops, mock_connection):
        """Test updating a record."""
        update_data = {"name": "John Smith", "email": "<EMAIL>"}
        updated_record = {"id": 1, **update_data, "age": 30}
        mock_connection.fetch.return_value = [updated_record]

        result = await crud_ops.update_by_id("users", 1, update_data)

        assert result["name"] == "John Smith"
        assert result["email"] == "<EMAIL>"
        assert mock_connection.fetch.call_count >= 1

    @pytest.mark.asyncio
    async def test_delete_record(self, crud_ops, mock_connection):
        """Test deleting a record by ID."""
        mock_connection.fetch.return_value = [{"id": 1, "name": "John Doe"}]

        result = await crud_ops.delete_by_id("users", 1)

        assert result["id"] == 1
        assert result["name"] == "John Doe"
        # fetch is called multiple times: once for table metadata, once for delete
        assert mock_connection.fetch.call_count >= 1

    @pytest.mark.asyncio
    async def test_upsert_record(self, crud_ops, mock_connection):
        """Test upserting (insert or update) a record."""
        data = {"email": "<EMAIL>", "name": "John Doe", "age": 30}
        mock_connection.fetchrow.return_value = {"id": 1, **data}

        result = await crud_ops.upsert("users", data, conflict_columns=["email"])

        assert result["email"] == "<EMAIL>"
        assert result["name"] == "John Doe"
        mock_connection.fetchrow.assert_called_once()

    @pytest.mark.asyncio
    async def test_count_records(self, crud_ops, mock_connection):
        """Test counting records."""
        count = await crud_ops.count("users")

        assert count == 42
        # fetchval is called multiple times: once for table existence check, once for count
        assert mock_connection.fetchval.call_count >= 1

    @pytest.mark.asyncio
    async def test_count_with_conditions(self, crud_ops, mock_connection):
        """Test counting records with conditions."""
        conditions = {"age": 25}  # Simple equality condition
        count = await crud_ops.count("users", conditions)

        assert count == 15
        # fetchval is called multiple times: once for table existence check, once for count
        assert mock_connection.fetchval.call_count >= 1

    @pytest.mark.asyncio
    async def test_exists_check(self, crud_ops, mock_connection):
        """Test checking if record exists."""
        # Define mock side effect for exists test
        def exists_mock_fetchval_side_effect(*args, **kwargs):
            query = args[0] if args else ""
            query_lower = query.lower()
            if "information_schema.tables" in query_lower:
                return True  # Table exists check
            elif "select 1" in query_lower and "limit 1" in query_lower:
                return 1  # Record exists
            return True  # Default for other checks
        
        # fetchval is called multiple times: once for table existence check, once for exists query
        mock_connection.fetchval.side_effect = exists_mock_fetchval_side_effect

        exists = await crud_ops.exists("users", {"email": "<EMAIL>"})

        assert exists is True
        # fetchval is called at least once (table existence + exists query)
        assert mock_connection.fetchval.call_count >= 1

    @pytest.mark.asyncio
    async def test_pagination(self, crud_ops, mock_connection):
        """Test paginated record retrieval."""
        expected_records = [
            {"id": 11, "name": "User 11"},
            {"id": 12, "name": "User 12"},
        ]
        mock_connection.fetch.return_value = expected_records

        result = await crud_ops.read_many("users", limit=2, offset=10)

        assert len(result) == 2
        assert result == expected_records
        assert mock_connection.fetch.call_count >= 1

    @pytest.mark.asyncio
    async def test_ordering(self, crud_ops, mock_connection):
        """Test ordered record retrieval."""
        expected_records = [
            {"id": 1, "name": "Alice", "age": 25},
            {"id": 2, "name": "Bob", "age": 30},
        ]
        mock_connection.fetch.return_value = expected_records

        result = await crud_ops.read_many("users", order_by=[("name", "ASC")])

        assert result == expected_records
        assert mock_connection.fetch.call_count >= 1


class TestBulkOperations:
    """Test cases for BulkOperations class."""

    @pytest.fixture
    def mock_connection(self):
        """Create mock database connection."""
        connection = AsyncMock()
        connection.executemany.return_value = None
        connection.copy_records_to_table.return_value = None
        return connection

    @pytest.fixture
    def bulk_ops(self, mock_connection):
        """Create BulkOperations instance."""
        return BulkOperations(mock_connection)

    @pytest.fixture
    def sample_dataframe(self):
        """Create sample DataFrame for testing."""
        return pd.DataFrame(
            {
                "name": ["Alice", "Bob", "Charlie"],
                "age": [25, 30, 35],
                "email": [
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            }
        )

    @pytest.mark.asyncio
    async def test_bulk_insert_from_dataframe(
        self, bulk_ops, mock_connection, sample_dataframe
    ):
        """Test bulk insert from pandas DataFrame."""
        await bulk_ops.bulk_insert_dataframe("users", sample_dataframe)

        mock_connection.copy_records_to_table.assert_called_once()
        call_args = mock_connection.copy_records_to_table.call_args
        assert call_args[0][0] == "users"  # table name
        assert len(call_args[1]["records"]) == 3  # number of records

    @pytest.mark.asyncio
    async def test_bulk_insert_from_list(self, bulk_ops, mock_connection):
        """Test bulk insert from list of dictionaries."""
        records = [
            {"name": "Alice", "age": 25, "email": "<EMAIL>"},
            {"name": "Bob", "age": 30, "email": "<EMAIL>"},
        ]

        await bulk_ops.bulk_insert("users", records)

        mock_connection.executemany.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_update(self, bulk_ops, mock_connection):
        """Test bulk update operations."""
        updates = [
            {"id": 1, "name": "Alice Updated", "age": 26},
            {"id": 2, "name": "Bob Updated", "age": 31},
        ]

        await bulk_ops.bulk_update("users", updates, key_column="id")

        mock_connection.executemany.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_upsert(self, bulk_ops, mock_connection):
        """Test bulk upsert operations."""
        records = [
            {"email": "<EMAIL>", "name": "Alice", "age": 25},
            {"email": "<EMAIL>", "name": "Bob", "age": 30},
        ]

        await bulk_ops.bulk_upsert("users", records, conflict_columns=["email"])

        mock_connection.executemany.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_delete(self, bulk_ops, mock_connection):
        """Test bulk delete operations."""
        ids_to_delete = [1, 2, 3, 4, 5]

        await bulk_ops.bulk_delete("users", ids_to_delete, key_column="id")

        mock_connection.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_chunked_processing(self, bulk_ops, mock_connection):
        """Test processing large datasets in chunks."""
        # Create large dataset
        large_records = [{"name": f"User{i}", "age": 20 + i} for i in range(1000)]

        await bulk_ops.bulk_insert("users", large_records, chunk_size=100)

        # Should be called 10 times (1000 records / 100 chunk_size)
        assert mock_connection.executemany.call_count == 10

    @pytest.mark.asyncio
    async def test_data_validation_before_insert(self, bulk_ops, sample_dataframe):
        """Test data validation before bulk operations."""
        # Test with invalid data types
        invalid_df = sample_dataframe.copy()
        invalid_df.loc[0, "age"] = "invalid_age"

        with pytest.raises(ValidationError):
            await bulk_ops.bulk_insert_dataframe("users", invalid_df, validate=True)

    @pytest.mark.asyncio
    async def test_progress_tracking(self, bulk_ops, mock_connection):
        """Test progress tracking for bulk operations."""
        records = [{"name": f"User{i}"} for i in range(100)]
        progress_callback = Mock()

        await bulk_ops.bulk_insert(
            "users", records, chunk_size=10, progress_callback=progress_callback
        )

        # Progress callback should be called for each chunk
        assert progress_callback.call_count == 10

    @pytest.mark.asyncio
    async def test_error_handling_in_bulk_operations(self, bulk_ops, mock_connection):
        """Test error handling during bulk operations."""
        mock_connection.executemany.side_effect = Exception("Database error")

        records = [{"name": "Test User"}]

        with pytest.raises(OperationError):
            await bulk_ops.bulk_insert("users", records)


class TestQueryBuilder:
    """Test cases for QueryBuilder class."""

    @pytest.fixture
    def query_builder(self):
        """Create QueryBuilder instance."""
        return QueryBuilder()

    def test_select_query_basic(self, query_builder):
        """Test basic SELECT query building."""
        query, params = query_builder.select("users").build()

        assert query == "SELECT * FROM users"
        assert params == []

    def test_select_with_columns(self, query_builder):
        """Test SELECT query with specific columns."""
        query, params = query_builder.select("users", ["id", "name", "email"]).build()

        assert query == "SELECT id, name, email FROM users"
        assert params == []

    def test_select_with_where_conditions(self, query_builder):
        """Test SELECT query with WHERE conditions."""
        query, params = (
            query_builder.select("users")
            .where("age", ">", 18)
            .where("status", "=", "active")
            .build()
        )

        assert "WHERE" in query
        assert "age > $1" in query
        assert "status = $2" in query
        assert params == [18, "active"]

    def test_select_with_joins(self, query_builder):
        """Test SELECT query with JOINs."""
        query, params = (
            query_builder.select("users", ["users.name", "profiles.bio"])
            .join("profiles", "users.id = profiles.user_id")
            .build()
        )

        assert "JOIN profiles ON users.id = profiles.user_id" in query
        assert "users.name, profiles.bio" in query

    def test_select_with_order_by(self, query_builder):
        """Test SELECT query with ORDER BY."""
        query, params = (
            query_builder.select("users")
            .order_by("name", "ASC")
            .order_by("created_at", "DESC")
            .build()
        )

        assert "ORDER BY name ASC, created_at DESC" in query

    def test_select_with_limit_offset(self, query_builder):
        """Test SELECT query with LIMIT and OFFSET."""
        query, params = query_builder.select("users").limit(10).offset(20).build()

        assert "LIMIT 10" in query
        assert "OFFSET 20" in query

    def test_insert_query(self, query_builder):
        """Test INSERT query building."""
        data = {"name": "John Doe", "email": "<EMAIL>", "age": 30}
        query, params = query_builder.insert("users", data).build()

        assert "INSERT INTO users" in query
        assert "(name, email, age)" in query
        assert "VALUES ($1, $2, $3)" in query
        assert params == ["John Doe", "<EMAIL>", 30]

    def test_update_query(self, query_builder):
        """Test UPDATE query building."""
        data = {"name": "John Smith", "email": "<EMAIL>"}
        query, params = query_builder.update("users", data).where("id", "=", 1).build()

        assert "UPDATE users SET" in query
        assert "name = $1" in query
        assert "email = $2" in query
        assert "WHERE id = $3" in query
        assert params == ["John Smith", "<EMAIL>", 1]

    def test_delete_query(self, query_builder):
        """Test DELETE query building."""
        query, params = query_builder.delete("users").where("id", "=", 1).build()

        assert query == "DELETE FROM users WHERE id = $1"
        assert params == [1]

    def test_complex_where_conditions(self, query_builder):
        """Test complex WHERE conditions."""
        query, params = (
            query_builder.select("users")
            .where("age", ">=", 18)
            .where("age", "<=", 65)
            .where_in("status", ["active", "pending"])
            .where_like("name", "%john%")
            .build()
        )

        assert "age >= $1" in query
        assert "age <= $2" in query
        assert "status IN ($3, $4)" in query
        assert "name LIKE $5" in query
        assert params == [18, 65, "active", "pending", "%john%"]

    def test_subquery_support(self, query_builder):
        """Test subquery support."""
        subquery = (
            QueryBuilder().select("orders", ["user_id"]).where("total", ">", 1000)
        )

        query, params = query_builder.select("users").where_in("id", subquery).build()

        assert "WHERE id IN (" in query
        assert "SELECT user_id FROM orders" in query
        assert "total > $1" in query
        assert params == [1000]

    def test_aggregate_functions(self, query_builder):
        """Test aggregate functions in queries."""
        query, params = (
            query_builder.select(
                "orders", ["COUNT(*) as order_count", "SUM(total) as total_amount"]
            )
            .group_by("user_id")
            .having("COUNT(*)", ">", 5)
            .build()
        )

        assert "COUNT(*) as order_count" in query
        assert "SUM(total) as total_amount" in query
        assert "GROUP BY user_id" in query
        assert "HAVING COUNT(*) > $1" in query
        assert params == [5]


class TestTransactionManager:
    """Test cases for TransactionManager class."""

    @pytest.fixture
    def mock_connection(self):
        """Create mock database connection."""
        connection = AsyncMock()
        mock_transaction = AsyncMock()
        connection.transaction.return_value.__aenter__ = AsyncMock(
            return_value=mock_transaction
        )
        connection.transaction.return_value.__aexit__ = AsyncMock(return_value=None)
        return connection

    @pytest.fixture
    def transaction_manager(self, mock_connection):
        """Create TransactionManager instance."""
        return TransactionManager(mock_connection)

    @pytest.mark.asyncio
    async def test_transaction_context_manager(
        self, transaction_manager, mock_connection
    ):
        """Test transaction as context manager."""
        async with transaction_manager:
            await mock_connection.execute("INSERT INTO users (name) VALUES ('test')")

        mock_connection.transaction.assert_called_once()
        mock_connection.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_transaction_commit(self, transaction_manager, mock_connection):
        """Test transaction commit."""
        await transaction_manager.begin()
        await mock_connection.execute("INSERT INTO users (name) VALUES ('test')")
        await transaction_manager.commit()

        mock_connection.transaction.assert_called_once()

    @pytest.mark.asyncio
    async def test_transaction_rollback(self, transaction_manager, mock_connection):
        """Test transaction rollback."""
        await transaction_manager.begin()
        await mock_connection.execute("INSERT INTO users (name) VALUES ('test')")
        await transaction_manager.rollback()

        mock_connection.transaction.assert_called_once()

    @pytest.mark.asyncio
    async def test_transaction_rollback_on_exception(
        self, transaction_manager, mock_connection
    ):
        """Test automatic rollback on exception."""
        mock_connection.execute.side_effect = Exception("Database error")

        with pytest.raises(Exception):
            async with transaction_manager:
                await mock_connection.execute(
                    "INSERT INTO users (name) VALUES ('test')"
                )

        mock_connection.transaction.assert_called_once()

    @pytest.mark.asyncio
    async def test_nested_transactions(self, transaction_manager, mock_connection):
        """Test nested transaction handling."""
        async with transaction_manager:
            await mock_connection.execute("INSERT INTO users (name) VALUES ('user1')")

            # Nested transaction (savepoint)
            async with transaction_manager:
                await mock_connection.execute(
                    "INSERT INTO users (name) VALUES ('user2')"
                )

        # Should handle nested transactions with savepoints
        assert mock_connection.transaction.call_count >= 1

    @pytest.mark.asyncio
    async def test_transaction_isolation_levels(
        self, transaction_manager, mock_connection
    ):
        """Test different transaction isolation levels."""
        await transaction_manager.begin(isolation="READ_COMMITTED")
        await transaction_manager.commit()

        await transaction_manager.begin(isolation="SERIALIZABLE")
        await transaction_manager.commit()

        assert mock_connection.transaction.call_count == 2

    @pytest.mark.asyncio
    async def test_transaction_timeout(self, transaction_manager, mock_connection):
        """Test transaction timeout handling."""
        with patch("asyncio.wait_for") as mock_wait_for:
            mock_wait_for.side_effect = asyncio.TimeoutError()

            with pytest.raises(asyncio.TimeoutError):
                async with transaction_manager.with_timeout(5.0):
                    await asyncio.sleep(10)  # Simulate long operation

    def test_transaction_status_tracking(self, transaction_manager):
        """Test transaction status tracking."""
        assert not transaction_manager.is_active
        assert transaction_manager.status == "IDLE"

        # Status should change during transaction lifecycle
        # This would be tested with actual transaction operations

    @pytest.mark.asyncio
    async def test_concurrent_transactions(self, mock_connection):
        """Test handling multiple concurrent transactions."""
        tm1 = TransactionManager(mock_connection)
        tm2 = TransactionManager(mock_connection)

        async def transaction1():
            async with tm1:
                await asyncio.sleep(0.1)
                await mock_connection.execute(
                    "INSERT INTO users (name) VALUES ('user1')"
                )

        async def transaction2():
            async with tm2:
                await asyncio.sleep(0.1)
                await mock_connection.execute(
                    "INSERT INTO users (name) VALUES ('user2')"
                )

        # Run transactions concurrently
        await asyncio.gather(transaction1(), transaction2())

        # Both transactions should complete
        assert mock_connection.transaction.call_count == 2
