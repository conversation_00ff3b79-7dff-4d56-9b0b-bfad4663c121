"""统一验证框架工厂测试

测试ValidationFactory的创建、缓存、验证等功能。
"""

import pytest
import pandas as pd
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

from ..factory import (
    ValidationFactory,
    get_validation_factory,
    validate_cdr_data,
    validate_kpi_data,
    validate_cfg_data,
    validate_file
)
from ..core import ValidationFramework, ValidationSeverity
from ..exceptions import ValidationError, ValidationConfigError
from .conftest import performance_test, memory_test


class TestValidationFactory:
    """验证工厂测试类"""
    
    def test_factory_initialization(self):
        """测试工厂初始化"""
        factory = ValidationFactory()
        assert factory is not None
        assert hasattr(factory, '_cache')
        assert hasattr(factory, '_config')
    
    def test_factory_with_config(self):
        """测试带配置的工厂初始化"""
        config = {
            'cache_enabled': True,
            'max_cache_size': 50,
            'parallel_validation': True,
            'max_workers': 4
        }
        factory = ValidationFactory(config)
        assert factory._config['cache_enabled'] is True
        assert factory._config['max_cache_size'] == 50
    
    def test_create_framework_cdr(self, sample_cdr_data):
        """测试创建CDR验证框架"""
        factory = ValidationFactory()
        framework = factory.create_framework('cdr')
        
        assert isinstance(framework, ValidationFramework)
        result = framework.validate(sample_cdr_data)
        assert result.is_valid
    
    def test_create_framework_kpi(self, sample_kpi_data):
        """测试创建KPI验证框架"""
        factory = ValidationFactory()
        framework = factory.create_framework('kpi')
        
        assert isinstance(framework, ValidationFramework)
        result = framework.validate(sample_kpi_data)
        assert result.is_valid
    
    def test_create_framework_cfg(self, sample_cfg_data):
        """测试创建CFG验证框架"""
        factory = ValidationFactory()
        framework = factory.create_framework('cfg')
        
        assert isinstance(framework, ValidationFramework)
        result = framework.validate(sample_cfg_data)
        assert result.is_valid
    
    def test_create_framework_invalid_type(self):
        """测试创建无效类型验证框架"""
        factory = ValidationFactory()
        
        with pytest.raises(ValidationError):
            factory.create_framework('invalid_type')
    
    def test_get_framework_caching(self, sample_cdr_data):
        """测试验证框架缓存"""
        factory = ValidationFactory({'cache_enabled': True})
        
        # 第一次获取
        framework1 = factory.get_framework('cdr')
        # 第二次获取（应该从缓存）
        framework2 = factory.get_framework('cdr')
        
        # 应该是同一个实例（如果使用缓存）
        assert framework1 is framework2
        
        # 验证功能正常
        result = framework1.validate(sample_cdr_data)
        assert result.is_valid
    
    def test_get_framework_no_caching(self, sample_cdr_data):
        """测试不使用缓存的验证框架"""
        factory = ValidationFactory({'cache_enabled': False})
        
        # 每次获取都应该是新实例
        framework1 = factory.get_framework('cdr')
        framework2 = factory.get_framework('cdr')
        
        # 功能应该正常
        result1 = framework1.validate(sample_cdr_data)
        result2 = framework2.validate(sample_cdr_data)
        
        assert result1.is_valid
        assert result2.is_valid
    
    def test_validate_data_cdr(self, sample_cdr_data):
        """测试验证CDR数据"""
        factory = ValidationFactory()
        
        result = factory.validate_data(sample_cdr_data, 'cdr')
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_validate_data_kpi(self, sample_kpi_data):
        """测试验证KPI数据"""
        factory = ValidationFactory()
        
        result = factory.validate_data(sample_kpi_data, 'kpi')
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_validate_data_cfg(self, sample_cfg_data):
        """测试验证CFG数据"""
        factory = ValidationFactory()
        
        result = factory.validate_data(sample_cfg_data, 'cfg')
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_validate_data_invalid(self, invalid_cdr_data):
        """测试验证无效数据"""
        factory = ValidationFactory()
        
        result = factory.validate_data(invalid_cdr_data, 'cdr')
        assert not result.is_valid
        assert len(result.issues) > 0
    
    def test_validate_file_csv(self, temp_csv_file):
        """测试验证CSV文件"""
        factory = ValidationFactory()
        
        result = factory.validate_file(temp_csv_file, 'cdr')
        assert result.is_valid
    
    def test_validate_file_nonexistent(self):
        """测试验证不存在的文件"""
        factory = ValidationFactory()
        
        result = factory.validate_file('/non/existent/file.csv', 'cdr')
        assert not result.is_valid
        assert any("File does not exist" in issue.message for issue in result.issues)
    
    def test_validate_cdr_convenience_method(self, sample_cdr_data):
        """测试CDR便捷验证方法"""
        factory = ValidationFactory()
        
        result = factory.validate_cdr(sample_cdr_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_validate_kpi_convenience_method(self, sample_kpi_data):
        """测试KPI便捷验证方法"""
        factory = ValidationFactory()
        
        result = factory.validate_kpi(sample_kpi_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_validate_cfg_convenience_method(self, sample_cfg_data):
        """测试CFG便捷验证方法"""
        factory = ValidationFactory()
        
        result = factory.validate_cfg(sample_cfg_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_clear_cache(self, sample_cdr_data):
        """测试清除缓存"""
        factory = ValidationFactory({'cache_enabled': True})
        
        # 创建缓存项
        framework1 = factory.get_framework('cdr')
        assert len(factory._cache) > 0
        
        # 清除缓存
        factory.clear_cache()
        assert len(factory._cache) == 0
        
        # 重新获取应该创建新实例
        framework2 = factory.get_framework('cdr')
        # 功能应该正常
        result = framework2.validate(sample_cdr_data)
        assert result.is_valid
    
    def test_get_cache_info(self):
        """测试获取缓存信息"""
        factory = ValidationFactory({'cache_enabled': True})
        
        # 初始状态
        info = factory.get_cache_info()
        assert 'size' in info
        assert 'max_size' in info
        assert 'hit_rate' in info
        
        # 创建一些缓存项
        factory.get_framework('cdr')
        factory.get_framework('kpi')
        
        info = factory.get_cache_info()
        assert info['size'] > 0
    
    def test_parallel_validation(self, sample_cdr_data, sample_kpi_data):
        """测试并行验证"""
        factory = ValidationFactory({
            'parallel_validation': True,
            'max_workers': 2
        })
        
        # 准备多个数据集
        datasets = [
            (sample_cdr_data, 'cdr'),
            (sample_kpi_data, 'kpi')
        ]
        
        results = factory.validate_parallel(datasets)
        assert len(results) == 2
        assert all(result.is_valid for result in results)
    
    def test_parallel_validation_disabled(self, sample_cdr_data, sample_kpi_data):
        """测试禁用并行验证"""
        factory = ValidationFactory({
            'parallel_validation': False
        })
        
        datasets = [
            (sample_cdr_data, 'cdr'),
            (sample_kpi_data, 'kpi')
        ]
        
        results = factory.validate_parallel(datasets)
        assert len(results) == 2
        assert all(result.is_valid for result in results)
    
    def test_factory_configuration_update(self):
        """测试工厂配置更新"""
        factory = ValidationFactory()
        
        # 更新配置
        new_config = {
            'cache_enabled': False,
            'max_workers': 8
        }
        factory.update_config(new_config)
        
        assert factory._config['cache_enabled'] is False
        assert factory._config['max_workers'] == 8
    
    def test_factory_statistics(self, sample_cdr_data):
        """测试工厂统计信息"""
        factory = ValidationFactory()
        
        # 执行一些验证
        factory.validate_cdr(sample_cdr_data)
        factory.validate_kpi(sample_cdr_data)  # 故意用错误类型
        
        stats = factory.get_statistics()
        assert 'total_validations' in stats
        assert 'successful_validations' in stats
        assert 'failed_validations' in stats
        assert stats['total_validations'] >= 2


class TestGlobalFactory:
    """全局工厂测试类"""
    
    def test_get_global_factory(self):
        """测试获取全局工厂"""
        factory1 = get_validation_factory()
        factory2 = get_validation_factory()
        
        # 应该是同一个实例
        assert factory1 is factory2
    
    def test_global_factory_functionality(self, sample_cdr_data):
        """测试全局工厂功能"""
        factory = get_validation_factory()
        
        result = factory.validate_cdr(sample_cdr_data)
        assert result.is_valid
    
    def test_convenience_functions(self, sample_cdr_data, sample_kpi_data, sample_cfg_data):
        """测试便捷函数"""
        # 测试CDR验证
        result = validate_cdr_data(sample_cdr_data)
        assert result.is_valid
        
        # 测试KPI验证
        result = validate_kpi_data(sample_kpi_data)
        assert result.is_valid
        
        # 测试CFG验证
        result = validate_cfg_data(sample_cfg_data)
        assert result.is_valid
    
    def test_convenience_file_validation(self, temp_csv_file):
        """测试便捷文件验证"""
        result = validate_file(temp_csv_file, 'cdr')
        assert result.is_valid
    
    def test_convenience_functions_with_invalid_data(self, invalid_cdr_data, invalid_kpi_data):
        """测试便捷函数处理无效数据"""
        # 测试无效CDR数据
        result = validate_cdr_data(invalid_cdr_data)
        assert not result.is_valid
        
        # 测试无效KPI数据
        result = validate_kpi_data(invalid_kpi_data)
        assert not result.is_valid


class TestFactoryPerformance:
    """工厂性能测试类"""
    
    @performance_test(max_time=1.0)
    def test_factory_creation_performance(self):
        """测试工厂创建性能"""
        # 创建多个工厂实例
        factories = [ValidationFactory() for _ in range(10)]
        assert len(factories) == 10
    
    @performance_test(max_time=2.0)
    def test_framework_creation_performance(self):
        """测试框架创建性能"""
        factory = ValidationFactory({'cache_enabled': True})
        
        # 创建多个框架
        frameworks = []
        for _ in range(20):
            frameworks.append(factory.get_framework('cdr'))
        
        assert len(frameworks) == 20
    
    @performance_test(max_time=3.0)
    def test_validation_performance(self, large_dataset):
        """测试验证性能"""
        factory = ValidationFactory()
        
        result = factory.validate_data(large_dataset, 'cdr')
        # 应该在时间限制内完成
    
    @memory_test(max_memory_mb=100)
    def test_factory_memory_usage(self, large_dataset):
        """测试工厂内存使用"""
        factory = ValidationFactory({'cache_enabled': True})
        
        # 执行多次验证
        for _ in range(5):
            result = factory.validate_data(large_dataset, 'cdr')
        
        # 内存使用应该在限制内
    
    @performance_test(max_time=5.0)
    def test_parallel_validation_performance(self, large_dataset):
        """测试并行验证性能"""
        factory = ValidationFactory({
            'parallel_validation': True,
            'max_workers': 4
        })
        
        # 准备多个数据集
        datasets = [(large_dataset, 'cdr') for _ in range(4)]
        
        results = factory.validate_parallel(datasets)
        assert len(results) == 4
    
    def test_cache_performance(self, sample_cdr_data):
        """测试缓存性能"""
        factory = ValidationFactory({'cache_enabled': True})
        
        # 第一次访问（创建缓存）
        start_time = datetime.now()
        framework1 = factory.get_framework('cdr')
        first_time = (datetime.now() - start_time).total_seconds()
        
        # 第二次访问（从缓存）
        start_time = datetime.now()
        framework2 = factory.get_framework('cdr')
        second_time = (datetime.now() - start_time).total_seconds()
        
        # 缓存访问应该更快
        assert second_time <= first_time
        assert framework1 is framework2


class TestFactoryErrorHandling:
    """工厂错误处理测试类"""
    
    def test_invalid_configuration(self):
        """测试无效配置"""
        with pytest.raises(ValidationConfigError):
            ValidationFactory({
                'max_workers': -1  # 无效值
            })
    
    def test_validation_with_none_data(self):
        """测试验证None数据"""
        factory = ValidationFactory()
        
        with pytest.raises(ValidationError):
            factory.validate_data(None, 'cdr')
    
    def test_validation_with_empty_dataframe(self, empty_dataframe):
        """测试验证空DataFrame"""
        factory = ValidationFactory()
        
        result = factory.validate_data(empty_dataframe, 'cdr')
        assert not result.is_valid
        assert any("empty" in issue.message.lower() for issue in result.issues)
    
    def test_file_validation_with_invalid_path(self):
        """测试无效路径文件验证"""
        factory = ValidationFactory()
        
        result = factory.validate_file('', 'cdr')
        assert not result.is_valid
    
    def test_parallel_validation_error_handling(self, sample_cdr_data):
        """测试并行验证错误处理"""
        factory = ValidationFactory({
            'parallel_validation': True,
            'max_workers': 2
        })
        
        # 包含有效和无效数据
        datasets = [
            (sample_cdr_data, 'cdr'),
            (None, 'cdr'),  # 这会导致错误
            (sample_cdr_data, 'invalid_type')  # 这也会导致错误
        ]
        
        results = factory.validate_parallel(datasets)
        # 应该返回所有结果，包括错误的
        assert len(results) == 3
    
    def test_cache_overflow_handling(self):
        """测试缓存溢出处理"""
        factory = ValidationFactory({
            'cache_enabled': True,
            'max_cache_size': 2  # 很小的缓存
        })
        
        # 创建超过缓存大小的项目
        factory.get_framework('cdr')
        factory.get_framework('kpi')
        factory.get_framework('cfg')  # 这应该触发缓存清理
        
        # 缓存大小应该在限制内
        info = factory.get_cache_info()
        assert info['size'] <= 2
    
    @patch('concurrent.futures.ThreadPoolExecutor')
    def test_parallel_execution_failure(self, mock_executor, sample_cdr_data):
        """测试并行执行失败"""
        # 模拟执行器失败
        mock_executor.side_effect = Exception("Executor failed")
        
        factory = ValidationFactory({
            'parallel_validation': True,
            'max_workers': 2
        })
        
        datasets = [(sample_cdr_data, 'cdr')]
        
        # 应该回退到串行执行
        results = factory.validate_parallel(datasets)
        assert len(results) == 1
        assert results[0].is_valid


class TestFactoryIntegration:
    """工厂集成测试类"""
    
    def test_end_to_end_validation_workflow(self, temp_csv_file, sample_cdr_data):
        """测试端到端验证工作流"""
        factory = ValidationFactory()
        
        # 1. 文件验证
        file_result = factory.validate_file(temp_csv_file, 'cdr')
        assert file_result.is_valid
        
        # 2. 数据验证
        data_result = factory.validate_cdr(sample_cdr_data)
        assert data_result.is_valid
        
        # 3. 获取统计信息
        stats = factory.get_statistics()
        assert stats['total_validations'] >= 2
    
    def test_multi_type_validation_session(self, sample_cdr_data, sample_kpi_data, sample_cfg_data):
        """测试多类型验证会话"""
        factory = ValidationFactory({'cache_enabled': True})
        
        # 验证不同类型的数据
        cdr_result = factory.validate_cdr(sample_cdr_data)
        kpi_result = factory.validate_kpi(sample_kpi_data)
        cfg_result = factory.validate_cfg(sample_cfg_data)
        
        assert cdr_result.is_valid
        assert kpi_result.is_valid
        assert cfg_result.is_valid
        
        # 检查缓存
        cache_info = factory.get_cache_info()
        assert cache_info['size'] >= 3
    
    def test_factory_state_consistency(self, sample_cdr_data):
        """测试工厂状态一致性"""
        factory = ValidationFactory()
        
        # 多次操作
        for _ in range(5):
            result = factory.validate_cdr(sample_cdr_data)
            assert result.is_valid
        
        # 状态应该保持一致
        stats = factory.get_statistics()
        assert stats['total_validations'] == 5
        assert stats['successful_validations'] == 5
        assert stats['failed_validations'] == 0
    
    def test_factory_with_custom_rules(self, sample_cdr_data):
        """测试工厂使用自定义规则"""
        from ..rules import CDRValidationRules
        
        factory = ValidationFactory()
        
        # 创建自定义规则集
        custom_rules = CDRValidationRules.get_structure_rules()
        custom_framework = factory.create_custom_framework(custom_rules)
        
        result = custom_framework.validate(sample_cdr_data)
        assert result.is_valid


if __name__ == "__main__":
    pytest.main([__file__])