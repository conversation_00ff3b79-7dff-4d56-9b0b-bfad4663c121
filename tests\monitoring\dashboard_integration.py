#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect平台CI/CD监控仪表板集成

本模块提供测试结果集成和监控仪表板功能，包括：
- 测试结果收集和聚合
- 实时监控仪表板
- 质量门禁检查
- 告警和通知系统
- 趋势分析和报告
- CI/CD流水线集成

作者: Connect质量工程团队
创建时间: 2024-01-20
"""

import json
import logging
import os
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

import requests
import yaml
from flask import Flask, render_template, jsonify, request
from flask_socketio import SocketIO, emit
import plotly.graph_objs as go
import plotly.utils
import pandas as pd

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TestResult:
    """测试结果数据类"""
    test_id: str
    test_type: str  # unit, integration, e2e, performance, security
    test_name: str
    status: str  # passed, failed, skipped, error
    duration: float
    timestamp: datetime
    environment: str
    branch: str
    commit_hash: str
    error_message: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None
    artifacts: Optional[List[str]] = None


@dataclass
class QualityMetrics:
    """质量指标数据类"""
    timestamp: datetime
    test_coverage: float
    code_coverage: float
    performance_score: float
    security_score: float
    reliability_score: float
    maintainability_score: float
    technical_debt_ratio: float
    bug_density: float
    defect_escape_rate: float


class TestResultCollector:
    """测试结果收集器"""
    
    def __init__(self, db_path: str = "test_results.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建测试结果表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_id TEXT NOT NULL,
                test_type TEXT NOT NULL,
                test_name TEXT NOT NULL,
                status TEXT NOT NULL,
                duration REAL NOT NULL,
                timestamp TEXT NOT NULL,
                environment TEXT NOT NULL,
                branch TEXT NOT NULL,
                commit_hash TEXT NOT NULL,
                error_message TEXT,
                metrics TEXT,
                artifacts TEXT
            )
        """)
        
        # 创建质量指标表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS quality_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                test_coverage REAL,
                code_coverage REAL,
                performance_score REAL,
                security_score REAL,
                reliability_score REAL,
                maintainability_score REAL,
                technical_debt_ratio REAL,
                bug_density REAL,
                defect_escape_rate REAL
            )
        """)
        
        # 创建构建信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS builds (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                build_id TEXT UNIQUE NOT NULL,
                branch TEXT NOT NULL,
                commit_hash TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                status TEXT NOT NULL,
                duration REAL,
                triggered_by TEXT,
                environment TEXT
            )
        """)
        
        conn.commit()
        conn.close()
        logger.info(f"数据库初始化完成: {self.db_path}")
    
    def collect_junit_results(self, junit_file: str) -> List[TestResult]:
        """收集JUnit测试结果"""
        import xml.etree.ElementTree as ET
        
        results = []
        
        try:
            tree = ET.parse(junit_file)
            root = tree.getroot()
            
            for testcase in root.findall('.//testcase'):
                test_name = testcase.get('name')
                classname = testcase.get('classname', '')
                duration = float(testcase.get('time', 0))
                
                # 确定测试状态
                status = 'passed'
                error_message = None
                
                if testcase.find('failure') is not None:
                    status = 'failed'
                    failure = testcase.find('failure')
                    error_message = failure.text if failure is not None else None
                elif testcase.find('error') is not None:
                    status = 'error'
                    error = testcase.find('error')
                    error_message = error.text if error is not None else None
                elif testcase.find('skipped') is not None:
                    status = 'skipped'
                
                result = TestResult(
                    test_id=f"{classname}.{test_name}",
                    test_type=self._determine_test_type(classname),
                    test_name=test_name,
                    status=status,
                    duration=duration,
                    timestamp=datetime.now(),
                    environment=os.getenv('TEST_ENVIRONMENT', 'unknown'),
                    branch=os.getenv('GIT_BRANCH', 'unknown'),
                    commit_hash=os.getenv('GIT_COMMIT', 'unknown'),
                    error_message=error_message
                )
                
                results.append(result)
            
            logger.info(f"从 {junit_file} 收集了 {len(results)} 个测试结果")
            
        except Exception as e:
            logger.error(f"解析JUnit文件失败: {e}")
        
        return results
    
    def collect_pytest_results(self, pytest_json: str) -> List[TestResult]:
        """收集pytest JSON结果"""
        results = []
        
        try:
            with open(pytest_json, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for test in data.get('tests', []):
                result = TestResult(
                    test_id=test.get('nodeid', ''),
                    test_type=self._determine_test_type(test.get('nodeid', '')),
                    test_name=test.get('name', ''),
                    status=test.get('outcome', 'unknown'),
                    duration=test.get('duration', 0),
                    timestamp=datetime.now(),
                    environment=os.getenv('TEST_ENVIRONMENT', 'unknown'),
                    branch=os.getenv('GIT_BRANCH', 'unknown'),
                    commit_hash=os.getenv('GIT_COMMIT', 'unknown'),
                    error_message=test.get('call', {}).get('longrepr')
                )
                
                results.append(result)
            
            logger.info(f"从 {pytest_json} 收集了 {len(results)} 个测试结果")
            
        except Exception as e:
            logger.error(f"解析pytest JSON文件失败: {e}")
        
        return results
    
    def collect_performance_results(self, performance_file: str) -> List[TestResult]:
        """收集性能测试结果"""
        results = []
        
        try:
            with open(performance_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for test_name, metrics in data.items():
                # 判断性能测试是否通过
                status = 'passed'
                if metrics.get('response_time', 0) > metrics.get('threshold', float('inf')):
                    status = 'failed'
                
                result = TestResult(
                    test_id=f"performance.{test_name}",
                    test_type='performance',
                    test_name=test_name,
                    status=status,
                    duration=metrics.get('duration', 0),
                    timestamp=datetime.now(),
                    environment=os.getenv('TEST_ENVIRONMENT', 'unknown'),
                    branch=os.getenv('GIT_BRANCH', 'unknown'),
                    commit_hash=os.getenv('GIT_COMMIT', 'unknown'),
                    metrics=metrics
                )
                
                results.append(result)
            
            logger.info(f"从 {performance_file} 收集了 {len(results)} 个性能测试结果")
            
        except Exception as e:
            logger.error(f"解析性能测试文件失败: {e}")
        
        return results
    
    def collect_security_results(self, security_file: str) -> List[TestResult]:
        """收集安全测试结果"""
        results = []
        
        try:
            with open(security_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 总体安全测试结果
            vulnerabilities = data.get('vulnerabilities', [])
            critical_count = len([v for v in vulnerabilities if v.get('severity') == 'Critical'])
            high_count = len([v for v in vulnerabilities if v.get('severity') == 'High'])
            
            status = 'passed'
            if critical_count > 0 or high_count > 0:
                status = 'failed'
            
            result = TestResult(
                test_id='security.overall',
                test_type='security',
                test_name='Security Scan',
                status=status,
                duration=0,
                timestamp=datetime.now(),
                environment=os.getenv('TEST_ENVIRONMENT', 'unknown'),
                branch=os.getenv('GIT_BRANCH', 'unknown'),
                commit_hash=os.getenv('GIT_COMMIT', 'unknown'),
                metrics={
                    'total_vulnerabilities': len(vulnerabilities),
                    'critical': critical_count,
                    'high': high_count,
                    'medium': len([v for v in vulnerabilities if v.get('severity') == 'Medium']),
                    'low': len([v for v in vulnerabilities if v.get('severity') == 'Low'])
                }
            )
            
            results.append(result)
            
            logger.info(f"从 {security_file} 收集了安全测试结果")
            
        except Exception as e:
            logger.error(f"解析安全测试文件失败: {e}")
        
        return results
    
    def store_results(self, results: List[TestResult]):
        """存储测试结果"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for result in results:
            cursor.execute("""
                INSERT INTO test_results (
                    test_id, test_type, test_name, status, duration,
                    timestamp, environment, branch, commit_hash,
                    error_message, metrics, artifacts
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                result.test_id,
                result.test_type,
                result.test_name,
                result.status,
                result.duration,
                result.timestamp.isoformat(),
                result.environment,
                result.branch,
                result.commit_hash,
                result.error_message,
                json.dumps(result.metrics) if result.metrics else None,
                json.dumps(result.artifacts) if result.artifacts else None
            ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"存储了 {len(results)} 个测试结果")
    
    def _determine_test_type(self, test_path: str) -> str:
        """根据测试路径确定测试类型"""
        if 'unit' in test_path.lower():
            return 'unit'
        elif 'integration' in test_path.lower():
            return 'integration'
        elif 'e2e' in test_path.lower() or 'end_to_end' in test_path.lower():
            return 'e2e'
        elif 'performance' in test_path.lower():
            return 'performance'
        elif 'security' in test_path.lower():
            return 'security'
        else:
            return 'unknown'


class QualityGateChecker:
    """质量门禁检查器"""
    
    def __init__(self, config_file: str = "quality_gates.yaml"):
        self.config = self._load_config(config_file)
    
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载质量门禁配置"""
        default_config = {
            'test_coverage': {'min': 80.0, 'target': 90.0},
            'code_coverage': {'min': 85.0, 'target': 95.0},
            'performance': {
                'response_time_p95': {'max': 3000},  # ms
                'throughput': {'min': 100},  # req/s
                'error_rate': {'max': 0.1}  # %
            },
            'security': {
                'critical_vulnerabilities': {'max': 0},
                'high_vulnerabilities': {'max': 0},
                'security_score': {'min': 80.0}
            },
            'reliability': {
                'test_pass_rate': {'min': 95.0},
                'defect_escape_rate': {'max': 5.0},
                'mtbf': {'min': 168}  # hours
            },
            'maintainability': {
                'technical_debt_ratio': {'max': 5.0},
                'code_smells': {'max': 10},
                'duplicated_lines': {'max': 3.0}  # %
            }
        }
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    # 合并默认配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                return config
        except Exception as e:
            logger.warning(f"加载质量门禁配置失败，使用默认配置: {e}")
        
        return default_config
    
    def check_quality_gates(self, metrics: QualityMetrics) -> Dict[str, Any]:
        """检查质量门禁"""
        results = {
            'passed': True,
            'checks': {},
            'summary': {
                'total': 0,
                'passed': 0,
                'failed': 0
            }
        }
        
        # 测试覆盖率检查
        test_cov_check = self._check_threshold(
            'test_coverage',
            metrics.test_coverage,
            self.config['test_coverage']['min']
        )
        results['checks']['test_coverage'] = test_cov_check
        
        # 代码覆盖率检查
        code_cov_check = self._check_threshold(
            'code_coverage',
            metrics.code_coverage,
            self.config['code_coverage']['min']
        )
        results['checks']['code_coverage'] = code_cov_check
        
        # 性能检查
        perf_check = self._check_threshold(
            'performance_score',
            metrics.performance_score,
            80.0  # 最低性能分数
        )
        results['checks']['performance'] = perf_check
        
        # 安全检查
        security_check = self._check_threshold(
            'security_score',
            metrics.security_score,
            self.config['security']['security_score']['min']
        )
        results['checks']['security'] = security_check
        
        # 可靠性检查
        reliability_check = self._check_threshold(
            'reliability_score',
            metrics.reliability_score,
            90.0  # 最低可靠性分数
        )
        results['checks']['reliability'] = reliability_check
        
        # 技术债务检查
        debt_check = self._check_threshold(
            'technical_debt_ratio',
            metrics.technical_debt_ratio,
            self.config['maintainability']['technical_debt_ratio']['max'],
            reverse=True
        )
        results['checks']['technical_debt'] = debt_check
        
        # 缺陷逃逸率检查
        escape_rate_check = self._check_threshold(
            'defect_escape_rate',
            metrics.defect_escape_rate,
            self.config['reliability']['defect_escape_rate']['max'],
            reverse=True
        )
        results['checks']['defect_escape_rate'] = escape_rate_check
        
        # 统计结果
        for check in results['checks'].values():
            results['summary']['total'] += 1
            if check['passed']:
                results['summary']['passed'] += 1
            else:
                results['summary']['failed'] += 1
                results['passed'] = False
        
        logger.info(f"质量门禁检查完成: {results['summary']['passed']}/{results['summary']['total']} 通过")
        
        return results
    
    def _check_threshold(self, name: str, value: float, threshold: float, reverse: bool = False) -> Dict[str, Any]:
        """检查阈值"""
        if reverse:
            passed = value <= threshold
            operator = '<=' 
        else:
            passed = value >= threshold
            operator = '>='
        
        return {
            'name': name,
            'value': value,
            'threshold': threshold,
            'operator': operator,
            'passed': passed,
            'message': f"{name}: {value} {operator} {threshold} - {'PASS' if passed else 'FAIL'}"
        }


class NotificationService:
    """通知服务"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    def send_slack_notification(self, message: str, channel: str = None):
        """发送Slack通知"""
        slack_config = self.config.get('slack', {})
        webhook_url = slack_config.get('webhook_url')
        
        if not webhook_url:
            logger.warning("Slack webhook URL未配置")
            return
        
        payload = {
            'text': message,
            'channel': channel or slack_config.get('channel', '#quality'),
            'username': 'Connect Quality Bot',
            'icon_emoji': ':robot_face:'
        }
        
        try:
            response = requests.post(webhook_url, json=payload)
            response.raise_for_status()
            logger.info("Slack通知发送成功")
        except Exception as e:
            logger.error(f"Slack通知发送失败: {e}")
    
    def send_email_notification(self, subject: str, body: str, recipients: List[str]):
        """发送邮件通知"""
        email_config = self.config.get('email', {})
        
        if not email_config.get('enabled', False):
            logger.info("邮件通知未启用")
            return
        
        # 这里可以集成SMTP邮件发送
        logger.info(f"邮件通知: {subject} -> {recipients}")
    
    def send_teams_notification(self, message: str):
        """发送Teams通知"""
        teams_config = self.config.get('teams', {})
        webhook_url = teams_config.get('webhook_url')
        
        if not webhook_url:
            logger.warning("Teams webhook URL未配置")
            return
        
        payload = {
            '@type': 'MessageCard',
            '@context': 'http://schema.org/extensions',
            'summary': 'Connect Quality Report',
            'themeColor': '0076D7',
            'sections': [{
                'activityTitle': 'Connect Quality Report',
                'activitySubtitle': 'Quality Gate Status',
                'text': message
            }]
        }
        
        try:
            response = requests.post(webhook_url, json=payload)
            response.raise_for_status()
            logger.info("Teams通知发送成功")
        except Exception as e:
            logger.error(f"Teams通知发送失败: {e}")


class DashboardApp:
    """监控仪表板应用"""
    
    def __init__(self, db_path: str = "test_results.db"):
        self.db_path = db_path
        self.app = Flask(__name__)
        self.app.config['SECRET_KEY'] = 'connect_quality_dashboard'
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        self._setup_routes()
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.route('/')
        def dashboard():
            """主仪表板页面"""
            return render_template('dashboard.html')
        
        @self.app.route('/api/test-results')
        def get_test_results():
            """获取测试结果API"""
            days = request.args.get('days', 7, type=int)
            test_type = request.args.get('type')
            
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT * FROM test_results 
                WHERE timestamp >= datetime('now', '-{} days')
            """.format(days)
            
            params = []
            if test_type:
                query += " AND test_type = ?"
                params.append(test_type)
            
            query += " ORDER BY timestamp DESC"
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            return jsonify(df.to_dict('records'))
        
        @self.app.route('/api/quality-metrics')
        def get_quality_metrics():
            """获取质量指标API"""
            days = request.args.get('days', 30, type=int)
            
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT * FROM quality_metrics 
                WHERE timestamp >= datetime('now', '-{} days')
                ORDER BY timestamp DESC
            """.format(days)
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            return jsonify(df.to_dict('records'))
        
        @self.app.route('/api/test-summary')
        def get_test_summary():
            """获取测试摘要API"""
            conn = sqlite3.connect(self.db_path)
            
            # 最近24小时的测试统计
            query = """
                SELECT 
                    test_type,
                    status,
                    COUNT(*) as count,
                    AVG(duration) as avg_duration
                FROM test_results 
                WHERE timestamp >= datetime('now', '-1 day')
                GROUP BY test_type, status
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            summary = {}
            for _, row in df.iterrows():
                test_type = row['test_type']
                if test_type not in summary:
                    summary[test_type] = {'total': 0, 'passed': 0, 'failed': 0, 'avg_duration': 0}
                
                summary[test_type]['total'] += row['count']
                summary[test_type][row['status']] = row['count']
                summary[test_type]['avg_duration'] = row['avg_duration']
            
            return jsonify(summary)
        
        @self.app.route('/api/trends')
        def get_trends():
            """获取趋势数据API"""
            days = request.args.get('days', 30, type=int)
            
            conn = sqlite3.connect(self.db_path)
            
            # 每日测试通过率趋势
            query = """
                SELECT 
                    DATE(timestamp) as date,
                    test_type,
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'passed' THEN 1 ELSE 0 END) as passed
                FROM test_results 
                WHERE timestamp >= datetime('now', '-{} days')
                GROUP BY DATE(timestamp), test_type
                ORDER BY date
            """.format(days)
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            # 计算通过率
            df['pass_rate'] = (df['passed'] / df['total'] * 100).round(2)
            
            return jsonify(df.to_dict('records'))
        
        @self.app.route('/api/performance-trends')
        def get_performance_trends():
            """获取性能趋势API"""
            days = request.args.get('days', 30, type=int)
            
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT 
                    DATE(timestamp) as date,
                    AVG(duration) as avg_duration,
                    MAX(duration) as max_duration,
                    MIN(duration) as min_duration
                FROM test_results 
                WHERE test_type = 'performance' 
                AND timestamp >= datetime('now', '-{} days')
                GROUP BY DATE(timestamp)
                ORDER BY date
            """.format(days)
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            return jsonify(df.to_dict('records'))
        
        @self.socketio.on('connect')
        def handle_connect():
            """WebSocket连接处理"""
            logger.info('客户端已连接')
            emit('status', {'message': '已连接到质量监控仪表板'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """WebSocket断开处理"""
            logger.info('客户端已断开连接')
    
    def broadcast_test_result(self, result: TestResult):
        """广播测试结果"""
        self.socketio.emit('test_result', asdict(result))
    
    def broadcast_quality_alert(self, alert: Dict[str, Any]):
        """广播质量告警"""
        self.socketio.emit('quality_alert', alert)
    
    def run(self, host: str = '0.0.0.0', port: int = 5000, debug: bool = False):
        """运行仪表板应用"""
        logger.info(f"启动质量监控仪表板: http://{host}:{port}")
        self.socketio.run(self.app, host=host, port=port, debug=debug)


class CIDashboardIntegration:
    """CI/CD仪表板集成主类"""
    
    def __init__(self, config_file: str = "dashboard_config.yaml"):
        self.config = self._load_config(config_file)
        self.collector = TestResultCollector(self.config.get('database', {}).get('path', 'test_results.db'))
        self.quality_gate = QualityGateChecker(self.config.get('quality_gates', {}).get('config_file', 'quality_gates.yaml'))
        self.notification = NotificationService(self.config.get('notifications', {}))
        self.dashboard = DashboardApp(self.config.get('database', {}).get('path', 'test_results.db'))
    
    def _load_config(self, config_file: str) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            'database': {'path': 'test_results.db'},
            'dashboard': {'host': '0.0.0.0', 'port': 5000},
            'notifications': {
                'slack': {'enabled': False},
                'email': {'enabled': False},
                'teams': {'enabled': False}
            },
            'quality_gates': {'config_file': 'quality_gates.yaml'},
            'ci_integration': {
                'jenkins': {'enabled': False},
                'github_actions': {'enabled': False},
                'gitlab_ci': {'enabled': False}
            }
        }
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    # 合并默认配置
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                return config
        except Exception as e:
            logger.warning(f"加载配置文件失败，使用默认配置: {e}")
        
        return default_config
    
    def process_test_results(self, results_dir: str):
        """处理测试结果目录"""
        logger.info(f"处理测试结果目录: {results_dir}")
        
        all_results = []
        
        # 收集各种类型的测试结果
        for root, dirs, files in os.walk(results_dir):
            for file in files:
                file_path = os.path.join(root, file)
                
                if file.endswith('.xml') and 'junit' in file.lower():
                    results = self.collector.collect_junit_results(file_path)
                    all_results.extend(results)
                elif file.endswith('.json') and 'pytest' in file.lower():
                    results = self.collector.collect_pytest_results(file_path)
                    all_results.extend(results)
                elif file.endswith('.json') and 'performance' in file.lower():
                    results = self.collector.collect_performance_results(file_path)
                    all_results.extend(results)
                elif file.endswith('.json') and 'security' in file.lower():
                    results = self.collector.collect_security_results(file_path)
                    all_results.extend(results)
        
        # 存储结果
        if all_results:
            self.collector.store_results(all_results)
            
            # 广播实时结果
            for result in all_results:
                self.dashboard.broadcast_test_result(result)
            
            # 生成质量报告
            self.generate_quality_report(all_results)
        
        logger.info(f"处理完成，共收集 {len(all_results)} 个测试结果")
    
    def generate_quality_report(self, results: List[TestResult]):
        """生成质量报告"""
        logger.info("生成质量报告")
        
        # 计算质量指标
        total_tests = len(results)
        passed_tests = len([r for r in results if r.status == 'passed'])
        test_coverage = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        # 性能指标
        performance_results = [r for r in results if r.test_type == 'performance']
        performance_score = 100.0
        if performance_results:
            failed_perf = len([r for r in performance_results if r.status == 'failed'])
            performance_score = max(0, 100 - (failed_perf / len(performance_results) * 100))
        
        # 安全指标
        security_results = [r for r in results if r.test_type == 'security']
        security_score = 100.0
        if security_results:
            failed_sec = len([r for r in security_results if r.status == 'failed'])
            security_score = max(0, 100 - (failed_sec / len(security_results) * 100))
        
        # 创建质量指标对象
        metrics = QualityMetrics(
            timestamp=datetime.now(),
            test_coverage=test_coverage,
            code_coverage=85.0,  # 这里应该从代码覆盖率工具获取
            performance_score=performance_score,
            security_score=security_score,
            reliability_score=test_coverage,  # 简化计算
            maintainability_score=80.0,  # 这里应该从代码质量工具获取
            technical_debt_ratio=3.5,  # 这里应该从SonarQube等工具获取
            bug_density=0.5,  # 每千行代码的bug数
            defect_escape_rate=2.0  # 逃逸到生产的缺陷率
        )
        
        # 质量门禁检查
        gate_results = self.quality_gate.check_quality_gates(metrics)
        
        # 发送通知
        if not gate_results['passed']:
            self._send_quality_alert(gate_results, metrics)
        
        # 广播质量告警
        if not gate_results['passed']:
            alert = {
                'type': 'quality_gate_failed',
                'message': f"质量门禁检查失败: {gate_results['summary']['failed']} 项检查未通过",
                'details': gate_results,
                'timestamp': datetime.now().isoformat()
            }
            self.dashboard.broadcast_quality_alert(alert)
        
        return gate_results
    
    def _send_quality_alert(self, gate_results: Dict[str, Any], metrics: QualityMetrics):
        """发送质量告警"""
        failed_checks = [check for check in gate_results['checks'].values() if not check['passed']]
        
        message = f"🚨 Connect质量门禁检查失败\n\n"
        message += f"失败检查项: {len(failed_checks)}/{gate_results['summary']['total']}\n\n"
        
        for check in failed_checks:
            message += f"❌ {check['message']}\n"
        
        message += f"\n📊 当前质量指标:\n"
        message += f"• 测试覆盖率: {metrics.test_coverage:.1f}%\n"
        message += f"• 代码覆盖率: {metrics.code_coverage:.1f}%\n"
        message += f"• 性能得分: {metrics.performance_score:.1f}\n"
        message += f"• 安全得分: {metrics.security_score:.1f}\n"
        message += f"• 技术债务比率: {metrics.technical_debt_ratio:.1f}%\n"
        
        # 发送各种通知
        if self.config.get('notifications', {}).get('slack', {}).get('enabled'):
            self.notification.send_slack_notification(message)
        
        if self.config.get('notifications', {}).get('teams', {}).get('enabled'):
            self.notification.send_teams_notification(message)
        
        if self.config.get('notifications', {}).get('email', {}).get('enabled'):
            recipients = self.config.get('notifications', {}).get('email', {}).get('recipients', [])
            self.notification.send_email_notification(
                "Connect质量门禁告警",
                message,
                recipients
            )
    
    def start_dashboard(self):
        """启动监控仪表板"""
        dashboard_config = self.config.get('dashboard', {})
        host = dashboard_config.get('host', '0.0.0.0')
        port = dashboard_config.get('port', 5000)
        
        self.dashboard.run(host=host, port=port)
    
    def create_ci_webhook(self) -> str:
        """创建CI/CD webhook端点"""
        @self.dashboard.app.route('/webhook/ci', methods=['POST'])
        def ci_webhook():
            """CI/CD webhook处理"""
            try:
                data = request.get_json()
                
                # 处理不同CI系统的webhook
                if 'jenkins' in request.headers.get('User-Agent', '').lower():
                    self._handle_jenkins_webhook(data)
                elif 'github' in request.headers.get('User-Agent', '').lower():
                    self._handle_github_webhook(data)
                elif 'gitlab' in request.headers.get('User-Agent', '').lower():
                    self._handle_gitlab_webhook(data)
                
                return jsonify({'status': 'success'})
            
            except Exception as e:
                logger.error(f"Webhook处理失败: {e}")
                return jsonify({'status': 'error', 'message': str(e)}), 500
        
        return '/webhook/ci'
    
    def _handle_jenkins_webhook(self, data: Dict[str, Any]):
        """处理Jenkins webhook"""
        logger.info("处理Jenkins webhook")
        # 这里可以添加Jenkins特定的处理逻辑
    
    def _handle_github_webhook(self, data: Dict[str, Any]):
        """处理GitHub webhook"""
        logger.info("处理GitHub webhook")
        # 这里可以添加GitHub Actions特定的处理逻辑
    
    def _handle_gitlab_webhook(self, data: Dict[str, Any]):
        """处理GitLab webhook"""
        logger.info("处理GitLab webhook")
        # 这里可以添加GitLab CI特定的处理逻辑


if __name__ == '__main__':
    # 创建仪表板集成实例
    integration = CIDashboardIntegration()
    
    # 创建CI webhook
    webhook_endpoint = integration.create_ci_webhook()
    logger.info(f"CI/CD webhook端点: {webhook_endpoint}")
    
    # 启动仪表板
    integration.start_dashboard()