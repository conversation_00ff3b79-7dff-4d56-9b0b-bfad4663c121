__author__ = "Vincent<PERSON>Li"
__email__ = "<EMAIL>"

"""Custom exceptions for the database framework.

This module defines all custom exceptions used throughout the database framework,
providing clear error handling and debugging capabilities.
"""

from typing import Any, Optional


class DatabaseError(Exception):
    """Base exception for all database-related errors.

    This is the base class for all database framework exceptions.
    It provides common functionality for error handling and logging.
    """

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[dict[str, Any]] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize database error.

        Args:
            message: Human-readable error message.
            error_code: Optional error code for programmatic handling.
            details: Optional dictionary with additional error details.
            original_exception: Optional original exception that caused this error.
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.original_exception = original_exception

    def __str__(self) -> str:
        """Return string representation of the error."""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message

    def __repr__(self) -> str:
        """Return detailed string representation of the error."""
        parts = [f"'{self.message}'"]
        if self.error_code:
            parts.append(f"error_code='{self.error_code}'")
        if self.details:
            parts.append(f"details={self.details}")
        return f"{self.__class__.__name__}({', '.join(parts)})"

    def to_dict(self) -> dict[str, Any]:
        """Convert exception to dictionary for serialization."""
        return {
            "type": self.__class__.__name__,
            "message": self.message,
            "error_code": self.error_code,
            "details": self.details,
        }


class ConnectionError(DatabaseError):
    """Exception raised when database connection fails.

    This exception is raised when the database framework cannot establish
    or maintain a connection to the database server.
    """

    def __init__(
        self,
        message: str,
        host: Optional[str] = None,
        port: Optional[int] = None,
        database: Optional[str] = None,
        error_code: Optional[str] = None,
        details: Optional[dict[str, Any]] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize connection error.

        Args:
            message: Human-readable error message.
            host: Database host that failed to connect.
            port: Database port that failed to connect.
            database: Database name that failed to connect.
            error_code: Error code for programmatic handling.
            details: Optional additional details dictionary.
            original_exception: Optional original exception that caused this error.
        """
        if details is None:
            details = {}
        if host:
            details["host"] = host
        if port:
            details["port"] = port
        if database:
            details["database"] = database

        super().__init__(message, error_code, details, original_exception)


class ImportError(DatabaseError):
    """Exception raised during data import operations."""

    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        error_code: Optional[str] = None,
        details: Optional[dict[str, Any]] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize import error.

        Args:
            message: Human-readable error message.
            file_path: Path to the file that caused the import error.
            error_code: Error code for programmatic handling.
            details: Optional additional details dictionary.
            original_exception: Optional original exception that caused this error.
        """
        if details is None:
            details = {}
        if file_path:
            details["file_path"] = file_path
        super().__init__(message, error_code, details, original_exception)


class MonitoringError(DatabaseError):
    """Exception raised during system monitoring operations."""

    def __init__(
        self,
        message: str,
        metric_name: Optional[str] = None,
        error_code: Optional[str] = None,
        details: Optional[dict[str, Any]] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize monitoring error.

        Args:
            message: Human-readable error message.
            metric_name: Name of the metric that caused the error.
            error_code: Error code for programmatic handling.
            details: Optional additional details dictionary.
            original_exception: Optional original exception that caused this error.
        """
        if details is None:
            details = {}
        if metric_name:
            details["metric_name"] = metric_name
        super().__init__(message, error_code, details, original_exception)


class OperationError(DatabaseError):
    """Exception raised for errors during database operations (CRUD, etc.)."""

    def __init__(
        self,
        message: str,
        operation_name: Optional[str] = None,
        error_code: Optional[str] = None,
        details: Optional[dict[str, Any]] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize operation error.

        Args:
            message: Human-readable error message.
            operation_name: Name of the operation that failed.
            error_code: Error code for programmatic handling.
            details: Optional additional details dictionary.
            original_exception: Optional original exception that caused this error.
        """
        if details is None:
            details = {}
        if operation_name:
            details["operation_name"] = operation_name
        super().__init__(message, error_code, details, original_exception)


class ValidationError(DatabaseError):
    """Exception raised when data validation fails.

    This can occur during data import, updates, or any operation
    where data integrity is crucial.
    """

    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        invalid_value: Optional[Any] = None,
        error_code: Optional[str] = None,
        details: Optional[dict[str, Any]] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize validation error.

        Args:
            message: Human-readable error message.
            field_name: Name of the field that failed validation.
            invalid_value: The value that caused the validation failure.
            error_code: Error code for programmatic handling.
            details: Optional additional details dictionary.
            original_exception: Optional original exception that caused this error.
        """
        if details is None:
            details = {}
        if field_name:
            details["field_name"] = field_name
        if invalid_value is not None:
            details["invalid_value"] = str(invalid_value) # Ensure serializable

        super().__init__(message, error_code, details, original_exception)


class SecurityError(DatabaseError):
    """Exception raised for security-related issues.

    This exception is raised when security violations are detected,
    such as unauthorized access attempts or invalid credentials.
    """

    def __init__(
        self,
        message: str,
        user: Optional[str] = None,
        operation: Optional[str] = None,
        error_code: str = "DB_SECURITY_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize security error.

        Args:
            message: Human-readable error message.
            user: User involved in the security violation.
            operation: Operation that triggered the security error.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if user:
            error_details["user"] = user
        if operation:
            error_details["operation"] = operation

        super().__init__(message, error_code, error_details, original_exception)


class PipelineError(DatabaseError):
    """Exception raised during pipeline operations.

    This exception is raised when pipeline operations encounter errors
    during execution, configuration, or validation.
    """

    def __init__(
        self,
        message: str,
        pipeline_name: Optional[str] = None,
        stage: Optional[str] = None,
        error_code: str = "PIPELINE_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize pipeline error.

        Args:
            message: Human-readable error message.
            pipeline_name: Name of the pipeline that failed.
            stage: Pipeline stage where error occurred.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if pipeline_name:
            error_details["pipeline_name"] = pipeline_name
        if stage:
            error_details["stage"] = stage

        super().__init__(message, error_code, error_details, original_exception)


class GeospatialError(DatabaseError):
    """Exception raised when geospatial operations fail.

    This exception is raised when there are issues with geospatial
    data processing, coordinate transformations, or spatial operations.
    """

    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        coordinates: Optional[tuple] = None,
        error_code: str = "GEOSPATIAL_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize geospatial error.

        Args:
            message: Human-readable error message.
            operation: Type of geospatial operation that failed.
            coordinates: Coordinates involved in the operation.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if operation:
            error_details["operation"] = operation
        if coordinates:
            error_details["coordinates"] = coordinates

        super().__init__(message, error_code, error_details, original_exception)


class NoAvailableReplicasError(DatabaseError):
    """Exception raised when no read replicas are available.

    This exception is raised by the ReadWriteSplitter when no healthy
    read replicas are available for read operations.
    """

    def __init__(
        self,
        message: str = "No read replicas available",
        error_code: str = "NO_REPLICAS_AVAILABLE",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize no available replicas error.

        Args:
            message: Human-readable error message.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        super().__init__(message, error_code, details, original_exception)


class PrimaryDatabaseUnavailableError(DatabaseError):
    """Exception raised when the primary database is unavailable.

    This exception is raised by the ReadWriteSplitter when the primary
    database is not available for write operations.
    """

    def __init__(
        self,
        message: str = "Primary database unavailable",
        error_code: str = "PRIMARY_DB_UNAVAILABLE",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize primary database unavailable error.

        Args:
            message: Human-readable error message.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        super().__init__(message, error_code, details, original_exception)


# Duplicate ValidationError class removed - using the one defined earlier


class PermissionError(DatabaseError):
    """Exception raised when permission checks fail.

    This exception is raised when a user or process doesn't have
    sufficient permissions to perform a requested operation.
    """

    def __init__(
        self,
        message: str,
        user: Optional[str] = None,
        resource: Optional[str] = None,
        operation: Optional[str] = None,
        error_code: str = "DB_PERMISSION_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize permission error.

        Args:
            message: Human-readable error message.
            user: User who lacks permission.
            resource: Resource that requires permission.
            operation: Operation that requires permission.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if user:
            error_details["user"] = user
        if resource:
            error_details["resource"] = resource
        if operation:
            error_details["operation"] = operation

        super().__init__(message, error_code, error_details, original_exception)


class QueryError(DatabaseError):
    """Exception raised when SQL query execution fails.

    This exception is raised when there are issues with SQL query
    execution, such as syntax errors or constraint violations.
    """

    def __init__(
        self,
        message: str,
        query: Optional[str] = None,
        parameters: Optional[dict] = None,
        error_code: str = "DB_QUERY_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize query error.

        Args:
            message: Human-readable error message.
            query: SQL query that failed.
            parameters: Query parameters that were used.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if query:
            error_details["query"] = query
        if parameters:
            error_details["parameters"] = parameters

        super().__init__(message, error_code, error_details, original_exception)


class TransactionError(DatabaseError):
    """Exception raised when database transaction fails.

    This exception is raised when there are issues with database
    transactions, such as deadlocks or rollback failures.
    """

    def __init__(
        self,
        message: str,
        transaction_id: Optional[str] = None,
        error_code: str = "DB_TRANSACTION_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize transaction error.

        Args:
            message: Human-readable error message.
            transaction_id: ID of the failed transaction.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if transaction_id:
            error_details["transaction_id"] = transaction_id

        super().__init__(message, error_code, error_details, original_exception)


# Telecommunications-specific exceptions

class TelecomDataError(DatabaseError):
    """Exception raised for telecommunications data processing errors.

    This exception is raised when there are issues specific to telecommunications
    data processing, such as invalid CDR records, EP measurement errors, or
    KPI calculation failures.
    """

    def __init__(
        self,
        message: str,
        data_type: Optional[str] = None,  # cdr, ep, kpi, nlg
        record_id: Optional[str] = None,
        cell_id: Optional[str] = None,
        error_code: str = "TELECOM_DATA_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize telecommunications data error.

        Args:
            message: Human-readable error message.
            data_type: Type of telecommunications data (cdr, ep, kpi, nlg).
            record_id: ID of the record that caused the error.
            cell_id: Cell ID associated with the error.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if data_type:
            error_details["data_type"] = data_type
        if record_id:
            error_details["record_id"] = record_id
        if cell_id:
            error_details["cell_id"] = cell_id

        super().__init__(message, error_code, error_details, original_exception)


class CDRProcessingError(TelecomDataError):
    """Exception raised during CDR (Call Detail Record) processing."""

    def __init__(
        self,
        message: str,
        call_id: Optional[str] = None,
        caller_number: Optional[str] = None,
        processing_stage: Optional[str] = None,
        error_code: str = "CDR_PROCESSING_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize CDR processing error.

        Args:
            message: Human-readable error message.
            call_id: Call ID that caused the error.
            caller_number: Caller number associated with the error.
            processing_stage: Stage of processing where error occurred.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if call_id:
            error_details["call_id"] = call_id
        if caller_number:
            error_details["caller_number"] = caller_number
        if processing_stage:
            error_details["processing_stage"] = processing_stage

        super().__init__(
            message,
            data_type="cdr",
            record_id=call_id,
            error_code=error_code,
            details=error_details,
            original_exception=original_exception
        )


class EPProcessingError(TelecomDataError):
    """Exception raised during EP (Energy Points) processing."""

    def __init__(
        self,
        message: str,
        measurement_id: Optional[str] = None,
        coordinates: Optional[tuple] = None,
        signal_type: Optional[str] = None,
        error_code: str = "EP_PROCESSING_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize EP processing error.

        Args:
            message: Human-readable error message.
            measurement_id: Measurement ID that caused the error.
            coordinates: Coordinates (lat, lon) associated with the error.
            signal_type: Type of signal measurement (rsrp, rsrq, sinr).
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if measurement_id:
            error_details["measurement_id"] = measurement_id
        if coordinates:
            error_details["coordinates"] = coordinates
        if signal_type:
            error_details["signal_type"] = signal_type

        super().__init__(
            message,
            data_type="ep",
            record_id=measurement_id,
            error_code=error_code,
            details=error_details,
            original_exception=original_exception
        )


class KPICalculationError(TelecomDataError):
    """Exception raised during KPI calculation."""

    def __init__(
        self,
        message: str,
        kpi_type: Optional[str] = None,
        entity_id: Optional[str] = None,
        calculation_period: Optional[str] = None,
        error_code: str = "KPI_CALCULATION_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize KPI calculation error.

        Args:
            message: Human-readable error message.
            kpi_type: Type of KPI being calculated.
            entity_id: Entity ID (cell, site, region) for the KPI.
            calculation_period: Time period for the calculation.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if kpi_type:
            error_details["kpi_type"] = kpi_type
        if entity_id:
            error_details["entity_id"] = entity_id
        if calculation_period:
            error_details["calculation_period"] = calculation_period

        super().__init__(
            message,
            data_type="kpi",
            record_id=f"{kpi_type}_{entity_id}" if kpi_type and entity_id else None,
            error_code=error_code,
            details=error_details,
            original_exception=original_exception
        )


class NetworkCoverageError(TelecomDataError):
    """Exception raised during network coverage analysis."""

    def __init__(
        self,
        message: str,
        coverage_type: Optional[str] = None,
        analysis_area: Optional[str] = None,
        threshold_value: Optional[float] = None,
        error_code: str = "NETWORK_COVERAGE_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize network coverage error.

        Args:
            message: Human-readable error message.
            coverage_type: Type of coverage analysis.
            analysis_area: Geographic area being analyzed.
            threshold_value: Threshold value used in analysis.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if coverage_type:
            error_details["coverage_type"] = coverage_type
        if analysis_area:
            error_details["analysis_area"] = analysis_area
        if threshold_value is not None:
            error_details["threshold_value"] = threshold_value

        super().__init__(
            message,
            data_type="coverage_analysis",
            error_code=error_code,
            details=error_details,
            original_exception=original_exception
        )


class SchemaError(DatabaseError):
    """Exception raised when database schema operations fail.

    This exception is raised when there are issues with database
    schema operations, such as table creation or migration failures.
    """

    def __init__(
        self,
        message: str,
        schema: Optional[str] = None,
        table: Optional[str] = None,
        operation: Optional[str] = None,
        error_code: str = "DB_SCHEMA_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize schema error.

        Args:
            message: Human-readable error message.
            schema: Schema name involved in the error.
            table: Table name involved in the error.
            operation: Schema operation that failed.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if schema:
            error_details["schema"] = schema
        if table:
            error_details["table"] = table
        if operation:
            error_details["operation"] = operation

        super().__init__(message, error_code, error_details, original_exception)


class ConfigurationError(DatabaseError):
    """Exception raised when configuration is invalid or missing.

    This exception is raised when there are issues with database
    configuration, such as missing required settings or invalid values.
    """

    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        error_code: str = "DB_CONFIG_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize configuration error.

        Args:
            message: Human-readable error message.
            config_key: Configuration key that caused the error.
            config_value: Configuration value that caused the error.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if config_key:
            error_details["config_key"] = config_key
        if config_value is not None:
            error_details["config_value"] = str(config_value)

        super().__init__(message, error_code, error_details, original_exception)


class TimeoutError(DatabaseError):
    """Exception raised when database operations timeout.

    This exception is raised when database operations exceed
    the configured timeout limits.
    """

    def __init__(
        self,
        message: str,
        timeout_seconds: Optional[float] = None,
        operation: Optional[str] = None,
        error_code: str = "DB_TIMEOUT_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize timeout error.

        Args:
            message: Human-readable error message.
            timeout_seconds: Timeout value that was exceeded.
            operation: Operation that timed out.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if timeout_seconds is not None:
            error_details["timeout_seconds"] = timeout_seconds
        if operation:
            error_details["operation"] = operation

        super().__init__(message, error_code, error_details, original_exception)


class TableNotFoundError(DatabaseError):
    """Exception raised when a table is not found.

    This exception is raised when attempting to perform operations
    on a table that does not exist in the database.
    """

    def __init__(
        self,
        message: str,
        table_name: Optional[str] = None,
        schema_name: Optional[str] = None,
        error_code: str = "DB_TABLE_NOT_FOUND",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize table not found error.

        Args:
            message: Human-readable error message.
            table_name: Name of the table that was not found.
            schema_name: Name of the schema where table was searched.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if table_name:
            error_details["table_name"] = table_name
        if schema_name:
            error_details["schema_name"] = schema_name

        super().__init__(message, error_code, error_details, original_exception)


class TableExistsError(DatabaseError):
    """Exception raised when a table already exists.

    This exception is raised when attempting to create a table
    that already exists in the database.
    """

    def __init__(
        self,
        message: str,
        table_name: Optional[str] = None,
        schema_name: Optional[str] = None,
        error_code: str = "DB_TABLE_EXISTS",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize table exists error.

        Args:
            message: Human-readable error message.
            table_name: Name of the table that already exists.
            schema_name: Name of the schema where table exists.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if table_name:
            error_details["table_name"] = table_name
        if schema_name:
            error_details["schema_name"] = schema_name

        super().__init__(message, error_code, error_details, original_exception)


class SchemaNotFoundError(DatabaseError):
    """Exception raised when a schema is not found.

    This exception is raised when attempting to perform operations
    on a schema that does not exist in the database.
    """

    def __init__(
        self,
        message: str,
        schema_name: Optional[str] = None,
        error_code: str = "DB_SCHEMA_NOT_FOUND",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize schema not found error.

        Args:
            message: Human-readable error message.
            schema_name: Name of the schema that was not found.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if schema_name:
            error_details["schema_name"] = schema_name

        super().__init__(message, error_code, error_details, original_exception)


class OptimizationError(DatabaseError):
    """Exception raised when query optimization fails.

    This exception is raised when there are issues with query
    optimization operations or when optimization cannot be performed.
    """

    def __init__(
        self,
        message: str,
        query: Optional[str] = None,
        optimization_type: Optional[str] = None,
        error_code: str = "DB_OPTIMIZATION_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize optimization error.

        Args:
            message: Human-readable error message.
            query: SQL query that failed optimization.
            optimization_type: Type of optimization that failed.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if query:
            error_details["query"] = query
        if optimization_type:
            error_details["optimization_type"] = optimization_type

        super().__init__(message, error_code, error_details, original_exception)


class FileOperationError(DatabaseError):
    """Exception raised when file operations fail.

    This exception is raised when file I/O operations fail during
    data import/export operations.
    """

    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        operation: Optional[str] = None,
        error_code: str = "FILE_OPERATION_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize file operation error.

        Args:
            message: Human-readable error message.
            file_path: Path to the file that caused the error.
            operation: Type of operation that failed (read, write, etc.).
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if file_path:
            error_details["file_path"] = file_path
        if operation:
            error_details["operation"] = operation

        super().__init__(message, error_code, error_details, original_exception)


class TransformationError(DatabaseError):
    """Exception raised when data transformation operations fail.

    This exception is raised during ETL operations when data
    transformation processes encounter errors.
    """

    def __init__(
        self,
        message: str,
        transformation_type: Optional[str] = None,
        field_name: Optional[str] = None,
        error_code: str = "TRANSFORMATION_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize transformation error.

        Args:
            message: Human-readable error message.
            transformation_type: Type of transformation that failed.
            field_name: Name of the field being transformed.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if transformation_type:
            error_details["transformation_type"] = transformation_type
        if field_name:
            error_details["field_name"] = field_name

        super().__init__(message, error_code, error_details, original_exception)


class ETLError(DatabaseError):
    """Exception raised during ETL (Extract, Transform, Load) operations.

    This is a general exception for ETL pipeline errors that don't
    fall into more specific categories.
    """

    def __init__(
        self,
        message: str,
        stage: Optional[str] = None,
        error_code: str = "ETL_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize ETL error.

        Args:
            message: Human-readable error message.
            stage: ETL stage where error occurred (extract, transform, load).
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if stage:
            error_details["stage"] = stage

        super().__init__(message, error_code, error_details, original_exception)


class DataProcessingError(DatabaseError):
    """Exception raised during data processing operations.

    This exception is raised when general data processing operations
    encounter errors that are not specific to ETL or transformation.
    """

    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        data_type: Optional[str] = None,
        error_code: str = "DATA_PROCESSING_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize data processing error.

        Args:
            message: Human-readable error message.
            operation: Type of operation that failed.
            data_type: Type of data being processed.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if operation:
            error_details["operation"] = operation
        if data_type:
            error_details["data_type"] = data_type

        super().__init__(message, error_code, error_details, original_exception)


# Additional specialized exceptions for enhanced error handling

class BatchProcessingError(DatabaseError):
    """Exception raised during batch processing operations.
    
    This exception is raised when batch processing operations encounter
    errors such as memory issues, batch size problems, or processing failures.
    """
    
    def __init__(
        self,
        message: str,
        batch_id: Optional[str] = None,
        batch_size: Optional[int] = None,
        processed_count: Optional[int] = None,
        error_code: str = "BATCH_PROCESSING_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize batch processing error.
        
        Args:
            message: Human-readable error message.
            batch_id: ID of the batch that failed.
            batch_size: Size of the batch being processed.
            processed_count: Number of items processed before failure.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if batch_id:
            error_details["batch_id"] = batch_id
        if batch_size is not None:
            error_details["batch_size"] = batch_size
        if processed_count is not None:
            error_details["processed_count"] = processed_count
            
        super().__init__(message, error_code, error_details, original_exception)


class MemoryError(DatabaseError):
    """Exception raised when memory-related issues occur.
    
    This exception is raised when operations fail due to insufficient
    memory or memory allocation problems.
    """
    
    def __init__(
        self,
        message: str,
        memory_usage: Optional[int] = None,
        memory_limit: Optional[int] = None,
        operation: Optional[str] = None,
        error_code: str = "MEMORY_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize memory error.
        
        Args:
            message: Human-readable error message.
            memory_usage: Current memory usage in bytes.
            memory_limit: Memory limit in bytes.
            operation: Operation that caused memory issue.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if memory_usage is not None:
            error_details["memory_usage"] = memory_usage
        if memory_limit is not None:
            error_details["memory_limit"] = memory_limit
        if operation:
            error_details["operation"] = operation
            
        super().__init__(message, error_code, error_details, original_exception)


class CacheError(DatabaseError):
    """Exception raised during cache operations.
    
    This exception is raised when cache operations fail, such as
    cache misses, eviction failures, or cache corruption.
    """
    
    def __init__(
        self,
        message: str,
        cache_key: Optional[str] = None,
        cache_type: Optional[str] = None,
        operation: Optional[str] = None,
        error_code: str = "CACHE_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize cache error.
        
        Args:
            message: Human-readable error message.
            cache_key: Cache key that caused the error.
            cache_type: Type of cache (memory, redis, etc.).
            operation: Cache operation that failed.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if cache_key:
            error_details["cache_key"] = cache_key
        if cache_type:
            error_details["cache_type"] = cache_type
        if operation:
            error_details["operation"] = operation
            
        super().__init__(message, error_code, error_details, original_exception)


class QGISError(GeospatialError):
    """Exception raised during QGIS operations.
    
    This exception is raised when QGIS-specific operations fail,
    such as QGIS initialization, plugin loading, or processing errors.
    """
    
    def __init__(
        self,
        message: str,
        qgis_path: Optional[str] = None,
        plugin_name: Optional[str] = None,
        operation: Optional[str] = None,
        error_code: str = "QGIS_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize QGIS error.
        
        Args:
            message: Human-readable error message.
            qgis_path: Path to QGIS installation.
            plugin_name: Name of QGIS plugin that failed.
            operation: QGIS operation that failed.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if qgis_path:
            error_details["qgis_path"] = qgis_path
        if plugin_name:
            error_details["plugin_name"] = plugin_name
        if operation:
            error_details["operation"] = operation
            
        super().__init__(message, operation, None, error_code, error_details, original_exception)


class PerformanceError(DatabaseError):
    """Exception raised when performance thresholds are exceeded.
    
    This exception is raised when operations exceed performance
    thresholds such as execution time, memory usage, or throughput.
    """
    
    def __init__(
        self,
        message: str,
        metric_name: Optional[str] = None,
        actual_value: Optional[float] = None,
        threshold_value: Optional[float] = None,
        operation: Optional[str] = None,
        error_code: str = "PERFORMANCE_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize performance error.
        
        Args:
            message: Human-readable error message.
            metric_name: Name of the performance metric.
            actual_value: Actual value that exceeded threshold.
            threshold_value: Threshold value that was exceeded.
            operation: Operation that caused performance issue.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if metric_name:
            error_details["metric_name"] = metric_name
        if actual_value is not None:
            error_details["actual_value"] = actual_value
        if threshold_value is not None:
            error_details["threshold_value"] = threshold_value
        if operation:
            error_details["operation"] = operation
            
        super().__init__(message, error_code, error_details, original_exception)


class RetryableError(DatabaseError):
    """Exception that indicates the operation can be retried.
    
    This exception is used to mark errors that are transient and
    can potentially succeed if retried.
    """
    
    def __init__(
        self,
        message: str,
        retry_count: int = 0,
        max_retries: Optional[int] = None,
        retry_delay: Optional[float] = None,
        error_code: str = "RETRYABLE_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize retryable error.
        
        Args:
            message: Human-readable error message.
            retry_count: Current number of retries attempted.
            max_retries: Maximum number of retries allowed.
            retry_delay: Delay between retries in seconds.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        error_details["retry_count"] = retry_count
        if max_retries is not None:
            error_details["max_retries"] = max_retries
        if retry_delay is not None:
            error_details["retry_delay"] = retry_delay
            
        super().__init__(message, error_code, error_details, original_exception)


class NonRetryableError(DatabaseError):
    """Exception that indicates the operation should not be retried.
    
    This exception is used to mark errors that are permanent and
    will not succeed even if retried.
    """
    
    def __init__(
        self,
        message: str,
        reason: Optional[str] = None,
        error_code: str = "NON_RETRYABLE_ERROR",
        details: Optional[dict] = None,
        original_exception: Optional[Exception] = None,
    ):
        """Initialize non-retryable error.
        
        Args:
            message: Human-readable error message.
            reason: Reason why the error is not retryable.
            error_code: Error code for programmatic handling.
            details: Additional error details.
            original_exception: Original exception that caused this error.
        """
        error_details = details or {}
        if reason:
            error_details["reason"] = reason
            
        super().__init__(message, error_code, error_details, original_exception)
