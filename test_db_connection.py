#!/usr/bin/env python3
"""测试数据库连接和schema状态"""

import asyncio
from src.database.operations.database_manager import DatabaseManager
from src.database.connection.pool import get_pool_manager
from src.config import get_config

async def test_database_connection():
    """测试数据库连接和schema状态"""
    try:
        print("🔍 测试数据库连接...")
        config = get_config()
        pool_manager = get_pool_manager(config)
        await pool_manager.initialize_pool()
        
        async with pool_manager.acquire() as conn:
            # 检查可用的schemas
            schemas = await conn.fetch("""
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name IN ('ep_to2', 'public')
                ORDER BY schema_name
            """)
            
            print(f"✅ 数据库连接成功")
            print(f"📊 可用schemas: {[s['schema_name'] for s in schemas]}")
            
            # 检查ep_to2 schema是否存在
            ep_schema_exists = any(s['schema_name'] == 'ep_to2' for s in schemas)
            if ep_schema_exists:
                print("✅ ep_to2 schema已存在")
                
                # 检查ep_to2中的表
                tables = await conn.fetch("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'ep_to2'
                    ORDER BY table_name
                """)
                
                if tables:
                    print(f"📋 ep_to2中的表: {[t['table_name'] for t in tables]}")
                else:
                    print("📋 ep_to2 schema为空")
            else:
                print("⚠️ ep_to2 schema不存在")
                
        await pool_manager.close_pool()
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_database_connection())