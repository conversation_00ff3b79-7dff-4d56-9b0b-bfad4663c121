# Connect API 文档

## 概述

Connect API 提供RESTful接口，支持电信数据的导入、查询和管理。API设计遵循REST原则，使用JSON格式进行数据交换。

## 基础信息

- **基础URL**: `http://localhost:8000/api`
- **API版本**: v1
- **数据格式**: JSON
- **认证方式**: <PERSON><PERSON>（可选）

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {}
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## API端点

### 1. 系统信息

#### GET /api/info
获取系统信息和状态

**响应示例:**
```json
{
  "success": true,
  "data": {
    "system": {
      "name": "Connect 电信数据导入系统",
      "version": "2.0.0",
      "status": "running"
    },
    "database": {
      "status": "connected",
      "type": "PostgreSQL",
      "schemas": ["ep_to2", "cdr_to2", "cdr_vdf", "cdr_tdg", "nlg_to2", "kpi_to2", "score_to2"]
    },
    "supported_types": ["EP", "CDR", "NLG", "KPI", "SCORE", "CFG"],
    "supported_operators": ["telefonica", "vodafone", "telekom"]
  }
}
```

### 2. 数据导入

#### POST /api/import/{data_type}
导入指定类型的数据文件

**路径参数:**
- `data_type`: 数据类型 (ep, cdr, nlg, kpi, score, cfg)

**请求体:**
```json
{
  "file_path": "/path/to/data/file.xlsx",
  "operator": "telefonica",  // 可选，自动检测
  "batch_size": 5000,        // 可选，默认5000
  "validate_only": false     // 可选，仅验证不导入
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "import_id": "imp_20240120_001",
    "file_info": {
      "name": "score_data.xlsx",
      "size": "45.2 KB",
      "type": "SCORE",
      "operator": "auto_detected"
    },
    "result": {
      "records_processed": 166,
      "records_imported": 166,
      "success_rate": 100,
      "processing_time": 0.85,
      "table_created": "score_to2.score_20240120_data",
      "errors": []
    }
  }
}
```

#### POST /api/import/batch
批量导入多个文件

**请求体:**
```json
{
  "directory_path": "/path/to/data/input",
  "recursive": true,
  "parallel": 4,
  "filters": {
    "data_types": ["score", "kpi"],  // 可选
    "operators": ["telefonica"]      // 可选
  },
  "options": {
    "batch_size": 5000,
    "validate_only": false,
    "preview_only": false
  }
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "batch_id": "batch_20240120_001",
    "summary": {
      "files_discovered": 282,
      "files_processed": 282,
      "files_successful": 282,
      "files_failed": 0,
      "success_rate": 100,
      "total_records": 15420,
      "processing_time": 45.2
    },
    "results": [
      {
        "file": "score_data_1.xlsx",
        "status": "success",
        "records": 166,
        "table": "score_to2.score_data_1"
      }
      // ... 更多文件结果
    ]
  }
}
```

### 3. 数据查询

#### GET /api/data/{schema}/{table}
查询指定表的数据

**路径参数:**
- `schema`: 数据库模式名
- `table`: 表名

**查询参数:**
- `limit`: 返回记录数限制（默认：100，最大：1000）
- `offset`: 偏移量（用于分页）
- `order_by`: 排序字段
- `order`: 排序方向（asc/desc）

**响应示例:**
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "col_0": "Voice",
        "col_1": "cities",
        "operator_salt": 85.5,
        "sunrise": 92.1,
        "swisscom": 88.7,
        "import_timestamp": "2024-01-20T10:30:00Z",
        "source_file": "score_data.xlsx"
      }
      // ... 更多记录
    ],
    "pagination": {
      "total": 166,
      "limit": 100,
      "offset": 0,
      "has_next": true
    }
  }
}
```

#### GET /api/schemas
获取所有可用的数据库模式

**响应示例:**
```json
{
  "success": true,
  "data": {
    "schemas": [
      {
        "name": "ep_to2",
        "description": "工程参数数据",
        "table_count": 15,
        "total_records": 25000
      },
      {
        "name": "score_to2", 
        "description": "性能评分数据",
        "table_count": 8,
        "total_records": 1200
      }
      // ... 更多模式
    ]
  }
}
```

#### GET /api/schemas/{schema}/tables
获取指定模式下的所有表

**响应示例:**
```json
{
  "success": true,
  "data": {
    "schema": "score_to2",
    "tables": [
      {
        "name": "score_20240120_data",
        "record_count": 166,
        "created_at": "2024-01-20T10:30:00Z",
        "last_updated": "2024-01-20T10:30:00Z",
        "source_file": "score_data.xlsx"
      }
      // ... 更多表
    ]
  }
}
```

### 4. 系统监控

#### GET /api/health
系统健康检查

**响应示例:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "checks": {
      "database": {
        "status": "connected",
        "response_time": 15
      },
      "memory": {
        "usage": "45%",
        "available": "8.2 GB"
      },
      "disk": {
        "usage": "65%",
        "available": "120 GB"
      }
    },
    "uptime": "2 days, 14 hours, 30 minutes"
  }
}
```

#### GET /api/stats
系统统计信息

**响应示例:**
```json
{
  "success": true,
  "data": {
    "import_stats": {
      "total_imports": 1250,
      "successful_imports": 1248,
      "failed_imports": 2,
      "success_rate": 99.84
    },
    "data_stats": {
      "total_records": 2500000,
      "total_tables": 156,
      "data_types": {
        "EP": 45000,
        "CDR": 1800000,
        "KPI": 350000,
        "SCORE": 12000,
        "NLG": 280000,
        "CFG": 13000
      }
    },
    "performance": {
      "avg_import_time": 2.5,
      "avg_records_per_second": 450,
      "peak_performance": 850
    }
  }
}
```

## 错误代码

| 代码 | 描述 |
|------|------|
| `INVALID_REQUEST` | 请求格式错误 |
| `FILE_NOT_FOUND` | 文件不存在 |
| `UNSUPPORTED_FORMAT` | 不支持的文件格式 |
| `DATABASE_ERROR` | 数据库操作错误 |
| `IMPORT_FAILED` | 导入失败 |
| `VALIDATION_ERROR` | 数据验证错误 |
| `SYSTEM_ERROR` | 系统内部错误 |

## 使用示例

### Python客户端示例
```python
import requests

# 基础URL
base_url = "http://localhost:8000/api"

# 获取系统信息
response = requests.get(f"{base_url}/info")
print(response.json())

# 导入单个文件
import_data = {
    "file_path": "/data/score/benchmark.xlsx",
    "batch_size": 5000
}
response = requests.post(f"{base_url}/import/score", json=import_data)
print(response.json())

# 查询数据
response = requests.get(f"{base_url}/data/score_to2/score_benchmark?limit=10")
print(response.json())
```

### cURL示例
```bash
# 获取系统信息
curl -X GET http://localhost:8000/api/info

# 导入SCORE文件
curl -X POST http://localhost:8000/api/import/score \
  -H "Content-Type: application/json" \
  -d '{"file_path": "/data/score/benchmark.xlsx"}'

# 查询数据
curl -X GET "http://localhost:8000/api/data/score_to2/score_benchmark?limit=10"
```

## 注意事项

1. **文件路径**: API接受绝对路径和相对路径
2. **并发限制**: 同时进行的导入操作建议不超过4个
3. **文件大小**: 单个文件建议不超过500MB
4. **超时设置**: 大文件导入可能需要较长时间，建议设置适当的超时时间
5. **错误处理**: 建议实现重试机制处理临时性错误

---

**版本**: v2.0.0  
**更新时间**: 2024-01-20  
**维护团队**: Connect开发团队
