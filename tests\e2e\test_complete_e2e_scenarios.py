#!/usr/bin/env python3
"""
完整的E2E测试场景

该测试套件包含:
1. 数据导入完整流程测试
2. 数据分析和可视化测试
3. 用户认证和权限测试
4. 性能基准测试
5. 安全漏洞测试
6. 业务流程集成测试

运行方法:
    pytest tests/e2e/test_complete_e2e_scenarios.py -v
    pytest tests/e2e/test_complete_e2e_scenarios.py::TestDataImportFlow -v
"""

import os
import sys
import pytest
import asyncio
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
import yaml
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    import pandas as pd
    import requests
    import psycopg2
    from sqlalchemy import create_engine
    import numpy as np
except ImportError as e:
    pytest.skip(f"缺少必要的依赖包: {e}", allow_module_level=True)

# 导入项目模块
try:
    from importers.ep_importer import EPImporter
    from importers.cdr_importer import CDRImporter
    from database.connection.pool import DatabasePoolManager
    from database.connection.session import DatabaseSessionManager
    from analysis.cdr_analyzer import CDRAnalyzer
    from analysis.site_analyzer import SiteAnalyzer
    from auth.user_manager import UserManager
    from api.main import app
except ImportError as e:
    pytest.skip(f"无法导入项目模块: {e}", allow_module_level=True)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class E2ETestConfig:
    """E2E测试配置类"""
    
    def __init__(self, config_path: str = "tests/e2e/config/test_config.yaml"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载测试配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            # 使用默认配置
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'database': {
                'test_db': {
                    'host': 'localhost',
                    'port': 5432,
                    'database': 'connect_test',
                    'username': 'test_user',
                    'password': 'test_password'
                }
            },
            'api': {
                'base_url': 'http://localhost:8000',
                'timeout': 30
            },
            'test_data': {
                'files': {
                    'base_path': 'tests/e2e/data'
                }
            },
            'performance': {
                'data_processing': {
                    'max_import_time': 10,
                    'max_query_time': 3
                }
            }
        }
    
    @property
    def database_url(self) -> str:
        """获取数据库连接URL"""
        db_config = self.config['database']['test_db']
        return f"postgresql://{db_config['username']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    
    @property
    def api_base_url(self) -> str:
        """获取API基础URL"""
        return self.config['api']['base_url']


@pytest.fixture(scope="session")
def e2e_config():
    """E2E测试配置fixture"""
    return E2ETestConfig()


@pytest.fixture(scope="session")
def database_engine(e2e_config):
    """数据库引擎fixture"""
    engine = create_engine(e2e_config.database_url)
    yield engine
    engine.dispose()


@pytest.fixture(scope="session")
def database_pool_manager(e2e_config):
    """数据库连接池管理器fixture"""
    manager = DatabasePoolManager(
        database_url=e2e_config.database_url,
        pool_size=5,
        max_overflow=10
    )
    manager.initialize_pool()
    yield manager
    manager.close()


@pytest.fixture(scope="session")
def test_data_dir(e2e_config):
    """测试数据目录fixture"""
    data_dir = Path(e2e_config.config['test_data']['files']['base_path'])
    data_dir.mkdir(parents=True, exist_ok=True)
    return data_dir


@pytest.fixture(scope="function")
def clean_database(database_engine):
    """清理数据库fixture"""
    # 测试前清理
    with database_engine.connect() as conn:
        conn.execute("TRUNCATE TABLE ep_data, cdr_data, site_data, kpi_data RESTART IDENTITY CASCADE")
        conn.commit()
    
    yield
    
    # 测试后清理
    with database_engine.connect() as conn:
        conn.execute("TRUNCATE TABLE ep_data, cdr_data, site_data, kpi_data RESTART IDENTITY CASCADE")
        conn.commit()


@pytest.fixture(scope="function")
def api_client(e2e_config):
    """API客户端fixture"""
    class APIClient:
        def __init__(self, base_url: str):
            self.base_url = base_url
            self.session = requests.Session()
            self.token = None
        
        def login(self, username: str, password: str) -> bool:
            """用户登录"""
            response = self.session.post(
                f"{self.base_url}/auth/login",
                json={"username": username, "password": password}
            )
            if response.status_code == 200:
                self.token = response.json().get("access_token")
                self.session.headers.update({"Authorization": f"Bearer {self.token}"})
                return True
            return False
        
        def upload_file(self, file_path: Path, data_type: str = "ep") -> Dict[str, Any]:
            """上传文件"""
            with open(file_path, 'rb') as f:
                files = {'file': f}
                data = {'data_type': data_type}
                response = self.session.post(
                    f"{self.base_url}/api/v1/data/upload",
                    files=files,
                    data=data
                )
            return response.json() if response.status_code == 200 else {}
        
        def import_data(self, file_id: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
            """导入数据"""
            payload = {'file_id': file_id}
            if options:
                payload.update(options)
            
            response = self.session.post(
                f"{self.base_url}/api/v1/data/import",
                json=payload
            )
            return response.json() if response.status_code == 200 else {}
        
        def get_analysis_result(self, analysis_type: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
            """获取分析结果"""
            response = self.session.get(
                f"{self.base_url}/api/v1/analysis/{analysis_type}",
                params=params or {}
            )
            return response.json() if response.status_code == 200 else {}
    
    client = APIClient(e2e_config.api_base_url)
    yield client
    client.session.close()


class TestDataImportFlow:
    """数据导入流程测试"""
    
    def test_ep_data_import_complete_flow(self, clean_database, database_pool_manager, test_data_dir):
        """测试EP数据导入完整流程"""
        logger.info("开始测试EP数据导入完整流程")
        
        # 1. 创建测试数据文件
        test_file = test_data_dir / "test_ep_data.csv"
        test_data = {
            'timestamp': pd.date_range('2024-01-01', periods=1000, freq='1min'),
            'longitude': np.random.uniform(116.0, 117.0, 1000),
            'latitude': np.random.uniform(39.5, 40.5, 1000),
            'signal_strength': np.random.uniform(-120, -60, 1000),
            'technology': np.random.choice(['4G', '5G'], 1000),
            'operator': np.random.choice(['移动', '联通', '电信'], 1000),
            'cell_id': [f'CELL_{i:06d}' for i in range(1000)]
        }
        
        df = pd.DataFrame(test_data)
        df.to_csv(test_file, index=False)
        
        # 2. 初始化导入器
        importer = EPImporter(database_pool_manager)
        
        # 3. 执行导入
        start_time = time.time()
        result = importer.import_ep_data(str(test_file))
        import_time = time.time() - start_time
        
        # 4. 验证结果
        assert result['success'] is True
        assert result['imported_rows'] == 1000
        assert import_time < 10  # 性能要求：10秒内完成
        
        # 5. 验证数据库中的数据
        with database_pool_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM ep_data")
            count = cursor.fetchone()[0]
            assert count == 1000
            
            # 验证地理数据
            cursor.execute("SELECT COUNT(*) FROM ep_data WHERE geom IS NOT NULL")
            geo_count = cursor.fetchone()[0]
            assert geo_count == 1000
        
        logger.info(f"EP数据导入测试完成，导入时间: {import_time:.2f}秒")
    
    def test_cdr_data_import_with_validation(self, clean_database, database_pool_manager, test_data_dir):
        """测试CDR数据导入及验证"""
        logger.info("开始测试CDR数据导入及验证")
        
        # 1. 创建包含无效数据的测试文件
        test_file = test_data_dir / "test_cdr_data.csv"
        test_data = {
            'call_id': [f'CALL_{i:08d}' for i in range(100)] + ['INVALID_CALL'],
            'start_time': list(pd.date_range('2024-01-01', periods=100, freq='5min')) + ['invalid_date'],
            'duration': list(np.random.randint(10, 3600, 100)) + [-1],  # 负数持续时间
            'caller_longitude': list(np.random.uniform(116.0, 117.0, 100)) + [200.0],  # 超出范围
            'caller_latitude': list(np.random.uniform(39.5, 40.5, 100)) + [100.0],   # 超出范围
            'callee_longitude': list(np.random.uniform(116.0, 117.0, 100)) + [116.5],
            'callee_latitude': list(np.random.uniform(39.5, 40.5, 100)) + [39.9],
            'call_type': list(np.random.choice(['voice', 'video', 'data'], 100)) + ['invalid_type'],
            'success': list(np.random.choice([True, False], 100)) + [True]
        }
        
        df = pd.DataFrame(test_data)
        df['end_time'] = df['start_time']
        df.to_csv(test_file, index=False)
        
        # 2. 初始化导入器
        importer = CDRImporter(database_pool_manager)
        
        # 3. 执行导入（应该过滤掉无效数据）
        result = importer.import_data(str(test_file))
        
        # 4. 验证结果
        assert result['success'] is True
        assert result['imported_rows'] == 100  # 只导入有效数据
        assert result['validation_errors'] > 0  # 应该有验证错误
        
        logger.info("CDR数据导入验证测试完成")
    
    def test_large_data_import_performance(self, clean_database, database_pool_manager, test_data_dir):
        """测试大数据量导入性能"""
        logger.info("开始测试大数据量导入性能")
        
        # 1. 创建大数据集（100万行）
        test_file = test_data_dir / "large_ep_data.csv"
        
        # 分批创建数据以避免内存问题
        batch_size = 100000
        total_rows = 1000000
        
        with open(test_file, 'w') as f:
            # 写入表头
            f.write('timestamp,longitude,latitude,signal_strength,technology,operator,cell_id\n')
            
            for batch_start in range(0, total_rows, batch_size):
                batch_end = min(batch_start + batch_size, total_rows)
                batch_data = {
                    'timestamp': pd.date_range('2024-01-01', periods=batch_end-batch_start, freq='1s'),
                    'longitude': np.random.uniform(116.0, 117.0, batch_end-batch_start),
                    'latitude': np.random.uniform(39.5, 40.5, batch_end-batch_start),
                    'signal_strength': np.random.uniform(-120, -60, batch_end-batch_start),
                    'technology': np.random.choice(['4G', '5G'], batch_end-batch_start),
                    'operator': np.random.choice(['移动', '联通', '电信'], batch_end-batch_start),
                    'cell_id': [f'CELL_{i:06d}' for i in range(batch_start, batch_end)]
                }
                
                batch_df = pd.DataFrame(batch_data)
                batch_df.to_csv(f, mode='a', header=False, index=False)
        
        # 2. 执行导入并测量性能
        importer = EPImporter(database_pool_manager)
        
        start_time = time.time()
        result = importer.import_ep_data(str(test_file))
        import_time = time.time() - start_time
        
        # 3. 验证性能要求
        assert result['success'] is True
        assert result['imported_rows'] == total_rows
        assert import_time < 60  # 100万行数据应在60秒内完成
        
        # 4. 计算性能指标
        rows_per_second = total_rows / import_time
        logger.info(f"大数据导入性能: {rows_per_second:.0f} 行/秒，总时间: {import_time:.2f}秒")
        
        # 清理大文件
        test_file.unlink()


class TestDataAnalysisFlow:
    """数据分析流程测试"""
    
    def test_cdr_analysis_complete_flow(self, clean_database, database_pool_manager, test_data_dir):
        """测试CDR分析完整流程"""
        logger.info("开始测试CDR分析完整流程")
        
        # 1. 准备测试数据
        self._prepare_cdr_test_data(database_pool_manager)
        
        # 2. 初始化分析器
        analyzer = CDRAnalyzer(database_pool_manager)
        
        # 3. 执行轨迹分析
        start_time = time.time()
        trajectory_result = analyzer.analyze_trajectory(
            start_date='2024-01-01',
            end_date='2024-01-02',
            user_filter=None
        )
        analysis_time = time.time() - start_time
        
        # 4. 验证分析结果
        assert trajectory_result['success'] is True
        assert 'trajectories' in trajectory_result
        assert len(trajectory_result['trajectories']) > 0
        assert analysis_time < 3  # 性能要求：3秒内完成
        
        # 5. 执行热力图分析
        heatmap_result = analyzer.generate_heatmap(
            bounds=[116.0, 39.5, 117.0, 40.5],
            resolution=0.01
        )
        
        assert heatmap_result['success'] is True
        assert 'heatmap_data' in heatmap_result
        
        logger.info(f"CDR分析测试完成，分析时间: {analysis_time:.2f}秒")
    
    def test_site_analysis_with_geo_queries(self, clean_database, database_pool_manager):
        """测试站点分析及地理查询"""
        logger.info("开始测试站点分析及地理查询")
        
        # 1. 准备站点测试数据
        self._prepare_site_test_data(database_pool_manager)
        
        # 2. 初始化分析器
        analyzer = SiteAnalyzer(database_pool_manager)
        
        # 3. 执行覆盖率分析
        start_time = time.time()
        coverage_result = analyzer.analyze_coverage(
            region_bounds=[116.0, 39.5, 117.0, 40.5],
            technology_filter=['4G', '5G']
        )
        query_time = time.time() - start_time
        
        # 4. 验证结果
        assert coverage_result['success'] is True
        assert 'coverage_percentage' in coverage_result
        assert 'gap_areas' in coverage_result
        assert query_time < 3  # 地理查询性能要求
        
        # 5. 执行邻近站点查询
        nearby_result = analyzer.find_nearby_sites(
            center_point=[116.5, 40.0],
            radius_km=5.0
        )
        
        assert nearby_result['success'] is True
        assert 'nearby_sites' in nearby_result
        
        logger.info(f"站点分析测试完成，查询时间: {query_time:.2f}秒")
    
    def _prepare_cdr_test_data(self, database_pool_manager):
        """准备CDR测试数据"""
        with database_pool_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 插入测试CDR数据
            for i in range(1000):
                cursor.execute("""
                    INSERT INTO cdr_data (
                        call_id, start_time, end_time, duration,
                        caller_location, callee_location,
                        call_type, success
                    ) VALUES (
                        %s, %s, %s, %s,
                        ST_SetSRID(ST_MakePoint(%s, %s), 4326),
                        ST_SetSRID(ST_MakePoint(%s, %s), 4326),
                        %s, %s
                    )
                """, (
                    f'CALL_{i:08d}',
                    datetime(2024, 1, 1) + timedelta(minutes=i*5),
                    datetime(2024, 1, 1) + timedelta(minutes=i*5+2),
                    120,
                    116.0 + (i % 100) * 0.01,
                    39.5 + (i % 100) * 0.01,
                    116.0 + ((i+50) % 100) * 0.01,
                    39.5 + ((i+50) % 100) * 0.01,
                    ['voice', 'video', 'data'][i % 3],
                    i % 10 != 0  # 90%成功率
                ))
            
            conn.commit()
    
    def _prepare_site_test_data(self, database_pool_manager):
        """准备站点测试数据"""
        with database_pool_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 插入测试站点数据
            for i in range(100):
                cursor.execute("""
                    INSERT INTO site_data (
                        site_id, site_name, location,
                        technology, operator, status, coverage_radius
                    ) VALUES (
                        %s, %s, ST_SetSRID(ST_MakePoint(%s, %s), 4326),
                        %s, %s, %s, %s
                    )
                """, (
                    f'SITE_{i:05d}',
                    f'基站_{i:03d}',
                    116.0 + (i % 10) * 0.1,
                    39.5 + (i // 10) * 0.1,
                    ['4G', '5G'][i % 2],
                    ['移动', '联通', '电信'][i % 3],
                    ['active', 'inactive'][i % 10 != 0],  # 90%活跃
                    1000 + (i % 5) * 500
                ))
            
            conn.commit()


class TestAPIIntegration:
    """API集成测试"""
    
    def test_complete_api_workflow(self, clean_database, api_client, test_data_dir):
        """测试完整的API工作流程"""
        logger.info("开始测试完整API工作流程")
        
        # 1. 用户登录
        login_success = api_client.login("test_admin", "test_password")
        if not login_success:
            pytest.skip("API服务未运行或登录失败")
        
        # 2. 创建测试文件
        test_file = test_data_dir / "api_test_data.csv"
        test_data = {
            'timestamp': pd.date_range('2024-01-01', periods=100, freq='1min'),
            'longitude': np.random.uniform(116.0, 117.0, 100),
            'latitude': np.random.uniform(39.5, 40.5, 100),
            'signal_strength': np.random.uniform(-120, -60, 100),
            'technology': np.random.choice(['4G', '5G'], 100),
            'operator': np.random.choice(['移动', '联通', '电信'], 100),
            'cell_id': [f'CELL_{i:06d}' for i in range(100)]
        }
        
        df = pd.DataFrame(test_data)
        df.to_csv(test_file, index=False)
        
        # 3. 上传文件
        upload_result = api_client.upload_file(test_file, "ep")
        assert 'file_id' in upload_result
        file_id = upload_result['file_id']
        
        # 4. 导入数据
        import_result = api_client.import_data(file_id)
        assert import_result.get('success') is True
        
        # 5. 获取分析结果
        analysis_result = api_client.get_analysis_result(
            "cdr",
            {"start_date": "2024-01-01", "end_date": "2024-01-02"}
        )
        
        # 验证API响应
        assert isinstance(analysis_result, dict)
        
        logger.info("API工作流程测试完成")
    
    def test_api_error_handling(self, api_client):
        """测试API错误处理"""
        logger.info("开始测试API错误处理")
        
        # 测试未授权访问
        api_client.token = None
        api_client.session.headers.pop("Authorization", None)
        
        response = api_client.session.get(f"{api_client.base_url}/api/v1/data/protected")
        assert response.status_code == 401
        
        # 测试无效参数
        response = api_client.session.get(
            f"{api_client.base_url}/api/v1/analysis/cdr",
            params={"invalid_param": "invalid_value"}
        )
        assert response.status_code in [400, 422]
        
        logger.info("API错误处理测试完成")


class TestSecurityScenarios:
    """安全场景测试"""
    
    def test_sql_injection_protection(self, database_pool_manager):
        """测试SQL注入防护"""
        logger.info("开始测试SQL注入防护")
        
        # 准备恶意输入
        malicious_inputs = [
            "'; DROP TABLE ep_data; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM users --",
            "'; INSERT INTO ep_data VALUES ('hacker'); --"
        ]
        
        # 测试导入器的SQL注入防护
        importer = EPImporter(database_pool_manager)
        
        for malicious_input in malicious_inputs:
            try:
                # 尝试使用恶意输入
                result = importer.validate_input_data({
                    'cell_id': malicious_input,
                    'operator': malicious_input
                })
                
                # 验证输入被正确过滤或拒绝
                assert result['valid'] is False or malicious_input not in str(result)
                
            except Exception as e:
                # 异常也是可接受的防护机制
                logger.info(f"恶意输入被异常处理阻止: {e}")
        
        logger.info("SQL注入防护测试完成")
    
    def test_input_validation_security(self, database_pool_manager):
        """测试输入验证安全性"""
        logger.info("开始测试输入验证安全性")
        
        importer = EPImporter(database_pool_manager)
        
        # 测试各种无效输入
        invalid_inputs = [
            {'longitude': 'javascript:alert("XSS")', 'latitude': 39.9},
            {'longitude': 116.5, 'latitude': '<script>alert("XSS")</script>'},
            {'signal_strength': '../../etc/passwd'},
            {'technology': 'A' * 1000},  # 超长输入
            {'operator': '\x00\x01\x02'},  # 控制字符
        ]
        
        for invalid_input in invalid_inputs:
            result = importer.validate_input_data(invalid_input)
            assert result['valid'] is False
        
        logger.info("输入验证安全性测试完成")


class TestPerformanceBenchmarks:
    """性能基准测试"""
    
    def test_concurrent_data_import(self, clean_database, database_pool_manager, test_data_dir):
        """测试并发数据导入"""
        logger.info("开始测试并发数据导入")
        
        import threading
        import queue
        
        # 创建多个测试文件
        test_files = []
        for i in range(5):
            test_file = test_data_dir / f"concurrent_test_{i}.csv"
            test_data = {
                'timestamp': pd.date_range('2024-01-01', periods=1000, freq='1min'),
                'longitude': np.random.uniform(116.0, 117.0, 1000),
                'latitude': np.random.uniform(39.5, 40.5, 1000),
                'signal_strength': np.random.uniform(-120, -60, 1000),
                'technology': np.random.choice(['4G', '5G'], 1000),
                'operator': np.random.choice(['移动', '联通', '电信'], 1000),
                'cell_id': [f'CELL_{i}_{j:06d}' for j in range(1000)]
            }
            
            df = pd.DataFrame(test_data)
            df.to_csv(test_file, index=False)
            test_files.append(test_file)
        
        # 并发导入函数
        def import_worker(file_path, result_queue):
            try:
                importer = EPImporter(database_pool_manager)
                start_time = time.time()
                result = importer.import_ep_data(str(file_path))
                end_time = time.time()
                
                result_queue.put({
                    'success': result['success'],
                    'time': end_time - start_time,
                    'rows': result['imported_rows']
                })
            except Exception as e:
                result_queue.put({'error': str(e)})
        
        # 启动并发导入
        result_queue = queue.Queue()
        threads = []
        
        start_time = time.time()
        for test_file in test_files:
            thread = threading.Thread(target=import_worker, args=(test_file, result_queue))
            thread.start()
            threads.append(thread)
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 收集结果
        results = []
        while not result_queue.empty():
            results.append(result_queue.get())
        
        # 验证结果
        assert len(results) == 5
        for result in results:
            assert 'error' not in result
            assert result['success'] is True
            assert result['rows'] == 1000
        
        # 验证性能
        assert total_time < 30  # 5个文件并发导入应在30秒内完成
        
        logger.info(f"并发导入测试完成，总时间: {total_time:.2f}秒")
        
        # 清理测试文件
        for test_file in test_files:
            test_file.unlink()
    
    def test_memory_usage_monitoring(self, clean_database, database_pool_manager, test_data_dir):
        """测试内存使用监控"""
        logger.info("开始测试内存使用监控")
        
        import psutil
        import gc
        
        # 获取初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建大数据集
        test_file = test_data_dir / "memory_test_data.csv"
        test_data = {
            'timestamp': pd.date_range('2024-01-01', periods=100000, freq='1s'),
            'longitude': np.random.uniform(116.0, 117.0, 100000),
            'latitude': np.random.uniform(39.5, 40.5, 100000),
            'signal_strength': np.random.uniform(-120, -60, 100000),
            'technology': np.random.choice(['4G', '5G'], 100000),
            'operator': np.random.choice(['移动', '联通', '电信'], 100000),
            'cell_id': [f'CELL_{i:06d}' for i in range(100000)]
        }
        
        df = pd.DataFrame(test_data)
        df.to_csv(test_file, index=False)
        
        # 执行导入并监控内存
        importer = EPImporter(database_pool_manager)
        
        memory_usage = []
        
        def monitor_memory():
            while True:
                current_memory = process.memory_info().rss / 1024 / 1024
                memory_usage.append(current_memory)
                time.sleep(0.1)
        
        # 启动内存监控
        import threading
        monitor_thread = threading.Thread(target=monitor_memory, daemon=True)
        monitor_thread.start()
        
        # 执行导入
        result = importer.import_ep_data(str(test_file))
        
        # 等待一段时间收集内存数据
        time.sleep(1)
        
        # 强制垃圾回收
        gc.collect()
        final_memory = process.memory_info().rss / 1024 / 1024
        
        # 验证结果
        assert result['success'] is True
        
        # 验证内存使用
        max_memory = max(memory_usage) if memory_usage else final_memory
        memory_increase = max_memory - initial_memory
        
        logger.info(f"内存使用: 初始 {initial_memory:.1f}MB, 最大 {max_memory:.1f}MB, 增长 {memory_increase:.1f}MB")
        
        # 内存增长应该在合理范围内（小于2GB）
        assert memory_increase < 2048
        
        # 清理
        test_file.unlink()


if __name__ == "__main__":
    # 运行测试
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "--durations=10"
    ])