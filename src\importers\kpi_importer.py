#!/usr/bin/env python3
"""
KPI (Key Performance Indicators) data importer.

This module provides functionality for importing and processing KPI data files.
KPI files typically contain performance metrics and indicators for telecommunications networks.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
from datetime import datetime

from .base import AbstractImporter, TelecomImportError, ImportResult

# Configure logging
logger = logging.getLogger(__name__)


class KPIImporter(AbstractImporter):
    """Key Performance Indicators data importer."""

    def __init__(
        self,
        config: Optional[Dict[str, Any]] = None,
        db_session=None,
        performance_logger=None,
        **kwargs,
    ):
        """Initialize KPI importer.

        Args:
            config: Configuration dictionary
            db_session: Database session for direct database operations
            performance_logger: Logger for performance metrics
            **kwargs: Additional configuration options
        """
        self.name = "KPIImporter"
        self.supported_formats = ["csv", "xlsx", "xls"]
        self.config = config or {}
        self.db_session = db_session
        self.performance_logger = performance_logger

        # Initialize database components if session provided
        if db_session:
            from src.database.operations import BulkOperations
            from src.database.schema import SchemaManager

            self.schema_manager = SchemaManager(db_session)
            self.bulk_operations = BulkOperations(db_session)

        # Prepare config for AbstractImporter
        importer_config = {
            'name': self.name,
            'data_type': 'kpi',
            'supported_formats': self.supported_formats,
            'batch_size': self.config.get('batch_size', 8000),
            **kwargs
        }
        super().__init__(config=importer_config)

    def set_database_context(self, pool=None, db_manager=None, db_ops=None, schema_manager=None):
        """Set database context for the importer."""
        if pool:
            self.pool_manager = pool
        if db_manager:
            self.db_manager = db_manager
        if db_ops:
            self.db_ops = db_ops
        if schema_manager:
            self.schema_manager = schema_manager

        # Initialize bulk operations if we have a schema manager with pool
        if schema_manager and hasattr(schema_manager, 'pool'):
            from src.database.operations.bulk_operations import BulkOperations
            self.bulk_operations = BulkOperations(schema_manager.pool)
            # Also set db_session for backward compatibility
            self.db_session = schema_manager

    def validate_file(self, file_path: Union[str, Path]) -> bool:
        """Validate KPI data file.

        Args:
            file_path: Path to the file

        Returns:
            bool: True if file is valid

        Raises:
            ImportError: If file is invalid
        """
        path = Path(file_path)

        if not path.exists():
            raise ImportError(f"File does not exist: {path}")

        if path.suffix.lower() not in [".csv", ".xlsx", ".xls"]:
            raise ImportError(f"Unsupported file format: {path.suffix}")

        return True

    def validate_data_structure(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate KPI data structure.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple[bool, List[str]]: Validation result and error messages
        """
        errors = []

        # Accept any column structure - no required columns validation

        # Check for empty DataFrame
        if data.empty:
            errors.append("Data file is empty")

        return len(errors) == 0, errors

    def validate_data_values(self, data: pd.DataFrame) -> Tuple[bool, List[str]]:
        """Validate KPI data values.

        Args:
            data: DataFrame to validate

        Returns:
            Tuple[bool, List[str]]: Validation result and error messages
        """
        errors = []

        # Skip required columns validation - accept any data structure

        # Validate KPI_VALUE is numeric
        if "KPI_VALUE" in data.columns:
            try:
                pd.to_numeric(data["KPI_VALUE"], errors="coerce")
            except Exception as e:
                errors.append(f"KPI_VALUE column contains non-numeric values: {e}")

        # Validate MEASUREMENT_TIME format
        if "MEASUREMENT_TIME" in data.columns:
            try:
                pd.to_datetime(data["MEASUREMENT_TIME"], errors="coerce")
            except Exception as e:
                errors.append(f"MEASUREMENT_TIME column has invalid datetime format: {e}")

        return len(errors) == 0, errors

    def preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Preprocess KPI data before import.

        Args:
            data: Raw DataFrame

        Returns:
            pd.DataFrame: Preprocessed DataFrame
        """
        processed_data = data.copy()

        # Normalize column names
        processed_data.columns = processed_data.columns.str.upper().str.strip()
        
        # Handle duplicate column names by adding counter suffix
        columns = list(processed_data.columns)
        seen = {}
        for i, col in enumerate(columns):
            if col in seen:
                seen[col] += 1
                columns[i] = f"{col}_{seen[col]}"
            else:
                seen[col] = 0
        processed_data.columns = columns

        # Convert KPI_VALUE to numeric
        if "KPI_VALUE" in processed_data.columns:
            processed_data["KPI_VALUE"] = pd.to_numeric(
                processed_data["KPI_VALUE"], errors="coerce"
            )

        # Convert MEASUREMENT_TIME to datetime
        if "MEASUREMENT_TIME" in processed_data.columns:
            processed_data["MEASUREMENT_TIME"] = pd.to_datetime(
                processed_data["MEASUREMENT_TIME"], errors="coerce"
            )

        # Add metadata columns
        processed_data["IMPORT_TIMESTAMP"] = datetime.now()
        processed_data["SOURCE_FILE"] = self.source_path.name

        # Remove rows with critical null values (only for columns that exist)
        required_columns = ["KPI_NAME", "KPI_VALUE", "MEASUREMENT_TIME"]
        existing_required_columns = [col for col in required_columns if col in processed_data.columns]

        if existing_required_columns:
            processed_data = processed_data.dropna(subset=existing_required_columns)
        else:
            # If none of the expected columns exist, try to work with whatever columns are available
            # Remove rows where all values are null
            processed_data = processed_data.dropna(how='all')

        return processed_data

    def get_table_name(self, filename: str) -> str:
        """Generate table name for KPI data.

        Args:
            filename: Source filename

        Returns:
            str: Table name
        """
        import re
        
        # Use pattern from config or default
        pattern = getattr(self.config, 'table_name_pattern', 'kpi_{filename}')
        
        # Clean filename (remove extension and special characters)
        clean_filename = Path(filename).stem.lower()
        clean_filename = "".join(c if c.isalnum() or c == "_" else "_" for c in clean_filename)
        
        # Prepare format parameters with defaults
        format_params = {
            'filename': clean_filename,
            'metric_type': 'general',  # Default metric type
            'period': 'monthly'       # Default period
        }
        
        # Try to extract metric type from filename (common KPI types)
        filename_lower = clean_filename.lower()
        metric_types = ['throughput', 'latency', 'availability', 'quality', 'capacity', 'utilization', 'performance']
        for metric in metric_types:
            if metric in filename_lower:
                format_params['metric_type'] = metric
                break
        
        # Try to extract period from filename
        if 'daily' in filename_lower or 'day' in filename_lower:
            format_params['period'] = 'daily'
        elif 'weekly' in filename_lower or 'week' in filename_lower or 'cw' in filename_lower:
            format_params['period'] = 'weekly'
        elif 'monthly' in filename_lower or 'month' in filename_lower:
            format_params['period'] = 'monthly'
        elif 'quarterly' in filename_lower or 'quarter' in filename_lower or 'q1' in filename_lower or 'q2' in filename_lower or 'q3' in filename_lower or 'q4' in filename_lower:
            format_params['period'] = 'quarterly'
        elif 'yearly' in filename_lower or 'annual' in filename_lower:
            format_params['period'] = 'yearly'
        
        try:
            raw_table_name = pattern.format(**format_params)
        except KeyError as e:
            # If pattern has unknown placeholders, fall back to simple filename
            raw_table_name = f"kpi_{clean_filename}"

        # Validate and fix table name to meet PostgreSQL requirements
        from src.database.utils.validators import InputValidator
        return InputValidator.validate_and_fix_table_name(raw_table_name)

    def get_schema_name(self) -> str:
        """Get schema name for KPI data.

        Returns:
            str: Schema name
        """
        return getattr(self.config, 'schema_name', 'kpi_to2')

    async def import_data(self, source_path: Union[str, Path] = None, **kwargs) -> ImportResult:
        """Import KPI data from source file.

        Args:
            source_path: Path to the source file
            **kwargs: Additional import options

        Returns:
            ImportResult: Result of the import operation

        Raises:
            ImportError: If import fails
        """
        # Set source_path from parameter or use existing
        if source_path:
            from pathlib import Path
            self.source_path = Path(source_path)
        elif not hasattr(self, 'source_path') or not self.source_path:
            raise TelecomImportError("No source path provided")

        start_time = datetime.now()

        try:
            logger.info(f"Starting KPI import from {self.source_path}")

            # Validate source file
            self.validate_file(self.source_path)

            # Read data based on file format with configuration-driven approach
            if self.source_path.suffix.lower() == ".csv":
                # Use configuration-driven CSV structure detection
                structure = self._detect_csv_structure_with_config(self.source_path, 'kpi')
                self.logger.info(f"Using KPI CSV structure: {structure}")

                # Enhanced CSV reading with configuration-based structure detection
                import csv
                data = pd.read_csv(
                    self.source_path,
                    encoding=structure['encoding'],
                    delimiter=structure['delimiter'],
                    skiprows=structure['skip_rows'],
                    header=structure['header_row'],  # Use configured header row
                    engine='python',  # More flexible parser
                    on_bad_lines='skip',  # Skip problematic lines
                    quoting=csv.QUOTE_MINIMAL,
                    skipinitialspace=True,
                    comment='#'  # Skip lines starting with #
                )
            else:
                data = pd.read_excel(self.source_path)

            logger.info(f"Read {len(data)} records from {self.source_path}")

            # Validate data structure
            is_valid, structure_errors = self.validate_data_structure(data)
            if not is_valid:
                raise ImportError(f"Data structure validation failed: {structure_errors}")

            # Validate data values
            is_valid, value_errors = self.validate_data_values(data)
            if not is_valid:
                logger.warning(f"Data value validation warnings: {value_errors}")

            # Preprocess data
            processed_data = self.preprocess_data(data)
            logger.info(f"Preprocessed data: {len(processed_data)} valid records")

            if processed_data.empty:
                raise ImportError("No valid records after preprocessing")

            # Import to database if session available
            records_imported = 0
            if self.db_session and self.bulk_operations:
                table_name = self.get_table_name(self.source_path.name)
                schema_name = self.get_schema_name()

                # Use bulk_insert_dataframe method
                success = await self.bulk_operations.bulk_insert_dataframe_async(
                    table_name=table_name,
                    df=processed_data,
                    schema=schema_name,
                    if_exists="append",
                    chunk_size=kwargs.get("batch_size", 1000)
                )

                if success:
                    records_imported = len(processed_data)
                    logger.info(f"Imported {records_imported} KPI records to {schema_name}.{table_name}")
                else:
                    logger.error("Failed to import KPI records to database")
                    records_imported = 0
            else:
                records_imported = len(processed_data)
                logger.info("Database session not available, skipping database import")

            # Create result using new ImportResult format
            from .base import ImportStatus, ImportMetrics

            processing_time = (datetime.now() - start_time).total_seconds()
            metrics = ImportMetrics(
                start_time=start_time,
                end_time=datetime.now(),
                records_processed=len(processed_data),
                processing_time_seconds=processing_time
            )

            import_result = ImportResult(
                status=ImportStatus.COMPLETED,
                metrics=metrics,
                source_info={
                    "path": str(self.source_path),
                    "size_bytes": self.source_path.stat().st_size,
                    "data_type": "KPI"
                },
                validation_results={
                    "original_records": len(data),
                    "processed_records": len(processed_data),
                    "table_name": self.get_table_name(self.source_path.name) if self.db_session else None,
                    "schema_name": self.get_schema_name() if self.db_session else None,
                },
                metadata={
                    "processing_time_seconds": processing_time,
                    "records_imported": records_imported,
                    "file_format": self.source_path.suffix.lower()
                }
            )

            logger.info(f"KPI import completed successfully in {processing_time:.2f} seconds")
            return import_result

        except Exception as e:
            error_msg = f"KPI import failed: {str(e)}"
            logger.error(error_msg)

            # Create error result using new ImportResult format
            from .base import ImportStatus, ImportMetrics

            processing_time = (datetime.now() - start_time).total_seconds()
            metrics = ImportMetrics(
                start_time=start_time,
                end_time=datetime.now(),
                records_processed=0,
                processing_time_seconds=processing_time,
                errors=[error_msg]
            )

            return ImportResult(
                status=ImportStatus.FAILED,
                error_message=error_msg,
                metrics=metrics,
                source_info={
                    "path": str(self.source_path),
                    "size_bytes": self.source_path.stat().st_size if self.source_path.exists() else 0,
                    "data_type": "KPI"
                }
            )

    def get_sample_data(self, num_rows: int = 5) -> Optional[pd.DataFrame]:
        """Get sample data from the source file.

        Args:
            num_rows: Number of rows to sample

        Returns:
            Optional[pd.DataFrame]: Sample data or None if error
        """
        try:
            if self.source_path.suffix.lower() == ".csv":
                # Use configuration-driven CSV structure detection for sample
                structure = self._detect_csv_structure_with_config(self.source_path, 'kpi')

                import csv
                return pd.read_csv(
                    self.source_path,
                    encoding=structure['encoding'],
                    delimiter=structure['delimiter'],
                    skiprows=structure['skip_rows'],
                    header=structure['header_row'],
                    nrows=num_rows,
                    engine='python',
                    on_bad_lines='skip',
                    quoting=csv.QUOTE_MINIMAL,
                    skipinitialspace=True,
                    comment='#'
                )
            else:
                return pd.read_excel(self.source_path, nrows=num_rows)
        except Exception as e:
            logger.error(f"Failed to get sample data: {e}")
            return None

    def get_file_info(self) -> Dict[str, Any]:
        """Get information about the source file.

        Returns:
            Dict[str, Any]: File information
        """
        try:
            stat = self.source_path.stat()
            return {
                "filename": self.source_path.name,
                "file_size_bytes": stat.st_size,
                "file_size_mb": round(stat.st_size / (1024 * 1024), 2),
                "modified_time": datetime.fromtimestamp(stat.st_mtime),
                "file_format": self.source_path.suffix.lower(),
                "supported": self.source_path.suffix.lower() in [".csv", ".xlsx", ".xls"],
            }
        except Exception as e:
            logger.error(f"Failed to get file info: {e}")
            return {"error": str(e)}

    def get_source_info(self) -> Dict[str, Any]:
        """Get source information for the importer."""
        return {
            'source_path': str(self.source_path) if self.source_path else None,
            'data_type': 'kpi',
            'supported_formats': self.supported_formats
        }

    async def validate_source(self, source_path: str = None) -> bool:
        """Validate the source file."""
        try:
            from pathlib import Path

            path = Path(source_path or self.source_path)

            # Check if file exists
            if not path.exists():
                return False

            # Check file extension
            if path.suffix.lower() not in ['.csv', '.xlsx', '.xls']:
                return False

            return True

        except Exception:
            return False