# -*- coding: utf-8 -*-
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""ETL (Extract, Transform, Load) Module

This module provides comprehensive ETL capabilities including:
- Data extraction from various sources
- Data transformation and processing
- Data loading to databases
- Excel file processing (P2 priority)
- JSON data handling (P2 priority)
- Data validation and cleaning
- Batch processing with progress tracking
- Error handling and logging

P2 Priority Features:
- Advanced data transformation pipelines
- Excel file import/export with complex formatting
- JSON data processing and validation
- Data type conversion and validation
- Batch processing with progress tracking
- Error recovery and data quality reporting
"""

from .extractor import DataExtractor
from .loader import DataLoader
from .pipeline import ETLPipeline
from .processors.csv_processor import read_csv_to_dataframe

# Import core transformer and validator classes
from .transformer import DataTransformer
from .validator import DataValidator

# P2 Priority imports (to be implemented)
try:
    from .processors import (
        ExcelProcessor,
        JSONProcessor,
        ProcessingError,
        ProcessingResult,
    )
    from .transformer import (
        AggregationTransformer,
        FieldTransformer,
        FilterTransformer,
        TransformationPipeline,
        TransformationRule,
        ValidationTransformer,
    )
    from .validators import (
        FieldValidator,
        SchemaValidator,
        ValidationResult,
        ValidationRule,
    )

    P2_FEATURES_AVAILABLE = True
except ImportError:
    P2_FEATURES_AVAILABLE = False

__all__ = [
    "read_csv_to_dataframe",
    "DataExtractor",
    "DataLoader",
    "ETLPipeline",
    "DataTransformer",
    "DataValidator",
    "P2_FEATURES_AVAILABLE",
]

# Add P2 features to __all__ if available
if P2_FEATURES_AVAILABLE:
    __all__.extend(
        [
            # Transformer classes
            "DataTransformer",
            "TransformationPipeline",
            "TransformationRule",
            "FieldTransformer",
            "ValidationTransformer",
            "AggregationTransformer",
            "FilterTransformer",
            # Processor classes
            "ExcelProcessor",
            "JSONProcessor",
            "ProcessingResult",
            "ProcessingError",
            # Validator classes
            "DataValidator",
            "FieldValidator",
            "SchemaValidator",
            "ValidationResult",
            "ValidationRule",
        ]
    )
