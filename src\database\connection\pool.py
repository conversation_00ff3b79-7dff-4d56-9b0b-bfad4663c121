__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Database Connection Pool Management.

This module provides robust asynchronous connection pool management using asyncpg.create_pool().
It includes automatic connection acquisition/release, wrapper methods for common operations,
and comprehensive logging of pool events and statistics.
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional, Union

import asyncpg
from asyncpg import Connection, Pool, Record

# Handle relative imports with fallback
try:
    from ...config import get_config
    from ...config.models import ConnectConfig
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    # Add parent directories to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from config import get_config
    from config.models import ConnectConfig
from ..exceptions import (
    ConfigurationError,
    ConnectionError,
    DatabaseError,
    TimeoutError,
)

# Configure logging
logger = logging.getLogger(__name__)


class DatabasePoolManager:
    """Database connection pool manager using asyncpg.create_pool()."""

    def __init__(self, config: Optional[ConnectConfig] = None):
        """Initialize pool manager with configuration.

        Args:
            config: Database configuration. If None, loads from default config.
        """
        self.config = config or get_config()
        self._pool: Optional[Pool] = None
        self._is_initialized = False
        self._stats = {
            "total_connections_created": 0,
            "total_connections_closed": 0,
            "active_connections": 0,
            "pool_acquisitions": 0,
            "pool_releases": 0,
        }

    async def initialize_pool(self) -> None:
        """Initialize the connection pool.

        Raises:
            ConnectionError: If pool initialization fails.
            ConfigurationError: If configuration is invalid.
            TimeoutError: If pool creation times out.
        """
        if self._is_initialized and self._pool:
            logger.warning("Pool is already initialized")
            return

        try:
            # Build connection parameters from config
            connection_params = self._build_connection_params()

            # Get pool configuration from database config
            pool_config = self.config.database.pool

            logger.info(
                f"Initializing connection pool to {connection_params['host']}:{connection_params['port']} "
                f"(min_size={pool_config.min_size}, max_size={pool_config.max_size})"
            )

            # Create connection pool with timeout
            self._pool = await asyncio.wait_for(
                asyncpg.create_pool(
                    min_size=pool_config.min_size,
                    max_size=pool_config.max_size,
                    command_timeout=self.config.database.connection.command_timeout,
                    max_inactive_connection_lifetime=pool_config.recycle,
                    **connection_params,
                ),
                timeout=pool_config.timeout,
            )

            self._is_initialized = True
            logger.info(
                f"Connection pool initialized successfully "
                f"(min_size={pool_config.min_size}, max_size={pool_config.max_size})"
            )

            # Log initial pool statistics
            await self._log_pool_stats()

        except asyncio.TimeoutError as e:
            error_msg = (
                f"Pool initialization timeout after {pool_config.timeout} seconds"
            )
            logger.error(error_msg)
            raise TimeoutError(
                error_msg,
                error_code="DB_POOL_INIT_TIMEOUT",
                details={"timeout": pool_config.timeout},
                original_exception=e,
            )

        except asyncpg.InvalidCatalogNameError as e:
            error_msg = f"Database '{self.config.database.name}' does not exist"
            logger.error(error_msg)
            raise ConfigurationError(
                error_msg,
                error_code="DB_INVALID_DATABASE",
                details={"database_name": self.config.database.name},
                original_exception=e,
            )

        except asyncpg.InvalidPasswordError as e:
            error_msg = "Invalid database credentials"
            logger.error(error_msg)
            raise ConnectionError(
                error_msg,
                error_code="DB_INVALID_CREDENTIALS",
                details={"user": self.config.database.user},
                original_exception=e,
            )

        except Exception as e:
            error_msg = f"Unexpected error initializing pool: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ConnectionError(
                error_msg,
                error_code="DB_POOL_INIT_FAILED",
                details={
                    "host": self.config.database.host,
                    "port": self.config.database.port,
                    "database": self.config.database.name,
                },
                original_exception=e,
            )

    async def close_pool(self) -> None:
        """Close the connection pool.

        Raises:
            ConnectionError: If pool closure fails.
        """
        if not self._is_initialized or not self._pool:
            logger.debug("No active pool to close")
            return

        try:
            logger.info("Closing connection pool")

            # Log final pool statistics
            await self._log_pool_stats()

            # Check if event loop is still running before closing pool
            try:
                loop = asyncio.get_running_loop()
                if loop.is_closed():
                    logger.warning("Event loop is closed, skipping pool close")
                    self._pool = None
                    self._is_initialized = False
                    return
            except RuntimeError:
                # No running loop, safe to proceed with synchronous cleanup
                logger.warning("No running event loop, performing synchronous cleanup")
                self._pool = None
                self._is_initialized = False
                return

            # Close the pool safely
            await self._pool.close()

            self._pool = None
            self._is_initialized = False

            logger.info("Connection pool closed successfully")
        except RuntimeError as e:
            if "Event loop is closed" in str(e):
                logger.warning("Event loop closed during pool cleanup, performing safe cleanup")
                self._pool = None
                self._is_initialized = False
                return
            else:
                error_msg = f"Runtime error closing connection pool: {str(e)}"
                logger.error(error_msg, exc_info=True)
                raise ConnectionError(
                    error_msg, error_code="DB_POOL_CLOSE_FAILED", original_exception=e
                )
        except Exception as e:
            error_msg = f"Error closing connection pool: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ConnectionError(
                error_msg, error_code="DB_POOL_CLOSE_FAILED", original_exception=e
            )

    async def close(self) -> None:
        """Close the connection pool - alias for close_pool().
        
        This method provides compatibility for code that expects a close() method.
        
        Raises:
            ConnectionError: If pool closure fails.
        """
        await self.close_pool()

    async def get_pool(self) -> Pool:
        """Get the connection pool, initializing if necessary.
        
        Returns:
            asyncpg.Pool: The connection pool.
            
        Raises:
            ConnectionError: If pool initialization fails.
        """
        if not self._is_initialized or not self._pool:
            await self.initialize_pool()
        return self._pool

    async def acquire_connection(self) -> Connection:
        """Acquire a connection from the pool.

        Returns:
            asyncpg.Connection: Database connection from pool.

        Raises:
            ConnectionError: If connection acquisition fails.
            RuntimeError: If pool is not initialized.
        """
        if not self._is_initialized or not self._pool:
            raise RuntimeError("Pool is not initialized. Call initialize_pool() first.")

        try:
            logger.debug("Acquiring connection from pool")

            connection = await asyncio.wait_for(
                self._pool.acquire(), timeout=self.config.database.pool.timeout
            )

            self._stats["pool_acquisitions"] += 1
            self._stats["active_connections"] += 1

            logger.debug(
                f"Connection acquired from pool (active: {self._stats['active_connections']})"
            )

            return connection

        except asyncio.TimeoutError as e:
            error_msg = f"Connection acquisition timeout after {self.config.database.pool.timeout} seconds"
            logger.error(error_msg)
            raise TimeoutError(
                error_msg,
                error_code="DB_CONNECTION_ACQUIRE_TIMEOUT",
                details={"timeout": self.config.database.pool.timeout},
                original_exception=e,
            )

        except Exception as e:
            error_msg = f"Error acquiring connection from pool: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ConnectionError(
                error_msg,
                error_code="DB_CONNECTION_ACQUIRE_FAILED",
                original_exception=e,
            )

    async def release_connection(self, connection: Connection) -> None:
        """Release a connection back to the pool.

        Args:
            connection: Connection to release back to pool.

        Raises:
            ConnectionError: If connection release fails.
            RuntimeError: If pool is not initialized.
        """
        if not self._is_initialized or not self._pool:
            raise RuntimeError("Pool is not initialized. Call initialize_pool() first.")

        if not connection:
            logger.warning("Attempted to release None connection")
            return

        try:
            logger.debug("Releasing connection back to pool")

            await self._pool.release(connection)

            self._stats["pool_releases"] += 1
            self._stats["active_connections"] = max(
                0, self._stats["active_connections"] - 1
            )

            logger.debug(
                f"Connection released to pool (active: {self._stats['active_connections']})"
            )

        except Exception as e:
            error_msg = f"Error releasing connection to pool: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ConnectionError(
                error_msg,
                error_code="DB_CONNECTION_RELEASE_FAILED",
                original_exception=e,
            )

    @asynccontextmanager
    async def get_connection(self):
        """Context manager for automatic connection acquisition and release.

        Yields:
            asyncpg.Connection: Database connection from pool.

        Raises:
            ConnectionError: If connection operations fail.
            RuntimeError: If pool is not initialized.
        """
        connection = None
        acquisition_time = None
        try:
            import time
            start_time = time.time()
            connection = await self.acquire_connection()
            acquisition_time = time.time() - start_time

            # Log slow acquisitions for telecommunications workload monitoring
            if acquisition_time > 1.0:  # More than 1 second
                logger.warning(f"Slow connection acquisition: {acquisition_time:.2f}s")

            yield connection

        except Exception as e:
            logger.error(f"Connection context manager error: {e}")
            # Ensure connection is marked as failed for pool health monitoring
            if connection:
                self._stats["pool_releases"] += 1
                self._stats["active_connections"] = max(0, self._stats["active_connections"] - 1)
            raise
        finally:
            if connection:
                try:
                    await self.release_connection(connection)
                    if acquisition_time:
                        logger.debug(f"Connection used for {time.time() - start_time - acquisition_time:.2f}s")
                except Exception as release_error:
                    logger.error(f"Failed to release connection: {release_error}")
                    # Force connection cleanup to prevent leaks
                    self._stats["active_connections"] = max(0, self._stats["active_connections"] - 1)

    # Wrapper methods for common database operations

    async def execute(self, query: str, *args, connection_type: str = "general") -> str:
        """Execute a query and return the status with telecommunications optimization.

        Args:
            query: SQL query to execute.
            *args: Query parameters.
            connection_type: Type of connection for workload optimization (cdr, ep, kpi, general).

        Returns:
            Query execution status.

        Raises:
            ConnectionError: If query execution fails.
            RuntimeError: If pool is not initialized.
        """
        import time
        start_time = time.time()

        async with self.get_connection() as connection:
            try:
                # Apply connection-specific optimizations for telecommunications workloads
                if connection_type == "cdr":
                    # Optimize for large CDR data processing
                    await connection.execute("SET work_mem = '256MB'")
                    await connection.execute("SET maintenance_work_mem = '512MB'")
                elif connection_type == "ep":
                    # Optimize for geospatial EP data processing
                    await connection.execute("SET work_mem = '128MB'")
                    await connection.execute("SET random_page_cost = 1.1")  # SSD optimization
                elif connection_type == "kpi":
                    # Optimize for KPI calculations with aggregations
                    await connection.execute("SET work_mem = '512MB'")
                    await connection.execute("SET enable_hashagg = on")
                    await connection.execute("SET enable_sort = on")

                logger.debug(f"Executing {connection_type} query: {query[:100]}...")
                result = await connection.execute(query, *args)

                execution_time = time.time() - start_time
                logger.debug(f"Query executed successfully in {execution_time:.3f}s: {result}")

                # Log slow queries for telecommunications monitoring
                if execution_time > 5.0:  # More than 5 seconds
                    logger.warning(f"Slow {connection_type} query detected: {execution_time:.2f}s - {query[:200]}")

                return result

            except Exception as e:
                execution_time = time.time() - start_time
                error_msg = f"Error executing {connection_type} query after {execution_time:.3f}s: {str(e)}"
                logger.error(error_msg, exc_info=True)
                raise DatabaseError(
                    error_msg,
                    error_code="DB_QUERY_EXECUTION_FAILED",
                    details={
                        "query": query[:200],
                        "connection_type": connection_type,
                        "execution_time": execution_time,
                    },
                    original_exception=e,
                )

    async def fetchval(self, query: str, *args) -> Any:
        """Fetch a single value from a query.

        Args:
            query: SQL query to execute.
            *args: Query parameters.

        Returns:
            Single value from query result.

        Raises:
            ConnectionError: If query execution fails.
            RuntimeError: If pool is not initialized.
        """
        async with self.get_connection() as connection:
            try:
                logger.debug(f"Fetching value: {query[:100]}...")
                result = await connection.fetchval(query, *args)
                logger.debug(f"Value fetched successfully")
                return result
            except Exception as e:
                error_msg = f"Error fetching value: {str(e)}"
                logger.error(error_msg, exc_info=True)
                raise DatabaseError(
                    error_msg,
                    error_code="DB_FETCHVAL_FAILED",
                    details={"query": query[:200]},
                    original_exception=e,
                )

    async def fetchrow(self, query: str, *args) -> Optional[Record]:
        """Fetch a single row from a query.

        Args:
            query: SQL query to execute.
            *args: Query parameters.

        Returns:
            Single row from query result or None.

        Raises:
            ConnectionError: If query execution fails.
            RuntimeError: If pool is not initialized.
        """
        async with self.get_connection() as connection:
            try:
                logger.debug(f"Fetching row: {query[:100]}...")
                result = await connection.fetchrow(query, *args)
                logger.debug(f"Row fetched successfully")
                return result
            except Exception as e:
                error_msg = f"Error fetching row: {str(e)}"
                logger.error(error_msg, exc_info=True)
                raise DatabaseError(
                    error_msg,
                    error_code="DB_FETCHROW_FAILED",
                    details={"query": query[:200]},
                    original_exception=e,
                )

    async def fetch(self, query: str, *args) -> List[Record]:
        """Fetch all rows from a query.

        Args:
            query: SQL query to execute.
            *args: Query parameters.

        Returns:
            List of rows from query result.

        Raises:
            ConnectionError: If query execution fails.
            RuntimeError: If pool is not initialized.
        """
        async with self.get_connection() as connection:
            try:
                logger.debug(f"Fetching rows: {query[:100]}...")
                result = await connection.fetch(query, *args)
                logger.debug(f"Fetched {len(result)} rows successfully")
                return result
            except Exception as e:
                error_msg = f"Error fetching rows: {str(e)}"
                logger.error(error_msg, exc_info=True)
                raise DatabaseError(
                    error_msg,
                    error_code="DB_FETCH_FAILED",
                    details={"query": query[:200]},
                    original_exception=e,
                )

    async def executemany(self, query: str, args_list: List[tuple]) -> None:
        """Execute a query multiple times with different parameters.

        Args:
            query: SQL query to execute.
            args_list: List of parameter tuples.

        Raises:
            ConnectionError: If query execution fails.
            RuntimeError: If pool is not initialized.
        """
        async with self.get_connection() as connection:
            try:
                logger.debug(
                    f"Executing query {len(args_list)} times: {query[:100]}..."
                )
                await connection.executemany(query, args_list)
                logger.debug(f"Query executed {len(args_list)} times successfully")
            except Exception as e:
                error_msg = f"Error executing query multiple times: {str(e)}"
                logger.error(error_msg, exc_info=True)
                raise DatabaseError(
                    error_msg,
                    error_code="DB_EXECUTEMANY_FAILED",
                    details={"query": query[:200], "batch_size": len(args_list)},
                    original_exception=e,
                )

    def _build_connection_params(self) -> Dict[str, Any]:
        """Build connection parameters from configuration.

        Returns:
            Dict containing connection parameters for asyncpg.create_pool().

        Raises:
            ConfigurationError: If required configuration is missing.
        """
        try:
            return {
                "host": self.config.database.host,
                "port": self.config.database.port,
                "database": self.config.database.name,
                "user": self.config.database.user,
                "password": self.config.database.password,
                "server_settings": {
                    "application_name": "connect_database_framework_pool"
                },
            }
        except AttributeError as e:
            error_msg = f"Missing required configuration: {str(e)}"
            logger.error(error_msg)
            raise ConfigurationError(
                error_msg, error_code="DB_CONFIG_MISSING", original_exception=e
            )

    async def _log_pool_stats(self) -> None:
        """Log current pool statistics."""
        if not self._pool:
            return

        try:
            pool_size = self._pool.get_size()
            pool_idle = self._pool.get_idle_size()
            pool_active = pool_size - pool_idle

            logger.info(
                f"Pool Stats - Size: {pool_size}, Active: {pool_active}, Idle: {pool_idle}, "
                f"Acquisitions: {self._stats['pool_acquisitions']}, "
                f"Releases: {self._stats['pool_releases']}"
            )
        except Exception as e:
            logger.warning(f"Error logging pool stats: {str(e)}")

    def get_pool_stats(self) -> Dict[str, Any]:
        """Get current pool statistics.

        Returns:
            Dictionary containing pool statistics.
        """
        stats = self._stats.copy()

        if self._pool:
            stats.update(
                {
                    "pool_size": self._pool.get_size(),
                    "pool_idle": self._pool.get_idle_size(),
                    "pool_active": self._pool.get_size() - self._pool.get_idle_size(),
                    "is_initialized": self._is_initialized,
                }
            )
        else:
            stats.update(
                {
                    "pool_size": 0,
                    "pool_idle": 0,
                    "pool_active": 0,
                    "is_initialized": self._is_initialized,
                }
            )

        return stats

    def is_initialized(self) -> bool:
        """Check if pool is initialized.

        Returns:
            bool: True if pool is initialized, False otherwise.
        """
        return self._is_initialized and self._pool is not None

    # Backward compatibility aliases for performance tests
    async def return_connection(self, connection) -> None:
        """Alias for release_connection for backward compatibility."""
        await self.release_connection(connection)

    def acquire(self):
        """Compatibility method for SchemaManager - returns the pool's acquire method."""
        if not self._pool:
            raise ConnectionError("Pool not initialized")
        return self._pool.acquire()


# Global pool manager instance
_pool_manager: Optional[DatabasePoolManager] = None


def get_pool_manager(config: Optional[ConnectConfig] = None) -> DatabasePoolManager:
    """Get or create global pool manager instance.

    Args:
        config: Database configuration. If None, uses existing or creates default.

    Returns:
        DatabasePoolManager: Global pool manager instance.
    """
    global _pool_manager

    if _pool_manager is None or config is not None:
        _pool_manager = DatabasePoolManager(config)

    return _pool_manager


async def initialize_global_pool(config: Optional[ConnectConfig] = None) -> None:
    """Initialize the global connection pool.

    Args:
        config: Optional database configuration.

    Raises:
        ConnectionError: If pool initialization fails.
        ConfigurationError: If configuration is invalid.
        TimeoutError: If pool creation times out.
    """
    pool_manager = get_pool_manager(config)
    await pool_manager.initialize_pool()


async def close_global_pool() -> None:
    """Close the global connection pool.

    Raises:
        ConnectionError: If pool closure fails.
    """
    global _pool_manager
    if _pool_manager:
        await _pool_manager.close_pool()


def get_pool_connection():
    """Get a connection from the global pool using context manager.

    Returns:
        AsyncContextManager: Context manager that yields database connection.

    Raises:
        ConnectionError: If connection operations fail.
        RuntimeError: If pool is not initialized.
    """
    pool_manager = get_pool_manager()
    return pool_manager.get_connection()


# Backward compatibility alias
DatabasePool = DatabasePoolManager  # Alias for backward compatibility
