"""Base importer classes and exceptions.

This module provides the base classes and exceptions for all data importers.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Configure logging
logger = logging.getLogger(__name__)


class ImportError(Exception):
    """Base exception for import operations."""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        original_exception: Optional[Exception] = None,
    ):
        super().__init__(message)
        self.error_code = error_code
        self.original_exception = original_exception


@dataclass
class ImportResult:
    """Result of an import operation."""

    success: bool
    source_path: Optional[Path] = None
    records_imported: int = 0
    records_failed: int = 0
    import_time: Optional[datetime] = None
    file_size_bytes: int = 0
    error_message: Optional[str] = None
    processing_time: float = 0.0
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.import_time is None:
            self.import_time = datetime.now()


# BaseImporter class has been migrated to AbstractImporter in src/importers/base/abstract_importer.py
# All functionality has been preserved and enhanced in the new architecture.
# This file now only contains the ImportResult class and utility functions.
