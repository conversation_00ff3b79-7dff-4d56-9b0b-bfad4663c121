"""Query optimization classes for SQL performance enhancement.

This module provides optimizers for improving SQL query performance
through various optimization techniques and query rewriting.
"""

import re
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple

from loguru import logger

from ..exceptions import OptimizationError
from .dialects import Dialect


class OptimizationLevel(Enum):
    """Optimization levels."""

    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"


@dataclass
class OptimizationResult:
    """Result of query optimization."""

    original_query: str
    optimized_query: str
    optimizations_applied: List[str]
    estimated_improvement: Optional[float] = None
    warnings: List[str] = None

    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


class Optimizer(ABC):
    """Abstract base class for query optimizers."""

    def __init__(self, level: OptimizationLevel = OptimizationLevel.MODERATE):
        """Initialize optimizer.

        Args:
            level: Optimization level
        """
        self.level = level

    @abstractmethod
    def optimize(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dialect: Optional[Dialect] = None,
    ) -> OptimizationResult:
        """Optimize query.

        Args:
            query: SQL query
            parameters: Query parameters
            dialect: SQL dialect

        Returns:
            OptimizationResult

        Raises:
            OptimizationError: If optimization fails
        """
        pass


class SelectOptimizer(Optimizer):
    """Optimizer for SELECT statements."""

    def optimize(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dialect: Optional[Dialect] = None,
    ) -> OptimizationResult:
        """Optimize SELECT query.

        Args:
            query: SQL query
            parameters: Query parameters
            dialect: SQL dialect

        Returns:
            OptimizationResult
        """
        original_query = query
        optimized_query = query
        optimizations = []
        warnings = []

        try:
            # Remove unnecessary SELECT *
            optimized_query, select_opt = self._optimize_select_star(optimized_query)
            if select_opt:
                optimizations.append(
                    "Suggested specific column selection instead of SELECT *"
                )
                warnings.append("Consider specifying exact columns instead of SELECT *")

            # Optimize WHERE clause
            optimized_query, where_opts = self._optimize_where_clause(optimized_query)
            optimizations.extend(where_opts)

            # Optimize JOIN operations
            optimized_query, join_opts = self._optimize_joins(optimized_query)
            optimizations.extend(join_opts)

            # Optimize ORDER BY
            optimized_query, order_opts = self._optimize_order_by(optimized_query)
            optimizations.extend(order_opts)

            # Add LIMIT if missing for potentially large result sets
            optimized_query, limit_opts = self._suggest_limit(optimized_query)
            if limit_opts:
                optimizations.extend(limit_opts)
                warnings.append("Consider adding LIMIT clause for large result sets")

            return OptimizationResult(
                original_query=original_query,
                optimized_query=optimized_query,
                optimizations_applied=optimizations,
                warnings=warnings,
            )

        except Exception as e:
            logger.error(f"SELECT optimization failed: {e}")
            raise OptimizationError(f"Optimization error: {e}")

    def _optimize_select_star(self, query: str) -> Tuple[str, bool]:
        """Optimize SELECT * usage.

        Args:
            query: SQL query

        Returns:
            Tuple of (optimized_query, was_optimized)
        """
        # In conservative mode, just warn about SELECT *
        if self.level == OptimizationLevel.CONSERVATIVE:
            has_select_star = re.search(r"\bselect\s+\*\s+from\b", query, re.IGNORECASE)
            return query, bool(has_select_star)

        # In moderate/aggressive mode, we could suggest alternatives
        # but we can't automatically replace without knowing the schema
        return query, False

    def _optimize_where_clause(self, query: str) -> Tuple[str, List[str]]:
        """Optimize WHERE clause.

        Args:
            query: SQL query

        Returns:
            Tuple of (optimized_query, optimizations_applied)
        """
        optimized = query
        optimizations = []

        # Convert LIKE with no wildcards to =
        like_pattern = re.compile(r"(\w+)\s+like\s+'([^%_]+)'\s*", re.IGNORECASE)
        matches = like_pattern.findall(optimized)
        for field, value in matches:
            old_condition = f"{field} LIKE '{value}'"
            new_condition = f"{field} = '{value}'"
            optimized = optimized.replace(old_condition, new_condition)
            optimizations.append(f"Converted LIKE to = for exact match: {field}")

        # Suggest index usage for functions in WHERE
        func_in_where = re.search(
            r"\bwhere\s+\w+\s*\([^)]*\)\s*[=<>]", optimized, re.IGNORECASE
        )
        if func_in_where:
            optimizations.append(
                "Consider avoiding functions in WHERE clause for better index usage"
            )

        # Optimize OR conditions to IN when possible
        if self.level in [OptimizationLevel.MODERATE, OptimizationLevel.AGGRESSIVE]:
            optimized, or_opts = self._optimize_or_to_in(optimized)
            optimizations.extend(or_opts)

        return optimized, optimizations

    def _optimize_or_to_in(self, query: str) -> Tuple[str, List[str]]:
        """Convert OR conditions to IN when possible.

        Args:
            query: SQL query

        Returns:
            Tuple of (optimized_query, optimizations_applied)
        """
        optimizations = []

        # Pattern for field = value OR field = value2 OR ...
        or_pattern = re.compile(
            r"(\w+)\s*=\s*([^\s]+)(?:\s+or\s+\1\s*=\s*([^\s]+))+", re.IGNORECASE
        )

        # This is a simplified implementation
        # In practice, you'd need more sophisticated parsing
        if or_pattern.search(query):
            optimizations.append("Consider converting OR conditions to IN clause")

        return query, optimizations

    def _optimize_joins(self, query: str) -> Tuple[str, List[str]]:
        """Optimize JOIN operations.

        Args:
            query: SQL query

        Returns:
            Tuple of (optimized_query, optimizations_applied)
        """
        optimizations = []

        # Check for Cartesian products (JOIN without ON)
        cartesian_pattern = re.compile(
            r"\bjoin\s+\w+(?:\s+\w+)?(?!\s+on\b)", re.IGNORECASE
        )

        if cartesian_pattern.search(query):
            optimizations.append(
                "Warning: Potential Cartesian product detected - ensure JOIN has ON clause"
            )

        # Suggest INNER JOIN instead of WHERE for joins
        where_join_pattern = re.compile(
            r"\bfrom\s+\w+\s*,\s*\w+.*\bwhere\s+\w+\.\w+\s*=\s*\w+\.\w+",
            re.IGNORECASE | re.DOTALL,
        )

        if where_join_pattern.search(query):
            optimizations.append(
                "Consider using explicit JOIN syntax instead of WHERE clause joins"
            )

        return query, optimizations

    def _optimize_order_by(self, query: str) -> Tuple[str, List[str]]:
        """Optimize ORDER BY clause.

        Args:
            query: SQL query

        Returns:
            Tuple of (optimized_query, optimizations_applied)
        """
        optimizations = []

        # Check for ORDER BY with functions
        func_order_pattern = re.compile(
            r"\border\s+by\s+\w+\s*\([^)]*\)", re.IGNORECASE
        )

        if func_order_pattern.search(query):
            optimizations.append(
                "Consider avoiding functions in ORDER BY for better performance"
            )

        # Check for ORDER BY without LIMIT
        has_order = re.search(r"\border\s+by\b", query, re.IGNORECASE)
        has_limit = re.search(r"\blimit\b", query, re.IGNORECASE)

        if has_order and not has_limit:
            optimizations.append("Consider adding LIMIT when using ORDER BY")

        return query, optimizations

    def _suggest_limit(self, query: str) -> Tuple[str, List[str]]:
        """Suggest LIMIT clause for potentially large result sets.

        Args:
            query: SQL query

        Returns:
            Tuple of (optimized_query, optimizations_applied)
        """
        optimizations = []

        # Check if query has no WHERE clause and no LIMIT
        has_where = re.search(r"\bwhere\b", query, re.IGNORECASE)
        has_limit = re.search(r"\blimit\b", query, re.IGNORECASE)

        if not has_where and not has_limit:
            optimizations.append(
                "Consider adding WHERE clause or LIMIT to prevent large result sets"
            )

        return query, optimizations


class SubqueryOptimizer(Optimizer):
    """Optimizer for subqueries."""

    def optimize(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dialect: Optional[Dialect] = None,
    ) -> OptimizationResult:
        """Optimize subqueries.

        Args:
            query: SQL query
            parameters: Query parameters
            dialect: SQL dialect

        Returns:
            OptimizationResult
        """
        original_query = query
        optimized_query = query
        optimizations = []
        warnings = []

        try:
            # Convert EXISTS subqueries to JOINs when possible
            optimized_query, exists_opts = self._optimize_exists_subqueries(
                optimized_query
            )
            optimizations.extend(exists_opts)

            # Convert IN subqueries to JOINs when possible
            optimized_query, in_opts = self._optimize_in_subqueries(optimized_query)
            optimizations.extend(in_opts)

            # Optimize correlated subqueries
            optimized_query, corr_opts = self._optimize_correlated_subqueries(
                optimized_query
            )
            optimizations.extend(corr_opts)

            return OptimizationResult(
                original_query=original_query,
                optimized_query=optimized_query,
                optimizations_applied=optimizations,
                warnings=warnings,
            )

        except Exception as e:
            logger.error(f"Subquery optimization failed: {e}")
            raise OptimizationError(f"Subquery optimization error: {e}")

    def _optimize_exists_subqueries(self, query: str) -> Tuple[str, List[str]]:
        """Optimize EXISTS subqueries.

        Args:
            query: SQL query

        Returns:
            Tuple of (optimized_query, optimizations_applied)
        """
        optimizations = []

        # Check for EXISTS subqueries
        exists_pattern = re.compile(r"\bexists\s*\(\s*select\b", re.IGNORECASE)

        if exists_pattern.search(query):
            if self.level == OptimizationLevel.AGGRESSIVE:
                optimizations.append(
                    "Consider converting EXISTS subquery to JOIN for better performance"
                )
            else:
                optimizations.append(
                    "EXISTS subquery detected - consider JOIN alternative"
                )

        return query, optimizations

    def _optimize_in_subqueries(self, query: str) -> Tuple[str, List[str]]:
        """Optimize IN subqueries.

        Args:
            query: SQL query

        Returns:
            Tuple of (optimized_query, optimizations_applied)
        """
        optimizations = []

        # Check for IN subqueries
        in_pattern = re.compile(r"\bin\s*\(\s*select\b", re.IGNORECASE)

        if in_pattern.search(query):
            optimizations.append(
                "Consider converting IN subquery to JOIN or EXISTS for better performance"
            )

        return query, optimizations

    def _optimize_correlated_subqueries(self, query: str) -> Tuple[str, List[str]]:
        """Optimize correlated subqueries.

        Args:
            query: SQL query

        Returns:
            Tuple of (optimized_query, optimizations_applied)
        """
        optimizations = []

        # This is a simplified check for correlated subqueries
        # Look for subqueries that reference outer query tables
        subquery_pattern = re.compile(
            r"\(\s*select\s+.*?\bwhere\s+.*?\)", re.IGNORECASE | re.DOTALL
        )

        subqueries = subquery_pattern.findall(query)
        for subquery in subqueries:
            # Very basic check - in practice you'd need proper parsing
            if "." in subquery:  # Likely table.column reference
                optimizations.append(
                    "Correlated subquery detected - consider JOIN or window function alternative"
                )
                break

        return query, optimizations


class IndexOptimizer(Optimizer):
    """Optimizer for index usage suggestions."""

    def optimize(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dialect: Optional[Dialect] = None,
    ) -> OptimizationResult:
        """Suggest index optimizations.

        Args:
            query: SQL query
            parameters: Query parameters
            dialect: SQL dialect

        Returns:
            OptimizationResult
        """
        original_query = query
        optimizations = []
        warnings = []

        try:
            # Analyze WHERE clause for index opportunities
            where_suggestions = self._analyze_where_for_indexes(query)
            optimizations.extend(where_suggestions)

            # Analyze JOIN conditions for index opportunities
            join_suggestions = self._analyze_joins_for_indexes(query)
            optimizations.extend(join_suggestions)

            # Analyze ORDER BY for index opportunities
            order_suggestions = self._analyze_order_by_for_indexes(query)
            optimizations.extend(order_suggestions)

            return OptimizationResult(
                original_query=original_query,
                optimized_query=query,  # No actual query changes, just suggestions
                optimizations_applied=optimizations,
                warnings=warnings,
            )

        except Exception as e:
            logger.error(f"Index optimization failed: {e}")
            raise OptimizationError(f"Index optimization error: {e}")

    def _analyze_where_for_indexes(self, query: str) -> List[str]:
        """Analyze WHERE clause for index opportunities.

        Args:
            query: SQL query

        Returns:
            List of optimization suggestions
        """
        suggestions = []

        # Find WHERE conditions
        where_pattern = re.compile(
            r"\bwhere\s+(.+?)(?:\bgroup\s+by|\border\s+by|\blimit|$)",
            re.IGNORECASE | re.DOTALL,
        )
        where_match = where_pattern.search(query)

        if where_match:
            where_clause = where_match.group(1)

            # Find equality conditions
            eq_pattern = re.compile(r"(\w+(?:\.\w+)?)\s*=\s*", re.IGNORECASE)
            eq_fields = eq_pattern.findall(where_clause)

            if eq_fields:
                suggestions.append(
                    f"Consider creating indexes on: {', '.join(set(eq_fields))}"
                )

            # Find range conditions
            range_pattern = re.compile(r"(\w+(?:\.\w+)?)\s*[<>]=?\s*", re.IGNORECASE)
            range_fields = range_pattern.findall(where_clause)

            if range_fields:
                suggestions.append(
                    f"Consider creating indexes for range queries on: {', '.join(set(range_fields))}"
                )

        return suggestions

    def _analyze_joins_for_indexes(self, query: str) -> List[str]:
        """Analyze JOIN conditions for index opportunities.

        Args:
            query: SQL query

        Returns:
            List of optimization suggestions
        """
        suggestions = []

        # Find JOIN conditions
        join_pattern = re.compile(
            r"\bjoin\s+\w+.*?\bon\s+([^\s]+)\s*=\s*([^\s]+)", re.IGNORECASE
        )
        join_conditions = join_pattern.findall(query)

        if join_conditions:
            join_fields = [
                field for condition in join_conditions for field in condition
            ]
            suggestions.append(
                f"Consider creating indexes on JOIN columns: {', '.join(set(join_fields))}"
            )

        return suggestions

    def _analyze_order_by_for_indexes(self, query: str) -> List[str]:
        """Analyze ORDER BY for index opportunities.

        Args:
            query: SQL query

        Returns:
            List of optimization suggestions
        """
        suggestions = []

        # Find ORDER BY columns
        order_pattern = re.compile(
            r"\border\s+by\s+([^\s]+(?:\s*,\s*[^\s]+)*)", re.IGNORECASE
        )
        order_match = order_pattern.search(query)

        if order_match:
            order_clause = order_match.group(1)
            # Extract column names (simplified)
            columns = re.findall(r"(\w+(?:\.\w+)?)", order_clause)

            if columns:
                suggestions.append(
                    f"Consider creating composite index for ORDER BY: {', '.join(columns)}"
                )

        return suggestions


class CompositeOptimizer(Optimizer):
    """Composite optimizer that runs multiple optimizers."""

    def __init__(
        self,
        optimizers: List[Optimizer],
        level: OptimizationLevel = OptimizationLevel.MODERATE,
    ):
        """Initialize composite optimizer.

        Args:
            optimizers: List of optimizers to run
            level: Optimization level
        """
        super().__init__(level)
        self.optimizers = optimizers

    def optimize(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dialect: Optional[Dialect] = None,
    ) -> OptimizationResult:
        """Run all optimizers.

        Args:
            query: SQL query
            parameters: Query parameters
            dialect: SQL dialect

        Returns:
            Combined OptimizationResult
        """
        original_query = query
        current_query = query
        all_optimizations = []
        all_warnings = []

        for optimizer in self.optimizers:
            try:
                result = optimizer.optimize(current_query, parameters, dialect)
                current_query = result.optimized_query
                all_optimizations.extend(result.optimizations_applied)
                all_warnings.extend(result.warnings)
            except OptimizationError as e:
                logger.warning(f"Optimizer {type(optimizer).__name__} failed: {e}")
                all_warnings.append(f"Optimizer {type(optimizer).__name__} failed: {e}")

        return OptimizationResult(
            original_query=original_query,
            optimized_query=current_query,
            optimizations_applied=all_optimizations,
            warnings=all_warnings,
        )

    def add_optimizer(self, optimizer: Optimizer):
        """Add an optimizer.

        Args:
            optimizer: Optimizer to add
        """
        self.optimizers.append(optimizer)

    def remove_optimizer(self, optimizer_type: type):
        """Remove optimizers of a specific type.

        Args:
            optimizer_type: Type of optimizer to remove
        """
        self.optimizers = [
            o for o in self.optimizers if not isinstance(o, optimizer_type)
        ]


# Convenience functions
def create_default_optimizer(
    level: OptimizationLevel = OptimizationLevel.MODERATE,
) -> CompositeOptimizer:
    """Create default optimizer with common optimizers.

    Args:
        level: Optimization level

    Returns:
        CompositeOptimizer instance
    """
    optimizers = [
        SelectOptimizer(level),
        SubqueryOptimizer(level),
        IndexOptimizer(level),
    ]

    return CompositeOptimizer(optimizers, level)


def create_conservative_optimizer() -> CompositeOptimizer:
    """Create conservative optimizer for production use.

    Returns:
        CompositeOptimizer instance
    """
    return create_default_optimizer(OptimizationLevel.CONSERVATIVE)


def create_aggressive_optimizer() -> CompositeOptimizer:
    """Create aggressive optimizer for development.

    Returns:
        CompositeOptimizer instance
    """
    return create_default_optimizer(OptimizationLevel.AGGRESSIVE)


def optimize_query(
    query: str,
    parameters: Optional[Dict[str, Any]] = None,
    dialect: Optional[Dialect] = None,
    level: OptimizationLevel = OptimizationLevel.MODERATE,
) -> OptimizationResult:
    """Optimize query with default settings.

    Args:
        query: SQL query
        parameters: Query parameters
        dialect: SQL dialect
        level: Optimization level

    Returns:
        OptimizationResult

    Raises:
        OptimizationError: If optimization fails
    """
    optimizer = create_default_optimizer(level)
    return optimizer.optimize(query, parameters, dialect)
