import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

"""Global test configuration and fixtures for the Connect database framework.

This module provides shared pytest fixtures and configuration for all test modules.
It includes database connections, mock objects, test data, and utility functions.
"""

import asyncio
import json
import os
import tempfile
from pathlib import Path
from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, Mock

import pandas as pd
import pytest
import yaml
from shapely.geometry import Point, Polygon

# Import framework components
from src.config import get_config
from src.config.models import ConnectConfig, DatabaseConfig
from src.database.connection.pool import DatabasePoolManager
from src.database.connection.session import SessionManager
from src.database.exceptions import DatabaseError
from src.database.geospatial.validator import GeometryValidator
from src.database.monitoring.logger import DatabaseLogger
from src.database.operations.crud import CRUDOperations
from src.database.schema.manager import SchemaManager


# Additional imports for E2E testing
from src.database.connection.pool import get_pool_manager, initialize_global_pool


# Test Configuration
@pytest.fixture(scope="session")
def test_config_dir() -> Path:
    """Create a temporary directory for test configuration files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture(scope="session")
def test_config_data() -> Dict[str, Any]:
    """Provide test configuration data with telecommunications support."""
    return {
        "database": {
            "host": os.getenv("TEST_DB_HOST", "localhost"),
            "port": int(os.getenv("TEST_DB_PORT", "5432")),
            "name": os.getenv("TEST_DB_NAME", "test_connect"),
            "user": os.getenv("TEST_DB_USER", "test_user"),
            "password": os.getenv("TEST_DB_PASSWORD", "test_password"),
            "ssl_mode": "prefer",
            "connection_timeout": 30,
            "command_timeout": 60,
        },
        "pool": {
            "size": 5,
            "max_overflow": 10,
            "timeout": 30,
            "recycle": 3600,
        },
        "table_standards": {
            "naming_convention": {
                "table": "snake_case",
                "column": "snake_case",
                "index": "idx_{table}_{column}",
            },
            "data_types": {
                "string_max_length": 255,
                "text_max_length": 65535,
                "decimal_precision": 10,
                "decimal_scale": 2,
            },
        },
        "data_sources": {
            "ep": {
                "schema_name": "ep_test",
                "file_extensions": [".xlsx", ".xls"],
                "skip_rows": 1,
                "header_row": 1,
                "table_name_pattern": "ep_{cell_type}_{year}_cw{week}",
            },
            "cdr": {
                "file_extensions": [".xlsx", ".xls"],
                "skip_rows": 0,
                "header_row": 0,
                "table_name_pattern": "cdr_{year}Q{quarter}_{service_type}",
                "sheet_schema_mapping": {
                    "telefonica": "cdr_to2",
                    "vodafone": "cdr_vdf",
                    "telekom": "cdr_tdg",
                },
            },
        },
        "logging": {
            "level": "DEBUG",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        },
        "testing": {
            "mock_data_size": 1000,
            "performance_threshold_ms": 1000,
            "memory_limit_mb": 512,
        },
        "telecom": {
            "cdr": {
                "test_batch_size": 100,
                "mock_call_duration_range": [10, 3600],  # 10 seconds to 1 hour
                "mock_success_rate": 0.95,
            },
            "ep": {
                "test_batch_size": 50,
                "mock_signal_range": [-120, -60],  # RSRP range in dBm
                "mock_coordinate_bounds": {
                    "lat_min": 35.0, "lat_max": 40.0,
                    "lon_min": 110.0, "lon_max": 120.0,
                },
            },
            "kpi": {
                "test_calculation_interval": 60,  # 1 minute for testing
                "mock_thresholds": {
                    "call_success_rate": 95.0,
                    "signal_strength_min": -110.0,
                    "handover_success_rate": 98.0,
                },
            },
        },
    }


@pytest.fixture
def test_config_file(test_config_dir: Path, test_config_data: Dict[str, Any]) -> Path:
    """Create a test configuration file."""
    config_file = test_config_dir / "test_database.yaml"
    with open(config_file, "w", encoding="utf-8") as f:
        yaml.dump(test_config_data, f, default_flow_style=False)
    return config_file


@pytest.fixture
def test_config(test_config_file: Path) -> ConnectConfig:
    """Load test configuration."""
    from src.config import get_config

    return get_config()


# Database Fixtures
@pytest.fixture
def mock_database_config():
    """Create a mock database configuration."""
    from src.config.models import DatabaseConfig, DatabasePoolConfig
    
    database_config = DatabaseConfig(
        host="localhost",
        port=5432,
        name="test_db",
        user="test_user",
        password="test_password",
        connection_timeout=30,
        command_timeout=60
    )
    
    pool_config = DatabasePoolConfig(
        min_size=5,
        max_size=20,
        timeout=30,
        recycle=3600
    )
    
    config = ConnectConfig(
        database=database_config
    )
    config.database.pool = pool_config
    
    return config


@pytest.fixture
def mock_database_connection():
    """Create a mock database connection."""
    mock_conn = AsyncMock()
    mock_conn.execute.return_value = Mock()
    mock_conn.fetch.return_value = []
    mock_conn.fetchrow.return_value = None
    mock_conn.fetchval.return_value = None
    mock_conn.transaction.return_value.__aenter__ = AsyncMock()
    mock_conn.transaction.return_value.__aexit__ = AsyncMock(return_value=None)
    mock_conn.close.return_value = None
    return mock_conn


@pytest.fixture
def mock_database_pool():
    """Create a mock database connection pool."""
    mock_pool = AsyncMock()
    
    # Create a proper async context manager mock
    mock_connection = AsyncMock()
    mock_connection.fetchval = AsyncMock(return_value=True)
    mock_connection.execute = AsyncMock()
    
    # Create a proper async context manager class
    class MockContextManager:
        def __init__(self, connection):
            self.connection = connection
            
        async def __aenter__(self):
            return self.connection
            
        async def __aexit__(self, exc_type, exc_val, exc_tb):
            return None
    
    # Mock the acquire method to return the context manager
    def mock_acquire():
        return MockContextManager(mock_connection)
    
    mock_pool.acquire = mock_acquire
    mock_pool.close.return_value = None
    mock_pool.wait_closed.return_value = None
    return mock_pool


@pytest.fixture
def session_manager(mock_database_config) -> SessionManager:
    """Create a SessionManager instance for testing."""
    return SessionManager(mock_database_config)


@pytest.fixture
def pool_manager(mock_database_config) -> DatabasePoolManager:
    """Create a DatabasePoolManager instance for testing."""
    return DatabasePoolManager(mock_database_config)


@pytest.fixture
def schema_manager(mock_database_pool) -> SchemaManager:
    """Create a SchemaManager instance for testing."""
    return SchemaManager(mock_database_pool)


@pytest.fixture
def crud_manager(mock_database_pool) -> CRUDOperations:
    """Create a CRUDOperations instance for testing."""
    return CRUDOperations(mock_database_pool)


# Data Fixtures
@pytest.fixture
def sample_dataframe() -> pd.DataFrame:
    """Create a sample DataFrame for testing."""
    return pd.DataFrame(
        {
            "id": [1, 2, 3, 4, 5],
            "name": ["Alice", "Bob", "Charlie", "David", "Eve"],
            "value": [10.5, 20.3, 15.7, 8.9, 12.1],
            "category": ["A", "B", "A", "C", "B"],
            "date": pd.date_range("2024-01-01", periods=5, freq="D"),
            "latitude": [52.5200, 48.8566, 51.5074, 40.7128, 34.0522],
            "longitude": [13.4050, 2.3522, -0.1278, -74.0060, -118.2437],
        }
    )


@pytest.fixture
def large_dataframe() -> pd.DataFrame:
    """Create a larger DataFrame for performance testing."""
    import numpy as np

    size = 10000
    return pd.DataFrame(
        {
            "id": range(size),
            "value": np.random.randn(size),
            "category": np.random.choice(["A", "B", "C"], size),
            "timestamp": pd.date_range("2024-01-01", periods=size, freq="1min"),
            "latitude": np.random.uniform(-90, 90, size),
            "longitude": np.random.uniform(-180, 180, size),
        }
    )


@pytest.fixture
def sample_ep_data() -> pd.DataFrame:
    """Create sample EP (Engineering Parameters) data for testing."""
    return pd.DataFrame(
        {
            "cell_id": ["CELL001", "CELL002", "CELL003"],
            "cell_name": ["Berlin_LTE_001", "Munich_5G_002", "Hamburg_UMTS_003"],
            "technology": ["LTE", "5G", "UMTS"],
            "vendor": ["Ericsson", "Nokia", "Huawei"],
            "latitude": [52.5200, 48.1351, 53.5511],
            "longitude": [13.4050, 11.5820, 9.9937],
            "azimuth": [45, 120, 270],
            "tilt": [3, 5, 2],
            "power": [43, 46, 40],
            "frequency": [1800, 3500, 2100],
        }
    )


@pytest.fixture
def sample_cdr_data() -> pd.DataFrame:
    """Create sample CDR (Call Detail Record) data for testing."""
    return pd.DataFrame(
        {
            "call_id": ["CDR001", "CDR002", "CDR003"],
            "calling_number": ["+491234567890", "+491234567891", "+491234567892"],
            "called_number": ["+491234567893", "+491234567894", "+491234567895"],
            "start_time": pd.to_datetime(
                ["2024-01-01 10:00:00", "2024-01-01 11:00:00", "2024-01-01 12:00:00"]
            ),
            "end_time": pd.to_datetime(
                ["2024-01-01 10:05:00", "2024-01-01 11:03:00", "2024-01-01 12:02:00"]
            ),
            "duration": [300, 180, 120],
            "cell_id": ["CELL001", "CELL002", "CELL003"],
            "service_type": ["voice", "data", "sms"],
        }
    )


# Geospatial Fixtures
@pytest.fixture
def sample_point() -> Point:
    """Create a sample Point geometry for testing."""
    return Point(13.4050, 52.5200)  # Berlin coordinates


@pytest.fixture
def sample_polygon() -> Polygon:
    """Create a sample Polygon geometry for testing."""
    return Polygon(
        [
            (13.3889, 52.5170),
            (13.4500, 52.5170),
            (13.4500, 52.5400),
            (13.3889, 52.5400),
            (13.3889, 52.5170),
        ]
    )


@pytest.fixture
def sample_geometries() -> List[Dict[str, Any]]:
    """Create sample geometries for testing."""
    return [
        {
            "id": 1,
            "name": "Berlin",
            "geometry": Point(13.4050, 52.5200),
            "properties": {"vendor": "telefonica", "technology": "LTE"},
        },
        {
            "id": 2,
            "name": "Munich",
            "geometry": Point(11.5820, 48.1351),
            "properties": {"vendor": "vodafone", "technology": "5G"},
        },
        {
            "id": 3,
            "name": "Hamburg",
            "geometry": Point(9.9937, 53.5511),
            "properties": {"vendor": "telekom", "technology": "UMTS"},
        },
    ]


@pytest.fixture
def geometry_validator() -> GeometryValidator:
    """Create a GeometryValidator instance for testing."""
    return GeometryValidator()


# File Fixtures
@pytest.fixture
def sample_csv_file(tmp_path: Path) -> Path:
    """Create a sample CSV file for testing."""
    csv_file = tmp_path / "sample_data.csv"
    data = {
        "id": [1, 2, 3, 4, 5],
        "name": ["Alice", "Bob", "Charlie", "David", "Eve"],
        "value": [10.5, 20.3, 15.7, 8.9, 12.1],
        "category": ["A", "B", "A", "C", "B"],
        "date": ["2024-01-01", "2024-01-02", "2024-01-03", "2024-01-04", "2024-01-05"],
        "latitude": [52.5200, 48.8566, 51.5074, 40.7128, 34.0522],
        "longitude": [13.4050, 2.3522, -0.1278, -74.0060, -118.2437],
    }
    df = pd.DataFrame(data)
    df.to_csv(csv_file, index=False)
    return csv_file


@pytest.fixture
def sample_geojson_file(tmp_path: Path) -> Path:
    """Create a sample GeoJSON file for testing."""
    geojson_file = tmp_path / "sample_polygons.json"
    geojson_data = {
        "type": "FeatureCollection",
        "features": [
            {
                "type": "Feature",
                "properties": {
                    "id": 1,
                    "name": "Berlin",
                    "vendor": "telefonica",
                    "technology": "LTE",
                },
                "geometry": {
                    "type": "Polygon",
                    "coordinates": [
                        [
                            [13.3889, 52.5170],
                            [13.4500, 52.5170],
                            [13.4500, 52.5400],
                            [13.3889, 52.5400],
                            [13.3889, 52.5170],
                        ]
                    ],
                },
            }
        ],
    }
    with open(geojson_file, "w", encoding="utf-8") as f:
        json.dump(geojson_data, f)
    return geojson_file


# Utility Fixtures
@pytest.fixture
def database_logger() -> DatabaseLogger:
    """Create a DatabaseLogger instance for testing."""
    return DatabaseLogger("test_logger")


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# Performance Testing Fixtures
@pytest.fixture
def performance_config() -> Dict[str, Any]:
    """Configuration for performance testing."""
    return {
        "max_execution_time_ms": 1000,
        "max_memory_usage_mb": 512,
        "min_throughput_ops_per_sec": 100,
        "connection_pool_size": 10,
        "concurrent_users": 20,
    }


# Error Simulation Fixtures
@pytest.fixture
def database_error():
    """Create a DatabaseError for testing error handling."""
    return DatabaseError("Test database error", error_code="TEST_ERROR")


@pytest.fixture
def connection_error():
    """Create a ConnectionError for testing connection error handling."""
    from src.database.exceptions import ConnectionError

    return ConnectionError("Test connection error")


# Cleanup Fixtures
@pytest.fixture(autouse=True)
def cleanup_test_environment():
    """Automatically clean up test environment after each test."""
    yield
    
    # Suppress asyncio task destruction warnings
    import warnings
    import asyncio
    
    # Filter out the specific RuntimeWarning about unawaited coroutines
    warnings.filterwarnings(
        "ignore", 
        message=".*Task.*was destroyed but it is pending.*",
        category=RuntimeWarning
    )
    
    # Also filter out coroutine warnings
    warnings.filterwarnings(
        "ignore",
        message=".*coroutine.*was never awaited.*",
        category=RuntimeWarning
    )


# Markers for test categorization
pytestmark = [
    pytest.mark.asyncio,  # Enable asyncio support for all tests
]


# Test utilities
def assert_dataframe_equal(df1: pd.DataFrame, df2: pd.DataFrame, **kwargs):
    """Assert that two DataFrames are equal with better error messages."""
    try:
        pd.testing.assert_frame_equal(df1, df2, **kwargs)
    except AssertionError as e:
        print(f"DataFrame comparison failed:\n{e}")
        print(f"\nFirst DataFrame:\n{df1}")
        print(f"\nSecond DataFrame:\n{df2}")
        raise


def assert_geometry_equal(geom1, geom2, tolerance=1e-6):
    """Assert that two geometries are equal within tolerance."""
    from shapely.geometry import Point, Polygon

    if type(geom1) != type(geom2):
        raise AssertionError(f"Geometry types differ: {type(geom1)} vs {type(geom2)}")

    if isinstance(geom1, Point):
        assert abs(geom1.x - geom2.x) < tolerance, f"X coordinates differ: {geom1.x} vs {geom2.x}"
        assert abs(geom1.y - geom2.y) < tolerance, f"Y coordinates differ: {geom1.y} vs {geom2.y}"
    elif isinstance(geom1, Polygon):
        coords1 = list(geom1.exterior.coords)
        coords2 = list(geom2.exterior.coords)
        assert len(coords1) == len(coords2), f"Coordinate count differs: {len(coords1)} vs {len(coords2)}"
        for (x1, y1), (x2, y2) in zip(coords1, coords2):
            assert abs(x1 - x2) < tolerance, f"X coordinates differ: {x1} vs {x2}"
            assert abs(y1 - y2) < tolerance, f"Y coordinates differ: {y1} vs {y2}"


# E2E Testing Fixtures
@pytest.fixture(scope="session")
async def test_database_pool():
    """Provide a test database pool for E2E testing."""
    try:
        await initialize_global_pool()
        pool_manager = get_pool_manager()
        yield pool_manager
    except Exception as e:
        pytest.skip(f"Database not available for E2E testing: {e}")


@pytest.fixture
def file_paths() -> Dict[str, str]:
    """Provide sample file paths for testing."""
    return {
        'ep': 'data/input/ep/2024/CW01/TEF_Sites_CW01.xlsx',
        'cdr': 'data/input/cdr/2024/Q1/Voice/Shared_Benchmark_Q1_DE_2024_Voice_M2M_Calls_2024-03-12_12-52-13.xlsx',
        'nlg': 'data/input/nlg/2024/NLG_CUBE_aktuell_2024-01-05.xlsb',
        'kpi': 'data/input/kpi/sample_kpi.xlsx',
        'site': 'data/input/ep/2024/CW01/TEF_Sites_CW01.xlsx'
    }


@pytest.fixture
def analysis_configs() -> List[Dict[str, Any]]:
    """Provide sample analysis configurations for testing."""
    return [
        {
            'type': 'coverage',
            'region': {'name': 'test_region', 'bounds': [0, 0, 10, 10]},
            'options': {'threshold': 0.8}
        },
        {
            'type': 'performance',
            'time_range': {'start': '2024-01-01', 'end': '2024-01-31'},
            'options': {'metrics': ['throughput', 'latency']}
        },
        {
            'type': 'competitor',
            'competitor_data': {'operator': 'test_operator'},
            'options': {'comparison_type': 'coverage'}
        }
    ]


@pytest.fixture
def report_configs() -> List[Dict[str, Any]]:
    """Provide sample report configurations for testing."""
    return [
        {
            'type': 'summary',
            'data': {'period': '2024-Q1'},
            'options': {'format': 'pdf', 'include_charts': True}
        },
        {
            'type': 'detailed',
            'data': {'analysis_id': 'test_analysis_001'},
            'options': {'format': 'excel', 'include_raw_data': True}
        }
    ]
