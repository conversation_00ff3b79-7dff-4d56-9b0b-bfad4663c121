# Connect 项目开发规则 (Connect Project Development Rules)

## 1. Python 代码标准 (Python Code Standards)

### 1.1 代码风格 (Code Style)
- 严格遵循 PEP 8 编码规范
- 使用 4 个空格缩进，禁用制表符
- 行长度限制 88 字符（Black 默认）
- 函数和类之间使用 2 个空行分隔
- 方法之间使用 1 个空行分隔

### 1.2 类型提示 (Type Hints)
- 所有函数参数和返回值必须有类型提示
- 使用 `from __future__ import annotations` 支持前向引用
- 复杂类型使用 `typing` 模块
- 使用 `Optional[T]` 而非 `Union[T, None]`

```python
from __future__ import annotations
from typing import Optional, List, Dict, Any

def process_data(
    data: pd.DataFrame,
    columns: Optional[List[str]] = None,
    config: Dict[str, Any] | None = None
) -> pd.DataFrame:
    """处理数据框。"""
    pass
```

### 1.3 文档字符串 (Docstrings)
- 使用 Google 风格文档字符串
- 所有公共函数、类、模块必须有文档
- 包含参数、返回值、异常说明
- 中英文混合，技术术语使用英文

```python
def validate_schema(schema: Dict[str, Any]) -> bool:
    """
    验证数据库 schema 的有效性。

    Args:
        schema: 包含表结构定义的字典

    Returns:
        bool: schema 是否有效

    Raises:
        ValidationError: 当 schema 格式不正确时
        DatabaseError: 当数据库连接失败时
    """
    pass
```

### 1.4 导入顺序 (Import Order)
```python
# 1. 标准库
import os
import sys
from datetime import datetime
from pathlib import Path

# 2. 第三方库
import pandas as pd
import numpy as np
from sqlalchemy import create_engine
from pydantic import BaseModel

# 3. 本地模块
from src.database import connection
from src.utils import helpers
from .exceptions import DatabaseError
```

## 2. 数据库操作标准 (Database Operation Standards)

### 2.1 异步操作 (Async Operations)
- 优先使用异步数据库操作
- 使用 `asyncpg` 或 `aiopg` 进行 PostgreSQL 连接
- 正确处理异步上下文管理器

```python
async def fetch_data(query: str) -> List[Dict[str, Any]]:
    async with get_db_connection() as conn:
        async with conn.transaction():
            result = await conn.fetch(query)
            return [dict(row) for row in result]
```

### 2.2 连接池管理 (Connection Pool Management)
- 使用连接池避免频繁创建连接
- 正确配置池大小和超时
- 实现连接健康检查

### 2.3 事务处理 (Transaction Handling)
- 使用事务确保数据一致性
- 实现适当的错误回滚
- 避免长时间持有事务

### 2.4 错误处理 (Error Handling)
```python
try:
    async with db_pool.acquire() as conn:
        result = await conn.execute(query)
except asyncpg.PostgresError as e:
    logger.error(f"Database error: {e}")
    raise DatabaseError(f"Query failed: {e}") from e
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise
```

## 3. 地理空间数据处理 (Geospatial Data Processing)

### 3.1 坐标系统 (Coordinate Reference Systems)
- 明确指定 CRS，避免假设
- 使用 EPSG 代码标识坐标系
- 在数据处理前验证 CRS 一致性

```python
import geopandas as gpd
from pyproj import CRS

def ensure_crs(gdf: gpd.GeoDataFrame, target_crs: str = "EPSG:4326") -> gpd.GeoDataFrame:
    """确保 GeoDataFrame 使用指定的坐标系。"""
    if gdf.crs is None:
        raise ValueError("GeoDataFrame 缺少 CRS 信息")

    if gdf.crs != target_crs:
        return gdf.to_crs(target_crs)
    return gdf
```

### 3.2 几何验证 (Geometry Validation)
- 使用 `is_valid` 检查几何有效性
- 使用 `make_valid()` 修复无效几何
- 处理空几何和无效坐标

### 3.3 性能优化 (Performance Optimization)
- 使用空间索引加速查询
- 适当简化复杂几何
- 使用 `sjoin` 进行空间连接

## 4. 测试标准 (Testing Standards)

### 4.1 测试结构 (Test Structure)
```python
import pytest
import asyncio
from unittest.mock import AsyncMock, patch

class TestDatabaseOperations:
    """数据库操作测试类。"""

    @pytest.fixture
    async def db_connection(self):
        """测试数据库连接 fixture。"""
        # 设置测试数据库连接
        pass

    @pytest.mark.asyncio
    async def test_fetch_data(self, db_connection):
        """测试数据获取功能。"""
        # 测试实现
        pass
```

### 4.2 异步测试 (Async Testing)
- 使用 `@pytest.mark.asyncio` 标记异步测试
- 使用 `AsyncMock` 模拟异步依赖
- 正确处理异步 fixtures

### 4.3 模拟和存根 (Mocking and Stubbing)
- 模拟外部依赖（数据库、API）
- 使用 `patch` 替换系统调用
- 验证模拟对象的调用

### 4.4 测试覆盖率 (Test Coverage)
- 目标覆盖率 > 80%
- 重点测试关键业务逻辑
- 包含边界条件和错误情况

## 5. 配置管理 (Configuration Management)

### 5.1 Pydantic 模型 (Pydantic Models)
```python
from pydantic import BaseSettings, Field
from typing import Optional

class DatabaseConfig(BaseSettings):
    """数据库配置模型。"""

    host: str = Field(..., description="数据库主机")
    port: int = Field(5432, description="数据库端口")
    database: str = Field(..., description="数据库名称")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    pool_size: int = Field(10, description="连接池大小")

    class Config:
        env_prefix = "DB_"
        env_file = ".env"
```

### 5.2 环境变量 (Environment Variables)
- 使用 `.env` 文件管理开发环境配置
- 生产环境使用系统环境变量
- 敏感信息不提交到版本控制

### 5.3 YAML 配置 (YAML Configuration)
- 使用 YAML 文件存储复杂配置
- 支持多环境配置
- 实现配置验证

## 6. 日志记录 (Logging)

### 6.1 Structlog 使用 (Structlog Usage)
```python
import structlog

logger = structlog.get_logger(__name__)

async def process_data(data_id: str) -> None:
    """处理数据的示例函数。"""
    log = logger.bind(data_id=data_id)

    try:
        log.info("开始处理数据")
        # 处理逻辑
        log.info("数据处理完成", records_processed=100)
    except Exception as e:
        log.error("数据处理失败", error=str(e))
        raise
```

### 6.2 日志级别 (Log Levels)
- DEBUG: 详细调试信息
- INFO: 一般信息和业务流程
- WARNING: 警告信息
- ERROR: 错误信息
- CRITICAL: 严重错误

### 6.3 结构化日志 (Structured Logging)
- 使用键值对记录上下文信息
- 包含请求 ID、用户 ID 等追踪信息
- 便于日志分析和监控

## 7. 反模式避免 (Anti-patterns to Avoid)

### 7.1 数据库反模式
- ❌ 在循环中执行 SQL 查询
- ❌ 不使用参数化查询
- ❌ 忽略数据库连接泄漏
- ❌ 不处理数据库异常

### 7.2 Pandas 反模式
- ❌ 使用 `iterrows()` 遍历 DataFrame
- ❌ 链式赋值警告 (`SettingWithCopyWarning`)
- ❌ 不指定 `dtype` 读取大文件
- ❌ 在循环中拼接 DataFrame

### 7.3 异步反模式
- ❌ 在异步函数中使用同步 I/O
- ❌ 不正确的异常处理
- ❌ 忘记 `await` 异步调用
- ❌ 阻塞事件循环

### 7.4 通用反模式
- ❌ 硬编码配置值
- ❌ 过度使用全局变量
- ❌ 忽略错误处理
- ❌ 不一致的命名约定

## 8. 文件组织 (File Organization)

### 8.1 模块结构
```
src/
├── database/
│   ├── __init__.py
│   ├── connection/
│   ├── operations/
│   ├── schema/
│   └── utils/
├── geo/
│   ├── __init__.py
│   ├── geometry.py
│   ├── raster.py
│   └── vector.py
└── config/
    ├── __init__.py
    ├── settings.py
    └── environment.py
```

### 8.2 命名约定
- 文件名：`snake_case.py`
- 类名：`PascalCase`
- 函数名：`snake_case`
- 常量：`UPPER_SNAKE_CASE`
- 私有成员：`_leading_underscore`

## 9. 代码审查清单 (Code Review Checklist)

### 9.1 功能性检查
- [ ] 代码实现了预期功能
- [ ] 处理了边界条件
- [ ] 错误处理完整
- [ ] 性能考虑合理

### 9.2 代码质量检查
- [ ] 遵循项目编码标准
- [ ] 函数和类职责单一
- [ ] 代码可读性良好
- [ ] 适当的注释和文档

### 9.3 测试检查
- [ ] 包含单元测试
- [ ] 测试覆盖关键路径
- [ ] 测试数据合理
- [ ] 模拟外部依赖

### 9.4 安全检查
- [ ] 无硬编码敏感信息
- [ ] 输入验证充分
- [ ] SQL 注入防护
- [ ] 权限控制适当

## 10. 性能指导原则 (Performance Guidelines)

### 10.1 数据库性能
- 使用批量操作代替单条插入
- 适当使用索引
- 避免 N+1 查询问题
- 使用连接池

### 10.2 内存管理
- 及时释放大对象
- 使用生成器处理大数据集
- 监控内存使用
- 避免内存泄漏

### 10.3 并发处理
- 使用异步 I/O 提高并发
- 适当的线程池大小
- 避免竞态条件
- 正确使用锁机制

---

**记住**: 这些规则旨在提高代码质量、可维护性和团队协作效率。在特殊情况下可以例外，但需要在代码中说明原因。
