"""Tests for CRUD operations module.

This module contains comprehensive tests for the CRUDOperations class,
including all CRUD methods, error handling, and edge cases.
"""

import asyncio
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from sqlalchemy import <PERSON>umn, Integer, MetaData, String, Table

from src.database.exceptions import (
    DatabaseError,
    QueryError,
    TableNotFoundError,
    ValidationError,
)
from src.database.operations.crud import CRUDOperations


def create_mock_table(table_name="users", schema="public", columns=None):
    """Helper function to create mock SQLAlchemy Table objects for testing."""
    metadata = MetaData()
    if columns is None:
        columns = [
            Column("id", Integer, nullable=False),
            Column("name", String, nullable=False),
            Column("email", String, nullable=True),
        ]
    return Table(table_name, metadata, *columns, schema=schema)


class TestCRUDOperations:
    """Test cases for CRUDOperations class."""

    @pytest.fixture
    def mock_session_manager(self):
        """Create a mock session manager."""
        from contextlib import asynccontextmanager
        from unittest.mock import AsyncMock

        session_manager = AsyncMock()
        mock_connection = AsyncMock()

        # Setup session manager as async context manager
        session_manager.__aenter__.return_value = mock_connection
        session_manager.__aexit__ = AsyncMock(return_value=None)
        session_manager._mock_connection = mock_connection

        return session_manager

    @pytest.fixture
    def crud_operations(self, mock_session_manager):
        """Create a CRUD operations instance for testing."""
        from unittest.mock import Mock
        mock_performance_monitor = Mock()
        mock_performance_monitor.measure_query_time.return_value.__enter__ = Mock()
        mock_performance_monitor.measure_query_time.return_value.__exit__ = Mock(return_value=None)
        return CRUDOperations(mock_session_manager, mock_performance_monitor)

    @pytest.fixture
    def mock_connection(self):
        """Create a mock database connection."""
        connection = AsyncMock()
        connection.fetch = AsyncMock()
        connection.fetchrow = AsyncMock()
        connection.execute = AsyncMock()
        connection.executemany = AsyncMock()
        return connection

    @pytest.fixture
    def sample_table_metadata(self):
        """Sample table metadata for testing."""
        return {
            "table_name": "users",
            "schema_name": "public",
            "columns": {
                "id": {"data_type": "integer", "is_nullable": False},
                "name": {"data_type": "character varying", "is_nullable": False},
                "email": {"data_type": "character varying", "is_nullable": True},
                "created_at": {
                    "data_type": "timestamp with time zone",
                    "is_nullable": False,
                },
            },
        }

    @pytest.mark.asyncio
    async def test_get_table_metadata_success(
        self, crud_operations, mock_session_manager, sample_table_metadata
    ):
        """Test successful table metadata retrieval."""
        # Get the mock connection from session manager
        mock_connection = mock_session_manager._mock_connection
        mock_connection.fetch.return_value = [
            {"column_name": "id", "data_type": "integer", "is_nullable": "NO"},
            {
                "column_name": "name",
                "data_type": "character varying",
                "is_nullable": "NO",
            },
            {
                "column_name": "email",
                "data_type": "character varying",
                "is_nullable": "YES",
            },
            {
                "column_name": "created_at",
                "data_type": "timestamp with time zone",
                "is_nullable": "NO",
            },
        ]

        mock_connection.fetchval.return_value = True  # Table exists

        with patch(
            "src.database.utils.security.SQLInjectionGuard.validate_identifier",
            return_value=True,
        ):
            table = await crud_operations._get_table_metadata("users", "public")

        assert table.name == "users"
        assert table.schema == "public"
        assert len(table.columns) == 4
        assert "id" in [col.name for col in table.columns]
        assert "name" in [col.name for col in table.columns]
        assert "email" in [col.name for col in table.columns]
        assert "created_at" in [col.name for col in table.columns]

    @pytest.mark.asyncio
    async def test_get_table_metadata_table_not_found(
        self, crud_operations, mock_connection
    ):
        """Test table metadata retrieval when table doesn't exist."""
        # Use the mock connection from session manager
        mock_connection = crud_operations.session_manager._mock_connection
        mock_connection.fetch.return_value = []  # No columns found

        with patch(
            "src.database.utils.security.SQLInjectionGuard.validate_identifier",
            return_value=True,
        ):
            with pytest.raises(DatabaseError, match="No columns found for table"):
                await crud_operations._get_table_metadata("nonexistent", "public")

    @pytest.mark.asyncio
    async def test_get_table_metadata_invalid_identifier(self, crud_operations):
        """Test table metadata retrieval with invalid identifiers."""
        with patch(
            "src.database.utils.security.SQLInjectionGuard.validate_identifier",
            return_value=False,
        ):
            with pytest.raises(ValidationError, match="Invalid table name"):
                await crud_operations._get_table_metadata(
                    "invalid; DROP TABLE users;", "public"
                )

    @pytest.mark.asyncio
    async def test_create_success(self, crud_operations, mock_connection):
        """Test successful record creation."""
        # Use the mock connection from session manager
        mock_connection = crud_operations.session_manager._mock_connection
        mock_connection.fetchrow.return_value = {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
        }

        # Mock table metadata
        with patch.object(crud_operations, "_get_table_metadata") as mock_metadata:
            mock_metadata.return_value = create_mock_table()

            with patch(
                "src.database.utils.security.SQLInjectionGuard.validate_identifier",
                return_value=True,
            ):
                result = await crud_operations.create(
                    table="users",
                    data={"name": "John Doe", "email": "<EMAIL>"},
                    schema_name="public",
                )

        assert result["id"] == 1
        assert result["name"] == "John Doe"
        assert result["email"] == "<EMAIL>"
        mock_connection.fetchrow.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_invalid_column(self, crud_operations):
        """Test record creation with invalid column names."""
        with patch.object(crud_operations, "_get_table_metadata") as mock_metadata:
            mock_metadata.return_value = create_mock_table(
                columns=[
                    Column("id", Integer, nullable=False),
                    Column("name", String, nullable=False),
                ]
            )

            with patch(
                "src.database.utils.security.SQLInjectionGuard.validate_identifier",
                side_effect=lambda x, t=None: x != "invalid; column",
            ):
                with pytest.raises(ValidationError, match="Invalid column name"):
                    await crud_operations.create(
                        table="users",
                        data={"name": "John Doe", "invalid; column": "value"},
                        schema_name="public",
                    )

    @pytest.mark.asyncio
    async def test_read_success(self, crud_operations, mock_connection):
        """Test successful record reading."""
        # Use the mock connection from session manager
        mock_connection = crud_operations.session_manager._mock_connection
        mock_connection.fetch.return_value = [
            {"id": 1, "name": "John Doe", "email": "<EMAIL>"},
            {"id": 2, "name": "Jane Smith", "email": "<EMAIL>"},
        ]

        with patch.object(crud_operations, "_get_table_metadata") as mock_metadata:
            mock_metadata.return_value = create_mock_table()

            with patch(
                "src.database.utils.security.SQLInjectionGuard.validate_identifier",
                return_value=True,
            ):
                results = await crud_operations.read(
                    table_name="users", filters={"name": "John Doe"}, schema="public"
                )

        assert len(results) == 2
        assert results[0]["id"] == 1
        assert results[0]["name"] == "John Doe"
        mock_connection.fetch.assert_called_once()

    @pytest.mark.asyncio
    async def test_read_with_limit_offset(self, crud_operations, mock_connection):
        """Test record reading with limit and offset."""
        # Use the mock connection from session manager
        mock_connection = crud_operations.session_manager._mock_connection
        mock_connection.fetch.return_value = [{"id": 2, "name": "Jane Smith"}]

        with patch.object(crud_operations, "_get_table_metadata") as mock_metadata:
            mock_metadata.return_value = create_mock_table()

            with patch(
                "src.database.utils.security.SQLInjectionGuard.validate_identifier",
                return_value=True,
            ):
                with patch(
                    "src.database.utils.security.SQLInjectionGuard.validate_limit_offset",
                    return_value=(10, 5),
                ):
                    results = await crud_operations.read(
                        table_name="users", limit=10, offset=5, schema="public"
                    )

        assert len(results) == 1
        mock_connection.fetch.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_success(self, crud_operations, mock_connection):
        """Test successful record update."""
        # Use the mock connection from session manager
        mock_connection = crud_operations.session_manager._mock_connection
        mock_connection.fetch.return_value = [
            {"id": 1, "name": "John Updated", "email": "<EMAIL>"}
        ]

        with patch.object(crud_operations, "_get_table_metadata") as mock_metadata:
            mock_metadata.return_value = create_mock_table()

            with patch(
                "src.database.utils.security.SQLInjectionGuard.validate_identifier",
                return_value=True,
            ):
                results = await crud_operations.update(
                    table_name="users",
                    data={"name": "John Updated"},
                    filters={"id": 1},
                    schema="public",
                )

        assert len(results) == 1
        assert results[0]["name"] == "John Updated"
        mock_connection.fetch.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_success(self, crud_operations, mock_connection):
        """Test successful record deletion."""
        # Use the mock connection from session manager
        mock_connection = crud_operations.session_manager._mock_connection
        mock_connection.fetch.return_value = [{"id": 1, "name": "John Doe"}]

        with patch.object(crud_operations, "_get_table_metadata") as mock_metadata:
            mock_metadata.return_value = create_mock_table(
                columns=[
                    Column("id", Integer, nullable=False),
                    Column("name", String, nullable=False),
                ]
            )

            with patch(
                "src.database.utils.security.SQLInjectionGuard.validate_identifier",
                return_value=True,
            ):
                results = await crud_operations.delete(
                    table_name="users", filters={"id": 1}, schema="public"
                )

        assert len(results) == 1
        assert results[0]["id"] == 1
        mock_connection.fetch.assert_called_once()

    @pytest.mark.asyncio
    async def test_bulk_insert_success(self, crud_operations, mock_connection):
        """Test successful bulk insert."""
        # Use the mock connection from session manager
        mock_connection = crud_operations.session_manager._mock_connection
        # bulk_insert uses execute, not fetch
        mock_connection.execute.return_value = None

        with patch.object(crud_operations, "_get_table_metadata") as mock_metadata:
            mock_metadata.return_value = create_mock_table(
                columns=[
                    Column("id", Integer, nullable=False),
                    Column("name", String, nullable=False),
                ]
            )

            with patch(
                "src.database.utils.security.SQLInjectionGuard.validate_identifier",
                return_value=True,
            ):
                result = await crud_operations.bulk_insert(
                    table_name="users",
                    data=[{"name": "John Doe"}, {"name": "Jane Smith"}],
                    schema="public",
                )

        assert result.total_processed == 2
        assert result.successful == 2
        assert result.failed == 0
        # bulk_insert calls execute for each record
        assert mock_connection.execute.call_count == 2

    @pytest.mark.asyncio
    async def test_count_success(self, crud_operations, mock_connection):
        """Test successful record count."""
        # Use the mock connection from session manager
        mock_connection = crud_operations.session_manager._mock_connection
        mock_connection.fetchrow.return_value = {'count': 42}

        with patch.object(crud_operations, "_get_table_metadata") as mock_metadata:
            mock_metadata.return_value = create_mock_table(
                columns=[
                    Column("id", Integer, nullable=False),
                    Column("name", String, nullable=False),
                ]
            )

            with patch(
                "src.database.utils.security.SQLInjectionGuard.validate_identifier",
                return_value=True,
            ):
                count = await crud_operations.count(
                    table_name="users", filters={"name": "John"}, schema="public"
                )

        assert count == 42
        mock_connection.fetchrow.assert_called_once()

    @pytest.mark.asyncio
    async def test_database_error_handling(self, crud_operations, mock_connection):
        """Test database error handling."""
        # Use the mock connection from session manager
        mock_connection = crud_operations.session_manager._mock_connection
        mock_connection.fetch.side_effect = Exception("Database connection failed")

        with patch.object(crud_operations, "_get_table_metadata") as mock_metadata:
            mock_metadata.return_value = create_mock_table()

            with patch(
                "src.database.utils.security.SQLInjectionGuard.validate_identifier",
                return_value=True,
            ):
                with pytest.raises(
                    DatabaseError, match="Unexpected error"
                ):
                    await crud_operations.read(table_name="users", schema="public")

    @pytest.mark.asyncio
    async def test_empty_data_validation(self, crud_operations):
        """Test validation of empty data."""
        with pytest.raises(ValidationError, match="Data cannot be empty"):
            await crud_operations.create(table="users", data={}, schema_name="public")

        with pytest.raises(ValidationError, match="Data list cannot be empty"):
            await crud_operations.bulk_insert(
                table_name="users", data=[], schema="public"
            )

    @pytest.mark.asyncio
    async def test_invalid_limit_offset(self, crud_operations):
        """Test validation of invalid limit and offset values."""
        with patch.object(crud_operations, "_get_table_metadata") as mock_metadata:
            mock_metadata.return_value = create_mock_table()

            with patch(
                "src.database.utils.security.SQLInjectionGuard.validate_identifier",
                return_value=True,
            ):
                with patch(
                    "src.database.utils.security.SQLInjectionGuard.validate_limit_offset",
                    side_effect=ValidationError("LIMIT must be a non-negative integer"),
                ):
                    with pytest.raises(
                        ValidationError, match="LIMIT must be a non-negative integer"
                    ):
                        await crud_operations.read(
                            table_name="users", limit=-1, schema="public"
                        )
