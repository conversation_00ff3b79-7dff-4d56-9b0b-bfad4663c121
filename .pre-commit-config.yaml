# Pre-commit hooks configuration for Connect project
# See https://pre-commit.com for more information

repos:
  # Code formatting
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3.12
        args: [--line-length=88]

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]

  # Code linting
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]
        additional_dependencies:
          - flake8-docstrings
          - flake8-import-order
          - flake8-bugbear

  # Type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies:
          - types-PyYAML
          - types-requests
          - types-python-dateutil
        args: [--ignore-missing-imports]

  # Security scanning
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, src/]
        exclude: tests/

  # General hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      # File formatting
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: mixed-line-ending
        args: [--fix=lf]

      # File validation
      - id: check-yaml
      - id: check-toml
      - id: check-json
      - id: check-xml

      # Python specific
      - id: check-ast
      - id: check-docstring-first
      - id: debug-statements
      - id: name-tests-test
        args: [--pytest-test-first]

      # Security
      - id: detect-private-key
      - id: check-merge-conflict

      # Large files
      - id: check-added-large-files
        args: [--maxkb=1000]

  # Documentation
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: [--convention=google]
        exclude: tests/

  # Jupyter notebooks (if any)
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.1
    hooks:
      - id: nbqa-black
      - id: nbqa-isort
      - id: nbqa-flake8

# Configuration for specific hooks
default_language_version:
  python: python3.12

# Global excludes
exclude: |
  (?x)^(
      \.venv/.*|
      \.pytest_cache/.*|
      __pycache__/.*|
      \.git/.*|
      \.tox/.*|
      build/.*|
      dist/.*|
      .*\.egg-info/.*|
      data/.*|
      \.taskmaster/.*|
      \.cursor/.*|
      \.trae/.*|
      \.roo/.*
  )$

# Fail fast - stop on first failure
fail_fast: false

# Minimum pre-commit version
minimum_pre_commit_version: 3.0.0
