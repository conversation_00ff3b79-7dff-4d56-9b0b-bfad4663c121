"""Geospatial Data Processing Module

Provides vector data, raster data processing and QGIS integration functionality.
"""

# Version information
__version__ = "0.1.0"
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

# Import main classes
try:
    from .geometry import GeometryProcessor
    from .qgis_integration import QGISIntegration
    from .raster import RasterProcessor
    from .vector import VectorProcessor
except ImportError:
    # Ignore import errors if modules are not yet created
    pass

__all__ = ["VectorProcessor", "RasterProcessor", "GeometryProcessor", "QGISIntegration"]
