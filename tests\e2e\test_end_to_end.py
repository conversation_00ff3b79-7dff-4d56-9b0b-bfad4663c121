"""End-to-end tests for the database framework."""

import json
import os
import tempfile
import time
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import numpy as np
import pandas as pd
import pytest

from src.config import get_config

# Import all components for end-to-end testing
from src.database.connection import (
    DatabasePoolManager,
    ReadWriteSplitter,
    SessionManager,
)
from src.database.monitoring import DatabaseLogger, MetricsCollector, PerformanceLogger
from src.database.operations import BulkOperations, CRUDOperations, TransactionManager
from src.database.schema import ColumnSchema, SchemaManager, TableSchema
from src.exporters.csv_exporter import CSVExporter
from src.exporters.excel_exporter import ExcelExporter
from src.exporters.json_exporter import JSONExporter
# from src.geospatial.analyzer import SpatialAnalyzer
# from src.geospatial.processor import GeospatialProcessor
from src.importers.cdr_importer import CDRImporter
from src.importers.ep_importer import EPImporter
from src.importers.nlg_importer import NLGImporter
from src.utils.data_validator import DataValidator
from src.utils.file_handler import FileHandler


@pytest.mark.e2e
class TestCompleteDataPipeline:
    """End-to-end tests for complete data processing pipeline."""

    @pytest.fixture
    def temp_workspace(self):
        """Create temporary workspace for E2E tests."""
        with tempfile.TemporaryDirectory() as temp_dir:
            workspace = Path(temp_dir)

            # Create directory structure
            (workspace / "input").mkdir()
            (workspace / "output").mkdir()
            (workspace / "logs").mkdir()
            (workspace / "config").mkdir()

            yield workspace

    @pytest.fixture
    def sample_ep_data(self, temp_workspace):
        """Create sample EP data file."""
        ep_data = pd.DataFrame(
            {
                "timestamp": pd.date_range("2023-01-01", periods=1000, freq="15min"),
                "meter_id": [f"EP_METER_{i:04d}" for i in range(1000)],
                "energy_kwh": np.random.uniform(10, 100, 1000),
                "power_kw": np.random.uniform(5, 50, 1000),
                "voltage": np.random.uniform(220, 240, 1000),
                "current": np.random.uniform(10, 30, 1000),
                "power_factor": np.random.uniform(0.8, 1.0, 1000),
                "location_lat": np.random.uniform(39.0, 41.0, 1000),
                "location_lon": np.random.uniform(-74.0, -72.0, 1000),
            }
        )

        file_path = temp_workspace / "input" / "ep_data.csv"
        ep_data.to_csv(file_path, index=False)
        return file_path

    @pytest.fixture
    def sample_cdr_data(self, temp_workspace):
        """Create sample CDR data file."""
        cdr_data = pd.DataFrame(
            {
                "call_id": [f"CDR_{i:08d}" for i in range(500)],
                "caller_number": [f"+1555{i:07d}" for i in range(500)],
                "callee_number": [f"+1666{i:07d}" for i in range(500)],
                "start_time": pd.date_range("2023-01-01", periods=500, freq="2min"),
                "end_time": pd.date_range(
                    "2023-01-01 00:01:00", periods=500, freq="2min"
                ),
                "duration_seconds": np.random.randint(10, 3600, 500),
                "call_type": np.random.choice(["voice", "video", "conference"], 500),
                "status": np.random.choice(
                    ["completed", "failed", "busy"], 500, p=[0.8, 0.1, 0.1]
                ),
                "bytes_transferred": np.random.randint(1000, 1000000, 500),
                "cell_tower_lat": np.random.uniform(39.0, 41.0, 500),
                "cell_tower_lon": np.random.uniform(-74.0, -72.0, 500),
            }
        )

        file_path = temp_workspace / "input" / "cdr_data.csv"
        cdr_data.to_csv(file_path, index=False)
        return file_path

    @pytest.fixture
    def sample_nlg_data(self, temp_workspace):
        """Create sample NLG data file."""
        nlg_data = pd.DataFrame(
            {
                "log_id": [f"NLG_{i:06d}" for i in range(300)],
                "timestamp": pd.date_range("2023-01-01", periods=300, freq="5min"),
                "source_ip": [
                    f"192.168.{np.random.randint(1, 255)}.{np.random.randint(1, 255)}"
                    for _ in range(300)
                ],
                "destination_ip": [
                    f"10.0.{np.random.randint(1, 255)}.{np.random.randint(1, 255)}"
                    for _ in range(300)
                ],
                "protocol": np.random.choice(["TCP", "UDP", "ICMP"], 300),
                "port": np.random.randint(1, 65535, 300),
                "bytes_sent": np.random.randint(100, 100000, 300),
                "bytes_received": np.random.randint(100, 100000, 300),
                "session_duration": np.random.randint(1, 7200, 300),
                "geo_lat": np.random.uniform(39.0, 41.0, 300),
                "geo_lon": np.random.uniform(-74.0, -72.0, 300),
            }
        )

        file_path = temp_workspace / "input" / "nlg_data.csv"
        nlg_data.to_csv(file_path, index=False)
        return file_path

    def test_complete_ep_pipeline(self, temp_workspace, sample_ep_data, test_config):
        """Test complete EP data processing pipeline."""
        # Mock database components
        with patch("src.database.connection.SessionManager") as mock_session:
            with patch("src.database.operations.BulkOperations") as mock_bulk:
                with patch("src.database.schema.SchemaManager") as mock_schema:
                    # Setup mocks
                    mock_session.return_value.get_connection.return_value.__enter__ = (
                        Mock()
                    )
                    mock_session.return_value.get_connection.return_value.__exit__ = (
                        Mock()
                    )
                    mock_bulk.return_value.bulk_insert_dataframe.return_value = True
                    mock_schema.return_value.table_exists.return_value = True

                    # Step 1: Import EP data
                    ep_importer = EPImporter(test_config)
                    import_result = ep_importer.import_file(str(sample_ep_data))

                    assert import_result.success
                    assert import_result.records_processed == 1000

                    # Step 2: Process geospatial data
                    geo_processor = GeospatialProcessor(test_config)

                    # Read the imported data for geospatial processing
                    ep_data = pd.read_csv(sample_ep_data)

                    # Create points from coordinates
                    points = []
                    for _, row in ep_data.iterrows():
                        point = geo_processor.create_point(
                            row["location_lon"], row["location_lat"]
                        )
                        points.append(point)

                    assert len(points) == 1000

                    # Step 3: Perform spatial analysis
                    spatial_analyzer = SpatialAnalyzer(test_config)

                    # Find clusters of meters
                    clusters = spatial_analyzer.cluster_points(
                        points, eps=0.01, min_samples=5
                    )
                    assert len(clusters) > 0

                    # Step 4: Export processed data
                    output_file = temp_workspace / "output" / "processed_ep_data.csv"

                    csv_exporter = CSVExporter(test_config)

                    # Add cluster information to data
                    ep_data["cluster_id"] = clusters

                    export_result = csv_exporter.export_dataframe(
                        ep_data, str(output_file)
                    )
                    assert export_result.success

                    # Verify output file
                    assert output_file.exists()
                    exported_data = pd.read_csv(output_file)
                    assert len(exported_data) == 1000
                    assert "cluster_id" in exported_data.columns

                    print(f"EP Pipeline completed successfully:")
                    print(f"  Imported: {import_result.records_processed} records")
                    print(f"  Processed: {len(points)} geospatial points")
                    print(f"  Found: {len(set(clusters))} clusters")
                    print(f"  Exported: {len(exported_data)} records")

    def test_complete_cdr_pipeline(self, temp_workspace, sample_cdr_data, test_config):
        """Test complete CDR data processing pipeline."""
        with patch("src.database.connection.SessionManager") as mock_session:
            with patch("src.database.operations.BulkOperations") as mock_bulk:
                with patch("src.database.operations.CRUDOperations") as mock_crud:
                    # Setup mocks
                    mock_session.return_value.get_connection.return_value.__enter__ = (
                        Mock()
                    )
                    mock_session.return_value.get_connection.return_value.__exit__ = (
                        Mock()
                    )
                    mock_bulk.return_value.bulk_insert_dataframe.return_value = True

                    # Mock query results for analysis
                    mock_crud.return_value.read_with_conditions.return_value = (
                        pd.read_csv(sample_cdr_data)
                    )

                    # Step 1: Import CDR data
                    cdr_importer = CDRImporter(test_config)
                    import_result = cdr_importer.import_file(str(sample_cdr_data))

                    assert import_result.success
                    assert import_result.records_processed == 500

                    # Step 2: Analyze call patterns
                    cdr_data = pd.read_csv(sample_cdr_data)

                    # Calculate call statistics
                    call_stats = {
                        "total_calls": len(cdr_data),
                        "completed_calls": len(
                            cdr_data[cdr_data["status"] == "completed"]
                        ),
                        "failed_calls": len(cdr_data[cdr_data["status"] == "failed"]),
                        "avg_duration": cdr_data["duration_seconds"].mean(),
                        "total_bytes": cdr_data["bytes_transferred"].sum(),
                    }

                    # Step 3: Geospatial analysis of cell towers
                    geo_processor = GeospatialProcessor(test_config)
                    spatial_analyzer = SpatialAnalyzer(test_config)

                    # Create points for cell towers
                    tower_points = []
                    for _, row in cdr_data.iterrows():
                        point = geo_processor.create_point(
                            row["cell_tower_lon"], row["cell_tower_lat"]
                        )
                        tower_points.append(point)

                    # Find tower coverage areas
                    coverage_areas = []
                    for point in tower_points[:10]:  # Sample first 10 towers
                        buffer = geo_processor.create_buffer(point, 0.01)  # ~1km buffer
                        coverage_areas.append(buffer)

                    # Step 4: Generate comprehensive report
                    report_data = {
                        "summary": call_stats,
                        "call_types": cdr_data["call_type"].value_counts().to_dict(),
                        "hourly_distribution": cdr_data.groupby(
                            cdr_data["start_time"].dt.hour
                        )
                        .size()
                        .to_dict(),
                        "tower_count": len(
                            set(
                                zip(
                                    cdr_data["cell_tower_lat"],
                                    cdr_data["cell_tower_lon"],
                                )
                            )
                        ),
                        "coverage_areas": len(coverage_areas),
                    }

                    # Step 5: Export results in multiple formats
                    # CSV export
                    csv_output = temp_workspace / "output" / "cdr_analysis.csv"
                    csv_exporter = CSVExporter(test_config)
                    csv_result = csv_exporter.export_dataframe(
                        cdr_data, str(csv_output)
                    )
                    assert csv_result.success

                    # JSON export for report
                    json_output = temp_workspace / "output" / "cdr_report.json"
                    json_exporter = JSONExporter(test_config)

                    # Convert report to DataFrame for export
                    report_df = pd.DataFrame([report_data])
                    json_result = json_exporter.export_dataframe(
                        report_df, str(json_output)
                    )
                    assert json_result.success

                    # Verify outputs
                    assert csv_output.exists()
                    assert json_output.exists()

                    # Verify JSON content
                    with open(json_output, "r") as f:
                        exported_report = json.load(f)

                    assert len(exported_report) == 1

                    print(f"CDR Pipeline completed successfully:")
                    print(f"  Imported: {import_result.records_processed} records")
                    print(f"  Completed calls: {call_stats['completed_calls']}")
                    print(
                        f"  Average duration: {call_stats['avg_duration']:.2f} seconds"
                    )
                    print(f"  Unique towers: {report_data['tower_count']}")
                    print(f"  Coverage areas: {report_data['coverage_areas']}")

    def test_multi_source_integration(
        self,
        temp_workspace,
        sample_ep_data,
        sample_cdr_data,
        sample_nlg_data,
        test_config,
    ):
        """Test integration of multiple data sources."""
        with patch("src.database.connection.SessionManager") as mock_session:
            with patch("src.database.operations.BulkOperations") as mock_bulk:
                with patch("src.database.operations.CRUDOperations") as mock_crud:
                    # Setup mocks
                    mock_session.return_value.get_connection.return_value.__enter__ = (
                        Mock()
                    )
                    mock_session.return_value.get_connection.return_value.__exit__ = (
                        Mock()
                    )
                    mock_bulk.return_value.bulk_insert_dataframe.return_value = True

                    # Step 1: Import all data sources
                    importers = {
                        "EP": EPImporter(test_config),
                        "CDR": CDRImporter(test_config),
                        "NLG": NLGImporter(test_config),
                    }

                    data_files = {
                        "EP": sample_ep_data,
                        "CDR": sample_cdr_data,
                        "NLG": sample_nlg_data,
                    }

                    import_results = {}
                    for source, importer in importers.items():
                        result = importer.import_file(str(data_files[source]))
                        assert result.success
                        import_results[source] = result

                    # Step 2: Load and combine data
                    ep_data = pd.read_csv(sample_ep_data)
                    cdr_data = pd.read_csv(sample_cdr_data)
                    nlg_data = pd.read_csv(sample_nlg_data)

                    # Step 3: Perform cross-source analysis
                    # Time-based correlation
                    time_ranges = {
                        "EP": (ep_data["timestamp"].min(), ep_data["timestamp"].max()),
                        "CDR": (
                            cdr_data["start_time"].min(),
                            cdr_data["end_time"].max(),
                        ),
                        "NLG": (
                            nlg_data["timestamp"].min(),
                            nlg_data["timestamp"].max(),
                        ),
                    }

                    # Geospatial correlation
                    geo_processor = GeospatialProcessor(test_config)

                    # Create geospatial points for each source
                    ep_points = [
                        geo_processor.create_point(
                            row["location_lon"], row["location_lat"]
                        )
                        for _, row in ep_data.iterrows()
                    ]

                    cdr_points = [
                        geo_processor.create_point(
                            row["cell_tower_lon"], row["cell_tower_lat"]
                        )
                        for _, row in cdr_data.iterrows()
                    ]

                    nlg_points = [
                        geo_processor.create_point(row["geo_lon"], row["geo_lat"])
                        for _, row in nlg_data.iterrows()
                    ]

                    # Step 4: Generate integrated analysis
                    integrated_analysis = {
                        "data_sources": {
                            "EP": {
                                "records": len(ep_data),
                                "time_range": time_ranges["EP"],
                                "geographic_points": len(ep_points),
                            },
                            "CDR": {
                                "records": len(cdr_data),
                                "time_range": time_ranges["CDR"],
                                "geographic_points": len(cdr_points),
                            },
                            "NLG": {
                                "records": len(nlg_data),
                                "time_range": time_ranges["NLG"],
                                "geographic_points": len(nlg_points),
                            },
                        },
                        "correlations": {
                            "temporal_overlap": True,  # All sources have overlapping time ranges
                            "geographic_coverage": {
                                "ep_coverage_area": len(
                                    set(
                                        zip(
                                            ep_data["location_lat"],
                                            ep_data["location_lon"],
                                        )
                                    )
                                ),
                                "cdr_coverage_area": len(
                                    set(
                                        zip(
                                            cdr_data["cell_tower_lat"],
                                            cdr_data["cell_tower_lon"],
                                        )
                                    )
                                ),
                                "nlg_coverage_area": len(
                                    set(zip(nlg_data["geo_lat"], nlg_data["geo_lon"]))
                                ),
                            },
                        },
                        "summary": {
                            "total_records": sum(
                                len(data) for data in [ep_data, cdr_data, nlg_data]
                            ),
                            "total_geographic_points": len(ep_points)
                            + len(cdr_points)
                            + len(nlg_points),
                            "processing_timestamp": datetime.now().isoformat(),
                        },
                    }

                    # Step 5: Export integrated results
                    # Create combined dataset with common fields
                    combined_data = []

                    # Add EP data
                    for _, row in ep_data.iterrows():
                        combined_data.append(
                            {
                                "source": "EP",
                                "timestamp": row["timestamp"],
                                "latitude": row["location_lat"],
                                "longitude": row["location_lon"],
                                "value": row["energy_kwh"],
                                "category": "energy",
                            }
                        )

                    # Add CDR data
                    for _, row in cdr_data.iterrows():
                        combined_data.append(
                            {
                                "source": "CDR",
                                "timestamp": row["start_time"],
                                "latitude": row["cell_tower_lat"],
                                "longitude": row["cell_tower_lon"],
                                "value": row["duration_seconds"],
                                "category": "communication",
                            }
                        )

                    # Add NLG data
                    for _, row in nlg_data.iterrows():
                        combined_data.append(
                            {
                                "source": "NLG",
                                "timestamp": row["timestamp"],
                                "latitude": row["geo_lat"],
                                "longitude": row["geo_lon"],
                                "value": row["bytes_sent"],
                                "category": "network",
                            }
                        )

                    combined_df = pd.DataFrame(combined_data)

                    # Export combined data
                    combined_output = temp_workspace / "output" / "integrated_data.csv"
                    csv_exporter = CSVExporter(test_config)
                    export_result = csv_exporter.export_dataframe(
                        combined_df, str(combined_output)
                    )
                    assert export_result.success

                    # Export analysis report
                    analysis_output = (
                        temp_workspace / "output" / "integration_analysis.json"
                    )
                    with open(analysis_output, "w") as f:
                        json.dump(integrated_analysis, f, indent=2, default=str)

                    # Verify results
                    assert combined_output.exists()
                    assert analysis_output.exists()

                    exported_combined = pd.read_csv(combined_output)
                    assert len(exported_combined) == len(combined_data)
                    assert set(exported_combined["source"].unique()) == {
                        "EP",
                        "CDR",
                        "NLG",
                    }

                    print(f"Multi-source Integration completed:")
                    print(f"  EP records: {import_results['EP'].records_processed}")
                    print(f"  CDR records: {import_results['CDR'].records_processed}")
                    print(f"  NLG records: {import_results['NLG'].records_processed}")
                    print(f"  Combined records: {len(combined_df)}")
                    print(
                        f"  Geographic points: {integrated_analysis['summary']['total_geographic_points']}"
                    )

    def test_error_handling_and_recovery(self, temp_workspace, test_config):
        """Test error handling and recovery in the pipeline."""
        # Create corrupted data files
        corrupted_ep_file = temp_workspace / "input" / "corrupted_ep.csv"
        with open(corrupted_ep_file, "w") as f:
            f.write("timestamp,meter_id,energy_kwh\n")
            f.write("2023-01-01,METER_001,100.5\n")
            f.write("invalid_date,METER_002,invalid_value\n")  # Corrupted row
            f.write("2023-01-03,METER_003,75.2\n")

        # Create file with missing columns
        incomplete_cdr_file = temp_workspace / "input" / "incomplete_cdr.csv"
        with open(incomplete_cdr_file, "w") as f:
            f.write("call_id,caller_number\n")  # Missing required columns
            f.write("CALL_001,+15551234567\n")
            f.write("CALL_002,+15559876543\n")

        with patch("src.database.connection.SessionManager") as mock_session:
            with patch("src.database.operations.BulkOperations") as mock_bulk:
                # Setup mocks
                mock_session.return_value.get_connection.return_value.__enter__ = Mock()
                mock_session.return_value.get_connection.return_value.__exit__ = Mock()
                mock_bulk.return_value.bulk_insert_dataframe.return_value = True

                # Test EP importer with corrupted data
                ep_importer = EPImporter(test_config)

                # Should handle corrupted data gracefully
                ep_result = ep_importer.import_file(str(corrupted_ep_file))

                # Should succeed but with warnings/errors logged
                assert ep_result.success  # Should still succeed with valid rows
                assert ep_result.records_processed < 3  # Some rows should be skipped

                # Test CDR importer with incomplete data
                cdr_importer = CDRImporter(test_config)

                # Should fail due to missing required columns
                cdr_result = cdr_importer.import_file(str(incomplete_cdr_file))
                assert not cdr_result.success
                assert (
                    "missing columns" in cdr_result.error_message.lower()
                    or "required" in cdr_result.error_message.lower()
                )

                # Test database connection failure simulation
                with patch(
                    "src.database.connection.SessionManager"
                ) as mock_failing_session:
                    mock_failing_session.side_effect = Exception(
                        "Database connection failed"
                    )

                    # Create valid data file
                    valid_ep_file = temp_workspace / "input" / "valid_ep.csv"
                    valid_data = pd.DataFrame(
                        {
                            "timestamp": pd.date_range(
                                "2023-01-01", periods=10, freq="1H"
                            ),
                            "meter_id": [f"METER_{i:03d}" for i in range(10)],
                            "energy_kwh": np.random.uniform(10, 100, 10),
                        }
                    )
                    valid_data.to_csv(valid_ep_file, index=False)

                    # Should handle database connection failure
                    ep_importer_failing = EPImporter(test_config)
                    failing_result = ep_importer_failing.import_file(str(valid_ep_file))

                    assert not failing_result.success
                    assert (
                        "database" in failing_result.error_message.lower()
                        or "connection" in failing_result.error_message.lower()
                    )

                print(f"Error Handling Test Results:")
                print(
                    f"  Corrupted EP file: {ep_result.success}, processed {ep_result.records_processed} records"
                )
                print(
                    f"  Incomplete CDR file: {cdr_result.success}, error: {cdr_result.error_message}"
                )
                print(
                    f"  Database failure: {failing_result.success}, error: {failing_result.error_message}"
                )

    def test_performance_monitoring_integration(
        self, temp_workspace, sample_ep_data, test_config
    ):
        """Test performance monitoring throughout the pipeline."""
        # Create performance logger
        performance_logger = PerformanceLogger()
        metrics_collector = MetricsCollector()

        with patch("src.database.connection.SessionManager") as mock_session:
            with patch("src.database.operations.BulkOperations") as mock_bulk:
                # Setup mocks
                mock_session.return_value.get_connection.return_value.__enter__ = Mock()
                mock_session.return_value.get_connection.return_value.__exit__ = Mock()
                mock_bulk.return_value.bulk_insert_dataframe.return_value = True

                # Step 1: Import with performance monitoring
                start_time = time.time()

                ep_importer = EPImporter(
                    test_config, performance_logger=performance_logger
                )
                import_result = ep_importer.import_file(str(sample_ep_data))

                import_time = time.time() - start_time

                # Record metrics
                metrics_collector.increment_counter("imports_completed")
                metrics_collector.record_histogram("import_duration", import_time)
                metrics_collector.record_gauge(
                    "records_imported", import_result.records_processed
                )

                # Step 2: Process with monitoring
                start_time = time.time()

                ep_data = pd.read_csv(sample_ep_data)
                geo_processor = GeospatialProcessor(test_config)

                # Create points with timing
                points = []
                for _, row in ep_data.iterrows():
                    point = geo_processor.create_point(
                        row["location_lon"], row["location_lat"]
                    )
                    points.append(point)

                processing_time = time.time() - start_time

                metrics_collector.record_histogram(
                    "geospatial_processing_duration", processing_time
                )
                metrics_collector.record_gauge("points_processed", len(points))

                # Step 3: Export with monitoring
                start_time = time.time()

                output_file = temp_workspace / "output" / "monitored_export.csv"
                csv_exporter = CSVExporter(test_config)
                export_result = csv_exporter.export_dataframe(ep_data, str(output_file))

                export_time = time.time() - start_time

                metrics_collector.record_histogram("export_duration", export_time)
                metrics_collector.increment_counter("exports_completed")

                # Step 4: Collect and analyze metrics
                metrics_summary = {
                    "imports_completed": metrics_collector.get_counter(
                        "imports_completed"
                    ),
                    "exports_completed": metrics_collector.get_counter(
                        "exports_completed"
                    ),
                    "import_duration": import_time,
                    "processing_duration": processing_time,
                    "export_duration": export_time,
                    "total_duration": import_time + processing_time + export_time,
                    "records_processed": import_result.records_processed,
                    "points_created": len(points),
                    "throughput_records_per_second": import_result.records_processed
                    / (import_time + processing_time + export_time),
                }

                # Export metrics report
                metrics_file = temp_workspace / "output" / "performance_metrics.json"
                with open(metrics_file, "w") as f:
                    json.dump(metrics_summary, f, indent=2)

                # Verify performance thresholds
                assert import_time < 10.0  # Import should complete within 10 seconds
                assert (
                    processing_time < 5.0
                )  # Processing should complete within 5 seconds
                assert export_time < 5.0  # Export should complete within 5 seconds
                assert (
                    metrics_summary["throughput_records_per_second"] > 50
                )  # At least 50 records/sec

                assert metrics_file.exists()

                print(f"Performance Monitoring Results:")
                print(f"  Import time: {import_time:.4f}s")
                print(f"  Processing time: {processing_time:.4f}s")
                print(f"  Export time: {export_time:.4f}s")
                print(f"  Total time: {metrics_summary['total_duration']:.4f}s")
                print(
                    f"  Throughput: {metrics_summary['throughput_records_per_second']:.2f} records/sec"
                )

    def test_data_quality_validation_pipeline(self, temp_workspace, test_config):
        """Test data quality validation throughout the pipeline."""
        # Create data with quality issues
        quality_test_data = pd.DataFrame(
            {
                "timestamp": [
                    "2023-01-01 00:00:00",
                    "2023-01-01 01:00:00",
                    "invalid_date",
                    "2023-01-01 03:00:00",
                ],
                "meter_id": ["METER_001", "", "METER_003", "METER_004"],  # Empty value
                "energy_kwh": [100.5, -50.0, 75.2, 200.0],  # Negative value
                "power_kw": [50.0, 25.0, None, 100.0],  # Missing value
                "voltage": [
                    230.0,
                    240.0,
                    220.0,
                    250.0,
                ],  # One value out of normal range
                "location_lat": [40.7128, 40.7589, 40.6892, 91.0],  # Invalid latitude
                "location_lon": [-74.0060, -73.9851, -74.0445, -74.0060],
            }
        )

        quality_file = temp_workspace / "input" / "quality_test.csv"
        quality_test_data.to_csv(quality_file, index=False)

        with patch("src.database.connection.SessionManager") as mock_session:
            with patch("src.database.operations.BulkOperations") as mock_bulk:
                # Setup mocks
                mock_session.return_value.get_connection.return_value.__enter__ = Mock()
                mock_session.return_value.get_connection.return_value.__exit__ = Mock()
                mock_bulk.return_value.bulk_insert_dataframe.return_value = True

                # Step 1: Validate data quality
                data_validator = DataValidator()

                # Add validation rules
                data_validator.add_rule(
                    "required_columns", ["timestamp", "meter_id", "energy_kwh"]
                )
                data_validator.add_rule(
                    "data_types",
                    {"energy_kwh": "float", "power_kw": "float", "voltage": "float"},
                )
                data_validator.add_rule(
                    "value_ranges",
                    {
                        "energy_kwh": (0, 1000),
                        "voltage": (200, 250),
                        "location_lat": (-90, 90),
                        "location_lon": (-180, 180),
                    },
                )
                data_validator.add_rule("not_null", ["meter_id"])

                # Validate the data
                validation_result = data_validator.validate_dataframe(quality_test_data)

                # Should find multiple issues
                assert not validation_result.is_valid
                assert len(validation_result.errors) > 0

                # Step 2: Clean and process valid data
                # Remove rows with critical issues
                clean_data = quality_test_data.copy()

                # Remove rows with invalid dates
                clean_data = clean_data[clean_data["timestamp"] != "invalid_date"]

                # Remove rows with empty meter_id
                clean_data = clean_data[clean_data["meter_id"] != ""]

                # Remove rows with negative energy values
                clean_data = clean_data[clean_data["energy_kwh"] >= 0]

                # Remove rows with invalid coordinates
                clean_data = clean_data[
                    (clean_data["location_lat"] >= -90)
                    & (clean_data["location_lat"] <= 90)
                ]

                # Fill missing power values with median
                clean_data["power_kw"] = clean_data["power_kw"].fillna(
                    clean_data["power_kw"].median()
                )

                # Step 3: Import cleaned data
                clean_file = temp_workspace / "input" / "cleaned_data.csv"
                clean_data.to_csv(clean_file, index=False)

                ep_importer = EPImporter(test_config)
                import_result = ep_importer.import_file(str(clean_file))

                assert import_result.success
                assert import_result.records_processed == len(clean_data)

                # Step 4: Generate data quality report
                quality_report = {
                    "original_records": len(quality_test_data),
                    "cleaned_records": len(clean_data),
                    "records_removed": len(quality_test_data) - len(clean_data),
                    "validation_errors": [
                        str(error) for error in validation_result.errors
                    ],
                    "data_quality_score": len(clean_data) / len(quality_test_data),
                    "cleaning_actions": [
                        "Removed rows with invalid dates",
                        "Removed rows with empty meter IDs",
                        "Removed rows with negative energy values",
                        "Removed rows with invalid coordinates",
                        "Filled missing power values with median",
                    ],
                }

                # Export quality report
                quality_report_file = (
                    temp_workspace / "output" / "data_quality_report.json"
                )
                with open(quality_report_file, "w") as f:
                    json.dump(quality_report, f, indent=2)

                assert quality_report_file.exists()
                assert (
                    quality_report["data_quality_score"] > 0.5
                )  # At least 50% of data should be salvageable

                print(f"Data Quality Validation Results:")
                print(f"  Original records: {quality_report['original_records']}")
                print(f"  Cleaned records: {quality_report['cleaned_records']}")
                print(f"  Records removed: {quality_report['records_removed']}")
                print(
                    f"  Data quality score: {quality_report['data_quality_score']:.2%}"
                )
                print(
                    f"  Validation errors found: {len(quality_report['validation_errors'])}"
                )


@pytest.mark.e2e
@pytest.mark.slow
class TestSystemResilience:
    """Test system resilience and recovery capabilities."""

    def test_concurrent_pipeline_execution(self, temp_workspace, test_config):
        """Test concurrent execution of multiple pipelines."""
        import concurrent.futures
        import threading

        # Create multiple data files
        data_files = []
        for i in range(3):
            data = pd.DataFrame(
                {
                    "timestamp": pd.date_range(
                        f"2023-01-{i+1:02d}", periods=100, freq="1H"
                    ),
                    "meter_id": [
                        f"METER_{j:03d}" for j in range(i * 100, (i + 1) * 100)
                    ],
                    "energy_kwh": np.random.uniform(10, 100, 100),
                }
            )

            file_path = temp_workspace / "input" / f"concurrent_data_{i}.csv"
            data.to_csv(file_path, index=False)
            data_files.append(file_path)

        results = []
        errors = []

        def process_pipeline(file_path, pipeline_id):
            """Process a single pipeline."""
            try:
                with patch("src.database.connection.SessionManager") as mock_session:
                    with patch("src.database.operations.BulkOperations") as mock_bulk:
                        # Setup mocks
                        mock_session.return_value.get_connection.return_value.__enter__ = (
                            Mock()
                        )
                        mock_session.return_value.get_connection.return_value.__exit__ = (
                            Mock()
                        )
                        mock_bulk.return_value.bulk_insert_dataframe.return_value = True

                        # Import data
                        ep_importer = EPImporter(test_config)
                        import_result = ep_importer.import_file(str(file_path))

                        # Process data
                        data = pd.read_csv(file_path)
                        processed_data = data.copy()
                        processed_data["pipeline_id"] = pipeline_id
                        processed_data["processed_at"] = datetime.now()

                        # Export results
                        output_file = (
                            temp_workspace
                            / "output"
                            / f"concurrent_result_{pipeline_id}.csv"
                        )
                        csv_exporter = CSVExporter(test_config)
                        export_result = csv_exporter.export_dataframe(
                            processed_data, str(output_file)
                        )

                        results.append(
                            {
                                "pipeline_id": pipeline_id,
                                "import_success": import_result.success,
                                "export_success": export_result.success,
                                "records_processed": import_result.records_processed,
                                "output_file": str(output_file),
                            }
                        )

            except Exception as e:
                errors.append(f"Pipeline {pipeline_id}: {str(e)}")

        # Execute pipelines concurrently
        start_time = time.time()

        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [
                executor.submit(process_pipeline, file_path, i)
                for i, file_path in enumerate(data_files)
            ]

            # Wait for completion
            concurrent.futures.wait(futures)

        end_time = time.time()
        total_time = end_time - start_time

        # Verify results
        assert len(errors) == 0, f"Errors occurred: {errors}"
        assert len(results) == 3

        total_records = sum(r["records_processed"] for r in results)

        # Verify all output files exist
        for result in results:
            assert Path(result["output_file"]).exists()
            assert result["import_success"]
            assert result["export_success"]

        print(f"Concurrent Pipeline Execution:")
        print(f"  Pipelines executed: {len(results)}")
        print(f"  Total time: {total_time:.4f}s")
        print(f"  Total records processed: {total_records}")
        print(f"  Average records per pipeline: {total_records / len(results):.0f}")

        for result in results:
            print(
                f"    Pipeline {result['pipeline_id']}: {result['records_processed']} records"
            )

    def test_resource_cleanup(self, temp_workspace, test_config):
        """Test proper resource cleanup after pipeline execution."""
        import gc

        import psutil

        process = psutil.Process()
        initial_memory = process.memory_info().rss
        initial_open_files = len(process.open_files())

        # Create test data
        test_data = pd.DataFrame(
            {
                "timestamp": pd.date_range("2023-01-01", periods=1000, freq="1H"),
                "meter_id": [f"METER_{i:04d}" for i in range(1000)],
                "energy_kwh": np.random.uniform(10, 100, 1000),
            }
        )

        test_file = temp_workspace / "input" / "cleanup_test.csv"
        test_data.to_csv(test_file, index=False)

        # Execute pipeline multiple times to test cleanup
        for iteration in range(5):
            with patch("src.database.connection.SessionManager") as mock_session:
                with patch("src.database.operations.BulkOperations") as mock_bulk:
                    # Setup mocks
                    mock_session.return_value.get_connection.return_value.__enter__ = (
                        Mock()
                    )
                    mock_session.return_value.get_connection.return_value.__exit__ = (
                        Mock()
                    )
                    mock_bulk.return_value.bulk_insert_dataframe.return_value = True

                    # Import and process
                    ep_importer = EPImporter(test_config)
                    import_result = ep_importer.import_file(str(test_file))

                    # Load and process data
                    data = pd.read_csv(test_file)
                    processed_data = data.copy()
                    processed_data["iteration"] = iteration

                    # Export
                    output_file = (
                        temp_workspace / "output" / f"cleanup_iteration_{iteration}.csv"
                    )
                    csv_exporter = CSVExporter(test_config)
                    export_result = csv_exporter.export_dataframe(
                        processed_data, str(output_file)
                    )

                    assert import_result.success
                    assert export_result.success

                    # Explicit cleanup
                    del ep_importer, csv_exporter, data, processed_data
                    gc.collect()

        # Check resource usage after cleanup
        final_memory = process.memory_info().rss
        final_open_files = len(process.open_files())

        memory_increase = final_memory - initial_memory
        file_handle_increase = final_open_files - initial_open_files

        print(f"Resource Cleanup Test:")
        print(f"  Initial memory: {initial_memory / 1024 / 1024:.2f} MB")
        print(f"  Final memory: {final_memory / 1024 / 1024:.2f} MB")
        print(f"  Memory increase: {memory_increase / 1024 / 1024:.2f} MB")
        print(f"  Initial open files: {initial_open_files}")
        print(f"  Final open files: {final_open_files}")
        print(f"  File handle increase: {file_handle_increase}")

        # Memory increase should be minimal
        assert (
            memory_increase < 100 * 1024 * 1024
        ), f"Excessive memory usage: {memory_increase / 1024 / 1024:.2f} MB"

        # File handles should not leak
        assert (
            file_handle_increase <= 2
        ), f"File handle leak detected: {file_handle_increase} additional handles"

    def test_graceful_shutdown(self, temp_workspace, test_config):
        """Test graceful shutdown of pipeline components."""
        import signal
        import threading
        import time

        shutdown_events = []

        class MockComponent:
            def __init__(self, name):
                self.name = name
                self.running = True
                self.shutdown_called = False

            def shutdown(self):
                self.shutdown_called = True
                self.running = False
                shutdown_events.append(f"{self.name} shutdown")

        # Create mock components
        components = {
            "database_session": MockComponent("SessionManager"),
            "connection_pool": MockComponent("ConnectionPool"),
            "performance_logger": MockComponent("PerformanceLogger"),
            "metrics_collector": MockComponent("MetricsCollector"),
        }

        # Simulate long-running process
        def long_running_process():
            """Simulate a long-running data processing task."""
            for i in range(100):
                if not all(comp.running for comp in components.values()):
                    break
                time.sleep(0.01)  # Simulate work

        # Start process in background
        process_thread = threading.Thread(target=long_running_process)
        process_thread.start()

        # Wait a bit, then trigger shutdown
        time.sleep(0.1)

        # Simulate shutdown signal
        for component in components.values():
            component.shutdown()

        # Wait for process to complete
        process_thread.join(timeout=1.0)

        # Verify graceful shutdown
        assert all(comp.shutdown_called for comp in components.values())
        assert len(shutdown_events) == len(components)

        print(f"Graceful Shutdown Test:")
        print(f"  Components shut down: {len(shutdown_events)}")
        for event in shutdown_events:
            print(f"    {event}")

        # Process should have stopped gracefully
        assert not process_thread.is_alive()
