"""Provides health checking capabilities for the database and system."""
from typing import Dict, Any, List, Tuple

class HealthChecker:
    """Base class for performing health checks."""
    def check(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Performs a health check.

        Returns:
            Tuple[bool, str, Dict[str, Any]]: A tuple containing:
                - bool: True if healthy, False otherwise.
                - str: A status message.
                - Dict[str, Any]: Additional details about the health check.
        """
        # Placeholder implementation
        return True, "Basic check OK", {}

class SystemHealthChecker(HealthChecker):
    """Performs health checks on system resources (CPU, memory, disk)."""
    def check(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Checks system health.

        Returns:
            Tuple[bool, str, Dict[str, Any]]: System health status.
        """
        # Placeholder - in a real scenario, use psutil or similar
        details = {
            "cpu_usage": 0.1,
            "memory_usage": 0.2,
            "disk_space_free_gb": 100
        }
        return True, "System health OK", details

class HealthMonitor:
    """Monitors the health of various components using registered checkers."""
    def __init__(self, checkers: List[HealthChecker]):
        """Initializes the HealthMonitor with a list of health checkers.

        Args:
            checkers (List[HealthChecker]): A list of HealthChecker instances.
        """
        self.checkers = checkers

    def get_overall_health(self) -> Dict[str, Any]:
        """Gets the overall health status by running all registered checkers.

        Returns:
            Dict[str, Any]: A dictionary representing the overall health status.
        """
        overall_status = "healthy"
        component_statuses: List[Dict[str, Any]] = [] 

        for checker in self.checkers:
            is_healthy, message, details = checker.check()
            component_name = checker.__class__.__name__
            component_statuses.append({
                "component": component_name,
                "status": "healthy" if is_healthy else "unhealthy",
                "message": message,
                "details": details
            })
            if not is_healthy:
                overall_status = "unhealthy"
        
        return {
            "overall_status": overall_status,
            "components": component_statuses
        }