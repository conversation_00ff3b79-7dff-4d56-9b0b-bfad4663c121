"""Bulk database operations module.

This module provides functionality for performing bulk operations on the database,
such as bulk inserts, updates, and deletes.
"""

from typing import Any, Dict, List, Optional, Union
import asyncio
import logging

import pandas as pd
from sqlalchemy import MetaData, Table
from sqlalchemy.exc import SQLAlchemyError

from src.database.connection import SessionManager
from src.database.schema import SchemaManager

# Configure logging
logger = logging.getLogger(__name__)


class BulkOperations:
    """Class for performing bulk database operations."""

    def __init__(self, config, pool_manager=None):
        """Initialize BulkOperations with configuration.

        Args:
            config: Configuration object containing database settings.
            pool_manager: Optional pool manager for async operations.
        """
        self.config = config
        self.session_manager = SessionManager(config)
        self.schema_manager = SchemaManager(config)
        self.pool_manager = pool_manager
        self.pool = pool_manager._pool if pool_manager else None

    def bulk_insert_dataframe(
        self,
        df: pd.DataFrame,
        table_name: str,
        schema: str = None,
        if_exists: str = "append",
        index: bool = False,
        chunk_size: int = 1000,
    ) -> bool:
        """Bulk insert a pandas DataFrame into a database table.

        Args:
            df: Pandas DataFrame containing the data to insert.
            table_name: Name of the target table.
            schema: Database schema name (optional).
            if_exists: How to behave if the table already exists ('fail', 'replace', or 'append').
            index: Whether to include the DataFrame's index as a column.
            chunk_size: Number of rows to insert at once.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Use simplified sync method to avoid async/sync conflicts
            return self._simplified_bulk_insert(df, table_name, schema, if_exists, index, chunk_size)
        except Exception as e:
            logger.error(f"Bulk insert error: {str(e)}")
            return False

    def _simplified_bulk_insert(self, df, table_name, schema=None, if_exists='append', index=False, chunk_size=1000):
        """Simplified bulk insert method using direct database connection."""
        try:
            import psycopg2
            import psycopg2.extras
            import io

            # Set default schema
            if schema is None:
                schema = 'public'

            # Get connection parameters from config
            if self.config and hasattr(self.config, 'database'):
                conn_params = {
                    'host': self.config.database.host,
                    'port': self.config.database.port,
                    'database': self.config.database.name,
                    'user': self.config.database.user,
                    'password': self.config.database.password
                }
            else:
                # Fallback to environment variables or defaults
                import os
                conn_params = {
                    'host': os.getenv('DATABASE_HOST', 'localhost'),
                    'port': int(os.getenv('DATABASE_PORT', 5432)),
                    'database': os.getenv('DATABASE_NAME', 'connect'),
                    'user': os.getenv('DATABASE_USER', 'to2'),
                    'password': os.getenv('DATABASE_PASSWORD', 'TO2')
                }

            # Create connection
            with psycopg2.connect(**conn_params) as conn:
                with conn.cursor() as cursor:
                    # Create table if it doesn't exist
                    self._ensure_table_exists(cursor, df, table_name, schema)

                    # Process data in chunks
                    total_inserted = 0
                    for i in range(0, len(df), chunk_size):
                        chunk = df.iloc[i:i + chunk_size]

                        # Convert chunk to CSV format
                        csv_buffer = io.StringIO()
                        chunk.to_csv(csv_buffer, index=False, header=False, na_rep='\\N')
                        csv_buffer.seek(0)

                        # Use COPY for efficient bulk insert
                        columns = ', '.join(f'"{col}"' for col in df.columns)
                        full_table_name = f'"{schema}"."{table_name}"'

                        try:
                            cursor.copy_expert(
                                f"COPY {full_table_name} ({columns}) FROM STDIN WITH CSV",
                                csv_buffer
                            )
                            total_inserted += len(chunk)
                            logger.info(f"Inserted {len(chunk)} records → {schema}.{table_name}")

                        except Exception as e:
                            logger.error(f"Error inserting chunk: {e}")
                            conn.rollback()
                            return False

                        csv_buffer.close()

                    conn.commit()
                    logger.info(f"Successfully inserted {total_inserted} total records into {schema}.{table_name}")
                    return True

        except Exception as e:
            logger.error(f"Simplified bulk insert failed: {e}")
            return False

    def _ensure_table_exists(self, cursor, df, table_name, schema):
        """Ensure table exists with proper structure."""
        try:
            # Check if table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = %s AND table_name = %s
                )
            """, (schema, table_name))

            if not cursor.fetchone()[0]:
                # Create table
                columns_sql = ['"id" BIGSERIAL PRIMARY KEY', '"created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP']

                for col_name in df.columns:
                    clean_name = self._clean_column_name(col_name)
                    if clean_name and clean_name not in ['id', 'created_at']:
                        columns_sql.append(f'"{clean_name}" TEXT')

                create_sql = f'''
                    CREATE TABLE "{schema}"."{table_name}" (
                        {', '.join(columns_sql)}
                    )
                '''
                cursor.execute(create_sql)
                logger.info(f"Created table {schema}.{table_name}")

        except Exception as e:
            logger.error(f"Error ensuring table exists: {e}")
            raise

    def _clean_column_name(self, col_name):
        """Clean column name for database compatibility."""
        import re
        clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', str(col_name).lower().strip())
        clean_name = re.sub(r'_+', '_', clean_name)
        return clean_name.strip('_')

    def _sync_bulk_insert_fallback(self, df, table_name, schema='public', if_exists='append', index=False, chunk_size=1000):
        """Fallback synchronous bulk insert method using asyncio."""
        try:
            import asyncio
            import concurrent.futures

            # Check if we're already in an event loop
            try:
                loop = asyncio.get_running_loop()
                # We're in an event loop, create a new thread with its own loop
                def run_in_new_loop():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(
                            self._async_bulk_insert_real(df, table_name, schema, if_exists, index, chunk_size)
                        )
                    finally:
                        new_loop.close()
                        asyncio.set_event_loop(None)

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_new_loop)
                    return future.result(timeout=300)  # 5 minute timeout

            except RuntimeError:
                # No event loop running, safe to use asyncio.run
                return asyncio.run(
                    self._async_bulk_insert_real(df, table_name, schema, if_exists, index, chunk_size)
                )

        except concurrent.futures.TimeoutError:
            logger.error(f"Bulk insert timeout for table {table_name}")
            return False
        except Exception as e:
            logger.error(f"Bulk insert error: {str(e)}")
            return False

    async def _async_bulk_insert_real(self, df, table_name, schema='public', if_exists='append', index=False, chunk_size=1000):
        """Real async bulk insert implementation with memory optimization."""
        try:
            # Create a fresh connection instead of using pool to avoid event loop conflicts
            import asyncpg
            import os
            import psutil
            import gc

            # Check system resources before starting
            try:
                memory = psutil.virtual_memory()
                if memory.percent > 85:
                    logger.warning(f"High memory usage before bulk insert: {memory.percent:.1f}%")
                    gc.collect()  # Force garbage collection
                
                # Check disk space with proper path handling
                try:
                    # Use absolute path to avoid Windows path format issues
                    current_dir = os.path.abspath(os.getcwd())
                    disk = psutil.disk_usage(current_dir)
                    free_gb = float(disk.free) / (1024**3)
                    if free_gb < 1.0:  # Less than 1GB free
                        logger.error(f"Insufficient disk space: {free_gb:.2f}GB free")
                        raise Exception(f"Insufficient disk space for bulk insert: {free_gb:.2f}GB free")
                    elif free_gb < 5.0:  # Less than 5GB free
                        logger.warning(f"Low disk space: {free_gb:.2f}GB free")
                except Exception as disk_error:
                    logger.warning(f"Could not check disk space: {disk_error}")
                    # Continue without disk space check if it fails
            except ImportError:
                logger.debug("psutil not available for system monitoring")

            # Use environment variables or defaults to avoid config loading issues
            host = os.getenv('DATABASE_HOST', 'localhost')
            port = int(os.getenv('DATABASE_PORT', '5432'))
            database = os.getenv('DATABASE_NAME', 'connect')
            user = os.getenv('DATABASE_USER', 'to2')
            password = os.getenv('DATABASE_PASSWORD', 'TO2')

            conn = await asyncpg.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=database
            )

            try:
                # Ensure table exists first
                # Check if table exists
                table_exists = await conn.fetchval(
                    'SELECT EXISTS(SELECT 1 FROM information_schema.tables WHERE table_schema = $1 AND table_name = $2)',
                    schema, table_name
                )

                if not table_exists:
                    # Try to create table automatically from DataFrame structure
                    logger.info(f"Table {schema}.{table_name} does not exist, creating it...")
                    try:
                        await self._create_table_from_dataframe(conn, df, table_name, schema)
                        logger.info(f"Created table {schema}.{table_name}")
                    except Exception as create_error:
                        logger.error(f"Failed to create table {schema}.{table_name}: {create_error}")
                        return False

                # Get table columns with their data types
                columns = await conn.fetch(
                    'SELECT column_name, data_type, is_nullable, column_default FROM information_schema.columns WHERE table_schema = $1 AND table_name = $2 ORDER BY ordinal_position',
                    schema, table_name
                )

                if not columns:
                    logger.error(f"No columns found for table {schema}.{table_name}")
                    return False

                table_columns = [col['column_name'] for col in columns]
                
                # Get columns that have defaults or are auto-generated (like SERIAL, TIMESTAMP with DEFAULT)
                auto_generated_columns = set()
                for col in columns:
                    col_name = col['column_name']
                    col_default = col['column_default']
                    data_type = col['data_type']
                    
                    # Skip columns that are auto-generated or have defaults
                    if (col_name in ['id', 'created_at'] or 
                        col_default is not None or 
                        'serial' in data_type.lower() or
                        'nextval' in str(col_default).lower() if col_default else False):
                        auto_generated_columns.add(col_name)

                # Create mapping between DataFrame columns and table columns (case-insensitive)
                df_to_table_mapping = {}
                for df_col in df.columns:
                    # Clean the DataFrame column name the same way as table creation
                    clean_df_col = self._clean_column_name(df_col)
                    if clean_df_col in table_columns and clean_df_col not in auto_generated_columns:
                        df_to_table_mapping[df_col] = clean_df_col

                if not df_to_table_mapping:
                    logger.warning(f"No matching columns found between DataFrame and table {schema}.{table_name}")
                    logger.debug(f"DataFrame columns: {list(df.columns)}")
                    logger.debug(f"Table columns: {table_columns}")
                    logger.debug(f"Auto-generated columns: {auto_generated_columns}")
                    return False

                # Use the original DataFrame column names for data access
                df_columns = list(df_to_table_mapping.keys())
                # Use the cleaned table column names for database operations
                table_column_names = [df_to_table_mapping[col] for col in df_columns]

                # Prepare data for insertion with memory optimization
                filtered_df = df[df_columns].copy()
                
                # Adaptive chunk size based on data size and memory
                adaptive_chunk_size = min(chunk_size, max(100, len(filtered_df) // 10))
                if len(filtered_df) > 50000:  # For large datasets
                    adaptive_chunk_size = min(500, adaptive_chunk_size)

                # Insert data in chunks with memory monitoring
                total_inserted = 0
                for i in range(0, len(filtered_df), adaptive_chunk_size):
                    # Monitor memory during processing
                    try:
                        memory = psutil.virtual_memory()
                        if memory.percent > 90:
                            logger.warning(f"Critical memory usage during bulk insert: {memory.percent:.1f}%")
                            gc.collect()
                            # Reduce chunk size if memory is critical
                            adaptive_chunk_size = max(50, adaptive_chunk_size // 2)
                    except ImportError:
                        pass
                    
                    chunk = filtered_df.iloc[i:i+adaptive_chunk_size]

                    # Prepare values for COPY with error handling
                    try:
                        values = []
                        for _, row in chunk.iterrows():
                            row_values = []
                            for col in df_columns:
                                value = row[col]
                                if pd.isna(value):
                                    row_values.append(None)
                                else:
                                    row_values.append(str(value))
                            values.append(row_values)

                        # Use COPY for efficient bulk insert
                        import io

                        # Build the COPY command using table column names
                        columns_str = ', '.join(f'"{col}"' for col in table_column_names)
                        full_table_name = f'"{schema}"."{table_name}"' if schema != 'public' else f'"{table_name}"'
                        copy_sql = f"COPY {full_table_name} ({columns_str}) FROM STDIN WITH CSV"

                        # Convert to CSV format without header
                        csv_buffer = io.StringIO()
                        chunk.to_csv(csv_buffer, index=False, header=False, na_rep='\\N')
                        csv_data = csv_buffer.getvalue()
                        
                        # Clear buffer to free memory
                        csv_buffer.close()
                        del csv_buffer
                    except Exception as chunk_error:
                        logger.error(f"Error processing chunk {i//adaptive_chunk_size + 1}: {chunk_error}")
                        continue

                    # Get column types from database to ensure proper type conversion
                    column_types = {}
                    try:
                        type_query = """
                        SELECT column_name, data_type, udt_name
                        FROM information_schema.columns 
                        WHERE table_name = $1 AND table_schema = $2
                        ORDER BY ordinal_position
                        """
                        type_result = await conn.fetch(type_query, table_name, schema)
                        for row in type_result:
                            column_types[row['column_name']] = {
                                'data_type': row['data_type'],
                                'udt_name': row['udt_name']
                            }
                    except Exception as e:
                        logger.warning(f"Could not fetch column types: {e}")
                    
                    # Use copy_to_table with correct asyncpg API for bulk insert
                    # Convert DataFrame to records format for copy_records_to_table
                    # ENHANCED FIX: Proper data type conversion based on database schema
                    records = []
                    for row in chunk.values:
                        converted_row = []
                        for i, value in enumerate(row):
                            if pd.isna(value) or value is None:
                                converted_row.append(None)
                                continue
                            
                            # Get column info for proper type conversion
                            col_name = df_columns[i] if i < len(df_columns) else ''
                            col_info = column_types.get(col_name, {})
                            data_type = col_info.get('data_type', 'text')
                            udt_name = col_info.get('udt_name', 'text')
                            
                            # Convert based on database column type
                            if data_type in ['timestamp', 'timestamptz', 'date'] or 'timestamp' in udt_name or 'date' in udt_name:
                                # Handle timestamp/date columns - convert to datetime object
                                if isinstance(value, pd.Timestamp):
                                    try:
                                        # Convert to Python datetime object
                                        if pd.isna(value):
                                            converted_row.append(None)
                                        elif value.tz is not None:
                                            converted_row.append(value.tz_convert('UTC').to_pydatetime().replace(tzinfo=None))
                                        else:
                                            converted_row.append(value.to_pydatetime())
                                    except Exception:
                                        converted_row.append(None)
                                elif isinstance(value, str):
                                    try:
                                        # Clean and validate string
                                        clean_value = str(value).strip()
                                        if clean_value == '' or clean_value.lower() in ['nan', 'null', 'none', 'nat']:
                                            converted_row.append(None)
                                        else:
                                            # Convert string to datetime object
                                            normalized_dt = self._convert_string_to_datetime(clean_value, col_name)
                                            if isinstance(normalized_dt, str):
                                                # If conversion failed and returned string, set to NULL
                                                logger.debug(f"Could not convert '{clean_value}' to datetime for column '{col_name}', setting to NULL")
                                                converted_row.append(None)
                                            else:
                                                converted_row.append(normalized_dt)
                                    except Exception as e:
                                        logger.debug(f"Error converting '{value}' to datetime for column '{col_name}': {e}")
                                        converted_row.append(None)
                                else:
                                    converted_row.append(None)
                            elif data_type in ['bigint', 'integer', 'smallint'] or udt_name in ['int8', 'int4', 'int2']:
                                # Handle integer columns with enhanced conversion
                                try:
                                    if isinstance(value, (int, float)):
                                        # Handle NaN and infinity
                                        import numpy as np
                                        if pd.isna(value) or not np.isfinite(value):
                                            converted_row.append(None)
                                        else:
                                            converted_row.append(int(value))
                                    elif isinstance(value, str):
                                        # Clean string and convert
                                        clean_value = str(value).strip()
                                        if clean_value == '' or clean_value.lower() in ['nan', 'null', 'none', '']:
                                            converted_row.append(None)
                                        else:
                                            # Try direct int conversion first
                                            try:
                                                converted_row.append(int(clean_value))
                                            except ValueError:
                                                # Try float then int for decimal strings
                                                converted_row.append(int(float(clean_value)))
                                    else:
                                        converted_row.append(None)
                                except (ValueError, TypeError, OverflowError):
                                    # Log the problematic value for debugging
                                    logger.debug(f"Could not convert '{value}' to integer for column '{col_name}', setting to NULL")
                                    converted_row.append(None)
                            elif data_type in ['numeric', 'decimal', 'real', 'double precision'] or udt_name in ['numeric', 'float4', 'float8']:
                                # Handle numeric columns with enhanced conversion
                                try:
                                    if isinstance(value, (int, float)):
                                        # Handle NaN and infinity
                                        import numpy as np
                                        if pd.isna(value) or not np.isfinite(value):
                                            converted_row.append(None)
                                        else:
                                            converted_row.append(float(value))
                                    elif isinstance(value, str):
                                        # Clean string and convert
                                        clean_value = str(value).strip()
                                        if clean_value == '' or clean_value.lower() in ['nan', 'null', 'none', 'inf', '-inf']:
                                            converted_row.append(None)
                                        else:
                                            converted_row.append(float(clean_value))
                                    else:
                                        converted_row.append(None)
                                except (ValueError, TypeError, OverflowError):
                                    # Log the problematic value for debugging
                                    logger.debug(f"Could not convert '{value}' to float for column '{col_name}', setting to NULL")
                                    converted_row.append(None)
                            elif data_type == 'boolean' or udt_name == 'bool':
                                # Handle boolean columns
                                try:
                                    if isinstance(value, bool):
                                        converted_row.append(value)
                                    elif isinstance(value, str):
                                        converted_row.append(value.lower() in ['true', '1', 'yes', 'on'])
                                    elif isinstance(value, (int, float)):
                                        converted_row.append(bool(value))
                                    else:
                                        converted_row.append(None)
                                except (ValueError, TypeError):
                                    converted_row.append(None)
                            else:
                                # Handle text and other columns as strings
                                if isinstance(value, pd.Timestamp):
                                    try:
                                        if value.tz is not None:
                                            converted_row.append(value.tz_convert('UTC').strftime('%Y-%m-%d %H:%M:%S'))
                                        else:
                                            converted_row.append(value.strftime('%Y-%m-%d %H:%M:%S'))
                                    except Exception:
                                        converted_row.append(str(value))
                                elif isinstance(value, pd.Timedelta):
                                    converted_row.append(str(value.total_seconds()))
                                else:
                                    converted_row.append(str(value))
                        
                        records.append(tuple(converted_row))

                    # Use copy_records_to_table which is the correct method for bulk insert
                    await conn.copy_records_to_table(
                        table_name,
                        records=records,
                        columns=table_column_names,
                        schema_name=schema if schema != 'public' else None
                    )

                    total_inserted += len(chunk)
                    
                    # Clean up chunk to free memory
                    del chunk
                    
                    # Periodic garbage collection for large datasets
                    if total_inserted % (adaptive_chunk_size * 10) == 0:
                        gc.collect()
                        logger.debug(f"Processed {total_inserted} records, memory cleanup performed")

                logger.info(f"Inserted {total_inserted} records → {schema}.{table_name}")
                
                # Final cleanup
                del filtered_df
                gc.collect()
                
                return True

            finally:
                # Always close the connection
                await conn.close()

        except MemoryError as e:
            logger.error(f"Memory error during bulk insert: {str(e)}")
            logger.error("Consider reducing chunk_size or processing data in smaller batches")
            return False
        except Exception as e:
            logger.error(f"Async bulk insert error: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    async def _create_table_from_dataframe(self, conn, df, table_name, schema='public'):
        """Create table from DataFrame structure."""
        import re

        # Build column definitions
        columns = []

        # Add primary key
        columns.append('"id" BIGSERIAL PRIMARY KEY')

        # Add timestamp
        columns.append('"created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP')

        # Add data columns based on DataFrame
        for col_name in df.columns:
            # Clean column name
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', str(col_name).lower().strip())
            clean_name = re.sub(r'_+', '_', clean_name)
            clean_name = clean_name.strip('_')

            if clean_name and clean_name not in ['id', 'created_at']:
                columns.append(f'"{clean_name}" TEXT')

        # Create table SQL
        columns_sql = ', '.join(columns)
        create_sql = f'CREATE TABLE IF NOT EXISTS "{schema}"."{table_name}" ({columns_sql})'

        # Execute table creation
        await conn.execute(create_sql)

    def _convert_string_to_datetime(self, value, col_name):
        """Helper method to convert string to datetime with enhanced error handling."""
        try:
            # Clean the input value
            clean_value = str(value).strip()

            # Handle empty or null-like values
            if not clean_value or clean_value.lower() in ['nan', 'null', 'none', 'nat', '']:
                return None

            # Try multiple datetime formats with more comprehensive list
            datetime_formats = [
                # ISO formats
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%d',
                # European formats
                '%d/%m/%Y %H:%M:%S',
                '%d/%m/%Y',
                '%d.%m.%Y %H:%M:%S',
                '%d.%m.%Y',
                '%d-%m-%Y %H:%M:%S',
                '%d-%m-%Y',
                # US formats
                '%m/%d/%Y %H:%M:%S',
                '%m/%d/%Y',
                '%m-%d-%Y %H:%M:%S',
                '%m-%d-%Y',
                # Other common formats
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d',
                '%Y.%m.%d %H:%M:%S',
                '%Y.%m.%d',
                # Time only formats (will use today's date)
                '%H:%M:%S',
                '%H:%M'
            ]

            from datetime import datetime
            converted_dt = None

            # Try each format
            for fmt in datetime_formats:
                try:
                    converted_dt = datetime.strptime(clean_value, fmt)
                    break
                except ValueError:
                    continue

            if converted_dt:
                return converted_dt
            else:
                # If all formats fail, try pandas to_datetime with more options
                try:
                    converted_dt = pd.to_datetime(clean_value, errors='coerce', infer_datetime_format=True)
                    if pd.notna(converted_dt):
                        return converted_dt.to_pydatetime()
                    else:
                        # Final attempt with dateutil parser
                        try:
                            from dateutil import parser
                            converted_dt = parser.parse(clean_value, fuzzy=True)
                            return converted_dt
                        except:
                            logger.debug(f"Could not convert '{clean_value}' to datetime for column '{col_name}', returning None")
                            return None
                except Exception as e:
                    logger.debug(f"Pandas datetime conversion failed for '{clean_value}' in column '{col_name}': {e}")
                    return None
        except Exception as e:
            logger.debug(f"Error converting '{value}' to datetime for column '{col_name}': {e}")
            return None

    async def _sync_bulk_insert(self, df, table_name, schema='public', if_exists='append', index=False, chunk_size=1000):
        """Async wrapper for sync bulk insert."""
        return self._sync_bulk_insert_fallback(df, table_name, schema, if_exists, index, chunk_size)

    def bulk_update(
        self,
        table_name: str,
        update_values: Dict[str, Any],
        condition_dict: Dict[str, Any],
        schema: str = None,
    ) -> int:
        """Perform a bulk update operation.

        Args:
            table_name: Name of the target table.
            update_values: Dictionary of column names and values to update.
            condition_dict: Dictionary of conditions (column name: value).
            schema: Database schema name (optional).

        Returns:
            int: Number of rows updated.
        """
        try:
            with self.session_manager.get_connection() as conn:
                metadata = MetaData()
                table = Table(table_name, metadata, schema=schema, autoload_with=conn)

                # Build the update condition
                conditions = [
                    getattr(table.c, col) == val for col, val in condition_dict.items()
                ]
                combined_condition = conditions[0]
                for condition in conditions[1:]:
                    combined_condition = combined_condition & condition

                # Build the update statement
                update_stmt = (
                    table.update().where(combined_condition).values(update_values)
                )

                # Execute the update
                result = conn.execute(update_stmt)
                return result.rowcount
        except SQLAlchemyError as e:
            # Log the error
            logger.error(f"Bulk update error: {str(e)}")
            return 0

    def bulk_delete(
        self, table_name: str, condition_dict: Dict[str, Any], schema: str = None
    ) -> int:
        """Perform a bulk delete operation.

        Args:
            table_name: Name of the target table.
            condition_dict: Dictionary of conditions (column name: value).
            schema: Database schema name (optional).

        Returns:
            int: Number of rows deleted.
        """
        try:
            with self.session_manager.get_connection() as conn:
                metadata = MetaData()
                table = Table(table_name, metadata, schema=schema, autoload_with=conn)

                # Build the delete condition
                conditions = [
                    getattr(table.c, col) == val for col, val in condition_dict.items()
                ]
                combined_condition = conditions[0]
                for condition in conditions[1:]:
                    combined_condition = combined_condition & condition

                # Build the delete statement
                delete_stmt = table.delete().where(combined_condition)

                # Execute the delete
                result = conn.execute(delete_stmt)
                return result.rowcount
        except SQLAlchemyError as e:
            # Log the error
            logger.error(f"Bulk delete error: {str(e)}")
            return 0

    def truncate_table(self, table_name: str, schema: str = None) -> bool:
        """Truncate a database table (remove all rows).

        Args:
            table_name: Name of the table to truncate.
            schema: Database schema name (optional).

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            with self.session_manager.get_connection() as conn:
                if schema:
                    conn.execute(f"TRUNCATE TABLE {schema}.{table_name}")
                else:
                    conn.execute(f"TRUNCATE TABLE {table_name}")
                return True
        except SQLAlchemyError as e:
            # Log the error
            logger.error(f"Truncate table error: {str(e)}")
            return False

    # Async versions for compatibility with async session managers
    async def bulk_insert_dataframe_async(
        self,
        table_name: str,
        df: pd.DataFrame,
        schema: str = 'public',
        if_exists: str = "append",
        index: bool = False,
        chunk_size: int = 10000,
        **kwargs
    ) -> bool:
        """Async version of bulk_insert_dataframe.

        Args:
            table_name: Name of the target table.
            df: DataFrame to insert.
            schema: Database schema name (defaults to 'public').
            if_exists: How to behave if the table exists ('fail', 'replace', 'append').
            index: Whether to write DataFrame index as a column.
            chunk_size: Number of rows to write at a time.
            **kwargs: Additional keyword arguments.

        Returns:
            bool: True if successful, False otherwise.
        """
        try:
            # Use the real async implementation
            return await self._async_bulk_insert_real(df, table_name, schema, if_exists, index, chunk_size)
        except Exception as e:
            # Handle case where async doesn't work, fall back to sync
            logger.warning(f"Async bulk insert failed, falling back to sync: {str(e)}")
            return self._sync_bulk_insert_fallback(df, table_name, schema, if_exists, index, chunk_size)

    def _execute_sql_sync(self, sql: str) -> bool:
        """Execute SQL synchronously using psycopg2 fallback.

        Args:
            sql: SQL statement to execute

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            import psycopg2
            import os

            # Use environment variables for connection
            host = os.getenv('DATABASE_HOST', 'localhost')
            port = int(os.getenv('DATABASE_PORT', '5432'))
            database = os.getenv('DATABASE_NAME', 'connect')
            user = os.getenv('DATABASE_USER', 'to2')
            password = os.getenv('DATABASE_PASSWORD', 'TO2')

            # Create connection
            conn = psycopg2.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=database
            )

            try:
                with conn.cursor() as cursor:
                    cursor.execute(sql)
                    conn.commit()
                    # Extract operation type and table name for concise logging
                    sql_lines = sql.strip().split('\n')
                    first_line = sql_lines[0].strip()
                    if 'CREATE TABLE' in first_line.upper():
                        # Extract table name from CREATE TABLE statement
                        import re
                        table_match = re.search(r'CREATE TABLE[^"]*"([^"]+)"\."([^"]+)"', first_line, re.IGNORECASE)
                        if table_match:
                            schema_name, table_name = table_match.groups()
                            logger.info(f"Created table {schema_name}.{table_name}")
                        else:
                            logger.info("Created table successfully")
                    else:
                        logger.info(f"Successfully executed SQL: {first_line[:50]}...")
                    return True
            finally:
                conn.close()

        except ImportError:
            logger.warning("psycopg2 not available for sync SQL execution")
            return False
        except Exception as e:
            logger.warning(f"Sync SQL execution failed: {e}")
            return False
