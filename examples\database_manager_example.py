"""Example usage of DatabaseManager.

This example demonstrates how to use the DatabaseManager class
for database-level operations including creation, schema initialization,
and information retrieval.
"""

import asyncio
import logging
from typing import List

from src.database.config import Config
from src.database.connection.pool import ConnectionPool
from src.database.exceptions import DatabaseError, ValidationError
from src.database.operations.database_manager import DatabaseManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def database_manager_example():
    """Demonstrate DatabaseManager functionality."""

    # Load configuration
    config = Config.from_yaml("config/base.yaml")

    # Create connection pool
    pool_manager = ConnectionPool(config)

    try:
        # Initialize connection pool
        await pool_manager.initialize()
        pool = pool_manager.get_pool()

        # Create DatabaseManager instance
        db_manager = DatabaseManager(pool)

        print("=== Database Manager Example ===")

        # 1. Get current database information
        print("\n1. Getting database information...")
        db_info = await db_manager.get_database_info()
        print(f"Database: {db_info['database_name']}")
        print(f"Size: {db_info['database_size']}")
        print(f"Version: {db_info['postgresql_version']}")
        print(f"Encoding: {db_info['encoding']}")
        print(f"Active connections: {db_info['active_connections']}")
        print(f"Schemas: {db_info['schemas']}")

        # 2. List all databases
        print("\n2. Listing all databases...")
        databases = await db_manager.list_databases()
        for db in databases:
            print(f"  - {db['name']} (Owner: {db['owner']}, Size: {db['size']})")

        # 3. Check if a database exists
        print("\n3. Checking database existence...")
        test_db_name = "test_example_db"
        exists = await db_manager.database_exists(test_db_name)
        print(f"Database '{test_db_name}' exists: {exists}")

        # 4. Ensure database exists (will create if needed)
        print("\n4. Ensuring database exists...")
        try:
            success = await db_manager.ensure_database_exists(test_db_name)
            print(f"Database '{test_db_name}' ensured: {success}")
        except DatabaseError as e:
            print(f"Note: Database creation may require superuser privileges: {e}")

        # 5. Initialize schemas
        print("\n5. Initializing schemas...")
        schemas_to_create = ["analytics", "reporting", "staging"]
        schema_results = await db_manager.initialize_schemas(schemas_to_create)

        for schema_name, success in schema_results.items():
            status = "✓" if success else "✗"
            print(f"  {status} Schema '{schema_name}': {success}")

        # 6. Get updated database information
        print("\n6. Updated database information...")
        updated_info = await db_manager.get_database_info()
        print(f"Total schemas: {updated_info['schema_count']}")
        print(f"Schema list: {updated_info['schemas']}")

        print("\n=== Example completed successfully! ===")

    except ValidationError as e:
        logger.error(f"Validation error: {e}")
    except DatabaseError as e:
        logger.error(f"Database error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        # Clean up connection pool
        if "pool_manager" in locals():
            await pool_manager.close()


async def database_creation_example():
    """Example of database creation workflow."""

    config = Config.from_yaml("config/base.yaml")
    pool_manager = ConnectionPool(config)

    try:
        await pool_manager.initialize()
        pool = pool_manager.get_pool()

        db_manager = DatabaseManager(pool)

        print("\n=== Database Creation Workflow ===")

        # Define new database parameters
        new_db_name = "project_analytics"
        db_owner = "analytics_user"
        required_schemas = ["raw_data", "processed", "reports", "temp"]

        print(f"\n1. Creating database '{new_db_name}'...")
        try:
            # Create database with owner
            success = await db_manager.create_database(new_db_name, db_owner)
            print(f"Database creation: {success}")

            # Note: To actually use the new database, you would need to
            # create a new connection pool pointing to that database
            print("\nNote: To work with the new database, create a new connection")
            print("pool with the new database name in the configuration.")

        except DatabaseError as e:
            print(f"Database creation failed (may need superuser privileges): {e}")

        print("\n=== Database creation workflow completed ===")

    except Exception as e:
        logger.error(f"Error in database creation example: {e}")
    finally:
        if "pool_manager" in locals():
            await pool_manager.close()


if __name__ == "__main__":
    # Run the main example
    asyncio.run(database_manager_example())

    # Uncomment to run database creation example
    # asyncio.run(database_creation_example())
