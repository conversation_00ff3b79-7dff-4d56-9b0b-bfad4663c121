"""Unit tests for geospatial processing components."""

import json
from decimal import Decimal
from typing import Any, Dict, List, Tuple
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import geopandas as gpd
import pandas as pd
import pytest
from shapely.geometry import LineString, MultiPolygon, Point, Polygon
from shapely.geometry.base import BaseGeometry

from src.database.exceptions import GeospatialError, ValidationError
# from src.database.geospatial.analyzer import <PERSON>ximityAnaly<PERSON>, SpatialAnalyzer
from src.database.geospatial.indexer import R<PERSON>reeIndexer, SpatialIndexer

# Import components to test
# from src.database.geospatial.processor import CoordinateTransformer, GeospatialProcessor
from src.database.geospatial.validator import CoordinateValidator, GeometryValidator


# class TestGeospatialProcessor:
#     """Test cases for GeospatialProcessor class."""
# 
#     @pytest.fixture
#     def processor(self):
#         """Create GeospatialProcessor instance."""
#         return GeospatialProcessor()

#     @pytest.fixture
#     def sample_point(self):
#         """Create sample point geometry."""
#         return Point(116.3974, 39.9093)  # Beijing coordinates
# 
#     @pytest.fixture
#     def sample_polygon(self):
#         """Create sample polygon geometry."""
#         return Polygon(
#             [(116.3, 39.8), (116.5, 39.8), (116.5, 40.0), (116.3, 40.0), (116.3, 39.8)]
#         )
# 
#     @pytest.fixture
#     def sample_geojson(self):
#         """Create sample GeoJSON data."""
#         return {
#             "type": "Feature",
#             "geometry": {"type": "Point", "coordinates": [116.3974, 39.9093]},
#             "properties": {"name": "Beijing", "population": 21540000},
#         }
# 
#     def test_point_creation(self, processor):
#         """Test creating point geometry."""
#         point = processor.create_point(116.3974, 39.9093)
# 
#         assert isinstance(point, Point)
#         assert point.x == 116.3974
#         assert point.y == 39.9093
# 
#     def test_polygon_creation(self, processor):
#         """Test creating polygon geometry."""
#         coordinates = [
#             (116.3, 39.8),
#             (116.5, 39.8),
#             (116.5, 40.0),
#             (116.3, 40.0),
#             (116.3, 39.8),
#         ]
# 
#         polygon = processor.create_polygon(coordinates)
# 
#         assert isinstance(polygon, Polygon)
#         assert polygon.is_valid
#         assert len(polygon.exterior.coords) == 5  # Including closing coordinate
# 
#     def test_linestring_creation(self, processor):
#         """Test creating linestring geometry."""
#         coordinates = [(116.3, 39.8), (116.4, 39.9), (116.5, 40.0)]
# 
#         linestring = processor.create_linestring(coordinates)

#         assert isinstance(linestring, LineString)
#         assert linestring.is_valid
#         assert len(linestring.coords) == 3
# 
#     def test_geometry_from_wkt(self, processor):
#         """Test creating geometry from WKT string."""
#         wkt = "POINT(116.3974 39.9093)"
# 
#         geometry = processor.from_wkt(wkt)
# 
#         assert isinstance(geometry, Point)
#         assert geometry.x == 116.3974
#         assert geometry.y == 39.9093
# 
#     def test_geometry_to_wkt(self, processor, sample_point):
#         """Test converting geometry to WKT string."""
#         wkt = processor.to_wkt(sample_point)
# 
#         assert isinstance(wkt, str)
#         assert "POINT" in wkt
#         assert "116.3974" in wkt
#         assert "39.9093" in wkt
# 
#     def test_geometry_from_geojson(self, processor, sample_geojson):
#         """Test creating geometry from GeoJSON."""
#         geometry = processor.from_geojson(sample_geojson["geometry"])
# 
#         assert isinstance(geometry, Point)
#         assert geometry.x == 116.3974
#         assert geometry.y == 39.9093
# 
#     def test_geometry_to_geojson(self, processor, sample_point):
#         """Test converting geometry to GeoJSON."""
#         geojson = processor.to_geojson(sample_point)
# 
#         assert geojson["type"] == "Point"
#         assert geojson["coordinates"] == [116.3974, 39.9093]
# 
#     def test_geometry_buffer(self, processor, sample_point):
#         """Test creating buffer around geometry."""
#         buffer_distance = 0.01  # degrees
# 
#         buffered = processor.buffer(sample_point, buffer_distance)
# 
#         assert isinstance(buffered, Polygon)
#         assert buffered.area > 0
#         assert buffered.contains(sample_point)
# 
#     def test_geometry_intersection(self, processor, sample_polygon):
#         """Test geometry intersection."""
#         other_polygon = Polygon(
#             [(116.4, 39.9), (116.6, 39.9), (116.6, 40.1), (116.4, 40.1), (116.4, 39.9)]
#         )
# 
#         intersection = processor.intersection(sample_polygon, other_polygon)
# 
#         assert isinstance(intersection, Polygon)
#         assert intersection.area > 0
#         assert intersection.area < sample_polygon.area
#         assert intersection.area < other_polygon.area
# 
#     def test_geometry_union(self, processor, sample_polygon):
#         """Test geometry union."""
#         other_polygon = Polygon(
#             [(116.4, 39.9), (116.6, 39.9), (116.6, 40.1), (116.4, 40.1), (116.4, 39.9)]
#         )
# 
#         union = processor.union(sample_polygon, other_polygon)

#         assert isinstance(union, (Polygon, MultiPolygon))
#         assert union.area > sample_polygon.area
#         assert union.area > other_polygon.area
# 
#     def test_geometry_difference(self, processor, sample_polygon):
#         """Test geometry difference."""
#         smaller_polygon = Polygon(
#             [
#                 (116.35, 39.85),
#                 (116.45, 39.85),
#                 (116.45, 39.95),
#                 (116.35, 39.95),
#                 (116.35, 39.85),
#             ]
#         )
# 
#         difference = processor.difference(sample_polygon, smaller_polygon)
# 
#         assert isinstance(difference, Polygon)
#         assert difference.area < sample_polygon.area
# 
#     def test_distance_calculation(self, processor):
#         """Test distance calculation between geometries."""
#         point1 = Point(116.3974, 39.9093)  # Beijing
#         point2 = Point(121.4737, 31.2304)  # Shanghai
# 
#         distance = processor.distance(point1, point2)
# 
#         assert distance > 0
#         assert isinstance(distance, float)
#         # Distance between Beijing and Shanghai should be significant
#         assert distance > 5  # degrees
# 
#     def test_area_calculation(self, processor, sample_polygon):
#         """Test area calculation for polygon."""
#         area = processor.area(sample_polygon)
# 
#         assert area > 0
#         assert isinstance(area, float)
# 
#     def test_length_calculation(self, processor):
#         """Test length calculation for linestring."""
#         linestring = LineString([(116.3, 39.8), (116.4, 39.9), (116.5, 40.0)])
# 
#         length = processor.length(linestring)
# 
#         assert length > 0
#         assert isinstance(length, float)
# 
#     def test_centroid_calculation(self, processor, sample_polygon):
#         """Test centroid calculation."""
#         centroid = processor.centroid(sample_polygon)
# 
#         assert isinstance(centroid, Point)
#         assert sample_polygon.contains(centroid) or sample_polygon.touches(centroid)
# 
#     def test_bounds_calculation(self, processor, sample_polygon):
#         """Test bounds calculation."""
#         bounds = processor.bounds(sample_polygon)
# 
#         assert len(bounds) == 4  # minx, miny, maxx, maxy
#         assert bounds[0] <= bounds[2]  # minx <= maxx
#         assert bounds[1] <= bounds[3]  # miny <= maxy
# 
#     def test_contains_check(self, processor, sample_polygon):
#         """Test contains relationship check."""
#         inner_point = Point(116.4, 39.9)
#         outer_point = Point(117.0, 40.5)
# 
#         assert processor.contains(sample_polygon, inner_point)
#         assert not processor.contains(sample_polygon, outer_point)
# 
#     def test_intersects_check(self, processor, sample_polygon):
#         """Test intersects relationship check."""
#         intersecting_polygon = Polygon(
#             [(116.4, 39.9), (116.6, 39.9), (116.6, 40.1), (116.4, 40.1), (116.4, 39.9)]
#         )
# 
#         non_intersecting_polygon = Polygon(
#             [(117.0, 40.5), (117.2, 40.5), (117.2, 40.7), (117.0, 40.7), (117.0, 40.5)]
#         )
# 
#         assert processor.intersects(sample_polygon, intersecting_polygon)
#         assert not processor.intersects(sample_polygon, non_intersecting_polygon)
# 
#     def test_simplify_geometry(self, processor):
#         """Test geometry simplification."""
#         complex_linestring = LineString(
#             [
#                 (0, 0),
#                 (0.1, 0.1),
#                 (0.2, 0.05),
#                 (0.3, 0.15),
#                 (0.4, 0.1),
#                 (0.5, 0.2),
#                 (1, 0),
#             ]
#         )
# 
#         simplified = processor.simplify(complex_linestring, tolerance=0.1)
# 
#         assert isinstance(simplified, LineString)
#         assert len(simplified.coords) <= len(complex_linestring.coords)
# 
#     def test_invalid_geometry_handling(self, processor):
#         """Test handling of invalid geometries."""
#         # Create invalid polygon (self-intersecting)
#         invalid_coords = [(0, 0), (1, 1), (1, 0), (0, 1), (0, 0)]
# 
#         with pytest.raises(GeospatialError):
#             processor.create_polygon(invalid_coords, validate=True)
# 
#     def test_empty_geometry_handling(self, processor):
#         """Test handling of empty geometries."""
#         empty_point = Point()
# 
#         assert processor.is_empty(empty_point)
#         assert not processor.is_valid(empty_point)


# class TestCoordinateTransformer:
#     """Test cases for CoordinateTransformer class."""
# 
#     @pytest.fixture
#     def transformer(self):
#         """Create CoordinateTransformer instance."""
#         return CoordinateTransformer()
# 
#     def test_wgs84_to_web_mercator(self, transformer):
#         """Test transformation from WGS84 to Web Mercator."""
#         # Beijing coordinates in WGS84
#         lon, lat = 116.3974, 39.9093
# 
#         x, y = transformer.transform_coordinates(
#             lon,
#             lat,
#             source_crs="EPSG:4326",  # WGS84
#             target_crs="EPSG:3857",  # Web Mercator
#         )
# 
#         assert x != lon  # Should be transformed
#         assert y != lat
#         assert abs(x) > abs(lon)  # Web Mercator has larger values
#         assert abs(y) > abs(lat)
# 
#     def test_web_mercator_to_wgs84(self, transformer):
#         """Test transformation from Web Mercator to WGS84."""
#         # Beijing coordinates in Web Mercator (approximate)
#         x, y = 12958019.0, 4825923.0
# 
#         lon, lat = transformer.transform_coordinates(
#             x,
#             y,
#             source_crs="EPSG:3857",  # Web Mercator
#             target_crs="EPSG:4326",  # WGS84
#         )
# 
#         assert abs(lon - 116.3974) < 0.01  # Should be close to original
#         assert abs(lat - 39.9093) < 0.01
# 
#     def test_transform_geometry(self, transformer):
#         """Test transforming geometry objects."""
#         # Create point in WGS84
#         point_wgs84 = Point(116.3974, 39.9093)
# 
#         point_mercator = transformer.transform_geometry(
#             point_wgs84, source_crs="EPSG:4326", target_crs="EPSG:3857"
#         )
# 
#         assert isinstance(point_mercator, Point)
#         assert point_mercator.x != point_wgs84.x
#         assert point_mercator.y != point_wgs84.y
# 
#     def test_transform_polygon(self, transformer):
#         """Test transforming polygon geometry."""
#         polygon_wgs84 = Polygon(
#             [(116.3, 39.8), (116.5, 39.8), (116.5, 40.0), (116.3, 40.0), (116.3, 39.8)]
#         )
# 
#         polygon_mercator = transformer.transform_geometry(
#             polygon_wgs84, source_crs="EPSG:4326", target_crs="EPSG:3857"
#         )
# 
#         assert isinstance(polygon_mercator, Polygon)
#         assert polygon_mercator.area != polygon_wgs84.area
# 
#     def test_batch_coordinate_transformation(self, transformer):
#         """Test batch transformation of multiple coordinates."""
#         coordinates = [
#             (116.3974, 39.9093),  # Beijing
#             (121.4737, 31.2304),  # Shanghai
#             (113.2644, 23.1291),  # Guangzhou
#         ]
# 
#         transformed = transformer.transform_coordinates_batch(
#             coordinates, source_crs="EPSG:4326", target_crs="EPSG:3857"
#         )
# 
#         assert len(transformed) == len(coordinates)
#         for i, (x, y) in enumerate(transformed):
#             assert x != coordinates[i][0]
#             assert y != coordinates[i][1]
# 
#     def test_invalid_crs_handling(self, transformer):
#         """Test handling of invalid CRS codes."""
#         with pytest.raises(GeospatialError):
#             transformer.transform_coordinates(
#                 116.3974, 39.9093, source_crs="INVALID:1234", target_crs="EPSG:4326"
#             )
# 
#     def test_coordinate_precision(self, transformer):
#         """Test coordinate precision preservation."""
#         # High precision coordinates
#         lon, lat = 116.397428, 39.909264
# 
#         x, y = transformer.transform_coordinates(
#             lon, lat, source_crs="EPSG:4326", target_crs="EPSG:3857"
#         )
# 
#         # Transform back
#         lon_back, lat_back = transformer.transform_coordinates(
#             x, y, source_crs="EPSG:3857", target_crs="EPSG:4326"
#         )
# 
#         # Should preserve reasonable precision
#         assert abs(lon_back - lon) < 0.000001
#         assert abs(lat_back - lat) < 0.000001


# class TestSpatialAnalyzer:
#     """Test cases for SpatialAnalyzer class."""
# 
#     @pytest.fixture
#     def analyzer(self):
#         """Create SpatialAnalyzer instance."""
#         return SpatialAnalyzer()
# 
#     @pytest.fixture
#     def sample_points(self):
#         """Create sample point dataset."""
#         return [
#             Point(116.3974, 39.9093),  # Beijing
#             Point(121.4737, 31.2304),  # Shanghai
#             Point(113.2644, 23.1291),  # Guangzhou
#             Point(114.0579, 22.5431),  # Shenzhen
#             Point(108.9480, 34.2588),  # Xi'an
#         ]
# 
#     @pytest.fixture
#     def sample_polygons(self):
#         """Create sample polygon dataset."""
#         return [
#             Polygon(
#                 [
#                     (116.3, 39.8),
#                     (116.5, 39.8),
#                     (116.5, 40.0),
#                     (116.3, 40.0),
#                     (116.3, 39.8),
#                 ]
#             ),
#             Polygon(
#                 [
#                     (121.4, 31.1),
#                     (121.6, 31.1),
#                     (121.6, 31.3),
#                     (121.4, 31.3),
#                     (121.4, 31.1),
#                 ]
#             ),
#             Polygon(
#                 [
#                     (113.2, 23.0),
#                     (113.4, 23.0),
#                     (113.4, 23.2),
#                     (113.2, 23.2),
#                     (113.2, 23.0),
#                 ]
#             ),
#         ]
# 
#     def test_nearest_neighbor_analysis(self, analyzer, sample_points):
#         """Test nearest neighbor analysis."""
#         query_point = Point(116.4, 39.9)  # Near Beijing
# 
#         nearest = analyzer.find_nearest_neighbor(query_point, sample_points)
# 
#         assert nearest is not None
#         assert isinstance(nearest, Point)
#         # Should be Beijing (closest to query point)
#         assert abs(nearest.x - 116.3974) < 0.1
#         assert abs(nearest.y - 39.9093) < 0.1
# 
#     def test_k_nearest_neighbors(self, analyzer, sample_points):
#         """Test k-nearest neighbors analysis."""
#         query_point = Point(116.4, 39.9)
#         k = 3
# 
#         neighbors = analyzer.find_k_nearest_neighbors(query_point, sample_points, k)
# 
#         assert len(neighbors) == k
#         assert all(isinstance(point, Point) for point in neighbors)
# 
#         # First neighbor should be Beijing (closest)
#         assert abs(neighbors[0].x - 116.3974) < 0.1
#         assert abs(neighbors[0].y - 39.9093) < 0.1
# 
#     def test_spatial_clustering(self, analyzer, sample_points):
#         """Test spatial clustering analysis."""
#         clusters = analyzer.spatial_clustering(sample_points, eps=5.0, min_samples=1)
# 
#         assert len(clusters) > 0
#         assert all(isinstance(cluster, list) for cluster in clusters)
# 
#         # Total points in clusters should equal input points
#         total_clustered = sum(len(cluster) for cluster in clusters)
#         assert total_clustered == len(sample_points)
# 
#     def test_convex_hull_analysis(self, analyzer, sample_points):
#         """Test convex hull analysis."""
#         hull = analyzer.convex_hull(sample_points)
# 
#         assert isinstance(hull, Polygon)
#         assert hull.is_valid
# 
#         # All points should be within or on the hull
#         for point in sample_points:
#             assert hull.contains(point) or hull.touches(point)
# 
#     def test_spatial_join_analysis(self, analyzer, sample_points, sample_polygons):
#         """Test spatial join analysis."""
#         # Create GeoDataFrames
#         points_gdf = gpd.GeoDataFrame(
#             {"id": range(len(sample_points)), "geometry": sample_points}
#         )
#         polygons_gdf = gpd.GeoDataFrame(
#             {"region_id": range(len(sample_polygons)), "geometry": sample_polygons}
#         )
# 
#         joined = analyzer.spatial_join(
#             points_gdf, polygons_gdf, how="inner", op="within"
#         )
# 
#         assert isinstance(joined, gpd.GeoDataFrame)
#         assert "region_id" in joined.columns
#         assert len(joined) <= len(sample_points)
# 
#     def test_buffer_analysis(self, analyzer, sample_points):
#         """Test buffer analysis."""
#         buffer_distance = 1.0  # degrees
# 
#         buffers = analyzer.create_buffers(sample_points, buffer_distance)
# 
#         assert len(buffers) == len(sample_points)
#         assert all(isinstance(buffer, Polygon) for buffer in buffers)
# 
#         # Each buffer should contain its original point
#         for i, buffer in enumerate(buffers):
#             assert buffer.contains(sample_points[i])
# 
#     def test_intersection_analysis(self, analyzer, sample_polygons):
#         """Test intersection analysis."""
#         # Create overlapping polygon
#         test_polygon = Polygon(
#             [(116.4, 39.9), (121.5, 31.2), (113.3, 23.1), (116.4, 39.9)]
#         )
# 
#         intersections = analyzer.find_intersections(test_polygon, sample_polygons)
# 
#         assert isinstance(intersections, list)
#         assert all(
#             isinstance(intersection, BaseGeometry)
#             for intersection in intersections
#             if intersection is not None
#         )
# 
#     def test_area_calculation_analysis(self, analyzer, sample_polygons):
#         """Test area calculation analysis."""
#         areas = analyzer.calculate_areas(sample_polygons)
# 
#         assert len(areas) == len(sample_polygons)
#         assert all(area > 0 for area in areas)
#         assert all(isinstance(area, float) for area in areas)
# 
#     def test_distance_matrix_analysis(self, analyzer, sample_points):
#         """Test distance matrix analysis."""
#         distance_matrix = analyzer.calculate_distance_matrix(sample_points)
# 
#         assert distance_matrix.shape == (len(sample_points), len(sample_points))
# 
#         # Diagonal should be zero (distance from point to itself)
#         for i in range(len(sample_points)):
#             assert distance_matrix[i][i] == 0
# 
#         # Matrix should be symmetric
#         for i in range(len(sample_points)):
#             for j in range(len(sample_points)):
#                 assert abs(distance_matrix[i][j] - distance_matrix[j][i]) < 1e-10
# 
#     def test_spatial_statistics(self, analyzer, sample_points):
#         """Test spatial statistics calculation."""
#         stats = analyzer.calculate_spatial_statistics(sample_points)
# 
#         assert "count" in stats
#         assert "centroid" in stats
#         assert "bounding_box" in stats
#         assert "total_area" in stats or "convex_hull_area" in stats
# 
#         assert stats["count"] == len(sample_points)
#         assert isinstance(stats["centroid"], Point)


class TestGeometryValidator:
    """Test cases for GeometryValidator class."""

    @pytest.fixture
    def validator(self):
        """Create GeometryValidator instance."""
        return GeometryValidator()

    def test_valid_point_validation(self, validator):
        """Test validation of valid point geometry."""
        valid_point = Point(116.3974, 39.9093)

        is_valid, errors = validator.validate_geometry(valid_point)

        assert is_valid
        assert len(errors) == 0

    def test_invalid_point_validation(self, validator):
        """Test validation of invalid point geometry."""
        invalid_point = Point()  # Empty point

        is_valid, errors = validator.validate_geometry(invalid_point)

        assert not is_valid
        assert len(errors) > 0
        assert any("empty" in error.lower() for error in errors)

    def test_valid_polygon_validation(self, validator):
        """Test validation of valid polygon geometry."""
        valid_polygon = Polygon([(0, 0), (1, 0), (1, 1), (0, 1), (0, 0)])

        is_valid, errors = validator.validate_geometry(valid_polygon)

        assert is_valid
        assert len(errors) == 0

    def test_invalid_polygon_validation(self, validator):
        """Test validation of invalid polygon geometry."""
        # Self-intersecting polygon
        invalid_polygon = Polygon([(0, 0), (1, 1), (1, 0), (0, 1), (0, 0)])

        is_valid, errors = validator.validate_geometry(invalid_polygon)

        assert not is_valid
        assert len(errors) > 0

    def test_coordinate_range_validation(self, validator):
        """Test coordinate range validation."""
        # Valid coordinates
        valid_point = Point(116.3974, 39.9093)
        is_valid, _ = validator.validate_coordinate_range(valid_point)
        assert is_valid

        # Invalid longitude
        invalid_lon_point = Point(200.0, 39.9093)
        is_valid, errors = validator.validate_coordinate_range(invalid_lon_point)
        assert not is_valid
        assert any("longitude" in error.lower() for error in errors)

        # Invalid latitude
        invalid_lat_point = Point(116.3974, 100.0)
        is_valid, errors = validator.validate_coordinate_range(invalid_lat_point)
        assert not is_valid
        assert any("latitude" in error.lower() for error in errors)

    def test_geometry_complexity_validation(self, validator):
        """Test geometry complexity validation."""
        # Simple geometry
        simple_polygon = Polygon([(0, 0), (1, 0), (1, 1), (0, 1), (0, 0)])

        is_valid, _ = validator.validate_complexity(simple_polygon, max_vertices=100)
        assert is_valid

        # Complex geometry (too many vertices)
        complex_coords = [(i, 0) for i in range(200)] + [(199, 1), (0, 1), (0, 0)]
        complex_polygon = Polygon(complex_coords)

        is_valid, errors = validator.validate_complexity(
            complex_polygon, max_vertices=100
        )
        assert not is_valid
        assert any(
            "complex" in error.lower() or "vertices" in error.lower()
            for error in errors
        )

    def test_geometry_area_validation(self, validator):
        """Test geometry area validation."""
        # Small valid polygon
        small_polygon = Polygon([(0, 0), (0.1, 0), (0.1, 0.1), (0, 0.1), (0, 0)])

        is_valid, _ = validator.validate_area(
            small_polygon, min_area=0.001, max_area=1.0
        )
        assert is_valid

        # Too small polygon
        tiny_polygon = Polygon(
            [(0, 0), (0.0001, 0), (0.0001, 0.0001), (0, 0.0001), (0, 0)]
        )

        is_valid, errors = validator.validate_area(
            tiny_polygon, min_area=0.001, max_area=1.0
        )
        assert not is_valid
        assert any("area" in error.lower() for error in errors)

    def test_geometry_topology_validation(self, validator):
        """Test geometry topology validation."""
        # Valid topology
        valid_polygon = Polygon([(0, 0), (1, 0), (1, 1), (0, 1), (0, 0)])

        is_valid, _ = validator.validate_topology(valid_polygon)
        assert is_valid

        # Invalid topology (self-intersection)
        invalid_polygon = Polygon([(0, 0), (1, 1), (1, 0), (0, 1), (0, 0)])

        is_valid, errors = validator.validate_topology(invalid_polygon)
        assert not is_valid
        assert len(errors) > 0

    def test_batch_geometry_validation(self, validator):
        """Test batch validation of multiple geometries."""
        geometries = [
            Point(116.3974, 39.9093),  # Valid
            Point(200.0, 39.9093),  # Invalid longitude
            Polygon([(0, 0), (1, 0), (1, 1), (0, 1), (0, 0)]),  # Valid
            Polygon(
                [(0, 0), (1, 1), (1, 0), (0, 1), (0, 0)]
            ),  # Invalid (self-intersecting)
        ]

        results = validator.validate_batch(geometries)

        assert len(results) == len(geometries)
        assert results[0]["is_valid"]  # Valid point
        assert not results[1]["is_valid"]  # Invalid longitude
        assert results[2]["is_valid"]  # Valid polygon
        assert not results[3]["is_valid"]  # Invalid polygon

    def test_custom_validation_rules(self, validator):
        """Test custom validation rules."""

        def custom_rule(geometry):
            """Custom rule: geometry must be within China bounds (approximate)."""
            bounds = geometry.bounds
            china_bounds = (73.0, 18.0, 135.0, 54.0)  # Approximate China bounds

            if (
                bounds[0] >= china_bounds[0]
                and bounds[1] >= china_bounds[1]
                and bounds[2] <= china_bounds[2]
                and bounds[3] <= china_bounds[3]
            ):
                return True, []
            else:
                return False, ["Geometry is outside China bounds"]

        validator.add_custom_rule("china_bounds", custom_rule)

        # Point in China
        china_point = Point(116.3974, 39.9093)  # Beijing
        is_valid, _ = validator.validate_geometry(china_point)
        assert is_valid

        # Point outside China
        outside_point = Point(-74.0060, 40.7128)  # New York
        is_valid, errors = validator.validate_geometry(outside_point)
        assert not is_valid
        assert any("China bounds" in error for error in errors)


class TestSpatialIndexer:
    """Test cases for SpatialIndexer class."""

    @pytest.fixture
    def indexer(self):
        """Create SpatialIndexer instance."""
        return SpatialIndexer()

    @pytest.fixture
    def sample_geometries_with_ids(self):
        """Create sample geometries with IDs for indexing."""
        return [
            (1, Point(116.3974, 39.9093)),  # Beijing
            (2, Point(121.4737, 31.2304)),  # Shanghai
            (3, Point(113.2644, 23.1291)),  # Guangzhou
            (
                4,
                Polygon(
                    [
                        (116.3, 39.8),
                        (116.5, 39.8),
                        (116.5, 40.0),
                        (116.3, 40.0),
                        (116.3, 39.8),
                    ]
                ),
            ),
            (
                5,
                Polygon(
                    [
                        (121.4, 31.1),
                        (121.6, 31.1),
                        (121.6, 31.3),
                        (121.4, 31.3),
                        (121.4, 31.1),
                    ]
                ),
            ),
        ]

    def test_index_creation(self, indexer, sample_geometries_with_ids):
        """Test spatial index creation."""
        for geom_id, geometry in sample_geometries_with_ids:
            indexer.insert(geom_id, geometry)

        assert indexer.size() == len(sample_geometries_with_ids)

    def test_point_query(self, indexer, sample_geometries_with_ids):
        """Test point-based spatial query."""
        # Build index
        for geom_id, geometry in sample_geometries_with_ids:
            indexer.insert(geom_id, geometry)

        # Query near Beijing
        query_point = Point(116.4, 39.9)
        results = indexer.query_point(query_point, radius=0.5)

        assert len(results) > 0
        assert 1 in results  # Beijing point should be found
        assert 4 in results  # Beijing polygon should be found

    def test_bbox_query(self, indexer, sample_geometries_with_ids):
        """Test bounding box spatial query."""
        # Build index
        for geom_id, geometry in sample_geometries_with_ids:
            indexer.insert(geom_id, geometry)

        # Query Beijing area
        bbox = (116.0, 39.5, 117.0, 40.5)
        results = indexer.query_bbox(bbox)

        assert len(results) > 0
        assert 1 in results  # Beijing point
        assert 4 in results  # Beijing polygon
        assert 2 not in results  # Shanghai should not be in results

    def test_geometry_query(self, indexer, sample_geometries_with_ids):
        """Test geometry-based spatial query."""
        # Build index
        for geom_id, geometry in sample_geometries_with_ids:
            indexer.insert(geom_id, geometry)

        # Query with polygon that covers Beijing area
        query_polygon = Polygon(
            [(116.0, 39.5), (117.0, 39.5), (117.0, 40.5), (116.0, 40.5), (116.0, 39.5)]
        )

        results = indexer.query_geometry(query_polygon)

        assert len(results) > 0
        assert 1 in results  # Beijing point
        assert 4 in results  # Beijing polygon

    def test_nearest_neighbor_query(self, indexer, sample_geometries_with_ids):
        """Test nearest neighbor query."""
        # Build index
        for geom_id, geometry in sample_geometries_with_ids:
            indexer.insert(geom_id, geometry)

        # Find nearest to a point near Beijing
        query_point = Point(116.4, 39.9)
        nearest = indexer.query_nearest(query_point, k=2)

        assert len(nearest) == 2
        assert 1 in nearest  # Beijing point should be nearest

    def test_index_update(self, indexer, sample_geometries_with_ids):
        """Test updating geometries in the index."""
        # Build initial index
        for geom_id, geometry in sample_geometries_with_ids[:3]:
            indexer.insert(geom_id, geometry)

        assert indexer.size() == 3

        # Update geometry
        new_geometry = Point(120.0, 35.0)
        indexer.update(1, new_geometry)

        # Query should find updated geometry
        bbox = (119.0, 34.0, 121.0, 36.0)
        results = indexer.query_bbox(bbox)
        assert 1 in results

    def test_index_deletion(self, indexer, sample_geometries_with_ids):
        """Test deleting geometries from the index."""
        # Build index
        for geom_id, geometry in sample_geometries_with_ids:
            indexer.insert(geom_id, geometry)

        initial_size = indexer.size()

        # Delete geometry
        indexer.delete(1)

        assert indexer.size() == initial_size - 1

        # Query should not find deleted geometry
        bbox = (116.0, 39.0, 117.0, 40.0)
        results = indexer.query_bbox(bbox)
        assert 1 not in results

    def test_index_performance(self, indexer):
        """Test index performance with large dataset."""
        import time

        # Create large dataset
        large_dataset = []
        for i in range(1000):
            x = 116.0 + (i % 100) * 0.01
            y = 39.0 + (i // 100) * 0.01
            large_dataset.append((i, Point(x, y)))

        # Measure insertion time
        start_time = time.time()
        for geom_id, geometry in large_dataset:
            indexer.insert(geom_id, geometry)
        insertion_time = time.time() - start_time

        # Measure query time
        start_time = time.time()
        bbox = (116.0, 39.0, 117.0, 40.0)
        results = indexer.query_bbox(bbox)
        query_time = time.time() - start_time

        # Performance should be reasonable
        assert insertion_time < 5.0  # Should insert 1000 points in under 5 seconds
        assert query_time < 1.0  # Should query in under 1 second
        assert len(results) > 0

    def test_index_persistence(self, indexer, sample_geometries_with_ids, tmp_path):
        """Test saving and loading spatial index."""
        # Build index
        for geom_id, geometry in sample_geometries_with_ids:
            indexer.insert(geom_id, geometry)

        # Save index
        index_file = tmp_path / "spatial_index.pkl"
        indexer.save(str(index_file))

        # Create new indexer and load
        new_indexer = SpatialIndexer()
        new_indexer.load(str(index_file))

        # Should have same size and query results
        assert new_indexer.size() == indexer.size()

        bbox = (116.0, 39.0, 117.0, 40.0)
        original_results = indexer.query_bbox(bbox)
        loaded_results = new_indexer.query_bbox(bbox)

        assert set(original_results) == set(loaded_results)
