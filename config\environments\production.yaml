# 生产环境配置
# 生产环境的安全和性能优化设置

database:
  host: ${DATABASE_HOST}
  port: ${DATABASE_PORT:5432}
  name: ${DATABASE_NAME}
  user: ${DATABASE_USER}
  password: ${DATABASE_PASSWORD}
  ssl_mode: require
  pool:
    min_size: 10
    max_size: 50
    command_timeout: 120

# 电信域生产配置
telecom:
  cdr:
    batch_size: 20000  # 生产环境使用更大批次
    validation_enabled: true
  ep:
    batch_size: 10000
  kpi:
    calculation_interval: 300
    real_time_enabled: true
  performance:
    max_memory_usage_mb: 4096
    processing_timeout_seconds: 7200
    parallel_workers: 8

logging:
  level: INFO
  handlers:
    console:
      level: WARNING
    file:
      level: INFO

debug: false
testing: false

security:
  jwt:
    access_token_expire_minutes: 15
    secret_key: ${JWT_SECRET_KEY}  # 生产环境必须设置
  password:
    min_length: 12  # 生产环境更严格的密码要求
