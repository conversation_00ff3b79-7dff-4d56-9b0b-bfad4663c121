name: Comprehensive Testing Framework

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'
  POSTGRES_VERSION: '15'
  TEST_DATABASE_URL: 'postgresql://test_user:test_password@localhost:5432/test_connect'

jobs:
  # Code Quality Checks
  code-quality:
    runs-on: ubuntu-latest
    name: Code Quality Analysis
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        
    - name: Run code formatting check (black)
      run: black --check --diff src/ tests/
      
    - name: Run import sorting check (isort)
      run: isort --check-only --diff src/ tests/
      
    - name: Run linting (flake8)
      run: flake8 src/ tests/
      
    - name: Run type checking (mypy)
      run: mypy src/
      
    - name: Run security analysis (bandit)
      run: bandit -r src/ -f json -o bandit-report.json
      
    - name: Upload security report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-report
        path: bandit-report.json

  # Unit Tests
  unit-tests:
    runs-on: ubuntu-latest
    name: Unit Tests
    needs: code-quality
    
    strategy:
      matrix:
        python-version: ['3.10', '3.11', '3.12']
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-${{ matrix.python-version }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-${{ matrix.python-version }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        
    - name: Run unit tests
      run: |
        pytest tests/unit/ \
          --cov=src \
          --cov-report=xml \
          --cov-report=html \
          --cov-report=term-missing \
          --cov-fail-under=80 \
          --junitxml=junit-unit-${{ matrix.python-version }}.xml \
          --maxfail=5 \
          -v
          
    - name: Upload unit test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-results-${{ matrix.python-version }}
        path: |
          junit-unit-${{ matrix.python-version }}.xml
          htmlcov/
          coverage.xml

  # Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    name: Integration Tests
    needs: unit-tests
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_connect
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        
    - name: Wait for PostgreSQL
      run: |
        until pg_isready -h localhost -p 5432 -U test_user; do
          echo "Waiting for PostgreSQL..."
          sleep 2
        done
        
    - name: Set up test database
      run: |
        export DATABASE_URL=${{ env.TEST_DATABASE_URL }}
        python -c "from src.database.config import load_config; from src.database.connection.session import SessionManager; import asyncio; asyncio.run(SessionManager(load_config()).connect())"
        
    - name: Run integration tests
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
      run: |
        pytest tests/integration/ \
          --cov=src \
          --cov-report=xml \
          --cov-report=html \
          --cov-report=term-missing \
          --junitxml=junit-integration.xml \
          --maxfail=3 \
          -v
          
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-results
        path: |
          junit-integration.xml
          htmlcov/
          coverage.xml

  # Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    name: Performance Tests
    needs: integration-tests
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_connect
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        
    - name: Wait for PostgreSQL
      run: |
        until pg_isready -h localhost -p 5432 -U test_user; do
          echo "Waiting for PostgreSQL..."
          sleep 2
        done
        
    - name: Run performance tests
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
      run: |
        pytest tests/performance/ \
          --junitxml=junit-performance.xml \
          --benchmark-json=benchmark-results.json \
          -v
          
    - name: Upload performance test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-test-results
        path: |
          junit-performance.xml
          benchmark-results.json

  # End-to-End Tests
  e2e-tests:
    runs-on: ubuntu-latest
    name: End-to-End Tests
    needs: integration-tests
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: test_connect
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        
    - name: Wait for PostgreSQL
      run: |
        until pg_isready -h localhost -p 5432 -U test_user; do
          echo "Waiting for PostgreSQL..."
          sleep 2
        done
        
    - name: Run end-to-end tests
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
      run: |
        pytest tests/e2e/ \
          --junitxml=junit-e2e.xml \
          -v
          
    - name: Upload e2e test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: junit-e2e.xml

  # Test Coverage Report
  coverage-report:
    runs-on: ubuntu-latest
    name: Coverage Report
    needs: [unit-tests, integration-tests]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download unit test artifacts
      uses: actions/download-artifact@v3
      with:
        name: unit-test-results-3.11
        path: unit-coverage/
        
    - name: Download integration test artifacts
      uses: actions/download-artifact@v3
      with:
        name: integration-test-results
        path: integration-coverage/
        
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install coverage tools
      run: |
        python -m pip install --upgrade pip
        pip install coverage[toml] codecov
        
    - name: Combine coverage reports
      run: |
        coverage combine unit-coverage/coverage.xml integration-coverage/coverage.xml
        coverage xml -o combined-coverage.xml
        coverage html -d combined-htmlcov
        coverage report --show-missing
        
    - name: Upload combined coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./combined-coverage.xml
        flags: unittests,integration
        name: codecov-umbrella
        fail_ci_if_error: false
        
    - name: Upload combined coverage report
      uses: actions/upload-artifact@v3
      with:
        name: combined-coverage-report
        path: |
          combined-coverage.xml
          combined-htmlcov/

  # Security Scan
  security-scan:
    runs-on: ubuntu-latest
    name: Security Vulnerability Scan
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install safety semgrep
        
    - name: Run dependency vulnerability scan
      run: |
        pip freeze | safety check --json --output safety-report.json
        
    - name: Run static analysis security scan
      run: |
        semgrep --config=auto --json --output=semgrep-report.json src/
        
    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-scan-results
        path: |
          safety-report.json
          semgrep-report.json

  # Test Summary
  test-summary:
    runs-on: ubuntu-latest
    name: Test Summary
    needs: [unit-tests, integration-tests, performance-tests, e2e-tests, coverage-report, security-scan]
    if: always()
    
    steps:
    - name: Test Results Summary
      run: |
        echo "## Test Execution Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "| Test Type | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|-----------|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Unit Tests | ${{ needs.unit-tests.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Integration Tests | ${{ needs.integration-tests.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Performance Tests | ${{ needs.performance-tests.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| E2E Tests | ${{ needs.e2e-tests.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Coverage Report | ${{ needs.coverage-report.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Security Scan | ${{ needs.security-scan.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Workflow completed at:** $(date)" >> $GITHUB_STEP_SUMMARY