# -*- coding: utf-8 -*-
"""
Adapter Factory for Data Processing

This module provides a factory for creating data processing adapters
with automatic engine selection based on data characteristics.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional, Type, Union

from .base_adapter import BaseAdapter
from .pandas_adapter import PandasAdapter
from .polars_adapter import PolarsAdapter
from ..types import ProcessingEngine, ProcessingConfig


class AdapterFactory:
    """Factory for creating data processing adapters.
    
    Provides automatic engine selection based on:
    - Data size
    - Performance requirements
    - Available engines
    - User preferences
    """
    
    # Engine selection thresholds
    DEFAULT_POLARS_THRESHOLD = 1_000_000  # Records
    DEFAULT_MEMORY_THRESHOLD = 2_000_000_000  # 2GB in bytes
    
    # Registered adapters
    _adapters: Dict[ProcessingEngine, Type[BaseAdapter]] = {
        ProcessingEngine.PANDAS: PandasAdapter,
        ProcessingEngine.POLARS: PolarsAdapter,
    }
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        """Initialize the adapter factory.
        
        Args:
            config: Processing configuration
        """
        self.config = config or ProcessingConfig()
        self.logger = logging.getLogger(__name__)
        
        # Check engine availability
        self._available_engines = self._check_available_engines()
        
        if not self._available_engines:
            raise RuntimeError("No data processing engines are available")
        
        self.logger.info(f"Available engines: {list(self._available_engines.keys())}")
    
    def create_adapter(
        self,
        engine: Optional[ProcessingEngine] = None,
        file_path: Optional[Path] = None,
        estimated_records: Optional[int] = None,
        estimated_memory: Optional[int] = None,
        **kwargs
    ) -> BaseAdapter:
        """Create a data processing adapter.
        
        Args:
            engine: Specific engine to use (optional)
            file_path: Path to data file for size estimation
            estimated_records: Estimated number of records
            estimated_memory: Estimated memory usage in bytes
            **kwargs: Additional arguments for adapter initialization
            
        Returns:
            Configured data processing adapter
        """
        # If engine is specified, use it directly
        if engine:
            if engine not in self._available_engines:
                raise ValueError(f"Engine {engine} is not available. Available: {list(self._available_engines.keys())}")
            
            adapter_class = self._adapters[engine]
            adapter = adapter_class(config=self.config, **kwargs)
            
            if not adapter.is_engine_available():
                raise RuntimeError(f"Engine {engine} is not properly installed")
            
            self.logger.info(f"Created {engine.value} adapter (user specified)")
            return adapter
        
        # Auto-select engine based on data characteristics
        selected_engine = self._select_optimal_engine(
            file_path=file_path,
            estimated_records=estimated_records,
            estimated_memory=estimated_memory
        )
        
        adapter_class = self._adapters[selected_engine]
        adapter = adapter_class(config=self.config, **kwargs)
        
        self.logger.info(f"Auto-selected {selected_engine.value} adapter")
        return adapter
    
    def get_available_engines(self) -> Dict[ProcessingEngine, bool]:
        """Get available processing engines.
        
        Returns:
            Dictionary mapping engines to availability status
        """
        return self._available_engines.copy()
    
    def get_engine_recommendations(
        self,
        file_path: Optional[Path] = None,
        estimated_records: Optional[int] = None,
        estimated_memory: Optional[int] = None
    ) -> Dict[ProcessingEngine, Dict[str, Any]]:
        """Get engine recommendations with reasoning.
        
        Args:
            file_path: Path to data file
            estimated_records: Estimated number of records
            estimated_memory: Estimated memory usage
            
        Returns:
            Dictionary with engine recommendations and reasoning
        """
        recommendations = {}
        
        # Estimate data characteristics
        data_info = self._estimate_data_characteristics(
            file_path=file_path,
            estimated_records=estimated_records,
            estimated_memory=estimated_memory
        )
        
        for engine in self._available_engines:
            if engine == ProcessingEngine.PANDAS:
                score, reasons = self._score_pandas_engine(data_info)
            elif engine == ProcessingEngine.POLARS:
                score, reasons = self._score_polars_engine(data_info)
            else:
                score, reasons = 0.0, ["Unknown engine"]
            
            recommendations[engine] = {
                'score': score,
                'reasons': reasons,
                'recommended': score >= 0.7,
                'available': self._available_engines[engine]
            }
        
        return recommendations
    
    def _check_available_engines(self) -> Dict[ProcessingEngine, bool]:
        """Check which engines are available.
        
        Returns:
            Dictionary mapping engines to availability
        """
        available = {}
        
        for engine, adapter_class in self._adapters.items():
            try:
                # Create temporary adapter to check availability
                temp_adapter = adapter_class()
                available[engine] = temp_adapter.is_engine_available()
            except Exception as e:
                self.logger.warning(f"Engine {engine} check failed: {e}")
                available[engine] = False
        
        return available
    
    def _select_optimal_engine(
        self,
        file_path: Optional[Path] = None,
        estimated_records: Optional[int] = None,
        estimated_memory: Optional[int] = None
    ) -> ProcessingEngine:
        """Select the optimal processing engine.
        
        Args:
            file_path: Path to data file
            estimated_records: Estimated number of records
            estimated_memory: Estimated memory usage
            
        Returns:
            Selected processing engine
        """
        # Get recommendations
        recommendations = self.get_engine_recommendations(
            file_path=file_path,
            estimated_records=estimated_records,
            estimated_memory=estimated_memory
        )
        
        # Filter available engines
        available_recommendations = {
            engine: rec for engine, rec in recommendations.items()
            if rec['available']
        }
        
        if not available_recommendations:
            raise RuntimeError("No processing engines are available")
        
        # Select engine with highest score
        best_engine = max(
            available_recommendations.keys(),
            key=lambda e: available_recommendations[e]['score']
        )
        
        self.logger.info(
            f"Selected {best_engine.value} (score: {available_recommendations[best_engine]['score']:.2f})"
        )
        
        return best_engine
    
    def _estimate_data_characteristics(
        self,
        file_path: Optional[Path] = None,
        estimated_records: Optional[int] = None,
        estimated_memory: Optional[int] = None
    ) -> Dict[str, Any]:
        """Estimate data characteristics for engine selection.
        
        Args:
            file_path: Path to data file
            estimated_records: Estimated number of records
            estimated_memory: Estimated memory usage
            
        Returns:
            Data characteristics dictionary
        """
        characteristics = {
            'file_size_bytes': 0,
            'estimated_records': estimated_records or 0,
            'estimated_memory_bytes': estimated_memory or 0,
            'file_format': None,
            'complexity': 'low',  # low, medium, high
        }
        
        # Get file information if available
        if file_path and file_path.exists():
            characteristics['file_size_bytes'] = file_path.stat().st_size
            characteristics['file_format'] = file_path.suffix.lower()
            
            # Estimate records from file size (rough approximation)
            if not estimated_records:
                if characteristics['file_format'] in ['.csv', '.tsv']:
                    # Assume average 100 bytes per record for CSV
                    characteristics['estimated_records'] = characteristics['file_size_bytes'] // 100
                elif characteristics['file_format'] in ['.xlsx', '.xls']:
                    # Excel files are more compact
                    characteristics['estimated_records'] = characteristics['file_size_bytes'] // 50
                elif characteristics['file_format'] == '.parquet':
                    # Parquet is highly compressed
                    characteristics['estimated_records'] = characteristics['file_size_bytes'] // 20
        
        # Estimate memory usage if not provided
        if not estimated_memory and characteristics['estimated_records']:
            # Rough estimate: 200 bytes per record in memory
            characteristics['estimated_memory_bytes'] = characteristics['estimated_records'] * 200
        
        # Determine complexity
        if characteristics['estimated_records'] > 10_000_000:
            characteristics['complexity'] = 'high'
        elif characteristics['estimated_records'] > 1_000_000:
            characteristics['complexity'] = 'medium'
        
        return characteristics
    
    def _score_pandas_engine(self, data_info: Dict[str, Any]) -> tuple[float, list[str]]:
        """Score Pandas engine for given data characteristics.
        
        Args:
            data_info: Data characteristics
            
        Returns:
            Tuple of (score, reasons)
        """
        score = 0.5  # Base score
        reasons = []
        
        records = data_info['estimated_records']
        memory_bytes = data_info['estimated_memory_bytes']
        file_format = data_info.get('file_format', '')
        
        # Pandas is good for smaller datasets
        if records < self.DEFAULT_POLARS_THRESHOLD:
            score += 0.3
            reasons.append(f"Good for datasets < {self.DEFAULT_POLARS_THRESHOLD:,} records")
        else:
            score -= 0.2
            reasons.append(f"Less optimal for large datasets (>{self.DEFAULT_POLARS_THRESHOLD:,} records)")
        
        # Memory considerations
        if memory_bytes < self.DEFAULT_MEMORY_THRESHOLD:
            score += 0.2
            reasons.append("Memory usage within acceptable limits")
        else:
            score -= 0.3
            reasons.append("High memory usage may cause issues")
        
        # File format support
        if file_format in ['.xlsx', '.xls', '.xlsb']:
            score += 0.2
            reasons.append("Excellent Excel file support")
        elif file_format in ['.csv', '.tsv']:
            score += 0.1
            reasons.append("Good CSV support")
        
        # Pandas has mature ecosystem
        score += 0.1
        reasons.append("Mature ecosystem with extensive library support")
        
        return min(1.0, max(0.0, score)), reasons
    
    def _score_polars_engine(self, data_info: Dict[str, Any]) -> tuple[float, list[str]]:
        """Score Polars engine for given data characteristics.
        
        Args:
            data_info: Data characteristics
            
        Returns:
            Tuple of (score, reasons)
        """
        score = 0.5  # Base score
        reasons = []
        
        records = data_info['estimated_records']
        memory_bytes = data_info['estimated_memory_bytes']
        file_format = data_info.get('file_format', '')
        complexity = data_info.get('complexity', 'low')
        
        # Polars excels with large datasets
        if records >= self.DEFAULT_POLARS_THRESHOLD:
            score += 0.4
            reasons.append(f"Optimized for large datasets (>={self.DEFAULT_POLARS_THRESHOLD:,} records)")
        else:
            score -= 0.1
            reasons.append(f"May be overkill for smaller datasets (<{self.DEFAULT_POLARS_THRESHOLD:,} records)")
        
        # Memory efficiency
        if memory_bytes > self.DEFAULT_MEMORY_THRESHOLD:
            score += 0.3
            reasons.append("Superior memory efficiency for large datasets")
        
        # Performance for complex operations
        if complexity in ['medium', 'high']:
            score += 0.2
            reasons.append("High performance for complex data operations")
        
        # File format support
        if file_format in ['.parquet', '.feather', '.ipc']:
            score += 0.3
            reasons.append("Excellent support for columnar formats")
        elif file_format in ['.csv', '.tsv']:
            score += 0.2
            reasons.append("Fast CSV processing")
        elif file_format in ['.xlsx', '.xls']:
            score -= 0.1
            reasons.append("Limited Excel support (requires pandas conversion)")
        
        # Parallel processing capabilities
        score += 0.1
        reasons.append("Built-in parallel processing capabilities")
        
        return min(1.0, max(0.0, score)), reasons
    
    @classmethod
    def register_adapter(cls, engine: ProcessingEngine, adapter_class: Type[BaseAdapter]) -> None:
        """Register a new adapter class.
        
        Args:
            engine: Processing engine type
            adapter_class: Adapter class to register
        """
        cls._adapters[engine] = adapter_class
    
    @classmethod
    def get_registered_adapters(cls) -> Dict[ProcessingEngine, Type[BaseAdapter]]:
        """Get all registered adapters.
        
        Returns:
            Dictionary of registered adapters
        """
        return cls._adapters.copy()


def create_adapter(
    engine: Optional[ProcessingEngine] = None,
    config: Optional[ProcessingConfig] = None,
    file_path: Optional[Path] = None,
    estimated_records: Optional[int] = None,
    estimated_memory: Optional[int] = None,
    **kwargs
) -> BaseAdapter:
    """Convenience function to create a data processing adapter.
    
    Args:
        engine: Specific engine to use (optional)
        config: Processing configuration
        file_path: Path to data file for size estimation
        estimated_records: Estimated number of records
        estimated_memory: Estimated memory usage in bytes
        **kwargs: Additional arguments for adapter initialization
        
    Returns:
        Configured data processing adapter
    """
    factory = AdapterFactory(config=config)
    return factory.create_adapter(
        engine=engine,
        file_path=file_path,
        estimated_records=estimated_records,
        estimated_memory=estimated_memory,
        **kwargs
    )


def get_optimal_engine(
    file_path: Optional[Path] = None,
    estimated_records: Optional[int] = None,
    estimated_memory: Optional[int] = None
) -> ProcessingEngine:
    """Get the optimal processing engine for given data characteristics.
    
    Args:
        file_path: Path to data file
        estimated_records: Estimated number of records
        estimated_memory: Estimated memory usage
        
    Returns:
        Optimal processing engine
    """
    factory = AdapterFactory()
    return factory._select_optimal_engine(
        file_path=file_path,
        estimated_records=estimated_records,
        estimated_memory=estimated_memory
    )


def get_engine_recommendations(
    file_path: Optional[Path] = None,
    estimated_records: Optional[int] = None,
    estimated_memory: Optional[int] = None
) -> Dict[ProcessingEngine, Dict[str, Any]]:
    """Get engine recommendations with detailed reasoning.
    
    Args:
        file_path: Path to data file
        estimated_records: Estimated number of records
        estimated_memory: Estimated memory usage
        
    Returns:
        Dictionary with engine recommendations
    """
    factory = AdapterFactory()
    return factory.get_engine_recommendations(
        file_path=file_path,
        estimated_records=estimated_records,
        estimated_memory=estimated_memory
    )