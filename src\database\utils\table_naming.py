"""
Table naming conventions and operator detection utilities.

This module provides functionality for generating standardized table names
and detecting operator information from telecommunications data files.
"""

import re
import logging
from pathlib import Path
from typing import Dict, Optional, Tuple, List
from datetime import datetime

logger = logging.getLogger(__name__)


class TableNamingManager:
    """Manages table naming conventions for telecommunications data."""
    
    def __init__(self, config: Dict):
        """Initialize with database configuration.
        
        Args:
            config: Database configuration containing naming patterns
        """
        self.config = config
        # Handle both dict and object config types
        if hasattr(config, 'get'):
            self.telecom_config = config.get('telecom_data_sources', {})
        elif hasattr(config, '__dict__'):
            config_dict = config.__dict__
            self.telecom_config = config_dict.get('telecom_data_sources', {})
        else:
            self.telecom_config = {}
        
    def generate_table_name(self, data_type: str, file_path: Path, 
                          operator: Optional[str] = None, 
                          sheet_name: Optional[str] = None) -> str:
        """Generate standardized table name based on data type and file information.
        
        Args:
            data_type: Type of data (ep, cdr, nlg, etc.)
            file_path: Path to the data file
            operator: Optional operator name for CDR data
            sheet_name: Optional sheet name for Excel files
            
        Returns:
            Standardized table name
        """
        try:
            data_config = self.telecom_config.get(data_type, {})
            pattern = data_config.get('table_name_pattern', f'{data_type}_{{filename}}')
            
            if data_type == 'cdr':
                return self._generate_cdr_table_name(file_path, pattern, operator, sheet_name)
            elif data_type == 'ep':
                return self._generate_ep_table_name(file_path, pattern)
            elif data_type == 'nlg':
                return self._generate_nlg_table_name(file_path, pattern)
            else:
                # Generic naming for other data types
                return self._generate_generic_table_name(file_path, pattern, data_type)
                
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Failed to generate table name using pattern for {data_type} data: {e}")
            logger.debug(f"Full error traceback: {error_details}")

            # Fallback to simple naming
            base_name = file_path.stem.lower()
            # Clean the name
            clean_name = re.sub(r'[^\w]', '_', base_name)
            clean_name = re.sub(r'_+', '_', clean_name)
            clean_name = clean_name.strip('_')

            fallback_name = f"{data_type}_{operator}_{clean_name}" if operator else f"{data_type}_{clean_name}"
            logger.warning(f"Using fallback table name: '{fallback_name}' for file: {file_path}")
            return fallback_name
    
    def _generate_cdr_table_name(self, file_path: Path, pattern: str, 
                                operator: Optional[str] = None, 
                                sheet_name: Optional[str] = None) -> str:
        """Generate CDR table name following pattern: cdr_{year}Q{quarter}_{service_type}"""
        
        # Extract information from filename
        filename = file_path.stem
        
        # Extract year and quarter
        year, quarter = self._extract_year_quarter(filename)
        
        # Extract service type
        service_type = self._extract_service_type(filename, sheet_name)
        
        # Apply pattern
        table_name = pattern.format(
            year=year,
            quarter=quarter,
            service_type=service_type
        )
        
        # Add operator prefix if specified
        if operator:
            table_name = f"{operator}_{table_name}"
            
        return self._clean_table_name(table_name)
    
    def _generate_ep_table_name(self, file_path: Path, pattern: str) -> str:
        """Generate EP table name following pattern: ep_{cell_type}_{year}_cw{week}"""

        filename = file_path.stem
        full_path_str = str(file_path)

        # Extract cell type
        cell_type = self._extract_cell_type(filename)

        # Extract year and week (pass full path for better year extraction)
        year, week = self._extract_year_week_from_path(full_path_str, filename)

        # Apply pattern
        table_name = pattern.format(
            cell_type=cell_type,
            year=year,
            week=week
        )

        return self._clean_table_name(table_name)
    
    def _generate_nlg_table_name(self, file_path: Path, pattern: str) -> str:
        """Generate NLG table name following pattern: nlg_cube_aktuell_{date}"""
        
        filename = file_path.stem
        
        # Extract date
        date = self._extract_date(filename)
        
        # Apply pattern
        table_name = pattern.format(date=date)
        
        return self._clean_table_name(table_name)
    
    def _generate_generic_table_name(self, file_path: Path, pattern: str, data_type: str) -> str:
        """Generate generic table name."""
        
        filename = file_path.stem
        clean_filename = re.sub(r'[^\w]', '_', filename.lower())
        clean_filename = re.sub(r'_+', '_', clean_filename).strip('_')
        
        # Apply pattern
        table_name = pattern.format(filename=clean_filename)
        
        return self._clean_table_name(table_name)
    
    def _extract_year_quarter(self, filename: str) -> Tuple[str, str]:
        """Extract year and quarter from filename with enhanced pattern matching.

        Handles CDR file patterns like:
        - Shared_Benchmark_Q2_DE_2024_HTTP_Browsing_2024-05-31_13-35-42.xlsx
        - CDR_2024Q1_Voice_Data.xlsx
        - Q1_2024_Benchmark.xlsx
        """

        # Enhanced quarter patterns (order matters - most specific first)
        quarter_patterns = [
            r'(\d{4})Q(\d)',           # 2024Q1, 2024Q2
            r'Q(\d).*?(\d{4})',        # Q2_DE_2024 (non-greedy match)
            r'(\d{4}).*?Q(\d)',        # 2024_Q1 (non-greedy match)
            r'(\d{4}).*?quarter(\d)',   # 2024_quarter1
            r'quarter(\d).*?(\d{4})',   # quarter1_2024
        ]

        for pattern in quarter_patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                groups = match.groups()
                if pattern.startswith(r'Q(\d)') or pattern.startswith(r'quarter(\d)'):
                    # Quarter comes first
                    quarter, year = groups
                else:
                    # Year comes first
                    year, quarter = groups

                # Validate year range
                year_int = int(year)
                if 2020 <= year_int <= 2030:
                    return year, quarter

        # Enhanced fallback: try to extract year from path and infer quarter from date
        year_match = re.search(r'\b(20\d{2})\b', filename)
        if year_match:
            year = year_match.group(1)

            # Try to extract month from date patterns like 2024-05-31
            date_match = re.search(r'(\d{4})-(\d{2})-(\d{2})', filename)
            if date_match:
                _, month_str, _ = date_match.groups()
                month = int(month_str)
                quarter = str((month - 1) // 3 + 1)
                return year, quarter

        # Final fallback
        current_year = str(datetime.now().year)
        return current_year, "1"
    
    def _extract_service_type(self, filename: str, sheet_name: Optional[str] = None) -> str:
        """Extract service type from filename or sheet name."""
        
        # Check sheet name first if available
        text_to_check = sheet_name or filename
        
        # Common service type patterns
        service_patterns = {
            'voice': r'voice|call|telefon',
            'data': r'data|http|internet|web',
            'sms': r'sms|text|message',
            'video': r'video|stream|youtube',
            'file': r'file|download|upload|dl|ul',
            'browsing': r'browsing|browse|surf',
            'ping': r'ping|icmp',
            'dns': r'dns',
            'app': r'app|application',
            'benchmark': r'benchmark|test'
        }
        
        for service_type, pattern in service_patterns.items():
            if re.search(pattern, text_to_check, re.IGNORECASE):
                return service_type
        
        return 'general'
    
    def _extract_cell_type(self, filename: str) -> str:
        """Extract cell type from filename with enhanced pattern matching.

        Handles specific EP file naming patterns:
        - GSMCELL_CW03.xlsx → gsm
        - LTECELL_CW10.xlsx → lte
        - NRCELL_CW26.xlsx → nr
        - TEF_SITE_CW48.xlsx → site
        - TEF_Sites_&_Locations_CW26.xlsx → site
        """

        # Enhanced cell type patterns with specific matching
        # Order matters - most specific patterns first to avoid false matches
        cell_patterns = [
            # Site patterns first (most specific)
            ('site', [
                r'TEF_SITE',         # TEF_SITE_CW48.xlsx
                r'TEF_Sites',        # TEF_Sites_&_Locations_CW26.xlsx
                r'tef_site',         # lowercase variants
                r'site'              # generic site files
            ]),
            # Cell type patterns (remove word boundaries that prevent matching)
            ('gsm', [
                r'GSMCELL',          # GSMCELL_CW03.xlsx
                r'gsm',              # gsm files
                r'2g'                # 2G files
            ]),
            ('lte', [
                r'LTECELL',          # LTECELL_CW10.xlsx
                r'lte',              # lte files
                r'4g'                # 4G files
            ]),
            ('nr', [
                r'NRCELL',           # NRCELL_CW26.xlsx
                r'nr',               # nr files
                r'5g'                # 5G files
            ]),
            ('umts', [
                r'UMTSCELL',         # UMTSCELL files
                r'umts',             # umts files
                r'3g'                # 3G files
            ])
        ]

        # Check each cell type pattern (order matters - most specific first)
        for cell_type, patterns in cell_patterns:
            for pattern in patterns:
                if re.search(pattern, filename, re.IGNORECASE):
                    logger.debug(f"Matched cell type '{cell_type}' for filename '{filename}' with pattern '{pattern}'")
                    return cell_type

        # Fallback to 'general' without 'mixed'
        return 'general'
    
    def _extract_year_week(self, filename: str) -> Tuple[str, str]:
        """Extract year and calendar week from filename with enhanced pattern matching.

        Handles specific EP file naming patterns:
        - GSMCELL_CW03.xlsx → year from path/context, week 03
        - TEF_SITE_CW48.xlsx → year from path/context, week 48
        - TEF_Sites_&_Locations_CW26.xlsx → year from path/context, week 26
        """

        # Enhanced week patterns (order matters - most specific first)
        week_patterns = [
            r'\bCW(\d{1,2})\b',     # CW03, CW48 (word boundary for exact match)
            r'\bcw(\d{1,2})\b',     # cw03, cw48 (lowercase)
            r'week(\d{1,2})',       # week03
            r'w(\d{1,2})',          # w03
            r'wk(\d{1,2})',         # wk03
        ]

        week = "01"  # default
        for pattern in week_patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                week = match.group(1).zfill(2)
                break

        # Enhanced year extraction
        year = self._extract_year_from_filename_or_path(filename)

        return year, week

    def _extract_year_week_from_path(self, full_path: str, filename: str) -> Tuple[str, str]:
        """Extract year and week from full path and filename.

        Handles path patterns like:
        - D:/connect/data/input/ep/2025/CW03/GSMCELL_CW03.xlsx
        - D:/connect/data/input/ep/2024/CW48/TEF_SITE_CW48.xlsx
        """

        # Extract week from filename first
        week_patterns = [
            r'CW(\d{1,2})',         # CW03, CW48 (most common pattern)
            r'_CW(\d{1,2})',        # _CW03, _CW48 (with underscore)
            r'cw(\d{1,2})',         # cw03, cw48 (lowercase)
            r'week(\d{1,2})',       # week03
            r'w(\d{1,2})',          # w03
            r'wk(\d{1,2})',         # wk03
        ]

        week = "01"  # default
        for pattern in week_patterns:
            match = re.search(pattern, filename, re.IGNORECASE)
            if match:
                week = match.group(1).zfill(2)
                break

        # Extract year from path first (prioritize path over filename)
        year = self._extract_year_from_path(full_path)
        if not year:
            # Fallback to filename extraction
            year = self._extract_year_from_filename_or_path(filename)

        # Ensure we have valid year and week
        if not year:
            year = str(datetime.now().year)  # Ultimate fallback
        if not week:
            week = "01"  # Default week if not found

        logger.debug(f"Extracted year='{year}', week='{week}' from path='{full_path}', filename='{filename}'")
        return year, week

    def _extract_year_from_path(self, full_path: str) -> str:
        """Extract year from directory path.

        Handles patterns like:
        - D:/connect/data/input/ep/2025/CW03/
        - D:/connect/data/input/ep/2024/CW48/
        """

        # Look for year in path segments
        path_segments = full_path.replace('\\', '/').split('/')

        for segment in path_segments:
            # Check if segment is a 4-digit year
            if re.match(r'^(20\d{2})$', segment):
                year = int(segment)
                # Validate year range
                if 2020 <= year <= 2030:
                    return str(year)

        return ""  # Return empty string if no year found in path

    def _extract_year_from_filename_or_path(self, filename: str) -> str:
        """Extract year from filename with enhanced logic.

        Tries multiple approaches to find the most relevant year:
        1. Four-digit year in filename
        2. Current year as fallback
        """

        # Look for 4-digit year in filename
        year_patterns = [
            r'\b(20\d{2})\b',  # 2020-2099 with word boundaries
            r'(\d{4})',        # Any 4-digit number
        ]

        for pattern in year_patterns:
            matches = re.findall(pattern, filename)
            if matches:
                # Take the first valid year found
                for year_str in matches:
                    year = int(year_str)
                    # Validate year range (reasonable for telecommunications data)
                    if 2020 <= year <= 2030:
                        return str(year)

        # Fallback to current year only if no valid year found
        current_year = datetime.now().year
        if 2020 <= current_year <= 2030:
            return str(current_year)
        return '2024'  # Default fallback year
    
    def _extract_date(self, filename: str) -> str:
        """Extract date from filename with enhanced pattern matching.

        Handles specific NLG file naming patterns:
        - NLG_CUBE_aktuell_2024-03-28.xlsb → 20240328
        - NLG_CUBE_aktuell_2024_03_28.xlsb → 20240328
        - NLG_CUBE_aktuell_20240328.xlsb → 20240328

        Returns date in YYYYMMDD format for table names.
        """

        # Enhanced date patterns (order matters - most specific first)
        date_patterns = [
            # ISO date format with separators: 2024-03-28, 2024_03_28
            r'(\d{4})[-_](\d{2})[-_](\d{2})',
            # Date with dots: 2024.03.28
            r'(\d{4})\.(\d{2})\.(\d{2})',
            # Date with slashes: 2024/03/28
            r'(\d{4})/(\d{2})/(\d{2})',
            # Compact date format: 20240328 (8 consecutive digits)
            r'\b(\d{8})\b',
            # Partial date patterns: 2024-03, 2024_03
            r'(\d{4})[-_](\d{2})',
            # Alternative formats: 2024-11-20 (common in NLG files)
            r'(\d{4})-(\d{1,2})-(\d{1,2})',
        ]

        for pattern in date_patterns:
            match = re.search(pattern, filename)
            if match:
                groups = match.groups()

                if len(groups) == 1:
                    # Compact format: 20240328
                    date_str = groups[0]
                    if len(date_str) == 8:
                        return date_str
                elif len(groups) == 2:
                    # Year-month format: 2024-03
                    year, month = groups
                    return f"{year}{month}01"  # Default to first day of month
                elif len(groups) == 3:
                    # Full date format: 2024-03-28
                    year, month, day = groups
                    # Ensure month and day are zero-padded
                    month = month.zfill(2)
                    day = day.zfill(2)
                    return f"{year}{month}{day}"

        # Fallback: try to extract just year and use current month/day
        year_match = re.search(r'(\d{4})', filename)
        if year_match:
            year = year_match.group(1)
            current_date = datetime.now()
            return f"{year}{current_date.month:02d}{current_date.day:02d}"

        # Final fallback to current date in YYYYMMDD format
        return datetime.now().strftime('%Y%m%d')
    
    def _clean_table_name(self, table_name: str) -> str:
        """Clean and standardize table name with enhanced special character handling.

        Handles special characters like & in TEF_Sites_&_Locations_CW26.xlsx
        and ensures PostgreSQL-compatible identifiers.
        """

        # Convert to lowercase
        table_name = table_name.lower()

        # Handle specific special characters before general replacement
        special_char_replacements = {
            '&': 'and',      # TEF_Sites_&_Locations → TEF_Sites_and_Locations
            '+': 'plus',     # Handle plus signs
            '-': '_',        # Hyphens to underscores
            ' ': '_',        # Spaces to underscores
            '.': '_',        # Dots to underscores
        }

        for char, replacement in special_char_replacements.items():
            table_name = table_name.replace(char, replacement)

        # Replace any remaining special characters with underscores
        table_name = re.sub(r'[^\w]', '_', table_name)

        # Remove multiple consecutive underscores
        table_name = re.sub(r'_+', '_', table_name)

        # Remove leading/trailing underscores
        table_name = table_name.strip('_')

        # Ensure it doesn't start with a number
        if table_name and table_name[0].isdigit():
            table_name = f'tbl_{table_name}'

        # Ensure minimum length
        if len(table_name) < 3:
            table_name = f'tbl_{table_name}'

        # Limit length (PostgreSQL identifier limit is 63 characters)
        if len(table_name) > 60:
            # Try to preserve meaningful parts
            parts = table_name.split('_')
            if len(parts) > 1:
                # Keep first and last parts, truncate middle if needed
                first_part = parts[0]
                last_part = parts[-1]
                remaining_length = 60 - len(first_part) - len(last_part) - 2  # 2 underscores

                if remaining_length > 0 and len(parts) > 2:
                    middle_parts = '_'.join(parts[1:-1])
                    if len(middle_parts) > remaining_length:
                        middle_parts = middle_parts[:remaining_length]
                    table_name = f"{first_part}_{middle_parts}_{last_part}"
                else:
                    table_name = f"{first_part}_{last_part}"
            else:
                table_name = table_name[:60]

        return table_name


class OperatorDetector:
    """Detects operator information from Excel files and sheet names."""
    
    def __init__(self, config: Dict):
        """Initialize with database configuration.
        
        Args:
            config: Database configuration containing operator mappings
        """
        self.config = config
        self.operator_mapping = config.get('telecom_data_sources', {}).get('cdr', {}).get('operator_schema_mapping', {})
    
    def detect_operators_in_excel(self, file_path: Path) -> List[Tuple[str, str]]:
        """Detect operators and their corresponding schemas from Excel file sheets.
        
        Args:
            file_path: Path to Excel file
            
        Returns:
            List of tuples (operator_name, schema_name)
        """
        try:
            import pandas as pd
            
            # Get all sheet names
            excel_file = pd.ExcelFile(file_path)
            sheet_names = excel_file.sheet_names
            
            detected_operators = []
            
            for sheet_name in sheet_names:
                operator = self._detect_operator_from_sheet_name(sheet_name)
                if operator:
                    schema = self.operator_mapping.get(operator)
                    if schema:
                        detected_operators.append((operator, schema))
                        logger.info(f"Detected operator '{operator}' in sheet '{sheet_name}' -> schema '{schema}'")
            
            return detected_operators
            
        except Exception as e:
            logger.error(f"Failed to detect operators in Excel file {file_path}: {e}")
            return []
    
    def _detect_operator_from_sheet_name(self, sheet_name: str) -> Optional[str]:
        """Detect operator from sheet name using fuzzy matching.
        
        Args:
            sheet_name: Name of the Excel sheet
            
        Returns:
            Detected operator name or None
        """
        sheet_name_lower = sheet_name.lower()
        
        # Define operator patterns for fuzzy matching
        operator_patterns = {
            'telefonica': ['telefonica', 'tef', 'movistar', 'o2'],
            'vodafone': ['vodafone', 'vdf', 'voda'],
            'telekom': ['telekom', 'tdg', 'deutsche telekom', 'dt', 'magenta']
        }
        
        for operator, patterns in operator_patterns.items():
            for pattern in patterns:
                if pattern in sheet_name_lower:
                    return operator
        
        return None
    
    def get_schema_for_operator(self, operator: str) -> Optional[str]:
        """Get schema name for a given operator.
        
        Args:
            operator: Operator name
            
        Returns:
            Schema name or None if not found
        """
        return self.operator_mapping.get(operator)
