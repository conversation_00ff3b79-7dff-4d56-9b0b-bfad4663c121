#!/usr/bin/env python3
"""
Test Settings and Configuration

This module provides centralized configuration for all test types,
including database settings, performance thresholds, security parameters,
and environment-specific configurations.
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, field


@dataclass
class DatabaseTestConfig:
    """Database configuration for testing."""
    host: str = "localhost"
    port: int = 5432
    database: str = "connect_test"
    username: str = "test_user"
    password: str = "test_password"
    pool_size: int = 5
    max_overflow: int = 10
    pool_timeout: int = 30
    pool_recycle: int = 3600
    echo: bool = False
    
    @property
    def connection_string(self) -> str:
        """Get database connection string."""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"


@dataclass
class PerformanceTestConfig:
    """Performance testing configuration and thresholds."""
    # Response time thresholds (seconds)
    max_query_time: float = 1.0
    max_bulk_insert_time: float = 10.0
    max_geospatial_query_time: float = 3.0
    max_export_time: float = 15.0
    
    # Throughput thresholds
    min_queries_per_second: int = 100
    min_bulk_insert_rate: int = 10000  # records per second
    
    # Memory thresholds (MB)
    max_memory_usage: int = 1024
    max_memory_growth: int = 100  # MB per operation
    
    # Concurrency settings
    max_concurrent_users: int = 20
    max_concurrent_connections: int = 50
    
    # Data volume settings
    small_dataset_size: int = 1000
    medium_dataset_size: int = 100000
    large_dataset_size: int = 5000000
    
    # Test duration settings (seconds)
    short_test_duration: int = 30
    medium_test_duration: int = 300
    long_test_duration: int = 1800


@dataclass
class SecurityTestConfig:
    """Security testing configuration."""
    # Authentication settings
    password_min_length: int = 8
    password_max_attempts: int = 3
    session_timeout: int = 3600  # seconds
    token_expiry: int = 1800  # seconds
    
    # Encryption settings
    encryption_algorithm: str = "AES-256-GCM"
    key_rotation_interval: int = 86400  # seconds (24 hours)
    
    # Rate limiting
    max_requests_per_minute: int = 100
    max_login_attempts_per_hour: int = 10
    
    # Audit settings
    audit_log_retention: int = 90  # days
    security_event_threshold: int = 5
    
    # Vulnerability scanning
    scan_timeout: int = 300  # seconds
    max_vulnerability_score: float = 7.0  # CVSS score


@dataclass
class _TestDataConfig:
    """Test data configuration."""
    # File paths
    fixtures_dir: Path = field(default_factory=lambda: Path(__file__).parent / "fixtures")
    sample_data_file: str = "sample_data.csv"
    large_data_file: str = "large_sample_data.csv"
    geospatial_data_file: str = "mock_polygons.json"
    
    # Data generation settings
    default_record_count: int = 1000
    max_string_length: int = 255
    coordinate_precision: int = 6
    
    # Test user data
    test_users: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        "admin": {
            "username": "test_admin",
            "password": "admin_password_123",
            "role": "admin",
            "permissions": ["read", "write", "delete", "admin"]
        },
        "user": {
            "username": "test_user",
            "password": "user_password_123",
            "role": "user",
            "permissions": ["read", "write"]
        },
        "viewer": {
            "username": "test_viewer",
            "password": "viewer_password_123",
            "role": "viewer",
            "permissions": ["read"]
        }
    })


@dataclass
class CITestConfig:
    """CI/CD specific test configuration."""
    # Test execution settings
    parallel_workers: int = 2
    timeout_multiplier: float = 1.5
    retry_attempts: int = 2
    
    # Coverage settings
    min_coverage_percentage: int = 80
    coverage_fail_under: int = 75
    
    # Reporting settings
    generate_html_report: bool = True
    generate_xml_report: bool = True
    generate_json_report: bool = True
    
    # Environment settings
    skip_slow_tests: bool = True
    skip_network_tests: bool = True
    skip_external_dependencies: bool = True


class _TestSettings:
    """Centralized test settings manager."""
    
    def __init__(self, environment: str = "test"):
        self.environment = environment
        self.project_root = Path(__file__).parent.parent
        self.tests_root = Path(__file__).parent
        
        # Load configuration from environment variables
        self.database = self._load_database_config()
        self.performance = self._load_performance_config()
        self.security = self._load_security_config()
        self.test_data = self._load_test_data_config()
        self.ci = self._load_ci_config()
    
    def _load_database_config(self) -> DatabaseTestConfig:
        """Load database configuration from environment."""
        return DatabaseTestConfig(
            host=os.getenv("TEST_DB_HOST", "localhost"),
            port=int(os.getenv("TEST_DB_PORT", "5432")),
            database=os.getenv("TEST_DB_NAME", "connect_test"),
            username=os.getenv("TEST_DB_USER", "test_user"),
            password=os.getenv("TEST_DB_PASSWORD", "test_password"),
            pool_size=int(os.getenv("TEST_DB_POOL_SIZE", "5")),
            max_overflow=int(os.getenv("TEST_DB_MAX_OVERFLOW", "10")),
            echo=os.getenv("TEST_DB_ECHO", "false").lower() == "true"
        )
    
    def _load_performance_config(self) -> PerformanceTestConfig:
        """Load performance configuration from environment."""
        return PerformanceTestConfig(
            max_query_time=float(os.getenv("TEST_MAX_QUERY_TIME", "1.0")),
            max_bulk_insert_time=float(os.getenv("TEST_MAX_BULK_INSERT_TIME", "10.0")),
            max_geospatial_query_time=float(os.getenv("TEST_MAX_GEOSPATIAL_QUERY_TIME", "3.0")),
            max_memory_usage=int(os.getenv("TEST_MAX_MEMORY_MB", "1024")),
            max_concurrent_users=int(os.getenv("TEST_MAX_CONCURRENT_USERS", "20")),
            large_dataset_size=int(os.getenv("TEST_LARGE_DATASET_SIZE", "5000000"))
        )
    
    def _load_security_config(self) -> SecurityTestConfig:
        """Load security configuration from environment."""
        return SecurityTestConfig(
            password_min_length=int(os.getenv("TEST_PASSWORD_MIN_LENGTH", "8")),
            session_timeout=int(os.getenv("TEST_SESSION_TIMEOUT", "3600")),
            max_requests_per_minute=int(os.getenv("TEST_MAX_REQUESTS_PER_MINUTE", "100")),
            audit_log_retention=int(os.getenv("TEST_AUDIT_LOG_RETENTION_DAYS", "90"))
        )
    
    def _load_test_data_config(self) -> _TestDataConfig:
        """Load test data configuration."""
        config = _TestDataConfig()
        config.fixtures_dir = self.tests_root / "fixtures"
        config.default_record_count = int(os.getenv("TEST_DEFAULT_RECORD_COUNT", "1000"))
        return config
    
    def _load_ci_config(self) -> CITestConfig:
        """Load CI/CD configuration from environment."""
        is_ci = os.getenv("CI", "false").lower() == "true"
        
        return CITestConfig(
            parallel_workers=int(os.getenv("TEST_PARALLEL_WORKERS", "2" if is_ci else "4")),
            min_coverage_percentage=int(os.getenv("TEST_MIN_COVERAGE", "80")),
            skip_slow_tests=os.getenv("TEST_SKIP_SLOW", str(is_ci)).lower() == "true",
            skip_network_tests=os.getenv("TEST_SKIP_NETWORK", str(is_ci)).lower() == "true",
            generate_xml_report=os.getenv("TEST_GENERATE_XML", str(is_ci)).lower() == "true"
        )
    
    def get_test_database_url(self, database_name: Optional[str] = None) -> str:
        """Get test database URL."""
        db_name = database_name or self.database.database
        return f"postgresql://{self.database.username}:{self.database.password}@{self.database.host}:{self.database.port}/{db_name}"
    
    def get_fixtures_path(self, filename: str) -> Path:
        """Get path to test fixture file."""
        return self.test_data.fixtures_dir / filename
    
    def is_ci_environment(self) -> bool:
        """Check if running in CI environment."""
        return os.getenv("CI", "false").lower() == "true"
    
    def should_skip_slow_tests(self) -> bool:
        """Check if slow tests should be skipped."""
        return self.ci.skip_slow_tests or os.getenv("SKIP_SLOW_TESTS", "false").lower() == "true"
    
    def should_skip_network_tests(self) -> bool:
        """Check if network-dependent tests should be skipped."""
        return self.ci.skip_network_tests or os.getenv("SKIP_NETWORK_TESTS", "false").lower() == "true"
    
    def get_performance_threshold(self, metric: str) -> float:
        """Get performance threshold for a specific metric."""
        thresholds = {
            "query_time": self.performance.max_query_time,
            "bulk_insert_time": self.performance.max_bulk_insert_time,
            "geospatial_query_time": self.performance.max_geospatial_query_time,
            "export_time": self.performance.max_export_time,
            "memory_usage": self.performance.max_memory_usage,
            "memory_growth": self.performance.max_memory_growth
        }
        return thresholds.get(metric, 0.0)
    
    def get_test_user(self, role: str) -> Dict[str, Any]:
        """Get test user configuration by role."""
        return self.test_data.test_users.get(role, {})
    
    def create_test_config_dict(self) -> Dict[str, Any]:
        """Create configuration dictionary for test fixtures."""
        return {
            "database": {
                "host": self.database.host,
                "port": self.database.port,
                "database": self.database.database,
                "username": self.database.username,
                "password": self.database.password,
                "pool_size": self.database.pool_size,
                "max_overflow": self.database.max_overflow,
                "pool_timeout": self.database.pool_timeout,
                "echo": self.database.echo
            },
            "performance": {
                "max_query_time": self.performance.max_query_time,
                "max_memory_usage": self.performance.max_memory_usage,
                "max_concurrent_users": self.performance.max_concurrent_users,
                "large_dataset_size": self.performance.large_dataset_size
            },
            "security": {
                "password_min_length": self.security.password_min_length,
                "session_timeout": self.security.session_timeout,
                "max_requests_per_minute": self.security.max_requests_per_minute
            },
            "test_data": {
                "fixtures_dir": str(self.test_data.fixtures_dir),
                "default_record_count": self.test_data.default_record_count
            }
        }


# Global test settings instance
test_settings = _TestSettings()


# Convenience functions for common test configurations
def get_test_db_config() -> Dict[str, Any]:
    """Get test database configuration."""
    return test_settings.create_test_config_dict()["database"]


def get_performance_config() -> Dict[str, Any]:
    """Get performance test configuration."""
    return test_settings.create_test_config_dict()["performance"]


def get_security_config() -> Dict[str, Any]:
    """Get security test configuration."""
    return test_settings.create_test_config_dict()["security"]


def is_slow_test_enabled() -> bool:
    """Check if slow tests are enabled."""
    return not test_settings.should_skip_slow_tests()


def is_network_test_enabled() -> bool:
    """Check if network tests are enabled."""
    return not test_settings.should_skip_network_tests()


def get_test_timeout(test_type: str = "default") -> int:
    """Get timeout for specific test type."""
    timeouts = {
        "unit": 30,
        "integration": 120,
        "performance": 600,
        "security": 300,
        "e2e": 900,
        "default": 60
    }
    
    timeout = timeouts.get(test_type, timeouts["default"])
    
    # Apply CI multiplier if in CI environment
    if test_settings.is_ci_environment():
        timeout = int(timeout * test_settings.ci.timeout_multiplier)
    
    return timeout