"""Unit tests for ReadWriteSplitter class.

This module contains comprehensive tests for the read-write splitting functionality,
including connection management, load balancing, failover, and error handling.
"""

import asyncio
import os
from typing import List
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.config.models import DatabaseConfig
from src.database.connection import LoadBalancingStrategy, ReadWriteSplitter
from src.database.connection.health_check import HealthStatus
from src.database.exceptions import (
    NoAvailableReplicasError,
    PrimaryDatabaseUnavailableError,
)


class TestReadWriteSplitter:
    """Test cases for ReadWriteSplitter class."""

    @pytest.fixture
    def primary_config(self):
        """Primary database configuration fixture."""
        return DatabaseConfig(
            host="primary.example.com",
            port=5432,
            name="test_db",
            user="test_user",
            password=os.getenv("TEST_DB_PASSWORD", "secure_test_password_123!"),
        )

    @pytest.fixture
    def replica_configs(self):
        """Replica database configurations fixture."""
        return [
            DatabaseConfig(
                host="replica1.example.com",
                port=5432,
                name="test_db",
                user="readonly_user",
                password=os.getenv(
                    "TEST_READONLY_PASSWORD", "secure_readonly_password_123!"
                ),
            ),
            DatabaseConfig(
                host="replica2.example.com",
                port=5432,
                name="test_db",
                user="readonly_user",
                password="readonly_password",
            ),
            DatabaseConfig(
                host="replica3.example.com",
                port=5432,
                name="test_db",
                user="readonly_user",
                password="readonly_password",
            ),
        ]

    @pytest.fixture
    def splitter(self, primary_config, replica_configs):
        """ReadWriteSplitter instance fixture."""
        return ReadWriteSplitter(
            primary_config=primary_config,
            replica_configs=replica_configs,
            load_balancing_strategy=LoadBalancingStrategy.ROUND_ROBIN,
            fallback_to_primary=True,
            health_check_interval=30,
        )

    @pytest.mark.asyncio
    async def test_initialization(self, splitter):
        """Test ReadWriteSplitter initialization."""
        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager, patch(
            "src.database.connection.read_write_splitter.HealthChecker"
        ) as mock_health_checker, patch(
            "asyncpg.create_pool"
        ) as mock_create_pool:
            # Mock asyncpg.create_pool to prevent real network connections
            mock_pool = AsyncMock()

            async def mock_create_pool_func(*args, **kwargs):
                return mock_pool

            mock_create_pool.side_effect = mock_create_pool_func

            # Setup mocks
            mock_pools = [AsyncMock() for _ in range(4)]  # 1 primary + 3 replicas
            for pool in mock_pools:
                mock_internal_pool = AsyncMock()
                pool._pool = mock_internal_pool  # Add _pool attribute
                pool.initialize_pool.return_value = None
                pool.close_pool.return_value = None  # Add close_pool mock
                # Mock the internal _pool for health checkers
                mock_context_manager = AsyncMock()
                mock_context_manager.__aenter__ = AsyncMock(return_value=AsyncMock())
                mock_context_manager.__aexit__ = AsyncMock(return_value=None)
                mock_internal_pool.acquire.return_value = mock_context_manager
            mock_pool_manager.side_effect = mock_pools

            mock_health_checkers = [AsyncMock() for _ in range(4)]
            for health_checker in mock_health_checkers:
                health_checker.start_monitoring.return_value = None
                health_checker.stop_monitoring.return_value = None  # Add stop_monitoring mock
            mock_health_checker.side_effect = mock_health_checkers

            try:
                await splitter.initialize()

                # Verify pools and health checkers were created
                assert splitter.primary_pool is not None
                assert len(splitter.replica_pools) == 3
                assert splitter.primary_health_checker is not None
                assert len(splitter.replica_health_checkers) == 3

                # Verify initialization was called
                assert mock_pool_manager.call_count == 4  # 1 primary + 3 replicas
            finally:
                # Clean up resources
                await splitter.close()

    @pytest.mark.asyncio
    async def test_acquire_write_connection(self, splitter):
        """Test acquiring connection for write operations."""
        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager, patch(
            "src.database.connection.read_write_splitter.HealthChecker"
        ) as mock_health_checker:
            # Setup mocks
            mock_primary_pool = AsyncMock()
            mock_pool_manager.return_value = mock_primary_pool

            mock_connection = AsyncMock()
            mock_primary_pool.acquire_connection.return_value = mock_connection
            mock_primary_pool.close_pool.return_value = None

            # Mock the acquire method for health checkers (DatabasePoolManager.acquire)
            mock_context_manager = AsyncMock()
            mock_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
            mock_context_manager.__aexit__ = AsyncMock(return_value=None)
            mock_primary_pool.acquire = AsyncMock(return_value=mock_context_manager)
            
            # Mock fetchval for health check
            mock_connection.fetchval = AsyncMock(return_value=1)

            # Mock health checker
            mock_health_checker_instance = AsyncMock()
            mock_health_checker_instance.start_monitoring.return_value = None
            mock_health_checker_instance.stop_monitoring.return_value = None
            mock_health_checker.return_value = mock_health_checker_instance

            try:
                await splitter.initialize()

                # Test write connection acquisition
                connection, pool = await splitter.acquire_connection(read_only=False)

                assert connection == mock_connection
                assert pool == mock_primary_pool
                mock_primary_pool.acquire_connection.assert_called_once()
            finally:
                # Clean up resources
                await splitter.close()

    @pytest.mark.asyncio
    async def test_acquire_read_connection_round_robin(self, splitter):
        """Test acquiring read connections with round-robin load balancing."""
        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager, patch(
            "src.database.connection.read_write_splitter.HealthChecker"
        ) as mock_health_checker:
            # Setup mocks
            mock_pools = [AsyncMock() for _ in range(4)]  # 1 primary + 3 replicas
            mock_pool_manager.side_effect = mock_pools

            mock_health_checkers = [AsyncMock() for _ in range(4)]
            for health_checker in mock_health_checkers:
                health_checker.start_monitoring.return_value = None
                health_checker.stop_monitoring.return_value = None
            mock_health_checker.side_effect = mock_health_checkers

            # Mock all replicas as healthy
            for health_checker in mock_health_checkers[1:]:  # Skip primary
                health_checker.get_health_status.return_value = HealthStatus.HEALTHY

            mock_connections = [AsyncMock() for _ in range(3)]
            for i, pool in enumerate(mock_pools[1:]):  # Skip primary
                pool.acquire_connection.return_value = mock_connections[i]
                pool.close_pool.return_value = None
                # Mock the acquire method for health checkers (DatabasePoolManager.acquire)
                mock_context_manager = AsyncMock()
                mock_context_manager.__aenter__ = AsyncMock(
                    return_value=mock_connections[i]
                )
                mock_context_manager.__aexit__ = AsyncMock(return_value=None)
                pool.acquire = AsyncMock(return_value=mock_context_manager)
                # Mock fetchval for health check
                mock_connections[i].fetchval = AsyncMock(return_value=1)

            # Mock primary pool for health checker
            mock_pools[0].close_pool.return_value = None
            mock_primary_connection = AsyncMock()
            mock_primary_connection.fetchval = AsyncMock(return_value=1)
            mock_primary_context = AsyncMock()
            mock_primary_context.__aenter__ = AsyncMock(return_value=mock_primary_connection)
            mock_primary_context.__aexit__ = AsyncMock(return_value=None)
            mock_pools[0].acquire = AsyncMock(return_value=mock_primary_context)

            try:
                await splitter.initialize()

                # Test round-robin distribution
                acquired_pools = []
                for _ in range(6):  # More than number of replicas to test cycling
                    connection, pool = await splitter.acquire_connection(read_only=True)
                    acquired_pools.append(pool)

                # Verify round-robin behavior (should cycle through replicas)
                assert len(set(acquired_pools)) <= 3  # Should use at most 3 different pools
            finally:
                # Clean up resources
                await splitter.close()

    @pytest.mark.asyncio
    async def test_acquire_read_connection_random(
        self, primary_config, replica_configs
    ):
        """Test acquiring read connections with random load balancing."""
        splitter = ReadWriteSplitter(
            primary_config=primary_config,
            replica_configs=replica_configs,
            load_balancing_strategy=LoadBalancingStrategy.RANDOM,
        )

        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager, patch(
            "src.database.connection.read_write_splitter.HealthChecker"
        ) as mock_health_checker, patch(
            "random.choice"
        ) as mock_random:
            # Setup mocks
            mock_pools = [AsyncMock() for _ in range(4)]
            for pool in mock_pools:
                pool.close_pool.return_value = None
            mock_pool_manager.side_effect = mock_pools

            mock_health_checkers = [AsyncMock() for _ in range(4)]
            for health_checker in mock_health_checkers:
                health_checker.start_monitoring.return_value = None
                health_checker.stop_monitoring.return_value = None
                health_checker.get_health_status.return_value = HealthStatus.HEALTHY
            mock_health_checker.side_effect = mock_health_checkers

            # Mock random choice to return first replica
            mock_random.return_value = mock_pools[1]

            mock_connection = AsyncMock()
            mock_pools[1].acquire_connection.return_value = mock_connection

            # Mock the acquire method for health checkers (DatabasePoolManager.acquire)
            mock_context_manager = AsyncMock()
            mock_context_manager.__aenter__ = AsyncMock(return_value=mock_connection)
            mock_context_manager.__aexit__ = AsyncMock(return_value=None)
            for pool in mock_pools:
                pool.acquire = AsyncMock(return_value=mock_context_manager)
            # Mock fetchval for health check
            mock_connection.fetchval = AsyncMock(return_value=1)

            try:
                await splitter.initialize()

                # Test random selection
                connection, pool = await splitter.acquire_connection(read_only=True)

                assert connection == mock_connection
                assert pool == mock_pools[1]
                mock_random.assert_called_once()
            finally:
                # Clean up resources
                await splitter.close()

    @pytest.mark.asyncio
    async def test_acquire_read_connection_least_connections(
        self, primary_config, replica_configs
    ):
        """Test acquiring read connections with least connections strategy."""
        splitter = ReadWriteSplitter(
            primary_config=primary_config,
            replica_configs=replica_configs,
            load_balancing_strategy=LoadBalancingStrategy.LEAST_CONNECTIONS,
        )

        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager, patch(
            "src.database.connection.read_write_splitter.HealthChecker"
        ) as mock_health_checker:
            # Setup mocks
            mock_pools = [AsyncMock() for _ in range(4)]
            for pool in mock_pools:
                pool.close_pool.return_value = None
            mock_pool_manager.side_effect = mock_pools

            # Mock the acquire method for health checkers (DatabasePoolManager.acquire)
            for mock_pool in mock_pools:
                mock_context_manager = AsyncMock()
                mock_connection_for_health = AsyncMock()
                mock_connection_for_health.fetchval = AsyncMock(return_value=1)
                mock_context_manager.__aenter__ = AsyncMock(return_value=mock_connection_for_health)
                mock_context_manager.__aexit__ = AsyncMock(return_value=None)
                mock_pool.acquire = AsyncMock(return_value=mock_context_manager)

            mock_health_checkers = [AsyncMock() for _ in range(4)]
            for health_checker in mock_health_checkers:
                health_checker.start_monitoring.return_value = None
                health_checker.stop_monitoring.return_value = None
                health_checker.get_health_status.return_value = HealthStatus.HEALTHY
            mock_health_checker.side_effect = mock_health_checkers

            try:
                await splitter.initialize()

                # Set different connection counts for replicas
                splitter.replica_connection_counts = [
                    5,
                    2,
                    8,
                ]  # Second replica has least connections

                mock_connection = AsyncMock()
                mock_pools[
                    2
                ].acquire_connection.return_value = (
                    mock_connection  # Index 2 = second replica
                )

                # Test least connections selection
                connection, pool = await splitter.acquire_connection(read_only=True)

                assert connection == mock_connection
                assert pool == mock_pools[2]  # Should select replica with least connections
                assert (
                    splitter.replica_connection_counts[1] == 3
                )  # Count should be incremented
            finally:
                # Clean up resources
                await splitter.close()

    @pytest.mark.asyncio
    async def test_fallback_to_primary_when_no_replicas_available(self, splitter):
        """Test fallback to primary when no replicas are available."""
        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager, patch(
            "src.database.connection.read_write_splitter.HealthChecker"
        ) as mock_health_checker:
            # Setup mocks
            mock_pools = [AsyncMock() for _ in range(4)]
            for pool in mock_pools:
                pool.close_pool.return_value = None
            mock_pool_manager.side_effect = mock_pools

            # Mock the internal _pool for health checkers
            for mock_pool in mock_pools:
                mock_internal_pool = AsyncMock()
                mock_pool._pool = mock_internal_pool
                mock_context_manager = AsyncMock()
                mock_context_manager.__aenter__ = AsyncMock(return_value=AsyncMock())
                mock_context_manager.__aexit__ = AsyncMock(return_value=None)
                mock_internal_pool.acquire.return_value = mock_context_manager

            mock_health_checkers = [AsyncMock() for _ in range(4)]
            for health_checker in mock_health_checkers:
                health_checker.start_monitoring.return_value = None
                health_checker.stop_monitoring.return_value = None
                health_checker.get_health_status.return_value = HealthStatus.HEALTHY
            # Mock replicas as unhealthy
            for health_checker in mock_health_checkers[1:]:
                health_checker.get_health_status.return_value = HealthStatus.UNHEALTHY
            mock_health_checker.side_effect = mock_health_checkers

            mock_connection = AsyncMock()
            mock_pools[
                0
            ].acquire_connection.return_value = mock_connection  # Primary pool

            # Mock the internal _pool for health checkers
            for mock_pool in mock_pools:
                mock_internal_pool = AsyncMock()
                mock_pool._pool = mock_internal_pool
                mock_context_manager = AsyncMock()
                mock_context_manager.__aenter__ = AsyncMock(
                    return_value=mock_connection
                )
                mock_context_manager.__aexit__ = AsyncMock(return_value=None)
                mock_internal_pool.acquire.return_value = mock_context_manager

            try:
                await splitter.initialize()

                # Test fallback to primary
                connection, pool = await splitter.acquire_connection(read_only=True)

                assert connection == mock_connection
                assert pool == mock_pools[0]  # Should use primary pool
            finally:
                # Clean up resources
                await splitter.close()

    @pytest.mark.asyncio
    async def test_no_fallback_raises_exception(self, primary_config, replica_configs):
        """Test that NoAvailableReplicasError is raised when fallback is disabled."""
        splitter = ReadWriteSplitter(
            primary_config=primary_config,
            replica_configs=replica_configs,
            fallback_to_primary=False,  # Disable fallback
        )

        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager, patch(
            "src.database.connection.read_write_splitter.HealthChecker"
        ) as mock_health_checker:
            # Setup mocks
            mock_pools = [AsyncMock() for _ in range(4)]
            for pool in mock_pools:
                pool.close_pool.return_value = None
                # Mock the internal _pool for health checkers
                mock_internal_pool = AsyncMock()
                pool._pool = mock_internal_pool
                mock_context_manager = AsyncMock()
                mock_context_manager.__aenter__ = AsyncMock(return_value=AsyncMock())
                mock_context_manager.__aexit__ = AsyncMock(return_value=None)
                mock_internal_pool.acquire.return_value = mock_context_manager
            mock_pool_manager.side_effect = mock_pools

            mock_health_checkers = [AsyncMock() for _ in range(4)]
            for health_checker in mock_health_checkers:
                health_checker.start_monitoring.return_value = None
                health_checker.stop_monitoring.return_value = None
                health_checker.get_health_status.return_value = HealthStatus.HEALTHY
            # Mock replicas as unhealthy
            for health_checker in mock_health_checkers[1:]:
                health_checker.get_health_status.return_value = HealthStatus.UNHEALTHY
            mock_health_checker.side_effect = mock_health_checkers

            try:
                await splitter.initialize()

                # Test that exception is raised
                with pytest.raises(NoAvailableReplicasError):
                    await splitter.acquire_connection(read_only=True)
            finally:
                # Clean up resources
                await splitter.close()

    @pytest.mark.asyncio
    async def test_primary_unavailable_raises_exception(self, splitter):
        """Test that PrimaryDatabaseUnavailableError is raised when primary is unavailable."""
        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager:
            # Setup mock to raise exception for primary pool
            mock_pool = AsyncMock()
            mock_pool.acquire_connection.side_effect = Exception(
                "Primary database unavailable"
            )
            mock_pool.close_pool.return_value = None
            mock_pool_manager.return_value = mock_pool

            # Mock the internal _pool for health checkers
            mock_internal_pool = AsyncMock()
            mock_pool._pool = mock_internal_pool
            mock_context_manager = AsyncMock()
            mock_context_manager.__aenter__ = AsyncMock(return_value=AsyncMock())
            mock_context_manager.__aexit__ = AsyncMock(return_value=None)
            mock_internal_pool.acquire.return_value = mock_context_manager

            try:
                await splitter.initialize()

                # Test that exception is raised for write operations
                with pytest.raises(PrimaryDatabaseUnavailableError):
                    await splitter.acquire_connection(read_only=False)
            finally:
                # Clean up resources
                await splitter.close()

    @pytest.mark.asyncio
    async def test_get_connection_context_manager(self, splitter):
        """Test the get_connection context manager."""
        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager:
            # Setup mocks
            mock_pool = AsyncMock()
            mock_connection = AsyncMock()
            mock_pool.acquire_connection.return_value = mock_connection
            mock_pool.close_pool.return_value = None
            mock_pool_manager.return_value = mock_pool

            # Mock acquire() to return async context manager
            mock_pool.acquire.return_value.__aenter__ = AsyncMock(
                return_value=mock_connection
            )
            mock_pool.acquire.return_value.__aexit__ = AsyncMock(return_value=None)

            try:
                await splitter.initialize()

                # Test context manager
                async with splitter.get_connection(read_only=False) as conn:
                    assert conn == mock_connection

                # Verify connection was released
                mock_pool.release_connection.assert_called_once_with(mock_connection)
            finally:
                # Clean up resources
                await splitter.close()

    @pytest.mark.asyncio
    async def test_get_health_status(self, splitter):
        """Test getting health status of all databases."""
        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager, patch(
            "src.database.connection.read_write_splitter.HealthChecker"
        ) as mock_health_checker:
            # Setup mocks
            mock_pools = [AsyncMock() for _ in range(4)]
            for pool in mock_pools:
                pool.close_pool.return_value = None
                # Mock the internal _pool for health checkers
                mock_internal_pool = AsyncMock()
                pool._pool = mock_internal_pool
                mock_context_manager = AsyncMock()
                mock_context_manager.__aenter__ = AsyncMock(return_value=AsyncMock())
                mock_context_manager.__aexit__ = AsyncMock(return_value=None)
                mock_internal_pool.acquire.return_value = mock_context_manager
            mock_pool_manager.side_effect = mock_pools

            mock_health_checkers = [AsyncMock() for _ in range(4)]
            for health_checker in mock_health_checkers:
                health_checker.start_monitoring.return_value = None
                health_checker.stop_monitoring.return_value = None
            mock_health_checker.side_effect = mock_health_checkers

            # Mock health status
            mock_health_checkers[
                0
            ].get_health_status.return_value = HealthStatus.HEALTHY  # Primary
            mock_health_checkers[
                1
            ].get_health_status.return_value = HealthStatus.HEALTHY  # Replica 1
            mock_health_checkers[
                2
            ].get_health_status.return_value = HealthStatus.UNHEALTHY  # Replica 2
            mock_health_checkers[
                3
            ].get_health_status.return_value = HealthStatus.HEALTHY  # Replica 3

            try:
                await splitter.initialize()

                # Test health status
                health_status = await splitter.get_health_status()

                assert health_status["summary"]["primary_healthy"] is True
                assert health_status["summary"]["healthy_replicas"] == 2
                assert health_status["summary"]["total_replicas"] == 3
                assert len(health_status["details"]["replicas"]) == 3
            finally:
                # Clean up resources
                await splitter.close()

    def test_get_stats(self, splitter):
        """Test getting connection statistics."""
        # Set some connection counts
        splitter.replica_connection_counts = [5, 3, 7]

        stats = splitter.get_stats()

        assert (
            stats["load_balancing_strategy"] == LoadBalancingStrategy.ROUND_ROBIN.value
        )
        assert stats["replica_connection_counts"] == [5, 3, 7]
        assert stats["fallback_to_primary"] is True
        assert stats["health_check_interval"] == 30

    @pytest.mark.asyncio
    async def test_close(self, splitter):
        """Test closing all connections and resources."""
        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager:
            # Setup mocks
            mock_pools = [AsyncMock() for _ in range(4)]
            mock_pool_manager.side_effect = mock_pools

            await splitter.initialize()
            await splitter.close()

            # Verify all pools were closed
            for mock_pool in mock_pools:
                mock_pool.close_pool.assert_called_once()

    @pytest.mark.asyncio
    async def test_replica_failover_during_operation(self, splitter):
        """Test replica failover during operation."""
        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager, patch(
            "src.database.connection.read_write_splitter.HealthChecker"
        ) as mock_health_checker:
            # Setup mocks
            mock_pools = [AsyncMock() for _ in range(4)]
            for pool in mock_pools:
                pool.close_pool.return_value = None
                # Mock the internal _pool for health checkers
                mock_internal_pool = AsyncMock()
                pool._pool = mock_internal_pool
                mock_context_manager = AsyncMock()
                mock_context_manager.__aenter__ = AsyncMock(return_value=AsyncMock())
                mock_context_manager.__aexit__ = AsyncMock(return_value=None)
                mock_internal_pool.acquire.return_value = mock_context_manager
            mock_pool_manager.side_effect = mock_pools

            mock_health_checkers = [AsyncMock() for _ in range(4)]
            for health_checker in mock_health_checkers:
                health_checker.start_monitoring.return_value = None
                health_checker.stop_monitoring.return_value = None
                health_checker.get_health_status.return_value = HealthStatus.HEALTHY
            mock_health_checker.side_effect = mock_health_checkers

            # Initially all replicas are healthy
            for health_checker in mock_health_checkers[1:]:
                health_checker.get_health_status.return_value = HealthStatus.HEALTHY

            # First replica fails, second succeeds
            mock_pools[1].acquire_connection.side_effect = Exception(
                "Connection failed"
            )
            mock_connection = AsyncMock()
            mock_pools[2].acquire_connection.return_value = mock_connection

            # Mock acquire() to return async context manager for second replica
            mock_pools[2].acquire.return_value.__aenter__ = AsyncMock(
                return_value=mock_connection
            )
            mock_pools[2].acquire.return_value.__aexit__ = AsyncMock(return_value=None)

            try:
                await splitter.initialize()

                # Test failover to next replica
                connection, pool = await splitter.acquire_connection(read_only=True)

                assert connection == mock_connection
                assert pool == mock_pools[2]  # Should use second replica
            finally:
                # Clean up resources
                await splitter.close()

    @pytest.mark.asyncio
    async def test_concurrent_connections(self, splitter):
        """Test handling multiple concurrent connections."""
        with patch(
            "src.database.connection.read_write_splitter.DatabasePoolManager"
        ) as mock_pool_manager, patch(
            "src.database.connection.read_write_splitter.HealthChecker"
        ) as mock_health_checker:
            # Setup mocks
            mock_pools = [AsyncMock() for _ in range(4)]
            for pool in mock_pools:
                pool.close_pool.return_value = None
            mock_pool_manager.side_effect = mock_pools

            # Mock the internal _pool attribute for health checkers
            for mock_pool in mock_pools:
                mock_internal_pool = AsyncMock()
                mock_pool._pool = mock_internal_pool
                mock_context_manager = AsyncMock()
                mock_context_manager.__aenter__ = AsyncMock(return_value=AsyncMock())
                mock_context_manager.__aexit__ = AsyncMock(return_value=None)
                mock_internal_pool.acquire.return_value = mock_context_manager

            mock_health_checkers = [AsyncMock() for _ in range(4)]
            for health_checker in mock_health_checkers:
                health_checker.start_monitoring.return_value = None
                health_checker.stop_monitoring.return_value = None
                health_checker.get_health_status.return_value = HealthStatus.HEALTHY
            mock_health_checker.side_effect = mock_health_checkers

            # Mock all replicas as healthy
            for health_checker in mock_health_checkers[1:]:  # Skip primary
                health_checker.get_health_status.return_value = HealthStatus.HEALTHY

            # Mock connections
            for pool in mock_pools[1:]:
                mock_conn = AsyncMock()
                pool.acquire_connection.return_value = mock_conn
                # Mock acquire() to return async context manager
                pool.acquire.return_value.__aenter__ = AsyncMock(return_value=mock_conn)
                pool.acquire.return_value.__aexit__ = AsyncMock(return_value=None)

            try:
                await splitter.initialize()

                # Test concurrent read connections
                tasks = []
                for _ in range(10):
                    task = asyncio.create_task(splitter.acquire_connection(read_only=True))
                    tasks.append(task)

                results = await asyncio.gather(*tasks)

                # Verify all connections were acquired
                assert len(results) == 10
                for connection, pool in results:
                    assert connection is not None
                    assert pool is not None
            finally:
                # Clean up resources
                await splitter.close()


if __name__ == "__main__":
    pytest.main([__file__])
