# -*- coding: utf-8 -*-
"""
Mock data generator for testing purposes.

This module provides utilities for generating mock data for various test scenarios,
including database records, API responses, and file content.
"""

import random
import string
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from faker import Faker
import pandas as pd
import numpy as np
import json


# Initialize Faker instance
fake = Faker()


@dataclass
class MockDataConfig:
    """Configuration for mock data generation."""
    seed: Optional[int] = None
    locale: str = 'en_US'
    size: int = 100
    start_date: datetime = field(default_factory=lambda: datetime(2020, 1, 1))
    end_date: datetime = field(default_factory=lambda: datetime.now())
    
    def __post_init__(self):
        if self.seed is not None:
            Faker.seed(self.seed)
            random.seed(self.seed)
            np.random.seed(self.seed)


class MockDataGenerator:
    """Main class for generating mock data."""
    
    def __init__(self, config: Optional[MockDataConfig] = None):
        self.config = config or MockDataConfig()
        self.fake = Faker(self.config.locale)
        
        if self.config.seed is not None:
            Faker.seed(self.config.seed)
            random.seed(self.config.seed)
            np.random.seed(self.config.seed)
    
    def generate_user_data(self, count: Optional[int] = None) -> List[Dict[str, Any]]:
        """Generate mock user data.
        
        Args:
            count: Number of users to generate. If None, uses config.size.
            
        Returns:
            List of user dictionaries.
        """
        count = count or self.config.size
        users = []
        
        for i in range(count):
            user = {
                'id': i + 1,
                'uuid': str(uuid.uuid4()),
                'username': self.fake.user_name(),
                'email': self.fake.email(),
                'first_name': self.fake.first_name(),
                'last_name': self.fake.last_name(),
                'phone': self.fake.phone_number(),
                'address': {
                    'street': self.fake.street_address(),
                    'city': self.fake.city(),
                    'state': self.fake.state(),
                    'zip_code': self.fake.zipcode(),
                    'country': self.fake.country()
                },
                'date_of_birth': self.fake.date_of_birth(minimum_age=18, maximum_age=80),
                'created_at': self.fake.date_time_between(
                    start_date=self.config.start_date,
                    end_date=self.config.end_date
                ),
                'is_active': self.fake.boolean(chance_of_getting_true=80),
                'profile': {
                    'bio': self.fake.text(max_nb_chars=200),
                    'avatar_url': self.fake.image_url(),
                    'website': self.fake.url(),
                    'social_media': {
                        'twitter': f"@{self.fake.user_name()}",
                        'linkedin': self.fake.url(),
                        'github': f"https://github.com/{self.fake.user_name()}"
                    }
                }
            }
            users.append(user)
        
        return users
    
    def generate_product_data(self, count: Optional[int] = None) -> List[Dict[str, Any]]:
        """Generate mock product data.
        
        Args:
            count: Number of products to generate. If None, uses config.size.
            
        Returns:
            List of product dictionaries.
        """
        count = count or self.config.size
        products = []
        
        categories = ['Electronics', 'Clothing', 'Books', 'Home & Garden', 'Sports', 'Toys']
        brands = ['BrandA', 'BrandB', 'BrandC', 'BrandD', 'BrandE']
        
        for i in range(count):
            product = {
                'id': i + 1,
                'sku': f"SKU-{random.randint(100000, 999999)}",
                'name': self.fake.catch_phrase(),
                'description': self.fake.text(max_nb_chars=500),
                'category': random.choice(categories),
                'brand': random.choice(brands),
                'price': round(random.uniform(10.0, 1000.0), 2),
                'cost': round(random.uniform(5.0, 500.0), 2),
                'weight': round(random.uniform(0.1, 50.0), 2),
                'dimensions': {
                    'length': round(random.uniform(1.0, 100.0), 1),
                    'width': round(random.uniform(1.0, 100.0), 1),
                    'height': round(random.uniform(1.0, 100.0), 1)
                },
                'stock_quantity': random.randint(0, 1000),
                'is_available': self.fake.boolean(chance_of_getting_true=85),
                'rating': round(random.uniform(1.0, 5.0), 1),
                'review_count': random.randint(0, 500),
                'tags': [self.fake.word() for _ in range(random.randint(1, 5))],
                'created_at': self.fake.date_time_between(
                    start_date=self.config.start_date,
                    end_date=self.config.end_date
                ),
                'updated_at': self.fake.date_time_between(
                    start_date=self.config.start_date,
                    end_date=self.config.end_date
                )
            }
            products.append(product)
        
        return products
    
    def generate_order_data(self, count: Optional[int] = None, user_ids: Optional[List[int]] = None) -> List[Dict[str, Any]]:
        """Generate mock order data.
        
        Args:
            count: Number of orders to generate. If None, uses config.size.
            user_ids: List of user IDs to use. If None, generates random IDs.
            
        Returns:
            List of order dictionaries.
        """
        count = count or self.config.size
        if user_ids is None:
            user_ids = list(range(1, min(count, 100) + 1))
        
        orders = []
        statuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled']
        payment_methods = ['credit_card', 'debit_card', 'paypal', 'bank_transfer']
        
        for i in range(count):
            order_date = self.fake.date_time_between(
                start_date=self.config.start_date,
                end_date=self.config.end_date
            )
            
            order = {
                'id': i + 1,
                'order_number': f"ORD-{random.randint(100000, 999999)}",
                'user_id': random.choice(user_ids),
                'status': random.choice(statuses),
                'total_amount': round(random.uniform(20.0, 2000.0), 2),
                'tax_amount': round(random.uniform(2.0, 200.0), 2),
                'shipping_amount': round(random.uniform(5.0, 50.0), 2),
                'discount_amount': round(random.uniform(0.0, 100.0), 2),
                'payment_method': random.choice(payment_methods),
                'shipping_address': {
                    'street': self.fake.street_address(),
                    'city': self.fake.city(),
                    'state': self.fake.state(),
                    'zip_code': self.fake.zipcode(),
                    'country': self.fake.country()
                },
                'order_date': order_date,
                'shipped_date': order_date + timedelta(days=random.randint(1, 7)) if random.random() > 0.3 else None,
                'delivered_date': order_date + timedelta(days=random.randint(3, 14)) if random.random() > 0.5 else None,
                'items': self._generate_order_items(random.randint(1, 5))
            }
            orders.append(order)
        
        return orders
    
    def _generate_order_items(self, count: int) -> List[Dict[str, Any]]:
        """Generate order items for an order."""
        items = []
        for i in range(count):
            item = {
                'product_id': random.randint(1, 1000),
                'quantity': random.randint(1, 10),
                'unit_price': round(random.uniform(10.0, 500.0), 2),
                'total_price': 0  # Will be calculated
            }
            item['total_price'] = round(item['quantity'] * item['unit_price'], 2)
            items.append(item)
        return items
    
    def generate_log_data(self, count: Optional[int] = None) -> List[Dict[str, Any]]:
        """Generate mock log data.
        
        Args:
            count: Number of log entries to generate. If None, uses config.size.
            
        Returns:
            List of log entry dictionaries.
        """
        count = count or self.config.size
        logs = []
        
        log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        services = ['auth-service', 'user-service', 'order-service', 'payment-service', 'notification-service']
        
        for i in range(count):
            log = {
                'id': i + 1,
                'timestamp': self.fake.date_time_between(
                    start_date=self.config.start_date,
                    end_date=self.config.end_date
                ),
                'level': random.choice(log_levels),
                'service': random.choice(services),
                'message': self.fake.sentence(),
                'user_id': random.randint(1, 1000) if random.random() > 0.3 else None,
                'session_id': str(uuid.uuid4()),
                'ip_address': self.fake.ipv4(),
                'user_agent': self.fake.user_agent(),
                'request_id': str(uuid.uuid4()),
                'duration_ms': random.randint(10, 5000),
                'status_code': random.choice([200, 201, 400, 401, 403, 404, 500, 502, 503]),
                'metadata': {
                    'endpoint': f"/api/v1/{self.fake.word()}",
                    'method': random.choice(['GET', 'POST', 'PUT', 'DELETE']),
                    'response_size': random.randint(100, 10000)
                }
            }
            logs.append(log)
        
        return logs
    
    def generate_geospatial_data(self, count: Optional[int] = None) -> List[Dict[str, Any]]:
        """Generate mock geospatial data.
        
        Args:
            count: Number of geospatial points to generate. If None, uses config.size.
            
        Returns:
            List of geospatial data dictionaries.
        """
        count = count or self.config.size
        points = []
        
        for i in range(count):
            point = {
                'id': i + 1,
                'name': self.fake.city(),
                'latitude': float(self.fake.latitude()),
                'longitude': float(self.fake.longitude()),
                'altitude': round(random.uniform(-100.0, 8000.0), 2),
                'accuracy': round(random.uniform(1.0, 100.0), 2),
                'timestamp': self.fake.date_time_between(
                    start_date=self.config.start_date,
                    end_date=self.config.end_date
                ),
                'properties': {
                    'country': self.fake.country(),
                    'region': self.fake.state(),
                    'population': random.randint(1000, 10000000),
                    'timezone': self.fake.timezone()
                }
            }
            points.append(point)
        
        return points
    
    def generate_time_series_data(self, 
                                 start_date: Optional[datetime] = None,
                                 end_date: Optional[datetime] = None,
                                 frequency: str = 'H',
                                 metrics: Optional[List[str]] = None) -> pd.DataFrame:
        """Generate mock time series data.
        
        Args:
            start_date: Start date for time series. If None, uses config.start_date.
            end_date: End date for time series. If None, uses config.end_date.
            frequency: Pandas frequency string (e.g., 'H', 'D', 'M').
            metrics: List of metric names to generate. If None, uses default metrics.
            
        Returns:
            DataFrame with time series data.
        """
        start_date = start_date or self.config.start_date
        end_date = end_date or self.config.end_date
        metrics = metrics or ['cpu_usage', 'memory_usage', 'disk_usage', 'network_io']
        
        # Generate date range
        date_range = pd.date_range(start=start_date, end=end_date, freq=frequency)
        
        # Generate data for each metric
        data = {'timestamp': date_range}
        
        for metric in metrics:
            if 'usage' in metric:
                # Generate usage percentages (0-100)
                base_value = random.uniform(20, 80)
                noise = np.random.normal(0, 10, len(date_range))
                values = np.clip(base_value + noise, 0, 100)
            elif 'count' in metric:
                # Generate count data
                base_value = random.randint(100, 1000)
                noise = np.random.poisson(base_value, len(date_range))
                values = noise
            else:
                # Generate general numeric data
                base_value = random.uniform(0, 1000)
                noise = np.random.normal(0, base_value * 0.1, len(date_range))
                values = np.maximum(0, base_value + noise)
            
            data[metric] = values
        
        return pd.DataFrame(data)
    
    def generate_api_response(self, 
                             status_code: int = 200,
                             data_type: str = 'users',
                             count: Optional[int] = None) -> Dict[str, Any]:
        """Generate mock API response.
        
        Args:
            status_code: HTTP status code.
            data_type: Type of data to include ('users', 'products', 'orders').
            count: Number of items to include. If None, uses config.size.
            
        Returns:
            Dictionary representing API response.
        """
        count = count or min(self.config.size, 20)  # Limit for API responses
        
        if status_code == 200:
            if data_type == 'users':
                data = self.generate_user_data(count)
            elif data_type == 'products':
                data = self.generate_product_data(count)
            elif data_type == 'orders':
                data = self.generate_order_data(count)
            else:
                data = [{'id': i, 'value': self.fake.word()} for i in range(count)]
            
            response = {
                'status': 'success',
                'data': data,
                'meta': {
                    'total': count,
                    'page': 1,
                    'per_page': count,
                    'total_pages': 1
                },
                'timestamp': datetime.now().isoformat()
            }
        else:
            response = {
                'status': 'error',
                'error': {
                    'code': status_code,
                    'message': self.fake.sentence(),
                    'details': self.fake.text(max_nb_chars=100)
                },
                'timestamp': datetime.now().isoformat()
            }
        
        return response
    
    def generate_csv_content(self, data_type: str = 'users', count: Optional[int] = None) -> str:
        """Generate CSV content as string.
        
        Args:
            data_type: Type of data to generate ('users', 'products', 'orders').
            count: Number of rows to generate. If None, uses config.size.
            
        Returns:
            CSV content as string.
        """
        count = count or self.config.size
        
        if data_type == 'users':
            data = self.generate_user_data(count)
            # Flatten nested data for CSV
            flattened_data = []
            for user in data:
                flat_user = {
                    'id': user['id'],
                    'username': user['username'],
                    'email': user['email'],
                    'first_name': user['first_name'],
                    'last_name': user['last_name'],
                    'phone': user['phone'],
                    'city': user['address']['city'],
                    'state': user['address']['state'],
                    'country': user['address']['country'],
                    'date_of_birth': user['date_of_birth'].strftime('%Y-%m-%d'),
                    'created_at': user['created_at'].strftime('%Y-%m-%d %H:%M:%S'),
                    'is_active': user['is_active']
                }
                flattened_data.append(flat_user)
            
            df = pd.DataFrame(flattened_data)
        
        elif data_type == 'products':
            data = self.generate_product_data(count)
            # Flatten for CSV
            flattened_data = []
            for product in data:
                flat_product = {
                    'id': product['id'],
                    'sku': product['sku'],
                    'name': product['name'],
                    'category': product['category'],
                    'brand': product['brand'],
                    'price': product['price'],
                    'cost': product['cost'],
                    'stock_quantity': product['stock_quantity'],
                    'is_available': product['is_available'],
                    'rating': product['rating'],
                    'review_count': product['review_count'],
                    'created_at': product['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                }
                flattened_data.append(flat_product)
            
            df = pd.DataFrame(flattened_data)
        
        else:
            # Generate simple tabular data
            df = pd.DataFrame({
                'id': range(1, count + 1),
                'name': [self.fake.name() for _ in range(count)],
                'value': [round(random.uniform(0, 100), 2) for _ in range(count)],
                'category': [random.choice(['A', 'B', 'C']) for _ in range(count)],
                'date': [self.fake.date() for _ in range(count)]
            })
        
        return df.to_csv(index=False)
    
    def generate_json_content(self, data_type: str = 'users', count: Optional[int] = None) -> str:
        """Generate JSON content as string.
        
        Args:
            data_type: Type of data to generate ('users', 'products', 'orders').
            count: Number of items to generate. If None, uses config.size.
            
        Returns:
            JSON content as string.
        """
        count = count or self.config.size
        
        if data_type == 'users':
            data = self.generate_user_data(count)
        elif data_type == 'products':
            data = self.generate_product_data(count)
        elif data_type == 'orders':
            data = self.generate_order_data(count)
        else:
            data = [{'id': i, 'value': self.fake.word()} for i in range(count)]
        
        return json.dumps(data, indent=2, default=str)


# Convenience functions
def create_mock_users(count: int = 10, seed: Optional[int] = None) -> List[Dict[str, Any]]:
    """Create mock user data quickly."""
    config = MockDataConfig(size=count, seed=seed)
    generator = MockDataGenerator(config)
    return generator.generate_user_data()


def create_mock_products(count: int = 10, seed: Optional[int] = None) -> List[Dict[str, Any]]:
    """Create mock product data quickly."""
    config = MockDataConfig(size=count, seed=seed)
    generator = MockDataGenerator(config)
    return generator.generate_product_data()


def create_mock_orders(count: int = 10, seed: Optional[int] = None) -> List[Dict[str, Any]]:
    """Create mock order data quickly."""
    config = MockDataConfig(size=count, seed=seed)
    generator = MockDataGenerator(config)
    return generator.generate_order_data()


def create_mock_dataframe(rows: int = 100, 
                         columns: Optional[List[str]] = None,
                         seed: Optional[int] = None) -> pd.DataFrame:
    """Create a mock DataFrame quickly.
    
    Args:
        rows: Number of rows to generate.
        columns: List of column names. If None, uses default columns.
        seed: Random seed for reproducibility.
        
    Returns:
        Mock DataFrame.
    """
    if seed is not None:
        np.random.seed(seed)
        random.seed(seed)
    
    columns = columns or ['id', 'name', 'value', 'category', 'date']
    
    data = {}
    for col in columns:
        if col == 'id':
            data[col] = range(1, rows + 1)
        elif col in ['name', 'username', 'email']:
            data[col] = [fake.name() for _ in range(rows)]
        elif col in ['value', 'price', 'amount']:
            data[col] = np.random.uniform(0, 1000, rows)
        elif col in ['category', 'type', 'status']:
            data[col] = np.random.choice(['A', 'B', 'C'], rows)
        elif col in ['date', 'created_at', 'updated_at']:
            data[col] = pd.date_range('2023-01-01', periods=rows, freq='D')
        elif col in ['is_active', 'is_available']:
            data[col] = np.random.choice([True, False], rows)
        else:
            # Default to random strings
            data[col] = [fake.word() for _ in range(rows)]
    
    return pd.DataFrame(data)


if __name__ == "__main__":
    # Example usage
    config = MockDataConfig(size=5, seed=42)
    generator = MockDataGenerator(config)
    
    # Generate different types of data
    users = generator.generate_user_data()
    products = generator.generate_product_data()
    orders = generator.generate_order_data()
    
    print("Generated Users:")
    for user in users[:2]:
        print(f"  {user['username']} ({user['email']})")
    
    print("\nGenerated Products:")
    for product in products[:2]:
        print(f"  {product['name']} - ${product['price']}")
    
    print("\nGenerated Orders:")
    for order in orders[:2]:
        print(f"  Order {order['order_number']} - ${order['total_amount']}")
    
    # Generate time series data
    ts_data = generator.generate_time_series_data(
        start_date=datetime(2023, 1, 1),
        end_date=datetime(2023, 1, 7),
        frequency='H'
    )
    print(f"\nTime series data shape: {ts_data.shape}")
    print(ts_data.head())