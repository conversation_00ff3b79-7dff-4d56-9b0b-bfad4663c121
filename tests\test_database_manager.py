"""Tests for DatabaseManager class.

This module contains comprehensive tests for database-level operations
including database creation, existence checking, and schema initialization.
"""

import asyncio
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, patch

import asyncpg
import pytest

from src.database.exceptions import <PERSON>Error, DatabaseError, ValidationError
from src.database.operations.database_manager import DatabaseManager
from src.database.utils.validators import InputValidator


class TestDatabaseManager:
    """Test cases for DatabaseManager class."""

    @pytest.fixture
    def mock_pool(self):
        """Create a mock connection pool."""
        pool = AsyncMock(spec=asyncpg.Pool)
        return pool

    @pytest.fixture
    def mock_validator(self):
        """Create a mock validator."""
        validator = MagicMock(spec=InputValidator)
        validator.validate_identifier.return_value = True
        return validator

    @pytest.fixture
    def database_manager(self, mock_pool, mock_validator):
        """Create a DatabaseManager instance with mocked dependencies."""
        with patch(
            "src.database.operations.database_manager.SchemaManager"
        ) as mock_schema_manager:
            manager = DatabaseManager(mock_pool, mock_validator)
            manager.schema_manager = mock_schema_manager.return_value
            return manager

    @pytest.mark.asyncio
    async def test_database_exists_true(self, database_manager, mock_pool):
        """Test database_exists returns True when database exists."""
        # Setup
        mock_conn = AsyncMock()
        mock_conn.fetchval.return_value = True
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        
        # Mock the validation method to avoid connection validation issues
        database_manager._validate_connection = AsyncMock()

        # Execute
        result = await database_manager.database_exists("test_db")

        # Assert
        assert result is True
        # Check that fetchval was called with the correct query
        assert mock_conn.fetchval.call_count >= 1
        # Verify the database existence query was called
        calls = mock_conn.fetchval.call_args_list
        db_check_call = None
        for call in calls:
            if "pg_database" in str(call):
                db_check_call = call
                break
        assert db_check_call is not None
        assert "test_db" in str(db_check_call)

    @pytest.mark.asyncio
    async def test_database_exists_false(self, database_manager, mock_pool):
        """Test database_exists returns False when database doesn't exist."""
        # Setup
        mock_conn = AsyncMock()
        mock_conn.fetchval.return_value = False
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn

        # Execute
        result = await database_manager.database_exists("nonexistent_db")

        # Assert
        assert result is False

    @pytest.mark.asyncio
    async def test_database_exists_invalid_name(self, database_manager, mock_validator):
        """Test database_exists raises ValidationError for invalid name."""
        # Setup
        mock_validator.validate_identifier.return_value = False

        # Execute & Assert
        with pytest.raises(ValidationError, match="Invalid database name"):
            await database_manager.database_exists("invalid-name")

    @pytest.mark.asyncio
    async def test_create_database_success(self, database_manager, mock_pool):
        """Test successful database creation."""
        # Setup
        mock_conn = AsyncMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn

        # Mock database_exists to return False (doesn't exist)
        database_manager.database_exists = AsyncMock(return_value=False)

        # Execute
        result = await database_manager.create_database("new_db")

        # Assert
        assert result is True
        mock_conn.execute.assert_called_once_with('CREATE DATABASE "new_db"')

    @pytest.mark.asyncio
    async def test_create_database_with_owner(self, database_manager, mock_pool):
        """Test database creation with owner."""
        # Setup
        mock_conn = AsyncMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn

        # Mock database_exists to return False
        database_manager.database_exists = AsyncMock(return_value=False)

        # Execute
        result = await database_manager.create_database("new_db", "test_owner")

        # Assert
        assert result is True
        mock_conn.execute.assert_called_once_with(
            'CREATE DATABASE "new_db" OWNER "test_owner"'
        )

    @pytest.mark.asyncio
    async def test_create_database_already_exists(self, database_manager, mock_pool):
        """Test create_database when database already exists."""
        # Setup
        mock_conn = AsyncMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn

        # Mock database_exists to return True
        database_manager.database_exists = AsyncMock(return_value=True)

        # Execute
        result = await database_manager.create_database("existing_db")

        # Assert
        assert result is True
        # execute should not be called since database already exists
        mock_conn.execute.assert_not_called()

    @pytest.mark.asyncio
    async def test_ensure_database_exists_creates_new(self, database_manager):
        """Test ensure_database_exists creates new database."""
        # Setup
        database_manager.database_exists = AsyncMock(return_value=False)
        database_manager.create_database = AsyncMock(return_value=True)

        # Execute
        result = await database_manager.ensure_database_exists("test_db")

        # Assert
        assert result is True
        database_manager.create_database.assert_called_once_with("test_db", None)

    @pytest.mark.asyncio
    async def test_ensure_database_exists_already_exists(self, database_manager):
        """Test ensure_database_exists when database already exists."""
        # Setup
        database_manager.database_exists = AsyncMock(return_value=True)
        database_manager.create_database = AsyncMock()

        # Execute
        result = await database_manager.ensure_database_exists("existing_db")

        # Assert
        assert result is True
        database_manager.create_database.assert_not_called()

    @pytest.mark.asyncio
    async def test_initialize_schemas_success(self, database_manager):
        """Test successful schema initialization."""
        # Setup
        schema_list = ["schema1", "schema2", "schema3"]
        database_manager.schema_manager.ensure_schema_exists = AsyncMock(
            return_value=True
        )

        # Execute
        result = await database_manager.initialize_schemas(schema_list)

        # Assert
        expected_result = {"schema1": True, "schema2": True, "schema3": True}
        assert result == expected_result
        assert database_manager.schema_manager.ensure_schema_exists.call_count == 3

    @pytest.mark.asyncio
    async def test_initialize_schemas_partial_failure(self, database_manager):
        """Test schema initialization with partial failures."""
        # Setup
        schema_list = ["schema1", "schema2", "schema3"]

        async def mock_ensure_schema(schema_name):
            if schema_name == "schema2":
                raise DatabaseError("Failed to create schema2")
            return True

        database_manager.schema_manager.ensure_schema_exists = AsyncMock(
            side_effect=mock_ensure_schema
        )

        # Execute
        result = await database_manager.initialize_schemas(schema_list)

        # Assert
        expected_result = {"schema1": True, "schema2": False, "schema3": True}
        assert result == expected_result

    @pytest.mark.asyncio
    async def test_initialize_schemas_empty_list(self, database_manager):
        """Test initialize_schemas with empty list."""
        # Execute
        result = await database_manager.initialize_schemas([])

        # Assert
        assert result == {}

    @pytest.mark.asyncio
    async def test_get_database_info_success(self, database_manager, mock_pool):
        """Test successful database info retrieval."""
        # Setup
        mock_conn = AsyncMock()
        mock_conn.fetchval.side_effect = [
            "test_database",  # current_database()
            "100 MB",  # database size
            "PostgreSQL 13.0",  # version
            "UTF8",  # encoding
            5,  # connection count
        ]
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn

        # Mock schema manager
        database_manager.schema_manager.list_schemas = AsyncMock(
            return_value=["public", "test_schema"]
        )

        # Execute
        result = await database_manager.get_database_info()

        # Assert
        expected_result = {
            "database_name": "test_database",
            "database_size": "100 MB",
            "postgresql_version": "PostgreSQL 13.0",
            "encoding": "UTF8",
            "active_connections": 5,
            "schemas": ["public", "test_schema"],
            "schema_count": 2,
        }
        assert result == expected_result

    @pytest.mark.asyncio
    async def test_list_databases_success(self, database_manager, mock_pool):
        """Test successful database listing."""
        # Setup
        mock_conn = AsyncMock()
        mock_rows = [
            {
                "name": "db1",
                "owner": "user1",
                "encoding": "UTF8",
                "collate": "en_US.UTF-8",
                "ctype": "en_US.UTF-8",
                "size": "50 MB",
            },
            {
                "name": "db2",
                "owner": "user2",
                "encoding": "UTF8",
                "collate": "en_US.UTF-8",
                "ctype": "en_US.UTF-8",
                "size": "100 MB",
            },
        ]
        mock_conn.fetch.return_value = mock_rows
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn

        # Execute
        result = await database_manager.list_databases()

        # Assert
        assert result == mock_rows
        assert len(result) == 2

    @pytest.mark.asyncio
    async def test_database_error_handling(self, database_manager, mock_pool):
        """Test proper error handling for database operations."""
        # Setup
        mock_conn = AsyncMock()
        mock_conn.fetchval.side_effect = asyncpg.PostgresError("Connection failed")
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn

        # Execute & Assert
        with pytest.raises(DatabaseError, match="Connection health check failed"):
            await database_manager.database_exists("test_db")
