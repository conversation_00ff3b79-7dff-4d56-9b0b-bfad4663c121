#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 数据库测试执行器
基于docs/database/database-framework.md需求的测试执行引擎

本模块提供：
- 测试执行调度和管理
- 并行测试执行
- 测试环境管理
- 结果收集和聚合
- 失败重试机制
- 实时监控和报告
"""

import asyncio
import logging
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import json
import yaml
import signal
import sys
from contextlib import contextmanager

from tests.framework.comprehensive_test_framework import (
    TestExecutionResult,
    TestPriority,
    TestStatus,
    TestType
)
from tests.framework.database_test_suites import DatabaseTestSuiteManager
from tests.framework.database_quality_gates import (
    DatabaseQualityGateManager,
    QualityGateReport
)
from tests.framework.database_test_reporter import DatabaseTestReporter
from tests.framework.database_test_data_generator import DatabaseTestDataGenerator


class ExecutionMode(Enum):
    """执行模式"""
    SEQUENTIAL = "sequential"      # 顺序执行
    PARALLEL = "parallel"          # 并行执行
    PRIORITY_BASED = "priority"    # 基于优先级执行
    FAIL_FAST = "fail_fast"        # 快速失败
    CONTINUE_ON_FAILURE = "continue"  # 失败继续


class RetryStrategy(Enum):
    """重试策略"""
    NONE = "none"                  # 不重试
    IMMEDIATE = "immediate"        # 立即重试
    EXPONENTIAL = "exponential"    # 指数退避
    LINEAR = "linear"              # 线性退避
    CUSTOM = "custom"              # 自定义策略


@dataclass
class ExecutionConfig:
    """执行配置"""
    mode: ExecutionMode = ExecutionMode.PRIORITY_BASED
    max_workers: int = 4
    timeout_seconds: int = 3600
    retry_strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    max_retries: int = 3
    retry_delay: float = 1.0
    fail_fast: bool = False
    continue_on_failure: bool = True
    parallel_priorities: bool = False
    enable_monitoring: bool = True
    enable_profiling: bool = True
    cleanup_on_exit: bool = True
    save_intermediate_results: bool = True
    intermediate_save_interval: int = 300  # 5分钟
    
    # 环境配置
    setup_timeout: int = 300
    teardown_timeout: int = 300
    isolation_level: str = "test_suite"  # test_suite, test_case, none
    
    # 质量门控配置
    enable_quality_gates: bool = True
    quality_gate_blocking: bool = True
    quality_gate_on_failure: bool = True
    
    # 报告配置
    generate_reports: bool = True
    report_formats: List[str] = field(default_factory=lambda: ["html", "json"])
    real_time_reporting: bool = True


@dataclass
class ExecutionContext:
    """执行上下文"""
    execution_id: str
    start_time: datetime
    config: ExecutionConfig
    test_suites: Dict[TestPriority, List[str]] = field(default_factory=dict)
    results: Dict[TestPriority, List[TestExecutionResult]] = field(default_factory=dict)
    quality_reports: List[QualityGateReport] = field(default_factory=list)
    metrics: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    status: TestStatus = TestStatus.PENDING
    current_priority: Optional[TestPriority] = None
    current_suite: Optional[str] = None
    progress: float = 0.0
    estimated_completion: Optional[datetime] = None
    
    def __post_init__(self):
        if not hasattr(self, 'end_time'):
            self.end_time: Optional[datetime] = None


@dataclass
class ExecutionStats:
    """执行统计"""
    total_suites: int = 0
    completed_suites: int = 0
    failed_suites: int = 0
    skipped_suites: int = 0
    total_tests: int = 0
    passed_tests: int = 0
    failed_tests: int = 0
    skipped_tests: int = 0
    total_duration: float = 0.0
    avg_duration_per_test: float = 0.0
    success_rate: float = 0.0
    
    def update_from_results(self, results: Dict[TestPriority, List[TestExecutionResult]]):
        """从测试结果更新统计信息"""
        self.total_suites = 0
        self.completed_suites = 0
        self.failed_suites = 0
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.skipped_tests = 0
        self.total_duration = 0.0
        
        for priority_results in results.values():
            for result in priority_results:
                self.total_suites += 1
                self.total_duration += result.execution_time
                
                if result.status == TestStatus.PASSED:
                    self.completed_suites += 1
                elif result.status == TestStatus.FAILED:
                    self.failed_suites += 1
                elif result.status == TestStatus.SKIPPED:
                    self.skipped_suites += 1
                
                self.total_tests += result.tests_total
                self.passed_tests += result.tests_passed
                self.failed_tests += result.tests_failed
                self.skipped_tests += result.tests_skipped
        
        # 计算派生指标
        if self.total_tests > 0:
            self.success_rate = (self.passed_tests / self.total_tests) * 100
            self.avg_duration_per_test = self.total_duration / self.total_tests
        else:
            self.success_rate = 0.0
            self.avg_duration_per_test = 0.0


class DatabaseTestExecutor:
    """数据库测试执行器"""
    
    def __init__(self, config: Optional[ExecutionConfig] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or ExecutionConfig()
        
        # 核心组件
        self.suite_manager = DatabaseTestSuiteManager()
        self.quality_gate_manager = DatabaseQualityGateManager()
        self.reporter = DatabaseTestReporter()
        self.data_generator = DatabaseTestDataGenerator()
        
        # 执行状态
        self.context: Optional[ExecutionContext] = None
        self.stats = ExecutionStats()
        self.is_running = False
        self.is_cancelled = False
        self.executor: Optional[ThreadPoolExecutor] = None
        
        # 监控和回调
        self.progress_callbacks: List[Callable[[ExecutionContext], None]] = []
        self.completion_callbacks: List[Callable[[ExecutionContext], None]] = []
        self.error_callbacks: List[Callable[[str, Exception], None]] = []
        
        # 信号处理
        self._setup_signal_handlers()
        
        # 中间结果保存
        self._last_save_time = time.time()
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，开始优雅关闭...")
            self.cancel_execution()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def add_progress_callback(self, callback: Callable[[ExecutionContext], None]):
        """添加进度回调"""
        self.progress_callbacks.append(callback)
    
    def add_completion_callback(self, callback: Callable[[ExecutionContext], None]):
        """添加完成回调"""
        self.completion_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable[[str, Exception], None]):
        """添加错误回调"""
        self.error_callbacks.append(callback)
    
    def execute_all_tests(self, 
                         priorities: Optional[List[TestPriority]] = None,
                         test_suites: Optional[List[str]] = None,
                         output_dir: Optional[Path] = None) -> ExecutionContext:
        """执行所有测试"""
        execution_id = f"exec_{int(time.time())}"
        
        # 创建执行上下文
        self.context = ExecutionContext(
            execution_id=execution_id,
            start_time=datetime.now(),
            config=self.config
        )
        
        self.logger.info(f"开始执行测试 - ID: {execution_id}")
        
        try:
            self.is_running = True
            self.is_cancelled = False
            
            # 设置测试套件
            self._setup_test_suites(priorities, test_suites)
            
            # 执行测试
            if self.config.mode == ExecutionMode.SEQUENTIAL:
                self._execute_sequential()
            elif self.config.mode == ExecutionMode.PARALLEL:
                self._execute_parallel()
            elif self.config.mode == ExecutionMode.PRIORITY_BASED:
                self._execute_priority_based()
            else:
                raise ValueError(f"不支持的执行模式: {self.config.mode}")
            
            # 质量门控检查
            if self.config.enable_quality_gates:
                self._run_quality_gates()
            
            # 生成报告
            if self.config.generate_reports and output_dir:
                self._generate_reports(output_dir)
            
            # 更新最终状态
            self._finalize_execution()
            
        except Exception as e:
            self.logger.error(f"测试执行失败: {e}")
            self.context.status = TestStatus.FAILED
            self.context.errors.append(str(e))
            
            # 调用错误回调
            for callback in self.error_callbacks:
                try:
                    callback("execution_failed", e)
                except Exception as cb_error:
                    self.logger.error(f"错误回调执行失败: {cb_error}")
        
        finally:
            self.is_running = False
            self.context.end_time = datetime.now()
            
            # 清理资源
            if self.config.cleanup_on_exit:
                self._cleanup()
            
            # 调用完成回调
            for callback in self.completion_callbacks:
                try:
                    callback(self.context)
                except Exception as cb_error:
                    self.logger.error(f"完成回调执行失败: {cb_error}")
        
        return self.context
    
    def _setup_test_suites(self, 
                          priorities: Optional[List[TestPriority]] = None,
                          test_suites: Optional[List[str]] = None):
        """设置测试套件"""
        self.logger.info("设置测试套件")
        
        if priorities is None:
            priorities = [TestPriority.P0, TestPriority.P1, TestPriority.P2, TestPriority.P3]
        
        for priority in priorities:
            if test_suites:
                # 使用指定的测试套件
                self.context.test_suites[priority] = test_suites
            else:
                # 获取该优先级的所有测试套件
                available_suites = self.suite_manager.get_test_suites_by_priority(priority)
                self.context.test_suites[priority] = [suite.__class__.__name__ for suite in available_suites]
        
        # 初始化结果字典
        for priority in priorities:
            self.context.results[priority] = []
        
        total_suites = sum(len(suites) for suites in self.context.test_suites.values())
        self.logger.info(f"设置完成，总共 {total_suites} 个测试套件")
    
    def _execute_sequential(self):
        """顺序执行测试"""
        self.logger.info("开始顺序执行测试")
        
        total_suites = sum(len(suites) for suites in self.context.test_suites.values())
        completed_suites = 0
        
        for priority in [TestPriority.P0, TestPriority.P1, TestPriority.P2, TestPriority.P3]:
            if priority not in self.context.test_suites:
                continue
            
            self.context.current_priority = priority
            self.logger.info(f"执行 {priority.value} 优先级测试")
            
            for suite_name in self.context.test_suites[priority]:
                if self.is_cancelled:
                    break
                
                self.context.current_suite = suite_name
                result = self._execute_single_suite(priority, suite_name)
                self.context.results[priority].append(result)
                
                completed_suites += 1
                self.context.progress = (completed_suites / total_suites) * 100
                
                # 更新进度
                self._update_progress()
                
                # 快速失败检查
                if self.config.fail_fast and result.status == TestStatus.FAILED:
                    self.logger.warning("快速失败模式：检测到失败，停止执行")
                    self.context.status = TestStatus.FAILED
                    return
                
                # 保存中间结果
                self._save_intermediate_results()
            
            if self.is_cancelled:
                break
    
    def _execute_parallel(self):
        """并行执行测试"""
        self.logger.info(f"开始并行执行测试，最大工作线程: {self.config.max_workers}")
        
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            self.executor = executor
            futures = {}
            
            # 提交所有任务
            for priority, suite_names in self.context.test_suites.items():
                for suite_name in suite_names:
                    future = executor.submit(self._execute_single_suite, priority, suite_name)
                    futures[future] = (priority, suite_name)
            
            total_suites = len(futures)
            completed_suites = 0
            
            # 收集结果
            for future in as_completed(futures, timeout=self.config.timeout_seconds):
                if self.is_cancelled:
                    break
                
                priority, suite_name = futures[future]
                
                try:
                    result = future.result()
                    self.context.results[priority].append(result)
                    
                    completed_suites += 1
                    self.context.progress = (completed_suites / total_suites) * 100
                    
                    self.logger.info(f"完成测试套件: {suite_name} ({priority.value})")
                    
                    # 更新进度
                    self._update_progress()
                    
                    # 快速失败检查
                    if self.config.fail_fast and result.status == TestStatus.FAILED:
                        self.logger.warning("快速失败模式：检测到失败，取消剩余任务")
                        self.cancel_execution()
                        break
                
                except Exception as e:
                    self.logger.error(f"测试套件 {suite_name} 执行失败: {e}")
                    
                    # 创建失败结果
                    failed_result = TestExecutionResult(
                        test_suite=suite_name,
                        status=TestStatus.FAILED,
                        execution_time=0.0,
                        tests_total=0,
                        tests_passed=0,
                        tests_failed=1,
                        tests_skipped=0,
                        error_message=str(e)
                    )
                    self.context.results[priority].append(failed_result)
                    
                    completed_suites += 1
                    self.context.progress = (completed_suites / total_suites) * 100
                    self._update_progress()
                
                # 保存中间结果
                self._save_intermediate_results()
    
    def _execute_priority_based(self):
        """基于优先级执行测试"""
        self.logger.info("开始基于优先级执行测试")
        
        for priority in [TestPriority.P0, TestPriority.P1, TestPriority.P2, TestPriority.P3]:
            if priority not in self.context.test_suites or self.is_cancelled:
                continue
            
            self.context.current_priority = priority
            self.logger.info(f"执行 {priority.value} 优先级测试")
            
            if self.config.parallel_priorities and len(self.context.test_suites[priority]) > 1:
                # 在优先级内并行执行
                self._execute_priority_parallel(priority)
            else:
                # 在优先级内顺序执行
                self._execute_priority_sequential(priority)
            
            # 检查P0级别的结果
            if priority == TestPriority.P0:
                p0_results = self.context.results[priority]
                if any(result.status == TestStatus.FAILED for result in p0_results):
                    self.logger.error("P0级别测试失败，停止后续测试")
                    self.context.status = TestStatus.FAILED
                    return
    
    def _execute_priority_sequential(self, priority: TestPriority):
        """在优先级内顺序执行"""
        for suite_name in self.context.test_suites[priority]:
            if self.is_cancelled:
                break
            
            self.context.current_suite = suite_name
            result = self._execute_single_suite(priority, suite_name)
            self.context.results[priority].append(result)
            
            self._update_progress()
            self._save_intermediate_results()
            
            # 快速失败检查
            if self.config.fail_fast and result.status == TestStatus.FAILED:
                self.logger.warning("快速失败模式：检测到失败，停止执行")
                return
    
    def _execute_priority_parallel(self, priority: TestPriority):
        """在优先级内并行执行"""
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            futures = {}
            
            for suite_name in self.context.test_suites[priority]:
                future = executor.submit(self._execute_single_suite, priority, suite_name)
                futures[future] = suite_name
            
            for future in as_completed(futures, timeout=self.config.timeout_seconds):
                if self.is_cancelled:
                    break
                
                suite_name = futures[future]
                
                try:
                    result = future.result()
                    self.context.results[priority].append(result)
                    
                    self._update_progress()
                    self._save_intermediate_results()
                    
                    if self.config.fail_fast and result.status == TestStatus.FAILED:
                        self.cancel_execution()
                        break
                
                except Exception as e:
                    self.logger.error(f"测试套件 {suite_name} 执行失败: {e}")
                    
                    failed_result = TestExecutionResult(
                        test_suite=suite_name,
                        status=TestStatus.FAILED,
                        execution_time=0.0,
                        tests_total=0,
                        tests_passed=0,
                        tests_failed=1,
                        tests_skipped=0,
                        error_message=str(e)
                    )
                    self.context.results[priority].append(failed_result)
                    self._update_progress()
    
    def _execute_single_suite(self, priority: TestPriority, suite_name: str) -> TestExecutionResult:
        """执行单个测试套件"""
        self.logger.info(f"执行测试套件: {suite_name} ({priority.value})")
        
        start_time = time.time()
        
        try:
            # 获取测试套件实例
            suite_instance = self.suite_manager.get_test_suite_instance(priority, suite_name)
            if not suite_instance:
                raise ValueError(f"找不到测试套件: {suite_name}")
            
            # 执行测试套件
            result = self._execute_with_retry(suite_instance)
            
            # 更新执行时间
            result.execution_time = time.time() - start_time
            
            self.logger.info(f"测试套件 {suite_name} 执行完成: {result.status.value}")
            return result
            
        except Exception as e:
            self.logger.error(f"测试套件 {suite_name} 执行异常: {e}")
            
            return TestExecutionResult(
                test_suite=suite_name,
                status=TestStatus.FAILED,
                execution_time=time.time() - start_time,
                tests_total=0,
                tests_passed=0,
                tests_failed=1,
                tests_skipped=0,
                error_message=str(e)
            )
    
    def _execute_with_retry(self, suite_instance) -> TestExecutionResult:
        """带重试的执行"""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"重试执行测试套件，第 {attempt} 次")
                    
                    # 应用重试延迟
                    delay = self._calculate_retry_delay(attempt)
                    if delay > 0:
                        time.sleep(delay)
                
                # 执行测试套件
                result = suite_instance.run_tests()
                
                # 如果成功或者不需要重试的失败，直接返回
                if result.status != TestStatus.FAILED or not self._should_retry(result):
                    return result
                
                last_exception = Exception(result.error_message or "测试失败")
                
            except Exception as e:
                last_exception = e
                self.logger.warning(f"测试套件执行失败 (尝试 {attempt + 1}): {e}")
                
                # 如果是最后一次尝试，抛出异常
                if attempt == self.config.max_retries:
                    raise e
        
        # 如果所有重试都失败了
        if last_exception:
            raise last_exception
        
        # 这种情况理论上不应该发生
        raise Exception("未知的执行失败")
    
    def _calculate_retry_delay(self, attempt: int) -> float:
        """计算重试延迟"""
        if self.config.retry_strategy == RetryStrategy.NONE:
            return 0.0
        elif self.config.retry_strategy == RetryStrategy.IMMEDIATE:
            return 0.0
        elif self.config.retry_strategy == RetryStrategy.LINEAR:
            return self.config.retry_delay * attempt
        elif self.config.retry_strategy == RetryStrategy.EXPONENTIAL:
            return self.config.retry_delay * (2 ** (attempt - 1))
        else:
            return self.config.retry_delay
    
    def _should_retry(self, result: TestExecutionResult) -> bool:
        """判断是否应该重试"""
        if self.config.retry_strategy == RetryStrategy.NONE:
            return False
        
        # 可以根据错误类型决定是否重试
        # 例如：网络错误可以重试，语法错误不应该重试
        if result.error_message:
            # 简单的启发式规则
            retryable_errors = [
                "timeout", "connection", "network", "temporary", 
                "busy", "lock", "deadlock", "resource"
            ]
            
            error_msg = result.error_message.lower()
            return any(keyword in error_msg for keyword in retryable_errors)
        
        return True
    
    def _run_quality_gates(self):
        """运行质量门控检查"""
        self.logger.info("开始质量门控检查")
        
        try:
            # 评估测试结果
            quality_report = self.quality_gate_manager.evaluate_test_results(self.context.results)
            self.context.quality_reports.append(quality_report)
            
            self.logger.info(f"质量门控检查完成: {quality_report.overall_status}")
            
            # 如果质量门控失败且配置为阻塞
            if (quality_report.overall_status in ["FAILED", "BLOCKED"] and 
                self.config.quality_gate_blocking):
                
                self.logger.error("质量门控检查失败，标记执行为失败")
                self.context.status = TestStatus.FAILED
                
                # 添加质量门控失败信息
                failed_rules = [r for r in quality_report.results if not r.passed]
                for rule_result in failed_rules:
                    self.context.errors.append(f"质量门控失败: {rule_result.message}")
        
        except Exception as e:
            self.logger.error(f"质量门控检查异常: {e}")
            self.context.errors.append(f"质量门控检查异常: {e}")
            
            if self.config.quality_gate_on_failure:
                self.context.status = TestStatus.FAILED
    
    def _generate_reports(self, output_dir: Path):
        """生成测试报告"""
        self.logger.info(f"生成测试报告到: {output_dir}")
        
        try:
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 添加测试结果到报告器
            for priority, results in self.context.results.items():
                for result in results:
                    self.reporter.add_test_result(result)
            
            # 添加质量门控结果
            for quality_report in self.context.quality_reports:
                self.reporter.add_quality_gate(quality_report)
            
            # 添加执行元数据
            self.reporter.add_execution_metadata({
                'execution_id': self.context.execution_id,
                'start_time': self.context.start_time.isoformat(),
                'end_time': self.context.end_time.isoformat() if self.context.end_time else None,
                'config': {
                    'mode': self.config.mode.value,
                    'max_workers': self.config.max_workers,
                    'retry_strategy': self.config.retry_strategy.value,
                    'max_retries': self.config.max_retries
                },
                'stats': {
                    'total_suites': self.stats.total_suites,
                    'success_rate': self.stats.success_rate,
                    'total_duration': self.stats.total_duration
                }
            })
            
            # 生成不同格式的报告
            for report_format in self.config.report_formats:
                if report_format.lower() == "html":
                    html_path = output_dir / f"test_report_{self.context.execution_id}.html"
                    self.reporter.generate_html_report(html_path)
                    self.logger.info(f"HTML报告已生成: {html_path}")
                
                elif report_format.lower() == "json":
                    json_path = output_dir / f"test_report_{self.context.execution_id}.json"
                    self.reporter.generate_json_report(json_path)
                    self.logger.info(f"JSON报告已生成: {json_path}")
                
                elif report_format.lower() == "junit":
                    junit_path = output_dir / f"test_report_{self.context.execution_id}.xml"
                    self.reporter.generate_junit_report(junit_path)
                    self.logger.info(f"JUnit报告已生成: {junit_path}")
        
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            self.context.errors.append(f"生成报告失败: {e}")
    
    def _update_progress(self):
        """更新进度"""
        # 更新统计信息
        self.stats.update_from_results(self.context.results)
        
        # 估算完成时间
        if self.context.progress > 0:
            elapsed = (datetime.now() - self.context.start_time).total_seconds()
            estimated_total = elapsed / (self.context.progress / 100)
            self.context.estimated_completion = self.context.start_time + timedelta(seconds=estimated_total)
        
        # 调用进度回调
        for callback in self.progress_callbacks:
            try:
                callback(self.context)
            except Exception as e:
                self.logger.error(f"进度回调执行失败: {e}")
    
    def _save_intermediate_results(self):
        """保存中间结果"""
        if not self.config.save_intermediate_results:
            return
        
        current_time = time.time()
        if current_time - self._last_save_time < self.config.intermediate_save_interval:
            return
        
        try:
            # 创建中间结果文件
            intermediate_file = Path(f"intermediate_results_{self.context.execution_id}.json")
            
            # 序列化当前状态
            intermediate_data = {
                'execution_id': self.context.execution_id,
                'timestamp': datetime.now().isoformat(),
                'progress': self.context.progress,
                'status': self.context.status.value,
                'stats': {
                    'total_suites': self.stats.total_suites,
                    'completed_suites': self.stats.completed_suites,
                    'success_rate': self.stats.success_rate
                },
                'results_summary': {
                    priority.value: len(results) 
                    for priority, results in self.context.results.items()
                }
            }
            
            with open(intermediate_file, 'w', encoding='utf-8') as f:
                json.dump(intermediate_data, f, indent=2, ensure_ascii=False)
            
            self._last_save_time = current_time
            
        except Exception as e:
            self.logger.warning(f"保存中间结果失败: {e}")
    
    def _finalize_execution(self):
        """完成执行"""
        # 更新最终统计
        self.stats.update_from_results(self.context.results)
        
        # 确定最终状态
        if self.context.status == TestStatus.PENDING:
            if self.is_cancelled:
                self.context.status = TestStatus.SKIPPED
            elif any(any(result.status == TestStatus.FAILED for result in results) 
                    for results in self.context.results.values()):
                self.context.status = TestStatus.FAILED
            else:
                self.context.status = TestStatus.PASSED
        
        # 设置进度为100%
        self.context.progress = 100.0
        
        # 最后一次进度更新
        self._update_progress()
        
        self.logger.info(f"测试执行完成: {self.context.status.value}, "
                        f"成功率: {self.stats.success_rate:.1f}%, "
                        f"总耗时: {self.stats.total_duration:.2f}秒")
    
    def _cleanup(self):
        """清理资源"""
        self.logger.info("清理执行资源")
        
        try:
            # 关闭线程池
            if self.executor:
                self.executor.shutdown(wait=False)
            
            # 清理中间文件
            if self.context:
                intermediate_file = Path(f"intermediate_results_{self.context.execution_id}.json")
                if intermediate_file.exists():
                    intermediate_file.unlink()
            
            # 清理测试数据
            if hasattr(self.data_generator, 'cleanup'):
                self.data_generator.cleanup()
            
        except Exception as e:
            self.logger.warning(f"清理资源时出错: {e}")
    
    def cancel_execution(self):
        """取消执行"""
        self.logger.info("取消测试执行")
        self.is_cancelled = True
        
        if self.executor:
            self.executor.shutdown(wait=False)
        
        if self.context:
            self.context.status = TestStatus.SKIPPED
    
    def get_execution_status(self) -> Optional[ExecutionContext]:
        """获取执行状态"""
        return self.context
    
    def get_execution_stats(self) -> ExecutionStats:
        """获取执行统计"""
        return self.stats
    
    @contextmanager
    def execution_timeout(self, timeout_seconds: int):
        """执行超时上下文管理器"""
        def timeout_handler():
            self.logger.warning(f"执行超时 ({timeout_seconds}秒)，取消执行")
            self.cancel_execution()
        
        timer = threading.Timer(timeout_seconds, timeout_handler)
        timer.start()
        
        try:
            yield
        finally:
            timer.cancel()


def create_test_executor(config: Optional[ExecutionConfig] = None) -> DatabaseTestExecutor:
    """创建测试执行器的工厂函数"""
    return DatabaseTestExecutor(config)


if __name__ == "__main__":
    # 示例用法和CLI接口
    import argparse
    
    def setup_logging(level: str = "INFO"):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('database_test_executor.log')
            ]
        )
    
    def progress_callback(context: ExecutionContext):
        """进度回调示例"""
        print(f"\r进度: {context.progress:.1f}% - 当前: {context.current_suite}", end="", flush=True)
    
    def completion_callback(context: ExecutionContext):
        """完成回调示例"""
        print(f"\n执行完成: {context.status.value}")
        print(f"执行ID: {context.execution_id}")
        print(f"开始时间: {context.start_time}")
        print(f"结束时间: {context.end_time}")
        if context.errors:
            print(f"错误: {len(context.errors)}")
            for error in context.errors[:5]:  # 只显示前5个错误
                print(f"  - {error}")
    
    parser = argparse.ArgumentParser(description="数据库测试执行器")
    parser.add_argument('--mode', choices=['sequential', 'parallel', 'priority'], 
                       default='priority', help='执行模式')
    parser.add_argument('--workers', type=int, default=4, help='最大工作线程数')
    parser.add_argument('--timeout', type=int, default=3600, help='执行超时时间（秒）')
    parser.add_argument('--retry-strategy', choices=['none', 'immediate', 'exponential', 'linear'], 
                       default='exponential', help='重试策略')
    parser.add_argument('--max-retries', type=int, default=3, help='最大重试次数')
    parser.add_argument('--fail-fast', action='store_true', help='快速失败模式')
    parser.add_argument('--priorities', nargs='+', choices=['P0', 'P1', 'P2', 'P3'], 
                       help='要执行的优先级')
    parser.add_argument('--suites', nargs='+', help='要执行的测试套件')
    parser.add_argument('--output-dir', default='./test_reports', help='报告输出目录')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='日志级别')
    parser.add_argument('--no-quality-gates', action='store_true', help='禁用质量门控')
    parser.add_argument('--no-reports', action='store_true', help='禁用报告生成')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 创建执行配置
    config = ExecutionConfig(
        mode=ExecutionMode(args.mode),
        max_workers=args.workers,
        timeout_seconds=args.timeout,
        retry_strategy=RetryStrategy(args.retry_strategy),
        max_retries=args.max_retries,
        fail_fast=args.fail_fast,
        enable_quality_gates=not args.no_quality_gates,
        generate_reports=not args.no_reports
    )
    
    # 创建执行器
    executor = create_test_executor(config)
    
    # 添加回调
    executor.add_progress_callback(progress_callback)
    executor.add_completion_callback(completion_callback)
    
    # 解析优先级
    priorities = None
    if args.priorities:
        priorities = [TestPriority(p) for p in args.priorities]
    
    # 执行测试
    try:
        with executor.execution_timeout(args.timeout):
            context = executor.execute_all_tests(
                priorities=priorities,
                test_suites=args.suites,
                output_dir=Path(args.output_dir)
            )
        
        # 显示最终统计
        stats = executor.get_execution_stats()
        print(f"\n=== 执行统计 ===")
        print(f"总测试套件: {stats.total_suites}")
        print(f"完成套件: {stats.completed_suites}")
        print(f"失败套件: {stats.failed_suites}")
        print(f"总测试数: {stats.total_tests}")
        print(f"通过测试: {stats.passed_tests}")
        print(f"失败测试: {stats.failed_tests}")
        print(f"成功率: {stats.success_rate:.1f}%")
        print(f"总耗时: {stats.total_duration:.2f}秒")
        
        # 退出码
        exit_code = 0 if context.status == TestStatus.PASSED else 1
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
        executor.cancel_execution()
        sys.exit(130)
    
    except Exception as e:
        print(f"\n执行失败: {e}")
        sys.exit(1)