__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Database session management.

This module provides basic PostgreSQL connection management using asyncpg,
including connection acquisition, release, and error handling with custom exceptions.
"""

import asyncio
import logging
from typing import Any, Dict, Optional

import asyncpg
from asyncpg import Connection

# Handle relative imports with fallback
try:
    from ...config import get_config
    from ...config.models import ConnectConfig
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    # Add parent directories to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from config import get_config
    from config.models import ConnectConfig
from ..exceptions import (
    ConfigurationError,
    ConnectionError,
    DatabaseError,
    TimeoutError,
)

# Configure logging
logger = logging.getLogger(__name__)


class SessionManager:
    """Database session manager for handling connections."""

    def __init__(self, config: Optional[ConnectConfig] = None):
        """Initialize session manager with configuration.

        Args:
            config: Database configuration. If None, loads from default config.
        """
        self.config = config or get_config()
        self._connection: Optional[Connection] = None
        self._is_connected = False

    async def __aenter__(self) -> Connection:
        """Async context manager entry."""
        return await self.get_connection()

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        # Close the connection to prevent connection leaks
        await self.close_connection()

    async def get_connection(self) -> Connection:
        """Get database connection.

        Returns:
            asyncpg.Connection: Active database connection.

        Raises:
            ConnectionError: If connection fails.
            ConfigurationError: If configuration is invalid.
            TimeoutError: If connection times out.
        """
        if self._connection and not self._connection.is_closed():
            return self._connection

        try:
            # Build connection parameters from config
            connection_params = self._build_connection_params()

            logger.info(
                f"Connecting to database at {connection_params['host']}:{connection_params['port']}"
            )

            # Create connection with timeout
            timeout = getattr(self.config.database.pool, 'command_timeout', 30)
            self._connection = await asyncio.wait_for(
                asyncpg.connect(**connection_params), timeout=timeout
            )

            self._is_connected = True
            logger.info("Database connection established successfully")

            return self._connection

        except asyncio.TimeoutError as e:
            timeout = getattr(self.config.database.pool, 'command_timeout', 30)
            error_msg = (
                f"Database connection timeout after {timeout} seconds"
            )
            logger.error(error_msg)
            raise TimeoutError(
                error_msg,
                error_code="DB_CONNECTION_TIMEOUT",
                details={"timeout": timeout},
                original_exception=e,
            )

        except asyncpg.InvalidCatalogNameError as e:
            error_msg = f"Database '{self.config.database.name}' does not exist"
            logger.error(error_msg)
            raise ConfigurationError(
                error_msg,
                error_code="DB_INVALID_DATABASE",
                details={"database_name": self.config.database.name},
                original_exception=e,
            )

        except asyncpg.InvalidPasswordError as e:
            error_msg = "Invalid database credentials"
            logger.error(error_msg)
            raise ConnectionError(
                error_msg,
                error_code="DB_INVALID_CREDENTIALS",
                details={"user": self.config.database.user},
                original_exception=e,
            )

        except asyncpg.CannotConnectNowError as e:
            error_msg = "Database server is not accepting connections"
            logger.error(error_msg)
            raise ConnectionError(
                error_msg,
                error_code="DB_SERVER_UNAVAILABLE",
                details={
                    "host": self.config.database.host,
                    "port": self.config.database.port,
                },
                original_exception=e,
            )

        except asyncpg.ConnectionDoesNotExistError as e:
            error_msg = "Database connection does not exist"
            logger.error(error_msg)
            raise ConnectionError(
                error_msg, error_code="DB_CONNECTION_NOT_EXIST", original_exception=e
            )

        except Exception as e:
            error_msg = f"Unexpected error connecting to database: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ConnectionError(
                error_msg,
                error_code="DB_CONNECTION_FAILED",
                details={
                    "host": self.config.database.host,
                    "port": self.config.database.port,
                    "database": self.config.database.name,
                },
                original_exception=e,
            )

    async def close_connection(self) -> None:
        """Close database connection.

        Raises:
            ConnectionError: If connection close fails.
        """
        if not self._connection or self._connection.is_closed():
            logger.debug("No active connection to close")
            return

        try:
            logger.info("Closing database connection")
            await self._connection.close()
            self._is_connected = False
            self._connection = None
            logger.info("Database connection closed successfully")

        except Exception as e:
            error_msg = f"Error closing database connection: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise ConnectionError(
                error_msg, error_code="DB_CONNECTION_CLOSE_FAILED", original_exception=e
            )

    def _build_connection_params(self) -> Dict[str, Any]:
        """Build connection parameters from configuration.

        Returns:
            Dict containing connection parameters for asyncpg.

        Raises:
            ConfigurationError: If required configuration is missing.
        """
        try:
            return {
                "host": self.config.database.host,
                "port": self.config.database.port,
                "database": self.config.database.name,
                "user": self.config.database.user,
                "password": self.config.database.password,
                "command_timeout": getattr(self.config.database.pool, 'command_timeout', 60),
                "server_settings": {"application_name": "connect_database_framework"},
            }
        except AttributeError as e:
            error_msg = f"Missing required configuration: {str(e)}"
            logger.error(error_msg)
            raise ConfigurationError(
                error_msg, error_code="DB_CONFIG_MISSING", original_exception=e
            )

    @property
    def is_connected(self) -> bool:
        """Check if connection is active.

        Returns:
            bool: True if connection is active, False otherwise.
        """
        return (
            self._is_connected
            and self._connection is not None
            and not self._connection.is_closed()
        )


# Global session manager instance
_session_manager: Optional[SessionManager] = None


def get_session_manager(config: Optional[ConnectConfig] = None) -> SessionManager:
    """Get or create global session manager instance.

    Args:
        config: Database configuration. If None, uses existing or creates default.

    Returns:
        SessionManager: Global session manager instance.
    """
    global _session_manager

    if _session_manager is None or config is not None:
        _session_manager = SessionManager(config)

    return _session_manager


async def get_db_connection(config: Optional[ConnectConfig] = None) -> Connection:
    """Get database connection using global session manager.

    Args:
        config: Optional database configuration.

    Returns:
        asyncpg.Connection: Active database connection.

    Raises:
        ConnectionError: If connection fails.
        ConfigurationError: If configuration is invalid.
        TimeoutError: If connection times out.
    """
    session_manager = get_session_manager(config)
    return await session_manager.get_connection()


async def close_db_connection(conn: Optional[Connection] = None) -> None:
    """Close database connection.

    Args:
        conn: Optional asyncpg.Connection object to close.
              If None, closes the global session manager connection.

    Raises:
        ConnectionError: If connection close fails.
    """
    if conn is None:
        # Close global session manager connection
        session_manager = get_session_manager()
        await session_manager.close_connection()
    else:
        # Close specific connection
        if conn and not conn.is_closed():
            try:
                logger.info("Closing specific database connection")
                await conn.close()
                logger.info("Database connection closed successfully")
            except Exception as e:
                error_msg = f"Error closing database connection: {str(e)}"
                logger.error(error_msg, exc_info=True)
                raise ConnectionError(
                    error_msg,
                    error_code="DB_CONNECTION_CLOSE_FAILED",
                    original_exception=e,
                )
        else:
            logger.debug("Connection is None or already closed")


async def test_connection(config: Optional[ConnectConfig] = None) -> bool:
    """Test database connection.

    Args:
        config: Optional database configuration.

    Returns:
        bool: True if connection successful, False otherwise.
    """
    connection = None
    try:
        connection = await get_db_connection(config)
        # Execute simple query to verify connection
        result = await connection.fetchval("SELECT 1")
        return result == 1
    except Exception as e:
        logger.error(f"Connection test failed: {str(e)}")
        return False
    finally:
        try:
            if connection:
                await close_db_connection(connection)
        except Exception as e:
            # Log cleanup errors but don't raise them
            logger.warning(f"Error during connection cleanup: {e}")


# Alias for backward compatibility
get_connection = get_db_connection
DatabaseSession = SessionManager  # Alias for backward compatibility
