"""Database framework for Connect platform."""

# Version information
__version__ = "0.1.0"
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
__description__ = "Database framework for Connect platform"

from .config import get_config, get_database_config, get_connection_params
from .connection import SessionManager, get_session_manager
from .exceptions import (
    ConfigurationError,
    ConnectionError,
    DatabaseError,
    QueryError,
    SchemaError,
    SchemaNotFoundError,
    TableExistsError,
    TableNotFoundError,
    TimeoutError,
    TransactionError,
)
from .monitoring import get_logger, setup_logging
from .operations import CRUDOperations
from .types import DataSourceType
from .utils import InputValidator

__all__ = [
    # Configuration
    "Config",
    "DatabaseConfig",
    "LoggingConfig",
    # Connection
    "get_session_manager",
    "SessionManager",
    # Exceptions
    "DatabaseError",
    "ConnectionError",
    "ConfigurationError",
    "QueryError",
    "TransactionError",
    "SchemaError",
    "TimeoutError",
    "TableNotFoundError",
    "TableExistsError",
    "SchemaNotFoundError",
    # Monitoring
    "setup_logging",
    "get_logger",
    # Types
    "DataSourceType",
    # Utils
    "InputValidator",
    # Operations
    "CRUDOperations",
]
