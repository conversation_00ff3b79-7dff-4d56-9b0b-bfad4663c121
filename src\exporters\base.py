__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Base exporter classes and exceptions.

This module provides the base classes and exceptions for all data exporters.
"""

import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# Configure logging
logger = logging.getLogger(__name__)


class ExportError(Exception):
    """Base exception for export operations."""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        original_exception: Optional[Exception] = None,
    ):
        super().__init__(message)
        self.error_code = error_code
        self.original_exception = original_exception


@dataclass
class ExportResult:
    """Result of an export operation."""

    success: bool
    file_path: Optional[Path] = None
    records_exported: int = 0
    export_time: Optional[datetime] = None
    file_size_bytes: int = 0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.export_time is None:
            self.export_time = datetime.now()


class BaseExporter(ABC):
    """Abstract base class for all data exporters."""

    def __init__(self, output_path: Union[str, Path], **kwargs):
        """Initialize the exporter.

        Args:
            output_path: Path where exported data will be saved
            **kwargs: Additional configuration options
        """
        self.output_path = Path(output_path)
        self.config = kwargs
        logger.info(
            f"Initialized {self.__class__.__name__} with output path: {self.output_path}"
        )

    @abstractmethod
    async def export(self, data: Any, **kwargs) -> ExportResult:
        """Export data to the specified format.

        Args:
            data: Data to export
            **kwargs: Additional export options

        Returns:
            ExportResult: Result of the export operation

        Raises:
            ExportError: If export fails
        """
        pass

    def validate_data(self, data: Any) -> bool:
        """Validate data before export.

        Args:
            data: Data to validate

        Returns:
            bool: True if data is valid

        Raises:
            ExportError: If data is invalid
        """
        if data is None:
            raise ExportError("Data cannot be None")
        return True

    def prepare_output_directory(self) -> None:
        """Ensure output directory exists."""
        self.output_path.parent.mkdir(parents=True, exist_ok=True)
