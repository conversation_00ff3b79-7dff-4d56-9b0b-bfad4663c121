#!/usr/bin/env python3
"""
测试结果聚合脚本

本脚本用于收集和聚合各种类型的测试结果，包括单元测试、集成测试、
E2E测试、性能测试和安全测试的结果，生成统一的测试报告。
"""

import argparse
import json
import logging
import os
import xml.etree.ElementTree as ET
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

import pandas as pd
from junitparser import JUnitXml

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestResultsAggregator:
    """测试结果聚合器 - 增强版"""
    
    def __init__(self, input_dir: str, output_file: str):
        """初始化聚合器
        
        Args:
            input_dir: 测试结果输入目录
            output_file: 聚合结果输出文件
        """
        self.input_dir = Path(input_dir)
        self.output_file = Path(output_file)
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "environment": os.getenv('TEST_ENVIRONMENT', 'unknown'),
            "commit_sha": os.getenv('GITHUB_SHA', 'unknown'),
            "branch": os.getenv('GITHUB_REF_NAME', 'unknown'),
            "workflow_run_id": os.getenv('GITHUB_RUN_ID', 'unknown'),
            "summary": {
                "total_tests": 0,
                "passed_tests": 0,
                "failed_tests": 0,
                "skipped_tests": 0,
                "error_tests": 0,
                "success_rate": 0.0,
                "total_duration": 0.0
            },
            "test_suites": {},
            "performance_metrics": {},
            "security_summary": {
                "total_issues": 0,
                "critical_issues": 0,
                "high_issues": 0,
                "medium_issues": 0,
                "low_issues": 0,
                "vulnerability_scans": [],
                "static_analysis": {},
                "dependency_check": {}
            },
            "quality_metrics": {
                "code_analysis": {},
                "complexity_metrics": {},
                "style_checks": {},
                "type_checks": {},
                "overall_score": 0.0
            },
            "coverage_data": {
                "line_coverage": 0.0,
                "branch_coverage": 0.0,
                "function_coverage": 0.0,
                "statement_coverage": 0.0
            },
            "artifacts": [],
            "trends": {},
            "alerts": []
        }
        
    def aggregate_all_results(self) -> Dict[str, Any]:
        """聚合所有测试结果
        
        Returns:
            聚合后的测试结果
        """
        logger.info(f"开始聚合测试结果，输入目录: {self.input_dir}")
        
        # 聚合各类测试结果
        self._aggregate_unit_tests()
        self._aggregate_integration_tests()
        self._aggregate_e2e_tests()
        self._aggregate_performance_tests()
        self._aggregate_security_tests()
        
        # 计算总体指标
        self._calculate_overall_metrics()
        
        # 生成趋势分析
        self._generate_trend_analysis()
        
        # 保存结果
        self._save_results()
        
        logger.info(f"测试结果聚合完成，输出文件: {self.output_file}")
        return self.results
    
    def _aggregate_unit_tests(self) -> None:
        """聚合单元测试结果"""
        logger.info("聚合单元测试结果")
        
        unit_results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "errors": 0,
            "duration": 0.0,
            "coverage": 0.0,
            "details": []
        }
        
        # 查找单元测试结果文件
        unit_test_files = list(self.input_dir.glob("**/unit-tests*.xml"))
        coverage_files = list(self.input_dir.glob("**/coverage.xml"))
        
        for test_file in unit_test_files:
            try:
                xml_data = JUnitXml.fromfile(str(test_file))
                
                for suite in xml_data:
                    unit_results["total"] += suite.tests
                    unit_results["passed"] += suite.tests - suite.failures - suite.errors - suite.skipped
                    unit_results["failed"] += suite.failures
                    unit_results["errors"] += suite.errors
                    unit_results["skipped"] += suite.skipped
                    unit_results["duration"] += suite.time or 0
                    
                    # 收集失败测试详情
                    for case in suite:
                        if case.result:
                            unit_results["details"].append({
                                "name": case.name,
                                "classname": case.classname,
                                "status": "failed" if case.is_failure else "error",
                                "message": str(case.result.message) if hasattr(case.result, 'message') else "",
                                "duration": case.time or 0
                            })
                            
            except Exception as e:
                logger.error(f"解析单元测试文件失败 {test_file}: {e}")
        
        # 解析覆盖率信息
        for coverage_file in coverage_files:
            try:
                tree = ET.parse(coverage_file)
                root = tree.getroot()
                
                # 查找总体覆盖率
                coverage_elem = root.find('.//coverage')
                if coverage_elem is not None:
                    line_rate = float(coverage_elem.get('line-rate', 0))
                    unit_results["coverage"] = round(line_rate * 100, 2)
                    
            except Exception as e:
                logger.error(f"解析覆盖率文件失败 {coverage_file}: {e}")
        
        # 计算通过率
        if unit_results["total"] > 0:
            unit_results["pass_rate"] = round((unit_results["passed"] / unit_results["total"]) * 100, 2)
        else:
            unit_results["pass_rate"] = 0.0
            
        self.results["details"]["unit"] = unit_results
        logger.info(f"单元测试聚合完成: {unit_results['passed']}/{unit_results['total']} 通过")
    
    def _aggregate_integration_tests(self) -> None:
        """聚合集成测试结果"""
        logger.info("聚合集成测试结果")
        
        integration_results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "errors": 0,
            "duration": 0.0,
            "details": []
        }
        
        # 查找集成测试结果文件
        integration_test_files = list(self.input_dir.glob("**/integration-tests*.xml"))
        
        for test_file in integration_test_files:
            try:
                xml_data = JUnitXml.fromfile(str(test_file))
                
                for suite in xml_data:
                    integration_results["total"] += suite.tests
                    integration_results["passed"] += suite.tests - suite.failures - suite.errors - suite.skipped
                    integration_results["failed"] += suite.failures
                    integration_results["errors"] += suite.errors
                    integration_results["skipped"] += suite.skipped
                    integration_results["duration"] += suite.time or 0
                    
                    # 收集失败测试详情
                    for case in suite:
                        if case.result:
                            integration_results["details"].append({
                                "name": case.name,
                                "classname": case.classname,
                                "status": "failed" if case.is_failure else "error",
                                "message": str(case.result.message) if hasattr(case.result, 'message') else "",
                                "duration": case.time or 0
                            })
                            
            except Exception as e:
                logger.error(f"解析集成测试文件失败 {test_file}: {e}")
        
        # 计算通过率
        if integration_results["total"] > 0:
            integration_results["pass_rate"] = round((integration_results["passed"] / integration_results["total"]) * 100, 2)
        else:
            integration_results["pass_rate"] = 0.0
            
        self.results["details"]["integration"] = integration_results
        logger.info(f"集成测试聚合完成: {integration_results['passed']}/{integration_results['total']} 通过")
    
    def _aggregate_e2e_tests(self) -> None:
        """聚合E2E测试结果"""
        logger.info("聚合E2E测试结果")
        
        e2e_results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "errors": 0,
            "duration": 0.0,
            "details": []
        }
        
        # 查找E2E测试结果文件
        e2e_test_files = list(self.input_dir.glob("**/e2e-tests*.xml"))
        
        for test_file in e2e_test_files:
            try:
                xml_data = JUnitXml.fromfile(str(test_file))
                
                for suite in xml_data:
                    e2e_results["total"] += suite.tests
                    e2e_results["passed"] += suite.tests - suite.failures - suite.errors - suite.skipped
                    e2e_results["failed"] += suite.failures
                    e2e_results["errors"] += suite.errors
                    e2e_results["skipped"] += suite.skipped
                    e2e_results["duration"] += suite.time or 0
                    
                    # 收集失败测试详情
                    for case in suite:
                        if case.result:
                            e2e_results["details"].append({
                                "name": case.name,
                                "classname": case.classname,
                                "status": "failed" if case.is_failure else "error",
                                "message": str(case.result.message) if hasattr(case.result, 'message') else "",
                                "duration": case.time or 0
                            })
                            
            except Exception as e:
                logger.error(f"解析E2E测试文件失败 {test_file}: {e}")
        
        # 计算通过率
        if e2e_results["total"] > 0:
            e2e_results["pass_rate"] = round((e2e_results["passed"] / e2e_results["total"]) * 100, 2)
        else:
            e2e_results["pass_rate"] = 0.0
            
        self.results["details"]["e2e"] = e2e_results
        logger.info(f"E2E测试聚合完成: {e2e_results['passed']}/{e2e_results['total']} 通过")
    
    def _aggregate_performance_tests(self) -> None:
        """聚合性能测试结果"""
        logger.info("聚合性能测试结果")
        
        performance_results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "duration": 0.0,
            "benchmarks": [],
            "metrics": {}
        }
        
        # 查找性能测试结果文件
        performance_test_files = list(self.input_dir.glob("**/performance-tests*.xml"))
        benchmark_files = list(self.input_dir.glob("**/benchmark*.json"))
        
        # 解析测试结果
        for test_file in performance_test_files:
            try:
                xml_data = JUnitXml.fromfile(str(test_file))
                
                for suite in xml_data:
                    performance_results["total"] += suite.tests
                    performance_results["passed"] += suite.tests - suite.failures - suite.errors - suite.skipped
                    performance_results["failed"] += suite.failures
                    performance_results["skipped"] += suite.skipped
                    performance_results["duration"] += suite.time or 0
                    
            except Exception as e:
                logger.error(f"解析性能测试文件失败 {test_file}: {e}")
        
        # 解析基准测试结果
        for benchmark_file in benchmark_files:
            try:
                with open(benchmark_file, 'r', encoding='utf-8') as f:
                    benchmark_data = json.load(f)
                    
                if 'benchmarks' in benchmark_data:
                    for benchmark in benchmark_data['benchmarks']:
                        performance_results["benchmarks"].append({
                            "name": benchmark.get('name', ''),
                            "mean": benchmark.get('stats', {}).get('mean', 0),
                            "min": benchmark.get('stats', {}).get('min', 0),
                            "max": benchmark.get('stats', {}).get('max', 0),
                            "stddev": benchmark.get('stats', {}).get('stddev', 0),
                            "rounds": benchmark.get('stats', {}).get('rounds', 0)
                        })
                        
            except Exception as e:
                logger.error(f"解析基准测试文件失败 {benchmark_file}: {e}")
        
        # 计算性能指标
        if performance_results["benchmarks"]:
            mean_times = [b["mean"] for b in performance_results["benchmarks"]]
            performance_results["metrics"] = {
                "avg_response_time": round(sum(mean_times) / len(mean_times), 3),
                "max_response_time": round(max(mean_times), 3),
                "min_response_time": round(min(mean_times), 3),
                "total_benchmarks": len(performance_results["benchmarks"])
            }
        
        # 计算通过率
        if performance_results["total"] > 0:
            performance_results["pass_rate"] = round((performance_results["passed"] / performance_results["total"]) * 100, 2)
        else:
            performance_results["pass_rate"] = 0.0
            
        self.results["details"]["performance"] = performance_results
        logger.info(f"性能测试聚合完成: {performance_results['passed']}/{performance_results['total']} 通过")
    
    def _aggregate_security_tests(self) -> None:
        """聚合安全测试结果"""
        logger.info("聚合安全测试结果")
        
        security_results = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "duration": 0.0,
            "vulnerabilities": [],
            "security_score": 0.0
        }
        
        # 查找安全测试结果文件
        security_test_files = list(self.input_dir.glob("**/security-tests*.xml"))
        bandit_files = list(self.input_dir.glob("**/bandit-report*.json"))
        safety_files = list(self.input_dir.glob("**/safety-report*.json"))
        
        # 解析测试结果
        for test_file in security_test_files:
            try:
                xml_data = JUnitXml.fromfile(str(test_file))
                
                for suite in xml_data:
                    security_results["total"] += suite.tests
                    security_results["passed"] += suite.tests - suite.failures - suite.errors - suite.skipped
                    security_results["failed"] += suite.failures
                    security_results["skipped"] += suite.skipped
                    security_results["duration"] += suite.time or 0
                    
            except Exception as e:
                logger.error(f"解析安全测试文件失败 {test_file}: {e}")
        
        # 解析Bandit报告
        for bandit_file in bandit_files:
            try:
                with open(bandit_file, 'r', encoding='utf-8') as f:
                    bandit_data = json.load(f)
                    
                if 'results' in bandit_data:
                    for result in bandit_data['results']:
                        security_results["vulnerabilities"].append({
                            "type": "code_security",
                            "severity": result.get('issue_severity', 'UNKNOWN'),
                            "confidence": result.get('issue_confidence', 'UNKNOWN'),
                            "test_id": result.get('test_id', ''),
                            "test_name": result.get('test_name', ''),
                            "filename": result.get('filename', ''),
                            "line_number": result.get('line_number', 0),
                            "issue_text": result.get('issue_text', '')
                        })
                        
            except Exception as e:
                logger.error(f"解析Bandit报告失败 {bandit_file}: {e}")
        
        # 解析Safety报告
        for safety_file in safety_files:
            try:
                with open(safety_file, 'r', encoding='utf-8') as f:
                    safety_data = json.load(f)
                    
                if isinstance(safety_data, list):
                    for vulnerability in safety_data:
                        security_results["vulnerabilities"].append({
                            "type": "dependency_vulnerability",
                            "package": vulnerability.get('package', ''),
                            "installed_version": vulnerability.get('installed_version', ''),
                            "affected_versions": vulnerability.get('affected_versions', ''),
                            "vulnerability_id": vulnerability.get('vulnerability_id', ''),
                            "advisory": vulnerability.get('advisory', '')
                        })
                        
            except Exception as e:
                logger.error(f"解析Safety报告失败 {safety_file}: {e}")
        
        # 计算安全评分
        total_vulnerabilities = len(security_results["vulnerabilities"])
        high_severity_count = sum(1 for v in security_results["vulnerabilities"] 
                                if v.get('severity', '').upper() in ['HIGH', 'CRITICAL'])
        
        if total_vulnerabilities == 0:
            security_results["security_score"] = 100.0
        else:
            # 基于漏洞数量和严重程度计算评分
            score = max(0, 100 - (total_vulnerabilities * 5) - (high_severity_count * 10))
            security_results["security_score"] = round(score, 2)
        
        # 计算通过率
        if security_results["total"] > 0:
            security_results["pass_rate"] = round((security_results["passed"] / security_results["total"]) * 100, 2)
        else:
            security_results["pass_rate"] = 0.0
            
        self.results["details"]["security"] = security_results
        logger.info(f"安全测试聚合完成: {security_results['passed']}/{security_results['total']} 通过，安全评分: {security_results['security_score']}")
    
    def _calculate_overall_metrics(self) -> None:
        """计算总体指标"""
        logger.info("计算总体指标")
        
        overall = {
            "total": 0,
            "passed": 0,
            "failed": 0,
            "skipped": 0,
            "errors": 0,
            "duration": 0.0,
            "pass_rate": 0.0,
            "status": "unknown"
        }
        
        # 汇总各类测试结果
        for test_type, results in self.results["details"].items():
            overall["total"] += results.get("total", 0)
            overall["passed"] += results.get("passed", 0)
            overall["failed"] += results.get("failed", 0)
            overall["skipped"] += results.get("skipped", 0)
            overall["errors"] += results.get("errors", 0)
            overall["duration"] += results.get("duration", 0.0)
        
        # 计算总体通过率
        if overall["total"] > 0:
            overall["pass_rate"] = round((overall["passed"] / overall["total"]) * 100, 2)
        
        # 确定总体状态
        if overall["failed"] == 0 and overall["errors"] == 0:
            overall["status"] = "success"
        elif overall["pass_rate"] >= 90:
            overall["status"] = "warning"
        else:
            overall["status"] = "failure"
        
        self.results["summary"]["overall"] = overall
        
        # 计算质量指标
        quality_metrics = {
            "code_coverage": self.results["details"].get("unit", {}).get("coverage", 0.0),
            "security_score": self.results["details"].get("security", {}).get("security_score", 0.0),
            "performance_score": self._calculate_performance_score(),
            "reliability_score": overall["pass_rate"]
        }
        
        # 计算综合质量评分
        quality_metrics["overall_quality_score"] = round(
            (quality_metrics["code_coverage"] * 0.3 +
             quality_metrics["security_score"] * 0.25 +
             quality_metrics["performance_score"] * 0.2 +
             quality_metrics["reliability_score"] * 0.25), 2
        )
        
        self.results["metrics"] = quality_metrics
        
        logger.info(f"总体指标计算完成: {overall['passed']}/{overall['total']} 通过，质量评分: {quality_metrics['overall_quality_score']}")
    
    def _calculate_performance_score(self) -> float:
        """计算性能评分
        
        Returns:
            性能评分 (0-100)
        """
        performance_data = self.results["details"].get("performance", {})
        
        if not performance_data.get("benchmarks"):
            return 100.0  # 没有性能测试时给满分
        
        # 基于响应时间计算评分
        avg_response_time = performance_data.get("metrics", {}).get("avg_response_time", 0)
        
        if avg_response_time == 0:
            return 100.0
        elif avg_response_time <= 0.1:  # 100ms以下
            return 100.0
        elif avg_response_time <= 0.5:  # 500ms以下
            return 90.0
        elif avg_response_time <= 1.0:  # 1s以下
            return 80.0
        elif avg_response_time <= 3.0:  # 3s以下
            return 70.0
        else:
            return max(0, 70 - (avg_response_time - 3) * 10)
    
    def _generate_trend_analysis(self) -> None:
        """生成趋势分析"""
        logger.info("生成趋势分析")
        
        # 这里可以添加历史数据对比逻辑
        # 目前只生成基本的趋势信息
        trends = {
            "test_count_trend": "stable",
            "pass_rate_trend": "stable",
            "coverage_trend": "stable",
            "performance_trend": "stable",
            "security_trend": "stable"
        }
        
        self.results["trends"] = trends
    
    def _save_results(self) -> None:
        """保存聚合结果"""
        try:
            # 确保输出目录存在
            self.output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存JSON格式结果
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            
            # 同时保存CSV格式的摘要
            summary_file = self.output_file.with_suffix('.csv')
            self._save_summary_csv(summary_file)
            
            logger.info(f"测试结果已保存到: {self.output_file}")
            
        except Exception as e:
            logger.error(f"保存测试结果失败: {e}")
            raise
    
    def _save_summary_csv(self, csv_file: Path) -> None:
        """保存CSV格式的摘要
        
        Args:
            csv_file: CSV文件路径
        """
        try:
            summary_data = []
            
            for test_type, results in self.results["details"].items():
                summary_data.append({
                    "test_type": test_type,
                    "total": results.get("total", 0),
                    "passed": results.get("passed", 0),
                    "failed": results.get("failed", 0),
                    "skipped": results.get("skipped", 0),
                    "pass_rate": results.get("pass_rate", 0.0),
                    "duration": results.get("duration", 0.0)
                })
            
            df = pd.DataFrame(summary_data)
            df.to_csv(csv_file, index=False, encoding='utf-8')
            
        except Exception as e:
            logger.warning(f"保存CSV摘要失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='聚合测试结果')
    parser.add_argument('--input-dir', required=True, help='测试结果输入目录')
    parser.add_argument('--output-file', required=True, help='聚合结果输出文件')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        aggregator = TestResultsAggregator(args.input_dir, args.output_file)
        results = aggregator.aggregate_all_results()
        
        # 输出摘要信息
        overall = results["summary"]["overall"]
        print(f"\n=== 测试结果摘要 ===")
        print(f"总测试数: {overall['total']}")
        print(f"通过: {overall['passed']}")
        print(f"失败: {overall['failed']}")
        print(f"跳过: {overall['skipped']}")
        print(f"通过率: {overall['pass_rate']}%")
        print(f"状态: {overall['status']}")
        print(f"质量评分: {results['metrics']['overall_quality_score']}/100")
        
        return 0 if overall['status'] == 'success' else 1
        
    except Exception as e:
        logger.error(f"聚合测试结果失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())