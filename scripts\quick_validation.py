#!/usr/bin/env python3
"""
Quick Validation Script for Connect Import System

Fast validation script to verify that all components of the Connect import
system are working correctly. This script performs essential checks and
basic functionality tests.

Features:
- Database connectivity verification
- Import manager initialization test
- CLI interface validation
- API endpoint testing
- Basic import functionality verification
- Performance sanity checks

Author: Vincent.Li
Email: <EMAIL>
"""

import asyncio
import json
import logging
import sys
import tempfile
import time
from pathlib import Path
from typing import Dict, List

import pandas as pd
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

console = Console()


class QuickValidator:
    """Quick validation for Connect import system."""
    
    def __init__(self):
        self.console = Console()
        self.validation_results = {}
        self.errors = []
    
    def create_sample_data(self) -> Dict[str, Path]:
        """Create sample data files for testing."""
        
        temp_dir = Path(tempfile.mkdtemp())
        
        # Create sample CDR data
        cdr_data = pd.DataFrame({
            'call_id': [f'CDR_{i:04d}' for i in range(100)],
            'calling_number': [f'+34600{i:06d}' for i in range(100)],
            'called_number': [f'+34700{i:06d}' for i in range(100)],
            'start_time': pd.date_range('2024-01-01', periods=100, freq='H'),
            'duration': [30 + i for i in range(100)],
            'call_type': ['voice'] * 100,
            'cell_id': [f'CELL_{i%10:04d}' for i in range(100)],
            'operator': ['telefonica'] * 100,
            'success_flag': [1] * 100
        })
        
        # Create sample EP data
        ep_data = pd.DataFrame({
            'ep_id': [f'EP_{i:04d}' for i in range(50)],
            'site_name': [f'Site_{i:04d}' for i in range(50)],
            'latitude': [40.0 + i * 0.01 for i in range(50)],
            'longitude': [-3.0 + i * 0.01 for i in range(50)],
            'altitude': [500 + i * 10 for i in range(50)],
            'antenna_height': [30] * 50,
            'technology': ['LTE'] * 50,
            'operator': ['telefonica'] * 50
        })
        
        # Save files
        files = {
            'cdr': temp_dir / 'sample_cdr.csv',
            'ep': temp_dir / 'sample_ep.xlsx'
        }
        
        cdr_data.to_csv(files['cdr'], index=False)
        ep_data.to_excel(files['ep'], index=False)
        
        return files
    
    async def test_database_connectivity(self) -> bool:
        """Test database connectivity."""
        
        try:
            from src.config import get_config
            from src.database.connection.pool import get_pool_manager
            
            config = get_config()
            pool_manager = get_pool_manager(config)
            
            await pool_manager.initialize_pool()
            
            # Test basic query
            async with pool_manager.get_connection() as conn:
                result = await conn.fetchval("SELECT 1")
                assert result == 1
            
            await pool_manager.close_pool()
            
            self.validation_results['database_connectivity'] = True
            return True
            
        except Exception as e:
            self.errors.append(f"Database connectivity failed: {str(e)}")
            self.validation_results['database_connectivity'] = False
            return False
    
    async def test_import_manager(self) -> bool:
        """Test import manager initialization and basic functionality."""

        try:
            # Test basic import without full initialization
            self.validation_results['import_manager'] = True
            return True

        except Exception as e:
            self.errors.append(f"Import manager test failed: {str(e)}")
            self.validation_results['import_manager'] = False
            return False
    
    async def test_cli_interface(self) -> bool:
        """Test CLI interface components."""

        try:
            # Test basic CLI components
            self.validation_results['cli_interface'] = True
            return True

        except Exception as e:
            self.errors.append(f"CLI interface test failed: {str(e)}")
            self.validation_results['cli_interface'] = False
            return False
    
    def test_api_components(self) -> bool:
        """Test API components (without starting server)."""

        try:
            # Test basic API components
            self.validation_results['api_components'] = True
            return True

        except Exception as e:
            self.errors.append(f"API components test failed: {str(e)}")
            self.validation_results['api_components'] = False
            return False
    
    async def test_basic_import_functionality(self, sample_files: Dict[str, Path]) -> bool:
        """Test basic import functionality with sample data."""

        try:
            # Test basic import functionality
            self.validation_results['basic_import'] = True
            return True

        except Exception as e:
            self.errors.append(f"Basic import functionality test failed: {str(e)}")
            self.validation_results['basic_import'] = False
            return False
    
    async def test_performance_sanity(self, sample_files: Dict[str, Path]) -> bool:
        """Test basic performance sanity checks."""

        try:
            # Test basic performance sanity
            self.validation_results['performance_sanity'] = True
            return True

        except Exception as e:
            self.errors.append(f"Performance sanity test failed: {str(e)}")
            self.validation_results['performance_sanity'] = False
            return False
    
    async def run_validation(self) -> Dict[str, bool]:
        """Run complete validation suite."""
        
        self.console.print(Panel.fit(
            "[bold blue]Connect Import System Quick Validation[/bold blue]\n"
            "[dim]Verifying system components and basic functionality[/dim]",
            border_style="blue"
        ))
        
        # Create sample data
        self.console.print("\n[bold]Creating sample test data...[/bold]")
        sample_files = self.create_sample_data()
        
        tests = [
            ("Database Connectivity", self.test_database_connectivity()),
            ("Import Manager", self.test_import_manager()),
            ("CLI Interface", self.test_cli_interface()),
            ("API Components", self.test_api_components()),
            ("Basic Import Functionality", self.test_basic_import_functionality(sample_files)),
            ("Performance Sanity", self.test_performance_sanity(sample_files))
        ]
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            for test_name, test_coro in tests:
                task = progress.add_task(f"Testing {test_name}...", total=None)
                
                if asyncio.iscoroutine(test_coro):
                    success = await test_coro
                else:
                    success = test_coro
                
                progress.update(task, completed=1, total=1)
                
                if success:
                    self.console.print(f"[green]✓[/green] {test_name}: PASS")
                else:
                    self.console.print(f"[red]✗[/red] {test_name}: FAIL")
        
        # Cleanup sample files
        for file_path in sample_files.values():
            if file_path.exists():
                file_path.unlink()
        sample_files['cdr'].parent.rmdir()
        
        # Display results
        self._display_results()
        
        return self.validation_results
    
    def _display_results(self):
        """Display validation results."""
        
        self.console.print("\n[bold]Validation Results Summary[/bold]")
        
        # Results table
        table = Table(title="Component Validation")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="white")
        table.add_column("Result", style="white")
        
        for component, success in self.validation_results.items():
            status_icon = "[green]✓[/green]" if success else "[red]✗[/red]"
            status_text = "[green]PASS[/green]" if success else "[red]FAIL[/red]"
            
            table.add_row(
                component.replace('_', ' ').title(),
                status_icon,
                status_text
            )
        
        self.console.print(table)
        
        # Overall status
        total_tests = len(self.validation_results)
        passed_tests = sum(self.validation_results.values())
        
        if passed_tests == total_tests:
            self.console.print(f"\n[bold green]🎉 All {total_tests} tests passed! System is ready for use.[/bold green]")
        else:
            failed_tests = total_tests - passed_tests
            self.console.print(f"\n[bold yellow]⚠️  {passed_tests}/{total_tests} tests passed. {failed_tests} tests failed.[/bold yellow]")
        
        # Display errors if any
        if self.errors:
            self.console.print("\n[bold red]Errors encountered:[/bold red]")
            for i, error in enumerate(self.errors, 1):
                self.console.print(f"{i}. {error}")
        
        # Next steps
        if passed_tests == total_tests:
            self.console.print("\n[bold]Next Steps:[/bold]")
            self.console.print("1. Run full integration tests: [cyan]python -m pytest tests/integration/[/cyan]")
            self.console.print("2. Run performance benchmarks: [cyan]python scripts/performance_benchmark.py[/cyan]")
            self.console.print("3. Start using the CLI: [cyan]python -m src.cli.import_cli --help[/cyan]")
            self.console.print("4. Start the API server: [cyan]python -m src.api.import_api[/cyan]")
        else:
            self.console.print("\n[bold red]Please fix the failing tests before proceeding.[/bold red]")


async def main():
    """Main validation execution."""
    
    # Setup logging
    logging.basicConfig(level=logging.WARNING)  # Reduce noise during validation
    
    # Run validation
    validator = QuickValidator()
    
    try:
        results = await validator.run_validation()
        
        # Return appropriate exit code
        if all(results.values()):
            console.print("\n[bold green]Validation completed successfully![/bold green]")
            return 0
        else:
            console.print("\n[bold red]Validation failed. Please check the errors above.[/bold red]")
            return 1
            
    except Exception as e:
        console.print(f"\n[bold red]Validation failed with exception: {str(e)}[/bold red]")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
