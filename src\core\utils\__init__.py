# -*- coding: utf-8 -*-
"""
Core Utilities Module

This module provides essential utilities for the Connect telecommunications
data processing system, including memory monitoring, performance tracking,
validation helpers, and logging utilities.

__version__ = "1.0.0"
__description__ = "Core utilities for Connect telecommunications system"
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

# Memory monitoring and management
from .memory import (
    MemoryMonitor,
    MemorySnapshot,
    MemoryStats,
    MemoryError,
    MemoryLimitExceededError
)

# Performance monitoring and metrics
from .performance import (
    PerformanceMonitor,
    PerformanceMetrics,
    PerformanceStats
)

# Validation utilities
from .validation import (
    ValidationError,
    FileValidationError,
    DataValidationError,
    TelecomValidationError,
    ValidationRule,
    ValidationResult,
    DataValidator,
    TelecomValidator,
    validate_file_path,
    validate_data_format,
    validate_csv_structure,
    validate_excel_structure
)

# Encoding utilities
from .encoding import (
    detect_encoding,
    detect_encoding_sync
)

# Logging utilities
from .logging import (
    <PERSON>Log<PERSON>,
    LogLevel,
    TelecomFormatter,
    LogContext,
    TelecomLogEntry,
    setup_logging,
    get_logger
)

# Module metadata
__all__ = [
    # Memory monitoring
    "MemoryMonitor",
    "MemorySnapshot",
    "MemoryStats",
    "MemoryError",
    "MemoryLimitExceededError",
    
    # Encoding utilities
    "detect_encoding",
    "detect_encoding_sync",
    
    # Performance monitoring
    "PerformanceMonitor",
    "PerformanceMetrics",
    "PerformanceStats",
    
    # Validation
    "ValidationError",
    "FileValidationError",
    "DataValidationError",
    "TelecomValidationError",
    "ValidationRule",
    "ValidationResult",
    "DataValidator",
    "TelecomValidator",
    "validate_file_path",
    "validate_data_format",
    "validate_csv_structure",
    "validate_excel_structure",
    
    # Logging
    # "setup_logging",
    # "get_logger",
    # "LogLevel",
    # "TelecomLogger",
]