#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Batch Processing Utilities.

This module provides utilities for batch processing to optimize bulk database operations.
Implements helper functions for generating batches and performing bulk inserts in manageable chunks.
"""

import asyncio
import logging
from typing import Any, Dict, Iterator, List, Optional, Union

import asyncpg
from sqlalchemy import Table

from ..connection.session import Session<PERSON>anager
from ..exceptions import DatabaseError, ValidationError
from ..utils.security import SQLInjectionGuard
from ..utils.validators import InputValidator

logger = logging.getLogger(__name__)


def generate_batches(
    data: List[Dict[str, Any]], batch_size: int
) -> Iterator[List[Dict[str, Any]]]:
    """Generate batches of data for processing.

    This function yields lists of records in manageable chunks to optimize
    bulk database operations and memory usage.

    Args:
        data: List of dictionaries containing the data to be batched
        batch_size: Number of records per batch

    Yields:
        Iterator of lists containing batched records

    Raises:
        ValidationError: If batch_size is invalid or data is not a list

    Example:
        >>> data = [{'id': 1, 'name': '<PERSON>'}, {'id': 2, 'name': '<PERSON>'}, {'id': 3, 'name': '<PERSON>'}]
        >>> for batch in generate_batches(data, batch_size=2):
        ...     print(batch)
        [{'id': 1, 'name': 'Alice'}, {'id': 2, 'name': 'Bob'}]
        [{'id': 3, 'name': 'Charlie'}]
    """
    # Validate inputs
    if not isinstance(data, list):
        raise ValidationError("Data must be a list of dictionaries")

    if not isinstance(batch_size, int) or batch_size <= 0:
        raise ValidationError("Batch size must be a positive integer")

    if not data:
        logger.warning("Empty data list provided to generate_batches")
        return

    # Validate that all items in data are dictionaries
    for i, item in enumerate(data):
        if not isinstance(item, dict):
            raise ValidationError(
                f"Item at index {i} is not a dictionary: {type(item)}"
            )

    logger.info(
        f"Generating batches for {len(data)} records with batch size {batch_size}"
    )

    # Generate batches
    for i in range(0, len(data), batch_size):
        batch = data[i : i + batch_size]
        logger.debug(f"Generated batch {i // batch_size + 1} with {len(batch)} records")
        yield batch


async def bulk_insert_batched(
    session_manager,
    records: List[Dict[str, Any]],
    table_name: str,
    schema_name: str = "public",
    batch_size: int = 1000,
    crud_operations=None,
) -> Dict[str, Any]:
    """Perform bulk insert operation using batched processing.

    This function uses the batch generator and CRUDOperations.bulk_insert
    to insert data in manageable chunks, optimizing performance and memory usage.

    Args:
        session_manager: Database session manager instance
        records: List of dictionaries containing the data to insert
        table_name: Name of the target table
        schema_name: Name of the target schema (default: 'public')
        batch_size: Number of records per batch (default: 1000)
        crud_operations: Optional CRUDOperations instance (will create if None)

    Returns:
        Dict containing operation results:
        {
            'total_records': int,
            'batches_processed': int,
            'records_inserted': int,
            'batch_size': int,
            'success': bool,
            'errors': List[str]
        }

    Raises:
        ValidationError: If parameters are invalid
        DatabaseError: If bulk insert operation fails

    Example:
        >>> records = [{'name': 'Alice', 'age': 30}, {'name': 'Bob', 'age': 25}]
        >>> result = await bulk_insert_batched(session_manager, records, 'users', 'public')
        >>> print(result['records_inserted'])
        2
    """
    # Validate inputs
    if not isinstance(records, list):
        raise ValidationError("Records must be a list of dictionaries")

    if not isinstance(batch_size, int) or batch_size <= 0:
        raise ValidationError("Batch size must be a positive integer")

    # Validate identifiers
    if not SQLInjectionGuard.validate_identifier(table_name, "table"):
        raise ValidationError(f"Invalid table name: {table_name}")

    if not SQLInjectionGuard.validate_identifier(schema_name, "schema"):
        raise ValidationError(f"Invalid schema name: {schema_name}")

    if not records:
        logger.warning("Empty records list provided to bulk_insert_batched")
        return {
            "total_records": 0,
            "batches_processed": 0,
            "records_inserted": 0,
            "batch_size": batch_size,
            "success": True,
            "errors": [],
        }

    # Initialize CRUD operations if not provided
    if crud_operations is None:
        from ..operations.crud import CRUDOperations

        crud_operations = CRUDOperations(session_manager)

    # Initialize result tracking
    total_records = len(records)
    batches_processed = 0
    records_inserted = 0
    errors = []

    logger.info(
        f"Starting bulk insert of {total_records} records into {schema_name}.{table_name} "
        f"with batch size {batch_size}"
    )

    try:
        # Process records in batches
        for batch_num, batch in enumerate(generate_batches(records, batch_size), 1):
            try:
                logger.debug(f"Processing batch {batch_num} with {len(batch)} records")

                # Perform bulk insert for this batch
                inserted_records = await crud_operations.bulk_insert(
                    table_name=table_name, data=batch, schema_name=schema_name
                )

                batch_inserted_count = (
                    len(inserted_records) if inserted_records else len(batch)
                )
                records_inserted += batch_inserted_count
                batches_processed += 1

                logger.info(
                    f"Batch {batch_num} completed: {batch_inserted_count} records inserted"
                )

            except Exception as e:
                error_msg = f"Error processing batch {batch_num}: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
                # Continue with next batch instead of failing completely
                continue

    except Exception as e:
        error_msg = f"Critical error during bulk insert operation: {str(e)}"
        logger.error(error_msg)
        errors.append(error_msg)
        raise DatabaseError(error_msg) from e

    # Prepare result summary
    success = len(errors) == 0
    result = {
        "total_records": total_records,
        "batches_processed": batches_processed,
        "records_inserted": records_inserted,
        "batch_size": batch_size,
        "success": success,
        "errors": errors,
    }

    if success:
        logger.info(
            f"Bulk insert completed successfully: {records_inserted}/{total_records} records inserted "
            f"in {batches_processed} batches"
        )
    else:
        logger.warning(
            f"Bulk insert completed with errors: {records_inserted}/{total_records} records inserted "
            f"in {batches_processed} batches, {len(errors)} errors occurred"
        )

    return result


async def bulk_insert_with_copy(
    session_manager: SessionManager,
    records: List[Dict[str, Any]],
    table_name: str,
    schema_name: str = "public",
    batch_size: int = 5000,
) -> Dict[str, Any]:
    """Perform high-performance bulk insert using asyncpg's copy_records_to_table.

    This function provides an alternative bulk insert method using PostgreSQL's
    COPY command for maximum performance with large datasets.

    Args:
        session_manager: Database session manager instance
        records: List of dictionaries containing the data to insert
        table_name: Name of the target table
        schema_name: Name of the target schema (default: 'public')
        batch_size: Number of records per batch (default: 5000)

    Returns:
        Dict containing operation results

    Raises:
        ValidationError: If parameters are invalid
        DatabaseError: If copy operation fails
    """
    # Validate inputs
    if not isinstance(records, list):
        raise ValidationError("Records must be a list of dictionaries")

    if not isinstance(batch_size, int) or batch_size <= 0:
        raise ValidationError("Batch size must be a positive integer")

    # Validate identifiers
    if not SQLInjectionGuard.validate_identifier(table_name, "table"):
        raise ValidationError(f"Invalid table name: {table_name}")

    if not SQLInjectionGuard.validate_identifier(schema_name, "schema"):
        raise ValidationError(f"Invalid schema name: {schema_name}")

    if not records:
        logger.warning("Empty records list provided to bulk_insert_with_copy")
        return {
            "total_records": 0,
            "batches_processed": 0,
            "records_inserted": 0,
            "batch_size": batch_size,
            "success": True,
            "errors": [],
        }

    total_records = len(records)
    batches_processed = 0
    records_inserted = 0
    errors = []

    logger.info(
        f"Starting high-performance bulk insert of {total_records} records into "
        f"{schema_name}.{table_name} using COPY with batch size {batch_size}"
    )

    try:
        # Get table columns for COPY operation
        async with session_manager as conn:
            # Get column names and order
            columns_query = """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = $1 AND table_name = $2
                ORDER BY ordinal_position
            """
            column_records = await conn.fetch(columns_query, schema_name, table_name)

            if not column_records:
                raise DatabaseError(
                    f"Table {schema_name}.{table_name} not found or has no columns"
                )

            columns = [record["column_name"] for record in column_records]

            # Process records in batches using COPY
            for batch_num, batch in enumerate(generate_batches(records, batch_size), 1):
                try:
                    logger.debug(
                        f"Processing batch {batch_num} with {len(batch)} records using COPY"
                    )

                    # Convert batch to list of tuples in column order
                    batch_tuples = []
                    for record in batch:
                        tuple_data = tuple(record.get(col) for col in columns)
                        batch_tuples.append(tuple_data)

                    # Use COPY for high-performance insert
                    full_table_name = f"{schema_name}.{table_name}"
                    await conn.copy_records_to_table(
                        full_table_name, records=batch_tuples, columns=columns
                    )

                    records_inserted += len(batch)
                    batches_processed += 1

                    logger.info(
                        f"Batch {batch_num} completed using COPY: {len(batch)} records inserted"
                    )

                except Exception as e:
                    error_msg = (
                        f"Error processing batch {batch_num} with COPY: {str(e)}"
                    )
                    logger.error(error_msg)
                    errors.append(error_msg)
                    continue

    except Exception as e:
        error_msg = f"Critical error during COPY bulk insert operation: {str(e)}"
        logger.error(error_msg)
        errors.append(error_msg)
        raise DatabaseError(error_msg) from e

    # Prepare result summary
    success = len(errors) == 0
    result = {
        "total_records": total_records,
        "batches_processed": batches_processed,
        "records_inserted": records_inserted,
        "batch_size": batch_size,
        "success": success,
        "errors": errors,
    }

    if success:
        logger.info(
            f"COPY bulk insert completed successfully: {records_inserted}/{total_records} records inserted "
            f"in {batches_processed} batches"
        )
    else:
        logger.warning(
            f"COPY bulk insert completed with errors: {records_inserted}/{total_records} records inserted "
            f"in {batches_processed} batches, {len(errors)} errors occurred"
        )

    return result


class BatchProcessor:
    """Batch processor class for managing bulk database operations.

    This class provides a high-level interface for batch processing operations,
    encapsulating the batch generation and bulk insert functionality.
    """

    def __init__(self, session_manager, crud_operations=None):
        """Initialize batch processor.

        Args:
            session_manager: Database session manager instance
            crud_operations: Optional CRUDOperations instance
        """
        self.session_manager = session_manager
        self.crud_operations = crud_operations
        if self.crud_operations is None:
            from ..operations.crud import CRUDOperations

            self.crud_operations = CRUDOperations(session_manager)

    async def process_batches(
        self,
        records: List[Dict[str, Any]],
        table_name: str,
        schema_name: str = "public",
        batch_size: int = 1000,
        use_copy: bool = False,
    ) -> Dict[str, Any]:
        """Process records in batches using the specified method.

        Args:
            records: List of dictionaries containing the data to insert
            table_name: Name of the target table
            schema_name: Name of the target schema (default: 'public')
            batch_size: Number of records per batch (default: 1000)
            use_copy: Whether to use COPY method for better performance (default: False)

        Returns:
            Dict containing operation results
        """
        if use_copy:
            return await bulk_insert_with_copy(
                self.session_manager, records, table_name, schema_name, batch_size
            )
        else:
            return await bulk_insert_batched(
                self.session_manager,
                records,
                table_name,
                schema_name,
                batch_size,
                self.crud_operations,
            )

    def generate_batches(
        self, data: List[Dict[str, Any]], batch_size: int
    ) -> Iterator[List[Dict[str, Any]]]:
        """Generate batches of data for processing.

        Args:
            data: List of dictionaries containing the data to be batched
            batch_size: Number of records per batch

        Yields:
            Iterator of lists containing batched records
        """
        return generate_batches(data, batch_size)
