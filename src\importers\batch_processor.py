"""Enhanced batch processing for telecommunications data import jobs.

This module provides memory-optimized batch processing capabilities with
async support and telecommunications-specific optimizations.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import logging
import psutil
import gc
from typing import List, Any, Callable, Optional, Dict, Union
from dataclasses import dataclass
from datetime import datetime
import pandas as pd


@dataclass
class JobMetrics:
    """Metrics for import job performance"""

    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    memory_usage_mb: float = 0.0
    records_processed: int = 0
    processing_time_seconds: float = 0.0


class ImportJob:
    """Enhanced import job with memory monitoring and async support."""

    def __init__(
        self,
        data: Any,
        importer: Callable,
        job_id: str = None,
        job_type: str = "general",
        priority: int = 1,
    ):
        """Initializes an ImportJob with enhanced tracking.

        Args:
            data: The data to be imported
            importer: The importer function/method to process the data
            job_id: Unique identifier for the job
            job_type: Type of job (cdr, ep, kpi, etc.)
            priority: Job priority (1=highest, 5=lowest)
        """
        self.data = data
        self.importer = importer
        self.job_id = job_id or f"job_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.job_type = job_type
        self.priority = priority
        self.status = "pending"  # pending, processing, completed, failed
        self.result = None
        self.error = None
        self.metrics = JobMetrics()
        self.logger = logging.getLogger(f"{self.__class__.__name__}.{self.job_id}")

    async def run_async(self) -> bool:
        """Executes the import job asynchronously with memory monitoring."""
        self.status = "processing"
        self.metrics.start_time = datetime.now()

        # Monitor initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        try:
            self.logger.info(f"Starting {self.job_type} job {self.job_id}")

            # Check if importer supports async
            if asyncio.iscoroutinefunction(self.importer):
                self.result = await self.importer(self.data)
            else:
                # Run synchronous importer in thread pool
                loop = asyncio.get_event_loop()
                self.result = await loop.run_in_executor(None, self.importer, self.data)

            self.status = "completed"

            # Extract metrics from result if available
            if hasattr(self.result, "records_imported"):
                self.metrics.records_processed = self.result.records_imported

            return True

        except Exception as e:
            self.error = e
            self.status = "failed"
            self.logger.error(f"Job {self.job_id} failed: {e}")
            return False

        finally:
            self.metrics.end_time = datetime.now()

            # Calculate processing time
            if self.metrics.start_time:
                self.metrics.processing_time_seconds = (
                    self.metrics.end_time - self.metrics.start_time
                ).total_seconds()

            # Monitor final memory usage
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            self.metrics.memory_usage_mb = final_memory - initial_memory

            # Force garbage collection for memory optimization
            gc.collect()

            self.logger.info(
                f"Job {self.job_id} completed in {self.metrics.processing_time_seconds:.2f}s, "
                f"memory delta: {self.metrics.memory_usage_mb:.2f}MB"
            )

    def run(self) -> bool:
        """Synchronous wrapper for backward compatibility."""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.run_async())
        except RuntimeError:
            # No event loop running, create new one
            return asyncio.run(self.run_async())


class BatchProcessor:
    """Enhanced batch processor with memory optimization and async support."""

    def __init__(
        self,
        jobs: List[ImportJob],
        max_concurrent_jobs: int = 4,
        memory_limit_mb: int = 4096,  # Increased from 2048MB for better memory handling
        enable_memory_monitoring: bool = True,
    ):
        """Initializes the BatchProcessor with enhanced capabilities.

        Args:
            jobs: List of import jobs to process
            max_concurrent_jobs: Maximum number of concurrent jobs
            memory_limit_mb: Memory limit for batch processing
            enable_memory_monitoring: Enable memory usage monitoring
        """
        self.jobs = jobs
        self.max_concurrent_jobs = max_concurrent_jobs
        self.memory_limit_mb = memory_limit_mb
        self.enable_memory_monitoring = enable_memory_monitoring
        self.logger = logging.getLogger(self.__class__.__name__)

        # Sort jobs by priority (1=highest priority)
        self.jobs.sort(key=lambda job: job.priority)

        # Batch metrics
        self.batch_start_time: Optional[datetime] = None
        self.batch_end_time: Optional[datetime] = None
        self.total_memory_used_mb: float = 0.0

    async def process_batch_async(self) -> Dict[str, Any]:
        """Processes all jobs in the batch asynchronously with memory optimization."""
        self.batch_start_time = datetime.now()
        self.logger.info(f"Starting batch processing of {len(self.jobs)} jobs")

        # Group jobs by type for optimized processing
        job_groups = self._group_jobs_by_type()

        completed_jobs = 0
        failed_jobs = 0

        for job_type, type_jobs in job_groups.items():
            self.logger.info(f"Processing {len(type_jobs)} {job_type} jobs")

            # Process jobs in chunks to manage memory
            chunk_size = min(self.max_concurrent_jobs, len(type_jobs))

            for i in range(0, len(type_jobs), chunk_size):
                chunk = type_jobs[i : i + chunk_size]

                # Check memory usage before processing chunk
                if self.enable_memory_monitoring:
                    current_memory = self._get_memory_usage()
                    if current_memory > self.memory_limit_mb:
                        self.logger.warning(
                            f"Memory usage ({current_memory:.2f}MB) exceeds limit "
                            f"({self.memory_limit_mb}MB). Forcing garbage collection."
                        )
                        gc.collect()

                        # Check again after GC
                        current_memory = self._get_memory_usage()
                        if current_memory > self.memory_limit_mb:
                            self.logger.error(
                                f"Memory usage still high after GC. "
                                f"Consider reducing batch size or increasing memory limit."
                            )

                # Process chunk concurrently
                chunk_results = await asyncio.gather(
                    *[job.run_async() for job in chunk], return_exceptions=True
                )

                # Count results
                for result in chunk_results:
                    if isinstance(result, Exception):
                        failed_jobs += 1
                    elif result:
                        completed_jobs += 1
                    else:
                        failed_jobs += 1

                # Force garbage collection after each chunk
                gc.collect()

        self.batch_end_time = datetime.now()

        # Calculate total metrics
        total_processing_time = (
            self.batch_end_time - self.batch_start_time
        ).total_seconds()
        self.total_memory_used_mb = sum(
            job.metrics.memory_usage_mb for job in self.jobs
        )

        summary = {
            "total_jobs": len(self.jobs),
            "completed": completed_jobs,
            "failed": failed_jobs,
            "processing_time_seconds": total_processing_time,
            "total_memory_used_mb": self.total_memory_used_mb,
            "average_job_time": total_processing_time / len(self.jobs)
            if self.jobs
            else 0,
            "jobs_per_second": len(self.jobs) / total_processing_time
            if total_processing_time > 0
            else 0,
        }

        self.logger.info(
            f"Batch processing completed: {completed_jobs} successful, "
            f"{failed_jobs} failed in {total_processing_time:.2f}s"
        )

        return summary

    def process_batch(self) -> Dict[str, Any]:
        """Synchronous wrapper for batch processing."""
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.process_batch_async())
        except RuntimeError:
            # No event loop running, create new one
            return asyncio.run(self.process_batch_async())

    def _group_jobs_by_type(self) -> Dict[str, List[ImportJob]]:
        """Group jobs by type for optimized processing."""
        groups = {}
        for job in self.jobs:
            if job.job_type not in groups:
                groups[job.job_type] = []
            groups[job.job_type].append(job)
        return groups

    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except Exception:
            return 0.0

    def get_summary(self) -> Dict[str, Any]:
        """Returns a comprehensive summary of the batch processing results."""
        summary = {
            "total_jobs": len(self.jobs),
            "completed": sum(1 for job in self.jobs if job.status == "completed"),
            "failed": sum(1 for job in self.jobs if job.status == "failed"),
            "pending": sum(1 for job in self.jobs if job.status == "pending"),
            "processing": sum(1 for job in self.jobs if job.status == "processing"),
        }

        # Add detailed metrics if available
        completed_jobs = [job for job in self.jobs if job.status == "completed"]
        if completed_jobs:
            processing_times = [
                job.metrics.processing_time_seconds for job in completed_jobs
            ]
            memory_usage = [job.metrics.memory_usage_mb for job in completed_jobs]

            summary.update(
                {
                    "average_processing_time": sum(processing_times)
                    / len(processing_times),
                    "total_memory_used_mb": sum(memory_usage),
                    "average_memory_per_job_mb": sum(memory_usage) / len(memory_usage),
                    "total_records_processed": sum(
                        job.metrics.records_processed for job in completed_jobs
                    ),
                }
            )

        return summary

    def get_failed_jobs(self) -> List[ImportJob]:
        """Returns list of failed jobs for retry or analysis."""
        return [job for job in self.jobs if job.status == "failed"]

    def retry_failed_jobs(self) -> Dict[str, Any]:
        """Retry all failed jobs."""
        failed_jobs = self.get_failed_jobs()
        if not failed_jobs:
            return {"message": "No failed jobs to retry"}

        # Reset failed jobs to pending
        for job in failed_jobs:
            job.status = "pending"
            job.error = None
            job.result = None
            job.metrics = JobMetrics()

        # Create new batch processor for retry
        retry_processor = BatchProcessor(
            failed_jobs,
            self.max_concurrent_jobs,
            self.memory_limit_mb,
            self.enable_memory_monitoring,
        )

        return retry_processor.process_batch()
