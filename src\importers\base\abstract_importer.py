"""Abstract base class for all data importers in the Connect telecommunications system.

This module provides the standardized interface and common functionality for all
data importers, including configuration loading, error handling, and async support.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, AsyncGenerator
from dataclasses import dataclass, field
from enum import Enum

import pandas as pd
from pydantic import BaseModel, Field, validator, field_validator, ConfigDict


class ImportStatus(Enum):
    """Status enumeration for import operations."""
    PENDING = "pending"
    VALIDATING = "validating"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DataEngine(Enum):
    """Supported data processing engines."""
    PANDAS = "pandas"
    POLARS = "polars"


@dataclass
class ImportMetrics:
    """Comprehensive metrics for import operations."""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    records_processed: int = 0
    records_validated: int = 0
    records_failed: int = 0
    memory_usage_mb: float = 0.0
    processing_time_seconds: float = 0.0
    validation_time_seconds: float = 0.0
    throughput_records_per_second: float = 0.0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

    def calculate_throughput(self) -> float:
        """Calculate processing throughput."""
        if self.processing_time_seconds > 0:
            self.throughput_records_per_second = self.records_processed / self.processing_time_seconds
        return self.throughput_records_per_second


@dataclass
class ImportResult:
    """Comprehensive result object for import operations."""
    status: ImportStatus
    data: Optional[Union[pd.DataFrame, Any]] = None
    metrics: ImportMetrics = field(default_factory=ImportMetrics)
    source_info: Dict[str, Any] = field(default_factory=dict)
    validation_results: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    @property
    def success(self) -> bool:
        """Check if import was successful."""
        return self.status == ImportStatus.COMPLETED

    @property
    def has_warnings(self) -> bool:
        """Check if import has warnings."""
        return len(self.warnings) > 0 or len(self.metrics.warnings) > 0


class ImporterConfig(BaseModel):
    """Pydantic configuration model for importers."""
    name: str = Field(..., description="Importer name")
    data_type: str = Field(..., description="Type of data being imported")
    supported_formats: List[str] = Field(default_factory=list, description="Supported file formats")
    batch_size: int = Field(default=10000, ge=1, le=1000000, description="Batch processing size")
    max_memory_mb: int = Field(default=1024, ge=128, le=8192, description="Maximum memory usage in MB")
    enable_async: bool = Field(default=True, description="Enable asynchronous processing")
    data_engine: DataEngine = Field(default=DataEngine.PANDAS, description="Data processing engine")
    validation_enabled: bool = Field(default=True, description="Enable data validation")
    performance_monitoring: bool = Field(default=True, description="Enable performance monitoring")
    log_level: str = Field(default="INFO", description="Logging level")
    
    # Telecommunications-specific settings
    operator_schemas: Dict[str, str] = Field(default_factory=dict, description="Operator-specific schemas")
    geospatial_enabled: bool = Field(default=False, description="Enable geospatial processing")
    quality_thresholds: Dict[str, float] = Field(default_factory=dict, description="Data quality thresholds")
    
    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'log_level must be one of {valid_levels}')
        return v.upper()

    model_config = ConfigDict(
        use_enum_values=True
    )
class TelecomImportError(Exception):
    """Base exception for telecommunications import operations."""
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = datetime.now()


class ValidationError(TelecomImportError):
    """Exception raised during data validation."""
    pass


class ProcessingError(TelecomImportError):
    """Exception raised during data processing."""
    pass


class ConfigurationError(TelecomImportError):
    """Exception raised for configuration issues."""
    pass


class AbstractImporter(ABC):
    """Abstract base class for all data importers.
    
    This class provides the standardized interface and common functionality
    for all data importers in the Connect telecommunications system.
    """
    
    def __init__(self, config: Union[ImporterConfig, Dict[str, Any]], **kwargs):
        """Initialize the importer with configuration.

        Args:
            config: Importer configuration (Pydantic model or dict)
            **kwargs: Additional configuration parameters
        """
        # Call super().__init__() to support multiple inheritance
        super().__init__()

        # Convert dict config to Pydantic model if needed
        if isinstance(config, dict):
            config.update(kwargs)
            self.config = ImporterConfig(**config)
        elif isinstance(config, str):
            # Handle string config as name
            config_dict = {'name': config}
            config_dict.update(kwargs)
            self.config = ImporterConfig(**config_dict)
        elif config is None:
            # Handle None config
            config_dict = {'name': self.__class__.__name__.lower()}
            config_dict.update(kwargs)
            self.config = ImporterConfig(**config_dict)
        else:
            self.config = config

        # Setup logging with safe config access
        config_name = getattr(self.config, 'name', self.__class__.__name__.lower())
        self.logger = logging.getLogger(f"{self.__class__.__name__}.{config_name}")

        # Set log level safely
        log_level = getattr(self.config, 'log_level', 'INFO')
        self.logger.setLevel(getattr(logging, log_level))

        # Load telecom configuration (migrated from BaseImporter)
        self._load_telecom_config()

        # Initialize metrics
        self.metrics = ImportMetrics()

        # Load telecommunications configuration
        self._load_telecom_config()

        # Initialize data engine
        self._initialize_data_engine()
        
    def _load_telecom_config(self) -> None:
        """Load telecommunications-specific configuration (enhanced from BaseImporter)."""
        try:
            # Try to load from new Pydantic config system first
            try:
                from src.config import get_config
                self.pydantic_config = get_config()
                self.telecom_config = self.pydantic_config.telecom

                # Get data source specific configuration
                data_type = getattr(self, 'data_type', None) or getattr(self.config, 'data_type', None)
                if data_type:
                    self.data_source_config = self._get_data_source_config(data_type)
                else:
                    self.data_source_config = {}

                self.logger.debug(f"Loaded Pydantic telecom config for {data_type}")
                return

            except Exception as e:
                self.logger.warning(f"Failed to load Pydantic config, using fallback: {e}")

            # Fallback to legacy config loading
            self.telecom_config = {
                'operators': getattr(self.config, 'operator_schemas', {}),
                'quality_thresholds': getattr(self.config, 'quality_thresholds', {}),
                'geospatial_enabled': getattr(self.config, 'geospatial_enabled', True)
            }
            self.data_source_config = {}
            self.pydantic_config = None

            self.logger.debug(f"Loaded fallback telecom config: {self.telecom_config}")

        except Exception as e:
            self.logger.warning(f"Failed to load telecom configuration: {e}")
            self.telecom_config = {}
            self.data_source_config = {}
            self.pydantic_config = None

    def _get_data_source_config(self, data_type: str) -> Dict[str, Any]:
        """Get configuration for specific data type (migrated from BaseImporter).

        Args:
            data_type: Data type (ep, cdr, nlg, kpi, etc.)

        Returns:
            Data source configuration dictionary
        """
        try:
            # Try to load telecom data source configuration from database.yaml
            import yaml
            from pathlib import Path

            config_path = Path(__file__).parent.parent.parent.parent / "config" / "database.yaml"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    db_config = yaml.safe_load(f)
                    telecom_sources = db_config.get('telecom_data_sources', {})
                    return telecom_sources.get(data_type, {})
        except Exception as e:
            self.logger.warning(f"Failed to load data source configuration: {e}")

        return {}

    def _detect_csv_structure_with_config(self, file_path: Path, data_type: str) -> dict:
        """Detect CSV file structure using configuration-driven approach.

        This method combines intelligent detection with database.yaml configuration
        to provide accurate CSV parsing parameters.

        Args:
            file_path: Path to the CSV file
            data_type: Data type (ep, cdr, nlg, kpi, cfg, score)

        Returns:
            dict: CSV structure configuration with keys:
                - encoding: File encoding
                - delimiter: CSV delimiter
                - skip_rows: Number of rows to skip
                - header_row: Header row index
        """
        # Default structure
        default_structure = {
            'encoding': 'utf-8',
            'delimiter': ',',
            'skip_rows': 0,
            'header_row': 0
        }

        try:
            # Load database configuration
            from src.config import get_config
            config = get_config()

            # Get data type specific configuration from database.yaml
            if hasattr(config, 'database') and hasattr(config.database, 'data_sources'):
                data_sources = config.database.data_sources
                if data_type in data_sources:
                    type_config = data_sources[data_type]

                    # Apply configuration values
                    if hasattr(type_config, 'skip_rows'):
                        default_structure['skip_rows'] = type_config.skip_rows
                    if hasattr(type_config, 'header_row'):
                        default_structure['header_row'] = type_config.header_row

                    self.logger.info(f"Applied {data_type} config: skip_rows={default_structure['skip_rows']}, header_row={default_structure['header_row']}")

            # Try to detect encoding
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        f.read(1024)  # Try to read first 1KB
                    default_structure['encoding'] = encoding
                    break
                except UnicodeDecodeError:
                    continue

            # Try to detect delimiter
            import csv
            with open(file_path, 'r', encoding=default_structure['encoding']) as f:
                # Skip the configured number of rows
                for _ in range(default_structure['skip_rows']):
                    try:
                        next(f)
                    except StopIteration:
                        break

                sample = f.read(1024)
                sniffer = csv.Sniffer()
                try:
                    dialect = sniffer.sniff(sample, delimiters=',;\t|')
                    default_structure['delimiter'] = dialect.delimiter
                except:
                    # Keep default comma delimiter
                    pass

        except Exception as e:
            self.logger.warning(f"Failed to detect CSV structure for {data_type}: {e}, using defaults")

        return default_structure

    def _get_data_source_config(self, data_type: str) -> dict:
        """Get configuration for specific data type.

        Args:
            data_type: Data type (ep, cdr, nlg, kpi, cfg, score)

        Returns:
            Dictionary with data source configuration
        """
        # Get configuration for this data type
        config = {}

        try:
            # Load configuration directly from database.yaml
            import yaml
            from pathlib import Path

            # Try to load database.yaml directly
            config_path = Path("config/database.yaml")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    yaml_config = yaml.safe_load(f)

                # Get telecom data sources configuration
                if 'telecom_data_sources' in yaml_config and data_type in yaml_config['telecom_data_sources']:
                    config = yaml_config['telecom_data_sources'][data_type]
                    self.logger.info(f"Loaded {data_type} configuration from database.yaml: {config}")
                else:
                    self.logger.warning(f"No configuration found for {data_type} in database.yaml")
            else:
                self.logger.warning(f"database.yaml not found at {config_path}")

        except Exception as e:
            self.logger.warning(f"Failed to load {data_type} configuration: {e}")

        return config

        # Initialize structure with configuration defaults
        structure = {
            'skip_rows': config.get('skip_rows', 0),
            'header_row': config.get('header_row', 0),
            'delimiter': ',',
            'encoding': 'utf-8',
            'comment_char': None,
            'config_source': 'database.yaml' if config else 'auto-detected'
        }

        self.logger.info(f"Using {data_type} configuration: skip_rows={structure['skip_rows']}, header_row={structure['header_row']}")

        # Try different encodings to find the correct one
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    lines = []
                    for i, line in enumerate(f):
                        lines.append(line.strip())
                        if i >= 20:  # Read first 20 lines for analysis
                            break

                structure['encoding'] = encoding
                break
            except UnicodeDecodeError:
                continue

        if not lines:
            self.logger.warning(f"Could not read any lines from {file_path}")
            return structure

        # Detect delimiter from the header row (after skipping configured rows)
        header_line_index = structure['skip_rows'] + structure['header_row']
        if header_line_index < len(lines):
            header_line = lines[header_line_index]

            if header_line.strip():
                # Use CSV sniffer to detect delimiter
                sniffer = csv.Sniffer()
                try:
                    dialect = sniffer.sniff(header_line, delimiters=',;\t|:')
                    structure['delimiter'] = dialect.delimiter
                    self.logger.debug(f"Detected delimiter: '{structure['delimiter']}'")
                except:
                    # Fallback delimiter detection
                    delimiters = [',', ';', '\t', '|', ':']
                    delimiter_counts = {}
                    for delim in delimiters:
                        delimiter_counts[delim] = header_line.count(delim)

                    if delimiter_counts:
                        structure['delimiter'] = max(delimiter_counts, key=delimiter_counts.get)
                        self.logger.debug(f"Fallback delimiter detection: '{structure['delimiter']}'")

        # Log final structure for debugging
        self.logger.info(f"Final CSV structure for {data_type}: {structure}")

        return structure

    def _initialize_data_engine(self) -> None:
        """Initialize the data processing engine."""
        self.data_engine = self.config.data_engine
        if self.data_engine == DataEngine.POLARS:
            try:
                import polars as pl
                self.pl = pl
                self.logger.info("Initialized Polars data engine")
            except ImportError:
                self.logger.warning("Polars not available, falling back to Pandas")
                self.data_engine = DataEngine.PANDAS
                
    @abstractmethod
    async def import_data(self, source: Union[str, Path, Any]) -> ImportResult:
        """Import data from the specified source.
        
        Args:
            source: Data source (file path, URL, or data object)
            
        Returns:
            ImportResult: Comprehensive import result
        """
        pass
        
    @abstractmethod
    async def validate_source(self, source: Union[str, Path, Any]) -> bool:
        """Validate that the data source exists and is accessible.
        
        Args:
            source: Data source to validate
            
        Returns:
            bool: True if source is valid
        """
        pass
        
    @abstractmethod
    async def get_source_info(self, source: Union[str, Path, Any]) -> Dict[str, Any]:
        """Get information about the data source.
        
        Args:
            source: Data source to analyze
            
        Returns:
            Dict containing source information (size, type, etc.)
        """
        pass
        
    async def process_batch(self, data_batch: Any) -> Any:
        """Process a batch of data.
        
        Args:
            data_batch: Batch of data to process
            
        Returns:
            Processed data batch
        """
        # Default implementation - can be overridden by subclasses
        return data_batch

    def validate_source_path(self, source_path: Union[str, Path]) -> bool:
        """Validate that the source path exists and is accessible (from BaseImporter).

        Args:
            source_path: Path to validate

        Returns:
            bool: True if source is valid

        Raises:
            TelecomImportError: If source is invalid
        """
        path = Path(source_path)
        if not path.exists():
            raise TelecomImportError(f"Source path does not exist: {source_path}")
        return True

    def get_source_info_from_path(self, source_path: Union[str, Path]) -> Dict[str, Any]:
        """Get information about a file source (from BaseImporter).

        Args:
            source_path: Path to analyze

        Returns:
            Dict: Source information
        """
        path = Path(source_path)
        if not path.exists():
            return {"exists": False}

        stat = path.stat()
        return {
            "exists": True,
            "size_bytes": stat.st_size,
            "modified": stat.st_mtime,
            "is_file": path.is_file(),
            "is_directory": path.is_dir(),
            "extension": path.suffix,
            "name": path.name,
        }
        
    async def validate_batch(self, data_batch: Any) -> Dict[str, Any]:
        """Validate a batch of data.
        
        Args:
            data_batch: Batch of data to validate
            
        Returns:
            Dict containing validation results
        """
        # Default implementation - can be overridden by subclasses
        return {'valid': True, 'errors': [], 'warnings': []}
        
    def get_metrics(self) -> ImportMetrics:
        """Get current import metrics.
        
        Returns:
            ImportMetrics: Current metrics
        """
        return self.metrics
        
    def reset_metrics(self) -> None:
        """Reset import metrics."""
        self.metrics = ImportMetrics()
        
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the importer.
        
        Returns:
            Dict containing health status
        """
        return {
            'status': 'healthy',
            'config': self.config.dict(),
            'data_engine': self.data_engine.value,
            'telecom_config_loaded': bool(self.telecom_config),
            'timestamp': datetime.now().isoformat()
        }