"""Query condition classes for building WHERE clauses.

This module provides classes for building complex WHERE conditions
with proper parameter binding and SQL injection protection.
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union

from loguru import logger

from ..exceptions import ValidationError
from .dialects import Dialect


class Operator(Enum):
    """SQL comparison operators."""

    EQ = "="
    NE = "!="
    LT = "<"
    LE = "<="
    GT = ">"
    GE = ">="
    LIKE = "LIKE"
    ILIKE = "ILIKE"
    NOT_LIKE = "NOT LIKE"
    NOT_ILIKE = "NOT ILIKE"
    IN = "IN"
    NOT_IN = "NOT IN"
    IS_NULL = "IS NULL"
    IS_NOT_NULL = "IS NOT NULL"
    BETWEEN = "BETWEEN"
    NOT_BETWEEN = "NOT BETWEEN"
    EXISTS = "EXISTS"
    NOT_EXISTS = "NOT EXISTS"
    REGEXP = "~"
    NOT_REGEXP = "!~"
    SIMILAR_TO = "SIMILAR TO"
    NOT_SIMILAR_TO = "NOT SIMILAR TO"


class Condition(ABC):
    """Abstract base class for query conditions."""

    def __init__(self):
        """Initialize condition."""
        self._parameter_counter = 0

    @abstractmethod
    def build(self, dialect: Dialect) -> Tuple[str, Dict[str, Any]]:
        """Build the condition SQL and parameters.

        Args:
            dialect: SQL dialect

        Returns:
            Tuple of (condition_sql, parameters)
        """
        pass

    def _get_parameter_name(self, prefix: str = "cond") -> str:
        """Generate unique parameter name.

        Args:
            prefix: Parameter name prefix

        Returns:
            Unique parameter name
        """
        self._parameter_counter += 1
        return f"{prefix}_{id(self)}_{self._parameter_counter}"

    def _validate_identifier(self, identifier: str, dialect: Dialect) -> str:
        """Validate and escape SQL identifier.

        Args:
            identifier: SQL identifier
            dialect: SQL dialect

        Returns:
            Escaped identifier
        """
        return dialect.escape_identifier(identifier)

    def __and__(self, other: "Condition") -> "AndCondition":
        """Combine conditions with AND.

        Args:
            other: Other condition

        Returns:
            AndCondition instance
        """
        return AndCondition(self, other)

    def __or__(self, other: "Condition") -> "OrCondition":
        """Combine conditions with OR.

        Args:
            other: Other condition

        Returns:
            OrCondition instance
        """
        return OrCondition(self, other)

    def __invert__(self) -> "NotCondition":
        """Negate condition with NOT.

        Returns:
            NotCondition instance
        """
        return NotCondition(self)


class SimpleCondition(Condition):
    """Simple field-operator-value condition."""

    def __init__(self, field: str, operator: Operator, value: Any = None):
        """Initialize simple condition.

        Args:
            field: Field name
            operator: Comparison operator
            value: Comparison value (not needed for IS NULL/IS NOT NULL)
        """
        super().__init__()
        self.field = field
        self.operator = operator
        self.value = value

        # Validate operator-value combination
        if operator in [Operator.IS_NULL, Operator.IS_NOT_NULL] and value is not None:
            raise ValidationError(f"Operator {operator.value} should not have a value")
        elif operator not in [Operator.IS_NULL, Operator.IS_NOT_NULL] and value is None:
            raise ValidationError(f"Operator {operator.value} requires a value")

    def build(self, dialect: Dialect) -> Tuple[str, Dict[str, Any]]:
        """Build simple condition SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Tuple of (condition_sql, parameters)
        """
        try:
            field_name = self._validate_identifier(self.field, dialect)
            parameters = {}

            # Handle operators that don't need parameters
            if self.operator in [Operator.IS_NULL, Operator.IS_NOT_NULL]:
                condition_sql = f"{field_name} {self.operator.value}"
                return condition_sql, parameters

            # Handle IN and NOT IN operators
            if self.operator in [Operator.IN, Operator.NOT_IN]:
                if not isinstance(self.value, (list, tuple)):
                    raise ValidationError(
                        f"Value for {self.operator.value} must be a list or tuple"
                    )

                if not self.value:
                    # Empty list handling
                    if self.operator == Operator.IN:
                        return "FALSE", parameters
                    else:  # NOT IN
                        return "TRUE", parameters

                placeholders = []
                for i, val in enumerate(self.value):
                    param_name = self._get_parameter_name(f"in_{i}")
                    placeholder = dialect.get_parameter_placeholder(param_name)
                    placeholders.append(placeholder)
                    parameters[param_name] = val

                condition_sql = (
                    f"{field_name} {self.operator.value} ({', '.join(placeholders)})"
                )
                return condition_sql, parameters

            # Handle BETWEEN operator
            if self.operator in [Operator.BETWEEN, Operator.NOT_BETWEEN]:
                if not isinstance(self.value, (list, tuple)) or len(self.value) != 2:
                    raise ValidationError(
                        f"Value for {self.operator.value} must be a list/tuple with exactly 2 elements"
                    )

                param1_name = self._get_parameter_name("between_start")
                param2_name = self._get_parameter_name("between_end")

                placeholder1 = dialect.get_parameter_placeholder(param1_name)
                placeholder2 = dialect.get_parameter_placeholder(param2_name)

                parameters[param1_name] = self.value[0]
                parameters[param2_name] = self.value[1]

                condition_sql = f"{field_name} {self.operator.value} {placeholder1} AND {placeholder2}"
                return condition_sql, parameters

            # Handle EXISTS and NOT EXISTS
            if self.operator in [Operator.EXISTS, Operator.NOT_EXISTS]:
                if not isinstance(self.value, str):
                    raise ValidationError(
                        f"Value for {self.operator.value} must be a subquery string"
                    )

                condition_sql = f"{self.operator.value} ({self.value})"
                return condition_sql, parameters

            # Handle regular operators
            param_name = self._get_parameter_name()
            placeholder = dialect.get_parameter_placeholder(param_name)
            parameters[param_name] = self.value

            condition_sql = f"{field_name} {self.operator.value} {placeholder}"
            return condition_sql, parameters

        except Exception as e:
            logger.error(f"Failed to build simple condition: {e}")
            raise ValidationError(f"Failed to build condition: {e}")


class RawCondition(Condition):
    """Raw SQL condition with optional parameters."""

    def __init__(self, sql: str, parameters: Optional[Dict[str, Any]] = None):
        """Initialize raw condition.

        Args:
            sql: Raw SQL condition
            parameters: Optional parameters dictionary
        """
        super().__init__()
        self.sql = sql
        self.parameters = parameters or {}

    def build(self, dialect: Dialect) -> Tuple[str, Dict[str, Any]]:
        """Build raw condition SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Tuple of (condition_sql, parameters)
        """
        return self.sql, self.parameters.copy()


class AndCondition(Condition):
    """AND combination of multiple conditions."""

    def __init__(self, *conditions: Condition):
        """Initialize AND condition.

        Args:
            *conditions: Conditions to combine with AND
        """
        super().__init__()
        if not conditions:
            raise ValidationError("AND condition requires at least one condition")
        self.conditions = list(conditions)

    def build(self, dialect: Dialect) -> Tuple[str, Dict[str, Any]]:
        """Build AND condition SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Tuple of (condition_sql, parameters)
        """
        try:
            condition_parts = []
            all_parameters = {}

            for condition in self.conditions:
                condition_sql, condition_params = condition.build(dialect)
                condition_parts.append(f"({condition_sql})")
                all_parameters.update(condition_params)

            condition_sql = " AND ".join(condition_parts)
            return condition_sql, all_parameters

        except Exception as e:
            logger.error(f"Failed to build AND condition: {e}")
            raise ValidationError(f"Failed to build AND condition: {e}")


class OrCondition(Condition):
    """OR combination of multiple conditions."""

    def __init__(self, *conditions: Condition):
        """Initialize OR condition.

        Args:
            *conditions: Conditions to combine with OR
        """
        super().__init__()
        if not conditions:
            raise ValidationError("OR condition requires at least one condition")
        self.conditions = list(conditions)

    def build(self, dialect: Dialect) -> Tuple[str, Dict[str, Any]]:
        """Build OR condition SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Tuple of (condition_sql, parameters)
        """
        try:
            condition_parts = []
            all_parameters = {}

            for condition in self.conditions:
                condition_sql, condition_params = condition.build(dialect)
                condition_parts.append(f"({condition_sql})")
                all_parameters.update(condition_params)

            condition_sql = " OR ".join(condition_parts)
            return condition_sql, all_parameters

        except Exception as e:
            logger.error(f"Failed to build OR condition: {e}")
            raise ValidationError(f"Failed to build OR condition: {e}")


class NotCondition(Condition):
    """NOT negation of a condition."""

    def __init__(self, condition: Condition):
        """Initialize NOT condition.

        Args:
            condition: Condition to negate
        """
        super().__init__()
        self.condition = condition

    def build(self, dialect: Dialect) -> Tuple[str, Dict[str, Any]]:
        """Build NOT condition SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Tuple of (condition_sql, parameters)
        """
        try:
            condition_sql, parameters = self.condition.build(dialect)
            negated_sql = f"NOT ({condition_sql})"
            return negated_sql, parameters

        except Exception as e:
            logger.error(f"Failed to build NOT condition: {e}")
            raise ValidationError(f"Failed to build NOT condition: {e}")


class FieldCondition(Condition):
    """Condition comparing two fields."""

    def __init__(self, field1: str, operator: Operator, field2: str):
        """Initialize field comparison condition.

        Args:
            field1: First field name
            operator: Comparison operator
            field2: Second field name
        """
        super().__init__()
        self.field1 = field1
        self.operator = operator
        self.field2 = field2

        # Validate operator
        if operator in [
            Operator.IS_NULL,
            Operator.IS_NOT_NULL,
            Operator.IN,
            Operator.NOT_IN,
            Operator.BETWEEN,
            Operator.NOT_BETWEEN,
            Operator.EXISTS,
            Operator.NOT_EXISTS,
        ]:
            raise ValidationError(
                f"Operator {operator.value} is not valid for field comparison"
            )

    def build(self, dialect: Dialect) -> Tuple[str, Dict[str, Any]]:
        """Build field comparison condition SQL.

        Args:
            dialect: SQL dialect

        Returns:
            Tuple of (condition_sql, parameters)
        """
        try:
            field1_name = self._validate_identifier(self.field1, dialect)
            field2_name = self._validate_identifier(self.field2, dialect)

            condition_sql = f"{field1_name} {self.operator.value} {field2_name}"
            return condition_sql, {}

        except Exception as e:
            logger.error(f"Failed to build field condition: {e}")
            raise ValidationError(f"Failed to build field condition: {e}")


# Convenience functions for creating conditions
def eq(field: str, value: Any) -> SimpleCondition:
    """Create equality condition."""
    return SimpleCondition(field, Operator.EQ, value)


def ne(field: str, value: Any) -> SimpleCondition:
    """Create not-equal condition."""
    return SimpleCondition(field, Operator.NE, value)


def lt(field: str, value: Any) -> SimpleCondition:
    """Create less-than condition."""
    return SimpleCondition(field, Operator.LT, value)


def le(field: str, value: Any) -> SimpleCondition:
    """Create less-than-or-equal condition."""
    return SimpleCondition(field, Operator.LE, value)


def gt(field: str, value: Any) -> SimpleCondition:
    """Create greater-than condition."""
    return SimpleCondition(field, Operator.GT, value)


def ge(field: str, value: Any) -> SimpleCondition:
    """Create greater-than-or-equal condition."""
    return SimpleCondition(field, Operator.GE, value)


def like(field: str, pattern: str) -> SimpleCondition:
    """Create LIKE condition."""
    return SimpleCondition(field, Operator.LIKE, pattern)


def ilike(field: str, pattern: str) -> SimpleCondition:
    """Create case-insensitive LIKE condition."""
    return SimpleCondition(field, Operator.ILIKE, pattern)


def not_like(field: str, pattern: str) -> SimpleCondition:
    """Create NOT LIKE condition."""
    return SimpleCondition(field, Operator.NOT_LIKE, pattern)


def not_ilike(field: str, pattern: str) -> SimpleCondition:
    """Create case-insensitive NOT LIKE condition."""
    return SimpleCondition(field, Operator.NOT_ILIKE, pattern)


def in_(field: str, values: List[Any]) -> SimpleCondition:
    """Create IN condition."""
    return SimpleCondition(field, Operator.IN, values)


def not_in(field: str, values: List[Any]) -> SimpleCondition:
    """Create NOT IN condition."""
    return SimpleCondition(field, Operator.NOT_IN, values)


def is_null(field: str) -> SimpleCondition:
    """Create IS NULL condition."""
    return SimpleCondition(field, Operator.IS_NULL)


def is_not_null(field: str) -> SimpleCondition:
    """Create IS NOT NULL condition."""
    return SimpleCondition(field, Operator.IS_NOT_NULL)


def between(field: str, start: Any, end: Any) -> SimpleCondition:
    """Create BETWEEN condition."""
    return SimpleCondition(field, Operator.BETWEEN, [start, end])


def not_between(field: str, start: Any, end: Any) -> SimpleCondition:
    """Create NOT BETWEEN condition."""
    return SimpleCondition(field, Operator.NOT_BETWEEN, [start, end])


def exists(subquery: str) -> SimpleCondition:
    """Create EXISTS condition."""
    return SimpleCondition("", Operator.EXISTS, subquery)


def not_exists(subquery: str) -> SimpleCondition:
    """Create NOT EXISTS condition."""
    return SimpleCondition("", Operator.NOT_EXISTS, subquery)


def regexp(field: str, pattern: str) -> SimpleCondition:
    """Create regular expression condition."""
    return SimpleCondition(field, Operator.REGEXP, pattern)


def not_regexp(field: str, pattern: str) -> SimpleCondition:
    """Create NOT regular expression condition."""
    return SimpleCondition(field, Operator.NOT_REGEXP, pattern)


def similar_to(field: str, pattern: str) -> SimpleCondition:
    """Create SIMILAR TO condition."""
    return SimpleCondition(field, Operator.SIMILAR_TO, pattern)


def not_similar_to(field: str, pattern: str) -> SimpleCondition:
    """Create NOT SIMILAR TO condition."""
    return SimpleCondition(field, Operator.NOT_SIMILAR_TO, pattern)


def raw(sql: str, parameters: Optional[Dict[str, Any]] = None) -> RawCondition:
    """Create raw SQL condition."""
    return RawCondition(sql, parameters)


def and_(*conditions: Condition) -> AndCondition:
    """Create AND condition."""
    return AndCondition(*conditions)


def or_(*conditions: Condition) -> OrCondition:
    """Create OR condition."""
    return OrCondition(*conditions)


def not_(condition: Condition) -> NotCondition:
    """Create NOT condition."""
    return NotCondition(condition)


def field_eq(field1: str, field2: str) -> FieldCondition:
    """Create field equality condition."""
    return FieldCondition(field1, Operator.EQ, field2)


def field_ne(field1: str, field2: str) -> FieldCondition:
    """Create field not-equal condition."""
    return FieldCondition(field1, Operator.NE, field2)


def field_lt(field1: str, field2: str) -> FieldCondition:
    """Create field less-than condition."""
    return FieldCondition(field1, Operator.LT, field2)


def field_le(field1: str, field2: str) -> FieldCondition:
    """Create field less-than-or-equal condition."""
    return FieldCondition(field1, Operator.LE, field2)


def field_gt(field1: str, field2: str) -> FieldCondition:
    """Create field greater-than condition."""
    return FieldCondition(field1, Operator.GT, field2)


def field_ge(field1: str, field2: str) -> FieldCondition:
    """Create field greater-than-or-equal condition."""
    return FieldCondition(field1, Operator.GE, field2)
