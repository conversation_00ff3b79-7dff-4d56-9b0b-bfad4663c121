"""Pydantic Configuration Models

This module defines all configuration models using Pydantic for type safety and validation.
Replaces the custom configuration framework with clean, simple Pydantic-based implementation.
"""

import os
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict

# Try to import BaseSettings from the correct location
try:
    from pydantic_settings import BaseSettings
except ImportError:
    try:
        from pydantic import BaseSettings, field_validator, ConfigDict
    except ImportError:
        # Fallback for older Pydantic versions
        BaseSettings = BaseModel


class Environment(str, Enum):
    """Environment types for the application."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class LogLevel(str, Enum):
    """Logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class DatabasePoolConfig(BaseModel):
    """Database connection pool configuration."""
    min_size: int = Field(default=5, ge=1, le=100)
    max_size: int = Field(default=20, ge=1, le=100)
    timeout: int = Field(default=30, ge=1)
    recycle: int = Field(default=3600, ge=60)

    @field_validator('max_size')
    @classmethod
    def max_size_must_be_greater_than_min_size(cls, v, info):
        if info.data.get('min_size') and v < info.data['min_size']:
            raise ValueError('max_size must be greater than or equal to min_size')
        return v


class DatabaseConnectionConfig(BaseModel):
    """Database connection configuration."""
    timeout: int = Field(default=30, ge=1)
    command_timeout: int = Field(default=60, ge=1)
    retry_attempts: int = Field(default=3, ge=0, le=10)
    retry_delay: float = Field(default=1.0, ge=0.1)


class DatabaseConfig(BaseModel):
    """Database configuration."""
    host: str = Field(default="localhost")
    port: int = Field(default=5432, ge=1, le=65535)
    name: str = Field(default="connect")
    user: str = Field(default="connect_user")
    password: str = Field(default="", env="DATABASE_PASSWORD")
    ssl_mode: str = Field(default="prefer")
    pool: DatabasePoolConfig = Field(default_factory=DatabasePoolConfig)
    connection: DatabaseConnectionConfig = Field(default_factory=DatabaseConnectionConfig)

    @field_validator('ssl_mode')
    @classmethod
    def validate_ssl_mode(cls, v):
        valid_modes = ['disable', 'allow', 'prefer', 'require', 'verify-ca', 'verify-full']
        if v not in valid_modes:
            raise ValueError(f'ssl_mode must be one of {valid_modes}')
        return v


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: LogLevel = LogLevel.INFO
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "logs/app.log"
    max_bytes: int = Field(default=10485760, ge=1024)  # 10MB
    backup_count: int = Field(default=5, ge=0)

    # Telecommunications module-specific logging (migrated from settings.yaml)
    telecom_modules: Dict[str, str] = Field(default_factory=lambda: {
        'ep_importer': 'logs/ep_importer.log',
        'cdr_importer': 'logs/cdr_importer.log',
        'nlg_importer': 'logs/nlg_importer.log'
    })


class ProjectConfig(BaseModel):
    """Project configuration."""
    name: str = "Connect Project"
    version: str = "1.0.0"
    description: str = "Telecommunications data processing and analysis project"


class GeoConfig(BaseModel):
    """Geospatial configuration."""
    default_crs: str = "EPSG:4326"
    output_format: str = "GeoJSON"
    precision: int = Field(default=8, ge=1, le=15)  # Updated from settings.yaml
    buffer_resolution: int = Field(default=16, ge=1)

    # Telecommunications-specific geospatial settings (migrated from settings.yaml)
    telecom_crs: str = "EPSG:4326"
    coverage_analysis_enabled: bool = True
    spatial_indexing: bool = True

    @field_validator('default_crs')
    @classmethod
    def validate_crs(cls, v):
        if not (v.startswith('EPSG:') or v.startswith('+proj=')):
            raise ValueError('CRS must start with "EPSG:" or "+proj="')
        return v

    @field_validator('output_format')
    @classmethod
    def validate_output_format(cls, v):
        valid_formats = ['GeoJSON', 'Shapefile', 'GeoPackage', 'KML']
        if v not in valid_formats:
            raise ValueError(f'output_format must be one of {valid_formats}')
        return v


class QGISConfig(BaseModel):
    """QGIS integration configuration."""
    auto_detect: bool = True
    path: Optional[str] = None
    plugins_enabled: bool = True

    # Telecommunications QGIS plugins (migrated from settings.yaml)
    telecom_plugins: List[str] = Field(default_factory=lambda: ['network_analyzer', 'coverage_mapper'])


class DataConfig(BaseModel):
    """Data directory configuration."""
    input_dir: str = "data/input"
    output_dir: str = "data/output"
    temp_dir: str = "data/temp"
    cache_enabled: bool = True

    # Telecommunications-specific data directories (migrated from settings.yaml)
    telecom_data_dir: str = "data/telecom"
    ep_data_dir: str = "data/telecom/ep"
    cdr_data_dir: str = "data/telecom/cdr"
    nlg_data_dir: str = "data/telecom/nlg"
    kpi_data_dir: str = "data/telecom/kpi"


class CDRConfig(BaseModel):
    """CDR (Call Detail Record) processing configuration."""
    batch_size: int = Field(default=10000, ge=100)
    table_prefix: str = "cdr_"
    validation_enabled: bool = True
    geospatial_enhancement: bool = True


class EPConfig(BaseModel):
    """EP (Engineering Parameters) processing configuration."""
    batch_size: int = Field(default=5000, ge=100)
    table_prefix: str = "ep_"
    signal_analysis: bool = True
    coverage_analysis: bool = True


class KPIAlertThresholds(BaseModel):
    """KPI alert thresholds."""
    call_success_rate: float = Field(default=95.0, ge=0.0, le=100.0)
    signal_strength_min: float = Field(default=-110.0, le=0.0)
    handover_success_rate: float = Field(default=98.0, ge=0.0, le=100.0)


class KPIConfig(BaseModel):
    """KPI (Key Performance Indicator) configuration."""
    calculation_interval: int = Field(default=300, ge=60)  # 5 minutes
    retention_days: int = Field(default=90, ge=1)
    real_time_enabled: bool = True
    alert_thresholds: KPIAlertThresholds = Field(default_factory=KPIAlertThresholds)


class PerformanceConfig(BaseModel):
    """Performance configuration."""
    max_memory_usage_mb: int = Field(default=2048, ge=512)
    processing_timeout_seconds: int = Field(default=3600, ge=60)
    parallel_workers: int = Field(default=8, ge=1, le=32)


class TelecomConfig(BaseModel):
    """Telecommunications domain-specific configuration."""
    cdr: CDRConfig = Field(default_factory=CDRConfig)
    ep: EPConfig = Field(default_factory=EPConfig)
    kpi: KPIConfig = Field(default_factory=KPIConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)


class JWTConfig(BaseModel):
    """JWT configuration."""
    algorithm: str = "HS256"
    access_token_expire_minutes: int = Field(default=30, ge=1)
    secret_key: str = Field(default="", env="JWT_SECRET_KEY")

    @field_validator('algorithm')
    @classmethod
    def validate_algorithm(cls, v):
        valid_algorithms = ['HS256', 'HS384', 'HS512', 'RS256', 'RS384', 'RS512']
        if v not in valid_algorithms:
            raise ValueError(f'algorithm must be one of {valid_algorithms}')
        return v


class PasswordConfig(BaseModel):
    """Password policy configuration."""
    min_length: int = Field(default=8, ge=4)
    require_uppercase: bool = True
    require_lowercase: bool = True
    require_numbers: bool = True
    require_special: bool = True


class SecurityConfig(BaseModel):
    """Security configuration."""
    jwt: JWTConfig = Field(default_factory=JWTConfig)
    password: PasswordConfig = Field(default_factory=PasswordConfig)


class AlertThresholds(BaseModel):
    """Monitoring alert thresholds."""
    cpu_usage: float = Field(default=80.0, ge=0.0, le=100.0)
    memory_usage: float = Field(default=85.0, ge=0.0, le=100.0)
    disk_usage: float = Field(default=90.0, ge=0.0, le=100.0)
    response_time_ms: float = Field(default=1000.0, ge=0.0)


class AlertsConfig(BaseModel):
    """Alerts configuration."""
    enabled: bool = True
    thresholds: AlertThresholds = Field(default_factory=AlertThresholds)


class MonitoringConfig(BaseModel):
    """Monitoring configuration."""
    enabled: bool = True
    metrics_interval: int = Field(default=60, ge=1)
    health_check_interval: int = Field(default=30, ge=1)
    performance_monitoring: bool = True
    memory_profiling: bool = False
    alerts: AlertsConfig = Field(default_factory=AlertsConfig)


class ProcessingConfig(BaseModel):
    """Processing configuration."""
    max_workers: int = Field(default=8, ge=1, le=32)  # Updated from settings.yaml
    chunk_size: int = Field(default=2000, ge=100)  # Updated from settings.yaml
    memory_limit: str = "2GB"

    # Telecommunications data processing optimizations (migrated from settings.yaml)
    telecom_optimizations: Dict[str, bool] = Field(default_factory=lambda: {
        'enable_parallel_import': True,
        'enable_batch_validation': True,
        'enable_memory_monitoring': True
    })

    @field_validator('memory_limit')
    @classmethod
    def validate_memory_limit(cls, v):
        if not v.endswith(('MB', 'GB')):
            raise ValueError('memory_limit must end with "MB" or "GB"')
        return v


class EnvironmentConfig(BaseModel):
    """Environment configuration."""
    use_virtual_env: bool = True
    python_version: str = "3.12+"
    auto_setup: bool = True


class ConnectConfig(BaseSettings):
    """Main configuration class for the Connect project.

    This class combines all configuration sections and provides environment variable
    support through Pydantic's BaseSettings.
    """

    # Environment and basic settings
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = Field(default=False, env="DEBUG")
    testing: bool = Field(default=False, env="TESTING")

    # Configuration sections
    project: ProjectConfig = Field(default_factory=ProjectConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    geo: GeoConfig = Field(default_factory=GeoConfig)
    qgis: QGISConfig = Field(default_factory=QGISConfig)
    data: DataConfig = Field(default_factory=DataConfig)
    telecom: TelecomConfig = Field(default_factory=TelecomConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    processing: ProcessingConfig = Field(default_factory=ProcessingConfig)
    env_config: EnvironmentConfig = Field(default_factory=EnvironmentConfig)

    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        env_nested_delimiter="__",  # Allows CONNECT__DATABASE__HOST format
        extra="ignore"  # Ignore extra fields for backward compatibility
    )
    @field_validator('environment', mode='before')
    @classmethod
    def parse_environment(cls, v):
        """Parse environment from string."""
        if isinstance(v, str):
            return Environment(v.lower())
        return v

    @model_validator(mode='after')
    def validate_config(self):
        """Validate the entire configuration."""
        # Ensure required fields are set in production
        if self.environment == Environment.PRODUCTION:
            if not self.database.password:
                raise ValueError('Database password is required in production')
            if not self.security.jwt.secret_key:
                raise ValueError('JWT secret key is required in production')

        return self

    def create_directories(self) -> None:
        """Create directories specified in configuration."""
        directories = [
            self.data.input_dir,
            self.data.output_dir,
            self.data.temp_dir,
            Path(self.logging.file).parent,
        ]

        for dir_path in directories:
            if dir_path:
                try:
                    Path(dir_path).mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    # Use print instead of logger to avoid circular dependency
                    print(f"Warning: Failed to create directory {dir_path}: {e}")

    def get_database_url(self) -> str:
        """Get database connection URL."""
        password_part = f":{self.database.password}" if self.database.password else ""
        return (
            f"postgresql://{self.database.user}{password_part}@"
            f"{self.database.host}:{self.database.port}/{self.database.name}"
        )
