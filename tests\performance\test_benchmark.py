# -*- coding: utf-8 -*-
"""
Benchmark tests for database operations and data processing.

This module contains benchmark tests to measure and compare performance
of different implementations and configurations.
"""

import time
import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
import threading
import psutil
import gc
from typing import Dict, List, Any, Callable
from dataclasses import dataclass

# Import modules to test
try:
    from src.database.operations.crud import CRUDOperations
    from src.database.connection.pool import ConnectionPool
    from src.database.etl.pipeline import ETLPipeline
    from src.database.query_builder.builder import QueryBuilder
    from src.importers.csv_importer import CSVImporter
    from src.exporters.csv_exporter import CSVExporter
    from src.database.monitoring.performance import PerformanceMonitor
except ImportError:
    # Mock imports for testing
    CRUDOperations = Mock
    ConnectionPool = Mock
    ETLPipeline = Mock
    QueryBuilder = Mock
    CSVImporter = Mock
    CSVExporter = Mock
    PerformanceMonitor = Mock


@dataclass
class BenchmarkResult:
    """Benchmark result data structure."""
    operation: str
    duration: float
    throughput: float
    memory_usage: float
    cpu_usage: float
    metadata: Dict[str, Any]


class BenchmarkRunner:
    """Utility class for running benchmarks."""
    
    def __init__(self):
        self.results: List[BenchmarkResult] = []
        self.process = psutil.Process()
    
    def run_benchmark(self, func: Callable, *args, **kwargs) -> BenchmarkResult:
        """Run a single benchmark and collect metrics."""
        # Collect initial metrics
        gc.collect()  # Force garbage collection
        initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        initial_cpu = self.process.cpu_percent()
        
        # Run the benchmark
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        
        # Collect final metrics
        final_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        final_cpu = self.process.cpu_percent()
        
        duration = end_time - start_time
        memory_usage = final_memory - initial_memory
        cpu_usage = final_cpu - initial_cpu
        
        # Calculate throughput (operations per second)
        throughput = 1 / duration if duration > 0 else 0
        
        benchmark_result = BenchmarkResult(
            operation=func.__name__,
            duration=duration,
            throughput=throughput,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            metadata={'result': result}
        )
        
        self.results.append(benchmark_result)
        return benchmark_result
    
    def compare_benchmarks(self, benchmarks: List[Callable], *args, **kwargs) -> Dict[str, BenchmarkResult]:
        """Compare multiple benchmark functions."""
        results = {}
        for benchmark in benchmarks:
            result = self.run_benchmark(benchmark, *args, **kwargs)
            results[benchmark.__name__] = result
        return results


@pytest.fixture
def benchmark_runner():
    """Fixture for benchmark runner."""
    return BenchmarkRunner()


@pytest.fixture
def mock_database_engine():
    """Mock database engine for testing."""
    engine = Mock()
    engine.execute.return_value = Mock()
    return engine


@pytest.fixture
def mock_connection():
    """Mock database connection for testing."""
    connection = Mock()
    connection.execute.return_value = Mock()
    connection.commit.return_value = None
    connection.rollback.return_value = None
    return connection


@pytest.fixture
def sample_dataframe():
    """Generate sample DataFrame for testing."""
    np.random.seed(42)
    return pd.DataFrame({
        'id': range(1000),
        'name': [f'user_{i}' for i in range(1000)],
        'value': np.random.randn(1000),
        'category': np.random.choice(['A', 'B', 'C'], 1000),
        'timestamp': pd.date_range('2023-01-01', periods=1000, freq='H')
    })


class TestQueryBenchmarks:
    """Benchmark tests for query operations."""
    
    def test_simple_select_benchmark(self, benchmark_runner, mock_connection):
        """Benchmark simple SELECT queries."""
        def simple_select():
            query = "SELECT * FROM users WHERE id = 1"
            mock_connection.execute(query)
            return "executed"
        
        result = benchmark_runner.run_benchmark(simple_select)
        
        assert result.duration > 0
        assert result.operation == "simple_select"
        assert mock_connection.execute.called
    
    def test_complex_join_benchmark(self, benchmark_runner, mock_connection):
        """Benchmark complex JOIN queries."""
        def complex_join():
            query = """
            SELECT u.id, u.name, p.title, c.name as category
            FROM users u
            JOIN posts p ON u.id = p.user_id
            JOIN categories c ON p.category_id = c.id
            WHERE u.created_at > '2023-01-01'
            ORDER BY u.id, p.created_at
            """
            mock_connection.execute(query)
            return "executed"
        
        result = benchmark_runner.run_benchmark(complex_join)
        
        assert result.duration > 0
        assert result.operation == "complex_join"
        assert mock_connection.execute.called
    
    def test_aggregation_benchmark(self, benchmark_runner, mock_connection):
        """Benchmark aggregation queries."""
        def aggregation_query():
            query = """
            SELECT category, COUNT(*) as count, AVG(value) as avg_value
            FROM data_table
            GROUP BY category
            HAVING COUNT(*) > 10
            ORDER BY avg_value DESC
            """
            mock_connection.execute(query)
            return "executed"
        
        result = benchmark_runner.run_benchmark(aggregation_query)
        
        assert result.duration > 0
        assert result.operation == "aggregation_query"
        assert mock_connection.execute.called


class TestCRUDBenchmarks:
    """Benchmark tests for CRUD operations."""
    
    @pytest.fixture
    def crud_operations(self, mock_connection):
        """Mock CRUD operations."""
        crud = Mock(spec=CRUDOperations)
        crud.connection = mock_connection
        return crud
    
    def test_insert_benchmark(self, benchmark_runner, crud_operations):
        """Benchmark INSERT operations."""
        def single_insert():
            data = {'name': 'test_user', 'email': '<EMAIL>'}
            crud_operations.insert('users', data)
            return "inserted"
        
        result = benchmark_runner.run_benchmark(single_insert)
        
        assert result.duration > 0
        assert result.operation == "single_insert"
        assert crud_operations.insert.called
    
    def test_bulk_insert_benchmark(self, benchmark_runner, crud_operations, sample_dataframe):
        """Benchmark bulk INSERT operations."""
        def bulk_insert():
            data = sample_dataframe.to_dict('records')
            crud_operations.bulk_insert('test_table', data)
            return len(data)
        
        result = benchmark_runner.run_benchmark(bulk_insert)
        
        assert result.duration > 0
        assert result.operation == "bulk_insert"
        assert crud_operations.bulk_insert.called
    
    def test_update_benchmark(self, benchmark_runner, crud_operations):
        """Benchmark UPDATE operations."""
        def single_update():
            data = {'name': 'updated_user'}
            crud_operations.update('users', data, {'id': 1})
            return "updated"
        
        result = benchmark_runner.run_benchmark(single_update)
        
        assert result.duration > 0
        assert result.operation == "single_update"
        assert crud_operations.update.called
    
    def test_delete_benchmark(self, benchmark_runner, crud_operations):
        """Benchmark DELETE operations."""
        def single_delete():
            crud_operations.delete('users', {'id': 1})
            return "deleted"
        
        result = benchmark_runner.run_benchmark(single_delete)
        
        assert result.duration > 0
        assert result.operation == "single_delete"
        assert crud_operations.delete.called


class TestETLBenchmarks:
    """Benchmark tests for ETL operations."""
    
    @pytest.fixture
    def etl_processor(self):
        """Mock ETL processor."""
        processor = Mock(spec=ETLProcessor)
        return processor
    
    def test_data_extraction_benchmark(self, benchmark_runner, etl_processor, sample_dataframe):
        """Benchmark data extraction."""
        etl_processor.extract.return_value = sample_dataframe
        
        def extract_data():
            return etl_processor.extract('source_table')
        
        result = benchmark_runner.run_benchmark(extract_data)
        
        assert result.duration > 0
        assert result.operation == "extract_data"
        assert etl_processor.extract.called
    
    def test_data_transformation_benchmark(self, benchmark_runner, etl_processor, sample_dataframe):
        """Benchmark data transformation."""
        etl_processor.transform.return_value = sample_dataframe
        
        def transform_data():
            return etl_processor.transform(sample_dataframe)
        
        result = benchmark_runner.run_benchmark(transform_data)
        
        assert result.duration > 0
        assert result.operation == "transform_data"
        assert etl_processor.transform.called
    
    def test_data_loading_benchmark(self, benchmark_runner, etl_processor, sample_dataframe):
        """Benchmark data loading."""
        etl_processor.load.return_value = True
        
        def load_data():
            return etl_processor.load(sample_dataframe, 'target_table')
        
        result = benchmark_runner.run_benchmark(load_data)
        
        assert result.duration > 0
        assert result.operation == "load_data"
        assert etl_processor.load.called


class TestDataProcessingBenchmarks:
    """Benchmark tests for data processing operations."""
    
    def test_pandas_operations_benchmark(self, benchmark_runner, sample_dataframe):
        """Benchmark pandas operations."""
        def pandas_groupby():
            return sample_dataframe.groupby('category')['value'].agg(['mean', 'std', 'count'])
        
        def pandas_merge():
            df2 = sample_dataframe.copy()
            df2['new_col'] = range(len(df2))
            return pd.merge(sample_dataframe, df2, on='id')
        
        def pandas_pivot():
            return sample_dataframe.pivot_table(
                values='value', 
                index='category', 
                columns=sample_dataframe['timestamp'].dt.hour,
                aggfunc='mean'
            )
        
        # Compare different pandas operations
        operations = [pandas_groupby, pandas_merge, pandas_pivot]
        results = benchmark_runner.compare_benchmarks(operations)
        
        assert len(results) == 3
        for op_name, result in results.items():
            assert result.duration > 0
            assert result.operation == op_name
    
    def test_numpy_operations_benchmark(self, benchmark_runner):
        """Benchmark numpy operations."""
        data = np.random.randn(100000)
        
        def numpy_mean():
            return np.mean(data)
        
        def numpy_std():
            return np.std(data)
        
        def numpy_sort():
            return np.sort(data)
        
        def numpy_fft():
            return np.fft.fft(data)
        
        # Compare different numpy operations
        operations = [numpy_mean, numpy_std, numpy_sort, numpy_fft]
        results = benchmark_runner.compare_benchmarks(operations)
        
        assert len(results) == 4
        for op_name, result in results.items():
            assert result.duration > 0
            assert result.operation == op_name


class TestImportExportBenchmarks:
    """Benchmark tests for import/export operations."""
    
    @pytest.fixture
    def csv_importer(self):
        """Mock CSV importer."""
        importer = Mock(spec=CSVImporter)
        return importer
    
    @pytest.fixture
    def csv_exporter(self):
        """Mock CSV exporter."""
        exporter = Mock(spec=CSVExporter)
        return exporter
    
    def test_csv_import_benchmark(self, benchmark_runner, csv_importer, sample_dataframe):
        """Benchmark CSV import operations."""
        csv_importer.import_data.return_value = sample_dataframe
        
        def import_csv():
            return csv_importer.import_data('test_file.csv')
        
        result = benchmark_runner.run_benchmark(import_csv)
        
        assert result.duration > 0
        assert result.operation == "import_csv"
        assert csv_importer.import_data.called
    
    def test_csv_export_benchmark(self, benchmark_runner, csv_exporter, sample_dataframe):
        """Benchmark CSV export operations."""
        csv_exporter.export_data.return_value = True
        
        def export_csv():
            return csv_exporter.export_data(sample_dataframe, 'output_file.csv')
        
        result = benchmark_runner.run_benchmark(export_csv)
        
        assert result.duration > 0
        assert result.operation == "export_csv"
        assert csv_exporter.export_data.called


class TestConcurrencyBenchmarks:
    """Benchmark tests for concurrent operations."""
    
    def test_threading_benchmark(self, benchmark_runner, mock_connection):
        """Benchmark threading performance."""
        def threaded_operations():
            def worker(thread_id):
                for i in range(10):
                    query = f"SELECT * FROM table WHERE id = {thread_id * 10 + i}"
                    mock_connection.execute(query)
                    time.sleep(0.001)  # Simulate work
                return thread_id
            
            with ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(worker, i) for i in range(5)]
                results = [future.result() for future in as_completed(futures)]
            
            return len(results)
        
        result = benchmark_runner.run_benchmark(threaded_operations)
        
        assert result.duration > 0
        assert result.operation == "threaded_operations"
        assert mock_connection.execute.call_count >= 50
    
    def test_async_simulation_benchmark(self, benchmark_runner):
        """Benchmark async-like operations using threading."""
        def async_simulation():
            results = []
            
            def async_task(task_id):
                # Simulate async work
                time.sleep(0.01)
                return f"task_{task_id}_completed"
            
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(async_task, i) for i in range(20)]
                for future in as_completed(futures):
                    results.append(future.result())
            
            return len(results)
        
        result = benchmark_runner.run_benchmark(async_simulation)
        
        assert result.duration > 0
        assert result.operation == "async_simulation"
        assert result.metadata['result'] == 20


class TestMemoryBenchmarks:
    """Benchmark tests for memory usage."""
    
    def test_memory_intensive_operations(self, benchmark_runner):
        """Benchmark memory-intensive operations."""
        def create_large_dataframe():
            # Create a large DataFrame
            size = 100000
            df = pd.DataFrame({
                'col1': np.random.randn(size),
                'col2': np.random.randn(size),
                'col3': [f'string_{i}' for i in range(size)],
                'col4': np.random.choice(['A', 'B', 'C', 'D'], size)
            })
            
            # Perform some operations
            result = df.groupby('col4').agg({
                'col1': ['mean', 'std'],
                'col2': ['sum', 'count']
            })
            
            return len(result)
        
        result = benchmark_runner.run_benchmark(create_large_dataframe)
        
        assert result.duration > 0
        assert result.operation == "create_large_dataframe"
        # Memory usage should be positive for large operations
        assert result.memory_usage >= 0
    
    def test_memory_cleanup_benchmark(self, benchmark_runner):
        """Benchmark memory cleanup operations."""
        def memory_cleanup_test():
            # Create and delete large objects
            large_objects = []
            for i in range(10):
                obj = np.random.randn(10000)
                large_objects.append(obj)
            
            # Clear references
            large_objects.clear()
            
            # Force garbage collection
            gc.collect()
            
            return "cleaned"
        
        result = benchmark_runner.run_benchmark(memory_cleanup_test)
        
        assert result.duration > 0
        assert result.operation == "memory_cleanup_test"


class TestPerformanceComparison:
    """Performance comparison tests."""
    
    def test_algorithm_comparison(self, benchmark_runner):
        """Compare different algorithm implementations."""
        data = list(range(10000))
        
        def bubble_sort_partial(arr):
            """Partial bubble sort implementation for testing."""
            n = min(len(arr), 100)  # Only sort first 100 elements
            arr_copy = arr[:n].copy()
            for i in range(n):
                for j in range(0, n - i - 1):
                    if arr_copy[j] > arr_copy[j + 1]:
                        arr_copy[j], arr_copy[j + 1] = arr_copy[j + 1], arr_copy[j]
            return arr_copy
        
        def python_sort(arr):
            """Python built-in sort."""
            return sorted(arr[:100])  # Only sort first 100 elements
        
        def numpy_sort_partial(arr):
            """NumPy sort implementation."""
            return np.sort(arr[:100])
        
        # Compare sorting algorithms
        algorithms = [bubble_sort_partial, python_sort, numpy_sort_partial]
        results = benchmark_runner.compare_benchmarks(algorithms, data)
        
        assert len(results) == 3
        
        # Find the fastest algorithm
        fastest = min(results.items(), key=lambda x: x[1].duration)
        print(f"\nFastest algorithm: {fastest[0]} ({fastest[1].duration:.6f}s)")
        
        # Verify all algorithms produced results
        for op_name, result in results.items():
            assert result.duration > 0
            assert result.operation == op_name
    
    def test_data_structure_comparison(self, benchmark_runner):
        """Compare different data structure operations."""
        size = 10000
        
        def list_operations():
            """Test list operations."""
            data = list(range(size))
            # Perform some operations
            data.append(size)
            data.insert(0, -1)
            data.remove(-1)
            return len(data)
        
        def set_operations():
            """Test set operations."""
            data = set(range(size))
            # Perform some operations
            data.add(size)
            data.add(-1)
            data.remove(-1)
            return len(data)
        
        def dict_operations():
            """Test dictionary operations."""
            data = {i: f"value_{i}" for i in range(size)}
            # Perform some operations
            data[size] = f"value_{size}"
            data[-1] = "value_-1"
            del data[-1]
            return len(data)
        
        # Compare data structures
        structures = [list_operations, set_operations, dict_operations]
        results = benchmark_runner.compare_benchmarks(structures)
        
        assert len(results) == 3
        
        # Print performance comparison
        print("\nData Structure Performance Comparison:")
        for op_name, result in sorted(results.items(), key=lambda x: x[1].duration):
            print(f"{op_name}: {result.duration:.6f}s (throughput: {result.throughput:.2f} ops/s)")
        
        # Verify all operations completed
        for op_name, result in results.items():
            assert result.duration > 0
            assert result.operation == op_name


if __name__ == "__main__":
    # Run benchmarks if executed directly
    pytest.main([__file__, "-v", "--tb=short"])