"""Unit tests for monitoring and logging components."""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, Mock, call, patch

import pytest

from src.config.models import DatabaseConfig
from src.database.exceptions import MonitoringError
from src.database.monitoring.alerts import <PERSON><PERSON>Manager, AlertRule, AlertSeverity
from src.database.monitoring.health import HealthMonitor, SystemHealthChecker

# Import components to test
from src.database.monitoring.logger import (
    DatabaseLogger,
    PerformanceLogger,
    QueryLogger,
)
from src.database.monitoring.metrics import MetricsCollector, PerformanceMetrics


class TestDatabaseLogger:
    """Test cases for DatabaseLogger class."""

    @pytest.fixture
    def mock_logger(self):
        """Create mock logger instance."""
        return Mock(spec=logging.Logger)

    @pytest.fixture
    def db_logger(self, mock_logger):
        """Create DatabaseLogger instance."""
        with patch("logging.getLogger", return_value=mock_logger):
            return DatabaseLogger(name="test_db_logger")

    def test_logger_initialization(self, db_logger, mock_logger):
        """Test logger initialization."""
        assert db_logger.logger == mock_logger
        assert db_logger.name == "test_db_logger"

    def test_info_logging(self, db_logger, mock_logger):
        """Test info level logging."""
        db_logger.info("Test info message", extra={"user_id": 123})

        mock_logger.info.assert_called_once()
        call_args = mock_logger.info.call_args
        assert "Test info message" in str(call_args)

    def test_error_logging(self, db_logger, mock_logger):
        """Test error level logging."""
        error = Exception("Test error")
        db_logger.error(
            "Database error occurred",
            error=error,
            extra={"query": "SELECT * FROM users"},
        )

        mock_logger.error.assert_called_once()
        call_args = mock_logger.error.call_args
        assert "Database error occurred" in str(call_args)

    def test_warning_logging(self, db_logger, mock_logger):
        """Test warning level logging."""
        db_logger.warning(
            "Slow query detected",
            extra={"duration": 5.2, "query": "SELECT * FROM large_table"},
        )

        mock_logger.warning.assert_called_once()

    def test_debug_logging(self, db_logger, mock_logger):
        """Test debug level logging."""
        db_logger.debug("Query execution details", extra={"params": [1, 2, 3]})

        mock_logger.debug.assert_called_once()

    def test_structured_logging(self, db_logger, mock_logger):
        """Test structured logging with metadata."""
        metadata = {
            "user_id": 123,
            "session_id": "abc123",
            "query_type": "SELECT",
            "table": "users",
            "duration": 0.5,
        }

        db_logger.log_structured("info", "Query executed successfully", metadata)

        mock_logger.info.assert_called_once()

    def test_context_manager(self, db_logger, mock_logger):
        """Test logger as context manager for operation tracking."""
        with db_logger.operation_context("user_creation", user_id=123):
            db_logger.info("Creating user")
            db_logger.info("User created successfully")

        # Should log operation start and end
        assert mock_logger.info.call_count >= 2

    def test_log_level_filtering(self, mock_logger):
        """Test log level filtering."""
        mock_logger.isEnabledFor.return_value = False

        with patch("logging.getLogger", return_value=mock_logger):
            db_logger = DatabaseLogger(name="test_logger")
            db_logger.debug("This should not be logged")

        # Debug message should not be logged if level is higher
        mock_logger.debug.assert_not_called()


class TestQueryLogger:
    """Test cases for QueryLogger class."""

    @pytest.fixture
    def query_logger(self):
        """Create QueryLogger instance."""
        return QueryLogger()

    def test_query_logging_basic(self, query_logger):
        """Test basic query logging."""
        with patch.object(query_logger, "logger") as mock_logger:
            query_logger.log_query(
                query="SELECT * FROM users WHERE id = $1", params=[123], duration=0.5
            )

            mock_logger.info.assert_called_once()

    def test_slow_query_detection(self, query_logger):
        """Test slow query detection and logging."""
        with patch.object(query_logger, "logger") as mock_logger:
            # Configure slow query threshold
            query_logger.slow_query_threshold = 1.0

            # Log a slow query
            query_logger.log_query(
                query="SELECT * FROM large_table", params=[], duration=2.5
            )

            # Should log as warning for slow query
            mock_logger.warning.assert_called_once()

    def test_query_error_logging(self, query_logger):
        """Test query error logging."""
        with patch.object(query_logger, "logger") as mock_logger:
            error = Exception("Syntax error in query")

            query_logger.log_query_error(
                query="SELECT * FROM non_existent_table", params=[], error=error
            )

            mock_logger.error.assert_called_once()

    def test_query_statistics_tracking(self, query_logger):
        """Test query statistics tracking."""
        # Log multiple queries
        query_logger.log_query("SELECT * FROM users", [], 0.1)
        query_logger.log_query(
            "INSERT INTO users VALUES ($1, $2)", ["John", "<EMAIL>"], 0.2
        )
        query_logger.log_query("UPDATE users SET name = $1", ["Jane"], 0.15)

        stats = query_logger.get_statistics()

        assert stats["total_queries"] == 3
        assert stats["average_duration"] > 0
        assert "SELECT" in stats["query_types"]
        assert "INSERT" in stats["query_types"]
        assert "UPDATE" in stats["query_types"]

    def test_query_parameter_sanitization(self, query_logger):
        """Test sensitive parameter sanitization."""
        with patch.object(query_logger, "logger") as mock_logger:
            # Configure sensitive fields
            query_logger.sensitive_fields = ["password", "ssn", "credit_card"]

            query_logger.log_query(
                query="INSERT INTO users (name, password, email) VALUES ($1, $2, $3)",
                params=["John", "secret123", "<EMAIL>"],
                duration=0.1,
            )

            # Check that sensitive data is sanitized
            call_args = mock_logger.info.call_args
            logged_message = str(call_args)
            assert "secret123" not in logged_message
            assert "[REDACTED]" in logged_message or "***" in logged_message


class TestPerformanceLogger:
    """Test cases for PerformanceLogger class."""

    @pytest.fixture
    def perf_logger(self):
        """Create PerformanceLogger instance."""
        return PerformanceLogger()

    def test_operation_timing(self, perf_logger):
        """Test operation timing measurement."""
        with patch.object(perf_logger, "logger") as mock_logger:
            with perf_logger.time_operation("database_connection"):
                time.sleep(0.1)  # Simulate operation

            mock_logger.info.assert_called_once()
            call_args = mock_logger.info.call_args
            logged_message = str(call_args)
            assert "database_connection" in logged_message
            assert "duration" in logged_message.lower()

    def test_performance_metrics_collection(self, perf_logger):
        """Test performance metrics collection."""
        # Simulate multiple operations
        with perf_logger.time_operation("query_execution"):
            time.sleep(0.05)

        with perf_logger.time_operation("query_execution"):
            time.sleep(0.1)

        with perf_logger.time_operation("connection_pool"):
            time.sleep(0.02)

        metrics = perf_logger.get_performance_metrics()

        assert "query_execution" in metrics
        assert "connection_pool" in metrics
        assert metrics["query_execution"]["count"] == 2
        assert metrics["connection_pool"]["count"] == 1

    def test_memory_usage_tracking(self, perf_logger):
        """Test memory usage tracking."""
        with patch("psutil.Process") as mock_process:
            mock_memory_info = Mock()
            mock_memory_info.rss = 1024 * 1024 * 100  # 100MB
            mock_process.return_value.memory_info.return_value = mock_memory_info

            memory_usage = perf_logger.get_memory_usage()

            assert memory_usage > 0
            assert isinstance(memory_usage, (int, float))

    def test_cpu_usage_tracking(self, perf_logger):
        """Test CPU usage tracking."""
        with patch("psutil.cpu_percent", return_value=25.5):
            cpu_usage = perf_logger.get_cpu_usage()

            assert cpu_usage == 25.5

    def test_performance_alert_thresholds(self, perf_logger):
        """Test performance alert thresholds."""
        with patch.object(perf_logger, "logger") as mock_logger:
            # Configure thresholds
            perf_logger.slow_operation_threshold = 0.5

            # Simulate slow operation
            with perf_logger.time_operation("slow_query"):
                time.sleep(0.6)

            # Should log warning for slow operation
            mock_logger.warning.assert_called()


class TestMetricsCollector:
    """Test cases for MetricsCollector class."""

    @pytest.fixture
    def metrics_collector(self):
        """Create MetricsCollector instance."""
        return MetricsCollector()

    def test_counter_metrics(self, metrics_collector):
        """Test counter metrics collection."""
        metrics_collector.increment_counter("database_connections")
        metrics_collector.increment_counter("database_connections")
        metrics_collector.increment_counter("query_executions", value=5)

        assert metrics_collector.get_counter("database_connections") == 2
        assert metrics_collector.get_counter("query_executions") == 5

    def test_gauge_metrics(self, metrics_collector):
        """Test gauge metrics collection."""
        metrics_collector.set_gauge("active_connections", 10)
        metrics_collector.set_gauge("memory_usage_mb", 256.5)

        assert metrics_collector.get_gauge("active_connections") == 10
        assert metrics_collector.get_gauge("memory_usage_mb") == 256.5

    def test_histogram_metrics(self, metrics_collector):
        """Test histogram metrics collection."""
        # Record multiple query durations
        durations = [0.1, 0.2, 0.15, 0.3, 0.25, 0.18, 0.22]
        for duration in durations:
            metrics_collector.record_histogram("query_duration", duration)

        histogram = metrics_collector.get_histogram("query_duration")

        assert histogram["count"] == len(durations)
        assert histogram["sum"] == sum(durations)
        assert histogram["avg"] == sum(durations) / len(durations)
        assert "percentiles" in histogram

    def test_timer_metrics(self, metrics_collector):
        """Test timer metrics collection."""
        with metrics_collector.timer("operation_duration"):
            time.sleep(0.1)

        timer_stats = metrics_collector.get_timer_stats("operation_duration")

        assert timer_stats["count"] == 1
        assert timer_stats["total_time"] > 0.09  # Account for timing precision

    def test_metrics_export(self, metrics_collector):
        """Test metrics export functionality."""
        # Add some metrics
        metrics_collector.increment_counter("requests", 100)
        metrics_collector.set_gauge("cpu_usage", 75.5)
        metrics_collector.record_histogram("response_time", 0.5)

        exported_metrics = metrics_collector.export_metrics()

        assert "counters" in exported_metrics
        assert "gauges" in exported_metrics
        assert "histograms" in exported_metrics
        assert exported_metrics["counters"]["requests"] == 100
        assert exported_metrics["gauges"]["cpu_usage"] == 75.5

    def test_metrics_reset(self, metrics_collector):
        """Test metrics reset functionality."""
        metrics_collector.increment_counter("test_counter", 10)
        metrics_collector.set_gauge("test_gauge", 50)

        metrics_collector.reset_metrics()

        assert metrics_collector.get_counter("test_counter") == 0
        assert metrics_collector.get_gauge("test_gauge") == 0


class TestHealthMonitor:
    """Test cases for HealthMonitor class."""

    @pytest.fixture
    def mock_connection(self):
        """Create mock database connection."""
        connection = AsyncMock()
        connection.fetchval.return_value = 1
        return connection

    @pytest.fixture
    def health_monitor(self, mock_connection):
        """Create HealthMonitor instance."""
        return HealthMonitor(mock_connection)

    @pytest.mark.asyncio
    async def test_database_health_check(self, health_monitor, mock_connection):
        """Test database health check."""
        health_status = await health_monitor.check_database_health()

        assert health_status["status"] == "healthy"
        assert "response_time" in health_status
        assert health_status["timestamp"] is not None
        mock_connection.fetchval.assert_called_once_with("SELECT 1")

    @pytest.mark.asyncio
    async def test_database_health_check_failure(self, health_monitor, mock_connection):
        """Test database health check failure."""
        mock_connection.fetchval.side_effect = Exception("Connection lost")

        health_status = await health_monitor.check_database_health()

        assert health_status["status"] == "unhealthy"
        assert "error" in health_status
        assert health_status["error"] == "Connection lost"

    @pytest.mark.asyncio
    async def test_connection_pool_health(self, health_monitor):
        """Test connection pool health monitoring."""
        mock_pool = AsyncMock()
        mock_pool.get_size.return_value = 5
        mock_pool.get_idle_size.return_value = 3
        mock_pool.get_max_size.return_value = 10

        pool_health = await health_monitor.check_connection_pool_health(mock_pool)

        assert pool_health["status"] == "healthy"
        assert pool_health["active_connections"] == 5
        assert pool_health["idle_connections"] == 3
        assert pool_health["max_connections"] == 10
        assert pool_health["utilization"] == 0.5  # 5/10

    @pytest.mark.asyncio
    async def test_system_resource_monitoring(self, health_monitor):
        """Test system resource monitoring."""
        with patch("psutil.cpu_percent", return_value=45.2), patch(
            "psutil.virtual_memory"
        ) as mock_memory, patch("psutil.disk_usage") as mock_disk:
            mock_memory.return_value.percent = 60.5
            mock_disk.return_value.percent = 75.0

            system_health = await health_monitor.check_system_health()

            assert system_health["cpu_usage"] == 45.2
            assert system_health["memory_usage"] == 60.5
            assert system_health["disk_usage"] == 75.0
            assert system_health["status"] == "healthy"

    @pytest.mark.asyncio
    async def test_comprehensive_health_check(self, health_monitor, mock_connection):
        """Test comprehensive health check."""
        with patch.object(health_monitor, "check_system_health") as mock_system_health:
            mock_system_health.return_value = {
                "status": "healthy",
                "cpu_usage": 30.0,
                "memory_usage": 50.0,
                "disk_usage": 40.0,
            }

            comprehensive_health = await health_monitor.get_comprehensive_health()

            assert "database" in comprehensive_health
            assert "system" in comprehensive_health
            assert comprehensive_health["overall_status"] == "healthy"
            assert "timestamp" in comprehensive_health

    def test_health_status_history(self, health_monitor):
        """Test health status history tracking."""
        # Add some health check results
        health_monitor.add_health_record(
            {"timestamp": datetime.now(), "status": "healthy", "response_time": 0.1}
        )

        health_monitor.add_health_record(
            {"timestamp": datetime.now(), "status": "degraded", "response_time": 2.5}
        )

        history = health_monitor.get_health_history(limit=10)

        assert len(history) == 2
        assert history[0]["status"] in ["healthy", "degraded"]
        assert history[1]["status"] in ["healthy", "degraded"]


class TestAlertManager:
    """Test cases for AlertManager class."""

    @pytest.fixture
    def alert_manager(self):
        """Create AlertManager instance."""
        return AlertManager()

    @pytest.fixture
    def sample_alert_rules(self):
        """Create sample alert rules."""
        return [
            AlertRule(
                name="high_cpu_usage",
                condition="cpu_usage > 80",
                severity=AlertSeverity.WARNING,
                message="High CPU usage detected: {cpu_usage}%",
            ),
            AlertRule(
                name="database_connection_failure",
                condition="database_status == 'unhealthy'",
                severity=AlertSeverity.CRITICAL,
                message="Database connection failed",
            ),
            AlertRule(
                name="slow_query_detected",
                condition="query_duration > 5.0",
                severity=AlertSeverity.WARNING,
                message="Slow query detected: {query_duration}s",
            ),
        ]

    def test_alert_rule_creation(self, sample_alert_rules):
        """Test alert rule creation."""
        rule = sample_alert_rules[0]

        assert rule.name == "high_cpu_usage"
        assert rule.condition == "cpu_usage > 80"
        assert rule.severity == AlertSeverity.WARNING
        assert "High CPU usage" in rule.message

    def test_add_alert_rules(self, alert_manager, sample_alert_rules):
        """Test adding alert rules to manager."""
        for rule in sample_alert_rules:
            alert_manager.add_rule(rule)

        assert len(alert_manager.rules) == 3
        assert "high_cpu_usage" in [rule.name for rule in alert_manager.rules]

    def test_evaluate_alert_conditions(self, alert_manager, sample_alert_rules):
        """Test evaluating alert conditions."""
        alert_manager.add_rule(sample_alert_rules[0])  # CPU usage rule

        # Test condition that should trigger alert
        metrics = {"cpu_usage": 85.5}
        triggered_alerts = alert_manager.evaluate_conditions(metrics)

        assert len(triggered_alerts) == 1
        assert triggered_alerts[0].name == "high_cpu_usage"
        assert triggered_alerts[0].severity == AlertSeverity.WARNING

    def test_alert_condition_not_triggered(self, alert_manager, sample_alert_rules):
        """Test alert condition that should not trigger."""
        alert_manager.add_rule(sample_alert_rules[0])  # CPU usage rule

        # Test condition that should NOT trigger alert
        metrics = {"cpu_usage": 45.0}
        triggered_alerts = alert_manager.evaluate_conditions(metrics)

        assert len(triggered_alerts) == 0

    def test_multiple_alert_evaluation(self, alert_manager, sample_alert_rules):
        """Test evaluating multiple alert conditions."""
        for rule in sample_alert_rules:
            alert_manager.add_rule(rule)

        # Metrics that should trigger multiple alerts
        metrics = {
            "cpu_usage": 90.0,
            "database_status": "unhealthy",
            "query_duration": 7.5,
        }

        triggered_alerts = alert_manager.evaluate_conditions(metrics)

        assert len(triggered_alerts) == 3
        alert_names = [alert.name for alert in triggered_alerts]
        assert "high_cpu_usage" in alert_names
        assert "database_connection_failure" in alert_names
        assert "slow_query_detected" in alert_names

    def test_alert_message_formatting(self, alert_manager, sample_alert_rules):
        """Test alert message formatting with variables."""
        alert_manager.add_rule(sample_alert_rules[0])  # CPU usage rule

        metrics = {"cpu_usage": 85.5}
        triggered_alerts = alert_manager.evaluate_conditions(metrics)

        formatted_message = triggered_alerts[0].format_message(metrics)
        assert "85.5%" in formatted_message

    def test_alert_severity_levels(self, sample_alert_rules):
        """Test different alert severity levels."""
        warning_rule = sample_alert_rules[0]
        critical_rule = sample_alert_rules[1]

        assert warning_rule.severity == AlertSeverity.WARNING
        assert critical_rule.severity == AlertSeverity.CRITICAL
        assert critical_rule.severity.value > warning_rule.severity.value

    def test_alert_suppression(self, alert_manager, sample_alert_rules):
        """Test alert suppression to prevent spam."""
        rule = sample_alert_rules[0]
        rule.suppression_duration = timedelta(minutes=5)
        alert_manager.add_rule(rule)

        metrics = {"cpu_usage": 85.0}

        # First evaluation should trigger alert
        alerts1 = alert_manager.evaluate_conditions(metrics)
        assert len(alerts1) == 1

        # Second evaluation within suppression period should not trigger
        alerts2 = alert_manager.evaluate_conditions(metrics)
        assert len(alerts2) == 0

    @pytest.mark.asyncio
    async def test_alert_notification_sending(self, alert_manager):
        """Test sending alert notifications."""
        with patch.object(alert_manager, "send_notification") as mock_send:
            alert = Mock()
            alert.name = "test_alert"
            alert.severity = AlertSeverity.WARNING
            alert.message = "Test alert message"

            await alert_manager.send_alert_notification(alert)

            mock_send.assert_called_once_with(alert)

    def test_alert_history_tracking(self, alert_manager, sample_alert_rules):
        """Test tracking alert history."""
        alert_manager.add_rule(sample_alert_rules[0])

        metrics = {"cpu_usage": 85.0}
        triggered_alerts = alert_manager.evaluate_conditions(metrics)

        # Add to history
        for alert in triggered_alerts:
            alert_manager.add_to_history(alert)

        history = alert_manager.get_alert_history(limit=10)

        assert len(history) == 1
        assert history[0]["name"] == "high_cpu_usage"
        assert "timestamp" in history[0]

    def test_alert_rule_removal(self, alert_manager, sample_alert_rules):
        """Test removing alert rules."""
        for rule in sample_alert_rules:
            alert_manager.add_rule(rule)

        assert len(alert_manager.rules) == 3

        alert_manager.remove_rule("high_cpu_usage")

        assert len(alert_manager.rules) == 2
        rule_names = [rule.name for rule in alert_manager.rules]
        assert "high_cpu_usage" not in rule_names
