"""统一验证框架

高性能、可扩展的电信数据验证框架，专为Connect系统设计。

主要特性:
- 🚀 高性能: 500万行数据处理 < 10秒
- 🔧 可扩展: 模块化设计，支持自定义验证规则
- 📊 专业化: 深度集成电信业务逻辑
- 🛡️ 企业级: 完整的监控、告警和报告功能
- 🎯 易用性: 简洁的API，丰富的便捷函数

快速开始:
    >>> from validation import validate_cdr_data, ValidationFactory
    >>> 
    >>> # 快速验证CDR数据
    >>> result = validate_cdr_data(cdr_dataframe)
    >>> print(f"验证结果: {'通过' if result.is_valid else '失败'}")
    >>> 
    >>> # 使用工厂模式进行高级验证
    >>> factory = ValidationFactory()
    >>> framework = factory.create_framework('cdr')
    >>> result = framework.validate(data)
"""

# 版本信息
__version__ = '1.0.0'
__author__ = 'Connect Team'
__email__ = '<EMAIL>'
__description__ = '统一验证框架 - 高性能电信数据验证解决方案'

# 核心组件导入
from .core import (
    ValidationFramework,
    ValidationRule,
    ValidationResult,
    ValidationIssue,
    ValidationContext,
    ValidationType,
    ValidationSeverity
)

# 验证器导入
from .validators import (
    DataStructureValidator,
    DataValueValidator,
    TelecomDataValidator,
    DatabaseValidator,
    FileValidator
)

# 验证规则导入
from .rules import (
    CDRValidationRules,
    KPIValidationRules,
    CFGValidationRules,
    SCOREValidationRules,
    DatabaseValidationRules,
    ValidationRuleFactory
)

# 工厂类导入
from .factory import (
    ValidationFactory,
    get_validation_factory,
    validate_data,
    validate_file,
    validate_cdr_data,
    validate_kpi_data,
    validate_cfg_data
)

# 异常类导入
from .exceptions import (
    ValidationError,
    ValidationConfigError,
    ValidationRuleError,
    DataValidationError,
    SchemaValidationError,
    FileValidationError,
    TelecomValidationError,
    ValidationTimeoutError,
    ValidationDependencyError
)

# 配置管理导入
from .config import (
    ValidationConfig,
    ConfigManager,
    ValidationMode,
    LogLevel,
    get_config,
    load_config,
    save_config
)

# 监控和指标导入
from .monitoring import (
    ValidationMonitor,
    ValidationMetrics,
    AlertManager,
    PerformanceTimer,
    get_monitor,
    monitor_validation,
    get_performance_metrics,
    get_health_status
)

# 工具函数导入
from .utils import (
    DataConverter,
    BatchProcessor,
    ReportGenerator,
    FileUtils,
    DataSampler,
    normalize_dataframe,
    generate_sample_data
)

# 公开的API
__all__ = [
    # 版本信息
    '__version__',
    '__author__',
    '__email__',
    '__description__',
    
    # 核心组件
    'ValidationFramework',
    'ValidationRule',
    'ValidationResult',
    'ValidationIssue',
    'ValidationContext',
    'ValidationType',
    'ValidationSeverity',
    
    # 验证器
    'DataStructureValidator',
    'DataValueValidator',
    'TelecomDataValidator',
    'DatabaseValidator',
    'FileValidator',
    
    # 验证规则
    'CDRValidationRules',
    'KPIValidationRules',
    'CFGValidationRules',
    'SCOREValidationRules',
    'DatabaseValidationRules',
    'ValidationRuleFactory',
    
    # 工厂类和便捷函数
    'ValidationFactory',
    'get_validation_factory',
    'validate_data',
    'validate_file',
    'validate_cdr_data',
    'validate_kpi_data',
    'validate_cfg_data',
    
    # 异常类
    'ValidationError',
    'ValidationConfigError',
    'ValidationRuleError',
    'DataValidationError',
    'SchemaValidationError',
    'FileValidationError',
    'TelecomValidationError',
    'ValidationTimeoutError',
    'ValidationDependencyError',
    
    # 配置管理
    'ValidationConfig',
    'ConfigManager',
    'ValidationMode',
    'LogLevel',
    'get_config',
    'load_config',
    'save_config',
    
    # 监控和指标
    'ValidationMonitor',
    'ValidationMetrics',
    'AlertManager',
    'PerformanceTimer',
    'get_monitor',
    'monitor_validation',
    'get_performance_metrics',
    'get_health_status',
    
    # 工具函数
    'DataConverter',
    'BatchProcessor',
    'ReportGenerator',
    'FileUtils',
    'DataSampler',
    'normalize_dataframe',
    'generate_sample_data'
]


# 模块级别的便捷配置
def configure(config_dict=None, config_file=None, **kwargs):
    """配置验证框架
    
    Args:
        config_dict: 配置字典
        config_file: 配置文件路径
        **kwargs: 其他配置参数
    
    Examples:
        >>> import validation
        >>> 
        >>> # 使用字典配置
        >>> validation.configure({
        ...     'validation_mode': 'strict',
        ...     'parallel_enabled': True,
        ...     'max_workers': 8
        ... })
        >>> 
        >>> # 使用文件配置
        >>> validation.configure(config_file='config.json')
        >>> 
        >>> # 使用关键字参数配置
        >>> validation.configure(
        ...     validation_mode='strict',
        ...     parallel_enabled=True,
        ...     monitoring_enabled=True
        ... )
    """
    from .config import ConfigManager
    from .monitoring import configure_monitoring
    
    config_manager = ConfigManager()
    
    if config_file:
        config_manager.load_from_file(config_file)
    elif config_dict:
        config_manager.update_config(config_dict)
    
    if kwargs:
        config_manager.update_config(kwargs)
    
    # 配置监控
    monitoring_config = config_manager.get_config().monitoring
    if monitoring_config.enabled:
        configure_monitoring(monitoring_config.__dict__)


def get_version_info():
    """获取版本信息
    
    Returns:
        包含版本信息的字典
    """
    return {
        'version': __version__,
        'author': __author__,
        'email': __email__,
        'description': __description__
    }


def health_check():
    """健康检查
    
    Returns:
        健康状态信息
    """
    try:
        # 检查核心组件
        factory = ValidationFactory()
        
        # 检查监控组件
        monitor = get_monitor()
        health_status = monitor.get_health_status()
        
        # 检查配置
        config_manager = ConfigManager()
        config = config_manager.get_config()
        
        return {
            'status': 'healthy',
            'version': __version__,
            'components': {
                'validation_factory': 'ok',
                'monitoring': health_status['status'],
                'configuration': 'ok'
            },
            'configuration': {
                'validation_mode': config.validation.mode.value,
                'parallel_enabled': config.performance.parallel_enabled,
                'monitoring_enabled': config.monitoring.enabled
            },
            'monitoring': health_status
        }
    except Exception as e:
        return {
            'status': 'error',
            'version': __version__,
            'error': str(e)
        }


# 模块初始化时的设置
def _initialize():
    """模块初始化"""
    import logging
    
    # 设置默认日志格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建模块日志记录器
    logger = logging.getLogger(__name__)
    logger.info(f"统一验证框架 v{__version__} 已加载")
    
    # 检查依赖
    try:
        import pandas
        import numpy
        logger.debug(f"依赖检查通过: pandas={pandas.__version__}, numpy={numpy.__version__}")
    except ImportError as e:
        logger.error(f"依赖检查失败: {e}")
        raise


# 执行初始化
_initialize()