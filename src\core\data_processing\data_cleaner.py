# -*- coding: utf-8 -*-
"""
Data Cleaner for Unified Data Processing

This module provides centralized data cleaning and validation capabilities
with support for telecommunications-specific data patterns.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import asyncio
import logging
import re
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable, Set, Tuple

import pandas as pd
import numpy as np
from .types import (
    ProcessingEngine, ProcessingMode, ProcessingStatus, QualityLevel,
    ProcessingMetrics, ProcessingResult, ProcessingConfig
)
from .adapters import create_adapter, BaseAdapter
from ..utils.memory import MemoryMonitor
from ..utils.validation import ValidationRule, ValidationResult


class DataCleaner:
    """Centralized data cleaning and validation system.
    
    Features:
    - Comprehensive data quality assessment
    - Automated data cleaning pipelines
    - Telecommunications-specific validation rules
    - Memory-optimized processing
    - Detailed quality reporting
    - Configurable cleaning strategies
    """
    
    # Common data quality issues
    QUALITY_CHECKS = {
        'missing_values': 'Check for missing/null values',
        'duplicates': 'Check for duplicate records',
        'data_types': 'Validate data types',
        'value_ranges': 'Check value ranges and constraints',
        'format_patterns': 'Validate format patterns (phone, email, etc.)',
        'referential_integrity': 'Check referential integrity',
        'outliers': 'Detect statistical outliers',
        'consistency': 'Check data consistency across fields'
    }
    
    # Telecommunications-specific patterns
    TELECOM_PATTERNS = {
        'msisdn': r'^\+?[1-9]\d{1,14}$',  # E.164 format
        'imsi': r'^\d{15}$',  # 15 digits
        'imei': r'^\d{15}$',  # 15 digits
        'cell_id': r'^\d+$',  # Numeric
        'lac': r'^\d+$',  # Location Area Code
        'mcc': r'^\d{3}$',  # Mobile Country Code
        'mnc': r'^\d{2,3}$',  # Mobile Network Code
        'ip_address': r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$',
        'mac_address': r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
    }
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        """Initialize data cleaner.
        
        Args:
            config: Processing configuration
        """
        self.config = config or ProcessingConfig()
        self.logger = logging.getLogger(__name__)
        self.memory_monitor = MemoryMonitor()
        
        # Processing state
        self._current_adapter: Optional[BaseAdapter] = None
        self._processing_metrics = ProcessingMetrics()
        self._quality_report: Dict[str, Any] = {}
    
    async def assess_quality(
        self,
        data: Union[pd.DataFrame, Any],
        validation_rules: Optional[Dict[str, Any]] = None,
        engine: Optional[ProcessingEngine] = None
    ) -> Dict[str, Any]:
        """Assess data quality comprehensively.
        
        Args:
            data: Data to assess
            validation_rules: Custom validation rules
            engine: Processing engine to use
            
        Returns:
            Comprehensive quality assessment report
        """
        start_time = time.time()
        
        try:
            # Create adapter
            adapter = create_adapter(engine=engine, config=self.config)
            self._current_adapter = adapter
            
            # Convert to pandas for analysis
            if not isinstance(data, pd.DataFrame):
                if hasattr(data, 'to_pandas'):
                    data = data.to_pandas()
                else:
                    data = pd.DataFrame(data)
            
            # Initialize quality report
            quality_report = {
                'timestamp': datetime.now().isoformat(),
                'record_count': len(data),
                'column_count': len(data.columns),
                'data_size_mb': data.memory_usage(deep=True).sum() / (1024 * 1024),
                'quality_score': 0.0,
                'quality_level': QualityLevel.UNKNOWN,
                'issues': [],
                'recommendations': [],
                'detailed_checks': {}
            }
            
            # Perform quality checks
            await self._check_missing_values(data, quality_report)
            await self._check_duplicates(data, quality_report)
            await self._check_data_types(data, quality_report)
            await self._check_value_ranges(data, quality_report, validation_rules)
            await self._check_format_patterns(data, quality_report)
            await self._check_outliers(data, quality_report)
            await self._check_consistency(data, quality_report)
            
            # Calculate overall quality score
            quality_report['quality_score'] = await self._calculate_quality_score(quality_report)
            quality_report['quality_level'] = await self._determine_quality_level(quality_report['quality_score'])
            
            # Generate recommendations
            quality_report['recommendations'] = await self._generate_recommendations(quality_report)
            
            # Update metrics
            self._processing_metrics.records_processed = len(data)
            self._processing_metrics.processing_time = time.time() - start_time
            self._processing_metrics.status = ProcessingStatus.COMPLETED
            
            self._quality_report = quality_report
            
            self.logger.info(
                f"Quality assessment completed: {quality_report['quality_score']:.2f} "
                f"({quality_report['quality_level'].value})"
            )
            
            return quality_report
            
        except Exception as e:
            self.logger.error(f"Quality assessment failed: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'quality_score': 0.0,
                'quality_level': QualityLevel.POOR
            }
    
    async def clean_data(
        self,
        data: Union[pd.DataFrame, Any],
        cleaning_rules: Optional[Dict[str, Any]] = None,
        engine: Optional[ProcessingEngine] = None,
        mode: ProcessingMode = ProcessingMode.SYNC
    ) -> ProcessingResult:
        """Clean data using specified rules and strategies.
        
        Args:
            data: Data to clean
            cleaning_rules: Custom cleaning rules
            engine: Processing engine to use
            mode: Processing mode
            
        Returns:
            Processing result with cleaned data
        """
        start_time = time.time()
        
        try:
            # Create adapter
            adapter = create_adapter(engine=engine, config=self.config)
            self._current_adapter = adapter
            
            # Convert to pandas for cleaning
            if not isinstance(data, pd.DataFrame):
                if hasattr(data, 'to_pandas'):
                    data = data.to_pandas()
                else:
                    data = pd.DataFrame(data)
            
            original_count = len(data)
            cleaned_data = data.copy()
            
            # Apply cleaning operations
            cleaning_operations = [
                ('remove_duplicates', self._remove_duplicates),
                ('handle_missing_values', self._handle_missing_values),
                ('standardize_formats', self._standardize_formats),
                ('fix_data_types', self._fix_data_types),
                ('remove_outliers', self._remove_outliers),
                ('validate_telecom_fields', self._validate_telecom_fields)
            ]
            
            cleaning_report = {
                'operations': [],
                'records_removed': 0,
                'records_modified': 0,
                'issues_fixed': []
            }
            
            for operation_name, operation_func in cleaning_operations:
                if cleaning_rules and operation_name in cleaning_rules:
                    operation_config = cleaning_rules[operation_name]
                    if not operation_config.get('enabled', True):
                        continue
                else:
                    operation_config = {}
                
                self.logger.debug(f"Applying cleaning operation: {operation_name}")
                
                try:
                    operation_result = await operation_func(
                        cleaned_data, operation_config, mode
                    )
                    
                    if operation_result:
                        cleaned_data = operation_result['data']
                        cleaning_report['operations'].append({
                            'name': operation_name,
                            'records_affected': operation_result.get('records_affected', 0),
                            'issues_fixed': operation_result.get('issues_fixed', []),
                            'status': 'completed'
                        })
                        
                        cleaning_report['records_modified'] += operation_result.get('records_affected', 0)
                        cleaning_report['issues_fixed'].extend(operation_result.get('issues_fixed', []))
                
                except Exception as e:
                    self.logger.warning(f"Cleaning operation {operation_name} failed: {e}")
                    cleaning_report['operations'].append({
                        'name': operation_name,
                        'status': 'failed',
                        'error': str(e)
                    })
            
            # Calculate final metrics
            final_count = len(cleaned_data)
            cleaning_report['records_removed'] = original_count - final_count
            
            # Convert back to original format if needed
            if hasattr(adapter, 'from_pandas') and not isinstance(data, pd.DataFrame):
                result_data = adapter.from_pandas(cleaned_data)
            else:
                result_data = cleaned_data
            
            # Create result
            result = ProcessingResult(
                status=ProcessingStatus.COMPLETED,
                records_processed=original_count,
                processing_time=time.time() - start_time,
                data=result_data,
                metadata={
                    'cleaning_report': cleaning_report,
                    'original_count': original_count,
                    'final_count': final_count,
                    'data_quality_improvement': await self._calculate_improvement(data, cleaned_data)
                },
                metrics=self._processing_metrics
            )
            
            self.logger.info(
                f"Data cleaning completed: {original_count:,} -> {final_count:,} records "
                f"({cleaning_report['records_removed']:,} removed, {cleaning_report['records_modified']:,} modified)"
            )
            
            return result
            
        except Exception as e:
            result = ProcessingResult(
                status=ProcessingStatus.FAILED,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
            self.logger.error(f"Data cleaning failed: {e}")
            return result
    
    async def validate_telecom_data(
        self,
        data: Union[pd.DataFrame, Any],
        data_type: str,
        engine: Optional[ProcessingEngine] = None
    ) -> ValidationResult:
        """Validate telecommunications-specific data.
        
        Args:
            data: Data to validate
            data_type: Type of telecom data (cdr, kpi, ep, nlg)
            engine: Processing engine to use
            
        Returns:
            Validation result
        """
        start_time = time.time()
        
        try:
            # Create adapter
            adapter = create_adapter(engine=engine, config=self.config)
            
            # Convert to pandas for validation
            if not isinstance(data, pd.DataFrame):
                if hasattr(data, 'to_pandas'):
                    data = data.to_pandas()
                else:
                    data = pd.DataFrame(data)
            
            # Get validation rules for data type
            validation_rules = await self._get_telecom_validation_rules(data_type)
            
            # Perform validation
            validation_result = await adapter.validate_data(data, validation_rules)
            
            # Add telecom-specific checks
            telecom_checks = await self._perform_telecom_checks(data, data_type)
            validation_result.update(telecom_checks)
            
            return ValidationResult(
                is_valid=validation_result['is_valid'],
                errors=validation_result.get('errors', []),
                warnings=validation_result.get('warnings', []),
                metrics={
                    'validation_time': time.time() - start_time,
                    'records_validated': len(data),
                    'error_rate': len(validation_result.get('errors', [])) / len(data) if len(data) > 0 else 0
                }
            )
            
        except Exception as e:
            self.logger.error(f"Telecom data validation failed: {e}")
            return ValidationResult(
                is_valid=False,
                errors=[f"Validation error: {e}"],
                warnings=[],
                metrics={'validation_time': time.time() - start_time}
            )
    
    def get_quality_report(self) -> Dict[str, Any]:
        """Get the latest quality assessment report.
        
        Returns:
            Quality assessment report
        """
        return self._quality_report
    
    def get_processing_metrics(self) -> ProcessingMetrics:
        """Get current processing metrics.
        
        Returns:
            Processing metrics
        """
        return self._processing_metrics
    
    # Quality check methods
    async def _check_missing_values(self, data: pd.DataFrame, report: Dict[str, Any]):
        """Check for missing values."""
        missing_info = data.isnull().sum()
        missing_percentage = (missing_info / len(data)) * 100
        
        critical_missing = missing_percentage[missing_percentage > 50]
        moderate_missing = missing_percentage[(missing_percentage > 10) & (missing_percentage <= 50)]
        
        report['detailed_checks']['missing_values'] = {
            'total_missing': int(missing_info.sum()),
            'columns_with_missing': len(missing_info[missing_info > 0]),
            'critical_columns': list(critical_missing.index),
            'moderate_columns': list(moderate_missing.index),
            'missing_by_column': missing_info.to_dict()
        }
        
        if len(critical_missing) > 0:
            report['issues'].append(f"Critical missing values in {len(critical_missing)} columns")
        if len(moderate_missing) > 0:
            report['issues'].append(f"Moderate missing values in {len(moderate_missing)} columns")
    
    async def _check_duplicates(self, data: pd.DataFrame, report: Dict[str, Any]):
        """Check for duplicate records."""
        duplicate_count = data.duplicated().sum()
        duplicate_percentage = (duplicate_count / len(data)) * 100
        
        report['detailed_checks']['duplicates'] = {
            'duplicate_count': int(duplicate_count),
            'duplicate_percentage': float(duplicate_percentage),
            'unique_count': len(data.drop_duplicates())
        }
        
        if duplicate_percentage > 5:
            report['issues'].append(f"High duplicate rate: {duplicate_percentage:.1f}%")
        elif duplicate_percentage > 1:
            report['issues'].append(f"Moderate duplicate rate: {duplicate_percentage:.1f}%")
    
    async def _check_data_types(self, data: pd.DataFrame, report: Dict[str, Any]):
        """Check data types and consistency."""
        type_info = data.dtypes.to_dict()
        object_columns = [col for col, dtype in type_info.items() if dtype == 'object']
        
        # Check for columns that should be numeric but are object
        potential_numeric = []
        for col in object_columns:
            if data[col].dtype == 'object':
                # Try to convert to numeric
                try:
                    pd.to_numeric(data[col], errors='raise')
                    potential_numeric.append(col)
                except (ValueError, TypeError):
                    pass
        
        report['detailed_checks']['data_types'] = {
            'column_types': {k: str(v) for k, v in type_info.items()},
            'object_columns': object_columns,
            'potential_numeric': potential_numeric
        }
        
        if len(potential_numeric) > 0:
            report['issues'].append(f"Columns that could be numeric: {', '.join(potential_numeric)}")
    
    async def _check_value_ranges(self, data: pd.DataFrame, report: Dict[str, Any], validation_rules: Optional[Dict]):
        """Check value ranges and constraints."""
        range_issues = []
        
        # Check numeric columns for reasonable ranges
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            col_data = data[col].dropna()
            if len(col_data) > 0:
                min_val, max_val = col_data.min(), col_data.max()
                
                # Check for extreme values
                q1, q3 = col_data.quantile([0.25, 0.75])
                iqr = q3 - q1
                lower_bound = q1 - 3 * iqr
                upper_bound = q3 + 3 * iqr
                
                extreme_low = (col_data < lower_bound).sum()
                extreme_high = (col_data > upper_bound).sum()
                
                if extreme_low > 0 or extreme_high > 0:
                    range_issues.append({
                        'column': col,
                        'extreme_low': int(extreme_low),
                        'extreme_high': int(extreme_high),
                        'range': [float(min_val), float(max_val)]
                    })
        
        report['detailed_checks']['value_ranges'] = {
            'range_issues': range_issues,
            'numeric_columns_checked': len(numeric_columns)
        }
        
        if len(range_issues) > 0:
            report['issues'].append(f"Value range issues in {len(range_issues)} columns")
    
    async def _check_format_patterns(self, data: pd.DataFrame, report: Dict[str, Any]):
        """Check format patterns for telecommunications fields."""
        pattern_issues = []
        
        for col in data.columns:
            col_lower = col.lower() if isinstance(col, str) else str(col).lower()
            
            # Check if column matches known telecom patterns
            for pattern_name, pattern in self.TELECOM_PATTERNS.items():
                if pattern_name in col_lower or any(keyword in col_lower for keyword in pattern_name.split('_')):
                    # Validate pattern
                    col_data = data[col].dropna().astype(str)
                    if len(col_data) > 0:
                        valid_count = col_data.str.match(pattern).sum()
                        invalid_count = len(col_data) - valid_count
                        
                        if invalid_count > 0:
                            pattern_issues.append({
                                'column': col,
                                'pattern_type': pattern_name,
                                'invalid_count': int(invalid_count),
                                'invalid_percentage': float(invalid_count / len(col_data) * 100)
                            })
        
        report['detailed_checks']['format_patterns'] = {
            'pattern_issues': pattern_issues,
            'patterns_checked': len(self.TELECOM_PATTERNS)
        }
        
        if len(pattern_issues) > 0:
            report['issues'].append(f"Format pattern issues in {len(pattern_issues)} columns")
    
    async def _check_outliers(self, data: pd.DataFrame, report: Dict[str, Any]):
        """Check for statistical outliers."""
        outlier_info = []
        
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            col_data = data[col].dropna()
            if len(col_data) > 10:  # Need sufficient data for outlier detection
                # Use IQR method
                q1, q3 = col_data.quantile([0.25, 0.75])
                iqr = q3 - q1
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr
                
                outliers = ((col_data < lower_bound) | (col_data > upper_bound)).sum()
                outlier_percentage = outliers / len(col_data) * 100
                
                if outlier_percentage > 5:  # More than 5% outliers
                    outlier_info.append({
                        'column': col,
                        'outlier_count': int(outliers),
                        'outlier_percentage': float(outlier_percentage),
                        'bounds': [float(lower_bound), float(upper_bound)]
                    })
        
        report['detailed_checks']['outliers'] = {
            'outlier_columns': outlier_info,
            'numeric_columns_checked': len(numeric_columns)
        }
        
        if len(outlier_info) > 0:
            report['issues'].append(f"Outlier issues in {len(outlier_info)} columns")
    
    async def _check_consistency(self, data: pd.DataFrame, report: Dict[str, Any]):
        """Check data consistency across fields."""
        consistency_issues = []
        
        # Check for common consistency patterns in telecom data
        # Example: timestamp consistency, geographic consistency, etc.
        
        # Check timestamp columns
        timestamp_columns = [col for col in data.columns if any(keyword in col.lower() for keyword in ['time', 'date', 'timestamp'])]
        
        for col in timestamp_columns:
            try:
                # Try to parse as datetime
                parsed_dates = pd.to_datetime(data[col], errors='coerce')
                invalid_dates = parsed_dates.isnull().sum() - data[col].isnull().sum()
                
                if invalid_dates > 0:
                    consistency_issues.append({
                        'type': 'timestamp_format',
                        'column': col,
                        'invalid_count': int(invalid_dates)
                    })
            except Exception:
                pass
        
        report['detailed_checks']['consistency'] = {
            'consistency_issues': consistency_issues,
            'timestamp_columns_checked': len(timestamp_columns)
        }
        
        if len(consistency_issues) > 0:
            report['issues'].append(f"Consistency issues in {len(consistency_issues)} areas")
    
    async def _calculate_quality_score(self, report: Dict[str, Any]) -> float:
        """Calculate overall quality score (0-100)."""
        score = 100.0
        
        # Deduct points for various issues
        checks = report['detailed_checks']
        
        # Missing values penalty
        if 'missing_values' in checks:
            missing_info = checks['missing_values']
            critical_columns = len(missing_info.get('critical_columns', []))
            moderate_columns = len(missing_info.get('moderate_columns', []))
            score -= (critical_columns * 15 + moderate_columns * 5)
        
        # Duplicates penalty
        if 'duplicates' in checks:
            duplicate_pct = checks['duplicates'].get('duplicate_percentage', 0)
            score -= min(duplicate_pct * 2, 20)  # Max 20 points deduction
        
        # Data type issues penalty
        if 'data_types' in checks:
            potential_numeric = len(checks['data_types'].get('potential_numeric', []))
            score -= potential_numeric * 3
        
        # Pattern issues penalty
        if 'format_patterns' in checks:
            pattern_issues = len(checks['format_patterns'].get('pattern_issues', []))
            score -= pattern_issues * 5
        
        # Outlier penalty
        if 'outliers' in checks:
            outlier_columns = len(checks['outliers'].get('outlier_columns', []))
            score -= outlier_columns * 3
        
        # Consistency penalty
        if 'consistency' in checks:
            consistency_issues = len(checks['consistency'].get('consistency_issues', []))
            score -= consistency_issues * 5
        
        return max(0.0, min(100.0, score))
    
    async def _determine_quality_level(self, score: float) -> QualityLevel:
        """Determine quality level based on score."""
        if score >= 90:
            return QualityLevel.EXCELLENT
        elif score >= 75:
            return QualityLevel.GOOD
        elif score >= 60:
            return QualityLevel.FAIR
        elif score >= 40:
            return QualityLevel.POOR
        else:
            return QualityLevel.CRITICAL
    
    async def _generate_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on quality assessment."""
        recommendations = []
        checks = report['detailed_checks']
        
        # Missing values recommendations
        if 'missing_values' in checks:
            missing_info = checks['missing_values']
            if missing_info.get('critical_columns'):
                recommendations.append("Consider removing or imputing critical missing value columns")
            if missing_info.get('moderate_columns'):
                recommendations.append("Implement missing value handling strategy for moderate missing columns")
        
        # Duplicates recommendations
        if 'duplicates' in checks:
            duplicate_pct = checks['duplicates'].get('duplicate_percentage', 0)
            if duplicate_pct > 5:
                recommendations.append("Remove duplicate records to improve data quality")
        
        # Data type recommendations
        if 'data_types' in checks:
            potential_numeric = checks['data_types'].get('potential_numeric', [])
            if potential_numeric:
                recommendations.append(f"Convert columns to numeric types: {', '.join(potential_numeric)}")
        
        # Pattern recommendations
        if 'format_patterns' in checks:
            pattern_issues = checks['format_patterns'].get('pattern_issues', [])
            if pattern_issues:
                recommendations.append("Standardize format patterns for telecommunications fields")
        
        # Outlier recommendations
        if 'outliers' in checks:
            outlier_columns = checks['outliers'].get('outlier_columns', [])
            if outlier_columns:
                recommendations.append("Investigate and handle outliers in numeric columns")
        
        return recommendations
    
    # Data cleaning methods
    async def _remove_duplicates(self, data: pd.DataFrame, config: Dict[str, Any], mode: ProcessingMode) -> Dict[str, Any]:
        """Remove duplicate records."""
        original_count = len(data)
        
        # Get subset of columns to check for duplicates
        subset = config.get('subset', None)
        keep = config.get('keep', 'first')
        
        cleaned_data = data.drop_duplicates(subset=subset, keep=keep)
        removed_count = original_count - len(cleaned_data)
        
        return {
            'data': cleaned_data,
            'records_affected': removed_count,
            'issues_fixed': [f"Removed {removed_count} duplicate records"]
        }
    
    async def _handle_missing_values(self, data: pd.DataFrame, config: Dict[str, Any], mode: ProcessingMode) -> Dict[str, Any]:
        """Handle missing values."""
        strategy = config.get('strategy', 'drop')
        threshold = config.get('threshold', 0.5)  # Drop columns with >50% missing
        
        cleaned_data = data.copy()
        issues_fixed = []
        records_affected = 0
        
        if strategy == 'drop_columns':
            # Drop columns with high missing percentage
            missing_pct = cleaned_data.isnull().sum() / len(cleaned_data)
            cols_to_drop = missing_pct[missing_pct > threshold].index.tolist()
            
            if cols_to_drop:
                cleaned_data = cleaned_data.drop(columns=cols_to_drop)
                issues_fixed.append(f"Dropped {len(cols_to_drop)} columns with >{threshold*100}% missing values")
        
        elif strategy == 'drop_rows':
            # Drop rows with missing values
            original_count = len(cleaned_data)
            cleaned_data = cleaned_data.dropna()
            records_affected = original_count - len(cleaned_data)
            issues_fixed.append(f"Dropped {records_affected} rows with missing values")
        
        elif strategy == 'fill':
            # Fill missing values
            fill_value = config.get('fill_value', 0)
            numeric_columns = cleaned_data.select_dtypes(include=[np.number]).columns
            
            for col in numeric_columns:
                missing_count = cleaned_data[col].isnull().sum()
                if missing_count > 0:
                    if fill_value == 'mean':
                        cleaned_data[col] = cleaned_data[col].fillna(cleaned_data[col].mean())
                    elif fill_value == 'median':
                        cleaned_data[col] = cleaned_data[col].fillna(cleaned_data[col].median())
                    else:
                        cleaned_data[col] = cleaned_data[col].fillna(fill_value)
                    
                    issues_fixed.append(f"Filled {missing_count} missing values in {col}")
                    records_affected += missing_count
        
        return {
            'data': cleaned_data,
            'records_affected': records_affected,
            'issues_fixed': issues_fixed
        }
    
    async def _standardize_formats(self, data: pd.DataFrame, config: Dict[str, Any], mode: ProcessingMode) -> Dict[str, Any]:
        """Standardize data formats."""
        cleaned_data = data.copy()
        issues_fixed = []
        records_affected = 0
        
        # Standardize phone numbers (MSISDN)
        phone_columns = [col for col in data.columns if any(keyword in col.lower() for keyword in ['msisdn', 'phone', 'mobile'])]
        
        for col in phone_columns:
            if col in cleaned_data.columns:
                original_values = cleaned_data[col].copy()
                
                # Remove non-numeric characters except +
                cleaned_data[col] = cleaned_data[col].astype(str).str.replace(r'[^+\d]', '', regex=True)
                
                # Standardize to E.164 format
                mask = ~cleaned_data[col].str.startswith('+')
                cleaned_data.loc[mask, col] = '+' + cleaned_data.loc[mask, col]
                
                changed_count = (original_values != cleaned_data[col]).sum()
                if changed_count > 0:
                    issues_fixed.append(f"Standardized {changed_count} phone numbers in {col}")
                    records_affected += changed_count
        
        # Standardize timestamps
        timestamp_columns = [col for col in data.columns if any(keyword in col.lower() for keyword in ['time', 'date', 'timestamp'])]
        
        for col in timestamp_columns:
            if col in cleaned_data.columns:
                try:
                    original_values = cleaned_data[col].copy()
                    cleaned_data[col] = pd.to_datetime(cleaned_data[col], errors='coerce')
                    
                    # Count successful conversions
                    valid_conversions = ~cleaned_data[col].isnull() & original_values.notnull()
                    conversion_count = valid_conversions.sum()
                    
                    if conversion_count > 0:
                        issues_fixed.append(f"Standardized {conversion_count} timestamps in {col}")
                        records_affected += conversion_count
                
                except Exception as e:
                    self.logger.warning(f"Failed to standardize timestamps in {col}: {e}")
        
        return {
            'data': cleaned_data,
            'records_affected': records_affected,
            'issues_fixed': issues_fixed
        }
    
    async def _fix_data_types(self, data: pd.DataFrame, config: Dict[str, Any], mode: ProcessingMode) -> Dict[str, Any]:
        """Fix data types."""
        cleaned_data = data.copy()
        issues_fixed = []
        records_affected = 0
        
        # Convert object columns that should be numeric
        for col in cleaned_data.columns:
            if cleaned_data[col].dtype == 'object':
                try:
                    # Try to convert to numeric
                    numeric_series = pd.to_numeric(cleaned_data[col], errors='coerce')
                    
                    # If most values can be converted, use numeric type
                    valid_ratio = numeric_series.notnull().sum() / len(numeric_series)
                    if valid_ratio > 0.8:  # 80% of values are numeric
                        cleaned_data[col] = numeric_series
                        issues_fixed.append(f"Converted {col} to numeric type")
                        records_affected += len(cleaned_data)
                
                except Exception:
                    continue
        
        return {
            'data': cleaned_data,
            'records_affected': records_affected,
            'issues_fixed': issues_fixed
        }
    
    async def _remove_outliers(self, data: pd.DataFrame, config: Dict[str, Any], mode: ProcessingMode) -> Dict[str, Any]:
        """Remove statistical outliers."""
        method = config.get('method', 'iqr')
        threshold = config.get('threshold', 1.5)
        
        cleaned_data = data.copy()
        issues_fixed = []
        total_removed = 0
        
        numeric_columns = cleaned_data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            original_count = len(cleaned_data)
            
            if method == 'iqr':
                q1, q3 = cleaned_data[col].quantile([0.25, 0.75])
                iqr = q3 - q1
                lower_bound = q1 - threshold * iqr
                upper_bound = q3 + threshold * iqr
                
                # Remove outliers
                mask = (cleaned_data[col] >= lower_bound) & (cleaned_data[col] <= upper_bound)
                cleaned_data = cleaned_data[mask]
            
            elif method == 'zscore':
                z_scores = np.abs((cleaned_data[col] - cleaned_data[col].mean()) / cleaned_data[col].std())
                mask = z_scores <= threshold
                cleaned_data = cleaned_data[mask]
            
            removed_count = original_count - len(cleaned_data)
            if removed_count > 0:
                issues_fixed.append(f"Removed {removed_count} outliers from {col}")
                total_removed += removed_count
        
        return {
            'data': cleaned_data,
            'records_affected': total_removed,
            'issues_fixed': issues_fixed
        }
    
    async def _validate_telecom_fields(self, data: pd.DataFrame, config: Dict[str, Any], mode: ProcessingMode) -> Dict[str, Any]:
        """Validate and clean telecommunications-specific fields."""
        cleaned_data = data.copy()
        issues_fixed = []
        records_affected = 0
        
        # Validate and clean each telecom field type
        for col in cleaned_data.columns:
            col_lower = col.lower() if isinstance(col, str) else str(col).lower()
            
            # MSISDN validation
            if any(keyword in col_lower for keyword in ['msisdn', 'phone', 'mobile']):
                original_count = len(cleaned_data)
                
                # Remove invalid MSISDNs
                pattern = self.TELECOM_PATTERNS['msisdn']
                mask = cleaned_data[col].astype(str).str.match(pattern, na=False)
                cleaned_data = cleaned_data[mask]
                
                removed_count = original_count - len(cleaned_data)
                if removed_count > 0:
                    issues_fixed.append(f"Removed {removed_count} invalid MSISDNs from {col}")
                    records_affected += removed_count
            
            # IMSI validation
            elif 'imsi' in col_lower:
                original_count = len(cleaned_data)
                
                pattern = self.TELECOM_PATTERNS['imsi']
                mask = cleaned_data[col].astype(str).str.match(pattern, na=False)
                cleaned_data = cleaned_data[mask]
                
                removed_count = original_count - len(cleaned_data)
                if removed_count > 0:
                    issues_fixed.append(f"Removed {removed_count} invalid IMSIs from {col}")
                    records_affected += removed_count
            
            # Cell ID validation
            elif any(keyword in col_lower for keyword in ['cell_id', 'cellid', 'ci']):
                # Ensure cell IDs are positive integers
                try:
                    numeric_values = pd.to_numeric(cleaned_data[col], errors='coerce')
                    valid_mask = (numeric_values > 0) & (numeric_values == numeric_values.astype(int))
                    
                    original_count = len(cleaned_data)
                    cleaned_data = cleaned_data[valid_mask]
                    
                    removed_count = original_count - len(cleaned_data)
                    if removed_count > 0:
                        issues_fixed.append(f"Removed {removed_count} invalid Cell IDs from {col}")
                        records_affected += removed_count
                
                except Exception:
                    continue
        
        return {
            'data': cleaned_data,
            'records_affected': records_affected,
            'issues_fixed': issues_fixed
        }
    
    async def _get_telecom_validation_rules(self, data_type: str) -> Dict[str, Any]:
        """Get validation rules for specific telecom data type."""
        base_rules = {
            'required_columns': [],
            'data_types': {},
            'value_ranges': {},
            'patterns': {}
        }
        
        if data_type.lower() == 'cdr':
            base_rules.update({
                'required_columns': ['msisdn', 'timestamp', 'duration'],
                'patterns': {
                    'msisdn': self.TELECOM_PATTERNS['msisdn']
                },
                'value_ranges': {
                    'duration': {'min': 0, 'max': 86400}  # Max 24 hours
                }
            })
        
        elif data_type.lower() == 'kpi':
            base_rules.update({
                'required_columns': ['timestamp', 'cell_id'],
                'patterns': {
                    'cell_id': self.TELECOM_PATTERNS['cell_id']
                }
            })
        
        elif data_type.lower() in ['ep', 'nlg']:
            base_rules.update({
                'required_columns': ['timestamp', 'latitude', 'longitude'],
                'value_ranges': {
                    'latitude': {'min': -90, 'max': 90},
                    'longitude': {'min': -180, 'max': 180}
                }
            })
        
        return base_rules
    
    async def _perform_telecom_checks(self, data: pd.DataFrame, data_type: str) -> Dict[str, Any]:
        """Perform telecommunications-specific validation checks."""
        checks = {
            'telecom_specific_issues': [],
            'data_type': data_type
        }
        
        # Check for common telecom data issues
        if data_type.lower() == 'cdr':
            # Check for reasonable call durations
            if 'duration' in data.columns:
                duration_col = data['duration']
                if duration_col.dtype in [np.number]:
                    # Check for negative durations
                    negative_durations = (duration_col < 0).sum()
                    if negative_durations > 0:
                        checks['telecom_specific_issues'].append(f"Found {negative_durations} negative call durations")
                    
                    # Check for extremely long durations (>24 hours)
                    long_durations = (duration_col > 86400).sum()
                    if long_durations > 0:
                        checks['telecom_specific_issues'].append(f"Found {long_durations} calls longer than 24 hours")
        
        elif data_type.lower() in ['ep', 'nlg']:
            # Check geographic coordinates
            if 'latitude' in data.columns and 'longitude' in data.columns:
                lat_col = data['latitude']
                lon_col = data['longitude']
                
                # Check for invalid coordinates
                invalid_lat = ((lat_col < -90) | (lat_col > 90)).sum()
                invalid_lon = ((lon_col < -180) | (lon_col > 180)).sum()
                
                if invalid_lat > 0:
                    checks['telecom_specific_issues'].append(f"Found {invalid_lat} invalid latitude values")
                if invalid_lon > 0:
                    checks['telecom_specific_issues'].append(f"Found {invalid_lon} invalid longitude values")
                
                # Check for (0,0) coordinates (often indicates missing data)
                zero_coords = ((lat_col == 0) & (lon_col == 0)).sum()
                if zero_coords > 0:
                    checks['telecom_specific_issues'].append(f"Found {zero_coords} records at (0,0) coordinates")
        
        # Set validation status
        checks['is_valid'] = len(checks['telecom_specific_issues']) == 0
        
        return checks
    
    async def _calculate_improvement(self, original_data: pd.DataFrame, cleaned_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate data quality improvement metrics."""
        try:
            # Calculate basic metrics
            original_quality = await self.assess_quality(original_data)
            cleaned_quality = await self.assess_quality(cleaned_data)
            
            improvement = {
                'quality_score_improvement': cleaned_quality['quality_score'] - original_quality['quality_score'],
                'record_count_change': len(cleaned_data) - len(original_data),
                'record_retention_rate': len(cleaned_data) / len(original_data) if len(original_data) > 0 else 0,
                'issues_resolved': len(original_quality['issues']) - len(cleaned_quality['issues'])
            }
            
            return improvement
            
        except Exception as e:
            self.logger.warning(f"Failed to calculate improvement metrics: {e}")
            return {
                'quality_score_improvement': 0,
                'record_count_change': len(cleaned_data) - len(original_data),
                'record_retention_rate': len(cleaned_data) / len(original_data) if len(original_data) > 0 else 0,
                'issues_resolved': 0
            }