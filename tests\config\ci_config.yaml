# Connect CI/CD配置文件
# 定义持续集成流水线的各个阶段配置

# 流水线阶段配置
stages:
  build:
    enabled: true
    timeout: 600  # 10分钟
    retry_count: 2
    parallel: false
    
  test:
    enabled: true
    timeout: 1800  # 30分钟
    retry_count: 1
    parallel: true
    fail_fast: false
    
  security:
    enabled: true
    timeout: 900  # 15分钟
    retry_count: 1
    parallel: false
    
  performance:
    enabled: true
    timeout: 1200  # 20分钟
    retry_count: 1
    parallel: false
    
  quality_gate:
    enabled: true
    timeout: 300  # 5分钟
    retry_count: 0
    parallel: false
    
  deploy:
    enabled: true
    timeout: 600  # 10分钟
    retry_count: 2
    parallel: false

# 测试套件配置
test_suites:
  unit:
    command: "pytest tests/unit"
    required: true
    coverage_threshold: 85
    timeout: 300
    parallel: true
    markers: "not slow"
    
  integration:
    command: "pytest tests/integration"
    required: true
    coverage_threshold: 75
    timeout: 600
    parallel: false
    markers: "integration"
    
  e2e:
    command: "pytest tests/e2e"
    required: false
    timeout: 900
    parallel: false
    markers: "e2e"
    browser: "chrome"
    headless: true
    
  performance:
    command: "pytest tests/performance"
    required: false
    timeout: 1200
    parallel: false
    markers: "performance"
    
  security:
    command: "pytest tests/security"
    required: true
    timeout: 600
    parallel: false
    markers: "security"

# 代码质量检查配置
code_quality:
  formatters:
    - name: "black"
      command: "black --check ."
      fix_command: "black ."
      
    - name: "isort"
      command: "isort --check-only ."
      fix_command: "isort ."
      
  linters:
    - name: "flake8"
      command: "flake8 ."
      config_file: ".flake8"
      
    - name: "pylint"
      command: "pylint src/"
      threshold: 8.0
      
    - name: "mypy"
      command: "mypy src/"
      config_file: "mypy.ini"
      
  frontend:
    - name: "eslint"
      command: "npm run lint"
      fix_command: "npm run lint:fix"
      
    - name: "prettier"
      command: "npm run format:check"
      fix_command: "npm run format"

# 安全扫描配置
security_scans:
  dependency_check:
    - name: "safety"
      command: "safety check --json"
      fail_on_error: true
      
    - name: "pip-audit"
      command: "pip-audit --format=json"
      fail_on_error: false
      
  static_analysis:
    - name: "bandit"
      command: "bandit -r . -f json"
      config_file: ".bandit"
      severity_threshold: "medium"
      
    - name: "semgrep"
      command: "semgrep --config=auto --json"
      fail_on_error: false
      
  secrets_detection:
    - name: "detect-secrets"
      command: "detect-secrets scan --all-files"
      baseline_file: ".secrets.baseline"
      
  container_scan:
    - name: "trivy"
      command: "trivy image --format json"
      severity_threshold: "HIGH"

# 性能测试配置
performance_tests:
  load_testing:
    tool: "locust"
    users: 20
    spawn_rate: 2
    duration: "5m"
    host: "http://localhost:8000"
    
  stress_testing:
    tool: "locust"
    users: 100
    spawn_rate: 10
    duration: "10m"
    host: "http://localhost:8000"
    
  benchmark_tests:
    data_import:
      max_duration: 10  # 秒
      max_memory: 2048  # MB
      
    query_performance:
      max_response_time: 3  # 秒
      max_cpu_usage: 80  # %
      
    concurrent_users:
      max_users: 20
      max_response_time: 5  # 秒

# 质量门禁配置
quality_gates:
  config_file: "tests/monitoring/quality_gates.yaml"
  fail_on_error: true
  generate_report: true
  
  thresholds:
    code_coverage: 85
    test_pass_rate: 95
    security_score: 90
    performance_score: 85
    
  blocking_issues:
    - "critical_security_vulnerabilities"
    - "test_failures_in_required_suites"
    - "code_coverage_below_threshold"
    - "performance_regression"

# 构建配置
build:
  python:
    version: "3.9"
    requirements_file: "requirements.txt"
    dev_requirements_file: "requirements-dev.txt"
    
  frontend:
    node_version: "16"
    package_manager: "npm"
    build_command: "npm run build"
    test_command: "npm test"
    
  docker:
    enabled: true
    dockerfile: "Dockerfile"
    image_name: "connect-platform"
    registry: "docker.io"
    
  artifacts:
    - "dist/"
    - "build/"
    - "*.whl"
    - "coverage-reports/"
    - "test-reports/"

# 部署配置
deployment:
  environments:
    development:
      auto_deploy: true
      approval_required: false
      health_check_url: "http://dev.connect.local/health"
      
    staging:
      auto_deploy: false
      approval_required: true
      health_check_url: "http://staging.connect.local/health"
      smoke_tests: true
      
    production:
      auto_deploy: false
      approval_required: true
      health_check_url: "http://connect.local/health"
      smoke_tests: true
      rollback_enabled: true
      
  strategies:
    blue_green:
      enabled: true
      health_check_timeout: 300
      
    rolling:
      enabled: false
      batch_size: 1
      
  verification:
    health_checks:
      - endpoint: "/health"
        timeout: 30
        retries: 3
        
      - endpoint: "/api/v1/status"
        timeout: 10
        retries: 2
        
    smoke_tests:
      command: "pytest tests/smoke"
      timeout: 300

# 通知配置
notifications:
  slack:
    enabled: false
    webhook_url: "${SLACK_WEBHOOK_URL}"
    channel: "#ci-cd"
    username: "Connect CI/CD"
    
    triggers:
      - "pipeline_failure"
      - "deployment_success"
      - "quality_gate_failure"
      
  email:
    enabled: false
    smtp_server: "smtp.company.com"
    smtp_port: 587
    username: "${EMAIL_USERNAME}"
    password: "${EMAIL_PASSWORD}"
    
    recipients:
      - "<EMAIL>"
      - "<EMAIL>"
      
    triggers:
      - "pipeline_failure"
      - "security_vulnerability"
      
  teams:
    enabled: false
    webhook_url: "${TEAMS_WEBHOOK_URL}"
    
    triggers:
      - "pipeline_failure"
      - "deployment_failure"
      
  jira:
    enabled: false
    server_url: "https://company.atlassian.net"
    username: "${JIRA_USERNAME}"
    api_token: "${JIRA_API_TOKEN}"
    project_key: "CONNECT"
    
    auto_create_issues:
      - "security_vulnerability"
      - "performance_regression"

# 报告配置
reporting:
  formats:
    - "json"
    - "html"
    - "junit"
    
  artifacts:
    test_reports:
      path: "test-reports/"
      retention_days: 30
      
    coverage_reports:
      path: "coverage-reports/"
      retention_days: 30
      
    security_reports:
      path: "security-reports/"
      retention_days: 90
      
    performance_reports:
      path: "performance-reports/"
      retention_days: 30
      
    pipeline_reports:
      path: "pipeline-reports/"
      retention_days: 90
      
  dashboard:
    enabled: true
    url: "http://localhost:5000"
    auto_refresh: 30  # 秒
    
  metrics:
    collection_enabled: true
    prometheus_endpoint: "http://localhost:9090"
    grafana_dashboard: "http://localhost:3000"

# 缓存配置
caching:
  dependencies:
    enabled: true
    key_template: "deps-{{ checksum 'requirements.txt' }}"
    
  build_cache:
    enabled: true
    key_template: "build-{{ .Branch }}-{{ .Revision }}"
    
  test_cache:
    enabled: true
    key_template: "tests-{{ checksum 'tests/**/*.py' }}"

# 并行化配置
parallelization:
  test_execution:
    enabled: true
    max_workers: 4
    strategy: "by_file"  # by_file, by_class, by_method
    
  build_jobs:
    enabled: true
    max_parallel: 2
    
  security_scans:
    enabled: false  # 安全扫描通常不并行执行

# 环境变量配置
environment_variables:
  global:
    PYTHONPATH: "src:tests"
    DJANGO_SETTINGS_MODULE: "connect.settings.test"
    
  test:
    DATABASE_URL: "sqlite:///test.db"
    REDIS_URL: "redis://localhost:6379/1"
    
  security:
    SECURITY_SCAN_MODE: "strict"
    
  performance:
    PERFORMANCE_TEST_MODE: "benchmark"

# 资源限制配置
resource_limits:
  memory:
    build: "2GB"
    test: "4GB"
    security: "1GB"
    performance: "8GB"
    
  cpu:
    build: "2"
    test: "4"
    security: "1"
    performance: "4"
    
  timeout:
    global: 3600  # 1小时
    per_stage: 1800  # 30分钟

# 调试配置
debugging:
  verbose_logging: false
  save_artifacts_on_failure: true
  enable_ssh_access: false
  
  log_levels:
    root: "INFO"
    ci_integration: "DEBUG"
    tests: "INFO"
    security: "WARNING"

# 集成配置
integrations:
  sonarqube:
    enabled: false
    server_url: "https://sonarqube.company.com"
    project_key: "connect-platform"
    
  codecov:
    enabled: false
    token: "${CODECOV_TOKEN}"
    
  sentry:
    enabled: false
    dsn: "${SENTRY_DSN}"
    environment: "ci"
    
  datadog:
    enabled: false
    api_key: "${DATADOG_API_KEY}"
    
  newrelic:
    enabled: false
    license_key: "${NEWRELIC_LICENSE_KEY}"