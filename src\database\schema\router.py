"""Schema Router Implementation.

This module implements the Schema Router functionality for dynamic schema selection
and routing based on data sources, tenants, or other criteria.
"""

import logging
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum

# Handle relative imports with fallback
try:
    from ..config import get_config
except ImportError:
    # Fallback for when running as standalone module
    import sys
    import os
    # Add parent directories to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(os.path.dirname(current_dir))
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    from config import get_config
from ..exceptions import DatabaseError
from .manager import SchemaManager
from ..utils.validators import InputValidator


logger = logging.getLogger(__name__)


class RoutingStrategy(Enum):
    """Schema routing strategies."""
    
    DATA_SOURCE = "data_source"  # Route based on data source type
    TENANT = "tenant"  # Route based on tenant ID
    CUSTOM = "custom"  # Custom routing logic
    DEFAULT = "default"  # Use default schema


@dataclass
class RoutingRule:
    """Schema routing rule definition."""
    
    name: str
    strategy: RoutingStrategy
    condition: Dict[str, Any]
    target_schema: str
    priority: int = 0
    enabled: bool = True


class SchemaRoutingError(DatabaseError):
    """Schema routing specific error."""
    pass


class SchemaRouter:
    """Schema Router for dynamic schema selection and routing.
    
    This class provides functionality to:
    - Route requests to appropriate schemas based on various criteria
    - Support multi-tenancy through schema isolation
    - Handle data source specific schema routing
    - Provide fallback mechanisms
    """
    
    def __init__(self, schema_manager: SchemaManager):
        """Initialize Schema Router.
        
        Args:
            schema_manager: Schema manager instance for schema operations.
        """
        self.schema_manager = schema_manager
        self.config = get_config()
        self.routing_rules: List[RoutingRule] = []
        self.default_schema = "public"
        self._initialize_default_rules()
        
        logger.info("Schema Router initialized")
    
    def _initialize_default_rules(self) -> None:
        """Initialize default routing rules based on configuration."""
        # Add data source based routing rules
        # Note: data_sources configuration is not yet implemented in ConnectConfig
        # This is a placeholder for future data source routing functionality
        data_sources = getattr(self.config, 'data_sources', {})
        
        for source_name, source_config in data_sources.items():
            if hasattr(source_config, 'schema_name') and source_config.schema_name:
                rule = RoutingRule(
                    name=f"data_source_{source_name}",
                    strategy=RoutingStrategy.DATA_SOURCE,
                    condition={"data_source": source_name},
                    target_schema=source_config.schema_name,
                    priority=10
                )
                self.routing_rules.append(rule)
        
        # Sort rules by priority (higher priority first)
        self.routing_rules.sort(key=lambda x: x.priority, reverse=True)
        
        logger.info(f"Initialized {len(self.routing_rules)} default routing rules")
    
    def add_routing_rule(self, rule: RoutingRule) -> None:
        """Add a new routing rule.
        
        Args:
            rule: Routing rule to add.
            
        Raises:
            SchemaRoutingError: If rule validation fails.
        """
        # Validate rule
        if not rule.name or not rule.target_schema:
            raise SchemaRoutingError("Rule name and target schema are required")
        
        if not InputValidator.validate_schema_name(rule.target_schema):
            raise SchemaRoutingError(f"Invalid target schema name: {rule.target_schema}")
        
        # Check for duplicate rule names
        if any(r.name == rule.name for r in self.routing_rules):
            raise SchemaRoutingError(f"Routing rule '{rule.name}' already exists")
        
        self.routing_rules.append(rule)
        # Re-sort by priority
        self.routing_rules.sort(key=lambda x: x.priority, reverse=True)
        
        logger.info(f"Added routing rule: {rule.name} -> {rule.target_schema}")
    
    def remove_routing_rule(self, rule_name: str) -> bool:
        """Remove a routing rule.
        
        Args:
            rule_name: Name of the rule to remove.
            
        Returns:
            True if rule was removed, False if not found.
        """
        for i, rule in enumerate(self.routing_rules):
            if rule.name == rule_name:
                del self.routing_rules[i]
                logger.info(f"Removed routing rule: {rule_name}")
                return True
        
        logger.warning(f"Routing rule not found: {rule_name}")
        return False
    
    def get_routing_rules(self) -> List[RoutingRule]:
        """Get all routing rules.
        
        Returns:
            List of all routing rules.
        """
        return self.routing_rules.copy()
    
    def route_schema(self, context: Dict[str, Any]) -> str:
        """Route to appropriate schema based on context.
        
        Args:
            context: Routing context containing criteria for schema selection.
                   Common keys: 'data_source', 'tenant_id', 'user_id', 'table_name'
        
        Returns:
            Target schema name.
            
        Raises:
            SchemaRoutingError: If routing fails.
        """
        if not context:
            logger.debug("Empty context, using default schema")
            return self.default_schema
        
        # Apply routing rules in priority order
        for rule in self.routing_rules:
            if not rule.enabled:
                continue
                
            if self._matches_rule(rule, context):
                logger.debug(f"Matched rule '{rule.name}' -> schema '{rule.target_schema}'")
                return rule.target_schema
        
        # No rule matched, use default schema
        logger.debug(f"No routing rule matched, using default schema: {self.default_schema}")
        return self.default_schema
    
    def _matches_rule(self, rule: RoutingRule, context: Dict[str, Any]) -> bool:
        """Check if context matches a routing rule.
        
        Args:
            rule: Routing rule to check.
            context: Routing context.
            
        Returns:
            True if context matches the rule.
        """
        if rule.strategy == RoutingStrategy.DATA_SOURCE:
            return self._matches_data_source_rule(rule, context)
        elif rule.strategy == RoutingStrategy.TENANT:
            return self._matches_tenant_rule(rule, context)
        elif rule.strategy == RoutingStrategy.CUSTOM:
            return self._matches_custom_rule(rule, context)
        elif rule.strategy == RoutingStrategy.DEFAULT:
            return True  # Default rule always matches
        
        return False
    
    def _matches_data_source_rule(self, rule: RoutingRule, context: Dict[str, Any]) -> bool:
        """Check if context matches a data source rule."""
        data_source = context.get("data_source")
        if not data_source:
            return False
        
        return rule.condition.get("data_source") == data_source
    
    def _matches_tenant_rule(self, rule: RoutingRule, context: Dict[str, Any]) -> bool:
        """Check if context matches a tenant rule."""
        tenant_id = context.get("tenant_id")
        if not tenant_id:
            return False
        
        # Support both exact match and pattern matching
        condition_tenant = rule.condition.get("tenant_id")
        if isinstance(condition_tenant, str):
            return condition_tenant == tenant_id
        elif isinstance(condition_tenant, list):
            return tenant_id in condition_tenant
        
        return False
    
    def _matches_custom_rule(self, rule: RoutingRule, context: Dict[str, Any]) -> bool:
        """Check if context matches a custom rule."""
        # Custom rules can have complex conditions
        for key, expected_value in rule.condition.items():
            context_value = context.get(key)
            
            if isinstance(expected_value, list):
                if context_value not in expected_value:
                    return False
            elif isinstance(expected_value, dict):
                # Support operators like {'operator': 'startswith', 'value': 'prefix_'}
                operator = expected_value.get("operator")
                value = expected_value.get("value")
                
                if operator == "startswith" and not str(context_value).startswith(str(value)):
                    return False
                elif operator == "endswith" and not str(context_value).endswith(str(value)):
                    return False
                elif operator == "contains" and str(value) not in str(context_value):
                    return False
                elif operator == "regex":
                    import re
                    if not re.match(str(value), str(context_value)):
                        return False
            else:
                if context_value != expected_value:
                    return False
        
        return True
    
    async def switch_schema(self, schema_name: str, create_if_not_exists: bool = False) -> bool:
        """Switch to a specific schema.
        
        Args:
            schema_name: Name of the schema to switch to.
            create_if_not_exists: Whether to create schema if it doesn't exist.
            
        Returns:
            True if switch was successful.
            
        Raises:
            SchemaRoutingError: If schema switch fails.
        """
        if not InputValidator.validate_schema_name(schema_name):
            raise SchemaRoutingError(f"Invalid schema name: {schema_name}")
        
        try:
            # Check if schema exists
            if not await self.schema_manager.schema_exists(schema_name):
                if create_if_not_exists:
                    await self.schema_manager.create_schema(schema_name)
                    logger.info(f"Created schema: {schema_name}")
                else:
                    raise SchemaRoutingError(f"Schema does not exist: {schema_name}")
            
            # Switch to schema (this would typically involve setting search_path)
            # For now, we'll just log the switch
            logger.info(f"Switched to schema: {schema_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to switch to schema {schema_name}: {e}")
            raise SchemaRoutingError(f"Schema switch failed: {e}")
    
    def get_schema_for_table(self, table_name: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Get the appropriate schema for a specific table.
        
        Args:
            table_name: Name of the table.
            context: Additional routing context.
            
        Returns:
            Schema name for the table.
        """
        if not context:
            context = {}
        
        # Add table name to context
        context["table_name"] = table_name
        
        # Try to infer data source from table name patterns
        if "data_source" not in context:
            data_source = self._infer_data_source_from_table(table_name)
            if data_source:
                context["data_source"] = data_source
        
        return self.route_schema(context)
    
    def _infer_data_source_from_table(self, table_name: str) -> Optional[str]:
        """Infer data source from table name patterns.
        
        Args:
            table_name: Name of the table.
            
        Returns:
            Inferred data source name or None.
        """
        for source_name, source_config in self.config.data_sources.items():
            pattern = source_config.table_name_pattern
            if pattern:
                # Simple pattern matching - could be enhanced with regex
                if source_name.lower() in table_name.lower():
                    return source_name
        
        return None
    
    def get_tenant_schema(self, tenant_id: str) -> str:
        """Get schema for a specific tenant.
        
        Args:
            tenant_id: Tenant identifier.
            
        Returns:
            Schema name for the tenant.
        """
        context = {"tenant_id": tenant_id}
        return self.route_schema(context)
    
    async def list_available_schemas(self) -> List[str]:
        """List all available schemas.
        
        Returns:
            List of schema names.
        """
        try:
            return await self.schema_manager.list_schemas()
        except Exception as e:
            logger.error(f"Failed to list schemas: {e}")
            return []
    
    async def validate_routing_configuration(self) -> Dict[str, Any]:
        """Validate the current routing configuration.
        
        Returns:
            Validation report with any issues found.
        """
        issues = []
        schemas_referenced = set()
        
        for rule in self.routing_rules:
            # Validate rule name
            if not rule.name or not isinstance(rule.name, str):
                issues.append(f"Rule has invalid name: {rule.name}")
            
            # Validate target schema
            if not rule.target_schema:
                issues.append(f"Rule '{rule.name}' has no target schema")
            else:
                schemas_referenced.add(rule.target_schema)
                
                # Validate schema name format
                if not InputValidator.validate_schema_name(rule.target_schema):
                    issues.append(f"Rule '{rule.name}' has invalid schema name: {rule.target_schema}")
                
                # Check if schema exists
                try:
                    if not await self.schema_manager.schema_exists(rule.target_schema):
                        issues.append(f"Rule '{rule.name}' references non-existent schema: {rule.target_schema}")
                except Exception as e:
                    issues.append(f"Failed to check schema existence for '{rule.target_schema}': {e}")
            
            # Validate strategy
            if rule.strategy not in RoutingStrategy:
                issues.append(f"Rule '{rule.name}' has invalid strategy: {rule.strategy}")
            
            # Validate condition
            if not rule.condition or not isinstance(rule.condition, dict):
                issues.append(f"Rule '{rule.name}' has invalid condition: {rule.condition}")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "rules_count": len(self.routing_rules),
            "schemas_referenced": list(schemas_referenced),
        }
    
    def set_default_schema(self, schema_name: str) -> None:
        """Set the default schema.
        
        Args:
            schema_name: Name of the default schema.
            
        Raises:
            SchemaRoutingError: If schema name is invalid.
        """
        if not InputValidator.validate_schema_name(schema_name):
            raise SchemaRoutingError(f"Invalid default schema name: {schema_name}")
        
        self.default_schema = schema_name
        logger.info(f"Default schema set to: {schema_name}")
    
    def get_default_schema(self) -> str:
        """Get the default schema name.
        
        Returns:
            Default schema name.
        """
        return self.default_schema