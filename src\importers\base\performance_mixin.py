"""Performance monitoring and optimization mixin for telecommunications data importers.

This module provides comprehensive performance monitoring, profiling, and optimization
capabilities for data import operations with telecommunications-specific optimizations.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import asyncio
import gc
import logging
import time
import threading
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from contextlib import contextmanager
from pathlib import Path
import json

import psutil
import pandas as pd
from pydantic import BaseModel, Field, ConfigDict


@dataclass
class PerformanceSnapshot:
    """Performance snapshot at a specific point in time."""
    timestamp: datetime
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_io_sent_mb: float = 0.0
    network_io_recv_mb: float = 0.0
    active_threads: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'timestamp': self.timestamp.isoformat(),
            'cpu_percent': self.cpu_percent,
            'memory_mb': self.memory_mb,
            'memory_percent': self.memory_percent,
            'disk_io_read_mb': self.disk_io_read_mb,
            'disk_io_write_mb': self.disk_io_write_mb,
            'network_io_sent_mb': self.network_io_sent_mb,
            'network_io_recv_mb': self.network_io_recv_mb,
            'active_threads': self.active_threads
        }


@dataclass
class OperationMetrics:
    """Metrics for a specific operation."""
    operation_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_seconds: float = 0.0
    records_processed: int = 0
    bytes_processed: int = 0
    memory_peak_mb: float = 0.0
    cpu_time_seconds: float = 0.0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    
    def finish(self) -> None:
        """Mark operation as finished and calculate duration."""
        self.end_time = datetime.now()
        if self.start_time:
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()
            
    def get_throughput(self) -> float:
        """Calculate records per second throughput."""
        if self.duration_seconds > 0:
            return self.records_processed / self.duration_seconds
        return 0.0
        
    def get_bandwidth_mbps(self) -> float:
        """Calculate bandwidth in MB/s."""
        if self.duration_seconds > 0:
            return (self.bytes_processed / 1024 / 1024) / self.duration_seconds
        return 0.0
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'operation_name': self.operation_name,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration_seconds': self.duration_seconds,
            'records_processed': self.records_processed,
            'bytes_processed': self.bytes_processed,
            'memory_peak_mb': self.memory_peak_mb,
            'cpu_time_seconds': self.cpu_time_seconds,
            'throughput_records_per_second': self.get_throughput(),
            'bandwidth_mbps': self.get_bandwidth_mbps(),
            'errors': self.errors,
            'warnings': self.warnings,
            'custom_metrics': self.custom_metrics
        }


class PerformanceConfig(BaseModel):
    """Configuration for performance monitoring."""
    enable_monitoring: bool = Field(default=True, description="Enable performance monitoring")
    enable_profiling: bool = Field(default=False, description="Enable detailed profiling")
    snapshot_interval_seconds: float = Field(default=1.0, ge=0.1, le=60.0, description="Snapshot interval")
    max_snapshots: int = Field(default=1000, ge=10, le=10000, description="Maximum snapshots to keep")
    
    # Memory optimization
    enable_memory_optimization: bool = Field(default=True, description="Enable memory optimization")
    memory_warning_threshold_mb: int = Field(default=1024, description="Memory warning threshold")
    memory_critical_threshold_mb: int = Field(default=2048, description="Memory critical threshold")
    gc_frequency: int = Field(default=10, description="Garbage collection frequency")
    
    # Performance thresholds
    cpu_warning_threshold: float = Field(default=80.0, description="CPU warning threshold (%)")
    memory_warning_threshold: float = Field(default=80.0, description="Memory warning threshold (%)")
    disk_io_warning_threshold_mbps: float = Field(default=100.0, description="Disk I/O warning threshold")
    
    # Reporting
    enable_real_time_reporting: bool = Field(default=False, description="Enable real-time reporting")
    report_interval_seconds: float = Field(default=30.0, description="Reporting interval")
    export_metrics: bool = Field(default=True, description="Export metrics to file")
    metrics_export_path: Optional[str] = Field(default=None, description="Metrics export path")
    
    # Telecommunications-specific optimizations
    enable_telecom_optimizations: bool = Field(default=True, description="Enable telecom optimizations")
    cdr_batch_size: int = Field(default=50000, description="CDR processing batch size")
    kpi_batch_size: int = Field(default=10000, description="KPI processing batch size")
    spatial_index_threshold: int = Field(default=100000, description="Spatial index creation threshold")
    
    model_config = ConfigDict(
        extra="allow"
    )
class PerformanceMixin:
    """Mixin class providing comprehensive performance monitoring and optimization.
    
    This mixin can be used with any importer to add performance monitoring,
    profiling, and optimization capabilities.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.performance_config = PerformanceConfig()
        self.performance_logger = logging.getLogger(f"{self.__class__.__name__}.performance")
        
        # Performance tracking
        self._performance_snapshots: List[PerformanceSnapshot] = []
        self._operation_metrics: Dict[str, OperationMetrics] = {}
        self._monitoring_active = False
        self._monitoring_task = None
        self._process = psutil.Process()
        self._initial_io_counters = None
        
        # Optimization state
        self._optimization_enabled = True
        self._last_gc_time = time.time()
        self._operation_counter = 0
        
        # Threading
        self._lock = threading.Lock()
        
    def configure_performance(self, config: Union[PerformanceConfig, Dict[str, Any]]) -> None:
        """Configure performance monitoring settings.
        
        Args:
            config: Performance configuration
        """
        if isinstance(config, dict):
            self.performance_config = PerformanceConfig(**config)
        else:
            self.performance_config = config
            
        self.performance_logger.info(f"Performance monitoring configured: {self.performance_config.enable_monitoring}")
        
    async def start_performance_monitoring(self) -> None:
        """Start performance monitoring."""
        if not self.performance_config.enable_monitoring:
            return
            
        if self._monitoring_active:
            self.performance_logger.warning("Performance monitoring already active")
            return
            
        self._monitoring_active = True
        self._initial_io_counters = self._process.io_counters()
        
        # Start monitoring task
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        self.performance_logger.info("Performance monitoring started")
        
    async def stop_performance_monitoring(self) -> None:
        """Stop performance monitoring."""
        if not self._monitoring_active:
            return
            
        self._monitoring_active = False
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
            self._monitoring_task = None
            
        self.performance_logger.info("Performance monitoring stopped")
        
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        try:
            while self._monitoring_active:
                # Capture snapshot
                snapshot = self._capture_performance_snapshot()
                
                # Store snapshot
                with self._lock:
                    self._performance_snapshots.append(snapshot)
                    
                    # Limit snapshots
                    if len(self._performance_snapshots) > self.performance_config.max_snapshots:
                        self._performance_snapshots.pop(0)
                        
                # Check thresholds
                self._check_performance_thresholds(snapshot)
                
                # Real-time reporting
                if self.performance_config.enable_real_time_reporting:
                    await self._report_real_time_metrics(snapshot)
                    
                await asyncio.sleep(self.performance_config.snapshot_interval_seconds)
                
        except asyncio.CancelledError:
            self.performance_logger.info("Monitoring loop cancelled")
            raise  # Re-raise to properly handle cancellation
        except Exception as e:
            self.performance_logger.error(f"Monitoring loop error: {e}")
        finally:
            self._monitoring_active = False
            self.performance_logger.debug("Monitoring loop finished")
            
    def _capture_performance_snapshot(self) -> PerformanceSnapshot:
        """Capture current performance snapshot."""
        try:
            # CPU and memory
            cpu_percent = self._process.cpu_percent()
            memory_info = self._process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            memory_percent = self._process.memory_percent()
            
            # Disk I/O
            io_counters = self._process.io_counters()
            if self._initial_io_counters:
                disk_read_mb = (io_counters.read_bytes - self._initial_io_counters.read_bytes) / 1024 / 1024
                disk_write_mb = (io_counters.write_bytes - self._initial_io_counters.write_bytes) / 1024 / 1024
            else:
                disk_read_mb = io_counters.read_bytes / 1024 / 1024
                disk_write_mb = io_counters.write_bytes / 1024 / 1024
                
            # Threads
            active_threads = self._process.num_threads()
            
            return PerformanceSnapshot(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_mb=memory_mb,
                memory_percent=memory_percent,
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                active_threads=active_threads
            )
            
        except Exception as e:
            self.performance_logger.error(f"Error capturing performance snapshot: {e}")
            return PerformanceSnapshot(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_mb=0.0,
                memory_percent=0.0,
                disk_io_read_mb=0.0,
                disk_io_write_mb=0.0
            )
            
    def _check_performance_thresholds(self, snapshot: PerformanceSnapshot) -> None:
        """Check performance thresholds and issue warnings."""
        # CPU threshold
        if snapshot.cpu_percent > self.performance_config.cpu_warning_threshold:
            self.performance_logger.warning(
                f"High CPU usage: {snapshot.cpu_percent:.1f}% "
                f"(threshold: {self.performance_config.cpu_warning_threshold}%)"
            )
            
        # Memory threshold
        if snapshot.memory_percent > self.performance_config.memory_warning_threshold:
            self.performance_logger.warning(
                f"High memory usage: {snapshot.memory_percent:.1f}% "
                f"({snapshot.memory_mb:.1f}MB, threshold: {self.performance_config.memory_warning_threshold}%)"
            )
            
        # Critical memory threshold
        if snapshot.memory_mb > self.performance_config.memory_critical_threshold_mb:
            self.performance_logger.critical(
                f"Critical memory usage: {snapshot.memory_mb:.1f}MB "
                f"(threshold: {self.performance_config.memory_critical_threshold_mb}MB)"
            )
            
            # Force garbage collection
            if self.performance_config.enable_memory_optimization:
                gc.collect()
                
    async def _report_real_time_metrics(self, snapshot: PerformanceSnapshot) -> None:
        """Report real-time metrics."""
        # This could be extended to send metrics to external systems
        self.performance_logger.info(
            f"Performance: CPU={snapshot.cpu_percent:.1f}%, "
            f"Memory={snapshot.memory_mb:.1f}MB ({snapshot.memory_percent:.1f}%), "
            f"Threads={snapshot.active_threads}"
        )
        
    @contextmanager
    def track_operation(self, operation_name: str, **custom_metrics):
        """Context manager to track operation performance.
        
        Args:
            operation_name: Name of the operation
            **custom_metrics: Additional custom metrics
        """
        metrics = OperationMetrics(
            operation_name=operation_name,
            start_time=datetime.now(),
            custom_metrics=custom_metrics
        )
        
        # Store initial memory
        initial_memory = self._process.memory_info().rss / 1024 / 1024
        
        try:
            yield metrics
            
        except Exception as e:
            metrics.errors.append(str(e))
            raise
            
        finally:
            metrics.finish()
            
            # Calculate peak memory
            current_memory = self._process.memory_info().rss / 1024 / 1024
            metrics.memory_peak_mb = max(initial_memory, current_memory)
            
            # Store metrics
            with self._lock:
                self._operation_metrics[f"{operation_name}_{int(time.time())}"] = metrics
                
            # Memory optimization
            self._operation_counter += 1
            if (self.performance_config.enable_memory_optimization and 
                self._operation_counter % self.performance_config.gc_frequency == 0):
                gc.collect()
                
            self.performance_logger.info(
                f"Operation '{operation_name}' completed in {metrics.duration_seconds:.2f}s, "
                f"processed {metrics.records_processed} records "
                f"({metrics.get_throughput():.0f} records/s)"
            )
            
    def optimize_for_telecom_data(self, data_type: str, record_count: int) -> Dict[str, Any]:
        """Apply telecommunications-specific optimizations.
        
        Args:
            data_type: Type of telecommunications data (CDR, KPI, EP, NLG)
            record_count: Number of records to process
            
        Returns:
            Optimization recommendations
        """
        if not self.performance_config.enable_telecom_optimizations:
            return {}
            
        recommendations = {
            'batch_size': 10000,
            'memory_limit_mb': 1024,
            'use_chunking': False,
            'enable_spatial_index': False,
            'compression_level': 6
        }
        
        # Data type specific optimizations
        if data_type.upper() == 'CDR':
            recommendations.update({
                'batch_size': self.performance_config.cdr_batch_size,
                'use_chunking': record_count > 1000000,
                'memory_limit_mb': min(2048, max(512, record_count // 1000)),
                'enable_time_partitioning': record_count > 5000000
            })
            
        elif data_type.upper() == 'KPI':
            recommendations.update({
                'batch_size': self.performance_config.kpi_batch_size,
                'use_chunking': record_count > 500000,
                'enable_aggregation_cache': True,
                'memory_limit_mb': min(1024, max(256, record_count // 2000))
            })
            
        elif data_type.upper() in ['EP', 'NLG']:
            recommendations.update({
                'batch_size': min(20000, max(5000, record_count // 100)),
                'enable_spatial_index': record_count > self.performance_config.spatial_index_threshold,
                'use_spatial_chunking': record_count > 100000,
                'memory_limit_mb': min(3072, max(512, record_count // 500))
            })
            
        self.performance_logger.info(
            f"Applied {data_type} optimizations for {record_count} records: {recommendations}"
        )
        
        return recommendations
        
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        with self._lock:
            snapshots = self._performance_snapshots.copy()
            operations = self._operation_metrics.copy()
            
        if not snapshots:
            return {'error': 'No performance data available'}
            
        # Calculate summary statistics
        cpu_values = [s.cpu_percent for s in snapshots]
        memory_values = [s.memory_mb for s in snapshots]
        
        summary = {
            'monitoring_period': {
                'start': snapshots[0].timestamp.isoformat(),
                'end': snapshots[-1].timestamp.isoformat(),
                'duration_minutes': (snapshots[-1].timestamp - snapshots[0].timestamp).total_seconds() / 60
            },
            'cpu_usage': {
                'average': sum(cpu_values) / len(cpu_values),
                'peak': max(cpu_values),
                'minimum': min(cpu_values)
            },
            'memory_usage': {
                'average_mb': sum(memory_values) / len(memory_values),
                'peak_mb': max(memory_values),
                'minimum_mb': min(memory_values)
            },
            'operations': {
                'total_count': len(operations),
                'total_records_processed': sum(op.records_processed for op in operations.values()),
                'total_processing_time': sum(op.duration_seconds for op in operations.values()),
                'average_throughput': sum(op.get_throughput() for op in operations.values()) / len(operations) if operations else 0
            },
            'snapshots_count': len(snapshots),
            'latest_snapshot': snapshots[-1].to_dict() if snapshots else None
        }
        
        return summary
        
    def export_performance_metrics(self, file_path: Optional[str] = None) -> str:
        """Export performance metrics to file.
        
        Args:
            file_path: Optional file path, uses config default if not provided
            
        Returns:
            Path to exported file
        """
        if not self.performance_config.export_metrics:
            raise ValueError("Metrics export is disabled in configuration")
            
        if file_path is None:
            if self.performance_config.metrics_export_path:
                file_path = self.performance_config.metrics_export_path
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_path = f"performance_metrics_{timestamp}.json"
                
        # Prepare export data
        export_data = {
            'export_timestamp': datetime.now().isoformat(),
            'configuration': self.performance_config.dict(),
            'summary': self.get_performance_summary(),
            'snapshots': [s.to_dict() for s in self._performance_snapshots],
            'operations': {k: v.to_dict() for k, v in self._operation_metrics.items()}
        }
        
        # Write to file
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
            
        self.performance_logger.info(f"Performance metrics exported to: {file_path}")
        return file_path
        
    def clear_performance_data(self) -> None:
        """Clear all performance data."""
        with self._lock:
            self._performance_snapshots.clear()
            self._operation_metrics.clear()
            
        self.performance_logger.info("Performance data cleared")
        
    def get_optimization_recommendations(self) -> List[str]:
        """Get performance optimization recommendations based on collected data."""
        recommendations = []
        
        if not self._performance_snapshots:
            return ["No performance data available for analysis"]
            
        # Analyze CPU usage
        cpu_values = [s.cpu_percent for s in self._performance_snapshots]
        avg_cpu = sum(cpu_values) / len(cpu_values)
        
        if avg_cpu > 80:
            recommendations.append("Consider reducing batch size or enabling parallel processing")
        elif avg_cpu < 20:
            recommendations.append("CPU utilization is low, consider increasing batch size")
            
        # Analyze memory usage
        memory_values = [s.memory_mb for s in self._performance_snapshots]
        peak_memory = max(memory_values)
        
        if peak_memory > 2048:
            recommendations.append("High memory usage detected, enable chunking for large datasets")
        if peak_memory > 4096:
            recommendations.append("Critical memory usage, consider using Polars instead of Pandas")
            
        # Analyze operations
        if self._operation_metrics:
            avg_throughput = sum(op.get_throughput() for op in self._operation_metrics.values()) / len(self._operation_metrics)
            if avg_throughput < 1000:  # records per second
                recommendations.append("Low throughput detected, consider optimizing data processing pipeline")
                
        if not recommendations:
            recommendations.append("Performance appears optimal based on current metrics")
            
        return recommendations
        
    def cleanup_performance(self) -> None:
        """Cleanup performance monitoring resources."""
        if self._monitoring_active:
            asyncio.create_task(self.stop_performance_monitoring())
            
        self.clear_performance_data()
        
    def __del__(self):
        """Destructor to cleanup resources."""
        self.cleanup_performance()