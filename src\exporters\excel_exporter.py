__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Excel data exporter.

This module provides Excel export functionality for various data formats.
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from .base import BaseExporter, ExportError, ExportResult

# Configure logging
logger = logging.getLogger(__name__)


class ExcelExporter(BaseExporter):
    """Excel data exporter."""

    def __init__(
        self, output_path: Union[str, Path], sheet_name: str = "Sheet1", **kwargs
    ):
        """Initialize Excel exporter.

        Args:
            output_path: Path where Excel file will be saved
            sheet_name: Name of the Excel sheet
            **kwargs: Additional configuration options
        """
        super().__init__(output_path, **kwargs)
        self.sheet_name = sheet_name

        # Ensure output file has .xlsx extension
        if not self.output_path.suffix:
            self.output_path = self.output_path.with_suffix(".xlsx")
        elif self.output_path.suffix.lower() not in [".xlsx", ".xls"]:
            self.output_path = self.output_path.with_suffix(".xlsx")

    async def export(self, data: Any, **kwargs) -> ExportResult:
        """Export data to Excel format.

        Args:
            data: Data to export (DataFrame, list of dicts, or dict of DataFrames)
            **kwargs: Additional export options
                - index: Whether to include index column (default: False)
                - multiple_sheets: Dict of sheet_name -> data for multiple sheets

        Returns:
            ExportResult: Result of the export operation

        Raises:
            ExportError: If export fails
        """
        try:
            self.validate_data(data)
            self.prepare_output_directory()

            include_index = kwargs.get("index", False)
            multiple_sheets = kwargs.get("multiple_sheets", None)

            records_exported = 0

            with pd.ExcelWriter(self.output_path, engine="openpyxl") as writer:
                if multiple_sheets:
                    # Export multiple sheets
                    for sheet_name, sheet_data in multiple_sheets.items():
                        if isinstance(sheet_data, pd.DataFrame):
                            sheet_data.to_excel(
                                writer, sheet_name=sheet_name, index=include_index
                            )
                            records_exported += len(sheet_data)
                        else:
                            # Convert to DataFrame if not already
                            df = self._convert_to_dataframe(sheet_data)
                            df.to_excel(
                                writer, sheet_name=sheet_name, index=include_index
                            )
                            records_exported += len(df)
                else:
                    # Export single sheet
                    if isinstance(data, pd.DataFrame):
                        data.to_excel(
                            writer, sheet_name=self.sheet_name, index=include_index
                        )
                        records_exported = len(data)
                    else:
                        # Convert to DataFrame
                        df = self._convert_to_dataframe(data)
                        df.to_excel(
                            writer, sheet_name=self.sheet_name, index=include_index
                        )
                        records_exported = len(df)

            file_size = (
                self.output_path.stat().st_size if self.output_path.exists() else 0
            )

            logger.info(
                f"Successfully exported {records_exported} records to {self.output_path}"
            )

            return ExportResult(
                success=True,
                file_path=self.output_path,
                records_exported=records_exported,
                file_size_bytes=file_size,
                metadata={
                    "sheet_name": self.sheet_name,
                    "include_index": include_index,
                    "multiple_sheets": multiple_sheets is not None,
                },
            )

        except Exception as e:
            error_msg = f"Failed to export Excel: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ExportResult(success=False, error_message=error_msg)

    def _convert_to_dataframe(self, data: Any) -> pd.DataFrame:
        """Convert various data types to pandas DataFrame."""
        if isinstance(data, list):
            if data and isinstance(data[0], dict):
                return pd.DataFrame(data)
            else:
                return pd.DataFrame(data, columns=["Value"])
        elif isinstance(data, dict):
            return pd.DataFrame([data])
        else:
            return pd.DataFrame([data], columns=["Value"])
