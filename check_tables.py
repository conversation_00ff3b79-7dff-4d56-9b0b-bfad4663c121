from src.database.operations.database_manager import DatabaseManager
from src.database.connection.pool import get_pool_manager
from src.config import get_config
import asyncio

async def check_ep_tables():
    config = get_config()
    pool_manager = get_pool_manager(config)
    await pool_manager.initialize_pool()
    
    async with pool_manager.acquire() as conn:
        # 首先检查所有schema
        schemas = await conn.fetch("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
            ORDER BY schema_name;
        """)
        
        print("Available schemas:")
        for schema in schemas:
            print(f"  - {schema['schema_name']}")
        print("-" * 50)
        
        # 检查所有schema中的表（不只是包含'ep'的）
        for schema in schemas:
            schema_name = schema['schema_name']
            if not schema_name.startswith('pg_'):
                tables = await conn.fetch("""
                    SELECT table_name, 
                           (SELECT COUNT(*) FROM information_schema.columns 
                            WHERE table_schema = $1 AND table_name = t.table_name) as column_count
                    FROM information_schema.tables t
                    WHERE table_schema = $1 
                    ORDER BY table_name;
                """, schema_name)
                
                print(f"\nFound {len(tables)} tables in {schema_name} schema:")
                print("-" * 50)
                
                for row in tables:
                    table_name = row['table_name']
                    column_count = row['column_count']
                    # 获取每个表的记录数
                    try:
                        record_count = await conn.fetchval(f'SELECT COUNT(*) FROM "{schema_name}"."{table_name}"')
                        print(f"📊 {table_name}: {record_count:,} records, {column_count} columns")
                    except Exception as e:
                        print(f"📊 {table_name}: Error counting records ({e}), {column_count} columns")
                
                print("-" * 50)
                print(f"Total tables in {schema_name}: {len(tables)}")
    
    await pool_manager.close()

if __name__ == "__main__":
    asyncio.run(check_ep_tables())