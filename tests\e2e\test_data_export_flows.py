#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 数据导出流程E2E测试

本模块提供数据导出相关的端到端测试，包括：
- 报告导出测试
- 数据导出测试
- 批量导出测试
- 导出格式验证
- 导出权限控制

作者: Connect质量工程团队
创建时间: 2024-01-20
"""

import os
import time
import json
import pytest
import zipfile
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from test_complete_business_flows import _E2ETestBase


class TestDataExportFlow(_E2ETestBase):
    """数据导出完整流程测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.setup_driver()
        self.download_dir = os.path.join(os.getcwd(), "test_downloads")
        os.makedirs(self.download_dir, exist_ok=True)
    
    def teardown_method(self):
        """测试后清理"""
        # 清理下载文件
        if os.path.exists(self.download_dir):
            for file in os.listdir(self.download_dir):
                try:
                    os.remove(os.path.join(self.download_dir, file))
                except:
                    pass
        
        self.cleanup()
        self.teardown_driver()
    
    @pytest.mark.e2e
    def test_kpi_report_export(self):
        """KPI报告导出测试"""
        # 1. 登录系统
        assert self.login('analyst'), "分析师登录失败"
        
        # 2. 导航到KPI监控页面
        self.navigate_to('/kpi-monitoring')
        
        # 3. 生成KPI报告
        self._generate_kpi_report()
        
        # 4. 导出Excel格式
        self._export_report('excel')
        
        # 5. 导出PDF格式
        self._export_report('pdf')
        
        # 6. 验证导出文件
        self._verify_exported_files(['kpi_report.xlsx', 'kpi_report.pdf'])
        
        # 7. 登出
        self.logout()
    
    @pytest.mark.e2e
    def test_drive_test_data_export(self):
        """路测数据导出测试"""
        # 1. 登录系统
        assert self.login('analyst'), "分析师登录失败"
        
        # 2. 导航到路测分析页面
        self.navigate_to('/drive-test-analysis')
        
        # 3. 执行路测分析
        self._execute_drive_test_analysis()
        
        # 4. 导出分析结果
        self._export_analysis_results()
        
        # 5. 导出原始数据
        self._export_raw_data()
        
        # 6. 验证导出文件
        self._verify_exported_files(['analysis_results.csv', 'raw_data.zip'])
        
        # 7. 登出
        self.logout()
    
    @pytest.mark.e2e
    def test_site_data_export(self):
        """站点数据导出测试"""
        # 1. 登录系统
        assert self.login('analyst'), "分析师登录失败"
        
        # 2. 导航到站点管理页面
        self.navigate_to('/site-management')
        
        # 3. 筛选站点数据
        self._filter_site_data()
        
        # 4. 导出站点列表
        self._export_site_list()
        
        # 5. 导出站点详细信息
        self._export_site_details()
        
        # 6. 验证导出文件
        self._verify_exported_files(['site_list.xlsx', 'site_details.json'])
        
        # 7. 登出
        self.logout()
    
    @pytest.mark.e2e
    def test_batch_export(self):
        """批量导出测试"""
        # 1. 登录系统
        assert self.login('admin'), "管理员登录失败"
        
        # 2. 导航到数据管理页面
        self.navigate_to('/data-management')
        
        # 3. 选择批量导出
        self._select_batch_export()
        
        # 4. 配置导出参数
        self._configure_batch_export()
        
        # 5. 启动批量导出
        self._start_batch_export()
        
        # 6. 监控导出进度
        self._monitor_export_progress()
        
        # 7. 验证导出结果
        self._verify_batch_export_results()
        
        # 8. 登出
        self.logout()
    
    @pytest.mark.e2e
    def test_export_permissions(self):
        """导出权限控制测试"""
        # 1. 测试查看者权限
        self._test_viewer_export_permissions()
        
        # 2. 测试分析师权限
        self._test_analyst_export_permissions()
        
        # 3. 测试管理员权限
        self._test_admin_export_permissions()
    
    @pytest.mark.e2e
    def test_export_format_validation(self):
        """导出格式验证测试"""
        # 1. 登录系统
        assert self.login('analyst'), "分析师登录失败"
        
        # 2. 测试Excel导出格式
        self._test_excel_export_format()
        
        # 3. 测试CSV导出格式
        self._test_csv_export_format()
        
        # 4. 测试JSON导出格式
        self._test_json_export_format()
        
        # 5. 测试PDF导出格式
        self._test_pdf_export_format()
        
        # 6. 登出
        self.logout()
    
    def _generate_kpi_report(self):
        """生成KPI报告"""
        # 点击生成报告按钮
        generate_button = self.wait_for_element((By.ID, "generate-kpi-report-button"))
        generate_button.click()
        
        # 设置报告参数
        time_range_select = self.wait_for_element((By.ID, "report-time-range-select"))
        time_range_select.click()
        
        last_month_option = self.wait_for_element((By.XPATH, "//option[@value='last_month']"))
        last_month_option.click()
        
        # 选择KPI指标
        kpi_checkboxes = self.driver.find_elements(By.CLASS_NAME, "kpi-checkbox")
        for checkbox in kpi_checkboxes[:5]:  # 选择前5个KPI
            if not checkbox.is_selected():
                checkbox.click()
        
        # 确认生成
        confirm_button = self.driver.find_element(By.ID, "confirm-generate-button")
        confirm_button.click()
        
        # 等待报告生成完成
        self.wait_for_element((By.ID, "report-ready-indicator"), timeout=120)
    
    def _export_report(self, format_type):
        """导出报告"""
        # 点击导出按钮
        export_button = self.wait_for_element((By.ID, "export-report-button"))
        export_button.click()
        
        # 选择导出格式
        format_select = self.wait_for_element((By.ID, "export-format-select"))
        format_select.click()
        
        format_option = self.wait_for_element((By.XPATH, f"//option[@value='{format_type}']"))
        format_option.click()
        
        # 确认导出
        confirm_export_button = self.driver.find_element(By.ID, "confirm-export-button")
        confirm_export_button.click()
        
        # 等待导出完成
        download_link = self.wait_for_element((By.ID, "download-link"), timeout=60)
        
        # 点击下载链接
        download_link.click()
        time.sleep(5)  # 等待下载完成
    
    def _execute_drive_test_analysis(self):
        """执行路测分析"""
        # 选择分析数据
        data_select = self.wait_for_element((By.ID, "analysis-data-select"))
        data_select.click()
        
        sample_data_option = self.wait_for_element((By.XPATH, "//option[@value='sample_data']"))
        sample_data_option.click()
        
        # 设置分析参数
        analysis_type_select = self.driver.find_element(By.ID, "analysis-type-select")
        analysis_type_select.click()
        
        coverage_option = self.wait_for_element((By.XPATH, "//option[@value='coverage_analysis']"))
        coverage_option.click()
        
        # 启动分析
        start_analysis_button = self.driver.find_element(By.ID, "start-analysis-button")
        start_analysis_button.click()
        
        # 等待分析完成
        self.wait_for_element((By.ID, "analysis-complete-indicator"), timeout=180)
    
    def _export_analysis_results(self):
        """导出分析结果"""
        # 切换到结果标签页
        results_tab = self.wait_for_element((By.ID, "results-tab"))
        results_tab.click()
        
        # 点击导出结果按钮
        export_results_button = self.wait_for_element((By.ID, "export-results-button"))
        export_results_button.click()
        
        # 选择CSV格式
        csv_format_radio = self.wait_for_element((By.ID, "csv-format-radio"))
        csv_format_radio.click()
        
        # 确认导出
        confirm_export_button = self.driver.find_element(By.ID, "confirm-results-export-button")
        confirm_export_button.click()
        
        # 等待下载
        time.sleep(10)
    
    def _export_raw_data(self):
        """导出原始数据"""
        # 点击导出原始数据按钮
        export_raw_button = self.wait_for_element((By.ID, "export-raw-data-button"))
        export_raw_button.click()
        
        # 选择压缩格式
        zip_format_radio = self.wait_for_element((By.ID, "zip-format-radio"))
        zip_format_radio.click()
        
        # 确认导出
        confirm_raw_export_button = self.driver.find_element(By.ID, "confirm-raw-export-button")
        confirm_raw_export_button.click()
        
        # 等待下载
        time.sleep(15)
    
    def _filter_site_data(self):
        """筛选站点数据"""
        # 设置筛选条件
        region_filter = self.wait_for_element((By.ID, "region-filter-select"))
        region_filter.click()
        
        beijing_option = self.wait_for_element((By.XPATH, "//option[@value='beijing']"))
        beijing_option.click()
        
        # 设置站点类型筛选
        site_type_filter = self.driver.find_element(By.ID, "site-type-filter-select")
        site_type_filter.click()
        
        macro_option = self.wait_for_element((By.XPATH, "//option[@value='macro']"))
        macro_option.click()
        
        # 应用筛选
        apply_filter_button = self.driver.find_element(By.ID, "apply-site-filter-button")
        apply_filter_button.click()
        
        # 等待筛选结果
        self.wait_for_element((By.CLASS_NAME, "site-list-item"))
    
    def _export_site_list(self):
        """导出站点列表"""
        # 点击导出站点列表按钮
        export_list_button = self.wait_for_element((By.ID, "export-site-list-button"))
        export_list_button.click()
        
        # 选择Excel格式
        excel_format_radio = self.wait_for_element((By.ID, "excel-format-radio"))
        excel_format_radio.click()
        
        # 确认导出
        confirm_list_export_button = self.driver.find_element(By.ID, "confirm-list-export-button")
        confirm_list_export_button.click()
        
        # 等待下载
        time.sleep(8)
    
    def _export_site_details(self):
        """导出站点详细信息"""
        # 点击导出详细信息按钮
        export_details_button = self.wait_for_element((By.ID, "export-site-details-button"))
        export_details_button.click()
        
        # 选择JSON格式
        json_format_radio = self.wait_for_element((By.ID, "json-format-radio"))
        json_format_radio.click()
        
        # 确认导出
        confirm_details_export_button = self.driver.find_element(By.ID, "confirm-details-export-button")
        confirm_details_export_button.click()
        
        # 等待下载
        time.sleep(8)
    
    def _select_batch_export(self):
        """选择批量导出"""
        # 点击批量导出标签
        batch_export_tab = self.wait_for_element((By.ID, "batch-export-tab"))
        batch_export_tab.click()
        
        # 选择导出类型
        export_type_select = self.wait_for_element((By.ID, "batch-export-type-select"))
        export_type_select.click()
        
        all_data_option = self.wait_for_element((By.XPATH, "//option[@value='all_data']"))
        all_data_option.click()
    
    def _configure_batch_export(self):
        """配置批量导出参数"""
        # 设置时间范围
        start_date_input = self.wait_for_element((By.ID, "batch-start-date-input"))
        start_date_input.clear()
        start_date_input.send_keys((datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d'))
        
        end_date_input = self.driver.find_element(By.ID, "batch-end-date-input")
        end_date_input.clear()
        end_date_input.send_keys(datetime.now().strftime('%Y-%m-%d'))
        
        # 选择数据表
        table_checkboxes = self.driver.find_elements(By.CLASS_NAME, "batch-table-checkbox")
        for checkbox in table_checkboxes[:3]:  # 选择前3个表
            if not checkbox.is_selected():
                checkbox.click()
        
        # 设置导出格式
        format_select = self.driver.find_element(By.ID, "batch-format-select")
        format_select.click()
        
        csv_option = self.wait_for_element((By.XPATH, "//option[@value='csv']"))
        csv_option.click()
    
    def _start_batch_export(self):
        """启动批量导出"""
        # 点击开始导出按钮
        start_export_button = self.wait_for_element((By.ID, "start-batch-export-button"))
        start_export_button.click()
        
        # 确认导出
        confirm_batch_button = self.wait_for_element((By.ID, "confirm-batch-export-button"))
        confirm_batch_button.click()
    
    def _monitor_export_progress(self):
        """监控导出进度"""
        # 等待进度条出现
        progress_bar = self.wait_for_element((By.ID, "batch-export-progress-bar"))
        
        # 监控进度直到完成
        timeout = 300  # 5分钟超时
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查是否完成
                complete_indicator = self.driver.find_element(By.ID, "batch-export-complete-indicator")
                if complete_indicator.is_displayed():
                    break
            except NoSuchElementException:
                pass
            
            time.sleep(5)
        else:
            raise TimeoutException("批量导出超时")
    
    def _verify_batch_export_results(self):
        """验证批量导出结果"""
        # 检查导出摘要
        export_summary = self.wait_for_element((By.ID, "batch-export-summary"))
        summary_text = export_summary.text
        
        assert "导出完成" in summary_text
        assert "文件数量" in summary_text
        
        # 下载导出文件
        download_button = self.driver.find_element(By.ID, "download-batch-export-button")
        download_button.click()
        
        # 等待下载完成
        time.sleep(20)
    
    def _test_viewer_export_permissions(self):
        """测试查看者导出权限"""
        # 查看者登录
        assert self.login('viewer'), "查看者登录失败"
        
        # 尝试访问导出功能
        self.navigate_to('/kpi-monitoring')
        
        # 验证导出按钮不可用或不存在
        try:
            export_button = self.driver.find_element(By.ID, "export-report-button")
            assert not export_button.is_enabled(), "查看者不应该有导出权限"
        except NoSuchElementException:
            # 按钮不存在是正确的
            pass
        
        self.logout()
    
    def _test_analyst_export_permissions(self):
        """测试分析师导出权限"""
        # 分析师登录
        assert self.login('analyst'), "分析师登录失败"
        
        # 验证可以导出分析结果
        self.navigate_to('/drive-test-analysis')
        
        try:
            export_button = self.driver.find_element(By.ID, "export-results-button")
            assert export_button.is_displayed(), "分析师应该有导出分析结果的权限"
        except NoSuchElementException:
            # 如果按钮不存在，可能需要先执行分析
            pass
        
        # 验证不能访问批量导出
        self.navigate_to('/data-management')
        
        try:
            batch_export_tab = self.driver.find_element(By.ID, "batch-export-tab")
            assert not batch_export_tab.is_displayed(), "分析师不应该有批量导出权限"
        except NoSuchElementException:
            # 标签不存在是正确的
            pass
        
        self.logout()
    
    def _test_admin_export_permissions(self):
        """测试管理员导出权限"""
        # 管理员登录
        assert self.login('admin'), "管理员登录失败"
        
        # 验证可以访问所有导出功能
        self.navigate_to('/data-management')
        
        batch_export_tab = self.wait_for_element((By.ID, "batch-export-tab"))
        assert batch_export_tab.is_displayed(), "管理员应该有批量导出权限"
        
        self.logout()
    
    def _test_excel_export_format(self):
        """测试Excel导出格式"""
        self.navigate_to('/kpi-monitoring')
        
        # 生成并导出Excel报告
        self._generate_kpi_report()
        self._export_report('excel')
        
        # 验证Excel文件
        excel_files = [f for f in os.listdir(self.download_dir) if f.endswith('.xlsx')]
        assert len(excel_files) > 0, "Excel文件未生成"
        
        # 可以添加更多Excel格式验证
    
    def _test_csv_export_format(self):
        """测试CSV导出格式"""
        self.navigate_to('/drive-test-analysis')
        
        # 执行分析并导出CSV
        self._execute_drive_test_analysis()
        self._export_analysis_results()
        
        # 验证CSV文件
        csv_files = [f for f in os.listdir(self.download_dir) if f.endswith('.csv')]
        assert len(csv_files) > 0, "CSV文件未生成"
    
    def _test_json_export_format(self):
        """测试JSON导出格式"""
        self.navigate_to('/site-management')
        
        # 筛选并导出JSON
        self._filter_site_data()
        self._export_site_details()
        
        # 验证JSON文件
        json_files = [f for f in os.listdir(self.download_dir) if f.endswith('.json')]
        assert len(json_files) > 0, "JSON文件未生成"
        
        # 验证JSON格式
        if json_files:
            with open(os.path.join(self.download_dir, json_files[0]), 'r', encoding='utf-8') as f:
                try:
                    json.load(f)
                except json.JSONDecodeError:
                    assert False, "JSON格式无效"
    
    def _test_pdf_export_format(self):
        """测试PDF导出格式"""
        self.navigate_to('/kpi-monitoring')
        
        # 生成并导出PDF报告
        self._generate_kpi_report()
        self._export_report('pdf')
        
        # 验证PDF文件
        pdf_files = [f for f in os.listdir(self.download_dir) if f.endswith('.pdf')]
        assert len(pdf_files) > 0, "PDF文件未生成"
    
    def _verify_exported_files(self, expected_files):
        """验证导出的文件"""
        # 等待文件下载完成
        time.sleep(10)
        
        downloaded_files = os.listdir(self.download_dir)
        
        for expected_file in expected_files:
            # 检查文件是否存在（可能有时间戳后缀）
            matching_files = [f for f in downloaded_files if expected_file.split('.')[0] in f]
            assert len(matching_files) > 0, f"期望的文件 {expected_file} 未找到"
            
            # 验证文件大小
            for file in matching_files:
                file_path = os.path.join(self.download_dir, file)
                file_size = os.path.getsize(file_path)
                assert file_size > 0, f"文件 {file} 大小为0"


if __name__ == "__main__":
    # 运行数据导出E2E测试
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "--maxfail=3",
        "--durations=10"
    ])