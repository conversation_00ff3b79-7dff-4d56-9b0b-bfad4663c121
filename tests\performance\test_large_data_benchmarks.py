#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信平台 - 大数据量性能基准测试

本文件包含大数据量处理的性能基准测试，用于验证系统在处理
大规模电信数据时的性能表现，确保满足PRD中定义的性能指标。

性能目标:
- 500万行数据处理 < 10秒
- 地理查询响应 < 3秒
- 支持20用户并发
- 内存使用 < 16GB
- CPU使用率 < 80%

作者: Connect质量工程师
创建时间: 2024-12-19
"""

import asyncio
import time
import psutil
import pytest
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import Dict, List, Any, Callable
import logging
import gc
import threading
from pathlib import Path
import json
import sqlite3
from contextlib import contextmanager

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    test_name: str
    execution_time: float
    memory_usage_mb: float
    cpu_usage_percent: float
    throughput_records_per_sec: float
    peak_memory_mb: float
    success: bool
    error_message: str = ""
    additional_metrics: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_metrics is None:
            self.additional_metrics = {}


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.start_time = None
        self.start_memory = None
        self.start_cpu = None
        self.peak_memory = 0
        self.monitoring = False
        self.monitor_thread = None
        self.metrics_history = []
    
    def start_monitoring(self):
        """开始性能监控"""
        self.start_time = time.time()
        self.start_memory = psutil.virtual_memory().used / 1024 / 1024  # MB
        self.start_cpu = psutil.cpu_percent()
        self.peak_memory = self.start_memory
        self.monitoring = True
        self.metrics_history = []
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_resources)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self) -> Dict[str, float]:
        """停止性能监控并返回指标"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        
        end_time = time.time()
        end_memory = psutil.virtual_memory().used / 1024 / 1024  # MB
        end_cpu = psutil.cpu_percent()
        
        return {
            'execution_time': end_time - self.start_time,
            'memory_usage_mb': end_memory - self.start_memory,
            'peak_memory_mb': self.peak_memory,
            'cpu_usage_percent': end_cpu,
            'avg_cpu_percent': np.mean([m['cpu'] for m in self.metrics_history]) if self.metrics_history else end_cpu
        }
    
    def _monitor_resources(self):
        """监控资源使用情况"""
        while self.monitoring:
            try:
                current_memory = psutil.virtual_memory().used / 1024 / 1024
                current_cpu = psutil.cpu_percent()
                
                self.peak_memory = max(self.peak_memory, current_memory)
                self.metrics_history.append({
                    'timestamp': time.time(),
                    'memory': current_memory,
                    'cpu': current_cpu
                })
                
                time.sleep(0.1)  # 每100ms采样一次
            except Exception as e:
                logger.error(f"资源监控异常: {e}")
                break


class LargeDataBenchmarks:
    """大数据量性能基准测试类"""
    
    def __init__(self):
        self.monitor = PerformanceMonitor()
        self.test_results = []
        self.temp_files = []
    
    def cleanup(self):
        """清理测试资源"""
        for file_path in self.temp_files:
            try:
                Path(file_path).unlink(missing_ok=True)
            except Exception as e:
                logger.warning(f"清理临时文件失败 {file_path}: {e}")
        self.temp_files.clear()
        gc.collect()
    
    @contextmanager
    def performance_test(self, test_name: str, record_count: int = 0):
        """性能测试上下文管理器"""
        self.monitor.start_monitoring()
        start_time = time.time()
        success = True
        error_message = ""
        
        try:
            yield
        except Exception as e:
            success = False
            error_message = str(e)
            logger.error(f"性能测试失败 {test_name}: {e}")
            raise
        finally:
            metrics = self.monitor.stop_monitoring()
            
            # 计算吞吐量
            throughput = record_count / metrics['execution_time'] if metrics['execution_time'] > 0 and record_count > 0 else 0
            
            result = PerformanceMetrics(
                test_name=test_name,
                execution_time=metrics['execution_time'],
                memory_usage_mb=metrics['memory_usage_mb'],
                cpu_usage_percent=metrics.get('avg_cpu_percent', metrics['cpu_usage_percent']),
                throughput_records_per_sec=throughput,
                peak_memory_mb=metrics['peak_memory_mb'],
                success=success,
                error_message=error_message
            )
            
            self.test_results.append(result)
            logger.info(f"性能测试完成 {test_name}: {metrics['execution_time']:.2f}s, {throughput:.0f} records/s")
    
    # ==================== 数据生成测试 ====================
    
    def test_large_data_generation(self, record_count: int = 5_000_000):
        """测试大数据量生成性能"""
        with self.performance_test(f"大数据生成_{record_count}条", record_count):
            # 生成大量EP数据
            np.random.seed(42)
            
            data = {
                'site_id': [f'SITE_{i:08d}' for i in range(record_count)],
                'cell_id': [f'CELL_{i:08d}' for i in range(record_count)],
                'frequency': np.random.choice([900, 1800, 2100, 2600], record_count),
                'power': np.random.uniform(10, 50, record_count),
                'azimuth': np.random.uniform(0, 360, record_count),
                'latitude': np.random.uniform(20, 50, record_count),
                'longitude': np.random.uniform(80, 130, record_count),
                'timestamp': pd.date_range('2024-01-01', periods=record_count, freq='1s')
            }
            
            df = pd.DataFrame(data)
            
            # 验证数据完整性
            assert len(df) == record_count
            assert df.isnull().sum().sum() == 0
            
            return df
    
    def test_large_data_processing(self, df: pd.DataFrame):
        """测试大数据量处理性能"""
        record_count = len(df)
        
        with self.performance_test(f"大数据处理_{record_count}条", record_count):
            # 数据清洗
            df_clean = df.dropna()
            
            # 数据转换
            df_clean['power_db'] = 10 * np.log10(df_clean['power'])
            df_clean['distance_km'] = np.sqrt(
                (df_clean['latitude'] - df_clean['latitude'].mean()) ** 2 +
                (df_clean['longitude'] - df_clean['longitude'].mean()) ** 2
            ) * 111  # 粗略转换为公里
            
            # 数据聚合
            agg_result = df_clean.groupby(['frequency', 'site_id']).agg({
                'power': ['mean', 'max', 'min'],
                'azimuth': 'mean',
                'distance_km': 'mean'
            }).reset_index()
            
            # 数据排序
            df_sorted = df_clean.sort_values(['frequency', 'power'], ascending=[True, False])
            
            return df_sorted, agg_result
    
    def test_large_data_io(self, df: pd.DataFrame, file_format: str = 'csv'):
        """测试大数据量I/O性能"""
        record_count = len(df)
        temp_file = f"temp_large_data_{record_count}.{file_format}"
        self.temp_files.append(temp_file)
        
        # 写入测试
        with self.performance_test(f"大数据写入_{record_count}条_{file_format}", record_count):
            if file_format == 'csv':
                df.to_csv(temp_file, index=False)
            elif file_format == 'parquet':
                df.to_parquet(temp_file, index=False)
            elif file_format == 'json':
                df.to_json(temp_file, orient='records')
        
        # 读取测试
        with self.performance_test(f"大数据读取_{record_count}条_{file_format}", record_count):
            if file_format == 'csv':
                df_read = pd.read_csv(temp_file)
            elif file_format == 'parquet':
                df_read = pd.read_parquet(temp_file)
            elif file_format == 'json':
                df_read = pd.read_json(temp_file, orient='records')
        
        # 验证数据完整性
        assert len(df_read) == record_count
        
        return df_read
    
    # ==================== 数据库性能测试 ====================
    
    def test_database_bulk_insert(self, df: pd.DataFrame, batch_size: int = 10000):
        """测试数据库批量插入性能"""
        record_count = len(df)
        db_file = f"temp_test_db_{record_count}.sqlite"
        self.temp_files.append(db_file)
        
        with self.performance_test(f"数据库批量插入_{record_count}条", record_count):
            conn = sqlite3.connect(db_file)
            
            # 创建表
            create_table_sql = """
            CREATE TABLE ep_data (
                site_id TEXT,
                cell_id TEXT,
                frequency INTEGER,
                power REAL,
                azimuth REAL,
                latitude REAL,
                longitude REAL,
                timestamp TEXT
            )
            """
            conn.execute(create_table_sql)
            
            # 批量插入
            for i in range(0, len(df), batch_size):
                batch = df.iloc[i:i+batch_size]
                batch.to_sql('ep_data', conn, if_exists='append', index=False)
            
            conn.commit()
            conn.close()
    
    def test_database_query_performance(self, record_count: int = 5_000_000):
        """测试数据库查询性能"""
        db_file = f"temp_test_db_{record_count}.sqlite"
        
        if not Path(db_file).exists():
            logger.warning(f"数据库文件不存在: {db_file}")
            return
        
        with self.performance_test(f"数据库查询_{record_count}条", record_count):
            conn = sqlite3.connect(db_file)
            
            # 简单查询
            result1 = pd.read_sql_query(
                "SELECT COUNT(*) as total FROM ep_data", conn
            )
            
            # 聚合查询
            result2 = pd.read_sql_query(
                "SELECT frequency, AVG(power) as avg_power, COUNT(*) as count FROM ep_data GROUP BY frequency", conn
            )
            
            # 地理范围查询
            result3 = pd.read_sql_query(
                "SELECT * FROM ep_data WHERE latitude BETWEEN 30 AND 40 AND longitude BETWEEN 100 AND 120 LIMIT 1000", conn
            )
            
            conn.close()
            
            return result1, result2, result3
    
    # ==================== 并发性能测试 ====================
    
    def test_concurrent_data_processing(self, df: pd.DataFrame, num_workers: int = 20):
        """测试并发数据处理性能"""
        record_count = len(df)
        chunk_size = len(df) // num_workers
        
        with self.performance_test(f"并发数据处理_{num_workers}线程_{record_count}条", record_count):
            def process_chunk(chunk_data):
                """处理数据块"""
                chunk_df = chunk_data.copy()
                
                # 模拟复杂计算
                chunk_df['power_normalized'] = (chunk_df['power'] - chunk_df['power'].mean()) / chunk_df['power'].std()
                chunk_df['distance_from_center'] = np.sqrt(
                    (chunk_df['latitude'] - chunk_df['latitude'].mean()) ** 2 +
                    (chunk_df['longitude'] - chunk_df['longitude'].mean()) ** 2
                )
                
                # 模拟地理空间计算
                chunk_df['coverage_area'] = chunk_df['power'] * np.pi * (chunk_df['distance_from_center'] ** 2)
                
                return len(chunk_df)
            
            # 分割数据
            chunks = [df.iloc[i:i+chunk_size] for i in range(0, len(df), chunk_size)]
            
            # 并发处理
            with ThreadPoolExecutor(max_workers=num_workers) as executor:
                futures = [executor.submit(process_chunk, chunk) for chunk in chunks]
                
                total_processed = 0
                for future in as_completed(futures):
                    total_processed += future.result()
            
            assert total_processed >= record_count * 0.95  # 允许5%的误差
    
    async def test_async_data_processing(self, df: pd.DataFrame, num_tasks: int = 20):
        """测试异步数据处理性能"""
        record_count = len(df)
        chunk_size = len(df) // num_tasks
        
        with self.performance_test(f"异步数据处理_{num_tasks}任务_{record_count}条", record_count):
            async def process_chunk_async(chunk_data):
                """异步处理数据块"""
                await asyncio.sleep(0.01)  # 模拟异步I/O
                
                chunk_df = chunk_data.copy()
                
                # 模拟异步计算
                chunk_df['signal_strength'] = chunk_df['power'] * np.random.uniform(0.8, 1.2, len(chunk_df))
                chunk_df['quality_score'] = (
                    chunk_df['signal_strength'] * 0.6 +
                    (360 - abs(chunk_df['azimuth'] - 180)) / 360 * 0.4
                )
                
                return len(chunk_df)
            
            # 分割数据
            chunks = [df.iloc[i:i+chunk_size] for i in range(0, len(df), chunk_size)]
            
            # 异步处理
            tasks = [process_chunk_async(chunk) for chunk in chunks]
            results = await asyncio.gather(*tasks)
            
            total_processed = sum(results)
            assert total_processed >= record_count * 0.95
    
    # ==================== 内存优化测试 ====================
    
    def test_memory_efficient_processing(self, record_count: int = 5_000_000, chunk_size: int = 100_000):
        """测试内存高效处理"""
        with self.performance_test(f"内存高效处理_{record_count}条", record_count):
            total_processed = 0
            
            # 分块处理大数据
            for i in range(0, record_count, chunk_size):
                current_chunk_size = min(chunk_size, record_count - i)
                
                # 生成数据块
                chunk_data = {
                    'site_id': [f'SITE_{j:08d}' for j in range(i, i + current_chunk_size)],
                    'power': np.random.uniform(10, 50, current_chunk_size),
                    'frequency': np.random.choice([900, 1800, 2100, 2600], current_chunk_size)
                }
                
                chunk_df = pd.DataFrame(chunk_data)
                
                # 处理数据块
                chunk_df['power_category'] = pd.cut(chunk_df['power'], bins=5, labels=['很低', '低', '中', '高', '很高'])
                result = chunk_df.groupby(['frequency', 'power_category']).size().reset_index(name='count')
                
                total_processed += len(chunk_df)
                
                # 清理内存
                del chunk_df, chunk_data, result
                gc.collect()
            
            assert total_processed == record_count
    
    # ==================== 地理空间性能测试 ====================
    
    def test_geospatial_query_performance(self, df: pd.DataFrame):
        """测试地理空间查询性能"""
        record_count = len(df)
        
        with self.performance_test(f"地理空间查询_{record_count}条", record_count):
            # 定义查询区域
            center_lat, center_lon = 35.0, 110.0
            radius_km = 50
            
            # 计算距离（使用简化的球面距离公式）
            df['distance_km'] = np.sqrt(
                ((df['latitude'] - center_lat) * 111) ** 2 +
                ((df['longitude'] - center_lon) * 111 * np.cos(np.radians(center_lat))) ** 2
            )
            
            # 范围查询
            nearby_sites = df[df['distance_km'] <= radius_km]
            
            # 最近邻查询
            nearest_sites = df.nsmallest(100, 'distance_km')
            
            # 热力图数据生成
            lat_bins = np.linspace(df['latitude'].min(), df['latitude'].max(), 50)
            lon_bins = np.linspace(df['longitude'].min(), df['longitude'].max(), 50)
            
            heatmap_data = np.zeros((len(lat_bins)-1, len(lon_bins)-1))
            
            for i in range(len(lat_bins)-1):
                for j in range(len(lon_bins)-1):
                    mask = (
                        (df['latitude'] >= lat_bins[i]) & (df['latitude'] < lat_bins[i+1]) &
                        (df['longitude'] >= lon_bins[j]) & (df['longitude'] < lon_bins[j+1])
                    )
                    heatmap_data[i, j] = df[mask]['power'].mean() if mask.any() else 0
            
            return nearby_sites, nearest_sites, heatmap_data
    
    # ==================== 报告生成 ====================
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能测试报告"""
        if not self.test_results:
            return {'error': '没有测试结果'}
        
        report = {
            'summary': {
                'total_tests': len(self.test_results),
                'passed_tests': sum(1 for r in self.test_results if r.success),
                'failed_tests': sum(1 for r in self.test_results if not r.success),
                'total_execution_time': sum(r.execution_time for r in self.test_results),
                'avg_execution_time': np.mean([r.execution_time for r in self.test_results]),
                'max_memory_usage': max(r.peak_memory_mb for r in self.test_results),
                'avg_cpu_usage': np.mean([r.cpu_usage_percent for r in self.test_results])
            },
            'performance_targets': {
                'data_processing_5m_records': {'target': 10.0, 'unit': 'seconds'},
                'geo_query_response': {'target': 3.0, 'unit': 'seconds'},
                'concurrent_users': {'target': 20, 'unit': 'users'},
                'memory_usage': {'target': 16384, 'unit': 'MB'},
                'cpu_usage': {'target': 80, 'unit': 'percent'}
            },
            'test_results': [],
            'recommendations': []
        }
        
        # 详细测试结果
        for result in self.test_results:
            test_detail = {
                'test_name': result.test_name,
                'execution_time': result.execution_time,
                'memory_usage_mb': result.memory_usage_mb,
                'peak_memory_mb': result.peak_memory_mb,
                'cpu_usage_percent': result.cpu_usage_percent,
                'throughput_records_per_sec': result.throughput_records_per_sec,
                'success': result.success,
                'error_message': result.error_message,
                'performance_grade': self._calculate_performance_grade(result)
            }
            report['test_results'].append(test_detail)
        
        # 生成建议
        report['recommendations'] = self._generate_recommendations()
        
        return report
    
    def _calculate_performance_grade(self, result: PerformanceMetrics) -> str:
        """计算性能等级"""
        if not result.success:
            return 'F'
        
        score = 0
        
        # 执行时间评分（权重40%）
        if '5000000' in result.test_name and result.execution_time <= 10:
            score += 40
        elif result.execution_time <= 3:
            score += 40
        elif result.execution_time <= 5:
            score += 30
        elif result.execution_time <= 10:
            score += 20
        
        # 内存使用评分（权重30%）
        if result.peak_memory_mb <= 8192:  # 8GB
            score += 30
        elif result.peak_memory_mb <= 12288:  # 12GB
            score += 20
        elif result.peak_memory_mb <= 16384:  # 16GB
            score += 10
        
        # CPU使用评分（权重30%）
        if result.cpu_usage_percent <= 50:
            score += 30
        elif result.cpu_usage_percent <= 70:
            score += 20
        elif result.cpu_usage_percent <= 80:
            score += 10
        
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'
    
    def _generate_recommendations(self) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        # 分析内存使用
        max_memory = max(r.peak_memory_mb for r in self.test_results if r.success)
        if max_memory > 16384:  # 16GB
            recommendations.append("内存使用超过16GB限制，建议优化数据处理算法或增加分块处理")
        
        # 分析CPU使用
        avg_cpu = np.mean([r.cpu_usage_percent for r in self.test_results if r.success])
        if avg_cpu > 80:
            recommendations.append("CPU使用率过高，建议优化计算密集型操作或增加并行处理")
        
        # 分析执行时间
        slow_tests = [r for r in self.test_results if r.success and '5000000' in r.test_name and r.execution_time > 10]
        if slow_tests:
            recommendations.append("大数据处理时间超过10秒目标，建议优化数据处理流程")
        
        # 分析吞吐量
        low_throughput_tests = [r for r in self.test_results if r.success and r.throughput_records_per_sec < 100000]
        if low_throughput_tests:
            recommendations.append("数据处理吞吐量较低，建议优化I/O操作和数据结构")
        
        # 分析失败测试
        failed_tests = [r for r in self.test_results if not r.success]
        if failed_tests:
            recommendations.append(f"有{len(failed_tests)}个测试失败，需要修复相关问题")
        
        if not recommendations:
            recommendations.append("所有性能测试均达到预期目标，系统性能良好")
        
        return recommendations
    
    def save_report(self, filename: str = "performance_benchmark_report.json"):
        """保存性能报告到文件"""
        report = self.generate_performance_report()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"性能报告已保存到: {filename}")
        return filename


# ==================== pytest测试用例 ====================

class TestLargeDataBenchmarks:
    """大数据量性能基准测试用例"""
    
    @pytest.fixture(scope="class")
    def benchmark_suite(self):
        """测试套件fixture"""
        suite = LargeDataBenchmarks()
        yield suite
        suite.cleanup()
    
    @pytest.mark.performance
    @pytest.mark.slow
    def test_5m_records_processing_benchmark(self, benchmark_suite):
        """500万条记录处理性能基准测试"""
        # 生成数据
        df = benchmark_suite.test_large_data_generation(5_000_000)
        
        # 处理数据
        df_processed, agg_result = benchmark_suite.test_large_data_processing(df)
        
        # 验证性能目标
        last_result = benchmark_suite.test_results[-1]
        assert last_result.execution_time <= 10.0, f"处理时间{last_result.execution_time:.2f}s超过10s目标"
        assert last_result.peak_memory_mb <= 16384, f"内存使用{last_result.peak_memory_mb:.0f}MB超过16GB限制"
    
    @pytest.mark.performance
    def test_concurrent_processing_benchmark(self, benchmark_suite):
        """并发处理性能基准测试"""
        # 生成测试数据
        df = benchmark_suite.test_large_data_generation(1_000_000)
        
        # 并发处理测试
        benchmark_suite.test_concurrent_data_processing(df, num_workers=20)
        
        # 验证并发性能
        last_result = benchmark_suite.test_results[-1]
        assert last_result.success, "并发处理测试失败"
        assert last_result.cpu_usage_percent <= 80, f"CPU使用率{last_result.cpu_usage_percent:.1f}%超过80%限制"
    
    @pytest.mark.performance
    def test_geospatial_query_benchmark(self, benchmark_suite):
        """地理空间查询性能基准测试"""
        # 生成地理数据
        df = benchmark_suite.test_large_data_generation(1_000_000)
        
        # 地理空间查询测试
        nearby, nearest, heatmap = benchmark_suite.test_geospatial_query_performance(df)
        
        # 验证查询性能
        last_result = benchmark_suite.test_results[-1]
        assert last_result.execution_time <= 3.0, f"地理查询时间{last_result.execution_time:.2f}s超过3s目标"
        assert len(nearby) > 0, "地理范围查询应返回结果"
        assert len(nearest) == 100, "最近邻查询应返回100个结果"
    
    @pytest.mark.performance
    def test_memory_efficiency_benchmark(self, benchmark_suite):
        """内存效率性能基准测试"""
        # 内存高效处理测试
        benchmark_suite.test_memory_efficient_processing(5_000_000, chunk_size=50_000)
        
        # 验证内存效率
        last_result = benchmark_suite.test_results[-1]
        assert last_result.success, "内存高效处理测试失败"
        assert last_result.peak_memory_mb <= 4096, f"内存使用{last_result.peak_memory_mb:.0f}MB超过4GB预期"
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_async_processing_benchmark(self, benchmark_suite):
        """异步处理性能基准测试"""
        # 生成测试数据
        df = benchmark_suite.test_large_data_generation(500_000)
        
        # 异步处理测试
        await benchmark_suite.test_async_data_processing(df, num_tasks=20)
        
        # 验证异步性能
        last_result = benchmark_suite.test_results[-1]
        assert last_result.success, "异步处理测试失败"
        assert last_result.throughput_records_per_sec > 10000, "异步处理吞吐量过低"
    
    @pytest.mark.performance
    def test_io_performance_benchmark(self, benchmark_suite):
        """I/O性能基准测试"""
        # 生成测试数据
        df = benchmark_suite.test_large_data_generation(1_000_000)
        
        # 测试不同格式的I/O性能
        for file_format in ['csv', 'parquet']:
            df_read = benchmark_suite.test_large_data_io(df, file_format)
            assert len(df_read) == len(df), f"{file_format}格式I/O测试数据完整性验证失败"
    
    @pytest.mark.performance
    def test_database_performance_benchmark(self, benchmark_suite):
        """数据库性能基准测试"""
        # 生成测试数据
        df = benchmark_suite.test_large_data_generation(500_000)
        
        # 数据库插入测试
        benchmark_suite.test_database_bulk_insert(df, batch_size=10000)
        
        # 数据库查询测试
        results = benchmark_suite.test_database_query_performance(500_000)
        
        # 验证数据库性能
        insert_result = benchmark_suite.test_results[-2]
        query_result = benchmark_suite.test_results[-1]
        
        assert insert_result.success, "数据库插入测试失败"
        assert query_result.success, "数据库查询测试失败"
        assert query_result.execution_time <= 5.0, "数据库查询时间过长"


if __name__ == "__main__":
    # 运行性能基准测试
    benchmark = LargeDataBenchmarks()
    
    try:
        # 执行主要性能测试
        print("开始大数据量性能基准测试...")
        
        # 1. 数据生成和处理测试
        df = benchmark.test_large_data_generation(1_000_000)
        df_processed, agg_result = benchmark.test_large_data_processing(df)
        
        # 2. I/O性能测试
        benchmark.test_large_data_io(df, 'csv')
        benchmark.test_large_data_io(df, 'parquet')
        
        # 3. 并发性能测试
        benchmark.test_concurrent_data_processing(df, num_workers=10)
        
        # 4. 地理空间查询测试
        benchmark.test_geospatial_query_performance(df)
        
        # 5. 内存效率测试
        benchmark.test_memory_efficient_processing(1_000_000, chunk_size=100_000)
        
        # 生成性能报告
        report_file = benchmark.save_report()
        print(f"\n性能测试完成，报告已保存到: {report_file}")
        
        # 打印摘要
        report = benchmark.generate_performance_report()
        print(f"\n性能测试摘要:")
        print(f"- 总测试数: {report['summary']['total_tests']}")
        print(f"- 通过测试: {report['summary']['passed_tests']}")
        print(f"- 失败测试: {report['summary']['failed_tests']}")
        print(f"- 总执行时间: {report['summary']['total_execution_time']:.2f}s")
        print(f"- 最大内存使用: {report['summary']['max_memory_usage']:.0f}MB")
        print(f"- 平均CPU使用: {report['summary']['avg_cpu_usage']:.1f}%")
        
        print("\n优化建议:")
        for rec in report['recommendations']:
            print(f"- {rec}")
    
    finally:
        benchmark.cleanup()