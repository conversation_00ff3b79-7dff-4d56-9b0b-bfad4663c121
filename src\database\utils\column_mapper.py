"""
Enhanced column mapping for EP data imports.

This module provides intelligent column mapping between DataFrame columns
and database table columns to handle case sensitivity and naming variations.
"""

import re
from typing import Dict, List, Set
import logging

logger = logging.getLogger(__name__)

class ColumnMapper:
    """Maps DataFrame columns to database table columns intelligently."""
    
    def __init__(self):
        self.mapping_cache = {}
    
    def normalize_column_name(self, column_name: str) -> str:
        """Normalize column name for comparison."""
        # Convert to lowercase and replace special characters
        normalized = column_name.lower()
        normalized = re.sub(r'[^a-z0-9]', '_', normalized)
        normalized = re.sub(r'_+', '_', normalized)
        normalized = normalized.strip('_')
        return normalized
    
    def create_column_mapping(self, df_columns: List[str], 
                            table_columns: List[str]) -> Dict[str, str]:
        """Create mapping between DataFrame and table columns.
        
        Args:
            df_columns: List of DataFrame column names
            table_columns: List of database table column names
            
        Returns:
            Dictionary mapping DataFrame columns to table columns
        """
        mapping = {}
        
        # Normalize all column names for comparison
        df_normalized = {self.normalize_column_name(col): col for col in df_columns}
        table_normalized = {self.normalize_column_name(col): col for col in table_columns}
        
        # Find exact matches first
        for norm_name, df_col in df_normalized.items():
            if norm_name in table_normalized:
                mapping[df_col] = table_normalized[norm_name]
        
        # Find partial matches for remaining columns
        unmapped_df = set(df_columns) - set(mapping.keys())
        unmapped_table = set(table_columns) - set(mapping.values())
        
        for df_col in unmapped_df:
            df_norm = self.normalize_column_name(df_col)
            best_match = None
            best_score = 0
            
            for table_col in unmapped_table:
                table_norm = self.normalize_column_name(table_col)
                
                # Calculate similarity score
                score = self._calculate_similarity(df_norm, table_norm)
                if score > best_score and score > 0.7:  # 70% similarity threshold
                    best_score = score
                    best_match = table_col
            
            if best_match:
                mapping[df_col] = best_match
        
        return mapping
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings."""
        if str1 == str2:
            return 1.0
        
        # Simple similarity based on common characters
        set1 = set(str1)
        set2 = set(str2)
        
        if not set1 or not set2:
            return 0.0
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def map_dataframe_columns(self, df, table_columns: List[str]) -> 'DataFrame':
        """Map DataFrame columns to match table columns.
        
        Args:
            df: pandas DataFrame
            table_columns: List of target table column names
            
        Returns:
            DataFrame with mapped column names
        """
        import pandas as pd
        
        # Create column mapping
        mapping = self.create_column_mapping(list(df.columns), table_columns)
        
        # Apply mapping
        df_mapped = df.copy()
        df_mapped = df_mapped.rename(columns=mapping)
        
        # Add missing columns with None values
        missing_columns = set(table_columns) - set(df_mapped.columns)
        for col in missing_columns:
            if col not in ['id', 'created_at', 'import_timestamp']:  # Skip auto-generated columns
                df_mapped[col] = None
        
        # Remove extra columns not in table
        extra_columns = set(df_mapped.columns) - set(table_columns)
        df_mapped = df_mapped.drop(columns=list(extra_columns))
        
        # Reorder columns to match table order
        df_mapped = df_mapped.reindex(columns=table_columns, fill_value=None)
        
        logger.info(f"Column mapping applied: {len(mapping)} mapped, {len(missing_columns)} added, {len(extra_columns)} removed")
        
        return df_mapped

# Global instance
_column_mapper = ColumnMapper()

def get_column_mapper() -> ColumnMapper:
    """Get the global column mapper instance."""
    return _column_mapper
