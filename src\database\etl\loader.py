# -*- coding: utf-8 -*-
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
Data Loader Module

This module provides functionality for loading data into PostgreSQL databases
with robust error handling, transaction management, and efficient COPY operations.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import asyncpg
import pandas as pd

from ..connection.pool import DatabasePoolManager as ConnectionPool
from ..etl.processors.csv_processor import read_csv_to_dataframe
from ..schema.manager import SchemaManager

# Setup logging
logger = logging.getLogger(__name__)


class DataLoader:
    """
    Data loader for PostgreSQL databases with transaction management and error handling.

    Features:
    - Efficient data loading using PostgreSQL COPY
    - Transaction management with rollback support
    - Batch processing for large datasets
    - Error handling and recovery
    - Schema validation and table creation
    """

    def __init__(self, connection_pool: ConnectionPool):
        """
        Initialize the data loader.

        Args:
            connection_pool: Database connection pool
        """
        self.connection_pool = connection_pool
        self.logger = logging.getLogger(self.__class__.__name__)
        self.schema_manager = SchemaManager(connection_pool)

    async def load_dataframe(
        self,
        df: pd.DataFrame,
        table_name: str,
        schema_name: str = "public",
        if_exists: str = "append",
        create_table: bool = True,
        batch_size: int = 10000,
        use_transaction: bool = True,
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Load a DataFrame into a PostgreSQL table.

        Args:
            df: DataFrame to load
            table_name: Target table name
            schema_name: Target schema name
            if_exists: Strategy when table exists ('fail', 'replace', 'append', 'skip')
            create_table: Whether to create table if it doesn't exist
            batch_size: Number of rows to process in each batch
            use_transaction: Whether to use transaction for the operation
            **kwargs: Additional parameters

        Returns:
            Dict[str, Any]: Loading results
        """
        if df.empty:
            self.logger.warning("DataFrame is empty, nothing to load")
            return {"success": True, "rows_loaded": 0, "message": "No data to load"}

        self.logger.info(f"Loading {len(df)} rows into {schema_name}.{table_name}")

        if use_transaction:
            return await self._load_with_transaction(
                df,
                table_name,
                schema_name,
                if_exists,
                create_table,
                batch_size,
                **kwargs,
            )
        else:
            return await self._load_without_transaction(
                df,
                table_name,
                schema_name,
                if_exists,
                create_table,
                batch_size,
                **kwargs,
            )

    async def load_from_file(
        self,
        file_path: Union[str, Path],
        table_name: str,
        schema_name: str = "public",
        **kwargs,
    ) -> Dict[str, Any]:
        """
        Load data from a file directly into a PostgreSQL table.

        Args:
            file_path: Path to the file to load
            table_name: Target table name
            schema_name: Target schema name
            **kwargs: Additional parameters for loading

        Returns:
            Dict[str, Any]: Loading results
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Load CSV/TSV files directly
        if file_path.suffix.lower() in [".csv", ".tsv"]:
            # Read CSV file to DataFrame
            df = await read_csv_to_dataframe(str(file_path))

            # Load DataFrame to table
            return await self.load_dataframe(
                df=df, table_name=table_name, schema_name=schema_name, **kwargs
            )
        else:
            raise ValueError(f"Unsupported file format: {file_path.suffix}")

    async def load_multiple_files(
        self, file_mappings: List[Dict[str, Any]], use_transaction: bool = True
    ) -> Dict[str, Any]:
        """
        Load multiple files into different tables.

        Args:
            file_mappings: List of file mapping dictionaries
            use_transaction: Whether to use a single transaction for all files

        Returns:
            Dict[str, Any]: Loading results for all files
        """
        if use_transaction:
            return await self._load_multiple_with_transaction(file_mappings)
        else:
            return await self._load_multiple_without_transaction(file_mappings)

    async def validate_load_requirements(
        self, df: pd.DataFrame, table_name: str, schema_name: str = "public"
    ) -> Dict[str, Any]:
        """
        Validate requirements before loading data.

        Args:
            df: DataFrame to validate
            table_name: Target table name
            schema_name: Target schema name

        Returns:
            Dict[str, Any]: Validation results
        """
        validation_results = {"valid": True, "errors": [], "warnings": []}

        # Check if DataFrame is empty
        if df.empty:
            validation_results["warnings"].append("DataFrame is empty")

        # Check for duplicate column names
        if len(df.columns) != len(set(df.columns)):
            validation_results["valid"] = False
            validation_results["errors"].append("DataFrame has duplicate column names")

        # Check schema existence
        try:
            schema_exists = await self.schema_manager.schema_exists(schema_name)
            if not schema_exists:
                validation_results["warnings"].append(
                    f"Schema '{schema_name}' does not exist"
                )
        except Exception as e:
            validation_results["errors"].append(
                f"Failed to check schema existence: {str(e)}"
            )

        # Check table existence and structure if it exists
        try:
            table_exists = await self.schema_manager.table_exists(
                table_name, schema_name
            )
            if table_exists:
                table_info = await self.schema_manager.get_table_info(
                    table_name, schema_name
                )
                table_columns = table_info["columns"]
                df_columns = set(df.columns)
                table_column_names = {col["column_name"] for col in table_columns}

                missing_columns = df_columns - table_column_names
                if missing_columns:
                    validation_results["warnings"].append(
                        f"DataFrame has columns not in table: {missing_columns}"
                    )
        except Exception as e:
            validation_results["errors"].append(
                f"Failed to check table structure: {str(e)}"
            )

        return validation_results

    async def _load_with_transaction(
        self,
        df: pd.DataFrame,
        table_name: str,
        schema_name: str,
        if_exists: str,
        create_table: bool,
        batch_size: int,
        **kwargs,
    ) -> Dict[str, Any]:
        """Load data with transaction management."""
        conn = await self.connection_pool.acquire_connection()
        try:
            async with conn.transaction():
                result = await self._perform_load(
                    conn,
                    df,
                    table_name,
                    schema_name,
                    if_exists,
                    create_table,
                    batch_size,
                    **kwargs,
                )
                self.logger.info(f"Transaction committed successfully for {table_name}")
                return result
        except Exception as e:
            self.logger.error(f"Transaction failed for {table_name}: {str(e)}")
            raise
        finally:
            await self.connection_pool.release_connection(conn)

    async def _load_without_transaction(
        self,
        df: pd.DataFrame,
        table_name: str,
        schema_name: str,
        if_exists: str,
        create_table: bool,
        batch_size: int,
        **kwargs,
    ) -> Dict[str, Any]:
        """Load data without transaction management."""
        conn = await self.connection_pool.acquire_connection()
        try:
            return await self._perform_load(
                conn,
                df,
                table_name,
                schema_name,
                if_exists,
                create_table,
                batch_size,
                **kwargs,
            )
        finally:
            await self.connection_pool.release_connection(conn)

    async def _perform_load(
        self,
        conn: asyncpg.Connection,
        df: pd.DataFrame,
        table_name: str,
        schema_name: str,
        if_exists: str,
        create_table: bool,
        batch_size: int,
        **kwargs,
    ) -> Dict[str, Any]:
        """Perform the actual data loading operation."""
        start_time = asyncio.get_running_loop().time()

        # Handle if_exists strategy
        table_exists = await self.table_manager.table_exists(table_name, schema_name)

        if table_exists:
            if if_exists == "fail":
                raise ValueError(f"Table {schema_name}.{table_name} already exists")
            elif if_exists == "replace":
                await self.table_manager.drop_table(table_name, schema_name)
                table_exists = False
            elif if_exists == "skip":
                return {
                    "success": True,
                    "rows_loaded": 0,
                    "message": f"Table {schema_name}.{table_name} already exists, skipped",
                }

        # Create table if needed
        if not table_exists and create_table:
            await self._create_table_from_dataframe(conn, df, table_name, schema_name)

        # Load data in batches
        total_rows = len(df)
        rows_loaded = 0

        for i in range(0, total_rows, batch_size):
            batch_df = df.iloc[i : i + batch_size]
            batch_rows = await self._load_batch(conn, batch_df, table_name, schema_name)
            rows_loaded += batch_rows

            self.logger.info(f"Loaded batch {i//batch_size + 1}: {batch_rows} rows")

        end_time = asyncio.get_running_loop().time()
        duration = end_time - start_time

        return {
            "success": True,
            "rows_loaded": rows_loaded,
            "duration_seconds": duration,
            "rows_per_second": rows_loaded / duration if duration > 0 else 0,
            "table_name": f"{schema_name}.{table_name}",
        }

    async def _create_table_from_dataframe(
        self,
        conn: asyncpg.Connection,
        df: pd.DataFrame,
        table_name: str,
        schema_name: str,
    ):
        """Create a table based on DataFrame structure."""
        # Generate column definitions
        column_definitions = []

        for column in df.columns:
            # Default to TEXT for all columns except id
            if column.lower() == "id":
                column_type = "BIGINT"
            else:
                column_type = "TEXT"

            column_definitions.append(f'"{column}" {column_type}')

        # Add primary key if id column exists
        if "id" in [col.lower() if isinstance(col, str) else str(col).lower() for col in df.columns]:
            column_definitions.append('PRIMARY KEY ("id")')

        create_sql = f"""
        CREATE TABLE "{schema_name}"."{table_name}" (
            {', '.join(column_definitions)}
        )
        """

        await conn.execute(create_sql)
        self.logger.info(f"Created table {schema_name}.{table_name}")

    async def _load_batch(
        self,
        conn: asyncpg.Connection,
        batch_df: pd.DataFrame,
        table_name: str,
        schema_name: str,
    ) -> int:
        """Load a batch of data using COPY."""
        # Get table columns
        table_info = await self.schema_manager.get_table_info(table_name, schema_name)
        table_columns = table_info["columns"]
        column_names = [col["column_name"] for col in table_columns]

        # Ensure DataFrame columns match table columns
        batch_df = batch_df.reindex(columns=column_names, fill_value=None)

        # Convert DataFrame to records
        records = []
        for _, row in batch_df.iterrows():
            record = []
            for col in column_names:
                value = row[col]
                if pd.isna(value):
                    record.append(None)
                else:
                    record.append(str(value))
            records.append(record)

        # Use COPY for efficient insertion
        await conn.copy_records_to_table(
            table_name=table_name,
            schema_name=schema_name,
            records=records,
            columns=column_names,
        )

        return len(records)

    async def _load_multiple_with_transaction(
        self, file_mappings: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Load multiple files within a single transaction."""
        conn = await self.connection_pool.acquire_connection()
        try:
            async with conn.transaction():
                results = []
                for mapping in file_mappings:
                    result = await self.load_from_file(**mapping)
                    results.append(result)

                self.logger.info(
                    f"Successfully loaded {len(file_mappings)} files in transaction"
                )
                return {
                    "success": True,
                    "files_loaded": len([r for r in results if r.get("success")]),
                    "total_files": len(file_mappings),
                    "results": results,
                }
        except Exception as e:
            self.logger.error(f"Transaction failed for multiple file load: {str(e)}")
            raise
        finally:
            await self.connection_pool.release_connection(conn)

    async def _load_multiple_without_transaction(
        self, file_mappings: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Load multiple files without transaction management."""
        results = []
        successful_loads = 0

        for mapping in file_mappings:
            try:
                result = await self.load_from_file(**mapping)
                results.append(result)
                if result.get("success"):
                    successful_loads += 1
            except Exception as e:
                self.logger.error(
                    f"Failed to load file {mapping.get('file_path')}: {str(e)}"
                )
                results.append(
                    {
                        "success": False,
                        "error": str(e),
                        "file_path": mapping.get("file_path"),
                    }
                )

        return {
            "success": successful_loads > 0,
            "files_loaded": successful_loads,
            "total_files": len(file_mappings),
            "results": results,
        }

    async def get_load_statistics(
        self, table_name: str, schema_name: str = "public"
    ) -> Dict[str, Any]:
        """Get statistics about a loaded table.

        Args:
            table_name: Table name
            schema_name: Schema name

        Returns:
            Dict[str, Any]: Table statistics
        """
        conn = await self.connection_pool.acquire_connection()
        try:
            # Get row count using safe identifier formatting
            # Note: PostgreSQL doesn't support parameterized table/schema names
            # Using double quotes for safe identifier quoting and validation
            import re

            # Validate identifiers to prevent injection
            if not re.match(r"^[a-zA-Z_][a-zA-Z0-9_]*$", schema_name):
                raise ValueError(f"Invalid schema name: {schema_name}")
            if not re.match(r"^[a-zA-Z_][a-zA-Z0-9_]*$", table_name):
                raise ValueError(f"Invalid table name: {table_name}")

            query = f'SELECT COUNT(*) FROM "{schema_name}"."{table_name}"'
            row_count = await conn.fetchval(query)

            # Get table size
            table_size = await conn.fetchval(
                "SELECT pg_total_relation_size($1::regclass)",
                f"{schema_name}.{table_name}",
            )

            # Get column information
            columns = await self.table_manager.get_table_columns(
                table_name, schema_name
            )

            return {
                "table_name": f"{schema_name}.{table_name}",
                "row_count": row_count,
                "table_size_bytes": table_size,
                "column_count": len(columns),
                "columns": [col["column_name"] for col in columns],
            }
        finally:
            await self.connection_pool.release_connection(conn)
