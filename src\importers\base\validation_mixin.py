"""Validation mixin for telecommunications data validation.

This module provides comprehensive data validation capabilities specifically
designed for telecommunications data types including CDR, EP, KPI, and NLG data.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
import re
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field

import pandas as pd
import numpy as np
from pydantic import BaseModel, Field, validator, ConfigDict


@dataclass
class ValidationRule:
    """Represents a single validation rule."""
    name: str
    description: str
    rule_type: str  # 'required', 'format', 'range', 'custom'
    parameters: Dict[str, Any] = field(default_factory=dict)
    severity: str = 'error'  # 'error', 'warning', 'info'
    enabled: bool = True


@dataclass
class ValidationResult:
    """Result of a validation operation."""
    rule_name: str
    passed: bool
    message: str
    severity: str
    affected_records: int = 0
    details: Dict[str, Any] = field(default_factory=dict)


class TelecomValidationConfig(BaseModel):
    """Configuration for telecommunications data validation."""
    
    # CDR validation settings - removed required columns constraint
    cdr_max_duration_hours: int = Field(default=24, description="Maximum call duration in hours")
    cdr_phone_number_pattern: str = Field(
        default=r'^[+]?[1-9]\d{1,14}$',
        description="Regex pattern for phone number validation"
    )
    
    # KPI validation settings - removed required columns constraint
    kpi_value_ranges: Dict[str, Tuple[float, float]] = Field(
        default_factory=lambda: {
            'rsrp': (-140, -44),
            'rsrq': (-20, -3),
            'sinr': (-20, 30),
            'throughput_dl': (0, 1000),
            'throughput_ul': (0, 500)
        },
        description="Valid ranges for KPI values"
    )
    
    # EP (Engineering Parameters) validation settings - removed required columns constraint
    ep_coordinate_ranges: Dict[str, Tuple[float, float]] = Field(
        default_factory=lambda: {
            'longitude': (-180, 180),
            'latitude': (-90, 90)
        },
        description="Valid coordinate ranges"
    )
    
    # NLG (Network Location Geography) validation settings - removed required columns constraint
    
    # General validation settings
    max_null_percentage: float = Field(default=0.1, description="Maximum allowed null percentage")
    enable_duplicate_check: bool = Field(default=True, description="Enable duplicate record checking")
    enable_outlier_detection: bool = Field(default=True, description="Enable outlier detection")
    outlier_std_threshold: float = Field(default=3.0, description="Standard deviation threshold for outliers")
    
    model_config = ConfigDict(
        extra="allow"
    )
class ValidationMixin:
    """Mixin class providing telecommunications data validation capabilities.
    
    This mixin can be used with any importer to add comprehensive validation
    functionality for telecommunications data types.
    """
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.validation_config = TelecomValidationConfig()
        self.validation_results: List[ValidationResult] = []
        self.validation_logger = logging.getLogger(f"{self.__class__.__name__}.validation")
        
    def configure_validation(self, config: Union[TelecomValidationConfig, Dict[str, Any]]) -> None:
        """Configure validation settings.
        
        Args:
            config: Validation configuration
        """
        if isinstance(config, dict):
            self.validation_config = TelecomValidationConfig(**config)
        else:
            self.validation_config = config
            
    async def validate_telecom_data(self, data: pd.DataFrame, data_type: str) -> Dict[str, Any]:
        """Validate telecommunications data based on type.

        Args:
            data: DataFrame to validate
            data_type: Type of data ('cdr', 'kpi', 'ep', 'nlg')

        Returns:
            Dict containing validation results
        """
        import asyncio

        self.validation_results = []

        # Add timeout to prevent infinite waiting
        try:
            return await asyncio.wait_for(self._perform_validation(data, data_type), timeout=30.0)
        except asyncio.TimeoutError:
            self.validation_logger.warning(f"Validation timeout for {data_type} data")
            return {
                'valid': True,  # Allow processing to continue
                'has_warnings': True,
                'summary': {'errors': 0, 'warnings': 1},
                'results': {'warnings': [{'message': 'Validation timeout - skipped detailed validation'}]}
            }

    async def _perform_validation(self, data: pd.DataFrame, data_type: str) -> Dict[str, Any]:
        """Perform the actual validation logic."""
        
        try:
            # Basic structure validation
            try:
                await self._validate_basic_structure(data)
            except Exception as e:
                self.validation_logger.error(f"Basic structure validation failed: {e}")
                raise
            
            # Type-specific validation
            try:
                if data_type.lower() == 'cdr':
                    await self._validate_cdr_data(data)
                elif data_type.lower() == 'kpi':
                    await self._validate_kpi_data(data)
                elif data_type.lower() == 'ep':
                    await self._validate_ep_data(data)
                elif data_type.lower() == 'nlg':
                    await self._validate_nlg_data(data)
                else:
                    self.validation_logger.warning(f"Unknown data type: {data_type}")
            except Exception as e:
                self.validation_logger.error(f"Type-specific validation failed for {data_type}: {e}")
                raise
                
            # General quality checks
            try:
                if self.validation_config.enable_duplicate_check:
                    await self._check_duplicates(data)
            except Exception as e:
                self.validation_logger.error(f"Duplicate check failed: {e}")
                raise
                
            try:
                if self.validation_config.enable_outlier_detection:
                    await self._detect_outliers(data)
            except Exception as e:
                self.validation_logger.error(f"Outlier detection failed: {e}")
                raise
                
            # Compile results
            try:
                return self._compile_validation_results()
            except Exception as e:
                self.validation_logger.error(f"Compile results failed: {e}")
                raise
            
        except Exception as e:
            self.validation_logger.error(f"Validation failed: {e}")
            return {
                'valid': False,
                'has_warnings': True,
                'summary': {'errors': 1, 'warnings': 0},
                'results': {
                    'errors': [{'message': f'Validation error: {e}'}],
                    'warnings': []
                }
            }
            
    async def _validate_basic_structure(self, data: pd.DataFrame) -> None:
        """Validate basic data structure."""
        # Check if DataFrame is empty
        if data.empty:
            self.validation_results.append(ValidationResult(
                rule_name="empty_data",
                passed=False,
                message="DataFrame is empty",
                severity="error"
            ))
            return
            
        # Check null percentage
        null_percentage = data.isnull().sum().sum() / (len(data) * len(data.columns))
        if null_percentage > self.validation_config.max_null_percentage:
            self.validation_results.append(ValidationResult(
                rule_name="high_null_percentage",
                passed=False,
                message=f"High null percentage: {null_percentage:.2%}",
                severity="warning",
                details={'null_percentage': float(null_percentage)}
            ))
            
    async def _validate_cdr_data(self, data: pd.DataFrame) -> None:
        """Validate CDR (Call Detail Records) data."""
        # Skip required columns validation - accept any column structure
            
        # Validate phone numbers
        if 'calling_number' in data.columns:
            await self._validate_phone_numbers(data, 'calling_number')
        if 'called_number' in data.columns:
            await self._validate_phone_numbers(data, 'called_number')
            
        # Validate call duration
        if 'call_duration' in data.columns:
            await self._validate_call_duration(data)
            
        # Validate call times
        if 'call_start_time' in data.columns and 'call_end_time' in data.columns:
            await self._validate_call_times(data)
            
    async def _validate_kpi_data(self, data: pd.DataFrame) -> None:
        """Validate KPI (Key Performance Indicators) data."""
        # Skip required columns validation - accept any column structure
            
        # Validate KPI values
        if 'kpi_name' in data.columns and 'kpi_value' in data.columns:
            await self._validate_kpi_values(data)
            
        # Validate timestamps
        if 'timestamp' in data.columns:
            await self._validate_timestamps(data, 'timestamp')
            
    async def _validate_ep_data(self, data: pd.DataFrame) -> None:
        """Validate EP (Engineering Parameters) data."""
        # Skip required columns validation - accept any column structure
            
        # Validate coordinates
        await self._validate_coordinates(data)
        
        # Validate measurement time
        if 'measurement_time' in data.columns:
            await self._validate_timestamps(data, 'measurement_time')
            
    async def _validate_nlg_data(self, data: pd.DataFrame) -> None:


        """Validate NLG (Network Location Geography) data."""
        # Skip required columns validation - accept any column structure
            
        # Validate coordinates
        await self._validate_coordinates(data)
        
        # Validate azimuth
        if 'azimuth' in data.columns:
            await self._validate_azimuth(data)
            
    async def _validate_phone_numbers(self, data: pd.DataFrame, column: str) -> None:
        """Validate phone number format."""
        pattern = re.compile(self.validation_config.cdr_phone_number_pattern)
        invalid_numbers = data[~data[column].astype(str).str.match(pattern, na=False)]
        
        if not invalid_numbers.empty:
            self.validation_results.append(ValidationResult(
                rule_name=f"invalid_phone_numbers_{column}",
                passed=False,
                message=f"Invalid phone numbers in {column}",
                severity="warning",
                affected_records=len(invalid_numbers),
                details={'invalid_count': int(len(invalid_numbers))}
            ))
            
    async def _validate_call_duration(self, data: pd.DataFrame) -> None:
        """Validate call duration values."""
        # Check for negative durations
        negative_durations = data[data['call_duration'] < 0]
        if not negative_durations.empty:
            self.validation_results.append(ValidationResult(
                rule_name="negative_call_duration",
                passed=False,
                message="Negative call durations found",
                severity="error",
                affected_records=len(negative_durations)
            ))
            
        # Check for extremely long calls
        max_duration_seconds = self.validation_config.cdr_max_duration_hours * 3600
        long_calls = data[data['call_duration'] > max_duration_seconds]
        if not long_calls.empty:
            self.validation_results.append(ValidationResult(
                rule_name="excessive_call_duration",
                passed=False,
                message=f"Calls longer than {self.validation_config.cdr_max_duration_hours} hours found",
                severity="warning",
                affected_records=len(long_calls)
            ))
            
    async def _validate_call_times(self, data: pd.DataFrame) -> None:
        """Validate call start and end times."""
        # Convert to datetime if not already
        start_times = pd.to_datetime(data['call_start_time'], errors='coerce')
        end_times = pd.to_datetime(data['call_end_time'], errors='coerce')
        
        # Check for end time before start time
        invalid_times = end_times < start_times
        if invalid_times.any():
            self.validation_results.append(ValidationResult(
                rule_name="invalid_call_times",
                passed=False,
                message="Call end time before start time",
                severity="error",
                affected_records=int(invalid_times.sum())
            ))
            
    async def _validate_kpi_values(self, data: pd.DataFrame) -> None:
        """Validate KPI values against expected ranges."""
        for kpi_name, (min_val, max_val) in self.validation_config.kpi_value_ranges.items():
            kpi_data = data[data['kpi_name'] == kpi_name]
            if not kpi_data.empty:
                out_of_range = kpi_data[
                    (kpi_data['kpi_value'] < min_val) | (kpi_data['kpi_value'] > max_val)
                ]
                if not out_of_range.empty:
                    self.validation_results.append(ValidationResult(
                        rule_name=f"kpi_out_of_range_{kpi_name}",
                        passed=False,
                        message=f"KPI {kpi_name} values out of range [{min_val}, {max_val}]",
                        severity="warning",
                        affected_records=len(out_of_range)
                    ))
                    
    async def _validate_coordinates(self, data: pd.DataFrame) -> None:
        """Validate longitude and latitude coordinates."""
        if 'longitude' in data.columns:
            lon_range = self.validation_config.ep_coordinate_ranges['longitude']
            invalid_lon = data[
                (data['longitude'] < lon_range[0]) | (data['longitude'] > lon_range[1])
            ]
            if not invalid_lon.empty:
                self.validation_results.append(ValidationResult(
                    rule_name="invalid_longitude",
                    passed=False,
                    message="Invalid longitude values",
                    severity="error",
                    affected_records=len(invalid_lon)
                ))
                
        if 'latitude' in data.columns:
            lat_range = self.validation_config.ep_coordinate_ranges['latitude']
            invalid_lat = data[
                (data['latitude'] < lat_range[0]) | (data['latitude'] > lat_range[1])
            ]
            if not invalid_lat.empty:
                self.validation_results.append(ValidationResult(
                    rule_name="invalid_latitude",
                    passed=False,
                    message="Invalid latitude values",
                    severity="error",
                    affected_records=len(invalid_lat)
                ))
                
    async def _validate_azimuth(self, data: pd.DataFrame) -> None:
        """Validate azimuth values (0-360 degrees)."""
        invalid_azimuth = data[(data['azimuth'] < 0) | (data['azimuth'] > 360)]
        if not invalid_azimuth.empty:
            self.validation_results.append(ValidationResult(
                rule_name="invalid_azimuth",
                passed=False,
                message="Invalid azimuth values (must be 0-360 degrees)",
                severity="error",
                affected_records=len(invalid_azimuth)
            ))
            
    async def _validate_timestamps(self, data: pd.DataFrame, column: str) -> None:
        """Validate timestamp format and values."""
        # Try to convert to datetime
        timestamps = pd.to_datetime(data[column], errors='coerce')
        invalid_timestamps = timestamps.isnull()
        
        if invalid_timestamps.any():
            self.validation_results.append(ValidationResult(
                rule_name=f"invalid_timestamps_{column}",
                passed=False,
                message=f"Invalid timestamp format in {column}",
                severity="error",
                affected_records=int(invalid_timestamps.sum())
            ))
            
        # Check for future timestamps
        future_timestamps = timestamps > datetime.now()
        if future_timestamps.any():
            self.validation_results.append(ValidationResult(
                rule_name=f"future_timestamps_{column}",
                passed=False,
                message=f"Future timestamps found in {column}",
                severity="warning",
                affected_records=int(future_timestamps.sum())
            ))
            
    async def _check_duplicates(self, data: pd.DataFrame) -> None:
        """Check for duplicate records."""
        try:
            # Convert all columns to string to handle unhashable types
            data_str = data.astype(str)
            duplicates = data_str.duplicated()
            if duplicates.any():
                self.validation_results.append(ValidationResult(
                    rule_name="duplicate_records",
                    passed=False,
                    message="Duplicate records found",
                    severity="warning",
                    affected_records=int(duplicates.sum())
                ))
        except Exception as e:
            self.validation_logger.warning(f"Could not check for duplicates: {e}")
            # Skip duplicate check if it fails
            
    async def _detect_outliers(self, data: pd.DataFrame) -> None:
        """Detect outliers in numeric columns."""
        numeric_columns = data.select_dtypes(include=[np.number]).columns
        
        for column in numeric_columns:
            if data[column].std() > 0:  # Avoid division by zero
                z_scores = np.abs((data[column] - data[column].mean()) / data[column].std())
                outliers = z_scores > self.validation_config.outlier_std_threshold
                
                if outliers.any():
                    self.validation_results.append(ValidationResult(
                        rule_name=f"outliers_{column}",
                        passed=False,
                        message=f"Outliers detected in {column}",
                        severity="info",
                        affected_records=int(outliers.sum()),
                        details={'threshold': float(self.validation_config.outlier_std_threshold)}
                    ))
                    
    def _compile_validation_results(self) -> Dict[str, Any]:
        """Compile validation results into a summary."""
        errors = [r for r in self.validation_results if r.severity == 'error' and not r.passed]
        warnings = [r for r in self.validation_results if r.severity == 'warning' and not r.passed]
        info = [r for r in self.validation_results if r.severity == 'info' and not r.passed]
        
        return {
            'valid': len(errors) == 0,
            'has_warnings': len(warnings) > 0,
            'summary': {
                'total_rules_checked': len(self.validation_results),
                'errors': len(errors),
                'warnings': len(warnings),
                'info': len(info)
            },
            'results': {
                'errors': [{'rule': r.rule_name, 'message': r.message, 'affected_records': r.affected_records} for r in errors],
                'warnings': [{'rule': r.rule_name, 'message': r.message, 'affected_records': r.affected_records} for r in warnings],
                'info': [{'rule': r.rule_name, 'message': r.message, 'affected_records': r.affected_records} for r in info]
            }
        }