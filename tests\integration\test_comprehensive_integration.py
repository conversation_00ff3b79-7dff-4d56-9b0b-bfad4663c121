#!/usr/bin/env python3
"""
Comprehensive Integration Tests

This module provides comprehensive integration tests for the database framework,
testing the interaction between multiple components in realistic scenarios:
- Database connection and pool management
- Schema creation and management
- Data import/export workflows
- Geospatial data processing pipelines
- Error handling and recovery
- Performance monitoring

Author: Connect Database Framework Team
Version: 1.0.0
Date: 2024-01-01
"""

import asyncio
import json
import tempfile
from pathlib import Path
from typing import Any, Dict, List

import pandas as pd
import pytest
from shapely.geometry import Point, Polygon

from src.config import get_config as load_config
from src.config.models import ConnectConfig as Config, DatabaseConfig
from src.database.connection.pool import DatabasePoolManager
from src.database.connection.session import SessionManager
from src.database.exceptions import (
    ConfigurationError,
    ConnectionError,
    DatabaseError,
    SchemaError,
    ValidationError,
)
from src.database.geospatial.processor import GeospatialProcessor
from src.database.geospatial.validator import GeometryValidator
from src.database.monitoring.logger import DatabaseLogger
from src.database.operations.crud import CRUDOperations
from src.database.operations.exporter import DataExporter
from src.database.operations.importer import DataImporter
from src.database.schema.manager import SchemaManager
from src.database.schema.models import TableSchema, ColumnSchema
from src.database.schema.router import SchemaRouter
from tests.e2e.scripts.setup_test_environment import TestEnvironmentManager
from tests.test_infrastructure import (
    PerformanceBenchmark,
    TestDataGenerator,
)


class TestDatabaseConnectionIntegration:
    """Test database connection integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_session_to_pool_migration(self, test_config):
        """Test migration from session-based to pool-based connections."""
        # Start with session manager
        session_manager = SessionManager(test_config.database)
        
        # Simulate some operations with session
        connection = await session_manager.get_connection()
        assert connection is not None
        
        # Migrate to pool manager
        pool_manager = DatabasePoolManager(test_config.database)
        pool = await pool_manager.get_pool()
        assert pool is not None
        
        # Verify pool can handle multiple concurrent connections
        async with pool.acquire() as conn1:
            async with pool.acquire() as conn2:
                assert conn1 != conn2
                
                # Test concurrent operations
                result1 = await conn1.fetchval("SELECT 1")
                result2 = await conn2.fetchval("SELECT 2")
                
                assert result1 == 1
                assert result2 == 2
        
        # Cleanup
        await session_manager.close()
        await pool_manager.close()
    
    @pytest.mark.asyncio
    async def test_connection_pool_resilience(self, test_config):
        """Test connection pool resilience under stress."""
        pool_manager = DatabasePoolManager(test_config.database)
        pool = await pool_manager.get_pool()
        
        # Test multiple concurrent acquisitions
        tasks = []
        for i in range(10):
            task = asyncio.create_task(self._test_connection_operation(pool, i))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify all operations completed successfully
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == 10
        
        await pool_manager.close()
    
    async def _test_connection_operation(self, pool, operation_id: int):
        """Helper method for testing individual connection operations."""
        async with pool.acquire() as connection:
            result = await connection.fetchval(f"SELECT {operation_id}")
            await asyncio.sleep(0.1)  # Simulate some work
            return result
    
    @pytest.mark.asyncio
    async def test_connection_error_recovery(self, test_config):
        """Test connection error recovery mechanisms."""
        pool_manager = DatabasePoolManager(test_config.database)
        
        # Test recovery from connection failures
        with pytest.raises(ConnectionError):
            # Use invalid config to trigger connection error
            invalid_config = DatabaseConfig(
                host="invalid_host",
                port=9999,
                name="invalid_db",
                user="invalid_user",
                password="invalid_password",
            )
            invalid_pool_manager = DatabasePoolManager(invalid_config)
            await invalid_pool_manager.get_pool()


class TestSchemaManagementIntegration:
    """Test schema management integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_complete_schema_lifecycle(self, test_database_pool):
        """Test complete schema lifecycle from creation to deletion."""
        schema_manager = SchemaManager(test_database_pool)
        schema_name = "test_integration_schema"
        
        try:
            # Create schema
            await schema_manager.create_schema(schema_name)
            
            # Verify schema exists
            exists = await schema_manager.schema_exists(schema_name)
            assert exists is True
            
            # Create table in schema
            table_schema = TableSchema(
                name="test_table",
                schema_name=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="name", data_type="VARCHAR(255)", nullable=False),
                    ColumnSchema(name="created_at", data_type="TIMESTAMP", default="NOW()"),
                ],
            )
            
            await schema_manager.create_table(table_schema)
            
            # Verify table exists
            table_exists = await schema_manager.table_exists("test_table", schema_name)
            assert table_exists is True
            
            # Get table info
            table_info = await schema_manager.get_table_info("test_table", schema_name)
            assert table_info["name"] == "test_table"
            assert len(table_info["columns"]) == 3
            
        finally:
            # Cleanup: drop schema (cascade to remove tables)
            await schema_manager.drop_schema(schema_name, cascade=True)
    
    @pytest.mark.asyncio
    async def test_schema_routing_integration(self, test_database_pool):
        """Test schema routing with multiple schemas."""
        schema_manager = SchemaManager(test_database_pool)
        
        # Create multiple schemas
        schemas = ["ep_data", "cdr_data", "geospatial_data"]
        
        try:
            for schema in schemas:
                await schema_manager.create_schema(schema)
            
            # Configure schema router
            routing_config = {
                "default_schema": "public",
                "routing_rules": [
                    {"pattern": "ep_*", "schema": "ep_data", "priority": 1},
                    {"pattern": "cdr_*", "schema": "cdr_data", "priority": 1},
                    {"pattern": "geo_*", "schema": "geospatial_data", "priority": 1},
                ],
            }
            
            router = SchemaRouter(schema_manager, routing_config)
            
            # Test routing decisions
            assert router.get_schema_for_table("ep_measurements") == "ep_data"
            assert router.get_schema_for_table("cdr_records") == "cdr_data"
            assert router.get_schema_for_table("geo_points") == "geospatial_data"
            assert router.get_schema_for_table("other_table") == "public"
            
        finally:
            # Cleanup
            for schema in schemas:
                await schema_manager.drop_schema(schema, cascade=True)
    
    @pytest.mark.asyncio
    async def test_schema_migration_workflow(self, test_database_pool):
        """Test schema migration workflow."""
        schema_manager = SchemaManager(test_database_pool)
        schema_name = "migration_test_schema"
        
        try:
            # Create initial schema
            await schema_manager.create_schema(schema_name)
            
            # Create initial table version
            initial_table = TableSchema(
                name="user_data",
                schema_name=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="username", data_type="VARCHAR(50)", nullable=False),
                ],
            )
            
            await schema_manager.create_table(initial_table)
            
            # Simulate migration: add new column
            await schema_manager.add_column(
                table_name="user_data",
                schema=schema_name,
                column=ColumnSchema(name="email", data_type="VARCHAR(255)"),
            )
            
            # Verify migration
            table_info = await schema_manager.get_table_info("user_data", schema_name)
            column_names = [col["name"] for col in table_info["columns"]]
            assert "email" in column_names
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)


class TestDataOperationsIntegration:
    """Test data operations integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_complete_data_workflow(self, test_database_pool, large_sample_dataframe):
        """Test complete data workflow from import to export."""
        # Initialize components
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        importer = DataImporter(test_database_pool)
        exporter = DataExporter(test_database_pool)
        
        schema_name = "data_workflow_test"
        table_name = "test_data"
        
        try:
            # Create schema and table
            await schema_manager.create_schema(schema_name)
            
            table_schema = TableSchema(
                name=table_name,
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="name", data_type="VARCHAR(255)"),
                    ColumnSchema(name="value", data_type="DECIMAL(10,2)"),
                    ColumnSchema(name="category", data_type="VARCHAR(100)"),
                    ColumnSchema(name="created_at", data_type="TIMESTAMP", default="NOW()"),
                ],
            )
            
            await schema_manager.create_table(table_schema)
            
            # Import data
            import_result = await crud.insert_dataframe(
                dataframe=large_sample_dataframe,
                table_name=table_name,
                schema=schema_name,
            )
            
            assert import_result["status"] == "success"
            assert import_result["rows_inserted"] == len(large_sample_dataframe)
            
            # Query data with filters
            filtered_data = await crud.read(
                table_name=table_name,
                schema=schema_name,
                where_clause="value > 50",
                limit=100,
            )
            
            assert len(filtered_data) > 0
            assert all(record["value"] > 50 for record in filtered_data)
            
            # Update data
            update_result = await crud.update_data(
                table_name=table_name,
                schema=schema_name,
                data={"category": "updated"},
                where_clause="value > 90",
            )
            
            assert "UPDATE" in update_result
            
            # Export data
            with tempfile.NamedTemporaryFile(mode="w", suffix=".csv", delete=False) as tmp_file:
                export_result = await exporter.export_to_csv(
                    table_name=table_name,
                    schema_name=schema_name,
                    output_path=tmp_file.name,
                    where_clause="category = 'updated'",
                )
                
                assert export_result["status"] == "success"
                assert Path(tmp_file.name).exists()
                
                # Verify exported data
                exported_df = pd.read_csv(tmp_file.name)
                assert len(exported_df) > 0
                assert all(exported_df["category"] == "updated")
        
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)
    
    @pytest.mark.asyncio
    async def test_bulk_data_operations(self, test_database_pool):
        """Test bulk data operations performance."""
        crud = CRUDOperations(test_database_pool)
        schema_manager = SchemaManager(test_database_pool)
        
        schema_name = "bulk_test_schema"
        table_name = "bulk_data"
        
        try:
            # Setup
            await schema_manager.create_schema(schema_name)
            
            table_schema = TableSchema(
                name=table_name,
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="batch_id", data_type="INTEGER"),
                    ColumnSchema(name="data_value", data_type="TEXT"),
                    ColumnSchema(name="timestamp", data_type="TIMESTAMP", default="NOW()"),
                ],
            )
            
            await schema_manager.create_table(table_schema)
            
            # Generate large dataset
            generator = TestDataGenerator()
            batch_size = 1000
            num_batches = 5
            
            with PerformanceBenchmark("bulk_insert", threshold_ms=10000.0) as benchmark:
                for batch_id in range(num_batches):
                    batch_data = pd.DataFrame({
                        "batch_id": [batch_id] * batch_size,
                        "data_value": [f"data_{i}_{batch_id}" for i in range(batch_size)],
                    })
                    
                    await crud.insert_dataframe(
                        dataframe=batch_data,
                        table_name=table_name,
                        schema=schema_name,
                    )
            
            # Verify data count
            count_result = await crud.read(
                table_name=table_name,
                schema=schema_name,
                select_clause="COUNT(*) as total_count",
            )
            
            total_count = count_result[0]["total_count"]
            assert total_count == batch_size * num_batches
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)
    
    @pytest.mark.asyncio
    async def test_transaction_management(self, test_database_pool):
        """Test transaction management across operations."""
        crud = CRUDOperations(test_database_pool)
        schema_manager = SchemaManager(test_database_pool)
        
        schema_name = "transaction_test_schema"
        table_name = "transaction_data"
        
        try:
            # Setup
            await schema_manager.create_schema(schema_name)
            
            table_schema = TableSchema(
                name=table_name,
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="amount", data_type="DECIMAL(10,2)"),
                    ColumnSchema(name="status", data_type="VARCHAR(20)"),
                ],
            )
            
            await schema_manager.create_table(table_schema)
            
            # Test successful transaction
            async with test_database_pool.acquire() as connection:
                async with connection.transaction():
                    # Insert initial data
                    await connection.execute(
                        f"INSERT INTO {schema_name}.{table_name} (amount, status) VALUES (100.00, 'pending')"
                    )
                    
                    # Update status
                    await connection.execute(
                        f"UPDATE {schema_name}.{table_name} SET status = 'completed' WHERE amount = 100.00"
                    )
            
            # Verify transaction committed
            result = await crud.read(
                table_name=table_name,
                schema=schema_name,
                where_clause="amount = 100.00",
            )
            
            assert len(result) == 1
            assert result[0]["status"] == "completed"
            
            # Test failed transaction (rollback)
            try:
                async with test_database_pool.acquire() as connection:
                    async with connection.transaction():
                        # Insert data
                        await connection.execute(
                            f"INSERT INTO {schema_name}.{table_name} (amount, status) VALUES (200.00, 'pending')"
                        )
                        
                        # Simulate error
                        raise Exception("Simulated error")
            except Exception:
                pass  # Expected exception
            
            # Verify transaction rolled back
            result = await crud.select_data(
                table_name=table_name,
                schema=schema_name,
                where_clause="amount = 200.00",
            )
            
            assert len(result) == 0
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)


class TestGeospatialIntegration:
    """Test geospatial data integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_geospatial_data_pipeline(self, test_database_pool, sample_geometries):
        """Test complete geospatial data processing pipeline."""
        # Initialize components
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        validator = GeometryValidator()
        processor = GeospatialProcessor()
        
        schema_name = "geospatial_test_schema"
        table_name = "geo_features"
        
        try:
            # Create geospatial schema and table
            await schema_manager.create_schema(schema_name)
            
            # Enable PostGIS extension
            async with test_database_pool.acquire() as connection:
                await connection.execute("CREATE EXTENSION IF NOT EXISTS postgis")
            
            table_schema = TableSchema(
                name=table_name,
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="name", data_type="VARCHAR(255)"),
                    ColumnSchema(name="geometry", data_type="GEOMETRY"),
                    ColumnSchema(name="properties", data_type="JSONB"),
                ],
            )
            
            await schema_manager.create_table(table_schema)
            
            # Process and insert geospatial data
            processed_features = []
            
            for geom_data in sample_geometries:
                geometry = geom_data["geometry"]
                properties = geom_data["properties"]
                
                # Validate geometry
                is_valid = validator.validate_geometry(geometry)
                if not is_valid:
                    # Try to fix invalid geometry
                    geometry = processor.fix_geometry(geometry)
                
                # Transform to target CRS if needed
                if properties.get("source_crs") != "EPSG:4326":
                    geometry = processor.transform_coordinates(
                        geometry=geometry,
                        source_crs=properties.get("source_crs", "EPSG:4326"),
                        target_crs="EPSG:4326",
                    )
                
                # Add buffer for point geometries
                if isinstance(geometry, Point):
                    buffered_geometry = processor.buffer_geometry(geometry, distance=1000)
                    properties["buffer_radius"] = 1000
                    geometry = buffered_geometry
                
                processed_features.append({
                    "name": properties.get("name", f"Feature_{len(processed_features)}"),
                    "geometry": geometry.wkt,
                    "properties": json.dumps(properties),
                })
            
            # Insert processed features
            features_df = pd.DataFrame(processed_features)
            insert_result = await crud.insert_dataframe(
                dataframe=features_df,
                table_name=table_name,
                schema=schema_name,
            )
            
            assert insert_result["status"] == "success"
            assert insert_result["rows_inserted"] == len(processed_features)
            
            # Perform spatial queries
            # Find features within a bounding box
            bbox_query = """
                SELECT id, name, ST_AsText(geometry) as geometry_wkt
                FROM {}.{}
                WHERE ST_Intersects(
                    geometry,
                    ST_MakeEnvelope(-180, -90, 180, 90, 4326)
                )
            """.format(schema_name, table_name)
            
            async with test_database_pool.acquire() as connection:
                bbox_results = await connection.fetch(bbox_query)
            
            assert len(bbox_results) > 0
            
            # Calculate distances between features
            distance_query = """
                SELECT 
                    a.id as id1,
                    b.id as id2,
                    ST_Distance(a.geometry, b.geometry) as distance
                FROM {}.{} a
                CROSS JOIN {}.{} b
                WHERE a.id < b.id
                ORDER BY distance
                LIMIT 5
            """.format(schema_name, table_name, schema_name, table_name)
            
            async with test_database_pool.acquire() as connection:
                distance_results = await connection.fetch(distance_query)
            
            assert len(distance_results) > 0
            assert all(result["distance"] >= 0 for result in distance_results)
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)
    
    @pytest.mark.asyncio
    async def test_geospatial_analysis_workflow(self, test_database_pool):
        """Test geospatial analysis workflow."""
        processor = GeospatialProcessor()
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        
        schema_name = "geo_analysis_schema"
        
        try:
            await schema_manager.create_schema(schema_name)
            
            # Enable PostGIS
            async with test_database_pool.acquire() as connection:
                await connection.execute("CREATE EXTENSION IF NOT EXISTS postgis")
            
            # Create analysis tables
            points_table = TableSchema(
                name="analysis_points",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="location", data_type="GEOMETRY(POINT, 4326)"),
                    ColumnSchema(name="value", data_type="DECIMAL(10,2)"),
                ],
            )
            
            polygons_table = TableSchema(
                name="analysis_polygons",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="boundary", data_type="GEOMETRY(POLYGON, 4326)"),
                    ColumnSchema(name="name", data_type="VARCHAR(255)"),
                ],
            )
            
            await schema_manager.create_table(points_table)
            await schema_manager.create_table(polygons_table)
            
            # Generate test data
            generator = TestDataGenerator()
            
            # Create points
            points_data = []
            for i in range(50):
                point = Point(
                    generator.fake.longitude(),
                    generator.fake.latitude(),
                )
                points_data.append({
                    "location": point.wkt,
                    "value": generator.fake.pydecimal(left_digits=3, right_digits=2, positive=True),
                })
            
            points_df = pd.DataFrame(points_data)
            await crud.insert_dataframe(
                dataframe=points_df,
                table_name="analysis_points",
                schema=schema_name,
            )
            
            # Create polygons
            polygons_data = []
            for i in range(5):
                # Create simple rectangular polygons
                min_x = generator.fake.longitude()
                min_y = generator.fake.latitude()
                max_x = min_x + 1.0
                max_y = min_y + 1.0
                
                polygon = Polygon([
                    (min_x, min_y),
                    (max_x, min_y),
                    (max_x, max_y),
                    (min_x, max_y),
                    (min_x, min_y),
                ])
                
                polygons_data.append({
                    "boundary": polygon.wkt,
                    "name": f"Region_{i}",
                })
            
            polygons_df = pd.DataFrame(polygons_data)
            await crud.insert_dataframe(
                dataframe=polygons_df,
                table_name="analysis_polygons",
                schema=schema_name,
            )
            
            # Perform spatial analysis
            # Points in polygons analysis
            analysis_query = """
                SELECT 
                    p.name as region_name,
                    COUNT(pt.id) as point_count,
                    AVG(pt.value) as avg_value,
                    ST_Area(p.boundary) as region_area
                FROM {}.analysis_polygons p
                LEFT JOIN {}.analysis_points pt ON ST_Within(pt.location, p.boundary)
                GROUP BY p.id, p.name, p.boundary
                ORDER BY point_count DESC
            """.format(schema_name, schema_name)
            
            async with test_database_pool.acquire() as connection:
                analysis_results = await connection.fetch(analysis_query)
            
            assert len(analysis_results) == 5  # Number of polygons
            assert all(result["region_area"] > 0 for result in analysis_results)
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)


class TestErrorHandlingIntegration:
    """Test error handling integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_cascading_error_handling(self, test_database_pool):
        """Test error handling across multiple components."""
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        
        # Test schema-level error propagation
        with pytest.raises(SchemaError):
            await schema_manager.create_table(
                TableSchema(
                    name="invalid_table",
                    schema_name="non_existent_schema",  # This should cause an error
                    columns=[
                        ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ],
                )
            )
        
        # Test CRUD operation error handling
        with pytest.raises(DatabaseError):
            await crud.read(
                table_name="non_existent_table",
                schema_name="non_existent_schema",
            )
    
    @pytest.mark.asyncio
    async def test_recovery_mechanisms(self, test_database_pool):
        """Test recovery mechanisms after errors."""
        schema_manager = SchemaManager(test_database_pool)
        schema_name = "recovery_test_schema"
        
        try:
            # Create schema successfully
            await schema_manager.create_schema(schema_name)
            
            # Attempt invalid operation
            try:
                await schema_manager.create_table(
                    TableSchema(
                        name="invalid_table",
                        schema=schema_name,
                        columns=[],  # Empty columns should cause error
                    )
                )
            except (SchemaError, ValidationError):
                pass  # Expected error
            
            # Verify schema still exists and can be used
            exists = await schema_manager.schema_exists(schema_name)
            assert exists is True
            
            # Create valid table after error
            valid_table = TableSchema(
                name="valid_table",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="data", data_type="TEXT"),
                ],
            )
            
            await schema_manager.create_table(valid_table)
            
            # Verify table was created successfully
            table_exists = await schema_manager.table_exists("valid_table", schema_name)
            assert table_exists is True
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)


class TestPerformanceIntegration:
    """Test performance integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_concurrent_operations_performance(self, test_database_pool):
        """Test performance under concurrent operations."""
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        
        schema_name = "performance_test_schema"
        
        try:
            # Setup
            await schema_manager.create_schema(schema_name)
            
            table_schema = TableSchema(
                name="performance_data",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="thread_id", data_type="INTEGER"),
                    ColumnSchema(name="operation_id", data_type="INTEGER"),
                    ColumnSchema(name="data", data_type="TEXT"),
                ],
            )
            
            await schema_manager.create_table(table_schema)
            
            # Test concurrent inserts
            async def insert_batch(thread_id: int, batch_size: int):
                batch_data = pd.DataFrame({
                    "thread_id": [thread_id] * batch_size,
                    "operation_id": list(range(batch_size)),
                    "data": [f"data_{thread_id}_{i}" for i in range(batch_size)],
                })
                
                return await crud.insert_dataframe(
                    dataframe=batch_data,
                    table_name="performance_data",
                    schema=schema_name,
                )
            
            # Run concurrent operations
            num_threads = 5
            batch_size = 100
            
            with PerformanceBenchmark("concurrent_inserts", threshold_ms=15000.0) as benchmark:
                tasks = [
                    asyncio.create_task(insert_batch(thread_id, batch_size))
                    for thread_id in range(num_threads)
                ]
                
                results = await asyncio.gather(*tasks)
            
            # Verify all operations completed successfully
            assert all(result["status"] == "success" for result in results)
            assert sum(result["rows_inserted"] for result in results) == num_threads * batch_size
            
            # Test concurrent reads
            async def read_batch(thread_id: int):
                return await crud.read(
                    table_name="performance_data",
                    schema=schema_name,
                    where_clause=f"thread_id = {thread_id}",
                )
            
            with PerformanceBenchmark("concurrent_reads", threshold_ms=5000.0) as benchmark:
                read_tasks = [
                    asyncio.create_task(read_batch(thread_id))
                    for thread_id in range(num_threads)
                ]
                
                read_results = await asyncio.gather(*read_tasks)
            
            # Verify read results
            assert all(len(result) == batch_size for result in read_results)
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)
    
    @pytest.mark.asyncio
    async def test_large_dataset_performance(self, test_database_pool):
        """Test performance with large datasets."""
        schema_manager = SchemaManager(test_database_pool)
        crud = CRUDOperations(test_database_pool)
        
        schema_name = "large_data_test_schema"
        
        try:
            # Setup
            await schema_manager.create_schema(schema_name)
            
            table_schema = TableSchema(
                name="large_dataset",
                schema=schema_name,
                columns=[
                    ColumnSchema(name="id", data_type="SERIAL", primary_key=True),
                    ColumnSchema(name="category", data_type="VARCHAR(50)"),
                    ColumnSchema(name="value", data_type="DECIMAL(15,4)"),
                    ColumnSchema(name="description", data_type="TEXT"),
                    ColumnSchema(name="created_at", data_type="TIMESTAMP", default="NOW()"),
                ],
            )
            
            await schema_manager.create_table(table_schema)
            
            # Generate large dataset
            generator = TestDataGenerator()
            total_rows = 10000
            chunk_size = 1000
            
            with PerformanceBenchmark("large_dataset_insert", threshold_ms=30000.0) as benchmark:
                for chunk_start in range(0, total_rows, chunk_size):
                    chunk_end = min(chunk_start + chunk_size, total_rows)
                    chunk_data = pd.DataFrame({
                        "category": [generator.fake.word() for _ in range(chunk_end - chunk_start)],
                        "value": [generator.fake.pydecimal(left_digits=10, right_digits=4) for _ in range(chunk_end - chunk_start)],
                        "description": [generator.fake.text(max_nb_chars=200) for _ in range(chunk_end - chunk_start)],
                    })
                    
                    await crud.insert_dataframe(
                        dataframe=chunk_data,
                        table_name="large_dataset",
                        schema=schema_name,
                    )
            
            # Test large dataset queries
            with PerformanceBenchmark("large_dataset_query", threshold_ms=5000.0) as benchmark:
                # Aggregation query
                agg_result = await crud.read(
                    table_name="large_dataset",
                    schema=schema_name,
                    select_clause="category, COUNT(*) as count, AVG(value) as avg_value",
                    group_by="category",
                    order_by="count DESC",
                    limit=10,
                )
            
            assert len(agg_result) > 0
            assert all("count" in record for record in agg_result)
            
        finally:
            await schema_manager.drop_schema(schema_name, cascade=True)


# Integration test markers
pytestmark = [
    pytest.mark.integration,
    pytest.mark.asyncio,
]