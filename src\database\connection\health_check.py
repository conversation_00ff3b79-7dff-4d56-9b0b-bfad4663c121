"""Database connection health check module.

This module provides robust health checking capabilities for database connections,
including periodic monitoring, status tracking, and automatic recovery detection.
"""

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, Optional

import asyncpg
from asyncpg import Pool

from ..exceptions import ConnectionError, DatabaseError, TimeoutError

# Configure logging
logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """Health status enumeration."""

    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"
    CHECKING = "checking"


@dataclass
class HealthCheckResult:
    """Result of a health check operation."""

    status: HealthStatus
    timestamp: datetime
    response_time_ms: float
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert health check result to dictionary."""
        return {
            "status": self.status.value,
            "timestamp": self.timestamp.isoformat(),
            "response_time_ms": self.response_time_ms,
            "error_message": self.error_message,
            "error_code": self.error_code,
            "details": self.details,
        }


@dataclass
class HealthStats:
    """Health check statistics."""

    total_checks: int = 0
    successful_checks: int = 0
    failed_checks: int = 0
    average_response_time_ms: float = 0.0
    last_check_time: Optional[datetime] = None
    uptime_percentage: float = 100.0
    consecutive_failures: int = 0
    consecutive_successes: int = 0

    def update_success(self, response_time_ms: float) -> None:
        """Update statistics for successful check."""
        self.total_checks += 1
        self.successful_checks += 1
        self.consecutive_successes += 1
        self.consecutive_failures = 0
        self.last_check_time = datetime.now()

        # Update average response time
        if self.total_checks == 1:
            self.average_response_time_ms = response_time_ms
        else:
            self.average_response_time_ms = (
                self.average_response_time_ms * (self.total_checks - 1)
                + response_time_ms
            ) / self.total_checks

        # Update uptime percentage
        self.uptime_percentage = (self.successful_checks / self.total_checks) * 100

    def update_failure(self) -> None:
        """Update statistics for failed check."""
        self.total_checks += 1
        self.failed_checks += 1
        self.consecutive_failures += 1
        self.consecutive_successes = 0
        self.last_check_time = datetime.now()

        # Update uptime percentage
        self.uptime_percentage = (self.successful_checks / self.total_checks) * 100

    def to_dict(self) -> Dict[str, Any]:
        """Convert health stats to dictionary."""
        return {
            "total_checks": self.total_checks,
            "successful_checks": self.successful_checks,
            "failed_checks": self.failed_checks,
            "average_response_time_ms": round(self.average_response_time_ms, 2),
            "last_check_time": self.last_check_time.isoformat()
            if self.last_check_time
            else None,
            "uptime_percentage": round(self.uptime_percentage, 2),
            "consecutive_failures": self.consecutive_failures,
            "consecutive_successes": self.consecutive_successes,
        }


class HealthChecker:
    """Database connection health checker.

    This class provides comprehensive health checking capabilities for database
    connections, including periodic monitoring, status tracking, and callbacks
    for status changes.
    """

    def __init__(
        self,
        pool: Pool,
        check_interval: float = 30.0,
        timeout: float = 5.0,
        max_retries: int = 3,
    ):
        """Initialize health checker.

        Args:
            pool: asyncpg connection pool to monitor.
            check_interval: Interval between health checks in seconds.
            timeout: Timeout for individual health checks in seconds.
            max_retries: Maximum number of retries for failed checks.
        """
        self.pool = pool
        self.check_interval = check_interval
        self.timeout = timeout
        self.max_retries = max_retries

        # Health status tracking
        self._current_status = HealthStatus.UNKNOWN
        self._last_result: Optional[HealthCheckResult] = None
        self._stats = HealthStats()

        # Monitoring control
        self._monitoring_task: Optional[asyncio.Task] = None
        self._is_monitoring = False
        self._stop_event = asyncio.Event()

        # Callbacks for status changes
        self._status_change_callbacks: list[
            Callable[[HealthStatus, HealthStatus], None]
        ] = []

        logger.info(
            f"Health checker initialized (interval={check_interval}s, timeout={timeout}s, "
            f"max_retries={max_retries})"
        )

    async def start_monitoring(self) -> None:
        """Start periodic health monitoring.

        Raises:
            RuntimeError: If monitoring is already running.
        """
        if self._is_monitoring:
            raise RuntimeError("Health monitoring is already running")

        logger.info(f"Starting health monitoring (interval={self.check_interval}s)")

        self._is_monitoring = True
        self._stop_event.clear()

        # Start monitoring task
        self._monitoring_task = asyncio.create_task(self._periodic_check())

        logger.info("Health monitoring started successfully")

    async def stop_monitoring(self) -> None:
        """Stop periodic health monitoring."""
        if not self._is_monitoring:
            logger.debug("Health monitoring is not running")
            return

        logger.info("Stopping health monitoring")

        self._is_monitoring = False
        self._stop_event.set()

        # Cancel monitoring task
        if self._monitoring_task and not self._monitoring_task.done():
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass

        self._monitoring_task = None

        logger.info("Health monitoring stopped")

    async def _periodic_check(self) -> None:
        """Perform periodic health checks."""
        logger.debug("Starting periodic health check loop")

        try:
            while self._is_monitoring and not self._stop_event.is_set():
                try:
                    # Perform health check
                    await self.check_health()

                    # Wait for next check or stop signal
                    try:
                        await asyncio.wait_for(
                            self._stop_event.wait(), timeout=self.check_interval
                        )
                        # Stop event was set, exit loop
                        break
                    except asyncio.TimeoutError:
                        # Timeout reached, continue with next check
                        continue

                except asyncio.CancelledError:
                    # Task was cancelled, exit gracefully
                    logger.debug("Periodic health check cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error in periodic health check: {str(e)}", exc_info=True)
                    # Continue monitoring even if individual check fails
                    try:
                        await asyncio.sleep(min(self.check_interval, 10.0))
                    except asyncio.CancelledError:
                        logger.debug("Periodic health check cancelled during sleep")
                        break
        except asyncio.CancelledError:
            # Task was cancelled at top level
            logger.debug("Periodic health check task cancelled")
        finally:
            logger.debug("Periodic health check loop ended")
            self._is_monitoring = False

    async def check_health(self) -> HealthCheckResult:
        """Perform a single health check.

        Returns:
            HealthCheckResult: Result of the health check.
        """
        start_time = datetime.now()
        old_status = self._current_status
        self._current_status = HealthStatus.CHECKING

        logger.debug("Performing health check")

        for attempt in range(self.max_retries + 1):
            try:
                # Measure response time
                check_start = asyncio.get_event_loop().time()

                # Perform health check query with timeout
                connection = None
                try:
                    async with asyncio.timeout(self.timeout):
                        async with self.pool.acquire() as connection:
                            result = await connection.fetchval("SELECT 1")

                            if result != 1:
                                raise DatabaseError(
                                    "Health check query returned unexpected result",
                                    error_code="HEALTH_CHECK_INVALID_RESULT",
                                    details={"result": result},
                                )
                except Exception:
                    # Ensure connection is properly released
                    if connection is not None:
                        try:
                            await connection.close()
                        except Exception:
                            pass
                    raise
                finally:
                    connection = None

                check_end = asyncio.get_event_loop().time()
                response_time_ms = (check_end - check_start) * 1000

                # Health check successful
                self._current_status = HealthStatus.HEALTHY

                result = HealthCheckResult(
                    status=HealthStatus.HEALTHY,
                    timestamp=start_time,
                    response_time_ms=response_time_ms,
                    details={"attempt": attempt + 1, "max_retries": self.max_retries},
                )

                self._last_result = result
                self._stats.update_success(response_time_ms)

                # Log status change if needed
                if old_status != HealthStatus.HEALTHY:
                    logger.info(
                        f"Database health status changed: {old_status.value} -> {HealthStatus.HEALTHY.value} "
                        f"(response_time={response_time_ms:.2f}ms)"
                    )
                    self._notify_status_change(old_status, HealthStatus.HEALTHY)
                else:
                    logger.debug(
                        f"Health check successful (response_time={response_time_ms:.2f}ms)"
                    )

                return result

            except asyncio.TimeoutError as e:
                error_msg = f"Health check timeout after {self.timeout} seconds (attempt {attempt + 1}/{self.max_retries + 1})"
                logger.warning(error_msg)

                if attempt == self.max_retries:
                    # Final attempt failed
                    self._current_status = HealthStatus.UNHEALTHY

                    result = HealthCheckResult(
                        status=HealthStatus.UNHEALTHY,
                        timestamp=start_time,
                        response_time_ms=self.timeout * 1000,
                        error_message=error_msg,
                        error_code="HEALTH_CHECK_TIMEOUT",
                        details={
                            "timeout": self.timeout,
                            "attempts": self.max_retries + 1,
                        },
                    )

                    self._last_result = result
                    self._stats.update_failure()

                    # Log status change
                    if old_status != HealthStatus.UNHEALTHY:
                        logger.error(
                            f"Database health status changed: {old_status.value} -> {HealthStatus.UNHEALTHY.value} "
                            f"(reason: timeout)"
                        )
                        self._notify_status_change(old_status, HealthStatus.UNHEALTHY)

                    return result

                # Wait before retry
                await asyncio.sleep(1.0)

            except Exception as e:
                error_msg = f"Health check failed: {str(e)} (attempt {attempt + 1}/{self.max_retries + 1})"
                logger.warning(error_msg)

                if attempt == self.max_retries:
                    # Final attempt failed
                    self._current_status = HealthStatus.UNHEALTHY

                    # Determine error code based on exception type
                    error_code = "HEALTH_CHECK_FAILED"
                    if isinstance(e, asyncpg.ConnectionDoesNotExistError):
                        error_code = "HEALTH_CHECK_NO_CONNECTION"
                    elif isinstance(e, asyncpg.InterfaceError):
                        error_code = "HEALTH_CHECK_INTERFACE_ERROR"
                    elif isinstance(e, ConnectionError):
                        error_code = "HEALTH_CHECK_CONNECTION_ERROR"

                    result = HealthCheckResult(
                        status=HealthStatus.UNHEALTHY,
                        timestamp=start_time,
                        response_time_ms=0.0,
                        error_message=error_msg,
                        error_code=error_code,
                        details={
                            "exception_type": type(e).__name__,
                            "attempts": self.max_retries + 1,
                        },
                    )

                    self._last_result = result
                    self._stats.update_failure()

                    # Log status change
                    if old_status != HealthStatus.UNHEALTHY:
                        logger.error(
                            f"Database health status changed: {old_status.value} -> {HealthStatus.UNHEALTHY.value} "
                            f"(reason: {str(e)})"
                        )
                        self._notify_status_change(old_status, HealthStatus.UNHEALTHY)

                    return result

                # Wait before retry
                await asyncio.sleep(1.0)

        # This should never be reached, but just in case
        raise RuntimeError("Health check loop completed without returning result")

    async def check_connection_retry_capability(self) -> Dict[str, Any]:
        """Test connection retry capability and resilience.

        Returns:
            Dictionary with retry capability test results
        """
        retry_results = {
            "status": "healthy",
            "max_connections_tested": 0,
            "successful_acquisitions": 0,
            "failed_acquisitions": 0,
            "average_acquisition_time_ms": 0.0,
            "pool_exhaustion_handled": False
        }

        try:
            # Test multiple connection acquisitions
            acquisition_times = []
            successful_acquisitions = 0

            for i in range(min(5, self.pool.get_max_size() if hasattr(self.pool, 'get_max_size') else 5)):
                conn = None
                try:
                    start_time = asyncio.get_event_loop().time()
                    async with self.pool.acquire() as conn:
                        await conn.fetchval("SELECT 1")
                        end_time = asyncio.get_event_loop().time()
                        acquisition_times.append((end_time - start_time) * 1000)
                        successful_acquisitions += 1
                except Exception as e:
                    logger.warning(f"Connection acquisition {i+1} failed: {e}")
                    retry_results["failed_acquisitions"] += 1
                    # Ensure connection is properly released
                    if conn is not None:
                        try:
                            await conn.close()
                        except Exception:
                            pass
                finally:
                    conn = None

            retry_results["max_connections_tested"] = 5
            retry_results["successful_acquisitions"] = successful_acquisitions

            if acquisition_times:
                retry_results["average_acquisition_time_ms"] = sum(acquisition_times) / len(acquisition_times)

            # Test pool behavior under stress
            if successful_acquisitions == 5:
                retry_results["pool_exhaustion_handled"] = True
                retry_results["status"] = "healthy"
            elif successful_acquisitions >= 3:
                retry_results["status"] = "warning"
            else:
                retry_results["status"] = "critical"

        except Exception as e:
            logger.error(f"Connection retry capability test failed: {e}")
            retry_results["status"] = "critical"
            retry_results["error"] = str(e)

        return retry_results

    def get_health_status(self) -> HealthStatus:
        """Get current health status.

        Returns:
            HealthStatus: Current health status.
        """
        return self._current_status

    def get_last_result(self) -> Optional[HealthCheckResult]:
        """Get last health check result.

        Returns:
            Optional[HealthCheckResult]: Last health check result or None.
        """
        return self._last_result

    def get_stats(self) -> HealthStats:
        """Get health check statistics.

        Returns:
            HealthStats: Current health check statistics.
        """
        return self._stats

    def is_monitoring(self) -> bool:
        """Check if monitoring is currently active.

        Returns:
            bool: True if monitoring is active, False otherwise.
        """
        return self._is_monitoring

    def add_status_change_callback(
        self, callback: Callable[[HealthStatus, HealthStatus], None]
    ) -> None:
        """Add callback for status changes.

        Args:
            callback: Function to call when status changes.
                     Receives (old_status, new_status) as arguments.
        """
        self._status_change_callbacks.append(callback)
        logger.debug(f"Added status change callback: {callback.__name__}")

    def remove_status_change_callback(
        self, callback: Callable[[HealthStatus, HealthStatus], None]
    ) -> None:
        """Remove status change callback.

        Args:
            callback: Callback function to remove.
        """
        if callback in self._status_change_callbacks:
            self._status_change_callbacks.remove(callback)
            logger.debug(f"Removed status change callback: {callback.__name__}")

    def _notify_status_change(
        self, old_status: HealthStatus, new_status: HealthStatus
    ) -> None:
        """Notify all registered callbacks of status change.

        Args:
            old_status: Previous health status.
            new_status: New health status.
        """
        for callback in self._status_change_callbacks:
            try:
                callback(old_status, new_status)
            except Exception as e:
                logger.error(
                    f"Error in status change callback {callback.__name__}: {str(e)}",
                    exc_info=True,
                )

    def reset_stats(self) -> None:
        """Reset health check statistics."""
        logger.info("Resetting health check statistics")
        self._stats = HealthStats()

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_monitoring()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop_monitoring()


# Global health checker instance
_global_health_checker: Optional[HealthChecker] = None


def get_health_checker() -> Optional[HealthChecker]:
    """Get global health checker instance.

    Returns:
        Optional[HealthChecker]: Global health checker instance or None.
    """
    return _global_health_checker


def initialize_global_health_checker(
    pool: Pool, check_interval: float = 30.0, timeout: float = 5.0, max_retries: int = 3
) -> HealthChecker:
    """Initialize global health checker instance.

    Args:
        pool: asyncpg connection pool to monitor.
        check_interval: Interval between health checks in seconds.
        timeout: Timeout for individual health checks in seconds.
        max_retries: Maximum number of retries for failed checks.

    Returns:
        HealthChecker: Initialized health checker instance.

    Raises:
        RuntimeError: If global health checker is already initialized.
    """
    global _global_health_checker

    if _global_health_checker is not None:
        raise RuntimeError("Global health checker is already initialized")

    _global_health_checker = HealthChecker(pool, check_interval, timeout, max_retries)
    logger.info("Global health checker initialized")

    return _global_health_checker


async def close_global_health_checker() -> None:
    """Close global health checker instance."""
    global _global_health_checker

    if _global_health_checker is not None:
        await _global_health_checker.stop_monitoring()
        _global_health_checker = None
        logger.info("Global health checker closed")
    else:
        logger.debug("No global health checker to close")


async def get_global_health_status() -> HealthStatus:
    """Get global health status.

    Returns:
        HealthStatus: Current global health status.

    Raises:
        RuntimeError: If global health checker is not initialized.
    """
    if _global_health_checker is None:
        raise RuntimeError("Global health checker is not initialized")

    return _global_health_checker.get_health_status()


async def perform_global_health_check() -> HealthCheckResult:
    """Perform global health check.

    Returns:
        HealthCheckResult: Result of the health check.

    Raises:
        RuntimeError: If global health checker is not initialized.
    """
    if _global_health_checker is None:
        raise RuntimeError("Global health checker is not initialized")

    return await _global_health_checker.check_health()
