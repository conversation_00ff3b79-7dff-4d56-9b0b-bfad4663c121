"""Telecommunications-specific data importers.

This module provides specialized importers for telecommunications data types
including CDR, KPI, EP, and NLG data with industry-specific processing capabilities.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

# Lazy imports to avoid circular import warnings
# Import classes only when needed

def _get_cdr_classes():
    import importlib
    module = importlib.import_module('.cdr_importer', package=__name__)
    return module.CDRImporter, module.CDRConfig

def _get_kpi_classes():
    import importlib
    module = importlib.import_module('.kpi_importer', package=__name__)
    return module.KPIImporter, module.KPIConfig

def _get_ep_classes():
    import importlib
    module = importlib.import_module('.ep_importer', package=__name__)
    return module.EPImporter, module.EPConfig

def _get_nlg_classes():
    import importlib
    module = importlib.import_module('.nlg_importer', package=__name__)
    return module.NLGImporter, module.NLGConfig

# Module-level cache to avoid repeated imports
_module_cache = {}

# Expose classes through module-level getattr
def __getattr__(name):
    """Lazy loading of classes through module-level getattr."""
    if name in _module_cache:
        return _module_cache[name]
        
    if name == 'CDRImporter':
        CDRImporter, _ = _get_cdr_classes()
        _module_cache[name] = CDRImporter
        return CDRImporter
    elif name == 'CDRConfig':
        _, CDRConfig = _get_cdr_classes()
        _module_cache[name] = CDRConfig
        return CDRConfig
    elif name == 'KPIImporter':
        KPIImporter, _ = _get_kpi_classes()
        _module_cache[name] = KPIImporter
        return KPIImporter
    elif name == 'KPIConfig':
        _, KPIConfig = _get_kpi_classes()
        _module_cache[name] = KPIConfig
        return KPIConfig
    elif name == 'EPImporter':
        EPImporter, _ = _get_ep_classes()
        _module_cache[name] = EPImporter
        return EPImporter
    elif name == 'EPConfig':
        _, EPConfig = _get_ep_classes()
        _module_cache[name] = EPConfig
        return EPConfig
    elif name == 'NLGImporter':
        NLGImporter, _ = _get_nlg_classes()
        _module_cache[name] = NLGImporter
        return NLGImporter
    elif name == 'NLGConfig':
        _, NLGConfig = _get_nlg_classes()
        _module_cache[name] = NLGConfig
        return NLGConfig
    
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

# Version information
__version__ = "2.0.0"
__description__ = "Telecommunications data importers with advanced processing capabilities"

# Export all importers and configurations
__all__ = [
    # CDR (Call Detail Records)
    'CDRImporter',
    'CDRConfig',
    
    # KPI (Key Performance Indicators)
    'KPIImporter', 
    'KPIConfig',
    
    # EP (Engineering Parameters)
    'EPImporter',
    'EPConfig',
    
    # NLG (Network Location Geography)
    'NLGImporter',
    'NLGConfig',
    
    # Module metadata
    '__version__',
    '__description__'
]

# Supported telecommunications data types (using lazy loading)
def _get_supported_data_types():
    """Get supported data types with lazy loading of classes."""
    CDRImporter, CDRConfig = _get_cdr_classes()
    KPIImporter, KPIConfig = _get_kpi_classes()
    EPImporter, EPConfig = _get_ep_classes()
    NLGImporter, NLGConfig = _get_nlg_classes()

    return {
        'CDR': {
            'importer': CDRImporter,
            'config': CDRConfig,
            'description': 'Call Detail Records - voice, SMS, data usage records',
            'typical_formats': ['.csv', '.xlsx', '.txt'],
            'operators': ['telefonica', 'vodafone', 'telekom', 'generic']
        },
        'KPI': {
            'importer': KPIImporter,
            'config': KPIConfig,
            'description': 'Key Performance Indicators - network performance metrics',
            'typical_formats': ['.csv', '.xlsx', '.xml'],
            'operators': ['telefonica', 'vodafone', 'telekom', 'generic']
        },
        'EP': {
            'importer': EPImporter,
            'config': EPConfig,
            'description': 'Engineering Parameters - cell configuration and antenna data',
            'typical_formats': ['.csv', '.xlsx', '.xml'],
            'operators': ['telefonica', 'vodafone', 'telekom', 'generic']
        },
        'NLG': {
            'importer': NLGImporter,
            'config': NLGConfig,
            'description': 'Network Location Geography - geospatial network data',
            'typical_formats': ['.csv', '.xlsx', '.gpx', '.kml'],
            'operators': ['telefonica', 'vodafone', 'telekom', 'generic']
        }
    }

# Cache for supported data types
SUPPORTED_DATA_TYPES = None

def get_supported_data_types():
    """Get supported data types with caching."""
    global SUPPORTED_DATA_TYPES
    if SUPPORTED_DATA_TYPES is None:
        SUPPORTED_DATA_TYPES = _get_supported_data_types()
    return SUPPORTED_DATA_TYPES

# Default configurations for quick setup
DEFAULT_CONFIGS = {
    'CDR': {
        'operator': 'generic',
        'enable_geolocation': True,
        'enable_fraud_detection': True,
        'batch_size': 10000
    },
    'KPI': {
        'operator': 'generic',
        'enable_anomaly_detection': True,
        'enable_trend_analysis': True,
        'batch_size': 15000
    },
    'EP': {
        'operator': 'generic',
        'enable_coverage_analysis': True,
        'enable_interference_detection': True,
        'batch_size': 8000
    },
    'NLG': {
        'operator': 'generic',
        'enable_spatial_clustering': True,
        'enable_hotspot_detection': True,
        'batch_size': 15000
    }
}


def create_importer(data_type: str, operator: str = 'generic', **kwargs):
    """Factory function to create telecommunications importers.
    
    Args:
        data_type: Type of telecommunications data ('CDR', 'KPI', 'EP', 'NLG')
        operator: Telecom operator ('telefonica', 'vodafone', 'telekom', 'generic')
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured importer instance
        
    Raises:
        ValueError: If data_type is not supported
        
    Example:
        >>> # Create a CDR importer for Telefonica
        >>> cdr_importer = create_importer('CDR', 'telefonica', enable_fraud_detection=True)
        
        >>> # Create a KPI importer with custom batch size
        >>> kpi_importer = create_importer('KPI', batch_size=20000)
    """
    data_type = data_type.upper()
    
    supported_data_types = get_supported_data_types()
    if data_type not in supported_data_types:
        raise ValueError(
            f"Unsupported data type: {data_type}. "
            f"Supported types: {list(supported_data_types.keys())}"
        )
        
    # Get importer and config classes
    importer_info = supported_data_types[data_type]
    importer_class = importer_info['importer']
    config_class = importer_info['config']
    
    # Merge default config with provided kwargs
    config_dict = DEFAULT_CONFIGS[data_type].copy()
    config_dict['operator'] = operator
    config_dict['data_type'] = data_type.lower()  # Add required data_type field
    config_dict.update(kwargs)
    
    # Create configuration instance
    specific_config = config_class(**{k: v for k, v in config_dict.items() 
                                    if k in config_class.model_fields})
    
    # Create base config for ImporterConfig
    base_config = {
        'data_type': data_type.lower(),
        'name': f'{data_type.lower()}importer',
        'description': f'{data_type} data importer'
    }
    
    if data_type == 'CDR':
        return importer_class(config=base_config, cdr_config=specific_config)
    elif data_type == 'KPI':
        return importer_class(config=base_config, kpi_config=specific_config)
    elif data_type == 'EP':
        return importer_class(config=base_config, ep_config=specific_config)
    elif data_type == 'NLG':
        return importer_class(config=base_config, nlg_config=specific_config)
        

def get_supported_operators(data_type: str = None) -> list:
    """Get list of supported operators.
    
    Args:
        data_type: Optional data type to get operators for specific type
        
    Returns:
        List of supported operator names
    """
    supported_data_types = get_supported_data_types()
    if data_type:
        data_type = data_type.upper()
        if data_type in supported_data_types:
            return supported_data_types[data_type]['operators']
        else:
            return []
    else:
        # Return all unique operators
        all_operators = set()
        for info in supported_data_types.values():
            all_operators.update(info['operators'])
        return sorted(list(all_operators))
        

def get_supported_formats(data_type: str) -> list:
    """Get supported file formats for a data type.
    
    Args:
        data_type: Telecommunications data type
        
    Returns:
        List of supported file formats
    """
    supported_data_types = get_supported_data_types()
    data_type = data_type.upper()
    if data_type in supported_data_types:
        return supported_data_types[data_type]['typical_formats']
    else:
        return []
        

def validate_operator_support(data_type: str, operator: str) -> bool:
    """Validate if operator is supported for given data type.
    
    Args:
        data_type: Telecommunications data type
        operator: Operator name
        
    Returns:
        True if operator is supported
    """
    supported_operators = get_supported_operators(data_type)
    return operator.lower() in [op.lower() for op in supported_operators]
    

def get_importer_info(data_type: str = None) -> dict:
    """Get information about available importers.
    
    Args:
        data_type: Optional specific data type to get info for
        
    Returns:
        Dictionary with importer information
    """
    if data_type:
        data_type = data_type.upper()
        if data_type in SUPPORTED_DATA_TYPES:
            return {data_type: SUPPORTED_DATA_TYPES[data_type]}
        else:
            return {}
    else:
        return SUPPORTED_DATA_TYPES.copy()