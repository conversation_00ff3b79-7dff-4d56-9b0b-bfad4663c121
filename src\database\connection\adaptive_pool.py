"""Adaptive connection pool for optimized database connection management.

This module provides an adaptive connection pool that automatically adjusts
pool size based on workload and performance metrics.
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from typing import Any, Dict, Optional

import asyncpg
from ..exceptions import ConnectionError, DatabaseError

logger = logging.getLogger(__name__)


@dataclass
class PoolMetrics:
    """Connection pool performance metrics."""
    active_connections: int = 0
    idle_connections: int = 0
    total_connections: int = 0
    avg_acquisition_time: float = 0.0
    peak_connections: int = 0
    connection_failures: int = 0
    last_adjustment: float = 0.0
    adjustment_count: int = 0


@dataclass
class AdaptiveConfig:
    """Configuration for adaptive pool behavior."""
    min_size: int = 5
    max_size: int = 20
    initial_size: int = 10
    
    # Scaling thresholds
    scale_up_threshold: float = 0.8  # Scale up when utilization > 80%
    scale_down_threshold: float = 0.3  # Scale down when utilization < 30%
    
    # Timing parameters
    adjustment_interval: float = 30.0  # Minimum seconds between adjustments
    acquisition_timeout: float = 10.0  # Connection acquisition timeout
    
    # Performance thresholds
    slow_acquisition_threshold: float = 1.0  # Seconds
    max_adjustment_step: int = 3  # Maximum connections to add/remove at once


class AdaptiveConnectionPool:
    """Adaptive database connection pool.
    
    Automatically adjusts pool size based on workload patterns and
    performance metrics to optimize resource usage and response times.
    """
    
    def __init__(
        self,
        dsn: str,
        config: Optional[AdaptiveConfig] = None,
        **pool_kwargs
    ):
        """Initialize adaptive connection pool.
        
        Args:
            dsn: Database connection string
            config: Adaptive pool configuration
            **pool_kwargs: Additional asyncpg pool arguments
        """
        self.dsn = dsn
        self.config = config or AdaptiveConfig()
        self.pool_kwargs = pool_kwargs
        
        self._pool: Optional[asyncpg.Pool] = None
        self._metrics = PoolMetrics()
        self._acquisition_times = []
        self._adjustment_lock = asyncio.Lock()
        self._monitoring_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self._last_metrics_update = time.time()
        self._connection_history = []
    
    async def initialize(self) -> None:
        """Initialize the connection pool."""
        try:
            self._pool = await asyncpg.create_pool(
                self.dsn,
                min_size=self.config.min_size,
                max_size=self.config.initial_size,
                **self.pool_kwargs
            )
            
            self._metrics.total_connections = self.config.initial_size
            
            # Start monitoring task
            self._monitoring_task = asyncio.create_task(self._monitor_pool())
            
            logger.info(
                f"Adaptive connection pool initialized with {self.config.initial_size} connections"
            )
            
        except Exception as e:
            raise ConnectionError(
                f"Failed to initialize adaptive connection pool: {e}",
                error_code="POOL_INIT_FAILED",
                details={"dsn": self.dsn, "config": self.config}
            )
    
    async def acquire(self) -> asyncpg.Connection:
        """Acquire a connection from the pool.
        
        Returns:
            Database connection
        """
        if not self._pool:
            raise ConnectionError(
                "Pool not initialized",
                error_code="POOL_NOT_INITIALIZED"
            )
        
        start_time = time.time()
        
        try:
            connection = await asyncio.wait_for(
                self._pool.acquire(),
                timeout=self.config.acquisition_timeout
            )
            
            acquisition_time = time.time() - start_time
            self._record_acquisition_time(acquisition_time)
            
            return connection
            
        except asyncio.TimeoutError:
            self._metrics.connection_failures += 1
            raise ConnectionError(
                "Connection acquisition timeout",
                error_code="ACQUISITION_TIMEOUT",
                details={"timeout": self.config.acquisition_timeout}
            )
        except Exception as e:
            self._metrics.connection_failures += 1
            raise ConnectionError(
                f"Failed to acquire connection: {e}",
                error_code="ACQUISITION_FAILED"
            )
    
    async def release(self, connection: asyncpg.Connection) -> None:
        """Release a connection back to the pool.
        
        Args:
            connection: Connection to release
        """
        if self._pool:
            await self._pool.release(connection)
    
    async def close(self) -> None:
        """Close the connection pool."""
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        if self._pool:
            await self._pool.close()
            self._pool = None
        
        logger.info("Adaptive connection pool closed")
    
    def get_metrics(self) -> PoolMetrics:
        """Get current pool metrics.
        
        Returns:
            Current pool metrics
        """
        if self._pool:
            self._update_current_metrics()
        return self._metrics
    
    async def _monitor_pool(self) -> None:
        """Monitor pool performance and adjust size as needed."""
        while True:
            try:
                await asyncio.sleep(self.config.adjustment_interval)
                await self._evaluate_and_adjust()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Pool monitoring error: {e}")
                await asyncio.sleep(5)  # Brief pause before retrying
    
    async def _evaluate_and_adjust(self) -> None:
        """Evaluate pool performance and adjust size if needed."""
        async with self._adjustment_lock:
            current_time = time.time()
            
            # Skip if too soon since last adjustment
            if (current_time - self._metrics.last_adjustment) < self.config.adjustment_interval:
                return
            
            self._update_current_metrics()
            
            # Calculate utilization
            utilization = self._calculate_utilization()
            avg_acquisition_time = self._calculate_avg_acquisition_time()
            
            # Determine if adjustment is needed
            adjustment = self._determine_adjustment(utilization, avg_acquisition_time)
            
            if adjustment != 0:
                await self._adjust_pool_size(adjustment)
                self._metrics.last_adjustment = current_time
                self._metrics.adjustment_count += 1
                
                logger.info(
                    f"Pool adjusted by {adjustment} connections. "
                    f"New size: {self._metrics.total_connections}, "
                    f"Utilization: {utilization:.2%}, "
                    f"Avg acquisition time: {avg_acquisition_time:.3f}s"
                )
    
    def _update_current_metrics(self) -> None:
        """Update current pool metrics."""
        if not self._pool:
            return
        
        # Get current pool state
        self._metrics.total_connections = self._pool.get_size()
        self._metrics.idle_connections = self._pool.get_idle_size()
        self._metrics.active_connections = (
            self._metrics.total_connections - self._metrics.idle_connections
        )
        
        # Update peak connections
        if self._metrics.active_connections > self._metrics.peak_connections:
            self._metrics.peak_connections = self._metrics.active_connections
        
        # Update average acquisition time
        if self._acquisition_times:
            self._metrics.avg_acquisition_time = sum(self._acquisition_times) / len(self._acquisition_times)
    
    def _calculate_utilization(self) -> float:
        """Calculate current pool utilization.
        
        Returns:
            Utilization ratio (0.0 to 1.0)
        """
        if self._metrics.total_connections == 0:
            return 0.0
        
        return self._metrics.active_connections / self._metrics.total_connections
    
    def _calculate_avg_acquisition_time(self) -> float:
        """Calculate average acquisition time from recent samples.
        
        Returns:
            Average acquisition time in seconds
        """
        if not self._acquisition_times:
            return 0.0
        
        # Use recent samples (last 100)
        recent_times = self._acquisition_times[-100:]
        return sum(recent_times) / len(recent_times)
    
    def _determine_adjustment(self, utilization: float, avg_acquisition_time: float) -> int:
        """Determine pool size adjustment needed.
        
        Args:
            utilization: Current pool utilization
            avg_acquisition_time: Average connection acquisition time
            
        Returns:
            Number of connections to add (positive) or remove (negative)
        """
        # Scale up conditions
        if (
            utilization > self.config.scale_up_threshold or
            avg_acquisition_time > self.config.slow_acquisition_threshold
        ):
            if self._metrics.total_connections < self.config.max_size:
                # Calculate scale up amount
                needed = min(
                    self.config.max_adjustment_step,
                    self.config.max_size - self._metrics.total_connections
                )
                return needed
        
        # Scale down conditions
        elif utilization < self.config.scale_down_threshold:
            if self._metrics.total_connections > self.config.min_size:
                # Calculate scale down amount
                excess = min(
                    self.config.max_adjustment_step,
                    self._metrics.total_connections - self.config.min_size
                )
                return -excess
        
        return 0
    
    async def _adjust_pool_size(self, adjustment: int) -> None:
        """Adjust pool size by the specified amount.
        
        Args:
            adjustment: Number of connections to add (positive) or remove (negative)
        """
        if not self._pool:
            return
        
        try:
            current_size = self._pool.get_size()
            new_size = max(
                self.config.min_size,
                min(self.config.max_size, current_size + adjustment)
            )
            
            if new_size != current_size:
                # Create new pool with adjusted size
                old_pool = self._pool
                
                self._pool = await asyncpg.create_pool(
                    self.dsn,
                    min_size=self.config.min_size,
                    max_size=new_size,
                    **self.pool_kwargs
                )
                
                # Close old pool
                await old_pool.close()
                
                self._metrics.total_connections = new_size
                
        except Exception as e:
            logger.error(f"Failed to adjust pool size: {e}")
            raise DatabaseError(
                f"Pool size adjustment failed: {e}",
                error_code="POOL_ADJUSTMENT_FAILED",
                details={"adjustment": adjustment}
            )
    
    def _record_acquisition_time(self, acquisition_time: float) -> None:
        """Record connection acquisition time.
        
        Args:
            acquisition_time: Time taken to acquire connection
        """
        self._acquisition_times.append(acquisition_time)
        
        # Keep only recent samples to prevent memory growth
        if len(self._acquisition_times) > 1000:
            self._acquisition_times = self._acquisition_times[-500:]
    
    def __str__(self) -> str:
        """String representation of the pool."""
        return (
            f"AdaptiveConnectionPool("
            f"size={self._metrics.total_connections}, "
            f"active={self._metrics.active_connections}, "
            f"idle={self._metrics.idle_connections}, "
            f"utilization={self._calculate_utilization():.2%}"
            f")"
        )