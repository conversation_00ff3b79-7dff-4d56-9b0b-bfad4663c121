"""Database dialect support for query building.

This module provides classes for handling different SQL dialects
with proper syntax and feature support.
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional, Set

from loguru import logger

from ..exceptions import ValidationError


class DatabaseType(Enum):
    """Supported database types."""

    POSTGRESQL = "postgresql"
    MYSQL = "mysql"
    SQLITE = "sqlite"
    ORACLE = "oracle"
    SQLSERVER = "sqlserver"
    MARIADB = "mariadb"
    CLICKHOUSE = "clickhouse"
    REDSHIFT = "redshift"
    SNOWFLAKE = "snowflake"
    BIGQUERY = "bigquery"


class Dialect(ABC):
    """Abstract base class for SQL dialects."""

    def __init__(self, database_type: DatabaseType):
        """Initialize dialect.

        Args:
            database_type: Type of database
        """
        self.database_type = database_type

    @abstractmethod
    def escape_identifier(self, identifier: str) -> str:
        """Escape SQL identifier.

        Args:
            identifier: SQL identifier

        Returns:
            Escaped identifier
        """
        pass

    @abstractmethod
    def escape_string(self, value: str) -> str:
        """Escape string value.

        Args:
            value: String value

        Returns:
            Escaped string
        """
        pass

    @abstractmethod
    def get_limit_clause(self, limit: Optional[int], offset: Optional[int]) -> str:
        """Get LIMIT clause.

        Args:
            limit: Limit value
            offset: Offset value

        Returns:
            LIMIT clause
        """
        pass

    @abstractmethod
    def supports_feature(self, feature: str) -> bool:
        """Check if dialect supports a feature.

        Args:
            feature: Feature name

        Returns:
            True if supported
        """
        pass

    @abstractmethod
    def get_data_type_mapping(self) -> Dict[str, str]:
        """Get data type mapping.

        Returns:
            Data type mapping dictionary
        """
        pass

    def validate_identifier(self, identifier: str) -> bool:
        """Validate SQL identifier.

        Args:
            identifier: SQL identifier

        Returns:
            True if valid
        """
        if not identifier:
            return False

        # Basic validation - starts with letter or underscore
        if not (identifier[0].isalpha() or identifier[0] == "_"):
            return False

        # Contains only alphanumeric characters and underscores
        return all(c.isalnum() or c == "_" for c in identifier)

    def format_parameter(self, param_name: str) -> str:
        """Format parameter placeholder.

        Args:
            param_name: Parameter name

        Returns:
            Formatted parameter
        """
        return f"${param_name}"

    def get_current_timestamp(self) -> str:
        """Get current timestamp expression.

        Returns:
            Current timestamp expression
        """
        return "CURRENT_TIMESTAMP"

    def get_random_function(self) -> str:
        """Get random function name.

        Returns:
            Random function name
        """
        return "RANDOM()"


class PostgreSQLDialect(Dialect):
    """PostgreSQL dialect implementation."""

    def __init__(self):
        """Initialize PostgreSQL dialect."""
        super().__init__(DatabaseType.POSTGRESQL)
        self.features = {
            "window_functions",
            "cte",
            "recursive_cte",
            "json_functions",
            "array_functions",
            "full_text_search",
            "lateral_joins",
            "returning_clause",
            "upsert",
            "partial_indexes",
            "expression_indexes",
            "materialized_views",
            "foreign_data_wrappers",
            "custom_aggregates",
        }

    def escape_identifier(self, identifier: str) -> str:
        """Escape PostgreSQL identifier.

        Args:
            identifier: SQL identifier

        Returns:
            Escaped identifier
        """
        if not self.validate_identifier(identifier):
            raise ValidationError(f"Invalid identifier: {identifier}")
        return f'"{identifier}"'

    def escape_string(self, value: str) -> str:
        """Escape PostgreSQL string.

        Args:
            value: String value

        Returns:
            Escaped string
        """
        escaped = value.replace("'", "''")
        return f"'{escaped}'"

    def get_limit_clause(self, limit: Optional[int], offset: Optional[int]) -> str:
        """Get PostgreSQL LIMIT clause.

        Args:
            limit: Limit value
            offset: Offset value

        Returns:
            LIMIT clause
        """
        clauses = []
        if limit is not None:
            clauses.append(f"LIMIT {limit}")
        if offset is not None:
            clauses.append(f"OFFSET {offset}")
        return " ".join(clauses)

    def supports_feature(self, feature: str) -> bool:
        """Check if PostgreSQL supports a feature.

        Args:
            feature: Feature name

        Returns:
            True if supported
        """
        return feature in self.features

    def get_data_type_mapping(self) -> Dict[str, str]:
        """Get PostgreSQL data type mapping.

        Returns:
            Data type mapping dictionary
        """
        return {
            "string": "TEXT",
            "integer": "INTEGER",
            "bigint": "BIGINT",
            "float": "REAL",
            "double": "DOUBLE PRECISION",
            "decimal": "NUMERIC",
            "boolean": "BOOLEAN",
            "date": "DATE",
            "datetime": "TIMESTAMP",
            "time": "TIME",
            "json": "JSONB",
            "uuid": "UUID",
            "binary": "BYTEA",
            "array": "ARRAY",
            "geometry": "GEOMETRY",
            "geography": "GEOGRAPHY",
        }

    def format_parameter(self, param_name: str) -> str:
        """Format PostgreSQL parameter placeholder.

        Args:
            param_name: Parameter name

        Returns:
            Formatted parameter
        """
        return f"${param_name}"

    def get_upsert_clause(
        self, conflict_columns: List[str], update_columns: List[str]
    ) -> str:
        """Get PostgreSQL UPSERT clause.

        Args:
            conflict_columns: Columns that may conflict
            update_columns: Columns to update on conflict

        Returns:
            UPSERT clause
        """
        conflict_cols = ", ".join(
            [self.escape_identifier(col) for col in conflict_columns]
        )
        update_sets = ", ".join(
            [
                f"{self.escape_identifier(col)} = EXCLUDED.{self.escape_identifier(col)}"
                for col in update_columns
            ]
        )
        return f"ON CONFLICT ({conflict_cols}) DO UPDATE SET {update_sets}"


class MySQLDialect(Dialect):
    """MySQL dialect implementation."""

    def __init__(self):
        """Initialize MySQL dialect."""
        super().__init__(DatabaseType.MYSQL)
        self.features = {
            "window_functions",
            "cte",
            "json_functions",
            "full_text_search",
            "spatial_functions",
            "generated_columns",
            "check_constraints",
        }

    def escape_identifier(self, identifier: str) -> str:
        """Escape MySQL identifier.

        Args:
            identifier: SQL identifier

        Returns:
            Escaped identifier
        """
        if not self.validate_identifier(identifier):
            raise ValidationError(f"Invalid identifier: {identifier}")
        return f"`{identifier}`"

    def escape_string(self, value: str) -> str:
        """Escape MySQL string.

        Args:
            value: String value

        Returns:
            Escaped string
        """
        escaped = value.replace("\\", "\\\\").replace("'", "\\'").replace('"', '\\"')
        return f"'{escaped}'"

    def get_limit_clause(self, limit: Optional[int], offset: Optional[int]) -> str:
        """Get MySQL LIMIT clause.

        Args:
            limit: Limit value
            offset: Offset value

        Returns:
            LIMIT clause
        """
        if limit is not None and offset is not None:
            return f"LIMIT {offset}, {limit}"
        elif limit is not None:
            return f"LIMIT {limit}"
        elif offset is not None:
            return f"LIMIT {offset}, 18446744073709551615"  # MySQL max value
        return ""

    def supports_feature(self, feature: str) -> bool:
        """Check if MySQL supports a feature.

        Args:
            feature: Feature name

        Returns:
            True if supported
        """
        return feature in self.features

    def get_data_type_mapping(self) -> Dict[str, str]:
        """Get MySQL data type mapping.

        Returns:
            Data type mapping dictionary
        """
        return {
            "string": "VARCHAR(255)",
            "text": "TEXT",
            "integer": "INT",
            "bigint": "BIGINT",
            "float": "FLOAT",
            "double": "DOUBLE",
            "decimal": "DECIMAL",
            "boolean": "BOOLEAN",
            "date": "DATE",
            "datetime": "DATETIME",
            "timestamp": "TIMESTAMP",
            "time": "TIME",
            "json": "JSON",
            "binary": "BLOB",
            "geometry": "GEOMETRY",
            "point": "POINT",
            "linestring": "LINESTRING",
            "polygon": "POLYGON",
        }

    def format_parameter(self, param_name: str) -> str:
        """Format MySQL parameter placeholder.

        Args:
            param_name: Parameter name

        Returns:
            Formatted parameter
        """
        return "%s"

    def get_random_function(self) -> str:
        """Get MySQL random function name.

        Returns:
            Random function name
        """
        return "RAND()"

    def get_upsert_clause(self, update_columns: List[str]) -> str:
        """Get MySQL UPSERT clause.

        Args:
            update_columns: Columns to update on duplicate

        Returns:
            UPSERT clause
        """
        update_sets = ", ".join(
            [
                f"{self.escape_identifier(col)} = VALUES({self.escape_identifier(col)})"
                for col in update_columns
            ]
        )
        return f"ON DUPLICATE KEY UPDATE {update_sets}"


class SQLiteDialect(Dialect):
    """SQLite dialect implementation."""

    def __init__(self):
        """Initialize SQLite dialect."""
        super().__init__(DatabaseType.SQLITE)
        self.features = {
            "window_functions",
            "cte",
            "recursive_cte",
            "json_functions",
            "full_text_search",
            "partial_indexes",
            "expression_indexes",
        }

    def escape_identifier(self, identifier: str) -> str:
        """Escape SQLite identifier.

        Args:
            identifier: SQL identifier

        Returns:
            Escaped identifier
        """
        if not self.validate_identifier(identifier):
            raise ValidationError(f"Invalid identifier: {identifier}")
        return f"[{identifier}]"

    def escape_string(self, value: str) -> str:
        """Escape SQLite string.

        Args:
            value: String value

        Returns:
            Escaped string
        """
        escaped = value.replace("'", "''")
        return f"'{escaped}'"

    def get_limit_clause(self, limit: Optional[int], offset: Optional[int]) -> str:
        """Get SQLite LIMIT clause.

        Args:
            limit: Limit value
            offset: Offset value

        Returns:
            LIMIT clause
        """
        clauses = []
        if limit is not None:
            clauses.append(f"LIMIT {limit}")
        if offset is not None:
            clauses.append(f"OFFSET {offset}")
        return " ".join(clauses)

    def supports_feature(self, feature: str) -> bool:
        """Check if SQLite supports a feature.

        Args:
            feature: Feature name

        Returns:
            True if supported
        """
        return feature in self.features

    def get_data_type_mapping(self) -> Dict[str, str]:
        """Get SQLite data type mapping.

        Returns:
            Data type mapping dictionary
        """
        return {
            "string": "TEXT",
            "integer": "INTEGER",
            "float": "REAL",
            "boolean": "INTEGER",  # SQLite doesn't have native boolean
            "date": "TEXT",
            "datetime": "TEXT",
            "time": "TEXT",
            "json": "TEXT",
            "binary": "BLOB",
        }

    def format_parameter(self, param_name: str) -> str:
        """Format SQLite parameter placeholder.

        Args:
            param_name: Parameter name

        Returns:
            Formatted parameter
        """
        return "?"

    def get_upsert_clause(
        self, conflict_columns: List[str], update_columns: List[str]
    ) -> str:
        """Get SQLite UPSERT clause.

        Args:
            conflict_columns: Columns that may conflict
            update_columns: Columns to update on conflict

        Returns:
            UPSERT clause
        """
        conflict_cols = ", ".join(
            [self.escape_identifier(col) for col in conflict_columns]
        )
        update_sets = ", ".join(
            [
                f"{self.escape_identifier(col)} = excluded.{self.escape_identifier(col)}"
                for col in update_columns
            ]
        )
        return f"ON CONFLICT ({conflict_cols}) DO UPDATE SET {update_sets}"


class OracleDialect(Dialect):
    """Oracle dialect implementation."""

    def __init__(self):
        """Initialize Oracle dialect."""
        super().__init__(DatabaseType.ORACLE)
        self.features = {
            "window_functions",
            "cte",
            "recursive_cte",
            "hierarchical_queries",
            "pivot_unpivot",
            "model_clause",
            "flashback_queries",
            "spatial_functions",
        }

    def escape_identifier(self, identifier: str) -> str:
        """Escape Oracle identifier.

        Args:
            identifier: SQL identifier

        Returns:
            Escaped identifier
        """
        if not self.validate_identifier(identifier):
            raise ValidationError(f"Invalid identifier: {identifier}")
        return f'"{identifier.upper()}"'

    def escape_string(self, value: str) -> str:
        """Escape Oracle string.

        Args:
            value: String value

        Returns:
            Escaped string
        """
        escaped = value.replace("'", "''")
        return f"'{escaped}'"

    def get_limit_clause(self, limit: Optional[int], offset: Optional[int]) -> str:
        """Get Oracle LIMIT clause (using ROWNUM or OFFSET/FETCH).

        Args:
            limit: Limit value
            offset: Offset value

        Returns:
            LIMIT clause
        """
        clauses = []
        if offset is not None:
            clauses.append(f"OFFSET {offset} ROWS")
        if limit is not None:
            clauses.append(f"FETCH NEXT {limit} ROWS ONLY")
        return " ".join(clauses)

    def supports_feature(self, feature: str) -> bool:
        """Check if Oracle supports a feature.

        Args:
            feature: Feature name

        Returns:
            True if supported
        """
        return feature in self.features

    def get_data_type_mapping(self) -> Dict[str, str]:
        """Get Oracle data type mapping.

        Returns:
            Data type mapping dictionary
        """
        return {
            "string": "VARCHAR2(4000)",
            "text": "CLOB",
            "integer": "NUMBER(10)",
            "bigint": "NUMBER(19)",
            "float": "BINARY_FLOAT",
            "double": "BINARY_DOUBLE",
            "decimal": "NUMBER",
            "boolean": "NUMBER(1)",
            "date": "DATE",
            "datetime": "TIMESTAMP",
            "time": "TIMESTAMP",
            "binary": "BLOB",
            "geometry": "SDO_GEOMETRY",
        }

    def format_parameter(self, param_name: str) -> str:
        """Format Oracle parameter placeholder.

        Args:
            param_name: Parameter name

        Returns:
            Formatted parameter
        """
        return f":{param_name}"


class DialectFactory:
    """Factory for creating dialect instances."""

    _dialects = {
        DatabaseType.POSTGRESQL: PostgreSQLDialect,
        DatabaseType.MYSQL: MySQLDialect,
        DatabaseType.SQLITE: SQLiteDialect,
        DatabaseType.ORACLE: OracleDialect,
        DatabaseType.MARIADB: MySQLDialect,  # MariaDB uses MySQL dialect
    }

    @classmethod
    def create_dialect(cls, database_type: DatabaseType) -> Dialect:
        """Create dialect instance.

        Args:
            database_type: Type of database

        Returns:
            Dialect instance

        Raises:
            ValidationError: If database type is not supported
        """
        if database_type not in cls._dialects:
            raise ValidationError(f"Unsupported database type: {database_type}")

        dialect_class = cls._dialects[database_type]
        return dialect_class()

    @classmethod
    def get_supported_databases(cls) -> List[DatabaseType]:
        """Get list of supported database types.

        Returns:
            List of supported database types
        """
        return list(cls._dialects.keys())

    @classmethod
    def register_dialect(cls, database_type: DatabaseType, dialect_class: type):
        """Register custom dialect.

        Args:
            database_type: Database type
            dialect_class: Dialect class
        """
        cls._dialects[database_type] = dialect_class
        logger.info(f"Registered custom dialect for {database_type}")


# Convenience functions
def get_dialect(database_type: str) -> Dialect:
    """Get dialect instance by database type string.

    Args:
        database_type: Database type string

    Returns:
        Dialect instance
    """
    try:
        db_type = DatabaseType(database_type.lower())
        return DialectFactory.create_dialect(db_type)
    except ValueError:
        raise ValidationError(f"Unknown database type: {database_type}")


def detect_dialect_from_url(database_url: str) -> Dialect:
    """Detect dialect from database URL.

    Args:
        database_url: Database connection URL

    Returns:
        Dialect instance
    """
    url_lower = database_url.lower()

    if url_lower.startswith("postgresql://") or url_lower.startswith("postgres://"):
        return DialectFactory.create_dialect(DatabaseType.POSTGRESQL)
    elif url_lower.startswith("mysql://"):
        return DialectFactory.create_dialect(DatabaseType.MYSQL)
    elif url_lower.startswith("sqlite:///"):
        return DialectFactory.create_dialect(DatabaseType.SQLITE)
    elif url_lower.startswith("oracle://"):
        return DialectFactory.create_dialect(DatabaseType.ORACLE)
    else:
        raise ValidationError(f"Cannot detect database type from URL: {database_url}")
