# -*- coding: utf-8 -*-
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""
Data Extractor Module

This module provides functionality for extracting data from various file formats
with async support for efficient file reading operations.
"""

import asyncio
import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import aiofiles
import pandas as pd

from ..etl.processors.csv_processor import read_csv_to_dataframe, validate_csv_structure

# Setup logging
logger = logging.getLogger(__name__)


class DataExtractor:
    """
    Data extractor for various file formats with async support.

    Supports:
    - CSV files (with various delimiters)
    - TSV files
    - JSON files
    - Text files
    """

    def __init__(self):
        """Initialize the data extractor."""
        self.logger = logging.getLogger(self.__class__.__name__)
        self.supported_formats = {".csv", ".tsv", ".json", ".txt"}

    async def extract_from_file(
        self, file_path: Union[str, Path], file_type: Optional[str] = None, **kwargs
    ) -> pd.DataFrame:
        """
        Extract data from a file with automatic format detection.

        Args:
            file_path: Path to the file to extract data from
            file_type: Optional file type override ('csv', 'tsv', 'json', 'txt')
            **kwargs: Additional parameters for specific extractors

        Returns:
            pd.DataFrame: Extracted data

        Raises:
            FileNotFoundError: If the file doesn't exist
            ValueError: If the file format is not supported
            Exception: For other extraction errors
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # Determine file type
        if file_type is None:
            file_type = self._detect_file_type(file_path)

        self.logger.info(f"Extracting data from {file_path} as {file_type}")

        try:
            if file_type in ["csv", "tsv"]:
                return await self._extract_csv(file_path, **kwargs)
            elif file_type == "json":
                return await self._extract_json(file_path, **kwargs)
            elif file_type == "txt":
                return await self._extract_text(file_path, **kwargs)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")

        except Exception as e:
            self.logger.error(f"Failed to extract data from {file_path}: {str(e)}")
            raise

    async def extract_multiple_files(
        self, file_paths: List[Union[str, Path]], **kwargs
    ) -> Dict[str, pd.DataFrame]:
        """
        Extract data from multiple files concurrently.

        Args:
            file_paths: List of file paths to extract data from
            **kwargs: Additional parameters for extraction

        Returns:
            Dict[str, pd.DataFrame]: Dictionary mapping file paths to extracted DataFrames
        """
        self.logger.info(f"Extracting data from {len(file_paths)} files")

        tasks = []
        for file_path in file_paths:
            task = self.extract_from_file(file_path, **kwargs)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        extracted_data = {}
        for file_path, result in zip(file_paths, results):
            if isinstance(result, Exception):
                self.logger.error(f"Failed to extract {file_path}: {str(result)}")
                extracted_data[str(file_path)] = None
            else:
                extracted_data[str(file_path)] = result

        successful_extractions = sum(
            1 for v in extracted_data.values() if v is not None
        )
        self.logger.info(
            f"Successfully extracted {successful_extractions}/{len(file_paths)} files"
        )

        return extracted_data

    async def validate_file_structure(
        self,
        file_path: Union[str, Path],
        expected_columns: Optional[List[str]] = None,
        min_rows: int = 1,
        max_rows: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Validate file structure before extraction.

        Args:
            file_path: Path to the file to validate
            expected_columns: List of expected column names
            min_rows: Minimum number of rows expected
            max_rows: Maximum number of rows expected

        Returns:
            Dict[str, Any]: Validation results
        """
        file_path = Path(file_path)
        file_type = self._detect_file_type(file_path)

        if file_type in ["csv", "tsv"]:
            return await validate_csv_structure(
                file_path=str(file_path),
                expected_columns=expected_columns,
                min_rows=min_rows,
                max_rows=max_rows,
            )
        else:
            # Basic validation for other file types
            return {
                "valid": file_path.exists(),
                "file_size": file_path.stat().st_size if file_path.exists() else 0,
                "file_type": file_type,
            }

    def _detect_file_type(self, file_path: Path) -> str:
        """
        Detect file type based on extension.

        Args:
            file_path: Path to the file

        Returns:
            str: Detected file type
        """
        extension = file_path.suffix.lower()

        if extension == ".csv":
            return "csv"
        elif extension == ".tsv":
            return "tsv"
        elif extension == ".json":
            return "json"
        elif extension in [".txt", ".text"]:
            return "txt"
        else:
            # Default to CSV for unknown extensions
            self.logger.warning(
                f"Unknown file extension {extension}, defaulting to CSV"
            )
            return "csv"

    async def _extract_csv(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """
        Extract data from CSV/TSV files using the csv_processor.

        Args:
            file_path: Path to the CSV file
            **kwargs: Additional parameters for CSV reading

        Returns:
            pd.DataFrame: Extracted data
        """
        return await read_csv_to_dataframe(str(file_path), **kwargs)

    async def _extract_json(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """
        Extract data from JSON files.

        Args:
            file_path: Path to the JSON file
            **kwargs: Additional parameters for JSON reading

        Returns:
            pd.DataFrame: Extracted data
        """
        try:
            async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                content = await f.read()

            data = json.loads(content)

            # Handle different JSON structures
            if isinstance(data, list):
                # List of records
                df = pd.DataFrame(data)
            elif isinstance(data, dict):
                if "data" in data:
                    # JSON with data key
                    df = pd.DataFrame(data["data"])
                else:
                    # Single record or nested structure
                    df = pd.json_normalize(data)
            else:
                raise ValueError("Unsupported JSON structure")

            self.logger.info(f"Successfully extracted {len(df)} rows from JSON file")
            return df

        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format: {str(e)}")
        except Exception as e:
            raise Exception(f"Failed to extract JSON data: {str(e)}")

    async def _extract_text(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """
        Extract data from text files.

        Args:
            file_path: Path to the text file
            **kwargs: Additional parameters for text reading

        Returns:
            pd.DataFrame: Extracted data with each line as a row
        """
        delimiter = kwargs.get("delimiter", "\n")
        column_name = kwargs.get("column_name", "text")

        try:
            async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                content = await f.read()

            lines = content.split(delimiter)
            # Remove empty lines
            lines = [line.strip() for line in lines if line.strip()]

            df = pd.DataFrame({column_name: lines})

            self.logger.info(f"Successfully extracted {len(df)} lines from text file")
            return df

        except Exception as e:
            raise Exception(f"Failed to extract text data: {str(e)}")

    def get_file_info(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get information about a file.

        Args:
            file_path: Path to the file

        Returns:
            Dict[str, Any]: File information
        """
        file_path = Path(file_path)

        if not file_path.exists():
            return {"exists": False}

        stat = file_path.stat()

        return {
            "exists": True,
            "size": stat.st_size,
            "modified": stat.st_mtime,
            "extension": file_path.suffix,
            "detected_type": self._detect_file_type(file_path),
            "is_supported": file_path.suffix.lower()
            in {".csv", ".tsv", ".json", ".txt"},
        }
