#!/usr/bin/env python3
"""性能监控和内存分析器

该模块提供测试执行过程中的性能监控和内存分析功能。

主要功能:
- CPU使用率监控
- 内存使用监控
- 执行时间统计
- 性能瓶颈分析
- 内存泄漏检测
- 性能报告生成
"""

import gc
import os
import psutil
import threading
import time
import tracemalloc
from collections import defaultdict, deque
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from enum import Enum
import json
import csv


class MetricType(Enum):
    """指标类型枚举"""
    CPU_PERCENT = "cpu_percent"
    MEMORY_PERCENT = "memory_percent"
    MEMORY_RSS = "memory_rss"
    MEMORY_VMS = "memory_vms"
    DISK_IO_READ = "disk_io_read"
    DISK_IO_WRITE = "disk_io_write"
    NETWORK_IO_SENT = "network_io_sent"
    NETWORK_IO_RECV = "network_io_recv"
    EXECUTION_TIME = "execution_time"
    THREAD_COUNT = "thread_count"
    FILE_DESCRIPTORS = "file_descriptors"


@dataclass
class PerformanceMetric:
    """性能指标数据结构"""
    timestamp: datetime
    metric_type: MetricType
    value: float
    unit: str
    process_id: Optional[int] = None
    thread_id: Optional[int] = None
    context: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MemorySnapshot:
    """内存快照"""
    timestamp: datetime
    total_memory: int
    available_memory: int
    used_memory: int
    memory_percent: float
    process_memory: int
    process_memory_percent: float
    tracemalloc_current: int
    tracemalloc_peak: int
    gc_stats: Dict[str, Any]
    top_allocations: List[Tuple[str, int]] = field(default_factory=list)


@dataclass
class PerformanceProfile:
    """性能分析结果"""
    start_time: datetime
    end_time: datetime
    duration: timedelta
    metrics: List[PerformanceMetric] = field(default_factory=list)
    memory_snapshots: List[MemorySnapshot] = field(default_factory=list)
    peak_cpu: float = 0.0
    peak_memory: int = 0
    average_cpu: float = 0.0
    average_memory: int = 0
    total_allocations: int = 0
    memory_leaks: List[Dict[str, Any]] = field(default_factory=list)
    bottlenecks: List[Dict[str, Any]] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, 
                 sampling_interval: float = 0.1,
                 max_samples: int = 10000,
                 enable_memory_tracking: bool = True):
        """初始化性能监控器
        
        Args:
            sampling_interval: 采样间隔（秒）
            max_samples: 最大样本数
            enable_memory_tracking: 是否启用内存跟踪
        """
        self.sampling_interval = sampling_interval
        self.max_samples = max_samples
        self.enable_memory_tracking = enable_memory_tracking
        
        self.process = psutil.Process()
        self.metrics: deque = deque(maxlen=max_samples)
        self.memory_snapshots: deque = deque(maxlen=max_samples)
        
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._start_time: Optional[datetime] = None
        self._end_time: Optional[datetime] = None
        
        # 内存跟踪
        self._tracemalloc_started = False
        self._initial_memory = None
        
        # 性能阈值
        self.cpu_threshold = 80.0  # CPU使用率阈值
        self.memory_threshold = 80.0  # 内存使用率阈值
        self.memory_leak_threshold = 100 * 1024 * 1024  # 内存泄漏阈值（100MB）
    
    def start_monitoring(self, context: str = "default") -> None:
        """开始性能监控
        
        Args:
            context: 监控上下文
        """
        if self._monitoring:
            return
        
        self._monitoring = True
        self._start_time = datetime.now()
        self._initial_memory = self.process.memory_info().rss
        
        # 启动内存跟踪
        if self.enable_memory_tracking and not self._tracemalloc_started:
            tracemalloc.start()
            self._tracemalloc_started = True
        
        # 启动监控线程
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(context,),
            daemon=True
        )
        self._monitor_thread.start()
    
    def stop_monitoring(self) -> PerformanceProfile:
        """停止性能监控并返回分析结果
        
        Returns:
            性能分析结果
        """
        if not self._monitoring:
            return self._create_empty_profile()
        
        self._monitoring = False
        self._end_time = datetime.now()
        
        # 等待监控线程结束
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=1.0)
        
        # 最后一次采样
        self._collect_metrics("final")
        
        # 生成性能分析报告
        profile = self._analyze_performance()
        
        # 停止内存跟踪
        if self._tracemalloc_started:
            tracemalloc.stop()
            self._tracemalloc_started = False
        
        return profile
    
    def _monitor_loop(self, context: str) -> None:
        """监控循环"""
        while self._monitoring:
            try:
                self._collect_metrics(context)
                time.sleep(self.sampling_interval)
            except Exception as e:
                print(f"监控错误: {e}")
                break
    
    def _collect_metrics(self, context: str) -> None:
        """收集性能指标"""
        timestamp = datetime.now()
        
        try:
            # CPU指标
            cpu_percent = self.process.cpu_percent()
            self.metrics.append(PerformanceMetric(
                timestamp=timestamp,
                metric_type=MetricType.CPU_PERCENT,
                value=cpu_percent,
                unit="%",
                process_id=self.process.pid,
                context=context
            ))
            
            # 内存指标
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            self.metrics.append(PerformanceMetric(
                timestamp=timestamp,
                metric_type=MetricType.MEMORY_PERCENT,
                value=memory_percent,
                unit="%",
                process_id=self.process.pid,
                context=context
            ))
            
            self.metrics.append(PerformanceMetric(
                timestamp=timestamp,
                metric_type=MetricType.MEMORY_RSS,
                value=memory_info.rss,
                unit="bytes",
                process_id=self.process.pid,
                context=context
            ))
            
            # 线程数
            thread_count = self.process.num_threads()
            self.metrics.append(PerformanceMetric(
                timestamp=timestamp,
                metric_type=MetricType.THREAD_COUNT,
                value=thread_count,
                unit="count",
                process_id=self.process.pid,
                context=context
            ))
            
            # 文件描述符数（仅Linux/macOS）
            try:
                fd_count = self.process.num_fds()
                self.metrics.append(PerformanceMetric(
                    timestamp=timestamp,
                    metric_type=MetricType.FILE_DESCRIPTORS,
                    value=fd_count,
                    unit="count",
                    process_id=self.process.pid,
                    context=context
                ))
            except (AttributeError, psutil.AccessDenied):
                pass  # Windows不支持或权限不足
            
            # IO指标
            try:
                io_counters = self.process.io_counters()
                self.metrics.append(PerformanceMetric(
                    timestamp=timestamp,
                    metric_type=MetricType.DISK_IO_READ,
                    value=io_counters.read_bytes,
                    unit="bytes",
                    process_id=self.process.pid,
                    context=context
                ))
                
                self.metrics.append(PerformanceMetric(
                    timestamp=timestamp,
                    metric_type=MetricType.DISK_IO_WRITE,
                    value=io_counters.write_bytes,
                    unit="bytes",
                    process_id=self.process.pid,
                    context=context
                ))
            except (AttributeError, psutil.AccessDenied):
                pass
            
            # 内存快照
            if self.enable_memory_tracking:
                self._take_memory_snapshot(timestamp, context)
                
        except psutil.NoSuchProcess:
            self._monitoring = False
        except Exception as e:
            print(f"收集指标时出错: {e}")
    
    def _take_memory_snapshot(self, timestamp: datetime, context: str) -> None:
        """拍摄内存快照"""
        try:
            # 系统内存信息
            system_memory = psutil.virtual_memory()
            
            # 进程内存信息
            process_memory = self.process.memory_info()
            process_memory_percent = self.process.memory_percent()
            
            # tracemalloc信息
            tracemalloc_current = 0
            tracemalloc_peak = 0
            top_allocations = []
            
            if self._tracemalloc_started:
                current, peak = tracemalloc.get_traced_memory()
                tracemalloc_current = current
                tracemalloc_peak = peak
                
                # 获取top分配
                snapshot = tracemalloc.take_snapshot()
                top_stats = snapshot.statistics('lineno')[:10]
                top_allocations = [
                    (str(stat.traceback), stat.size)
                    for stat in top_stats
                ]
            
            # GC统计
            gc_stats = {
                'collections': gc.get_stats(),
                'count': gc.get_count(),
                'threshold': gc.get_threshold()
            }
            
            snapshot = MemorySnapshot(
                timestamp=timestamp,
                total_memory=system_memory.total,
                available_memory=system_memory.available,
                used_memory=system_memory.used,
                memory_percent=system_memory.percent,
                process_memory=process_memory.rss,
                process_memory_percent=process_memory_percent,
                tracemalloc_current=tracemalloc_current,
                tracemalloc_peak=tracemalloc_peak,
                gc_stats=gc_stats,
                top_allocations=top_allocations
            )
            
            self.memory_snapshots.append(snapshot)
            
        except Exception as e:
            print(f"拍摄内存快照时出错: {e}")
    
    def _analyze_performance(self) -> PerformanceProfile:
        """分析性能数据"""
        if not self._start_time or not self._end_time:
            return self._create_empty_profile()
        
        duration = self._end_time - self._start_time
        
        # 计算统计信息
        cpu_metrics = [m for m in self.metrics if m.metric_type == MetricType.CPU_PERCENT]
        memory_metrics = [m for m in self.metrics if m.metric_type == MetricType.MEMORY_RSS]
        
        peak_cpu = max((m.value for m in cpu_metrics), default=0.0)
        peak_memory = max((m.value for m in memory_metrics), default=0)
        average_cpu = sum(m.value for m in cpu_metrics) / len(cpu_metrics) if cpu_metrics else 0.0
        average_memory = sum(m.value for m in memory_metrics) / len(memory_metrics) if memory_metrics else 0
        
        # 检测内存泄漏
        memory_leaks = self._detect_memory_leaks()
        
        # 检测性能瓶颈
        bottlenecks = self._detect_bottlenecks()
        
        # 生成建议
        recommendations = self._generate_recommendations(peak_cpu, peak_memory, memory_leaks, bottlenecks)
        
        # 计算总分配
        total_allocations = 0
        if self.memory_snapshots:
            latest_snapshot = self.memory_snapshots[-1]
            total_allocations = latest_snapshot.tracemalloc_current
        
        return PerformanceProfile(
            start_time=self._start_time,
            end_time=self._end_time,
            duration=duration,
            metrics=list(self.metrics),
            memory_snapshots=list(self.memory_snapshots),
            peak_cpu=peak_cpu,
            peak_memory=peak_memory,
            average_cpu=average_cpu,
            average_memory=average_memory,
            total_allocations=total_allocations,
            memory_leaks=memory_leaks,
            bottlenecks=bottlenecks,
            recommendations=recommendations
        )
    
    def _detect_memory_leaks(self) -> List[Dict[str, Any]]:
        """检测内存泄漏"""
        leaks = []
        
        if len(self.memory_snapshots) < 2:
            return leaks
        
        # 比较初始和最终内存使用
        initial_snapshot = self.memory_snapshots[0]
        final_snapshot = self.memory_snapshots[-1]
        
        memory_growth = final_snapshot.process_memory - initial_snapshot.process_memory
        
        if memory_growth > self.memory_leak_threshold:
            leaks.append({
                'type': 'process_memory_growth',
                'growth': memory_growth,
                'threshold': self.memory_leak_threshold,
                'severity': 'high' if memory_growth > self.memory_leak_threshold * 2 else 'medium'
            })
        
        # 检查tracemalloc增长
        if self._tracemalloc_started:
            tracemalloc_growth = final_snapshot.tracemalloc_current - initial_snapshot.tracemalloc_current
            if tracemalloc_growth > self.memory_leak_threshold:
                leaks.append({
                    'type': 'tracemalloc_growth',
                    'growth': tracemalloc_growth,
                    'threshold': self.memory_leak_threshold,
                    'severity': 'high' if tracemalloc_growth > self.memory_leak_threshold * 2 else 'medium'
                })
        
        return leaks
    
    def _detect_bottlenecks(self) -> List[Dict[str, Any]]:
        """检测性能瓶颈"""
        bottlenecks = []
        
        # CPU瓶颈
        cpu_metrics = [m for m in self.metrics if m.metric_type == MetricType.CPU_PERCENT]
        high_cpu_count = sum(1 for m in cpu_metrics if m.value > self.cpu_threshold)
        
        if high_cpu_count > len(cpu_metrics) * 0.1:  # 超过10%的时间CPU高负载
            bottlenecks.append({
                'type': 'cpu_bottleneck',
                'threshold': self.cpu_threshold,
                'high_usage_ratio': high_cpu_count / len(cpu_metrics) if cpu_metrics else 0,
                'severity': 'high' if high_cpu_count > len(cpu_metrics) * 0.3 else 'medium'
            })
        
        # 内存瓶颈
        memory_metrics = [m for m in self.metrics if m.metric_type == MetricType.MEMORY_PERCENT]
        high_memory_count = sum(1 for m in memory_metrics if m.value > self.memory_threshold)
        
        if high_memory_count > len(memory_metrics) * 0.1:
            bottlenecks.append({
                'type': 'memory_bottleneck',
                'threshold': self.memory_threshold,
                'high_usage_ratio': high_memory_count / len(memory_metrics) if memory_metrics else 0,
                'severity': 'high' if high_memory_count > len(memory_metrics) * 0.3 else 'medium'
            })
        
        return bottlenecks
    
    def _generate_recommendations(self, peak_cpu: float, peak_memory: int, 
                                memory_leaks: List[Dict], bottlenecks: List[Dict]) -> List[str]:
        """生成性能优化建议"""
        recommendations = []
        
        # CPU建议
        if peak_cpu > self.cpu_threshold:
            recommendations.append(f"CPU使用率峰值达到{peak_cpu:.1f}%，建议优化算法复杂度或使用多线程")
        
        # 内存建议
        if peak_memory > 1024 * 1024 * 1024:  # 1GB
            recommendations.append(f"内存使用峰值达到{peak_memory / 1024 / 1024 / 1024:.1f}GB，建议优化内存使用")
        
        # 内存泄漏建议
        if memory_leaks:
            recommendations.append("检测到潜在内存泄漏，建议检查对象生命周期管理")
        
        # 瓶颈建议
        for bottleneck in bottlenecks:
            if bottleneck['type'] == 'cpu_bottleneck':
                recommendations.append("检测到CPU瓶颈，建议使用性能分析工具定位热点代码")
            elif bottleneck['type'] == 'memory_bottleneck':
                recommendations.append("检测到内存瓶颈，建议优化数据结构或增加内存")
        
        return recommendations
    
    def _create_empty_profile(self) -> PerformanceProfile:
        """创建空的性能分析结果"""
        now = datetime.now()
        return PerformanceProfile(
            start_time=now,
            end_time=now,
            duration=timedelta(0)
        )
    
    @contextmanager
    def monitor(self, context: str = "default"):
        """性能监控上下文管理器
        
        Args:
            context: 监控上下文
            
        Yields:
            性能监控器实例
        """
        self.start_monitoring(context)
        try:
            yield self
        finally:
            profile = self.stop_monitoring()
            return profile
    
    def get_current_metrics(self) -> Dict[str, float]:
        """获取当前性能指标
        
        Returns:
            当前性能指标字典
        """
        try:
            return {
                'cpu_percent': self.process.cpu_percent(),
                'memory_percent': self.process.memory_percent(),
                'memory_rss': self.process.memory_info().rss,
                'thread_count': self.process.num_threads()
            }
        except Exception:
            return {}
    
    def export_metrics(self, file_path: str, format: str = 'json') -> None:
        """导出性能指标
        
        Args:
            file_path: 导出文件路径
            format: 导出格式（json, csv）
        """
        if format.lower() == 'json':
            self._export_json(file_path)
        elif format.lower() == 'csv':
            self._export_csv(file_path)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def _export_json(self, file_path: str) -> None:
        """导出为JSON格式"""
        data = {
            'metrics': [
                {
                    'timestamp': m.timestamp.isoformat(),
                    'metric_type': m.metric_type.value,
                    'value': m.value,
                    'unit': m.unit,
                    'process_id': m.process_id,
                    'thread_id': m.thread_id,
                    'context': m.context,
                    'metadata': m.metadata
                }
                for m in self.metrics
            ],
            'memory_snapshots': [
                {
                    'timestamp': s.timestamp.isoformat(),
                    'total_memory': s.total_memory,
                    'available_memory': s.available_memory,
                    'used_memory': s.used_memory,
                    'memory_percent': s.memory_percent,
                    'process_memory': s.process_memory,
                    'process_memory_percent': s.process_memory_percent,
                    'tracemalloc_current': s.tracemalloc_current,
                    'tracemalloc_peak': s.tracemalloc_peak,
                    'gc_stats': s.gc_stats,
                    'top_allocations': s.top_allocations
                }
                for s in self.memory_snapshots
            ]
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def _export_csv(self, file_path: str) -> None:
        """导出为CSV格式"""
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow([
                'timestamp', 'metric_type', 'value', 'unit',
                'process_id', 'thread_id', 'context'
            ])
            
            # 写入数据
            for metric in self.metrics:
                writer.writerow([
                    metric.timestamp.isoformat(),
                    metric.metric_type.value,
                    metric.value,
                    metric.unit,
                    metric.process_id,
                    metric.thread_id,
                    metric.context
                ])


class MemoryProfiler:
    """内存分析器"""
    
    def __init__(self):
        """初始化内存分析器"""
        self.snapshots: List[tracemalloc.Snapshot] = []
        self.started = False
    
    def start(self) -> None:
        """开始内存分析"""
        if not self.started:
            tracemalloc.start()
            self.started = True
    
    def stop(self) -> None:
        """停止内存分析"""
        if self.started:
            tracemalloc.stop()
            self.started = False
    
    def take_snapshot(self) -> tracemalloc.Snapshot:
        """拍摄内存快照
        
        Returns:
            内存快照
        """
        if not self.started:
            raise RuntimeError("内存分析器未启动")
        
        snapshot = tracemalloc.take_snapshot()
        self.snapshots.append(snapshot)
        return snapshot
    
    def compare_snapshots(self, snapshot1: tracemalloc.Snapshot, 
                         snapshot2: tracemalloc.Snapshot) -> List[Dict[str, Any]]:
        """比较两个内存快照
        
        Args:
            snapshot1: 第一个快照
            snapshot2: 第二个快照
            
        Returns:
            内存差异列表
        """
        top_stats = snapshot2.compare_to(snapshot1, 'lineno')
        
        differences = []
        for stat in top_stats[:20]:  # 取前20个差异最大的
            differences.append({
                'traceback': str(stat.traceback),
                'size_diff': stat.size_diff,
                'count_diff': stat.count_diff,
                'size': stat.size,
                'count': stat.count
            })
        
        return differences
    
    def get_top_allocations(self, snapshot: Optional[tracemalloc.Snapshot] = None, 
                           limit: int = 10) -> List[Dict[str, Any]]:
        """获取最大内存分配
        
        Args:
            snapshot: 内存快照，如果为None则使用最新的
            limit: 返回的条目数限制
            
        Returns:
            最大内存分配列表
        """
        if snapshot is None:
            if not self.snapshots:
                return []
            snapshot = self.snapshots[-1]
        
        top_stats = snapshot.statistics('lineno')[:limit]
        
        allocations = []
        for stat in top_stats:
            allocations.append({
                'traceback': str(stat.traceback),
                'size': stat.size,
                'count': stat.count,
                'size_mb': stat.size / 1024 / 1024
            })
        
        return allocations
    
    @contextmanager
    def profile(self):
        """内存分析上下文管理器"""
        self.start()
        initial_snapshot = self.take_snapshot()
        try:
            yield self
        finally:
            final_snapshot = self.take_snapshot()
            self.stop()
            
            # 返回分析结果
            return {
                'initial_snapshot': initial_snapshot,
                'final_snapshot': final_snapshot,
                'differences': self.compare_snapshots(initial_snapshot, final_snapshot),
                'top_allocations': self.get_top_allocations(final_snapshot)
            }


# 使用示例
if __name__ == "__main__":
    import time
    
    # 性能监控示例
    monitor = PerformanceMonitor(sampling_interval=0.1)
    
    print("开始性能监控...")
    monitor.start_monitoring("test_context")
    
    # 模拟一些工作负载
    data = []
    for i in range(100000):
        data.append(i ** 2)
    
    time.sleep(2)
    
    # 停止监控并获取结果
    profile = monitor.stop_monitoring()
    
    print(f"监控时长: {profile.duration}")
    print(f"峰值CPU: {profile.peak_cpu:.1f}%")
    print(f"峰值内存: {profile.peak_memory / 1024 / 1024:.1f} MB")
    print(f"平均CPU: {profile.average_cpu:.1f}%")
    print(f"平均内存: {profile.average_memory / 1024 / 1024:.1f} MB")
    
    if profile.memory_leaks:
        print("检测到内存泄漏:")
        for leak in profile.memory_leaks:
            print(f"  - {leak}")
    
    if profile.bottlenecks:
        print("检测到性能瓶颈:")
        for bottleneck in profile.bottlenecks:
            print(f"  - {bottleneck}")
    
    if profile.recommendations:
        print("优化建议:")
        for rec in profile.recommendations:
            print(f"  - {rec}")
    
    # 导出指标
    monitor.export_metrics("performance_metrics.json", "json")
    monitor.export_metrics("performance_metrics.csv", "csv")
    print("性能指标已导出")
    
    # 内存分析示例
    print("\n内存分析示例...")
    profiler = MemoryProfiler()
    
    with profiler.profile() as p:
        # 模拟内存分配
        large_data = [list(range(1000)) for _ in range(1000)]
        time.sleep(1)
    
    print("内存分析完成")