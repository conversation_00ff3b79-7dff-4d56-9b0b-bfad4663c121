"""Geometry processing module

Provides geometry object processing functionality based on shapely.
"""

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pyproj
from pyproj import Transformer
from shapely import wkb, wkt
from shapely.affinity import rotate, scale, translate
from shapely.geometry import (
    GeometryCollection,
    LineString,
    MultiLineString,
    MultiPoint,
    MultiPolygon,
    Point,
    Polygon,
    box,
)
from shapely.ops import snap, split, transform, unary_union

logger = logging.getLogger(__name__)


class GeometryProcessor:
    """Geometry processor

    Provides geometry object creation, transformation, analysis and processing functionality.
    """

    def __init__(self):
        """
        Initialize geometry processor
        """
        self.logger = logging.getLogger(self.__class__.__name__)

    def create_point(self, x: float, y: float, z: Optional[float] = None) -> Point:
        """
        Create point geometry object

        Args:
            x: X coordinate
            y: Y coordinate
            z: Z coordinate (optional)

        Returns:
            Point: Point geometry object
        """
        if z is not None:
            return Point(x, y, z)
        return Point(x, y)

    def create_linestring(self, coordinates: List[Tuple[float, float]]) -> LineString:
        """
        Create line geometry object

        Args:
            coordinates: List of coordinate points

        Returns:
            LineString: Line geometry object

        Raises:
            ValueError: Insufficient coordinate points
        """
        if len(coordinates) < 2:
            raise ValueError("Line geometry requires at least 2 coordinate points")

        return LineString(coordinates)

    def create_polygon(
        self,
        exterior: List[Tuple[float, float]],
        holes: Optional[List[List[Tuple[float, float]]]] = None,
    ) -> Polygon:
        """
        Create polygon geometry object

        Args:
            exterior: Exterior ring coordinate points list
            holes: Interior ring coordinate points list (optional)

        Returns:
            Polygon: Polygon geometry object

        Raises:
            ValueError: Insufficient coordinate points
        """
        if len(exterior) < 3:
            raise ValueError("Polygon geometry requires at least 3 coordinate points")

        # Ensure polygon is closed
        if exterior[0] != exterior[-1]:
            exterior.append(exterior[0])

        return Polygon(exterior, holes)

    def create_rectangle(
        self, min_x: float, min_y: float, max_x: float, max_y: float
    ) -> Polygon:
        """
        Create rectangle geometry object

        Args:
            min_x: Minimum X coordinate
            min_y: Minimum Y coordinate
            max_x: Maximum X coordinate
            max_y: Maximum Y coordinate

        Returns:
            Polygon: Rectangle geometry object
        """
        return box(min_x, min_y, max_x, max_y)

    def create_circle(
        self, center_x: float, center_y: float, radius: float, resolution: int = 32
    ) -> Polygon:
        """
        Create circle geometry object

        Args:
            center_x: Circle center X coordinate
            center_y: Circle center Y coordinate
            radius: Radius
            resolution: Circle resolution (number of sides)

        Returns:
            Polygon: Circle geometry object
        """
        center = Point(center_x, center_y)
        circle = center.buffer(radius, resolution=resolution)
        return circle

    def buffer_geometry(
        self,
        geometry: Union[Point, LineString, Polygon],
        distance: float,
        resolution: int = 16,
        cap_style: int = 1,
        join_style: int = 1,
    ) -> Polygon:
        """
        Geometry object buffer analysis

        Args:
            geometry: Input geometry object
            distance: Buffer distance
            resolution: Buffer resolution
            cap_style: End cap style (1=round, 2=flat, 3=square)
            join_style: Join style (1=round, 2=mitre, 3=bevel)

        Returns:
            Polygon: Buffer geometry object
        """
        try:
            buffered = geometry.buffer(
                distance,
                resolution=resolution,
                cap_style=cap_style,
                join_style=join_style,
            )
            self.logger.info(f"Buffer analysis completed, distance: {distance}")
            return buffered
        except Exception as e:
            self.logger.error(f"Buffer analysis failed: {e}")
            raise ValueError(f"Buffer analysis failed: {e}")

    def intersect_geometries(
        self,
        geom1: Union[Point, LineString, Polygon],
        geom2: Union[Point, LineString, Polygon],
    ) -> Union[Point, LineString, Polygon, GeometryCollection]:
        """
        Geometry object intersection analysis

        Args:
            geom1: Geometry object 1
            geom2: Geometry object 2

        Returns:
            Geometry object: Intersection result
        """
        try:
            intersection = geom1.intersection(geom2)
            self.logger.info(
                f"Intersection analysis completed, result type: {type(intersection).__name__}"
            )
            return intersection
        except Exception as e:
            self.logger.error(f"Intersection analysis failed: {e}")
            raise ValueError(f"Intersection analysis failed: {e}")

    def union_geometries(
        self, geometries: List[Union[Point, LineString, Polygon]]
    ) -> Union[Point, LineString, Polygon, MultiPoint, MultiLineString, MultiPolygon]:
        """
        Geometry object union analysis

        Args:
            geometries: List of geometry objects

        Returns:
            Geometry object: Union result
        """
        try:
            union_result = unary_union(geometries)
            self.logger.info(
                f"Union analysis completed, input count: {len(geometries)}"
            )
            return union_result
        except Exception as e:
            self.logger.error(f"Union analysis failed: {e}")
            raise ValueError(f"Union analysis failed: {e}")

    def difference_geometries(
        self,
        geom1: Union[Point, LineString, Polygon],
        geom2: Union[Point, LineString, Polygon],
    ) -> Union[Point, LineString, Polygon, GeometryCollection]:
        """
        Geometry object difference analysis

        Args:
            geom1: Geometry object to subtract from
            geom2: Geometry object to subtract

        Returns:
            Geometry object: Difference result
        """
        try:
            difference = geom1.difference(geom2)
            self.logger.info(
                f"Difference analysis completed, result type: {type(difference).__name__}"
            )
            return difference
        except Exception as e:
            self.logger.error(f"Difference analysis failed: {e}")
            raise ValueError(f"Difference analysis failed: {e}")

    def transform_geometry(
        self, geometry: Union[Point, LineString, Polygon], src_crs: str, dst_crs: str
    ) -> Union[Point, LineString, Polygon]:
        """
        Geometry object coordinate transformation

        Args:
            geometry: Input geometry object
            src_crs: Source coordinate system
            dst_crs: Target coordinate system

        Returns:
            Geometry object: Transformed geometry object
        """
        try:
            transformer = Transformer.from_crs(src_crs, dst_crs, always_xy=True)

            def transform_coords(x, y, z=None):
                if z is not None:
                    return transformer.transform(x, y, z)
                else:
                    return transformer.transform(x, y)

            transformed = transform(transform_coords, geometry)
            self.logger.info(
                f"Coordinate transformation completed: {src_crs} -> {dst_crs}"
            )
            return transformed
        except Exception as e:
            self.logger.error(f"Coordinate transformation failed: {e}")
            raise ValueError(f"Coordinate transformation failed: {e}")

    def rotate_geometry(
        self,
        geometry: Union[Point, LineString, Polygon],
        angle: float,
        origin: str = "center",
    ) -> Union[Point, LineString, Polygon]:
        """
        Geometry object rotation

        Args:
            geometry: Input geometry object
            angle: Rotation angle (degrees)
            origin: Rotation center ('center', 'centroid' or coordinate point)

        Returns:
            Geometry object: Rotated geometry object
        """
        try:
            rotated = rotate(geometry, angle, origin=origin)
            self.logger.info(f"Geometry rotation completed, angle: {angle} degrees")
            return rotated
        except Exception as e:
            self.logger.error(f"Geometry rotation failed: {e}")
            raise ValueError(f"Geometry rotation failed: {e}")

    def scale_geometry(
        self,
        geometry: Union[Point, LineString, Polygon],
        xfact: float = 1.0,
        yfact: float = 1.0,
        origin: str = "center",
    ) -> Union[Point, LineString, Polygon]:
        """
        Geometry object scaling

        Args:
            geometry: Input geometry object
            xfact: X direction scaling factor
            yfact: Y direction scaling factor
            origin: Scaling center

        Returns:
            Geometry object: Scaled geometry object
        """
        try:
            scaled = scale(geometry, xfact=xfact, yfact=yfact, origin=origin)
            self.logger.info(f"Geometry scaling completed, X: {xfact}, Y: {yfact}")
            return scaled
        except Exception as e:
            self.logger.error(f"Geometry scaling failed: {e}")
            raise ValueError(f"Geometry scaling failed: {e}")

    def translate_geometry(
        self,
        geometry: Union[Point, LineString, Polygon],
        xoff: float = 0.0,
        yoff: float = 0.0,
    ) -> Union[Point, LineString, Polygon]:
        """
        Geometry object translation

        Args:
            geometry: Input geometry object
            xoff: X direction offset
            yoff: Y direction offset

        Returns:
            Geometry object: Translated geometry object
        """
        try:
            translated = translate(geometry, xoff=xoff, yoff=yoff)
            self.logger.info(f"Geometry translation completed, X: {xoff}, Y: {yoff}")
            return translated
        except Exception as e:
            self.logger.error(f"Geometry translation failed: {e}")
            raise ValueError(f"Geometry translation failed: {e}")

    def simplify_geometry(
        self,
        geometry: Union[LineString, Polygon],
        tolerance: float,
        preserve_topology: bool = True,
    ) -> Union[LineString, Polygon]:
        """
        Geometry object simplification

        Args:
            geometry: Input geometry object
            tolerance: Simplification tolerance
            preserve_topology: Whether to preserve topology

        Returns:
            Geometry object: Simplified geometry object
        """
        try:
            simplified = geometry.simplify(
                tolerance, preserve_topology=preserve_topology
            )
            self.logger.info(
                f"Geometry simplification completed, tolerance: {tolerance}"
            )
            return simplified
        except Exception as e:
            self.logger.error(f"Geometry simplification failed: {e}")
            raise ValueError(f"Geometry simplification failed: {e}")

    def get_geometry_properties(
        self, geometry: Union[Point, LineString, Polygon]
    ) -> Dict[str, Any]:
        """
        Get geometry object properties

        Args:
            geometry: Input geometry object

        Returns:
            dict: Geometry properties dictionary
        """
        properties = {
            "geometry_type": type(geometry).__name__,
            "is_valid": geometry.is_valid,
            "is_empty": geometry.is_empty,
            "bounds": geometry.bounds,
        }

        if hasattr(geometry, "area"):
            properties["area"] = geometry.area

        if hasattr(geometry, "length"):
            properties["length"] = geometry.length

        if hasattr(geometry, "centroid"):
            centroid = geometry.centroid
            properties["centroid"] = (centroid.x, centroid.y)

        return properties

    def geometry_to_wkt(self, geometry: Union[Point, LineString, Polygon]) -> str:
        """
        Convert geometry object to WKT format

        Args:
            geometry: Input geometry object

        Returns:
            str: WKT string
        """
        return geometry.wkt

    def wkt_to_geometry(self, wkt_string: str) -> Union[Point, LineString, Polygon]:
        """
        Convert WKT format to geometry object

        Args:
            wkt_string: WKT string

        Returns:
            Geometry object: Parsed geometry object
        """
        try:
            geometry = wkt.loads(wkt_string)
            self.logger.info(
                f"WKT parsing completed, geometry type: {type(geometry).__name__}"
            )
            return geometry
        except Exception as e:
            self.logger.error(f"WKT parsing failed: {e}")
            raise ValueError(f"WKT parsing failed: {e}")
