# CDR Spatial Analysis Core Logic - Code Generation Prompts

## Core Requirement Description

Please create a Python program that implements the following core functionality: **Determine if a point, based on its latitude and longitude coordinates recorded in a database table, falls within a specified polygon area, and accordingly set the corresponding `vendor` (supplier) tag field in the database.**

## Detailed Technical Requirements

### 1. Data Sources and Targets
- **Input Data**: CDR (Call Detail Record) tables in a PostgreSQL database.
- **Spatial Data**: Polygon files in MapInfo .TAB format (representing the coverage areas of different operators).
- **Processing Target**: Update the `vendor` field in the database tables.

### 2. Operator Configuration
```python
# Configuration information for the three operators
OPERATORS = {
    "TO2": {
        "schema": "cdr_to2",
        "polygon_file": "TO2 A Region.TAB",
        "inside_vendor": "Huawei",    # Equipment supplier inside the region
        "outside_vendor": "Nokia"     # Equipment supplier outside the region
    },
    "VDF": {
        "schema": "cdr_vdf",
        "polygon_file": "VDF A Region.TAB",
        "inside_vendor": "Huawei",
        "outside_vendor": "<PERSON>sson"
    },
    "TDG": {
        "schema": "cdr_tdg",
        "polygon_file": "TDG A Region 84.TAB",
        "inside_vendor": "Huawei",
        "outside_vendor": "Ericsson"
    }
}
```

### 3. Field Mapping Logic
- **Coordinate Fields**: Look up in order of priority: `(u_Latitude, u_Longitude)`, `(u_Call_Setup_LAT, u_Call_Setup_LON)`, etc.
- **Vendor Fields**:
  - Standard Tables: `u_Telefonica_Vendor`, `u_Operator_Vendor`
  - M2M Tables: `u_Side1_Telefonica_Vendor`, `u_Side1_Operator_Vendor`
- **Table Type Identification**: Determined by checking if the table name contains the string "m2m".

### 4. Core Spatial Judgment Algorithm
```python
def point_in_polygon_check(latitude, longitude, polygon_layer):
    """
    Core Logic: Determine if a point is inside a polygon.

    Input Validation:
    - Coordinates cannot be null or 0.
    - Must be valid numerical values.

    Spatial Query:
    1. Use a spatial index to quickly filter candidate polygons.
    2. Perform a precise geometric containment check on the candidate polygons.

    Returns: True (inside) / False (outside)
    """
```

### 5. Vendor Tag Setting Rules

#### Core Business Logic:
1.  **TO2 `Telefonica_Vendor` field** (updated in all tables):
    -   Point is inside the TO2 polygon → "Huawei"
    -   Point is outside the TO2 polygon → "Nokia"
    -   Invalid/missing coordinates → "undefined"

2.  **Each operator's `Operator_Vendor` field** (only updated in the corresponding schema's tables):
    -   Point is inside the operator's own polygon → "Huawei"
    -   Point is outside the operator's own polygon → "Ericsson" or "Nokia"
    -   Invalid/missing coordinates → "undefined"
    -   **VDF Special Rule**: Only update if the field is NULL.

#### Update Execution Strategy:
```python
# Batch update implementation
def batch_update_vendors(batch_results):
    """
    Batch Update Strategy:
    1. Group and collect record_ids based on the spatial judgment results.
    2. Use a temporary table + JOIN for batch updates.
    3. Avoid the performance issues of row-by-row UPDATEs.
    """

    # Example Update SQL
    UPDATE_SQL = """
        UPDATE schema.table t
        SET vendor_field = %s
        FROM temp_table tmp
        WHERE t.ctid::text = tmp.ctid_val
        AND additional_conditions
    """
```

### 6. Performance Requirements and Optimization

#### Processing Scale:
- Records per table: Millions to tens of millions.
- Batch size: 250,000 records/batch.
- Concurrent processing: Support for parallel processing of multiple tables.

#### Key Optimization Points:
1.  **Spatial Index**: Accelerate spatial queries.
2.  **Memory Cache**: Cache polygon features in memory.
3.  **Batch Operations**: Avoid row-by-row database operations.
4.  **Asynchronous Processing**: Use `asyncio` to coordinate concurrent tasks.

### 7. Technology Stack Requirements
- **Spatial Processing**:
- **Database**: PostgreSQL + SQLAlchemy + psycopg2
- **Concurrency**: asyncio + ThreadPoolExecutor

### 8. Error Handling and Monitoring
- Database connection exception handling.
- Fault tolerance for invalid coordinate data.

### 9. Output Requirements
- Update the `operator_vendor` and `telefonica_vendor` fields (e.g., with Huawei, Nokia, Ericsson, undefined) in the data tables under the corresponding schemas based on the spatial calculation results.
- Generate a processing statistics report (number of records processed, number of records updated, time taken, etc.).

## Implementation Hints

This is a typical **large-scale geospatial data processing system**. The core challenges are:
1.  **Spatial Calculation Efficiency**: The point-in-polygon judgment needs to be highly optimized.
2.  **Database Performance**: Optimization of large-batch update operations.
3.  **Memory Management**: Reasonable batch processing and caching strategies.
4.  **Concurrency Control**: Coordination of parallel processing for multiple tables.
