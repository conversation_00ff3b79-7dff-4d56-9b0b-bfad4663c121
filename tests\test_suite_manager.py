#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 测试套件管理器

本模块提供统一的测试套件管理和执行功能，包括：
- 测试套件组织和管理
- 测试执行策略
- 测试结果聚合
- 测试报告生成
- CI/CD集成

作者: Connect质量工程团队
创建时间: 2024-01-20
"""

import os
import sys
import time
import json
import asyncio
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor, as_completed

import pytest
import coverage
from junit_xml import TestSuite, TestCase

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tests.config.test_config import config
from tests.utils.test_helpers import TestResult, _TestReporter, PerformanceMonitor


@dataclass
class TestSuiteConfig:
    """测试套件配置"""
    name: str
    description: str
    test_paths: List[str]
    markers: List[str]
    parallel: bool = False
    timeout: int = 300
    retry_count: int = 0
    dependencies: List[str] = None
    environment_setup: Optional[str] = None
    environment_teardown: Optional[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


@dataclass
class TestExecutionResult:
    """测试执行结果"""
    suite_name: str
    status: str  # 'passed', 'failed', 'skipped', 'error'
    start_time: datetime
    end_time: datetime
    duration: float
    test_count: int
    passed_count: int
    failed_count: int
    skipped_count: int
    error_count: int
    coverage_percentage: Optional[float] = None
    log_file: Optional[str] = None
    report_file: Optional[str] = None
    artifacts: List[str] = None
    
    def __post_init__(self):
        if self.artifacts is None:
            self.artifacts = []
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.test_count == 0:
            return 0.0
        return (self.passed_count / self.test_count) * 100


class _TestSuiteManager:
    """测试套件管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.suites = {}
        self.execution_results = []
        self.performance_monitor = PerformanceMonitor()
        self.reporter = _TestReporter()
        
        # 创建必要的目录
        self._create_directories()
        
        # 加载测试套件配置
        self._load_test_suites()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            'tests/reports',
            'tests/logs',
            'tests/artifacts',
            'tests/coverage',
            'tests/screenshots'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def _load_test_suites(self):
        """加载测试套件配置"""
        # 单元测试套件
        self.suites['unit'] = TestSuiteConfig(
            name='unit',
            description='单元测试套件',
            test_paths=['tests/unit'],
            markers=['unit'],
            parallel=True,
            timeout=120
        )
        
        # 集成测试套件
        self.suites['integration'] = TestSuiteConfig(
            name='integration',
            description='集成测试套件',
            test_paths=['tests/integration'],
            markers=['integration'],
            parallel=False,
            timeout=300,
            dependencies=['unit']
        )
        
        # E2E测试套件
        self.suites['e2e'] = TestSuiteConfig(
            name='e2e',
            description='端到端测试套件',
            test_paths=['tests/e2e'],
            markers=['e2e'],
            parallel=False,
            timeout=600,
            dependencies=['integration'],
            environment_setup='setup_e2e_environment',
            environment_teardown='teardown_e2e_environment'
        )
        
        # 性能测试套件
        self.suites['performance'] = TestSuiteConfig(
            name='performance',
            description='性能测试套件',
            test_paths=['tests/performance'],
            markers=['performance'],
            parallel=False,
            timeout=1800,
            dependencies=['integration']
        )
        
        # 安全测试套件
        self.suites['security'] = TestSuiteConfig(
            name='security',
            description='安全测试套件',
            test_paths=['tests/security'],
            markers=['security'],
            parallel=False,
            timeout=900,
            dependencies=['integration']
        )
        
        # 业务测试套件
        self.suites['business'] = TestSuiteConfig(
            name='business',
            description='业务逻辑测试套件',
            test_paths=['tests/e2e/business'],
            markers=['business'],
            parallel=False,
            timeout=600,
            dependencies=['integration']
        )
        
        # 监控测试套件
        self.suites['monitoring'] = TestSuiteConfig(
            name='monitoring',
            description='监控集成测试套件',
            test_paths=['tests/monitoring'],
            markers=['monitoring'],
            parallel=False,
            timeout=300
        )
        
        # 回归测试套件
        self.suites['regression'] = TestSuiteConfig(
            name='regression',
            description='回归测试套件',
            test_paths=[
                'tests/unit',
                'tests/integration',
                'tests/e2e/business'
            ],
            markers=['regression'],
            parallel=True,
            timeout=900
        )
        
        # 冒烟测试套件
        self.suites['smoke'] = TestSuiteConfig(
            name='smoke',
            description='冒烟测试套件',
            test_paths=['tests/smoke'],
            markers=['smoke'],
            parallel=True,
            timeout=180
        )
    
    def register_suite(self, suite_config: TestSuiteConfig):
        """注册测试套件"""
        self.suites[suite_config.name] = suite_config
    
    def get_suite(self, name: str) -> Optional[TestSuiteConfig]:
        """获取测试套件配置"""
        return self.suites.get(name)
    
    def list_suites(self) -> List[str]:
        """列出所有测试套件"""
        return list(self.suites.keys())
    
    def validate_dependencies(self, suite_name: str) -> List[str]:
        """验证测试套件依赖"""
        suite = self.suites.get(suite_name)
        if not suite:
            return [f"测试套件 '{suite_name}' 不存在"]
        
        errors = []
        for dep in suite.dependencies:
            if dep not in self.suites:
                errors.append(f"依赖的测试套件 '{dep}' 不存在")
        
        return errors
    
    def get_execution_order(self, suite_names: List[str]) -> List[str]:
        """获取测试套件执行顺序"""
        # 简单的拓扑排序
        ordered = []
        remaining = set(suite_names)
        
        while remaining:
            # 找到没有未满足依赖的套件
            ready = []
            for suite_name in remaining:
                suite = self.suites[suite_name]
                deps_satisfied = all(
                    dep in ordered or dep not in suite_names 
                    for dep in suite.dependencies
                )
                if deps_satisfied:
                    ready.append(suite_name)
            
            if not ready:
                # 循环依赖或缺失依赖
                raise ValueError(f"无法解析测试套件依赖: {remaining}")
            
            # 按优先级排序（smoke > unit > integration > others）
            priority_order = ['smoke', 'unit', 'integration', 'business', 
                            'e2e', 'performance', 'security', 'monitoring']
            ready.sort(key=lambda x: priority_order.index(x) if x in priority_order else 999)
            
            ordered.extend(ready)
            remaining -= set(ready)
        
        return ordered
    
    def setup_environment(self, suite_config: TestSuiteConfig) -> bool:
        """设置测试环境"""
        if not suite_config.environment_setup:
            return True
        
        try:
            # 执行环境设置脚本
            setup_method = getattr(self, suite_config.environment_setup, None)
            if setup_method:
                return setup_method()
            else:
                print(f"警告: 环境设置方法 '{suite_config.environment_setup}' 不存在")
                return True
        except Exception as e:
            print(f"环境设置失败: {e}")
            return False
    
    def teardown_environment(self, suite_config: TestSuiteConfig) -> bool:
        """清理测试环境"""
        if not suite_config.environment_teardown:
            return True
        
        try:
            # 执行环境清理脚本
            teardown_method = getattr(self, suite_config.environment_teardown, None)
            if teardown_method:
                return teardown_method()
            else:
                print(f"警告: 环境清理方法 '{suite_config.environment_teardown}' 不存在")
                return True
        except Exception as e:
            print(f"环境清理失败: {e}")
            return False
    
    def execute_suite(self, suite_name: str, 
                     additional_args: List[str] = None) -> TestExecutionResult:
        """执行单个测试套件"""
        suite_config = self.suites.get(suite_name)
        if not suite_config:
            raise ValueError(f"测试套件 '{suite_name}' 不存在")
        
        print(f"\n开始执行测试套件: {suite_config.name}")
        print(f"描述: {suite_config.description}")
        
        start_time = datetime.now()
        
        # 设置环境
        if not self.setup_environment(suite_config):
            return TestExecutionResult(
                suite_name=suite_name,
                status='error',
                start_time=start_time,
                end_time=datetime.now(),
                duration=0,
                test_count=0,
                passed_count=0,
                failed_count=0,
                skipped_count=0,
                error_count=1
            )
        
        try:
            # 开始性能监控
            self.performance_monitor.start_monitoring()
            
            # 构建pytest命令
            cmd = self._build_pytest_command(suite_config, additional_args)
            
            # 执行测试
            result = self._run_pytest(cmd, suite_config)
            
            # 停止性能监控
            self.performance_monitor.stop_monitoring()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 解析测试结果
            execution_result = self._parse_test_result(
                suite_name, result, start_time, end_time, duration
            )
            
            # 生成覆盖率报告
            if config.should_generate_coverage():
                coverage_percentage = self._generate_coverage_report(suite_name)
                execution_result.coverage_percentage = coverage_percentage
            
            return execution_result
            
        except Exception as e:
            print(f"测试执行失败: {e}")
            return TestExecutionResult(
                suite_name=suite_name,
                status='error',
                start_time=start_time,
                end_time=datetime.now(),
                duration=(datetime.now() - start_time).total_seconds(),
                test_count=0,
                passed_count=0,
                failed_count=0,
                skipped_count=0,
                error_count=1
            )
        
        finally:
            # 清理环境
            self.teardown_environment(suite_config)
    
    def execute_suites(self, suite_names: List[str], 
                      parallel: bool = False,
                      additional_args: List[str] = None) -> List[TestExecutionResult]:
        """执行多个测试套件"""
        # 验证依赖
        for suite_name in suite_names:
            errors = self.validate_dependencies(suite_name)
            if errors:
                raise ValueError(f"测试套件依赖验证失败: {errors}")
        
        # 获取执行顺序
        execution_order = self.get_execution_order(suite_names)
        
        print(f"\n测试套件执行顺序: {' -> '.join(execution_order)}")
        
        results = []
        
        if parallel and len(execution_order) > 1:
            # 并行执行（仅限没有依赖关系的套件）
            results = self._execute_suites_parallel(execution_order, additional_args)
        else:
            # 串行执行
            results = self._execute_suites_sequential(execution_order, additional_args)
        
        self.execution_results.extend(results)
        return results
    
    def _execute_suites_sequential(self, suite_names: List[str],
                                  additional_args: List[str] = None) -> List[TestExecutionResult]:
        """串行执行测试套件"""
        results = []
        
        for suite_name in suite_names:
            print(f"\n{'='*60}")
            print(f"执行测试套件: {suite_name}")
            print(f"{'='*60}")
            
            result = self.execute_suite(suite_name, additional_args)
            results.append(result)
            
            # 如果关键测试失败，停止执行
            if result.status == 'failed' and suite_name in ['unit', 'integration']:
                print(f"\n关键测试套件 '{suite_name}' 失败，停止后续测试")
                break
        
        return results
    
    def _execute_suites_parallel(self, suite_names: List[str],
                                additional_args: List[str] = None) -> List[TestExecutionResult]:
        """并行执行测试套件"""
        results = []
        max_workers = min(len(suite_names), config.get_parallel_workers())
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_suite = {
                executor.submit(self.execute_suite, suite_name, additional_args): suite_name
                for suite_name in suite_names
            }
            
            # 收集结果
            for future in as_completed(future_to_suite):
                suite_name = future_to_suite[future]
                try:
                    result = future.result()
                    results.append(result)
                    print(f"测试套件 '{suite_name}' 执行完成: {result.status}")
                except Exception as e:
                    print(f"测试套件 '{suite_name}' 执行异常: {e}")
                    results.append(TestExecutionResult(
                        suite_name=suite_name,
                        status='error',
                        start_time=datetime.now(),
                        end_time=datetime.now(),
                        duration=0,
                        test_count=0,
                        passed_count=0,
                        failed_count=0,
                        skipped_count=0,
                        error_count=1
                    ))
        
        return results
    
    def _build_pytest_command(self, suite_config: TestSuiteConfig,
                             additional_args: List[str] = None) -> List[str]:
        """构建pytest命令"""
        cmd = ['python', '-m', 'pytest']
        
        # 添加测试路径
        for path in suite_config.test_paths:
            if os.path.exists(path):
                cmd.append(path)
        
        # 添加标记
        if suite_config.markers:
            markers = ' or '.join(suite_config.markers)
            cmd.extend(['-m', markers])
        
        # 并行执行
        if suite_config.parallel:
            workers = config.get_parallel_workers()
            cmd.extend(['-n', str(workers)])
        
        # 超时设置
        cmd.extend(['--timeout', str(suite_config.timeout)])
        
        # 详细输出
        cmd.extend(['-v', '--tb=short'])
        
        # JUnit XML报告
        junit_file = f"tests/reports/junit_{suite_config.name}.xml"
        cmd.extend(['--junit-xml', junit_file])
        
        # HTML报告
        html_file = f"tests/reports/report_{suite_config.name}.html"
        cmd.extend(['--html', html_file, '--self-contained-html'])
        
        # 覆盖率
        if config.should_generate_coverage():
            cmd.extend([
                '--cov=src',
                '--cov-report=html:tests/coverage/html',
                '--cov-report=xml:tests/coverage/coverage.xml',
                '--cov-report=term-missing'
            ])
        
        # 添加额外参数
        if additional_args:
            cmd.extend(additional_args)
        
        return cmd
    
    def _run_pytest(self, cmd: List[str], 
                   suite_config: TestSuiteConfig) -> subprocess.CompletedProcess:
        """运行pytest命令"""
        print(f"执行命令: {' '.join(cmd)}")
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONPATH'] = str(project_root)
        
        # 执行命令
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=suite_config.timeout,
            env=env,
            cwd=str(project_root)
        )
        
        # 保存日志
        log_file = f"tests/logs/{suite_config.name}.log"
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"命令: {' '.join(cmd)}\n")
            f.write(f"返回码: {result.returncode}\n")
            f.write(f"标准输出:\n{result.stdout}\n")
            f.write(f"标准错误:\n{result.stderr}\n")
        
        return result
    
    def _parse_test_result(self, suite_name: str, 
                          result: subprocess.CompletedProcess,
                          start_time: datetime, end_time: datetime,
                          duration: float) -> TestExecutionResult:
        """解析测试结果"""
        # 解析JUnit XML文件
        junit_file = f"tests/reports/junit_{suite_name}.xml"
        test_count = passed_count = failed_count = skipped_count = error_count = 0
        
        if os.path.exists(junit_file):
            try:
                import xml.etree.ElementTree as ET
                tree = ET.parse(junit_file)
                root = tree.getroot()
                
                if root.tag == 'testsuites':
                    for testsuite in root.findall('testsuite'):
                        test_count += int(testsuite.get('tests', 0))
                        failed_count += int(testsuite.get('failures', 0))
                        error_count += int(testsuite.get('errors', 0))
                        skipped_count += int(testsuite.get('skipped', 0))
                elif root.tag == 'testsuite':
                    test_count = int(root.get('tests', 0))
                    failed_count = int(root.get('failures', 0))
                    error_count = int(root.get('errors', 0))
                    skipped_count = int(root.get('skipped', 0))
                
                passed_count = test_count - failed_count - error_count - skipped_count
                
            except Exception as e:
                print(f"解析JUnit XML失败: {e}")
        
        # 确定状态
        if result.returncode == 0:
            status = 'passed'
        elif failed_count > 0 or error_count > 0:
            status = 'failed'
        else:
            status = 'error'
        
        return TestExecutionResult(
            suite_name=suite_name,
            status=status,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            test_count=test_count,
            passed_count=passed_count,
            failed_count=failed_count,
            skipped_count=skipped_count,
            error_count=error_count,
            log_file=f"tests/logs/{suite_name}.log",
            report_file=f"tests/reports/report_{suite_name}.html"
        )
    
    def _generate_coverage_report(self, suite_name: str) -> Optional[float]:
        """生成覆盖率报告"""
        try:
            # 读取覆盖率数据
            cov_file = 'tests/coverage/coverage.xml'
            if os.path.exists(cov_file):
                import xml.etree.ElementTree as ET
                tree = ET.parse(cov_file)
                root = tree.getroot()
                
                # 提取覆盖率百分比
                coverage_elem = root.find('.//coverage')
                if coverage_elem is not None:
                    line_rate = float(coverage_elem.get('line-rate', 0))
                    return line_rate * 100
            
            return None
            
        except Exception as e:
            print(f"生成覆盖率报告失败: {e}")
            return None
    
    def generate_summary_report(self, output_file: str = None) -> str:
        """生成汇总报告"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"tests/reports/summary_report_{timestamp}.html"
        
        # 计算汇总统计
        total_suites = len(self.execution_results)
        passed_suites = sum(1 for r in self.execution_results if r.status == 'passed')
        failed_suites = sum(1 for r in self.execution_results if r.status == 'failed')
        error_suites = sum(1 for r in self.execution_results if r.status == 'error')
        
        total_tests = sum(r.test_count for r in self.execution_results)
        total_passed = sum(r.passed_count for r in self.execution_results)
        total_failed = sum(r.failed_count for r in self.execution_results)
        total_skipped = sum(r.skipped_count for r in self.execution_results)
        total_errors = sum(r.error_count for r in self.execution_results)
        
        total_duration = sum(r.duration for r in self.execution_results)
        overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # 生成HTML报告
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Connect测试汇总报告</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }}
                .summary {{ background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                .suite-results {{ margin: 20px 0; }}
                .suite {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }}
                .passed {{ border-left-color: #28a745; }}
                .failed {{ border-left-color: #dc3545; }}
                .error {{ border-left-color: #ffc107; }}
                .metrics {{ display: flex; justify-content: space-around; margin: 20px 0; }}
                .metric {{ text-align: center; padding: 15px; background: white; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .metric-value {{ font-size: 2em; font-weight: bold; color: #2c3e50; }}
                .metric-label {{ color: #7f8c8d; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                th {{ background-color: #f2f2f2; font-weight: bold; }}
                .status-passed {{ color: #28a745; font-weight: bold; }}
                .status-failed {{ color: #dc3545; font-weight: bold; }}
                .status-error {{ color: #ffc107; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Connect电信数据分析平台 - 测试汇总报告</h1>
                <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="summary">
                <h2>执行摘要</h2>
                <div class="metrics">
                    <div class="metric">
                        <div class="metric-value">{total_suites}</div>
                        <div class="metric-label">测试套件</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">{total_tests}</div>
                        <div class="metric-label">测试用例</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">{overall_success_rate:.1f}%</div>
                        <div class="metric-label">成功率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">{total_duration:.1f}s</div>
                        <div class="metric-label">总耗时</div>
                    </div>
                </div>
            </div>
            
            <div class="suite-results">
                <h2>测试套件结果</h2>
                <table>
                    <tr>
                        <th>套件名称</th>
                        <th>状态</th>
                        <th>测试数量</th>
                        <th>通过</th>
                        <th>失败</th>
                        <th>跳过</th>
                        <th>错误</th>
                        <th>成功率</th>
                        <th>耗时(秒)</th>
                        <th>覆盖率</th>
                    </tr>
        """
        
        for result in self.execution_results:
            status_class = f"status-{result.status}"
            coverage_text = f"{result.coverage_percentage:.1f}%" if result.coverage_percentage else "N/A"
            
            html_content += f"""
                    <tr>
                        <td>{result.suite_name}</td>
                        <td class="{status_class}">{result.status.upper()}</td>
                        <td>{result.test_count}</td>
                        <td>{result.passed_count}</td>
                        <td>{result.failed_count}</td>
                        <td>{result.skipped_count}</td>
                        <td>{result.error_count}</td>
                        <td>{result.success_rate:.1f}%</td>
                        <td>{result.duration:.1f}</td>
                        <td>{coverage_text}</td>
                    </tr>
            """
        
        html_content += """
                </table>
            </div>
            
            <div class="summary">
                <h2>质量指标</h2>
                <ul>
                    <li>套件成功率: {:.1f}% ({}/{})</li>
                    <li>测试成功率: {:.1f}% ({}/{})</li>
                    <li>平均执行时间: {:.1f}秒</li>
                    <li>总执行时间: {:.1f}秒</li>
                </ul>
            </div>
        </body>
        </html>
        """.format(
            (passed_suites / total_suites * 100) if total_suites > 0 else 0,
            passed_suites, total_suites,
            overall_success_rate, total_passed, total_tests,
            total_duration / total_suites if total_suites > 0 else 0,
            total_duration
        )
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"\n汇总报告已生成: {output_file}")
        return output_file
    
    def export_results_json(self, output_file: str = None) -> str:
        """导出结果为JSON"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"tests/reports/results_{timestamp}.json"
        
        results_data = {
            'summary': {
                'total_suites': len(self.execution_results),
                'total_tests': sum(r.test_count for r in self.execution_results),
                'total_passed': sum(r.passed_count for r in self.execution_results),
                'total_failed': sum(r.failed_count for r in self.execution_results),
                'total_duration': sum(r.duration for r in self.execution_results),
                'generated_at': datetime.now().isoformat()
            },
            'results': [asdict(result) for result in self.execution_results]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, ensure_ascii=False, indent=2, default=str)
        
        return output_file
    
    # 环境设置方法
    def setup_e2e_environment(self) -> bool:
        """设置E2E测试环境"""
        try:
            print("设置E2E测试环境...")
            # 启动测试数据库
            # 启动Web服务器
            # 初始化测试数据
            return True
        except Exception as e:
            print(f"E2E环境设置失败: {e}")
            return False
    
    def teardown_e2e_environment(self) -> bool:
        """清理E2E测试环境"""
        try:
            print("清理E2E测试环境...")
            # 停止Web服务器
            # 清理测试数据
            # 停止测试数据库
            return True
        except Exception as e:
            print(f"E2E环境清理失败: {e}")
            return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Connect测试套件管理器')
    parser.add_argument('--suites', nargs='+', 
                       help='要执行的测试套件名称')
    parser.add_argument('--list', action='store_true',
                       help='列出所有可用的测试套件')
    parser.add_argument('--parallel', action='store_true',
                       help='并行执行测试套件')
    parser.add_argument('--coverage', action='store_true',
                       help='生成覆盖率报告')
    parser.add_argument('--report', type=str,
                       help='汇总报告输出文件')
    parser.add_argument('--config', type=str,
                       help='测试配置文件')
    
    args = parser.parse_args()
    
    # 创建测试套件管理器
    manager = _TestSuiteManager(args.config)
    
    if args.list:
        print("可用的测试套件:")
        for suite_name in manager.list_suites():
            suite = manager.get_suite(suite_name)
            print(f"  {suite_name}: {suite.description}")
        return
    
    # 确定要执行的测试套件
    if args.suites:
        suite_names = args.suites
    else:
        # 默认执行回归测试
        suite_names = ['regression']
    
    print(f"\n开始执行测试套件: {', '.join(suite_names)}")
    
    try:
        # 执行测试套件
        results = manager.execute_suites(
            suite_names, 
            parallel=args.parallel
        )
        
        # 生成汇总报告
        report_file = manager.generate_summary_report(args.report)
        
        # 导出JSON结果
        json_file = manager.export_results_json()
        
        # 打印摘要
        total_tests = sum(r.test_count for r in results)
        total_passed = sum(r.passed_count for r in results)
        success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n{'='*60}")
        print(f"测试执行完成")
        print(f"{'='*60}")
        print(f"总测试数: {total_tests}")
        print(f"通过数: {total_passed}")
        print(f"成功率: {success_rate:.1f}%")
        print(f"汇总报告: {report_file}")
        print(f"JSON结果: {json_file}")
        
        # 根据结果设置退出码
        if success_rate < 95:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except Exception as e:
        print(f"测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()