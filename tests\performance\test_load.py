"""Load testing module for database operations.

This module contains load tests to evaluate system behavior under various
load conditions including concurrent users, high throughput scenarios,
and stress testing.
"""

import asyncio
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, AsyncMock

# Import project modules
try:
    from src.connection.session import SessionManager
    from src.connection.pool import DatabasePoolManager
    from src.operations.crud import CRUDOperations
    from src.operations.bulk import BulkOperations
    from src.etl.pipeline import ETLPipeline
    from src.config.models import DatabaseConfig
    from src.monitoring.metrics import PerformanceMetrics
    from src.utils.performance import PerformanceMonitor
except ImportError:
    # Fallback for testing without actual implementation
    SessionManager = Mock
    DatabasePoolManager = Mock
    CRUDOperations = Mock
    BulkOperations = Mock
    ETLPipeline = Mock
    DatabaseConfig = Mock
    PerformanceMetrics = Mock
    PerformanceMonitor = Mock


@pytest.mark.performance
@pytest.mark.load
class TestLoadTesting:
    """Load testing for database operations."""

    @pytest.fixture
    async def load_test_config(self, test_config):
        """Configuration for load testing."""
        config = DatabaseConfig(test_config)
        config.max_connections = 50  # Higher connection limit for load testing
        config.connection_timeout = 30
        config.query_timeout = 60
        return config

    @pytest.fixture
    async def load_test_session_manager(self, load_test_config):
        """Session manager configured for load testing."""
        pool_manager = DatabasePoolManager(load_test_config)
        session_manager = SessionManager(pool_manager)
        
        yield session_manager
        
        await pool_manager.close()

    @pytest.fixture
    def load_test_data_generator(self):
        """Generate test data for load testing."""
        def generate_data(num_records: int, record_size: str = 'small') -> pd.DataFrame:
            if record_size == 'small':
                data_size = 50
            elif record_size == 'medium':
                data_size = 200
            else:  # large
                data_size = 1000
            
            return pd.DataFrame({
                'id': range(num_records),
                'name': [f'LoadTest_{i}' for i in range(num_records)],
                'data': ['x' * data_size] * num_records,
                'value': np.random.uniform(0, 1000, num_records),
                'category': np.random.choice(['A', 'B', 'C', 'D', 'E'], num_records),
                'timestamp': pd.date_range('2023-01-01', periods=num_records, freq='S')
            })
        
        return generate_data

    @pytest.mark.asyncio
    async def test_concurrent_read_load(self, load_test_session_manager, load_test_data_generator):
        """Test system behavior under concurrent read load."""
        crud_operations = CRUDOperations(load_test_session_manager)
        
        # Setup test data
        test_data = load_test_data_generator(10000, 'medium')
        
        # Mock bulk insert for setup
        with patch.object(crud_operations, 'bulk_insert', return_value=Mock(success=True)):
            await crud_operations.bulk_insert('load_test_table', test_data)
        
        # Define concurrent read operations
        async def read_operation(operation_id: int):
            """Single read operation."""
            start_time = time.time()
            
            try:
                # Mock select operation
                with patch.object(crud_operations, 'select_all', 
                                return_value=test_data.sample(100)):
                    result = await crud_operations.select_all(
                        'load_test_table',
                        filters={'category': np.random.choice(['A', 'B', 'C'])}
                    )
                
                end_time = time.time()
                
                return {
                    'operation_id': operation_id,
                    'success': True,
                    'duration': end_time - start_time,
                    'records_returned': len(result)
                }
            except Exception as e:
                return {
                    'operation_id': operation_id,
                    'success': False,
                    'error': str(e),
                    'duration': time.time() - start_time
                }
        
        # Test different concurrency levels
        concurrency_levels = [10, 25, 50, 100]
        results = {}
        
        for concurrency in concurrency_levels:
            print(f"\nTesting concurrent reads with {concurrency} operations...")
            
            start_time = time.time()
            
            # Execute concurrent operations
            tasks = [read_operation(i) for i in range(concurrency)]
            operation_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Analyze results
            successful_ops = [r for r in operation_results if isinstance(r, dict) and r.get('success', False)]
            failed_ops = [r for r in operation_results if not (isinstance(r, dict) and r.get('success', False))]
            
            avg_duration = np.mean([op['duration'] for op in successful_ops]) if successful_ops else 0
            max_duration = max([op['duration'] for op in successful_ops]) if successful_ops else 0
            min_duration = min([op['duration'] for op in successful_ops]) if successful_ops else 0
            
            results[concurrency] = {
                'total_time': total_time,
                'successful_operations': len(successful_ops),
                'failed_operations': len(failed_ops),
                'success_rate': len(successful_ops) / concurrency * 100,
                'avg_response_time': avg_duration,
                'max_response_time': max_duration,
                'min_response_time': min_duration,
                'throughput': concurrency / total_time
            }
            
            print(f"  Success rate: {results[concurrency]['success_rate']:.1f}%")
            print(f"  Avg response time: {avg_duration:.4f}s")
            print(f"  Throughput: {results[concurrency]['throughput']:.2f} ops/sec")
        
        # Verify performance requirements
        for concurrency, metrics in results.items():
            # Success rate should be high
            assert metrics['success_rate'] >= 95.0, f"Success rate too low at concurrency {concurrency}"
            
            # Response time should be reasonable
            assert metrics['avg_response_time'] < 2.0, f"Average response time too high at concurrency {concurrency}"
            
            # Should handle at least some throughput
            assert metrics['throughput'] > 5.0, f"Throughput too low at concurrency {concurrency}"

    @pytest.mark.asyncio
    async def test_concurrent_write_load(self, load_test_session_manager, load_test_data_generator):
        """Test system behavior under concurrent write load."""
        crud_operations = CRUDOperations(load_test_session_manager)
        
        async def write_operation(operation_id: int):
            """Single write operation."""
            start_time = time.time()
            
            try:
                # Generate small batch of data for each operation
                batch_data = load_test_data_generator(100, 'small')
                batch_data['batch_id'] = operation_id
                
                # Mock bulk insert
                with patch.object(crud_operations, 'bulk_insert', 
                                return_value=Mock(success=True, rows_affected=100)):
                    result = await crud_operations.bulk_insert(
                        f'load_test_writes_{operation_id}', 
                        batch_data
                    )
                
                end_time = time.time()
                
                return {
                    'operation_id': operation_id,
                    'success': result.success,
                    'duration': end_time - start_time,
                    'records_written': result.rows_affected
                }
            except Exception as e:
                return {
                    'operation_id': operation_id,
                    'success': False,
                    'error': str(e),
                    'duration': time.time() - start_time
                }
        
        # Test concurrent writes
        concurrency_levels = [5, 10, 20]
        results = {}
        
        for concurrency in concurrency_levels:
            print(f"\nTesting concurrent writes with {concurrency} operations...")
            
            start_time = time.time()
            
            # Execute concurrent write operations
            tasks = [write_operation(i) for i in range(concurrency)]
            operation_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Analyze results
            successful_ops = [r for r in operation_results if isinstance(r, dict) and r.get('success', False)]
            failed_ops = [r for r in operation_results if not (isinstance(r, dict) and r.get('success', False))]
            
            total_records = sum([op.get('records_written', 0) for op in successful_ops])
            avg_duration = np.mean([op['duration'] for op in successful_ops]) if successful_ops else 0
            
            results[concurrency] = {
                'total_time': total_time,
                'successful_operations': len(successful_ops),
                'failed_operations': len(failed_ops),
                'success_rate': len(successful_ops) / concurrency * 100,
                'total_records_written': total_records,
                'avg_response_time': avg_duration,
                'write_throughput': total_records / total_time if total_time > 0 else 0
            }
            
            print(f"  Success rate: {results[concurrency]['success_rate']:.1f}%")
            print(f"  Total records written: {total_records:,}")
            print(f"  Write throughput: {results[concurrency]['write_throughput']:.0f} records/sec")
        
        # Verify write performance
        for concurrency, metrics in results.items():
            assert metrics['success_rate'] >= 90.0, f"Write success rate too low at concurrency {concurrency}"
            assert metrics['write_throughput'] > 1000, f"Write throughput too low at concurrency {concurrency}"

    @pytest.mark.asyncio
    async def test_mixed_workload_performance(self, load_test_session_manager, load_test_data_generator):
        """Test system behavior under mixed read/write workload."""
        crud_operations = CRUDOperations(load_test_session_manager)
        
        # Setup base data
        base_data = load_test_data_generator(5000, 'medium')
        with patch.object(crud_operations, 'bulk_insert', return_value=Mock(success=True)):
            await crud_operations.bulk_insert('mixed_workload_table', base_data)
        
        async def mixed_operation(operation_id: int, operation_type: str):
            """Mixed read/write operation."""
            start_time = time.time()
            
            try:
                if operation_type == 'read':
                    with patch.object(crud_operations, 'select_all', 
                                    return_value=base_data.sample(50)):
                        result = await crud_operations.select_all(
                            'mixed_workload_table',
                            filters={'id': {'$lt': 1000}}
                        )
                    records_affected = len(result)
                    
                elif operation_type == 'write':
                    new_data = load_test_data_generator(50, 'small')
                    new_data['operation_id'] = operation_id
                    
                    with patch.object(crud_operations, 'bulk_insert', 
                                    return_value=Mock(success=True, rows_affected=50)):
                        result = await crud_operations.bulk_insert(
                            'mixed_workload_table', 
                            new_data
                        )
                    records_affected = result.rows_affected
                    
                elif operation_type == 'update':
                    update_data = {'value': np.random.uniform(0, 1000)}
                    
                    with patch.object(crud_operations, 'update', 
                                    return_value=Mock(success=True, rows_affected=10)):
                        result = await crud_operations.update(
                            'mixed_workload_table',
                            update_data,
                            filters={'id': {'$lt': 100}}
                        )
                    records_affected = result.rows_affected
                
                end_time = time.time()
                
                return {
                    'operation_id': operation_id,
                    'operation_type': operation_type,
                    'success': True,
                    'duration': end_time - start_time,
                    'records_affected': records_affected
                }
                
            except Exception as e:
                return {
                    'operation_id': operation_id,
                    'operation_type': operation_type,
                    'success': False,
                    'error': str(e),
                    'duration': time.time() - start_time
                }
        
        # Define mixed workload (70% reads, 20% writes, 10% updates)
        total_operations = 100
        operation_types = (['read'] * 70 + ['write'] * 20 + ['update'] * 10)
        np.random.shuffle(operation_types)
        
        print(f"\nTesting mixed workload with {total_operations} operations...")
        
        start_time = time.time()
        
        # Execute mixed operations
        tasks = [
            mixed_operation(i, operation_types[i]) 
            for i in range(total_operations)
        ]
        operation_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results by operation type
        results_by_type = {'read': [], 'write': [], 'update': []}
        
        for result in operation_results:
            if isinstance(result, dict) and result.get('success', False):
                op_type = result['operation_type']
                results_by_type[op_type].append(result)
        
        # Calculate metrics
        overall_metrics = {
            'total_time': total_time,
            'total_operations': total_operations,
            'overall_throughput': total_operations / total_time,
            'by_type': {}
        }
        
        for op_type, ops in results_by_type.items():
            if ops:
                avg_duration = np.mean([op['duration'] for op in ops])
                total_records = sum([op.get('records_affected', 0) for op in ops])
                
                overall_metrics['by_type'][op_type] = {
                    'count': len(ops),
                    'avg_duration': avg_duration,
                    'total_records': total_records,
                    'success_rate': len(ops) / operation_types.count(op_type) * 100
                }
                
                print(f"  {op_type.upper()} operations:")
                print(f"    Count: {len(ops)}")
                print(f"    Avg duration: {avg_duration:.4f}s")
                print(f"    Success rate: {overall_metrics['by_type'][op_type]['success_rate']:.1f}%")
        
        print(f"  Overall throughput: {overall_metrics['overall_throughput']:.2f} ops/sec")
        
        # Verify mixed workload performance
        assert overall_metrics['overall_throughput'] > 10.0, "Mixed workload throughput too low"
        
        for op_type, metrics in overall_metrics['by_type'].items():
            assert metrics['success_rate'] >= 95.0, f"{op_type} success rate too low"
            assert metrics['avg_duration'] < 1.0, f"{op_type} average duration too high"

    def test_stress_testing_connection_limits(self, load_test_config):
        """Test system behavior when approaching connection limits."""
        max_connections = load_test_config.max_connections
        
        def create_connection_worker(worker_id: int):
            """Worker that creates and holds a connection."""
            try:
                # Mock connection creation
                connection = Mock()
                connection.is_connected = True
                connection.worker_id = worker_id
                
                # Hold connection for a short time
                time.sleep(0.5)
                
                return {
                    'worker_id': worker_id,
                    'success': True,
                    'connection_created': True
                }
            except Exception as e:
                return {
                    'worker_id': worker_id,
                    'success': False,
                    'error': str(e)
                }
        
        # Test at different percentages of max connections
        test_levels = [0.5, 0.8, 0.95, 1.1]  # 110% to test over-limit behavior
        
        for level in test_levels:
            num_connections = int(max_connections * level)
            print(f"\nTesting with {num_connections} connections ({level*100:.0f}% of limit)...")
            
            start_time = time.time()
            
            # Create connections using thread pool
            with ThreadPoolExecutor(max_workers=num_connections) as executor:
                futures = [
                    executor.submit(create_connection_worker, i) 
                    for i in range(num_connections)
                ]
                
                results = []
                for future in as_completed(futures, timeout=10):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        results.append({
                            'success': False,
                            'error': str(e)
                        })
            
            end_time = time.time()
            
            successful_connections = len([r for r in results if r.get('success', False)])
            success_rate = successful_connections / num_connections * 100
            
            print(f"  Successful connections: {successful_connections}/{num_connections}")
            print(f"  Success rate: {success_rate:.1f}%")
            print(f"  Time taken: {end_time - start_time:.2f}s")
            
            # Verify behavior based on connection level
            if level <= 1.0:  # Within limits
                assert success_rate >= 90.0, f"Success rate too low within connection limits"
            else:  # Over limits - should handle gracefully
                # System should either succeed or fail gracefully, not crash
                assert success_rate >= 0.0, "System should handle over-limit gracefully"

    @pytest.mark.asyncio
    async def test_sustained_load_endurance(self, load_test_session_manager, load_test_data_generator):
        """Test system behavior under sustained load over time."""
        crud_operations = CRUDOperations(load_test_session_manager)
        
        # Test parameters
        duration_seconds = 30  # Reduced for testing
        operations_per_second = 10
        
        async def sustained_operation(operation_id: int):
            """Single operation in sustained test."""
            try:
                # Alternate between reads and writes
                if operation_id % 2 == 0:
                    # Read operation
                    with patch.object(crud_operations, 'select_all', 
                                    return_value=pd.DataFrame({'id': [1, 2, 3]})):
                        await crud_operations.select_all('sustained_test_table')
                else:
                    # Write operation
                    test_data = load_test_data_generator(10, 'small')
                    with patch.object(crud_operations, 'bulk_insert', 
                                    return_value=Mock(success=True)):
                        await crud_operations.bulk_insert('sustained_test_table', test_data)
                
                return {'success': True, 'operation_id': operation_id}
            except Exception as e:
                return {'success': False, 'operation_id': operation_id, 'error': str(e)}
        
        print(f"\nRunning sustained load test for {duration_seconds} seconds...")
        
        start_time = time.time()
        operation_id = 0
        results = []
        
        while time.time() - start_time < duration_seconds:
            batch_start = time.time()
            
            # Execute batch of operations
            batch_tasks = [
                sustained_operation(operation_id + i) 
                for i in range(operations_per_second)
            ]
            
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            results.extend(batch_results)
            
            operation_id += operations_per_second
            
            # Wait for next second
            batch_duration = time.time() - batch_start
            if batch_duration < 1.0:
                await asyncio.sleep(1.0 - batch_duration)
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        # Analyze sustained load results
        successful_ops = [r for r in results if isinstance(r, dict) and r.get('success', False)]
        failed_ops = [r for r in results if not (isinstance(r, dict) and r.get('success', False))]
        
        total_operations = len(results)
        success_rate = len(successful_ops) / total_operations * 100 if total_operations > 0 else 0
        actual_ops_per_second = total_operations / actual_duration
        
        print(f"  Duration: {actual_duration:.2f}s")
        print(f"  Total operations: {total_operations}")
        print(f"  Successful operations: {len(successful_ops)}")
        print(f"  Success rate: {success_rate:.1f}%")
        print(f"  Actual ops/sec: {actual_ops_per_second:.2f}")
        
        # Verify sustained performance
        assert success_rate >= 95.0, "Success rate degraded during sustained load"
        assert actual_ops_per_second >= operations_per_second * 0.8, "Throughput degraded significantly"
        assert len(failed_ops) < total_operations * 0.1, "Too many failed operations during sustained load"