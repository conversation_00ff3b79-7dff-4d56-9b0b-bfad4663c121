"""Core validation framework components.

This module provides the foundational classes and interfaces for the
unified validation framework, including validation rules, results,
and the main validation framework class.
"""

import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Union

import pandas as pd
from loguru import logger

from ..types.telecom_types import DataSourceType
from .exceptions import ValidationError, ValidationRuleError


class ValidationType(Enum):
    """Types of validation operations."""
    SCHEMA = "schema"
    DATA_TYPE = "data_type"
    RANGE = "range"
    PATTERN = "pattern"
    UNIQUENESS = "uniqueness"
    COMPLETENESS = "completeness"
    CONSISTENCY = "consistency"
    CUSTOM = "custom"
    REFERENTIAL = "referential"
    BUSINESS_RULE = "business_rule"
    FILE_FORMAT = "file_format"
    TELECOM_SPECIFIC = "telecom_specific"


class ValidationSeverity(Enum):
    """Validation severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationContext:
    """Context information for validation operations."""
    data_source_type: Optional[DataSourceType] = None
    file_path: Optional[str] = None
    table_name: Optional[str] = None
    schema_name: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert context to dictionary."""
        return {
            "data_source_type": self.data_source_type.value if self.data_source_type else None,
            "file_path": self.file_path,
            "table_name": self.table_name,
            "schema_name": self.schema_name,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "metadata": self.metadata,
        }


@dataclass
class ValidationIssue:
    """Individual validation issue."""
    rule_name: str
    validation_type: ValidationType
    severity: ValidationSeverity
    message: str
    column: Optional[str] = None
    row_index: Optional[int] = None
    value: Optional[Any] = None
    expected: Optional[Any] = None
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert issue to dictionary."""
        return {
            "rule_name": self.rule_name,
            "validation_type": self.validation_type.value,
            "severity": self.severity.value,
            "message": self.message,
            "column": self.column,
            "row_index": self.row_index,
            "value": str(self.value) if self.value is not None else None,
            "expected": str(self.expected) if self.expected is not None else None,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
        }


@dataclass
class ValidationResult:
    """Result of validation operation."""
    success: bool
    total_rules: int = 0
    passed_rules: int = 0
    failed_rules: int = 0
    issues: List[ValidationIssue] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)
    execution_time: Optional[float] = None
    context: Optional[ValidationContext] = None
    data_summary: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    def add_issue(self, issue: ValidationIssue) -> None:
        """Add a validation issue."""
        self.issues.append(issue)
        if issue.severity in [ValidationSeverity.ERROR, ValidationSeverity.CRITICAL]:
            self.success = False
            self.failed_rules += 1
        else:
            self.passed_rules += 1
    
    def get_issues_by_severity(self, severity: ValidationSeverity) -> List[ValidationIssue]:
        """Get issues by severity level."""
        return [issue for issue in self.issues if issue.severity == severity]
    
    def get_error_count(self) -> int:
        """Get total error count."""
        return len([i for i in self.issues if i.severity in [ValidationSeverity.ERROR, ValidationSeverity.CRITICAL]])
    
    def get_warning_count(self) -> int:
        """Get total warning count."""
        return len([i for i in self.issues if i.severity == ValidationSeverity.WARNING])
    
    def merge(self, other: 'ValidationResult') -> None:
        """Merge another validation result."""
        if not other.success:
            self.success = False
        
        self.total_rules += other.total_rules
        self.passed_rules += other.passed_rules
        self.failed_rules += other.failed_rules
        self.issues.extend(other.issues)
        
        # Merge statistics
        for key, value in other.statistics.items():
            if key in self.statistics:
                if isinstance(value, (int, float)):
                    self.statistics[key] += value
                elif isinstance(value, list):
                    self.statistics[key].extend(value)
            else:
                self.statistics[key] = value
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            "success": self.success,
            "total_rules": self.total_rules,
            "passed_rules": self.passed_rules,
            "failed_rules": self.failed_rules,
            "error_count": self.get_error_count(),
            "warning_count": self.get_warning_count(),
            "issues": [issue.to_dict() for issue in self.issues],
            "statistics": self.statistics,
            "execution_time": self.execution_time,
            "context": self.context.to_dict() if self.context else None,
            "data_summary": self.data_summary,
            "timestamp": self.timestamp.isoformat(),
        }


class ValidationRule(ABC):
    """Abstract base class for validation rules."""
    
    def __init__(
        self,
        name: str,
        validation_type: ValidationType,
        severity: ValidationSeverity = ValidationSeverity.ERROR,
        description: str = "",
        enabled: bool = True,
        **kwargs
    ):
        self.name = name
        self.validation_type = validation_type
        self.severity = severity
        self.description = description
        self.enabled = enabled
        self.parameters = kwargs
    
    @abstractmethod
    def validate(self, data: Any, context: Optional[ValidationContext] = None) -> ValidationResult:
        """Execute validation rule.
        
        Args:
            data: Data to validate
            context: Optional validation context
            
        Returns:
            ValidationResult: Result of validation
        """
        pass
    
    def is_applicable(self, data: Any, context: Optional[ValidationContext] = None) -> bool:
        """Check if rule is applicable to the given data and context.
        
        Args:
            data: Data to check
            context: Optional validation context
            
        Returns:
            bool: True if rule is applicable
        """
        return self.enabled


class ValidationFramework:
    """Main validation framework class."""
    
    def __init__(self):
        self._rules: Dict[str, ValidationRule] = {}
        self._rule_groups: Dict[str, Set[str]] = {}
        self._global_context: Optional[ValidationContext] = None
    
    def add_rule(self, rule: ValidationRule, group: Optional[str] = None) -> None:
        """Add a validation rule.
        
        Args:
            rule: Validation rule to add
            group: Optional group name for organizing rules
        """
        if rule.name in self._rules:
            logger.warning(f"Overwriting existing rule: {rule.name}")
        
        self._rules[rule.name] = rule
        
        if group:
            if group not in self._rule_groups:
                self._rule_groups[group] = set()
            self._rule_groups[group].add(rule.name)
        
        logger.debug(f"Added validation rule: {rule.name} (type: {rule.validation_type.value})")
    
    def remove_rule(self, rule_name: str) -> bool:
        """Remove a validation rule.
        
        Args:
            rule_name: Name of rule to remove
            
        Returns:
            bool: True if rule was removed
        """
        if rule_name in self._rules:
            del self._rules[rule_name]
            
            # Remove from groups
            for group_rules in self._rule_groups.values():
                group_rules.discard(rule_name)
            
            logger.debug(f"Removed validation rule: {rule_name}")
            return True
        return False
    
    def get_rule(self, rule_name: str) -> Optional[ValidationRule]:
        """Get a validation rule by name.
        
        Args:
            rule_name: Name of rule to get
            
        Returns:
            ValidationRule or None: The rule if found
        """
        return self._rules.get(rule_name)
    
    def get_rules_by_group(self, group: str) -> List[ValidationRule]:
        """Get all rules in a group.
        
        Args:
            group: Group name
            
        Returns:
            List[ValidationRule]: Rules in the group
        """
        if group not in self._rule_groups:
            return []
        
        return [self._rules[rule_name] for rule_name in self._rule_groups[group] if rule_name in self._rules]
    
    def get_rules_by_type(self, validation_type: ValidationType) -> List[ValidationRule]:
        """Get all rules of a specific type.
        
        Args:
            validation_type: Type of validation
            
        Returns:
            List[ValidationRule]: Rules of the specified type
        """
        return [rule for rule in self._rules.values() if rule.validation_type == validation_type]
    
    def set_global_context(self, context: ValidationContext) -> None:
        """Set global validation context.
        
        Args:
            context: Global validation context
        """
        self._global_context = context
    
    def validate(
        self,
        data: Any,
        rules: Optional[List[str]] = None,
        groups: Optional[List[str]] = None,
        context: Optional[ValidationContext] = None,
        stop_on_first_error: bool = False
    ) -> ValidationResult:
        """Execute validation.
        
        Args:
            data: Data to validate
            rules: Optional list of specific rule names to execute
            groups: Optional list of rule groups to execute
            context: Optional validation context
            stop_on_first_error: Whether to stop on first error
            
        Returns:
            ValidationResult: Validation result
        """
        start_time = time.time()
        
        # Use provided context or global context
        validation_context = context or self._global_context
        
        # Determine which rules to execute
        rules_to_execute = self._determine_rules_to_execute(rules, groups)
        
        # Initialize result
        result = ValidationResult(
            success=True,
            total_rules=len(rules_to_execute),
            context=validation_context
        )
        
        # Add data summary
        result.data_summary = self._generate_data_summary(data)
        
        logger.info(f"Starting validation with {len(rules_to_execute)} rules")
        
        # Execute rules
        for rule in rules_to_execute:
            try:
                if not rule.is_applicable(data, validation_context):
                    logger.debug(f"Skipping rule {rule.name} - not applicable")
                    continue
                
                logger.debug(f"Executing rule: {rule.name}")
                rule_result = rule.validate(data, validation_context)
                
                # Merge rule result
                result.merge(rule_result)
                
                # Stop on first error if requested
                if stop_on_first_error and not rule_result.success:
                    logger.warning(f"Stopping validation on first error from rule: {rule.name}")
                    break
                    
            except Exception as e:
                logger.error(f"Error executing rule {rule.name}: {e}")
                error_issue = ValidationIssue(
                    rule_name=rule.name,
                    validation_type=rule.validation_type,
                    severity=ValidationSeverity.CRITICAL,
                    message=f"Rule execution failed: {str(e)}",
                    details={"exception_type": type(e).__name__}
                )
                result.add_issue(error_issue)
                
                if stop_on_first_error:
                    break
        
        # Calculate execution time
        result.execution_time = time.time() - start_time
        
        logger.info(
            f"Validation completed in {result.execution_time:.3f}s. "
            f"Success: {result.success}, Errors: {result.get_error_count()}, "
            f"Warnings: {result.get_warning_count()}"
        )
        
        return result
    
    def _determine_rules_to_execute(self, rules: Optional[List[str]], groups: Optional[List[str]]) -> List[ValidationRule]:
        """Determine which rules to execute based on parameters."""
        if rules:
            # Execute specific rules
            return [self._rules[rule_name] for rule_name in rules if rule_name in self._rules]
        elif groups:
            # Execute rules from specific groups
            rules_to_execute = []
            for group in groups:
                rules_to_execute.extend(self.get_rules_by_group(group))
            return rules_to_execute
        else:
            # Execute all rules
            return list(self._rules.values())
    
    def _generate_data_summary(self, data: Any) -> Dict[str, Any]:
        """Generate summary of data being validated."""
        summary = {"data_type": type(data).__name__}
        
        if isinstance(data, pd.DataFrame):
            summary.update({
                "rows": len(data),
                "columns": len(data.columns),
                "column_names": list(data.columns),
                "memory_usage_mb": data.memory_usage(deep=True).sum() / 1024 / 1024,
                "null_counts": data.isnull().sum().to_dict(),
            })
        elif isinstance(data, dict):
            summary.update({
                "keys": list(data.keys()),
                "key_count": len(data),
            })
        elif isinstance(data, (list, tuple)):
            summary.update({
                "length": len(data),
                "item_types": list(set(type(item).__name__ for item in data[:100]))  # Sample first 100
            })
        
        return summary
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get framework statistics."""
        return {
            "total_rules": len(self._rules),
            "rule_groups": len(self._rule_groups),
            "rules_by_type": {
                vtype.value: len(self.get_rules_by_type(vtype))
                for vtype in ValidationType
            },
            "enabled_rules": len([r for r in self._rules.values() if r.enabled]),
            "disabled_rules": len([r for r in self._rules.values() if not r.enabled]),
        }