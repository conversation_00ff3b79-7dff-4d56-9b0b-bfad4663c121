name: Connect Platform CI/CD

# 触发条件
on:
  push:
    branches: [ main, develop, 'feature/*', 'hotfix/*' ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点执行完整测试
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'staging'
        type: choice
        options:
        - development
        - staging
        - production
      run_performance_tests:
        description: '运行性能测试'
        required: false
        default: false
        type: boolean
      run_security_scans:
        description: '运行安全扫描'
        required: false
        default: true
        type: boolean

# 环境变量
env:
  PYTHON_VERSION: '3.9'
  NODE_VERSION: '16'
  POSTGRES_VERSION: '13'
  REDIS_VERSION: '6'

# 作业定义
jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
    
    - name: 代码格式检查
      run: |
        black --check .
        isort --check-only .
    
    - name: 代码风格检查
      run: |
        flake8 .
        pylint src/ --fail-under=8.0
    
    - name: 类型检查
      run: mypy src/
    
    - name: 前端代码质量检查
      if: hashFiles('package.json') != ''
      run: |
        npm ci
        npm run lint
        npm run format:check

  # 构建阶段
  build:
    name: 构建应用
    runs-on: ubuntu-latest
    needs: code-quality
    timeout-minutes: 20
    
    strategy:
      matrix:
        python-version: ['3.8', '3.9', '3.10']
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
    
    - name: 设置Node.js
      if: hashFiles('package.json') != ''
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 安装前端依赖
      if: hashFiles('package.json') != ''
      run: npm ci
    
    - name: 构建前端
      if: hashFiles('package.json') != ''
      run: npm run build
    
    - name: 构建Python包
      run: |
        python -m build
    
    - name: 上传构建产物
      uses: actions/upload-artifact@v3
      with:
        name: build-artifacts-${{ matrix.python-version }}
        path: |
          dist/
          build/
        retention-days: 7

  # 单元测试
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: build
    timeout-minutes: 30
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: connect_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:${{ env.REDIS_VERSION }}
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    strategy:
      matrix:
        python-version: ['3.8', '3.9', '3.10']
      fail-fast: false
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
    
    - name: 下载构建产物
      uses: actions/download-artifact@v3
      with:
        name: build-artifacts-${{ matrix.python-version }}
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 运行单元测试
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/connect_test
        REDIS_URL: redis://localhost:6379/1
      run: |
        pytest tests/unit \
          --cov=src \
          --cov-report=xml:coverage-reports/coverage.xml \
          --cov-report=html:coverage-reports/html \
          --junitxml=test-reports/junit.xml \
          --json-report \
          --json-report-file=test-reports/report.json \
          -v
    
    - name: 上传测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-reports-${{ matrix.python-version }}
        path: |
          test-reports/
          coverage-reports/
        retention-days: 30
    
    - name: 上传覆盖率到Codecov
      uses: codecov/codecov-action@v3
      if: matrix.python-version == '3.9'
      with:
        file: coverage-reports/coverage.xml
        flags: unittests
        name: codecov-umbrella

  # 集成测试
  integration-tests:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: unit-tests
    timeout-minutes: 45
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: connect_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:${{ env.REDIS_VERSION }}
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 运行集成测试
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/connect_test
        REDIS_URL: redis://localhost:6379/1
      run: |
        pytest tests/integration \
          --junitxml=test-reports/integration-junit.xml \
          --json-report \
          --json-report-file=test-reports/integration-report.json \
          -v
    
    - name: 上传集成测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-reports
        path: test-reports/
        retention-days: 30

  # 端到端测试
  e2e-tests:
    name: 端到端测试
    runs-on: ubuntu-latest
    needs: integration-tests
    timeout-minutes: 60
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: connect_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:${{ env.REDIS_VERSION }}
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 设置Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        npm ci
    
    - name: 启动应用服务
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/connect_test
        REDIS_URL: redis://localhost:6379/1
      run: |
        python manage.py migrate
        python manage.py collectstatic --noinput
        python manage.py runserver &
        sleep 10
    
    - name: 运行端到端测试
      run: |
        pytest tests/e2e \
          --junitxml=test-reports/e2e-junit.xml \
          --json-report \
          --json-report-file=test-reports/e2e-report.json \
          --browser=chrome \
          --headless \
          -v
    
    - name: 上传E2E测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-reports
        path: |
          test-reports/
          screenshots/
        retention-days: 30

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: code-quality
    timeout-minutes: 30
    if: github.event.inputs.run_security_scans != 'false'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装安全扫描工具
      run: |
        python -m pip install --upgrade pip
        pip install safety bandit semgrep
    
    - name: 依赖库安全扫描
      run: |
        safety check --json --output security-reports/safety-report.json || true
        pip-audit --format=json --output=security-reports/pip-audit-report.json || true
    
    - name: 代码安全扫描
      run: |
        bandit -r . -f json -o security-reports/bandit-report.json || true
        semgrep --config=auto --json --output=security-reports/semgrep-report.json || true
    
    - name: 运行安全测试
      run: |
        pytest tests/security \
          --junitxml=security-reports/security-junit.xml \
          --json-report \
          --json-report-file=security-reports/security-report.json \
          -v
    
    - name: 上传安全扫描报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: security-reports/
        retention-days: 90

  # 性能测试
  performance-tests:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: integration-tests
    timeout-minutes: 60
    if: github.event.inputs.run_performance_tests == 'true' || github.event_name == 'schedule'
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: connect_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:${{ env.REDIS_VERSION }}
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 启动应用服务
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/connect_test
        REDIS_URL: redis://localhost:6379/1
      run: |
        python manage.py migrate
        python manage.py runserver &
        sleep 10
    
    - name: 运行性能测试
      run: |
        pytest tests/performance \
          --junitxml=performance-reports/performance-junit.xml \
          --json-report \
          --json-report-file=performance-reports/performance-report.json \
          -v
    
    - name: 上传性能测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-reports
        path: performance-reports/
        retention-days: 30

  # 质量门禁检查
  quality-gate:
    name: 质量门禁检查
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests, security-scan]
    if: always()
    timeout-minutes: 15
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements-dev.txt
    
    - name: 下载所有测试报告
      uses: actions/download-artifact@v3
      with:
        path: test-results/
    
    - name: 执行质量门禁检查
      run: |
        python tests/scripts/ci_integration.py \
          --stages quality_gate \
          --verbose
    
    - name: 生成质量报告
      if: always()
      run: |
        python tests/monitoring/dashboard_integration.py \
          --generate-report \
          --input-dir test-results/ \
          --output-dir quality-reports/
    
    - name: 上传质量报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: quality-reports
        path: quality-reports/
        retention-days: 90

  # 构建Docker镜像
  build-docker:
    name: 构建Docker镜像
    runs-on: ubuntu-latest
    needs: quality-gate
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    timeout-minutes: 30
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: 登录Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: 提取元数据
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: connect/platform
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: 构建并推送Docker镜像
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 部署到开发环境
  deploy-development:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: build-docker
    if: github.ref == 'refs/heads/develop'
    environment: development
    timeout-minutes: 20
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 部署到开发环境
      run: |
        echo "部署到开发环境"
        # 这里添加实际的部署脚本
    
    - name: 运行部署后验证
      run: |
        pytest tests/deployment \
          --env=development \
          --junitxml=deployment-reports/verification-junit.xml
    
    - name: 上传部署报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: deployment-reports-development
        path: deployment-reports/
        retention-days: 30

  # 部署到预发布环境
  deploy-staging:
    name: 部署到预发布环境
    runs-on: ubuntu-latest
    needs: build-docker
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    environment: staging
    timeout-minutes: 30
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 部署到预发布环境
      run: |
        echo "部署到预发布环境"
        # 这里添加实际的部署脚本
    
    - name: 运行冒烟测试
      run: |
        pytest tests/smoke \
          --env=staging \
          --junitxml=deployment-reports/smoke-junit.xml
    
    - name: 上传部署报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: deployment-reports-staging
        path: deployment-reports/
        retention-days: 30

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.event.inputs.environment == 'production'
    environment: production
    timeout-minutes: 45
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 部署到生产环境
      run: |
        echo "部署到生产环境"
        # 这里添加实际的部署脚本
    
    - name: 运行生产验证测试
      run: |
        pytest tests/production \
          --env=production \
          --junitxml=deployment-reports/production-junit.xml
    
    - name: 上传部署报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: deployment-reports-production
        path: deployment-reports/
        retention-days: 90

  # 通知
  notify:
    name: 发送通知
    runs-on: ubuntu-latest
    needs: [quality-gate, deploy-development, deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: 发送Slack通知
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#ci-cd'
        webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
    
    - name: 发送邮件通知
      if: failure() && github.ref == 'refs/heads/main'
      uses: dawidd6/action-send-mail@v3
      with:
        server_address: smtp.gmail.com
        server_port: 587
        username: ${{ secrets.EMAIL_USERNAME }}
        password: ${{ secrets.EMAIL_PASSWORD }}
        subject: 'Connect Platform CI/CD 失败通知'
        body: |
          CI/CD流水线执行失败
          
          仓库: ${{ github.repository }}
          分支: ${{ github.ref }}
          提交: ${{ github.sha }}
          作者: ${{ github.actor }}
          
          请查看详细信息: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
        to: <EMAIL>
        from: <EMAIL>