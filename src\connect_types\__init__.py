"""Type definitions for the Connect telecommunications system.

This module provides type definitions and data structures for telecommunications
data processing including CDR records, EP measurements, KPI calculations, and
system-wide enumerations.
"""

from .telecom_types import (
    # Data record types
    CDRRecord,
    EPRecord,
    KPIRecord,
    
    # Enumerations
    DataSourceType,
    ProcessingStatus,
    NetworkTechnology,
    CallType,
    SignalType,
    
    # Result types
    ValidationResult,
    ProcessingResult,
    
    # Configuration types
    DatabaseConfig,
    ProcessingConfig,
    TelecomConfig,
)

__all__ = [
    # Data record types
    "CDRRecord",
    "EPRecord", 
    "KPIRecord",
    
    # Enumerations
    "DataSourceType",
    "ProcessingStatus",
    "NetworkTechnology",
    "CallType",
    "SignalType",
    
    # Result types
    "ValidationResult",
    "ProcessingResult",
    
    # Configuration types
    "DatabaseConfig",
    "ProcessingConfig",
    "TelecomConfig",
]
