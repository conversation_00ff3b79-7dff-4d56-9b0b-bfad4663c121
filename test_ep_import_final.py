#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终EP导入测试
验证完整的EP数据导入流程
"""

import sys
import os
import yaml
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database.utils.table_naming import TableNamingManager
from src.importers.ep_importer import EPImporter
from src.config.core import ConnectConfigManager

def test_ep_import_final():
    """最终EP导入测试"""
    
    print("🚀 最终EP导入测试...")
    
    # 1. 加载配置
    try:
        # 加载database.yaml
        config_path = Path(__file__).parent / 'config' / 'database.yaml'
        with open(config_path, 'r', encoding='utf-8') as f:
            database_config = yaml.safe_load(f)
        print(f"✅ Database config loaded")
        
        # 加载应用配置
        config_manager = ConnectConfigManager()
        app_config = config_manager.get_config()
        print(f"✅ App config loaded")
        
    except Exception as e:
        print(f"❌ Error loading configs: {e}")
        return
    
    # 2. 验证配置结构
    try:
        print(f"✅ Configuration validation passed")
        print(f"  📊 Database config keys: {list(database_config.keys())}")
        print(f"  📊 App config type: {type(app_config).__name__}")
        
    except Exception as e:
        print(f"❌ Configuration validation error: {e}")
        return
    
    # 3. 测试TableNamingManager
    try:
        naming_manager = TableNamingManager(database_config)
        print(f"✅ TableNamingManager initialized")
        
        # 测试表名生成
        test_file = Path("D:/connect/data/input/ep/2025/CW03/GSMCELL_CW03.xlsx")
        table_name = naming_manager.generate_table_name('ep', test_file)
        print(f"✅ Table name generated: {table_name}")
        
    except Exception as e:
        print(f"❌ TableNamingManager error: {e}")
        return
    
    # 4. 测试EPImporter
    try:
        ep_importer = EPImporter(database_config)
        print(f"✅ EPImporter initialized")
        
        # 测试表名生成
        table_name = ep_importer.get_table_name(test_file)
        print(f"✅ EPImporter table name: {table_name}")
        
        # 测试schema名称
        schema_name = ep_importer.get_schema_name()
        print(f"✅ EPImporter schema name: {schema_name}")
        
    except Exception as e:
        print(f"❌ EPImporter error: {e}")
        return
    
    # 5. 验证配置一致性
    print("\n🔍 配置一致性最终验证:")
    
    ep_config = database_config['telecom_data_sources']['ep']
    expected_values = {
        'schema_name': 'ep_to2',
        'table_name_pattern': 'ep_{cell_type}_{year}_cw{week}',
        'batch_size': 5000
    }
    
    all_good = True
    for key, expected in expected_values.items():
        if key == 'batch_size':
            actual = ep_config['batch_processing']['batch_size']
        else:
            actual = ep_config.get(key)
            
        if actual == expected:
            print(f"  ✅ {key}: {actual}")
        else:
            print(f"  ❌ {key}: 期望={expected}, 实际={actual}")
            all_good = False
    
    # 6. 最终状态报告
    print("\n📊 最终状态报告:")
    if all_good:
        print("  🎉 所有测试通过！EP导入系统配置正确。")
        print("  📋 系统已准备好处理EP数据导入")
        print("  🔧 表命名逻辑: ep_{cell_type}_{year}_cw{week}")
        print("  🗄️ 目标Schema: ep_to2")
        print("  📦 批处理大小: 5000")
    else:
        print("  ⚠️ 存在配置不一致问题，需要检查")
    
    # 7. 显示可用的测试文件
    print("\n📁 可用的测试文件:")
    ep_dir = Path("data/input/ep")
    if ep_dir.exists():
        count = 0
        for file_path in ep_dir.rglob("*.xlsx"):
            if count < 5:  # 只显示前5个
                table_name = naming_manager.generate_table_name('ep', file_path)
                print(f"  📄 {file_path.name} → {table_name}")
                count += 1
        if count == 0:
            print("  ⚠️ 未找到EP测试文件")
        elif count >= 5:
            print(f"  ... 还有更多文件")
    else:
        print("  ⚠️ EP数据目录不存在")

if __name__ == "__main__":
    test_ep_import_final()