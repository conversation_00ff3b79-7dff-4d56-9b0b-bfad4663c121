#!/usr/bin/env python3
"""
安全漏洞扫描和渗透测试模块

本模块提供全面的安全测试功能，包括SQL注入、XSS攻击、认证绕过、
权限提升、数据泄露等安全漏洞的检测和防护测试。
"""

import asyncio
import hashlib
import logging
import re
import secrets
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import pytest
import requests
from cryptography.fernet import Fernet

from src.config import get_config
from src.database.connection.pool import DatabasePoolManager
from src.database.security.encryption import DataEncryption
from src.database.security.access_control import AccessController

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SecurityTester:
    """安全测试类"""
    
    def __init__(self):
        """初始化安全测试环境"""
        from unittest.mock import Mock
        from src.config.models import DatabaseConfig
        
        # Mock config to avoid loading real configuration
        mock_db_config = Mock(spec=DatabaseConfig)
        mock_db_config.host = "localhost"
        mock_db_config.port = 5432
        mock_db_config.database = "test_db"
        mock_db_config.username = "test_user"
        mock_db_config.password = "test_pass"
        
        self.config = Mock()
        self.config.database = mock_db_config
        self.pool_manager = None
        self.test_results = {}
        self.vulnerability_count = 0
        
    async def setup(self):
        """设置测试环境"""
        from unittest.mock import AsyncMock, patch
        
        self.pool_manager = DatabasePoolManager(self.config)
        
        # Mock the initialize_pool method to avoid real database connection
        with patch.object(self.pool_manager, 'initialize_pool', new_callable=AsyncMock) as mock_init:
            mock_init.return_value = None
            self.pool_manager._is_initialized = True
            self.pool_manager._pool = AsyncMock()
            await self.pool_manager.initialize_pool()
        
    async def teardown(self):
        """清理测试环境"""
        if self.pool_manager:
            await self.pool_manager.close()
    
    def generate_sql_injection_payloads(self) -> List[str]:
        """生成SQL注入攻击载荷
        
        Returns:
            SQL注入测试载荷列表
        """
        return [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' OR 1=1 --",
            "' UNION SELECT * FROM information_schema.tables --",
            "'; INSERT INTO users (username, password) VALUES ('hacker', 'password'); --",
            "' OR EXISTS(SELECT * FROM users WHERE username='admin') --",
            "'; UPDATE users SET password='hacked' WHERE username='admin'; --",
            "' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --",
            "'; EXEC xp_cmdshell('dir'); --",
            "' OR SLEEP(5) --"
        ]
    
    def generate_xss_payloads(self) -> List[str]:
        """生成XSS攻击载荷
        
        Returns:
            XSS测试载荷列表
        """
        return [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>",
            "<textarea onfocus=alert('XSS') autofocus>",
            "<keygen onfocus=alert('XSS') autofocus>"
        ]
    
    async def test_sql_injection_vulnerabilities(self) -> Dict[str, any]:
        """测试SQL注入漏洞
        
        Returns:
            SQL注入测试结果
        """
        logger.info("开始SQL注入漏洞测试")
        
        results = {
            "total_tests": 0,
            "vulnerabilities_found": 0,
            "vulnerable_queries": [],
            "protected_queries": [],
            "test_details": []
        }
        
        payloads = self.generate_sql_injection_payloads()
        
        # 测试查询接口
        test_queries = [
            "SELECT * FROM ep_data WHERE ep_id = '{payload}'",
            "SELECT * FROM users WHERE username = '{payload}'",
            "SELECT * FROM cdr_data WHERE caller_id = '{payload}'",
            "UPDATE ep_data SET status = 'Active' WHERE ep_id = '{payload}'",
            "DELETE FROM temp_data WHERE session_id = '{payload}'"
        ]
        
        for query_template in test_queries:
            for payload in payloads:
                results["total_tests"] += 1
                
                try:
                    # 构造恶意查询
                    malicious_query = query_template.format(payload=payload)
                    
                    # 尝试执行查询
                    async with self.pool_manager.get_connection() as conn:
                        start_time = time.time()
                        
                        try:
                            await conn.fetch(malicious_query)
                            execution_time = time.time() - start_time
                            
                            # 检查是否存在时间盲注
                            if execution_time > 3.0 and "SLEEP" in payload:
                                results["vulnerabilities_found"] += 1
                                results["vulnerable_queries"].append({
                                    "query": malicious_query,
                                    "payload": payload,
                                    "vulnerability_type": "Time-based SQL Injection",
                                    "execution_time": execution_time
                                })
                                logger.warning(f"发现时间盲注漏洞: {payload}")
                            else:
                                results["protected_queries"].append({
                                    "query": query_template,
                                    "payload": payload,
                                    "status": "Protected"
                                })
                                
                        except Exception as e:
                            # 查询失败通常表示有防护措施
                            error_msg = str(e).lower()
                            
                            # 检查是否是预期的安全错误
                            if any(keyword in error_msg for keyword in 
                                   ["syntax error", "invalid", "permission denied", "access denied"]):
                                results["protected_queries"].append({
                                    "query": query_template,
                                    "payload": payload,
                                    "status": "Protected",
                                    "error": str(e)
                                })
                            else:
                                # 可能存在其他类型的漏洞
                                results["vulnerabilities_found"] += 1
                                results["vulnerable_queries"].append({
                                    "query": malicious_query,
                                    "payload": payload,
                                    "vulnerability_type": "Potential SQL Injection",
                                    "error": str(e)
                                })
                                logger.warning(f"发现潜在SQL注入漏洞: {payload}")
                                
                except Exception as e:
                    logger.error(f"SQL注入测试失败: {e}")
                    
        results["vulnerability_rate"] = results["vulnerabilities_found"] / results["total_tests"] if results["total_tests"] > 0 else 0
        
        logger.info(f"SQL注入测试完成: {results['vulnerabilities_found']}/{results['total_tests']} 个漏洞")
        return results
    
    def test_input_validation_bypass(self) -> Dict[str, any]:
        """测试输入验证绕过
        
        Returns:
            输入验证测试结果
        """
        logger.info("开始输入验证绕过测试")
        
        results = {
            "total_tests": 0,
            "bypasses_found": 0,
            "test_cases": []
        }
        
        # 测试各种绕过技术
        bypass_techniques = [
            # 长度限制绕过
            "A" * 1000,
            "A" * 10000,
            
            # 特殊字符绕过
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            
            # 编码绕过
            "%2e%2e%2f%2e%2e%2f%2e%2e%2f%65%74%63%2f%70%61%73%73%77%64",
            "\u002e\u002e\u002f\u002e\u002e\u002f\u002e\u002e\u002f\u0065\u0074\u0063\u002f\u0070\u0061\u0073\u0073\u0077\u0064",
            
            # NULL字节绕过
            "test\x00.txt",
            "admin\x00",
            
            # 格式字符串攻击
            "%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s%s",
            "%x%x%x%x%x%x%x%x%x%x%x%x%x%x%x%x%x%x%x%x",
            
            # 缓冲区溢出
            "A" * 256,
            "\x41" * 1024,
        ]
        
        for technique in bypass_techniques:
            results["total_tests"] += 1
            
            try:
                # 测试不同的输入字段
                test_fields = ["username", "password", "email", "filename", "query"]
                
                for field in test_fields:
                    # 模拟输入验证
                    validation_result = self._validate_input(field, technique)
                    
                    if not validation_result["is_valid"] and not validation_result["is_safe"]:
                        results["bypasses_found"] += 1
                        results["test_cases"].append({
                            "field": field,
                            "technique": technique[:100],  # 截断显示
                            "bypass_type": "Input Validation Bypass",
                            "risk_level": "High"
                        })
                        logger.warning(f"发现输入验证绕过: {field} - {technique[:50]}...")
                        
            except Exception as e:
                logger.error(f"输入验证测试失败: {e}")
                
        results["bypass_rate"] = results["bypasses_found"] / results["total_tests"] if results["total_tests"] > 0 else 0
        
        logger.info(f"输入验证测试完成: {results['bypasses_found']}/{results['total_tests']} 个绕过")
        return results
    
    def _validate_input(self, field: str, value: str) -> Dict[str, bool]:
        """模拟输入验证
        
        Args:
            field: 字段名
            value: 输入值
            
        Returns:
            验证结果
        """
        # 基本长度检查
        if len(value) > 255:
            return {"is_valid": False, "is_safe": True, "reason": "Length exceeded"}
        
        # 危险字符检查
        dangerous_patterns = [
            r"[<>\"']",  # XSS字符
            r"[;\-\-]",   # SQL注入字符
            r"\.\./",     # 路径遍历
            r"\x00",      # NULL字节
            r"%[0-9a-fA-F]{2}",  # URL编码
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, value):
                return {"is_valid": False, "is_safe": False, "reason": f"Dangerous pattern: {pattern}"}
        
        return {"is_valid": True, "is_safe": True, "reason": "Valid input"}
    
    def test_authentication_bypass(self) -> Dict[str, any]:
        """测试认证绕过漏洞
        
        Returns:
            认证绕过测试结果
        """
        logger.info("开始认证绕过测试")
        
        results = {
            "total_tests": 0,
            "bypasses_found": 0,
            "test_cases": []
        }
        
        # 测试认证绕过技术
        bypass_attempts = [
            # 弱密码
            {"username": "admin", "password": "admin"},
            {"username": "admin", "password": "password"},
            {"username": "admin", "password": "123456"},
            {"username": "root", "password": "root"},
            {"username": "test", "password": "test"},
            
            # 空密码
            {"username": "admin", "password": ""},
            {"username": "guest", "password": ""},
            
            # SQL注入绕过
            {"username": "admin' --", "password": "anything"},
            {"username": "admin' OR '1'='1", "password": "anything"},
            
            # 特殊字符
            {"username": "admin\x00", "password": "password"},
            {"username": "admin", "password": "password\x00"},
        ]
        
        for attempt in bypass_attempts:
            results["total_tests"] += 1
            
            try:
                # 模拟认证尝试
                auth_result = self._simulate_authentication(
                    attempt["username"], 
                    attempt["password"]
                )
                
                if auth_result["success"] and not auth_result["legitimate"]:
                    results["bypasses_found"] += 1
                    results["test_cases"].append({
                        "username": attempt["username"],
                        "password": attempt["password"][:20] + "..." if len(attempt["password"]) > 20 else attempt["password"],
                        "bypass_type": "Authentication Bypass",
                        "method": auth_result["method"],
                        "risk_level": "Critical"
                    })
                    logger.warning(f"发现认证绕过: {attempt['username']}")
                    
            except Exception as e:
                logger.error(f"认证测试失败: {e}")
                
        results["bypass_rate"] = results["bypasses_found"] / results["total_tests"] if results["total_tests"] > 0 else 0
        
        logger.info(f"认证绕过测试完成: {results['bypasses_found']}/{results['total_tests']} 个绕过")
        return results
    
    def _simulate_authentication(self, username: str, password: str) -> Dict[str, any]:
        """模拟认证过程
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            认证结果
        """
        # 模拟弱密码检查
        weak_passwords = ["admin", "password", "123456", "root", "test", ""]
        
        if password in weak_passwords and username in ["admin", "root", "test", "guest"]:
            return {
                "success": True,
                "legitimate": False,
                "method": "Weak Password",
                "reason": "Default or weak credentials"
            }
        
        # 模拟SQL注入检查
        if "'" in username or "--" in username or "OR" in username.upper():
            return {
                "success": True,
                "legitimate": False,
                "method": "SQL Injection",
                "reason": "SQL injection in username"
            }
        
        # 模拟NULL字节检查
        if "\x00" in username or "\x00" in password:
            return {
                "success": True,
                "legitimate": False,
                "method": "NULL Byte Injection",
                "reason": "NULL byte in credentials"
            }
        
        return {
            "success": False,
            "legitimate": True,
            "method": "Normal Authentication",
            "reason": "Valid authentication attempt"
        }
    
    def test_data_encryption_strength(self) -> Dict[str, any]:
        """测试数据加密强度
        
        Returns:
            加密强度测试结果
        """
        logger.info("开始数据加密强度测试")
        
        results = {
            "total_tests": 0,
            "weak_encryption_found": 0,
            "test_cases": []
        }
        
        # 测试数据
        test_data = [
            "sensitive_password_123",
            "credit_card_4111111111111111",
            "ssn_123-45-6789",
            "api_key_sk_test_123456789",
            "database_connection_string"
        ]
        
        for data in test_data:
            results["total_tests"] += 1
            
            try:
                # 测试不同的加密方法
                encryption_tests = [
                    self._test_base64_encoding(data),
                    self._test_md5_hashing(data),
                    self._test_weak_cipher(data),
                    self._test_strong_encryption(data)
                ]
                
                for test_result in encryption_tests:
                    if not test_result["is_secure"]:
                        results["weak_encryption_found"] += 1
                        results["test_cases"].append({
                            "data_type": data[:20] + "...",
                            "encryption_method": test_result["method"],
                            "weakness": test_result["weakness"],
                            "risk_level": test_result["risk_level"]
                        })
                        logger.warning(f"发现弱加密: {test_result['method']} - {test_result['weakness']}")
                        
            except Exception as e:
                logger.error(f"加密测试失败: {e}")
                
        results["weak_encryption_rate"] = results["weak_encryption_found"] / (results["total_tests"] * 4) if results["total_tests"] > 0 else 0
        
        logger.info(f"加密强度测试完成: {results['weak_encryption_found']} 个弱加密")
        return results
    
    def _test_base64_encoding(self, data: str) -> Dict[str, any]:
        """测试Base64编码（不是加密）"""
        import base64
        
        encoded = base64.b64encode(data.encode()).decode()
        decoded = base64.b64decode(encoded).decode()
        
        return {
            "method": "Base64 Encoding",
            "is_secure": False,
            "weakness": "Base64 is encoding, not encryption",
            "risk_level": "High",
            "reversible": decoded == data
        }
    
    def _test_md5_hashing(self, data: str) -> Dict[str, any]:
        """测试MD5哈希（已过时）"""
        md5_hash = hashlib.md5(data.encode()).hexdigest()
        
        return {
            "method": "MD5 Hashing",
            "is_secure": False,
            "weakness": "MD5 is cryptographically broken",
            "risk_level": "High",
            "hash": md5_hash
        }
    
    def _test_weak_cipher(self, data: str) -> Dict[str, any]:
        """测试弱密码算法"""
        # 模拟简单的Caesar密码
        shift = 3
        encrypted = ''.join(chr((ord(char) + shift) % 256) for char in data)
        
        return {
            "method": "Caesar Cipher",
            "is_secure": False,
            "weakness": "Easily breakable substitution cipher",
            "risk_level": "Critical",
            "encrypted": encrypted[:50]
        }
    
    def _test_strong_encryption(self, data: str) -> Dict[str, any]:
        """测试强加密算法"""
        try:
            # 使用Fernet（AES 128）
            key = Fernet.generate_key()
            cipher = Fernet(key)
            encrypted = cipher.encrypt(data.encode())
            
            return {
                "method": "Fernet (AES 128)",
                "is_secure": True,
                "weakness": None,
                "risk_level": "Low",
                "key_length": len(key)
            }
        except Exception as e:
            return {
                "method": "Fernet (AES 128)",
                "is_secure": False,
                "weakness": f"Encryption failed: {e}",
                "risk_level": "High"
            }
    
    async def generate_security_report(self) -> Dict[str, any]:
        """生成安全测试报告
        
        Returns:
            完整的安全测试报告
        """
        logger.info("生成安全测试报告")
        
        # 运行所有安全测试
        sql_injection_results = await self.test_sql_injection_vulnerabilities()
        input_validation_results = self.test_input_validation_bypass()
        auth_bypass_results = self.test_authentication_bypass()
        encryption_results = self.test_data_encryption_strength()
        
        # 计算总体安全评分
        total_vulnerabilities = (
            sql_injection_results["vulnerabilities_found"] +
            input_validation_results["bypasses_found"] +
            auth_bypass_results["bypasses_found"] +
            encryption_results["weak_encryption_found"]
        )
        
        total_tests = (
            sql_injection_results["total_tests"] +
            input_validation_results["total_tests"] +
            auth_bypass_results["total_tests"] +
            encryption_results["total_tests"]
        )
        
        security_score = max(0, 100 - (total_vulnerabilities / total_tests * 100)) if total_tests > 0 else 0
        
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "security_score": round(security_score, 2),
            "total_vulnerabilities": total_vulnerabilities,
            "total_tests": total_tests,
            "vulnerability_rate": round(total_vulnerabilities / total_tests * 100, 2) if total_tests > 0 else 0,
            "test_results": {
                "sql_injection": sql_injection_results,
                "input_validation": input_validation_results,
                "authentication_bypass": auth_bypass_results,
                "data_encryption": encryption_results
            },
            "recommendations": self._generate_security_recommendations({
                "sql_injection": sql_injection_results,
                "input_validation": input_validation_results,
                "authentication_bypass": auth_bypass_results,
                "data_encryption": encryption_results
            })
        }
        
        return report
    
    def _generate_security_recommendations(self, test_results: Dict) -> List[str]:
        """生成安全建议
        
        Args:
            test_results: 测试结果
            
        Returns:
            安全建议列表
        """
        recommendations = []
        
        # SQL注入建议
        if test_results["sql_injection"]["vulnerabilities_found"] > 0:
            recommendations.extend([
                "使用参数化查询或预编译语句防止SQL注入",
                "实施输入验证和输出编码",
                "使用最小权限原则配置数据库用户",
                "定期进行SQL注入安全测试"
            ])
        
        # 输入验证建议
        if test_results["input_validation"]["bypasses_found"] > 0:
            recommendations.extend([
                "实施严格的输入验证和过滤",
                "使用白名单而非黑名单进行输入验证",
                "对所有用户输入进行长度和格式检查",
                "实施输出编码防止XSS攻击"
            ])
        
        # 认证绕过建议
        if test_results["authentication_bypass"]["bypasses_found"] > 0:
            recommendations.extend([
                "强制使用强密码策略",
                "实施多因素认证(MFA)",
                "定期更换默认凭据",
                "实施账户锁定机制防止暴力破解"
            ])
        
        # 加密建议
        if test_results["data_encryption"]["weak_encryption_found"] > 0:
            recommendations.extend([
                "使用强加密算法(AES-256)",
                "实施密钥管理最佳实践",
                "对敏感数据进行端到端加密",
                "定期轮换加密密钥"
            ])
        
        # 通用安全建议
        recommendations.extend([
            "定期进行安全代码审查",
            "实施安全开发生命周期(SDLC)",
            "建立安全监控和日志记录",
            "定期进行渗透测试和漏洞扫描"
        ])
        
        return list(set(recommendations))  # 去重


# Pytest测试用例
@pytest.fixture
async def security_tester():
    """安全测试fixture"""
    tester = SecurityTester()
    await tester.setup()
    yield tester
    await tester.teardown()


@pytest.mark.asyncio
@pytest.mark.security
async def test_sql_injection_protection(security_tester):
    """测试SQL注入防护"""
    results = await security_tester.test_sql_injection_vulnerabilities()
    
    # 要求：SQL注入漏洞率应该为0
    assert results["vulnerability_rate"] == 0, f"发现SQL注入漏洞: {results['vulnerabilities_found']} 个"
    
    logger.info(f"✓ SQL注入防护测试通过: {results['total_tests']} 个测试，0 个漏洞")


@pytest.mark.security
def test_input_validation_security(security_tester):
    """测试输入验证安全性"""
    results = security_tester.test_input_validation_bypass()
    
    # 要求：输入验证绕过率应该低于5%
    assert results["bypass_rate"] < 0.05, f"输入验证绕过率过高: {results['bypass_rate']*100:.1f}%"
    
    logger.info(f"✓ 输入验证安全测试通过: {results['bypasses_found']}/{results['total_tests']} 个绕过")


@pytest.mark.security
def test_authentication_security(security_tester):
    """测试认证安全性"""
    results = security_tester.test_authentication_bypass()
    
    # 要求：认证绕过率应该为0
    assert results["bypass_rate"] == 0, f"发现认证绕过: {results['bypasses_found']} 个"
    
    logger.info(f"✓ 认证安全测试通过: {results['total_tests']} 个测试，0 个绕过")


@pytest.mark.security
def test_encryption_strength(security_tester):
    """测试加密强度"""
    results = security_tester.test_data_encryption_strength()
    
    # 要求：弱加密使用率应该低于10%
    assert results["weak_encryption_rate"] < 0.1, f"弱加密使用率过高: {results['weak_encryption_rate']*100:.1f}%"
    
    logger.info(f"✓ 加密强度测试通过: {results['weak_encryption_found']} 个弱加密")


@pytest.mark.asyncio
@pytest.mark.security
async def test_comprehensive_security_scan(security_tester):
    """综合安全扫描测试"""
    report = await security_tester.generate_security_report()
    
    # 要求：总体安全评分应该大于80分
    assert report["security_score"] > 80, f"安全评分过低: {report['security_score']}/100"
    
    # 要求：高危漏洞数量应该为0
    critical_vulnerabilities = 0
    for test_type, results in report["test_results"].items():
        if "test_cases" in results:
            critical_vulnerabilities += sum(1 for case in results["test_cases"] 
                                           if case.get("risk_level") == "Critical")
    
    assert critical_vulnerabilities == 0, f"发现 {critical_vulnerabilities} 个高危漏洞"
    
    logger.info(f"✓ 综合安全扫描通过: 安全评分 {report['security_score']}/100")
    
    # 输出安全建议
    if report["recommendations"]:
        logger.info("安全建议:")
        for i, recommendation in enumerate(report["recommendations"][:5], 1):
            logger.info(f"  {i}. {recommendation}")


if __name__ == "__main__":
    """运行安全测试"""
    async def main():
        tester = SecurityTester()
        await tester.setup()
        
        try:
            logger.info("=== 开始安全漏洞扫描 ===")
            
            # 生成完整的安全报告
            report = await tester.generate_security_report()
            
            # 输出报告摘要
            logger.info(f"\n=== 安全扫描报告 ===")
            logger.info(f"安全评分: {report['security_score']}/100")
            logger.info(f"总漏洞数: {report['total_vulnerabilities']}")
            logger.info(f"总测试数: {report['total_tests']}")
            logger.info(f"漏洞率: {report['vulnerability_rate']:.2f}%")
            
            # 输出详细结果
            for test_type, results in report["test_results"].items():
                logger.info(f"\n{test_type.replace('_', ' ').title()}:")
                if "vulnerabilities_found" in results:
                    logger.info(f"  漏洞数: {results['vulnerabilities_found']}")
                elif "bypasses_found" in results:
                    logger.info(f"  绕过数: {results['bypasses_found']}")
                elif "weak_encryption_found" in results:
                    logger.info(f"  弱加密数: {results['weak_encryption_found']}")
            
            # 输出安全建议
            logger.info(f"\n=== 安全建议 ===")
            for i, recommendation in enumerate(report["recommendations"], 1):
                logger.info(f"{i}. {recommendation}")
                
        finally:
            await tester.teardown()
    
    asyncio.run(main())