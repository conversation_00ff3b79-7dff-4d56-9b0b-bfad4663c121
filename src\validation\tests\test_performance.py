"""统一验证框架性能测试

专门测试验证框架在大数据量和高并发场景下的性能表现。
"""

import pytest
import pandas as pd
import numpy as np
import time
import threading
import multiprocessing
import psutil
import gc
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from datetime import datetime, timedelta
from unittest.mock import patch

from ..factory import ValidationFactory, get_validation_factory
from ..core import ValidationFramework, ValidationContext
from ..validators import DataStructureValidator, DataValueValidator, TelecomDataValidator
from ..rules import CDRValidationRules, KPIValidationRules
from .conftest import performance_test, memory_test


class TestDataSizePerformance:
    """数据规模性能测试类"""
    
    def create_large_cdr_dataset(self, size):
        """创建大型CDR数据集"""
        np.random.seed(42)  # 确保可重现性
        
        data = {
            'CALLING_NUMBER': [f'1{np.random.randint(100000000, 999999999)}' for _ in range(size)],
            'CALLED_NUMBER': [f'1{np.random.randint(100000000, 999999999)}' for _ in range(size)],
            'CALL_START_TIME': [
                (datetime.now() - timedelta(days=np.random.randint(0, 30))).strftime('%Y-%m-%d %H:%M:%S')
                for _ in range(size)
            ],
            'CALL_END_TIME': [
                (datetime.now() - timedelta(days=np.random.randint(0, 30)) + timedelta(minutes=np.random.randint(1, 60))).strftime('%Y-%m-%d %H:%M:%S')
                for _ in range(size)
            ],
            'CALL_DURATION': np.random.randint(1, 3600, size),
            'CELL_ID': np.random.randint(1000, 9999, size),
            'LAC': np.random.randint(100, 999, size),
            'IMSI': [f'{np.random.randint(100000000000000, 999999999999999)}' for _ in range(size)]
        }
        
        return pd.DataFrame(data)
    
    @performance_test(max_time=2.0)
    def test_small_dataset_performance(self):
        """测试小数据集性能（1K行）"""
        data = self.create_large_cdr_dataset(1000)
        factory = get_validation_factory()
        
        result = factory.validate_cdr(data)
        assert result is not None
    
    @performance_test(max_time=5.0)
    def test_medium_dataset_performance(self):
        """测试中等数据集性能（10K行）"""
        data = self.create_large_cdr_dataset(10000)
        factory = get_validation_factory()
        
        result = factory.validate_cdr(data)
        assert result is not None
    
    @performance_test(max_time=15.0)
    def test_large_dataset_performance(self):
        """测试大数据集性能（100K行）"""
        data = self.create_large_cdr_dataset(100000)
        factory = get_validation_factory()
        
        result = factory.validate_cdr(data)
        assert result is not None
    
    @performance_test(max_time=60.0)
    def test_very_large_dataset_performance(self):
        """测试超大数据集性能（500K行）"""
        data = self.create_large_cdr_dataset(500000)
        factory = ValidationFactory({
            'parallel_validation': True,
            'max_workers': 4
        })
        
        result = factory.validate_cdr(data)
        assert result is not None
    
    def test_performance_scaling(self):
        """测试性能扩展性"""
        factory = get_validation_factory()
        sizes = [1000, 5000, 10000, 25000]
        times = []
        
        for size in sizes:
            data = self.create_large_cdr_dataset(size)
            
            start_time = time.time()
            result = factory.validate_cdr(data)
            end_time = time.time()
            
            validation_time = end_time - start_time
            times.append(validation_time)
            
            assert result is not None
            print(f"Size: {size}, Time: {validation_time:.3f}s, Rate: {size/validation_time:.0f} rows/s")
        
        # 验证性能不会急剧下降（允许一些非线性增长）
        for i in range(1, len(times)):
            size_ratio = sizes[i] / sizes[i-1]
            time_ratio = times[i] / times[i-1]
            # 时间增长不应该超过数据量增长的2倍
            assert time_ratio <= size_ratio * 2, f"Performance degradation at size {sizes[i]}"


class TestMemoryPerformance:
    """内存性能测试类"""
    
    @memory_test(max_memory_mb=100)
    def test_memory_usage_small_dataset(self):
        """测试小数据集内存使用"""
        data = pd.DataFrame({
            'CALLING_NUMBER': ['1234567890'] * 1000,
            'CALLED_NUMBER': ['0987654321'] * 1000,
            'CALL_START_TIME': ['2023-01-01 10:00:00'] * 1000,
            'CALL_END_TIME': ['2023-01-01 10:05:00'] * 1000,
            'CALL_DURATION': [300] * 1000,
            'CELL_ID': [1234] * 1000,
            'LAC': [567] * 1000,
            'IMSI': ['123456789012345'] * 1000
        })
        
        factory = get_validation_factory()
        result = factory.validate_cdr(data)
        assert result is not None
    
    @memory_test(max_memory_mb=500)
    def test_memory_usage_large_dataset(self):
        """测试大数据集内存使用"""
        # 创建50K行数据
        size = 50000
        data = pd.DataFrame({
            'CALLING_NUMBER': [f'1{i:09d}' for i in range(size)],
            'CALLED_NUMBER': [f'2{i:09d}' for i in range(size)],
            'CALL_START_TIME': ['2023-01-01 10:00:00'] * size,
            'CALL_END_TIME': ['2023-01-01 10:05:00'] * size,
            'CALL_DURATION': [300] * size,
            'CELL_ID': list(range(1000, 1000 + size)),
            'LAC': [567] * size,
            'IMSI': [f'{i:015d}' for i in range(size)]
        })
        
        factory = get_validation_factory()
        result = factory.validate_cdr(data)
        assert result is not None
    
    def test_memory_cleanup(self):
        """测试内存清理"""
        factory = get_validation_factory()
        
        # 记录初始内存
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行多次验证
        for i in range(10):
            data = pd.DataFrame({
                'CALLING_NUMBER': [f'1{j:09d}' for j in range(1000)],
                'CALLED_NUMBER': [f'2{j:09d}' for j in range(1000)],
                'CALL_START_TIME': ['2023-01-01 10:00:00'] * 1000,
                'CALL_END_TIME': ['2023-01-01 10:05:00'] * 1000,
                'CALL_DURATION': [300] * 1000,
                'CELL_ID': list(range(1000, 2000)),
                'LAC': [567] * 1000,
                'IMSI': [f'{j:015d}' for j in range(1000)]
            })
            
            result = factory.validate_cdr(data)
            assert result is not None
            
            # 强制垃圾回收
            del data, result
            gc.collect()
        
        # 检查最终内存
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该在合理范围内（小于100MB）
        assert memory_increase < 100, f"Memory leak detected: {memory_increase:.2f}MB increase"
    
    def test_memory_efficient_validation(self):
        """测试内存高效验证"""
        factory = get_validation_factory()
        
        # 创建大数据集
        size = 20000
        data = pd.DataFrame({
            'CALLING_NUMBER': [f'1{i:09d}' for i in range(size)],
            'CALLED_NUMBER': [f'2{i:09d}' for i in range(size)],
            'CALL_START_TIME': ['2023-01-01 10:00:00'] * size,
            'CALL_END_TIME': ['2023-01-01 10:05:00'] * size,
            'CALL_DURATION': [300] * size,
            'CELL_ID': list(range(1000, 1000 + size)),
            'LAC': [567] * size,
            'IMSI': [f'{i:015d}' for i in range(size)]
        })
        
        # 监控内存使用
        process = psutil.Process()
        start_memory = process.memory_info().rss / 1024 / 1024
        
        result = factory.validate_cdr(data)
        
        peak_memory = process.memory_info().rss / 1024 / 1024
        memory_usage = peak_memory - start_memory
        
        assert result is not None
        # 内存使用应该合理（数据大小的3倍以内）
        data_size_mb = data.memory_usage(deep=True).sum() / 1024 / 1024
        assert memory_usage <= data_size_mb * 3, f"Memory usage too high: {memory_usage:.2f}MB for {data_size_mb:.2f}MB data"


class TestConcurrencyPerformance:
    """并发性能测试类"""
    
    def create_test_data(self, size=1000):
        """创建测试数据"""
        return pd.DataFrame({
            'CALLING_NUMBER': [f'1{i:09d}' for i in range(size)],
            'CALLED_NUMBER': [f'2{i:09d}' for i in range(size)],
            'CALL_START_TIME': ['2023-01-01 10:00:00'] * size,
            'CALL_END_TIME': ['2023-01-01 10:05:00'] * size,
            'CALL_DURATION': [300] * size,
            'CELL_ID': list(range(1000, 1000 + size)),
            'LAC': [567] * size,
            'IMSI': [f'{i:015d}' for i in range(size)]
        })
    
    @performance_test(max_time=10.0)
    def test_sequential_validation_performance(self):
        """测试顺序验证性能"""
        factory = get_validation_factory()
        data = self.create_test_data(5000)
        
        # 顺序执行10次验证
        results = []
        for _ in range(10):
            result = factory.validate_cdr(data)
            results.append(result)
        
        assert len(results) == 10
        assert all(r is not None for r in results)
    
    @performance_test(max_time=8.0)
    def test_thread_concurrent_validation_performance(self):
        """测试线程并发验证性能"""
        factory = ValidationFactory({
            'parallel_validation': True,
            'max_workers': 4
        })
        data = self.create_test_data(2000)
        
        def validate_data():
            return factory.validate_cdr(data)
        
        # 使用线程池并发执行
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [executor.submit(validate_data) for _ in range(10)]
            results = [future.result() for future in futures]
        
        assert len(results) == 10
        assert all(r is not None for r in results)
    
    def test_thread_safety(self):
        """测试线程安全性"""
        factory = get_validation_factory()
        data = self.create_test_data(1000)
        
        results = []
        errors = []
        
        def validate_worker():
            try:
                for _ in range(5):
                    result = factory.validate_cdr(data)
                    results.append(result)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程
        threads = []
        for _ in range(4):
            thread = threading.Thread(target=validate_worker)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(errors) == 0, f"Thread safety errors: {errors}"
        assert len(results) == 20  # 4 threads * 5 validations each
        assert all(r is not None for r in results)
    
    def test_cache_concurrency(self):
        """测试缓存并发性"""
        factory = ValidationFactory({
            'cache_enabled': True,
            'max_cache_size': 10
        })
        data = self.create_test_data(1000)
        
        cache_hits = []
        
        def cache_worker():
            for _ in range(10):
                # 获取框架（应该使用缓存）
                framework = factory.get_framework('cdr')
                result = framework.validate(data)
                cache_hits.append(result is not None)
        
        # 并发访问缓存
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=cache_worker)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 验证缓存工作正常
        assert len(cache_hits) == 30
        assert all(cache_hits)
        
        # 检查缓存统计
        cache_info = factory.get_cache_info()
        assert cache_info['size'] > 0
    
    @performance_test(max_time=15.0)
    def test_high_concurrency_stress(self):
        """测试高并发压力"""
        factory = ValidationFactory({
            'parallel_validation': True,
            'max_workers': 8,
            'cache_enabled': True
        })
        data = self.create_test_data(1000)
        
        def stress_worker():
            results = []
            for _ in range(20):
                result = factory.validate_cdr(data)
                results.append(result is not None)
            return results
        
        # 高并发执行
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = [executor.submit(stress_worker) for _ in range(8)]
            all_results = []
            for future in futures:
                all_results.extend(future.result())
        
        # 验证所有验证都成功
        assert len(all_results) == 160  # 8 workers * 20 validations each
        assert all(all_results)


class TestValidationRulePerformance:
    """验证规则性能测试类"""
    
    def test_rule_execution_performance(self):
        """测试规则执行性能"""
        data = pd.DataFrame({
            'CALLING_NUMBER': ['1234567890'] * 10000,
            'CALLED_NUMBER': ['0987654321'] * 10000,
            'CALL_START_TIME': ['2023-01-01 10:00:00'] * 10000,
            'CALL_END_TIME': ['2023-01-01 10:05:00'] * 10000,
            'CALL_DURATION': [300] * 10000,
            'CELL_ID': [1234] * 10000,
            'LAC': [567] * 10000,
            'IMSI': ['123456789012345'] * 10000
        })
        
        # 测试不同类型的规则性能
        structure_rules = CDRValidationRules.get_structure_rules()
        value_rules = CDRValidationRules.get_value_rules()
        telecom_rules = CDRValidationRules.get_telecom_rules()
        
        # 创建验证器
        structure_validator = DataStructureValidator()
        value_validator = DataValueValidator()
        telecom_validator = TelecomDataValidator()
        
        # 测试结构规则性能
        start_time = time.time()
        for rule in structure_rules:
            structure_validator.validate(data, [rule])
        structure_time = time.time() - start_time
        
        # 测试值规则性能
        start_time = time.time()
        for rule in value_rules:
            value_validator.validate(data, [rule])
        value_time = time.time() - start_time
        
        # 测试电信规则性能
        start_time = time.time()
        for rule in telecom_rules:
            telecom_validator.validate(data, [rule])
        telecom_time = time.time() - start_time
        
        print(f"Structure rules time: {structure_time:.3f}s")
        print(f"Value rules time: {value_time:.3f}s")
        print(f"Telecom rules time: {telecom_time:.3f}s")
        
        # 所有规则执行时间应该在合理范围内
        assert structure_time < 5.0
        assert value_time < 5.0
        assert telecom_time < 5.0
    
    def test_rule_complexity_scaling(self):
        """测试规则复杂度扩展性"""
        sizes = [1000, 5000, 10000]
        times = []
        
        for size in sizes:
            data = pd.DataFrame({
                'CALLING_NUMBER': [f'1{i:09d}' for i in range(size)],
                'CALLED_NUMBER': [f'2{i:09d}' for i in range(size)],
                'CALL_START_TIME': ['2023-01-01 10:00:00'] * size,
                'CALL_END_TIME': ['2023-01-01 10:05:00'] * size,
                'CALL_DURATION': [300] * size,
                'CELL_ID': list(range(1000, 1000 + size)),
                'LAC': [567] * size,
                'IMSI': [f'{i:015d}' for i in range(size)]
            })
            
            factory = get_validation_factory()
            
            start_time = time.time()
            result = factory.validate_cdr(data)
            end_time = time.time()
            
            validation_time = end_time - start_time
            times.append(validation_time)
            
            assert result is not None
        
        # 验证时间复杂度是线性的或接近线性的
        for i in range(1, len(times)):
            size_ratio = sizes[i] / sizes[i-1]
            time_ratio = times[i] / times[i-1]
            # 时间增长不应该超过数据量增长的1.5倍
            assert time_ratio <= size_ratio * 1.5, f"Poor scaling at size {sizes[i]}"
    
    @performance_test(max_time=3.0)
    def test_parallel_rule_execution(self):
        """测试并行规则执行"""
        data = pd.DataFrame({
            'CALLING_NUMBER': ['1234567890'] * 5000,
            'CALLED_NUMBER': ['0987654321'] * 5000,
            'CALL_START_TIME': ['2023-01-01 10:00:00'] * 5000,
            'CALL_END_TIME': ['2023-01-01 10:05:00'] * 5000,
            'CALL_DURATION': [300] * 5000,
            'CELL_ID': [1234] * 5000,
            'LAC': [567] * 5000,
            'IMSI': ['123456789012345'] * 5000
        })
        
        factory = ValidationFactory({
            'parallel_validation': True,
            'max_workers': 4
        })
        
        result = factory.validate_cdr(data)
        assert result is not None


class TestCachePerformance:
    """缓存性能测试类"""
    
    def test_cache_hit_performance(self):
        """测试缓存命中性能"""
        factory = ValidationFactory({
            'cache_enabled': True,
            'max_cache_size': 10
        })
        
        # 第一次访问（缓存未命中）
        start_time = time.time()
        framework1 = factory.get_framework('cdr')
        miss_time = time.time() - start_time
        
        # 第二次访问（缓存命中）
        start_time = time.time()
        framework2 = factory.get_framework('cdr')
        hit_time = time.time() - start_time
        
        # 缓存命中应该显著更快
        assert hit_time < miss_time * 0.1, f"Cache not effective: hit={hit_time:.6f}s, miss={miss_time:.6f}s"
        assert framework1 is framework2
    
    def test_cache_size_impact(self):
        """测试缓存大小对性能的影响"""
        data = pd.DataFrame({
            'CALLING_NUMBER': ['1234567890'] * 1000,
            'CALLED_NUMBER': ['0987654321'] * 1000,
            'CALL_START_TIME': ['2023-01-01 10:00:00'] * 1000,
            'CALL_END_TIME': ['2023-01-01 10:05:00'] * 1000,
            'CALL_DURATION': [300] * 1000,
            'CELL_ID': [1234] * 1000,
            'LAC': [567] * 1000,
            'IMSI': ['123456789012345'] * 1000
        })
        
        # 测试不同缓存大小
        cache_sizes = [1, 5, 10, 20]
        performance_results = []
        
        for cache_size in cache_sizes:
            factory = ValidationFactory({
                'cache_enabled': True,
                'max_cache_size': cache_size
            })
            
            # 预热缓存
            factory.get_framework('cdr')
            factory.get_framework('kpi')
            
            # 测试性能
            start_time = time.time()
            for _ in range(10):
                result = factory.validate_cdr(data)
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 10
            performance_results.append(avg_time)
            
            cache_info = factory.get_cache_info()
            print(f"Cache size: {cache_size}, Avg time: {avg_time:.4f}s, Hit rate: {cache_info.get('hit_rate', 0):.2f}")
        
        # 较大的缓存应该有更好的性能
        assert performance_results[-1] <= performance_results[0] * 1.2
    
    def test_cache_eviction_performance(self):
        """测试缓存淘汰性能"""
        factory = ValidationFactory({
            'cache_enabled': True,
            'max_cache_size': 3  # 小缓存，容易触发淘汰
        })
        
        # 创建超过缓存大小的框架
        frameworks = []
        times = []
        
        for data_type in ['cdr', 'kpi', 'cfg', 'cdr', 'kpi']:  # 重复访问触发淘汰
            start_time = time.time()
            framework = factory.get_framework(data_type)
            end_time = time.time()
            
            frameworks.append(framework)
            times.append(end_time - start_time)
        
        # 缓存淘汰不应该显著影响性能
        avg_time = sum(times) / len(times)
        max_time = max(times)
        assert max_time <= avg_time * 3, f"Cache eviction causing performance issues: max={max_time:.6f}s, avg={avg_time:.6f}s"
        
        # 验证缓存大小限制
        cache_info = factory.get_cache_info()
        assert cache_info['size'] <= 3


class TestBenchmarkSuite:
    """基准测试套件"""
    
    def test_comprehensive_benchmark(self):
        """综合基准测试"""
        factory = get_validation_factory()
        
        # 测试场景
        scenarios = [
            ('Small CDR', 1000, 'cdr'),
            ('Medium CDR', 10000, 'cdr'),
            ('Large CDR', 50000, 'cdr'),
            ('Small KPI', 1000, 'kpi'),
            ('Medium KPI', 10000, 'kpi')
        ]
        
        benchmark_results = []
        
        for name, size, data_type in scenarios:
            # 创建测试数据
            if data_type == 'cdr':
                data = pd.DataFrame({
                    'CALLING_NUMBER': [f'1{i:09d}' for i in range(size)],
                    'CALLED_NUMBER': [f'2{i:09d}' for i in range(size)],
                    'CALL_START_TIME': ['2023-01-01 10:00:00'] * size,
                    'CALL_END_TIME': ['2023-01-01 10:05:00'] * size,
                    'CALL_DURATION': [300] * size,
                    'CELL_ID': list(range(1000, 1000 + size)),
                    'LAC': [567] * size,
                    'IMSI': [f'{i:015d}' for i in range(size)]
                })
            else:  # KPI
                data = pd.DataFrame({
                    'CELL_ID': list(range(1000, 1000 + size)),
                    'KPI_NAME': ['RSRP'] * size,
                    'KPI_VALUE': [-80.5] * size,
                    'MEASUREMENT_TIME': ['2023-01-01 10:00:00'] * size,
                    'LAC': [567] * size
                })
            
            # 执行基准测试
            start_time = time.time()
            if data_type == 'cdr':
                result = factory.validate_cdr(data)
            else:
                result = factory.validate_kpi(data)
            end_time = time.time()
            
            validation_time = end_time - start_time
            throughput = size / validation_time
            
            benchmark_results.append({
                'scenario': name,
                'size': size,
                'time': validation_time,
                'throughput': throughput,
                'valid': result.is_valid if result else False
            })
            
            print(f"{name}: {size} rows in {validation_time:.3f}s ({throughput:.0f} rows/s)")
        
        # 验证所有基准测试通过
        assert all(r['valid'] for r in benchmark_results)
        
        # 验证性能基准
        for result in benchmark_results:
            if result['size'] <= 10000:
                assert result['throughput'] >= 1000, f"Low throughput for {result['scenario']}: {result['throughput']:.0f} rows/s"
            else:
                assert result['throughput'] >= 500, f"Low throughput for {result['scenario']}: {result['throughput']:.0f} rows/s"
        
        return benchmark_results
    
    def test_performance_regression(self):
        """性能回归测试"""
        # 基准性能指标（可以根据实际情况调整）
        baseline_performance = {
            'small_dataset_throughput': 5000,  # rows/s
            'medium_dataset_throughput': 3000,  # rows/s
            'validation_latency': 0.001,  # s per row
            'memory_efficiency': 2.0  # MB per 1000 rows
        }
        
        factory = get_validation_factory()
        
        # 测试小数据集吞吐量
        small_data = pd.DataFrame({
            'CALLING_NUMBER': ['1234567890'] * 1000,
            'CALLED_NUMBER': ['0987654321'] * 1000,
            'CALL_START_TIME': ['2023-01-01 10:00:00'] * 1000,
            'CALL_END_TIME': ['2023-01-01 10:05:00'] * 1000,
            'CALL_DURATION': [300] * 1000,
            'CELL_ID': [1234] * 1000,
            'LAC': [567] * 1000,
            'IMSI': ['123456789012345'] * 1000
        })
        
        start_time = time.time()
        result = factory.validate_cdr(small_data)
        small_time = time.time() - start_time
        small_throughput = 1000 / small_time
        
        # 测试中等数据集吞吐量
        medium_data = pd.DataFrame({
            'CALLING_NUMBER': [f'1{i:09d}' for i in range(5000)],
            'CALLED_NUMBER': [f'2{i:09d}' for i in range(5000)],
            'CALL_START_TIME': ['2023-01-01 10:00:00'] * 5000,
            'CALL_END_TIME': ['2023-01-01 10:05:00'] * 5000,
            'CALL_DURATION': [300] * 5000,
            'CELL_ID': list(range(1000, 6000)),
            'LAC': [567] * 5000,
            'IMSI': [f'{i:015d}' for i in range(5000)]
        })
        
        start_time = time.time()
        result = factory.validate_cdr(medium_data)
        medium_time = time.time() - start_time
        medium_throughput = 5000 / medium_time
        
        # 验证性能不低于基准
        performance_ratio = 0.8  # 允许20%的性能下降
        
        assert small_throughput >= baseline_performance['small_dataset_throughput'] * performance_ratio, \
            f"Small dataset throughput regression: {small_throughput:.0f} < {baseline_performance['small_dataset_throughput'] * performance_ratio:.0f}"
        
        assert medium_throughput >= baseline_performance['medium_dataset_throughput'] * performance_ratio, \
            f"Medium dataset throughput regression: {medium_throughput:.0f} < {baseline_performance['medium_dataset_throughput'] * performance_ratio:.0f}"
        
        print(f"Performance test passed:")
        print(f"  Small dataset: {small_throughput:.0f} rows/s (baseline: {baseline_performance['small_dataset_throughput']})")
        print(f"  Medium dataset: {medium_throughput:.0f} rows/s (baseline: {baseline_performance['medium_dataset_throughput']})")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])