{"total_modules": 101, "total_dependencies": 37, "circular_dependencies": [], "modules": ["database.security.encryption.encryption", "database.monitoring.health", "database.schema.models", "exporters.json_exporter", "geo.vector", "database.query_builder.validators", "geospatial", "database.query_builder.dialects", "database.etl.excel_processor", "database.operations.table_operations", "database.connection.read_write_splitter", "database.connection", "exporters.geojson_exporter", "database.utils.helpers", "importers.nlg_importer", "database.etl", "database.operations.exporter", "database.etl.validator", "database.operations", "database.security.auth", "database", "database.etl.batch_processor", "geo.raster", "database.operations.bulk_operations", "importers.json_importer", "config.settings", "config", "database.query_builder.builder", "utils.data_validator", "database.etl.processors", "database.schema.table_schema", "database.schema", "database.geospatial.vendor_tagger", "importers.data_transformer", "importers", "exporters.csv_exporter", "database.etl.loader", "database.query_builder.aggregations", "database.monitoring.alerts", "database.query_builder", "database.geospatial.polygon_handler", "exporters.report_generator", "database.schema.validators", "database.utils.progress_tracker", "database.monitoring", "database.utils.performance", "database.security", "config.environment", "database.utils.validators", "database.monitoring.metrics", "utils", "database.utils.decorators", "exporters", "geo.qgis_integration", "database.geospatial.validator", "database.etl.pipeline", "geo.geometry", "utils.file_handler", "importers.ep_importer", "database.security.audit", "database.query_builder.conditions", "importers.excel_importer", "exporters.file_manager", "importers.cdr_importer", "utils.cache_manager", "database.etl.extractor", "database.etl.error_handler", "database.geospatial.processor", "database.operations.transaction_manager", "database.schema.manager", "database.query_builder.joins", "database.connection.pool", "database.geospatial", "database.query_builder.optimizers", "database.geospatial.indexer", "exporters.base", "database.security.access_control", "database.monitoring.logger", "database.utils", "importers.csv_importer", "database.exceptions", "database.utils.security", "database.etl.processors.csv_processor", "exporters.excel_exporter", "database.types", "database.connection.health_check", "importers.batch_processor", "database.utils.batch_processor", "database.constants", "database.etl.json_processor", "database.operations.importer", "importers.base", "database.schema.router", "database.config", "database.operations.crud", "database.connection.session", "database.etl.transformer", "database.security.encryption", "geo", "exporters.data_formatter", "database.operations.database_manager"], "dependency_graph": {"database.etl.batch_processor": ["utils.progress_tracker"], "database.etl.error_handler": ["utils.progress_tracker"], "database.etl.excel_processor": ["utils.progress_tracker"], "database.etl.json_processor": ["utils.progress_tracker"], "database.etl.pipeline": ["utils.data_cleaner"], "database.etl.transformer": ["utils.progress_tracker"], "database.etl.validator": ["utils.progress_tracker"], "database.etl.processors.csv_processor": ["utils.validators"], "database.geospatial.polygon_handler": ["utils.validators", "utils.performance"], "database.geospatial.processor": ["utils.validators", "utils.performance"], "database.geospatial.vendor_tagger": ["utils.batch_processor", "utils.performance"], "database.operations.bulk_operations": ["src.database.connection", "src.database.schema"], "database.operations.crud": ["utils.validators", "utils.security"], "database.operations.database_manager": ["utils.validators"], "database.operations.importer": ["utils.validators", "utils.security"], "database.operations.table_operations": ["utils.validators", "utils.security"], "database.operations.transaction_manager": ["src.database.connection"], "database.schema.manager": ["utils.validators"], "database.schema.models": ["utils.validators"], "database.schema.router": ["utils.validators"], "database.schema.validators": ["utils.validators"], "database.utils.batch_processor": ["utils.validators", "utils.security"], "importers.cdr_importer": ["src.database.operations", "src.database.schema"], "importers.ep_importer": ["src.database.operations", "src.database.schema"], "importers.nlg_importer": ["src.database.operations", "src.database.schema"], "utils": ["database.utils"]}}