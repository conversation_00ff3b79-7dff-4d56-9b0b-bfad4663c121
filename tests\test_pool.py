"""Tests for database connection pool management.

This module contains comprehensive tests for the DatabasePoolManager class,
including pool initialization, connection acquisition/release, wrapper methods,
and error handling scenarios.
"""

import asyncio
import os
from typing import Any, List
from unittest.mock import AsyncMock, MagicMock, Mock, patch

import asyncpg
import pytest
from asyncpg import Connection, Pool, Record

from src.config import get_config
from src.database.connection.pool import (
    DatabasePoolManager,
    close_global_pool,
    get_pool_connection,
    get_pool_manager,
    initialize_global_pool,
)
from src.database.exceptions import (
    ConfigurationError,
    ConnectionError,
    DatabaseError,
    TimeoutError,
)


@pytest.fixture
def mock_config():
    """Create a mock configuration for testing."""
    config = Mock(spec=Config)
    config.database = Mock(spec=DatabaseConfig)
    config.database.host = "localhost"
    config.database.port = 5432
    config.database.name = "test_db"
    config.database.user = "test_user"
    config.database.password = os.getenv(
        "TEST_DB_PASSWORD", "secure_test_password_123!"
    )

    config.pool = Mock(spec=PoolConfig)
    config.pool.size = 5
    config.pool.max_overflow = 10
    config.pool.timeout = 30
    config.pool.recycle = 3600

    return config


@pytest.fixture
def mock_pool():
    """Create a mock asyncpg pool."""
    pool = AsyncMock(spec=Pool)
    pool.get_size.return_value = 5
    pool.get_idle_size.return_value = 3
    return pool


@pytest.fixture
def mock_connection():
    """Create a mock asyncpg connection."""
    connection = AsyncMock(spec=Connection)
    connection.is_closed.return_value = False
    return connection


@pytest.fixture
def pool_manager(mock_config):
    """Create a DatabasePoolManager instance for testing."""
    return DatabasePoolManager(mock_config)


class TestDatabasePoolManager:
    """Test cases for DatabasePoolManager class."""

    def test_init(self, mock_config):
        """Test pool manager initialization."""
        manager = DatabasePoolManager(mock_config)

        assert manager.config == mock_config
        assert manager._pool is None
        assert not manager._is_initialized
        assert manager._stats["total_connections_created"] == 0
        assert manager._stats["pool_acquisitions"] == 0

    def test_init_with_default_config(self):
        """Test pool manager initialization with default config."""
        with patch("src.database.config.get_config") as mock_get_config:
            mock_config_instance = Mock()
            mock_get_config.return_value = mock_config_instance

            manager = DatabasePoolManager()

            mock_get_config.assert_called_once()
            assert manager.config == mock_config_instance

    @pytest.mark.asyncio
    async def test_initialize_pool_success(self, pool_manager, mock_pool):
        """Test successful pool initialization."""
        with patch("asyncpg.create_pool", new_callable=AsyncMock) as mock_create_pool:
            mock_create_pool.return_value = mock_pool

            await pool_manager.initialize_pool()

            assert pool_manager._is_initialized
            assert pool_manager._pool == mock_pool

            mock_create_pool.assert_called_once_with(
                min_size=5,
                max_size=10,
                command_timeout=30,
                max_inactive_connection_lifetime=3600,
                host="localhost",
                port=5432,
                database="test_db",
                user="test_user",
                password=os.getenv("TEST_DB_PASSWORD", "secure_test_password_123!"),
                server_settings={"application_name": "connect_database_framework_pool"},
            )

    @pytest.mark.asyncio
    async def test_initialize_pool_already_initialized(self, pool_manager, mock_pool):
        """Test pool initialization when already initialized."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool

        with patch("asyncpg.create_pool", new_callable=AsyncMock) as mock_create_pool:
            await pool_manager.initialize_pool()

            # Should not create a new pool
            mock_create_pool.assert_not_called()

    @pytest.mark.asyncio
    async def test_initialize_pool_timeout(self, pool_manager):
        """Test pool initialization timeout."""
        with patch("asyncpg.create_pool", new_callable=AsyncMock) as mock_create_pool:
            mock_create_pool.side_effect = asyncio.TimeoutError()

            with pytest.raises(TimeoutError) as exc_info:
                await pool_manager.initialize_pool()

            assert exc_info.value.error_code == "DB_POOL_INIT_TIMEOUT"
            assert not pool_manager._is_initialized

    @pytest.mark.asyncio
    async def test_initialize_pool_invalid_database(self, pool_manager):
        """Test pool initialization with invalid database."""
        with patch("asyncpg.create_pool", new_callable=AsyncMock) as mock_create_pool:
            mock_create_pool.side_effect = asyncpg.InvalidCatalogNameError(
                "Database does not exist"
            )

            with pytest.raises(ConfigurationError) as exc_info:
                await pool_manager.initialize_pool()

            assert exc_info.value.error_code == "DB_INVALID_DATABASE"
            assert not pool_manager._is_initialized

    @pytest.mark.asyncio
    async def test_initialize_pool_invalid_credentials(self, pool_manager):
        """Test pool initialization with invalid credentials."""
        with patch("asyncpg.create_pool", new_callable=AsyncMock) as mock_create_pool:
            mock_create_pool.side_effect = asyncpg.InvalidPasswordError(
                "Invalid password"
            )

            with pytest.raises(ConnectionError) as exc_info:
                await pool_manager.initialize_pool()

            assert exc_info.value.error_code == "DB_INVALID_CREDENTIALS"
            assert not pool_manager._is_initialized

    @pytest.mark.asyncio
    async def test_close_pool_success(self, pool_manager, mock_pool):
        """Test successful pool closure."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool

        await pool_manager.close_pool()

        mock_pool.close.assert_called_once()
        assert not pool_manager._is_initialized
        assert pool_manager._pool is None

    @pytest.mark.asyncio
    async def test_close_pool_not_initialized(self, pool_manager):
        """Test closing pool when not initialized."""
        await pool_manager.close_pool()

        # Should not raise any exception
        assert not pool_manager._is_initialized
        assert pool_manager._pool is None

    @pytest.mark.asyncio
    async def test_close_pool_error(self, pool_manager, mock_pool):
        """Test pool closure error."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.close.side_effect = Exception("Close error")

        with pytest.raises(ConnectionError) as exc_info:
            await pool_manager.close_pool()

        assert exc_info.value.error_code == "DB_POOL_CLOSE_FAILED"

    @pytest.mark.asyncio
    async def test_acquire_connection_success(
        self, pool_manager, mock_pool, mock_connection
    ):
        """Test successful connection acquisition."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(return_value=mock_connection)

        connection = await pool_manager.acquire_connection()

        assert connection == mock_connection
        assert pool_manager._stats["pool_acquisitions"] == 1
        assert pool_manager._stats["active_connections"] == 1
        mock_pool.acquire.assert_called_once()

    @pytest.mark.asyncio
    async def test_acquire_connection_not_initialized(self, pool_manager):
        """Test connection acquisition when pool not initialized."""
        with pytest.raises(RuntimeError) as exc_info:
            await pool_manager.acquire_connection()

        assert "Pool is not initialized" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_acquire_connection_timeout(self, pool_manager, mock_pool):
        """Test connection acquisition timeout."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(side_effect=asyncio.TimeoutError())

        with pytest.raises(TimeoutError) as exc_info:
            await pool_manager.acquire_connection()

        assert exc_info.value.error_code == "DB_CONNECTION_ACQUIRE_TIMEOUT"

    @pytest.mark.asyncio
    async def test_release_connection_success(
        self, pool_manager, mock_pool, mock_connection
    ):
        """Test successful connection release."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        pool_manager._stats["active_connections"] = 1

        await pool_manager.release_connection(mock_connection)

        mock_pool.release.assert_called_once_with(mock_connection)
        assert pool_manager._stats["pool_releases"] == 1
        assert pool_manager._stats["active_connections"] == 0

    @pytest.mark.asyncio
    async def test_release_connection_not_initialized(
        self, pool_manager, mock_connection
    ):
        """Test connection release when pool not initialized."""
        with pytest.raises(RuntimeError) as exc_info:
            await pool_manager.release_connection(mock_connection)

        assert "Pool is not initialized" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_release_connection_none(self, pool_manager, mock_pool):
        """Test releasing None connection."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool

        await pool_manager.release_connection(None)

        mock_pool.release.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_connection_context_manager(
        self, pool_manager, mock_pool, mock_connection
    ):
        """Test connection context manager."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(return_value=mock_connection)
        mock_pool.release = AsyncMock()

        async with pool_manager.get_connection() as conn:
            assert conn == mock_connection
            mock_pool.acquire.assert_called_once()

        mock_pool.release.assert_called_once_with(mock_connection)

    @pytest.mark.asyncio
    async def test_execute_success(self, pool_manager, mock_pool, mock_connection):
        """Test successful query execution."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(return_value=mock_connection)
        mock_pool.release = AsyncMock()
        mock_connection.execute = AsyncMock(return_value="SELECT 1")

        result = await pool_manager.execute("SELECT 1")

        assert result == "SELECT 1"
        mock_connection.execute.assert_called_once_with("SELECT 1")
        mock_pool.acquire.assert_called_once()
        mock_pool.release.assert_called_once_with(mock_connection)

    @pytest.mark.asyncio
    async def test_execute_error(self, pool_manager, mock_pool, mock_connection):
        """Test query execution error."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(return_value=mock_connection)
        mock_pool.release = AsyncMock()
        mock_connection.execute = AsyncMock(side_effect=Exception("Query error"))

        with pytest.raises(DatabaseError) as exc_info:
            await pool_manager.execute("SELECT 1")

        assert exc_info.value.error_code == "DB_QUERY_EXECUTION_FAILED"

    @pytest.mark.asyncio
    async def test_fetchval_success(self, pool_manager, mock_pool, mock_connection):
        """Test successful fetchval operation."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(return_value=mock_connection)
        mock_pool.release = AsyncMock()
        mock_connection.fetchval = AsyncMock(return_value=42)

        result = await pool_manager.fetchval("SELECT COUNT(*) FROM table")

        assert result == 42
        mock_connection.fetchval.assert_called_once_with("SELECT COUNT(*) FROM table")

    @pytest.mark.asyncio
    async def test_fetchrow_success(self, pool_manager, mock_pool, mock_connection):
        """Test successful fetchrow operation."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(return_value=mock_connection)
        mock_pool.release = AsyncMock()
        mock_record = Mock(spec=Record)
        mock_connection.fetchrow = AsyncMock(return_value=mock_record)

        result = await pool_manager.fetchrow("SELECT * FROM table LIMIT 1")

        assert result == mock_record
        mock_connection.fetchrow.assert_called_once_with("SELECT * FROM table LIMIT 1")

    @pytest.mark.asyncio
    async def test_fetch_success(self, pool_manager, mock_pool, mock_connection):
        """Test successful fetch operation."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(return_value=mock_connection)
        mock_pool.release = AsyncMock()
        mock_records = [Mock(spec=Record), Mock(spec=Record)]
        mock_connection.fetch = AsyncMock(return_value=mock_records)

        result = await pool_manager.fetch("SELECT * FROM table")

        assert result == mock_records
        assert len(result) == 2
        mock_connection.fetch.assert_called_once_with("SELECT * FROM table")

    @pytest.mark.asyncio
    async def test_executemany_success(self, pool_manager, mock_pool, mock_connection):
        """Test successful executemany operation."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(return_value=mock_connection)
        mock_pool.release = AsyncMock()
        mock_connection.executemany = AsyncMock()

        args_list = [(1, "test1"), (2, "test2")]
        await pool_manager.executemany("INSERT INTO table VALUES ($1, $2)", args_list)

        mock_connection.executemany.assert_called_once_with(
            "INSERT INTO table VALUES ($1, $2)", args_list
        )

    def test_get_pool_stats(self, pool_manager, mock_pool):
        """Test getting pool statistics."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        pool_manager._stats["pool_acquisitions"] = 5
        pool_manager._stats["pool_releases"] = 3

        stats = pool_manager.get_pool_stats()

        assert stats["pool_acquisitions"] == 5
        assert stats["pool_releases"] == 3
        assert stats["pool_size"] == 5
        assert stats["pool_idle"] == 3
        assert stats["pool_active"] == 2
        assert stats["is_initialized"] is True

    def test_get_pool_stats_not_initialized(self, pool_manager):
        """Test getting pool statistics when not initialized."""
        stats = pool_manager.get_pool_stats()

        assert stats["pool_size"] == 0
        assert stats["pool_idle"] == 0
        assert stats["pool_active"] == 0
        assert stats["is_initialized"] is False

    def test_is_initialized(self, pool_manager, mock_pool):
        """Test is_initialized method."""
        assert not pool_manager.is_initialized()

        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool

        assert pool_manager.is_initialized()

    def test_build_connection_params(self, pool_manager):
        """Test building connection parameters."""
        params = pool_manager._build_connection_params()

        expected_params = {
            "host": "localhost",
            "port": 5432,
            "database": "test_db",
            "user": "test_user",
            "password": os.getenv("TEST_DB_PASSWORD", "secure_test_password_123!"),
            "server_settings": {"application_name": "connect_database_framework_pool"},
        }

        assert params == expected_params

    def test_build_connection_params_missing_config(self):
        """Test building connection parameters with missing config."""
        config = Mock()
        del config.database  # Remove database attribute

        manager = DatabasePoolManager(config)

        with pytest.raises(ConfigurationError) as exc_info:
            manager._build_connection_params()

        assert exc_info.value.error_code == "DB_CONFIG_MISSING"


class TestGlobalPoolFunctions:
    """Test cases for global pool management functions."""

    def test_get_pool_manager_new_instance(self, mock_config):
        """Test getting new pool manager instance."""
        # Clear global instance
        import src.database.connection.pool as pool_module

        pool_module._pool_manager = None

        manager = get_pool_manager(mock_config)

        assert isinstance(manager, DatabasePoolManager)
        assert manager.config == mock_config

    def test_get_pool_manager_existing_instance(self, mock_config):
        """Test getting existing pool manager instance."""
        # Set up existing instance
        import src.database.connection.pool as pool_module

        existing_manager = DatabasePoolManager(mock_config)
        pool_module._pool_manager = existing_manager

        manager = get_pool_manager()

        assert manager == existing_manager

    @pytest.mark.asyncio
    async def test_initialize_global_pool(self, mock_config):
        """Test initializing global pool."""
        with patch("src.database.connection.pool.get_pool_manager") as mock_get_manager:
            mock_manager = AsyncMock()
            mock_get_manager.return_value = mock_manager

            await initialize_global_pool(mock_config)

            mock_get_manager.assert_called_once_with(mock_config)
            mock_manager.initialize_pool.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_global_pool(self):
        """Test closing global pool."""
        import src.database.connection.pool as pool_module

        mock_manager = AsyncMock()
        pool_module._pool_manager = mock_manager

        await close_global_pool()

        mock_manager.close_pool.assert_called_once()

    @pytest.mark.asyncio
    async def test_close_global_pool_no_manager(self):
        """Test closing global pool when no manager exists."""
        import src.database.connection.pool as pool_module

        pool_module._pool_manager = None

        # Should not raise any exception
        await close_global_pool()

    @pytest.mark.asyncio
    async def test_get_pool_connection(self, mock_connection):
        """Test getting connection from global pool."""
        with patch("src.database.connection.pool.get_pool_manager") as mock_get_manager:
            mock_manager = Mock()

            # Create a proper async context manager mock
            class MockAsyncContextManager:
                async def __aenter__(self):
                    return mock_connection

                async def __aexit__(self, exc_type, exc_val, exc_tb):
                    pass

            mock_manager.get_connection.return_value = MockAsyncContextManager()
            mock_get_manager.return_value = mock_manager

            async with get_pool_connection() as conn:
                assert conn == mock_connection


class TestPoolStressScenarios:
    """Test cases for stress scenarios and edge cases."""

    @pytest.mark.asyncio
    async def test_concurrent_connection_acquisition(self, pool_manager, mock_pool):
        """Test concurrent connection acquisition."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool

        # Create multiple mock connections
        connections = [AsyncMock(spec=Connection) for _ in range(10)]
        mock_pool.acquire = AsyncMock(side_effect=connections)

        # Simulate concurrent acquisition
        tasks = [pool_manager.acquire_connection() for _ in range(10)]
        results = await asyncio.gather(*tasks)

        assert len(results) == 10
        assert pool_manager._stats["pool_acquisitions"] == 10
        assert pool_manager._stats["active_connections"] == 10

    @pytest.mark.asyncio
    async def test_connection_leak_prevention(
        self, pool_manager, mock_pool, mock_connection
    ):
        """Test that connections are properly released even on exceptions."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(return_value=mock_connection)
        mock_pool.release = AsyncMock()
        mock_connection.execute = AsyncMock(side_effect=Exception("Query error"))

        with pytest.raises(DatabaseError):
            await pool_manager.execute("SELECT 1")

        # Connection should still be released
        mock_pool.release.assert_called_once_with(mock_connection)

    @pytest.mark.asyncio
    async def test_pool_size_limits(self, pool_manager, mock_pool):
        """Test pool size limits and behavior."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool

        # Simulate pool at max capacity
        mock_pool.acquire = AsyncMock(side_effect=asyncio.TimeoutError())

        with pytest.raises(TimeoutError):
            await pool_manager.acquire_connection()

    @pytest.mark.asyncio
    async def test_connection_recycling_behavior(
        self, pool_manager, mock_pool, mock_connection
    ):
        """Test connection recycling behavior."""
        pool_manager._is_initialized = True
        pool_manager._pool = mock_pool
        mock_pool.acquire = AsyncMock(return_value=mock_connection)
        mock_pool.release = AsyncMock()

        # Acquire and release connection multiple times
        for _ in range(5):
            async with pool_manager.get_connection() as conn:
                assert conn == mock_connection

        # Verify proper acquisition and release calls
        assert mock_pool.acquire.call_count == 5
        assert mock_pool.release.call_count == 5
