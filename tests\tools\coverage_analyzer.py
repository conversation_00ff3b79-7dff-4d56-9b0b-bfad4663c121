"""Coverage analysis and reporting tools."""

import ast
import os
import re
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass, field
import xml.etree.ElementTree as ET
import json


@dataclass
class FileCoverage:
    """Coverage information for a single file."""
    filepath: str
    total_lines: int = 0
    covered_lines: int = 0
    missing_lines: List[int] = field(default_factory=list)
    excluded_lines: List[int] = field(default_factory=list)
    partial_lines: List[int] = field(default_factory=list)
    
    @property
    def coverage_percentage(self) -> float:
        """Calculate coverage percentage."""
        if self.total_lines == 0:
            return 0.0
        return (self.covered_lines / self.total_lines) * 100
    
    @property
    def is_fully_covered(self) -> bool:
        """Check if file is fully covered."""
        return len(self.missing_lines) == 0


@dataclass
class ModuleCoverage:
    """Coverage information for a module/package."""
    name: str
    files: Dict[str, FileCoverage] = field(default_factory=dict)
    
    @property
    def total_lines(self) -> int:
        """Total lines in module."""
        return sum(f.total_lines for f in self.files.values())
    
    @property
    def covered_lines(self) -> int:
        """Covered lines in module."""
        return sum(f.covered_lines for f in self.files.values())
    
    @property
    def coverage_percentage(self) -> float:
        """Module coverage percentage."""
        if self.total_lines == 0:
            return 0.0
        return (self.covered_lines / self.total_lines) * 100


@dataclass
class CoverageReport:
    """Complete coverage report."""
    modules: Dict[str, ModuleCoverage] = field(default_factory=dict)
    total_files: int = 0
    timestamp: str = ""
    
    @property
    def total_lines(self) -> int:
        """Total lines across all modules."""
        return sum(m.total_lines for m in self.modules.values())
    
    @property
    def covered_lines(self) -> int:
        """Covered lines across all modules."""
        return sum(m.covered_lines for m in self.modules.values())
    
    @property
    def overall_coverage(self) -> float:
        """Overall coverage percentage."""
        if self.total_lines == 0:
            return 0.0
        return (self.covered_lines / self.total_lines) * 100


class CoverageAnalyzer:
    """Analyze and report code coverage."""
    
    def __init__(self, source_dir: str = "src", test_dir: str = "tests"):
        """Initialize coverage analyzer.
        
        Args:
            source_dir: Source code directory
            test_dir: Test directory
        """
        self.source_dir = Path(source_dir)
        self.test_dir = Path(test_dir)
        self.coverage_data: Optional[CoverageReport] = None
        
    def run_coverage(self, 
                    test_path: Optional[str] = None,
                    include_patterns: Optional[List[str]] = None,
                    exclude_patterns: Optional[List[str]] = None,
                    min_coverage: Optional[float] = None) -> subprocess.CompletedProcess:
        """Run coverage analysis.
        
        Args:
            test_path: Specific test path to run
            include_patterns: Patterns to include in coverage
            exclude_patterns: Patterns to exclude from coverage
            min_coverage: Minimum coverage threshold
            
        Returns:
            CompletedProcess result
        """
        cmd = ["python", "-m", "pytest"]
        
        # Add test path
        if test_path:
            cmd.append(test_path)
        else:
            cmd.append(str(self.test_dir))
            
        # Add coverage options
        cmd.extend([
            "--cov", str(self.source_dir),
            "--cov-report", "xml",
            "--cov-report", "html",
            "--cov-report", "term-missing",
            "--cov-report", "json"
        ])
        
        # Add include patterns
        if include_patterns:
            for pattern in include_patterns:
                cmd.extend(["--cov", pattern])
                
        # Add exclude patterns
        if exclude_patterns:
            for pattern in exclude_patterns:
                cmd.extend(["--cov-config", f"exclude_lines = {pattern}"])
                
        # Add minimum coverage
        if min_coverage is not None:
            cmd.extend(["--cov-fail-under", str(min_coverage)])
            
        return subprocess.run(cmd, capture_output=True, text=True)
        
    def parse_coverage_xml(self, xml_file: str = "coverage.xml") -> CoverageReport:
        """Parse coverage XML file.
        
        Args:
            xml_file: Path to coverage XML file
            
        Returns:
            CoverageReport object
        """
        if not os.path.exists(xml_file):
            raise FileNotFoundError(f"Coverage XML file not found: {xml_file}")
            
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        report = CoverageReport()
        report.timestamp = root.get('timestamp', '')
        
        # Parse packages and classes
        for package in root.findall('.//package'):
            package_name = package.get('name', 'unknown')
            module = ModuleCoverage(name=package_name)
            
            for class_elem in package.findall('classes/class'):
                filename = class_elem.get('filename', '')
                
                # Parse line coverage
                lines_elem = class_elem.find('lines')
                if lines_elem is not None:
                    file_cov = FileCoverage(filepath=filename)
                    
                    covered_lines = []
                    missing_lines = []
                    partial_lines = []
                    
                    for line in lines_elem.findall('line'):
                        line_num = int(line.get('number', 0))
                        hits = int(line.get('hits', 0))
                        branch = line.get('branch', 'false') == 'true'
                        
                        if hits > 0:
                            covered_lines.append(line_num)
                        else:
                            missing_lines.append(line_num)
                            
                        # Handle partial coverage (branches)
                        if branch:
                            condition_coverage = line.get('condition-coverage', '')
                            if condition_coverage and '100%' not in condition_coverage:
                                partial_lines.append(line_num)
                                
                    file_cov.total_lines = len(covered_lines) + len(missing_lines)
                    file_cov.covered_lines = len(covered_lines)
                    file_cov.missing_lines = missing_lines
                    file_cov.partial_lines = partial_lines
                    
                    module.files[filename] = file_cov
                    
            report.modules[package_name] = module
            
        return report
        
    def parse_coverage_json(self, json_file: str = "coverage.json") -> CoverageReport:
        """Parse coverage JSON file.
        
        Args:
            json_file: Path to coverage JSON file
            
        Returns:
            CoverageReport object
        """
        if not os.path.exists(json_file):
            raise FileNotFoundError(f"Coverage JSON file not found: {json_file}")
            
        with open(json_file, 'r') as f:
            data = json.load(f)
            
        report = CoverageReport()
        report.timestamp = data.get('meta', {}).get('timestamp', '')
        
        files_data = data.get('files', {})
        
        # Group files by module
        modules = {}
        for filepath, file_data in files_data.items():
            # Extract module name from filepath
            path_parts = Path(filepath).parts
            if len(path_parts) > 1:
                module_name = path_parts[0]
            else:
                module_name = 'root'
                
            if module_name not in modules:
                modules[module_name] = ModuleCoverage(name=module_name)
                
            # Parse file coverage
            file_cov = FileCoverage(filepath=filepath)
            
            summary = file_data.get('summary', {})
            file_cov.total_lines = summary.get('num_statements', 0)
            file_cov.covered_lines = summary.get('covered_lines', 0)
            
            # Parse missing lines
            missing_lines = file_data.get('missing_lines', [])
            file_cov.missing_lines = missing_lines
            
            # Parse excluded lines
            excluded_lines = file_data.get('excluded_lines', [])
            file_cov.excluded_lines = excluded_lines
            
            modules[module_name].files[filepath] = file_cov
            
        report.modules = modules
        return report
        
    def analyze_uncovered_code(self, report: CoverageReport) -> Dict[str, List[Dict]]:
        """Analyze uncovered code to identify patterns.
        
        Args:
            report: CoverageReport to analyze
            
        Returns:
            Dictionary with analysis results
        """
        analysis = {
            'critical_files': [],
            'low_coverage_modules': [],
            'uncovered_functions': [],
            'uncovered_classes': []
        }
        
        for module_name, module in report.modules.items():
            # Check for low coverage modules
            if module.coverage_percentage < 80:
                analysis['low_coverage_modules'].append({
                    'name': module_name,
                    'coverage': module.coverage_percentage,
                    'total_lines': module.total_lines,
                    'missing_lines': module.total_lines - module.covered_lines
                })
                
            for filepath, file_cov in module.files.items():
                # Check for critical files with low coverage
                if file_cov.coverage_percentage < 70 and file_cov.total_lines > 50:
                    analysis['critical_files'].append({
                        'filepath': filepath,
                        'coverage': file_cov.coverage_percentage,
                        'total_lines': file_cov.total_lines,
                        'missing_lines': len(file_cov.missing_lines)
                    })
                    
                # Analyze uncovered functions and classes
                if os.path.exists(filepath):
                    uncovered_items = self._analyze_file_ast(filepath, file_cov.missing_lines)
                    analysis['uncovered_functions'].extend(uncovered_items['functions'])
                    analysis['uncovered_classes'].extend(uncovered_items['classes'])
                    
        return analysis
        
    def _analyze_file_ast(self, filepath: str, missing_lines: List[int]) -> Dict[str, List[Dict]]:
        """Analyze file AST to identify uncovered functions and classes.
        
        Args:
            filepath: Path to Python file
            missing_lines: List of uncovered line numbers
            
        Returns:
            Dictionary with uncovered functions and classes
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
                
            tree = ast.parse(content)
            
            uncovered_functions = []
            uncovered_classes = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    if node.lineno in missing_lines:
                        uncovered_functions.append({
                            'name': node.name,
                            'filepath': filepath,
                            'line': node.lineno,
                            'type': 'function'
                        })
                        
                elif isinstance(node, ast.ClassDef):
                    if node.lineno in missing_lines:
                        uncovered_classes.append({
                            'name': node.name,
                            'filepath': filepath,
                            'line': node.lineno,
                            'type': 'class'
                        })
                        
            return {
                'functions': uncovered_functions,
                'classes': uncovered_classes
            }
            
        except Exception as e:
            print(f"Error analyzing {filepath}: {e}")
            return {'functions': [], 'classes': []}
            
    def generate_coverage_suggestions(self, report: CoverageReport) -> List[Dict[str, str]]:
        """Generate suggestions for improving coverage.
        
        Args:
            report: CoverageReport to analyze
            
        Returns:
            List of suggestions
        """
        suggestions = []
        analysis = self.analyze_uncovered_code(report)
        
        # Suggestions for low coverage modules
        for module in analysis['low_coverage_modules']:
            suggestions.append({
                'type': 'module',
                'priority': 'high' if module['coverage'] < 50 else 'medium',
                'message': f"Module '{module['name']}' has low coverage ({module['coverage']:.1f}%). "
                          f"Consider adding tests for {module['missing_lines']} uncovered lines.",
                'target': module['name']
            })
            
        # Suggestions for critical files
        for file_info in analysis['critical_files']:
            suggestions.append({
                'type': 'file',
                'priority': 'high',
                'message': f"Critical file '{file_info['filepath']}' has low coverage "
                          f"({file_info['coverage']:.1f}%). This file has {file_info['total_lines']} lines "
                          f"with {file_info['missing_lines']} uncovered.",
                'target': file_info['filepath']
            })
            
        # Suggestions for uncovered functions
        function_files = {}
        for func in analysis['uncovered_functions']:
            filepath = func['filepath']
            if filepath not in function_files:
                function_files[filepath] = []
            function_files[filepath].append(func['name'])
            
        for filepath, functions in function_files.items():
            suggestions.append({
                'type': 'function',
                'priority': 'medium',
                'message': f"File '{filepath}' has {len(functions)} uncovered functions: "
                          f"{', '.join(functions[:5])}{'...' if len(functions) > 5 else ''}",
                'target': filepath
            })
            
        # Overall coverage suggestion
        if report.overall_coverage < 80:
            suggestions.append({
                'type': 'overall',
                'priority': 'high',
                'message': f"Overall coverage is {report.overall_coverage:.1f}%, below the 80% target. "
                          f"Focus on testing the most critical uncovered code first.",
                'target': 'project'
            })
            
        return suggestions
        
    def generate_test_file_suggestions(self, report: CoverageReport) -> Dict[str, str]:
        """Generate suggestions for test files to create.
        
        Args:
            report: CoverageReport to analyze
            
        Returns:
            Dictionary mapping source files to suggested test files
        """
        suggestions = {}
        
        for module_name, module in report.modules.items():
            for filepath, file_cov in module.files.items():
                if file_cov.coverage_percentage < 80:
                    # Convert source file path to test file path
                    path_obj = Path(filepath)
                    
                    # Remove 'src' prefix if present
                    if path_obj.parts[0] == 'src':
                        relative_path = Path(*path_obj.parts[1:])
                    else:
                        relative_path = path_obj
                        
                    # Create test file path
                    test_file = self.test_dir / "unit" / relative_path.parent / f"test_{path_obj.stem}.py"
                    
                    suggestions[filepath] = str(test_file)
                    
        return suggestions
        
    def export_coverage_report(self, 
                             report: CoverageReport, 
                             output_file: str = "coverage_analysis.json") -> str:
        """Export detailed coverage report.
        
        Args:
            report: CoverageReport to export
            output_file: Output file path
            
        Returns:
            Path to exported file
        """
        analysis = self.analyze_uncovered_code(report)
        suggestions = self.generate_coverage_suggestions(report)
        test_suggestions = self.generate_test_file_suggestions(report)
        
        export_data = {
            'summary': {
                'overall_coverage': report.overall_coverage,
                'total_lines': report.total_lines,
                'covered_lines': report.covered_lines,
                'total_files': len([f for m in report.modules.values() for f in m.files]),
                'timestamp': report.timestamp
            },
            'modules': {
                name: {
                    'coverage': module.coverage_percentage,
                    'total_lines': module.total_lines,
                    'covered_lines': module.covered_lines,
                    'files': {
                        filepath: {
                            'coverage': file_cov.coverage_percentage,
                            'total_lines': file_cov.total_lines,
                            'covered_lines': file_cov.covered_lines,
                            'missing_lines': file_cov.missing_lines,
                            'partial_lines': file_cov.partial_lines
                        }
                        for filepath, file_cov in module.files.items()
                    }
                }
                for name, module in report.modules.items()
            },
            'analysis': analysis,
            'suggestions': suggestions,
            'test_file_suggestions': test_suggestions
        }
        
        with open(output_file, 'w') as f:
            json.dump(export_data, f, indent=2)
            
        return output_file
        
    def check_coverage_thresholds(self, 
                                report: CoverageReport,
                                overall_threshold: float = 80.0,
                                file_threshold: float = 70.0) -> Dict[str, bool]:
        """Check if coverage meets specified thresholds.
        
        Args:
            report: CoverageReport to check
            overall_threshold: Overall coverage threshold
            file_threshold: Individual file coverage threshold
            
        Returns:
            Dictionary with threshold check results
        """
        results = {
            'overall_pass': report.overall_coverage >= overall_threshold,
            'overall_coverage': report.overall_coverage,
            'overall_threshold': overall_threshold,
            'failing_files': []
        }
        
        for module_name, module in report.modules.items():
            for filepath, file_cov in module.files.items():
                if file_cov.coverage_percentage < file_threshold:
                    results['failing_files'].append({
                        'filepath': filepath,
                        'coverage': file_cov.coverage_percentage,
                        'threshold': file_threshold
                    })
                    
        results['file_pass'] = len(results['failing_files']) == 0
        
        return results
        
    def load_coverage_data(self, 
                          xml_file: Optional[str] = None,
                          json_file: Optional[str] = None) -> CoverageReport:
        """Load coverage data from files.
        
        Args:
            xml_file: Path to coverage XML file
            json_file: Path to coverage JSON file
            
        Returns:
            CoverageReport object
        """
        if json_file and os.path.exists(json_file):
            self.coverage_data = self.parse_coverage_json(json_file)
        elif xml_file and os.path.exists(xml_file):
            self.coverage_data = self.parse_coverage_xml(xml_file)
        else:
            # Try default locations
            if os.path.exists('coverage.json'):
                self.coverage_data = self.parse_coverage_json('coverage.json')
            elif os.path.exists('coverage.xml'):
                self.coverage_data = self.parse_coverage_xml('coverage.xml')
            else:
                raise FileNotFoundError("No coverage data files found")
                
        return self.coverage_data