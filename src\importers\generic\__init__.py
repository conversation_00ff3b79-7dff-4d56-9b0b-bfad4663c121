"""Generic data format importers.

This module provides importers for common data formats that are not specific
to telecommunications data. These importers can be used for general data
processing tasks and provide a foundation for custom data import workflows.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

from .csv_importer import CSVImporter, CSVConfig
from .excel_importer import ExcelImporter, ExcelConfig
from .json_importer import JSONImporter, JSONConfig

# Module metadata
__version__ = "1.0.0"
__description__ = "Generic data format importers for Connect system"

# Supported formats
SUPPORTED_FORMATS = {
    'csv': {
        'extensions': ['.csv', '.tsv', '.txt'],
        'importer': CSVImporter,
        'config': CSVConfig,
        'description': 'Comma-separated values and text files'
    },
    'excel': {
        'extensions': ['.xlsx', '.xls', '.xlsm'],
        'importer': ExcelImporter,
        'config': ExcelConfig,
        'description': 'Microsoft Excel files'
    },
    'json': {
        'extensions': ['.json', '.jsonl', '.ndjson'],
        'importer': JSONImporter,
        'config': JSONConfig,
        'description': 'JSON and line-delimited JSON files'
    }
}

# Default configurations
DEFAULT_CONFIGS = {
    'csv': {
        'delimiter': ',',
        'encoding': 'utf-8',
        'chunk_size': 10000,
        'auto_detect_types': True,
        'skip_blank_lines': True,
        'strip_whitespace': True
    },
    'excel': {
        'sheet_name': 0,
        'header': 0,
        'chunk_size': 10000,
        'auto_detect_types': True,
        'skip_blank_lines': True,
        'strip_whitespace': True
    },
    'json': {
        'flatten_nested': True,
        'array_handling': 'expand',
        'auto_detect_types': True,
        'normalize_column_names': True,
        'streaming': False,
        'chunk_size': 10000
    }
}


def create_importer(format_type: str, config: dict = None, **kwargs):
    """Create a generic importer instance.
    
    Args:
        format_type: Type of format ('csv', 'excel', 'json')
        config: Configuration dictionary
        **kwargs: Additional configuration parameters
        
    Returns:
        Configured importer instance
        
    Raises:
        ValueError: If format type is not supported
    """
    if format_type not in SUPPORTED_FORMATS:
        raise ValueError(f"Unsupported format: {format_type}. Supported: {list(SUPPORTED_FORMATS.keys())}")
        
    format_info = SUPPORTED_FORMATS[format_type]
    importer_class = format_info['importer']
    config_class = format_info['config']
    
    # Merge default config with provided config
    final_config = DEFAULT_CONFIGS[format_type].copy()
    if config:
        final_config.update(config)
    final_config.update(kwargs)
    
    # Create format-specific config
    format_config_key = f'{format_type}_config'
    importer_config = {
        format_config_key: config_class(**final_config)
    }
    
    return importer_class(config=importer_config)


def get_supported_extensions():
    """Get all supported file extensions.
    
    Returns:
        List of supported file extensions
    """
    extensions = []
    for format_info in SUPPORTED_FORMATS.values():
        extensions.extend(format_info['extensions'])
    return sorted(set(extensions))


def detect_format_from_extension(file_path: str):
    """Detect format type from file extension.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Format type or None if not supported
    """
    from pathlib import Path
    
    extension = Path(file_path).suffix.lower()
    
    for format_type, format_info in SUPPORTED_FORMATS.items():
        if extension in format_info['extensions']:
            return format_type
            
    return None


def get_format_info(format_type: str):
    """Get information about a specific format.
    
    Args:
        format_type: Type of format
        
    Returns:
        Format information dictionary
    """
    return SUPPORTED_FORMATS.get(format_type)


def list_available_formats():
    """List all available format types with descriptions.
    
    Returns:
        Dictionary of format types and their descriptions
    """
    return {
        format_type: format_info['description']
        for format_type, format_info in SUPPORTED_FORMATS.items()
    }


# Export all classes and functions
__all__ = [
    # Importers
    'CSVImporter',
    'ExcelImporter', 
    'JSONImporter',
    
    # Configs
    'CSVConfig',
    'ExcelConfig',
    'JSONConfig',
    
    # Constants
    'SUPPORTED_FORMATS',
    'DEFAULT_CONFIGS',
    
    # Factory functions
    'create_importer',
    'get_supported_extensions',
    'detect_format_from_extension',
    'get_format_info',
    'list_available_formats'
]