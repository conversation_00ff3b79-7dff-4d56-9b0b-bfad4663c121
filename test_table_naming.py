#!/usr/bin/env python3
"""测试表命名逻辑"""

import sys
import os
import yaml
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database.utils.table_naming import TableNamingManager

def test_table_naming():
    """测试EP文件的表命名逻辑"""
    print("🔍 测试表命名逻辑...")
    
    # Load database.yaml configuration directly
    try:
        config_path = Path(__file__).parent / 'config' / 'database.yaml'
        with open(config_path, 'r', encoding='utf-8') as f:
            database_config = yaml.safe_load(f)
        print(f"Database config loaded successfully from: {config_path}")
        print(f"Config keys: {list(database_config.keys())}")
        
        # Check telecom_data_sources
        if 'telecom_data_sources' in database_config:
            telecom_config = database_config['telecom_data_sources']
            print(f"telecom_data_sources found with keys: {list(telecom_config.keys())}")
            if 'ep' in telecom_config:
                ep_config = telecom_config['ep']
                print(f"EP config: {ep_config}")
        else:
            print("telecom_data_sources not found in database config")
            return
            
    except Exception as e:
        print(f"Error loading database config: {e}")
        return
    
    # Initialize TableNamingManager with database config
    try:
        naming_manager = TableNamingManager(database_config)
        print(f"TableNamingManager initialized successfully")
    except Exception as e:
        print(f"Error initializing TableNamingManager: {e}")
        return
    
    # 测试文件路径
    test_files = [
        "D:/connect/data/input/ep/2025/CW03/GSMCELL_CW03.xlsx",
        "D:/connect/data/input/ep/2025/CW07/LTECELL_CW07.xlsx", 
        "D:/connect/data/input/ep/2025/CW10/NRCELL_CW10.xlsx",
        "D:/connect/data/input/ep/2024/CW48/TEF_SITE_CW48.xlsx",
        "D:/connect/data/input/ep/2024/CW01/TEF_Sites_CW01.xlsx",
        "GSMCELL_CW03.xlsx",  # 仅文件名
        "LTECELL_CW07.xlsx",  # 仅文件名
        "NRCELL_CW10.xlsx",   # 仅文件名
    ]
    
    print("\n📋 表命名测试结果:")
    for file_path in test_files:
        try:
            path_obj = Path(file_path)
            table_name = naming_manager.generate_table_name(
                data_type="ep",
                file_path=path_obj
            )
            print(f"  📁 {file_path}")
            print(f"  📊 → {table_name}")
            print()
        except Exception as e:
            print(f"  ❌ {file_path} → 错误: {e}")
            print()
    
    # 测试配置加载
    print("\n🔧 配置验证:")
    try:
        # 检查EP配置
        ep_config = database_config.get('telecom_data_sources', {}).get('ep', {})
        print(f"  📊 EP schema: {ep_config.get('schema_name', 'NOT_FOUND')}")
        print(f"  📊 表命名模式: {ep_config.get('table_name_pattern', 'NOT_FOUND')}")
        print(f"  📊 跳过行数: {ep_config.get('skip_rows', 'NOT_FOUND')}")
        print(f"  📊 标题行: {ep_config.get('header_row', 'NOT_FOUND')}")
        print(f"  📊 批处理大小: {ep_config.get('batch_processing', {}).get('batch_size', 'NOT_FOUND')}")
    except Exception as e:
        print(f"  ❌ 配置加载错误: {e}")

if __name__ == "__main__":
    test_table_naming()