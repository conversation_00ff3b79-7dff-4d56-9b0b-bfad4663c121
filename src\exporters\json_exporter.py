__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""JSON data exporter.

This module provides JSON export functionality for various data formats.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from .base import BaseExporter, ExportError, ExportResult

# Configure logging
logger = logging.getLogger(__name__)


class JSONExporter(BaseExporter):
    """JSON data exporter."""

    def __init__(
        self,
        output_path: Union[str, Path],
        indent: Optional[int] = 2,
        encoding: str = "utf-8",
        **kwargs,
    ):
        """Initialize JSON exporter.

        Args:
            output_path: Path where JSON file will be saved
            indent: Number of spaces for indentation (None for compact)
            encoding: File encoding
            **kwargs: Additional configuration options
        """
        super().__init__(output_path, **kwargs)
        self.indent = indent
        self.encoding = encoding

        # Ensure output file has .json extension
        if not self.output_path.suffix:
            self.output_path = self.output_path.with_suffix(".json")
        elif self.output_path.suffix.lower() != ".json":
            self.output_path = self.output_path.with_suffix(".json")

    async def export(self, data: Any, **kwargs) -> ExportResult:
        """Export data to JSON format.

        Args:
            data: Data to export (DataFrame, dict, list, or any JSON-serializable object)
            **kwargs: Additional export options
                - orient: DataFrame orientation for JSON conversion
                - date_format: Format for datetime objects

        Returns:
            ExportResult: Result of the export operation

        Raises:
            ExportError: If export fails
        """
        try:
            self.validate_data(data)
            self.prepare_output_directory()

            orient = kwargs.get("orient", "records")
            date_format = kwargs.get("date_format", "iso")

            # Convert data to JSON-serializable format
            json_data = self._prepare_data_for_json(data, orient, date_format)

            # Write JSON to file
            with open(self.output_path, "w", encoding=self.encoding) as f:
                json.dump(
                    json_data, f, indent=self.indent, ensure_ascii=False, default=str
                )

            # Calculate records exported
            records_exported = self._count_records(json_data)
            file_size = (
                self.output_path.stat().st_size if self.output_path.exists() else 0
            )

            logger.info(
                f"Successfully exported {records_exported} records to {self.output_path}"
            )

            return ExportResult(
                success=True,
                file_path=self.output_path,
                records_exported=records_exported,
                file_size_bytes=file_size,
                metadata={
                    "indent": self.indent,
                    "encoding": self.encoding,
                    "orient": orient,
                    "date_format": date_format,
                },
            )

        except Exception as e:
            error_msg = f"Failed to export JSON: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ExportResult(success=False, error_message=error_msg)

    def _prepare_data_for_json(self, data: Any, orient: str, date_format: str) -> Any:
        """Prepare data for JSON serialization."""
        if isinstance(data, pd.DataFrame):
            return data.to_dict(orient=orient)
        elif isinstance(data, pd.Series):
            return data.to_dict()
        elif isinstance(data, (dict, list, str, int, float, bool)) or data is None:
            return data
        else:
            # Try to convert to dict if object has __dict__
            if hasattr(data, "__dict__"):
                return data.__dict__
            else:
                return str(data)

    def _count_records(self, data: Any) -> int:
        """Count the number of records in the data."""
        if isinstance(data, list):
            return len(data)
        elif isinstance(data, dict):
            # If it's a dict with list values (like DataFrame orient='dict')
            if data and isinstance(next(iter(data.values())), list):
                return len(next(iter(data.values())))
            else:
                return 1
        else:
            return 1
