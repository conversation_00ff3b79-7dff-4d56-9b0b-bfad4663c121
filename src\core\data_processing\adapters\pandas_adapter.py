# -*- coding: utf-8 -*-
"""
Pandas Adapter for Data Processing

This module provides a Pandas-based implementation of the data processing adapter,
optimized for small to medium datasets with rich functionality.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import asyncio
import logging
import warnings
from pathlib import Path
from typing import Any, Dict, List, Optional, AsyncGenerator

import pandas as pd
import numpy as np
from pandas.api.types import is_numeric_dtype, is_datetime64_any_dtype

from .base_adapter import BaseAdapter, AdapterError
from ..types import (
    ProcessingEngine, ProcessingStatus, DataFormat,
    ProcessingMetrics, ProcessingConfig
)

# Suppress pandas warnings for cleaner output
warnings.filterwarnings('ignore', category=pd.errors.DtypeWarning)
warnings.filterwarnings('ignore', category=pd.errors.ParserWarning)


class PandasAdapter(BaseAdapter):
    """Pandas-based data processing adapter.
    
    Optimized for:
    - Small to medium datasets (<1M records)
    - Complex data transformations
    - Rich data analysis capabilities
    - Maximum compatibility
    """
    
    @property
    def engine_name(self) -> str:
        """Get the name of the processing engine."""
        return "pandas"
    
    @property
    def engine_type(self) -> ProcessingEngine:
        """Get the processing engine type."""
        return ProcessingEngine.PANDAS
    
    def is_engine_available(self) -> bool:
        """Check if Pandas is available."""
        try:
            import pandas as pd
            return True
        except ImportError:
            return False
    
    async def read_file(self, file_path: Path, **kwargs) -> pd.DataFrame:
        """Read data from a file using Pandas.
        
        Args:
            file_path: Path to the file
            **kwargs: Additional arguments for reading
            
        Returns:
            Pandas DataFrame
        """
        file_info = self.get_file_info(file_path)
        
        # Default read parameters
        read_params = {
            'encoding': kwargs.get('encoding', 'utf-8'),
            'low_memory': False,  # Ensure consistent dtypes
            'na_values': ['', 'NULL', 'null', 'None', 'N/A', 'n/a'],
            'keep_default_na': True,
        }
        read_params.update(kwargs)
        
        try:
            if file_info.format == DataFormat.CSV:
                # Auto-detect delimiter if not specified
                if 'sep' not in read_params and 'delimiter' not in read_params:
                    read_params['sep'] = self._detect_delimiter(file_path)
                
                df = pd.read_csv(file_path, **read_params)
                
            elif file_info.format == DataFormat.TSV:
                read_params['sep'] = '\t'
                df = pd.read_csv(file_path, **read_params)
                
            elif file_info.format == DataFormat.EXCEL:
                # Remove CSV-specific parameters
                excel_params = {k: v for k, v in read_params.items() 
                              if k not in ['sep', 'delimiter', 'quotechar']}
                df = pd.read_excel(file_path, **excel_params)
                
            elif file_info.format == DataFormat.JSON:
                df = pd.read_json(file_path, **kwargs)
                
            elif file_info.format == DataFormat.JSONL:
                df = pd.read_json(file_path, lines=True, **kwargs)
                
            elif file_info.format == DataFormat.PARQUET:
                df = pd.read_parquet(file_path, **kwargs)
                
            elif file_info.format == DataFormat.FEATHER:
                df = pd.read_feather(file_path, **kwargs)
                
            elif file_info.format == DataFormat.HDF5:
                key = kwargs.get('key', 'data')
                df = pd.read_hdf(file_path, key=key, **kwargs)
                
            else:
                raise AdapterError(f"Unsupported file format: {file_info.format}")
            
            # Optimize data types
            if kwargs.get('optimize_dtypes', True):
                df = self._optimize_dtypes(df)
            
            self.logger.info(f"Read {len(df)} records from {file_path}")
            return df
            
        except Exception as e:
            raise AdapterError(f"Failed to read file {file_path}: {e}")
    
    async def write_file(self, data: pd.DataFrame, file_path: Path, **kwargs) -> None:
        """Write DataFrame to a file.
        
        Args:
            data: DataFrame to write
            file_path: Path to write the file
            **kwargs: Additional arguments for writing
        """
        file_info = self.get_file_info(file_path)
        
        # Create parent directory if it doesn't exist
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            if file_info.format == DataFormat.CSV:
                write_params = {
                    'index': False,
                    'encoding': 'utf-8',
                }
                write_params.update(kwargs)
                data.to_csv(file_path, **write_params)
                
            elif file_info.format == DataFormat.TSV:
                write_params = {
                    'index': False,
                    'sep': '\t',
                    'encoding': 'utf-8',
                }
                write_params.update(kwargs)
                data.to_csv(file_path, **write_params)
                
            elif file_info.format == DataFormat.EXCEL:
                write_params = {
                    'index': False,
                    'engine': 'openpyxl',
                }
                write_params.update(kwargs)
                data.to_excel(file_path, **write_params)
                
            elif file_info.format == DataFormat.JSON:
                data.to_json(file_path, **kwargs)
                
            elif file_info.format == DataFormat.JSONL:
                data.to_json(file_path, orient='records', lines=True, **kwargs)
                
            elif file_info.format == DataFormat.PARQUET:
                data.to_parquet(file_path, **kwargs)
                
            elif file_info.format == DataFormat.FEATHER:
                data.to_feather(file_path, **kwargs)
                
            elif file_info.format == DataFormat.HDF5:
                key = kwargs.get('key', 'data')
                data.to_hdf(file_path, key=key, **kwargs)
                
            else:
                raise AdapterError(f"Unsupported file format: {file_info.format}")
            
            self.logger.info(f"Wrote {len(data)} records to {file_path}")
            
        except Exception as e:
            raise AdapterError(f"Failed to write file {file_path}: {e}")
    
    async def process_chunk(self, chunk: pd.DataFrame, processor_func: callable) -> pd.DataFrame:
        """Process a DataFrame chunk.
        
        Args:
            chunk: DataFrame chunk to process
            processor_func: Processing function
            
        Returns:
            Processed DataFrame chunk
        """
        try:
            # Run processor function in thread pool for CPU-intensive operations
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, processor_func, chunk)
            
            # Ensure result is a DataFrame
            if not isinstance(result, pd.DataFrame):
                raise AdapterError(f"Processor function must return DataFrame, got {type(result)}")
            
            return result
            
        except Exception as e:
            raise AdapterError(f"Failed to process chunk: {e}")
    
    async def validate_data(self, data: pd.DataFrame, validation_rules: Dict[str, Any]) -> Dict[str, Any]:
        """Validate DataFrame against rules.
        
        Args:
            data: DataFrame to validate
            validation_rules: Validation rules
            
        Returns:
            Validation results
        """
        results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'metrics': {
                'total_records': len(data),
                'valid_records': 0,
                'invalid_records': 0,
                'completeness': {},
                'data_types': {},
            }
        }
        
        try:
            # Check required columns
            required_columns = validation_rules.get('required_columns', [])
            missing_columns = set(required_columns) - set(data.columns)
            if missing_columns:
                results['errors'].append(f"Missing required columns: {missing_columns}")
                results['is_valid'] = False
            
            # Check data types
            expected_dtypes = validation_rules.get('dtypes', {})
            for column, expected_dtype in expected_dtypes.items():
                if column in data.columns:
                    actual_dtype = str(data[column].dtype)
                    results['metrics']['data_types'][column] = actual_dtype
                    
                    if not self._is_dtype_compatible(actual_dtype, expected_dtype):
                        results['warnings'].append(
                            f"Column {column} has dtype {actual_dtype}, expected {expected_dtype}"
                        )
            
            # Check completeness
            for column in data.columns:
                null_count = data[column].isnull().sum()
                completeness = (len(data) - null_count) / len(data) if len(data) > 0 else 0
                results['metrics']['completeness'][column] = completeness
                
                min_completeness = validation_rules.get('min_completeness', {}).get(column, 0.0)
                if completeness < min_completeness:
                    results['errors'].append(
                        f"Column {column} completeness {completeness:.2%} below minimum {min_completeness:.2%}"
                    )
                    results['is_valid'] = False
            
            # Check value ranges
            value_ranges = validation_rules.get('value_ranges', {})
            for column, (min_val, max_val) in value_ranges.items():
                if column in data.columns and is_numeric_dtype(data[column]):
                    out_of_range = ((data[column] < min_val) | (data[column] > max_val)).sum()
                    if out_of_range > 0:
                        results['warnings'].append(
                            f"Column {column} has {out_of_range} values out of range [{min_val}, {max_val}]"
                        )
            
            # Check unique constraints
            unique_columns = validation_rules.get('unique_columns', [])
            for column in unique_columns:
                if column in data.columns:
                    duplicates = data[column].duplicated().sum()
                    if duplicates > 0:
                        results['errors'].append(f"Column {column} has {duplicates} duplicate values")
                        results['is_valid'] = False
            
            # Calculate valid/invalid records
            if results['is_valid']:
                results['metrics']['valid_records'] = len(data)
            else:
                results['metrics']['invalid_records'] = len(data)
            
            return results
            
        except Exception as e:
            results['errors'].append(f"Validation error: {e}")
            results['is_valid'] = False
            return results
    
    async def transform_data(self, data: pd.DataFrame, transformations: List[Dict[str, Any]]) -> pd.DataFrame:
        """Transform DataFrame using specified transformations.
        
        Args:
            data: DataFrame to transform
            transformations: List of transformation specifications
            
        Returns:
            Transformed DataFrame
        """
        result = data.copy()
        
        try:
            for transform in transformations:
                transform_type = transform.get('type')
                
                if transform_type == 'rename_columns':
                    mapping = transform.get('mapping', {})
                    result = result.rename(columns=mapping)
                
                elif transform_type == 'drop_columns':
                    columns = transform.get('columns', [])
                    result = result.drop(columns=columns, errors='ignore')
                
                elif transform_type == 'add_column':
                    column_name = transform.get('name')
                    value = transform.get('value')
                    result[column_name] = value
                
                elif transform_type == 'convert_dtypes':
                    dtype_mapping = transform.get('mapping', {})
                    for column, dtype in dtype_mapping.items():
                        if column in result.columns:
                            result[column] = self._convert_dtype(result[column], dtype)
                
                elif transform_type == 'fill_na':
                    fill_values = transform.get('values', {})
                    result = result.fillna(fill_values)
                
                elif transform_type == 'filter_rows':
                    condition = transform.get('condition')
                    if condition:
                        result = result.query(condition)
                
                elif transform_type == 'sort':
                    columns = transform.get('columns', [])
                    ascending = transform.get('ascending', True)
                    result = result.sort_values(by=columns, ascending=ascending)
                
                elif transform_type == 'group_by':
                    group_columns = transform.get('columns', [])
                    agg_functions = transform.get('aggregations', {})
                    result = result.groupby(group_columns).agg(agg_functions).reset_index()
                
                elif transform_type == 'custom':
                    func = transform.get('function')
                    if callable(func):
                        result = func(result)
                
                else:
                    self.logger.warning(f"Unknown transformation type: {transform_type}")
            
            return result
            
        except Exception as e:
            raise AdapterError(f"Failed to transform data: {e}")
    
    def get_data_info(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Get information about the DataFrame.
        
        Args:
            data: DataFrame
            
        Returns:
            Data information dictionary
        """
        return {
            'shape': data.shape,
            'columns': list(data.columns),
            'dtypes': {col: str(dtype) for col, dtype in data.dtypes.items()},
            'memory_usage_mb': data.memory_usage(deep=True).sum() / (1024 * 1024),
            'null_counts': data.isnull().sum().to_dict(),
            'numeric_columns': list(data.select_dtypes(include=[np.number]).columns),
            'categorical_columns': list(data.select_dtypes(include=['category']).columns),
            'datetime_columns': list(data.select_dtypes(include=['datetime64']).columns),
            'object_columns': list(data.select_dtypes(include=['object']).columns),
        }
    
    def to_pandas(self, data: pd.DataFrame) -> pd.DataFrame:
        """Convert data to Pandas DataFrame (no-op for Pandas).
        
        Args:
            data: DataFrame
            
        Returns:
            Same DataFrame
        """
        return data
    
    def from_pandas(self, df: pd.DataFrame) -> pd.DataFrame:
        """Convert Pandas DataFrame to Pandas format (no-op).
        
        Args:
            df: Pandas DataFrame
            
        Returns:
            Same DataFrame
        """
        return df
    
    async def _read_file_chunks(self, file_path: Path, chunk_size: int) -> AsyncGenerator[pd.DataFrame, None]:
        """Read file in chunks.
        
        Args:
            file_path: Path to the file
            chunk_size: Size of each chunk
            
        Yields:
            DataFrame chunks
        """
        file_info = self.get_file_info(file_path)
        
        try:
            if file_info.format in [DataFormat.CSV, DataFormat.TSV]:
                # Use pandas chunking for CSV/TSV
                sep = '\t' if file_info.format == DataFormat.TSV else self._detect_delimiter(file_path)
                
                chunk_reader = pd.read_csv(
                    file_path,
                    chunksize=chunk_size,
                    sep=sep,
                    encoding='utf-8',
                    low_memory=False
                )
                
                for chunk in chunk_reader:
                    if self.config.enable_optimization:
                        chunk = self._optimize_dtypes(chunk)
                    yield chunk
            
            else:
                # For other formats, read entire file and split
                data = await self.read_file(file_path)
                
                for i in range(0, len(data), chunk_size):
                    chunk = data.iloc[i:i + chunk_size]
                    yield chunk
                    
        except Exception as e:
            raise AdapterError(f"Failed to read file chunks: {e}")
    
    def _detect_delimiter(self, file_path: Path, sample_size: int = 1024) -> str:
        """Detect CSV delimiter.
        
        Args:
            file_path: Path to CSV file
            sample_size: Size of sample to analyze
            
        Returns:
            Detected delimiter
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                sample = f.read(sample_size)
            
            # Common delimiters to test
            delimiters = [',', ';', '\t', '|']
            delimiter_counts = {}
            
            for delimiter in delimiters:
                count = sample.count(delimiter)
                if count > 0:
                    delimiter_counts[delimiter] = count
            
            if delimiter_counts:
                return max(delimiter_counts, key=delimiter_counts.get)
            else:
                return ','  # Default to comma
                
        except Exception:
            return ','  # Default to comma on error
    
    def _optimize_dtypes(self, df: pd.DataFrame) -> pd.DataFrame:
        """Optimize DataFrame data types for memory efficiency.
        
        Args:
            df: DataFrame to optimize
            
        Returns:
            Optimized DataFrame
        """
        optimized = df.copy()
        
        for column in optimized.columns:
            col_type = optimized[column].dtype
            
            # Optimize numeric types
            if pd.api.types.is_integer_dtype(col_type):
                optimized[column] = pd.to_numeric(optimized[column], downcast='integer')
            elif pd.api.types.is_float_dtype(col_type):
                optimized[column] = pd.to_numeric(optimized[column], downcast='float')
            
            # Convert object columns with low cardinality to category
            elif col_type == 'object':
                unique_ratio = optimized[column].nunique() / len(optimized)
                if unique_ratio < 0.5:  # Less than 50% unique values
                    optimized[column] = optimized[column].astype('category')
        
        return optimized
    
    def _is_dtype_compatible(self, actual: str, expected: str) -> bool:
        """Check if data types are compatible.
        
        Args:
            actual: Actual data type
            expected: Expected data type
            
        Returns:
            True if compatible
        """
        # Normalize type names
        actual = actual.lower()
        expected = expected.lower()
        
        # Define compatibility groups
        numeric_types = ['int', 'float', 'number']
        string_types = ['object', 'string', 'str']
        datetime_types = ['datetime', 'timestamp']
        
        # Check if both are in the same group
        for type_group in [numeric_types, string_types, datetime_types]:
            if any(t in actual for t in type_group) and any(t in expected for t in type_group):
                return True
        
        return actual == expected
    
    def _convert_dtype(self, series: pd.Series, target_dtype: str) -> pd.Series:
        """Convert series to target data type.
        
        Args:
            series: Series to convert
            target_dtype: Target data type
            
        Returns:
            Converted series
        """
        try:
            if target_dtype.lower() in ['int', 'integer']:
                return pd.to_numeric(series, errors='coerce').astype('Int64')
            elif target_dtype.lower() in ['float', 'number']:
                return pd.to_numeric(series, errors='coerce')
            elif target_dtype.lower() in ['str', 'string']:
                return series.astype('string')
            elif target_dtype.lower() in ['datetime', 'timestamp']:
                return pd.to_datetime(series, errors='coerce')
            elif target_dtype.lower() == 'category':
                return series.astype('category')
            else:
                return series.astype(target_dtype)
        except Exception:
            self.logger.warning(f"Failed to convert column to {target_dtype}, keeping original type")
            return series