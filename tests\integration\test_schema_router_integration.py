"""Integration tests for Schema Router."""

import pytest
import async<PERSON>
from pathlib import Path
from unittest.mock import patch, <PERSON><PERSON>, AsyncMock

from src.database.schema.router import <PERSON>hema<PERSON>outer, Routing<PERSON><PERSON>, RoutingStrategy
from src.database.schema.manager import SchemaManager
from src.config import get_config as load_config
from src.database.connection.session import DatabaseSession


class TestSchemaRouterIntegration:
    """Integration tests for SchemaRouter with other database components."""
    
    @pytest.fixture
    def config_path(self, tmp_path):
        """Create a temporary config file for testing."""
        config_content = """
database:
  host: localhost
  port: 5432
  name: test_db
  user: test_user
  password: test_pass

pool:
  size: 5
  max_overflow: 10
  timeout: 30
  recycle: 3600

data_sources:
  ep:
    schema_name: ep_to2
    file_extensions: ['.xlsx', '.xls']
    table_name_pattern: 'ep_{cell_type}_{year}_cw{week}'
  
  cdr:
    schema_name: cdr_to2
    file_extensions: ['.xlsx', '.xls']
    table_name_pattern: 'cdr_{year}Q{quarter}_{service_type}'
  
  nlg:
    schema_name: nlg_to2
    file_extensions: ['.xlsb']
    table_name_pattern: 'nlg_cube_aktuell_{date}'

logging:
  level: INFO
  file: logs/test_database.log
"""
        config_file = tmp_path / "test_database.yaml"
        config_file.write_text(config_content)
        return str(config_file)
    
    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        session = Mock(spec=DatabaseSession)
        session.execute = Mock()
        session.fetch_all = Mock()
        session.fetch_one = Mock()
        return session
    
    @pytest.fixture
    def schema_manager(self, mock_db_session):
        """Create a SchemaManager instance for testing."""
        manager = SchemaManager(mock_db_session)

        # Mock schema existence checks with async methods
        existing_schemas = ["public", "ep_to2", "cdr_to2", "nlg_to2"]
        manager.schema_exists = AsyncMock(side_effect=lambda name: name in existing_schemas)
        manager.list_schemas = AsyncMock(return_value=existing_schemas)
        manager.create_schema = AsyncMock()

        return manager
    
    @pytest.fixture
    def schema_router(self, schema_manager, config_path):
        """Create a SchemaRouter instance with real configuration."""
        with patch('src.database.schema.router.get_config') as mock_get_config:
            config = load_config(config_path)
            mock_get_config.return_value = config
            return SchemaRouter(schema_manager)
    
    def test_router_initialization_with_real_config(self, schema_router):
        """Test router initialization with real configuration."""
        assert schema_router.default_schema == "public"
        
        # Check that data source rules were created from config
        rule_names = [rule.name for rule in schema_router.routing_rules]
        assert "data_source_ep" in rule_names
        assert "data_source_cdr" in rule_names
        assert "data_source_nlg" in rule_names
        
        # Verify rule details
        ep_rule = next(rule for rule in schema_router.routing_rules if rule.name == "data_source_ep")
        assert ep_rule.target_schema == "ep_to2"
        assert ep_rule.strategy == RoutingStrategy.DATA_SOURCE
        assert ep_rule.condition["data_source"] == "ep"
    
    def test_data_source_routing_integration(self, schema_router):
        """Test data source routing with real configuration."""
        # Test EP data source routing
        context = {"data_source": "ep"}
        schema = schema_router.route_schema(context)
        assert schema == "ep_to2"
        
        # Test CDR data source routing
        context = {"data_source": "cdr"}
        schema = schema_router.route_schema(context)
        assert schema == "cdr_to2"
        
        # Test NLG data source routing
        context = {"data_source": "nlg"}
        schema = schema_router.route_schema(context)
        assert schema == "nlg_to2"
    
    def test_table_name_inference_integration(self, schema_router):
        """Test table name pattern inference with real configuration."""
        # Test EP table name inference
        schema = schema_router.get_schema_for_table("ep_lte_2024_cw01")
        assert schema == "ep_to2"
        
        # Test CDR table name inference
        schema = schema_router.get_schema_for_table("cdr_2024Q1_voice")
        assert schema == "cdr_to2"
        
        # Test NLG table name inference
        schema = schema_router.get_schema_for_table("nlg_cube_aktuell_20240101")
        assert schema == "nlg_to2"
        
        # Test unknown table pattern
        schema = schema_router.get_schema_for_table("unknown_table")
        assert schema == "public"  # Should fall back to default
    
    @pytest.mark.asyncio
    async def test_schema_switching_integration(self, schema_router, schema_manager):
        """Test schema switching integration with schema manager."""
        # Switch to existing schema
        result = await schema_router.switch_schema("ep_to2")
        assert result is True
        schema_manager.schema_exists.assert_called_with("ep_to2")

        # Switch to non-existing schema with creation
        schema_manager.schema_exists.return_value = False
        result = await schema_router.switch_schema("new_tenant_schema", create_if_not_exists=True)
        assert result is True
        schema_manager.create_schema.assert_called_with("new_tenant_schema")
    
    def test_multi_tenant_scenario(self, schema_router, schema_manager):
        """Test multi-tenant scenario with schema routing."""
        # Add tenant-specific routing rules
        tenant_rules = [
            RoutingRule(
                name="tenant_a",
                strategy=RoutingStrategy.TENANT,
                condition={"tenant_id": "tenant_a"},
                target_schema="tenant_a_schema",
                priority=50
            ),
            RoutingRule(
                name="tenant_b",
                strategy=RoutingStrategy.TENANT,
                condition={"tenant_id": "tenant_b"},
                target_schema="tenant_b_schema",
                priority=50
            ),
        ]
        
        for rule in tenant_rules:
            schema_router.add_routing_rule(rule)
        
        # Test tenant A routing
        schema = schema_router.get_tenant_schema("tenant_a")
        assert schema == "tenant_a_schema"
        
        # Test tenant B routing
        schema = schema_router.get_tenant_schema("tenant_b")
        assert schema == "tenant_b_schema"
        
        # Test unknown tenant (should fall back to default)
        schema = schema_router.get_tenant_schema("unknown_tenant")
        assert schema == "public"
    
    def test_complex_routing_scenario(self, schema_router):
        """Test complex routing scenario with multiple criteria."""
        # Add complex routing rule
        complex_rule = RoutingRule(
            name="complex_rule",
            strategy=RoutingStrategy.CUSTOM,
            condition={
                "data_source": "ep",
                "tenant_id": "premium_tenant",
                "table_name": {"operator": "startswith", "value": "premium_"}
            },
            target_schema="premium_ep_schema",
            priority=100  # High priority
        )
        schema_router.add_routing_rule(complex_rule)
        
        # Context that matches complex rule
        context = {
            "data_source": "ep",
            "tenant_id": "premium_tenant",
            "table_name": "premium_ep_data"
        }
        schema = schema_router.route_schema(context)
        assert schema == "premium_ep_schema"
        
        # Context that partially matches (should fall back to data source rule)
        context = {
            "data_source": "ep",
            "tenant_id": "regular_tenant",
            "table_name": "regular_ep_data"
        }
        schema = schema_router.route_schema(context)
        assert schema == "ep_to2"  # Falls back to data source rule
    
    @pytest.mark.asyncio
    async def test_routing_validation_integration(self, schema_router, schema_manager):
        """Test routing configuration validation integration."""
        # Add rule with non-existent schema
        non_existent_rule = RoutingRule(
            name="non_existent_schema_rule",
            strategy=RoutingStrategy.TENANT,
            condition={"tenant_id": "test_tenant"},
            target_schema="non_existent_schema"
        )
        schema_router.add_routing_rule(non_existent_rule)

        # Mock schema existence check with async function
        async def mock_schema_exists(schema_name):
            existing = ["public", "ep_to2", "cdr_to2", "nlg_to2"]
            return schema_name in existing

        schema_manager.schema_exists.side_effect = mock_schema_exists

        # Validate configuration
        report = await schema_router.validate_routing_configuration()

        assert report["valid"] is False  # Should have validation errors
        assert len(report["issues"]) > 0  # Should have issues about non-existent schema
        assert "non_existent_schema" in str(report["issues"])
    
    def test_performance_with_many_rules(self, schema_router):
        """Test router performance with many routing rules."""
        import time
        
        # Add many routing rules
        for i in range(100):
            rule = RoutingRule(
                name=f"rule_{i}",
                strategy=RoutingStrategy.CUSTOM,
                condition={"rule_id": str(i)},
                target_schema=f"schema_{i}",
                priority=i
            )
            schema_router.add_routing_rule(rule)
        
        # Test routing performance
        start_time = time.time()
        
        for i in range(100):
            context = {"rule_id": str(i)}
            schema = schema_router.route_schema(context)
            assert schema == f"schema_{i}"
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should complete within reasonable time (adjust threshold as needed)
        assert elapsed < 1.0, f"Routing took too long: {elapsed:.3f} seconds"
    
    def test_concurrent_routing(self, schema_router):
        """Test concurrent schema routing operations."""
        import threading
        import time
        
        results = []
        errors = []
        
        def route_worker(worker_id):
            try:
                for i in range(10):
                    context = {"data_source": "ep", "worker_id": worker_id, "iteration": i}
                    schema = schema_router.route_schema(context)
                    results.append((worker_id, i, schema))
                    time.sleep(0.001)  # Small delay to increase chance of concurrency issues
            except Exception as e:
                errors.append((worker_id, str(e)))
        
        # Start multiple worker threads
        threads = []
        for worker_id in range(5):
            thread = threading.Thread(target=route_worker, args=(worker_id,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        assert len(errors) == 0, f"Errors occurred during concurrent routing: {errors}"
        assert len(results) == 50  # 5 workers * 10 iterations each
        
        # All results should have the same schema for ep data source
        for worker_id, iteration, schema in results:
            assert schema == "ep_to2"
    
    def test_configuration_reload_integration(self, schema_router, config_path, tmp_path):
        """Test configuration reload integration."""
        # Initial state
        initial_rules_count = len(schema_router.routing_rules)
        
        # Create updated configuration
        updated_config_content = """
database:
  host: localhost
  port: 5432
  name: test_db
  user: test_user
  password: test_pass

data_sources:
  ep:
    schema_name: ep_to2
    file_extensions: ['.xlsx', '.xls']
  
  cdr:
    schema_name: cdr_to2
    file_extensions: ['.xlsx', '.xls']
  
  new_source:
    schema_name: new_schema
    file_extensions: ['.csv']
    table_name_pattern: 'new_{table}'

logging:
  level: INFO
"""
        
        updated_config_file = tmp_path / "updated_database.yaml"
        updated_config_file.write_text(updated_config_content)
        
        # Simulate configuration reload by creating new router
        with patch('src.database.schema.router.get_config') as mock_get_config:
            updated_config = load_config(str(updated_config_file))
            mock_get_config.return_value = updated_config
            
            # Create new router with updated config
            new_schema_manager = Mock()
            new_schema_manager.schema_exists.return_value = True
            new_router = SchemaRouter(new_schema_manager)
        
        # Check that new data source rule was added
        new_rule_names = [rule.name for rule in new_router.routing_rules]
        assert "data_source_new_source" in new_rule_names
        
        # Test routing with new data source
        context = {"data_source": "new_source"}
        schema = new_router.route_schema(context)
        assert schema == "new_schema"