"""Unit tests for connection management components."""

import asyncio
import os
from typing import Any, Dict
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
import pytest_asyncio

from src.config.models import DatabaseConfig
from src.database.connection.health_check import <PERSON><PERSON><PERSON><PERSON>
from src.database.connection.pool import <PERSON><PERSON><PERSON><PERSON>anager
from src.database.connection.read_write_splitter import ReadWriteSplitter, LoadBalancingStrategy

# Import components to test
from src.database.connection.session import SessionManager
from src.database.exceptions import Configuration<PERSON>rror, ConnectionError, DatabaseError


class TestSessionManager:
    """Test cases for SessionManager class."""

    @pytest.fixture
    def mock_config(self):
        """Create mock database configuration."""
        config = Mock()
        config.database = Mock()
        config.database.host = "localhost"
        config.database.port = 5432
        config.database.name = "test_db"
        config.database.user = "test_user"
        config.database.password = "test_pass"
        config.database.ssl = False
        config.database.timeout = 30
        config.pool = Mock()
        config.pool.timeout = 30
        return config

    @pytest.fixture
    def session(self, mock_config):
        """Create SessionManager instance for testing."""
        from src.database.connection.session import SessionManager
        return SessionManager(mock_config)

    def test_session_initialization(self, session, mock_config):
        """Test session initialization with configuration."""
        assert session.config == mock_config
        assert session._connection is None
        assert not session.is_connected

    @pytest.mark.asyncio
    async def test_connect_success(self, session):
        """Test successful database connection."""
        with patch("src.database.connection.session.asyncpg.connect", new_callable=AsyncMock) as mock_connect:
            mock_connection = AsyncMock()
            mock_connection.is_closed = Mock(return_value=False)
            mock_connect.return_value = mock_connection

            connection = await session.get_connection()
            
            # The get_connection method should set these internally
            assert session.is_connected
            assert connection == mock_connection
            mock_connect.assert_called_once()

    @pytest.mark.asyncio
    async def test_connect_failure(self, session):
        """Test connection failure handling."""
        with patch("src.database.connection.session.asyncpg.connect", new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = Exception("Connection failed")

            with pytest.raises(ConnectionError):
                await session.get_connection()

            assert not session.is_connected
            assert session._connection is None

    @pytest.mark.asyncio
    async def test_disconnect(self, session):
        """Test database disconnection."""
        # Setup connected session
        mock_connection = AsyncMock()
        mock_connection.is_closed = Mock(return_value=False)
        session._connection = mock_connection
        session._is_connected = True

        await session.close_connection()

        assert not session.is_connected
        mock_connection.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_context_manager(self, session):
        """Test session as async context manager."""
        with patch("src.database.connection.session.asyncpg.connect", new_callable=AsyncMock) as mock_connect:
            mock_connection = AsyncMock()
            mock_connection.is_closed = Mock(return_value=False)
            mock_connect.return_value = mock_connection

            async with session as conn:
                # The context manager should establish connection
                assert session.is_connected
                assert conn == mock_connection

            # Should be disconnected after context
            assert not session.is_connected
            mock_connection.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_query(self, session):
        """Test query execution through connection."""
        with patch("src.database.connection.session.asyncpg.connect", new_callable=AsyncMock) as mock_connect:
            mock_connection = AsyncMock()
            mock_connection.is_closed.return_value = False
            mock_connection.fetch.return_value = [{"id": 1, "name": "test"}]
            mock_connect.return_value = mock_connection

            # Get connection and execute query
            connection = await session.get_connection()
            result = await connection.fetch("SELECT * FROM test")

            assert result == [{"id": 1, "name": "test"}]
            mock_connection.fetch.assert_called_once_with("SELECT * FROM test")

    @pytest.mark.asyncio
    async def test_get_connection_without_config(self):
        """Test getting connection without proper config raises error."""
        # Create config with missing required attributes
        incomplete_config = Mock()
        incomplete_config.database = Mock()
        incomplete_config.pool = Mock()
        # Missing host attribute to trigger ConfigurationError
        del incomplete_config.database.host
        incomplete_config.database.port = 5432
        incomplete_config.database.name = "test_db"
        incomplete_config.database.user = "test_user"
        incomplete_config.database.password = "test_pass"
        incomplete_config.pool.timeout = 30
        
        session = SessionManager(incomplete_config)
        
        with pytest.raises((ConfigurationError, ConnectionError, AttributeError)):
            await session.get_connection()


class TestDatabasePoolManager:
    """Test cases for DatabasePoolManager class."""

    @pytest.fixture
    def mock_config(self):
        """Create mock configuration for testing."""
        config = Mock()
        config.database = Mock()
        config.database.host = "localhost"
        config.database.port = 5432
        config.database.name = "test_db"
        config.database.user = "test_user"
        config.database.password = "test_pass"
        config.database.ssl = False
        config.database.timeout = 30
        config.pool = Mock()
        config.pool.size = 2
        config.pool.max_overflow = 10
        config.pool.timeout = 30
        config.pool.recycle = 3600
        return config

    @pytest.fixture
    def pool_manager(self, mock_config):
        """Create DatabasePoolManager instance."""
        return DatabasePoolManager(mock_config)

    @pytest.fixture
    def connection_pool(self, mock_config):
        """Create initialized DatabasePoolManager instance."""
        pool = DatabasePoolManager(mock_config)
        pool._is_initialized = True
        return pool

    def test_pool_initialization(self, pool_manager, mock_config):
        """Test pool initialization."""
        assert pool_manager.config == mock_config
        assert pool_manager._pool is None
        assert not pool_manager._is_initialized

    @pytest.mark.asyncio
    async def test_pool_creation(self, pool_manager):
        """Test connection pool creation."""
        with patch("asyncpg.create_pool", new_callable=AsyncMock) as mock_create_pool:
            mock_pool = AsyncMock()
            mock_create_pool.return_value = mock_pool

            await pool_manager.initialize_pool()

            assert pool_manager._pool == mock_pool
            assert pool_manager._is_initialized
            mock_create_pool.assert_called_once()

    @pytest.mark.asyncio
    async def test_acquire_connection(self, connection_pool):
        """Test acquiring connection from pool."""
        mock_pool = AsyncMock()
        mock_connection = AsyncMock()
        mock_pool.acquire.return_value = mock_connection
        connection_pool._pool = mock_pool

        conn = await connection_pool.acquire_connection()
        assert conn == mock_connection

    def test_pool_stats(self, connection_pool):
        """Test pool statistics."""
        mock_pool = Mock()
        mock_pool.get_size.return_value = 5
        mock_pool.get_idle_size.return_value = 3
        connection_pool._pool = mock_pool

        stats = connection_pool.get_pool_stats()

        assert stats["pool_size"] == 5
        assert stats["pool_idle"] == 3
        assert stats["pool_active"] == 2

    @pytest.mark.asyncio
    async def test_pool_close(self, connection_pool):
        """Test pool closure."""
        mock_pool = AsyncMock()
        connection_pool._pool = mock_pool

        await connection_pool.close_pool()

        mock_pool.close.assert_called_once()
        assert connection_pool._pool is None


class TestHealthChecker:
    """Test cases for HealthChecker class."""

    @pytest.fixture
    def health_checker(self):
        """Create HealthChecker instance."""
        mock_pool = AsyncMock()
        return HealthChecker(pool=mock_pool, check_interval=1.0)

    @pytest.mark.asyncio
    async def test_health_check_success(self, health_checker):
        """Test successful health check."""
        mock_connection = AsyncMock()
        mock_connection.fetchval.return_value = 1

        result = await health_checker.check_connection(mock_connection)

        assert result is True
        mock_connection.fetchval.assert_called_once_with("SELECT 1")

    @pytest.mark.asyncio
    async def test_health_check_failure(self, health_checker):
        """Test health check failure."""
        mock_connection = AsyncMock()
        mock_connection.fetchval.side_effect = Exception("Connection lost")

        result = await health_checker.check_connection(mock_connection)

        assert result is False

    @pytest.mark.asyncio
    async def test_health_check_timeout(self, health_checker):
        """Test health check timeout."""
        mock_connection = AsyncMock()
        mock_connection.fetchval.side_effect = asyncio.TimeoutError()

        result = await health_checker.check_connection(mock_connection)

        assert result is False

    def test_health_status_tracking(self, health_checker):
        """Test health status tracking."""
        # Initially unknown status (not healthy)
        assert not health_checker.is_healthy

        # Mark as healthy first
        health_checker._mark_healthy()
        assert health_checker.is_healthy

        # Mark as unhealthy
        health_checker._mark_unhealthy("Test failure")
        assert not health_checker.is_healthy

        # Mark as healthy again
        health_checker._mark_healthy()
        assert health_checker.is_healthy


class TestReadWriteSplitter:
    """Test cases for ReadWriteSplitter class."""

    @pytest.fixture
    def rw_config(self):
        """Create read-write splitter configuration."""
        return {
            "primary": {
                "host": "primary.db.com",
                "port": 5432,
                "name": "test_db",
                "user": "user",
                "password": "pass",
            },
            "replicas": [
                {
                    "host": "replica1.db.com",
                    "port": 5432,
                    "name": "test_db",
                    "user": "user",
                    "password": "pass",
                },
                {
                    "host": "replica2.db.com",
                    "port": 5432,
                    "name": "test_db",
                    "user": "user",
                    "password": "pass",
                },
            ],
            "load_balancing_strategy": "round_robin",
            "fallback_to_primary": True,
            "health_check_interval": 30,
        }

    @pytest_asyncio.fixture
    async def rw_splitter(self, rw_config):
        """Create ReadWriteSplitter instance."""
        # DatabaseConfig already imported at top of file
        
        # Convert config dict to DatabaseConfig objects
        primary_config = DatabaseConfig(**rw_config["primary"])
        replica_configs = [DatabaseConfig(**replica) for replica in rw_config["replicas"]]
        
        splitter = ReadWriteSplitter(
            primary_config=primary_config,
            replica_configs=replica_configs,
            load_balancing_strategy=LoadBalancingStrategy.ROUND_ROBIN,
            fallback_to_primary=rw_config["fallback_to_primary"],
            health_check_interval=rw_config["health_check_interval"]
        )
        
        # Mock the initialize method to avoid actual database connections
        with patch.object(splitter, 'initialize', new_callable=AsyncMock):
            await splitter.initialize()
            splitter._is_initialized = True
            yield splitter

    @pytest.mark.asyncio
    async def test_splitter_initialization(self, rw_splitter, rw_config):
        """Test read-write splitter initialization."""
        # Check primary config attributes
        assert rw_splitter.primary_config.host == rw_config["primary"]["host"]
        assert rw_splitter.primary_config.port == rw_config["primary"]["port"]
        assert rw_splitter.primary_config.name == rw_config["primary"]["name"]
        assert rw_splitter.primary_config.user == rw_config["primary"]["user"]
        
        # Check replica configs
        assert len(rw_splitter.replica_configs) == 2
        assert rw_splitter.replica_configs[0].host == rw_config["replicas"][0]["host"]
        assert rw_splitter.replica_configs[1].host == rw_config["replicas"][1]["host"]
        
        # Check other attributes
        assert rw_splitter.load_balancing_strategy == LoadBalancingStrategy.ROUND_ROBIN
        assert rw_splitter.fallback_to_primary is True

    @pytest.mark.asyncio
    async def test_get_write_connection(self, rw_splitter):
        """Test getting write connection from primary."""
        with patch.object(
            rw_splitter, "_acquire_write_connection", new_callable=AsyncMock
        ) as mock_acquire:
            mock_connection = AsyncMock()
            mock_pool = AsyncMock()
            mock_acquire.return_value = (mock_connection, mock_pool)

            conn, pool = await rw_splitter.acquire_connection(read_only=False)

            assert conn == mock_connection
            assert pool == mock_pool
            mock_acquire.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_read_connection_round_robin(self, rw_splitter):
        """Test getting read connection with round-robin strategy."""
        with patch.object(
            rw_splitter, "_acquire_read_connection", new_callable=AsyncMock
        ) as mock_acquire:
            mock_connection1 = AsyncMock()
            mock_connection2 = AsyncMock()
            mock_pool1 = AsyncMock()
            mock_pool2 = AsyncMock()
            mock_acquire.side_effect = [(mock_connection1, mock_pool1), (mock_connection2, mock_pool2)]

            # First call should get first replica
            conn1, pool1 = await rw_splitter.acquire_connection(read_only=True)
            assert conn1 == mock_connection1
            assert pool1 == mock_pool1

            # Second call should get second replica
            conn2, pool2 = await rw_splitter.acquire_connection(read_only=True)
            assert conn2 == mock_connection2
            assert pool2 == mock_pool2

    @pytest.mark.asyncio
    async def test_read_connection_fallback_to_primary(self, rw_splitter):
        """Test fallback to primary when replicas are unavailable."""
        # Mock the replica pools to simulate no healthy replicas
        with patch.object(
            rw_splitter, "_get_healthy_replicas", new_callable=AsyncMock
        ) as mock_healthy, patch.object(
            rw_splitter, "_acquire_write_connection", new_callable=AsyncMock
        ) as mock_write:
            # Simulate no healthy replicas available
            mock_healthy.return_value = []
            mock_connection = AsyncMock()
            mock_pool = AsyncMock()
            mock_write.return_value = (mock_connection, mock_pool)

            conn, pool = await rw_splitter.acquire_connection(read_only=True)

            assert conn == mock_connection
            assert pool == mock_pool
            mock_write.assert_called_once()

    @pytest.mark.asyncio
    async def test_connection_health_monitoring(self, rw_splitter):
        """Test connection health monitoring."""
        # Mock health checkers
        from unittest.mock import Mock
        from src.database.connection.health_check import HealthStatus, HealthCheckResult
        
        # Create mock health checkers
        mock_primary_checker = AsyncMock()
        mock_replica_checker1 = AsyncMock()
        mock_replica_checker2 = AsyncMock()
        
        # Set up health check results
        from datetime import datetime
        
        primary_result = HealthCheckResult(
            status=HealthStatus.HEALTHY,
            timestamp=datetime.now(),
            response_time_ms=100.0,
            error_message=None
        )
        replica1_result = HealthCheckResult(
            status=HealthStatus.HEALTHY,
            timestamp=datetime.now(),
            response_time_ms=200.0,
            error_message=None
        )
        replica2_result = HealthCheckResult(
            status=HealthStatus.UNHEALTHY,
            timestamp=datetime.now(),
            response_time_ms=0.0,
            error_message="Connection timeout"
        )
        
        mock_primary_checker.check_health.return_value = primary_result
        mock_replica_checker1.check_health.return_value = replica1_result
        mock_replica_checker2.check_health.return_value = replica2_result
        
        # Assign mock health checkers
        rw_splitter.primary_health_checker = mock_primary_checker
        rw_splitter.replica_health_checkers = [mock_replica_checker1, mock_replica_checker2]
        rw_splitter.replica_pools = [Mock(), Mock()]  # Mock pools
        rw_splitter._replica_connection_counts = {0: 5, 1: 3}
        
        health_status = await rw_splitter.get_health_status()
        
        # Verify the structure and content
        assert "primary" in health_status
        assert "replicas" in health_status
        assert "summary" in health_status
        assert health_status["summary"]["primary_healthy"] is True
        assert health_status["summary"]["healthy_replicas"] == 1
        assert len(health_status["replicas"]) == 2
        assert health_status["replicas"][0]["status"] == "healthy"
        assert health_status["replicas"][1]["status"] == "unhealthy"

    @pytest.mark.asyncio
    async def test_connection_statistics(self, rw_splitter):
        """Test connection statistics tracking."""
        # Mock replica pools to simulate initialized state
        from unittest.mock import Mock
        rw_splitter.replica_pools = [Mock(), Mock()]  # Two mock pools
        
        # Initialize connection counts dictionary
        rw_splitter._replica_connection_counts = {0: 10, 1: 5}
        rw_splitter._current_replica_index = 1

        stats = rw_splitter.get_stats()

        assert stats["replica_connection_counts"][0] == 10
        assert stats["replica_connection_counts"][1] == 5
        assert stats["current_replica_index"] == 1
        assert stats["total_replicas"] == 2
        assert stats["load_balancing_strategy"] == "round_robin"

    @pytest.mark.asyncio
    async def test_context_manager_write(self, rw_splitter):
        """Test read-write splitter as context manager for write operations."""
        with patch.object(
            rw_splitter, "acquire_connection", new_callable=AsyncMock
        ) as mock_acquire:
            mock_connection = AsyncMock()
            mock_pool = AsyncMock()
            mock_acquire.return_value = (mock_connection, mock_pool)

            async with rw_splitter.get_connection(read_only=False) as conn:
                assert conn == mock_connection

            mock_acquire.assert_called_once_with(False)
            mock_pool.release_connection.assert_called_once_with(mock_connection)

    @pytest.mark.asyncio
    async def test_context_manager_read(self, rw_splitter):
        """Test read-write splitter as context manager for read operations."""
        with patch.object(
            rw_splitter, "acquire_connection", new_callable=AsyncMock
        ) as mock_acquire:
            mock_connection = AsyncMock()
            mock_pool = AsyncMock()
            mock_acquire.return_value = (mock_connection, mock_pool)

            async with rw_splitter.get_connection(read_only=True) as conn:
                assert conn == mock_connection

            mock_acquire.assert_called_once_with(True)
            mock_pool.release_connection.assert_called_once_with(mock_connection)

    @pytest.mark.asyncio
    async def test_concurrent_connections(self, rw_splitter):
        """Test handling of concurrent connection requests."""
        with patch.object(
            rw_splitter, "_acquire_read_connection", new_callable=AsyncMock
        ) as mock_acquire_read:
            mock_connections = [AsyncMock() for _ in range(5)]
            mock_pools = [AsyncMock() for _ in range(5)]
            mock_acquire_read.side_effect = [(conn, pool) for conn, pool in zip(mock_connections, mock_pools)]

            # Simulate concurrent read requests
            tasks = [rw_splitter.acquire_connection(read_only=True) for _ in range(5)]
            results = await asyncio.gather(*tasks)

            assert len(results) == 5
            # Each result should be a tuple of (connection, pool)
            connections = [result[0] for result in results]
            assert all(conn in mock_connections for conn in connections)

    def test_load_balancing_strategies(self, rw_config):
        """Test different load balancing strategies."""
        # DatabaseConfig already imported at top of file
        
        # Convert config dict to DatabaseConfig objects
        primary_config = DatabaseConfig(**rw_config["primary"])
        replica_configs = [DatabaseConfig(**replica) for replica in rw_config["replicas"]]
        
        # Test round-robin
        splitter_rr = ReadWriteSplitter(
            primary_config=primary_config,
            replica_configs=replica_configs,
            load_balancing_strategy=LoadBalancingStrategy.ROUND_ROBIN
        )
        assert splitter_rr.load_balancing_strategy == LoadBalancingStrategy.ROUND_ROBIN

        # Test random
        splitter_random = ReadWriteSplitter(
            primary_config=primary_config,
            replica_configs=replica_configs,
            load_balancing_strategy=LoadBalancingStrategy.RANDOM
        )
        assert splitter_random.load_balancing_strategy == LoadBalancingStrategy.RANDOM

        # Test least_connections
        splitter_lc = ReadWriteSplitter(
            primary_config=primary_config,
            replica_configs=replica_configs,
            load_balancing_strategy=LoadBalancingStrategy.LEAST_CONNECTIONS
        )
        assert splitter_lc.load_balancing_strategy == LoadBalancingStrategy.LEAST_CONNECTIONS

    @pytest.mark.asyncio
    async def test_cleanup_resources(self, rw_splitter):
        """Test proper cleanup of resources."""
        # Mock some active connections
        mock_primary_pool = AsyncMock()
        mock_replica_pools = [AsyncMock(), AsyncMock()]

        rw_splitter.primary_pool = mock_primary_pool
        rw_splitter.replica_pools = mock_replica_pools

        await rw_splitter.close()

        mock_primary_pool.close_pool.assert_called_once()
        for pool in mock_replica_pools:
            pool.close_pool.assert_called_once()
