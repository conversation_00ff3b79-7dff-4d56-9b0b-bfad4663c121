# 项目规则与标准 (Project Rules and Standards)

## 1. 项目结构标准 (Project Structure Standards)

## 2. 数据管理标准 (Data Management Standards)

### 2.1 版本控制 (Version Control)
- 使用Git进行代码管理，遵循GitFlow或GitHub Flow工作流
- 分支命名规范：
  - `main`/`master`: 稳定生产版本
  - `develop`: 开发集成分支
  - `feature/*`: 新功能开发
  - `bugfix/*`: 错误修复
  - `release/*`: 版本准备
  - `hotfix/*`: 紧急生产修复
- 提交消息格式：`<type>(<scope>): <subject>`
  - 类型：feat、fix、docs、style、refactor、test、chore

### 2.2 数据隐私与安全 (Data Privacy and Security)
- 敏感数据不得提交到版本控制系统
- 使用`.env`文件和环境变量管理敏感配置
- 实施数据访问控制和权限管理
- 定期进行安全审计和漏洞扫描
- 遵循相关数据保护法规和标准

### 2.3 数据备份 (Data Backup)
- 建立定期数据备份机制
- 实施增量和全量备份策略
- 定期测试数据恢复流程
- 异地备份关键数据

### 2.4 数据文档 (Data Documentation)
- 维护数据字典和数据血缘文档
- 记录数据来源、格式和处理流程
- 使用元数据管理系统跟踪数据变更
- 为每个数据集创建README文件

## 3. 环境管理 (Environment Management)

### 3.1 虚拟环境 (Virtual Environments)
- 使用conda或venv创建隔离的Python环境
- 为不同项目维护独立的环境
- 使用环境变量区分开发、测试和生产环境

### 3.2 依赖管理 (Dependency Management)
- 维护精确的`requirements.txt`或`environment.yml`
- 使用`pip-compile`或`conda-lock`锁定依赖版本
- 定期更新依赖包版本并检查安全漏洞
- 使用`pyproject.toml`和Poetry进行现代化依赖管理

## 4. 配置管理 (Configuration Management)

### 4.1 配置文件 (Configuration Files)
- 使用YAML格式的配置文件管理项目配置
- 敏感配置使用`.env`文件或环境变量
- 配置文件应包含详细注释

### 4.2 多环境支持 (Multi-environment Support)
- 区分开发、测试和生产环境配置
- 使用环境变量覆盖默认配置
- 实现配置验证机制

## 5. 数据分析工作流 (Data Analysis Workflow)

```python
# 标准数据分析工作流模板
def analyze_dataset(data_path: str, config: dict) -> dict:
    """
    标准数据分析工作流
    
    Args:
        data_path: 数据文件路径
        config: 分析配置参数
    
    Returns:
        分析结果字典
    """
    # 1. 数据加载和验证
    df = load_and_validate_data(data_path)
    
    # 2. 探索性数据分析
    eda_results = perform_eda(df)
    
    # 3. 数据清洗
    df_clean = clean_data(df, config['cleaning_rules'])
    
    # 4. 特征工程
    df_features = engineer_features(df_clean, config['feature_config'])
    
    # 5. 分析执行
    analysis_results = execute_analysis(df_features, config['analysis_type'])
    
    # 6. 结果输出
    save_results(analysis_results, config['output_path'])
    
    return analysis_results
```

### 5.1 数据处理标准 (Data Processing Standards)

#### 5.1.1 数据验证 (Data Validation)
- 始终执行空值检查、数据类型验证和范围检查
- 使用Great Expectations或Pandera进行数据验证
- 实现数据质量检查和监控

#### 5.1.2 Pandas最佳实践 (Pandas Best Practices)
- 优先使用`.loc`和`.iloc`进行数据索引
- 使用`.copy()`方法避免链式赋值警告
- 合理使用`pd.read_csv()`的参数(如dtype, parse_dates等)
- 处理大型数据集时使用`pd.read_csv(chunksize=)`或dask

#### 5.1.3 数据清洗流程 (Data Cleaning Process)
- 建立标准化的数据清洗流程
- 记录所有数据转换步骤
- 保留原始数据的副本

### 5.2 数据流模式 (Data Flow Patterns)
- 遵循三层架构模式：
  - 原始层(Raw/Bronze): 存储来自上游源的原始数据
  - 转换层(Transformed/Silver): 基于选定的建模原则转换数据
  - 消费层(Consumption/Gold): 组合转换层数据形成直接映射到最终用户用例的数据集
  - 接口层(Interface): 作为仓库表和消费者之间的接口

## 6. 代码质量检查规则 (Code Quality Check Rules)

### 6.1 代码格式化 (Code Formatting)
- 使用Black进行代码格式化
- 使用isort对导入进行排序
- 使用flake8检查代码风格和错误

### 6.2 类型检查 (Type Checking)
- 使用mypy进行静态类型检查
- 为所有函数参数和返回值添加类型注解
- 使用typing模块的高级类型

### 6.3 安全检查 (Security Checking)
- 使用bandit检查安全漏洞
- 定期更新依赖以修复已知漏洞
- 实施安全编码实践

### 6.4 复杂度检查 (Complexity Checking)
- 使用radon监控代码复杂度
- 限制函数的圈复杂度不超过10
- 限制函数长度不超过50行

### 6.5 代码覆盖率 (Code Coverage)
- 使用pytest-cov或coverage.py测量代码覆盖率
- 核心模块的测试覆盖率至少达到90%
- 非核心模块的测试覆盖率至少达到70%
- 整体项目的测试覆盖率至少达到80%

## 7. 数据可视化标准 (Data Visualization Standards)

### 7.1 一致性 (Consistency)
- 使用统一的配色方案和字体
- 创建可重用的可视化模板
- 标准化图表标题、轴标签和图例

### 7.2 可访问性 (Accessibility)
- 确保图表对色盲友好
- 使用足够的对比度
- 提供替代文本描述

### 7.3 交互性 (Interactivity)
- 使用plotly或bokeh创建交互式图表
- 实现筛选、缩放和悬停功能
- 支持响应式设计

### 7.4 导出标准 (Export Standards)
- 支持多种导出格式(PNG, SVG, PDF)
- 保持高分辨率和清晰度
- 包含必要的元数据

## 8. 模型开发标准 (Model Development Standards)

### 8.1 实验跟踪 (Experiment Tracking)
- 使用MLflow或Weights & Biases跟踪实验
- 记录所有超参数、指标和结果
- 保存模型工件和环境信息

### 8.2 模型版本管理 (Model Version Management)
- 建立模型版本控制机制
- 使用语义化版本号
- 记录模型变更和性能差异

### 8.3 模型验证 (Model Validation)
- 实施交叉验证和留出验证
- 使用多种评估指标
- 进行A/B测试

### 8.4 模型可解释性 (Model Explainability)
- 使用SHAP或LIME提供模型解释
- 生成特征重要性图表
- 记录模型决策逻辑

## 9. 部署与监控 (Deployment and Monitoring)

### 9.1 容器化 (Containerization)
- 使用Docker进行应用容器化
- 创建轻量级和安全的容器镜像
- 实施多阶段构建

### 9.2 API设计 (API Design)
- 遵循RESTful API设计原则
- 使用OpenAPI/Swagger进行API文档
- 实现适当的错误处理和状态码

### 9.3 监控告警 (Monitoring Alerts)
- 建立数据质量和模型性能监控
- 设置关键指标的告警阈值
- 实施自动恢复机制

### 9.4 文档维护 (Documentation Maintenance)
- 保持API文档和用户手册更新
- 记录系统架构和组件交互
- 提供故障排除指南

## 10. 推荐工具 (Recommended Tools)

### 10.1 核心库 (Essential Libraries)

## 11. IDE配置建议 (IDE Configuration Suggestions)

### 11.1 VS Code扩展 (VS Code Extensions)
- Python: 基本Python支持
- Pylance: 增强的语言服务器
- Jupyter: Jupyter笔记本支持
- Black Formatter: 自动格式化
- isort: 导入排序
- Python Test Explorer: 测试发现和执行
- GitLens: Git集成
- Docker: 容器支持

### 11.2 Cursor配置 (Cursor Configuration)
- 启用代码补全、类型检查和自动格式化
- 配置保存时自动格式化
- 启用智能提示和代码导航

### 11.3 调试配置 (Debug Configuration)
- 设置断点调试和变量监控
- 配置远程调试
- 启用性能分析工具

## 12. 性能优化工具 (Performance Optimization Tools)

### 12.1 性能分析 (Profiling)
- cProfile: 标准库性能分析器
- line_profiler: 行级性能分析
- memory_profiler: 内存使用分析
- py-spy: 采样分析器

### 12.2 并行计算 (Parallel Computing)
- multiprocessing: 多进程并行
- joblib: 并行任务执行
- dask: 分布式计算
- ray: 分布式框架

### 12.3 加速库 (Acceleration Libraries)
- numba: JIT编译器
- cython: C扩展
- pyarrow: 高效数据处理

## 13. 提交前检查 (Pre-commit Checks)

### 13.1 代码质量 (Code Quality)
- 代码遵循PEP 8标准
- 所有函数都有类型提示和文档字符串
- 没有安全漏洞和代码异味
- 性能符合要求

### 13.2 测试 (Testing)
- 所有单元测试通过
- 测试覆盖率达到目标
- 集成测试通过
- 没有回归问题

## 14. 数据分析检查 (Data Analysis Checks)

### 14.1 数据质量 (Data Quality)
- 数据源和质量已验证
- 已处理缺失值和异常值
- 数据分布已分析

### 14.2 分析方法 (Analysis Methods)
- 分析方法科学合理
- 结果可重现
- 假设已验证

### 14.3 可视化 (Visualizations)
- 可视化清晰有效
- 包含适当的标题和标签
- 配色方案一致

### 14.4 文档 (Documentation)
- 文档完整准确
- 包含方法说明和结果解释
- 提供代码示例

## 15. 语言标准 (Language Standards)
- 和我对话请使用中文
- 写代码或配置文件中必须使用英文，注释也需要使用英文，不允许出现中文
- 所有代码注释、文档和变量名使用英文
- 遵循国际化和本地化最佳实践

### Python 标准 (Python Standards)
- 使用 Python 3.12+
- 遵循 PEP 8 编码规范
- 使用类型提示 (Type Hints)
- 编写清晰的文档字符串
- 使用 f-string 进行字符串格式化

### SQL 标准 (SQL Standards)
- 使用参数化查询防止 SQL 注入
- 编写可读的 SQL 语句
- 使用适当的索引优化查询
- 实现事务管理
- 使用连接池管理数据库连接

## 地理空间数据处理标准 (Geospatial Data Processing Standards)

### 坐标参考系统 (Coordinate Reference Systems)
- 明确指定和验证 CRS
- 使用 EPSG 代码标识坐标系
- 在数据处理前确保 CRS 一致性
- 记录坐标转换过程

```python
import geopandas as gpd
from pyproj import CRS

def ensure_crs(gdf: gpd.GeoDataFrame, target_crs: str = "EPSG:4326") -> gpd.GeoDataFrame:
    """Ensure GeoDataFrame uses specified coordinate system."""
    if gdf.crs is None:
        raise ValueError("GeoDataFrame missing CRS information")
    
    if gdf.crs != target_crs:
        logger.info(f"Converting CRS from {gdf.crs} to {target_crs}")
        return gdf.to_crs(target_crs)
    return gdf
```

### 几何验证和处理 (Geometry Validation and Processing)
- 使用 `is_valid` 检查几何有效性
- 使用 `make_valid()` 修复无效几何
- 处理空几何和无效坐标
- 实现几何简化和缓冲区操作

```python
def validate_geometries(gdf: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
    """Validate and fix geometry objects."""
    # Check invalid geometries
    invalid_mask = ~gdf.geometry.is_valid
    if invalid_mask.any():
        logger.warning(f"Found {invalid_mask.sum()} invalid geometries")
        gdf.loc[invalid_mask, 'geometry'] = gdf.loc[invalid_mask, 'geometry'].apply(
            lambda geom: geom.buffer(0) if geom is not None else None
        )
    
    # Remove empty geometries
    gdf = gdf[~gdf.geometry.is_empty]
    return gdf
```

### 空间索引和性能优化 (Spatial Indexing and Performance)
- 使用空间索引加速查询
- 适当简化复杂几何
- 使用 `sjoin` 进行空间连接
- 实现分块处理大型数据集

```python
def spatial_join_optimized(left_gdf: gpd.GeoDataFrame, right_gdf: gpd.GeoDataFrame) -> gpd.GeoDataFrame:
    """Optimized spatial join operation."""
    # Ensure same CRS
    if left_gdf.crs != right_gdf.crs:
        right_gdf = right_gdf.to_crs(left_gdf.crs)
    
    # Use spatial index
    return gpd.sjoin(left_gdf, right_gdf, how='left', predicate='intersects')
```

## 异步编程标准 (Asynchronous Programming Standards)

### 异步函数设计 (Async Function Design)
- 优先使用异步 I/O 操作
- 正确使用 `async`/`await` 语法
- 实现适当的异常处理
- 使用异步上下文管理器

```python
import asyncio
import aiofiles
from contextlib import asynccontextmanager

@asynccontextmanager
async def get_db_connection():
    """Async database connection context manager."""
    conn = None
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        yield conn
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise
    finally:
        if conn:
            await conn.close()

async def process_file_async(file_path: str) -> Dict[str, Any]:
    """Async file processing."""
    async with aiofiles.open(file_path, 'r') as f:
        content = await f.read()
        # Process content
        return {"status": "success", "content_length": len(content)}
```

### 并发控制 (Concurrency Control)
- 使用信号量控制并发数量
- 实现适当的超时机制
- 避免竞态条件
- 使用队列管理任务

```python
async def process_files_concurrently(file_paths: List[str], max_concurrent: int = 5) -> List[Dict[str, Any]]:
    """Process multiple files concurrently."""
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def process_with_semaphore(file_path: str) -> Dict[str, Any]:
        async with semaphore:
            return await process_file_async(file_path)
    
    tasks = [process_with_semaphore(path) for path in file_paths]
    return await asyncio.gather(*tasks, return_exceptions=True)
```

### 错误处理和重试 (Error Handling and Retry)
- 实现指数退避重试机制
- 区分可重试和不可重试错误
- 记录详细的错误信息
- 实现断路器模式

```python
import asyncio
from typing import Callable, Any

async def retry_with_backoff(
    func: Callable,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0
) -> Any:
    """Retry mechanism with exponential backoff."""
    for attempt in range(max_retries + 1):
        try:
            return await func()
        except Exception as e:
            if attempt == max_retries:
                logger.error(f"Failed after {max_retries} retries: {e}")
                raise
            
            delay = min(base_delay * (backoff_factor ** attempt), max_delay)
            logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay:.2f}s: {e}")
            await asyncio.sleep(delay)
```

## QGIS 集成标准 (QGIS Integration Standards)

### 环境隔离 (Environment Isolation)
- 使用独立的 QGIS 环境
- 避免与主 Python 环境冲突
- 正确配置 QGIS Python 路径
- 实现环境检测和切换

```python
import os
import sys
from pathlib import Path

def setup_qgis_environment():
    """Setup QGIS environment."""
    qgis_path = Path("C:/Program Files/QGIS 3.30/apps/qgis")
    if not qgis_path.exists():
        raise EnvironmentError("QGIS not installed or incorrect path")
    
    # Add QGIS Python paths
    sys.path.insert(0, str(qgis_path / "python"))
    sys.path.insert(0, str(qgis_path / "python/plugins"))
    
    # Set environment variables
    os.environ['QGIS_PREFIX_PATH'] = str(qgis_path)
    os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = str(qgis_path / "qtplugins")
```

### QGIS 处理算法 (QGIS Processing Algorithms)
- 使用 QGIS 处理框架
- 实现批处理操作
- 正确处理临时文件
- 实现进度监控

```python
from qgis.core import QgsApplication, QgsProcessingFeedback
from qgis.analysis import QgsNativeAlgorithms
import processing

class QGISProcessor:
    """QGIS processor class."""
    
    def __init__(self):
        self.qgs = None
        self._initialize_qgis()
    
    def _initialize_qgis(self):
        """Initialize QGIS application."""
        QgsApplication.setPrefixPath(os.environ.get('QGIS_PREFIX_PATH'), True)
        self.qgs = QgsApplication([], False)
        self.qgs.initQgis()
        
        # Initialize processing algorithms
        from processing.core.Processing import Processing
        Processing.initialize()
    
    def buffer_analysis(self, input_layer: str, distance: float, output_path: str) -> str:
        """Buffer analysis."""
        feedback = QgsProcessingFeedback()
        
        result = processing.run("native:buffer", {
            'INPUT': input_layer,
            'DISTANCE': distance,
            'SEGMENTS': 5,
            'END_CAP_STYLE': 0,
            'JOIN_STYLE': 0,
            'MITER_LIMIT': 2,
            'DISSOLVE': False,
            'OUTPUT': output_path
        }, feedback=feedback)
        
        return result['OUTPUT']
    
    def cleanup(self):
        """Clean up QGIS resources."""
        if self.qgs:
            self.qgs.exitQgis()
```

### 插件开发标准 (Plugin Development Standards)
- 遵循 QGIS 插件开发规范
- 实现适当的用户界面
- 提供多语言支持
- 实现错误处理和日志记录

```python
from qgis.PyQt.QtCore import QSettings, QTranslator, QCoreApplication
from qgis.PyQt.QtWidgets import QAction, QMessageBox
from qgis.core import QgsProject, QgsMessageLog, Qgis

class ConnectPlugin:
    """Connect QGIS plugin."""
    
    def __init__(self, iface):
        self.iface = iface
        self.plugin_dir = os.path.dirname(__file__)
        
        # Initialize translation
        locale = QSettings().value('locale/userLocale')[0:2]
        locale_path = os.path.join(
            self.plugin_dir,
            'i18n',
            f'connect_{locale}.qm'
        )
        
        if os.path.exists(locale_path):
            self.translator = QTranslator()
            self.translator.load(locale_path)
            QCoreApplication.installTranslator(self.translator)
    
    def initGui(self):
        """Initialize plugin GUI."""
        self.action = QAction(
            self.tr('Connect Data Processor'),
            self.iface.mainWindow()
        )
        self.action.triggered.connect(self.run)
        self.iface.addToolBarIcon(self.action)
    
    def unload(self):
        """Unload plugin."""
        self.iface.removeToolBarIcon(self.action)
    
    def run(self):
        """Run plugin main functionality."""
        try:
            # Plugin main logic
            self.process_data()
        except Exception as e:
            QgsMessageLog.logMessage(
                f"Plugin runtime error: {str(e)}",
                'Connect Plugin',
                Qgis.Critical
            )
            QMessageBox.critical(
                self.iface.mainWindow(),
                self.tr('Error'),
                self.tr(f'Error occurred during processing: {str(e)}')
            )
    
    def tr(self, message):
        """Translate message."""
        return QCoreApplication.translate('ConnectPlugin', message)
```

## 16. 命名标准 (Naming Standards)

### 16.1 命名约定 (Naming Conventions)
- 类名：使用大驼峰命名法（如DataProcessor）
- 函数名：使用下划线命名法（如process_data）
- 变量名：使用下划线命名法（如data_frame）
- 常量名：使用全大写下划线命名法（如MAX_ITERATIONS）
- 文件名：使用下划线命名法（如data_processor.py）
- 模块名：使用小写字母，可包含下划线（如data_processing）

### 16.2 命名原则 (Naming Principles)
- 新创建的文件、模块、变量、表、类、列等必须严格遵循对称性、美观性和简洁性标准
- 输出文件、列、类、表、变量、模块也必须严格遵循对称性、美观性和简洁性标准
- 名称应当具有描述性，清晰表达其用途和内容
- 避免使用缩写，除非是广泛接受的缩写（如URL, HTTP）

## 17. 错误处理标准 (Error Handling Standards)

### 17.1 异常处理策略 (Exception Handling Strategy)
- 使用具体的异常类型而非通用Exception
- 只捕获预期的异常
- 提供有意义的错误消息
- 记录异常信息到日志

### 17.2 自定义异常类 (Custom Exception Classes)
- 为特定错误场景创建自定义异常类
- 继承自适当的基础异常类
- 提供详细的错误上下文

## 18. 日志标准 (Logging Standards)

### 18.1 日志级别使用 (Log Level Usage)
- DEBUG：详细的调试信息
- INFO：一般信息性消息
- WARNING：警告但不影响功能
- ERROR：错误导致功能失败
- CRITICAL：严重错误影响系统稳定性

### 18.2 日志格式 (Log Format)
- 时间戳（ISO 8601格式）
- 日志级别
- 模块/函数名
- 线程ID（多线程环境）
- 消息内容

## 19. 测试标准 (Testing Standards)

### 19.1 测试覆盖率要求 (Test Coverage Requirements)
- 核心模块：至少90%
- 非核心模块：至少70%
- 整体项目：至少80%

### 19.2 测试类型 (Test Types)
- 单元测试：测试单个函数/类
- 集成测试：测试多个组件交互
- 系统测试：测试整个系统功能
- 性能测试：测试系统性能和响应时间

## 20. 持续集成/持续部署 (CI/CD)

### 20.1 CI流程 (CI Pipeline)
- 代码提交触发自动构建
- 运行所有测试和代码质量检查
- 生成覆盖率和质量报告
- 构建容器镜像

### 20.2 CD流程 (CD Pipeline)
- 自动部署到测试环境
- 运行集成和系统测试
- 经批准后部署到生产环境
- 实施蓝绿部署或金丝雀发布

### 20.3 质量门禁 (Quality Gates)
- 定义明确的质量标准和阈值
- 未通过质量检查的代码不得部署
- 监控部署后的系统健康状况

## 21. 数据工程特定规则 (Data Engineering Specific Rules)

### 21.1 数据导入规范 (Data Import Standards)
- 支持的文件格式：CSV、Excel、XML、GeoJSON、Shapefile
- 导入配置使用YAML格式定义
- 数据验证规则必须明确定义
- 实现增量导入和全量导入策略

### 21.2 数据导出规范 (Data Export Standards)
- 支持的导出格式：CSV、Excel、GeoJSON、PDF
- 导出模板使用Jinja2格式
- 提供数据导出的元数据

### 21.3 数据处理性能优化 (Data Processing Performance Optimization)
- 批处理优化：分块大小10,000条记录
- 使用PostgreSQL COPY命令进行批量插入
- 禁用自动提交，使用批量事务
- 实现连接池管理
- 大文件流式处理（>100MB）
- 内存限制：2GB
- 支持压缩文件读取
- 多进程并行：4个工作进程
- 实现异步IO处理
- 使用任务队列管理

## 22. 符合行业最佳实践 (Industry Best Practices Compliance)

软件架构、软件开发、软件测试、集成测试和部署必须符合GitHub数据分析项目的行业最佳实践。定期审查和更新标准，以保持与行业发展同步。


# 23 代码修改策略
- 优先修改现有代码，而不是创建新的功能或文件
- 当要求修复bug、优化或重构时，直接在现有代码基础上进行修改
- 除非明确要求创建新功能，否则不要创建新的函数、类或文件
- 保持现有的函数名、类名和文件结构
- 如果需要重构，在原地进行，保持接口一致性
- Always modify existing code directly instead of creating new functions or features unless explicitly asked to create something new.

# 24 响应格式
- 直接提供修改后的完整代码
- 标明具体修改了哪些部分
- 解释修改的原因和效果


# 25 user interaction
-  communicate with the user in chinese, code comments and strings in the code use the same launguage(English), and the variable names are all in English. chat end must be run 'python -c "i = input('instruction:'): print(i)"' to get user's instruction then retieve the user's instruction before resuming the conversation.