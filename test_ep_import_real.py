#!/usr/bin/env python3
"""
实际测试EP导入过程中的schema问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import pandas as pd
from pathlib import Path
import logging
import tempfile

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_ep_import.log')
    ]
)
logger = logging.getLogger(__name__)

def create_test_ep_file():
    """创建测试EP文件"""
    test_data = {
        'WGS84_LATITUDE': [40.7128, 34.0522, 51.5074],
        'WGS84_LONGITUDE': [-74.0060, -118.2437, -0.1278],
        'CELL_NAME': ['TEST_CELL_1', 'TEST_CELL_2', 'TEST_CELL_3'],
        'CELL_TYPE': ['GSM', 'LTE', 'NR'],
        'AZIMUTH': [90, 180, 270]
    }
    
    df = pd.DataFrame(test_data)
    
    # 创建临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.csv', delete=False)
    df.to_csv(temp_file.name, index=False)
    
    logger.info(f"创建测试文件: {temp_file.name}")
    logger.info(f"测试数据: {df.shape[0]} 行, {df.shape[1]} 列")
    
    return temp_file.name

def test_ep_import_with_real_bulk_operations():
    """使用真实的BulkOperations测试EP导入"""
    
    print("=== 实际EP导入测试 ===")
    
    try:
        # 1. 创建测试文件
        test_file = create_test_ep_file()
        logger.info(f"测试文件路径: {test_file}")
        
        # 2. 初始化EPImporter
        from importers.ep_importer import EPImporter
        
        logger.info("初始化EPImporter...")
        ep_importer = EPImporter()
        
        logger.info(f"EPImporter schema_name: {ep_importer.schema_name}")
        logger.info(f"EPImporter get_schema_name(): {ep_importer.get_schema_name()}")
        
        # 3. 读取测试数据
        df = pd.read_csv(test_file)
        logger.info(f"读取数据: {df.shape}")
        
        # 4. 生成表名
        table_name = ep_importer.get_table_name(test_file)
        logger.info(f"生成表名: {table_name}")
        
        # 5. 获取schema名称
        schema_name = ep_importer.get_schema_name()
        logger.info(f"Schema名称: '{schema_name}' (type: {type(schema_name)})")
        
        # 6. 检查BulkOperations
        if hasattr(ep_importer, 'bulk_operations') and ep_importer.bulk_operations:
            bulk_ops = ep_importer.bulk_operations
            logger.info(f"使用现有BulkOperations: {bulk_ops}")
        else:
            logger.info("创建新的BulkOperations实例")
            from database.operations.bulk_operations import BulkOperations
            bulk_ops = BulkOperations(None)
        
        # 7. 模拟bulk_insert_dataframe调用
        logger.info("\n=== 模拟bulk_insert_dataframe调用 ===")
        logger.info(f"传入参数:")
        logger.info(f"  - df.shape: {df.shape}")
        logger.info(f"  - table_name: '{table_name}'")
        logger.info(f"  - schema: '{schema_name}'")
        logger.info(f"  - schema is None: {schema_name is None}")
        logger.info(f"  - schema == '': {schema_name == ''}")
        
        # 8. 模拟_simplified_bulk_insert中的schema处理
        logger.info("\n=== 模拟_simplified_bulk_insert中的schema处理 ===")
        
        # 这是bulk_operations.py第68行的逻辑
        original_schema = schema_name
        if schema_name is None:
            final_schema = 'public'
            logger.warning(f"Schema为None，设置为public: {original_schema} -> {final_schema}")
        else:
            final_schema = schema_name
            logger.info(f"Schema不为None，保持原值: {final_schema}")
        
        logger.info(f"最终使用的schema: '{final_schema}'")
        
        # 9. 检查可能的问题
        logger.info("\n=== 检查可能的问题 ===")
        
        # 检查data_source_config
        logger.info(f"data_source_config: {ep_importer.data_source_config}")
        
        # 检查schema_name属性
        logger.info(f"ep_importer.schema_name: '{ep_importer.schema_name}'")
        
        # 检查是否有其他地方可能修改schema
        if hasattr(ep_importer, '_original_schema_name'):
            logger.info(f"_original_schema_name: {ep_importer._original_schema_name}")
        
        # 10. 尝试实际调用（如果可能）
        logger.info("\n=== 尝试实际调用bulk_insert_dataframe ===")
        
        try:
            # 注意：这可能会失败，因为没有真实的数据库连接
            result = bulk_ops.bulk_insert_dataframe(
                df=df,
                table_name=table_name,
                schema=schema_name,
                if_exists='append'
            )
            logger.info(f"bulk_insert_dataframe结果: {result}")
        except Exception as e:
            logger.warning(f"bulk_insert_dataframe调用失败（预期）: {e}")
            logger.info("这是正常的，因为没有真实的数据库连接")
        
        # 清理临时文件
        os.unlink(test_file)
        logger.info(f"清理临时文件: {test_file}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

def test_schema_parameter_tracing():
    """追踪schema参数在整个调用链中的变化"""
    
    print("\n=== Schema参数追踪测试 ===")
    
    try:
        from importers.ep_importer import EPImporter
        
        # 创建EPImporter实例
        ep_importer = EPImporter()
        
        # 追踪schema参数的来源
        logger.info("\n=== Schema参数来源追踪 ===")
        
        # 1. 检查data_source_config
        logger.info(f"1. data_source_config: {ep_importer.data_source_config}")
        schema_from_config = ep_importer.data_source_config.get('schema_name')
        logger.info(f"   从config获取的schema_name: '{schema_from_config}'")
        
        # 2. 检查默认值
        default_schema = ep_importer.data_source_config.get('schema_name', 'ep_to2')
        logger.info(f"   带默认值的schema_name: '{default_schema}'")
        
        # 3. 检查实例属性
        logger.info(f"2. ep_importer.schema_name: '{ep_importer.schema_name}'")
        
        # 4. 检查get_schema_name()方法
        schema_from_method = ep_importer.get_schema_name()
        logger.info(f"3. get_schema_name()返回: '{schema_from_method}'")
        
        # 5. 检查类型和值
        logger.info(f"\n=== 类型和值检查 ===")
        logger.info(f"schema_name类型: {type(ep_importer.schema_name)}")
        logger.info(f"schema_name值: '{ep_importer.schema_name}'")
        logger.info(f"schema_name是否为None: {ep_importer.schema_name is None}")
        logger.info(f"schema_name是否为空字符串: {ep_importer.schema_name == ''}")
        logger.info(f"schema_name布尔值: {bool(ep_importer.schema_name)}")
        
        # 6. 检查可能的修改点
        logger.info(f"\n=== 可能的修改点检查 ===")
        
        # 检查是否有其他属性可能影响schema
        for attr in dir(ep_importer):
            if 'schema' in attr.lower():
                value = getattr(ep_importer, attr, 'NOT_ACCESSIBLE')
                if not callable(value):
                    logger.info(f"属性 {attr}: {value}")
        
    except Exception as e:
        logger.error(f"Schema参数追踪失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_ep_import_with_real_bulk_operations()
    test_schema_parameter_tracing()
    
    print("\n=== 测试完成 ===")
    print("请检查 test_ep_import.log 文件获取详细日志")