"""Unit tests for schema management components."""

import asyncio
from datetime import datetime
from typing import Any, Dict, List
from unittest.mock import Async<PERSON>ock, <PERSON>Mock, Mock, patch

import pytest

from src.config.models import DatabaseConfig
from src.database.exceptions import SchemaError, ValidationError

# Import components to test
from src.database.schema.manager import SchemaManager
from src.database.schema import TableSchema, ColumnSchema
from src.database.schema.validators import SchemaValidator


class TestTableSchema:
    """Test cases for TableSchema model."""

    def test_table_schema_creation(self):
        """Test creating a table schema."""
        columns = [
            ColumnSchema(name="id", data_type="INTEGER", primary_key=True),
            ColumnSchema(name="name", data_type="VARCHAR(255)", nullable=False),
            ColumnSchema(name="created_at", data_type="TIMESTAMP", default="NOW()"),
        ]

        table = TableSchema(name="users", columns=columns, schema="public")

        assert table.name == "users"
        assert table.schema == "public"
        assert len(table.columns) == 3
        assert table.get_column("id").primary_key is True
        assert table.get_column("name").nullable is False

    def test_table_schema_validation(self):
        """Test table schema validation."""
        # Valid schema
        columns = [ColumnSchema(name="id", data_type="INTEGER", primary_key=True)]
        table = TableSchema(name="test_table", columns=columns)
        assert table.is_valid()

        # Invalid schema - no primary key
        columns_no_pk = [ColumnSchema(name="name", data_type="VARCHAR(255)")]
        table_no_pk = TableSchema(name="test_table", columns=columns_no_pk)
        assert not table_no_pk.is_valid()

    def test_column_operations(self):
        """Test column operations on table schema."""
        table = TableSchema(name="test_table", columns=[])

        # Add column
        column = ColumnSchema(name="id", data_type="INTEGER", primary_key=True)
        table.add_column(column)
        assert len(table.columns) == 1
        assert table.has_column("id")

        # Remove column
        table.remove_column("id")
        assert len(table.columns) == 0
        assert not table.has_column("id")

    def test_index_operations(self):
        """Test index operations on table schema."""
        table = TableSchema(name="test_table", columns=[])

        # Add index
        index = IndexSchema(name="idx_name", columns=["name"], unique=True)
        table.add_index(index)
        assert len(table.indexes) == 1
        assert table.has_index("idx_name")

        # Remove index
        table.remove_index("idx_name")
        assert len(table.indexes) == 0
        assert not table.has_index("idx_name")

    def test_table_schema_comparison(self):
        """Test comparing table schemas."""
        columns1 = [ColumnSchema(name="id", data_type="INTEGER", primary_key=True)]
        columns2 = [ColumnSchema(name="id", data_type="INTEGER", primary_key=True)]
        columns3 = [ColumnSchema(name="id", data_type="BIGINT", primary_key=True)]

        table1 = TableSchema(name="test", columns=columns1)
        table2 = TableSchema(name="test", columns=columns2)
        table3 = TableSchema(name="test", columns=columns3)

        assert table1.equals(table2)
        assert not table1.equals(table3)

    def test_table_schema_sql_generation(self):
        """Test SQL generation from table schema."""
        columns = [
            ColumnSchema(name="id", data_type="INTEGER", primary_key=True),
            ColumnSchema(name="name", data_type="VARCHAR(255)", nullable=False),
            ColumnSchema(name="email", data_type="VARCHAR(255)", unique=True),
        ]

        table = TableSchema(name="users", columns=columns)
        sql = table.to_sql()

        assert "CREATE TABLE users" in sql
        assert "id INTEGER PRIMARY KEY" in sql
        assert "name VARCHAR(255) NOT NULL" in sql
        assert "email VARCHAR(255) UNIQUE" in sql


class TestColumnSchema:
    """Test cases for ColumnSchema model."""

    def test_column_schema_creation(self):
        """Test creating a column schema."""
        column = ColumnSchema(
            name="user_id",
            data_type="INTEGER",
            nullable=False,
            primary_key=True,
            foreign_key="users.id",
            default=None,
            unique=False,
        )

        assert column.name == "user_id"
        assert column.data_type == "INTEGER"
        assert column.nullable is False
        assert column.primary_key is True
        assert column.foreign_key == "users.id"
        assert column.unique is False

    def test_column_validation(self):
        """Test column validation."""
        # Valid column
        valid_column = ColumnSchema(name="id", data_type="INTEGER")
        assert valid_column.is_valid()

        # Invalid column - empty name
        invalid_column = ColumnSchema(name="", data_type="INTEGER")
        assert not invalid_column.is_valid()

        # Invalid column - empty data type
        invalid_column2 = ColumnSchema(name="id", data_type="")
        assert not invalid_column2.is_valid()

    def test_column_sql_generation(self):
        """Test SQL generation for column."""
        column = ColumnSchema(
            name="email",
            data_type="VARCHAR(255)",
            nullable=False,
            unique=True,
            default="''",
        )

        sql = column.to_sql()
        assert "email VARCHAR(255)" in sql
        assert "NOT NULL" in sql
        assert "UNIQUE" in sql
        assert "DEFAULT ''" in sql

    def test_column_comparison(self):
        """Test comparing columns."""
        column1 = ColumnSchema(name="id", data_type="INTEGER", primary_key=True)
        column2 = ColumnSchema(name="id", data_type="INTEGER", primary_key=True)
        column3 = ColumnSchema(name="id", data_type="BIGINT", primary_key=True)

        assert column1.equals(column2)
        assert not column1.equals(column3)


class TestIndexSchema:
    """Test cases for IndexSchema model."""

    def test_index_schema_creation(self):
        """Test creating an index schema."""
        index = IndexSchema(
            name="idx_user_email", columns=["email"], unique=True, method="btree"
        )

        assert index.name == "idx_user_email"
        assert index.columns == ["email"]
        assert index.unique is True
        assert index.method == "btree"

    def test_composite_index(self):
        """Test creating composite index."""
        index = IndexSchema(
            name="idx_user_name_email",
            columns=["last_name", "first_name", "email"],
            unique=False,
        )

        assert len(index.columns) == 3
        assert "last_name" in index.columns
        assert "first_name" in index.columns
        assert "email" in index.columns

    def test_index_sql_generation(self):
        """Test SQL generation for index."""
        index = IndexSchema(name="idx_user_email", columns=["email"], unique=True)

        sql = index.to_sql("users")
        assert "CREATE UNIQUE INDEX idx_user_email" in sql
        assert "ON users (email)" in sql

    def test_index_validation(self):
        """Test index validation."""
        # Valid index
        valid_index = IndexSchema(name="idx_test", columns=["col1"])
        assert valid_index.is_valid()

        # Invalid index - no columns
        invalid_index = IndexSchema(name="idx_test", columns=[])
        assert not invalid_index.is_valid()

        # Invalid index - empty name
        invalid_index2 = IndexSchema(name="", columns=["col1"])
        assert not invalid_index2.is_valid()


class TestSchemaManager:
    """Test cases for SchemaManager class."""

    @pytest.fixture
    def mock_connection(self):
        """Create mock database connection."""
        connection = AsyncMock()
        connection.fetch.return_value = []
        connection.execute.return_value = None
        return connection

    @pytest.fixture
    def schema_manager(self, mock_connection):
        """Create SchemaManager instance."""
        return SchemaManager(mock_connection)

    @pytest.mark.asyncio
    async def test_get_table_schema(self, schema_manager, mock_connection):
        """Test retrieving table schema from database."""
        # Mock database response
        mock_connection.fetch.return_value = [
            {
                "column_name": "id",
                "data_type": "integer",
                "is_nullable": "NO",
                "column_default": None,
                "is_primary_key": True,
            },
            {
                "column_name": "name",
                "data_type": "character varying",
                "is_nullable": "NO",
                "column_default": None,
                "is_primary_key": False,
            },
        ]

        schema = await schema_manager.get_table_schema("users")

        assert schema.name == "users"
        assert len(schema.columns) == 2
        assert schema.get_column("id").primary_key is True
        assert schema.get_column("name").nullable is False

    @pytest.mark.asyncio
    async def test_create_table(self, schema_manager, mock_connection):
        """Test creating table from schema."""
        columns = [
            ColumnSchema(name="id", data_type="INTEGER", primary_key=True),
            ColumnSchema(name="name", data_type="VARCHAR(255)", nullable=False),
        ]
        table_schema = TableSchema(name="test_table", columns=columns)

        await schema_manager.create_table(table_schema)

        mock_connection.execute.assert_called_once()
        call_args = mock_connection.execute.call_args[0][0]
        assert "CREATE TABLE test_table" in call_args

    @pytest.mark.asyncio
    async def test_drop_table(self, schema_manager, mock_connection):
        """Test dropping table."""
        await schema_manager.drop_table("test_table")

        mock_connection.execute.assert_called_once_with(
            "DROP TABLE IF EXISTS test_table CASCADE"
        )

    @pytest.mark.asyncio
    async def test_table_exists(self, schema_manager, mock_connection):
        """Test checking if table exists."""
        mock_connection.fetchval.return_value = 1

        exists = await schema_manager.table_exists("users", "public")

        assert exists is True
        mock_connection.fetchval.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_all_tables(self, schema_manager, mock_connection):
        """Test getting all tables in schema."""
        mock_connection.fetch.return_value = [
            {"table_name": "users"},
            {"table_name": "orders"},
            {"table_name": "products"},
        ]

        tables = await schema_manager.get_all_tables()

        assert len(tables) == 3
        assert "users" in tables
        assert "orders" in tables
        assert "products" in tables

    @pytest.mark.asyncio
    async def test_add_column(self, schema_manager, mock_connection):
        """Test adding column to existing table."""
        column = ColumnSchema(name="email", data_type="VARCHAR(255)", unique=True)

        await schema_manager.add_column("users", column)

        mock_connection.execute.assert_called_once()
        call_args = mock_connection.execute.call_args[0][0]
        assert "ALTER TABLE users ADD COLUMN" in call_args
        assert "email VARCHAR(255)" in call_args

    @pytest.mark.asyncio
    async def test_drop_column(self, schema_manager, mock_connection):
        """Test dropping column from table."""
        await schema_manager.drop_column("users", "email")

        mock_connection.execute.assert_called_once_with(
            "ALTER TABLE users DROP COLUMN email"
        )

    @pytest.mark.asyncio
    async def test_create_index(self, schema_manager, mock_connection):
        """Test creating index on table."""
        index = IndexSchema(name="idx_user_email", columns=["email"], unique=True)

        await schema_manager.create_index("users", index)

        mock_connection.execute.assert_called_once()
        call_args = mock_connection.execute.call_args[0][0]
        assert "CREATE UNIQUE INDEX idx_user_email" in call_args
        assert "ON users (email)" in call_args

    @pytest.mark.asyncio
    async def test_drop_index(self, schema_manager, mock_connection):
        """Test dropping index."""
        await schema_manager.drop_index("idx_user_email")

        mock_connection.execute.assert_called_once_with(
            "DROP INDEX IF EXISTS idx_user_email"
        )


class TestSchemaValidator:
    """Test cases for SchemaValidator class."""

    @pytest.fixture
    def validator(self):
        """Create SchemaValidator instance."""
        return SchemaValidator()

    def test_validate_table_name(self, validator):
        """Test table name validation."""
        # Valid names
        assert validator.validate_table_name("users")
        assert validator.validate_table_name("user_profiles")
        assert validator.validate_table_name("table123")

        # Invalid names
        assert not validator.validate_table_name("123table")  # starts with number
        assert not validator.validate_table_name("user-profiles")  # contains hyphen
        assert not validator.validate_table_name("select")  # reserved keyword
        assert not validator.validate_table_name("")  # empty

    def test_validate_column_name(self, validator):
        """Test column name validation."""
        # Valid names
        assert validator.validate_column_name("id")
        assert validator.validate_column_name("user_id")
        assert validator.validate_column_name("created_at")

        # Invalid names
        assert not validator.validate_column_name("123id")
        assert not validator.validate_column_name("user-id")
        assert not validator.validate_column_name("where")
        assert not validator.validate_column_name("")

    def test_validate_data_type(self, validator):
        """Test data type validation."""
        # Valid types
        assert validator.validate_data_type("INTEGER")
        assert validator.validate_data_type("VARCHAR(255)")
        assert validator.validate_data_type("TIMESTAMP")
        assert validator.validate_data_type("DECIMAL(10,2)")

        # Invalid types
        assert not validator.validate_data_type("INVALID_TYPE")
        assert not validator.validate_data_type("VARCHAR()")
        assert not validator.validate_data_type("")

    def test_validate_table_schema(self, validator):
        """Test complete table schema validation."""
        # Valid schema
        valid_columns = [
            ColumnSchema(name="id", data_type="INTEGER", primary_key=True),
            ColumnSchema(name="name", data_type="VARCHAR(255)", nullable=False),
        ]
        valid_table = TableSchema(name="users", columns=valid_columns)

        result = validator.validate_table_schema(valid_table)
        assert result.is_valid
        assert len(result.errors) == 0

        # Invalid schema - no primary key
        invalid_columns = [ColumnSchema(name="name", data_type="VARCHAR(255)")]
        invalid_table = TableSchema(name="users", columns=invalid_columns)

        result = validator.validate_table_schema(invalid_table)
        assert not result.is_valid
        assert len(result.errors) > 0
        assert any("primary key" in error.lower() for error in result.errors)

    def test_validate_foreign_key_reference(self, validator):
        """Test foreign key reference validation."""
        # Valid reference
        assert validator.validate_foreign_key_reference("users.id")
        assert validator.validate_foreign_key_reference("public.users.id")

        # Invalid reference
        assert not validator.validate_foreign_key_reference("invalid_reference")
        assert not validator.validate_foreign_key_reference("")
        assert not validator.validate_foreign_key_reference("users.")

    def test_validate_index_schema(self, validator):
        """Test index schema validation."""
        # Valid index
        valid_index = IndexSchema(name="idx_user_email", columns=["email"])
        result = validator.validate_index_schema(valid_index)
        assert result.is_valid

        # Invalid index - no columns
        invalid_index = IndexSchema(name="idx_test", columns=[])
        result = validator.validate_index_schema(invalid_index)
        assert not result.is_valid
        assert len(result.errors) > 0


class TestSchemaMigrator:
    """Test cases for SchemaMigrator class."""

    @pytest.fixture
    def mock_connection(self):
        """Create mock database connection."""
        connection = AsyncMock()
        return connection

    @pytest.fixture
    def migrator(self, mock_connection):
        """Create SchemaMigrator instance."""
        return SchemaMigrator(mock_connection)

    @pytest.mark.asyncio
    async def test_compare_schemas(self, migrator):
        """Test comparing two table schemas."""
        # Original schema
        original_columns = [
            ColumnSchema(name="id", data_type="INTEGER", primary_key=True),
            ColumnSchema(name="name", data_type="VARCHAR(255)"),
        ]
        original_schema = TableSchema(name="users", columns=original_columns)

        # New schema with additional column
        new_columns = [
            ColumnSchema(name="id", data_type="INTEGER", primary_key=True),
            ColumnSchema(name="name", data_type="VARCHAR(255)"),
            ColumnSchema(name="email", data_type="VARCHAR(255)", unique=True),
        ]
        new_schema = TableSchema(name="users", columns=new_columns)

        diff = migrator.compare_schemas(original_schema, new_schema)

        assert len(diff.added_columns) == 1
        assert diff.added_columns[0].name == "email"
        assert len(diff.removed_columns) == 0
        assert len(diff.modified_columns) == 0

    @pytest.mark.asyncio
    async def test_generate_migration_sql(self, migrator):
        """Test generating migration SQL."""
        # Schema with changes
        original_columns = [
            ColumnSchema(name="id", data_type="INTEGER", primary_key=True)
        ]
        original_schema = TableSchema(name="users", columns=original_columns)

        new_columns = [
            ColumnSchema(name="id", data_type="INTEGER", primary_key=True),
            ColumnSchema(name="email", data_type="VARCHAR(255)"),
        ]
        new_schema = TableSchema(name="users", columns=new_columns)

        sql_statements = migrator.generate_migration_sql(original_schema, new_schema)

        assert len(sql_statements) > 0
        assert any("ADD COLUMN" in stmt for stmt in sql_statements)
        assert any("email" in stmt for stmt in sql_statements)

    @pytest.mark.asyncio
    async def test_apply_migration(self, migrator, mock_connection):
        """Test applying migration to database."""
        migration_sql = [
            "ALTER TABLE users ADD COLUMN email VARCHAR(255)",
            "CREATE INDEX idx_user_email ON users (email)",
        ]

        await migrator.apply_migration(migration_sql)

        assert mock_connection.execute.call_count == len(migration_sql)
        for i, sql in enumerate(migration_sql):
            assert mock_connection.execute.call_args_list[i][0][0] == sql

    @pytest.mark.asyncio
    async def test_rollback_migration(self, migrator, mock_connection):
        """Test rolling back migration."""
        rollback_sql = [
            "DROP INDEX IF EXISTS idx_user_email",
            "ALTER TABLE users DROP COLUMN email",
        ]

        await migrator.rollback_migration(rollback_sql)

        assert mock_connection.execute.call_count == len(rollback_sql)
        for i, sql in enumerate(rollback_sql):
            assert mock_connection.execute.call_args_list[i][0][0] == sql

    def test_migration_versioning(self, migrator):
        """Test migration version tracking."""
        # Test version comparison
        assert migrator.compare_versions("1.0.0", "1.0.1") < 0
        assert migrator.compare_versions("1.1.0", "1.0.1") > 0
        assert migrator.compare_versions("1.0.0", "1.0.0") == 0

        # Test version validation
        assert migrator.is_valid_version("1.0.0")
        assert migrator.is_valid_version("10.5.2")
        assert not migrator.is_valid_version("invalid")
        assert not migrator.is_valid_version("1.0")

    @pytest.mark.asyncio
    async def test_backup_before_migration(self, migrator, mock_connection):
        """Test creating backup before migration."""
        table_name = "users"
        backup_name = f"{table_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        await migrator.create_backup(table_name)

        mock_connection.execute.assert_called_once()
        call_args = mock_connection.execute.call_args[0][0]
        assert "CREATE TABLE" in call_args
        assert table_name in call_args
        assert "backup" in call_args.lower()

    @pytest.mark.asyncio
    async def test_migration_transaction(self, migrator, mock_connection):
        """Test migration within transaction."""
        migration_sql = [
            "ALTER TABLE users ADD COLUMN email VARCHAR(255)",
            "UPDATE users SET email = '<EMAIL>'",
        ]

        # Mock transaction context
        mock_transaction = AsyncMock()
        mock_connection.transaction.return_value.__aenter__ = AsyncMock(
            return_value=mock_transaction
        )
        mock_connection.transaction.return_value.__aexit__ = AsyncMock(
            return_value=None
        )

        await migrator.apply_migration_with_transaction(migration_sql)

        mock_connection.transaction.assert_called_once()
        assert mock_connection.execute.call_count == len(migration_sql)
