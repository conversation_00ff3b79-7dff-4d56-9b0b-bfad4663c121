"""Tests for database exceptions."""

from unittest.mock import Mock

import pytest

from src.database.exceptions import (
    ConnectionError,
    DatabaseError,
    PermissionError,
    QueryError,
    SchemaError,
    SecurityError,
    TransactionError,
    ValidationError,
)


class TestDatabaseError:
    """Test cases for base DatabaseError class."""

    def test_database_error_creation(self):
        """Test creating a DatabaseError instance."""
        error = DatabaseError("Test error message")

        assert str(error) == "Test error message"
        assert error.message == "Test error message"
        assert error.error_code is None
        assert error.details == {}

    def test_database_error_with_code(self):
        """Test creating a DatabaseError with error code."""
        error = DatabaseError("Test error", error_code="DB001")

        assert error.error_code == "DB001"

    def test_database_error_with_details(self):
        """Test creating a DatabaseError with details."""
        details = {"table": "users", "operation": "SELECT"}
        error = DatabaseError("Test error", details=details)

        assert error.details == details

    def test_database_error_with_original_exception(self):
        """Test creating a DatabaseError with original exception."""
        original = ValueError("Original error")
        error = DatabaseError("Wrapped error", original_exception=original)

        assert error.original_exception == original

    def test_database_error_repr(self):
        """Test DatabaseError string representation."""
        error = DatabaseError("Test error", error_code="DB001")
        repr_str = repr(error)

        assert "DatabaseError" in repr_str
        assert "Test error" in repr_str
        assert "DB001" in repr_str

    def test_database_error_inheritance(self):
        """Test that DatabaseError inherits from Exception."""
        error = DatabaseError("Test error")

        assert isinstance(error, Exception)
        assert isinstance(error, DatabaseError)


class TestConnectionError:
    """Test cases for ConnectionError class."""

    def test_connection_error_creation(self):
        """Test creating a ConnectionError instance."""
        error = ConnectionError("Connection failed")

        assert str(error) == "Connection failed"  # No error code by default
        assert isinstance(error, DatabaseError)

    def test_connection_error_with_host_info(self):
        """Test creating a ConnectionError with host information."""
        details = {"host": "localhost", "port": 5432, "database": "test_db"}
        error = ConnectionError("Connection timeout", details=details)

        assert error.details["host"] == "localhost"
        assert error.details["port"] == 5432
        assert error.details["database"] == "test_db"

    def test_connection_error_from_psycopg2_error(self):
        """Test creating ConnectionError from psycopg2 error."""
        # Mock psycopg2 error
        mock_psycopg2_error = Mock()
        mock_psycopg2_error.pgcode = "08006"  # Connection failure
        mock_psycopg2_error.pgerror = "Connection refused"

        error = ConnectionError(
            "Database connection failed",
            original_exception=mock_psycopg2_error,
            error_code="CONN001",
        )

        assert error.error_code == "CONN001"
        assert error.original_exception == mock_psycopg2_error


class TestSecurityError:
    """Test cases for SecurityError class."""

    def test_security_error_creation(self):
        """Test creating a SecurityError instance."""
        error = SecurityError("Authentication failed")

        assert str(error) == "[DB_SECURITY_ERROR] Authentication failed"
        assert isinstance(error, DatabaseError)

    def test_security_error_with_user_info(self):
        """Test creating a SecurityError with user information."""
        details = {"user": "test_user", "database": "test_db", "host": "localhost"}
        error = SecurityError("Invalid credentials", details=details)

        assert error.details["user"] == "test_user"
        assert error.details["database"] == "test_db"

    def test_security_error_sensitive_data_handling(self):
        """Test that sensitive data is not exposed in error messages."""
        # This test ensures that passwords are not included in error details
        details = {"user": "test_user", "password": "secret123"}
        error = SecurityError("Authentication failed", details=details)

        # The implementation should filter out sensitive fields
        # This test assumes such filtering is implemented
        error_str = str(error)
        assert "secret123" not in error_str


class TestValidationError:
    """Test cases for ValidationError class."""

    def test_validation_error_creation(self):
        """Test creating a ValidationError instance."""
        error = ValidationError("Invalid data format")

        assert str(error) == "[DB_VALIDATION_ERROR] Invalid data format"
        assert isinstance(error, DatabaseError)

    def test_validation_error_with_field_info(self):
        """Test creating a ValidationError with field information."""
        details = {
            "field": "email",
            "value": "invalid-email",
            "expected_format": "<EMAIL>",
        }
        error = ValidationError("Invalid email format", details=details)

        assert error.details["field"] == "email"
        assert error.details["value"] == "invalid-email"

    def test_validation_error_with_multiple_fields(self):
        """Test creating a ValidationError with multiple field errors."""
        details = {
            "errors": [
                {"field": "name", "error": "Required field missing"},
                {"field": "age", "error": "Must be a positive integer"},
            ]
        }
        error = ValidationError("Multiple validation errors", details=details)

        assert len(error.details["errors"]) == 2
        assert error.details["errors"][0]["field"] == "name"
        assert error.details["errors"][1]["field"] == "age"


class TestPermissionError:
    """Test cases for PermissionError class."""

    def test_permission_error_creation(self):
        """Test creating a PermissionError instance."""
        error = PermissionError("Access denied")

        assert str(error) == "[DB_PERMISSION_ERROR] Access denied"
        assert isinstance(error, DatabaseError)

    def test_permission_error_with_operation_info(self):
        """Test creating a PermissionError with operation information."""
        details = {"operation": "DELETE", "table": "users", "user": "readonly_user"}
        error = PermissionError("Insufficient privileges", details=details)

        assert error.details["operation"] == "DELETE"
        assert error.details["table"] == "users"
        assert error.details["user"] == "readonly_user"


class TestQueryError:
    """Test cases for QueryError class."""

    def test_query_error_creation(self):
        """Test creating a QueryError instance."""
        error = QueryError("Syntax error in SQL")

        assert str(error) == "[DB_QUERY_ERROR] Syntax error in SQL"
        assert isinstance(error, DatabaseError)

    def test_query_error_with_sql_info(self):
        """Test creating a QueryError with SQL information."""
        details = {
            "sql": "SELECT * FROM users WHERE id = ?",
            "parameters": [123],
            "line_number": 1,
            "column_number": 15,
        }
        error = QueryError("Invalid column name", details=details)

        assert error.details["sql"] == "SELECT * FROM users WHERE id = ?"
        assert error.details["parameters"] == [123]
        assert error.details["line_number"] == 1

    def test_query_error_sql_truncation(self):
        """Test that long SQL queries are truncated in error messages."""
        long_sql = "SELECT * FROM users WHERE " + "id = 1 OR " * 100 + "name = 'test'"
        details = {"sql": long_sql}
        error = QueryError("Query too complex", details=details)

        # The implementation should truncate long SQL in string representation
        # This test assumes such truncation is implemented
        error_str = str(error)
        assert len(error_str) < len(long_sql) + 100  # Some reasonable limit


class TestTransactionError:
    """Test cases for TransactionError class."""

    def test_transaction_error_creation(self):
        """Test creating a TransactionError instance."""
        error = TransactionError("Transaction rollback failed")

        assert str(error) == "[DB_TRANSACTION_ERROR] Transaction rollback failed"
        assert isinstance(error, DatabaseError)

    def test_transaction_error_with_transaction_info(self):
        """Test creating a TransactionError with transaction information."""
        details = {
            "transaction_id": "tx_12345",
            "isolation_level": "READ_COMMITTED",
            "operations_count": 5,
            "rollback_reason": "Deadlock detected",
        }
        error = TransactionError("Transaction failed", details=details)

        assert error.details["transaction_id"] == "tx_12345"
        assert error.details["isolation_level"] == "READ_COMMITTED"
        assert error.details["operations_count"] == 5

    def test_transaction_error_deadlock(self):
        """Test creating a TransactionError for deadlock scenarios."""
        details = {
            "error_type": "deadlock",
            "involved_tables": ["users", "orders"],
            "conflicting_transactions": ["tx_001", "tx_002"],
        }
        error = TransactionError("Deadlock detected", details=details)

        assert error.details["error_type"] == "deadlock"
        assert "users" in error.details["involved_tables"]
        assert "orders" in error.details["involved_tables"]


class TestSchemaError:
    """Test cases for SchemaError class."""

    def test_schema_error_creation(self):
        """Test creating a SchemaError instance."""
        error = SchemaError("Table does not exist")

        assert str(error) == "[DB_SCHEMA_ERROR] Table does not exist"
        assert isinstance(error, DatabaseError)

    def test_schema_error_with_schema_info(self):
        """Test creating a SchemaError with schema information."""
        details = {
            "schema": "public",
            "table": "users",
            "column": "email",
            "operation": "ALTER TABLE",
        }
        error = SchemaError("Column does not exist", details=details)

        assert error.details["schema"] == "public"
        assert error.details["table"] == "users"
        assert error.details["column"] == "email"

    def test_schema_error_migration_context(self):
        """Test creating a SchemaError in migration context."""
        details = {
            "migration_version": "001_create_users",
            "migration_direction": "up",
            "failed_statement": "CREATE TABLE users (...)",
            "schema_state": "inconsistent",
        }
        error = SchemaError("Migration failed", details=details)

        assert error.details["migration_version"] == "001_create_users"
        assert error.details["migration_direction"] == "up"
        assert error.details["schema_state"] == "inconsistent"


class TestExceptionHierarchy:
    """Test cases for exception hierarchy and inheritance."""

    def test_all_exceptions_inherit_from_database_error(self):
        """Test that all custom exceptions inherit from DatabaseError."""
        exceptions = [
            ConnectionError("test"),
            SecurityError("test"),
            ValidationError("test"),
            PermissionError("test"),
            QueryError("test"),
            TransactionError("test"),
            SchemaError("test"),
        ]

        for exc in exceptions:
            assert isinstance(exc, DatabaseError)
            assert isinstance(exc, Exception)

    def test_exception_catching_hierarchy(self):
        """Test that exceptions can be caught by their parent classes."""
        # Test catching specific exception
        try:
            raise ConnectionError("Connection failed")
        except ConnectionError as e:
            assert str(e) == "Connection failed"

        # Test catching by parent class
        try:
            raise ConnectionError("Connection failed")
        except DatabaseError as e:
            assert str(e) == "Connection failed"

        # Test catching by base Exception
        try:
            raise ConnectionError("Connection failed")
        except Exception as e:
            assert str(e) == "Connection failed"


class TestExceptionUtilities:
    """Test cases for exception utility functions and methods."""

    def test_exception_serialization(self):
        """Test that exceptions can be serialized for logging/debugging."""
        details = {"host": "localhost", "port": 5432}
        error = ConnectionError(
            "Connection failed", error_code="CONN001", details=details
        )

        # Test that exception can be converted to dict for serialization
        # This assumes such functionality is implemented
        error_dict = {
            "type": type(error).__name__,
            "message": str(error),
            "error_code": error.error_code,
            "details": error.details,
        }

        assert error_dict["type"] == "ConnectionError"
        assert error_dict["message"] == "[CONN001] Connection failed"
        assert error_dict["error_code"] == "CONN001"
        assert error_dict["details"] == details

    def test_exception_chaining(self):
        """Test exception chaining with original exceptions."""
        original = ValueError("Original error")

        try:
            raise original
        except ValueError as e:
            wrapped = DatabaseError("Wrapped error", original_exception=e)

            assert wrapped.original_exception == original
            assert str(wrapped) == "Wrapped error"

    def test_exception_context_manager(self):
        """Test using exceptions in context managers."""
        # This test assumes context manager functionality for exception handling
        # The actual implementation may vary
        pass


# Test fixtures and utilities
@pytest.fixture
def sample_database_error():
    """Provide a sample DatabaseError for testing."""
    return DatabaseError(
        "Sample error",
        error_code="TEST001",
        details={"table": "test_table", "operation": "SELECT"},
    )


@pytest.fixture
def sample_connection_error():
    """Provide a sample ConnectionError for testing."""
    return ConnectionError(
        "Connection timeout",
        error_code="CONN001",
        details={"host": "localhost", "port": 5432, "timeout": 30},
    )


def test_exception_error_codes():
    """Test that error codes are properly assigned and unique."""
    # This test ensures that error codes follow a consistent pattern
    # and are unique across different exception types

    connection_error = ConnectionError("test", error_code="CONN001")
    security_error = SecurityError("test", error_code="SEC001")
    validation_error = ValidationError("test", error_code="VAL001")

    error_codes = [
        connection_error.error_code,
        security_error.error_code,
        validation_error.error_code,
    ]

    # Check that all error codes are unique
    assert len(error_codes) == len(set(error_codes))

    # Check that error codes follow expected pattern
    for code in error_codes:
        if code:  # Skip None values
            assert len(code) >= 4  # Minimum length
            assert code.isupper()  # Should be uppercase
