"""综合测试框架包

该包提供了完整的测试框架功能，包括：
- 测试套件管理
- 测试执行引擎
- 质量门控检查
- 测试报告生成
- 性能监控
- 覆盖率分析
"""

from .comprehensive_test_framework import (
    ComprehensiveTestFramework,
    TestSuiteConfig,
    TestExecutionResult,
    QualityGate,
    TestPriority,
    TestType,
    TestStatus
)

from .comprehensive_test_framework_execution import (
    TestExecutionEngine,
    QualityGateChecker
)

from .comprehensive_test_framework_reporting import (
    TestReportGenerator
)

__all__ = [
    # 核心框架类
    'ComprehensiveTestFramework',
    'TestExecutionEngine',
    'QualityGateChecker',
    'TestReportGenerator',
    
    # 数据类
    'TestSuiteConfig',
    'TestExecutionResult',
    'QualityGate',
    
    # 枚举类
    'TestPriority',
    'TestType',
    'TestStatus'
]

# 版本信息
__version__ = '1.0.0'
__author__ = 'Connect Team'
__description__ = 'Comprehensive Testing Framework for Connect Platform'