# Test configuration for database framework
# This file contains test-specific configuration values

database:
  host: localhost
  port: 5432
  name: test_connect
  user: test_user
  password: test_password

pool:
  size: 5
  max_overflow: 10
  timeout: 30
  recycle: 3600

table_standards:
  naming_convention:
    table: "snake_case"
    column: "snake_case"
    index: "idx_{table}_{column}"

data_sources:
  ep:
    schema_name: ep_test
    file_extensions: ['.xlsx', '.xls']
    skip_rows: 1
    header_row: 1
    table_name_pattern: 'ep_{cell_type}_{year}_cw{week}'

  cdr:
    file_extensions: ['.xlsx', '.xls']
    skip_rows: 0
    header_row: 0
    table_name_pattern: 'cdr_{year}Q{quarter}_{service_type}'
    sheet_schema_mapping:
      telefonica: cdr_test_to2
      vodafone: cdr_test_vdf
      telekom: cdr_test_tdg

  nlg:
    schema_name: nlg_test
    file_extensions: ['.xlsb']
    sheet_name: 'Techno_2G_4G_5G'
    skip_rows: 4
    header_row: 4
    table_name_pattern: 'nlg_cube_test_{date}'

  kpi:
    schema_name: kpi_test
    file_extensions: ['.xlsx', '.csv']
    skip_rows: 0
    header_row: 0
    table_name_pattern: 'kpi_test_{metric_type}_{period}'

  score:
    schema_name: score_test
    file_extensions: ['.xlsx', '.csv']
    skip_rows: 1
    header_row: 1
    table_name_pattern: 'score_test_{algorithm}_{date}'

  cfg:
    schema_name: cfg_test
    file_extensions: ['.yaml', '.json']
    skip_rows: 0
    header_row: 0
    table_name_pattern: 'cfg_test_{config_type}'

logging:
  level: DEBUG
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: null
  max_bytes: 1048576  # 1MB for tests
  backup_count: 2

# Test-specific settings
test_settings:
  # Database settings for testing
  test_database:
    create_test_db: true
    drop_test_db_after: true
    test_schema_prefix: "test_"

  # File processing settings for testing
  file_processing:
    max_test_file_size_mb: 10
    test_data_dir: "tests/data"
    temp_dir: "tests/temp"

  # Performance testing settings
  performance:
    max_execution_time_seconds: 30
    memory_limit_mb: 512
    max_test_rows: 10000

  # Mock settings
  mocking:
    mock_external_apis: true
    mock_file_operations: false
    mock_database_connections: false

  # Validation settings
  validation:
    strict_type_checking: true
    validate_all_constraints: true
    fail_on_warnings: false

# Test data configurations
test_data:
  # Sample EP data configuration
  ep_sample:
    cell_types: ['gsm', 'umts', 'lte', 'nr']
    years: [2023, 2024]
    weeks: [1, 2, 3, 4, 5]
    metrics: ['throughput', 'users', 'quality']

  # Sample CDR data configuration
  cdr_sample:
    operators: ['telefonica', 'vodafone', 'telekom']
    service_types: ['voice', 'sms', 'data', 'm2m']
    years: [2023, 2024]
    quarters: [1, 2, 3, 4]

  # Sample NLG data configuration
  nlg_sample:
    technologies: ['2G', '3G', '4G', '5G']
    metrics: ['coverage', 'quality', 'capacity']
    date_range:
      start: '2024-01-01'
      end: '2024-12-31'

  # Sample KPI data configuration
  kpi_sample:
    metric_types: ['network', 'service', 'customer']
    periods: ['daily', 'weekly', 'monthly']
    aggregation_levels: ['cell', 'site', 'region']

  # Sample Score data configuration
  score_sample:
    algorithms: ['ml_model_v1', 'rule_based', 'hybrid']
    score_types: ['quality', 'performance', 'satisfaction']
    date_range:
      start: '2024-01-01'
      end: '2024-12-31'

# Error simulation for testing
error_simulation:
  # Connection errors
  connection_errors:
    simulate_timeout: false
    simulate_connection_refused: false
    simulate_auth_failure: false

  # Query errors
  query_errors:
    simulate_syntax_error: false
    simulate_constraint_violation: false
    simulate_deadlock: false

  # File errors
  file_errors:
    simulate_file_not_found: false
    simulate_permission_denied: false
    simulate_corrupted_file: false

# Test environment configuration
test_environment:
  # CI/CD settings
  ci_cd:
    skip_slow_tests: false
    parallel_execution: true
    test_timeout_minutes: 10

  # Local development settings
  local_dev:
    verbose_logging: true
    keep_test_data: false
    auto_cleanup: true

  # Coverage settings
  coverage:
    minimum_coverage: 80
    fail_under: 75
    exclude_patterns:
      - "*/tests/*"
      - "*/conftest.py"
      - "*/__init__.py"
