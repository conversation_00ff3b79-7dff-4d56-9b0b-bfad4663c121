#!/usr/bin/env python3
"""
Performance Benchmark Script for Connect Import System

Comprehensive performance testing and benchmarking for the Connect telecommunications
data import system. Tests various scenarios including large files, concurrent
processing, memory usage, and throughput optimization.

Features:
- Large file processing benchmarks
- Concurrent import testing
- Memory usage monitoring
- Throughput measurement
- Database performance testing
- Comparison with baseline metrics

Author: Vincent.Li
Email: <EMAIL>
"""

import asyncio
import gc
import json
import logging
import os
import psutil
import sys
import tempfile
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Tuple

import pandas as pd
from rich.console import Console
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TimeElapsedColumn,
)
from rich.table import Table
from rich.panel import Panel

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.importers.import_manager import ImportManager, ImportJobConfig
from src.config import get_config

console = Console()
logger = logging.getLogger(__name__)


class PerformanceBenchmark:
    """Performance benchmark suite for import system."""

    def __init__(self):
        self.console = Console()
        self.results = {}
        self.baseline_metrics = {
            "cdr_throughput_min": 1000,  # records/second
            "ep_throughput_min": 500,
            "kpi_throughput_min": 800,
            "memory_limit_mb": 4096,  # Increased from 2048MB for better memory handling
            "large_file_time_limit": 300,  # seconds for 100k records
        }

    def generate_test_data(self, data_type: str, num_records: int) -> pd.DataFrame:
        """Generate test data for benchmarking."""

        if data_type == "cdr":
            return self._generate_cdr_data(num_records)
        elif data_type == "ep":
            return self._generate_ep_data(num_records)
        elif data_type == "kpi":
            return self._generate_kpi_data(num_records)
        else:
            raise ValueError(f"Unsupported data type: {data_type}")

    def _generate_cdr_data(self, num_records: int) -> pd.DataFrame:
        """Generate CDR test data."""
        import random
        from datetime import datetime, timedelta

        base_time = datetime.now() - timedelta(days=30)

        data = []
        for i in range(num_records):
            record = {
                "call_id": f"CDR_{i:08d}",
                "calling_number": f"+34{random.randint(600000000, 699999999)}",
                "called_number": f"+34{random.randint(600000000, 699999999)}",
                "start_time": base_time + timedelta(minutes=random.randint(0, 43200)),
                "duration": random.randint(10, 3600),
                "call_type": random.choice(["voice", "video", "data"]),
                "cell_id": f"CELL_{random.randint(1000, 9999)}",
                "operator": "telefonica",
                "success_flag": random.choice([0, 1]),
                "bytes_uploaded": random.randint(1024, 1048576),
                "bytes_downloaded": random.randint(1024, ********),
                "signal_strength": random.randint(-120, -60),
                "location_area": random.randint(1000, 9999),
                "routing_area": random.randint(100, 999),
            }
            data.append(record)

        return pd.DataFrame(data)

    def _generate_ep_data(self, num_records: int) -> pd.DataFrame:
        """Generate EP test data."""
        import random

        data = []
        for i in range(num_records):
            record = {
                "ep_id": f"EP_{i:06d}",
                "site_name": f"Site_{i:06d}",
                "latitude": 40.0 + random.uniform(-2.0, 2.0),
                "longitude": -3.0 + random.uniform(-2.0, 2.0),
                "altitude": random.randint(500, 2000),
                "antenna_height": random.randint(15, 50),
                "azimuth": random.randint(0, 359),
                "tilt": random.randint(-10, 10),
                "power": random.randint(10, 50),
                "frequency": random.choice([900, 1800, 2100, 2600]),
                "technology": random.choice(["GSM", "UMTS", "LTE", "NR"]),
                "operator": "telefonica",
                "sector_id": f"SEC_{random.randint(1, 3)}",
                "cell_range": random.randint(500, 5000),
                "capacity_mbps": random.randint(10, 1000),
            }
            data.append(record)

        return pd.DataFrame(data)

    def _generate_kpi_data(self, num_records: int) -> pd.DataFrame:
        """Generate KPI test data."""
        import random
        from datetime import datetime, timedelta

        base_time = datetime.now() - timedelta(days=7)

        data = []
        kpi_names = [
            "call_success_rate",
            "handover_success_rate",
            "signal_strength",
            "throughput_dl",
            "throughput_ul",
            "latency",
            "packet_loss",
            "availability",
            "utilization",
            "interference_level",
        ]

        for i in range(num_records):
            record = {
                "kpi_id": f"KPI_{i:06d}",
                "kpi_name": random.choice(kpi_names),
                "value": random.uniform(0.1, 100.0),
                "unit": random.choice(["%", "dBm", "Mbps", "ms"]),
                "timestamp": base_time + timedelta(hours=random.randint(0, 168)),
                "cell_id": f"CELL_{random.randint(1000, 9999)}",
                "aggregation_level": random.choice(["cell", "sector", "site"]),
                "operator": "telefonica",
                "threshold_min": random.uniform(0, 50),
                "threshold_max": random.uniform(50, 100),
            }
            data.append(record)

        return pd.DataFrame(data)

    async def benchmark_single_file_import(
        self,
        manager: ImportManager,
        data_type: str,
        num_records: int,
        batch_size: int = None,
    ) -> Dict[str, Any]:
        """Benchmark single file import performance."""

        # Generate test data
        test_data = self.generate_test_data(data_type, num_records)

        # Create temporary file with appropriate extension
        file_extension = (
            ".xlsx" if data_type in ["ep", "nlg", "cfg", "score"] else ".csv"
        )

        with tempfile.NamedTemporaryFile(suffix=file_extension, delete=False) as f:
            if file_extension == ".csv":
                test_data.to_csv(f.name, index=False)
            else:
                test_data.to_excel(f.name, index=False)
            file_path = f.name

        try:
            # Monitor memory before import
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB

            # Create and execute import job
            start_time = time.time()

            job_config = ImportJobConfig(
                source_path=file_path,
                data_type=data_type,
                validate_only=True,  # Only validate for benchmarking
                batch_size=batch_size,
            )

            job_id = await manager.create_import_job(job_config)
            result = await manager.execute_import_job(job_id)

            end_time = time.time()

            # Monitor memory after import
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_used = final_memory - initial_memory

            # Calculate metrics
            processing_time = end_time - start_time
            throughput = num_records / processing_time if processing_time > 0 else 0

            return {
                "data_type": data_type,
                "num_records": num_records,
                "batch_size": batch_size,
                "processing_time_seconds": processing_time,
                "throughput_records_per_second": throughput,
                "memory_used_mb": memory_used,
                "status": result.status.value
                if hasattr(result.status, "value")
                else str(result.status),
                "records_processed": result.records_processed,
                "file_size_mb": Path(file_path).stat().st_size / 1024 / 1024,
            }

        finally:
            # Cleanup
            Path(file_path).unlink()
            gc.collect()

    async def benchmark_concurrent_imports(
        self,
        manager: ImportManager,
        data_type: str,
        num_files: int,
        records_per_file: int,
    ) -> Dict[str, Any]:
        """Benchmark concurrent file imports."""

        # Create multiple test files
        temp_files = []
        for i in range(num_files):
            test_data = self.generate_test_data(data_type, records_per_file)

            with tempfile.NamedTemporaryFile(suffix=f"_{i}.csv", delete=False) as f:
                test_data.to_csv(f.name, index=False)
                temp_files.append(f.name)

        try:
            # Monitor memory before import
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB

            start_time = time.time()

            # Create concurrent import jobs
            tasks = []
            for file_path in temp_files:
                job_config = ImportJobConfig(
                    source_path=file_path, data_type=data_type, validate_only=True
                )

                task = asyncio.create_task(
                    self._execute_single_job(manager, job_config)
                )
                tasks.append(task)

            # Execute all jobs concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            end_time = time.time()

            # Monitor memory after import
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_used = final_memory - initial_memory

            # Calculate metrics
            processing_time = end_time - start_time
            total_records = num_files * records_per_file
            throughput = total_records / processing_time if processing_time > 0 else 0

            successful_jobs = sum(1 for r in results if not isinstance(r, Exception))
            failed_jobs = len(results) - successful_jobs

            return {
                "data_type": data_type,
                "num_files": num_files,
                "records_per_file": records_per_file,
                "total_records": total_records,
                "processing_time_seconds": processing_time,
                "throughput_records_per_second": throughput,
                "memory_used_mb": memory_used,
                "successful_jobs": successful_jobs,
                "failed_jobs": failed_jobs,
                "concurrency_efficiency": successful_jobs / num_files,
            }

        finally:
            # Cleanup
            for file_path in temp_files:
                Path(file_path).unlink()
            gc.collect()

    async def _execute_single_job(
        self, manager: ImportManager, job_config: ImportJobConfig
    ):
        """Execute a single import job."""
        job_id = await manager.create_import_job(job_config)
        return await manager.execute_import_job(job_id)

    async def benchmark_memory_scaling(
        self, manager: ImportManager, data_type: str, record_counts: List[int]
    ) -> List[Dict[str, Any]]:
        """Benchmark memory usage scaling with data size."""

        results = []

        for num_records in record_counts:
            self.console.print(f"Testing {num_records:,} records...")

            result = await self.benchmark_single_file_import(
                manager, data_type, num_records
            )

            results.append(result)

            # Force garbage collection between tests
            gc.collect()

            # Brief pause to allow memory cleanup
            await asyncio.sleep(1)

        return results

    async def run_comprehensive_benchmark(self) -> Dict[str, Any]:
        """Run comprehensive performance benchmark suite."""

        self.console.print(
            Panel.fit(
                "[bold blue]Connect Import System Performance Benchmark[/bold blue]\n"
                "[dim]Comprehensive performance testing and optimization validation[/dim]",
                border_style="blue",
            )
        )

        # Initialize import manager
        manager = ImportManager()
        await manager.initialize()

        try:
            benchmark_results = {}

            # Test 1: Single file performance for each data type
            self.console.print("\n[bold]Test 1: Single File Performance[/bold]")

            single_file_results = {}
            data_types = ["cdr", "ep", "kpi"]

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TimeElapsedColumn(),
                console=self.console,
            ) as progress:
                task = progress.add_task(
                    "Running single file tests...", total=len(data_types)
                )

                for data_type in data_types:
                    result = await self.benchmark_single_file_import(
                        manager, data_type, 10000
                    )
                    single_file_results[data_type] = result
                    progress.update(task, advance=1)

            benchmark_results["single_file"] = single_file_results

            # Test 2: Large file processing
            self.console.print("\n[bold]Test 2: Large File Processing[/bold]")

            large_file_result = await self.benchmark_single_file_import(
                manager, "cdr", 100000, batch_size=5000
            )
            benchmark_results["large_file"] = large_file_result

            # Test 3: Concurrent processing
            self.console.print("\n[bold]Test 3: Concurrent Processing[/bold]")

            concurrent_result = await self.benchmark_concurrent_imports(
                manager, "cdr", 5, 5000
            )
            benchmark_results["concurrent"] = concurrent_result

            # Test 4: Memory scaling
            self.console.print("\n[bold]Test 4: Memory Scaling[/bold]")

            memory_scaling_results = await self.benchmark_memory_scaling(
                manager, "cdr", [1000, 5000, 10000, 25000]
            )
            benchmark_results["memory_scaling"] = memory_scaling_results

            # Generate performance report
            self._generate_performance_report(benchmark_results)

            return benchmark_results

        finally:
            await manager.cleanup()

    def _generate_performance_report(self, results: Dict[str, Any]):
        """Generate and display performance report."""

        self.console.print("\n[bold]Performance Benchmark Results[/bold]")

        # Single file performance table
        table = Table(title="Single File Performance")
        table.add_column("Data Type", style="cyan")
        table.add_column("Records", style="green")
        table.add_column("Time (s)", style="yellow")
        table.add_column("Throughput (rec/s)", style="blue")
        table.add_column("Memory (MB)", style="magenta")
        table.add_column("Status", style="white")

        for data_type, result in results["single_file"].items():
            throughput = f"{result['throughput_records_per_second']:.0f}"
            baseline = self.baseline_metrics.get(f"{data_type}_throughput_min", 0)

            # Color code based on performance
            if result["throughput_records_per_second"] >= baseline:
                status = "[green]✓ PASS[/green]"
            else:
                status = "[red]✗ FAIL[/red]"

            table.add_row(
                data_type.upper(),
                f"{result['num_records']:,}",
                f"{result['processing_time_seconds']:.2f}",
                throughput,
                f"{result['memory_used_mb']:.1f}",
                status,
            )

        self.console.print(table)

        # Large file performance
        large_result = results["large_file"]
        self.console.print(f"\n[bold]Large File Performance (100k records):[/bold]")
        self.console.print(
            f"Processing time: {large_result['processing_time_seconds']:.2f}s"
        )
        self.console.print(
            f"Throughput: {large_result['throughput_records_per_second']:.0f} records/s"
        )
        self.console.print(f"Memory usage: {large_result['memory_used_mb']:.1f} MB")

        # Performance validation
        if (
            large_result["processing_time_seconds"]
            <= self.baseline_metrics["large_file_time_limit"]
        ):
            self.console.print("[green]✓ Large file performance: PASS[/green]")
        else:
            self.console.print("[red]✗ Large file performance: FAIL[/red]")

        # Concurrent processing
        concurrent_result = results["concurrent"]
        self.console.print(f"\n[bold]Concurrent Processing (5 files):[/bold]")
        self.console.print(
            f"Total processing time: {concurrent_result['processing_time_seconds']:.2f}s"
        )
        self.console.print(
            f"Overall throughput: {concurrent_result['throughput_records_per_second']:.0f} records/s"
        )
        self.console.print(
            f"Success rate: {concurrent_result['concurrency_efficiency']:.1%}"
        )

        # Memory scaling analysis
        memory_results = results["memory_scaling"]
        self.console.print(f"\n[bold]Memory Scaling Analysis:[/bold]")

        for result in memory_results:
            records = result["num_records"]
            memory_per_record = (
                result["memory_used_mb"] / records * 1000
            )  # KB per record
            self.console.print(
                f"{records:,} records: {result['memory_used_mb']:.1f} MB ({memory_per_record:.2f} KB/record)"
            )

        # Save results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"benchmark_results_{timestamp}.json"

        with open(results_file, "w") as f:
            json.dump(results, f, indent=2, default=str)

        self.console.print(f"\n[dim]Results saved to: {results_file}[/dim]")


async def main():
    """Main benchmark execution."""

    # Setup logging
    logging.basicConfig(level=logging.INFO)

    # Run benchmark
    benchmark = PerformanceBenchmark()

    try:
        results = await benchmark.run_comprehensive_benchmark()

        console.print("\n[bold green]Benchmark completed successfully![/bold green]")

        return results

    except Exception as e:
        console.print(f"\n[bold red]Benchmark failed: {str(e)}[/bold red]")
        raise


if __name__ == "__main__":
    asyncio.run(main())
