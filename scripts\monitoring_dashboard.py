#!/usr/bin/env python3
"""
Connect电信平台监控Dashboard脚本
用于集成测试结果到CI/CD监控系统，支持Prometheus、Grafana和多种通知方式
"""

import json
import argparse
import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
import logging
import requests
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders

# 第三方库导入
try:
    from prometheus_client import CollectorRegistry, Gauge, Counter, Histogram, push_to_gateway
    from slack_sdk import WebClient
    from slack_sdk.errors import SlackApiError
    import matplotlib.pyplot as plt
    import pandas as pd
    import seaborn as sns
    from jinja2 import Template
except ImportError as e:
    logging.warning(f"Optional dependency not available: {e}")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
except:
    pass


class MonitoringDashboard:
    """监控Dashboard集成器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化监控Dashboard
        
        Args:
            config_file: 配置文件路径
        """
        self.config = self._load_config(config_file)
        self.metrics_registry = CollectorRegistry()
        self.test_metrics = {}
        self.alerts = []
        
        # 初始化Prometheus指标
        self._init_prometheus_metrics()
        
        # 初始化Slack客户端
        self._init_slack_client()
        
        self.dashboard_data = {
            "timestamp": datetime.now().isoformat(),
            "metrics": {},
            "charts": {},
            "alerts": []
        }
        
    def _load_config(self, config_file: Optional[str]) -> Dict[str, Any]:
        """加载配置文件
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            配置字典
        """
        default_config = {
            'prometheus': {
                'enabled': os.getenv('PROMETHEUS_ENABLED', 'false').lower() == 'true',
                'gateway_url': os.getenv('PROMETHEUS_GATEWAY_URL', 'http://localhost:9091'),
                'job_name': os.getenv('PROMETHEUS_JOB_NAME', 'connect-tests')
            },
            'grafana': {
                'enabled': os.getenv('GRAFANA_ENABLED', 'false').lower() == 'true',
                'url': os.getenv('GRAFANA_URL', 'http://localhost:3000'),
                'api_key': os.getenv('GRAFANA_API_KEY', ''),
                'dashboard_uid': os.getenv('GRAFANA_DASHBOARD_UID', 'connect-tests')
            },
            'slack': {
                'enabled': os.getenv('SLACK_ENABLED', 'false').lower() == 'true',
                'webhook_url': os.getenv('SLACK_WEBHOOK_URL', ''),
                'token': os.getenv('SLACK_BOT_TOKEN', ''),
                'channel': os.getenv('SLACK_CHANNEL', '#ci-cd')
            },
            'email': {
                'enabled': os.getenv('EMAIL_ENABLED', 'false').lower() == 'true',
                'smtp_server': os.getenv('SMTP_SERVER', 'smtp.gmail.com'),
                'smtp_port': int(os.getenv('SMTP_PORT', '587')),
                'username': os.getenv('EMAIL_USERNAME', ''),
                'password': os.getenv('EMAIL_PASSWORD', ''),
                'from_email': os.getenv('FROM_EMAIL', ''),
                'to_emails': os.getenv('TO_EMAILS', '').split(',') if os.getenv('TO_EMAILS') else []
            },
            'webhook': {
                'enabled': os.getenv('WEBHOOK_ENABLED', 'false').lower() == 'true',
                'url': os.getenv('WEBHOOK_URL', ''),
                'headers': json.loads(os.getenv('WEBHOOK_HEADERS', '{}'))
            },
            'thresholds': {
                'success_rate_warning': float(os.getenv('SUCCESS_RATE_WARNING', '95.0')),
                'success_rate_critical': float(os.getenv('SUCCESS_RATE_CRITICAL', '80.0')),
                'coverage_warning': float(os.getenv('COVERAGE_WARNING', '80.0')),
                'coverage_critical': float(os.getenv('COVERAGE_CRITICAL', '60.0')),
                'security_critical': int(os.getenv('SECURITY_CRITICAL', '0')),
                'performance_warning': float(os.getenv('PERFORMANCE_WARNING', '10.0'))  # seconds
            },
            'dashboard': {
                'output_dir': os.getenv('DASHBOARD_OUTPUT_DIR', './dashboard'),
                'template_dir': os.getenv('DASHBOARD_TEMPLATE_DIR', './templates'),
                'static_dir': os.getenv('DASHBOARD_STATIC_DIR', './static')
            }
        }
        
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    # 深度合并配置
                    self._deep_merge(default_config, file_config)
            except Exception as e:
                logger.warning(f"Failed to load config file {config_file}: {e}")
        
        return default_config
    
    def _deep_merge(self, base: Dict, update: Dict) -> None:
        """深度合并字典"""
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge(base[key], value)
            else:
                base[key] = value
    
    def _init_prometheus_metrics(self) -> None:
        """初始化Prometheus指标"""
        if not self.config['prometheus']['enabled']:
            return
        
        try:
            # 测试成功率指标
            self.test_success_rate = Gauge(
                'connect_test_success_rate',
                'Test success rate percentage',
                ['test_suite', 'environment'],
                registry=self.metrics_registry
            )
            
            # 测试执行时间
            self.test_duration = Histogram(
                'connect_test_duration_seconds',
                'Test execution duration in seconds',
                ['test_suite', 'environment'],
                registry=self.metrics_registry
            )
            
            # 测试计数器
            self.test_counter = Counter(
                'connect_tests_total',
                'Total number of tests executed',
                ['test_suite', 'status', 'environment'],
                registry=self.metrics_registry
            )
            
            # 代码覆盖率
            self.code_coverage = Gauge(
                'connect_code_coverage_percentage',
                'Code coverage percentage',
                ['coverage_type', 'environment'],
                registry=self.metrics_registry
            )
            
            # 安全问题计数
            self.security_issues = Gauge(
                'connect_security_issues_total',
                'Number of security issues found',
                ['severity', 'scan_type', 'environment'],
                registry=self.metrics_registry
            )
            
            # 质量分数
            self.quality_score = Gauge(
                'connect_quality_score',
                'Overall quality score',
                ['metric_type', 'environment'],
                registry=self.metrics_registry
            )
            
            # 性能基准
            self.performance_benchmark = Gauge(
                'connect_performance_benchmark_seconds',
                'Performance benchmark execution time',
                ['benchmark_name', 'operation', 'environment'],
                registry=self.metrics_registry
            )
            
            logger.info("Prometheus metrics initialized")
        except Exception as e:
            logger.error(f"Failed to initialize Prometheus metrics: {e}")
    
    def _init_slack_client(self) -> None:
        """初始化Slack客户端"""
        if not self.config['slack']['enabled']:
            return
        
        try:
            if self.config['slack']['token']:
                self.slack_client = WebClient(token=self.config['slack']['token'])
                logger.info("Slack client initialized")
        except Exception as e:
            logger.error(f"Failed to initialize Slack client: {e}")
            self.slack_client = None
    
    def load_test_results(self, results_file: str) -> Dict[str, Any]:
        """加载测试结果数据"""
        try:
            with open(results_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading test results from {results_file}: {e}")
            return {}
    
    def load_dashboard_data(self, dashboard_file: str) -> Dict[str, Any]:
        """加载dashboard数据"""
        try:
            with open(dashboard_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading dashboard data from {dashboard_file}: {e}")
            return {}
    
    def send_test_results(self, results_file: str) -> bool:
        """发送测试结果到监控系统
        
        Args:
            results_file: 测试结果文件路径
            
        Returns:
            bool: 发送是否成功
        """
        try:
            results = self.load_test_results(results_file)
            if not results:
                logger.error("No test results to send")
                return False
            
            success = True
            
            # 推送到Prometheus
            if self.config['prometheus']['enabled']:
                success &= self._push_to_prometheus(results)
            
            # 更新Grafana dashboard
            if self.config['grafana']['enabled']:
                success &= self._update_grafana_dashboard(results)
            
            # 发送通知
            self._send_notifications(results)
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending test results: {e}")
            return False
    
    def _push_to_prometheus(self, results: Dict[str, Any]) -> bool:
        """推送指标到Prometheus"""
        try:
            environment = results.get('environment', {}).get('branch', 'unknown')
            
            # 推送测试成功率
            if 'summary' in results:
                summary = results['summary']
                success_rate = summary.get('success_rate', 0)
                
                self.test_success_rate.labels(
                    test_suite='overall',
                    environment=environment
                ).set(success_rate)
                
                # 推送测试计数
                for status in ['passed', 'failed', 'skipped', 'error']:
                    count = summary.get(f'{status}_tests', 0)
                    self.test_counter.labels(
                        test_suite='overall',
                        status=status,
                        environment=environment
                    ).inc(count)
                
                # 推送执行时间
                duration = summary.get('total_duration', 0)
                self.test_duration.labels(
                    test_suite='overall',
                    environment=environment
                ).observe(duration)
            
            # 推送覆盖率数据
            if 'coverage' in results:
                coverage = results['coverage']
                for coverage_type in ['line', 'branch', 'function', 'statement']:
                    if coverage_type in coverage:
                        self.code_coverage.labels(
                            coverage_type=coverage_type,
                            environment=environment
                        ).set(coverage[coverage_type])
            
            # 推送安全问题
            if 'security' in results:
                security = results['security']
                for severity in ['critical', 'high', 'medium', 'low']:
                    count = security.get('issues_by_severity', {}).get(severity, 0)
                    self.security_issues.labels(
                        severity=severity,
                        scan_type='overall',
                        environment=environment
                    ).set(count)
            
            # 推送质量分数
            if 'quality' in results:
                quality = results['quality']
                for metric_type in ['complexity', 'style', 'type_checks', 'overall_score']:
                    if metric_type in quality:
                        self.quality_score.labels(
                            metric_type=metric_type,
                            environment=environment
                        ).set(quality[metric_type])
            
            # 推送性能基准
            if 'performance' in results:
                performance = results['performance']
                for benchmark in performance.get('benchmarks', []):
                    self.performance_benchmark.labels(
                        benchmark_name=benchmark.get('name', 'unknown'),
                        operation=benchmark.get('operation', 'unknown'),
                        environment=environment
                    ).set(benchmark.get('duration', 0))
            
            # 推送到Gateway
            gateway_url = self.config['prometheus']['gateway_url']
            job_name = self.config['prometheus']['job_name']
            
            push_to_gateway(
                gateway_url,
                job=job_name,
                registry=self.metrics_registry
            )
            
            logger.info("Successfully pushed metrics to Prometheus")
            return True
            
        except Exception as e:
            logger.error(f"Error pushing to Prometheus: {e}")
            return False
    
    def _update_grafana_dashboard(self, results: Dict[str, Any]) -> bool:
        """更新Grafana dashboard"""
        try:
            if not self.config['grafana']['api_key']:
                logger.warning("Grafana API key not configured")
                return False
            
            dashboard_config = self._generate_grafana_dashboard(results)
            
            headers = {
                'Authorization': f"Bearer {self.config['grafana']['api_key']}",
                'Content-Type': 'application/json'
            }
            
            url = f"{self.config['grafana']['url']}/api/dashboards/db"
            
            response = requests.post(
                url,
                headers=headers,
                json=dashboard_config,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info("Successfully updated Grafana dashboard")
                return True
            else:
                logger.error(f"Failed to update Grafana dashboard: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating Grafana dashboard: {e}")
            return False
    
    def _generate_grafana_dashboard(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成Grafana dashboard配置"""
        dashboard = {
            "dashboard": {
                "id": None,
                "uid": self.config['grafana']['dashboard_uid'],
                "title": "Connect电信平台测试监控",
                "tags": ["connect", "tests", "ci-cd"],
                "timezone": "browser",
                "panels": [
                    {
                        "id": 1,
                        "title": "测试成功率",
                        "type": "stat",
                        "targets": [{
                            "expr": "connect_test_success_rate",
                            "legendFormat": "成功率"
                        }],
                        "fieldConfig": {
                            "defaults": {
                                "unit": "percent",
                                "min": 0,
                                "max": 100
                            }
                        },
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
                    },
                    {
                        "id": 2,
                        "title": "代码覆盖率",
                        "type": "stat",
                        "targets": [{
                            "expr": "connect_code_coverage_percentage",
                            "legendFormat": "{{coverage_type}}"
                        }],
                        "fieldConfig": {
                            "defaults": {
                                "unit": "percent",
                                "min": 0,
                                "max": 100
                            }
                        },
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
                    },
                    {
                        "id": 3,
                        "title": "安全问题统计",
                        "type": "bargauge",
                        "targets": [{
                            "expr": "connect_security_issues_total",
                            "legendFormat": "{{severity}}"
                        }],
                        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
                    },
                    {
                        "id": 4,
                        "title": "性能基准测试",
                        "type": "timeseries",
                        "targets": [{
                            "expr": "connect_performance_benchmark_seconds",
                            "legendFormat": "{{benchmark_name}}"
                        }],
                        "fieldConfig": {
                            "defaults": {
                                "unit": "s"
                            }
                        },
                        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
                    }
                ],
                "time": {
                    "from": "now-1h",
                    "to": "now"
                },
                "refresh": "5s"
            },
            "overwrite": True
        }
        
        return dashboard
    
    def _send_notifications(self, results: Dict[str, Any]) -> None:
        """发送通知"""
        # 分析结果并生成告警
        alerts = self._analyze_results(results)
        
        if not alerts:
            logger.info("No alerts to send")
            return
        
        # 发送Slack通知
        if self.config['slack']['enabled']:
            self._send_slack_notification(results, alerts)
        
        # 发送邮件通知
        if self.config['email']['enabled']:
            self._send_email_notification(results, alerts)
        
        # 发送Webhook通知
        if self.config['webhook']['enabled']:
            self._send_webhook_notification(results, alerts)
    
    def _analyze_results(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析测试结果并生成告警"""
        alerts = []
        thresholds = self.config['thresholds']
        
        # 检查成功率
        if 'summary' in results:
            success_rate = results['summary'].get('success_rate', 0)
            
            if success_rate < thresholds['success_rate_critical']:
                alerts.append({
                    'severity': 'critical',
                    'type': 'success_rate',
                    'message': f'测试成功率过低: {success_rate:.1f}% (阈值: {thresholds["success_rate_critical"]}%)',
                    'value': success_rate,
                    'threshold': thresholds['success_rate_critical']
                })
            elif success_rate < thresholds['success_rate_warning']:
                alerts.append({
                    'severity': 'warning',
                    'type': 'success_rate',
                    'message': f'测试成功率偏低: {success_rate:.1f}% (阈值: {thresholds["success_rate_warning"]}%)',
                    'value': success_rate,
                    'threshold': thresholds['success_rate_warning']
                })
        
        # 检查覆盖率
        if 'coverage' in results:
            line_coverage = results['coverage'].get('line', 0)
            
            if line_coverage < thresholds['coverage_critical']:
                alerts.append({
                    'severity': 'critical',
                    'type': 'coverage',
                    'message': f'代码覆盖率过低: {line_coverage:.1f}% (阈值: {thresholds["coverage_critical"]}%)',
                    'value': line_coverage,
                    'threshold': thresholds['coverage_critical']
                })
            elif line_coverage < thresholds['coverage_warning']:
                alerts.append({
                    'severity': 'warning',
                    'type': 'coverage',
                    'message': f'代码覆盖率偏低: {line_coverage:.1f}% (阈值: {thresholds["coverage_warning"]}%)',
                    'value': line_coverage,
                    'threshold': thresholds['coverage_warning']
                })
        
        # 检查安全问题
        if 'security' in results:
            critical_issues = results['security'].get('issues_by_severity', {}).get('critical', 0)
            
            if critical_issues > thresholds['security_critical']:
                alerts.append({
                    'severity': 'critical',
                    'type': 'security',
                    'message': f'发现严重安全问题: {critical_issues}个',
                    'value': critical_issues,
                    'threshold': thresholds['security_critical']
                })
        
        # 检查性能
        if 'performance' in results:
            for benchmark in results['performance'].get('benchmarks', []):
                duration = benchmark.get('duration', 0)
                if duration > thresholds['performance_warning']:
                    alerts.append({
                        'severity': 'warning',
                        'type': 'performance',
                        'message': f'性能基准测试超时: {benchmark.get("name", "unknown")} - {duration:.2f}s (阈值: {thresholds["performance_warning"]}s)',
                        'value': duration,
                        'threshold': thresholds['performance_warning']
                    })
        
        return alerts
    
    def _send_slack_notification(self, results: Dict[str, Any], alerts: List[Dict[str, Any]]) -> None:
        """发送Slack通知"""
        try:
            # 构建消息
            message = self._build_slack_message(results, alerts)
            
            if self.config['slack']['webhook_url']:
                # 使用Webhook
                response = requests.post(
                    self.config['slack']['webhook_url'],
                    json=message,
                    timeout=30
                )
                
                if response.status_code == 200:
                    logger.info("Slack notification sent via webhook")
                else:
                    logger.error(f"Failed to send Slack webhook: {response.status_code}")
            
            elif hasattr(self, 'slack_client') and self.slack_client:
                # 使用Bot Token
                response = self.slack_client.chat_postMessage(
                    channel=self.config['slack']['channel'],
                    **message
                )
                
                if response['ok']:
                    logger.info("Slack notification sent via bot")
                else:
                    logger.error(f"Failed to send Slack message: {response['error']}")
            
        except Exception as e:
            logger.error(f"Error sending Slack notification: {e}")
    
    def _build_slack_message(self, results: Dict[str, Any], alerts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """构建Slack消息"""
        summary = results.get('summary', {})
        environment = results.get('environment', {})
        
        # 确定消息颜色
        color = 'good'  # 绿色
        if any(alert['severity'] == 'critical' for alert in alerts):
            color = 'danger'  # 红色
        elif any(alert['severity'] == 'warning' for alert in alerts):
            color = 'warning'  # 黄色
        
        # 构建附件
        attachments = [{
            'color': color,
            'title': f"Connect电信平台测试报告 - {environment.get('branch', 'unknown')}",
            'fields': [
                {
                    'title': '测试成功率',
                    'value': f"{summary.get('success_rate', 0):.1f}%",
                    'short': True
                },
                {
                    'title': '总测试数',
                    'value': str(summary.get('total_tests', 0)),
                    'short': True
                },
                {
                    'title': '通过/失败',
                    'value': f"{summary.get('passed_tests', 0)}/{summary.get('failed_tests', 0)}",
                    'short': True
                },
                {
                    'title': '执行时间',
                    'value': f"{summary.get('total_duration', 0):.2f}s",
                    'short': True
                }
            ],
            'footer': 'Connect CI/CD',
            'ts': int(datetime.now().timestamp())
        }]
        
        # 添加告警信息
        if alerts:
            alert_text = '\n'.join([f"• {alert['message']}" for alert in alerts])
            attachments.append({
                'color': 'danger',
                'title': '⚠️ 告警信息',
                'text': alert_text
            })
        
        return {
            'text': '测试执行完成',
            'attachments': attachments
        }
    
    def _send_email_notification(self, results: Dict[str, Any], alerts: List[Dict[str, Any]]) -> None:
        """发送邮件通知"""
        try:
            if not self.config['email']['to_emails']:
                logger.warning("No email recipients configured")
                return
            
            # 构建邮件内容
            subject, body = self._build_email_content(results, alerts)
            
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.config['email']['from_email']
            msg['To'] = ', '.join(self.config['email']['to_emails'])
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'html'))
            
            # 发送邮件
            with smtplib.SMTP(self.config['email']['smtp_server'], self.config['email']['smtp_port']) as server:
                server.starttls()
                server.login(self.config['email']['username'], self.config['email']['password'])
                server.send_message(msg)
            
            logger.info("Email notification sent")
            
        except Exception as e:
            logger.error(f"Error sending email notification: {e}")
    
    def _build_email_content(self, results: Dict[str, Any], alerts: List[Dict[str, Any]]) -> tuple:
        """构建邮件内容"""
        summary = results.get('summary', {})
        environment = results.get('environment', {})
        
        # 主题
        status = '✅ 成功' if not alerts else '⚠️ 有问题'
        subject = f"Connect测试报告 - {status} - {environment.get('branch', 'unknown')}"
        
        # 邮件正文
        body = f"""
        <html>
        <body>
            <h2>Connect电信平台测试报告</h2>
            <p><strong>分支:</strong> {environment.get('branch', 'unknown')}</p>
            <p><strong>提交:</strong> {environment.get('commit_sha', 'unknown')[:8]}</p>
            <p><strong>时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <h3>测试概要</h3>
            <table border="1" style="border-collapse: collapse;">
                <tr><td>测试成功率</td><td>{summary.get('success_rate', 0):.1f}%</td></tr>
                <tr><td>总测试数</td><td>{summary.get('total_tests', 0)}</td></tr>
                <tr><td>通过测试</td><td>{summary.get('passed_tests', 0)}</td></tr>
                <tr><td>失败测试</td><td>{summary.get('failed_tests', 0)}</td></tr>
                <tr><td>执行时间</td><td>{summary.get('total_duration', 0):.2f}s</td></tr>
            </table>
        """
        
        if alerts:
            body += "<h3>⚠️ 告警信息</h3><ul>"
            for alert in alerts:
                body += f"<li>{alert['message']}</li>"
            body += "</ul>"
        
        body += """
        </body>
        </html>
        """
        
        return subject, body
    
    def _send_webhook_notification(self, results: Dict[str, Any], alerts: List[Dict[str, Any]]) -> None:
        """发送Webhook通知"""
        try:
            payload = {
                'timestamp': datetime.now().isoformat(),
                'results': results,
                'alerts': alerts,
                'summary': {
                    'success_rate': results.get('summary', {}).get('success_rate', 0),
                    'total_tests': results.get('summary', {}).get('total_tests', 0),
                    'alert_count': len(alerts),
                    'critical_alerts': len([a for a in alerts if a['severity'] == 'critical'])
                }
            }
            
            headers = self.config['webhook']['headers'].copy()
            headers['Content-Type'] = 'application/json'
            
            response = requests.post(
                self.config['webhook']['url'],
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info("Webhook notification sent")
            else:
                logger.error(f"Failed to send webhook: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Error sending webhook notification: {e}")
    
    def generate_dashboard(self, dashboard_data_file: str, output_dir: str = None) -> bool:
        """生成本地HTML dashboard
        
        Args:
            dashboard_data_file: dashboard数据文件路径
            output_dir: 输出目录
            
        Returns:
            bool: 生成是否成功
        """
        try:
            dashboard_data = self.load_dashboard_data(dashboard_data_file)
            if not dashboard_data:
                logger.error("No dashboard data to generate")
                return False
            
            output_dir = output_dir or 'dashboard'
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成HTML dashboard
            html_content = self._generate_html_dashboard(dashboard_data)
            
            # 写入HTML文件
            html_file = os.path.join(output_dir, 'index.html')
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # 生成CSS文件
            css_content = self._generate_css()
            css_file = os.path.join(output_dir, 'style.css')
            with open(css_file, 'w', encoding='utf-8') as f:
                f.write(css_content)
            
            # 生成JavaScript文件
            js_content = self._generate_javascript(dashboard_data)
            js_file = os.path.join(output_dir, 'dashboard.js')
            with open(js_file, 'w', encoding='utf-8') as f:
                f.write(js_content)
            
            logger.info(f"Dashboard generated successfully at {html_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error generating dashboard: {e}")
            return False
    
    def _generate_html_dashboard(self, dashboard_data: Dict[str, Any]) -> str:
        """生成HTML dashboard内容"""
        template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connect电信平台测试监控Dashboard</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>Connect电信平台测试监控Dashboard</h1>
            <div class="timestamp">最后更新: {{ timestamp }}</div>
        </header>
        
        <div class="overview-section">
            <div class="metric-card success-rate">
                <h3>测试成功率</h3>
                <div class="metric-value">{{ overview.success_rate }}%</div>
                <div class="metric-status {{ overview.success_rate_status }}">{{ overview.success_rate_text }}</div>
            </div>
            
            <div class="metric-card total-tests">
                <h3>总测试数</h3>
                <div class="metric-value">{{ overview.total_tests }}</div>
                <div class="metric-breakdown">
                    <span class="passed">通过: {{ overview.passed_tests }}</span>
                    <span class="failed">失败: {{ overview.failed_tests }}</span>
                </div>
            </div>
            
            <div class="metric-card coverage">
                <h3>代码覆盖率</h3>
                <div class="metric-value">{{ overview.coverage }}%</div>
                <div class="metric-status {{ overview.coverage_status }}">{{ overview.coverage_text }}</div>
            </div>
            
            <div class="metric-card security">
                <h3>安全问题</h3>
                <div class="metric-value">{{ overview.security_issues }}</div>
                <div class="metric-status {{ overview.security_status }}">{{ overview.security_text }}</div>
            </div>
        </div>
        
        <div class="charts-section">
            <div class="chart-container">
                <h3>测试趋势</h3>
                <canvas id="testTrendChart"></canvas>
            </div>
            
            <div class="chart-container">
                <h3>性能基准</h3>
                <canvas id="performanceChart"></canvas>
            </div>
            
            <div class="chart-container">
                <h3>安全扫描结果</h3>
                <canvas id="securityChart"></canvas>
            </div>
            
            <div class="chart-container">
                <h3>质量指标</h3>
                <canvas id="qualityChart"></canvas>
            </div>
        </div>
        
        {% if alerts %}
        <div class="alerts-section">
            <h3>⚠️ 告警信息</h3>
            <div class="alerts-list">
                {% for alert in alerts %}
                <div class="alert alert-{{ alert.severity }}">
                    <div class="alert-title">{{ alert.type | title }}</div>
                    <div class="alert-message">{{ alert.message }}</div>
                    <div class="alert-time">{{ alert.timestamp }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <div class="details-section">
            <div class="details-grid">
                <div class="detail-card">
                    <h4>测试详情</h4>
                    <table>
                        <tr><td>单元测试</td><td>{{ details.unit_tests.passed }}/{{ details.unit_tests.total }}</td></tr>
                        <tr><td>集成测试</td><td>{{ details.integration_tests.passed }}/{{ details.integration_tests.total }}</td></tr>
                        <tr><td>E2E测试</td><td>{{ details.e2e_tests.passed }}/{{ details.e2e_tests.total }}</td></tr>
                        <tr><td>性能测试</td><td>{{ details.performance_tests.passed }}/{{ details.performance_tests.total }}</td></tr>
                    </table>
                </div>
                
                <div class="detail-card">
                    <h4>覆盖率详情</h4>
                    <table>
                        <tr><td>行覆盖率</td><td>{{ details.coverage.line }}%</td></tr>
                        <tr><td>分支覆盖率</td><td>{{ details.coverage.branch }}%</td></tr>
                        <tr><td>函数覆盖率</td><td>{{ details.coverage.function }}%</td></tr>
                        <tr><td>语句覆盖率</td><td>{{ details.coverage.statement }}%</td></tr>
                    </table>
                </div>
                
                <div class="detail-card">
                    <h4>性能指标</h4>
                    <table>
                        {% for benchmark in details.performance %}
                        <tr><td>{{ benchmark.name }}</td><td>{{ benchmark.duration }}s</td></tr>
                        {% endfor %}
                    </table>
                </div>
                
                <div class="detail-card">
                    <h4>环境信息</h4>
                    <table>
                        <tr><td>分支</td><td>{{ environment.branch }}</td></tr>
                        <tr><td>提交</td><td>{{ environment.commit_sha[:8] }}</td></tr>
                        <tr><td>工作流</td><td>{{ environment.workflow_run_id }}</td></tr>
                        <tr><td>执行时间</td><td>{{ environment.execution_time }}</td></tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <script src="dashboard.js"></script>
</body>
</html>
        """
        
        # 使用Jinja2模板渲染
        from jinja2 import Template
        template_obj = Template(template)
        
        return template_obj.render(**dashboard_data)
    
    def _generate_css(self) -> str:
        """生成CSS样式"""
        return """
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.timestamp {
    font-size: 1.1em;
    opacity: 0.9;
}

.overview-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
}

.metric-card h3 {
    color: #666;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.metric-value {
    font-size: 3em;
    font-weight: bold;
    margin-bottom: 10px;
}

.success-rate .metric-value {
    color: #28a745;
}

.total-tests .metric-value {
    color: #007bff;
}

.coverage .metric-value {
    color: #17a2b8;
}

.security .metric-value {
    color: #dc3545;
}

.metric-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9em;
    font-weight: bold;
}

.metric-status.good {
    background-color: #d4edda;
    color: #155724;
}

.metric-status.warning {
    background-color: #fff3cd;
    color: #856404;
}

.metric-status.critical {
    background-color: #f8d7da;
    color: #721c24;
}

.metric-breakdown {
    display: flex;
    justify-content: space-around;
    margin-top: 10px;
}

.metric-breakdown .passed {
    color: #28a745;
}

.metric-breakdown .failed {
    color: #dc3545;
}

.charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.alerts-section {
    margin-bottom: 30px;
}

.alerts-section h3 {
    margin-bottom: 20px;
    color: #dc3545;
}

.alerts-list {
    display: grid;
    gap: 15px;
}

.alert {
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid;
}

.alert-critical {
    background-color: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-left-color: #ffc107;
    color: #856404;
}

.alert-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.alert-time {
    font-size: 0.9em;
    opacity: 0.7;
    margin-top: 5px;
}

.details-section {
    margin-bottom: 30px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.detail-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.detail-card h4 {
    margin-bottom: 15px;
    color: #333;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

.detail-card table {
    width: 100%;
    border-collapse: collapse;
}

.detail-card table td {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.detail-card table td:first-child {
    font-weight: bold;
    color: #666;
}

.detail-card table td:last-child {
    text-align: right;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .overview-section {
        grid-template-columns: 1fr;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .details-grid {
        grid-template-columns: 1fr;
    }
}
        """
    
    def _generate_javascript(self, dashboard_data: Dict[str, Any]) -> str:
        """生成JavaScript代码"""
        return f"""
// Dashboard数据
const dashboardData = {json.dumps(dashboard_data, indent=2)};

// 初始化图表
document.addEventListener('DOMContentLoaded', function() {{
    initializeCharts();
    
    // 每30秒刷新一次
    setInterval(function() {{
        location.reload();
    }}, 30000);
}});

function initializeCharts() {{
    // 测试趋势图
    const testTrendCtx = document.getElementById('testTrendChart').getContext('2d');
    new Chart(testTrendCtx, {{
        type: 'line',
        data: {{
            labels: dashboardData.charts.test_trend.labels,
            datasets: [{{
                label: '成功率',
                data: dashboardData.charts.test_trend.success_rate,
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }}, {{
                label: '覆盖率',
                data: dashboardData.charts.test_trend.coverage,
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.1)',
                tension: 0.4
            }}]
        }},
        options: {{
            responsive: true,
            scales: {{
                y: {{
                    beginAtZero: true,
                    max: 100
                }}
            }}
        }}
    }});
    
    // 性能基准图
    const performanceCtx = document.getElementById('performanceChart').getContext('2d');
    new Chart(performanceCtx, {{
        type: 'bar',
        data: {{
            labels: dashboardData.charts.performance.labels,
            datasets: [{{
                label: '执行时间 (秒)',
                data: dashboardData.charts.performance.durations,
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#6f42c1'
                ]
            }}]
        }},
        options: {{
            responsive: true,
            scales: {{
                y: {{
                    beginAtZero: true
                }}
            }}
        }}
    }});
    
    // 安全扫描结果图
    const securityCtx = document.getElementById('securityChart').getContext('2d');
    new Chart(securityCtx, {{
        type: 'doughnut',
        data: {{
            labels: dashboardData.charts.security.labels,
            datasets: [{{
                data: dashboardData.charts.security.counts,
                backgroundColor: [
                    '#dc3545',  // Critical
                    '#fd7e14',  // High
                    '#ffc107',  // Medium
                    '#28a745'   // Low
                ]
            }}]
        }},
        options: {{
            responsive: true,
            plugins: {{
                legend: {{
                    position: 'bottom'
                }}
            }}
        }}
    }});
    
    // 质量指标图
    const qualityCtx = document.getElementById('qualityChart').getContext('2d');
    new Chart(qualityCtx, {{
        type: 'radar',
        data: {{
            labels: dashboardData.charts.quality.labels,
            datasets: [{{
                label: '质量分数',
                data: dashboardData.charts.quality.scores,
                borderColor: '#6f42c1',
                backgroundColor: 'rgba(111, 66, 193, 0.2)',
                pointBackgroundColor: '#6f42c1'
            }}]
        }},
        options: {{
            responsive: true,
            scales: {{
                r: {{
                    beginAtZero: true,
                    max: 100
                }}
            }}
        }}
    }});
}}

// 工具函数
function formatDuration(seconds) {{
    if (seconds < 60) {{
        return seconds.toFixed(2) + 's';
    }} else if (seconds < 3600) {{
        return (seconds / 60).toFixed(1) + 'm';
    }} else {{
        return (seconds / 3600).toFixed(1) + 'h';
    }}
}}

function formatTimestamp(timestamp) {{
    return new Date(timestamp).toLocaleString('zh-CN');
}}
        """


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Connect测试监控Dashboard')
    parser.add_argument('--results-file', required=True, help='测试结果文件路径')
    parser.add_argument('--dashboard-data-file', help='Dashboard数据文件路径')
    parser.add_argument('--output-dir', default='dashboard', help='输出目录')
    parser.add_argument('--config-file', help='配置文件路径')
    parser.add_argument('--send-notifications', action='store_true', help='发送通知')
    parser.add_argument('--generate-dashboard', action='store_true', help='生成dashboard')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)
    
    # 创建监控dashboard实例
    dashboard = MonitoringDashboard(config_file=args.config_file)
    
    success = True
    
    # 发送测试结果到监控系统
    if args.send_notifications:
        logger.info("Sending test results to monitoring systems...")
        success &= dashboard.send_test_results(args.results_file)
    
    # 生成本地dashboard
    if args.generate_dashboard:
        logger.info("Generating local dashboard...")
        dashboard_data_file = args.dashboard_data_file or args.results_file
        success &= dashboard.generate_dashboard(dashboard_data_file, args.output_dir)
    
    if success:
        logger.info("Monitoring dashboard operations completed successfully")
        sys.exit(0)
    else:
        logger.error("Some monitoring dashboard operations failed")
        sys.exit(1)


if __name__ == '__main__':
    main()
        logger.info(f"发送测试结果到监控系统: {results_file}")
        
        try:
            # 加载测试结果
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 发送到各个监控端点
            success = True
            
            # 发送到Prometheus
            if self.config["monitoring_endpoints"]["prometheus"]:
                success &= self._send_to_prometheus(results)
            
            # 发送到Grafana
            if self.config["monitoring_endpoints"]["grafana"]:
                success &= self._send_to_grafana(results)
            
            # 发送到DataDog
            if self.config["monitoring_endpoints"]["datadog"]:
                success &= self._send_to_datadog(results)
            
            # 检查告警条件
            self._check_alerts(results)
            
            # 发送告警通知
            if self.dashboard_data["alerts"]:
                self._send_alert_notifications()
            
            logger.info(f"测试结果发送完成，成功: {success}")
            return success
            
        except Exception as e:
            logger.error(f"发送测试结果失败: {e}")
            return False
    
    def _send_to_prometheus(self, results: Dict[str, Any]) -> bool:
        """发送指标到Prometheus Pushgateway
        
        Args:
            results: 测试结果
            
        Returns:
            是否发送成功
        """
        try:
            pushgateway_url = self.config["monitoring_endpoints"]["prometheus"]
            if not pushgateway_url:
                return True
            
            # 构建Prometheus指标
            metrics = []
            overall = results["summary"]["overall"]
            
            # 基本测试指标
            metrics.extend([
                f"test_total{{job=\"connect_tests\"}} {overall['total']}",
                f"test_passed{{job=\"connect_tests\"}} {overall['passed']}",
                f"test_failed{{job=\"connect_tests\"}} {overall['failed']}",
                f"test_skipped{{job=\"connect_tests\"}} {overall['skipped']}",
                f"test_pass_rate{{job=\"connect_tests\"}} {overall['pass_rate']}",
                f"test_duration{{job=\"connect_tests\"}} {overall['duration']}"
            ])
            
            # 质量指标
            quality_metrics = results["metrics"]
            metrics.extend([
                f"code_coverage{{job=\"connect_tests\"}} {quality_metrics['code_coverage']}",
                f"security_score{{job=\"connect_tests\"}} {quality_metrics['security_score']}",
                f"performance_score{{job=\"connect_tests\"}} {quality_metrics['performance_score']}",
                f"overall_quality_score{{job=\"connect_tests\"}} {quality_metrics['overall_quality_score']}"
            ])
            
            # 分类测试指标
            for test_type, test_results in results["details"].items():
                metrics.extend([
                    f"test_{test_type}_total{{job=\"connect_tests\"}} {test_results.get('total', 0)}",
                    f"test_{test_type}_passed{{job=\"connect_tests\"}} {test_results.get('passed', 0)}",
                    f"test_{test_type}_failed{{job=\"connect_tests\"}} {test_results.get('failed', 0)}",
                    f"test_{test_type}_pass_rate{{job=\"connect_tests\"}} {test_results.get('pass_rate', 0)}"
                ])
            
            # 发送指标
            metrics_data = "\n".join(metrics)
            response = requests.post(
                f"{pushgateway_url}/metrics/job/connect_tests",
                data=metrics_data,
                headers={'Content-Type': 'text/plain'},
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info("成功发送指标到Prometheus")
                return True
            else:
                logger.error(f"发送Prometheus指标失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"发送Prometheus指标异常: {e}")
            return False
    
    def _send_to_grafana(self, results: Dict[str, Any]) -> bool:
        """发送数据到Grafana
        
        Args:
            results: 测试结果
            
        Returns:
            是否发送成功
        """
        try:
            grafana_url = self.config["monitoring_endpoints"]["grafana"]
            if not grafana_url:
                return True
            
            # 构建Grafana注解
            annotation = {
                "time": int(datetime.now().timestamp() * 1000),
                "timeEnd": int(datetime.now().timestamp() * 1000),
                "tags": ["test-results", "ci-cd"],
                "text": f"测试完成: {results['summary']['overall']['passed']}/{results['summary']['overall']['total']} 通过"
            }
            
            # 发送注解
            response = requests.post(
                f"{grafana_url}/api/annotations",
                json=annotation,
                headers={
                    'Authorization': f"Bearer {os.getenv('GRAFANA_API_TOKEN')}",
                    'Content-Type': 'application/json'
                },
                timeout=30
            )
            
            if response.status_code in [200, 201]:
                logger.info("成功发送注解到Grafana")
                return True
            else:
                logger.error(f"发送Grafana注解失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"发送Grafana注解异常: {e}")
            return False
    
    def _send_to_datadog(self, results: Dict[str, Any]) -> bool:
        """发送指标到DataDog
        
        Args:
            results: 测试结果
            
        Returns:
            是否发送成功
        """
        try:
            datadog_url = self.config["monitoring_endpoints"]["datadog"]
            if not datadog_url:
                return True
            
            # 构建DataDog指标
            timestamp = int(datetime.now().timestamp())
            overall = results["summary"]["overall"]
            
            metrics = {
                "series": [
                    {
                        "metric": "connect.tests.total",
                        "points": [[timestamp, overall["total"]]],
                        "tags": ["env:ci", "service:connect"]
                    },
                    {
                        "metric": "connect.tests.passed",
                        "points": [[timestamp, overall["passed"]]],
                        "tags": ["env:ci", "service:connect"]
                    },
                    {
                        "metric": "connect.tests.pass_rate",
                        "points": [[timestamp, overall["pass_rate"]]],
                        "tags": ["env:ci", "service:connect"]
                    },
                    {
                        "metric": "connect.quality.overall_score",
                        "points": [[timestamp, results["metrics"]["overall_quality_score"]]],
                        "tags": ["env:ci", "service:connect"]
                    }
                ]
            }
            
            # 发送指标
            response = requests.post(
                f"{datadog_url}/api/v1/series",
                json=metrics,
                headers={
                    'DD-API-KEY': os.getenv('DATADOG_API_KEY'),
                    'Content-Type': 'application/json'
                },
                timeout=30
            )
            
            if response.status_code == 202:
                logger.info("成功发送指标到DataDog")
                return True
            else:
                logger.error(f"发送DataDog指标失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"发送DataDog指标异常: {e}")
            return False
    
    def _check_alerts(self, results: Dict[str, Any]) -> None:
        """检查告警条件
        
        Args:
            results: 测试结果
        """
        thresholds = self.config["thresholds"]
        overall = results["summary"]["overall"]
        metrics = results["metrics"]
        
        # 检查通过率告警
        pass_rate = overall["pass_rate"]
        if pass_rate < thresholds["pass_rate_critical"]:
            self.dashboard_data["alerts"].append({
                "level": "critical",
                "type": "pass_rate",
                "message": f"测试通过率严重偏低: {pass_rate}% (阈值: {thresholds['pass_rate_critical']}%)",
                "value": pass_rate,
                "threshold": thresholds["pass_rate_critical"]
            })
        elif pass_rate < thresholds["pass_rate_warning"]:
            self.dashboard_data["alerts"].append({
                "level": "warning",
                "type": "pass_rate",
                "message": f"测试通过率偏低: {pass_rate}% (阈值: {thresholds['pass_rate_warning']}%)",
                "value": pass_rate,
                "threshold": thresholds["pass_rate_warning"]
            })
        
        # 检查代码覆盖率告警
        coverage = metrics["code_coverage"]
        if coverage < thresholds["coverage_critical"]:
            self.dashboard_data["alerts"].append({
                "level": "critical",
                "type": "coverage",
                "message": f"代码覆盖率严重偏低: {coverage}% (阈值: {thresholds['coverage_critical']}%)",
                "value": coverage,
                "threshold": thresholds["coverage_critical"]
            })
        elif coverage < thresholds["coverage_warning"]:
            self.dashboard_data["alerts"].append({
                "level": "warning",
                "type": "coverage",
                "message": f"代码覆盖率偏低: {coverage}% (阈值: {thresholds['coverage_warning']}%)",
                "value": coverage,
                "threshold": thresholds["coverage_warning"]
            })
        
        # 检查安全评分告警
        security_score = metrics["security_score"]
        if security_score < thresholds["security_score_critical"]:
            self.dashboard_data["alerts"].append({
                "level": "critical",
                "type": "security",
                "message": f"安全评分严重偏低: {security_score} (阈值: {thresholds['security_score_critical']})",
                "value": security_score,
                "threshold": thresholds["security_score_critical"]
            })
        elif security_score < thresholds["security_score_warning"]:
            self.dashboard_data["alerts"].append({
                "level": "warning",
                "type": "security",
                "message": f"安全评分偏低: {security_score} (阈值: {thresholds['security_score_warning']})",
                "value": security_score,
                "threshold": thresholds["security_score_warning"]
            })
        
        # 检查性能告警
        performance_data = results["details"].get("performance", {})
        if performance_data.get("metrics"):
            avg_response_time = performance_data["metrics"].get("avg_response_time", 0)
            if avg_response_time > thresholds["performance_critical"]:
                self.dashboard_data["alerts"].append({
                    "level": "critical",
                    "type": "performance",
                    "message": f"平均响应时间过长: {avg_response_time}s (阈值: {thresholds['performance_critical']}s)",
                    "value": avg_response_time,
                    "threshold": thresholds["performance_critical"]
                })
            elif avg_response_time > thresholds["performance_warning"]:
                self.dashboard_data["alerts"].append({
                    "level": "warning",
                    "type": "performance",
                    "message": f"平均响应时间较长: {avg_response_time}s (阈值: {thresholds['performance_warning']}s)",
                    "value": avg_response_time,
                    "threshold": thresholds["performance_warning"]
                })
    
    def _send_alert_notifications(self) -> None:
        """发送告警通知"""
        logger.info(f"发送告警通知，共 {len(self.dashboard_data['alerts'])} 个告警")
        
        # 发送到Slack
        if self.config["webhook_urls"]["slack"]:
            self._send_slack_notification()
        
        # 发送到Teams
        if self.config["webhook_urls"]["teams"]:
            self._send_teams_notification()
        
        # 发送到Discord
        if self.config["webhook_urls"]["discord"]:
            self._send_discord_notification()
    
    def _send_slack_notification(self) -> None:
        """发送Slack通知"""
        try:
            webhook_url = self.config["webhook_urls"]["slack"]
            
            # 构建Slack消息
            critical_alerts = [a for a in self.dashboard_data["alerts"] if a["level"] == "critical"]
            warning_alerts = [a for a in self.dashboard_data["alerts"] if a["level"] == "warning"]
            
            color = "danger" if critical_alerts else "warning"
            title = "🚨 测试告警" if critical_alerts else "⚠️ 测试警告"
            
            fields = []
            for alert in self.dashboard_data["alerts"]:
                fields.append({
                    "title": f"{alert['type'].upper()} - {alert['level'].upper()}",
                    "value": alert["message"],
                    "short": False
                })
            
            message = {
                "attachments": [{
                    "color": color,
                    "title": title,
                    "fields": fields,
                    "footer": "Connect CI/CD Pipeline",
                    "ts": int(datetime.now().timestamp())
                }]
            }
            
            response = requests.post(webhook_url, json=message, timeout=30)
            
            if response.status_code == 200:
                logger.info("成功发送Slack通知")
            else:
                logger.error(f"发送Slack通知失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"发送Slack通知异常: {e}")
    
    def _send_teams_notification(self) -> None:
        """发送Teams通知"""
        try:
            webhook_url = self.config["webhook_urls"]["teams"]
            
            # 构建Teams消息
            critical_alerts = [a for a in self.dashboard_data["alerts"] if a["level"] == "critical"]
            
            theme_color = "FF0000" if critical_alerts else "FFA500"
            title = "🚨 测试告警" if critical_alerts else "⚠️ 测试警告"
            
            facts = []
            for alert in self.dashboard_data["alerts"]:
                facts.append({
                    "name": f"{alert['type'].upper()} - {alert['level'].upper()}",
                    "value": alert["message"]
                })
            
            message = {
                "@type": "MessageCard",
                "@context": "http://schema.org/extensions",
                "themeColor": theme_color,
                "summary": title,
                "sections": [{
                    "activityTitle": title,
                    "facts": facts
                }]
            }
            
            response = requests.post(webhook_url, json=message, timeout=30)
            
            if response.status_code == 200:
                logger.info("成功发送Teams通知")
            else:
                logger.error(f"发送Teams通知失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"发送Teams通知异常: {e}")
    
    def _send_discord_notification(self) -> None:
        """发送Discord通知"""
        try:
            webhook_url = self.config["webhook_urls"]["discord"]
            
            # 构建Discord消息
            critical_alerts = [a for a in self.dashboard_data["alerts"] if a["level"] == "critical"]
            
            color = 0xFF0000 if critical_alerts else 0xFFA500
            title = "🚨 测试告警" if critical_alerts else "⚠️ 测试警告"
            
            fields = []
            for alert in self.dashboard_data["alerts"]:
                fields.append({
                    "name": f"{alert['type'].upper()} - {alert['level'].upper()}",
                    "value": alert["message"],
                    "inline": False
                })
            
            message = {
                "embeds": [{
                    "title": title,
                    "color": color,
                    "fields": fields,
                    "footer": {
                        "text": "Connect CI/CD Pipeline"
                    },
                    "timestamp": datetime.now().isoformat()
                }]
            }
            
            response = requests.post(webhook_url, json=message, timeout=30)
            
            if response.status_code == 204:
                logger.info("成功发送Discord通知")
            else:
                logger.error(f"发送Discord通知失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"发送Discord通知异常: {e}")
    
    def generate_dashboard(self, results_file: str, output_dir: str) -> str:
        """生成可视化仪表板
        
        Args:
            results_file: 测试结果文件路径
            output_dir: 输出目录
            
        Returns:
            仪表板HTML文件路径
        """
        logger.info(f"生成可视化仪表板: {output_dir}")
        
        try:
            # 加载测试结果
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            # 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 生成图表
            self._generate_charts(results, output_path)
            
            # 生成HTML仪表板
            dashboard_file = self._generate_html_dashboard(results, output_path)
            
            logger.info(f"仪表板生成完成: {dashboard_file}")
            return str(dashboard_file)
            
        except Exception as e:
            logger.error(f"生成仪表板失败: {e}")
            raise
    
    def _generate_charts(self, results: Dict[str, Any], output_path: Path) -> None:
        """生成图表
        
        Args:
            results: 测试结果
            output_path: 输出路径
        """
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 1. 测试结果总览饼图
        self._generate_test_overview_chart(results, output_path)
        
        # 2. 各类测试通过率柱状图
        self._generate_pass_rate_chart(results, output_path)
        
        # 3. 质量指标雷达图
        self._generate_quality_radar_chart(results, output_path)
        
        # 4. 性能趋势图
        self._generate_performance_chart(results, output_path)
        
        # 5. 安全漏洞分布图
        self._generate_security_chart(results, output_path)
    
    def _generate_test_overview_chart(self, results: Dict[str, Any], output_path: Path) -> None:
        """生成测试结果总览饼图"""
        overall = results["summary"]["overall"]
        
        labels = ['通过', '失败', '跳过']
        sizes = [overall['passed'], overall['failed'], overall['skipped']]
        colors = ['#2ecc71', '#e74c3c', '#f39c12']
        
        plt.figure(figsize=(10, 8))
        plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        plt.title('测试结果总览', fontsize=16, fontweight='bold')
        plt.axis('equal')
        
        plt.tight_layout()
        plt.savefig(output_path / 'test_overview.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_pass_rate_chart(self, results: Dict[str, Any], output_path: Path) -> None:
        """生成各类测试通过率柱状图"""
        test_types = []
        pass_rates = []
        
        for test_type, test_results in results["details"].items():
            test_types.append(test_type.upper())
            pass_rates.append(test_results.get('pass_rate', 0))
        
        plt.figure(figsize=(12, 8))
        bars = plt.bar(test_types, pass_rates, color=['#3498db', '#9b59b6', '#1abc9c', '#f1c40f', '#e67e22'])
        
        # 添加数值标签
        for bar, rate in zip(bars, pass_rates):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        plt.title('各类测试通过率', fontsize=16, fontweight='bold')
        plt.xlabel('测试类型', fontsize=12)
        plt.ylabel('通过率 (%)', fontsize=12)
        plt.ylim(0, 105)
        plt.grid(axis='y', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path / 'pass_rate.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_quality_radar_chart(self, results: Dict[str, Any], output_path: Path) -> None:
        """生成质量指标雷达图"""
        metrics = results["metrics"]
        
        categories = ['代码覆盖率', '安全评分', '性能评分', '可靠性评分']
        values = [
            metrics['code_coverage'],
            metrics['security_score'],
            metrics['performance_score'],
            metrics['reliability_score']
        ]
        
        # 计算角度
        angles = [n / float(len(categories)) * 2 * 3.14159 for n in range(len(categories))]
        angles += angles[:1]  # 闭合图形
        values += values[:1]
        
        plt.figure(figsize=(10, 10))
        ax = plt.subplot(111, projection='polar')
        
        # 绘制雷达图
        ax.plot(angles, values, 'o-', linewidth=2, label='当前评分')
        ax.fill(angles, values, alpha=0.25)
        
        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_ylim(0, 100)
        
        # 添加网格线
        ax.grid(True)
        
        plt.title('质量指标雷达图', size=16, fontweight='bold', pad=20)
        plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        plt.tight_layout()
        plt.savefig(output_path / 'quality_radar.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_performance_chart(self, results: Dict[str, Any], output_path: Path) -> None:
        """生成性能趋势图"""
        performance_data = results["details"].get("performance", {})
        
        if not performance_data.get("benchmarks"):
            # 如果没有性能数据，创建空图表
            plt.figure(figsize=(12, 8))
            plt.text(0.5, 0.5, '暂无性能测试数据', ha='center', va='center', 
                    transform=plt.gca().transAxes, fontsize=16)
            plt.title('性能基准测试结果', fontsize=16, fontweight='bold')
            plt.axis('off')
        else:
            benchmarks = performance_data["benchmarks"]
            
            names = [b["name"] for b in benchmarks]
            means = [b["mean"] for b in benchmarks]
            
            plt.figure(figsize=(14, 8))
            bars = plt.bar(range(len(names)), means, color='#3498db')
            
            # 添加数值标签
            for bar, mean in zip(bars, means):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(means) * 0.01,
                        f'{mean:.3f}s', ha='center', va='bottom', fontweight='bold')
            
            plt.title('性能基准测试结果', fontsize=16, fontweight='bold')
            plt.xlabel('测试项目', fontsize=12)
            plt.ylabel('平均响应时间 (秒)', fontsize=12)
            plt.xticks(range(len(names)), names, rotation=45, ha='right')
            plt.grid(axis='y', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path / 'performance.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_security_chart(self, results: Dict[str, Any], output_path: Path) -> None:
        """生成安全漏洞分布图"""
        security_data = results["details"].get("security", {})
        vulnerabilities = security_data.get("vulnerabilities", [])
        
        if not vulnerabilities:
            # 如果没有漏洞，显示安全状态良好
            plt.figure(figsize=(10, 8))
            plt.text(0.5, 0.5, '🛡️ 未发现安全漏洞\n安全状态良好', ha='center', va='center',
                    transform=plt.gca().transAxes, fontsize=20, color='green')
            plt.title('安全漏洞分析', fontsize=16, fontweight='bold')
            plt.axis('off')
        else:
            # 统计漏洞严重程度
            severity_counts = {}
            for vuln in vulnerabilities:
                severity = vuln.get('severity', 'UNKNOWN').upper()
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            severities = list(severity_counts.keys())
            counts = list(severity_counts.values())
            
            # 设置颜色
            color_map = {
                'CRITICAL': '#8B0000',
                'HIGH': '#FF0000',
                'MEDIUM': '#FFA500',
                'LOW': '#FFFF00',
                'UNKNOWN': '#808080'
            }
            colors = [color_map.get(s, '#808080') for s in severities]
            
            plt.figure(figsize=(10, 8))
            bars = plt.bar(severities, counts, color=colors)
            
            # 添加数值标签
            for bar, count in zip(bars, counts):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                        str(count), ha='center', va='bottom', fontweight='bold')
            
            plt.title('安全漏洞分布', fontsize=16, fontweight='bold')
            plt.xlabel('严重程度', fontsize=12)
            plt.ylabel('漏洞数量', fontsize=12)
            plt.grid(axis='y', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path / 'security.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _generate_html_dashboard(self, results: Dict[str, Any], output_path: Path) -> Path:
        """生成HTML仪表板
        
        Args:
            results: 测试结果
            output_path: 输出路径
            
        Returns:
            HTML文件路径
        """
        # HTML模板
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connect 测试监控仪表板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .metric-label {
            font-size: 1.1em;
            color: #666;
        }
        
        .success { color: #27ae60; }
        .warning { color: #f39c12; }
        .danger { color: #e74c3c; }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .chart-card h3 {
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        
        .chart-card img {
            width: 100%;
            height: auto;
            border-radius: 5px;
        }
        
        .alerts {
            margin-bottom: 30px;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .alert-critical {
            background-color: #fdf2f2;
            border-color: #e74c3c;
            color: #c0392b;
        }
        
        .alert-warning {
            background-color: #fef9e7;
            border-color: #f39c12;
            color: #d68910;
        }
        
        .details-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .details-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .details-table th,
        .details-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .details-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .details-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            border-top: 1px solid #eee;
            margin-top: 30px;
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Connect 测试监控仪表板</h1>
            <p>生成时间: {{ timestamp }}</p>
        </div>
        
        <!-- 关键指标 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value {{ 'success' if overall.pass_rate >= 90 else 'warning' if overall.pass_rate >= 80 else 'danger' }}">
                    {{ overall.pass_rate }}%
                </div>
                <div class="metric-label">总体通过率</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value {{ 'success' if metrics.code_coverage >= 80 else 'warning' if metrics.code_coverage >= 70 else 'danger' }}">
                    {{ metrics.code_coverage }}%
                </div>
                <div class="metric-label">代码覆盖率</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value {{ 'success' if metrics.security_score >= 80 else 'warning' if metrics.security_score >= 70 else 'danger' }}">
                    {{ metrics.security_score }}
                </div>
                <div class="metric-label">安全评分</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-value {{ 'success' if metrics.overall_quality_score >= 80 else 'warning' if metrics.overall_quality_score >= 70 else 'danger' }}">
                    {{ metrics.overall_quality_score }}
                </div>
                <div class="metric-label">综合质量评分</div>
            </div>
        </div>
        
        <!-- 告警信息 -->
        {% if alerts %}
        <div class="alerts">
            <h2>🚨 告警信息</h2>
            {% for alert in alerts %}
            <div class="alert alert-{{ alert.level }}">
                <strong>{{ alert.type.upper() }}</strong>: {{ alert.message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- 图表展示 -->
        <div class="charts-grid">
            <div class="chart-card">
                <h3>📊 测试结果总览</h3>
                <img src="test_overview.png" alt="测试结果总览">
            </div>
            
            <div class="chart-card">
                <h3>📈 各类测试通过率</h3>
                <img src="pass_rate.png" alt="各类测试通过率">
            </div>
            
            <div class="chart-card">
                <h3>🎯 质量指标雷达图</h3>
                <img src="quality_radar.png" alt="质量指标雷达图">
            </div>
            
            <div class="chart-card">
                <h3>⚡ 性能基准测试</h3>
                <img src="performance.png" alt="性能基准测试">
            </div>
            
            <div class="chart-card">
                <h3>🛡️ 安全漏洞分析</h3>
                <img src="security.png" alt="安全漏洞分析">
            </div>
        </div>
        
        <!-- 详细数据表格 -->
        <div class="details-table">
            <h2>📋 详细测试数据</h2>
            <table>
                <thead>
                    <tr>
                        <th>测试类型</th>
                        <th>总数</th>
                        <th>通过</th>
                        <th>失败</th>
                        <th>跳过</th>
                        <th>通过率</th>
                        <th>耗时</th>
                    </tr>
                </thead>
                <tbody>
                    {% for test_type, test_data in details.items() %}
                    <tr>
                        <td>{{ test_type.upper() }}</td>
                        <td>{{ test_data.total }}</td>
                        <td class="success">{{ test_data.passed }}</td>
                        <td class="danger">{{ test_data.failed }}</td>
                        <td class="warning">{{ test_data.skipped }}</td>
                        <td class="{{ 'success' if test_data.pass_rate >= 90 else 'warning' if test_data.pass_rate >= 80 else 'danger' }}">
                            {{ test_data.pass_rate }}%
                        </td>
                        <td>{{ "%.2f"|format(test_data.duration) }}s</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>© 2024 Connect 电信数据分析平台 - 自动化测试监控系统</p>
        </div>
    </div>
</body>
</html>
        """
        
        # 渲染模板
        template = Template(html_template)
        html_content = template.render(
            timestamp=results["timestamp"],
            overall=results["summary"]["overall"],
            metrics=results["metrics"],
            details=results["details"],
            alerts=self.dashboard_data["alerts"]
        )
        
        # 保存HTML文件
        html_file = output_path / 'dashboard.html'
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return html_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='监控仪表板')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--results-file', required=True, help='测试结果文件路径')
    parser.add_argument('--output-dir', default='./dashboard', help='仪表板输出目录')
    parser.add_argument('--send-notifications', action='store_true', help='发送通知')
    parser.add_argument('--generate-dashboard', action='store_true', help='生成仪表板')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        dashboard = MonitoringDashboard(args.config)
        
        success = True
        
        # 发送测试结果到监控系统
        if args.send_notifications:
            success &= dashboard.send_test_results(args.results_file)
        
        # 生成可视化仪表板
        if args.generate_dashboard:
            dashboard_file = dashboard.generate_dashboard(args.results_file, args.output_dir)
            print(f"仪表板已生成: {dashboard_file}")
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"监控仪表板执行失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())