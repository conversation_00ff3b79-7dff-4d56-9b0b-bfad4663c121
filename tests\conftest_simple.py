#!/usr/bin/env python3
"""
简化的测试配置文件 - Connect项目

提供基本的pytest配置和fixtures，避免复杂的导入问题。
"""

import sys
import os
import tempfile
from pathlib import Path
from typing import Dict, Any

import pytest

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))


@pytest.fixture(scope="session")
def project_root_path() -> Path:
    """项目根目录路径"""
    return Path(__file__).parent.parent


@pytest.fixture(scope="session")
def test_config_dir() -> Path:
    """创建临时配置目录"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture(scope="session")
def test_config_data() -> Dict[str, Any]:
    """提供测试配置数据"""
    return {
        "database": {
            "host": "localhost",
            "port": 5432,
            "name": "test_db",
            "user": "test_user",
            "password": "test_password",
            "pool_size": 5,
            "max_overflow": 10,
            "pool_timeout": 30,
            "pool_recycle": 3600
        },
        "logging": {
            "level": "DEBUG",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file": "test.log"
        },
        "security": {
            "secret_key": "test_secret_key",
            "jwt_secret": "test_jwt_secret",
            "token_expiry": 3600
        }
    }


@pytest.fixture
def sample_dataframe():
    """提供示例DataFrame"""
    try:
        import pandas as pd
        return pd.DataFrame({
            'id': [1, 2, 3],
            'name': ['Alice', 'Bob', 'Charlie'],
            'age': [25, 30, 35]
        })
    except ImportError:
        pytest.skip("pandas not available")


@pytest.fixture
def temp_file():
    """创建临时文件"""
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
        temp_path = Path(f.name)
        yield temp_path
    
    # 清理
    if temp_path.exists():
        temp_path.unlink()


@pytest.fixture
def mock_database_config():
    """模拟数据库配置"""
    return {
        'host': 'localhost',
        'port': 5432,
        'database': 'test_db',
        'user': 'test_user',
        'password': 'test_pass'
    }


# 测试标记
pytest_plugins = []

# 自定义标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "database: 需要数据库的测试"
    )


# 测试收集钩子
def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为没有标记的测试添加默认标记
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)


# 测试会话钩子
@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境"""
    # 设置测试环境变量
    os.environ['TESTING'] = 'true'
    os.environ['LOG_LEVEL'] = 'DEBUG'
    
    yield
    
    # 清理
    os.environ.pop('TESTING', None)
    os.environ.pop('LOG_LEVEL', None)


# 错误处理
@pytest.fixture
def capture_logs(caplog):
    """捕获日志"""
    with caplog.at_level('DEBUG'):
        yield caplog