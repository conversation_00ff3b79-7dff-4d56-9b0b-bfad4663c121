#!/usr/bin/env python3
"""
性能基准测试模块

本模块提供大数据量处理的性能基准测试，用于验证系统在高负载下的性能表现。
包括数据导入、查询、地理空间操作等核心功能的性能测试。
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional

import pandas as pd
import pytest
from memory_profiler import profile

from src.config import get_config
from src.database.connection.pool import DatabasePoolManager
from src.database.utils.performance import PerformanceMonitor
from src.importers.ep_importer import EPImporter
from src.importers.cdr_importer import CDRImporter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceBenchmark:
    """性能基准测试类"""
    
    def __init__(self):
        """初始化性能测试环境"""
        from unittest.mock import Mock
        from src.config.models import DatabaseConfig
        
        # Mock config to avoid loading real configuration
        mock_db_config = Mock(spec=DatabaseConfig)
        mock_db_config.host = "localhost"
        mock_db_config.port = 5432
        mock_db_config.database = "test_db"
        mock_db_config.username = "test_user"
        mock_db_config.password = "test_pass"
        
        self.config = Mock()
        self.config.database = mock_db_config
        self.pool_manager = None
        self.performance_monitor = PerformanceMonitor()
        self.test_results = {}
        
    async def setup(self):
        """设置测试环境"""
        from unittest.mock import AsyncMock, patch
        
        self.pool_manager = DatabasePoolManager(self.config)
        
        # Mock the initialize_pool method to avoid real database connection
        with patch.object(self.pool_manager, 'initialize_pool', new_callable=AsyncMock) as mock_init:
            mock_init.return_value = None
            self.pool_manager._is_initialized = True
            self.pool_manager._pool = AsyncMock()
            await self.pool_manager.initialize_pool()
        
    async def teardown(self):
        """清理测试环境"""
        if self.pool_manager:
            await self.pool_manager.close()
            
    def generate_large_dataset(self, size: int, data_type: str = "ep") -> pd.DataFrame:
        """生成大数据集用于性能测试
        
        Args:
            size: 数据集大小（行数）
            data_type: 数据类型 ('ep', 'cdr')
            
        Returns:
            生成的数据框
        """
        if data_type == "ep":
            return pd.DataFrame({
                'OBJECTID': range(1, size + 1),
                'EP_ID': [f'EP_{i:06d}' for i in range(1, size + 1)],
                'EP_NAME': [f'Energy Point {i}' for i in range(1, size + 1)],
                'LATITUDE': [39.9042 + (i % 1000) * 0.001 for i in range(size)],
                'LONGITUDE': [116.4074 + (i % 1000) * 0.001 for i in range(size)],
                'ELEVATION': [50 + (i % 100) for i in range(size)],
                'EP_TYPE': ['Base Station' if i % 2 == 0 else 'Repeater' for i in range(size)],
                'STATUS': ['Active' if i % 10 != 0 else 'Inactive' for i in range(size)]
            })
        elif data_type == "cdr":
            return pd.DataFrame({
                'CALL_ID': [f'CALL_{i:08d}' for i in range(1, size + 1)],
                'CALLER_ID': [f'USER_{i % 10000:06d}' for i in range(size)],
                'CALLEE_ID': [f'USER_{(i + 1) % 10000:06d}' for i in range(size)],
                'START_TIME': pd.date_range('2024-01-01', periods=size, freq='1min'),
                'DURATION': [60 + (i % 300) for i in range(size)],
                'CALL_TYPE': ['Voice' if i % 3 == 0 else 'Data' for i in range(size)],
                'CELL_ID': [f'CELL_{i % 1000:04d}' for i in range(size)],
                'LATITUDE': [39.9042 + (i % 1000) * 0.001 for i in range(size)],
                'LONGITUDE': [116.4074 + (i % 1000) * 0.001 for i in range(size)]
            })
        else:
            raise ValueError(f"Unsupported data type: {data_type}")
    
    @profile
    async def benchmark_data_import(self, dataset_sizes: List[int]) -> Dict[str, Dict]:
        """数据导入性能基准测试
        
        Args:
            dataset_sizes: 测试的数据集大小列表
            
        Returns:
            性能测试结果
        """
        results = {}
        
        for size in dataset_sizes:
            logger.info(f"Testing data import with {size:,} records")
            
            # 生成测试数据
            test_data = self.generate_large_dataset(size, "ep")
            
            # 保存到临时文件
            temp_file = Path(f"temp_ep_data_{size}.csv")
            test_data.to_csv(temp_file, index=False)
            
            try:
                # 创建导入器
                importer = EPImporter(
                    config=self.config.database,
                    performance_logger=self.performance_monitor
                )
                
                # 开始性能监控
                start_time = time.time()
                start_memory = self.performance_monitor.get_memory_usage()
                
                # 执行导入
                result = await importer.import_data(file_path=str(temp_file))
                
                # 结束性能监控
                end_time = time.time()
                end_memory = self.performance_monitor.get_memory_usage()
                
                # 记录结果
                results[f"{size}_records"] = {
                    "success": result.success,
                    "duration_seconds": end_time - start_time,
                    "records_per_second": size / (end_time - start_time) if result.success else 0,
                    "memory_usage_mb": end_memory - start_memory,
                    "file_size_mb": temp_file.stat().st_size / (1024 * 1024),
                    "error_message": result.error_message if not result.success else None
                }
                
                logger.info(f"Import completed: {results[f'{size}_records']}")
                
            finally:
                # 清理临时文件
                if temp_file.exists():
                    temp_file.unlink()
                    
        return results
    
    async def benchmark_query_performance(self, query_types: List[str]) -> Dict[str, Dict]:
        """查询性能基准测试
        
        Args:
            query_types: 查询类型列表
            
        Returns:
            查询性能结果
        """
        results = {}
        
        # 准备测试查询
        test_queries = {
            "simple_select": "SELECT COUNT(*) FROM ep_data WHERE status = 'Active'",
            "spatial_query": """
                SELECT ep_id, ep_name, latitude, longitude 
                FROM ep_data 
                WHERE latitude BETWEEN 39.9 AND 40.0 
                AND longitude BETWEEN 116.4 AND 116.5
            """,
            "aggregation_query": """
                SELECT ep_type, COUNT(*) as count, AVG(elevation) as avg_elevation
                FROM ep_data 
                GROUP BY ep_type
            """,
            "complex_join": """
                SELECT e.ep_id, e.ep_name, COUNT(c.call_id) as call_count
                FROM ep_data e
                LEFT JOIN cdr_data c ON ST_DWithin(
                    ST_Point(e.longitude, e.latitude),
                    ST_Point(c.longitude, c.latitude),
                    0.001
                )
                GROUP BY e.ep_id, e.ep_name
                LIMIT 1000
            """
        }
        
        for query_type in query_types:
            if query_type not in test_queries:
                continue
                
            logger.info(f"Testing query performance: {query_type}")
            
            query = test_queries[query_type]
            
            # 执行多次查询取平均值
            durations = []
            for i in range(5):
                start_time = time.time()
                
                try:
                    async with self.pool_manager.get_connection() as conn:
                        await conn.fetch(query)
                    
                    end_time = time.time()
                    durations.append(end_time - start_time)
                    
                except Exception as e:
                    logger.error(f"Query failed: {e}")
                    durations.append(float('inf'))
            
            # 计算统计信息
            valid_durations = [d for d in durations if d != float('inf')]
            
            results[query_type] = {
                "avg_duration_seconds": sum(valid_durations) / len(valid_durations) if valid_durations else float('inf'),
                "min_duration_seconds": min(valid_durations) if valid_durations else float('inf'),
                "max_duration_seconds": max(valid_durations) if valid_durations else float('inf'),
                "success_rate": len(valid_durations) / len(durations),
                "total_runs": len(durations)
            }
            
            logger.info(f"Query performance: {results[query_type]}")
            
        return results
    
    async def benchmark_concurrent_operations(self, concurrent_users: List[int]) -> Dict[str, Dict]:
        """并发操作性能基准测试
        
        Args:
            concurrent_users: 并发用户数列表
            
        Returns:
            并发性能结果
        """
        results = {}
        
        async def simulate_user_operation():
            """模拟用户操作"""
            try:
                async with self.pool_manager.get_connection() as conn:
                    # 模拟复杂查询
                    await conn.fetch("""
                        SELECT ep_id, ep_name, latitude, longitude
                        FROM ep_data
                        WHERE status = 'Active'
                        ORDER BY RANDOM()
                        LIMIT 100
                    """)
                return True
            except Exception as e:
                logger.error(f"User operation failed: {e}")
                return False
        
        for user_count in concurrent_users:
            logger.info(f"Testing concurrent operations with {user_count} users")
            
            start_time = time.time()
            
            # 创建并发任务
            tasks = [simulate_user_operation() for _ in range(user_count)]
            
            # 执行并发操作
            task_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            
            # 统计结果
            successful_operations = sum(1 for result in task_results if result is True)
            
            results[f"{user_count}_users"] = {
                "total_duration_seconds": end_time - start_time,
                "successful_operations": successful_operations,
                "failed_operations": user_count - successful_operations,
                "success_rate": successful_operations / user_count,
                "operations_per_second": successful_operations / (end_time - start_time)
            }
            
            logger.info(f"Concurrent performance: {results[f'{user_count}_users']}")
            
        return results


# Pytest测试用例
@pytest.fixture
async def performance_benchmark():
    """性能基准测试fixture"""
    benchmark = PerformanceBenchmark()
    await benchmark.setup()
    yield benchmark
    await benchmark.teardown()


@pytest.mark.asyncio
@pytest.mark.performance
async def test_large_dataset_import_performance(performance_benchmark):
    """测试大数据集导入性能"""
    # 测试不同大小的数据集
    dataset_sizes = [1000, 10000, 50000, 100000]
    
    results = await performance_benchmark.benchmark_data_import(dataset_sizes)
    
    # 验证性能要求
    for size, result in results.items():
        if result["success"]:
            # 要求：500万行数据处理时间<10秒
            if "100000" in size:  # 10万行数据应该在1秒内完成
                assert result["duration_seconds"] < 1.0, f"Import too slow for {size}: {result['duration_seconds']}s"
            
            # 要求：处理速度至少10000行/秒
            assert result["records_per_second"] > 10000, f"Import rate too slow for {size}: {result['records_per_second']} records/s"
            
            logger.info(f"✓ Performance test passed for {size}: {result['records_per_second']:.0f} records/s")


@pytest.mark.asyncio
@pytest.mark.performance
async def test_query_performance_benchmarks(performance_benchmark):
    """测试查询性能基准"""
    query_types = ["simple_select", "spatial_query", "aggregation_query"]
    
    results = await performance_benchmark.benchmark_query_performance(query_types)
    
    # 验证查询性能要求
    for query_type, result in results.items():
        if result["success_rate"] > 0:
            # 要求：地理查询响应时间<3秒
            if "spatial" in query_type:
                assert result["avg_duration_seconds"] < 3.0, f"Spatial query too slow: {result['avg_duration_seconds']}s"
            
            # 要求：简单查询响应时间<0.5秒
            if "simple" in query_type:
                assert result["avg_duration_seconds"] < 0.5, f"Simple query too slow: {result['avg_duration_seconds']}s"
            
            logger.info(f"✓ Query performance test passed for {query_type}: {result['avg_duration_seconds']:.3f}s")


@pytest.mark.asyncio
@pytest.mark.performance
async def test_concurrent_user_performance(performance_benchmark):
    """测试并发用户性能"""
    concurrent_users = [5, 10, 20]
    
    results = await performance_benchmark.benchmark_concurrent_operations(concurrent_users)
    
    # 验证并发性能要求
    for user_count, result in results.items():
        # 要求：支持20用户并发，成功率>95%
        if "20" in user_count:
            assert result["success_rate"] > 0.95, f"Concurrent performance too low for {user_count}: {result['success_rate']*100:.1f}%"
        
        # 要求：并发操作响应时间合理
        assert result["total_duration_seconds"] < 10.0, f"Concurrent operations too slow for {user_count}: {result['total_duration_seconds']}s"
        
        logger.info(f"✓ Concurrent performance test passed for {user_count}: {result['success_rate']*100:.1f}% success rate")


if __name__ == "__main__":
    """运行性能基准测试"""
    async def main():
        benchmark = PerformanceBenchmark()
        await benchmark.setup()
        
        try:
            # 运行所有基准测试
            logger.info("=== 开始性能基准测试 ===")
            
            # 数据导入性能测试
            logger.info("\n1. 数据导入性能测试")
            import_results = await benchmark.benchmark_data_import([1000, 10000, 50000])
            
            # 查询性能测试
            logger.info("\n2. 查询性能测试")
            query_results = await benchmark.benchmark_query_performance(["simple_select", "spatial_query"])
            
            # 并发性能测试
            logger.info("\n3. 并发性能测试")
            concurrent_results = await benchmark.benchmark_concurrent_operations([5, 10, 20])
            
            # 输出汇总报告
            logger.info("\n=== 性能基准测试完成 ===")
            logger.info(f"数据导入测试: {len(import_results)} 个测试完成")
            logger.info(f"查询性能测试: {len(query_results)} 个测试完成")
            logger.info(f"并发性能测试: {len(concurrent_results)} 个测试完成")
            
        finally:
            await benchmark.teardown()
    
    asyncio.run(main())