#!/usr/bin/env python3
"""
Connect Test Data Generator
Used to generate data for various test scenarios, including CDR data, EP data, site data, etc.

Author: Connect Quality Engineer
Date: 2024
"""

import random
import csv
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import uuid
import math
from faker import Faker
import numpy as np

class _TestDataGenerator:
    """Main class for test data generation"""
    
    def __init__(self, seed: int = 42):
        """Initialize the data generator
        
        Args:
            seed: Random seed to ensure data generation reproducibility
        """
        self.seed = seed
        random.seed(seed)
        np.random.seed(seed)
        self.fake = Faker(['zh_CN'])
        Faker.seed(seed)
        
        # Telecom-related constants
        self.cell_technologies = ['2G', '3G', '4G', '5G']
        self.frequency_bands = {
            '2G': [900, 1800],
            '3G': [900, 2100],
            '4G': [800, 900, 1800, 2100, 2600],
            '5G': [3500, 4900, 28000]
        }
        self.operators = ['China Mobile', 'China Unicom', 'China Telecom']
        self.provinces = [
            'Beijing', 'Shanghai', 'Tianjin', 'Chongqing', 'Hebei', 'Shanxi', 'Liaoning', 'Jilin',
            'Heilongjiang', 'Jiangsu', 'Zhejiang', 'Anhui', 'Fujian', 'Jiangxi', 'Shandong', 'Henan',
            'Hubei', 'Hunan', 'Guangdong', 'Hainan', 'Sichuan', 'Guizhou', 'Yunnan', 'Shaanxi',
            'Gansu', 'Qinghai', 'Taiwan', 'Inner Mongolia', 'Guangxi', 'Tibet', 'Ningxia', 'Xinjiang'
        ]
        
        # Geographical coordinate range (within China)
        self.lat_range = (18.0, 54.0)
        self.lon_range = (73.0, 135.0)
        
        # Performance indicator ranges
        self.rsrp_range = (-140, -44)  # dBm
        self.rsrq_range = (-20, -3)    # dB
        self.sinr_range = (-10, 30)    # dB
        self.throughput_range = (1, 1000)  # Mbps
    
    def generate_cdr_data(self, 
                         num_records: int = 1000,
                         start_date: datetime = None,
                         end_date: datetime = None,
                         output_format: str = 'csv') -> str:
        """Generate CDR (Call Detail Record) data
        
        Args:
            num_records: Number of records to generate
            start_date: Start date
            end_date: End date
            output_format: Output format ('csv', 'json', 'excel')
            
        Returns:
            Path to the generated data file
        """
        if start_date is None:
            start_date = datetime.now() - timedelta(days=30)
        if end_date is None:
            end_date = datetime.now()
        
        records = []
        
        for i in range(num_records):
            # Generate basic information
            call_id = f"CDR_{uuid.uuid4().hex[:8]}"
            msisdn = self.fake.phone_number()
            imsi = f"460{random.randint(10000000000000, 99999999999999)}"
            imei = f"{random.randint(100000000000000, 999999999999999)}"
            
            # Generate time information
            call_time = self.fake.date_time_between(start_date=start_date, end_date=end_date)
            duration = random.randint(10, 3600)  # Call duration (seconds)
            
            # Generate location information
            latitude = random.uniform(*self.lat_range)
            longitude = random.uniform(*self.lon_range)
            cell_id = random.randint(100000, 999999)
            lac = random.randint(1000, 9999)
            
            # Generate network information
            technology = random.choice(self.cell_technologies)
            operator = random.choice(self.operators)
            frequency = random.choice(self.frequency_bands[technology])
            
            # Generate performance indicators
            rsrp = random.uniform(*self.rsrp_range)
            rsrq = random.uniform(*self.rsrq_range)
            sinr = random.uniform(*self.sinr_range)
            
            # Generate service information
            call_type = random.choice(['Voice', 'Video', 'Data'])
            service_type = random.choice(['Call', 'SMS', 'Internet', 'Video Call'])
            data_volume = random.randint(0, 1024*1024) if call_type == 'Data' else 0
            
            record = {
                'call_id': call_id,
                'msisdn': msisdn,
                'imsi': imsi,
                'imei': imei,
                'call_time': call_time.strftime('%Y-%m-%d %H:%M:%S'),
                'duration': duration,
                'latitude': round(latitude, 6),
                'longitude': round(longitude, 6),
                'cell_id': cell_id,
                'lac': lac,
                'technology': technology,
                'operator': operator,
                'frequency': frequency,
                'rsrp': round(rsrp, 2),
                'rsrq': round(rsrq, 2),
                'sinr': round(sinr, 2),
                'call_type': call_type,
                'service_type': service_type,
                'data_volume': data_volume,
                'province': random.choice(self.provinces),
                'city': self.fake.city(),
                'district': self.fake.district()
            }
            
            records.append(record)
        
        # Save data
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('test_data/cdr')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            filename = f"cdr_data_{timestamp}.csv"
            filepath = output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=records[0].keys())
                writer.writeheader()
                writer.writerows(records)
                
        elif output_format == 'json':
            filename = f"cdr_data_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'excel':
            filename = f"cdr_data_{timestamp}.xlsx"
            filepath = output_dir / filename
            
            df = pd.DataFrame(records)
            df.to_excel(filepath, index=False)
        
        return str(filepath)
    
    def generate_ep_data(self, 
                        num_records: int = 500,
                        start_date: datetime = None,
                        end_date: datetime = None,
                        output_format: str = 'csv') -> str:
        """Generate EP (Engineering Parameter) data
        
        Args:
            num_records: Number of records to generate
            start_date: Start date
            end_date: End date
            output_format: Output format
            
        Returns:
            Path to the generated data file
        """
        if start_date is None:
            start_date = datetime.now() - timedelta(days=7)
        if end_date is None:
            end_date = datetime.now()
        
        records = []
        
        for i in range(num_records):
            # Generate basic information
            ep_id = f"EP_{uuid.uuid4().hex[:8]}"
            cell_id = random.randint(100000, 999999)
            site_id = random.randint(10000, 99999)
            
            # Generate time information
            measurement_time = self.fake.date_time_between(start_date=start_date, end_date=end_date)
            
            # Generate location information
            latitude = random.uniform(*self.lat_range)
            longitude = random.uniform(*self.lon_range)
            height = random.randint(10, 200)  # Antenna height
            
            # Generate network configuration
            technology = random.choice(self.cell_technologies)
            operator = random.choice(self.operators)
            frequency = random.choice(self.frequency_bands[technology])
            bandwidth = random.choice([5, 10, 15, 20]) if technology in ['4G', '5G'] else random.choice([5, 10])
            
            # Generate antenna parameters
            azimuth = random.randint(0, 359)  # Azimuth
            tilt = random.randint(-10, 10)    # Downtilt
            antenna_gain = random.uniform(12, 21)  # Antenna gain
            
            # Generate power parameters
            tx_power = random.uniform(20, 46)  # Transmit power dBm
            max_power = random.uniform(40, 50)
            
            # Generate performance parameters
            pci = random.randint(0, 503)  # Physical Cell ID
            tac = random.randint(1, 65535)  # Tracking Area Code
            
            # Generate configuration parameters
            handover_threshold = random.uniform(-110, -70)
            reselection_threshold = random.uniform(-110, -70)
            
            record = {
                'ep_id': ep_id,
                'cell_id': cell_id,
                'site_id': site_id,
                'measurement_time': measurement_time.strftime('%Y-%m-%d %H:%M:%S'),
                'latitude': round(latitude, 6),
                'longitude': round(longitude, 6),
                'height': height,
                'technology': technology,
                'operator': operator,
                'frequency': frequency,
                'bandwidth': bandwidth,
                'azimuth': azimuth,
                'tilt': tilt,
                'antenna_gain': round(antenna_gain, 2),
                'tx_power': round(tx_power, 2),
                'max_power': round(max_power, 2),
                'pci': pci,
                'tac': tac,
                'handover_threshold': round(handover_threshold, 2),
                'reselection_threshold': round(reselection_threshold, 2),
                'province': random.choice(self.provinces),
                'city': self.fake.city(),
                'district': self.fake.district(),
                'site_name': f"{self.fake.company()}_BaseStation",
                'site_type': random.choice(['Macro Site', 'Micro Site', 'Indoor DAS', 'Small Cell'])
            }
            
            records.append(record)
        
        # Save data
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('test_data/ep')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            filename = f"ep_data_{timestamp}.csv"
            filepath = output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=records[0].keys())
                writer.writeheader()
                writer.writerows(records)
                
        elif output_format == 'json':
            filename = f"ep_data_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'excel':
            filename = f"ep_data_{timestamp}.xlsx"
            filepath = output_dir / filename
            
            df = pd.DataFrame(records)
            df.to_excel(filepath, index=False)
        
        return str(filepath)
    
    def generate_site_data(self, 
                          num_records: int = 200,
                          output_format: str = 'csv') -> str:
        """Generate site data
        
        Args:
            num_records: Number of records to generate
            output_format: Output format
            
        Returns:
            Path to the generated data file
        """
        records = []
        
        for i in range(num_records):
            # Generate basic information
            site_id = random.randint(10000, 99999)
            site_name = f"{self.fake.company()}_BaseStation_{site_id}"
            
            # Generate location information
            latitude = random.uniform(*self.lat_range)
            longitude = random.uniform(*self.lon_range)
            
            # Generate site attributes
            site_type = random.choice(['Macro Site', 'Micro Site', 'Indoor DAS', 'Small Cell'])
            operator = random.choice(self.operators)
            technologies = random.sample(self.cell_technologies, random.randint(1, 3))
            
            # Generate coverage information
            coverage_radius = random.randint(100, 5000)  # Coverage radius (meters)
            if site_type == 'Macro Site':
                coverage_radius = random.randint(1000, 5000)
            elif site_type == 'Micro Site':
                coverage_radius = random.randint(200, 1000)
            elif site_type == 'Small Cell':
                coverage_radius = random.randint(50, 200)
            
            # Generate capacity information
            max_users = random.randint(50, 1000)
            current_users = random.randint(0, max_users)
            
            # Generate status information
            status = random.choice(['Normal', 'Alarm', 'Fault', 'Maintenance'])
            online_time = random.uniform(95, 100)  # Online rate
            
            # Generate construction information
            build_date = self.fake.date_between(start_date='-5y', end_date='today')
            vendor = random.choice(['Huawei', 'ZTE', 'Ericsson', 'Nokia', 'Datang'])
            
            record = {
                'site_id': site_id,
                'site_name': site_name,
                'latitude': round(latitude, 6),
                'longitude': round(longitude, 6),
                'site_type': site_type,
                'operator': operator,
                'technologies': ','.join(technologies),
                'coverage_radius': coverage_radius,
                'max_users': max_users,
                'current_users': current_users,
                'utilization_rate': round(current_users / max_users * 100, 2),
                'status': status,
                'online_time': round(online_time, 2),
                'build_date': build_date.strftime('%Y-%m-%d'),
                'vendor': vendor,
                'province': random.choice(self.provinces),
                'city': self.fake.city(),
                'district': self.fake.district(),
                'address': self.fake.address(),
                'contact_person': self.fake.name(),
                'contact_phone': self.fake.phone_number()
            }
            
            records.append(record)
        
        # Save data
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('test_data/site')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            filename = f"site_data_{timestamp}.csv"
            filepath = output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=records[0].keys())
                writer.writeheader()
                writer.writerows(records)
                
        elif output_format == 'json':
            filename = f"site_data_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'excel':
            filename = f"site_data_{timestamp}.xlsx"
            filepath = output_dir / filename
            
            df = pd.DataFrame(records)
            df.to_excel(filepath, index=False)
        
        return str(filepath)
    
    def generate_drive_test_data(self, 
                               num_records: int = 2000,
                               route_length: float = 10.0,
                               output_format: str = 'csv') -> str:
        """Generate drive test data
        
        Args:
            num_records: Number of records to generate
            route_length: Drive test route length (km)
            output_format: Output format
            
        Returns:
            Path to the generated data file
        """
        records = []
        
        # Generate drive test route
        start_lat = random.uniform(*self.lat_range)
        start_lon = random.uniform(*self.lon_range)
        
        # Calculate route points
        points_per_km = num_records / route_length
        
        for i in range(num_records):
            # Generate GPS track
            progress = i / num_records
            
            # Simple straight path, can be more complex in actual applications
            lat_offset = (random.uniform(-0.01, 0.01) + progress * 0.1)
            lon_offset = (random.uniform(-0.01, 0.01) + progress * 0.1)
            
            latitude = start_lat + lat_offset
            longitude = start_lon + lon_offset
            
            # Generate timestamp
            test_time = datetime.now() - timedelta(hours=2) + timedelta(seconds=i*5)
            
            # Generate speed information
            speed = random.uniform(0, 120)  # km/h
            
            # Generate network information
            technology = random.choice(self.cell_technologies)
            operator = random.choice(self.operators)
            cell_id = random.randint(100000, 999999)
            
            # Generate signal quality
            rsrp = random.uniform(*self.rsrp_range)
            rsrq = random.uniform(*self.rsrq_range)
            sinr = random.uniform(*self.sinr_range)
            
            # Add some realistic signal attenuation models
            if speed > 60:  # Signal quality degrades at high speed
                rsrp -= random.uniform(5, 15)
                rsrq -= random.uniform(2, 8)
                sinr -= random.uniform(3, 10)
            
            # Generate service performance
            throughput_dl = random.uniform(*self.throughput_range)
            throughput_ul = throughput_dl * random.uniform(0.1, 0.5)  # Uplink is usually lower
            
            # Generate call quality
            call_setup_time = random.uniform(1, 10)  # seconds
            call_drop_rate = random.uniform(0, 5)    # %
            handover_success_rate = random.uniform(90, 100)  # %
            
            record = {
                'test_id': f"DT_{uuid.uuid4().hex[:8]}",
                'test_time': test_time.strftime('%Y-%m-%d %H:%M:%S'),
                'latitude': round(latitude, 6),
                'longitude': round(longitude, 6),
                'speed': round(speed, 2),
                'technology': technology,
                'operator': operator,
                'cell_id': cell_id,
                'rsrp': round(rsrp, 2),
                'rsrq': round(rsrq, 2),
                'sinr': round(sinr, 2),
                'throughput_dl': round(throughput_dl, 2),
                'throughput_ul': round(throughput_ul, 2),
                'call_setup_time': round(call_setup_time, 2),
                'call_drop_rate': round(call_drop_rate, 2),
                'handover_success_rate': round(handover_success_rate, 2),
                'province': random.choice(self.provinces),
                'city': self.fake.city(),
                'road_type': random.choice(['Highway', 'Urban Road', 'Rural Road', 'Tunnel', 'Bridge']),
                'environment': random.choice(['Urban', 'Suburban', 'Rural', 'Mountainous', 'Water Area'])
            }
            
            records.append(record)
        
        # Save data
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('test_data/drive_test')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            filename = f"drive_test_data_{timestamp}.csv"
            filepath = output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=records[0].keys())
                writer.writeheader()
                writer.writerows(records)
                
        elif output_format == 'json':
            filename = f"drive_test_data_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'excel':
            filename = f"drive_test_data_{timestamp}.xlsx"
            filepath = output_dir / filename
            
            df = pd.DataFrame(records)
            df.to_excel(filepath, index=False)
        
        return str(filepath)
    
    def generate_kpi_data(self, 
                         num_days: int = 30,
                         output_format: str = 'csv') -> str:
        """Generate KPI data
        
        Args:
            num_days: Number of days to generate
            output_format: Output format
            
        Returns:
            Path to the generated data file
        """
        records = []
        
        # KPI metric definitions
        kpi_metrics = {
            'Call Setup Success Rate': (95, 100),
            'Call Drop Rate': (0, 5),
            'Handover Success Rate': (90, 100),
            'Download Speed': (10, 100),
            'Upload Speed': (5, 50),
            'Network Latency': (10, 100),
            'Network Availability': (95, 100),
            'User Satisfaction': (80, 100),
            'Traffic Utilization Rate': (30, 90),
            'Base Station Utilization Rate': (40, 85)
        }
        
        start_date = datetime.now() - timedelta(days=num_days)
        
        for day in range(num_days):
            current_date = start_date + timedelta(days=day)
            
            # Generate 24 hours of data per day
            for hour in range(24):
                timestamp = current_date.replace(hour=hour, minute=0, second=0)
                
                for kpi_name, (min_val, max_val) in kpi_metrics.items():
                    # Add time patterns (working hours vs. non-working hours)
                    if 8 <= hour <= 22:  # Working hours
                        if kpi_name in ['Call Setup Success Rate', 'Handover Success Rate', 'Network Availability']:
                            value = random.uniform(min_val * 0.95, max_val)
                        elif kpi_name in ['Call Drop Rate', 'Network Latency']:
                            value = random.uniform(min_val, max_val * 1.2)
                        else:
                            value = random.uniform(min_val, max_val)
                    else:  # Non-working hours
                        if kpi_name in ['Download Speed', 'Upload Speed', 'Traffic Utilization Rate']:
                            value = random.uniform(min_val * 0.5, max_val * 0.7)
                        else:
                            value = random.uniform(min_val, max_val)
                    
                    # Add weekend effect
                    if current_date.weekday() >= 5:  # Weekend
                        if kpi_name in ['Traffic Utilization Rate', 'Base Station Utilization Rate']:
                            value *= random.uniform(0.7, 0.9)
                    
                    record = {
                        'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                        'date': current_date.strftime('%Y-%m-%d'),
                        'hour': hour,
                        'kpi_name': kpi_name,
                        'kpi_value': round(value, 2),
                        'unit': self._get_kpi_unit(kpi_name),
                        'operator': random.choice(self.operators),
                        'technology': random.choice(self.cell_technologies),
                        'province': random.choice(self.provinces),
                        'city': self.fake.city(),
                        'measurement_type': random.choice(['Real-time', 'Statistical', 'Predicted']),
                        'data_source': random.choice(['NMS', 'Drive Test', 'User Feedback', 'OMC'])
                    }
                    
                    records.append(record)
        
        # Save data
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = Path('test_data/kpi')
        output_dir.mkdir(parents=True, exist_ok=True)
        
        if output_format == 'csv':
            filename = f"kpi_data_{timestamp}.csv"
            filepath = output_dir / filename
            
            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=records[0].keys())
                writer.writeheader()
                writer.writerows(records)
                
        elif output_format == 'json':
            filename = f"kpi_data_{timestamp}.json"
            filepath = output_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(records, f, ensure_ascii=False, indent=2)
                
        elif output_format == 'excel':
            filename = f"kpi_data_{timestamp}.xlsx"
            filepath = output_dir / filename
            
            df = pd.DataFrame(records)
            df.to_excel(filepath, index=False)
        
        return str(filepath)
    
    def _get_kpi_unit(self, kpi_name: str) -> str:
        """Get KPI metric unit"""
        unit_mapping = {
            'Call Setup Success Rate': '%',
            'Call Drop Rate': '%',
            'Handover Success Rate': '%',
            'Download Speed': 'Mbps',
            'Upload Speed': 'Mbps',
            'Network Latency': 'ms',
            'Network Availability': '%',
            'User Satisfaction': 'Points',
            'Traffic Utilization Rate': '%',
            'Base Station Utilization Rate': '%'
        }
        return unit_mapping.get(kpi_name, '')
    
    def generate_large_dataset(self, 
                              data_type: str,
                              size_mb: int = 100,
                              output_format: str = 'csv') -> str:
        """Generate large dataset for performance testing
        
        Args:
            data_type: Data type ('cdr', 'ep', 'site', 'drive_test', 'kpi')
            size_mb: Target file size (MB)
            output_format: Output format
            
        Returns:
            Path to the generated data file
        """
        # Estimate record count (based on average record size)
        avg_record_sizes = {
            'cdr': 500,      # bytes
            'ep': 400,
            'site': 300,
            'drive_test': 600,
            'kpi': 200
        }
        
        target_bytes = size_mb * 1024 * 1024
        avg_size = avg_record_sizes.get(data_type, 400)
        estimated_records = target_bytes // avg_size
        
        print(f"Generating {data_type} data, target size: {size_mb}MB, estimated records: {estimated_records}")
        
        if data_type == 'cdr':
            return self.generate_cdr_data(estimated_records, output_format=output_format)
        elif data_type == 'ep':
            return self.generate_ep_data(estimated_records, output_format=output_format)
        elif data_type == 'site':
            return self.generate_site_data(estimated_records, output_format=output_format)
        elif data_type == 'drive_test':
            return self.generate_drive_test_data(estimated_records, output_format=output_format)
        elif data_type == 'kpi':
            # KPI data is calculated by number of days
            days = max(1, estimated_records // 240)  # Approximately 240 records per day
            return self.generate_kpi_data(days, output_format=output_format)
        else:
            raise ValueError(f"Unsupported data type: {data_type}")
    
    def generate_test_suite(self, output_dir: str = 'test_data') -> Dict[str, str]:
        """Generate a complete test data suite
        
        Args:
            output_dir: Output directory
            
        Returns:
            Dictionary of generated file paths
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        print("Starting to generate test data suite...")
        
        files = {}
        
        # Generate various types of data
        print("Generating CDR data...")
        files['cdr'] = self.generate_cdr_data(1000)
        
        print("Generating EP data...")
        files['ep'] = self.generate_ep_data(500)
        
        print("Generating Site data...")
        files['site'] = self.generate_site_data(200)
        
        print("Generating Drive Test data...")
        files['drive_test'] = self.generate_drive_test_data(2000)
        
        print("Generating KPI data...")
        files['kpi'] = self.generate_kpi_data(30)
        
        print("Test data suite generation complete!")
        print("Generated files:")
        for data_type, filepath in files.items():
            print(f"  {data_type}: {filepath}")
        
        return files

def main():
    """Main function for command-line invocation"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Connect Test Data Generator')
    parser.add_argument('--type', choices=['cdr', 'ep', 'site', 'drive_test', 'kpi', 'all'],
                       default='all', help='Data type')
    parser.add_argument('--records', type=int, default=1000, help='Number of records')
    parser.add_argument('--size', type=int, help='File size (MB), for large dataset generation')
    parser.add_argument('--format', choices=['csv', 'json', 'excel'], 
                       default='csv', help='Output format')
    parser.add_argument('--output', default='test_data', help='Output directory')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    
    args = parser.parse_args()
    
    generator = TestDataGenerator(seed=args.seed)
    
    if args.type == 'all':
        generator.generate_test_suite(args.output)
    else:
        if args.size:
            filepath = generator.generate_large_dataset(
                args.type, args.size, args.format
            )
        else:
            if args.type == 'cdr':
                filepath = generator.generate_cdr_data(args.records, output_format=args.format)
            elif args.type == 'ep':
                filepath = generator.generate_ep_data(args.records, output_format=args.format)
            elif args.type == 'site':
                filepath = generator.generate_site_data(args.records, output_format=args.format)
            elif args.type == 'drive_test':
                filepath = generator.generate_drive_test_data(args.records, output_format=args.format)
            elif args.type == 'kpi':
                days = max(1, args.records // 240)
                filepath = generator.generate_kpi_data(days, output_format=args.format)
        
        print(f"Data generation complete: {filepath}")

if __name__ == '__main__':
    main()