"""Memory profiling tools for testing."""

import gc
import os
import psutil
import sys
import tracemalloc
from contextlib import contextmanager
from dataclasses import dataclass, field
from functools import wraps
from typing import Any, Dict, List, Optional, Tuple, Union
from datetime import datetime
import threading
import time


@dataclass
class MemorySnapshot:
    """Memory usage snapshot."""
    timestamp: datetime
    rss_mb: float  # Resident Set Size
    vms_mb: float  # Virtual Memory Size
    percent: float  # Memory percentage
    available_mb: float  # Available system memory
    tracemalloc_current: int = 0  # Current tracemalloc usage
    tracemalloc_peak: int = 0  # Peak tracemalloc usage
    gc_objects: int = 0  # Number of objects tracked by GC
    custom_metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MemoryLeak:
    """Memory leak detection result."""
    test_name: str
    start_memory: float
    end_memory: float
    leak_amount: float
    leak_percentage: float
    duration: float
    is_leak: bool
    threshold_mb: float


@dataclass
class AllocationTrace:
    """Memory allocation trace."""
    filename: str
    line_number: int
    size_mb: float
    count: int
    traceback: List[str] = field(default_factory=list)


class MemoryProfiler:
    """Profile memory usage during tests."""
    
    def __init__(self, leak_threshold_mb: float = 10.0):
        """Initialize memory profiler.
        
        Args:
            leak_threshold_mb: Memory leak threshold in MB
        """
        self.leak_threshold_mb = leak_threshold_mb
        self.snapshots: List[MemorySnapshot] = []
        self.leaks: List[MemoryLeak] = []
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # Start tracemalloc if not already started
        if not tracemalloc.is_tracing():
            tracemalloc.start()
            
    def take_snapshot(self, custom_metrics: Optional[Dict[str, Any]] = None) -> MemorySnapshot:
        """Take a memory usage snapshot.
        
        Args:
            custom_metrics: Additional custom metrics to include
            
        Returns:
            MemorySnapshot object
        """
        process = psutil.Process()
        memory_info = process.memory_info()
        memory_percent = process.memory_percent()
        
        # System memory info
        system_memory = psutil.virtual_memory()
        
        # Tracemalloc info
        tracemalloc_current = 0
        tracemalloc_peak = 0
        if tracemalloc.is_tracing():
            current, peak = tracemalloc.get_traced_memory()
            tracemalloc_current = current
            tracemalloc_peak = peak
            
        # Garbage collector info
        gc_objects = len(gc.get_objects())
        
        snapshot = MemorySnapshot(
            timestamp=datetime.now(),
            rss_mb=memory_info.rss / 1024 / 1024,
            vms_mb=memory_info.vms / 1024 / 1024,
            percent=memory_percent,
            available_mb=system_memory.available / 1024 / 1024,
            tracemalloc_current=tracemalloc_current,
            tracemalloc_peak=tracemalloc_peak,
            gc_objects=gc_objects,
            custom_metrics=custom_metrics or {}
        )
        
        self.snapshots.append(snapshot)
        return snapshot
        
    def start_monitoring(self, interval: float = 1.0):
        """Start continuous memory monitoring.
        
        Args:
            interval: Monitoring interval in seconds
        """
        if self.monitoring:
            return
            
        self.monitoring = True
        self.stop_event.clear()
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        
    def stop_monitoring(self):
        """Stop continuous memory monitoring."""
        if not self.monitoring:
            return
            
        self.monitoring = False
        self.stop_event.set()
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
            
    def _monitor_loop(self, interval: float):
        """Continuous monitoring loop."""
        while not self.stop_event.wait(interval):
            try:
                self.take_snapshot({'monitoring': True})
            except Exception as e:
                print(f"Error taking memory snapshot: {e}")
                
    @contextmanager
    def profile_memory(self, test_name: str = "test", check_leaks: bool = True):
        """Context manager to profile memory usage.
        
        Args:
            test_name: Name of the test being profiled
            check_leaks: Whether to check for memory leaks
        """
        # Force garbage collection before starting
        gc.collect()
        
        # Take initial snapshot
        start_snapshot = self.take_snapshot({'test_name': test_name, 'phase': 'start'})
        start_time = time.time()
        
        try:
            yield self
        finally:
            # Force garbage collection after test
            gc.collect()
            
            # Take final snapshot
            end_snapshot = self.take_snapshot({'test_name': test_name, 'phase': 'end'})
            end_time = time.time()
            
            # Check for memory leaks
            if check_leaks:
                leak = self._detect_leak(
                    test_name, start_snapshot, end_snapshot, end_time - start_time
                )
                if leak:
                    self.leaks.append(leak)
                    
    def memory_test(self, test_name: str = None, check_leaks: bool = True):
        """Decorator to profile memory usage of test functions.
        
        Args:
            test_name: Optional name for the test
            check_leaks: Whether to check for memory leaks
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                name = test_name or func.__name__
                with self.profile_memory(name, check_leaks):
                    return func(*args, **kwargs)
            return wrapper
        return decorator
        
    def _detect_leak(self, 
                    test_name: str, 
                    start_snapshot: MemorySnapshot, 
                    end_snapshot: MemorySnapshot,
                    duration: float) -> Optional[MemoryLeak]:
        """Detect memory leaks between snapshots.
        
        Args:
            test_name: Name of the test
            start_snapshot: Initial memory snapshot
            end_snapshot: Final memory snapshot
            duration: Test duration in seconds
            
        Returns:
            MemoryLeak object if leak detected, None otherwise
        """
        memory_diff = end_snapshot.rss_mb - start_snapshot.rss_mb
        
        if memory_diff > self.leak_threshold_mb:
            leak_percentage = (memory_diff / start_snapshot.rss_mb) * 100
            
            return MemoryLeak(
                test_name=test_name,
                start_memory=start_snapshot.rss_mb,
                end_memory=end_snapshot.rss_mb,
                leak_amount=memory_diff,
                leak_percentage=leak_percentage,
                duration=duration,
                is_leak=True,
                threshold_mb=self.leak_threshold_mb
            )
            
        return None
        
    def get_top_allocations(self, limit: int = 10) -> List[AllocationTrace]:
        """Get top memory allocations using tracemalloc.
        
        Args:
            limit: Number of top allocations to return
            
        Returns:
            List of AllocationTrace objects
        """
        if not tracemalloc.is_tracing():
            return []
            
        snapshot = tracemalloc.take_snapshot()
        top_stats = snapshot.statistics('lineno')
        
        allocations = []
        for stat in top_stats[:limit]:
            trace = AllocationTrace(
                filename=stat.traceback.format()[-1].split(',')[0].strip(),
                line_number=stat.traceback.format()[-1].split(',')[1].strip().split()[1],
                size_mb=stat.size / 1024 / 1024,
                count=stat.count,
                traceback=[frame.strip() for frame in stat.traceback.format()]
            )
            allocations.append(trace)
            
        return allocations
        
    def compare_snapshots(self, 
                         snapshot1: MemorySnapshot, 
                         snapshot2: MemorySnapshot) -> Dict[str, float]:
        """Compare two memory snapshots.
        
        Args:
            snapshot1: First snapshot
            snapshot2: Second snapshot
            
        Returns:
            Dictionary with comparison metrics
        """
        return {
            'rss_diff_mb': snapshot2.rss_mb - snapshot1.rss_mb,
            'vms_diff_mb': snapshot2.vms_mb - snapshot1.vms_mb,
            'percent_diff': snapshot2.percent - snapshot1.percent,
            'tracemalloc_diff': snapshot2.tracemalloc_current - snapshot1.tracemalloc_current,
            'gc_objects_diff': snapshot2.gc_objects - snapshot1.gc_objects,
            'time_diff_seconds': (snapshot2.timestamp - snapshot1.timestamp).total_seconds()
        }
        
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get comprehensive memory statistics.
        
        Returns:
            Dictionary with memory statistics
        """
        if not self.snapshots:
            return {}
            
        rss_values = [s.rss_mb for s in self.snapshots]
        vms_values = [s.vms_mb for s in self.snapshots]
        percent_values = [s.percent for s in self.snapshots]
        
        stats = {
            'total_snapshots': len(self.snapshots),
            'memory_rss': {
                'min': min(rss_values),
                'max': max(rss_values),
                'avg': sum(rss_values) / len(rss_values),
                'current': rss_values[-1] if rss_values else 0
            },
            'memory_vms': {
                'min': min(vms_values),
                'max': max(vms_values),
                'avg': sum(vms_values) / len(vms_values),
                'current': vms_values[-1] if vms_values else 0
            },
            'memory_percent': {
                'min': min(percent_values),
                'max': max(percent_values),
                'avg': sum(percent_values) / len(percent_values),
                'current': percent_values[-1] if percent_values else 0
            },
            'leaks_detected': len(self.leaks),
            'total_leak_amount': sum(leak.leak_amount for leak in self.leaks)
        }
        
        if self.snapshots:
            first_snapshot = self.snapshots[0]
            last_snapshot = self.snapshots[-1]
            
            stats['session_memory_growth'] = last_snapshot.rss_mb - first_snapshot.rss_mb
            stats['session_duration'] = (last_snapshot.timestamp - first_snapshot.timestamp).total_seconds()
            
        return stats
        
    def assert_no_memory_leaks(self, max_leak_mb: Optional[float] = None):
        """Assert that no significant memory leaks occurred.
        
        Args:
            max_leak_mb: Maximum allowed memory leak in MB
            
        Raises:
            AssertionError: If memory leaks are detected
        """
        threshold = max_leak_mb or self.leak_threshold_mb
        
        significant_leaks = [leak for leak in self.leaks if leak.leak_amount > threshold]
        
        if significant_leaks:
            leak_details = []
            for leak in significant_leaks:
                leak_details.append(
                    f"{leak.test_name}: {leak.leak_amount:.2f}MB "
                    f"({leak.leak_percentage:.1f}% increase)"
                )
                
            raise AssertionError(
                f"Memory leaks detected:\n" + "\n".join(leak_details)
            )
            
    def assert_memory_usage(self, max_memory_mb: float):
        """Assert that memory usage stays below threshold.
        
        Args:
            max_memory_mb: Maximum allowed memory usage in MB
            
        Raises:
            AssertionError: If memory usage exceeds threshold
        """
        if not self.snapshots:
            return
            
        max_usage = max(s.rss_mb for s in self.snapshots)
        
        if max_usage > max_memory_mb:
            raise AssertionError(
                f"Memory usage {max_usage:.2f}MB exceeds threshold {max_memory_mb}MB"
            )
            
    def generate_memory_report(self) -> Dict[str, Any]:
        """Generate comprehensive memory report.
        
        Returns:
            Dictionary with memory report data
        """
        stats = self.get_memory_stats()
        top_allocations = self.get_top_allocations()
        
        report = {
            'summary': stats,
            'leaks': [
                {
                    'test_name': leak.test_name,
                    'leak_amount_mb': leak.leak_amount,
                    'leak_percentage': leak.leak_percentage,
                    'start_memory_mb': leak.start_memory,
                    'end_memory_mb': leak.end_memory,
                    'duration_seconds': leak.duration,
                    'threshold_mb': leak.threshold_mb
                }
                for leak in self.leaks
            ],
            'top_allocations': [
                {
                    'filename': alloc.filename,
                    'line_number': alloc.line_number,
                    'size_mb': alloc.size_mb,
                    'count': alloc.count,
                    'traceback': alloc.traceback[:3]  # Limit traceback length
                }
                for alloc in top_allocations
            ],
            'snapshots': [
                {
                    'timestamp': snapshot.timestamp.isoformat(),
                    'rss_mb': snapshot.rss_mb,
                    'vms_mb': snapshot.vms_mb,
                    'percent': snapshot.percent,
                    'gc_objects': snapshot.gc_objects,
                    'custom_metrics': snapshot.custom_metrics
                }
                for snapshot in self.snapshots[-50:]  # Last 50 snapshots
            ]
        }
        
        return report
        
    def export_memory_report(self, filepath: str):
        """Export memory report to file.
        
        Args:
            filepath: Path to export file
        """
        import json
        
        report = self.generate_memory_report()
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2, default=str)
            
    def clear_data(self):
        """Clear all collected memory data."""
        self.snapshots.clear()
        self.leaks.clear()
        
    def get_current_memory_info(self) -> Dict[str, float]:
        """Get current memory information.
        
        Returns:
            Dictionary with current memory info
        """
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024
        }
        
    def force_garbage_collection(self) -> Dict[str, int]:
        """Force garbage collection and return statistics.
        
        Returns:
            Dictionary with GC statistics
        """
        # Get stats before GC
        before_objects = len(gc.get_objects())
        
        # Force collection
        collected = gc.collect()
        
        # Get stats after GC
        after_objects = len(gc.get_objects())
        
        return {
            'objects_before': before_objects,
            'objects_after': after_objects,
            'objects_collected': collected,
            'objects_freed': before_objects - after_objects
        }


# Global memory profiler instance
memory_profiler = MemoryProfiler()


# Convenience functions
def profile_memory(test_name: str = "test", check_leaks: bool = True):
    """Convenience function to profile memory."""
    return memory_profiler.profile_memory(test_name, check_leaks)


def memory_test(test_name: str = None, check_leaks: bool = True):
    """Convenience decorator for memory testing."""
    return memory_profiler.memory_test(test_name, check_leaks)


def take_memory_snapshot(custom_metrics: Optional[Dict[str, Any]] = None):
    """Convenience function to take memory snapshot."""
    return memory_profiler.take_snapshot(custom_metrics)