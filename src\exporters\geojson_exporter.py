__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""GeoJSON data exporter.

This module provides GeoJSON export functionality for geospatial data.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from .base import BaseExporter, ExportError, ExportResult

# Configure logging
logger = logging.getLogger(__name__)


class GeoJSONExporter(BaseExporter):
    """GeoJSON data exporter."""

    def __init__(
        self,
        output_path: Union[str, Path],
        indent: Optional[int] = 2,
        encoding: str = "utf-8",
        **kwargs,
    ):
        """Initialize GeoJSON exporter.

        Args:
            output_path: Path where GeoJSON file will be saved
            indent: Number of spaces for indentation (None for compact)
            encoding: File encoding
            **kwargs: Additional configuration options
        """
        super().__init__(output_path, **kwargs)
        self.indent = indent
        self.encoding = encoding

        # Ensure output file has .geojson extension
        if not self.output_path.suffix:
            self.output_path = self.output_path.with_suffix(".geojson")
        elif self.output_path.suffix.lower() not in [".geojson", ".json"]:
            self.output_path = self.output_path.with_suffix(".geojson")

    async def export(self, data: Any, **kwargs) -> ExportResult:
        """Export data to GeoJSON format.

        Args:
            data: Data to export (GeoDataFrame, dict, or list of features)
            **kwargs: Additional export options
                - crs: Coordinate reference system
                - geometry_column: Name of geometry column

        Returns:
            ExportResult: Result of the export operation

        Raises:
            ExportError: If export fails
        """
        try:
            self.validate_data(data)
            self.prepare_output_directory()

            crs = kwargs.get("crs", None)
            geometry_column = kwargs.get("geometry_column", "geometry")

            # Convert data to GeoJSON format
            geojson_data = self._prepare_data_for_geojson(data, crs, geometry_column)

            # Write GeoJSON to file
            with open(self.output_path, "w", encoding=self.encoding) as f:
                json.dump(
                    geojson_data, f, indent=self.indent, ensure_ascii=False, default=str
                )

            # Calculate records exported
            records_exported = len(geojson_data.get("features", []))
            file_size = (
                self.output_path.stat().st_size if self.output_path.exists() else 0
            )

            logger.info(
                f"Successfully exported {records_exported} features to {self.output_path}"
            )

            return ExportResult(
                success=True,
                file_path=self.output_path,
                records_exported=records_exported,
                file_size_bytes=file_size,
                metadata={
                    "indent": self.indent,
                    "encoding": self.encoding,
                    "crs": crs,
                    "geometry_column": geometry_column,
                },
            )

        except Exception as e:
            error_msg = f"Failed to export GeoJSON: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ExportResult(success=False, error_message=error_msg)

    def _prepare_data_for_geojson(
        self, data: Any, crs: Optional[str], geometry_column: str
    ) -> Dict[str, Any]:
        """Prepare data for GeoJSON serialization."""
        if hasattr(data, "to_json"):  # GeoDataFrame
            return json.loads(data.to_json())
        elif isinstance(data, dict):
            # Assume it's already a GeoJSON-like structure
            if "type" not in data:
                data["type"] = "FeatureCollection"
            if "features" not in data:
                data["features"] = []
            return data
        elif isinstance(data, list):
            # List of features
            return {"type": "FeatureCollection", "features": data}
        else:
            # Try to create a basic feature collection
            return {"type": "FeatureCollection", "features": []}
