#!/usr/bin/env python3
"""Schema validators module.

This module provides schema validation logic for validating table structures
against expected definitions or rules. It focuses on verifying existing
schema structures rather than initial input validation.
"""

import logging
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple

import asyncpg
from sqlalchemy import MetaData, Table, create_engine, inspect
from sqlalchemy.engine import Engine

from ..exceptions import DatabaseError, SchemaError, ValidationError
from ..utils.validators import InputValidator

logger = logging.getLogger(__name__)


class ValidationSeverity(Enum):
    """Validation severity levels."""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class ValidationResult:
    """Result of a schema validation operation."""

    is_valid: bool
    discrepancies: List[Dict[str, Any]]
    summary: str
    severity: ValidationSeverity

    def add_discrepancy(self, discrepancy: Dict[str, Any]) -> None:
        """Add a discrepancy to the validation result."""
        self.discrepancies.append(discrepancy)
        self.is_valid = False


@dataclass
class ColumnDefinition:
    """Expected column definition for validation."""

    name: str
    data_type: str
    nullable: bool = True
    primary_key: bool = False
    foreign_key: Optional[str] = None
    default_value: Optional[Any] = None
    max_length: Optional[int] = None
    unique: bool = False


@dataclass
class TableStructure:
    """Expected table structure definition."""

    table_name: str
    schema_name: str
    columns: List[ColumnDefinition]
    indexes: Optional[List[str]] = None
    constraints: Optional[List[str]] = None


class SchemaValidator:
    """Schema validation class for validating table structures.

    This class provides methods to validate actual table structures against
    expected definitions, focusing on verifying existing schemas rather than
    initial input validation.
    """

    def __init__(
        self, connection_pool: asyncpg.Pool, validator: Optional[InputValidator] = None
    ):
        """Initialize schema validator.

        Args:
            connection_pool: AsyncPG connection pool
            validator: Input validator instance for name validation
        """
        self.pool = connection_pool
        self.validator = validator or InputValidator()

        # Create SQLAlchemy engine for introspection
        # Note: This is only used for schema introspection, not actual connections
        self._engine = None

    async def _get_sqlalchemy_engine(self) -> Engine:
        """Get SQLAlchemy engine for introspection.

        Returns:
            SQLAlchemy engine instance
        """
        if self._engine is None:
            # Get connection info from the pool
            async with self.pool.acquire() as conn:
                dsn = conn.get_dsn_parameters()
                db_url = f"postgresql://{dsn['user']}:{dsn.get('password', '')}@{dsn['host']}:{dsn['port']}/{dsn['dbname']}"
                self._engine = create_engine(db_url)
        return self._engine

    async def validate_table_structure(
        self, table_name: str, schema_name: str, expected_structure: Dict[str, Any]
    ) -> ValidationResult:
        """Validate table structure against expected definition.

        Args:
            table_name: Name of the table to validate
            schema_name: Schema name containing the table
            expected_structure: Expected table structure definition

        Returns:
            ValidationResult with validation details

        Raises:
            ValidationError: If table or schema names are invalid
            DatabaseError: If database operation fails
        """
        # Validate input parameters
        is_valid_table, table_error = self.validator.validate_identifier(
            table_name, "table name"
        )
        if not is_valid_table:
            raise ValidationError(f"Invalid table name: {table_error}")

        is_valid_schema, schema_error = self.validator.validate_identifier(
            schema_name, "schema name"
        )
        if not is_valid_schema:
            raise ValidationError(f"Invalid schema name: {schema_error}")

        try:
            # Check if table exists
            if not await self._table_exists(table_name, schema_name):
                return ValidationResult(
                    is_valid=False,
                    discrepancies=[
                        {
                            "type": "table_missing",
                            "message": f"Table '{schema_name}.{table_name}' does not exist",
                            "severity": ValidationSeverity.CRITICAL.value,
                        }
                    ],
                    summary=f"Table '{schema_name}.{table_name}' not found",
                    severity=ValidationSeverity.CRITICAL,
                )

            # Get actual table structure
            actual_structure = await self._get_table_structure(table_name, schema_name)

            # Perform validation
            result = self._compare_structures(
                actual_structure, expected_structure, table_name, schema_name
            )

            # Log validation results
            self._log_validation_results(result, table_name, schema_name)

            return result

        except asyncpg.PostgresError as e:
            logger.error(
                f"Database error during validation of {schema_name}.{table_name}: {e}"
            )
            raise DatabaseError(f"Database error during validation: {e}") from e
        except Exception as e:
            logger.error(
                f"Unexpected error during validation of {schema_name}.{table_name}: {e}"
            )
            raise DatabaseError(f"Unexpected error during validation: {e}") from e

    async def validate_multiple_tables(
        self, table_definitions: List[Dict[str, Any]]
    ) -> Dict[str, ValidationResult]:
        """Validate multiple tables against their expected structures.

        Args:
            table_definitions: List of table definition dictionaries

        Returns:
            Dictionary mapping table names to validation results
        """
        results = {}

        for table_def in table_definitions:
            table_name = table_def.get("table_name")
            schema_name = table_def.get("schema_name")
            expected_structure = table_def.get("expected_structure")

            if not all([table_name, schema_name, expected_structure]):
                logger.warning(f"Incomplete table definition: {table_def}")
                continue

            try:
                result = await self.validate_table_structure(
                    table_name, schema_name, expected_structure
                )
                results[f"{schema_name}.{table_name}"] = result
            except Exception as e:
                logger.error(f"Failed to validate {schema_name}.{table_name}: {e}")
                results[f"{schema_name}.{table_name}"] = ValidationResult(
                    is_valid=False,
                    discrepancies=[
                        {
                            "type": "validation_error",
                            "message": str(e),
                            "severity": ValidationSeverity.ERROR.value,
                        }
                    ],
                    summary=f"Validation failed: {e}",
                    severity=ValidationSeverity.ERROR,
                )

        return results

    async def _table_exists(self, table_name: str, schema_name: str) -> bool:
        """Check if table exists in the specified schema.

        Args:
            table_name: Name of the table
            schema_name: Schema name

        Returns:
            True if table exists, False otherwise
        """
        async with self.pool.acquire() as conn:
            query = """
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables
                    WHERE table_schema = $1 AND table_name = $2
                )
            """
            result = await conn.fetchval(query, schema_name, table_name)
            return bool(result)

    async def _get_table_structure(
        self, table_name: str, schema_name: str
    ) -> Dict[str, Any]:
        """Get actual table structure from database.

        Args:
            table_name: Name of the table
            schema_name: Schema name

        Returns:
            Dictionary containing table structure information
        """
        async with self.pool.acquire() as conn:
            # Get column information
            columns_query = """
                SELECT
                    column_name,
                    data_type,
                    is_nullable,
                    column_default,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale
                FROM information_schema.columns
                WHERE table_schema = $1 AND table_name = $2
                ORDER BY ordinal_position
            """

            columns = await conn.fetch(columns_query, schema_name, table_name)

            # Get primary key information
            pk_query = """
                SELECT column_name
                FROM information_schema.key_column_usage kcu
                JOIN information_schema.table_constraints tc
                    ON kcu.constraint_name = tc.constraint_name
                WHERE tc.table_schema = $1
                    AND tc.table_name = $2
                    AND tc.constraint_type = 'PRIMARY KEY'
                ORDER BY kcu.ordinal_position
            """

            primary_keys = await conn.fetch(pk_query, schema_name, table_name)
            pk_columns = {row["column_name"] for row in primary_keys}

            # Get foreign key information
            fk_query = """
                SELECT
                    kcu.column_name,
                    ccu.table_schema AS foreign_table_schema,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.key_column_usage kcu
                JOIN information_schema.referential_constraints rc
                    ON kcu.constraint_name = rc.constraint_name
                JOIN information_schema.constraint_column_usage ccu
                    ON rc.unique_constraint_name = ccu.constraint_name
                WHERE kcu.table_schema = $1 AND kcu.table_name = $2
            """

            foreign_keys = await conn.fetch(fk_query, schema_name, table_name)
            fk_map = {}
            for fk in foreign_keys:
                fk_map[
                    fk["column_name"]
                ] = f"{fk['foreign_table_schema']}.{fk['foreign_table_name']}.{fk['foreign_column_name']}"

            # Get unique constraints
            unique_query = """
                SELECT column_name
                FROM information_schema.key_column_usage kcu
                JOIN information_schema.table_constraints tc
                    ON kcu.constraint_name = tc.constraint_name
                WHERE tc.table_schema = $1
                    AND tc.table_name = $2
                    AND tc.constraint_type = 'UNIQUE'
            """

            unique_constraints = await conn.fetch(unique_query, schema_name, table_name)
            unique_columns = {row["column_name"] for row in unique_constraints}

            # Build structure dictionary
            structure = {
                "table_name": table_name,
                "schema_name": schema_name,
                "columns": [],
                "primary_keys": list(pk_columns),
                "foreign_keys": fk_map,
                "unique_columns": list(unique_columns),
            }

            for col in columns:
                column_info = {
                    "name": col["column_name"],
                    "data_type": col["data_type"],
                    "nullable": col["is_nullable"] == "YES",
                    "default": col["column_default"],
                    "max_length": col["character_maximum_length"],
                    "precision": col["numeric_precision"],
                    "scale": col["numeric_scale"],
                    "primary_key": col["column_name"] in pk_columns,
                    "foreign_key": fk_map.get(col["column_name"]),
                    "unique": col["column_name"] in unique_columns,
                }
                structure["columns"].append(column_info)

            return structure

    def _compare_structures(
        self,
        actual: Dict[str, Any],
        expected: Dict[str, Any],
        table_name: str,
        schema_name: str,
    ) -> ValidationResult:
        """Compare actual and expected table structures.

        Args:
            actual: Actual table structure from database
            expected: Expected table structure
            table_name: Name of the table
            schema_name: Schema name

        Returns:
            ValidationResult with comparison details
        """
        result = ValidationResult(
            is_valid=True,
            discrepancies=[],
            summary="",
            severity=ValidationSeverity.INFO,
        )

        # Compare columns
        actual_columns = {col["name"]: col for col in actual["columns"]}
        expected_columns = expected.get("columns", {})

        # Check for missing columns
        for exp_col_name, exp_col_def in expected_columns.items():
            if exp_col_name not in actual_columns:
                result.add_discrepancy(
                    {
                        "type": "missing_column",
                        "column": exp_col_name,
                        "message": f"Expected column '{exp_col_name}' not found in table",
                        "severity": ValidationSeverity.ERROR.value,
                    }
                )
                result.severity = ValidationSeverity.ERROR
                continue

            # Compare column properties
            actual_col = actual_columns[exp_col_name]
            self._compare_column_properties(
                actual_col, exp_col_def, exp_col_name, result
            )

        # Check for unexpected columns
        for actual_col_name in actual_columns:
            if actual_col_name not in expected_columns:
                result.add_discrepancy(
                    {
                        "type": "unexpected_column",
                        "column": actual_col_name,
                        "message": f"Unexpected column '{actual_col_name}' found in table",
                        "severity": ValidationSeverity.WARNING.value,
                    }
                )
                if result.severity == ValidationSeverity.INFO:
                    result.severity = ValidationSeverity.WARNING

        # Compare primary keys
        expected_pks = expected.get("primary_keys", [])
        actual_pks = actual.get("primary_keys", [])

        if set(expected_pks) != set(actual_pks):
            result.add_discrepancy(
                {
                    "type": "primary_key_mismatch",
                    "expected": expected_pks,
                    "actual": actual_pks,
                    "message": f"Primary key mismatch. Expected: {expected_pks}, Actual: {actual_pks}",
                    "severity": ValidationSeverity.ERROR.value,
                }
            )
            result.severity = ValidationSeverity.ERROR

        # Set summary
        if result.is_valid:
            result.summary = f"Table '{schema_name}.{table_name}' structure is valid"
        else:
            discrepancy_count = len(result.discrepancies)
            result.summary = f"Table '{schema_name}.{table_name}' has {discrepancy_count} discrepancies"

        return result

    def _compare_column_properties(
        self,
        actual_col: Dict[str, Any],
        expected_col: Dict[str, Any],
        column_name: str,
        result: ValidationResult,
    ) -> None:
        """Compare individual column properties.

        Args:
            actual_col: Actual column information
            expected_col: Expected column definition
            column_name: Name of the column
            result: ValidationResult to update
        """
        # Compare data type
        expected_type = expected_col.get("data_type", "").lower()
        actual_type = actual_col.get("data_type", "").lower()

        if expected_type and expected_type != actual_type:
            result.add_discrepancy(
                {
                    "type": "data_type_mismatch",
                    "column": column_name,
                    "expected": expected_type,
                    "actual": actual_type,
                    "message": f"Column '{column_name}' data type mismatch. Expected: {expected_type}, Actual: {actual_type}",
                    "severity": ValidationSeverity.ERROR.value,
                }
            )
            result.severity = ValidationSeverity.ERROR

        # Compare nullable
        if "nullable" in expected_col:
            expected_nullable = expected_col["nullable"]
            actual_nullable = actual_col.get("nullable", True)

            if expected_nullable != actual_nullable:
                result.add_discrepancy(
                    {
                        "type": "nullable_mismatch",
                        "column": column_name,
                        "expected": expected_nullable,
                        "actual": actual_nullable,
                        "message": f"Column '{column_name}' nullable mismatch. Expected: {expected_nullable}, Actual: {actual_nullable}",
                        "severity": ValidationSeverity.WARNING.value,
                    }
                )
                if result.severity == ValidationSeverity.INFO:
                    result.severity = ValidationSeverity.WARNING

        # Compare primary key
        if "primary_key" in expected_col:
            expected_pk = expected_col["primary_key"]
            actual_pk = actual_col.get("primary_key", False)

            if expected_pk != actual_pk:
                result.add_discrepancy(
                    {
                        "type": "primary_key_column_mismatch",
                        "column": column_name,
                        "expected": expected_pk,
                        "actual": actual_pk,
                        "message": f"Column '{column_name}' primary key mismatch. Expected: {expected_pk}, Actual: {actual_pk}",
                        "severity": ValidationSeverity.ERROR.value,
                    }
                )
                result.severity = ValidationSeverity.ERROR

        # Compare foreign key
        if "foreign_key" in expected_col:
            expected_fk = expected_col["foreign_key"]
            actual_fk = actual_col.get("foreign_key")

            if expected_fk != actual_fk:
                result.add_discrepancy(
                    {
                        "type": "foreign_key_mismatch",
                        "column": column_name,
                        "expected": expected_fk,
                        "actual": actual_fk,
                        "message": f"Column '{column_name}' foreign key mismatch. Expected: {expected_fk}, Actual: {actual_fk}",
                        "severity": ValidationSeverity.WARNING.value,
                    }
                )
                if result.severity == ValidationSeverity.INFO:
                    result.severity = ValidationSeverity.WARNING

    def _log_validation_results(
        self, result: ValidationResult, table_name: str, schema_name: str
    ) -> None:
        """Log validation results.

        Args:
            result: Validation result
            table_name: Name of the table
            schema_name: Schema name
        """
        if result.is_valid:
            logger.info(f"Schema validation passed for {schema_name}.{table_name}")
        else:
            logger.warning(
                f"Schema validation failed for {schema_name}.{table_name}: {result.summary}"
            )

            for discrepancy in result.discrepancies:
                severity = discrepancy.get("severity", "info")
                message = discrepancy.get("message", "Unknown discrepancy")

                if severity == "critical":
                    logger.critical(f"  CRITICAL: {message}")
                elif severity == "error":
                    logger.error(f"  ERROR: {message}")
                elif severity == "warning":
                    logger.warning(f"  WARNING: {message}")
                else:
                    logger.info(f"  INFO: {message}")

    async def validate_schema_consistency(self, schema_name: str) -> ValidationResult:
        """Validate overall schema consistency.

        Args:
            schema_name: Schema name to validate

        Returns:
            ValidationResult with schema consistency details
        """
        is_valid_schema, schema_error = self.validator.validate_identifier(
            schema_name, "schema name"
        )
        if not is_valid_schema:
            raise ValidationError(f"Invalid schema name: {schema_error}")

        result = ValidationResult(
            is_valid=True,
            discrepancies=[],
            summary="",
            severity=ValidationSeverity.INFO,
        )

        try:
            async with self.pool.acquire() as conn:
                # Check if schema exists
                schema_exists_query = """
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.schemata
                        WHERE schema_name = $1
                    )
                """

                schema_exists = await conn.fetchval(schema_exists_query, schema_name)

                if not schema_exists:
                    result.add_discrepancy(
                        {
                            "type": "schema_missing",
                            "message": f"Schema '{schema_name}' does not exist",
                            "severity": ValidationSeverity.CRITICAL.value,
                        }
                    )
                    result.severity = ValidationSeverity.CRITICAL
                    result.summary = f"Schema '{schema_name}' not found"
                    return result

                # Get all tables in schema
                tables_query = """
                    SELECT table_name
                    FROM information_schema.tables
                    WHERE table_schema = $1 AND table_type = 'BASE TABLE'
                    ORDER BY table_name
                """

                tables = await conn.fetch(tables_query, schema_name)
                table_count = len(tables)

                # Check for orphaned foreign keys
                orphaned_fks_query = """
                    SELECT
                        kcu.table_name,
                        kcu.column_name,
                        ccu.table_name AS referenced_table
                    FROM information_schema.key_column_usage kcu
                    JOIN information_schema.referential_constraints rc
                        ON kcu.constraint_name = rc.constraint_name
                    JOIN information_schema.constraint_column_usage ccu
                        ON rc.unique_constraint_name = ccu.constraint_name
                    WHERE kcu.table_schema = $1
                        AND NOT EXISTS (
                            SELECT 1 FROM information_schema.tables t
                            WHERE t.table_schema = ccu.table_schema
                                AND t.table_name = ccu.table_name
                        )
                """

                orphaned_fks = await conn.fetch(orphaned_fks_query, schema_name)

                for fk in orphaned_fks:
                    result.add_discrepancy(
                        {
                            "type": "orphaned_foreign_key",
                            "table": fk["table_name"],
                            "column": fk["column_name"],
                            "referenced_table": fk["referenced_table"],
                            "message": f"Foreign key in {fk['table_name']}.{fk['column_name']} references non-existent table {fk['referenced_table']}",
                            "severity": ValidationSeverity.ERROR.value,
                        }
                    )
                    result.severity = ValidationSeverity.ERROR

                # Set summary
                if result.is_valid:
                    result.summary = f"Schema '{schema_name}' is consistent with {table_count} tables"
                else:
                    discrepancy_count = len(result.discrepancies)
                    result.summary = f"Schema '{schema_name}' has {discrepancy_count} consistency issues"

                return result

        except asyncpg.PostgresError as e:
            logger.error(f"Database error during schema consistency validation: {e}")
            raise DatabaseError(f"Database error during schema validation: {e}") from e
        except Exception as e:
            logger.error(f"Unexpected error during schema consistency validation: {e}")
            raise DatabaseError(
                f"Unexpected error during schema validation: {e}"
            ) from e
