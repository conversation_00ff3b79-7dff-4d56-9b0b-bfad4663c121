# Connect CLI 使用指南

## 概述

Connect CLI 是一个智能化的电信数据导入命令行工具，专为电信行业设计。它具有强大的自动检测功能，能够智能识别数据类型、运营商和文件格式，支持CDR、EP、NLG、KPI、SCORE、CFG等多种电信数据类型的高效导入。

## 核心特性

### 🚀 智能自动检测
- **数据类型自动识别**：根据目录名、文件名和内容智能判断数据类型
- **运营商自动识别**：支持Telefonica、Vodafone、Telekom等运营商的自动检测
- **Schema自动映射**：根据数据类型和运营商自动选择正确的数据库Schema
- **文件格式自动适配**：支持Excel (.xlsx, .xls, .xlsb)、CSV、XML等多种格式

### ⚡ 高性能处理
- **并行处理**：自动并行处理多个文件，提高导入效率
- **批量导入**：智能批处理，优化内存使用和数据库性能
- **递归扫描**：自动递归扫描目录结构，发现所有可导入文件
- **进度监控**：实时显示导入进度和统计信息

### 🛡️ 可靠性保障
- **预览模式**：导入前预览文件和数据，确保准确性
- **错误处理**：完善的错误处理和恢复机制
- **数据验证**：自动验证数据完整性和格式正确性
- **事务保护**：确保数据导入的原子性和一致性

## 安装与配置

### 环境要求
- Python 3.8+
- PostgreSQL 数据库
- 必要的依赖包（见 requirements.txt）

### 配置文件
配置文件会自动从以下位置加载：
- `config/base.yaml` - 基础配置
- `config/database.yaml` - 数据库配置
- 或通过环境变量覆盖

### 数据库配置
确保PostgreSQL数据库已正确配置，包括以下模式：
- `ep_to2` - EP数据
- `cdr_to2`, `cdr_vdf`, `cdr_tdg` - CDR数据（按运营商）
- `nlg_to2` - NLG数据
- `kpi_to2` - KPI数据
- `score_to2` - SCORE数据

## 支持的数据类型

| 类型 | 描述 | 支持格式 | 数据库模式 |
|------|------|----------|------------|
| EP | 工程参数 | .xlsx, .xls, .csv | ep_to2 |
| CDR | 呼叫详单 | .xlsx, .xls, .csv | cdr_to2/cdr_vdf/cdr_tdg |
| KPI | 关键性能指标 | .xlsx, .xls, .csv | kpi_to2 |
| SCORE | 性能评分 | .xlsx, .xls, .csv | score_to2 |
| NLG | 网络日志 | .xlsb, .xlsx, .xls | nlg_to2 |
| CFG | 配置文件 | .xml, .tar.gz, .zip | cfg_to2 |

## 命令概览

Connect CLI 提供了简洁而强大的命令接口，所有功能都通过智能自动检测实现：

```bash
# Connect CLI 基本命令
python -m src.cli.import_cli --help         # 显示帮助信息
python -m src.cli.import_cli info           # 显示系统信息和状态
python -m src.cli.import_cli import [PATH]  # 智能导入文件或目录
```

## 详细命令说明

### 1. 系统信息 (info)

显示Connect导入系统的详细信息，包括支持的数据类型、运营商配置和数据库连接状态。

#### 基本语法
```bash
python -m src.cli.import_cli info
```

#### 功能特性
- 显示所有支持的数据类型 (CDR, EP, NLG, KPI, CFG, SCORE)
- 显示支持的运营商 (Telefonica, Vodafone, Telekom)
- 显示数据库模式映射关系
- 显示支持的文件格式
- 实时检查数据库连接状态
- 显示系统版本和配置信息

#### 输出示例
```
🚀 Connect 电信数据导入系统 v2.0.0

📊 支持的数据类型:
  • EP (工程参数) → ep_to2
  • CDR (呼叫详单) → cdr_to2/cdr_vdf/cdr_tdg
  • NLG (网络日志) → nlg_to2
  • KPI (关键指标) → kpi_to2
  • SCORE (性能评分) → score_to2
  • CFG (配置文件) → cfg_to2

🏢 支持的运营商:
  • Telefonica → cdr_to2
  • Vodafone → cdr_vdf
  • Telekom → cdr_tdg

💾 数据库状态: ✅ 已连接 (PostgreSQL)
```

### 2. 智能导入 (import)

Connect CLI的核心功能，提供完全自动化的数据导入体验。无需手动指定参数，系统会智能检测所有必要信息。

#### 基本语法
```bash
python -m src.cli.import_cli import [PATH] [OPTIONS]
```

#### 主要选项
- `--preview`: 预览模式，显示将要处理的文件但不执行实际导入

#### 🧠 智能检测功能（全自动）
- **数据类型自动检测**: 基于目录名称（ep/cdr/nlg/kpi/cfg/score）智能识别
- **运营商自动检测**: 基于文件内容和Excel工作表名称识别（telefonica/vodafone/telekom）
- **Schema自动选择**: 根据配置自动选择正确的数据库Schema
- **递归目录扫描**: 自动递归扫描所有子目录
- **并行处理**: 自动并行处理多个文件（智能调整并发数）
- **批处理大小**: 根据文件大小和系统资源自动调整批处理大小

#### 🎯 自动化特性
- **零配置导入**: 无需手动指定任何参数
- **智能文件发现**: 自动发现和分类所有可导入文件
- **自适应处理**: 根据文件大小和类型自动调整处理策略
- **实时进度显示**: 动态显示导入进度和统计信息

#### 使用示例

##### 单文件导入（完全自动化）
```bash
# 导入单个文件（自动检测数据类型、运营商、Schema）
python -m src.cli.import_cli import data/input/ep/2024/CW01/TEF_Sites_CW01.xlsx

# 导入CDR文件（自动检测运营商和对应Schema）
python -m src.cli.import_cli import data/input/cdr/2024/Q1/Voice/Shared_Benchmark_Q1_DE_2024_Voice_M2M_Calls.xlsx

# 导入NLG文件（自动处理.xlsb格式）
python -m src.cli.import_cli import data/input/nlg/2024/NLG_CUBE_aktuell_2024-01-05.xlsb

# 预览文件内容（查看检测结果但不导入）
python -m src.cli.import_cli import data/input/ep/2024/CW01/TEF_Sites_CW01.xlsx --preview
```

##### 目录批量导入（智能递归处理）
```bash
# 导入整个目录（自动递归扫描和并行处理）
python -m src.cli.import_cli import data/input

# 导入特定数据类型目录
python -m src.cli.import_cli import data/input/ep/2024/CW01

# 预览模式（查看所有将要导入的文件和检测结果）
python -m src.cli.import_cli import data/input --preview

# 导入根目录（处理所有数据类型）
python -m src.cli.import_cli import D:\connect\data\input
```

## 实际使用示例

### 真实CLI输出示例

以下是Connect CLI的实际运行输出，展示智能检测和导入过程：

#### 系统信息查看
```bash
$ python -m src.cli.import_cli info

╭────────────────────────────────────────────────────╮
│ Connect Telecommunications Data Import CLI│
│ Advanced telecom data processing and import system │
╰────────────────────────────────────────────────────╯

🚀 Connect 电信数据导入系统 v2.0.0

📊 支持的数据类型:
  • EP (工程参数) → ep_to2
  • CDR (呼叫详单) → cdr_to2/cdr_vdf/cdr_tdg
  • NLG (网络日志) → nlg_to2
  • KPI (关键指标) → kpi_to2
  • SCORE (性能评分) → score_to2
  • CFG (配置文件) → cfg_to2

🏢 支持的运营商:
  • Telefonica → cdr_to2
  • Vodafone → cdr_vdf
  • Telekom → cdr_tdg

📁 支持的文件格式:
  • Excel: .xlsx, .xls, .xlsb
  • CSV: .csv
  • 配置: .xml, .tar.gz, .zip

💾 数据库状态: ✅ 已连接 (PostgreSQL localhost:5432)
🗄️ 可用Schemas: ep_to2, cdr_to2, cdr_vdf, cdr_tdg, nlg_to2, kpi_to2
```

#### EP数据单文件导入
```bash
$ python -m src.cli.import_cli import "data/input/ep/2024/CW01/TEF_Sites_CW01.xlsx"

╭────────────────────────────────────────────────────╮
│ Connect Telecommunications Data Import CLI│
│ Advanced telecom data processing and import system │
╰────────────────────────────────────────────────────╯

📁 Single File Import Mode
🔍 Analyzing file: TEF_Sites_CW01.xlsx

📊 File Analysis Results:
  📊 Data Type: ep (auto-detected from directory)
  🏢 Operator: telefonica (auto-detected from filename)
  🗄️ Target Schema: ep_to2
  📏 File Size: 27.8 MB
  📋 Estimated Records: ~47,000

🚀 Starting import...
⚡ Processing: TEF_Sites_CW01.xlsx → ep (telefonica)
✅ Import completed successfully!

📈 Import Statistics:
  • Records imported: 47,927
  • Processing time: 4.52 seconds
  • Import rate: 10,606 records/second
  • Success rate: 100%
  • Target table: ep_to2.tef_sites_cw01
```

#### 目录预览模式
```bash
$ python -m src.cli.import_cli import "data/input/ep/2024/CW01" --preview

╭────────────────────────────────────────────────────╮
│ Connect Telecommunications Data Import CLI│
│ Advanced telecom data processing and import system │
╰────────────────────────────────────────────────────╯

📁 Directory Import Mode
🔍 Recursively scanning directory: data\input\ep\2024\CW01
📊 Scan results: Total 4 files, found 4 importable files

📊 Processing Plan:
  Total files: 4
  Total size: 111.2 MB
  Estimated time: 1.1 minutes
  Memory requirement: 11 MB

📋 File Distribution Statistics:
  📊 EP (qgis): 3 files
  📊 EP (telefonica): 1 files

🔍 Preview Mode - Files to be processed:
    1. GSMCELL_CW01.xlsx
       📊 ep | 🏢 qgis | 🗄️ ep_to2
    2. LTECELL_CW01.xlsx
       📊 ep | 🏢 qgis | 🗄️ ep_to2
    3. NRCELL_CW01.xlsx
       📊 ep | 🏢 qgis | 🗄️ ep_to2
    4. TEF_Sites_CW01.xlsx
       📊 ep | 🏢 telefonica | 🗄️ ep_to2

📈 Import Configuration:
  📁 Total files: 4
  🔄 Parallel workers: 4
  📦 Batch size: 1000

Start import? [y/N]: n
❌ Import cancelled by user.
```

#### CDR数据智能检测
```bash
$ python -m src.cli.import_cli import "data/input/cdr/2024/Q1/Voice/Shared_Benchmark_Q1_DE_2024_Voice_M2M_Calls.xlsx" --preview

╭────────────────────────────────────────────────────╮
│ Connect Telecommunications Data Import CLI│
│ Advanced telecom data processing and import system │
╰────────────────────────────────────────────────────╯

📁 Single File Import Mode
🔍 Analyzing file: Shared_Benchmark_Q1_DE_2024_Voice_M2M_Calls.xlsx

📊 File Analysis Results:
  📊 Data Type: cdr (auto-detected from directory)
  🏢 Operator: telefonica (auto-detected from sheet analysis)
  🗄️ Target Schema: cdr_to2
  📏 File Size: 15.3 MB
  📋 Sheet Detection: Found 'Telefonica' sheet
  📋 Estimated Records: ~25,000

🔍 Preview Mode - File details:
    📊 cdr | 🏢 telefonica | 🗄️ cdr_to2
    📋 Excel Sheets: ['Summary', 'Telefonica', 'Vodafone', 'Telekom']
    🎯 Selected Sheet: 'Telefonica' (operator match)

Start import? [y/N]: n
❌ Import cancelled by user.
```

#### NLG数据导入（.xlsb格式）
```bash
$ python -m src.cli.import_cli import "data/input/nlg/2024/NLG_CUBE_aktuell_2024-01-05.xlsb" --preview

╭────────────────────────────────────────────────────╮
│ Connect Telecommunications Data Import CLI│
│ Advanced telecom data processing and import system │
╰────────────────────────────────────────────────────╯

📁 Single File Import Mode
🔍 Analyzing file: NLG_CUBE_aktuell_2024-01-05.xlsb

📊 File Analysis Results:
  📊 Data Type: nlg (auto-detected from directory)
  🏢 Operator: generic (no specific operator detected)
  🗄️ Target Schema: nlg_to2
  📏 File Size: 8.9 MB
  📋 Format: Excel Binary (.xlsb)
  📋 Estimated Records: ~15,000

🔍 Preview Mode - File details:
    📊 nlg | 🏢 generic | 🗄️ nlg_to2
    📋 Binary Excel format supported
    🎯 Target table: nlg_cube_aktuell_2024_01_05

Start import? [y/N]: n
❌ Import cancelled by user.
```

## 文件组织方式

系统支持多种文件组织方式，包括深度嵌套的复杂目录结构。

### 方式1：标准子目录组织
```
data/input/
├── EP/
│   ├── ep_params_2024.csv
│   └── ep_config.xlsx
├── CDR/
│   ├── cdr_records_jan.csv
│   └── cdr_records_feb.csv
└── KPI/
    └── kpi_metrics.xlsx
```

### 方式2：文件名前缀识别
```
data/input/
├── ep_params_2024.csv
├── cdr_records_jan.csv
├── kpi_metrics.xlsx
└── score_results.xlsx
```

### 方式3：深度嵌套目录（推荐）
```
data/input/
├── 2024/
│   ├── Q1/
│   │   ├── EP/
│   │   │   ├── engineering_params.xlsx
│   │   │   └── equipment_data.csv
│   │   └── CDR/
│   │       ├── call_records_jan.csv
│   │       └── call_records_feb.csv
│   └── Q2/
│       ├── performance/
│       │   ├── kpi_metrics.xlsx
│       │   └── score_analysis.csv
│       └── network_logs/
│           └── nlg_data.xlsb
└── archive/
    ├── config_files/
    │   ├── cfg_network.xml
    │   └── cfg_system.tar.gz
    └── historical/
        └── old_ep_data.csv
```

### 方式4：智能关键词识别
```
data/input/
├── telecom_data/
│   ├── engineering/          # 包含"engineering"关键词，识别为EP
│   │   └── params.xlsx
│   ├── call_detail/          # 包含"call"关键词，识别为CDR
│   │   └── records.csv
│   └── performance/          # 包含"performance"关键词，识别为KPI
│       └── metrics.xlsx
└── mixed_files/
    ├── ep_data.csv           # 文件名前缀识别
    ├── cdr_analysis.xlsx     # 文件名前缀识别
    └── network_config.xml    # 内容智能识别为CFG
```

### 支持的识别关键词

| 数据类型 | 目录关键词 | 文件名关键词 |
|----------|------------|-------------|
| EP | engineering, equipment, parameter | ep, engineering, equipment, param |
| CDR | call, cdr, detail, record | cdr, call, detail, record |
| KPI | kpi, performance, metric | kpi, performance, metric, indicator |
| SCORE | score, rating, evaluation | score, rating, evaluation, assessment |
| NLG | log, nlg, network | nlg, log, network, trace |
| CFG | config, configuration, setting | cfg, config, configuration, setting |

## 全局选项

- `--config, -c`: 指定配置文件路径
- `--verbose, -v`: 启用详细日志输出
- `--version`: 显示版本信息

## 输出说明

### 进度显示
- 🚀 处理进度条，显示当前状态
- ⏱️ 预计完成时间（ETA）
- 📊 实时统计信息

### 结果报告
- ✅ 成功导入的文件数量
- ❌ 失败的文件数量
- 📈 成功率百分比
- ⏱️ 总处理时间

### 状态指示
- 🔍 文件发现阶段
- 📦 批量处理阶段
- ✓ 成功完成
- ✗ 处理失败
- ⚠ 警告信息

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```
   ❌ 错误: 无法连接到数据库
   ```
   **解决方案:**
   - 检查 `config/base.yaml` 中的数据库连接参数
   - 确认PostgreSQL服务正在运行
   - 验证用户权限（默认用户：to2，密码：TO2）
   - 检查防火墙设置

2. **文件未被识别**
   ```
   ⚠️ 警告: 未找到可导入的文件
   ```
   **解决方案:**
   - 确认文件扩展名是否支持（.xlsx, .xls, .csv, .xlsb, .xml, .tar.gz, .zip）
   - 检查文件名或目录名是否包含识别关键词
   - 使用 `--preview` 查看文件扫描结果
   - 手动指定数据类型：`--type score`

3. **导入失败**
   ```
   ❌ 错误: 数据导入失败
   ```
   **解决方案:**
   - 检查文件格式和数据完整性
   - 验证数据库模式是否存在
   - 检查表名长度（PostgreSQL限制64字符）
   - 查看详细错误信息

4. **列名不匹配**
   ```
   ⚠️ 警告: No matching columns found
   ```
   **解决方案:**
   - 检查Excel文件的表头结构
   - 确认 `config/database.yaml` 中的 `skip_rows` 和 `header_row` 设置
   - 对于复杂表头，系统会自动处理

### 调试技巧

```bash
# 1. 查看系统信息和数据库状态
python -m src.cli.import_cli info

# 2. 预览模式检查文件和自动检测结果
python -m src.cli.import_cli import data/input --preview

# 3. 测试单个文件的检测和导入
python -m src.cli.import_cli import data/test.xlsx

# 4. 预览单个文件的详细检测信息
python -m src.cli.import_cli import data/test.xlsx --preview

# 5. 检查特定目录的文件发现和分类
python -m src.cli.import_cli import data/input/ep --preview
```

### 常见智能检测问题

#### 数据类型检测失败
```
⚠️ 警告: 无法确定数据类型
```
**解决方案:**
- 确保文件位于正确的目录中（ep/, cdr/, nlg/, kpi/, score/, cfg/）
- 检查文件名是否包含类型关键词
- 使用标准的目录结构组织文件

#### 运营商检测失败（CDR文件）
```
⚠️ 警告: 无法检测运营商信息
```
**解决方案:**
- 确保Excel文件包含运营商名称的工作表（Telefonica, Vodafone, Telekom）
- 检查文件名是否包含运营商信息
- 验证工作表名称的拼写和大小写

#### Schema映射错误
```
❌ 错误: 目标Schema不存在
```
**解决方案:**
- 运行 `python -m src.cli.import_cli info` 检查可用Schema
- 确认数据库连接正常
- 验证Schema权限设置

## 性能优化

### 并发设置
- **小文件（<10MB）**: 使用较高并发数（6-8）
- **大文件（>100MB）**: 使用较低并发数（2-4）
- **系统资源**: 根据CPU核心数和可用内存调整
- **默认设置**: 4个并发进程，适合大多数场景

### 内存管理
- 系统自动监控内存使用
- 大文件自动分批处理（默认批大小：5000条记录）
- 内存优化的CSV和Excel读取
- 自动垃圾回收

### 数据库优化
- 使用批量插入（COPY命令）提高性能
- 自动创建索引（时间戳、主键）
- 连接池管理，避免连接泄漏
- 事务管理，确保数据一致性

## 最佳实践

### 1. 推荐的文件组织结构
Connect CLI的智能检测功能支持多种目录结构，以下是推荐的组织方式：

```
data/input/
├── ep/                    # EP工程参数数据
│   ├── 2024/
│   │   ├── CW01/         # 按周组织
│   │   └── CW02/
│   └── 2023/
├── cdr/                   # CDR呼叫详单数据
│   ├── 2024/
│   │   ├── Q1/
│   │   │   ├── Voice/    # 按业务类型
│   │   │   └── Data/
│   │   └── Q2/
│   └── 2023/
├── nlg/                   # NLG网络日志数据
│   ├── 2024/
│   └── archive/
├── kpi/                   # KPI关键指标数据
│   ├── monthly/
│   └── quarterly/
├── score/                 # SCORE性能评分数据
└── cfg/                   # CFG配置文件数据
```

### 2. 简化的导入流程
Connect CLI的全自动化设计大大简化了导入流程：

```bash
# 1. 检查系统状态和配置
python -m src.cli.import_cli info

# 2. 预览导入计划（推荐）
python -m src.cli.import_cli import data/input --preview

# 3. 执行自动化导入（一键完成）
python -m src.cli.import_cli import data/input

# 4. 验证结果（可选）
# 系统会自动显示导入统计和成功率
```

### 3. 智能检测优化建议
- **目录命名**：使用标准的数据类型名称（ep, cdr, nlg, kpi, score, cfg）
- **文件命名**：包含运营商信息有助于自动检测（如：TEF_Sites_CW01.xlsx）
- **Excel工作表**：CDR文件中使用运营商名称作为工作表名（Telefonica, Vodafone, Telekom）
- **文件格式**：优先使用.xlsx格式，系统对其支持最完善

### 4. 性能优化建议
- **小文件批量导入**：直接导入整个目录，系统会自动并行处理
- **大文件处理**：系统会自动检测文件大小并调整处理策略
- **内存管理**：系统自动管理内存使用，无需手动调整
- **数据库优化**：系统使用连接池和批量插入优化性能

## 完整工作流示例

### 日常数据导入流程（全自动化）
```bash
# 1. 检查系统状态和配置
$ python -m src.cli.import_cli info

╭────────────────────────────────────────────────────╮
│ Connect Telecommunications Data Import CLI│
│ Advanced telecom data processing and import system │
╰────────────────────────────────────────────────────╯

🚀 Connect 电信数据导入系统 v2.0.0
💾 数据库状态: ✅ 已连接 (PostgreSQL localhost:5432)
🗄️ 可用Schemas: ep_to2, cdr_to2, cdr_vdf, cdr_tdg, nlg_to2, kpi_to2

# 2. 预览导入计划（推荐步骤）
$ python -m src.cli.import_cli import D:\connect\data\input --preview

📁 Directory Import Mode
🔍 Recursively scanning directory: D:\connect\data\input
📊 Scan results: Total 282 files, found 282 importable files

📋 File Distribution Statistics:
  📊 EP: 45 files
  📊 CDR: 128 files
  📊 NLG: 67 files
  📊 KPI: 23 files
  📊 SCORE: 19 files

Start import? [y/N]: y

# 3. 执行全自动批量导入
🚀 Starting batch import...
⚡ Processing files with auto-parallel workers...
✅ Import completed: 282/282 files (100% success rate)

📈 Final Statistics:
  • Total files processed: 282
  • Total records imported: 1,247,893
  • Total processing time: 3m 45s
  • Average import rate: 5,547 records/second
  • Success rate: 100%
```

### 单文件快速测试
```bash
# 1. 快速测试单个文件（自动检测所有参数）
$ python -m src.cli.import_cli import data/input/ep/2024/CW01/TEF_Sites_CW01.xlsx

✅ Import completed successfully!
📈 Import Statistics:
  • Records imported: 47,927
  • Processing time: 4.52 seconds
  • Success rate: 100%

# 2. 预览文件检测结果
$ python -m src.cli.import_cli import data/input/cdr/sample.xlsx --preview

📊 File Analysis Results:
  📊 Data Type: cdr (auto-detected)
  🏢 Operator: telefonica (auto-detected)
  🗄️ Target Schema: cdr_to2
```

## 技术规格

### 智能检测能力
- **数据类型识别**: 基于目录名、文件名、内容的多层检测
- **运营商识别**: Excel工作表名称、文件名模式匹配
- **Schema映射**: 自动选择最适合的数据库Schema
- **文件格式支持**: Excel (.xlsx, .xls, .xlsb), CSV, XML等

### 性能指标
- **处理速度**: 平均 5,000-10,000 记录/秒（实测）
- **并发能力**: 自动调整并发数，最优化系统资源利用
- **内存使用**: 智能内存管理，大文件自动分批处理
- **数据库连接**: 连接池管理，自动优化连接数

### 自动化特性
- **零配置导入**: 无需手动指定任何参数
- **自适应处理**: 根据文件大小和类型自动调整策略
- **智能错误处理**: 自动重试和错误恢复机制
- **实时监控**: 动态显示进度和性能统计

### 数据完整性保障
- **事务保护**: 每个文件作为独立事务，确保原子性
- **错误隔离**: 单个文件失败不影响其他文件处理
- **数据验证**: 多层数据验证和格式检查
- **幂等操作**: 支持安全的重复导入

## 快速开始指南

### 第一次使用
```bash
# 1. 检查系统状态
python -m src.cli.import_cli info

# 2. 测试单个文件
python -m src.cli.import_cli import your_file.xlsx --preview

# 3. 执行实际导入
python -m src.cli.import_cli import your_file.xlsx

# 4. 批量导入整个目录
python -m src.cli.import_cli import your_data_directory
```

### 推荐工作流程
1. **组织文件**: 按数据类型组织到对应目录（ep/, cdr/, nlg/等）
2. **预览检查**: 使用 `--preview` 验证自动检测结果
3. **执行导入**: 运行导入命令，系统自动处理所有细节
4. **验证结果**: 检查导入统计和成功率

---

**重要提示**:
- Connect CLI采用全自动化设计，大多数情况下无需手动配置
- 系统会自动检测和处理各种电信数据格式
- 建议在生产环境使用前先在测试环境验证
- 如遇问题，首先使用 `--preview` 模式检查自动检测结果