"""磁盘空间管理工具 - 用于监控和清理磁盘空间"""
import os
import shutil
import tempfile
import gc
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import psutil
import time

logger = logging.getLogger(__name__)

@dataclass
class DiskSpaceInfo:
    """磁盘空间信息"""
    total_gb: float
    used_gb: float
    free_gb: float
    usage_percent: float
    
    @property
    def is_critical(self) -> bool:
        """是否处于临界状态 (>90%)"""
        return self.usage_percent > 90
    
    @property
    def is_warning(self) -> bool:
        """是否处于警告状态 (>80%)"""
        return self.usage_percent > 80

@dataclass
class CleanupResult:
    """清理结果"""
    cleaned_files: int
    freed_space_mb: float
    errors: List[str]
    
    def __post_init__(self):
        """确保 freed_space_mb 是浮点数类型"""
        if isinstance(self.freed_space_mb, str):
            try:
                self.freed_space_mb = float(self.freed_space_mb)
            except (ValueError, TypeError):
                logger.warning(f"无法将 freed_space_mb 转换为浮点数: {self.freed_space_mb}，设置为 0.0")
                self.freed_space_mb = 0.0
        elif not isinstance(self.freed_space_mb, (int, float)):
            logger.warning(f"freed_space_mb 类型错误: {type(self.freed_space_mb)}，设置为 0.0")
            self.freed_space_mb = 0.0
        else:
            self.freed_space_mb = float(self.freed_space_mb)
    
class DiskSpaceManager:
    """磁盘空间管理器"""
    
    def __init__(self, project_root: Optional[Path] = None):
        self.project_root = project_root or Path.cwd()
        self.temp_dirs = [
            Path(tempfile.gettempdir()),
            self.project_root / "temp",
            self.project_root / "tmp",
            self.project_root / ".cache",
            self.project_root / "__pycache__",
        ]
        
        # 可清理的文件模式
        self.cleanable_patterns = [
            "*.tmp",
            "*.temp",
            "*.log",
            "*.cache",
            "*.bak",
            "*.old",
            "*~",
            ".DS_Store",
            "Thumbs.db",
            "*.pyc",
            "*.pyo",
            "__pycache__",
        ]
        
        # 项目特定的临时文件模式
        self.project_temp_patterns = [
            "debug_*.py",
            "test_*.tmp",
            "import_*.log",
            "performance_*.json",
            "*.processing",
            "intermediate_results_*.json",
        ]
    
    def get_disk_space_info(self, path: Optional[Path] = None) -> Dict[str, float]:
        """获取磁盘空间信息"""
        if path is None:
            path = self.project_root
        
        try:
            usage = shutil.disk_usage(path)
            total_gb = float(usage.total) / (1024**3)
            free_gb = float(usage.free) / (1024**3)
            used_gb = float(usage.total - usage.free) / (1024**3)
            
            # 确保避免除零错误和类型错误
            if total_gb > 0:
                usage_percent = float((used_gb / total_gb) * 100)
            else:
                usage_percent = 100.0
            
            return {
                'total_gb': float(total_gb),
                'used_gb': float(used_gb),
                'free_gb': float(free_gb),
                'usage_percent': float(usage_percent)
            }
        except Exception as e:
            logger.error(f"获取磁盘空间信息失败: {e}")
            # 返回默认值，确保所有值都是float类型
            return {
                'total_gb': 0.0,
                'used_gb': 0.0,
                'free_gb': 0.0,
                'usage_percent': 100.0
            }
    
    def check_disk_space_critical(self) -> Tuple[bool, DiskSpaceInfo]:
        """检查磁盘空间是否临界"""
        disk_info_dict = self.get_disk_space_info()
        disk_info = DiskSpaceInfo(
            total_gb=disk_info_dict['total_gb'],
            used_gb=disk_info_dict['used_gb'],
            free_gb=disk_info_dict['free_gb'],
            usage_percent=disk_info_dict['usage_percent']
        )
        return disk_info.is_critical, disk_info
    
    def cleanup_temp_files(self) -> CleanupResult:
        """清理临时文件"""
        cleaned_files = 0
        freed_space_mb = 0.0
        errors = []
        
        logger.info("开始清理临时文件...")
        
        # 清理系统临时目录
        for temp_dir in self.temp_dirs:
            if not temp_dir.exists():
                continue
                
            try:
                result = self._cleanup_directory(temp_dir, self.cleanable_patterns)
                cleaned_files += result.cleaned_files
                freed_space_mb = float(freed_space_mb) + float(result.freed_space_mb)
                errors.extend(result.errors)
            except Exception as e:
                error_msg = f"清理目录 {temp_dir} 失败: {e}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        # 清理项目特定的临时文件
        try:
            result = self._cleanup_directory(self.project_root, self.project_temp_patterns)
            cleaned_files += result.cleaned_files
            freed_space_mb = float(freed_space_mb) + float(result.freed_space_mb)
            errors.extend(result.errors)
        except Exception as e:
            error_msg = f"清理项目临时文件失败: {e}"
            logger.error(error_msg)
            errors.append(error_msg)
        
        logger.info(f"临时文件清理完成: 清理了 {cleaned_files} 个文件，释放了 {freed_space_mb:.2f} MB")
        
        return CleanupResult(
            cleaned_files=cleaned_files,
            freed_space_mb=freed_space_mb,
            errors=errors
        )
    
    def _cleanup_directory(self, directory: Path, patterns: List[str]) -> CleanupResult:
        """清理指定目录中匹配模式的文件"""
        cleaned_files = 0
        freed_space_mb = 0.0
        errors = []
        
        try:
            for pattern in patterns:
                if pattern == "__pycache__":
                    # 特殊处理 __pycache__ 目录
                    for pycache_dir in directory.rglob("__pycache__"):
                        try:
                            size_mb = self._get_directory_size(pycache_dir) / (1024**2)
                            shutil.rmtree(pycache_dir)
                            cleaned_files += 1
                            freed_space_mb += size_mb
                            logger.debug(f"删除 __pycache__ 目录: {pycache_dir}")
                        except Exception as e:
                            error_msg = f"删除 __pycache__ 目录 {pycache_dir} 失败: {e}"
                            errors.append(error_msg)
                else:
                    # 处理文件模式
                    for file_path in directory.rglob(pattern):
                        if file_path.is_file():
                            try:
                                size_mb = file_path.stat().st_size / (1024**2)
                                file_path.unlink()
                                cleaned_files += 1
                                freed_space_mb += size_mb
                                logger.debug(f"删除临时文件: {file_path}")
                            except Exception as e:
                                error_msg = f"删除文件 {file_path} 失败: {e}"
                                errors.append(error_msg)
        except Exception as e:
            error_msg = f"清理目录 {directory} 时发生错误: {e}"
            errors.append(error_msg)
        
        return CleanupResult(
            cleaned_files=cleaned_files,
            freed_space_mb=freed_space_mb,
            errors=errors
        )
    
    def _get_directory_size(self, directory: Path) -> int:
        """获取目录大小（字节）"""
        total_size = 0
        try:
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
        except Exception:
            pass
        return total_size
    
    def cleanup_python_cache(self) -> CleanupResult:
        """清理Python缓存文件"""
        cleaned_files = 0
        freed_space_mb = 0.0
        errors = []
        
        logger.info("开始清理Python缓存...")
        
        # 清理 .pyc 和 .pyo 文件
        for ext in ["*.pyc", "*.pyo"]:
            for file_path in self.project_root.rglob(ext):
                try:
                    size_mb = file_path.stat().st_size / (1024**2)
                    file_path.unlink()
                    cleaned_files += 1
                    freed_space_mb += size_mb
                except Exception as e:
                    errors.append(f"删除 {file_path} 失败: {e}")
        
        # 清理 __pycache__ 目录
        for pycache_dir in self.project_root.rglob("__pycache__"):
            try:
                size_mb = self._get_directory_size(pycache_dir) / (1024**2)
                shutil.rmtree(pycache_dir)
                cleaned_files += 1
                freed_space_mb += size_mb
            except Exception as e:
                errors.append(f"删除 __pycache__ 目录 {pycache_dir} 失败: {e}")
        
        # 强制垃圾回收
        gc.collect()
        
        logger.info(f"Python缓存清理完成: 清理了 {cleaned_files} 个文件/目录，释放了 {freed_space_mb:.2f} MB")
        
        return CleanupResult(
            cleaned_files=cleaned_files,
            freed_space_mb=freed_space_mb,
            errors=errors
        )
    
    def cleanup_logs(self, max_age_days: int = 7) -> CleanupResult:
        """清理旧日志文件"""
        cleaned_files = 0
        freed_space_mb = 0.0
        errors = []
        
        logger.info(f"开始清理 {max_age_days} 天前的日志文件...")
        
        current_time = time.time()
        max_age_seconds = max_age_days * 24 * 3600
        
        # 查找日志文件
        log_patterns = ["*.log", "*.log.*", "*.out"]
        
        for pattern in log_patterns:
            for log_file in self.project_root.rglob(pattern):
                try:
                    if log_file.is_file():
                        file_age = current_time - log_file.stat().st_mtime
                        if file_age > max_age_seconds:
                            size_mb = log_file.stat().st_size / (1024**2)
                            log_file.unlink()
                            cleaned_files += 1
                            freed_space_mb += size_mb
                            logger.debug(f"删除旧日志文件: {log_file}")
                except Exception as e:
                    errors.append(f"删除日志文件 {log_file} 失败: {e}")
        
        logger.info(f"日志清理完成: 清理了 {cleaned_files} 个文件，释放了 {freed_space_mb:.2f} MB")
        
        return CleanupResult(
            cleaned_files=cleaned_files,
            freed_space_mb=freed_space_mb,
            errors=errors
        )
    
    def emergency_cleanup(self) -> CleanupResult:
        """紧急清理 - 当磁盘空间严重不足时使用"""
        logger.warning("执行紧急磁盘空间清理...")
        
        total_result = CleanupResult(0, 0.0, [])
        
        # 1. 清理临时文件
        result = self.cleanup_temp_files()
        total_result.cleaned_files += result.cleaned_files
        # 确保类型安全的累加
        total_result.freed_space_mb = float(total_result.freed_space_mb) + float(result.freed_space_mb)
        total_result.errors.extend(result.errors)
        
        # 2. 清理Python缓存
        result = self.cleanup_python_cache()
        total_result.cleaned_files += result.cleaned_files
        # 确保类型安全的累加
        total_result.freed_space_mb = float(total_result.freed_space_mb) + float(result.freed_space_mb)
        total_result.errors.extend(result.errors)
        
        # 3. 清理旧日志文件（1天前的）
        result = self.cleanup_logs(max_age_days=1)
        total_result.cleaned_files += result.cleaned_files
        # 确保类型安全的累加
        total_result.freed_space_mb = float(total_result.freed_space_mb) + float(result.freed_space_mb)
        total_result.errors.extend(result.errors)
        
        # 4. 强制内存清理
        gc.collect()
        
        logger.warning(f"紧急清理完成: 总共清理了 {total_result.cleaned_files} 个文件，释放了 {total_result.freed_space_mb:.2f} MB")
        
        return total_result
    
    def monitor_disk_space(self) -> Dict[str, any]:
        """监控磁盘空间状态"""
        
        disk_info_dict = self.get_disk_space_info()
        disk_info = DiskSpaceInfo(
            total_gb=disk_info_dict['total_gb'],
            used_gb=disk_info_dict['used_gb'],
            free_gb=disk_info_dict['free_gb'],
            usage_percent=disk_info_dict['usage_percent']
        )
        
        status = {
            "disk_info": disk_info_dict,
            "status": "normal",
            "recommendations": []
        }
        
        if disk_info.is_critical:
            status["status"] = "critical"
            status["recommendations"] = [
                "立即执行紧急清理",
                "停止非必要的导入操作",
                "检查并删除大文件",
                "考虑扩展磁盘空间"
            ]
        elif disk_info.is_warning:
            status["status"] = "warning"
            status["recommendations"] = [
                "执行常规清理",
                "监控磁盘使用情况",
                "清理临时文件和缓存"
            ]
        
        return status
    
    def get_large_files(self, min_size_mb: float = 100, limit: int = 10) -> List[Tuple[Path, float]]:
        """查找大文件"""
        large_files = []
        
        try:
            for file_path in self.project_root.rglob("*"):
                if file_path.is_file():
                    try:
                        size_mb = file_path.stat().st_size / (1024**2)
                        if size_mb >= min_size_mb:
                            large_files.append((file_path, size_mb))
                    except Exception:
                        continue
            
            # 按大小排序，返回最大的文件
            large_files.sort(key=lambda x: x[1], reverse=True)
            return large_files[:limit]
            
        except Exception as e:
            logger.error(f"查找大文件时发生错误: {e}")
            return []

# 全局实例
_disk_manager = None

def get_disk_manager(project_root: Optional[Path] = None) -> DiskSpaceManager:
    """获取磁盘空间管理器实例"""
    global _disk_manager
    if _disk_manager is None:
        _disk_manager = DiskSpaceManager(project_root)
    return _disk_manager

def check_and_cleanup_if_needed(project_root: Optional[Path] = None) -> bool:
    """检查磁盘空间，如果需要则自动清理"""
    manager = get_disk_manager(project_root)
    
    is_critical, disk_info = manager.check_disk_space_critical()
    
    if is_critical:
        logger.warning(f"磁盘空间不足 ({disk_info.usage_percent:.1f}%)，执行紧急清理...")
        result = manager.emergency_cleanup()
        
        # 再次检查
        _, new_disk_info = manager.check_disk_space_critical()
        logger.info(f"清理后磁盘使用率: {new_disk_info.usage_percent:.1f}%")
        
        return new_disk_info.free_gb > 1.0  # 至少需要1GB空闲空间
    
    return True