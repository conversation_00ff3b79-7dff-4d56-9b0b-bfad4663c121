#!/usr/bin/env python3
"""
调试EP数据导入到错误schema的问题
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import pandas as pd
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ep_schema_issue():
    """测试EP schema问题的完整调用链"""
    
    print("=== 测试EP Schema问题 ===")
    
    # 1. 测试EPImporter初始化
    try:
        from importers.ep_importer import EPImporter
        
        print("\n1. 初始化EPImporter...")
        ep_importer = EPImporter()
        
        print(f"   - data_source_config: {ep_importer.data_source_config}")
        print(f"   - schema_name: {ep_importer.schema_name}")
        print(f"   - get_schema_name(): {ep_importer.get_schema_name()}")
        
        # 2. 创建测试数据
        print("\n2. 创建测试DataFrame...")
        test_data = {
            'WGS84_LATITUDE': [40.7128, 34.0522],
            'WGS84_LONGITUDE': [-74.0060, -118.2437],
            'CELL_NAME': ['TEST_CELL_1', 'TEST_CELL_2']
        }
        df = pd.DataFrame(test_data)
        print(f"   - DataFrame shape: {df.shape}")
        print(f"   - DataFrame columns: {list(df.columns)}")
        
        # 3. 测试bulk_operations初始化
        print("\n3. 测试BulkOperations...")
        if hasattr(ep_importer, 'bulk_operations') and ep_importer.bulk_operations:
            bulk_ops = ep_importer.bulk_operations
            print(f"   - BulkOperations存在: {bulk_ops}")
        else:
            print("   - 需要手动初始化BulkOperations")
            try:
                from database.operations.bulk_operations import BulkOperations
                bulk_ops = BulkOperations(None)  # 无session初始化
                print(f"   - 手动创建BulkOperations: {bulk_ops}")
            except Exception as e:
                print(f"   - 创建BulkOperations失败: {e}")
                return
        
        # 4. 测试schema参数传递
        print("\n4. 测试schema参数传递...")
        schema_value = ep_importer.get_schema_name()
        print(f"   - get_schema_name()返回: '{schema_value}' (type: {type(schema_value)})")
        print(f"   - schema_value is None: {schema_value is None}")
        print(f"   - bool(schema_value): {bool(schema_value)}")
        
        # 5. 模拟bulk_insert_dataframe调用
        print("\n5. 模拟bulk_insert_dataframe调用...")
        table_name = "test_ep_table"
        
        # 检查_simplified_bulk_insert中的schema处理逻辑
        print(f"   - 传入schema参数: '{schema_value}'")
        
        # 模拟_simplified_bulk_insert中的逻辑
        if schema_value is None:
            final_schema = 'public'
            print(f"   - schema为None，设置为: '{final_schema}'")
        else:
            final_schema = schema_value
            print(f"   - schema不为None，使用: '{final_schema}'")
        
        print(f"   - 最终使用的schema: '{final_schema}'")
        
        # 6. 检查可能的问题点
        print("\n6. 检查可能的问题点...")
        
        # 检查data_source_config是否为空字典
        if not ep_importer.data_source_config:
            print("   - 警告: data_source_config为空！")
        
        # 检查schema_name是否被意外修改
        if hasattr(ep_importer, '_original_schema_name'):
            print(f"   - 原始schema_name: {ep_importer._original_schema_name}")
        
        # 检查是否有其他地方修改了schema_name
        print(f"   - 当前schema_name属性: {getattr(ep_importer, 'schema_name', 'NOT_SET')}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def test_bulk_operations_schema_handling():
    """专门测试BulkOperations中schema参数的处理"""
    
    print("\n=== 测试BulkOperations Schema处理 ===")
    
    try:
        from database.operations.bulk_operations import BulkOperations
        
        # 创建BulkOperations实例
        bulk_ops = BulkOperations(None)
        
        # 测试不同的schema值
        test_cases = [
            ('ep_to2', 'ep_to2'),
            (None, 'public'),
            ('', 'public'),  # 空字符串
            ('custom_schema', 'custom_schema')
        ]
        
        for input_schema, expected_schema in test_cases:
            print(f"\n测试schema输入: '{input_schema}' (type: {type(input_schema)})")
            
            # 模拟_simplified_bulk_insert中的逻辑
            if input_schema is None:
                result_schema = 'public'
            else:
                result_schema = input_schema
            
            print(f"   - 预期结果: '{expected_schema}'")
            print(f"   - 实际结果: '{result_schema}'")
            print(f"   - 匹配: {result_schema == expected_schema}")
            
            if result_schema != expected_schema:
                print(f"   - 警告: 结果不匹配！")
    
    except Exception as e:
        print(f"BulkOperations测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_ep_schema_issue()
    test_bulk_operations_schema_handling()