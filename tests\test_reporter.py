#!/usr/bin/env python3
"""
Test reporter for the Connect database framework.

This module provides functionality to generate comprehensive test reports
including coverage, performance metrics, and test results analysis.
"""

import json
import os
import subprocess
import sys
import xml.etree.ElementTree as ET
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional


@dataclass
class _TestResult:
    """Represents a single test result."""

    name: str
    status: str  # passed, failed, skipped, error
    duration: float
    message: Optional[str] = None
    traceback: Optional[str] = None
    category: Optional[str] = None


@dataclass
class _TestSuite:
    """Represents a test suite."""

    name: str
    tests: List[_TestResult]
    duration: float
    setup_duration: float = 0.0
    teardown_duration: float = 0.0


@dataclass
class _TestReport:
    """Represents a complete test report."""

    timestamp: datetime
    total_tests: int
    passed: int
    failed: int
    skipped: int
    errors: int
    total_duration: float
    coverage_percentage: Optional[float] = None
    suites: List[_TestSuite] = None
    environment: Dict[str, str] = None
    performance_metrics: Dict[str, Any] = None


class _TestReporter:
    """Generate comprehensive test reports."""

    def __init__(self, output_dir: str = "test_reports"):
        """Initialize the test reporter.

        Args:
            output_dir: Directory to save test reports
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

    def parse_junit_xml(self, xml_file: Path) -> _TestReport:
        """Parse JUnit XML file and extract test results.

        Args:
            xml_file: Path to JUnit XML file

        Returns:
            _TestReport object with parsed data
        """
        tree = ET.parse(xml_file)
        root = tree.getroot()

        # Parse test suites
        suites = []
        total_tests = 0
        passed = 0
        failed = 0
        skipped = 0
        errors = 0
        total_duration = 0.0

        for testsuite in root.findall(".//testsuite"):
            suite_name = testsuite.get("name", "Unknown")
            suite_duration = float(testsuite.get("time", 0))
            total_duration += suite_duration

            tests = []
            for testcase in testsuite.findall("testcase"):
                test_name = testcase.get("name")
                test_duration = float(testcase.get("time", 0))

                # Determine test status
                if testcase.find("failure") is not None:
                    status = "failed"
                    failed += 1
                    failure_elem = testcase.find("failure")
                    message = failure_elem.get("message", "")
                    traceback = failure_elem.text
                elif testcase.find("error") is not None:
                    status = "error"
                    errors += 1
                    error_elem = testcase.find("error")
                    message = error_elem.get("message", "")
                    traceback = error_elem.text
                elif testcase.find("skipped") is not None:
                    status = "skipped"
                    skipped += 1
                    skipped_elem = testcase.find("skipped")
                    message = skipped_elem.get("message", "")
                    traceback = None
                else:
                    status = "passed"
                    passed += 1
                    message = None
                    traceback = None

                test_result = TestResult(
                    name=test_name,
                    status=status,
                    duration=test_duration,
                    message=message,
                    traceback=traceback,
                    category=self._extract_category(test_name),
                )
                tests.append(test_result)
                total_tests += 1

            suite = _TestSuite(name=suite_name, tests=tests, duration=suite_duration)
            suites.append(suite)

        return _TestReport(
            timestamp=datetime.now(),
            total_tests=total_tests,
            passed=passed,
            failed=failed,
            skipped=skipped,
            errors=errors,
            total_duration=total_duration,
            suites=suites,
            environment=self._get_environment_info(),
        )

    def parse_coverage_xml(self, xml_file: Path) -> float:
        """Parse coverage XML file and extract coverage percentage.

        Args:
            xml_file: Path to coverage XML file

        Returns:
            Coverage percentage
        """
        try:
            tree = ET.parse(xml_file)
            root = tree.getroot()

            # Find coverage element
            coverage_elem = root.find(".//coverage")
            if coverage_elem is not None:
                line_rate = float(coverage_elem.get("line-rate", 0))
                return line_rate * 100

            return 0.0
        except Exception:
            return 0.0

    def generate_html_report(
        self, report: _TestReport, output_file: str = "test_report.html"
    ) -> Path:
        """Generate HTML test report.

        Args:
            report: _TestReport object
            output_file: Output HTML file name

        Returns:
            Path to generated HTML file
        """
        html_content = self._generate_html_content(report)

        output_path = self.output_dir / output_file
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(html_content)

        return output_path

    def generate_json_report(
        self, report: _TestReport, output_file: str = "test_report.json"
    ) -> Path:
        """Generate JSON test report.

        Args:
            report: _TestReport object
            output_file: Output JSON file name

        Returns:
            Path to generated JSON file
        """
        # Convert dataclasses to dictionaries
        report_dict = asdict(report)

        # Convert datetime to string
        report_dict["timestamp"] = report.timestamp.isoformat()

        output_path = self.output_dir / output_file
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(report_dict, f, indent=2, ensure_ascii=False)

        return output_path

    def generate_markdown_report(
        self, report: _TestReport, output_file: str = "test_report.md"
    ) -> Path:
        """Generate Markdown test report.

        Args:
            report: _TestReport object
            output_file: Output Markdown file name

        Returns:
            Path to generated Markdown file
        """
        markdown_content = self._generate_markdown_content(report)

        output_path = self.output_dir / output_file
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(markdown_content)

        return output_path

    def generate_performance_report(
        self,
        performance_data: Dict[str, Any],
        output_file: str = "performance_report.html",
    ) -> Path:
        """Generate performance test report.

        Args:
            performance_data: Performance metrics data
            output_file: Output HTML file name

        Returns:
            Path to generated HTML file
        """
        html_content = self._generate_performance_html(performance_data)

        output_path = self.output_dir / output_file
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(html_content)

        return output_path

    def _extract_category(self, test_name: str) -> str:
        """Extract test category from test name."""
        if "unit" in test_name.lower():
            return "unit"
        elif "integration" in test_name.lower():
            return "integration"
        elif "e2e" in test_name.lower():
            return "e2e"
        elif "performance" in test_name.lower():
            return "performance"
        else:
            return "other"

    def _get_environment_info(self) -> Dict[str, str]:
        """Get environment information."""
        return {
            "python_version": sys.version,
            "platform": sys.platform,
            "working_directory": os.getcwd(),
            "timestamp": datetime.now().isoformat(),
        }

    def _generate_html_content(self, report: _TestReport) -> str:
        """Generate HTML content for test report."""
        success_rate = (
            (report.passed / report.total_tests * 100) if report.total_tests > 0 else 0
        )

        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Report - Connect Database Framework</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
        }}
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background-color: #f8f9fa;
        }}
        .metric {{
            text-align: center;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .metric-label {{
            color: #666;
            font-size: 0.9em;
        }}
        .passed {{ color: #28a745; }}
        .failed {{ color: #dc3545; }}
        .skipped {{ color: #ffc107; }}
        .errors {{ color: #fd7e14; }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }}
        .test-suite {{
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }}
        .suite-header {{
            background-color: #f8f9fa;
            padding: 15px 20px;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
        }}
        .test-list {{
            padding: 0;
        }}
        .test-item {{
            padding: 10px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .test-item:last-child {{
            border-bottom: none;
        }}
        .test-status {{
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }}
        .status-passed {{
            background-color: #d4edda;
            color: #155724;
        }}
        .status-failed {{
            background-color: #f8d7da;
            color: #721c24;
        }}
        .status-skipped {{
            background-color: #fff3cd;
            color: #856404;
        }}
        .status-error {{
            background-color: #f5c6cb;
            color: #721c24;
        }}
        .progress-bar {{
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }}
        .progress-fill {{
            height: 100%;
            background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
            transition: width 0.3s ease;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Test Report</h1>
            <p>Connect Database Framework - {report.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>

        <div class="summary">
            <div class="metric">
                <div class="metric-value">{report.total_tests}</div>
                <div class="metric-label">Total Tests</div>
            </div>
            <div class="metric">
                <div class="metric-value passed">{report.passed}</div>
                <div class="metric-label">Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value failed">{report.failed}</div>
                <div class="metric-label">Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value skipped">{report.skipped}</div>
                <div class="metric-label">Skipped</div>
            </div>
            <div class="metric">
                <div class="metric-value errors">{report.errors}</div>
                <div class="metric-label">Errors</div>
            </div>
            <div class="metric">
                <div class="metric-value">{success_rate:.1f}%</div>
                <div class="metric-label">Success Rate</div>
            </div>
            <div class="metric">
                <div class="metric-value">{report.total_duration:.2f}s</div>
                <div class="metric-label">Total Duration</div>
            </div>
            {f'<div class="metric"><div class="metric-value">{report.coverage_percentage:.1f}%</div><div class="metric-label">Coverage</div></div>' if report.coverage_percentage else ''}
        </div>

        <div class="content">
            <div class="progress-bar">
                <div class="progress-fill" style="width: {success_rate}%"></div>
            </div>

            <div class="section">
                <h2>Test Suites</h2>
                {self._generate_suites_html(report.suites) if report.suites else '<p>No test suites found.</p>'}
            </div>

            {self._generate_environment_html(report.environment) if report.environment else ''}
        </div>
    </div>
</body>
</html>
        """

        return html

    def _generate_suites_html(self, suites: List[_TestSuite]) -> str:
        """Generate HTML for test suites."""
        html = ""

        for suite in suites:
            html += f"""
            <div class="test-suite">
                <div class="suite-header">
                    {suite.name} ({len(suite.tests)} tests, {suite.duration:.2f}s)
                </div>
                <div class="test-list">
            """

            for test in suite.tests:
                status_class = f"status-{test.status}"
                html += f"""
                <div class="test-item">
                    <span>{test.name}</span>
                    <div>
                        <span class="test-status {status_class}">{test.status}</span>
                        <span style="margin-left: 10px; color: #666;">{test.duration:.3f}s</span>
                    </div>
                </div>
                """

            html += "</div></div>"

        return html

    def _generate_environment_html(self, environment: Dict[str, str]) -> str:
        """Generate HTML for environment information."""
        html = """
        <div class="section">
            <h2>Environment</h2>
            <table style="width: 100%; border-collapse: collapse;">
        """

        for key, value in environment.items():
            html += f"""
            <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px; font-weight: bold; width: 200px;">{key}</td>
                <td style="padding: 10px;">{value}</td>
            </tr>
            """

        html += "</table></div>"
        return html

    def _generate_markdown_content(self, report: _TestReport) -> str:
        """Generate Markdown content for test report."""
        success_rate = (
            (report.passed / report.total_tests * 100) if report.total_tests > 0 else 0
        )

        markdown = f"""
# Test Report - Connect Database Framework

**Generated:** {report.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

## Summary

| Metric | Value |
|--------|-------|
| Total Tests | {report.total_tests} |
| Passed | {report.passed} |
| Failed | {report.failed} |
| Skipped | {report.skipped} |
| Errors | {report.errors} |
| Success Rate | {success_rate:.1f}% |
| Total Duration | {report.total_duration:.2f}s |
{f'| Coverage | {report.coverage_percentage:.1f}% |' if report.coverage_percentage else ''}

## Test Results

        """

        if report.suites:
            for suite in report.suites:
                markdown += f"""
### {suite.name}

**Duration:** {suite.duration:.2f}s
**Tests:** {len(suite.tests)}

| Test | Status | Duration |
|------|--------|----------|
                """

                for test in suite.tests:
                    status_emoji = {
                        "passed": "✅",
                        "failed": "❌",
                        "skipped": "⏭️",
                        "error": "💥",
                    }.get(test.status, "❓")

                    markdown += f"| {test.name} | {status_emoji} {test.status} | {test.duration:.3f}s |\n"

                markdown += "\n"

        if report.environment:
            markdown += "## Environment\n\n"
            for key, value in report.environment.items():
                markdown += f"- **{key}:** {value}\n"

        return markdown

    def _generate_performance_html(self, performance_data: Dict[str, Any]) -> str:
        """Generate HTML for performance report."""
        # This is a simplified version - you can expand this based on your performance data structure
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Report - Connect Database Framework</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .metric {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .metric h3 {{ margin-top: 0; }}
    </style>
</head>
<body>
    <h1>Performance Report</h1>
    <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

        """

        for metric_name, metric_value in performance_data.items():
            html += f"""
    <div class="metric">
        <h3>{metric_name}</h3>
        <p>{metric_value}</p>
    </div>
            """

        html += "</body></html>"
        return html


def main():
    """Main function for command-line usage."""
    import argparse

    parser = argparse.ArgumentParser(description="Generate test reports")
    parser.add_argument("--junit", help="Path to JUnit XML file")
    parser.add_argument("--coverage", help="Path to coverage XML file")
    parser.add_argument("--output", default="test_reports", help="Output directory")
    parser.add_argument(
        "--format",
        choices=["html", "json", "markdown", "all"],
        default="html",
        help="Report format",
    )

    args = parser.parse_args()

    reporter = _TestReporter(args.output)

    if args.junit:
        junit_path = Path(args.junit)
        if not junit_path.exists():
            print(f"Error: JUnit file {junit_path} not found")
            return 1

        report = reporter.parse_junit_xml(junit_path)

        if args.coverage:
            coverage_path = Path(args.coverage)
            if coverage_path.exists():
                report.coverage_percentage = reporter.parse_coverage_xml(coverage_path)

        if args.format == "html" or args.format == "all":
            html_file = reporter.generate_html_report(report)
            print(f"HTML report generated: {html_file}")

        if args.format == "json" or args.format == "all":
            json_file = reporter.generate_json_report(report)
            print(f"JSON report generated: {json_file}")

        if args.format == "markdown" or args.format == "all":
            md_file = reporter.generate_markdown_report(report)
            print(f"Markdown report generated: {md_file}")

    else:
        print("Error: JUnit XML file is required")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
