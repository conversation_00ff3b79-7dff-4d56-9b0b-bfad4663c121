# -*- coding: utf-8 -*-
"""
Connect Telecom Data Analysis Platform - Pipeline Integration Tests

This module contains integration tests for data processing pipelines.

Author: <PERSON><PERSON> <<EMAIL>>
Creation Date: 2024-01-20
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, Mock, patch

import pytest
import pytest_asyncio

from src.config.models import ConnectConfig as Config, DatabaseConfig
from src.database.connection import SessionManager, DatabasePoolManager
from src.database.exceptions import <PERSON><PERSON>ine<PERSON>rror, ETLError, ValidationError
from src.database.etl.pipeline import ETLPipeline
from src.database.operations import CRUDOperations, BulkOperations
from src.database.schema import SchemaManager


class TestDataPipelineIntegration:
    """Test data pipeline integration scenarios."""

    @pytest.fixture
    def mock_config(self):
        """Create mock configuration for pipeline testing."""
        config = Mock(spec=Config)
        config.database = Mock(spec=DatabaseConfig)
        config.database.host = "localhost"
        config.database.port = 5432
        config.database.name = "test_pipeline_db"
        config.database.user = "test_user"
        config.database.password = "test_pass"
        
        config.pipeline = Mock()
        config.pipeline.batch_size = 1000
        config.pipeline.parallel_workers = 4
        config.pipeline.timeout = 300
        config.pipeline.retry_attempts = 3
        config.pipeline.error_handling = "continue"
        
        config.etl = Mock()
        config.etl.validation_enabled = True
        config.etl.transformation_enabled = True
        config.etl.monitoring_enabled = True
        
        return config

    @pytest.fixture
    def mock_session_manager(self, mock_config):
        """Create mock session manager."""
        session_manager = Mock(spec=SessionManager)
        session_manager.config = mock_config
        session_manager.is_connected = True
        return session_manager

    @pytest.fixture
    def mock_pool_manager(self, mock_config):
        """Create mock pool manager."""
        pool_manager = Mock(spec=DatabasePoolManager)
        pool_manager.config = mock_config
        pool_manager.is_initialized = True
        return pool_manager

    @pytest.fixture
    def sample_ep_data(self):
        """Create sample EP (Engineering Parameters) data."""
        return pd.DataFrame({
            'cell_id': ['CELL001', 'CELL002', 'CELL003', 'CELL004', 'CELL005'],
            'cell_name': ['Berlin_LTE_001', 'Munich_5G_002', 'Hamburg_UMTS_003', 'Cologne_LTE_004', 'Frankfurt_5G_005'],
            'technology': ['LTE', '5G', 'UMTS', 'LTE', '5G'],
            'vendor': ['Ericsson', 'Nokia', 'Huawei', 'Ericsson', 'Nokia'],
            'latitude': [52.5200, 48.1351, 53.5511, 50.9375, 50.1109],
            'longitude': [13.4050, 11.5820, 9.9937, 6.9603, 8.6821],
            'azimuth': [45, 120, 270, 180, 90],
            'tilt': [3, 5, 2, 4, 6],
            'power': [43, 46, 40, 44, 47],
            'frequency': [1800, 3500, 2100, 1800, 3500],
            'timestamp': pd.date_range('2024-01-01', periods=5, freq='H')
        })

    @pytest.fixture
    def sample_cdr_data(self):
        """Create sample CDR (Call Detail Record) data."""
        return pd.DataFrame({
            'call_id': ['CDR001', 'CDR002', 'CDR003', 'CDR004', 'CDR005'],
            'calling_number': ['+491234567890', '+491234567891', '+491234567892', '+491234567893', '+491234567894'],
            'called_number': ['+491234567895', '+491234567896', '+491234567897', '+491234567898', '+491234567899'],
            'start_time': pd.to_datetime([
                '2024-01-01 10:00:00', '2024-01-01 11:00:00', '2024-01-01 12:00:00',
                '2024-01-01 13:00:00', '2024-01-01 14:00:00'
            ]),
            'end_time': pd.to_datetime([
                '2024-01-01 10:05:00', '2024-01-01 11:03:00', '2024-01-01 12:02:00',
                '2024-01-01 13:04:00', '2024-01-01 14:01:00'
            ]),
            'duration': [300, 180, 120, 240, 60],
            'cell_id': ['CELL001', 'CELL002', 'CELL003', 'CELL004', 'CELL005'],
            'service_type': ['voice', 'data', 'sms', 'voice', 'data'],
            'bytes_uploaded': [0, 1024000, 0, 0, 2048000],
            'bytes_downloaded': [0, 5120000, 0, 0, 10240000]
        })

    @pytest.mark.asyncio
    async def test_ep_data_pipeline_integration(self, mock_config, mock_pool_manager, sample_ep_data):
        """Test EP data processing pipeline integration."""
        # Create pipeline configuration
        pipeline_config = {
            'name': 'ep_data_pipeline',
            'source': 'ep_files',
            'destination': 'ep_processed',
            'stages': [
                {
                    'name': 'extract',
                    'type': 'extract',
                    'config': {'file_pattern': '*.xlsx', 'sheet_name': 'EP_Data'}
                },
                {
                    'name': 'validate',
                    'type': 'validate',
                    'rules': [
                        {'column': 'cell_id', 'rule': 'not_null'},
                        {'column': 'latitude', 'rule': 'range', 'min': -90, 'max': 90},
                        {'column': 'longitude', 'rule': 'range', 'min': -180, 'max': 180},
                        {'column': 'frequency', 'rule': 'range', 'min': 700, 'max': 6000}
                    ]
                },
                {
                    'name': 'transform',
                    'type': 'transform',
                    'transformations': [
                        {'type': 'standardize_cell_names'},
                        {'type': 'calculate_coverage_area'},
                        {'type': 'add_geospatial_index'}
                    ]
                },
                {
                    'name': 'load',
                    'type': 'load',
                    'config': {'table': 'ep_data', 'schema': 'telecom', 'batch_size': 1000}
                }
            ]
        }
        
        # Mock pipeline components
        with patch('src.database.etl.pipeline.ETLPipeline') as mock_pipeline_class:
            mock_pipeline = Mock()
            mock_pipeline_class.return_value = mock_pipeline
            
            # Mock successful pipeline execution
            mock_pipeline.run.return_value = {
                'status': 'success',
                'stages_completed': 4,
                'records_processed': 5,
                'execution_time': 45.2,
                'errors': []
            }
            
            # Execute pipeline
            pipeline = ETLPipeline(mock_config)
            result = await pipeline.run(pipeline_config)
            
            # Verify results
            assert result['status'] == 'success'
            assert result['stages_completed'] == 4
            assert result['records_processed'] == 5
            assert result['execution_time'] > 0
            assert len(result['errors']) == 0

    @pytest.mark.asyncio
    async def test_cdr_data_pipeline_integration(self, mock_config, mock_pool_manager, sample_cdr_data):
        """Test CDR data processing pipeline integration."""
        # Create pipeline configuration
        pipeline_config = {
            'name': 'cdr_data_pipeline',
            'source': 'cdr_files',
            'destination': 'cdr_processed',
            'stages': [
                {
                    'name': 'extract',
                    'type': 'extract',
                    'config': {'file_pattern': '*.csv', 'delimiter': ','}
                },
                {
                    'name': 'validate',
                    'type': 'validate',
                    'rules': [
                        {'column': 'call_id', 'rule': 'not_null'},
                        {'column': 'calling_number', 'rule': 'format', 'pattern': r'^\+\d{10,15}$'},
                        {'column': 'duration', 'rule': 'range', 'min': 0, 'max': 86400},
                        {'column': 'service_type', 'rule': 'enum', 'values': ['voice', 'data', 'sms']}
                    ]
                },
                {
                    'name': 'transform',
                    'type': 'transform',
                    'transformations': [
                        {'type': 'calculate_call_cost'},
                        {'type': 'anonymize_phone_numbers'},
                        {'type': 'add_time_buckets'}
                    ]
                },
                {
                    'name': 'aggregate',
                    'type': 'aggregate',
                    'config': {
                        'group_by': ['cell_id', 'service_type'],
                        'metrics': ['total_calls', 'total_duration', 'avg_duration']
                    }
                },
                {
                    'name': 'load',
                    'type': 'load',
                    'config': {'table': 'cdr_data', 'schema': 'telecom', 'batch_size': 5000}
                }
            ]
        }
        
        # Mock pipeline components
        with patch('src.database.etl.pipeline.ETLPipeline') as mock_pipeline_class:
            mock_pipeline = Mock()
            mock_pipeline_class.return_value = mock_pipeline
            
            # Mock successful pipeline execution
            mock_pipeline.run.return_value = {
                'status': 'success',
                'stages_completed': 5,
                'records_processed': 5,
                'execution_time': 62.8,
                'errors': []
            }
            
            # Execute pipeline
            pipeline = ETLPipeline(mock_config)
            result = await pipeline.run(pipeline_config)
            
            # Verify results
            assert result['status'] == 'success'
            assert result['stages_completed'] == 5
            assert result['records_processed'] == 5

    @pytest.mark.asyncio
    async def test_multi_source_pipeline_integration(self, mock_config, mock_pool_manager, sample_ep_data, sample_cdr_data):
        """Test multi-source data pipeline integration."""
        # Create multi-source pipeline configuration
        pipeline_config = {
            'name': 'multi_source_pipeline',
            'sources': {
                'ep_data': {'type': 'file', 'path': '/data/ep/', 'format': 'xlsx'},
                'cdr_data': {'type': 'file', 'path': '/data/cdr/', 'format': 'csv'}
            },
            'destination': 'integrated_telecom_data',
            'stages': [
                {
                    'name': 'extract_all',
                    'type': 'parallel_extract',
                    'sources': ['ep_data', 'cdr_data']
                },
                {
                    'name': 'validate_all',
                    'type': 'parallel_validate',
                    'validation_configs': {
                        'ep_data': {'rules': [{'column': 'cell_id', 'rule': 'not_null'}]},
                        'cdr_data': {'rules': [{'column': 'call_id', 'rule': 'not_null'}]}
                    }
                },
                {
                    'name': 'join_data',
                    'type': 'join',
                    'config': {
                        'left': 'cdr_data',
                        'right': 'ep_data',
                        'on': 'cell_id',
                        'how': 'inner'
                    }
                },
                {
                    'name': 'enrich',
                    'type': 'transform',
                    'transformations': [
                        {'type': 'calculate_network_quality'},
                        {'type': 'add_location_info'},
                        {'type': 'calculate_usage_metrics'}
                    ]
                },
                {
                    'name': 'load_integrated',
                    'type': 'load',
                    'config': {'table': 'integrated_data', 'schema': 'analytics'}
                }
            ]
        }
        
        # Mock pipeline execution
        with patch('src.database.etl.pipeline.ETLPipeline') as mock_pipeline_class:
            mock_pipeline = Mock()
            mock_pipeline_class.return_value = mock_pipeline
            
            # Mock successful multi-source pipeline execution
            mock_pipeline.run.return_value = {
                'status': 'success',
                'stages_completed': 5,
                'sources_processed': 2,
                'records_processed': 5,  # After join
                'execution_time': 120.5,
                'errors': []
            }
            
            # Execute pipeline
            pipeline = ETLPipeline(mock_config)
            result = await pipeline.run(pipeline_config)
            
            # Verify results
            assert result['status'] == 'success'
            assert result['stages_completed'] == 5
            assert result['sources_processed'] == 2

    @pytest.mark.asyncio
    async def test_pipeline_error_handling_integration(self, mock_config, mock_pool_manager):
        """Test pipeline error handling integration."""
        # Create pipeline configuration with potential failure points
        pipeline_config = {
            'name': 'error_prone_pipeline',
            'source': 'unreliable_source',
            'destination': 'test_table',
            'error_handling': 'continue',
            'stages': [
                {
                    'name': 'extract',
                    'type': 'extract',
                    'config': {'source': 'missing_file.csv'}
                },
                {
                    'name': 'validate',
                    'type': 'validate',
                    'rules': [{'column': 'required_field', 'rule': 'not_null'}]
                },
                {
                    'name': 'transform',
                    'type': 'transform',
                    'transformations': [{'type': 'invalid_transformation'}]
                },
                {
                    'name': 'load',
                    'type': 'load',
                    'config': {'table': 'non_existent_table'}
                }
            ]
        }
        
        # Mock pipeline with errors
        with patch('src.database.etl.pipeline.ETLPipeline') as mock_pipeline_class:
            mock_pipeline = Mock()
            mock_pipeline_class.return_value = mock_pipeline
            
            # Mock pipeline execution with errors
            mock_pipeline.run.return_value = {
                'status': 'partial_success',
                'stages_completed': 2,
                'stages_failed': 2,
                'records_processed': 0,
                'execution_time': 30.1,
                'errors': [
                    {'stage': 'extract', 'error': 'File not found: missing_file.csv'},
                    {'stage': 'transform', 'error': 'Invalid transformation type'}
                ]
            }
            
            # Execute pipeline
            pipeline = ETLPipeline(mock_config)
            result = await pipeline.run(pipeline_config)
            
            # Verify error handling
            assert result['status'] == 'partial_success'
            assert result['stages_failed'] == 2
            assert len(result['errors']) == 2

    @pytest.mark.asyncio
    async def test_pipeline_performance_monitoring(self, mock_config, mock_pool_manager):
        """Test pipeline performance monitoring integration."""
        # Create performance-monitored pipeline
        pipeline_config = {
            'name': 'monitored_pipeline',
            'source': 'large_dataset',
            'destination': 'processed_data',
            'monitoring': {
                'enabled': True,
                'metrics': ['execution_time', 'memory_usage', 'throughput'],
                'alerts': {
                    'max_execution_time': 300,
                    'max_memory_usage': 1024,
                    'min_throughput': 1000
                }
            },
            'stages': [
                {
                    'name': 'extract',
                    'type': 'extract',
                    'config': {'batch_size': 10000}
                },
                {
                    'name': 'process',
                    'type': 'transform',
                    'transformations': [{'type': 'heavy_computation'}]
                },
                {
                    'name': 'load',
                    'type': 'load',
                    'config': {'batch_size': 5000}
                }
            ]
        }
        
        # Mock pipeline with monitoring
        with patch('src.database.etl.pipeline.ETLPipeline') as mock_pipeline_class:
            mock_pipeline = Mock()
            mock_pipeline_class.return_value = mock_pipeline
            
            # Mock pipeline execution with monitoring data
            mock_pipeline.run.return_value = {
                'status': 'success',
                'stages_completed': 3,
                'records_processed': 50000,
                'execution_time': 180.5,
                'monitoring': {
                    'peak_memory_usage': 512,
                    'average_throughput': 1500,
                    'stage_timings': {
                        'extract': 45.2,
                        'process': 120.8,
                        'load': 14.5
                    }
                },
                'alerts': []
            }
            
            # Execute pipeline
            pipeline = ETLPipeline(mock_config)
            result = await pipeline.run(pipeline_config)
            
            # Verify monitoring data
            assert result['status'] == 'success'
            assert 'monitoring' in result
            assert result['monitoring']['peak_memory_usage'] < 1024
            assert result['monitoring']['average_throughput'] > 1000
            assert len(result['alerts']) == 0

    @pytest.mark.asyncio
    async def test_pipeline_rollback_integration(self, mock_config, mock_pool_manager):
        """Test pipeline rollback integration."""
        # Create pipeline configuration with rollback capability
        pipeline_config = {
            'name': 'rollback_pipeline',
            'source': 'critical_data',
            'destination': 'production_table',
            'rollback': {
                'enabled': True,
                'strategy': 'checkpoint',
                'checkpoints': ['after_validate', 'after_transform']
            },
            'stages': [
                {
                    'name': 'extract',
                    'type': 'extract'
                },
                {
                    'name': 'validate',
                    'type': 'validate',
                    'checkpoint': True
                },
                {
                    'name': 'transform',
                    'type': 'transform',
                    'checkpoint': True
                },
                {
                    'name': 'load',
                    'type': 'load',
                    'critical': True
                }
            ]
        }
        
        # Mock pipeline with rollback scenario
        with patch('src.database.etl.pipeline.ETLPipeline') as mock_pipeline_class:
            mock_pipeline = Mock()
            mock_pipeline_class.return_value = mock_pipeline
            
            # Mock pipeline execution with rollback
            mock_pipeline.run.return_value = {
                'status': 'rolled_back',
                'stages_completed': 3,
                'rollback_point': 'after_transform',
                'rollback_reason': 'Load stage failed - data integrity check',
                'execution_time': 95.3,
                'errors': [
                    {'stage': 'load', 'error': 'Foreign key constraint violation'}
                ]
            }
            
            # Execute pipeline
            pipeline = ETLPipeline(mock_config)
            result = await pipeline.run(pipeline_config)
            
            # Verify rollback behavior
            assert result['status'] == 'rolled_back'
            assert result['rollback_point'] == 'after_transform'
            assert 'rollback_reason' in result


class TestPipelineComponentIntegration:
    """Test integration between pipeline components."""

    @pytest.fixture
    def mock_components(self):
        """Create mock pipeline components."""
        return {
            'session_manager': Mock(spec=SessionManager),
            'schema_manager': Mock(spec=SchemaManager),
            'crud_operations': Mock(spec=CRUDOperations),
            'bulk_operations': Mock(spec=BulkOperations),
            'etl_processor': Mock(spec=ETLProcessor)
        }

    @pytest.mark.asyncio
    async def test_schema_crud_integration(self, mock_components):
        """Test integration between schema management and CRUD operations."""
        schema_manager = mock_components['schema_manager']
        crud_operations = mock_components['crud_operations']
        
        # Mock schema creation
        schema_manager.create_table.return_value = True
        schema_manager.table_exists.return_value = True
        
        # Mock CRUD operations
        crud_operations.insert_dataframe.return_value = True
        crud_operations.select_all.return_value = pd.DataFrame({'id': [1, 2, 3]})
        
        # Test integration workflow
        table_name = 'test_integration_table'
        test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})
        
        # Create table schema
        schema_created = schema_manager.create_table(table_name, test_data.dtypes.to_dict())
        assert schema_created is True
        
        # Verify table exists
        table_exists = schema_manager.table_exists(table_name)
        assert table_exists is True
        
        # Insert data
        insert_success = crud_operations.insert_dataframe(table_name, test_data)
        assert insert_success is True
        
        # Query data
        retrieved_data = crud_operations.select_all(table_name)
        assert isinstance(retrieved_data, pd.DataFrame)
        assert len(retrieved_data) == 3

    @pytest.mark.asyncio
    async def test_etl_bulk_operations_integration(self, mock_components):
        """Test integration between ETL processor and bulk operations."""
        etl_processor = mock_components['etl_processor']
        bulk_operations = mock_components['bulk_operations']
        
        # Create large dataset for bulk processing
        large_dataset = pd.DataFrame({
            'id': range(10000),
            'value': np.random.randn(10000),
            'category': np.random.choice(['A', 'B', 'C'], 10000)
        })
        
        # Mock ETL processing
        etl_processor.process_batch.return_value = {
            'status': 'success',
            'processed_data': large_dataset,
            'records_processed': 10000
        }
        
        # Mock bulk operations
        bulk_operations.bulk_insert_dataframe.return_value = True
        bulk_operations.get_bulk_insert_stats.return_value = {
            'total_inserted': 10000,
            'batch_count': 10,
            'avg_batch_time': 2.5
        }
        
        # Test integration workflow
        etl_result = etl_processor.process_batch('source', 'destination')
        assert etl_result['status'] == 'success'
        
        # Bulk insert processed data
        bulk_success = bulk_operations.bulk_insert_dataframe(
            'destination_table', 
            etl_result['processed_data']
        )
        assert bulk_success is True
        
        # Get bulk operation statistics
        stats = bulk_operations.get_bulk_insert_stats()
        assert stats['total_inserted'] == 10000
        assert stats['batch_count'] == 10

    @pytest.mark.asyncio
    async def test_connection_pool_pipeline_integration(self, mock_components):
        """Test integration between connection pooling and pipeline execution."""
        session_manager = mock_components['session_manager']
        etl_processor = mock_components['etl_processor']
        
        # Mock connection pool behavior
        session_manager.get_connection.return_value = Mock()
        session_manager.return_connection.return_value = None
        session_manager.get_pool_stats.return_value = {
            'active_connections': 5,
            'idle_connections': 3,
            'total_connections': 8,
            'max_connections': 10
        }
        
        # Mock pipeline execution with connection management
        etl_processor.process_with_connection_pool.return_value = {
            'status': 'success',
            'batches_processed': 20,
            'total_records': 50000,
            'connection_reuse_count': 15
        }
        
        # Test integration workflow
        connection = session_manager.get_connection()
        assert connection is not None
        
        # Process data using connection pool
        result = etl_processor.process_with_connection_pool(
            source='large_dataset',
            destination='processed_table',
            batch_size=2500
        )
        
        assert result['status'] == 'success'
        assert result['batches_processed'] == 20
        assert result['connection_reuse_count'] > 0
        
        # Check pool statistics
        pool_stats = session_manager.get_pool_stats()
        assert pool_stats['active_connections'] <= pool_stats['max_connections']
        assert pool_stats['total_connections'] == pool_stats['active_connections'] + pool_stats['idle_connections']

    @pytest.mark.asyncio
    async def test_error_propagation_integration(self, mock_components):
        """Test error propagation across pipeline components."""
        schema_manager = mock_components['schema_manager']
        crud_operations = mock_components['crud_operations']
        etl_processor = mock_components['etl_processor']
        
        # Mock component failures
        schema_manager.create_table.side_effect = Exception("Schema creation failed")
        crud_operations.insert_dataframe.side_effect = Exception("Insert operation failed")
        etl_processor.process_batch.side_effect = ETLError("ETL processing failed")
        
        # Test error propagation in schema operations
        with pytest.raises(Exception) as exc_info:
            schema_manager.create_table('test_table', {})
        assert "Schema creation failed" in str(exc_info.value)
        
        # Test error propagation in CRUD operations
        with pytest.raises(Exception) as exc_info:
            crud_operations.insert_dataframe('test_table', pd.DataFrame())
        assert "Insert operation failed" in str(exc_info.value)
        
        # Test error propagation in ETL operations
        with pytest.raises(ETLError) as exc_info:
            etl_processor.process_batch('source', 'destination')
        assert "ETL processing failed" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_transaction_management_integration(self, mock_components):
        """Test transaction management across pipeline components."""
        session_manager = mock_components['session_manager']
        crud_operations = mock_components['crud_operations']
        
        # Mock transaction management
        mock_transaction = AsyncMock()
        session_manager.begin_transaction.return_value = mock_transaction
        
        crud_operations.insert_dataframe.return_value = True
        crud_operations.update_records.return_value = 5
        crud_operations.delete_records.return_value = 2
        
        # Test transactional pipeline execution
        async with session_manager.begin_transaction() as transaction:
            # Perform multiple operations in transaction
            insert_result = crud_operations.insert_dataframe('table1', pd.DataFrame({'id': [1, 2, 3]}))
            update_result = crud_operations.update_records('table2', {'status': 'processed'}, 'id IN (1,2,3,4,5)')
            delete_result = crud_operations.delete_records('table3', 'status = "obsolete"')
            
            # Verify all operations succeeded
            assert insert_result is True
            assert update_result == 5
            assert delete_result == 2
            
            # Transaction should commit automatically
        
        # Verify transaction was handled properly
        mock_transaction.__aenter__.assert_called_once()
        mock_transaction.__aexit__.assert_called_once()