# -*- coding: utf-8 -*-
"""
Validation Utilities

This module provides comprehensive validation utilities for the Connect
telecommunications data processing system, including data format validation,
telecommunications-specific field validation, and file validation.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import re
import os
import logging
from typing import Dict, List, Optional, Any, Union, Callable, Pattern
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
import ipaddress
from urllib.parse import urlparse


class ValidationError(Exception):
    """Base validation error."""
    pass


class FileValidationError(ValidationError):
    """File validation error."""
    pass


class DataValidationError(ValidationError):
    """Data validation error."""
    pass


class TelecomValidationError(ValidationError):
    """Telecommunications-specific validation error."""
    pass


@dataclass
class ValidationRule:
    """Validation rule definition."""
    name: str
    validator: Callable[[Any], bool]
    error_message: str
    required: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ValidationResult:
    """Validation result."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_error(self, message: str) -> None:
        """Add validation error."""
        self.errors.append(message)
        self.is_valid = False
    
    def add_warning(self, message: str) -> None:
        """Add validation warning."""
        self.warnings.append(message)
    
    def merge(self, other: 'ValidationResult') -> None:
        """Merge another validation result."""
        if not other.is_valid:
            self.is_valid = False
        self.errors.extend(other.errors)
        self.warnings.extend(other.warnings)
        self.metadata.update(other.metadata)


class DataValidator:
    """General data validation utilities.
    
    Features:
    - Type validation
    - Range validation
    - Format validation
    - Custom validation rules
    - Batch validation
    """
    
    def __init__(self):
        """Initialize data validator."""
        self.logger = logging.getLogger(__name__)
        self._rules: Dict[str, List[ValidationRule]] = {}
    
    def add_rule(self, field_name: str, rule: ValidationRule) -> None:
        """Add validation rule for a field.
        
        Args:
            field_name: Name of the field
            rule: Validation rule
        """
        if field_name not in self._rules:
            self._rules[field_name] = []
        self._rules[field_name].append(rule)
    
    def validate_type(self, value: Any, expected_type: type) -> ValidationResult:
        """Validate value type.
        
        Args:
            value: Value to validate
            expected_type: Expected type
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        if value is None:
            result.add_error("Value cannot be None")
            return result


def validate_excel_structure(
    file_path: Union[str, Path],
    expected_columns: Optional[List[str]] = None,
    sheet_name: Union[str, int] = 0,
    **kwargs
) -> ValidationResult:
    """Validate Excel file structure.
    
    Args:
        file_path: Path to Excel file
        expected_columns: List of expected column names
        sheet_name: Sheet name or index to validate
        **kwargs: Additional arguments for pandas.read_excel
        
    Returns:
        ValidationResult with validation details
        
    Raises:
        FileValidationError: If file cannot be read or parsed
    """
    try:
        import pandas as pd
    except ImportError:
        raise ValidationError("pandas is required for Excel validation")
    
    file_path = Path(file_path)
    
    # Check if file exists
    if not file_path.exists():
        raise FileValidationError(f"Excel file not found: {file_path}")
    
    # Check file extension
    if file_path.suffix.lower() not in ['.xlsx', '.xls', '.xlsm']:
        raise FileValidationError(f"Invalid Excel file extension: {file_path.suffix}")
    
    try:
        # Read Excel file header only
        df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=0, **kwargs)
    except Exception as e:
        raise FileValidationError(f"Failed to read Excel file {file_path}: {str(e)}")
    
    errors = []
    warnings = []
    
    # Check for expected columns
    if expected_columns:
        missing_columns = set(expected_columns) - set(df.columns)
        if missing_columns:
            errors.append(f"Missing expected columns: {list(missing_columns)}")
        
        extra_columns = set(df.columns) - set(expected_columns)
        if extra_columns:
            warnings.append(f"Extra columns found: {list(extra_columns)}")
    
    # Check for duplicate columns
    if len(df.columns) != len(set(df.columns)):
        duplicates = [col for col in df.columns if list(df.columns).count(col) > 1]
        errors.append(f"Duplicate columns found: {list(set(duplicates))}")
    
    # Check for empty column names
    empty_cols = [i for i, col in enumerate(df.columns) if pd.isna(col) or str(col).strip() == '']
    if empty_cols:
        errors.append(f"Empty column names at positions: {empty_cols}")
    
    return ValidationResult(
        is_valid=len(errors) == 0,
        errors=errors,
        warnings=warnings,
        metadata={
            'columns_found': list(df.columns),
            'total_columns': len(df.columns),
            'sheet_name': sheet_name
        }
    )
    
    def validate_range(
        self,
        value: Union[int, float],
        min_value: Optional[Union[int, float]] = None,
        max_value: Optional[Union[int, float]] = None,
        inclusive: bool = True
    ) -> ValidationResult:
        """Validate numeric range.
        
        Args:
            value: Value to validate
            min_value: Minimum value
            max_value: Maximum value
            inclusive: Whether bounds are inclusive
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        if not isinstance(value, (int, float)):
            result.add_error(f"Expected numeric value, got {type(value).__name__}")
            return result
        
        if min_value is not None:
            if inclusive and value < min_value:
                result.add_error(f"Value {value} is less than minimum {min_value}")
            elif not inclusive and value <= min_value:
                result.add_error(f"Value {value} is less than or equal to minimum {min_value}")
        
        if max_value is not None:
            if inclusive and value > max_value:
                result.add_error(f"Value {value} is greater than maximum {max_value}")
            elif not inclusive and value >= max_value:
                result.add_error(f"Value {value} is greater than or equal to maximum {max_value}")
        
        return result
    
    def validate_length(
        self,
        value: Union[str, list, dict],
        min_length: Optional[int] = None,
        max_length: Optional[int] = None
    ) -> ValidationResult:
        """Validate length of string, list, or dict.
        
        Args:
            value: Value to validate
            min_length: Minimum length
            max_length: Maximum length
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        if not hasattr(value, '__len__'):
            result.add_error(f"Value {type(value).__name__} does not have length")
            return result
        
        length = len(value)
        
        if min_length is not None and length < min_length:
            result.add_error(f"Length {length} is less than minimum {min_length}")
        
        if max_length is not None and length > max_length:
            result.add_error(f"Length {length} is greater than maximum {max_length}")
        
        return result
    
    def validate_pattern(self, value: str, pattern: Union[str, Pattern]) -> ValidationResult:
        """Validate string against regex pattern.
        
        Args:
            value: String to validate
            pattern: Regex pattern
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        if not isinstance(value, str):
            result.add_error(f"Expected string, got {type(value).__name__}")
            return result
        
        if isinstance(pattern, str):
            pattern = re.compile(pattern)
        
        if not pattern.match(value):
            result.add_error(f"Value '{value}' does not match pattern {pattern.pattern}")
        
        return result
    
    def validate_email(self, email: str) -> ValidationResult:
        """Validate email address.
        
        Args:
            email: Email address to validate
            
        Returns:
            Validation result
        """
        email_pattern = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )
        return self.validate_pattern(email, email_pattern)
    
    def validate_url(self, url: str) -> ValidationResult:
        """Validate URL.
        
        Args:
            url: URL to validate
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        try:
            parsed = urlparse(url)
            if not all([parsed.scheme, parsed.netloc]):
                result.add_error(f"Invalid URL format: {url}")
        except Exception as e:
            result.add_error(f"URL parsing error: {e}")
        
        return result
    
    def validate_ip_address(self, ip: str) -> ValidationResult:
        """Validate IP address (IPv4 or IPv6).
        
        Args:
            ip: IP address to validate
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        try:
            ipaddress.ip_address(ip)
        except ValueError as e:
            result.add_error(f"Invalid IP address '{ip}': {e}")
        
        return result
    
    def validate_datetime(
        self,
        value: Union[str, datetime],
        format_string: Optional[str] = None
    ) -> ValidationResult:
        """Validate datetime value.
        
        Args:
            value: Datetime value to validate
            format_string: Expected datetime format
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        if isinstance(value, datetime):
            return result
        
        if not isinstance(value, str):
            result.add_error(f"Expected string or datetime, got {type(value).__name__}")
            return result
        
        if format_string:
            try:
                datetime.strptime(value, format_string)
            except ValueError as e:
                result.add_error(f"Invalid datetime format '{value}': {e}")
        else:
            # Try common formats
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%S.%f',
                '%Y-%m-%d',
                '%d/%m/%Y',
                '%m/%d/%Y'
            ]
            
            parsed = False
            for fmt in formats:
                try:
                    datetime.strptime(value, fmt)
                    parsed = True
                    break
                except ValueError:
                    continue
            
            if not parsed:
                result.add_error(f"Unable to parse datetime '{value}'")
        
        return result
    
    def validate_field(
        self,
        field_name: str,
        value: Any,
        allow_none: bool = False
    ) -> ValidationResult:
        """Validate field using registered rules.
        
        Args:
            field_name: Name of the field
            value: Value to validate
            allow_none: Whether None values are allowed
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        if value is None:
            if not allow_none:
                result.add_error(f"Field '{field_name}' cannot be None")
            return result
        
        rules = self._rules.get(field_name, [])
        
        for rule in rules:
            try:
                if not rule.validator(value):
                    if rule.required:
                        result.add_error(f"Field '{field_name}': {rule.error_message}")
                    else:
                        result.add_warning(f"Field '{field_name}': {rule.error_message}")
            except Exception as e:
                result.add_error(f"Validation error for field '{field_name}': {e}")
        
        return result
    
    def validate_record(
        self,
        record: Dict[str, Any],
        required_fields: Optional[List[str]] = None,
        allow_extra_fields: bool = True
    ) -> ValidationResult:
        """Validate entire record.
        
        Args:
            record: Record to validate
            required_fields: List of required field names
            allow_extra_fields: Whether extra fields are allowed
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        # Check required fields
        if required_fields:
            for field in required_fields:
                if field not in record:
                    result.add_error(f"Required field '{field}' is missing")
                elif record[field] is None:
                    result.add_error(f"Required field '{field}' cannot be None")
        
        # Check extra fields
        if not allow_extra_fields:
            known_fields = set(self._rules.keys())
            if required_fields:
                known_fields.update(required_fields)
            
            extra_fields = set(record.keys()) - known_fields
            if extra_fields:
                result.add_warning(f"Extra fields found: {', '.join(extra_fields)}")
        
        # Validate each field
        for field_name, value in record.items():
            field_result = self.validate_field(field_name, value)
            result.merge(field_result)
        
        return result


class TelecomValidator(DataValidator):
    """Telecommunications-specific data validator.
    
    Features:
    - IMSI validation
    - MSISDN validation
    - Cell ID validation
    - LAC/TAC validation
    - Frequency validation
    - Signal strength validation
    """
    
    def __init__(self):
        """Initialize telecom validator."""
        super().__init__()
        self._setup_telecom_rules()
    
    def _setup_telecom_rules(self) -> None:
        """Setup telecommunications-specific validation rules."""
        # IMSI validation (15 digits)
        self.add_rule('imsi', ValidationRule(
            name='imsi_format',
            validator=lambda x: self._validate_imsi(x),
            error_message='IMSI must be 15 digits'
        ))
        
        # MSISDN validation
        self.add_rule('msisdn', ValidationRule(
            name='msisdn_format',
            validator=lambda x: self._validate_msisdn(x),
            error_message='Invalid MSISDN format'
        ))
        
        # Cell ID validation
        self.add_rule('cell_id', ValidationRule(
            name='cell_id_range',
            validator=lambda x: self._validate_cell_id(x),
            error_message='Cell ID must be positive integer'
        ))
        
        # LAC validation
        self.add_rule('lac', ValidationRule(
            name='lac_range',
            validator=lambda x: self._validate_lac(x),
            error_message='LAC must be between 1 and 65533'
        ))
        
        # TAC validation
        self.add_rule('tac', ValidationRule(
            name='tac_range',
            validator=lambda x: self._validate_tac(x),
            error_message='TAC must be between 1 and 65533'
        ))
        
        # Signal strength validation (RSSI)
        self.add_rule('rssi', ValidationRule(
            name='rssi_range',
            validator=lambda x: self._validate_rssi(x),
            error_message='RSSI must be between -120 and -30 dBm'
        ))
        
        # Signal quality validation (RSRP)
        self.add_rule('rsrp', ValidationRule(
            name='rsrp_range',
            validator=lambda x: self._validate_rsrp(x),
            error_message='RSRP must be between -140 and -44 dBm'
        ))
        
        # Signal quality validation (RSRQ)
        self.add_rule('rsrq', ValidationRule(
            name='rsrq_range',
            validator=lambda x: self._validate_rsrq(x),
            error_message='RSRQ must be between -20 and -3 dB'
        ))
        
        # Frequency validation
        self.add_rule('frequency', ValidationRule(
            name='frequency_range',
            validator=lambda x: self._validate_frequency(x),
            error_message='Frequency must be valid mobile frequency'
        ))
    
    def _validate_imsi(self, imsi: Any) -> bool:
        """Validate IMSI format.
        
        Args:
            imsi: IMSI to validate
            
        Returns:
            True if valid
        """
        if not isinstance(imsi, str):
            return False
        
        # IMSI should be 15 digits
        return len(imsi) == 15 and imsi.isdigit()
    
    def _validate_msisdn(self, msisdn: Any) -> bool:
        """Validate MSISDN format.
        
        Args:
            msisdn: MSISDN to validate
            
        Returns:
            True if valid
        """
        if not isinstance(msisdn, str):
            return False
        
        # Remove common prefixes and separators
        cleaned = re.sub(r'[+\-\s()]', '', msisdn)
        
        # MSISDN should be 10-15 digits
        return 10 <= len(cleaned) <= 15 and cleaned.isdigit()
    
    def _validate_cell_id(self, cell_id: Any) -> bool:
        """Validate Cell ID.
        
        Args:
            cell_id: Cell ID to validate
            
        Returns:
            True if valid
        """
        try:
            cid = int(cell_id)
            return cid > 0
        except (ValueError, TypeError):
            return False
    
    def _validate_lac(self, lac: Any) -> bool:
        """Validate LAC (Location Area Code).
        
        Args:
            lac: LAC to validate
            
        Returns:
            True if valid
        """
        try:
            lac_val = int(lac)
            return 1 <= lac_val <= 65533
        except (ValueError, TypeError):
            return False
    
    def _validate_tac(self, tac: Any) -> bool:
        """Validate TAC (Tracking Area Code).
        
        Args:
            tac: TAC to validate
            
        Returns:
            True if valid
        """
        try:
            tac_val = int(tac)
            return 1 <= tac_val <= 65533
        except (ValueError, TypeError):
            return False
    
    def _validate_rssi(self, rssi: Any) -> bool:
        """Validate RSSI (Received Signal Strength Indicator).
        
        Args:
            rssi: RSSI to validate
            
        Returns:
            True if valid
        """
        try:
            rssi_val = float(rssi)
            return -120 <= rssi_val <= -30
        except (ValueError, TypeError):
            return False
    
    def _validate_rsrp(self, rsrp: Any) -> bool:
        """Validate RSRP (Reference Signal Received Power).
        
        Args:
            rsrp: RSRP to validate
            
        Returns:
            True if valid
        """
        try:
            rsrp_val = float(rsrp)
            return -140 <= rsrp_val <= -44
        except (ValueError, TypeError):
            return False
    
    def _validate_rsrq(self, rsrq: Any) -> bool:
        """Validate RSRQ (Reference Signal Received Quality).
        
        Args:
            rsrq: RSRQ to validate
            
        Returns:
            True if valid
        """
        try:
            rsrq_val = float(rsrq)
            return -20 <= rsrq_val <= -3
        except (ValueError, TypeError):
            return False
    
    def _validate_frequency(self, frequency: Any) -> bool:
        """Validate mobile frequency.
        
        Args:
            frequency: Frequency to validate (in MHz)
            
        Returns:
            True if valid
        """
        try:
            freq = float(frequency)
            
            # Common mobile frequency bands (MHz)
            valid_ranges = [
                (450, 470),    # CDMA 450
                (698, 960),    # GSM 850/900
                (1710, 2170),  # GSM 1800/1900, UMTS
                (2300, 2400),  # LTE Band 40
                (2500, 2690),  # LTE Band 7/38/41
                (3400, 3800),  # 5G n78
                (24250, 29500) # 5G mmWave
            ]
            
            return any(start <= freq <= end for start, end in valid_ranges)
        except (ValueError, TypeError):
            return False
    
    def validate_cdr_record(self, record: Dict[str, Any]) -> ValidationResult:
        """Validate CDR (Call Detail Record).
        
        Args:
            record: CDR record to validate
            
        Returns:
            Validation result
        """
        required_fields = [
            'imsi', 'msisdn', 'call_start_time', 'call_end_time',
            'cell_id', 'lac', 'duration'
        ]
        
        result = self.validate_record(record, required_fields)
        
        # Additional CDR-specific validations
        if 'call_start_time' in record and 'call_end_time' in record:
            try:
                start_time = datetime.fromisoformat(record['call_start_time'])
                end_time = datetime.fromisoformat(record['call_end_time'])
                
                if end_time <= start_time:
                    result.add_error("Call end time must be after start time")
                    
                duration = (end_time - start_time).total_seconds()
                if 'duration' in record:
                    recorded_duration = float(record['duration'])
                    if abs(duration - recorded_duration) > 1:  # 1 second tolerance
                        result.add_warning(
                            f"Duration mismatch: calculated {duration}s, "
                            f"recorded {recorded_duration}s"
                        )
            except (ValueError, TypeError) as e:
                result.add_error(f"Invalid datetime format: {e}")
        
        return result
    
    def validate_kpi_record(self, record: Dict[str, Any]) -> ValidationResult:
        """Validate KPI record.
        
        Args:
            record: KPI record to validate
            
        Returns:
            Validation result
        """
        required_fields = ['timestamp', 'cell_id', 'kpi_name', 'kpi_value']
        
        result = self.validate_record(record, required_fields)
        
        # Additional KPI-specific validations
        if 'kpi_value' in record:
            try:
                kpi_value = float(record['kpi_value'])
                if kpi_value < 0:
                    result.add_warning("Negative KPI value detected")
            except (ValueError, TypeError):
                result.add_error("KPI value must be numeric")
        
        return result


def validate_file_path(file_path: Union[str, Path]) -> ValidationResult:
    """Validate file path.
    
    Args:
        file_path: File path to validate
        
    Returns:
        Validation result
    """
    result = ValidationResult(is_valid=True)
    
    try:
        path = Path(file_path)
        
        # Check if path exists
        if not path.exists():
            result.add_error(f"File does not exist: {file_path}")
            return result
        
        # Check if it's a file (not directory)
        if not path.is_file():
            result.add_error(f"Path is not a file: {file_path}")
            return result
        
        # Check file permissions
        if not os.access(path, os.R_OK):
            result.add_error(f"File is not readable: {file_path}")
        
        # Check file size
        file_size = path.stat().st_size
        if file_size == 0:
            result.add_warning(f"File is empty: {file_path}")
        elif file_size > 10 * 1024 * 1024 * 1024:  # 10GB
            result.add_warning(f"Large file detected ({file_size / (1024**3):.1f}GB): {file_path}")
        
        result.metadata['file_size'] = file_size
        result.metadata['file_extension'] = path.suffix.lower()
        
    except Exception as e:
        result.add_error(f"File validation error: {e}")
    
    return result


def validate_data_format(
    file_path: Union[str, Path],
    expected_format: Optional[str] = None
) -> ValidationResult:
    """Validate data file format.
    
    Args:
        file_path: File path to validate
        expected_format: Expected file format (csv, excel, json, etc.)
        
    Returns:
        Validation result
    """
    result = ValidationResult(is_valid=True)
    
    # First validate file path
    path_result = validate_file_path(file_path)
    result.merge(path_result)
    
    if not path_result.is_valid:
        return result
    
    path = Path(file_path)
    extension = path.suffix.lower()
    
    # Supported formats
    supported_formats = {
        '.csv': 'csv',
        '.tsv': 'tsv',
        '.txt': 'text',
        '.xlsx': 'excel',
        '.xls': 'excel',
        '.xlsb': 'excel',
        '.json': 'json',
        '.parquet': 'parquet',
        '.feather': 'feather',
        '.h5': 'hdf5',
        '.hdf5': 'hdf5'
    }
    
    detected_format = supported_formats.get(extension)
    
    if not detected_format:
        result.add_error(f"Unsupported file format: {extension}")
        return result
    
    if expected_format and detected_format != expected_format:
        result.add_error(
            f"Format mismatch: expected {expected_format}, got {detected_format}"
        )
    
    result.metadata['detected_format'] = detected_format
    result.metadata['file_extension'] = extension
    
    return result


async def validate_csv_structure(
    file_path: Union[str, Path],
    expected_columns: Optional[List[str]] = None,
    delimiter: str = ',',
    encoding: str = 'utf-8'
) -> ValidationResult:
    """Validate CSV file structure.
    
    Args:
        file_path: Path to CSV file
        expected_columns: Expected column names
        delimiter: CSV delimiter
        encoding: File encoding
        
    Returns:
        Validation result
    """
    result = ValidationResult(is_valid=True)
    
    try:
        import pandas as pd
        
        # First validate file path and format
        path_result = validate_data_format(file_path, 'csv')
        result.merge(path_result)
        
        if not path_result.is_valid:
            return result
        
        # Try to read CSV header
        try:
            df_sample = pd.read_csv(
                file_path,
                delimiter=delimiter,
                encoding=encoding,
                nrows=0  # Only read header
            )
            
            columns = df_sample.columns.tolist()
            result.metadata['columns'] = columns
            result.metadata['column_count'] = len(columns)
            
            # Check for expected columns
            if expected_columns:
                missing_columns = set(expected_columns) - set(columns)
                if missing_columns:
                    result.add_error(
                        f"Missing expected columns: {list(missing_columns)}"
                    )
                
                extra_columns = set(columns) - set(expected_columns)
                if extra_columns:
                    result.add_warning(
                        f"Extra columns found: {list(extra_columns)}"
                    )
            
            # Check for duplicate columns
            if len(columns) != len(set(columns)):
                duplicates = [col for col in columns if columns.count(col) > 1]
                result.add_error(f"Duplicate columns found: {list(set(duplicates))}")
            
            # Check for empty column names
            empty_cols = [i for i, col in enumerate(columns) if not str(col).strip()]
            if empty_cols:
                result.add_error(f"Empty column names at positions: {empty_cols}")
                
        except pd.errors.EmptyDataError:
            result.add_error("CSV file is empty")
        except pd.errors.ParserError as e:
            result.add_error(f"CSV parsing error: {e}")
        except UnicodeDecodeError as e:
            result.add_error(f"Encoding error: {e}")
            
    except ImportError:
        result.add_error("pandas is required for CSV validation")
    except Exception as e:
        result.add_error(f"CSV validation error: {e}")
    
    return result