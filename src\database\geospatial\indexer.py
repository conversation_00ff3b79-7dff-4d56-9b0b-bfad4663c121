__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Spatial indexing module for geospatial data.

This module provides spatial indexing capabilities using R-tree and other
spatial data structures for efficient spatial queries and operations.
"""

import logging
from typing import Any, Dict, List, Optional, Tuple, Union

import geopandas as gpd
import pandas as pd
from shapely.geometry import Point, Polygon

try:
    from rtree import index
    HAS_RTREE = True
except ImportError:
    HAS_RTREE = False
    index = None

logger = logging.getLogger(__name__)


class SpatialIndexer:
    """Base class for spatial indexing."""
    
    def __init__(self):
        """Initialize the spatial indexer."""
        self._geometries = {}
        self._size = 0
    
    def insert(self, geom_id, geometry):
        """Insert a geometry into the index.
        
        Args:
            geom_id: Unique identifier for the geometry
            geometry: Geometry object to index
        """
        self._geometries[geom_id] = geometry
        self._size += 1
    
    def delete(self, geom_id):
        """Delete a geometry from the index.
        
        Args:
            geom_id: Unique identifier for the geometry to delete
        """
        if geom_id in self._geometries:
            del self._geometries[geom_id]
            self._size -= 1
    
    def update(self, geom_id, geometry):
        """Update a geometry in the index.
        
        Args:
            geom_id: Unique identifier for the geometry
            geometry: New geometry object
        """
        if geom_id in self._geometries:
            self._geometries[geom_id] = geometry
        else:
            self.insert(geom_id, geometry)
    
    def size(self):
        """Get the number of geometries in the index.
        
        Returns:
            Number of indexed geometries
        """
        return self._size
    
    def query_bbox(self, bbox):
        """Query geometries within a bounding box.
        
        Args:
            bbox: Tuple of (minx, miny, maxx, maxy)
            
        Returns:
            List of geometry IDs within the bounding box
        """
        minx, miny, maxx, maxy = bbox
        results = []
        
        for geom_id, geometry in self._geometries.items():
            geom_bounds = geometry.bounds
            if (geom_bounds[0] <= maxx and geom_bounds[2] >= minx and
                geom_bounds[1] <= maxy and geom_bounds[3] >= miny):
                results.append(geom_id)
        
        return results
    
    def query_point(self, point, radius=0.1):
        """Query geometries near a point.
        
        Args:
            point: Query point geometry
            radius: Search radius
            
        Returns:
            List of geometry IDs within radius of the point
        """
        results = []
        
        for geom_id, geometry in self._geometries.items():
            if geometry.distance(point) <= radius:
                results.append(geom_id)
        
        return results
    
    def query_geometry(self, geometry):
        """Query geometries that intersect with the given geometry.
        
        Args:
            geometry: Query geometry
            
        Returns:
            List of geometry IDs that intersect
        """
        results = []
        
        for geom_id, indexed_geometry in self._geometries.items():
            if geometry.intersects(indexed_geometry):
                results.append(geom_id)
        
        return results
    
    def query_nearest(self, geometry, k=1):
        """Find k nearest geometries.
        
        Args:
            geometry: Query geometry
            k: Number of nearest neighbors to find
            
        Returns:
            List of k nearest geometry IDs
        """
        distances = []
        
        for geom_id, indexed_geometry in self._geometries.items():
            distance = geometry.distance(indexed_geometry)
            distances.append((distance, geom_id))
        
        distances.sort(key=lambda x: x[0])
        return [geom_id for _, geom_id in distances[:k]]
    
    def build_index(self, geometries):
        """Build spatial index from geometries.
        
        Args:
            geometries: List of (geom_id, geometry) tuples
        """
        self.clear()
        for geom_id, geometry in geometries:
            self.insert(geom_id, geometry)
    
    def query(self, geometry):
        """Query the spatial index.
        
        Args:
            geometry: Query geometry
            
        Returns:
            List of matching geometry IDs
        """
        return self.query_geometry(geometry)
    
    def nearest(self, geometry, k=1):
        """Find k nearest neighbors.
        
        Args:
            geometry: Query geometry
            k: Number of neighbors to find
            
        Returns:
            List of nearest geometry IDs
        """
        return self.query_nearest(geometry, k)
    
    def clear(self):
        """Clear all geometries from the index."""
        self._geometries.clear()
        self._size = 0
    
    def save(self, filepath):
        """Save the spatial index to a file.
        
        Args:
            filepath: Path to save the index
        """
        import pickle
        
        data = {
            'geometries': self._geometries,
            'size': self._size
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(data, f)
    
    def load(self, filepath):
        """Load the spatial index from a file.
        
        Args:
            filepath: Path to load the index from
        """
        import pickle
        
        with open(filepath, 'rb') as f:
            data = pickle.load(f)
        
        self._geometries = data['geometries']
        self._size = data['size']


class RTreeIndexer(SpatialIndexer):
    """R-tree based spatial indexer for efficient spatial queries."""
    
    def __init__(self, properties: Optional[Any] = None):
        """Initialize R-tree indexer.
        
        Args:
            properties: R-tree properties for customization
        """
        super().__init__()
        if not HAS_RTREE:
            logger.warning("rtree package not available, using fallback implementation")
            self.properties = None
            self.index = None
            self._fallback_data = []
        else:
            self.properties = properties or index.Property()
            self.index = index.Index(properties=self.properties)
        self._geometries = {}
    
    def build_index(self, data: gpd.GeoDataFrame) -> None:
        """Build R-tree index from GeoDataFrame.
        
        Args:
            data: GeoDataFrame containing spatial data
        """
        logger.info(f"Building spatial index for {len(data)} geometries")
        
        # Clear existing data
        self._geometries.clear()
        self._indexed_data.clear()
        
        if HAS_RTREE and self.index is not None:
            # Clear existing index
            self.index = index.Index(properties=self.properties)
            
            for idx, row in data.iterrows():
                geometry = row.geometry
                if geometry and not geometry.is_empty:
                    # Get bounding box for R-tree
                    bounds = geometry.bounds
                    self.index.insert(idx, bounds)
                    self._geometries[idx] = geometry
                    self._indexed_data[idx] = row
        else:
            # Fallback implementation without R-tree
            self._fallback_data = []
            for idx, row in data.iterrows():
                geometry = row.geometry
                if geometry and not geometry.is_empty:
                    self._geometries[idx] = geometry
                    self._indexed_data[idx] = row
                    self._fallback_data.append((idx, geometry.bounds))
        
        logger.info(f"Spatial index built with {len(self._geometries)} geometries")
    
    def query(self, geometry: Any, predicate: str = 'intersects') -> List[int]:
        """Query the spatial index.
        
        Args:
            geometry: Query geometry (Point, Polygon, etc.)
            predicate: Spatial predicate ('intersects', 'contains', 'within')
            
        Returns:
            List of indices matching the query
        """
        if not hasattr(geometry, 'bounds'):
            raise ValueError("Query geometry must have bounds attribute")
        
        if HAS_RTREE and self.index is not None:
            # Get candidates from R-tree using bounding box
            candidates = list(self.index.intersection(geometry.bounds))
        else:
            # Fallback: check all geometries
            query_bounds = geometry.bounds
            candidates = []
            for idx, bounds in self._fallback_data:
                # Simple bounding box intersection check
                if (bounds[0] <= query_bounds[2] and bounds[2] >= query_bounds[0] and
                    bounds[1] <= query_bounds[3] and bounds[3] >= query_bounds[1]):
                    candidates.append(idx)
        
        # Filter candidates using exact geometry predicate
        results = []
        for idx in candidates:
            indexed_geom = self._geometries.get(idx)
            if indexed_geom:
                if predicate == 'intersects' and geometry.intersects(indexed_geom):
                    results.append(idx)
                elif predicate == 'contains' and geometry.contains(indexed_geom):
                    results.append(idx)
                elif predicate == 'within' and geometry.within(indexed_geom):
                    results.append(idx)
        
        return results
    
    def nearest(self, point: Point, k: int = 1) -> List[int]:
        """Find k nearest neighbors to a point.
        
        Args:
            point: Query point
            k: Number of nearest neighbors to find
            
        Returns:
            List of indices of nearest neighbors
        """
        if not isinstance(point, Point):
            raise ValueError("Query must be a Point geometry")
        
        if HAS_RTREE and self.index is not None:
            # Use R-tree nearest neighbor search
            nearest_ids = list(self.index.nearest(point.bounds, k))
        else:
            # Fallback: calculate distances to all geometries
            nearest_ids = list(self._geometries.keys())
        
        # Calculate actual distances and sort
        distances = []
        for idx in nearest_ids:
            geom = self._geometries.get(idx)
            if geom:
                distance = point.distance(geom)
                distances.append((distance, idx))
        
        # Sort by distance and return top k
        distances.sort(key=lambda x: x[0])
        return [idx for _, idx in distances[:k]]
    
    def get_geometry(self, idx: int) -> Optional[Any]:
        """Get geometry by index.
        
        Args:
            idx: Geometry index
            
        Returns:
            Geometry object or None if not found
        """
        return self._geometries.get(idx)
    
    def get_data(self, idx: int) -> Optional[pd.Series]:
        """Get data row by index.
        
        Args:
            idx: Data index
            
        Returns:
            Data row or None if not found
        """
        return self._indexed_data.get(idx)
    
    def clear(self) -> None:
        """Clear the index and all stored data."""
        if HAS_RTREE and self.properties is not None:
            self.index = index.Index(properties=self.properties)
        else:
            self.index = None
            self._fallback_data = []
        self._geometries.clear()
        self._indexed_data.clear()
        logger.info("Spatial index cleared")
    
    def __len__(self) -> int:
        """Return number of indexed geometries."""
        return len(self._geometries)
    
    def __contains__(self, idx: int) -> bool:
        """Check if index exists in the spatial index."""
        return idx in self._geometries