[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "connect"
version = "0.1.0"
description = "Modern geospatial data processing platform with QGIS integration"
authors = ["Vincent.Li <<EMAIL>>"]
readme = "README.md"
license = "MIT"
repository = "https://github.com/connect-team/connect"
documentation = "https://connect.readthedocs.io"
keywords = ["geospatial", "gis", "qgis", "database", "data-processing"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: GIS",
    "Topic :: Database",
]
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = ">=3.12,<4.0"
# Core dependencies
pydantic = ">=2.0.0,<3.0.0"
PyYAML = ">=6.0.0,<7.0.0"
click = ">=8.1.0,<9.0.0"
python-dotenv = ">=1.0.0,<2.0.0"
rich = ">=13.0.0,<14.0.0"
structlog = ">=23.0.0,<24.0.0"
tqdm = ">=4.65.0,<5.0.0"

# Database dependencies
psycopg2-binary = ">=2.9.0,<3.0.0"
SQLAlchemy = ">=2.0.0,<3.0.0"
aiofiles = ">=23.0.0,<24.0.0"
asyncpg = ">=0.28.0,<1.0.0"

# Data processing dependencies
pandas = ">=2.0.0,<3.0.0"
numpy = ">=1.24.0,<2.0.0"
openpyxl = ">=3.1.0,<4.0.0"
# xlrd = ">=2.0.0,<3.0.0"  # Removed - limited Excel support, use openpyxl instead
xlsxwriter = ">=3.1.0,<4.0.0"
ijson = ">=3.2.0,<4.0.0"  # For streaming JSON parsing

# Geospatial dependencies
geopandas = ">=1.1.0,<2.0.0"
shapely = ">=2.1.1,<3.0.0"
pyproj = ">=3.7.1,<4.0.0"
rasterio = ">=1.4.3,<2.0.0"
fiona = ">=1.9.0,<2.0.0"

# Security and validation
# cerberus = ">=1.3.0,<2.0.0"  # Removed - replaced with pydantic validation
jsonschema = ">=4.17.0,<5.0.0"
cryptography = ">=41.0.0,<42.0.0"

# Performance monitoring
memory-profiler = ">=0.61.0,<1.0.0"
psutil = ">=5.9.0,<6.0.0"

[tool.poetry.group.dev.dependencies]
# Testing
pytest = ">=7.4.0,<8.0.0"
pytest-asyncio = ">=0.21.0,<1.0.0"
pytest-cov = ">=4.1.0,<5.0.0"
pytest-mock = ">=3.11.0,<4.0.0"

# Code quality
black = ">=23.3.0,<24.0.0"
isort = ">=5.12.0,<6.0.0"
flake8 = ">=6.0.0,<7.0.0"
mypy = ">=1.5.0,<2.0.0"
bandit = ">=1.7.0,<2.0.0"

# Type stubs
types-PyYAML = ">=6.0.0,<7.0.0"

[tool.poetry.group.qgis]
optional = true

[tool.poetry.group.qgis.dependencies]
# QGIS integration dependencies (install via conda with QGIS)
# These are marked as optional since QGIS should be installed via conda/OSGeo4W
matplotlib = ">=3.7.0,<4.0.0"
plotly = ">=5.15.0,<6.0.0"

[tool.poetry.extras]
qgis = ["matplotlib", "plotly"]

[tool.poetry.scripts]
connect = "src.cli.import_cli:cli"

# Project configuration is handled by [tool.poetry] section above

# Development tools configuration
[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
  )/
)
'''

[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["src"]
known_third_party = [
    "geopandas", "shapely", "rasterio", "fiona", "pyproj",
    "pandas", "numpy", "sqlalchemy", "psycopg2", "pydantic"
]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
no_implicit_optional = true
show_error_codes = true
strict = true
warn_redundant_casts = true
warn_unused_ignores = true
disallow_any_generics = true
check_untyped_defs = true
disallow_untyped_calls = true

# 忽略第三方库的类型检查
[[tool.mypy.overrides]]
module = [
    "geopandas.*",
    "shapely.*",
    "rasterio.*",
    "fiona.*",
    "pyproj.*",
    "psycopg2.*",
    "aiopg.*",
    "asyncpg.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
pythonpath = [".", "src"]
addopts = "-v --tb=short"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "asyncio: marks tests as async tests",
    "unit: marks tests as unit tests",
    "geo: marks tests as geospatial tests"
]
asyncio_mode = "auto"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning"
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "build",
    "dist",
    "*.egg-info",
    "migrations",
]
max-complexity = 10

[tool.bandit]
exclude_dirs = ["tests", "test_*"]
skips = ["B101", "B601"]

[tool.bandit.assert_used]
skips = ["*_test.py", "test_*.py"]
