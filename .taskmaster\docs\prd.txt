# Connect 数据分析与可视化系统 - PostgreSQL 数据库框架解决方案

## 📋 项目概述

本文档整合了多个版本的数据库框架设计方案，结合业界最佳实践，为 Connect 电信数据分析与可视化系统提供完整的 PostgreSQL 数据库操作框架解决方案。

### 核心目标
- 构建高性能、可扩展的数据库操作框架
- 支持多种电信数据类型的导入导出和处理
- 实现高并发、大数据量的数据操作
- 提供完整的 Schema 管理和数据清洗功能
- 确保系统在单机环境下的卓越性能表现

### 🎯 开发策略与优先级

本框架采用**渐进式开发策略**，基于产品管理最佳实践，按照业务价值和技术风险进行功能优先级排序，确保快速交付核心价值，同时为未来扩展预留空间。

#### 功能优先级分级 (Product Management Framework)

**🔥 P0 - 必需核心功能 (Critical - MVP阶段，1-2周)**
- **业务价值**: 系统基础运行能力，无此功能系统无法使用
- **技术风险**: 低，成熟技术栈
- **核心模块**:
  - 基础连接管理: `connection/session.py`
  - Schema基础管理: `schema/manager.py`, `schema/models.py`
  - 基础CRUD操作: `operations/crud.py`
  - CSV数据导入导出: `operations/importer.py`, `operations/exporter.py`
  - 基础数据验证: `utils/validators.py`
  - 基础日志监控: `monitoring/logger.py`
  - 异常处理框架: `exceptions.py`
  - 配置管理: `config.py`

**⚡ P1 - 重要功能 (High Priority - 生产就绪阶段，2-3周)**
- **业务价值**: 生产环境必需，性能和稳定性保障
- **技术风险**: 中等，需要性能调优
- **核心模块**:
  - 连接池管理: `connection/pool.py`
  - 健康检查: `connection/health_check.py`
  - ETL基础流水线: `etl/pipeline.py`, `etl/extractor.py`, `etl/loader.py`
  - 数据清洗: `utils/data_cleaner.py`
  - 性能监控: `monitoring/metrics.py`, `utils/performance.py`
  - 批处理优化: `utils/batch_processor.py`
  - Schema验证器: `schema/validators.py`

**🚀 P2 - 有价值功能 (Medium Priority - 功能增强阶段，3-4周)**
- **业务价值**: 提升用户体验和系统能力
- **技术风险**: 中等，需要架构设计
- **核心模块**:
  - 地理空间处理: `geospatial/processor.py`, `geospatial/polygon_handler.py`
  - CDR Vendor标签: `geospatial/vendor_tagger.py`
  - 高级查询构建: `core/query_builder.py`
  - 数据转换器: `etl/transformer.py`
  - Excel/JSON处理: `etl/processors/excel_processor.py`, `etl/processors/json_processor.py`
  - 缓存管理: `utils/cache.py`
  - 进度追踪: `utils/progress_tracker.py`
  - Schema路由: `schema/router.py`

**🔧 P3 - 可选功能 (Low Priority - 高级特性阶段，4-6周)**
- **业务价值**: 高级特性，提升系统竞争力
- **技术风险**: 高，复杂架构和新技术
- **核心模块**:
  - 读写分离: `connection/read_write_splitter.py`
  - 专用仓库模式: `repositories/*_repository.py`
  - 数据库迁移: `schema/migrations.py`
  - 高级ETL处理器: `etl/processors/parquet_processor.py`
  - 内存优化: `utils/memory_optimizer.py`
  - 高级性能监控: `core/performance_monitor.py`
  - 数据合并器: `operations/merger.py`
  - 坐标转换工具: `geospatial/coordinate_utils.py`

#### 开发执行策略

**🎯 MVP快速交付 (P0功能)**
- 时间目标: 1-2周
- 成功标准: 基础数据操作、CSV导入导出、基础监控
- 技术债务: 可接受，后续重构

**🏗️ 生产就绪 (P0+P1功能)**
- 时间目标: 3-5周
- 成功标准: 支持生产环境部署，满足性能要求
- 质量要求: 完整测试覆盖，性能基准达标

**🚀 功能完善 (P0+P1+P2功能)**
- 时间目标: 6-9周
- 成功标准: 完整电信数据分析能力
- 用户价值: 满足专业用户需求

**🔮 高级特性 (全功能)**
- 时间目标: 10-15周
- 成功标准: 企业级特性完备
- 战略价值: 技术领先，市场竞争优势



## 🏗️ 系统架构设计

### 1. 分层架构模式

```
┌─────────────────────────────────────────────────────────────┐
│                    应用服务层 (Application Layer)              │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Business Layer)                │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (Data Access Layer)             │
├─────────────────────────────────────────────────────────────┤
│                    连接管理层 (Connection Layer)              │
├─────────────────────────────────────────────────────────────┤
│                    PostgreSQL 数据库                         │
└─────────────────────────────────────────────────────────────┘
```

### 2. 核心设计原则
- **单一职责原则**: 每个模块专注于特定功能
- **开闭原则**: 对扩展开放，对修改封闭
- **依赖倒置**: 依赖抽象而非具体实现
- **接口隔离**: 提供细粒度的接口设计
- **高内聚低耦合**: 模块内部紧密协作，模块间松散耦合

## 🗂️ 项目目录结构

### 基于业界最佳实践和安全性优化的目录结构

```
src/database/
├── __init__.py                    # 框架主入口和API导出 [P0]
├── config.py                      # 配置管理和环境变量 [P0]
├── exceptions.py                  # 自定义异常类定义 [P0]
├── constants.py                   # 常量定义 [P0]
├── types.py                       # 类型定义和类型别名 [P0]
├── core/                         # 核心框架层
│   ├── __init__.py               # [P0]
│   ├── connection_pool.py        # 智能连接池(支持读写分离) [P3]
│   ├── base_repository.py        # 基础仓库(批量操作优化) [P3]
│   ├── query_builder.py          # 高性能查询构建器 [P2]
│   └── performance_monitor.py    # 性能监控 [P3]
├── connection/                   # 连接管理模块
│   ├── __init__.py               # [P0]
│   ├── pool.py                   # 异步连接池管理 [P1]
│   ├── session.py                # 数据库会话管理 [P0]
│   ├── health_check.py           # 连接健康检查 [P1]
│   ├── factory.py                # 连接工厂模式 [P1]
│   └── read_write_splitter.py    # 读写分离管理器 [P3]
├── repositories/                 # 数据访问层 [P3]
│   ├── __init__.py               # [P3]
│   ├── ep_repository.py          # EP Schema专用仓库 [P3]
│   ├── kpi_repository.py         # KPI Schema专用仓库 [P3]
│   ├── cdr_repository.py         # CDR Schema专用仓库 [P3]
│   ├── score_repository.py       # Score Schema专用仓库 [P3]
│   ├── cfg_repository.py         # CFG Schema专用仓库 [P3]
│   ├── nlg_repository.py         # NLG Schema专用仓库 [P3]
│   └── public_repository.py      # Public Schema仓库 [P3]
├── schema/                       # Schema 和表管理模块
│   ├── __init__.py               # [P0]
│   ├── manager.py                # Schema 操作管理 [P0]
│   ├── router.py                 # Schema 路由和映射 [P2]
│   ├── models.py                 # 数据模型定义 [P0]
│   ├── migrations.py             # 数据库迁移管理 [P3]
│   └── validators.py             # Schema 验证器 [P1]

├── etl/                         # ETL 数据处理模块
│   ├── __init__.py               # [P0]
│   ├── pipeline.py               # ETL 流水线管理 [P1]
│   ├── extractor.py             # 数据提取器 [P1-简化版]
│   ├── transformer.py           # 数据转换器 [P2]
│   ├── loader.py                # 数据加载器 [P1-简化版]
│   └── processors/               # 专用数据处理器
│       ├── __init__.py           # [P0]
│       ├── csv_processor.py      # CSV 处理器 [P0]
│       ├── excel_processor.py    # Excel 处理器 [P2]
│       ├── json_processor.py     # JSON 处理器 [P2]
│       └── parquet_processor.py  # Parquet 处理器 [P3]

├── operations/                  # 数据操作模块
│   ├── __init__.py               # [P0]
│   ├── base.py                  # 基础操作抽象类 [P0]
│   ├── crud.py                  # CRUD 操作实现 [P0]
│   ├── importer.py              # 数据导入器 [P0]
│   ├── exporter.py              # 数据导出器 [P0]
│   ├── merger.py                # 数据合并器 [P3]
│   ├── database_manager.py      # 数据库管理器 [P1]
│   └── table_operations.py      # 表操作管理 [P1]
├── geospatial/                  # 地理空间数据处理模块 [P2]
│   ├── __init__.py               # [P2]
│   ├── processor.py             # 地理空间处理器 [P2]
│   ├── polygon_handler.py       # MapInfo .TAB 多边形处理 [P2]
│   ├── coordinate_utils.py      # 坐标验证和转换工具 [P3]
│   └── vendor_tagger.py         # CDR数据Vendor标签自动设置 [P2]
├── utils/                       # 工具和辅助模块
│   ├── __init__.py               # [P0]
│   ├── validators.py            # 数据验证工具 [P0]
│   ├── helpers.py               # 通用辅助函数 [P0]
│   ├── data_cleaner.py          # 数据清洗工具 [P1]
│   ├── performance.py           # 性能监控工具 [P1]
│   ├── cache.py                 # 缓存管理 [P2]
│   ├── security.py              # 安全工具 [P0-基础版]
│   ├── decorators.py            # 装饰器工具 [P1]
│   ├── schema_mapper.py         # Schema映射工具 [P1]
│   ├── memory_optimizer.py      # 内存优化器 [P3]
│   ├── batch_processor.py       # 批处理器 [P1]
│   └── progress_tracker.py      # 进度追踪器 [P2]
├── monitoring/                  # 监控和日志模块
│   ├── __init__.py               # [P0]
│   ├── metrics.py               # 性能指标收集 [P1]
│   ├── logger.py                # 日志管理 [P0]
│   └── metrics.py               # 性能指标收集 [P1]
└── tests/                       # 测试模块
    ├── __init__.py               # [P0]
    ├── conftest.py              # pytest 配置 [P0]
    ├── unit/                    # 单元测试
    │   ├── test_connection.py    # [P0]
    │   ├── test_schema.py        # [P0]
    │   ├── test_etl.py           # [P1]
    │   └── test_operations.py    # [P0]
    ├── integration/             # 集成测试
    │   ├── test_pipeline.py      # [P1]
    │   └── test_end_to_end.py    # [P2]
    └── fixtures/                # 测试数据
        ├── sample_data.csv       # [P0]
        ├── test_config.yaml      # [P0]
        └── mock_polygons.json    # [P2]
```

### 目录结构设计原则

1. **分层架构**: 清晰的分层设计，职责分离
2. **模块化**: 每个模块专注于特定功能领域
3. **可扩展性**: 支持插件化和适配器模式
4. **测试友好**: 完整的测试目录结构
5. **业界标准**: 遵循 Python 项目最佳实践
6. **维护性**: 便于代码维护和团队协作

## 🔧 技术栈选型

### 核心技术栈
- **数据库驱动**: `asyncpg` (高性能异步 PostgreSQL 驱动)
- **ORM 框架**: `SQLAlchemy 2.0+` (异步 ORM 支持)
- **异步框架**: `asyncio` (Python 原生异步支持)
- **数据处理**: `pandas`, `numpy` (数据分析核心库)
- **配置管理**: `pydantic` (数据验证和设置管理)
- **文件处理**: `aiofiles` (异步文件操作)
- **连接池**: `asyncpg.pool` (异步连接池)



### 业界最佳实践参考
- **连接池管理**: 基于 HikariCP 的连接池设计理念
- **异步编程**: 参考 FastAPI 的异步最佳实践
- **数据处理**: 借鉴 Apache Spark 的批处理优化
- **监控体系**: 参考 Prometheus + Grafana 监控模式
- **安全架构**: 参考 OWASP 安全最佳实践

- **依赖注入**: 参考 Spring Framework 的 DI 模式


## 🛡️ 安全性增强

### 1. SQL注入防护机制

#### 参数化查询实现
```python
class SQLInjectionGuard:
    """SQL注入防护类"""
    
    @staticmethod
    def validate_query_params(params: Dict[str, Any]) -> Dict[str, Any]:
        """验证查询参数，防止SQL注入"""
        validated_params = {}
        for key, value in params.items():
            # 参数名验证
            if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', key):
                raise SecurityError(f"Invalid parameter name: {key}")
            
            # 参数值验证
            validated_params[key] = SQLInjectionGuard._sanitize_value(value)
        
        return validated_params
    
    @staticmethod
    def _sanitize_value(value: Any) -> Any:
        """清理参数值"""
        if isinstance(value, str):
            # 移除危险字符
            dangerous_patterns = [
                r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
                r'(--|/\*|\*/|;)',
                r'(\bOR\b.*\b=\b)',
                r'(\bAND\b.*\b=\b)'
            ]
            
            for pattern in dangerous_patterns:
                if re.search(pattern, value, re.IGNORECASE):
                    raise SecurityError(f"Potentially dangerous SQL pattern detected: {value}")
            
            # 转义特殊字符
            return value.replace("'", "''").replace('"', '""')
        
        return value

    @staticmethod
    def build_safe_query(template: str, params: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
        """构建安全的参数化查询"""
        validated_params = SQLInjectionGuard.validate_query_params(params)
        
        # 确保使用参数化查询
        if any(char in template for char in ['%', 'format']):
            raise SecurityError("Use parameterized queries instead of string formatting")
        
        return template, validated_params
```

#### 输入验证机制
```python
class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_schema_name(schema_name: str) -> str:
        """验证Schema名称"""
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', schema_name):
            raise ValidationError(f"Invalid schema name: {schema_name}")
        
        if len(schema_name) > 63:  # PostgreSQL限制
            raise ValidationError("Schema name too long")
        
        return schema_name.lower()
    
    @staticmethod
    def validate_table_name(table_name: str) -> str:
        """验证表名"""
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', table_name):
            raise ValidationError(f"Invalid table name: {table_name}")
        
        if len(table_name) > 63:  # PostgreSQL限制
            raise ValidationError("Table name too long")
        
        return table_name.lower()
    
    @staticmethod
    def validate_column_name(column_name: str) -> str:
        """验证列名"""
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', column_name):
            raise ValidationError(f"Invalid column name: {column_name}")
        
        if len(column_name) > 63:  # PostgreSQL限制
            raise ValidationError("Column name too long")
        
        return column_name.lower()
```


```

## 🔄 错误处理统一化

### 1. 异常处理策略

#### 统一异常框架
```python
class DatabaseError(Exception):
    """数据库基础异常类"""
    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        self.message = message
        self.error_code = error_code or "DB_ERROR"
        self.details = details or {}
        self.timestamp = datetime.utcnow()
        super().__init__(self.message)

class ConnectionError(DatabaseError):
    """连接异常"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "DB_CONNECTION_ERROR", details)

class SecurityError(DatabaseError):
    """安全异常"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "DB_SECURITY_ERROR", details)

class ValidationError(DatabaseError):
    """验证异常"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "DB_VALIDATION_ERROR", details)

class PermissionError(DatabaseError):
    """权限异常"""
    def __init__(self, message: str, details: Dict = None):
        super().__init__(message, "DB_PERMISSION_ERROR", details)


```

#### 错误码体系
```python
class ErrorCodes:
    """错误码定义"""
    
    # 连接相关错误
    CONNECTION_FAILED = "DB_001"
    CONNECTION_TIMEOUT = "DB_002"
    CONNECTION_POOL_EXHAUSTED = "DB_003"
    
    # 安全相关错误
    SQL_INJECTION_DETECTED = "SEC_001"
    PERMISSION_DENIED = "SEC_002"
    AUTHENTICATION_FAILED = "SEC_003"
    ENCRYPTION_FAILED = "SEC_004"
    
    # 验证相关错误
    INVALID_SCHEMA_NAME = "VAL_001"
    INVALID_TABLE_NAME = "VAL_002"
    INVALID_COLUMN_NAME = "VAL_003"
    DATA_VALIDATION_FAILED = "VAL_004"
    
    # 操作相关错误
    QUERY_EXECUTION_FAILED = "OP_001"
    TRANSACTION_FAILED = "OP_002"
    BULK_OPERATION_FAILED = "OP_003"
    

```



### 2. 故障恢复机制

#### 健康检查机制
```python
class HealthChecker:
    """健康检查器"""
    
    def __init__(self, pool, check_interval: int = 30):
        self.pool = pool
        self.check_interval = check_interval
        self.is_healthy = True
        self.last_check = None
        self._check_task = None
    
    async def start_monitoring(self):
        """开始健康监控"""
        self._check_task = asyncio.create_task(self._periodic_check())
    
    async def stop_monitoring(self):
        """停止健康监控"""
        if self._check_task:
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass
    
    async def _periodic_check(self):
        """定期健康检查"""
        while True:
            try:
                await self.check_health()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health check failed: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def check_health(self) -> bool:
        """执行健康检查"""
        try:
            async with self.pool.acquire() as conn:
                await conn.execute("SELECT 1")
            
            self.is_healthy = True
            self.last_check = datetime.utcnow()
            return True
        
        except Exception as e:
            self.is_healthy = False
            self.last_check = datetime.utcnow()
            logger.error(f"Database health check failed: {e}")
            return False
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        return {
            'is_healthy': self.is_healthy,
            'last_check': self.last_check.isoformat() if self.last_check else None,
            'check_interval': self.check_interval
        }
```

## 📊 Schema 映射规则

### 数据源类型到 Schema 映射

```python
SCHEMA_MAPPING = {
    'ep': 'ep_to2',
    'kpi': 'kpi_to2',
    'score': 'score_to2',
    'cfg': 'cfg_to2',
    'nlg': 'nlg_to2',
    'cdr': {
        'Telefonica': 'cdr_to2',
        'Vodafone': 'cdr_vdf',
        'Telekom': 'cdr_tdg'
    },
    'public': 'public'
}
```

### Schema 特性说明
- **ep_to2**: EP 数据存储，支持路测分析
- **kpi_to2**: KPI 指标数据，支持性能分析
- **score_to2**: 评分数据，支持质量评估
- **cfg_to2**: 配置数据，支持参数管理
- **nlg_to2**: NLG 数据，支持自然语言生成
- **cdr_xxx**: CDR 数据，按环境分离存储
- **public**: 系统公共数据和元数据

## 🚀 核心功能模块

### 1. 连接池管理 (Connection Pool)

#### 设计要求
- 使用 `asyncpg.create_pool()` 创建异步连接池
- 连接池配置：最小 5 个，最大 20 个连接
- 支持连接健康检查和自动重连
- 实现连接超时处理机制
- 提供连接池监控和统计信息

#### 核心特性
```python
class DatabasePool:
    async def initialize(self, connection_string: str, **kwargs)
    async def close(self)
    async def get_connection(self)
    async def execute_query(self, query: str, *args)
    async def fetch_all(self, query: str, *args)
    async def fetch_one(self, query: str, *args)
    async def execute_many(self, query: str, args_list: List)
    async def health_check(self) -> bool
    def get_pool_stats(self) -> Dict[str, Any]
```

### 2. Schema 管理 (Schema Management)

#### 功能要求
- 动态创建和删除 Schema
- 自动表结构生成和管理
- 支持表结构版本控制
- 提供 Schema 路由功能
- **Schema 存在性检查**: 如果 Schema 不存在则自动创建，否则跳过创建
- **表操作策略**: 支持替换、不替换、更新、追加等多种数据表操作模式

#### 核心特性
```python
class SchemaManager:
    async def create_schema(self, schema_name: str) -> bool
    async def drop_schema(self, schema_name: str, cascade: bool = False) -> bool
    async def schema_exists(self, schema_name: str) -> bool
    async def ensure_schema_exists(self, schema_name: str) -> bool
    async def list_schemas(self) -> List[str]
    async def create_table_from_dataframe(self, df: pd.DataFrame, 
                                         table_name: str, 
                                         schema_name: str,
                                         if_exists: str = 'fail') -> bool
    async def table_exists(self, table_name: str, schema_name: str) -> bool
    async def handle_table_operation(self, table_name: str, 
                                   schema_name: str, 
                                   operation: str) -> bool
    def get_target_schema(self, data_type: str, 
                         environment: str = 'to2') -> str
```

#### 表操作策略 (Table Operation Strategies)
- **replace**: 删除现有表并重新创建
- **append**: 向现有表追加数据
- **update**: 更新现有表中的数据
- **fail**: 如果表存在则抛出异常
- **skip**: 如果表存在则跳过操作

### 3. 数据清洗 (Data Cleaning)

#### 列名处理规则
1. **小写转换**: 所有列名转换为小写
2. **特殊字符处理**: 非字母数字字符替换为下划线 `_`
3. **长度限制**: 超过 63 字符的列名进行截断
4. **重复处理**: 重复列名添加数字后缀 `_1`, `_2`
5. **保留字处理**: 避免使用 PostgreSQL 保留字

#### 自动列增强
- **主键列**: 自动添加 `id` 列 (SERIAL PRIMARY KEY)
- **时间戳列**: 自动添加 `created_at` 列 (TIMESTAMP WITHOUT TIME ZONE)
- **更新时间**: 可选添加 `updated_at` 列

```python
class DataCleaner:
    def clean_column_names(self, columns: List[str]) -> List[str]
    def add_metadata_columns(self, df: pd.DataFrame) -> pd.DataFrame
    def validate_data_types(self, df: pd.DataFrame) -> pd.DataFrame
    def handle_null_values(self, df: pd.DataFrame, 
                          strategy: str = 'default') -> pd.DataFrame
```

### 4. ETL 数据处理 (ETL Pipeline)

#### 数据提取器 (Extractor)
- 支持多种文件格式：CSV, Excel, JSON, Parquet
- 异步文件读取，支持大文件分块处理
- 并发处理多个文件
- 内存优化，流式读取

#### 数据转换器 (Transformer)
- 列名标准化处理
- 统一数据类型为 TEXT 格式（除主键 "id" 为 BIGINT）
- 空值处理策略
- 数据验证和清洗

#### 数据加载器 (Loader)
- 使用 PostgreSQL COPY 命令高速导入
- 批量插入优化
- 并发加载多张表
- 事务管理和错误恢复

```python
class ETLPipeline:
    async def extract(self, file_path: str, 
                     file_type: str = None) -> pd.DataFrame
    async def transform(self, df: pd.DataFrame, 
                       data_type: str) -> pd.DataFrame
    async def load(self, df: pd.DataFrame, 
                  table_name: str, 
                  schema_name: str) -> int
    async def process_file(self, file_path: str, 
                          data_type: str) -> Dict[str, Any]
```

### 5. CRUD 操作 (CRUD Operations)

#### 基础操作
- 异步增删改查操作
- 批量操作支持
- 条件查询和复杂查询
- 分页查询和排序

#### 高级功能
- 跨 Schema 数据合并
- 动态 SQL 构建
- 查询结果缓存
- 性能监控

```python
class CRUDOperations:
    async def create(self, table_name: str, 
                    data: Dict[str, Any], 
                    schema_name: str) -> int
    async def read(self, table_name: str, 
                  filters: Dict[str, Any], 
                  schema_name: str) -> List[Dict]
    async def update(self, table_name: str, 
                    data: Dict[str, Any], 
                    filters: Dict[str, Any], 
                    schema_name: str) -> int
    async def delete(self, table_name: str, 
                    filters: Dict[str, Any], 
                    schema_name: str) -> int
    async def bulk_insert(self, table_name: str, 
                         data: List[Dict[str, Any]], 
                         schema_name: str) -> List[int]
```

### 6. 数据导入导出 (Import/Export)

#### 导入功能
- 支持多种文件格式导入
- 并发导入处理
- 进度监控和状态反馈
- 错误处理和恢复

#### 导出功能
- 多格式导出：CSV, Excel, JSON, Parquet
- 大数据量分批导出
- 压缩文件支持
- 自定义查询导出

```python
class DataImporter:
    async def import_file(self, file_path: str, 
                         data_type: str, 
                         table_name: str = None) -> Dict[str, Any]
    async def import_dataframe(self, df: pd.DataFrame, 
                              table_name: str, 
                              schema_name: str) -> int
    async def batch_import(self, file_paths: List[str], 
                          data_type: str) -> List[Dict[str, Any]]

class DataExporter:
    async def export_table(self, table_name: str, 
                          schema_name: str, 
                          output_path: str, 
                          format: str = 'csv') -> str
    async def export_query(self, query: str, 
                          output_path: str, 
                          format: str = 'csv') -> str
    async def batch_export(self, tables: List[Tuple[str, str]], 
                          output_dir: str) -> List[str]
```

### 7. 数据库管理 (Database Management)

#### 数据库创建与初始化
- **智能数据库检测**: 自动检测 connect 数据库是否存在，不存在则创建
- **Schema 存在性管理**: 检查 Schema 是否存在，不存在则自动创建
- **表操作策略**: 支持多种表存在时的处理策略
- **版本控制**: 数据库结构版本管理和迁移

```python
class DatabaseManager:
    async def ensure_database_exists(self, db_name: str = 'connect') -> bool
    async def create_database(self, db_name: str, 
                             owner: str = None) -> bool
    async def database_exists(self, db_name: str) -> bool
    async def initialize_schemas(self, schema_list: List[str]) -> Dict[str, bool]
    async def ensure_schema_exists(self, schema_name: str) -> bool
    async def setup_permissions(self, user: str, 
                               schema: str, 
                               permissions: List[str]) -> bool
    async def migrate_database(self, version: str) -> bool
    async def get_database_info(self) -> Dict[str, Any]
    async def validate_database_structure(self) -> Dict[str, Any]
```

#### 表操作管理 (Table Operation Management)
```python
class TableOperationManager:
    async def handle_table_exists(self, 
                                 table_name: str, 
                                 schema_name: str, 
                                 operation: str = 'fail') -> bool
    async def replace_table(self, table_name: str, 
                           schema_name: str, 
                           df: pd.DataFrame) -> bool
    async def append_to_table(self, table_name: str, 
                             schema_name: str, 
                             df: pd.DataFrame) -> int
    async def update_table(self, table_name: str, 
                          schema_name: str, 
                          df: pd.DataFrame, 
                          key_columns: List[str]) -> int
    async def skip_table_operation(self, table_name: str, 
                                  schema_name: str) -> bool
```

### 8. 数据合并操作 (Data Merge Operations)

#### 第一种：Excel配置驱动的智能数据合并

**核心目标**: 通过Excel配置文件定义多个源数据表到统一目标表的列映射关系，实现同一schema下不同数据表的智能合并。

##### 1. 配置驱动的数据映射系统
- **目标**: 通过Excel配置文件定义多个源数据表到统一目标表的列映射关系
- **关键要求**:
  - 读取Excel配置文件（必须包含`Unique_cdr_name`列作为目标列名）
  - 配置文件中每列代表一个源表的列映射规则
  - 支持灵活的列名匹配（大小写不敏感、下划线空格互换）
  - 保持Excel原始列顺序在最终输出中

##### 2. 智能源表发现机制
- **目标**: 自动发现数据库中需要处理的源数据表
- **关键要求**:
  - 扫描指定数据库模式中的所有表
  - 排除特定模式的表（包含20xx、Merge、TRACE_ROUTE等关键词）
  - 基于表名和配置列名进行智能匹配
  - 支持模糊匹配和变体匹配（下划线、空格处理）

##### 3. 数据转换和增强处理
- **目标**: 将各源表数据转换为统一格式并添加元数据
- **关键要求**:
  - 根据配置映射将源表列名转换为目标列名
  - 从源表名提取基准信息（如2024Q3格式）添加到`u_Benchmark`字段
  - 添加`u_Benchmark_service`字段记录数据源表名
  - 处理缺失列（填充None值）
  - 保持数据类型一致性

##### 4. 多维度数据分类和导出
- **目标**: 根据业务规则对合并后的数据进行分类处理
- **关键要求**:
  - **主表**: 完整的合并数据表，基于主表来得到如下各子表
  - **不合格数据**: 筛选包含"NOT QUALIFIED"的记录
  - **P10性能数据**: 基于传输速率阈值分类
    - `HTTP_FDTT_DL`: ≤20Mbps, ≤100Mbps
    - `HTTP_FDTT_UL`: ≤2Mbps, ≤5Mbps
    - `HTTP_FILE_DL`: ≤25Mbps
    - `HTTP_FILE_UL`: ≤15Mbps
  - **清洁数据**: 排除Ping/DNS/TRACE_ROUTE相关数据

#### 第二种：跨Schema同名表合并

**核心目标**: 将多个schemas下相同数据表名的数据表合并到public schema下的统一表中。

##### 核心特点
- **表结构一致性**: 数据表和列名均完全相同
- **跨Schema整合**: 支持三个或多个schemas的数据合并
- **目标Schema**: 统一保存在public schema下
- **数据完整性**: 保持原始数据的完整性和一致性
- **自动化处理**: 自动识别同名表并执行合并操作

```python
class DataMerger:
    """数据合并器 - 支持两种主要合并模式"""
    
    # 第一种：Excel配置驱动的智能数据合并
    async def merge_by_excel_config(self, 
                                   config_path: str,
                                   source_schema: str,
                                   target_table: str = 'merged_data',
                                   target_schema: str = 'public',
                                   exclude_patterns: List[str] = ['20xx', 'Merge', 'TRACE_ROUTE'],
                                   case_sensitive: bool = False,
                                   generate_subtables: bool = True) -> Dict[str, Any]:
        """
        基于Excel配置文件的智能数据合并
        
        Args:
            config_path: Excel配置文件路径（必须包含Unique_cdr_name列）
            source_schema: 源数据schema名称
            target_table: 目标合并表名
            target_schema: 目标schema（默认public）
            exclude_patterns: 排除的表名模式
            case_sensitive: 是否大小写敏感
            generate_subtables: 是否生成分类子表
        
        Returns:
            合并结果统计信息
        """
    
    async def discover_source_tables(self,
                                   schema_name: str,
                                   config_columns: List[str],
                                   exclude_patterns: List[str]) -> List[str]:
        """智能源表发现机制"""
    
    async def transform_and_enhance_data(self,
                                       source_table: str,
                                       column_mapping: Dict[str, str],
                                       benchmark_info: str) -> pd.DataFrame:
        """数据转换和增强处理"""
    
    async def generate_classified_tables(self,
                                        main_table: str,
                                        target_schema: str) -> Dict[str, int]:
        """多维度数据分类和导出"""
    
    # 第二种：跨Schema同名表合并
    async def merge_schemas_by_table_name(self, 
                                         source_schemas: List[str],
                                         target_schema: str = 'public',
                                         table_names: List[str] = None,
                                         auto_discover: bool = True,
                                         preserve_source_info: bool = True) -> Dict[str, Any]:
        """
        跨Schema同名表合并到public schema
        
        Args:
            source_schemas: 源schema列表
            target_schema: 目标schema（默认public）
            table_names: 指定要合并的表名列表（None则自动发现）
            auto_discover: 是否自动发现同名表
            preserve_source_info: 是否保留源schema信息
        
        Returns:
            合并结果统计信息
        """
    
    async def discover_common_tables(self,
                                   schemas: List[str]) -> List[str]:
        """发现多个schema中的同名表"""
    
    async def validate_table_structure(self,
                                      table_name: str,
                                      schemas: List[str]) -> bool:
        """验证表结构一致性"""
    
    async def resolve_merge_conflicts(self, 
                                     conflicts: List[Dict], 
                                     strategy: str = 'latest') -> List[Dict]:
        """合并冲突解决策略"""
```

### 9. 地理空间数据处理 (Geospatial Data Processing)

#### 核心功能概述
基于电信CDR数据的地理空间分析系统，实现**根据数据库表中记录的经纬度坐标，判断该点是否位于指定的多边形区域内，并据此在数据库中设置对应的vendor（供应商）标签字段**。

#### 业务场景
- **输入数据**: PostgreSQL数据库中的CDR（通话详单）表
- **空间数据**: MapInfo .TAB格式的多边形文件（代表不同运营商的覆盖区域）
- **处理目标**: 更新数据库表中的vendor字段
- **处理规模**: 单表记录数百万到千万级别，批处理大小250,000条记录/批次

#### 运营商配置管理
支持多运营商的地理区域配置，包括TO2、VDF、TDG等运营商的覆盖区域定义：

```python
# 运营商配置示例
OPERATORS = {
    "TO2": {
        "schema": "cdr_to2",
        "polygon_file": "TO2 A Region.TAB",
        "inside_vendor": "Huawei",    # 区域内设备供应商
        "outside_vendor": "Nokia"     # 区域外设备供应商
    },
    "VDF": {
        "schema": "cdr_vdf", 
        "polygon_file": "VDF A Region.TAB",
        "inside_vendor": "Huawei",
        "outside_vendor": "Ericsson"
    },
    "TDG": {
        "schema": "cdr_tdg",
        "polygon_file": "TDG A Region 84.TAB", 
        "inside_vendor": "Huawei",
        "outside_vendor": "Ericsson"
    }
}
```

#### 字段映射与表类型识别
- **坐标字段**: 按优先级查找 `(u_Latitude, u_Longitude)`, `(u_Call_Setup_LAT, u_Call_Setup_LON)` 等
- **Vendor字段**:
  - 标准表: `u_Telefonica_Vendor`, `u_Operator_Vendor`
  - M2M表: `u_Side1_Telefonica_Vendor`, `u_Side1_Operator_Vendor`
- **表类型识别**: 通过表名包含"m2m"字符串判断

#### Vendor标签设置规则

**核心业务逻辑**:
1. **TO2 Telefonica_Vendor字段**（所有表都更新）：
   - 点在TO2多边形内 → "Huawei"
   - 点在TO2多边形外 → "Nokia"
   - 坐标无效/缺失 → "undefined"

2. **各运营商Operator_Vendor字段**（仅更新对应schema的表）：
   - 点在运营商自己的多边形内 → "Huawei"
   - 点在运营商自己的多边形外 → "Ericsson"或"Nokia"
   - 坐标无效/缺失 → "undefined"
   - **VDF特殊规则**: 仅当字段为NULL时才更新

#### 核心空间算法
高性能的点在多边形内判断算法，支持大规模数据处理：

```python
def point_in_polygon_check(latitude, longitude, polygon_layer):
    """
    核心逻辑：判断点是否在多边形内
    
    输入验证：
    - 坐标不能为空或0
    - 必须是有效的数值
    
    空间查询：
    1. 使用空间索引快速筛选候选多边形
    2. 对候选多边形进行精确的几何包含判断
    
    返回：True(在内) / False(在外)
    """
```

#### 性能优化策略
- **空间索引**: 使用GiST索引加速空间查询
- **内存缓存**: 将多边形要素缓存到内存
- **批量操作**: 使用临时表+JOIN方式批量更新，避免逐条UPDATE
- **异步处理**: 使用asyncio协调并发任务，支持多表并行处理

```python
class GeospatialProcessor:
    async def load_mapinfo_polygons(self, 
                                   polygon_file: str, 
                                   operator_config: Dict[str, Any]) -> bool
    
    async def point_in_polygon_batch_check(self,
                                          coordinates: List[Tuple[float, float]],
                                          polygon_layer: Any) -> List[bool]
    
    async def update_vendor_tags_batch(self, 
                                      table_name: str, 
                                      schema_name: str, 
                                      batch_results: List[Dict[str, Any]]) -> Dict[str, Any]
    
    async def process_cdr_vendor_tagging(self, 
                                        operator: str,
                                        batch_size: int = 250000) -> Dict[str, Any]
    
    async def discover_coordinate_fields(self,
                                       table_name: str,
                                       schema_name: str) -> Tuple[str, str]
    
    async def discover_vendor_fields(self,
                                   table_name: str,
                                   schema_name: str,
                                   is_m2m_table: bool = False) -> Dict[str, str]
    
    async def validate_coordinates(self, 
                                  coordinates: List[Tuple[float, float]]) -> List[bool]
    
    async def generate_processing_report(self,
                                       processing_results: List[Dict[str, Any]]) -> Dict[str, Any]
```

## ⚡ 性能优化策略

### 1. 数据库层面优化

#### 连接池优化
- 连接池大小：CPU 核心数 × 2 + 1
- 连接超时设置：30 秒
- 空闲连接回收：15 分钟
- 连接预热机制

#### 查询优化
- 使用预编译语句
- 批量操作替代单条操作
- 合理使用索引
- 查询结果缓存
- 地理空间索引：为经纬度字段建立 GiST 或 SP-GiST 索引
- 并行查询：启用并行查询处理大数据量操作

#### 事务优化
- 减少事务持有时间
- 使用适当的隔离级别
- 批量提交策略

### 2. 应用层面优化

#### 内存管理
- 分块处理大数据集
- 及时释放不用的对象
- 使用生成器减少内存占用
- 监控内存使用情况

#### 并发控制
- 使用 `asyncio.Semaphore` 控制并发数（单机环境建议2-4个并发）
- 避免过度并发导致资源竞争

#### 缓存策略
- Schema 元数据缓存
- 基础查询结果缓存
- 地理数据缓存：缓存常用的polygon边界数据

### 3. 数据处理优化

#### 批处理策略
- 批处理大小：1000-10000 条记录
- 使用 PostgreSQL COPY 命令
- 并行处理多个批次

#### 数据类型优化
- 统一使用 TEXT 格式存储所有数据列（除 BIGINT 主键 "id" 外）
- 简化数据类型管理，避免类型转换错误
- 提高数据导入的兼容性和稳定性

#### 地理空间数据优化
- **空间索引**：使用 PostGIS GiST 索引加速空间查询，支持点在多边形内的快速判断
- **内存缓存**：将 MapInfo .TAB 多边形要素缓存到内存，避免重复文件读取
- **批量操作**：使用临时表+JOIN方式批量更新vendor字段，避免逐条UPDATE的性能问题
- **异步处理**：使用asyncio协调并发任务，支持多表并行处理（单机环境建议2-4个并发）
- **批处理策略**：250,000条记录/批次，优化大规模CDR数据处理
- **坐标验证**：预先验证坐标有效性，过滤无效数据减少计算量
- **流式处理**：对大文件采用流式读取和处理，支持百万到千万级别记录处理

## 🔒 安全与质量保障

### 1. 安全措施

#### 数据库安全
- 使用参数化查询防止 SQL 注入
- 输入数据验证和基础安全检查

#### 应用安全
- 输入数据验证
- 错误信息脱敏
- 基础访问日志记录
- 配置文件安全：Excel配置文件的访问控制和验证

### 2. 质量保障

#### 代码质量
- 类型注解覆盖率 > 90%
- 单元测试覆盖率 > 80%
- 代码静态分析
- 代码审查机制

#### 性能标准
- 数据导入速度：> 10MB/s
- 查询响应时间：< 500ms
- 并发支持：20+ 用户
- 系统可用性：99.9%

#### 质量保障
- **数据验证**：输入数据格式和完整性检查
- **事务管理**：确保数据一致性
- **错误处理**：完善的异常处理机制
- **监控告警**：实时监控系统状态
- **测试覆盖**：全面的单元测试和集成测试
- **合并结果验证**：自动验证数据合并的正确性和完整性
- **地理标签准确性**：验证polygon内点判断的准确性
- **配置文件校验**：Excel配置文件格式和内容的严格校验
- **数据库状态检查**：定期检查数据库完整性和一致性

## 📈 监控与运维

### 1. 性能监控

#### 关键指标
- 连接池使用率
- 查询执行时间
- 数据导入导出速度
- 内存使用情况
- 错误率统计

#### 监控工具
- Prometheus 指标收集
- Grafana 可视化展示
- 自定义告警规则
- 性能报告生成

### 2. 日志管理

#### 日志分类
- 操作日志：数据库操作记录
- 错误日志：异常和错误信息
- 性能日志：性能指标记录

#### 日志配置
```python
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        }
    },
    'handlers': {
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'logs/database.log',
            'formatter': 'standard'
        },
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        }
    },
    'loggers': {
        'database': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False
        }
    }
}
```

## 🔧 配置管理

### 1. 数据库配置

```yaml
# config/database.yaml
database:
  host: localhost
  port: 5432
  name: connect
  user: to2
  password: TO2

# Connection pool configuration (单机优化)
pool:
  size: 5
  max_overflow: 10
  timeout: 30
  recycle: 3600

# Table design standards
table_standards:
  naming_convention:
    table: "snake_case"
    column: "snake_case"
    index: "idx_{table}_{column}"

# data sources rules
data_sources:
  ep:
    schema_name: ep_to2
    file_extensions: ['.xlsx', '.xls']
    skip_rows: 1
    header_row: 1
    table_name_pattern: 'ep_{cell_type}_{year}_cw{week}'
  
  cdr:
    file_extensions: ['.xlsx', '.xls']
    skip_rows: 0
    header_row: 0
    table_name_pattern: 'cdr_{year}Q{quarter}_{service_type}'
    sheet_schema_mapping:
      telefonica: cdr_to2
      vodafone: cdr_vdf
      telekom: cdr_tdg
  
  nlg:
    schema_name: nlg_to2
    file_extensions: ['.xlsb']
    sheet_name: 'Techno_2G_4G_5G'
    skip_rows: 4
    header_row: 4
    table_name_pattern: 'nlg_cube_aktuell_{date}'
  
  kpi:
    schema_name: kpi_to2
    file_extensions: ['.xlsx', '.xls', '.csv']
    table_name_pattern: 'kpi_{filename}'
  
  cfg:
    schema_name: cfg_to2
    file_extensions: ['.xml']
    table_name_pattern: 'cfg_{filename}'
  
  score:
    schema_name: score_to2
    file_extensions: ['.xlsx', '.xls', '.csv']
    table_name_pattern: 'score_{filename}'

column_processing:
  normalize_names: true
  lowercase: true
  replace_special_chars: true
  max_length: 63

table_schema:
  primary_key:
    name: id
    type: BIGSERIAL
  timestamps:
    created_at:
      type: TIMESTAMP
      default: CURRENT_TIMESTAMP
  default_column_type: TEXT
```

### 2. 数据源处理规则详解

#### EP 数据源配置
- **Schema**: `ep_to2`
- **支持格式**: Excel (.xlsx, .xls)
- **数据处理**: 跳过第1行，第1行为表头
- **表命名**: `ep_{cell_type}_{year}_cw{week}`
- **用途**: 路测数据分析，支持多种小区类型

#### CDR 数据源配置
- **支持格式**: Excel (.xlsx, .xls)
- **数据处理**: 第0行为表头，无跳过行
- **表命名**: `cdr_{year}Q{quarter}_{service_type}`
- **Sheet（包含）-Schema映射**:
  - Telefonica → `cdr_to2`
  - Vodafone → `cdr_vdf`
  - Telekom → `cdr_tdg`
- **用途**: 通话详单记录分析

#### NLG 数据源配置
- **Schema**: `nlg_to2`
- **支持格式**: Excel Binary (.xlsb)
- **工作表**: `Techno_2G_4G_5G`
- **数据处理**: 跳过前4行，第4行为表头
- **表命名**: `nlg_cube_aktuell_{date}`
- **用途**: 网络日志生成数据分析

#### KPI 数据源配置
- **Schema**: `kpi_to2`
- **支持格式**: Excel (.xlsx, .xls), CSV (.csv)
- **表命名**: `kpi_{filename}`
- **用途**: 关键性能指标监控

#### CFG 数据源配置
- **Schema**: `cfg_to2`
- **支持格式**: XML (.xml), 压缩文件 (.tar.gz)
- **表命名**: `cfg_{filename}`
- **用途**: 配置文件管理
- **压缩格式处理**: 自动解压tar.gz文件并提取其中的XML文件进行处理
- **解压策略**: 支持多XML文件的压缩包，按文件名排序选择第一个文件

#### Score 数据源配置
- **Schema**: `score_to2`
- **支持格式**: Excel (.xlsx, .xls), CSV (.csv)
- **表命名**: `score_{filename}`
- **用途**: 质量评分数据分析

### 3. 列处理规则

#### 列名标准化
- **normalize_names**: 启用列名标准化
- **lowercase**: 转换为小写
- **replace_special_chars**: 替换特殊字符为下划线
- **max_length**: 列名最大长度63字符（PostgreSQL限制）

#### 列名处理示例
```python
# 原始列名 → 处理后列名
"Cell ID" → "cell_id"
"Signal Strength (dBm)" → "signal_strength_dbm"
"Frequency-Band" → "frequency_band"
"2G/3G/4G/5G" → "2g_3g_4g_5g"
```

### 4. 表结构标准

#### 主键设计
- **名称**: `id`
- **类型**: `BIGSERIAL`
- **特点**: 自增长，支持大数据量

#### 时间戳字段
- **created_at**: 记录创建时间
- **类型**: `TIMESTAMP`
- **默认值**: `CURRENT_TIMESTAMP`

#### 默认列类型
- **统一使用**: `TEXT` 类型
- **优势**: 避免类型转换错误，提高兼容性
- **例外**: 主键使用 `BIGSERIAL`
- **Sheet（包含）-Schema映射**:
  - Telefonica → `cdr_to2`
  - Vodafone → `cdr_vdf`
  - Telekom → `cdr_tdg`
### 5. Schema 映射配置

```yaml
# config/schema_mapping.yaml
schema_mapping:
  ep: ep_to2
  kpi: kpi_to2
  score: score_to2
  cfg: cfg_to2
  nlg: nlg_to2
  cdr:
    Telefonica: cdr_to2
    Vodafone: cdr_vdf
    Telekom: cdr_tdg
  public: public

data_types:
  ep:
    description: "EP 路测数据"
    default_schema: "ep_to2"
    auto_create_table: true
  kpi:
    description: "KPI 性能指标数据"
    default_schema: "kpi_to2"
    auto_create_table: true
  nlg:
    description: "NLG 网络日志数据"
    default_schema: "nlg_to2"
    auto_create_table: true
  cdr:
    description: "CDR 通话详单数据"
    default_schema: "cdr_to2"
    auto_create_table: true
    multi_schema: true
  cfg:
    description: "CFG 配置数据"
    default_schema: "cfg_to2"
    auto_create_table: true
  score:
    description: "Score 评分数据"
    default_schema: "score_to2"
    auto_create_table: true
```

## 🔧 配置驱动的数据处理器实现

### 1. 数据源配置处理器

```python
# src/database/config/data_source_processor.py
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path
import yaml

@dataclass
class DataSourceConfig:
    """数据源配置类"""
    schema_name: str
    file_extensions: List[str]
    skip_rows: int = 0
    header_row: int = 0
    table_name_pattern: str = ""
    sheet_name: Optional[str] = None
    sheet_schema_mapping: Optional[Dict[str, str]] = None

class DataSourceConfigManager:
    """数据源配置管理器"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.data_sources = self._parse_data_sources()
        self.column_processing = self.config.get('column_processing', {})
        self.table_standards = self.config.get('table_standards', {})
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _parse_data_sources(self) -> Dict[str, DataSourceConfig]:
        """解析数据源配置"""
        sources = {}
        data_sources_config = self.config.get('data_sources', {})
        
        for source_type, config in data_sources_config.items():
            sources[source_type] = DataSourceConfig(
                schema_name=config.get('schema_name', f"{source_type}_to2"),
                file_extensions=config.get('file_extensions', ['.csv']),
                skip_rows=config.get('skip_rows', 0),
                header_row=config.get('header_row', 0),
                table_name_pattern=config.get('table_name_pattern', f"{source_type}_{{filename}}"),
                sheet_name=config.get('sheet_name'),
                sheet_schema_mapping=config.get('sheet_schema_mapping')
            )
        
        return sources
    
    def get_data_source_config(self, source_type: str) -> DataSourceConfig:
        """获取指定数据源配置"""
        if source_type not in self.data_sources:
            raise ValueError(f"Unknown data source type: {source_type}")
        return self.data_sources[source_type]
    
    def get_schema_for_source(self, source_type: str, sheet_name: Optional[str] = None) -> str:
        """获取数据源对应的Schema"""
        config = self.get_data_source_config(source_type)
        
        # 处理CDR多Schema映射
        if config.sheet_schema_mapping and sheet_name:
            sheet_lower = sheet_name.lower()
            for key, schema in config.sheet_schema_mapping.items():
                if key.lower() in sheet_lower:
                    return schema
        
        return config.schema_name
    
    def generate_table_name(self, source_type: str, **kwargs) -> str:
        """根据配置生成表名"""
        config = self.get_data_source_config(source_type)
        pattern = config.table_name_pattern
        
        try:
            return pattern.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"Missing required parameter for table name generation: {e}")
    
    def is_supported_file(self, source_type: str, file_path: str) -> bool:
        """检查文件是否为支持的格式"""
        config = self.get_data_source_config(source_type)
        file_ext = Path(file_path).suffix.lower()
        return file_ext in config.file_extensions
```

### 2. 列名处理器

```python
# src/database/utils/column_processor.py
import re
from typing import List, Dict

class ColumnNameProcessor:
    """列名处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.normalize_names = config.get('normalize_names', True)
        self.lowercase = config.get('lowercase', True)
        self.replace_special_chars = config.get('replace_special_chars', True)
        self.max_length = config.get('max_length', 63)
    
    def process_column_names(self, columns: List[str]) -> List[str]:
        """批量处理列名"""
        processed = []
        for col in columns:
            processed_name = self.process_single_column(col)
            processed.append(processed_name)
        return processed
    
    def process_single_column(self, column_name: str) -> str:
        """处理单个列名"""
        if not self.normalize_names:
            return column_name
        
        processed = column_name.strip()
        
        # 转换为小写
        if self.lowercase:
            processed = processed.lower()
        
        # 替换特殊字符
        if self.replace_special_chars:
            # 替换空格和特殊字符为下划线
            processed = re.sub(r'[\s\-\(\)\[\]\{\}\/\\]+', '_', processed)
            # 移除开头和结尾的下划线
            processed = processed.strip('_')
            # 合并多个连续下划线
            processed = re.sub(r'_+', '_', processed)
        
        # 确保以字母开头
        if processed and not processed[0].isalpha():
            processed = 'col_' + processed
        
        # 限制长度
        if len(processed) > self.max_length:
            processed = processed[:self.max_length].rstrip('_')
        
        # 确保不为空
        if not processed:
            processed = 'unnamed_column'
        
        return processed
    
    def create_column_mapping(self, original_columns: List[str]) -> Dict[str, str]:
        """创建原始列名到处理后列名的映射"""
        processed_columns = self.process_column_names(original_columns)
        return dict(zip(original_columns, processed_columns))
```

### 3. 表结构生成器

```python
# src/database/schema/table_generator.py
from typing import List, Dict, Any
from sqlalchemy import Column, BigInteger, Text, TIMESTAMP, func
from sqlalchemy.ext.declarative import declarative_base

class TableStructureGenerator:
    """表结构生成器"""
    
    def __init__(self, table_standards: Dict[str, Any]):
        self.table_standards = table_standards
        self.primary_key_config = table_standards.get('primary_key', {})
        self.timestamps_config = table_standards.get('timestamps', {})
        self.default_column_type = table_standards.get('default_column_type', 'TEXT')
    
    def generate_table_ddl(self, table_name: str, columns: List[str], schema_name: str) -> str:
        """生成表创建DDL"""
        ddl_parts = []
        ddl_parts.append(f"CREATE TABLE IF NOT EXISTS {schema_name}.{table_name} (")
        
        # 添加主键
        pk_name = self.primary_key_config.get('name', 'id')
        pk_type = self.primary_key_config.get('type', 'BIGSERIAL')
        ddl_parts.append(f"    {pk_name} {pk_type} PRIMARY KEY,")
        
        # 添加数据列
        for col in columns:
            ddl_parts.append(f"    {col} {self.default_column_type},")
        
        # 添加时间戳字段
        if self.timestamps_config:
            for ts_name, ts_config in self.timestamps_config.items():
                ts_type = ts_config.get('type', 'TIMESTAMP')
                ts_default = ts_config.get('default', '')
                default_clause = f" DEFAULT {ts_default}" if ts_default else ""
                ddl_parts.append(f"    {ts_name} {ts_type}{default_clause},")
        
        # 移除最后一个逗号
        if ddl_parts[-1].endswith(','):
            ddl_parts[-1] = ddl_parts[-1][:-1]
        
        ddl_parts.append(");")
        
        return "\n".join(ddl_parts)
    
    def generate_index_ddl(self, table_name: str, columns: List[str], schema_name: str) -> List[str]:
        """生成索引创建DDL"""
        index_ddls = []
        naming_convention = self.table_standards.get('naming_convention', {})
        index_pattern = naming_convention.get('index', 'idx_{table}_{column}')
        
        for col in columns:
            index_name = index_pattern.format(table=table_name, column=col)
            index_ddl = f"CREATE INDEX IF NOT EXISTS {index_name} ON {schema_name}.{table_name} ({col});"
            index_ddls.append(index_ddl)
        
        return index_ddls
```

### 4. 配置驱动的文件处理器

```python
# src/database/etl/config_driven_processor.py
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Tuple, Optional

class ConfigDrivenFileProcessor:
    """配置驱动的文件处理器"""
    
    def __init__(self, config_manager: DataSourceConfigManager):
        self.config_manager = config_manager
        self.column_processor = ColumnNameProcessor(
            self.config_manager.column_processing
        )
    
    def process_file(self, file_path: str, source_type: str, **kwargs) -> Tuple[pd.DataFrame, str, str]:
        """处理文件并返回DataFrame、Schema名和表名"""
        config = self.config_manager.get_data_source_config(source_type)
        
        # 检查文件格式
        if not self.config_manager.is_supported_file(source_type, file_path):
            raise ValueError(f"Unsupported file format for {source_type}: {file_path}")
        
        # 处理压缩文件
        actual_file_path = file_path
        if file_path.endswith('.tar.gz'):
            actual_file_path = self._extract_compressed_file(file_path, config)
        
        # 读取文件
        df = self._read_file(actual_file_path, config)
        
        # 处理列名
        original_columns = df.columns.tolist()
        processed_columns = self.column_processor.process_column_names(original_columns)
        df.columns = processed_columns
        
        # 确定Schema
        sheet_name = kwargs.get('sheet_name')
        schema_name = self.config_manager.get_schema_for_source(source_type, sheet_name)
        
        # 生成表名
        table_name = self.config_manager.generate_table_name(source_type, **kwargs)
        
        return df, schema_name, table_name
    
    def _read_file(self, file_path: str, config: DataSourceConfig) -> pd.DataFrame:
        """根据配置读取文件"""
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext in ['.xlsx', '.xls']:
            return self._read_excel(file_path, config)
        elif file_ext == '.xlsb':
            return self._read_excel_binary(file_path, config)
        elif file_ext == '.csv':
            return self._read_csv(file_path, config)
        elif file_ext == '.xml':
            return self._read_xml(file_path, config)
        else:
            raise ValueError(f"Unsupported file extension: {file_ext}")
    
    def _read_excel(self, file_path: str, config: DataSourceConfig) -> pd.DataFrame:
        """读取Excel文件"""
        read_kwargs = {
            'skiprows': config.skip_rows,
            'header': config.header_row
        }
        
        if config.sheet_name:
            read_kwargs['sheet_name'] = config.sheet_name
        
        return pd.read_excel(file_path, **read_kwargs)
    
    def _read_excel_binary(self, file_path: str, config: DataSourceConfig) -> pd.DataFrame:
        """读取Excel Binary文件"""
        read_kwargs = {
            'skiprows': config.skip_rows,
            'header': config.header_row,
            'engine': 'pyxlsb'
        }
        
        if config.sheet_name:
            read_kwargs['sheet_name'] = config.sheet_name
        
        return pd.read_excel(file_path, **read_kwargs)
    
    def _read_csv(self, file_path: str, config: DataSourceConfig) -> pd.DataFrame:
        """读取CSV文件"""
        return pd.read_csv(
            file_path,
            skiprows=config.skip_rows,
            header=config.header_row
        )
    
    def _read_xml(self, file_path: str, config: DataSourceConfig) -> pd.DataFrame:
        """读取XML文件"""
        # XML处理逻辑需要根据具体格式实现
        # 这里提供基础框架
        return pd.read_xml(file_path)
    
    def _extract_compressed_file(self, file_path: str, config: DataSourceConfig) -> str:
        """解压缩文件并返回解压后的文件路径"""
        import tarfile
        import tempfile
        import os
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 解压tar.gz文件
            with tarfile.open(file_path, 'r:gz') as tar:
                tar.extractall(temp_dir)
            
            # 查找解压后的XML文件
            extracted_files = []
            for root, dirs, files in os.walk(temp_dir):
                for file in files:
                    if file.endswith('.xml'):
                        extracted_files.append(os.path.join(root, file))
            
            if not extracted_files:
                raise ValueError(f"No XML files found in compressed archive: {file_path}")
            
            if len(extracted_files) > 1:
                # 如果有多个XML文件，选择第一个或根据命名规则选择
                # 这里可以根据具体业务需求调整选择逻辑
                extracted_files.sort()  # 按文件名排序
            
            return extracted_files[0]
            
        except Exception as e:
            # 清理临时目录
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
            raise ValueError(f"Failed to extract compressed file {file_path}: {str(e)}")
```

### 5. 使用示例

```python
# 使用配置驱动处理器的示例
from database.config.data_source_processor import DataSourceConfigManager
from database.etl.config_driven_processor import ConfigDrivenFileProcessor

# 初始化配置管理器
config_manager = DataSourceConfigManager("config/database.yaml")

# 初始化文件处理器
file_processor = ConfigDrivenFileProcessor(config_manager)

# 处理EP数据文件
ep_file = "data/ep/2024/ep_lte_2024_cw05.xlsx"
df, schema, table = file_processor.process_file(
    ep_file, 
    "ep", 
    cell_type="lte", 
    year="2024", 
    week="05"
)
print(f"Schema: {schema}, Table: {table}")
print(f"Columns: {df.columns.tolist()}")

# 处理CDR数据文件
cdr_file = "data/cdr/2024/cdr_2024Q1_voice_telefonica.xlsx"
df, schema, table = file_processor.process_file(
    cdr_file, 
    "cdr", 
    year="2024", 
    quarter="1", 
    service_type="voice",
    sheet_name="telefonica"
)
print(f"Schema: {schema}, Table: {table}")

# 处理NLG数据文件
nlg_file = "data/nlg/2024/nlg_cube_20240315.xlsb"
df, schema, table = file_processor.process_file(
    nlg_file, 
    "nlg", 
    date="20240315"
)
print(f"Schema: {schema}, Table: {table}")

# 处理CFG压缩数据文件
cfg_file = "data/cfg/2024/cfg_network_config_20240315.tar.gz"
df, schema, table = file_processor.process_file(
    cfg_file, 
    "cfg", 
    date="20240315"
)
print(f"Schema: {schema}, Table: {table}")
print(f"CFG data extracted and processed from compressed archive")
```

## 🧪 测试策略

### 1. 单元测试

#### 测试覆盖范围
- 连接池管理测试
- Schema 操作测试
- 数据清洗功能测试
- CRUD 操作测试
- ETL 流程测试
- 配置驱动处理器测试
- 列名处理器测试
- 表结构生成器测试

#### 测试工具
- `pytest` 测试框架
- `pytest-asyncio` 异步测试支持
- `pytest-cov` 覆盖率统计
- `factory_boy` 测试数据生成

### 2. 集成测试

#### 测试场景
- 端到端数据导入流程
- 大数据量性能测试
- 并发操作测试
- 错误恢复测试

#### 测试环境
- 本地测试数据库环境
- 自动化测试流水线

## 📚 使用示例

### 1. 基础使用示例

```python
import asyncio
from src.database import DatabaseManager

async def main():
    # 初始化数据库管理器
    db_manager = DatabaseManager()
    await db_manager.initialize()
    
    try:
        # 导入 EP 数据
        result = await db_manager.import_file(
            file_path="data/ep_data.csv",
            data_type="ep",
            table_name="ep_measurements"
        )
        print(f"导入结果: {result}")
        
        # 查询数据
        data = await db_manager.query(
            schema_name="ep_to2",
            table_name="ep_measurements",
            filters={"date": "2024-01-01"},
            limit=100
        )
        print(f"查询到 {len(data)} 条记录")
        
        # 导出数据
        export_path = await db_manager.export_table(
            schema_name="ep_to2",
            table_name="ep_measurements",
            output_path="export/ep_data.csv",
            format="csv"
        )
        print(f"数据已导出到: {export_path}")
        
    finally:
        await db_manager.close()

if __name__ == "__main__":
    asyncio.run(main())
```

### 数据库管理示例

```python
from src.database import DatabaseManager

# 初始化数据库管理器
db_manager = DatabaseManager()

# 确保connect数据库存在
db_exists = await db_manager.ensure_database_exists('connect')
if not db_exists:
    # 创建数据库
    await db_manager.create_database('connect', owner='connect_user')
    
# 初始化schemas
schemas = ['cdr', 'network', 'performance', 'geo']
result = await db_manager.initialize_schemas(schemas)

# 设置权限
await db_manager.setup_permissions('analyst_user', 'cdr', ['SELECT', 'INSERT'])
```

### 数据合并示例

#### 第一种：Excel配置驱动的智能数据合并示例

```python
from src.database import DataMerger

# 初始化数据合并器
merger = DataMerger()

# Excel配置驱动的智能数据合并
# 配置文件必须包含Unique_cdr_name列作为目标列名
merge_result = await merger.merge_by_excel_config(
    config_path='config/cdr_merge_config.xlsx',  # Excel配置文件路径
    source_schema='cdr_raw',                     # 源数据schema
    target_table='unified_cdr_data',             # 目标合并表名
    target_schema='public',                      # 目标schema
    exclude_patterns=['2024', 'Merge', 'TRACE_ROUTE'],  # 排除的表名模式
    case_sensitive=False,                        # 列名匹配不区分大小写
    generate_subtables=True                      # 生成分类子表
)

print(f"✅ 主表合并完成: {merge_result['main_table_rows']} 行")
print(f"📊 生成子表数量: {len(merge_result['subtables'])}")
print(f"🚫 不合格数据: {merge_result['subtables']['not_qualified_data']} 行")
print(f"⚡ P10性能数据: {merge_result['subtables']['p10_performance_data']} 行")
print(f"🧹 清洁数据: {merge_result['subtables']['clean_data']} 行")

# 智能源表发现
source_tables = await merger.discover_source_tables(
    schema_name='cdr_raw',
    config_columns=['Unique_cdr_name', 'HTTP_FDTT_DL', 'HTTP_FDTT_UL'],
    exclude_patterns=['20xx', 'merge',  'Merge',  'config','TRACE_ROUTE']
)
print(f"🔍 发现源表: {source_tables}")

# 生成分类子表
classified_results = await merger.generate_classified_tables(
    main_table='unified_cdr_data',
    target_schema='public'
)
print(f"📈 分类结果: {classified_results}")
```

#### 第二种：跨Schema同名表合并示例

```python
from src.database import DataMerger

# 初始化数据合并器
merger = DataMerger()

# 跨Schema同名表合并到public schema
schema_merge_result = await merger.merge_schemas_by_table_name(
    source_schemas=['vendor_a_data', 'vendor_b_data', 'vendor_c_data'],  # 源schema列表
    target_schema='public',                      # 目标schema
    table_names=None,                           # 自动发现同名表
    auto_discover=True,                         # 启用自动发现
    preserve_source_info=True                   # 保留源schema信息
)

print(f"✅ 合并完成: {schema_merge_result['total_tables']} 个表")
print(f"📊 总数据行数: {schema_merge_result['total_rows']} 行")
print(f"🔗 合并的表: {schema_merge_result['merged_tables']}")

# 发现多个schema中的同名表
common_tables = await merger.discover_common_tables(
    schemas=['vendor_a_data', 'vendor_b_data', 'vendor_c_data']
)
print(f"🔍 发现同名表: {common_tables}")

# 验证表结构一致性
for table in common_tables:
    is_consistent = await merger.validate_table_structure(
        table_name=table,
        schemas=['vendor_a_data', 'vendor_b_data', 'vendor_c_data']
    )
    print(f"📋 表 {table} 结构一致性: {'✅ 一致' if is_consistent else '❌ 不一致'}")

# 指定特定表进行合并
specific_merge_result = await merger.merge_schemas_by_table_name(
    source_schemas=['vendor_a_data', 'vendor_b_data'],
    target_schema='public',
    table_names=['performance_data', 'quality_metrics'],  # 指定要合并的表
    auto_discover=False,
    preserve_source_info=True
)
```

#### Excel配置文件格式示例

```excel
# merge_config.xlsx 格式示例
| Unique_cdr_name | Table_A_Columns | Table_B_Columns | Table_C_Columns |
|-----------------|-----------------|-----------------|------------------|
| cdr_id          | id              | cdr_identifier  | record_id        |
| timestamp       | time_stamp      | event_time      | timestamp        |
| http_fdtt_dl    | download_speed  | dl_throughput   | fdtt_dl          |
| http_fdtt_ul    | upload_speed    | ul_throughput   | fdtt_ul          |
| location_lat    | latitude        | lat             | location_lat     |
| location_lon    | longitude       | lon             | location_lon     |
| benchmark       | test_period     | benchmark_info  | test_benchmark   |
```

### 地理空间处理示例

```python
from src.database import GeospatialProcessor

# 初始化地理空间处理器
geo_processor = GeospatialProcessor()

# 运营商配置
operators_config = {
    "TO2": {
        "schema": "cdr_to2",
        "polygon_file": "data/TO2 A Region.TAB",
        "inside_vendor": "Huawei",
        "outside_vendor": "Nokia"
    },
    "VDF": {
        "schema": "cdr_vdf", 
        "polygon_file": "data/VDF A Region.TAB",
        "inside_vendor": "Huawei",
        "outside_vendor": "Ericsson"
    },
    "TDG": {
        "schema": "cdr_tdg",
        "polygon_file": "data/TDG A Region 84.TAB", 
        "inside_vendor": "Huawei",
        "outside_vendor": "Ericsson"
    }
}

# 加载MapInfo polygon数据
for operator, config in operators_config.items():
    await geo_processor.load_mapinfo_polygons(
        polygon_file=config['polygon_file'],
        operator_config=config
    )
    print(f"已加载 {operator} 运营商的多边形数据")

# 处理CDR数据的vendor标签
for operator in operators_config.keys():
    processing_result = await geo_processor.process_cdr_vendor_tagging(
        operator=operator,
        batch_size=250000
    )
    print(f"{operator} 处理结果: {processing_result}")

# 发现坐标字段
coord_fields = await geo_processor.discover_coordinate_fields(
    table_name='cdr_data_table',
    schema_name='cdr_to2'
)
print(f"发现坐标字段: {coord_fields}")

# 发现vendor字段
vendor_fields = await geo_processor.discover_vendor_fields(
    table_name='cdr_data_table',
    schema_name='cdr_to2',
    is_m2m_table=False
)
print(f"发现vendor字段: {vendor_fields}")

# 批量坐标验证
test_coordinates = [
    (40.7128, -74.0060),  # 纽约
    (51.5074, -0.1278),   # 伦敦
    (35.6762, 139.6503),  # 东京
    (None, None),         # 无效坐标
    (0, 0)                # 无效坐标
]
valid_coords = await geo_processor.validate_coordinates(test_coordinates)
print(f"坐标验证结果: {valid_coords}")

# 生成处理报告
all_results = []
for operator in operators_config.keys():
    result = await geo_processor.process_cdr_vendor_tagging(operator)
    all_results.append(result)

final_report = await geo_processor.generate_processing_report(all_results)
print(f"最终处理报告: {final_report}")
```

### 2. 批量操作示例

```python
async def batch_operations_example():
    db_manager = DatabaseManager()
    await db_manager.initialize()
    
    try:
        # 批量导入多个文件
        file_paths = [
            "data/kpi_data_1.csv",
            "data/kpi_data_2.csv",
            "data/kpi_data_3.csv"
        ]
        
        results = await db_manager.batch_import(
            file_paths=file_paths,
            data_type="kpi"
        )
        
        for result in results:
            print(f"文件 {result['file_path']} 导入 {result['records_count']} 条记录")
        
        # 批量导出
        tables = [
            ("kpi_to2", "kpi_data_1"),
            ("kpi_to2", "kpi_data_2"),
            ("kpi_to2", "kpi_data_3")
        ]
        
        export_paths = await db_manager.batch_export(
            tables=tables,
            output_dir="export/kpi_batch"
        )
        
        print(f"批量导出完成，文件保存在: {export_paths}")
        
    finally:
        await db_manager.close()
```

### 3. 高级查询示例

```python
async def advanced_query_example():
    db_manager = DatabaseManager()
    await db_manager.initialize()
    
    try:
        # 跨 Schema 查询
        query = """
        SELECT 
            e.cell_id,
            e.rsrp,
            k.throughput,
            s.score
        FROM ep_to2.ep_measurements e
        JOIN kpi_to2.kpi_metrics k ON e.cell_id = k.cell_id
        JOIN score_to2.quality_scores s ON e.cell_id = s.cell_id
        WHERE e.measurement_date >= %s
        AND k.metric_type = %s
        ORDER BY s.score DESC
        LIMIT %s
        """
        
        results = await db_manager.execute_query(
            query=query,
            params=["2024-01-01", "throughput", 100]
        )
        
        print(f"跨 Schema 查询返回 {len(results)} 条记录")
        
        # 聚合查询
        aggregation_query = """
        SELECT 
            DATE_TRUNC('day', measurement_date) as date,
            AVG(rsrp) as avg_rsrp,
            COUNT(*) as measurement_count
        FROM ep_to2.ep_measurements
        WHERE measurement_date >= %s
        GROUP BY DATE_TRUNC('day', measurement_date)
        ORDER BY date
        """
        
        agg_results = await db_manager.execute_query(
            query=aggregation_query,
            params=["2024-01-01"]
        )
        
        print(f"聚合查询返回 {len(agg_results)} 条记录")
        
    finally:
        await db_manager.close()
```

## 🚀 部署与运维

### 1. 部署准备

#### 环境要求
- Python 3.9+
- PostgreSQL 13+ with PostGIS
- 内存：16GB+
- 存储：SSD 推荐
- CPU：4 核心+

#### 依赖安装
```bash
# 安装 Python 依赖
pip install -r requirements.txt

# 安装 PostgreSQL 扩展
psql -d automator -c "CREATE EXTENSION IF NOT EXISTS postgis;"
psql -d automator -c "CREATE EXTENSION IF NOT EXISTS pg_stat_statements;"
```

### 2. 初始化脚本

```python
# scripts/db_init.py
import asyncio
from src.database import DatabaseManager
from src.database.schema.manager import SchemaManager

async def initialize_database():
    """初始化数据库和 Schema"""
    db_manager = DatabaseManager()
    await db_manager.initialize()
    
    schema_manager = SchemaManager(db_manager.pool)
    
    # 创建所有必要的 Schema
    schemas = ['ep_to2', 'kpi_to2', 'score_to2', 'cfg_to2', 
               'nlg_to2', 'cdr_to2', 'cdr_vdf', 'cdr_tdg']
    
    for schema in schemas:
        if not await schema_manager.schema_exists(schema):
            await schema_manager.create_schema(schema)
            print(f"创建 Schema: {schema}")
        else:
            print(f"Schema 已存在: {schema}")
    
    await db_manager.close()
    print("数据库初始化完成")

if __name__ == "__main__":
    asyncio.run(initialize_database())
```

### 3. 监控脚本

```python
# scripts/db_monitor.py
import asyncio
import time
from src.database import DatabaseManager
from src.database.monitoring.metrics import MetricsCollector

async def monitor_database():
    """数据库监控脚本"""
    db_manager = DatabaseManager()
    await db_manager.initialize()
    
    metrics_collector = MetricsCollector(db_manager.pool)
    
    while True:
        try:
            # 收集性能指标
            metrics = await metrics_collector.collect_metrics()
            
            print(f"连接池状态: {metrics['pool_stats']}")
            print(f"活跃连接数: {metrics['active_connections']}")
            print(f"查询统计: {metrics['query_stats']}")
            
            # 检查健康状态
            health_status = await db_manager.health_check()
            print(f"健康状态: {'正常' if health_status else '异常'}")
            
            time.sleep(60)  # 每分钟检查一次
            
        except Exception as e:
            print(f"监控异常: {e}")
            time.sleep(10)

if __name__ == "__main__":
    asyncio.run(monitor_database())
```

## 📋 Claude 4 Sonnet 代码生成提示词

基于以上完整的数据库框架设计方案，以下是用于 Claude 4 Sonnet 生成具体代码的提示词：

### 🎯 主提示词

```
作为一个资深的 Python 数据库架构师和 Connect 电信数据分析系统的首席技术负责人，请基于以下完整的技术规范为我生成生产级的 PostgreSQL 数据库操作框架代码。

**项目背景**:
Connect 是一个电信数据分析与可视化系统，需要处理多种电信数据类型（EP、KPI、CDR、Score、CFG、NLG），支持高性能的数据导入导出、实时分析和可视化展示。系统部署在 ThinkPad P1 单机环境，要求极致的性能优化。

**核心技术要求**:
1. **异步高性能**: 使用 asyncpg + asyncio 实现异步数据库操作
2. **连接池管理**: 最小 5 个，最大 20 个连接，支持健康检查和自动重连
3. **多 Schema 支持**: ep_to2, kpi_to2, score_to2, cfg_to2, nlg_to2, cdr_to2, cdr_vdf, cdr_tdg, public
4. **数据清洗**: 自动列名标准化、特殊字符处理、长度限制、重复列处理
5. **自动增强**: 自动添加 id 主键和 created_at 时间戳列
6. **批量操作**: 支持大数据量的批量导入导出，使用 COPY 命令优化
7. **并发控制**: 支持高并发操作，合理的资源管理
8. **监控体系**: 完整的性能监控、日志记录和错误处理

**性能目标**:
- 数据处理速度: 500万行数据 < 10秒
- 查询响应时间: < 500ms
- 并发支持: 20+ 用户同时操作
- 内存使用: < 16GB (单机优化)
- 系统可用性: 99.9%

**请按以下模块顺序生成完整的生产级代码**:

1. **src/database/config.py** - 配置管理模块
2. **src/database/exceptions.py** - 自定义异常类
3. **src/database/connection/pool.py** - 连接池管理器
4. **src/database/connection/session.py** - 会话管理器
5. **src/database/schema/manager.py** - Schema 管理器
6. **src/database/schema/router.py** - Schema 路由器
7. **src/database/utils/data_cleaner.py** - 数据清洗工具
8. **src/database/utils/validators.py** - 数据验证工具
9. **src/database/etl/extractor.py** - 数据提取器
10. **src/database/etl/transformer.py** - 数据转换器
11. **src/database/etl/loader.py** - 数据加载器
12. **src/database/operations/base.py** - 基础操作类
13. **src/database/operations/crud.py** - CRUD 操作
14. **src/database/operations/importer.py** - 数据导入器
15. **src/database/operations/exporter.py** - 数据导出器
16. **src/database/monitoring/metrics.py** - 性能监控
17. **src/database/monitoring/logger.py** - 日志管理
18. **src/database/__init__.py** - 主入口模块

**代码质量要求**:
- 使用完整的类型注解 (Python 3.9+ typing)
- 遵循 PEP 8 代码规范和最佳实践
- 包含详细的 docstring 文档
- 完善的错误处理和异常管理
- 高性能优化：批量操作、连接复用、内存管理
- 线程安全和异步安全
- 可配置和可扩展的设计
- 完整的单元测试支持

**特殊优化要求**:
1. **单机性能优化**: 针对 ThinkPad P1 环境的 CPU、内存、IO 优化
2. **电信数据特化**: 针对 CDR、路测、KPI 等电信数据的专门优化
3. **地理空间支持**: 集成 PostGIS 进行 MapInfo .TAB 多边形数据处理，实现基于经纬度的 vendor 标签自动设置
4. **实时性能**: 支持实时数据分析和可视化需求
5. **容错机制**: 完善的错误恢复和数据一致性保障

请为每个模块生成完整的、可直接运行的生产级代码，确保代码结构清晰、性能优异、易于维护和扩展。每个文件都应该包含完整的功能实现、错误处理、性能优化和详细的文档说明。
```

### 💡 完整使用示例

#### 数据库初始化和Schema管理示例
```python
import asyncio
from src.database import DatabaseFramework
from src.database.operations.database_manager import DatabaseManager
from src.database.schema.manager import SchemaManager

async def initialize_database_example():
    """数据库初始化示例"""
    # 初始化数据库框架
    db_framework = DatabaseFramework(
        host="localhost",
        port=5432,
        user="to2",
        password="TO2"
    )
    
    # 数据库管理器
    db_manager = DatabaseManager(db_framework.pool)
    
    # 1. 确保 connect 数据库存在
    if not await db_manager.database_exists("connect"):
        await db_manager.create_database("connect")
        print("✅ Connect 数据库已创建")
    else:
        print("ℹ️ Connect 数据库已存在")
    
    # 2. 初始化所有必要的 Schema
    schemas = ['ep_to2', 'kpi_to2', 'score_to2', 'cfg_to2', 
               'nlg_to2', 'cdr_to2', 'cdr_vdf', 'cdr_tdg', 'public']
    
    schema_manager = SchemaManager(db_framework.pool)
    for schema in schemas:
        if not await schema_manager.schema_exists(schema):
            await schema_manager.create_schema(schema)
            print(f"✅ Schema {schema} 已创建")
        else:
            print(f"ℹ️ Schema {schema} 已存在")
    
    await db_framework.close()

# 运行示例
asyncio.run(initialize_database_example())
```

#### 表操作策略示例
```python
import pandas as pd
from src.database.operations.table_operations import TableOperationManager

async def table_operations_example():
    """表操作策略示例"""
    db_framework = DatabaseFramework(connection_string)
    table_manager = TableOperationManager(db_framework.pool)
    
    # 示例数据
    df = pd.DataFrame({
        'cell_id': [1, 2, 3],
        'rsrp': [-85.5, -90.2, -88.1],
        'rsrq': [-12.3, -14.5, -13.2],
        'timestamp': pd.to_datetime(['2024-01-01', '2024-01-02', '2024-01-03'])
    })
    
    table_name = "ep_measurements"
    schema_name = "ep_to2"
    
    # 1. 替换策略：删除现有表并重新创建
    await table_manager.handle_table_exists(
        table_name, schema_name, operation="replace"
    )
    
    # 2. 追加策略：向现有表追加数据
    rows_added = await table_manager.append_to_table(
        table_name, schema_name, df
    )
    print(f"✅ 追加了 {rows_added} 行数据")
    
    # 3. 更新策略：基于主键更新数据
    await table_manager.update_table(
        table_name, schema_name, df, key_columns=['cell_id']
    )
    
    # 4. 跳过策略：如果表存在则跳过操作
    await table_manager.skip_table_operation(table_name, schema_name)
    
    await db_framework.close()
```

#### 数据合并和地理标签示例
```python
from src.database.operations.merger import DataMerger
from src.database.geospatial.processor import GeospatialProcessor

async def advanced_operations_example():
    """高级操作示例"""
    db_framework = DatabaseFramework(connection_string)
    
    # 数据合并器
    merger = DataMerger(db_framework.pool)
    
    # 1. Excel配置驱动的智能数据合并
    merge_result = await merger.merge_by_excel_config(
        config_path="config/cdr_merge_config.xlsx",
        source_schema="ep_to2",
        target_table="unified_ep_analysis",
        target_schema="public",
        exclude_patterns=['2024', 'Merge', 'TRACE_ROUTE'],
        generate_subtables=True
    )
    print(f"✅ 主表合并完成: {merge_result['main_table_rows']} 行")
    print(f"📊 生成子表: {len(merge_result['subtables'])} 个")
    
    # 2. 跨Schema同名表合并
    schema_merge_result = await merger.merge_schemas_by_table_name(
        source_schemas=["cdr_to2", "cdr_vdf", "cdr_tdg"],
        target_schema="public",
        auto_discover=True,
        preserve_source_info=True
    )
    print(f"✅ 跨Schema合并完成: {schema_merge_result['total_tables']} 个表")
    
    # 地理空间处理器
    geo_processor = GeospatialProcessor(db_framework.pool)
    
    # 3. 运营商配置
    operators_config = {
        "TO2": {
            "schema": "cdr_to2",
            "polygon_file": "data/TO2 A Region.TAB",
            "inside_vendor": "Huawei",
            "outside_vendor": "Nokia"
        },
        "VDF": {
            "schema": "cdr_vdf", 
            "polygon_file": "data/VDF A Region.TAB",
            "inside_vendor": "Huawei",
            "outside_vendor": "Ericsson"
        }
    }
    
    # 4. 加载MapInfo polygon数据
    for operator, config in operators_config.items():
        await geo_processor.load_mapinfo_polygons(
            polygon_file=config['polygon_file'],
            operator_config=config
        )
        print(f"已加载 {operator} 运营商的多边形数据")
    
    # 5. CDR数据vendor标签处理
     for operator in operators_config.keys():
         processing_result = await geo_processor.process_cdr_vendor_tagging(
             operator=operator,
             batch_size=250000
         )
         print(f"{operator} vendor标签处理完成: {processing_result['updated_records']} 条记录")
    
    for result in tagging_results:
        print(f"✅ 表 {result['table']} 标签完成: {result['tagged_rows']} 行")
    
    await db_framework.close()
```

### 🔧 分模块提示词

#### 连接池管理提示词
```
请生成 src/database/connection/pool.py 连接池管理模块，要求：

**核心功能**:
1. 使用 asyncpg.create_pool() 创建异步连接池
2. 支持连接池配置：最小 5 个，最大 20 个连接
3. 实现连接健康检查和自动重连机制
4. 提供连接超时处理策略
5. 支持连接池监控和统计信息
6. 实现优雅关闭和资源清理
7. 集成详细的日志记录

**性能优化**:
- 连接预热机制
- 连接复用策略
- 动态连接池大小调整
- 连接泄漏检测

**错误处理**:
- 连接丢失自动重连
- 超时处理机制
- 详细的异常信息记录
- 连接池状态监控

请生成完整的、生产级别的连接池管理代码。
```

#### Schema 管理提示词
```
请生成 src/database/schema/manager.py Schema 管理模块，要求：

**目标 Schemas**: ep_to2, kpi_to2, score_to2, cfg_to2, nlg_to2, cdr_to2, cdr_vdf, cdr_tdg, public

**核心功能**:
1. 异步创建/删除 Schema
2. 检查 Schema 是否存在
3. 列出所有 Schema
4. 根据 pandas DataFrame 动态创建表结构
5. 自动数据类型映射 (pandas -> PostgreSQL)
6. 表结构修改和更新
7. 表删除和重建

**自动增强功能**:
- 为每张表自动添加自增 id 主键
- 自动添加 created_at 时间戳列
- 列名标准化处理

**数据类型映射**:
- 统一将所有数据列映射为 TEXT 类型（除主键 "id" 使用 BIGINT）
- 简化类型转换逻辑，提高导入稳定性
- 保持数据原始格式，避免精度丢失

请生成包含完整功能的 Schema 管理器。
```

#### ETL 数据处理提示词
```
请生成高性能 ETL 数据处理模块，包含 extractor.py, transformer.py, loader.py 三个文件。

**extractor.py 数据提取器要求**:
1. 支持多种文件格式：CSV, Excel, JSON, Parquet
2. 异步文件读取，支持大文件分块处理
3. 并发处理多个文件
4. 内存优化，流式读取
5. 文件格式自动检测
6. 错误文件记录和跳过机制

**transformer.py 数据转换器要求**:
1. 列名清洗转换：小写转换、特殊字符处理、长度限制、重复列名处理
2. 数据类型优化：统一使用 TEXT 格式（除主键 "id" 为 BIGINT）、空值处理策略
3. 性能优化：批处理机制、内存管理、进度监控

**loader.py 数据加载器要求**:
1. 高性能加载：使用 PostgreSQL COPY 命令、批量插入优化、并发加载多张表
2. 事务管理：支持事务批处理、错误回滚机制、部分失败处理
3. 监控和日志：加载进度追踪、性能指标收集、详细错误日志

**通用要求**:
- 支持断点续传
- 内存使用监控
- 可配置的批处理大小
- 完整的异常处理

请生成生产级别的 ETL 处理代码。
```

#### CRUD 操作提示词
```
请生成数据操作模块 src/database/operations/crud.py 和 exporter.py。

**crud.py CRUD 操作要求**:
1. 基础 CRUD：异步增删改查操作、批量操作支持、条件查询和复杂查询、分页查询
2. 跨 Schema 操作：多 Schema 数据合并、跨表关联查询、数据一致性保证
3. 高级功能：动态 SQL 构建、参数化查询防注入、查询结果缓存、查询性能监控

**exporter.py 数据导出要求**:
1. 多格式导出：CSV, Excel, JSON, Parquet 格式、大数据量分批导出、压缩文件支持
2. 并发导出：多表并行导出、内存优化处理、进度监控
3. 查询导出：支持自定义 SQL 查询导出、复杂聚合查询结果导出、跨 Schema 查询导出

**性能优化**:
- 流式导出，减少内存占用
- 异步 IO 优化
- 导出进度回调
- 错误恢复机制

请生成高性能的数据操作代码。
```

#### 主入口模块提示词
```
请生成数据库框架的主入口文件 src/database/__init__.py 和完整的使用示例。

**__init__.py 要求**:
1. 统一的 API 接口导出
2. 框架初始化配置
3. 全局异常处理
4. 日志配置
5. 版本信息管理
6. 遵循 Python 包管理最佳实践

**使用示例要求**:
创建一个 example_usage.py 文件，展示：
1. 基础使用：数据库连接初始化、Schema 创建和管理、数据导入流程
2. 核心功能：批量操作、跨 Schema 查询、健康检查
3. 错误处理：异常捕获、错误恢复、日志记录
4. 最佳实践：资源管理、性能优化、安全考虑

**集成要求**:
- 提供简洁易用的 API 接口
- 支持配置文件和环境变量
- 完整的错误处理和日志记录
- 基础性能统计信息
- 优雅的资源管理和清理
- 符合 PEP 8 编码规范

请生成完整的主入口模块和使用示例代码。
```

## 🚀 MVP 快速开始指南

### P0功能开发重点（1-2周目标）

#### 核心交付物
1. **基础连接管理** (`src/database/connection/` [P0])
   - 实现 `session.py` 的基本连接功能
   - 支持 PostgreSQL 连接配置和基础连接管理
   - 基础的连接健康检查

2. **Schema 基础操作** (`src/database/schema/` [P0])
   - 实现 `manager.py` 的基本 Schema 管理
   - 支持创建、删除、切换 Schema
   - 基础的表结构查询和创建

3. **基本 CRUD 操作** (`src/database/operations/` [P0])
   - 实现 `crud.py` 的基础增删改查
   - 支持简单的 SQL 查询执行
   - 基本的参数化查询（防 SQL 注入）

4. **CSV 数据导入导出** (`src/database/operations/` [P0])
   - 实现 `importer.py` 和 `exporter.py` 的基础版本
   - 支持 CSV 文件导入到数据库
   - 支持查询结果导出为 CSV

5. **基础工具支持** (`src/database/utils/` [P0])
   - 实现 `validators.py` 的基本数据验证
   - 实现 `helpers.py` 的通用辅助函数
   - 实现 `security.py` 的基础安全功能（SQL注入防护）

6. **基础监控日志** (`src/database/monitoring/` [P0])
   - 实现 `logger.py` 的基础日志功能
   - 支持操作日志记录和错误追踪
   - 基础的异常处理框架

#### MVP配置驱动设计
```yaml
# config/database.yaml (P0配置)
features:
  connection_pool: basic          # 简化版连接池
  advanced_security: false       # P2启用
  monitoring: basic              # 基础日志
  etl_pipeline: false           # P1启用
  geospatial: false            # P2启用
  workers: false               # P1启用

connection:
  host: localhost
  port: 5432
  database: connect
  user: to2
  password: TO2
  min_connections: 5
  max_connections: 20            # MVP简化版
  timeout: 30

logging:
  level: INFO
  format: basic
```

#### 成功标准
- ✅ 能够连接 PostgreSQL 数据库
- ✅ 能够管理不同 Schema（创建、切换、删除）
- ✅ 能够导入 CSV 文件到指定表
- ✅ 能够执行基本查询并导出结果
- ✅ 具备基本的错误处理和日志记录
- ✅ 用户能在1小时内完成基础操作培训
- ✅ 支持电信数据的基本导入和查询

#### 团队分工建议
- **Tech Lead**: 架构设计、connection 模块、代码审查
- **Full Stack Developer**: schema + operations 模块
- **Data Scientist**: operations + utils 模块、数据验证
- **Quality Engineer**: tests 模块 + 质量保证、CI/CD
- **UX Designer**: 用户操作流程设计、文档优化

#### 开发里程碑
**第1周**:
- 完成 P0 核心功能：connection、schema、基础 operations
- 建立基础测试框架和监控日志
- 完成基本配置管理

**第2周**:
- 完成 P0 剩余功能：CSV导入导出、数据验证、安全防护
- 实现完整的单元测试覆盖
- 集成测试和基础性能验证

### 后续优先级预览
- **P1功能** (2-3周): 性能优化（完整连接池、查询构建器、ETL流水线、批处理）
- **P2功能** (3-4周): 高级功能（安全增强、地理空间、并发处理、高性能服务）
- **P3功能** (按需): 可选功能（高级监控、数据治理、智能化特性）


## 📝 总结

本数据库框架解决方案整合了多个版本的设计思路，结合业界最佳实践，为 Connect 电信数据分析与可视化系统提供了完整的 PostgreSQL 数据库操作框架。

### 核心优势
1. **渐进式开发**: 通过3个阶段逐步构建完整功能，确保快速交付价值
2. **单机优化**: 针对单机部署环境的性能优化设计
3. **高性能**: 异步操作 + 优化连接池 + 批量处理
4. **模块化**: 清晰的模块化设计 + 配置驱动
5. **易维护**: 简洁的代码结构 + 完整的文档
6. **稳定性**: 完善的错误处理 + 简化监控
7. **专业化**: 针对电信数据的专门优化

### 技术亮点
- 基于 asyncpg 的高性能异步数据库操作
- 智能的 Schema 路由和管理
- 完整的 ETL 数据处理流水线
- 自动化的数据清洗和增强
- 全面的性能监控和日志记录
- 配置驱动的功能模块管理

### 适用场景
- 电信数据分析和可视化
- 大数据量的批量处理
- 数据导入导出和ETL处理
- Schema管理和数据清洗
- 单机高性能数据操作

通过使用提供的 Claude 4 Sonnet 提示词，可以快速生成完整的、生产级的数据库框架代码，满足 Connect 系统的所有技术需求和性能目标。MVP 阶段将在2-3周内交付核心功能，确保项目能够快速启动并获得用户反馈。