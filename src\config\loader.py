"""Configuration Loader

This module provides simple, clean configuration loading using Pydantic models.
Replaces the complex custom configuration framework with a straightforward approach.
"""

import os
import logging
import re
from pathlib import Path
from typing import Dict, Any, Optional, Union

import yaml

from .models import ConnectConfig, Environment

logger = logging.getLogger(__name__)


class ConfigLoader:
    """Simple configuration loader using Pydantic models."""
    
    def __init__(self, config_root: Optional[Union[str, Path]] = None):
        """Initialize the configuration loader.
        
        Args:
            config_root: Root directory for configuration files
        """
        self.config_root = Path(config_root) if config_root else Path.cwd() / "config"
        self.config_root.mkdir(exist_ok=True)
        
    def load_config(
        self, 
        environment: Optional[Environment] = None,
        config_file: Optional[Union[str, Path]] = None
    ) -> ConnectConfig:
        """Load configuration with environment-specific overrides.
        
        Args:
            environment: Target environment (auto-detected if None)
            config_file: Specific config file to load (optional)
            
        Returns:
            ConnectConfig: Validated configuration object
        """
        # Detect environment if not provided
        if environment is None:
            environment = self._detect_environment()
        
        # Load configuration data
        config_data = {}
        
        # 1. Load base configuration
        base_config = self._load_yaml_file(self.config_root / "base.yaml")
        if base_config:
            config_data.update(base_config)
            
        # 2. Load main settings
        settings_config = self._load_yaml_file(self.config_root / "settings.yaml")
        if settings_config:
            config_data = self._deep_merge(config_data, settings_config)
            
        # 3. Load environment-specific configuration
        env_config_file = self.config_root / "environments" / f"{environment.value}.yaml"
        env_config = self._load_yaml_file(env_config_file)
        if env_config:
            config_data = self._deep_merge(config_data, env_config)
            
        # 4. Load specific config file if provided
        if config_file:
            specific_config = self._load_yaml_file(config_file)
            if specific_config:
                config_data = self._deep_merge(config_data, specific_config)
        
        # Set environment in config data
        config_data['environment'] = environment

        # Resolve environment variables in configuration
        config_data = self._resolve_env_variables(config_data)

        # Clean up extra fields that aren't in our Pydantic model
        config_data = self._clean_config_data(config_data)

        # Create and validate configuration using Pydantic
        try:
            config = ConnectConfig(**config_data)
            logger.info(f"Configuration loaded successfully for environment: {environment.value}")
            return config
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            raise ValueError(f"Configuration validation failed: {e}")
    
    def _detect_environment(self) -> Environment:
        """Detect current environment from environment variables.
        
        Returns:
            Environment: Detected environment
        """
        env_value = os.getenv('CONNECT_ENV', os.getenv('ENV', 'development')).lower()
        
        try:
            environment = Environment(env_value)
            logger.info(f"Detected environment: {environment.value}")
            return environment
        except ValueError:
            logger.warning(f"Unknown environment '{env_value}', defaulting to development")
            return Environment.DEVELOPMENT
    
    def _load_yaml_file(self, file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """Load YAML configuration file.
        
        Args:
            file_path: Path to YAML file
            
        Returns:
            Dict containing configuration data or None if file doesn't exist
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            logger.debug(f"Configuration file not found: {file_path}")
            return None
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                logger.debug(f"Loaded configuration from: {file_path}")
                return data or {}
        except Exception as e:
            logger.error(f"Failed to load configuration file {file_path}: {e}")
            raise ValueError(f"Failed to load configuration file {file_path}: {e}")
    
    def _deep_merge(self, base: Dict[str, Any], update: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries.
        
        Args:
            base: Base dictionary
            update: Dictionary to merge into base
            
        Returns:
            Merged dictionary
        """
        result = base.copy()
        
        for key, value in update.items():
            if (key in result and 
                isinstance(result[key], dict) and 
                isinstance(value, dict)):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
                
        return result

    def _resolve_env_variables(self, data: Any) -> Any:
        """Resolve environment variables in configuration data.

        Supports formats like:
        - ${VAR_NAME}
        - ${VAR_NAME:default_value}

        Args:
            data: Configuration data (can be dict, list, or string)

        Returns:
            Configuration data with resolved environment variables
        """
        if isinstance(data, dict):
            return {key: self._resolve_env_variables(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._resolve_env_variables(item) for item in data]
        elif isinstance(data, str):
            # Pattern to match ${VAR_NAME} or ${VAR_NAME:default}
            pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'

            def replace_env_var(match):
                var_name = match.group(1)
                default_value = match.group(2) if match.group(2) is not None else ""
                return os.getenv(var_name, default_value)

            resolved = re.sub(pattern, replace_env_var, data)

            # Try to convert to appropriate type
            if resolved.isdigit():
                return int(resolved)
            elif resolved.lower() in ('true', 'false'):
                return resolved.lower() == 'true'
            elif self._is_float(resolved):
                return float(resolved)
            else:
                return resolved
        else:
            return data

    def _is_float(self, value: str) -> bool:
        """Check if a string represents a valid float (but not a version string).

        Args:
            value: String to check

        Returns:
            True if the string is a valid float
        """
        try:
            # Don't treat version strings like "1.0.0" as floats
            if value.count('.') > 1:
                return False
            float(value)
            return True
        except ValueError:
            return False

    def _clean_config_data(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean configuration data by removing/mapping fields for Pydantic compatibility.

        Args:
            config_data: Raw configuration data

        Returns:
            Cleaned configuration data
        """
        cleaned = config_data.copy()

        # Remove API keys and other extra fields that aren't in our Pydantic model
        extra_fields_to_remove = [
            'anthropic_api_key', 'perplexity_api_key', 'openai_api_key',
            'google_api_key', 'mistral_api_key', 'xai_api_key',
            'azure_openai_api_key', 'ollama_api_key'
        ]

        for field in extra_fields_to_remove:
            cleaned.pop(field, None)

        # Map 'application' to 'project' for backward compatibility
        if 'application' in cleaned:
            cleaned['project'] = cleaned.pop('application')

        return cleaned

    def save_config_template(
        self, 
        file_path: Union[str, Path], 
        environment: Environment = Environment.DEVELOPMENT
    ) -> None:
        """Save a configuration template file.
        
        Args:
            file_path: Path where to save the template
            environment: Environment for the template
        """
        # Create default configuration
        config = ConnectConfig(environment=environment)
        
        # Convert to dictionary
        config_dict = config.model_dump()
        
        # Add comments for documentation
        config_dict['_comments'] = {
            'environment': f'Environment: {environment.value}',
            'project': 'Basic project information',
            'database': 'Database connection settings',
            'logging': 'Logging configuration',
            'geo': 'Geospatial processing settings',
            'telecom': 'Telecommunications domain-specific settings',
            'security': 'Security and authentication settings',
            'monitoring': 'System monitoring configuration'
        }
        
        # Save to file
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(
                    config_dict,
                    f,
                    default_flow_style=False,
                    allow_unicode=True,
                    indent=2,
                    sort_keys=False
                )
            logger.info(f"Configuration template saved to: {file_path}")
        except Exception as e:
            logger.error(f"Failed to save configuration template: {e}")
            raise ValueError(f"Failed to save configuration template: {e}")


# Global configuration instance
_config_instance: Optional[ConnectConfig] = None
_config_loader: Optional[ConfigLoader] = None


def get_config(
    reload: bool = False,
    environment: Optional[Environment] = None,
    config_root: Optional[Union[str, Path]] = None
) -> ConnectConfig:
    """Get the global configuration instance.
    
    Args:
        reload: Force reload of configuration
        environment: Target environment
        config_root: Configuration root directory
        
    Returns:
        ConnectConfig: Global configuration instance
    """
    global _config_instance, _config_loader
    
    if _config_instance is None or reload:
        if _config_loader is None or config_root:
            _config_loader = ConfigLoader(config_root)
        
        _config_instance = _config_loader.load_config(environment)
        
        # Create directories specified in configuration
        _config_instance.create_directories()
    
    return _config_instance


def reset_config() -> None:
    """Reset the global configuration instance."""
    global _config_instance, _config_loader
    _config_instance = None
    _config_loader = None


# Backward compatibility function
def load_config(
    config_path: Optional[str] = None, 
    env: Optional[Environment] = None
) -> ConnectConfig:
    """Load configuration (backward compatibility function).
    
    Args:
        config_path: Configuration file path (legacy parameter)
        env: Environment type
        
    Returns:
        ConnectConfig: Validated configuration object
    """
    return get_config(environment=env, reload=True)
