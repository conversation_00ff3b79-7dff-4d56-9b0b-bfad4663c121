"""Configuration Management Module

Provides Pydantic-based configuration management functionality with backward compatibility.
"""

__version__ = "2.0.0"
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

# New unified configuration system
from .core import (
    ConnectConfigManager,
    get_config_manager,
    get_config,
    load_config,
    reload_config,
    reset_config,
)
from .models import (
    ConnectConfig,
    Environment,
    DatabaseConfig,
    LoggingConfig,
    TelecomConfig,
    SecurityConfig,
    MonitoringConfig,
)
from .loader import ConfigLoader

# Backward compatibility imports
from .environment import EnvironmentManager
from .settings import Settings, ConfigManager

# Compatibility functions
def get_legacy_config():
    """Get configuration in legacy format for backward compatibility."""
    return get_config()

def get_pydantic_config():
    """Get Pydantic configuration object."""
    return get_config()

def migrate_to_unified_config():
    """Migrate to unified configuration system."""
    return get_config_manager()

__all__ = [
    # New unified configuration system
    "ConnectConfigManager",
    "get_config_manager",
    "get_config",
    "load_config",
    "reload_config",
    "reset_config",
    # Configuration models
    "ConnectConfig",
    "Environment",
    "DatabaseConfig",
    "LoggingConfig",
    "TelecomConfig",
    "SecurityConfig",
    "MonitoringConfig",
    # Backward compatibility
    "ConfigLoader",
    "EnvironmentManager",
    "Settings",
    "ConfigManager",
    "get_legacy_config",
    "get_pydantic_config",
    "migrate_to_unified_config",
]
