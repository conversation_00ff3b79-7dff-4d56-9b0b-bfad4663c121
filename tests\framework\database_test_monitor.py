#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 数据库测试监控器
基于docs/database/database-framework.md需求的测试监控系统

本模块提供：
- 实时性能监控
- 资源使用监控
- 数据库连接监控
- 测试执行监控
- 告警和通知
- 监控数据收集和分析
"""

import asyncio
import logging
import time
import threading
import psutil
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import sqlite3
import statistics
from contextlib import contextmanager

try:
    import psycopg2
    from psycopg2 import pool
except ImportError:
    psycopg2 = None
    pool = None

try:
    import pymongo
except ImportError:
    pymongo = None


class MonitorType(Enum):
    """监控类型"""
    SYSTEM = "system"              # 系统监控
    DATABASE = "database"          # 数据库监控
    APPLICATION = "application"    # 应用监控
    NETWORK = "network"            # 网络监控
    CUSTOM = "custom"              # 自定义监控


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"            # 计数器
    GAUGE = "gauge"                # 仪表盘
    HISTOGRAM = "histogram"        # 直方图
    TIMER = "timer"                # 计时器


@dataclass
class MonitorConfig:
    """监控配置"""
    # 基本配置
    enabled: bool = True
    collection_interval: float = 5.0  # 收集间隔（秒）
    retention_period: int = 3600      # 数据保留时间（秒）
    max_data_points: int = 1000       # 最大数据点数
    
    # 系统监控配置
    monitor_cpu: bool = True
    monitor_memory: bool = True
    monitor_disk: bool = True
    monitor_network: bool = True
    monitor_processes: bool = True
    
    # 数据库监控配置
    monitor_connections: bool = True
    monitor_queries: bool = True
    monitor_locks: bool = True
    monitor_transactions: bool = True
    
    # 告警配置
    enable_alerts: bool = True
    cpu_threshold: float = 80.0       # CPU使用率阈值
    memory_threshold: float = 85.0    # 内存使用率阈值
    disk_threshold: float = 90.0      # 磁盘使用率阈值
    connection_threshold: int = 100   # 数据库连接数阈值
    query_timeout_threshold: float = 30.0  # 查询超时阈值
    
    # 存储配置
    storage_backend: str = "sqlite"   # sqlite, file, memory
    storage_path: Optional[str] = None
    
    # 导出配置
    export_enabled: bool = False
    export_interval: int = 300        # 导出间隔（秒）
    export_format: str = "json"       # json, csv, prometheus
    export_path: Optional[str] = None


@dataclass
class MetricData:
    """指标数据"""
    name: str
    value: Union[int, float, str]
    timestamp: datetime
    metric_type: MetricType
    tags: Dict[str, str] = field(default_factory=dict)
    unit: Optional[str] = None
    description: Optional[str] = None


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    metric_name: str
    condition: str  # >, <, >=, <=, ==, !=
    threshold: Union[int, float]
    level: AlertLevel
    enabled: bool = True
    cooldown: int = 300  # 冷却时间（秒）
    message_template: str = "{metric_name} {condition} {threshold}: current value is {value}"
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class Alert:
    """告警"""
    rule_name: str
    metric_name: str
    level: AlertLevel
    message: str
    value: Union[int, float]
    threshold: Union[int, float]
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class SystemMetrics:
    """系统指标"""
    cpu_percent: float
    memory_percent: float
    memory_available: int
    memory_used: int
    disk_percent: float
    disk_free: int
    disk_used: int
    network_bytes_sent: int
    network_bytes_recv: int
    load_average: List[float]
    process_count: int
    timestamp: datetime


@dataclass
class DatabaseMetrics:
    """数据库指标"""
    active_connections: int
    idle_connections: int
    total_connections: int
    queries_per_second: float
    avg_query_time: float
    slow_queries: int
    locks_count: int
    deadlocks_count: int
    transactions_per_second: float
    cache_hit_ratio: float
    timestamp: datetime


class MetricCollector:
    """指标收集器基类"""
    
    def __init__(self, name: str, config: MonitorConfig):
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{name}")
        self.enabled = True
    
    def collect(self) -> List[MetricData]:
        """收集指标数据"""
        raise NotImplementedError
    
    def is_enabled(self) -> bool:
        """检查是否启用"""
        return self.enabled and self.config.enabled


class SystemMetricCollector(MetricCollector):
    """系统指标收集器"""
    
    def __init__(self, config: MonitorConfig):
        super().__init__("system", config)
        self._last_network_stats = None
        self._last_network_time = None
    
    def collect(self) -> List[MetricData]:
        """收集系统指标"""
        metrics = []
        timestamp = datetime.now()
        
        try:
            # CPU指标
            if self.config.monitor_cpu:
                cpu_percent = psutil.cpu_percent(interval=1)
                metrics.append(MetricData(
                    name="system.cpu.percent",
                    value=cpu_percent,
                    timestamp=timestamp,
                    metric_type=MetricType.GAUGE,
                    unit="percent",
                    description="CPU使用率"
                ))
                
                # CPU负载
                try:
                    load_avg = psutil.getloadavg()
                    for i, load in enumerate(load_avg):
                        metrics.append(MetricData(
                            name=f"system.cpu.load_avg_{i+1}m",
                            value=load,
                            timestamp=timestamp,
                            metric_type=MetricType.GAUGE,
                            description=f"{i+1}分钟平均负载"
                        ))
                except (AttributeError, OSError):
                    # Windows系统不支持getloadavg
                    pass
            
            # 内存指标
            if self.config.monitor_memory:
                memory = psutil.virtual_memory()
                metrics.extend([
                    MetricData(
                        name="system.memory.percent",
                        value=memory.percent,
                        timestamp=timestamp,
                        metric_type=MetricType.GAUGE,
                        unit="percent",
                        description="内存使用率"
                    ),
                    MetricData(
                        name="system.memory.available",
                        value=memory.available,
                        timestamp=timestamp,
                        metric_type=MetricType.GAUGE,
                        unit="bytes",
                        description="可用内存"
                    ),
                    MetricData(
                        name="system.memory.used",
                        value=memory.used,
                        timestamp=timestamp,
                        metric_type=MetricType.GAUGE,
                        unit="bytes",
                        description="已用内存"
                    )
                ])
            
            # 磁盘指标
            if self.config.monitor_disk:
                disk = psutil.disk_usage('/')
                metrics.extend([
                    MetricData(
                        name="system.disk.percent",
                        value=disk.percent,
                        timestamp=timestamp,
                        metric_type=MetricType.GAUGE,
                        unit="percent",
                        description="磁盘使用率"
                    ),
                    MetricData(
                        name="system.disk.free",
                        value=disk.free,
                        timestamp=timestamp,
                        metric_type=MetricType.GAUGE,
                        unit="bytes",
                        description="磁盘可用空间"
                    ),
                    MetricData(
                        name="system.disk.used",
                        value=disk.used,
                        timestamp=timestamp,
                        metric_type=MetricType.GAUGE,
                        unit="bytes",
                        description="磁盘已用空间"
                    )
                ])
            
            # 网络指标
            if self.config.monitor_network:
                network = psutil.net_io_counters()
                current_time = time.time()
                
                if self._last_network_stats and self._last_network_time:
                    time_delta = current_time - self._last_network_time
                    bytes_sent_rate = (network.bytes_sent - self._last_network_stats.bytes_sent) / time_delta
                    bytes_recv_rate = (network.bytes_recv - self._last_network_stats.bytes_recv) / time_delta
                    
                    metrics.extend([
                        MetricData(
                            name="system.network.bytes_sent_rate",
                            value=bytes_sent_rate,
                            timestamp=timestamp,
                            metric_type=MetricType.GAUGE,
                            unit="bytes/sec",
                            description="网络发送速率"
                        ),
                        MetricData(
                            name="system.network.bytes_recv_rate",
                            value=bytes_recv_rate,
                            timestamp=timestamp,
                            metric_type=MetricType.GAUGE,
                            unit="bytes/sec",
                            description="网络接收速率"
                        )
                    ])
                
                metrics.extend([
                    MetricData(
                        name="system.network.bytes_sent",
                        value=network.bytes_sent,
                        timestamp=timestamp,
                        metric_type=MetricType.COUNTER,
                        unit="bytes",
                        description="网络发送总字节数"
                    ),
                    MetricData(
                        name="system.network.bytes_recv",
                        value=network.bytes_recv,
                        timestamp=timestamp,
                        metric_type=MetricType.COUNTER,
                        unit="bytes",
                        description="网络接收总字节数"
                    )
                ])
                
                self._last_network_stats = network
                self._last_network_time = current_time
            
            # 进程指标
            if self.config.monitor_processes:
                process_count = len(psutil.pids())
                metrics.append(MetricData(
                    name="system.processes.count",
                    value=process_count,
                    timestamp=timestamp,
                    metric_type=MetricType.GAUGE,
                    description="进程数量"
                ))
        
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
        
        return metrics


class DatabaseMetricCollector(MetricCollector):
    """数据库指标收集器"""
    
    def __init__(self, config: MonitorConfig, db_config: Dict[str, Any]):
        super().__init__("database", config)
        self.db_config = db_config
        self._connection_pool = None
        self._last_query_stats = None
        self._last_query_time = None
    
    def _get_connection(self):
        """获取数据库连接"""
        if self.db_config.get('type') == 'postgresql' and psycopg2:
            if not self._connection_pool:
                self._connection_pool = psycopg2.pool.SimpleConnectionPool(
                    1, 5,
                    host=self.db_config.get('host', 'localhost'),
                    port=self.db_config.get('port', 5432),
                    database=self.db_config.get('database', 'postgres'),
                    user=self.db_config.get('user', 'postgres'),
                    password=self.db_config.get('password', '')
                )
            return self._connection_pool.getconn()
        
        return None
    
    def _return_connection(self, conn):
        """归还数据库连接"""
        if self._connection_pool and conn:
            self._connection_pool.putconn(conn)
    
    def collect(self) -> List[MetricData]:
        """收集数据库指标"""
        metrics = []
        timestamp = datetime.now()
        
        try:
            conn = self._get_connection()
            if not conn:
                return metrics
            
            cursor = conn.cursor()
            
            # 连接数指标
            if self.config.monitor_connections:
                cursor.execute("""
                    SELECT state, count(*) 
                    FROM pg_stat_activity 
                    WHERE datname = current_database()
                    GROUP BY state
                """)
                
                connection_stats = dict(cursor.fetchall())
                active_connections = connection_stats.get('active', 0)
                idle_connections = connection_stats.get('idle', 0)
                total_connections = sum(connection_stats.values())
                
                metrics.extend([
                    MetricData(
                        name="database.connections.active",
                        value=active_connections,
                        timestamp=timestamp,
                        metric_type=MetricType.GAUGE,
                        description="活跃连接数"
                    ),
                    MetricData(
                        name="database.connections.idle",
                        value=idle_connections,
                        timestamp=timestamp,
                        metric_type=MetricType.GAUGE,
                        description="空闲连接数"
                    ),
                    MetricData(
                        name="database.connections.total",
                        value=total_connections,
                        timestamp=timestamp,
                        metric_type=MetricType.GAUGE,
                        description="总连接数"
                    )
                ])
            
            # 查询统计
            if self.config.monitor_queries:
                cursor.execute("""
                    SELECT 
                        sum(calls) as total_calls,
                        sum(total_time) as total_time,
                        avg(mean_time) as avg_time,
                        sum(rows) as total_rows
                    FROM pg_stat_statements
                """)
                
                query_stats = cursor.fetchone()
                if query_stats and query_stats[0]:
                    total_calls, total_time, avg_time, total_rows = query_stats
                    current_time = time.time()
                    
                    if self._last_query_stats and self._last_query_time:
                        time_delta = current_time - self._last_query_time
                        calls_delta = total_calls - self._last_query_stats[0]
                        qps = calls_delta / time_delta if time_delta > 0 else 0
                        
                        metrics.append(MetricData(
                            name="database.queries.per_second",
                            value=qps,
                            timestamp=timestamp,
                            metric_type=MetricType.GAUGE,
                            unit="queries/sec",
                            description="每秒查询数"
                        ))
                    
                    metrics.extend([
                        MetricData(
                            name="database.queries.avg_time",
                            value=avg_time or 0,
                            timestamp=timestamp,
                            metric_type=MetricType.GAUGE,
                            unit="ms",
                            description="平均查询时间"
                        ),
                        MetricData(
                            name="database.queries.total_calls",
                            value=total_calls,
                            timestamp=timestamp,
                            metric_type=MetricType.COUNTER,
                            description="总查询次数"
                        )
                    ])
                    
                    self._last_query_stats = query_stats
                    self._last_query_time = current_time
            
            # 锁统计
            if self.config.monitor_locks:
                cursor.execute("""
                    SELECT mode, count(*) 
                    FROM pg_locks 
                    WHERE database = (SELECT oid FROM pg_database WHERE datname = current_database())
                    GROUP BY mode
                """)
                
                lock_stats = dict(cursor.fetchall())
                total_locks = sum(lock_stats.values())
                
                metrics.append(MetricData(
                    name="database.locks.total",
                    value=total_locks,
                    timestamp=timestamp,
                    metric_type=MetricType.GAUGE,
                    description="总锁数量"
                ))
            
            # 缓存命中率
            cursor.execute("""
                SELECT 
                    sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) * 100 as cache_hit_ratio
                FROM pg_statio_user_tables
                WHERE heap_blks_hit + heap_blks_read > 0
            """)
            
            cache_hit_ratio = cursor.fetchone()[0]
            if cache_hit_ratio:
                metrics.append(MetricData(
                    name="database.cache.hit_ratio",
                    value=cache_hit_ratio,
                    timestamp=timestamp,
                    metric_type=MetricType.GAUGE,
                    unit="percent",
                    description="缓存命中率"
                ))
            
            cursor.close()
            self._return_connection(conn)
        
        except Exception as e:
            self.logger.error(f"收集数据库指标失败: {e}")
        
        return metrics


class TestMetricCollector(MetricCollector):
    """测试指标收集器"""
    
    def __init__(self, config: MonitorConfig):
        super().__init__("test", config)
        self.test_stats = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0,
            'test_duration': 0.0,
            'suite_count': 0
        }
    
    def update_test_stats(self, stats: Dict[str, Any]):
        """更新测试统计"""
        self.test_stats.update(stats)
    
    def collect(self) -> List[MetricData]:
        """收集测试指标"""
        metrics = []
        timestamp = datetime.now()
        
        for metric_name, value in self.test_stats.items():
            metrics.append(MetricData(
                name=f"test.{metric_name}",
                value=value,
                timestamp=timestamp,
                metric_type=MetricType.GAUGE,
                description=f"测试{metric_name}"
            ))
        
        # 计算成功率
        total_tests = self.test_stats.get('total_tests', 0)
        if total_tests > 0:
            success_rate = (self.test_stats.get('passed_tests', 0) / total_tests) * 100
            metrics.append(MetricData(
                name="test.success_rate",
                value=success_rate,
                timestamp=timestamp,
                metric_type=MetricType.GAUGE,
                unit="percent",
                description="测试成功率"
            ))
        
        return metrics


class AlertManager:
    """告警管理器"""
    
    def __init__(self, config: MonitorConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.alerts")
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.last_alert_times: Dict[str, datetime] = {}
        
        # 默认告警规则
        self._setup_default_rules()
    
    def _setup_default_rules(self):
        """设置默认告警规则"""
        default_rules = [
            AlertRule(
                name="high_cpu_usage",
                metric_name="system.cpu.percent",
                condition=">",
                threshold=self.config.cpu_threshold,
                level=AlertLevel.WARNING,
                message_template="CPU使用率过高: {value:.1f}% > {threshold}%"
            ),
            AlertRule(
                name="high_memory_usage",
                metric_name="system.memory.percent",
                condition=">",
                threshold=self.config.memory_threshold,
                level=AlertLevel.WARNING,
                message_template="内存使用率过高: {value:.1f}% > {threshold}%"
            ),
            AlertRule(
                name="high_disk_usage",
                metric_name="system.disk.percent",
                condition=">",
                threshold=self.config.disk_threshold,
                level=AlertLevel.ERROR,
                message_template="磁盘使用率过高: {value:.1f}% > {threshold}%"
            ),
            AlertRule(
                name="too_many_connections",
                metric_name="database.connections.total",
                condition=">",
                threshold=self.config.connection_threshold,
                level=AlertLevel.WARNING,
                message_template="数据库连接数过多: {value} > {threshold}"
            ),
            AlertRule(
                name="slow_query",
                metric_name="database.queries.avg_time",
                condition=">",
                threshold=self.config.query_timeout_threshold * 1000,  # 转换为毫秒
                level=AlertLevel.WARNING,
                message_template="查询响应时间过长: {value:.1f}ms > {threshold}ms"
            )
        ]
        
        for rule in default_rules:
            self.add_rule(rule)
    
    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.rules[rule.name] = rule
        self.logger.info(f"添加告警规则: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除告警规则"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            self.logger.info(f"移除告警规则: {rule_name}")
    
    def enable_rule(self, rule_name: str):
        """启用告警规则"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = True
    
    def disable_rule(self, rule_name: str):
        """禁用告警规则"""
        if rule_name in self.rules:
            self.rules[rule_name].enabled = False
    
    def check_metrics(self, metrics: List[MetricData]):
        """检查指标并触发告警"""
        if not self.config.enable_alerts:
            return
        
        for metric in metrics:
            self._check_metric_against_rules(metric)
    
    def _check_metric_against_rules(self, metric: MetricData):
        """检查单个指标是否触发告警"""
        for rule in self.rules.values():
            if not rule.enabled or rule.metric_name != metric.name:
                continue
            
            # 检查冷却时间
            if self._is_in_cooldown(rule.name):
                continue
            
            # 评估条件
            if self._evaluate_condition(metric.value, rule.condition, rule.threshold):
                self._trigger_alert(rule, metric)
            else:
                # 检查是否需要解决告警
                self._resolve_alert(rule.name)
    
    def _is_in_cooldown(self, rule_name: str) -> bool:
        """检查是否在冷却时间内"""
        if rule_name not in self.last_alert_times:
            return False
        
        rule = self.rules[rule_name]
        last_alert_time = self.last_alert_times[rule_name]
        cooldown_end = last_alert_time + timedelta(seconds=rule.cooldown)
        
        return datetime.now() < cooldown_end
    
    def _evaluate_condition(self, value: Union[int, float], condition: str, threshold: Union[int, float]) -> bool:
        """评估告警条件"""
        try:
            if condition == ">":
                return value > threshold
            elif condition == ">=":
                return value >= threshold
            elif condition == "<":
                return value < threshold
            elif condition == "<=":
                return value <= threshold
            elif condition == "==":
                return value == threshold
            elif condition == "!=":
                return value != threshold
            else:
                self.logger.warning(f"未知的条件操作符: {condition}")
                return False
        except (TypeError, ValueError) as e:
            self.logger.error(f"评估条件时出错: {e}")
            return False
    
    def _trigger_alert(self, rule: AlertRule, metric: MetricData):
        """触发告警"""
        message = rule.message_template.format(
            metric_name=metric.name,
            condition=rule.condition,
            threshold=rule.threshold,
            value=metric.value
        )
        
        alert = Alert(
            rule_name=rule.name,
            metric_name=metric.name,
            level=rule.level,
            message=message,
            value=metric.value,
            threshold=rule.threshold,
            timestamp=metric.timestamp,
            tags=rule.tags.copy()
        )
        
        # 添加到活跃告警
        self.active_alerts[rule.name] = alert
        self.alert_history.append(alert)
        self.last_alert_times[rule.name] = metric.timestamp
        
        self.logger.warning(f"触发告警 [{rule.level.value.upper()}]: {message}")
        
        # 可以在这里添加通知逻辑（邮件、短信、Webhook等）
        self._send_notification(alert)
    
    def _resolve_alert(self, rule_name: str):
        """解决告警"""
        if rule_name in self.active_alerts:
            alert = self.active_alerts[rule_name]
            alert.resolved = True
            alert.resolved_at = datetime.now()
            
            del self.active_alerts[rule_name]
            
            self.logger.info(f"告警已解决: {rule_name}")
    
    def _send_notification(self, alert: Alert):
        """发送通知（可扩展）"""
        # 这里可以实现各种通知方式
        # 例如：邮件、短信、Slack、钉钉等
        pass
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """获取告警历史"""
        return self.alert_history[-limit:]


class MonitorStorage:
    """监控数据存储"""
    
    def __init__(self, config: MonitorConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.storage")
        
        if config.storage_backend == "sqlite":
            self._init_sqlite_storage()
        elif config.storage_backend == "memory":
            self._init_memory_storage()
        else:
            self._init_file_storage()
    
    def _init_sqlite_storage(self):
        """初始化SQLite存储"""
        db_path = self.config.storage_path or "monitor_data.db"
        self.conn = sqlite3.connect(db_path, check_same_thread=False)
        self.conn.execute("""
            CREATE TABLE IF NOT EXISTS metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                value REAL NOT NULL,
                timestamp TEXT NOT NULL,
                metric_type TEXT NOT NULL,
                unit TEXT,
                description TEXT,
                tags TEXT
            )
        """)
        self.conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_metrics_name_timestamp 
            ON metrics(name, timestamp)
        """)
        self.conn.commit()
    
    def _init_memory_storage(self):
        """初始化内存存储"""
        self.memory_data = defaultdict(lambda: deque(maxlen=self.config.max_data_points))
    
    def _init_file_storage(self):
        """初始化文件存储"""
        self.file_path = Path(self.config.storage_path or "monitor_data.jsonl")
        self.file_path.parent.mkdir(parents=True, exist_ok=True)
    
    def store_metrics(self, metrics: List[MetricData]):
        """存储指标数据"""
        if self.config.storage_backend == "sqlite":
            self._store_metrics_sqlite(metrics)
        elif self.config.storage_backend == "memory":
            self._store_metrics_memory(metrics)
        else:
            self._store_metrics_file(metrics)
    
    def _store_metrics_sqlite(self, metrics: List[MetricData]):
        """存储到SQLite"""
        try:
            cursor = self.conn.cursor()
            for metric in metrics:
                cursor.execute("""
                    INSERT INTO metrics (name, value, timestamp, metric_type, unit, description, tags)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    metric.name,
                    metric.value,
                    metric.timestamp.isoformat(),
                    metric.metric_type.value,
                    metric.unit,
                    metric.description,
                    json.dumps(metric.tags)
                ))
            self.conn.commit()
        except Exception as e:
            self.logger.error(f"存储指标到SQLite失败: {e}")
    
    def _store_metrics_memory(self, metrics: List[MetricData]):
        """存储到内存"""
        for metric in metrics:
            self.memory_data[metric.name].append({
                'value': metric.value,
                'timestamp': metric.timestamp.isoformat(),
                'metric_type': metric.metric_type.value,
                'unit': metric.unit,
                'description': metric.description,
                'tags': metric.tags
            })
    
    def _store_metrics_file(self, metrics: List[MetricData]):
        """存储到文件"""
        try:
            with open(self.file_path, 'a', encoding='utf-8') as f:
                for metric in metrics:
                    data = {
                        'name': metric.name,
                        'value': metric.value,
                        'timestamp': metric.timestamp.isoformat(),
                        'metric_type': metric.metric_type.value,
                        'unit': metric.unit,
                        'description': metric.description,
                        'tags': metric.tags
                    }
                    f.write(json.dumps(data, ensure_ascii=False) + '\n')
        except Exception as e:
            self.logger.error(f"存储指标到文件失败: {e}")
    
    def get_metrics(self, metric_name: str, start_time: Optional[datetime] = None, 
                   end_time: Optional[datetime] = None, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取指标数据"""
        if self.config.storage_backend == "sqlite":
            return self._get_metrics_sqlite(metric_name, start_time, end_time, limit)
        elif self.config.storage_backend == "memory":
            return self._get_metrics_memory(metric_name, start_time, end_time, limit)
        else:
            return self._get_metrics_file(metric_name, start_time, end_time, limit)
    
    def _get_metrics_sqlite(self, metric_name: str, start_time: Optional[datetime], 
                           end_time: Optional[datetime], limit: int) -> List[Dict[str, Any]]:
        """从SQLite获取指标"""
        try:
            cursor = self.conn.cursor()
            query = "SELECT * FROM metrics WHERE name = ?"
            params = [metric_name]
            
            if start_time:
                query += " AND timestamp >= ?"
                params.append(start_time.isoformat())
            
            if end_time:
                query += " AND timestamp <= ?"
                params.append(end_time.isoformat())
            
            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            columns = ['id', 'name', 'value', 'timestamp', 'metric_type', 'unit', 'description', 'tags']
            return [dict(zip(columns, row)) for row in rows]
        
        except Exception as e:
            self.logger.error(f"从SQLite获取指标失败: {e}")
            return []
    
    def _get_metrics_memory(self, metric_name: str, start_time: Optional[datetime], 
                           end_time: Optional[datetime], limit: int) -> List[Dict[str, Any]]:
        """从内存获取指标"""
        if metric_name not in self.memory_data:
            return []
        
        data = list(self.memory_data[metric_name])
        
        # 时间过滤
        if start_time or end_time:
            filtered_data = []
            for item in data:
                timestamp = datetime.fromisoformat(item['timestamp'])
                if start_time and timestamp < start_time:
                    continue
                if end_time and timestamp > end_time:
                    continue
                filtered_data.append(item)
            data = filtered_data
        
        # 限制数量
        return data[-limit:] if limit else data
    
    def _get_metrics_file(self, metric_name: str, start_time: Optional[datetime], 
                         end_time: Optional[datetime], limit: int) -> List[Dict[str, Any]]:
        """从文件获取指标"""
        if not self.file_path.exists():
            return []
        
        try:
            data = []
            with open(self.file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        item = json.loads(line.strip())
                        if item['name'] != metric_name:
                            continue
                        
                        timestamp = datetime.fromisoformat(item['timestamp'])
                        if start_time and timestamp < start_time:
                            continue
                        if end_time and timestamp > end_time:
                            continue
                        
                        data.append(item)
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue
            
            return data[-limit:] if limit else data
        
        except Exception as e:
            self.logger.error(f"从文件获取指标失败: {e}")
            return []
    
    def cleanup_old_data(self):
        """清理过期数据"""
        cutoff_time = datetime.now() - timedelta(seconds=self.config.retention_period)
        
        if self.config.storage_backend == "sqlite":
            try:
                cursor = self.conn.cursor()
                cursor.execute("DELETE FROM metrics WHERE timestamp < ?", (cutoff_time.isoformat(),))
                self.conn.commit()
                self.logger.info(f"清理了 {cursor.rowcount} 条过期数据")
            except Exception as e:
                self.logger.error(f"清理SQLite数据失败: {e}")
        
        elif self.config.storage_backend == "memory":
            # 内存存储使用deque自动限制大小，不需要手动清理
            pass
    
    def close(self):
        """关闭存储"""
        if hasattr(self, 'conn'):
            self.conn.close()


class DatabaseTestMonitor:
    """数据库测试监控器主类"""
    
    def __init__(self, config: Optional[MonitorConfig] = None, db_config: Optional[Dict[str, Any]] = None):
        self.config = config or MonitorConfig()
        self.db_config = db_config or {}
        self.logger = logging.getLogger(__name__)
        
        # 核心组件
        self.collectors: List[MetricCollector] = []
        self.alert_manager = AlertManager(self.config)
        self.storage = MonitorStorage(self.config)
        
        # 运行状态
        self.is_running = False
        self.collection_thread: Optional[threading.Thread] = None
        self.cleanup_thread: Optional[threading.Thread] = None
        
        # 初始化收集器
        self._init_collectors()
        
        # 回调函数
        self.metric_callbacks: List[Callable[[List[MetricData]], None]] = []
        self.alert_callbacks: List[Callable[[Alert], None]] = []
    
    def _init_collectors(self):
        """初始化指标收集器"""
        # 系统指标收集器
        if self.config.monitor_cpu or self.config.monitor_memory or \
           self.config.monitor_disk or self.config.monitor_network:
            self.collectors.append(SystemMetricCollector(self.config))
        
        # 数据库指标收集器
        if self.db_config and (self.config.monitor_connections or 
                              self.config.monitor_queries or 
                              self.config.monitor_locks):
            self.collectors.append(DatabaseMetricCollector(self.config, self.db_config))
        
        # 测试指标收集器
        self.test_collector = TestMetricCollector(self.config)
        self.collectors.append(self.test_collector)
    
    def add_metric_callback(self, callback: Callable[[List[MetricData]], None]):
        """添加指标回调"""
        self.metric_callbacks.append(callback)
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def start(self):
        """启动监控"""
        if self.is_running:
            self.logger.warning("监控已经在运行中")
            return
        
        self.logger.info("启动数据库测试监控")
        self.is_running = True
        
        # 启动数据收集线程
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
        
        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
    
    def stop(self):
        """停止监控"""
        if not self.is_running:
            return
        
        self.logger.info("停止数据库测试监控")
        self.is_running = False
        
        # 等待线程结束
        if self.collection_thread and self.collection_thread.is_alive():
            self.collection_thread.join(timeout=5)
        
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=5)
        
        # 关闭存储
        self.storage.close()
    
    def _collection_loop(self):
        """数据收集循环"""
        while self.is_running:
            try:
                start_time = time.time()
                
                # 收集所有指标
                all_metrics = []
                for collector in self.collectors:
                    if collector.is_enabled():
                        try:
                            metrics = collector.collect()
                            all_metrics.extend(metrics)
                        except Exception as e:
                            self.logger.error(f"收集器 {collector.name} 收集指标失败: {e}")
                
                if all_metrics:
                    # 存储指标
                    self.storage.store_metrics(all_metrics)
                    
                    # 检查告警
                    self.alert_manager.check_metrics(all_metrics)
                    
                    # 调用回调
                    for callback in self.metric_callbacks:
                        try:
                            callback(all_metrics)
                        except Exception as e:
                            self.logger.error(f"指标回调执行失败: {e}")
                
                # 计算睡眠时间
                collection_time = time.time() - start_time
                sleep_time = max(0, self.config.collection_interval - collection_time)
                
                if sleep_time > 0:
                    time.sleep(sleep_time)
            
            except Exception as e:
                self.logger.error(f"数据收集循环异常: {e}")
                time.sleep(self.config.collection_interval)
    
    def _cleanup_loop(self):
        """清理循环"""
        while self.is_running:
            try:
                # 每小时清理一次过期数据
                time.sleep(3600)
                if self.is_running:
                    self.storage.cleanup_old_data()
            except Exception as e:
                self.logger.error(f"清理循环异常: {e}")
    
    def update_test_stats(self, stats: Dict[str, Any]):
        """更新测试统计"""
        if hasattr(self, 'test_collector'):
            self.test_collector.update_test_stats(stats)
    
    def get_metrics(self, metric_name: str, start_time: Optional[datetime] = None, 
                   end_time: Optional[datetime] = None, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取指标数据"""
        return self.storage.get_metrics(metric_name, start_time, end_time, limit)
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return self.alert_manager.get_active_alerts()
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """获取告警历史"""
        return self.alert_manager.get_alert_history(limit)
    
    def add_alert_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.alert_manager.add_rule(rule)
    
    def remove_alert_rule(self, rule_name: str):
        """移除告警规则"""
        self.alert_manager.remove_rule(rule_name)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态摘要"""
        # 获取最新的系统指标
        cpu_metrics = self.get_metrics("system.cpu.percent", limit=1)
        memory_metrics = self.get_metrics("system.memory.percent", limit=1)
        disk_metrics = self.get_metrics("system.disk.percent", limit=1)
        
        # 获取数据库指标
        db_connections = self.get_metrics("database.connections.total", limit=1)
        db_queries = self.get_metrics("database.queries.per_second", limit=1)
        
        # 获取测试指标
        test_success_rate = self.get_metrics("test.success_rate", limit=1)
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system': {
                'cpu_percent': cpu_metrics[0]['value'] if cpu_metrics else None,
                'memory_percent': memory_metrics[0]['value'] if memory_metrics else None,
                'disk_percent': disk_metrics[0]['value'] if disk_metrics else None
            },
            'database': {
                'connections': db_connections[0]['value'] if db_connections else None,
                'queries_per_second': db_queries[0]['value'] if db_queries else None
            },
            'test': {
                'success_rate': test_success_rate[0]['value'] if test_success_rate else None
            },
            'alerts': {
                'active_count': len(self.get_active_alerts()),
                'active_alerts': [alert.message for alert in self.get_active_alerts()]
            },
            'monitoring': {
                'is_running': self.is_running,
                'collectors_count': len(self.collectors),
                'collection_interval': self.config.collection_interval
            }
        }
    
    @contextmanager
    def monitor_context(self):
        """监控上下文管理器"""
        self.start()
        try:
            yield self
        finally:
            self.stop()


def create_test_monitor(config: Optional[MonitorConfig] = None, 
                       db_config: Optional[Dict[str, Any]] = None) -> DatabaseTestMonitor:
    """创建测试监控器的工厂函数"""
    return DatabaseTestMonitor(config, db_config)


if __name__ == "__main__":
    # 示例用法和CLI接口
    import argparse
    import signal
    import sys
    
    def setup_logging(level: str = "INFO"):
        """设置日志"""
        logging.basicConfig(
            level=getattr(logging, level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('database_test_monitor.log')
            ]
        )
    
    def signal_handler(signum, frame):
        """信号处理器"""
        print("\n接收到停止信号，正在关闭监控...")
        if 'monitor' in globals():
            monitor.stop()
        sys.exit(0)
    
    parser = argparse.ArgumentParser(description="数据库测试监控器")
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--interval', type=float, default=5.0, help='收集间隔（秒）')
    parser.add_argument('--retention', type=int, default=3600, help='数据保留时间（秒）')
    parser.add_argument('--storage', choices=['sqlite', 'memory', 'file'], 
                       default='sqlite', help='存储后端')
    parser.add_argument('--storage-path', help='存储路径')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='日志级别')
    parser.add_argument('--no-alerts', action='store_true', help='禁用告警')
    parser.add_argument('--db-host', default='localhost', help='数据库主机')
    parser.add_argument('--db-port', type=int, default=5432, help='数据库端口')
    parser.add_argument('--db-name', default='postgres', help='数据库名称')
    parser.add_argument('--db-user', default='postgres', help='数据库用户')
    parser.add_argument('--db-password', default='', help='数据库密码')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建配置
    config = MonitorConfig(
        collection_interval=args.interval,
        retention_period=args.retention,
        storage_backend=args.storage,
        storage_path=args.storage_path,
        enable_alerts=not args.no_alerts
    )
    
    # 数据库配置
    db_config = {
        'type': 'postgresql',
        'host': args.db_host,
        'port': args.db_port,
        'database': args.db_name,
        'user': args.db_user,
        'password': args.db_password
    }
    
    # 创建监控器
    monitor = create_test_monitor(config, db_config)
    
    # 添加回调示例
    def metric_callback(metrics: List[MetricData]):
        for metric in metrics:
            if metric.name == "system.cpu.percent" and metric.value > 90:
                print(f"高CPU使用率警告: {metric.value}%")
    
    def alert_callback(alert: Alert):
        print(f"告警: [{alert.level.value.upper()}] {alert.message}")
    
    monitor.add_metric_callback(metric_callback)
    monitor.add_alert_callback(alert_callback)
    
    # 启动监控
    try:
        print("启动数据库测试监控器...")
        print(f"收集间隔: {config.collection_interval}秒")
        print(f"存储后端: {config.storage_backend}")
        print("按 Ctrl+C 停止监控")
        
        with monitor.monitor_context():
            # 定期显示状态
            while True:
                time.sleep(30)  # 每30秒显示一次状态
                status = monitor.get_system_status()
                print(f"\n=== 系统状态 ({status['timestamp']}) ===")
                print(f"CPU: {status['system']['cpu_percent']:.1f}%" if status['system']['cpu_percent'] else "CPU: N/A")
                print(f"内存: {status['system']['memory_percent']:.1f}%" if status['system']['memory_percent'] else "内存: N/A")
                print(f"磁盘: {status['system']['disk_percent']:.1f}%" if status['system']['disk_percent'] else "磁盘: N/A")
                print(f"活跃告警: {status['alerts']['active_count']}")
                
                if status['alerts']['active_alerts']:
                    for alert_msg in status['alerts']['active_alerts']:
                        print(f"  - {alert_msg}")
    
    except KeyboardInterrupt:
        print("\n用户中断，正在停止监控...")
    except Exception as e:
        print(f"\n监控运行异常: {e}")
        sys.exit(1)
    
    print("监控已停止")