"""Tests for the unified configuration system.

This module tests the new ConnectConfigManager and ensures
backward compatibility with existing code.

Author: Vincent.Li
Email: <EMAIL>
"""

import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import patch, MagicMock

from src.config import (
    get_config_manager,
    get_config,
    load_config,
    reload_config,
    reset_config,
    get_legacy_config,
    get_pydantic_config
)
from src.config.core import ConnectConfigManager
from src.config.models import ConnectConfig


class TestConfigSystem:
    """Test suite for the configuration system."""
    
    def setup_method(self):
        """Setup test environment."""
        # Reset the singleton instance before each test
        reset_config()
    
    def teardown_method(self):
        """Cleanup after each test."""
        # Reset the singleton instance after each test
        reset_config()
    
    def test_singleton_behavior(self):
        """Test that ConnectConfigManager follows singleton pattern."""
        manager1 = get_config_manager()
        manager2 = get_config_manager()
        
        assert manager1 is manager2
        assert isinstance(manager1, ConnectConfigManager)
    
    def test_config_loading(self):
        """Test basic configuration loading."""
        config = get_config()
        
        assert isinstance(config, ConnectConfig)
        assert hasattr(config, 'database')
        assert hasattr(config, 'project')
        assert hasattr(config, 'logging')
    
    def test_config_reload(self):
        """Test configuration reloading."""
        config1 = get_config()
        config2 = reload_config()
        
        assert isinstance(config1, ConnectConfig)
        assert isinstance(config2, ConnectConfig)
        # They should be the same instance due to singleton
        assert config1 is config2
    
    def test_database_config_access(self):
        """Test database configuration access."""
        config = get_config()
        db_config = config.database
        
        assert hasattr(db_config, 'host')
        assert hasattr(db_config, 'port')
        assert hasattr(db_config, 'name')
        assert hasattr(db_config, 'user')
    
    def test_environment_detection(self):
        """Test environment detection."""
        manager = get_config_manager()
        
        # Test default environment
        env = manager.detect_environment()
        assert env in ['development', 'production', 'testing']
        
        # Test with environment variable
        with patch.dict(os.environ, {'CONNECT_ENV': 'testing'}):
            env = manager.detect_environment()
            assert env == 'testing'
    
    def test_config_validation(self):
        """Test configuration validation."""
        config = get_config()
        
        # Test that config passes validation
        assert config is not None
        
        # Test database config validation
        db_config = config.database
        assert 1 <= db_config.port <= 65535
        assert db_config.host is not None
        assert db_config.name is not None
    
    def test_backward_compatibility_functions(self):
        """Test backward compatibility functions."""
        # Test legacy config access
        legacy_config = get_legacy_config()
        assert legacy_config is not None
        
        # Test pydantic config access
        pydantic_config = get_pydantic_config()
        assert isinstance(pydantic_config, ConnectConfig)
    
    def test_config_manager_methods(self):
        """Test ConnectConfigManager methods."""
        manager = get_config_manager()
        
        # Test get_config method
        config = manager.get_config()
        assert isinstance(config, ConnectConfig)
        
        # Test reload method
        reloaded_config = manager.reload()
        assert isinstance(reloaded_config, ConnectConfig)
        
        # Test is_loaded property
        assert manager.is_loaded
    
    def test_environment_specific_config(self):
        """Test environment-specific configuration loading."""
        with patch.dict(os.environ, {'CONNECT_ENV': 'testing'}):
            reset_config()  # Reset to pick up new environment
            config = get_config()
            
            # Should still be a valid config
            assert isinstance(config, ConnectConfig)
            assert hasattr(config, 'database')
    
    def test_config_file_paths(self):
        """Test configuration file path resolution."""
        manager = get_config_manager()
        
        # Test base config path
        base_path = manager._get_config_path('base.yaml')
        assert base_path.name == 'base.yaml'
        
        # Test environment config path
        env_path = manager._get_environment_config_path('development')
        assert 'development' in str(env_path)
    
    def test_error_handling(self):
        """Test error handling in configuration loading."""
        # Test with invalid config directory
        with patch('src.config.core.Path') as mock_path:
            mock_path.return_value.exists.return_value = False
            
            # Should still return a config (with defaults)
            config = get_config()
            assert isinstance(config, ConnectConfig)
    
    def test_hot_reload_capability(self):
        """Test hot reload capability."""
        manager = get_config_manager()
        
        # Get initial config
        config1 = manager.get_config()
        
        # Force reload
        config2 = manager.reload()
        
        # Should be the same instance (singleton)
        assert config1 is config2
        assert isinstance(config2, ConnectConfig)
    
    def test_config_serialization(self):
        """Test configuration serialization."""
        config = get_config()
        
        # Test model_dump
        config_dict = config.model_dump()
        assert isinstance(config_dict, dict)
        assert 'database' in config_dict
        assert 'project' in config_dict
        
        # Test model_dump_json
        config_json = config.model_dump_json()
        assert isinstance(config_json, str)
        assert 'database' in config_json


class TestBackwardCompatibility:
    """Test backward compatibility with existing code."""
    
    def setup_method(self):
        """Setup test environment."""
        reset_config()
    
    def teardown_method(self):
        """Cleanup after each test."""
        reset_config()
    
    def test_database_config_compatibility(self):
        """Test compatibility with database config module."""
        from src.database.config import get_config as db_get_config
        from src.database.config import get_database_config, get_connection_params
        
        # Test database config access
        config = db_get_config()
        assert isinstance(config, ConnectConfig)
        
        # Test database-specific functions
        db_config = get_database_config()
        assert hasattr(db_config, 'host')
        assert hasattr(db_config, 'port')
        
        # Test connection parameters
        params = get_connection_params()
        assert isinstance(params, dict)
        assert 'host' in params
        assert 'port' in params
    
    def test_settings_proxy_compatibility(self):
        """Test SettingsProxy for backward compatibility."""
        from src.database.config import settings
        
        # Test that settings proxy works
        assert hasattr(settings, 'database')
        
        # Test database attribute access
        db_config = settings.database
        assert hasattr(db_config, 'host')
        assert hasattr(db_config, 'port')
    
    def test_legacy_import_patterns(self):
        """Test that legacy import patterns still work."""
        # Test old-style imports
        from src.config import ConfigLoader, EnvironmentManager
        
        assert ConfigLoader is not None
        assert EnvironmentManager is not None
    
    def test_cli_compatibility(self):
        """Test CLI compatibility with new config system."""
        # Test that get_config works in CLI context
        config = get_config()
        
        # Should have all required attributes for CLI
        assert hasattr(config, 'database')
        assert hasattr(config, 'project')
        
        # Test that config can be used as settings object
        assert hasattr(config.database, 'host')
        assert hasattr(config.database, 'port')


if __name__ == '__main__':
    pytest.main([__file__, '-v'])