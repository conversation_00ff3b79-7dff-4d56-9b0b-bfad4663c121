#!/usr/bin/env python3
"""
最终EP Schema测试 - 验证EP数据导入到哪个schema
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import pandas as pd
import tempfile
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_ep_schema_import():
    """测试EP数据实际导入到哪个schema"""
    
    print("=== EP Schema 最终测试 ===")
    
    try:
        # 1. 创建测试数据
        test_data = {
            'WGS84_LATITUDE': [40.7128],
            'WGS84_LONGITUDE': [-74.0060],
            'CELL_NAME': ['TEST_CELL_SCHEMA'],
            'CELL_TYPE': ['LTE'],
            'AZIMUTH': [90]
        }
        
        df = pd.DataFrame(test_data)
        
        # 2. 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as temp_file:
            df.to_csv(temp_file.name, index=False)
            temp_file_path = temp_file.name
        
        logger.info(f"创建测试文件: {temp_file_path}")
        
        # 3. 初始化EPImporter
        from importers.ep_importer import EPImporter
        
        ep_importer = EPImporter()
        
        logger.info(f"EPImporter初始化完成")
        logger.info(f"Schema名称: '{ep_importer.get_schema_name()}'")
        
        # 4. 生成表名
        table_name = f"ep_schema_test_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"使用表名: {table_name}")
        
        # 5. 获取BulkOperations实例
        if hasattr(ep_importer, 'bulk_operations') and ep_importer.bulk_operations:
            bulk_ops = ep_importer.bulk_operations
        else:
            from database.operations.bulk_operations import BulkOperations
            bulk_ops = BulkOperations(None)
        
        # 6. 执行导入
        schema_name = ep_importer.get_schema_name()
        logger.info(f"准备导入到schema: '{schema_name}'")
        
        try:
            result = bulk_ops.bulk_insert_dataframe(
                df=df,
                table_name=table_name,
                schema=schema_name,
                if_exists='replace'  # 使用replace确保表被创建
            )
            
            logger.info(f"导入结果: {result}")
            
            # 7. 验证表的位置
            logger.info("\n=== 验证表的实际位置 ===")
            
            # 检查数据库中表的实际位置
            import psycopg2
            from src.config.core import ConnectConfigManager
            
            config_manager = ConnectConfigManager()
            config = config_manager.get_config()
            
            # 构建数据库连接字符串
            db_config = config.database
            conn_str = f"host={db_config.host} port={db_config.port} dbname={db_config.name} user={db_config.user} password={db_config.password}"
            
            with psycopg2.connect(conn_str) as conn:
                with conn.cursor() as cursor:
                    # 查找表的位置
                    cursor.execute("""
                        SELECT schemaname, tablename 
                        FROM pg_tables 
                        WHERE tablename = %s
                        ORDER BY schemaname
                    """, (table_name,))
                    
                    results = cursor.fetchall()
                    
                    if results:
                        for schema, table in results:
                            logger.info(f"找到表: {schema}.{table}")
                            print(f"✅ 表 '{table}' 位于 schema: '{schema}'")
                            
                            if schema == 'ep_to2':
                                print(f"✅ 成功！EP数据正确导入到 ep_to2 schema")
                            elif schema == 'public':
                                print(f"❌ 问题确认！EP数据错误导入到 public schema")
                            else:
                                print(f"⚠️  意外！EP数据导入到了 {schema} schema")
                    else:
                        logger.warning(f"未找到表 {table_name}")
                        print(f"❌ 表 '{table_name}' 未找到")
            
        except Exception as e:
            logger.error(f"导入失败: {e}")
            print(f"❌ 导入失败: {e}")
        
        # 8. 清理临时文件
        os.unlink(temp_file_path)
        logger.info(f"清理临时文件: {temp_file_path}")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        print(f"❌ 测试失败: {e}")

def check_current_ep_tables():
    """检查当前数据库中的EP表"""
    
    print("\n=== 检查当前EP表 ===")
    
    try:
        import psycopg2
        from src.config.core import ConnectConfigManager
        
        config_manager = ConnectConfigManager()
        config = config_manager.get_config()
        
        # 构建数据库连接字符串
        db_config = config.database
        conn_str = f"host={db_config.host} port={db_config.port} dbname={db_config.name} user={db_config.user} password={db_config.password}"
        
        with psycopg2.connect(conn_str) as conn:
            with conn.cursor() as cursor:
                # 查找所有EP表
                cursor.execute("""
                    SELECT schemaname, tablename 
                    FROM pg_tables 
                    WHERE tablename LIKE 'ep_%'
                    ORDER BY schemaname, tablename
                """)
                
                results = cursor.fetchall()
                
                if results:
                    print("当前数据库中的EP表:")
                    for schema, table in results:
                        print(f"  - {schema}.{table}")
                else:
                    print("当前数据库中没有EP表")
                
                # 检查schema是否存在
                cursor.execute("""
                    SELECT schema_name 
                    FROM information_schema.schemata 
                    WHERE schema_name IN ('ep_to2', 'public')
                    ORDER BY schema_name
                """)
                
                schemas = cursor.fetchall()
                print(f"\n相关schema状态:")
                for (schema,) in schemas:
                    print(f"  - {schema}: 存在")
    
    except Exception as e:
        logger.error(f"检查EP表失败: {e}")
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_current_ep_tables()
    test_ep_schema_import()
    
    print("\n=== 测试完成 ===")