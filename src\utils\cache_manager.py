# -*- coding: utf-8 -*-
__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""Cache management utilities.

This module provides caching functionality for the Connect platform,
including in-memory caching, cache statistics, and cache entry management.
"""

import time
from dataclasses import dataclass
from typing import Any, Dict, Optional, Union
from threading import Lock


@dataclass
class CacheEntry:
    """Represents a cache entry with value and metadata."""
    value: Any
    timestamp: float
    ttl: Optional[float] = None
    hit_count: int = 0
    
    def is_expired(self) -> bool:
        """Check if the cache entry has expired."""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl
    
    def touch(self) -> None:
        """Update the hit count and timestamp."""
        self.hit_count += 1
        self.timestamp = time.time()


@dataclass
class CacheStats:
    """Cache statistics."""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    size: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate."""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def miss_rate(self) -> float:
        """Calculate cache miss rate."""
        return 1.0 - self.hit_rate


class CacheManager:
    """Thread-safe in-memory cache manager."""
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[float] = None):
        """Initialize cache manager.
        
        Args:
            max_size: Maximum number of entries in cache
            default_ttl: Default time-to-live in seconds
        """
        self._cache: Dict[str, CacheEntry] = {}
        self._max_size = max_size
        self._default_ttl = default_ttl
        self._lock = Lock()
        self._stats = CacheStats()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                self._stats.misses += 1
                return None
            
            if entry.is_expired():
                del self._cache[key]
                self._stats.misses += 1
                self._stats.evictions += 1
                self._stats.size -= 1
                return None
            
            entry.touch()
            self._stats.hits += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (overrides default)
        """
        with self._lock:
            # Use provided TTL or default
            effective_ttl = ttl if ttl is not None else self._default_ttl
            
            # Check if we need to evict entries
            if key not in self._cache and len(self._cache) >= self._max_size:
                self._evict_lru()
            
            # Create or update entry
            if key in self._cache:
                self._cache[key].value = value
                self._cache[key].timestamp = time.time()
                self._cache[key].ttl = effective_ttl
            else:
                self._cache[key] = CacheEntry(
                    value=value,
                    timestamp=time.time(),
                    ttl=effective_ttl
                )
                self._stats.size += 1
    
    def delete(self, key: str) -> bool:
        """Delete entry from cache.
        
        Args:
            key: Cache key
            
        Returns:
            True if entry was deleted, False if not found
        """
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._stats.size -= 1
                return True
            return False
    
    def clear(self) -> None:
        """Clear all entries from cache."""
        with self._lock:
            self._cache.clear()
            self._stats.size = 0
            self._stats.evictions += self._stats.size
    
    def _evict_lru(self) -> None:
        """Evict least recently used entry."""
        if not self._cache:
            return
        
        # Find entry with oldest timestamp
        lru_key = min(self._cache.keys(), key=lambda k: self._cache[k].timestamp)
        del self._cache[lru_key]
        self._stats.evictions += 1
        self._stats.size -= 1
    
    def cleanup_expired(self) -> int:
        """Remove all expired entries.
        
        Returns:
            Number of entries removed
        """
        with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items()
                if entry.is_expired()
            ]
            
            for key in expired_keys:
                del self._cache[key]
                self._stats.evictions += 1
                self._stats.size -= 1
            
            return len(expired_keys)
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics.
        
        Returns:
            Current cache statistics
        """
        with self._lock:
            return CacheStats(
                hits=self._stats.hits,
                misses=self._stats.misses,
                evictions=self._stats.evictions,
                size=self._stats.size
            )
    
    def reset_stats(self) -> None:
        """Reset cache statistics."""
        with self._lock:
            self._stats = CacheStats(size=self._stats.size)
    
    @property
    def size(self) -> int:
        """Get current cache size."""
        return self._stats.size
    
    @property
    def max_size(self) -> int:
        """Get maximum cache size."""
        return self._max_size