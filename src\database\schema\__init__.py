"""Schema management module.

This module provides schema-related functionality including:
- Schema creation and management
- Table schema validation
- Data model definitions
- Schema routing and multi-tenancy support
"""

from .manager import SchemaManager
from .table_schema import TableSchema, ColumnSchema
from .validators import SchemaValidator
from .router import SchemaRouter, RoutingStrategy, RoutingRule, SchemaRoutingError

__all__ = [
    "SchemaManager",
    "TableSchema",
    "ColumnSchema",
    "SchemaValidator",
    "SchemaRouter",
    "RoutingStrategy",
    "RoutingRule",
    "SchemaRoutingError",
]
