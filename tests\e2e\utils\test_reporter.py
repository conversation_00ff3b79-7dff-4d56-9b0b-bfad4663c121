#!/usr/bin/env python3
"""
E2E测试报告生成器

该模块提供:
1. 测试结果收集和聚合
2. 多格式报告生成 (HTML, JSON, PDF, Excel)
3. 性能指标分析
4. 可视化图表生成
5. 测试趋势分析
6. 邮件和通知集成

使用方法:
    from tests.e2e.utils.test_reporter import _TestReporter

reporter = _TestReporter()
    reporter.add_test_result(test_result)
    reporter.generate_report('html', 'test_report.html')
"""

import os
import sys
import json
import csv
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from enum import Enum
import logging
import base64
import io

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    import pandas as pd
    import numpy as np
    import matplotlib.pyplot as plt
    import seaborn as sns
    from jinja2 import Template, Environment, FileSystemLoader
except ImportError as e:
    print(f"警告: 缺少可选依赖包: {e}")
    pd = None
    np = None
    plt = None
    sns = None
    Template = None
    Environment = None
    FileSystemLoader = None

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
except ImportError:
    print("警告: ReportLab未安装，PDF报告功能不可用")
    SimpleDocTemplate = None

try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment
    from openpyxl.chart import BarChart, LineChart, PieChart, Reference
except ImportError:
    print("警告: openpyxl未安装，Excel报告功能不可用")
    openpyxl = None

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestStatus(Enum):
    """测试状态"""
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"


class ReportFormat(Enum):
    """报告格式"""
    HTML = "html"
    JSON = "json"
    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"


@dataclass
class TestResult:
    """测试结果"""
    test_id: str
    test_name: str
    test_category: str
    status: TestStatus
    duration: float  # 秒
    start_time: datetime
    end_time: datetime
    error_message: Optional[str] = None
    stack_trace: Optional[str] = None
    assertions: List[Dict[str, Any]] = field(default_factory=list)
    performance_metrics: Dict[str, Any] = field(default_factory=dict)
    screenshots: List[str] = field(default_factory=list)
    logs: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TestSuite:
    """测试套件"""
    suite_name: str
    suite_category: str
    start_time: datetime
    end_time: datetime
    total_tests: int
    passed_tests: int
    failed_tests: int
    skipped_tests: int
    error_tests: int
    total_duration: float
    test_results: List[TestResult] = field(default_factory=list)
    setup_duration: float = 0.0
    teardown_duration: float = 0.0
    environment: Dict[str, Any] = field(default_factory=dict)


@dataclass
class TestExecution:
    """测试执行"""
    execution_id: str
    execution_name: str
    start_time: datetime
    end_time: datetime
    total_duration: float
    environment: Dict[str, Any]
    test_suites: List[TestSuite] = field(default_factory=list)
    overall_status: TestStatus = TestStatus.PASSED
    summary: Dict[str, Any] = field(default_factory=dict)
    configuration: Dict[str, Any] = field(default_factory=dict)


class _TestReporter:
    """测试报告生成器"""
    
    def __init__(self, output_dir: str = "test_reports"):
        """初始化测试报告生成器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.test_executions: List[TestExecution] = []
        self.current_execution: Optional[TestExecution] = None
        
        # 配置图表样式
        if plt:
            plt.style.use('seaborn-v0_8' if hasattr(plt.style, 'seaborn-v0_8') else 'default')
            sns.set_palette("husl") if sns else None
        
        logger.info(f"测试报告生成器初始化完成，输出目录: {self.output_dir}")
    
    def start_execution(self, 
                       execution_name: str, 
                       environment: Dict[str, Any] = None,
                       configuration: Dict[str, Any] = None) -> str:
        """开始测试执行
        
        Args:
            execution_name: 执行名称
            environment: 环境信息
            configuration: 配置信息
        
        Returns:
            执行ID
        """
        execution_id = f"exec_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        self.current_execution = TestExecution(
            execution_id=execution_id,
            execution_name=execution_name,
            start_time=datetime.now(),
            end_time=datetime.now(),
            total_duration=0.0,
            environment=environment or {},
            configuration=configuration or {}
        )
        
        logger.info(f"开始测试执行: {execution_name} (ID: {execution_id})")
        return execution_id
    
    def end_execution(self) -> Optional[TestExecution]:
        """结束测试执行
        
        Returns:
            测试执行对象
        """
        if not self.current_execution:
            logger.warning("没有正在进行的测试执行")
            return None
        
        self.current_execution.end_time = datetime.now()
        self.current_execution.total_duration = (
            self.current_execution.end_time - self.current_execution.start_time
        ).total_seconds()
        
        # 计算总体状态和摘要
        self._calculate_execution_summary(self.current_execution)
        
        # 添加到执行列表
        self.test_executions.append(self.current_execution)
        
        logger.info(f"测试执行结束: {self.current_execution.execution_name}")
        
        execution = self.current_execution
        self.current_execution = None
        return execution
    
    def start_suite(self, suite_name: str, suite_category: str = "general") -> TestSuite:
        """开始测试套件
        
        Args:
            suite_name: 套件名称
            suite_category: 套件分类
        
        Returns:
            测试套件对象
        """
        if not self.current_execution:
            raise RuntimeError("必须先开始测试执行")
        
        suite = TestSuite(
            suite_name=suite_name,
            suite_category=suite_category,
            start_time=datetime.now(),
            end_time=datetime.now(),
            total_tests=0,
            passed_tests=0,
            failed_tests=0,
            skipped_tests=0,
            error_tests=0,
            total_duration=0.0
        )
        
        self.current_execution.test_suites.append(suite)
        logger.info(f"开始测试套件: {suite_name}")
        return suite
    
    def end_suite(self, suite: TestSuite):
        """结束测试套件
        
        Args:
            suite: 测试套件对象
        """
        suite.end_time = datetime.now()
        suite.total_duration = (suite.end_time - suite.start_time).total_seconds()
        
        # 统计测试结果
        suite.total_tests = len(suite.test_results)
        suite.passed_tests = sum(1 for r in suite.test_results if r.status == TestStatus.PASSED)
        suite.failed_tests = sum(1 for r in suite.test_results if r.status == TestStatus.FAILED)
        suite.skipped_tests = sum(1 for r in suite.test_results if r.status == TestStatus.SKIPPED)
        suite.error_tests = sum(1 for r in suite.test_results if r.status == TestStatus.ERROR)
        
        logger.info(f"测试套件结束: {suite.suite_name}, 通过: {suite.passed_tests}/{suite.total_tests}")
    
    def add_test_result(self, test_result: TestResult, suite: Optional[TestSuite] = None):
        """添加测试结果
        
        Args:
            test_result: 测试结果
            suite: 测试套件（可选）
        """
        if suite:
            suite.test_results.append(test_result)
        elif self.current_execution and self.current_execution.test_suites:
            # 添加到最后一个套件
            self.current_execution.test_suites[-1].test_results.append(test_result)
        else:
            logger.warning("没有可用的测试套件来添加测试结果")
    
    def _calculate_execution_summary(self, execution: TestExecution):
        """计算执行摘要
        
        Args:
            execution: 测试执行对象
        """
        total_tests = sum(suite.total_tests for suite in execution.test_suites)
        passed_tests = sum(suite.passed_tests for suite in execution.test_suites)
        failed_tests = sum(suite.failed_tests for suite in execution.test_suites)
        skipped_tests = sum(suite.skipped_tests for suite in execution.test_suites)
        error_tests = sum(suite.error_tests for suite in execution.test_suites)
        
        # 确定总体状态
        if error_tests > 0 or failed_tests > 0:
            execution.overall_status = TestStatus.FAILED
        elif total_tests == 0:
            execution.overall_status = TestStatus.SKIPPED
        else:
            execution.overall_status = TestStatus.PASSED
        
        # 生成摘要
        execution.summary = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': failed_tests,
            'skipped_tests': skipped_tests,
            'error_tests': error_tests,
            'pass_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            'total_suites': len(execution.test_suites),
            'avg_test_duration': execution.total_duration / total_tests if total_tests > 0 else 0
        }
    
    def generate_report(self, 
                       format_type: ReportFormat, 
                       output_file: str,
                       execution: Optional[TestExecution] = None,
                       template_dir: Optional[str] = None) -> str:
        """生成测试报告
        
        Args:
            format_type: 报告格式
            output_file: 输出文件名
            execution: 测试执行对象（可选，默认使用最新的）
            template_dir: 模板目录（可选）
        
        Returns:
            生成的报告文件路径
        """
        if not execution:
            if not self.test_executions:
                raise ValueError("没有可用的测试执行数据")
            execution = self.test_executions[-1]
        
        output_path = self.output_dir / output_file
        
        logger.info(f"生成{format_type.value}格式报告: {output_path}")
        
        if format_type == ReportFormat.HTML:
            return self._generate_html_report(execution, output_path, template_dir)
        elif format_type == ReportFormat.JSON:
            return self._generate_json_report(execution, output_path)
        elif format_type == ReportFormat.PDF:
            return self._generate_pdf_report(execution, output_path)
        elif format_type == ReportFormat.EXCEL:
            return self._generate_excel_report(execution, output_path)
        elif format_type == ReportFormat.CSV:
            return self._generate_csv_report(execution, output_path)
        else:
            raise ValueError(f"不支持的报告格式: {format_type}")
    
    def _generate_html_report(self, 
                            execution: TestExecution, 
                            output_path: Path,
                            template_dir: Optional[str] = None) -> str:
        """生成HTML报告
        
        Args:
            execution: 测试执行对象
            output_path: 输出路径
            template_dir: 模板目录
        
        Returns:
            报告文件路径
        """
        # 生成图表
        charts = self._generate_charts(execution)
        
        # 准备模板数据
        template_data = {
            'execution': execution,
            'charts': charts,
            'generation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_duration_formatted': self._format_duration(execution.total_duration)
        }
        
        # 使用模板生成HTML
        if Template and template_dir:
            env = Environment(loader=FileSystemLoader(template_dir))
            template = env.get_template('test_report.html')
            html_content = template.render(**template_data)
        else:
            # 使用内置模板
            html_content = self._get_default_html_template().format(**self._prepare_template_data(template_data))
        
        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML报告生成完成: {output_path}")
        return str(output_path)
    
    def _generate_json_report(self, execution: TestExecution, output_path: Path) -> str:
        """生成JSON报告
        
        Args:
            execution: 测试执行对象
            output_path: 输出路径
        
        Returns:
            报告文件路径
        """
        # 转换为可序列化的字典
        report_data = self._execution_to_dict(execution)
        
        # 写入JSON文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"JSON报告生成完成: {output_path}")
        return str(output_path)
    
    def _generate_pdf_report(self, execution: TestExecution, output_path: Path) -> str:
        """生成PDF报告
        
        Args:
            execution: 测试执行对象
            output_path: 输出路径
        
        Returns:
            报告文件路径
        """
        if not SimpleDocTemplate:
            raise ImportError("ReportLab未安装，无法生成PDF报告")
        
        doc = SimpleDocTemplate(str(output_path), pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # 标题
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # 居中
        )
        story.append(Paragraph(f"测试执行报告: {execution.execution_name}", title_style))
        story.append(Spacer(1, 20))
        
        # 执行摘要
        summary_data = [
            ['执行ID', execution.execution_id],
            ['开始时间', execution.start_time.strftime('%Y-%m-%d %H:%M:%S')],
            ['结束时间', execution.end_time.strftime('%Y-%m-%d %H:%M:%S')],
            ['总耗时', self._format_duration(execution.total_duration)],
            ['总体状态', execution.overall_status.value],
            ['测试套件数', str(len(execution.test_suites))],
            ['总测试数', str(execution.summary.get('total_tests', 0))],
            ['通过率', f"{execution.summary.get('pass_rate', 0):.1f}%"]
        ]
        
        summary_table = Table(summary_data, colWidths=[2*inch, 3*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(Paragraph("执行摘要", styles['Heading2']))
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # 测试套件详情
        story.append(Paragraph("测试套件详情", styles['Heading2']))
        
        for suite in execution.test_suites:
            suite_data = [
                ['套件名称', suite.suite_name],
                ['分类', suite.suite_category],
                ['总测试数', str(suite.total_tests)],
                ['通过', str(suite.passed_tests)],
                ['失败', str(suite.failed_tests)],
                ['跳过', str(suite.skipped_tests)],
                ['错误', str(suite.error_tests)],
                ['耗时', self._format_duration(suite.total_duration)]
            ]
            
            suite_table = Table(suite_data, colWidths=[1.5*inch, 2*inch])
            suite_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(suite_table)
            story.append(Spacer(1, 10))
        
        # 构建PDF
        doc.build(story)
        
        logger.info(f"PDF报告生成完成: {output_path}")
        return str(output_path)
    
    def _generate_excel_report(self, execution: TestExecution, output_path: Path) -> str:
        """生成Excel报告
        
        Args:
            execution: 测试执行对象
            output_path: 输出路径
        
        Returns:
            报告文件路径
        """
        if not openpyxl:
            raise ImportError("openpyxl未安装，无法生成Excel报告")
        
        wb = openpyxl.Workbook()
        
        # 删除默认工作表
        wb.remove(wb.active)
        
        # 创建摘要工作表
        summary_ws = wb.create_sheet("执行摘要")
        self._create_summary_sheet(summary_ws, execution)
        
        # 创建套件详情工作表
        suites_ws = wb.create_sheet("测试套件")
        self._create_suites_sheet(suites_ws, execution)
        
        # 创建测试详情工作表
        tests_ws = wb.create_sheet("测试详情")
        self._create_tests_sheet(tests_ws, execution)
        
        # 创建图表工作表
        if len(execution.test_suites) > 0:
            charts_ws = wb.create_sheet("图表分析")
            self._create_charts_sheet(charts_ws, execution)
        
        # 保存文件
        wb.save(output_path)
        
        logger.info(f"Excel报告生成完成: {output_path}")
        return str(output_path)
    
    def _generate_csv_report(self, execution: TestExecution, output_path: Path) -> str:
        """生成CSV报告
        
        Args:
            execution: 测试执行对象
            output_path: 输出路径
        
        Returns:
            报告文件路径
        """
        # 收集所有测试结果
        all_tests = []
        for suite in execution.test_suites:
            for test in suite.test_results:
                test_data = {
                    'execution_id': execution.execution_id,
                    'execution_name': execution.execution_name,
                    'suite_name': suite.suite_name,
                    'suite_category': suite.suite_category,
                    'test_id': test.test_id,
                    'test_name': test.test_name,
                    'test_category': test.test_category,
                    'status': test.status.value,
                    'duration': test.duration,
                    'start_time': test.start_time.isoformat(),
                    'end_time': test.end_time.isoformat(),
                    'error_message': test.error_message or '',
                    'assertions_count': len(test.assertions),
                    'screenshots_count': len(test.screenshots),
                    'logs_count': len(test.logs)
                }
                all_tests.append(test_data)
        
        # 写入CSV文件
        if all_tests:
            fieldnames = all_tests[0].keys()
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(all_tests)
        
        logger.info(f"CSV报告生成完成: {output_path}")
        return str(output_path)
    
    def _generate_charts(self, execution: TestExecution) -> Dict[str, str]:
        """生成图表
        
        Args:
            execution: 测试执行对象
        
        Returns:
            图表文件路径字典
        """
        charts = {}
        
        if not plt:
            return charts
        
        # 创建图表目录
        charts_dir = self.output_dir / "charts"
        charts_dir.mkdir(exist_ok=True)
        
        # 1. 测试状态分布饼图
        status_chart = self._create_status_pie_chart(execution, charts_dir)
        if status_chart:
            charts['status_distribution'] = status_chart
        
        # 2. 套件性能柱状图
        performance_chart = self._create_performance_bar_chart(execution, charts_dir)
        if performance_chart:
            charts['suite_performance'] = performance_chart
        
        # 3. 测试时长分布直方图
        duration_chart = self._create_duration_histogram(execution, charts_dir)
        if duration_chart:
            charts['duration_distribution'] = duration_chart
        
        # 4. 时间线图
        timeline_chart = self._create_timeline_chart(execution, charts_dir)
        if timeline_chart:
            charts['execution_timeline'] = timeline_chart
        
        return charts
    
    def _create_status_pie_chart(self, execution: TestExecution, charts_dir: Path) -> Optional[str]:
        """创建测试状态分布饼图"""
        try:
            summary = execution.summary
            labels = []
            sizes = []
            colors = []
            
            if summary.get('passed_tests', 0) > 0:
                labels.append(f"通过 ({summary['passed_tests']})")
                sizes.append(summary['passed_tests'])
                colors.append('#28a745')
            
            if summary.get('failed_tests', 0) > 0:
                labels.append(f"失败 ({summary['failed_tests']})")
                sizes.append(summary['failed_tests'])
                colors.append('#dc3545')
            
            if summary.get('skipped_tests', 0) > 0:
                labels.append(f"跳过 ({summary['skipped_tests']})")
                sizes.append(summary['skipped_tests'])
                colors.append('#ffc107')
            
            if summary.get('error_tests', 0) > 0:
                labels.append(f"错误 ({summary['error_tests']})")
                sizes.append(summary['error_tests'])
                colors.append('#fd7e14')
            
            if not sizes:
                return None
            
            plt.figure(figsize=(10, 8))
            plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            plt.title('测试状态分布', fontsize=16, fontweight='bold')
            plt.axis('equal')
            
            chart_path = charts_dir / 'status_distribution.png'
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
        
        except Exception as e:
            logger.error(f"创建状态分布图失败: {e}")
            return None
    
    def _create_performance_bar_chart(self, execution: TestExecution, charts_dir: Path) -> Optional[str]:
        """创建套件性能柱状图"""
        try:
            if not execution.test_suites:
                return None
            
            suite_names = [suite.suite_name for suite in execution.test_suites]
            durations = [suite.total_duration for suite in execution.test_suites]
            test_counts = [suite.total_tests for suite in execution.test_suites]
            
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
            
            # 执行时间
            bars1 = ax1.bar(suite_names, durations, color='skyblue', alpha=0.7)
            ax1.set_title('测试套件执行时间', fontsize=14, fontweight='bold')
            ax1.set_ylabel('时间 (秒)')
            ax1.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, duration in zip(bars1, durations):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{duration:.1f}s', ha='center', va='bottom')
            
            # 测试数量
            bars2 = ax2.bar(suite_names, test_counts, color='lightcoral', alpha=0.7)
            ax2.set_title('测试套件测试数量', fontsize=14, fontweight='bold')
            ax2.set_ylabel('测试数量')
            ax2.set_xlabel('测试套件')
            ax2.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, count in zip(bars2, test_counts):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height,
                        str(count), ha='center', va='bottom')
            
            plt.tight_layout()
            
            chart_path = charts_dir / 'suite_performance.png'
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
        
        except Exception as e:
            logger.error(f"创建性能柱状图失败: {e}")
            return None
    
    def _create_duration_histogram(self, execution: TestExecution, charts_dir: Path) -> Optional[str]:
        """创建测试时长分布直方图"""
        try:
            # 收集所有测试时长
            all_durations = []
            for suite in execution.test_suites:
                for test in suite.test_results:
                    all_durations.append(test.duration)
            
            if not all_durations:
                return None
            
            plt.figure(figsize=(10, 6))
            plt.hist(all_durations, bins=20, color='lightgreen', alpha=0.7, edgecolor='black')
            plt.title('测试时长分布', fontsize=14, fontweight='bold')
            plt.xlabel('时长 (秒)')
            plt.ylabel('测试数量')
            plt.grid(True, alpha=0.3)
            
            # 添加统计信息
            mean_duration = np.mean(all_durations) if np else sum(all_durations) / len(all_durations)
            plt.axvline(mean_duration, color='red', linestyle='--', 
                       label=f'平均时长: {mean_duration:.2f}s')
            plt.legend()
            
            chart_path = charts_dir / 'duration_distribution.png'
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
        
        except Exception as e:
            logger.error(f"创建时长分布图失败: {e}")
            return None
    
    def _create_timeline_chart(self, execution: TestExecution, charts_dir: Path) -> Optional[str]:
        """创建执行时间线图"""
        try:
            if not execution.test_suites:
                return None
            
            fig, ax = plt.subplots(figsize=(14, 8))
            
            # 为每个套件创建时间线
            y_pos = 0
            colors = plt.cm.Set3(np.linspace(0, 1, len(execution.test_suites))) if np else ['blue'] * len(execution.test_suites)
            
            for i, suite in enumerate(execution.test_suites):
                start_time = suite.start_time
                duration = suite.total_duration
                
                # 绘制套件时间条
                ax.barh(y_pos, duration, left=(start_time - execution.start_time).total_seconds(),
                       height=0.6, color=colors[i], alpha=0.7, 
                       label=f"{suite.suite_name} ({suite.total_tests} tests)")
                
                # 添加套件名称
                ax.text((start_time - execution.start_time).total_seconds() + duration/2, y_pos,
                       f"{suite.suite_name}\n{duration:.1f}s", 
                       ha='center', va='center', fontsize=8, fontweight='bold')
                
                y_pos += 1
            
            ax.set_xlabel('时间 (秒)')
            ax.set_ylabel('测试套件')
            ax.set_title('测试执行时间线', fontsize=14, fontweight='bold')
            ax.set_yticks(range(len(execution.test_suites)))
            ax.set_yticklabels([suite.suite_name for suite in execution.test_suites])
            ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            chart_path = charts_dir / 'execution_timeline.png'
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(chart_path)
        
        except Exception as e:
            logger.error(f"创建时间线图失败: {e}")
            return None
    
    def _format_duration(self, seconds: float) -> str:
        """格式化时长
        
        Args:
            seconds: 秒数
        
        Returns:
            格式化的时长字符串
        """
        if seconds < 60:
            return f"{seconds:.2f}秒"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            remaining_seconds = seconds % 60
            return f"{minutes}分{remaining_seconds:.2f}秒"
        else:
            hours = int(seconds // 3600)
            remaining_minutes = int((seconds % 3600) // 60)
            remaining_seconds = seconds % 60
            return f"{hours}小时{remaining_minutes}分{remaining_seconds:.2f}秒"
    
    def _execution_to_dict(self, execution: TestExecution) -> Dict[str, Any]:
        """将测试执行对象转换为字典
        
        Args:
            execution: 测试执行对象
        
        Returns:
            字典表示
        """
        return {
            'execution_id': execution.execution_id,
            'execution_name': execution.execution_name,
            'start_time': execution.start_time.isoformat(),
            'end_time': execution.end_time.isoformat(),
            'total_duration': execution.total_duration,
            'overall_status': execution.overall_status.value,
            'environment': execution.environment,
            'configuration': execution.configuration,
            'summary': execution.summary,
            'test_suites': [
                {
                    'suite_name': suite.suite_name,
                    'suite_category': suite.suite_category,
                    'start_time': suite.start_time.isoformat(),
                    'end_time': suite.end_time.isoformat(),
                    'total_duration': suite.total_duration,
                    'total_tests': suite.total_tests,
                    'passed_tests': suite.passed_tests,
                    'failed_tests': suite.failed_tests,
                    'skipped_tests': suite.skipped_tests,
                    'error_tests': suite.error_tests,
                    'setup_duration': suite.setup_duration,
                    'teardown_duration': suite.teardown_duration,
                    'environment': suite.environment,
                    'test_results': [
                        {
                            'test_id': test.test_id,
                            'test_name': test.test_name,
                            'test_category': test.test_category,
                            'status': test.status.value,
                            'duration': test.duration,
                            'start_time': test.start_time.isoformat(),
                            'end_time': test.end_time.isoformat(),
                            'error_message': test.error_message,
                            'stack_trace': test.stack_trace,
                            'assertions': test.assertions,
                            'performance_metrics': test.performance_metrics,
                            'screenshots': test.screenshots,
                            'logs': test.logs,
                            'metadata': test.metadata
                        }
                        for test in suite.test_results
                    ]
                }
                for suite in execution.test_suites
            ]
        }
    
    def _get_default_html_template(self) -> str:
        """获取默认HTML模板
        
        Returns:
            HTML模板字符串
        """
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试执行报告</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }}
        .header h1 {{
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }}
        .summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .summary-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }}
        .summary-card .value {{
            font-size: 2em;
            font-weight: bold;
        }}
        .charts {{
            margin: 30px 0;
        }}
        .chart {{
            text-align: center;
            margin: 20px 0;
        }}
        .chart img {{
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}
        .suites {{
            margin-top: 30px;
        }}
        .suite {{
            background-color: #f8f9fa;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #007bff;
        }}
        .suite h3 {{
            color: #333;
            margin-top: 0;
        }}
        .suite-stats {{
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }}
        .stat {{
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
        }}
        .stat.passed {{ background-color: #d4edda; color: #155724; }}
        .stat.failed {{ background-color: #f8d7da; color: #721c24; }}
        .stat.skipped {{ background-color: #fff3cd; color: #856404; }}
        .stat.error {{ background-color: #f5c6cb; color: #721c24; }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>测试执行报告</h1>
            <p><strong>{execution_name}</strong></p>
            <p>执行ID: {execution_id} | 生成时间: {generation_time}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="value">{total_tests}</div>
            </div>
            <div class="summary-card">
                <h3>通过率</h3>
                <div class="value">{pass_rate:.1f}%</div>
            </div>
            <div class="summary-card">
                <h3>总耗时</h3>
                <div class="value">{total_duration_formatted}</div>
            </div>
            <div class="summary-card">
                <h3>测试套件</h3>
                <div class="value">{total_suites}</div>
            </div>
        </div>
        
        <div class="charts">
            <h2>图表分析</h2>
            {charts_html}
        </div>
        
        <div class="suites">
            <h2>测试套件详情</h2>
            {suites_html}
        </div>
        
        <div class="footer">
            <p>报告由Connect测试系统自动生成</p>
        </div>
    </div>
</body>
</html>
        """
    
    def _prepare_template_data(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """准备模板数据
        
        Args:
            template_data: 原始模板数据
        
        Returns:
            处理后的模板数据
        """
        execution = template_data['execution']
        charts = template_data['charts']
        
        # 准备图表HTML
        charts_html = ""
        for chart_name, chart_path in charts.items():
            if os.path.exists(chart_path):
                with open(chart_path, 'rb') as f:
                    chart_data = base64.b64encode(f.read()).decode()
                charts_html += f'''
                <div class="chart">
                    <img src="data:image/png;base64,{chart_data}" alt="{chart_name}">
                </div>
                '''
        
        # 准备套件HTML
        suites_html = ""
        for suite in execution.test_suites:
            suites_html += f'''
            <div class="suite">
                <h3>{suite.suite_name}</h3>
                <p>分类: {suite.suite_category} | 耗时: {self._format_duration(suite.total_duration)}</p>
                <div class="suite-stats">
                    <span class="stat passed">通过: {suite.passed_tests}</span>
                    <span class="stat failed">失败: {suite.failed_tests}</span>
                    <span class="stat skipped">跳过: {suite.skipped_tests}</span>
                    <span class="stat error">错误: {suite.error_tests}</span>
                </div>
            </div>
            '''
        
        return {
            'execution_name': execution.execution_name,
            'execution_id': execution.execution_id,
            'generation_time': template_data['generation_time'],
            'total_tests': execution.summary.get('total_tests', 0),
            'pass_rate': execution.summary.get('pass_rate', 0),
            'total_duration_formatted': template_data['total_duration_formatted'],
            'total_suites': len(execution.test_suites),
            'charts_html': charts_html,
            'suites_html': suites_html
        }
    
    def _create_summary_sheet(self, ws, execution: TestExecution):
        """创建Excel摘要工作表"""
        # 设置标题
        ws['A1'] = '测试执行摘要'
        ws['A1'].font = Font(size=16, bold=True)
        
        # 基本信息
        row = 3
        info_data = [
            ('执行ID', execution.execution_id),
            ('执行名称', execution.execution_name),
            ('开始时间', execution.start_time.strftime('%Y-%m-%d %H:%M:%S')),
            ('结束时间', execution.end_time.strftime('%Y-%m-%d %H:%M:%S')),
            ('总耗时', self._format_duration(execution.total_duration)),
            ('总体状态', execution.overall_status.value),
            ('', ''),
            ('总测试数', execution.summary.get('total_tests', 0)),
            ('通过测试', execution.summary.get('passed_tests', 0)),
            ('失败测试', execution.summary.get('failed_tests', 0)),
            ('跳过测试', execution.summary.get('skipped_tests', 0)),
            ('错误测试', execution.summary.get('error_tests', 0)),
            ('通过率', f"{execution.summary.get('pass_rate', 0):.1f}%"),
            ('测试套件数', len(execution.test_suites))
        ]
        
        for label, value in info_data:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            if label:  # 非空行加粗
                ws[f'A{row}'].font = Font(bold=True)
            row += 1
    
    def _create_suites_sheet(self, ws, execution: TestExecution):
        """创建Excel套件工作表"""
        # 设置标题
        headers = ['套件名称', '分类', '总测试数', '通过', '失败', '跳过', '错误', '耗时(秒)', '开始时间', '结束时间']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # 添加数据
        for row, suite in enumerate(execution.test_suites, 2):
            ws.cell(row=row, column=1, value=suite.suite_name)
            ws.cell(row=row, column=2, value=suite.suite_category)
            ws.cell(row=row, column=3, value=suite.total_tests)
            ws.cell(row=row, column=4, value=suite.passed_tests)
            ws.cell(row=row, column=5, value=suite.failed_tests)
            ws.cell(row=row, column=6, value=suite.skipped_tests)
            ws.cell(row=row, column=7, value=suite.error_tests)
            ws.cell(row=row, column=8, value=round(suite.total_duration, 2))
            ws.cell(row=row, column=9, value=suite.start_time.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=10, value=suite.end_time.strftime('%Y-%m-%d %H:%M:%S'))
    
    def _create_tests_sheet(self, ws, execution: TestExecution):
        """创建Excel测试详情工作表"""
        # 设置标题
        headers = ['套件名称', '测试ID', '测试名称', '分类', '状态', '耗时(秒)', '开始时间', '结束时间', '错误信息']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # 添加数据
        row = 2
        for suite in execution.test_suites:
            for test in suite.test_results:
                ws.cell(row=row, column=1, value=suite.suite_name)
                ws.cell(row=row, column=2, value=test.test_id)
                ws.cell(row=row, column=3, value=test.test_name)
                ws.cell(row=row, column=4, value=test.test_category)
                ws.cell(row=row, column=5, value=test.status.value)
                ws.cell(row=row, column=6, value=round(test.duration, 2))
                ws.cell(row=row, column=7, value=test.start_time.strftime('%Y-%m-%d %H:%M:%S'))
                ws.cell(row=row, column=8, value=test.end_time.strftime('%Y-%m-%d %H:%M:%S'))
                ws.cell(row=row, column=9, value=test.error_message or '')
                
                # 根据状态设置颜色
                status_cell = ws.cell(row=row, column=5)
                if test.status == TestStatus.PASSED:
                    status_cell.fill = PatternFill(start_color='90EE90', end_color='90EE90', fill_type='solid')
                elif test.status == TestStatus.FAILED:
                    status_cell.fill = PatternFill(start_color='FFB6C1', end_color='FFB6C1', fill_type='solid')
                elif test.status == TestStatus.SKIPPED:
                    status_cell.fill = PatternFill(start_color='FFFFE0', end_color='FFFFE0', fill_type='solid')
                elif test.status == TestStatus.ERROR:
                    status_cell.fill = PatternFill(start_color='FFA07A', end_color='FFA07A', fill_type='solid')
                
                row += 1
    
    def _create_charts_sheet(self, ws, execution: TestExecution):
        """创建Excel图表工作表"""
        # 准备数据
        suite_names = [suite.suite_name for suite in execution.test_suites]
        passed_counts = [suite.passed_tests for suite in execution.test_suites]
        failed_counts = [suite.failed_tests for suite in execution.test_suites]
        
        # 创建数据表
        ws['A1'] = '套件名称'
        ws['B1'] = '通过测试'
        ws['C1'] = '失败测试'
        
        for i, (name, passed, failed) in enumerate(zip(suite_names, passed_counts, failed_counts), 2):
            ws[f'A{i}'] = name
            ws[f'B{i}'] = passed
            ws[f'C{i}'] = failed
        
        # 创建柱状图
        chart = BarChart()
        chart.title = "测试结果统计"
        chart.x_axis.title = "测试套件"
        chart.y_axis.title = "测试数量"
        
        data = Reference(ws, min_col=2, min_row=1, max_col=3, max_row=len(suite_names) + 1)
        cats = Reference(ws, min_col=1, min_row=2, max_row=len(suite_names) + 1)
        
        chart.add_data(data, titles_from_data=True)
        chart.set_categories(cats)
        
        ws.add_chart(chart, "E2")


if __name__ == "__main__":
    # 示例用法
    reporter = _TestReporter()
    
    # 开始测试执行
    execution_id = reporter.start_execution(
        "Connect E2E测试",
        environment={"os": "Windows 10", "browser": "Chrome"},
        configuration={"parallel": True, "timeout": 300}
    )
    
    # 创建测试套件
    suite = reporter.start_suite("数据导入测试", "功能测试")
    
    # 添加测试结果
    test_result = TestResult(
        test_id="test_001",
        test_name="EP数据导入测试",
        test_category="数据导入",
        status=TestStatus.PASSED,
        duration=5.2,
        start_time=datetime.now() - timedelta(seconds=5),
        end_time=datetime.now(),
        performance_metrics={"memory_usage": "256MB", "cpu_usage": "15%"}
    )
    
    reporter.add_test_result(test_result, suite)
    
    # 结束套件和执行
    reporter.end_suite(suite)
    execution = reporter.end_execution()
    
    # 生成报告
    if execution:
        html_report = reporter.generate_report(ReportFormat.HTML, "test_report.html")
        json_report = reporter.generate_report(ReportFormat.JSON, "test_report.json")
        
        print(f"HTML报告: {html_report}")
        print(f"JSON报告: {json_report}")