"""JOIN operation classes for query building.

This module provides classes for building various types of JOIN operations
with proper syntax and validation.
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, Optional, Tuple

from loguru import logger

from ..exceptions import ValidationError
from .conditions import Condition
from .dialects import Dialect


class JoinType(Enum):
    """Types of JOIN operations."""

    INNER = "INNER JOIN"
    LEFT = "LEFT JOIN"
    RIGHT = "RIGHT JOIN"
    FULL = "FULL OUTER JOIN"
    CROSS = "CROSS JOIN"
    LEFT_OUTER = "LEFT OUTER JOIN"
    RIGHT_OUTER = "RIGHT OUTER JOIN"
    NATURAL = "NATURAL JOIN"
    NATURAL_LEFT = "NATURAL LEFT JOIN"
    NATURAL_RIGHT = "NATURAL RIGHT JOIN"
    NATURAL_FULL = "NATURAL FULL OUTER JOIN"


class Join(ABC):
    """Abstract base class for JOIN operations."""

    def __init__(
        self, table: str, schema: Optional[str] = None, alias: Optional[str] = None
    ):
        """Initialize JOIN operation.

        Args:
            table: Table name to join
            schema: Optional schema name
            alias: Optional table alias
        """
        self.table = table
        self.schema = schema
        self.alias = alias

    @abstractmethod
    def build(self, dialect: Dialect) -> str:
        """Build the JOIN SQL.

        Args:
            dialect: SQL dialect

        Returns:
            JOIN SQL string
        """
        pass

    def _validate_identifier(self, identifier: str, dialect: Dialect) -> str:
        """Validate and escape SQL identifier.

        Args:
            identifier: SQL identifier
            dialect: SQL dialect

        Returns:
            Escaped identifier
        """
        return dialect.escape_identifier(identifier)

    def _build_table_name(self, dialect: Dialect) -> str:
        """Build full table name with schema.

        Args:
            dialect: SQL dialect

        Returns:
            Full table name
        """
        table_name = self._validate_identifier(self.table, dialect)

        if self.schema:
            schema_name = self._validate_identifier(self.schema, dialect)
            table_name = f"{schema_name}.{table_name}"

        if self.alias:
            alias_name = self._validate_identifier(self.alias, dialect)
            table_name += f" AS {alias_name}"

        return table_name


class ConditionalJoin(Join):
    """JOIN with ON condition."""

    def __init__(
        self,
        join_type: JoinType,
        table: str,
        condition: Condition,
        schema: Optional[str] = None,
        alias: Optional[str] = None,
    ):
        """Initialize conditional JOIN.

        Args:
            join_type: Type of JOIN
            table: Table name to join
            condition: JOIN condition
            schema: Optional schema name
            alias: Optional table alias
        """
        super().__init__(table, schema, alias)
        self.join_type = join_type
        self.condition = condition

    def build(self, dialect: Dialect) -> str:
        """Build conditional JOIN SQL.

        Args:
            dialect: SQL dialect

        Returns:
            JOIN SQL string
        """
        try:
            table_name = self._build_table_name(dialect)
            condition_sql, _ = self.condition.build(dialect)

            join_sql = f"{self.join_type.value} {table_name} ON {condition_sql}"
            return join_sql

        except Exception as e:
            logger.error(f"Failed to build conditional JOIN: {e}")
            raise ValidationError(f"Failed to build JOIN: {e}")


class UsingJoin(Join):
    """JOIN with USING clause."""

    def __init__(
        self,
        join_type: JoinType,
        table: str,
        columns: list,
        schema: Optional[str] = None,
        alias: Optional[str] = None,
    ):
        """Initialize USING JOIN.

        Args:
            join_type: Type of JOIN
            table: Table name to join
            columns: Columns for USING clause
            schema: Optional schema name
            alias: Optional table alias
        """
        super().__init__(table, schema, alias)
        self.join_type = join_type
        self.columns = columns

        if not columns:
            raise ValidationError("USING JOIN requires at least one column")

    def build(self, dialect: Dialect) -> str:
        """Build USING JOIN SQL.

        Args:
            dialect: SQL dialect

        Returns:
            JOIN SQL string
        """
        try:
            table_name = self._build_table_name(dialect)

            # Validate and escape column names
            escaped_columns = [
                self._validate_identifier(col, dialect) for col in self.columns
            ]
            using_clause = f"USING ({', '.join(escaped_columns)})"

            join_sql = f"{self.join_type.value} {table_name} {using_clause}"
            return join_sql

        except Exception as e:
            logger.error(f"Failed to build USING JOIN: {e}")
            raise ValidationError(f"Failed to build USING JOIN: {e}")


class CrossJoin(Join):
    """CROSS JOIN operation."""

    def __init__(
        self, table: str, schema: Optional[str] = None, alias: Optional[str] = None
    ):
        """Initialize CROSS JOIN.

        Args:
            table: Table name to join
            schema: Optional schema name
            alias: Optional table alias
        """
        super().__init__(table, schema, alias)

    def build(self, dialect: Dialect) -> str:
        """Build CROSS JOIN SQL.

        Args:
            dialect: SQL dialect

        Returns:
            JOIN SQL string
        """
        try:
            table_name = self._build_table_name(dialect)
            join_sql = f"{JoinType.CROSS.value} {table_name}"
            return join_sql

        except Exception as e:
            logger.error(f"Failed to build CROSS JOIN: {e}")
            raise ValidationError(f"Failed to build CROSS JOIN: {e}")


class NaturalJoin(Join):
    """NATURAL JOIN operation."""

    def __init__(
        self,
        join_type: JoinType,
        table: str,
        schema: Optional[str] = None,
        alias: Optional[str] = None,
    ):
        """Initialize NATURAL JOIN.

        Args:
            join_type: Type of NATURAL JOIN
            table: Table name to join
            schema: Optional schema name
            alias: Optional table alias
        """
        super().__init__(table, schema, alias)
        self.join_type = join_type

        # Validate that it's a natural join type
        if join_type not in [
            JoinType.NATURAL,
            JoinType.NATURAL_LEFT,
            JoinType.NATURAL_RIGHT,
            JoinType.NATURAL_FULL,
        ]:
            raise ValidationError(f"Invalid natural join type: {join_type}")

    def build(self, dialect: Dialect) -> str:
        """Build NATURAL JOIN SQL.

        Args:
            dialect: SQL dialect

        Returns:
            JOIN SQL string
        """
        try:
            table_name = self._build_table_name(dialect)
            join_sql = f"{self.join_type.value} {table_name}"
            return join_sql

        except Exception as e:
            logger.error(f"Failed to build NATURAL JOIN: {e}")
            raise ValidationError(f"Failed to build NATURAL JOIN: {e}")


class SubqueryJoin(Join):
    """JOIN with subquery."""

    def __init__(
        self, join_type: JoinType, subquery: str, alias: str, condition: Condition
    ):
        """Initialize subquery JOIN.

        Args:
            join_type: Type of JOIN
            subquery: Subquery SQL
            alias: Subquery alias (required)
            condition: JOIN condition
        """
        super().__init__("subquery", None, alias)
        self.join_type = join_type
        self.subquery = subquery
        self.condition = condition

        if not alias:
            raise ValidationError("Subquery JOIN requires an alias")

    def build(self, dialect: Dialect) -> str:
        """Build subquery JOIN SQL.

        Args:
            dialect: SQL dialect

        Returns:
            JOIN SQL string
        """
        try:
            alias_name = self._validate_identifier(self.alias, dialect)
            condition_sql, _ = self.condition.build(dialect)

            join_sql = f"{self.join_type.value} ({self.subquery}) AS {alias_name} ON {condition_sql}"
            return join_sql

        except Exception as e:
            logger.error(f"Failed to build subquery JOIN: {e}")
            raise ValidationError(f"Failed to build subquery JOIN: {e}")


# Convenience classes for specific JOIN types
class InnerJoin(ConditionalJoin):
    """INNER JOIN operation."""

    def __init__(
        self,
        table: str,
        condition: Condition,
        schema: Optional[str] = None,
        alias: Optional[str] = None,
    ):
        """Initialize INNER JOIN.

        Args:
            table: Table name to join
            condition: JOIN condition
            schema: Optional schema name
            alias: Optional table alias
        """
        super().__init__(JoinType.INNER, table, condition, schema, alias)


class LeftJoin(ConditionalJoin):
    """LEFT JOIN operation."""

    def __init__(
        self,
        table: str,
        condition: Condition,
        schema: Optional[str] = None,
        alias: Optional[str] = None,
    ):
        """Initialize LEFT JOIN.

        Args:
            table: Table name to join
            condition: JOIN condition
            schema: Optional schema name
            alias: Optional table alias
        """
        super().__init__(JoinType.LEFT, table, condition, schema, alias)


class RightJoin(ConditionalJoin):
    """RIGHT JOIN operation."""

    def __init__(
        self,
        table: str,
        condition: Condition,
        schema: Optional[str] = None,
        alias: Optional[str] = None,
    ):
        """Initialize RIGHT JOIN.

        Args:
            table: Table name to join
            condition: JOIN condition
            schema: Optional schema name
            alias: Optional table alias
        """
        super().__init__(JoinType.RIGHT, table, condition, schema, alias)


class FullJoin(ConditionalJoin):
    """FULL OUTER JOIN operation."""

    def __init__(
        self,
        table: str,
        condition: Condition,
        schema: Optional[str] = None,
        alias: Optional[str] = None,
    ):
        """Initialize FULL OUTER JOIN.

        Args:
            table: Table name to join
            condition: JOIN condition
            schema: Optional schema name
            alias: Optional table alias
        """
        super().__init__(JoinType.FULL, table, condition, schema, alias)


class LeftOuterJoin(ConditionalJoin):
    """LEFT OUTER JOIN operation."""

    def __init__(
        self,
        table: str,
        condition: Condition,
        schema: Optional[str] = None,
        alias: Optional[str] = None,
    ):
        """Initialize LEFT OUTER JOIN.

        Args:
            table: Table name to join
            condition: JOIN condition
            schema: Optional schema name
            alias: Optional table alias
        """
        super().__init__(JoinType.LEFT_OUTER, table, condition, schema, alias)


class RightOuterJoin(ConditionalJoin):
    """RIGHT OUTER JOIN operation."""

    def __init__(
        self,
        table: str,
        condition: Condition,
        schema: Optional[str] = None,
        alias: Optional[str] = None,
    ):
        """Initialize RIGHT OUTER JOIN.

        Args:
            table: Table name to join
            condition: JOIN condition
            schema: Optional schema name
            alias: Optional table alias
        """
        super().__init__(JoinType.RIGHT_OUTER, table, condition, schema, alias)


# Convenience functions for creating JOINs
def inner_join(
    table: str,
    condition: Condition,
    schema: Optional[str] = None,
    alias: Optional[str] = None,
) -> InnerJoin:
    """Create INNER JOIN.

    Args:
        table: Table name to join
        condition: JOIN condition
        schema: Optional schema name
        alias: Optional table alias

    Returns:
        InnerJoin instance
    """
    return InnerJoin(table, condition, schema, alias)


def left_join(
    table: str,
    condition: Condition,
    schema: Optional[str] = None,
    alias: Optional[str] = None,
) -> LeftJoin:
    """Create LEFT JOIN.

    Args:
        table: Table name to join
        condition: JOIN condition
        schema: Optional schema name
        alias: Optional table alias

    Returns:
        LeftJoin instance
    """
    return LeftJoin(table, condition, schema, alias)


def right_join(
    table: str,
    condition: Condition,
    schema: Optional[str] = None,
    alias: Optional[str] = None,
) -> RightJoin:
    """Create RIGHT JOIN.

    Args:
        table: Table name to join
        condition: JOIN condition
        schema: Optional schema name
        alias: Optional table alias

    Returns:
        RightJoin instance
    """
    return RightJoin(table, condition, schema, alias)


def full_join(
    table: str,
    condition: Condition,
    schema: Optional[str] = None,
    alias: Optional[str] = None,
) -> FullJoin:
    """Create FULL OUTER JOIN.

    Args:
        table: Table name to join
        condition: JOIN condition
        schema: Optional schema name
        alias: Optional table alias

    Returns:
        FullJoin instance
    """
    return FullJoin(table, condition, schema, alias)


def cross_join(
    table: str, schema: Optional[str] = None, alias: Optional[str] = None
) -> CrossJoin:
    """Create CROSS JOIN.

    Args:
        table: Table name to join
        schema: Optional schema name
        alias: Optional table alias

    Returns:
        CrossJoin instance
    """
    return CrossJoin(table, schema, alias)


def using_join(
    join_type: JoinType,
    table: str,
    columns: list,
    schema: Optional[str] = None,
    alias: Optional[str] = None,
) -> UsingJoin:
    """Create USING JOIN.

    Args:
        join_type: Type of JOIN
        table: Table name to join
        columns: Columns for USING clause
        schema: Optional schema name
        alias: Optional table alias

    Returns:
        UsingJoin instance
    """
    return UsingJoin(join_type, table, columns, schema, alias)


def natural_join(
    table: str, schema: Optional[str] = None, alias: Optional[str] = None
) -> NaturalJoin:
    """Create NATURAL JOIN.

    Args:
        table: Table name to join
        schema: Optional schema name
        alias: Optional table alias

    Returns:
        NaturalJoin instance
    """
    return NaturalJoin(JoinType.NATURAL, table, schema, alias)


def natural_left_join(
    table: str, schema: Optional[str] = None, alias: Optional[str] = None
) -> NaturalJoin:
    """Create NATURAL LEFT JOIN.

    Args:
        table: Table name to join
        schema: Optional schema name
        alias: Optional table alias

    Returns:
        NaturalJoin instance
    """
    return NaturalJoin(JoinType.NATURAL_LEFT, table, schema, alias)


def natural_right_join(
    table: str, schema: Optional[str] = None, alias: Optional[str] = None
) -> NaturalJoin:
    """Create NATURAL RIGHT JOIN.

    Args:
        table: Table name to join
        schema: Optional schema name
        alias: Optional table alias

    Returns:
        NaturalJoin instance
    """
    return NaturalJoin(JoinType.NATURAL_RIGHT, table, schema, alias)


def natural_full_join(
    table: str, schema: Optional[str] = None, alias: Optional[str] = None
) -> NaturalJoin:
    """Create NATURAL FULL OUTER JOIN.

    Args:
        table: Table name to join
        schema: Optional schema name
        alias: Optional table alias

    Returns:
        NaturalJoin instance
    """
    return NaturalJoin(JoinType.NATURAL_FULL, table, schema, alias)


def subquery_join(
    join_type: JoinType, subquery: str, alias: str, condition: Condition
) -> SubqueryJoin:
    """Create subquery JOIN.

    Args:
        join_type: Type of JOIN
        subquery: Subquery SQL
        alias: Subquery alias
        condition: JOIN condition

    Returns:
        SubqueryJoin instance
    """
    return SubqueryJoin(join_type, subquery, alias, condition)
