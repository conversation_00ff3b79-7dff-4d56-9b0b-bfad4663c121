__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

"""Data transformation classes for ETL operations.

This module provides advanced data transformation capabilities including
field transformations, validation, aggregation, and filtering.
"""

import re
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import date, datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from loguru import logger

from ..exceptions import TransformationError, ValidationError
from ..utils.progress_tracker import ProgressTracker


class TransformationType(Enum):
    """Types of transformations."""

    FIELD = "field"
    VALIDATION = "validation"
    AGGREGATION = "aggregation"
    FILTER = "filter"
    CUSTOM = "custom"


class DataType(Enum):
    """Supported data types for transformation."""

    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    DATE = "date"
    DATETIME = "datetime"
    JSON = "json"
    LIST = "list"


@dataclass
class TransformationRule:
    """Rule for data transformation."""

    name: str
    transformation_type: TransformationType
    source_fields: List[str]
    target_field: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    condition: Optional[Callable[[pd.Series], bool]] = None
    description: Optional[str] = None
    enabled: bool = True


@dataclass
class TransformationResult:
    """Result of transformation operation."""

    success: bool
    transformed_data: Optional[pd.DataFrame] = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)
    execution_time: Optional[float] = None


class Transformer(ABC):
    """Abstract base class for data transformers."""

    def __init__(self, name: str):
        """Initialize transformer.

        Args:
            name: Transformer name
        """
        self.name = name

    @abstractmethod
    def transform(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> TransformationResult:
        """Transform data according to rule.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            TransformationResult
        """
        pass

    def validate_rule(self, rule: TransformationRule) -> bool:
        """Validate transformation rule.

        Args:
            rule: Transformation rule

        Returns:
            True if valid

        Raises:
            ValidationError: If rule is invalid
        """
        if not rule.source_fields:
            raise ValidationError("Source fields cannot be empty")

        return True


class FieldTransformer(Transformer):
    """Transformer for field-level operations."""

    def __init__(self):
        """Initialize field transformer."""
        super().__init__("FieldTransformer")

    def transform(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> TransformationResult:
        """Transform fields according to rule.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            TransformationResult
        """
        try:
            start_time = datetime.now()
            result_data = data.copy()
            errors = []
            warnings = []

            # Validate rule
            self.validate_rule(rule)

            # Check if source fields exist
            missing_fields = [f for f in rule.source_fields if f not in data.columns]
            if missing_fields:
                raise TransformationError(f"Missing source fields: {missing_fields}")

            # Apply transformation based on parameters
            operation = rule.parameters.get("operation")

            if operation == "rename":
                result_data = self._rename_field(result_data, rule)
            elif operation == "convert_type":
                result_data = self._convert_type(result_data, rule)
            elif operation == "concatenate":
                result_data = self._concatenate_fields(result_data, rule)
            elif operation == "split":
                result_data = self._split_field(result_data, rule)
            elif operation == "extract":
                result_data = self._extract_pattern(result_data, rule)
            elif operation == "replace":
                result_data = self._replace_values(result_data, rule)
            elif operation == "normalize":
                result_data = self._normalize_field(result_data, rule)
            elif operation == "calculate":
                result_data = self._calculate_field(result_data, rule)
            else:
                raise TransformationError(f"Unknown field operation: {operation}")

            execution_time = (datetime.now() - start_time).total_seconds()

            return TransformationResult(
                success=True,
                transformed_data=result_data,
                errors=errors,
                warnings=warnings,
                statistics={
                    "rows_processed": len(data),
                    "fields_processed": len(rule.source_fields),
                },
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Field transformation failed: {e}")
            return TransformationResult(success=False, errors=[str(e)])

    def _rename_field(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Rename field.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Transformed data
        """
        old_name = rule.source_fields[0]
        new_name = rule.target_field or rule.parameters.get("new_name")

        if not new_name:
            raise TransformationError("New field name not specified")

        return data.rename(columns={old_name: new_name})

    def _convert_type(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Convert field data type.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Transformed data
        """
        field_name = rule.source_fields[0]
        target_type = rule.parameters.get("target_type")

        if not target_type:
            raise TransformationError("Target type not specified")

        try:
            if target_type == DataType.STRING.value:
                data[field_name] = data[field_name].astype(str)
            elif target_type == DataType.INTEGER.value:
                data[field_name] = pd.to_numeric(
                    data[field_name], errors="coerce"
                ).astype("Int64")
            elif target_type == DataType.FLOAT.value:
                data[field_name] = pd.to_numeric(data[field_name], errors="coerce")
            elif target_type == DataType.BOOLEAN.value:
                data[field_name] = data[field_name].astype(bool)
            elif target_type == DataType.DATE.value:
                data[field_name] = pd.to_datetime(
                    data[field_name], errors="coerce"
                ).dt.date
            elif target_type == DataType.DATETIME.value:
                data[field_name] = pd.to_datetime(data[field_name], errors="coerce")
            else:
                raise TransformationError(f"Unsupported target type: {target_type}")

        except Exception as e:
            raise TransformationError(f"Type conversion failed: {e}")

        return data

    def _concatenate_fields(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Concatenate multiple fields.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Transformed data
        """
        separator = rule.parameters.get("separator", "")
        target_field = rule.target_field or f"{'_'.join(rule.source_fields)}_concat"

        # Handle null values
        fill_na = rule.parameters.get("fill_na", "")

        concatenated = (
            data[rule.source_fields]
            .fillna(fill_na)
            .astype(str)
            .agg(separator.join, axis=1)
        )
        data[target_field] = concatenated

        return data

    def _split_field(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Split field into multiple fields.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Transformed data
        """
        field_name = rule.source_fields[0]
        separator = rule.parameters.get("separator", ",")
        target_fields = rule.parameters.get("target_fields", [])
        max_split = rule.parameters.get("max_split", -1)

        if not target_fields:
            raise TransformationError("Target fields not specified for split operation")

        # Split the field
        split_data = (
            data[field_name].astype(str).str.split(separator, n=max_split, expand=True)
        )

        # Assign to target fields
        for i, target_field in enumerate(target_fields):
            if i < split_data.shape[1]:
                data[target_field] = split_data[i]
            else:
                data[target_field] = None

        return data

    def _extract_pattern(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Extract pattern from field using regex.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Transformed data
        """
        field_name = rule.source_fields[0]
        pattern = rule.parameters.get("pattern")
        target_field = rule.target_field or f"{field_name}_extracted"

        if not pattern:
            raise TransformationError("Pattern not specified for extract operation")

        try:
            extracted = data[field_name].astype(str).str.extract(pattern, expand=False)
            data[target_field] = extracted
        except Exception as e:
            raise TransformationError(f"Pattern extraction failed: {e}")

        return data

    def _replace_values(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Replace values in field.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Transformed data
        """
        field_name = rule.source_fields[0]
        replacements = rule.parameters.get("replacements", {})
        use_regex = rule.parameters.get("use_regex", False)

        if not replacements:
            raise TransformationError("Replacements not specified")

        if use_regex:
            for pattern, replacement in replacements.items():
                data[field_name] = (
                    data[field_name]
                    .astype(str)
                    .str.replace(pattern, replacement, regex=True)
                )
        else:
            data[field_name] = data[field_name].replace(replacements)

        return data

    def _normalize_field(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Normalize field values.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Transformed data
        """
        field_name = rule.source_fields[0]
        method = rule.parameters.get("method", "minmax")

        if data[field_name].dtype not in ["int64", "float64"]:
            raise TransformationError("Normalization requires numeric data")

        if method == "minmax":
            min_val = data[field_name].min()
            max_val = data[field_name].max()
            if max_val != min_val:
                data[field_name] = (data[field_name] - min_val) / (max_val - min_val)
        elif method == "zscore":
            mean_val = data[field_name].mean()
            std_val = data[field_name].std()
            if std_val != 0:
                data[field_name] = (data[field_name] - mean_val) / std_val
        else:
            raise TransformationError(f"Unknown normalization method: {method}")

        return data

    def _calculate_field(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Calculate new field from existing fields.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Transformed data
        """
        expression = rule.parameters.get("expression")
        target_field = rule.target_field or f"calculated_field"

        if not expression:
            raise TransformationError("Expression not specified for calculation")

        try:
            # Create a safe evaluation environment
            eval_env = {field: data[field] for field in rule.source_fields}
            eval_env.update(
                {
                    "np": np,
                    "pd": pd,
                    "abs": abs,
                    "min": min,
                    "max": max,
                    "sum": sum,
                    "len": len,
                }
            )

            # Evaluate expression safely
            import ast

            try:
                # Try literal evaluation first (safer)
                result = ast.literal_eval(expression)
            except (ValueError, SyntaxError):
                # Fall back to restricted eval for complex expressions
                # Only allow safe built-ins
                safe_builtins = {
                    "abs": abs,
                    "min": min,
                    "max": max,
                    "round": round,
                    "len": len,
                    "sum": sum,
                    "int": int,
                    "float": float,
                    "str": str,
                    "bool": bool,
                }
                result = eval(expression, {"__builtins__": safe_builtins}, eval_env)
            data[target_field] = result

        except Exception as e:
            raise TransformationError(f"Calculation failed: {e}")

        return data


class ValidationTransformer(Transformer):
    """Transformer for data validation."""

    def __init__(self):
        """Initialize validation transformer."""
        super().__init__("ValidationTransformer")

    def transform(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> TransformationResult:
        """Validate data according to rule.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            TransformationResult
        """
        try:
            start_time = datetime.now()
            result_data = data.copy()
            errors = []
            warnings = []

            # Validate rule
            self.validate_rule(rule)

            validation_type = rule.parameters.get("validation_type")

            if validation_type == "not_null":
                errors.extend(self._validate_not_null(data, rule))
            elif validation_type == "range":
                errors.extend(self._validate_range(data, rule))
            elif validation_type == "pattern":
                errors.extend(self._validate_pattern(data, rule))
            elif validation_type == "unique":
                errors.extend(self._validate_unique(data, rule))
            elif validation_type == "custom":
                errors.extend(self._validate_custom(data, rule))
            else:
                raise TransformationError(f"Unknown validation type: {validation_type}")

            # Remove invalid rows if specified
            if rule.parameters.get("remove_invalid", False) and errors:
                # This is a simplified implementation
                # In practice, you'd need to track which rows are invalid
                pass

            execution_time = (datetime.now() - start_time).total_seconds()

            return TransformationResult(
                success=len(errors) == 0,
                transformed_data=result_data,
                errors=errors,
                warnings=warnings,
                statistics={
                    "rows_validated": len(data),
                    "validation_errors": len(errors),
                },
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Validation transformation failed: {e}")
            return TransformationResult(success=False, errors=[str(e)])

    def _validate_not_null(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> List[str]:
        """Validate that fields are not null.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            List of validation errors
        """
        errors = []

        for field in rule.source_fields:
            null_count = data[field].isnull().sum()
            if null_count > 0:
                errors.append(f"Field '{field}' has {null_count} null values")

        return errors

    def _validate_range(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> List[str]:
        """Validate that numeric fields are within range.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            List of validation errors
        """
        errors = []
        min_val = rule.parameters.get("min_value")
        max_val = rule.parameters.get("max_value")

        for field in rule.source_fields:
            if data[field].dtype not in ["int64", "float64"]:
                errors.append(f"Field '{field}' is not numeric for range validation")
                continue

            if min_val is not None:
                below_min = (data[field] < min_val).sum()
                if below_min > 0:
                    errors.append(
                        f"Field '{field}' has {below_min} values below minimum {min_val}"
                    )

            if max_val is not None:
                above_max = (data[field] > max_val).sum()
                if above_max > 0:
                    errors.append(
                        f"Field '{field}' has {above_max} values above maximum {max_val}"
                    )

        return errors

    def _validate_pattern(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> List[str]:
        """Validate that fields match pattern.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            List of validation errors
        """
        errors = []
        pattern = rule.parameters.get("pattern")

        if not pattern:
            errors.append("Pattern not specified for pattern validation")
            return errors

        for field in rule.source_fields:
            try:
                non_matching = ~data[field].astype(str).str.match(pattern)
                non_matching_count = non_matching.sum()
                if non_matching_count > 0:
                    errors.append(
                        f"Field '{field}' has {non_matching_count} values not matching pattern"
                    )
            except Exception as e:
                errors.append(f"Pattern validation failed for field '{field}': {e}")

        return errors

    def _validate_unique(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> List[str]:
        """Validate that fields have unique values.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            List of validation errors
        """
        errors = []

        for field in rule.source_fields:
            duplicate_count = data[field].duplicated().sum()
            if duplicate_count > 0:
                errors.append(f"Field '{field}' has {duplicate_count} duplicate values")

        return errors

    def _validate_custom(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> List[str]:
        """Validate using custom function.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            List of validation errors
        """
        errors = []
        validator_func = rule.parameters.get("validator_function")

        if not validator_func or not callable(validator_func):
            errors.append("Custom validator function not provided or not callable")
            return errors

        try:
            for field in rule.source_fields:
                validation_result = validator_func(data[field])
                if not validation_result:
                    errors.append(f"Custom validation failed for field '{field}'")
        except Exception as e:
            errors.append(f"Custom validation error: {e}")

        return errors


class AggregationTransformer(Transformer):
    """Transformer for data aggregation."""

    def __init__(self):
        """Initialize aggregation transformer."""
        super().__init__("AggregationTransformer")

    def transform(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> TransformationResult:
        """Aggregate data according to rule.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            TransformationResult
        """
        try:
            start_time = datetime.now()
            errors = []
            warnings = []

            # Validate rule
            self.validate_rule(rule)

            group_by_fields = rule.parameters.get("group_by", [])
            aggregation_functions = rule.parameters.get("aggregations", {})

            if not aggregation_functions:
                raise TransformationError("Aggregation functions not specified")

            if group_by_fields:
                # Group by aggregation
                grouped = data.groupby(group_by_fields)
                result_data = grouped.agg(aggregation_functions).reset_index()
            else:
                # Global aggregation
                agg_results = {}
                for field, func in aggregation_functions.items():
                    if field in data.columns:
                        if isinstance(func, str):
                            agg_results[f"{field}_{func}"] = getattr(
                                data[field], func
                            )()
                        elif callable(func):
                            agg_results[f"{field}_custom"] = func(data[field])

                result_data = pd.DataFrame([agg_results])

            execution_time = (datetime.now() - start_time).total_seconds()

            return TransformationResult(
                success=True,
                transformed_data=result_data,
                errors=errors,
                warnings=warnings,
                statistics={
                    "input_rows": len(data),
                    "output_rows": len(result_data),
                    "aggregation_functions": len(aggregation_functions),
                },
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Aggregation transformation failed: {e}")
            return TransformationResult(success=False, errors=[str(e)])


class FilterTransformer(Transformer):
    """Transformer for data filtering."""

    def __init__(self):
        """Initialize filter transformer."""
        super().__init__("FilterTransformer")

    def transform(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> TransformationResult:
        """Filter data according to rule.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            TransformationResult
        """
        try:
            start_time = datetime.now()
            errors = []
            warnings = []

            # Validate rule
            self.validate_rule(rule)

            filter_type = rule.parameters.get("filter_type")

            if filter_type == "condition":
                result_data = self._filter_by_condition(data, rule)
            elif filter_type == "range":
                result_data = self._filter_by_range(data, rule)
            elif filter_type == "values":
                result_data = self._filter_by_values(data, rule)
            elif filter_type == "custom":
                result_data = self._filter_by_custom(data, rule)
            else:
                raise TransformationError(f"Unknown filter type: {filter_type}")

            execution_time = (datetime.now() - start_time).total_seconds()

            return TransformationResult(
                success=True,
                transformed_data=result_data,
                errors=errors,
                warnings=warnings,
                statistics={
                    "input_rows": len(data),
                    "output_rows": len(result_data),
                    "filtered_rows": len(data) - len(result_data),
                },
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Filter transformation failed: {e}")
            return TransformationResult(success=False, errors=[str(e)])

    def _filter_by_condition(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Filter by condition expression.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Filtered data
        """
        condition = rule.parameters.get("condition")

        if not condition:
            raise TransformationError("Condition not specified")

        try:
            # Create evaluation environment
            eval_env = {col: data[col] for col in data.columns}
            eval_env.update({"pd": pd, "np": np})

            # Evaluate condition safely
            import ast

            try:
                # Try literal evaluation first (safer)
                mask = ast.literal_eval(condition)
            except (ValueError, SyntaxError):
                # Fall back to restricted eval for complex conditions
                # Only allow safe built-ins and comparison operators
                safe_builtins = {
                    "abs": abs,
                    "min": min,
                    "max": max,
                    "round": round,
                    "len": len,
                    "sum": sum,
                    "int": int,
                    "float": float,
                    "str": str,
                    "bool": bool,
                }
                mask = eval(condition, {"__builtins__": safe_builtins}, eval_env)
            return data[mask]

        except Exception as e:
            raise TransformationError(f"Condition evaluation failed: {e}")

    def _filter_by_range(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Filter by numeric range.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Filtered data
        """
        field = rule.source_fields[0]
        min_val = rule.parameters.get("min_value")
        max_val = rule.parameters.get("max_value")

        mask = pd.Series([True] * len(data))

        if min_val is not None:
            mask &= data[field] >= min_val

        if max_val is not None:
            mask &= data[field] <= max_val

        return data[mask]

    def _filter_by_values(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Filter by specific values.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Filtered data
        """
        field = rule.source_fields[0]
        include_values = rule.parameters.get("include_values", [])
        exclude_values = rule.parameters.get("exclude_values", [])

        mask = pd.Series([True] * len(data))

        if include_values:
            mask &= data[field].isin(include_values)

        if exclude_values:
            mask &= ~data[field].isin(exclude_values)

        return data[mask]

    def _filter_by_custom(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> pd.DataFrame:
        """Filter using custom function.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            Filtered data
        """
        filter_func = rule.parameters.get("filter_function")

        if not filter_func or not callable(filter_func):
            raise TransformationError(
                "Custom filter function not provided or not callable"
            )

        try:
            mask = filter_func(data)
            return data[mask]
        except Exception as e:
            raise TransformationError(f"Custom filter failed: {e}")


class DataTransformer:
    """Main data transformer class."""

    def __init__(self):
        """Initialize data transformer."""
        self.transformers = {
            TransformationType.FIELD: FieldTransformer(),
            TransformationType.VALIDATION: ValidationTransformer(),
            TransformationType.AGGREGATION: AggregationTransformer(),
            TransformationType.FILTER: FilterTransformer(),
        }

    def transform(
        self, data: pd.DataFrame, rule: TransformationRule
    ) -> TransformationResult:
        """Transform data using appropriate transformer.

        Args:
            data: Input data
            rule: Transformation rule

        Returns:
            TransformationResult
        """
        if not rule.enabled:
            return TransformationResult(
                success=True,
                transformed_data=data,
                warnings=[f"Rule '{rule.name}' is disabled"],
            )

        transformer = self.transformers.get(rule.transformation_type)
        if not transformer:
            return TransformationResult(
                success=False,
                errors=[f"No transformer found for type: {rule.transformation_type}"],
            )

        return transformer.transform(data, rule)

    def add_transformer(
        self, transformation_type: TransformationType, transformer: Transformer
    ):
        """Add custom transformer.

        Args:
            transformation_type: Type of transformation
            transformer: Transformer instance
        """
        self.transformers[transformation_type] = transformer


class TransformationPipeline:
    """Pipeline for executing multiple transformations."""

    def __init__(self, name: str = "TransformationPipeline"):
        """Initialize transformation pipeline.

        Args:
            name: Pipeline name
        """
        self.name = name
        self.rules: List[TransformationRule] = []
        self.transformer = DataTransformer()

    def add_rule(self, rule: TransformationRule):
        """Add transformation rule to pipeline.

        Args:
            rule: Transformation rule
        """
        self.rules.append(rule)

    def remove_rule(self, rule_name: str):
        """Remove transformation rule from pipeline.

        Args:
            rule_name: Name of rule to remove
        """
        self.rules = [rule for rule in self.rules if rule.name != rule_name]

    def execute(
        self, data: pd.DataFrame, progress_tracker: Optional[ProgressTracker] = None
    ) -> TransformationResult:
        """Execute all transformation rules in pipeline.

        Args:
            data: Input data
            progress_tracker: Optional progress tracker

        Returns:
            Combined TransformationResult
        """
        try:
            start_time = datetime.now()
            current_data = data.copy()
            all_errors = []
            all_warnings = []
            all_statistics = {}

            if progress_tracker:
                progress_tracker.start_task(
                    f"Executing pipeline: {self.name}", len(self.rules)
                )

            for i, rule in enumerate(self.rules):
                try:
                    logger.info(f"Executing rule: {rule.name}")

                    # Apply condition if specified
                    if rule.condition and not rule.condition(current_data):
                        logger.info(f"Skipping rule '{rule.name}' - condition not met")
                        continue

                    result = self.transformer.transform(current_data, rule)

                    if result.success:
                        current_data = result.transformed_data
                        logger.info(f"Rule '{rule.name}' executed successfully")
                    else:
                        all_errors.extend(result.errors)
                        logger.error(f"Rule '{rule.name}' failed: {result.errors}")

                    all_warnings.extend(result.warnings)
                    all_statistics[rule.name] = result.statistics

                    if progress_tracker:
                        progress_tracker.update_progress(i + 1)

                except Exception as e:
                    error_msg = f"Rule '{rule.name}' execution failed: {e}"
                    all_errors.append(error_msg)
                    logger.error(error_msg)

            if progress_tracker:
                progress_tracker.complete_task()

            execution_time = (datetime.now() - start_time).total_seconds()

            return TransformationResult(
                success=len(all_errors) == 0,
                transformed_data=current_data,
                errors=all_errors,
                warnings=all_warnings,
                statistics={
                    "pipeline_name": self.name,
                    "rules_executed": len(self.rules),
                    "input_rows": len(data),
                    "output_rows": len(current_data),
                    "rule_statistics": all_statistics,
                },
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Pipeline execution failed: {e}")
            return TransformationResult(
                success=False, errors=[f"Pipeline execution failed: {e}"]
            )

    def validate_pipeline(self) -> List[str]:
        """Validate all rules in pipeline.

        Returns:
            List of validation errors
        """
        errors = []

        for rule in self.rules:
            try:
                transformer = self.transformer.transformers.get(
                    rule.transformation_type
                )
                if transformer:
                    transformer.validate_rule(rule)
                else:
                    errors.append(
                        f"No transformer found for rule '{rule.name}' type: {rule.transformation_type}"
                    )
            except ValidationError as e:
                errors.append(f"Rule '{rule.name}' validation failed: {e}")

        return errors

    def get_rule_by_name(self, name: str) -> Optional[TransformationRule]:
        """Get rule by name.

        Args:
            name: Rule name

        Returns:
            TransformationRule if found, None otherwise
        """
        for rule in self.rules:
            if rule.name == name:
                return rule
        return None

    def enable_rule(self, name: str):
        """Enable rule by name.

        Args:
            name: Rule name
        """
        rule = self.get_rule_by_name(name)
        if rule:
            rule.enabled = True

    def disable_rule(self, name: str):
        """Disable rule by name.

        Args:
            name: Rule name
        """
        rule = self.get_rule_by_name(name)
        if rule:
            rule.enabled = False

    def get_statistics(self) -> Dict[str, Any]:
        """Get pipeline statistics.

        Returns:
            Pipeline statistics
        """
        return {
            "name": self.name,
            "total_rules": len(self.rules),
            "enabled_rules": len([r for r in self.rules if r.enabled]),
            "disabled_rules": len([r for r in self.rules if not r.enabled]),
            "rule_types": {
                t.value: len([r for r in self.rules if r.transformation_type == t])
                for t in TransformationType
            },
        }
