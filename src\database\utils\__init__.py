#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""Utilities package.

This package contains utility modules for the database framework,
including validators, helpers, security tools, performance utilities,
and batch processing tools.
"""

from .batch_processor import (
    BatchProcessor,
    bulk_insert_batched,
    bulk_insert_with_copy,
    generate_batches,
)
from .decorators import (
    cache_result,
    deprecated,
    rate_limit,
    retry,
    singleton,
    timeout,
    validate_args,
)
from .helpers import (
    camel_to_snake,
    chunk_list,
    deep_merge_dicts,
    ensure_directory_exists,
    flatten_dict,
    format_bytes,
    get_file_extension,
    is_valid_email,
    safe_file_name,
    sanitize_string,
    snake_to_camel,
    truncate_string,
)
from .performance import PerformanceMonitor, PerformanceTimer, time_execution
from .security import SQLInjectionGuard
from .validators import InputValidator

__all__ = [
    # Validators
    "InputValidator",
    # Helpers
    "sanitize_string",
    "snake_to_camel",
    "camel_to_snake",
    "ensure_directory_exists",
    "safe_file_name",
    "get_file_extension",
    "flatten_dict",
    "chunk_list",
    "deep_merge_dicts",
    "format_bytes",
    "is_valid_email",
    "truncate_string",
    # Security
    "SQLInjectionGuard",
    # Performance
    "PerformanceTimer",
    "PerformanceMonitor",
    "time_execution",
    # Decorators
    "retry",
    "timeout",
    "validate_args",
    "cache_result",
    "deprecated",
    "singleton",
    "rate_limit",
    # Batch Processing
    "generate_batches",
    "bulk_insert_batched",
    "bulk_insert_with_copy",
    "BatchProcessor",
]
