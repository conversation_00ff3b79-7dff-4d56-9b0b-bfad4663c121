"""磁盘清理CLI工具"""
import asyncio
import click
import logging
from pathlib import Path
from typing import Optional

from ..utils.disk_space_manager import get_disk_manager, check_and_cleanup_if_needed

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@click.group()
@click.option('--project-root', type=click.Path(exists=True, path_type=Path), 
              help='项目根目录路径')
@click.pass_context
def disk_cleanup(ctx, project_root: Optional[Path]):
    """磁盘空间清理工具"""
    ctx.ensure_object(dict)
    ctx.obj['project_root'] = project_root or Path.cwd()

@disk_cleanup.command()
@click.pass_context
def status(ctx):
    """检查磁盘空间状态"""
    project_root = ctx.obj['project_root']
    manager = get_disk_manager(project_root)
    
    click.echo("\n=== 磁盘空间状态 ===")
    
    # 获取磁盘信息
    disk_info = manager.get_disk_space_info()
    
    click.echo(f"总空间: {disk_info.total_gb:.2f} GB")
    click.echo(f"已使用: {disk_info.used_gb:.2f} GB")
    click.echo(f"可用空间: {disk_info.free_gb:.2f} GB")
    click.echo(f"使用率: {disk_info.usage_percent:.1f}%")
    
    # 状态指示
    if disk_info.is_critical:
        click.echo(click.style("⚠️  状态: 临界 (>90%)", fg='red', bold=True))
        click.echo("建议立即执行清理操作！")
    elif disk_info.is_warning:
        click.echo(click.style("⚠️  状态: 警告 (>80%)", fg='yellow', bold=True))
        click.echo("建议执行清理操作")
    else:
        click.echo(click.style("✅ 状态: 正常", fg='green', bold=True))
    
    # 监控状态
    status_info = manager.monitor_disk_space()
    if status_info['recommendations']:
        click.echo("\n建议操作:")
        for rec in status_info['recommendations']:
            click.echo(f"  • {rec}")

@disk_cleanup.command()
@click.pass_context
def temp(ctx):
    """清理临时文件"""
    project_root = ctx.obj['project_root']
    manager = get_disk_manager(project_root)
    
    click.echo("\n=== 清理临时文件 ===")
    
    with click.progressbar(length=1, label='清理中...') as bar:
        result = manager.cleanup_temp_files()
        bar.update(1)
    
    click.echo(f"\n✅ 清理完成:")
    click.echo(f"  清理文件数: {result.cleaned_files}")
    click.echo(f"  释放空间: {result.freed_space_mb:.2f} MB")
    
    if result.errors:
        click.echo(f"\n⚠️  发生 {len(result.errors)} 个错误:")
        for error in result.errors[:5]:  # 只显示前5个错误
            click.echo(f"  • {error}")
        if len(result.errors) > 5:
            click.echo(f"  ... 还有 {len(result.errors) - 5} 个错误")

@disk_cleanup.command()
@click.pass_context
def cache(ctx):
    """清理Python缓存文件"""
    project_root = ctx.obj['project_root']
    manager = get_disk_manager(project_root)
    
    click.echo("\n=== 清理Python缓存 ===")
    
    with click.progressbar(length=1, label='清理中...') as bar:
        result = manager.cleanup_python_cache()
        bar.update(1)
    
    click.echo(f"\n✅ 清理完成:")
    click.echo(f"  清理文件数: {result.cleaned_files}")
    click.echo(f"  释放空间: {result.freed_space_mb:.2f} MB")
    
    if result.errors:
        click.echo(f"\n⚠️  发生 {len(result.errors)} 个错误:")
        for error in result.errors[:5]:
            click.echo(f"  • {error}")

@disk_cleanup.command()
@click.option('--days', default=7, help='清理多少天前的日志文件')
@click.pass_context
def logs(ctx, days: int):
    """清理旧日志文件"""
    project_root = ctx.obj['project_root']
    manager = get_disk_manager(project_root)
    
    click.echo(f"\n=== 清理 {days} 天前的日志文件 ===")
    
    with click.progressbar(length=1, label='清理中...') as bar:
        result = manager.cleanup_logs(max_age_days=days)
        bar.update(1)
    
    click.echo(f"\n✅ 清理完成:")
    click.echo(f"  清理文件数: {result.cleaned_files}")
    click.echo(f"  释放空间: {result.freed_space_mb:.2f} MB")
    
    if result.errors:
        click.echo(f"\n⚠️  发生 {len(result.errors)} 个错误:")
        for error in result.errors[:5]:
            click.echo(f"  • {error}")

@disk_cleanup.command()
@click.pass_context
def emergency(ctx):
    """紧急清理 - 清理所有可清理的文件"""
    project_root = ctx.obj['project_root']
    manager = get_disk_manager(project_root)
    
    # 先检查状态
    disk_info = manager.get_disk_space_info()
    click.echo(f"\n当前磁盘使用率: {disk_info.usage_percent:.1f}%")
    
    if not disk_info.is_warning:
        if not click.confirm("磁盘空间充足，确定要执行紧急清理吗？"):
            return
    
    click.echo("\n=== 执行紧急清理 ===")
    click.echo("这将清理所有临时文件、缓存和旧日志...")
    
    with click.progressbar(length=1, label='紧急清理中...') as bar:
        result = manager.emergency_cleanup()
        bar.update(1)
    
    # 显示结果
    click.echo(f"\n✅ 紧急清理完成:")
    click.echo(f"  清理文件数: {result.cleaned_files}")
    click.echo(f"  释放空间: {result.freed_space_mb:.2f} MB")
    
    # 显示清理后的状态
    new_disk_info = manager.get_disk_space_info()
    click.echo(f"\n清理后磁盘状态:")
    click.echo(f"  使用率: {new_disk_info.usage_percent:.1f}%")
    click.echo(f"  可用空间: {new_disk_info.free_gb:.2f} GB")
    
    if result.errors:
        click.echo(f"\n⚠️  发生 {len(result.errors)} 个错误:")
        for error in result.errors[:5]:
            click.echo(f"  • {error}")
        if len(result.errors) > 5:
            click.echo(f"  ... 还有 {len(result.errors) - 5} 个错误")

@disk_cleanup.command()
@click.option('--min-size', default=100, help='最小文件大小(MB)')
@click.option('--limit', default=10, help='显示文件数量限制')
@click.pass_context
def large_files(ctx, min_size: int, limit: int):
    """查找大文件"""
    project_root = ctx.obj['project_root']
    manager = get_disk_manager(project_root)
    
    click.echo(f"\n=== 查找大于 {min_size} MB 的文件 ===")
    
    with click.progressbar(length=1, label='搜索中...') as bar:
        large_files = manager.get_large_files(min_size_mb=min_size, limit=limit)
        bar.update(1)
    
    if not large_files:
        click.echo(f"\n未找到大于 {min_size} MB 的文件")
        return
    
    click.echo(f"\n找到 {len(large_files)} 个大文件:")
    click.echo("\n{:<60} {:>10}".format("文件路径", "大小(MB)"))
    click.echo("-" * 72)
    
    for file_path, size_mb in large_files:
        # 截断长路径
        path_str = str(file_path)
        if len(path_str) > 55:
            path_str = "..." + path_str[-52:]
        
        click.echo("{:<60} {:>10.2f}".format(path_str, size_mb))
    
    total_size = sum(size for _, size in large_files)
    click.echo("-" * 72)
    click.echo("{:<60} {:>10.2f}".format("总计", total_size))

@disk_cleanup.command()
@click.pass_context
def auto(ctx):
    """自动检查并清理（如果需要）"""
    project_root = ctx.obj['project_root']
    
    click.echo("\n=== 自动磁盘空间检查和清理 ===")
    
    success = check_and_cleanup_if_needed(project_root)
    
    if success:
        click.echo(click.style("✅ 磁盘空间充足", fg='green', bold=True))
    else:
        click.echo(click.style("❌ 磁盘空间仍然不足，请手动清理或扩展磁盘", fg='red', bold=True))
        
        # 显示建议
        manager = get_disk_manager(project_root)
        status_info = manager.monitor_disk_space()
        if status_info['recommendations']:
            click.echo("\n建议操作:")
            for rec in status_info['recommendations']:
                click.echo(f"  • {rec}")

if __name__ == '__main__':
    disk_cleanup()