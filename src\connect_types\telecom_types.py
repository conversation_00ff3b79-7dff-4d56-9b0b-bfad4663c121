"""Type definitions for telecommunications data processing

This module provides comprehensive type definitions for telecommunications
data structures, ensuring type safety across the Connect system.
"""

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"

from typing import Dict, List, Optional, Union, Any, Tuple, Protocol, TypeVar, Generic, Callable, Awaitable, Literal
from datetime import datetime, <PERSON><PERSON><PERSON>
from enum import Enum
from dataclasses import dataclass
import pandas as pd
import geopandas as gpd
from pathlib import Path

# Type variables
T = TypeVar('T')
DataFrameType = TypeVar('DataFrameType', bound=pd.DataFrame)
GeoDataFrameType = TypeVar('GeoDataFrameType', bound=gpd.GeoDataFrame)

# Basic types
Coordinate = Tuple[float, float]  # (longitude, latitude)
BoundingBox = Tuple[float, float, float, float]  # (min_lon, min_lat, max_lon, max_lat)
TimeRange = Tuple[datetime, datetime]  # (start_time, end_time)

# File path types
FilePath = Union[str, Path]
FilePathList = List[FilePath]

# Database types
ConnectionString = str
TableName = str
ColumnName = str
QueryString = str

# Configuration types
ConfigDict = Dict[str, Any]
EnvironmentVariables = Dict[str, str]


class DataSourceType(str, Enum):
    """Data source type enumeration"""
    CDR = "cdr"  # Call Detail Records
    EP = "ep"    # Energy Points / External Provider
    KPI = "kpi"  # Key Performance Indicators
    NLG = "nlg"  # Network Log
    CFG = "cfg"  # Configuration data
    SCORE = "score"  # Score data


class ProcessingStatus(str, Enum):
    """Processing status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class SignalType(str, Enum):
    """Signal measurement type enumeration"""
    RSRP = "rsrp"  # Reference Signal Received Power
    RSRQ = "rsrq"  # Reference Signal Received Quality
    SINR = "sinr"  # Signal to Interference plus Noise Ratio
    RSSI = "rssi"  # Received Signal Strength Indicator


class NetworkTechnology(str, Enum):
    """Network technology enumeration"""
    GSM = "gsm"
    UMTS = "umts"
    LTE = "lte"
    NR = "nr"  # 5G New Radio


class CallStatus(str, Enum):
    """Call status enumeration"""
    INITIATED = "initiated"
    CONNECTED = "connected"
    COMPLETED = "completed"
    FAILED = "failed"
    DROPPED = "dropped"


class CallType(str, Enum):
    """Call type enumeration"""
    VOICE = "voice"
    SMS = "sms"
    DATA = "data"
    VIDEO = "video"
    EMERGENCY = "emergency"
    ROAMING = "roaming"


@dataclass
class CDRRecord:
    """Call Detail Record structure"""
    call_id: str
    caller_number: str
    called_number: str
    call_start_time: datetime
    call_end_time: Optional[datetime]
    call_duration: Optional[int]  # seconds
    call_status: CallStatus
    cell_id: Optional[str]
    lac: Optional[int]  # Location Area Code
    imsi: Optional[str]
    cell_tower_lat: Optional[float]
    cell_tower_lon: Optional[float]
    network_technology: Optional[NetworkTechnology]


@dataclass
class EPRecord:
    """Energy Point / External Provider record structure"""
    measurement_id: str
    timestamp: datetime
    latitude: float
    longitude: float
    rsrp: Optional[float]
    rsrq: Optional[float]
    sinr: Optional[float]
    cell_id: Optional[str]
    frequency: Optional[float]
    network_technology: Optional[NetworkTechnology]
    signal_quality: Optional[str]


@dataclass
class KPIRecord:
    """KPI record structure"""
    kpi_id: str
    kpi_type: str
    value: float
    unit: str
    entity_id: str
    entity_type: str  # cell, site, region, network
    calculation_time: datetime
    data_points: int
    threshold: Optional[float]
    meets_threshold: bool


@dataclass
class ImportResult:
    """Import operation result"""
    success: bool
    records_imported: int = 0
    records_failed: int = 0
    source_path: Optional[Path] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    file_size_bytes: Optional[int] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ValidationResult:
    """Data validation result"""
    is_valid: bool
    errors: List[str] = None
    warnings: List[str] = None
    validated_records: int = 0
    invalid_records: int = 0

    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


@dataclass
class ProcessingMetrics:
    """Processing performance metrics"""
    start_time: datetime
    end_time: Optional[datetime] = None
    processing_time_seconds: float = 0.0
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    records_per_second: float = 0.0
    throughput_mbps: float = 0.0


@dataclass
class DatabaseConfig:
    """Database configuration structure"""
    host: str
    port: int
    name: str
    user: str
    password: str
    ssl_mode: str = "prefer"
    pool_min_size: int = 5
    pool_max_size: int = 20
    pool_timeout: int = 30
    connection_timeout: int = 30
    command_timeout: int = 60


@dataclass
class GeospatialConfig:
    """Geospatial processing configuration"""
    default_crs: str = "EPSG:4326"
    output_format: str = "GeoJSON"
    precision: int = 6
    buffer_resolution: int = 16
    qgis_auto_detect: bool = True
    qgis_path: Optional[str] = None


@dataclass
class ProcessingConfig:
    """Data processing configuration"""
    batch_size: int = 1000
    max_workers: int = 4
    timeout_seconds: int = 300
    memory_limit_mb: int = 1024
    enable_parallel: bool = True
    chunk_size: int = 10000


@dataclass
class TelecomConfig:
    """Telecommunications processing configuration"""
    cdr_batch_size: int = 10000
    ep_batch_size: int = 5000
    kpi_calculation_interval: int = 300  # seconds
    max_memory_usage_mb: int = 2048
    processing_timeout_seconds: int = 3600
    parallel_workers: int = 4


@dataclass
class ProcessingResult:
    """Data processing result"""
    success: bool
    records_processed: int = 0
    records_skipped: int = 0
    processing_time: float = 0.0
    error_message: Optional[str] = None
    warnings: List[str] = None
    metrics: Optional[ProcessingMetrics] = None
    output_data: Optional[pd.DataFrame] = None

    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


# Protocol definitions for type checking
class DataImporter(Protocol):
    """Protocol for data importers"""
    
    def import_file(self, file_path: FilePath) -> ImportResult:
        """Import data from file"""
        ...
    
    def validate_data(self, data: pd.DataFrame) -> ValidationResult:
        """Validate data structure and values"""
        ...


class DataProcessor(Protocol):
    """Protocol for data processors"""
    
    def process_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Process data with transformations"""
        ...
    
    def calculate_metrics(self, data: pd.DataFrame) -> ProcessingMetrics:
        """Calculate processing metrics"""
        ...


class KPICalculator(Protocol):
    """Protocol for KPI calculators"""
    
    def calculate_kpi(self, data: pd.DataFrame, kpi_type: str) -> List[KPIRecord]:
        """Calculate specific KPI from data"""
        ...
    
    def get_thresholds(self) -> Dict[str, float]:
        """Get KPI thresholds"""
        ...


# Generic types for data containers
class DataContainer(Generic[T]):
    """Generic data container with metadata"""
    
    def __init__(self, data: T, metadata: Optional[Dict[str, Any]] = None):
        self.data = data
        self.metadata = metadata or {}
        self.created_at = datetime.now()
    
    def get_size(self) -> int:
        """Get size of contained data"""
        if hasattr(self.data, '__len__'):
            return len(self.data)
        return 1
    
    def is_empty(self) -> bool:
        """Check if container is empty"""
        if hasattr(self.data, 'empty'):
            return self.data.empty
        return self.get_size() == 0


# Specialized data containers
CDRContainer = DataContainer[pd.DataFrame]
EPContainer = DataContainer[gpd.GeoDataFrame]
KPIContainer = DataContainer[pd.DataFrame]

# Function type definitions
DataTransformer = Callable[[pd.DataFrame], pd.DataFrame]
DataValidator = Callable[[pd.DataFrame], ValidationResult]
KPICalculatorFunc = Callable[[pd.DataFrame], List[KPIRecord]]
ErrorHandler = Callable[[Exception, Dict[str, Any]], None]

# Async function types
AsyncDataImporter = Callable[[FilePath], Awaitable[ImportResult]]
AsyncDataProcessor = Callable[[pd.DataFrame], Awaitable[pd.DataFrame]]
AsyncKPICalculator = Callable[[pd.DataFrame], Awaitable[List[KPIRecord]]]

# Complex type combinations
ProcessingPipeline = List[Union[DataTransformer, DataValidator]]
ImporterRegistry = Dict[DataSourceType, DataImporter]
ProcessorRegistry = Dict[str, DataProcessor]
KPIRegistry = Dict[str, KPICalculator]

# Configuration type combinations
DatabaseConnectionConfig = Dict[str, Union[str, int, bool]]
LoggingConfig = Dict[str, Union[str, int, bool]]
MonitoringConfig = Dict[str, Union[str, int, float, bool, Dict[str, float]]]

# Result type combinations
BatchProcessingResult = Dict[str, Union[int, float, List[ImportResult]]]
KPICalculationResult = Dict[str, List[KPIRecord]]
ValidationResults = Dict[str, ValidationResult]

# Spatial analysis types
SpatialAnalysisResult = Dict[str, Union[float, int, Dict[str, Any]]]
CoverageAnalysisResult = Dict[str, Union[float, List[Dict[str, Any]]]]
SignalAnalysisResult = Dict[str, Union[float, Dict[str, float]]]

# Time series types
TimeSeriesData = pd.DataFrame  # DataFrame with datetime index
KPITimeSeries = Dict[str, TimeSeriesData]
TrendAnalysisResult = Dict[str, Union[str, float, List[float]]]

# Export types
ExportFormat = Literal['csv', 'excel', 'json', 'geojson', 'parquet']
ExportOptions = Dict[str, Any]
ExportResult = Dict[str, Union[bool, str, int]]
