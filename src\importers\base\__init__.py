"""Base classes and mixins for telecommunications data importers.

This module provides the foundational architecture for all data importers
in the Connect telecommunications system, including abstract base classes,
validation mixins, processing mixins, and performance monitoring.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

from .abstract_importer import (
    AbstractImporter,
    ImporterConfig,
    ImportStatus,
    ImportResult,
    ImportMetrics,
    DataEngine,
    TelecomImportError,
    ValidationError,
    ProcessingError,
    ConfigurationError
)

from .validation_mixin import (
    ValidationMixin,
    ValidationRule,
    ValidationResult,
    TelecomValidationConfig
)

from .processing_mixin import (
    ProcessingMixin,
    ProcessingConfig,
    ProcessingMetrics
)

from .performance_mixin import (
    PerformanceMixin,
    PerformanceConfig,
    PerformanceSnapshot,
    OperationMetrics
)

__all__ = [
    # Abstract base classes
    'AbstractImporter',
    'ImporterConfig',
    'ImportStatus',
    'ImportResult',
    'ImportMetrics',
    'DataEngine',
    
    # Exception classes
    'TelecomImportError',
    'ValidationError',
    'ProcessingError',
    'ConfigurationError',
    
    # Validation mixin
    'ValidationMixin',
    'ValidationRule',
    'ValidationResult',
    'TelecomValidationConfig',
    
    # Processing mixin
    'ProcessingMixin',
    'ProcessingConfig',
    'ProcessingMetrics',
    
    # Performance mixin
    'PerformanceMixin',
    'PerformanceConfig',
    'PerformanceSnapshot',
    'OperationMetrics'
]

# Version information
__version__ = '2.0.0'
__description__ = 'Connect Telecommunications Data Importer Architecture'

# Module-level configuration
DEFAULT_CONFIG = {
    'validation': {
        'enable_strict_validation': True,
        'max_error_rate': 0.05,
        'enable_data_quality_checks': True
    },
    'processing': {
        'batch_size': 10000,
        'max_workers': 4,
        'memory_limit_mb': 1024,
        'enable_parallel': True
    },
    'performance': {
        'enable_monitoring': True,
        'snapshot_interval_seconds': 1.0,
        'enable_memory_optimization': True
    }
}


def get_default_config() -> dict:
    """Get default configuration for importers.
    
    Returns:
        Default configuration dictionary
    """
    return DEFAULT_CONFIG.copy()


def create_importer_config(**kwargs) -> ImporterConfig:
    """Create importer configuration with defaults.
    
    Args:
        **kwargs: Configuration overrides
        
    Returns:
        ImporterConfig instance
    """
    config_dict = get_default_config()
    config_dict.update(kwargs)
    return ImporterConfig(**config_dict)