"""Batch processing for ETL operations.

This module provides batch processing capabilities for handling large datasets
that cannot fit in memory or need to be processed in chunks.
"""

import asyncio
import concurrent.futures
import gc
import time
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, Iterator, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import psutil
from loguru import logger

from ..exceptions import ProcessingError
from ..utils.progress_tracker import ProgressTracker
from .transformer import DataTransformer, TransformationRule
from .validator import DataValidator, ValidationResult


class BatchStrategy(Enum):
    """Batch processing strategies."""

    FIXED_SIZE = "fixed_size"
    MEMORY_BASED = "memory_based"
    TIME_BASED = "time_based"
    ADAPTIVE = "adaptive"
    CUSTOM = "custom"


class ProcessingMode(Enum):
    """Processing modes."""

    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    ASYNC = "async"


@dataclass
class BatchConfig:
    """Batch processing configuration."""

    strategy: BatchStrategy = BatchStrategy.FIXED_SIZE
    batch_size: int = 10000
    max_memory_mb: float = 1024.0
    max_processing_time: float = 300.0  # seconds
    overlap_rows: int = 0
    processing_mode: ProcessingMode = ProcessingMode.SEQUENTIAL
    max_workers: int = 4
    chunk_size_multiplier: float = 1.0
    memory_threshold: float = 0.8  # 80% of available memory
    enable_gc: bool = True
    gc_frequency: int = 10  # Run GC every N batches


@dataclass
class BatchInfo:
    """Information about a batch."""

    batch_id: int
    start_index: int
    end_index: int
    size: int
    memory_usage_mb: float = 0.0
    processing_time: float = 0.0
    status: str = "pending"
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BatchResult:
    """Result of batch processing."""

    batch_info: BatchInfo
    data: Optional[pd.DataFrame] = None
    success: bool = True
    error: Optional[str] = None
    validation_result: Optional[ValidationResult] = None
    metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BatchProcessingResult:
    """Overall result of batch processing operation."""

    success: bool
    total_batches: int = 0
    successful_batches: int = 0
    failed_batches: int = 0
    total_rows_processed: int = 0
    total_processing_time: float = 0.0
    average_batch_time: float = 0.0
    peak_memory_usage_mb: float = 0.0
    batch_results: List[BatchResult] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    statistics: Dict[str, Any] = field(default_factory=dict)


class BatchProcessor:
    """Batch processor for ETL operations."""

    def __init__(self, config: Optional[BatchConfig] = None):
        """Initialize batch processor.

        Args:
            config: Batch processing configuration
        """
        self.config = config or BatchConfig()
        self.transformers: List[DataTransformer] = []
        self.validators: List[DataValidator] = []
        self.custom_processors: Dict[str, Callable] = {}
        self.batch_counter = 0
        self.peak_memory = 0.0

    def add_transformer(self, transformer: DataTransformer) -> None:
        """Add data transformer.

        Args:
            transformer: Data transformer to add
        """
        self.transformers.append(transformer)
        logger.debug(f"Added transformer to batch processor")

    def add_validator(self, validator: DataValidator) -> None:
        """Add data validator.

        Args:
            validator: Data validator to add
        """
        self.validators.append(validator)
        logger.debug(f"Added validator to batch processor")

    def register_custom_processor(self, name: str, processor_func: Callable) -> None:
        """Register custom processor function.

        Args:
            name: Name of processor
            processor_func: Processor function
        """
        self.custom_processors[name] = processor_func
        logger.debug(f"Registered custom processor: {name}")

    def process_dataframe(
        self, data: pd.DataFrame, progress_tracker: Optional[ProgressTracker] = None
    ) -> BatchProcessingResult:
        """Process DataFrame in batches.

        Args:
            data: DataFrame to process
            progress_tracker: Optional progress tracker

        Returns:
            BatchProcessingResult
        """
        try:
            start_time = time.time()
            logger.info(f"Starting batch processing of DataFrame with {len(data)} rows")

            if data.empty:
                logger.warning("Empty DataFrame provided for batch processing")
                return BatchProcessingResult(
                    success=False, errors=["DataFrame is empty"]
                )

            # Generate batches
            batches = list(self._generate_batches(data))

            if progress_tracker:
                progress_tracker.start_task("Processing batches", len(batches))

            # Process batches based on mode
            if self.config.processing_mode == ProcessingMode.SEQUENTIAL:
                batch_results = self._process_batches_sequential(
                    data, batches, progress_tracker
                )
            elif self.config.processing_mode == ProcessingMode.PARALLEL:
                batch_results = self._process_batches_parallel(
                    data, batches, progress_tracker
                )
            elif self.config.processing_mode == ProcessingMode.ASYNC:
                batch_results = asyncio.run(
                    self._process_batches_async(data, batches, progress_tracker)
                )
            else:
                raise ProcessingError(
                    f"Unknown processing mode: {self.config.processing_mode}"
                )

            if progress_tracker:
                progress_tracker.complete_task()

            # Calculate results
            total_time = time.time() - start_time
            successful_results = [r for r in batch_results if r.success]
            failed_results = [r for r in batch_results if not r.success]

            total_rows = sum(r.batch_info.size for r in successful_results)
            avg_batch_time = total_time / len(batches) if batches else 0

            # Collect errors
            errors = [r.error for r in failed_results if r.error]

            # Calculate statistics
            statistics = self._calculate_statistics(batch_results)

            logger.info(
                f"Batch processing completed: {len(successful_results)}/{len(batches)} batches successful"
            )

            return BatchProcessingResult(
                success=len(failed_results) == 0,
                total_batches=len(batches),
                successful_batches=len(successful_results),
                failed_batches=len(failed_results),
                total_rows_processed=total_rows,
                total_processing_time=total_time,
                average_batch_time=avg_batch_time,
                peak_memory_usage_mb=self.peak_memory,
                batch_results=batch_results,
                errors=errors,
                statistics=statistics,
            )

        except Exception as e:
            logger.error(f"Batch processing failed: {e}")
            return BatchProcessingResult(success=False, errors=[str(e)])

    def process_file(
        self,
        file_path: Union[str, Path],
        output_path: Optional[Union[str, Path]] = None,
        file_format: str = "csv",
        read_options: Optional[Dict[str, Any]] = None,
        write_options: Optional[Dict[str, Any]] = None,
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> BatchProcessingResult:
        """Process file in batches.

        Args:
            file_path: Input file path
            output_path: Optional output file path
            file_format: File format (csv, parquet, excel)
            read_options: Options for reading file
            write_options: Options for writing file
            progress_tracker: Optional progress tracker

        Returns:
            BatchProcessingResult
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                raise ProcessingError(f"File not found: {file_path}")

            logger.info(f"Starting batch processing of file: {file_path}")

            read_options = read_options or {}
            write_options = write_options or {}

            # Process based on file format
            if file_format.lower() == "csv":
                return self._process_csv_file(
                    file_path,
                    output_path,
                    read_options,
                    write_options,
                    progress_tracker,
                )
            elif file_format.lower() == "parquet":
                return self._process_parquet_file(
                    file_path,
                    output_path,
                    read_options,
                    write_options,
                    progress_tracker,
                )
            elif file_format.lower() in ["excel", "xlsx", "xls"]:
                return self._process_excel_file(
                    file_path,
                    output_path,
                    read_options,
                    write_options,
                    progress_tracker,
                )
            else:
                raise ProcessingError(f"Unsupported file format: {file_format}")

        except Exception as e:
            logger.error(f"File batch processing failed: {e}")
            return BatchProcessingResult(success=False, errors=[str(e)])

    def _generate_batches(self, data: pd.DataFrame) -> Iterator[BatchInfo]:
        """Generate batch information.

        Args:
            data: DataFrame to batch

        Yields:
            BatchInfo objects
        """
        total_rows = len(data)

        if self.config.strategy == BatchStrategy.FIXED_SIZE:
            batch_size = self.config.batch_size
        elif self.config.strategy == BatchStrategy.MEMORY_BASED:
            batch_size = self._calculate_memory_based_batch_size(data)
        elif self.config.strategy == BatchStrategy.ADAPTIVE:
            batch_size = self._calculate_adaptive_batch_size(data)
        else:
            batch_size = self.config.batch_size

        logger.debug(f"Using batch size: {batch_size}")

        batch_id = 0
        start_idx = 0

        while start_idx < total_rows:
            end_idx = min(start_idx + batch_size, total_rows)

            # Add overlap if configured
            if self.config.overlap_rows > 0 and end_idx < total_rows:
                end_idx = min(end_idx + self.config.overlap_rows, total_rows)

            batch_info = BatchInfo(
                batch_id=batch_id,
                start_index=start_idx,
                end_index=end_idx,
                size=end_idx - start_idx,
            )

            yield batch_info

            start_idx = (
                end_idx - self.config.overlap_rows
                if self.config.overlap_rows > 0
                else end_idx
            )
            batch_id += 1

    def _calculate_memory_based_batch_size(self, data: pd.DataFrame) -> int:
        """Calculate batch size based on memory constraints.

        Args:
            data: Sample DataFrame

        Returns:
            Calculated batch size
        """
        try:
            # Get available memory
            available_memory = psutil.virtual_memory().available / (1024 * 1024)  # MB
            target_memory = available_memory * self.config.memory_threshold

            # Estimate memory per row
            sample_size = min(1000, len(data))
            sample_data = data.head(sample_size)
            memory_per_row = (
                sample_data.memory_usage(deep=True).sum() / sample_size / (1024 * 1024)
            )  # MB

            # Calculate batch size
            batch_size = int(target_memory / memory_per_row)

            # Apply limits
            batch_size = max(100, min(batch_size, 100000))

            logger.debug(
                f"Memory-based batch size: {batch_size} (memory per row: {memory_per_row:.4f} MB)"
            )

            return batch_size

        except Exception as e:
            logger.warning(f"Failed to calculate memory-based batch size: {e}")
            return self.config.batch_size

    def _calculate_adaptive_batch_size(self, data: pd.DataFrame) -> int:
        """Calculate adaptive batch size.

        Args:
            data: Sample DataFrame

        Returns:
            Calculated batch size
        """
        # Start with memory-based calculation
        memory_batch_size = self._calculate_memory_based_batch_size(data)

        # Adjust based on data complexity
        complexity_factor = 1.0

        # Consider number of columns
        if len(data.columns) > 50:
            complexity_factor *= 0.8
        elif len(data.columns) > 100:
            complexity_factor *= 0.6

        # Consider data types
        string_columns = len(data.select_dtypes(include=["object"]).columns)
        if string_columns > len(data.columns) * 0.5:
            complexity_factor *= 0.9

        # Apply complexity factor
        adaptive_batch_size = int(memory_batch_size * complexity_factor)

        logger.debug(
            f"Adaptive batch size: {adaptive_batch_size} (complexity factor: {complexity_factor})"
        )

        return adaptive_batch_size

    def _process_batches_sequential(
        self,
        data: pd.DataFrame,
        batches: List[BatchInfo],
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> List[BatchResult]:
        """Process batches sequentially.

        Args:
            data: DataFrame to process
            batches: List of batch information
            progress_tracker: Optional progress tracker

        Returns:
            List of batch results
        """
        results = []

        for i, batch_info in enumerate(batches):
            try:
                result = self._process_single_batch(data, batch_info)
                results.append(result)

                # Update peak memory
                self.peak_memory = max(
                    self.peak_memory, result.batch_info.memory_usage_mb
                )

                # Run garbage collection if enabled
                if self.config.enable_gc and (i + 1) % self.config.gc_frequency == 0:
                    gc.collect()

                if progress_tracker:
                    progress_tracker.update_progress(i + 1)

            except Exception as e:
                logger.error(f"Error processing batch {batch_info.batch_id}: {e}")
                result = BatchResult(batch_info=batch_info, success=False, error=str(e))
                results.append(result)

        return results

    def _process_batches_parallel(
        self,
        data: pd.DataFrame,
        batches: List[BatchInfo],
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> List[BatchResult]:
        """Process batches in parallel.

        Args:
            data: DataFrame to process
            batches: List of batch information
            progress_tracker: Optional progress tracker

        Returns:
            List of batch results
        """
        results = [None] * len(batches)

        with concurrent.futures.ThreadPoolExecutor(
            max_workers=self.config.max_workers
        ) as executor:
            # Submit all batch processing tasks
            future_to_index = {
                executor.submit(self._process_single_batch, data, batch_info): i
                for i, batch_info in enumerate(batches)
            }

            # Collect results as they complete
            completed = 0
            for future in concurrent.futures.as_completed(future_to_index):
                index = future_to_index[future]

                try:
                    result = future.result()
                    results[index] = result

                    # Update peak memory
                    self.peak_memory = max(
                        self.peak_memory, result.batch_info.memory_usage_mb
                    )

                except Exception as e:
                    logger.error(
                        f"Error processing batch {batches[index].batch_id}: {e}"
                    )
                    result = BatchResult(
                        batch_info=batches[index], success=False, error=str(e)
                    )
                    results[index] = result

                completed += 1
                if progress_tracker:
                    progress_tracker.update_progress(completed)

        return results

    async def _process_batches_async(
        self,
        data: pd.DataFrame,
        batches: List[BatchInfo],
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> List[BatchResult]:
        """Process batches asynchronously.

        Args:
            data: DataFrame to process
            batches: List of batch information
            progress_tracker: Optional progress tracker

        Returns:
            List of batch results
        """
        semaphore = asyncio.Semaphore(self.config.max_workers)

        async def process_batch_async(batch_info: BatchInfo) -> BatchResult:
            async with semaphore:
                loop = asyncio.get_running_loop()
                return await loop.run_in_executor(
                    None, self._process_single_batch, data, batch_info
                )

        # Create tasks for all batches
        tasks = [process_batch_async(batch_info) for batch_info in batches]

        # Process batches and collect results
        results = []
        completed = 0

        for coro in asyncio.as_completed(tasks):
            try:
                result = await coro
                results.append(result)

                # Update peak memory
                self.peak_memory = max(
                    self.peak_memory, result.batch_info.memory_usage_mb
                )

            except Exception as e:
                logger.error(f"Error in async batch processing: {e}")
                # Create error result (batch_info not available here)
                result = BatchResult(
                    batch_info=BatchInfo(
                        batch_id=-1, start_index=0, end_index=0, size=0
                    ),
                    success=False,
                    error=str(e),
                )
                results.append(result)

            completed += 1
            if progress_tracker:
                progress_tracker.update_progress(completed)

        # Sort results by batch_id to maintain order
        results.sort(key=lambda x: x.batch_info.batch_id)

        return results

    def _process_single_batch(
        self, data: pd.DataFrame, batch_info: BatchInfo
    ) -> BatchResult:
        """Process a single batch.

        Args:
            data: Full DataFrame
            batch_info: Batch information

        Returns:
            BatchResult
        """
        start_time = time.time()

        try:
            # Extract batch data
            batch_data = data.iloc[batch_info.start_index : batch_info.end_index].copy()

            # Calculate memory usage
            memory_usage = batch_data.memory_usage(deep=True).sum() / (
                1024 * 1024
            )  # MB
            batch_info.memory_usage_mb = memory_usage

            logger.debug(
                f"Processing batch {batch_info.batch_id}: rows {batch_info.start_index}-{batch_info.end_index}"
            )

            # Apply transformations
            for transformer in self.transformers:
                batch_data = transformer.transform(batch_data)

            # Apply validations
            validation_result = None
            for validator in self.validators:
                validation_result = validator.validate(batch_data)
                if not validation_result.success:
                    logger.warning(f"Validation failed for batch {batch_info.batch_id}")

            # Apply custom processors
            for name, processor in self.custom_processors.items():
                try:
                    batch_data = processor(batch_data)
                    logger.debug(f"Applied custom processor: {name}")
                except Exception as e:
                    logger.error(f"Custom processor {name} failed: {e}")

            # Update batch info
            processing_time = time.time() - start_time
            batch_info.processing_time = processing_time
            batch_info.status = "completed"

            # Calculate metrics
            metrics = {
                "rows_processed": len(batch_data),
                "memory_usage_mb": memory_usage,
                "processing_time": processing_time,
                "rows_per_second": len(batch_data) / processing_time
                if processing_time > 0
                else 0,
            }

            logger.debug(
                f"Batch {batch_info.batch_id} completed in {processing_time:.2f}s"
            )

            return BatchResult(
                batch_info=batch_info,
                data=batch_data,
                success=True,
                validation_result=validation_result,
                metrics=metrics,
            )

        except Exception as e:
            processing_time = time.time() - start_time
            batch_info.processing_time = processing_time
            batch_info.status = "failed"
            batch_info.error = str(e)

            logger.error(f"Batch {batch_info.batch_id} failed: {e}")

            return BatchResult(batch_info=batch_info, success=False, error=str(e))

    def _process_csv_file(
        self,
        file_path: Path,
        output_path: Optional[Path],
        read_options: Dict[str, Any],
        write_options: Dict[str, Any],
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> BatchProcessingResult:
        """Process CSV file in batches.

        Args:
            file_path: Input CSV file path
            output_path: Optional output file path
            read_options: CSV read options
            write_options: CSV write options
            progress_tracker: Optional progress tracker

        Returns:
            BatchProcessingResult
        """
        try:
            # Read file in chunks
            chunk_size = self.config.batch_size
            chunks = pd.read_csv(file_path, chunksize=chunk_size, **read_options)

            batch_results = []
            batch_id = 0
            total_rows = 0

            # Prepare output file if specified
            if output_path:
                output_path = Path(output_path)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                first_chunk = True
            else:
                first_chunk = False

            for chunk in chunks:
                batch_info = BatchInfo(
                    batch_id=batch_id,
                    start_index=total_rows,
                    end_index=total_rows + len(chunk),
                    size=len(chunk),
                )

                result = self._process_single_batch(chunk, batch_info)
                batch_results.append(result)

                # Write processed chunk to output file
                if output_path and result.success and result.data is not None:
                    mode = "w" if first_chunk else "a"
                    header = first_chunk

                    result.data.to_csv(
                        output_path,
                        mode=mode,
                        header=header,
                        index=False,
                        **write_options,
                    )
                    first_chunk = False

                total_rows += len(chunk)
                batch_id += 1

                if progress_tracker:
                    progress_tracker.update_progress(batch_id)
                
                # Memory cleanup: explicitly delete chunk and result data
                del chunk
                if result.data is not None:
                    del result.data
                    result.data = None
                
                # Force garbage collection every 10 batches
                if batch_id % 10 == 0:
                    import gc
                    gc.collect()

            # Calculate overall results
            successful_results = [r for r in batch_results if r.success]
            failed_results = [r for r in batch_results if not r.success]

            return BatchProcessingResult(
                success=len(failed_results) == 0,
                total_batches=len(batch_results),
                successful_batches=len(successful_results),
                failed_batches=len(failed_results),
                total_rows_processed=total_rows,
                batch_results=batch_results,
            )

        except Exception as e:
            logger.error(f"CSV file processing failed: {e}")
            return BatchProcessingResult(success=False, errors=[str(e)])

    def _process_parquet_file(
        self,
        file_path: Path,
        output_path: Optional[Path],
        read_options: Dict[str, Any],
        write_options: Dict[str, Any],
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> BatchProcessingResult:
        """Process Parquet file in batches.

        Args:
            file_path: Input Parquet file path
            output_path: Optional output file path
            read_options: Parquet read options
            write_options: Parquet write options
            progress_tracker: Optional progress tracker

        Returns:
            BatchProcessingResult
        """
        try:
            # For Parquet, we need to read the entire file first to determine batching
            # This is a limitation of the current pandas Parquet reader
            data = pd.read_parquet(file_path, **read_options)

            # Process using DataFrame method
            result = self.process_dataframe(data, progress_tracker)

            # Write output if specified and processing was successful
            if output_path and result.success:
                output_path = Path(output_path)
                output_path.parent.mkdir(parents=True, exist_ok=True)

                # Combine all successful batch results
                processed_data_frames = []
                for batch_result in result.batch_results:
                    if batch_result.success and batch_result.data is not None:
                        processed_data_frames.append(batch_result.data)

                if processed_data_frames:
                    combined_data = pd.concat(processed_data_frames, ignore_index=True)
                    combined_data.to_parquet(output_path, **write_options)

            return result

        except Exception as e:
            logger.error(f"Parquet file processing failed: {e}")
            return BatchProcessingResult(success=False, errors=[str(e)])

    def _process_excel_file(
        self,
        file_path: Path,
        output_path: Optional[Path],
        read_options: Dict[str, Any],
        write_options: Dict[str, Any],
        progress_tracker: Optional[ProgressTracker] = None,
    ) -> BatchProcessingResult:
        """Process Excel file in batches.

        Args:
            file_path: Input Excel file path
            output_path: Optional output file path
            read_options: Excel read options
            write_options: Excel write options
            progress_tracker: Optional progress tracker

        Returns:
            BatchProcessingResult
        """
        try:
            # Read Excel file
            data = pd.read_excel(file_path, **read_options)

            # Process using DataFrame method
            result = self.process_dataframe(data, progress_tracker)

            # Write output if specified and processing was successful
            if output_path and result.success:
                output_path = Path(output_path)
                output_path.parent.mkdir(parents=True, exist_ok=True)

                # Combine all successful batch results
                processed_data_frames = []
                for batch_result in result.batch_results:
                    if batch_result.success and batch_result.data is not None:
                        processed_data_frames.append(batch_result.data)

                if processed_data_frames:
                    combined_data = pd.concat(processed_data_frames, ignore_index=True)
                    combined_data.to_excel(output_path, index=False, **write_options)

            return result

        except Exception as e:
            logger.error(f"Excel file processing failed: {e}")
            return BatchProcessingResult(success=False, errors=[str(e)])

    def _calculate_statistics(self, batch_results: List[BatchResult]) -> Dict[str, Any]:
        """Calculate processing statistics.

        Args:
            batch_results: List of batch results

        Returns:
            Statistics dictionary
        """
        if not batch_results:
            return {}

        successful_results = [r for r in batch_results if r.success]

        if not successful_results:
            return {"all_batches_failed": True}

        processing_times = [r.batch_info.processing_time for r in successful_results]
        memory_usages = [r.batch_info.memory_usage_mb for r in successful_results]
        batch_sizes = [r.batch_info.size for r in successful_results]

        statistics = {
            "processing_time": {
                "min": min(processing_times),
                "max": max(processing_times),
                "mean": np.mean(processing_times),
                "median": np.median(processing_times),
                "std": np.std(processing_times),
            },
            "memory_usage": {
                "min": min(memory_usages),
                "max": max(memory_usages),
                "mean": np.mean(memory_usages),
                "median": np.median(memory_usages),
                "std": np.std(memory_usages),
            },
            "batch_size": {
                "min": min(batch_sizes),
                "max": max(batch_sizes),
                "mean": np.mean(batch_sizes),
                "median": np.median(batch_sizes),
                "std": np.std(batch_sizes),
            },
            "throughput": {
                "total_rows": sum(batch_sizes),
                "total_time": sum(processing_times),
                "rows_per_second": sum(batch_sizes) / sum(processing_times)
                if sum(processing_times) > 0
                else 0,
            },
        }

        return statistics

    def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage information.

        Returns:
            Memory usage information in MB
        """
        process = psutil.Process()
        memory_info = process.memory_info()

        return {
            "rss_mb": memory_info.rss / (1024 * 1024),
            "vms_mb": memory_info.vms / (1024 * 1024),
            "peak_memory_mb": self.peak_memory,
            "available_mb": psutil.virtual_memory().available / (1024 * 1024),
            "percent_used": psutil.virtual_memory().percent,
        }

    def reset_statistics(self) -> None:
        """Reset processing statistics."""
        self.batch_counter = 0
        self.peak_memory = 0.0
        logger.debug("Reset batch processor statistics")


# Convenience functions
def create_batch_processor(
    strategy: BatchStrategy = BatchStrategy.FIXED_SIZE,
    batch_size: int = 10000,
    processing_mode: ProcessingMode = ProcessingMode.SEQUENTIAL,
    max_workers: int = 8,
) -> BatchProcessor:
    """Create batch processor with common configuration.

    Args:
        strategy: Batch processing strategy
        batch_size: Size of each batch
        processing_mode: Processing mode
        max_workers: Maximum number of workers for parallel processing

    Returns:
        Configured BatchProcessor
    """
    config = BatchConfig(
        strategy=strategy,
        batch_size=batch_size,
        processing_mode=processing_mode,
        max_workers=max_workers,
    )

    return BatchProcessor(config)


def create_memory_optimized_processor(
    max_memory_mb: float = 1024.0, memory_threshold: float = 0.8
) -> BatchProcessor:
    """Create memory-optimized batch processor.

    Args:
        max_memory_mb: Maximum memory usage in MB
        memory_threshold: Memory threshold (0-1)

    Returns:
        Memory-optimized BatchProcessor
    """
    config = BatchConfig(
        strategy=BatchStrategy.MEMORY_BASED,
        max_memory_mb=max_memory_mb,
        memory_threshold=memory_threshold,
        enable_gc=True,
        gc_frequency=5,
    )

    return BatchProcessor(config)


def create_parallel_processor(
    max_workers: int = 8, batch_size: int = 5000
) -> BatchProcessor:
    """Create parallel batch processor.

    Args:
        max_workers: Maximum number of parallel workers
        batch_size: Size of each batch

    Returns:
        Parallel BatchProcessor
    """
    config = BatchConfig(
        strategy=BatchStrategy.FIXED_SIZE,
        batch_size=batch_size,
        processing_mode=ProcessingMode.PARALLEL,
        max_workers=max_workers,
    )

    return BatchProcessor(config)
