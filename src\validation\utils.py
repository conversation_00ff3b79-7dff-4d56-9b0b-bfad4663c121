"""Unified Validation Framework Utility Module

Provides utility functions for data transformation, batch processing, report generation, etc.
"""

import os
import json
import csv
import hashlib
import tempfile
import shutil
from typing import Dict, Any, List, Optional, Union, Iterator, Tuple, Callable
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import asdict
import pandas as pd
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
from contextlib import contextmanager

from .core import ValidationResult, ValidationIssue, ValidationSeverity
from .exceptions import ValidationError, FileValidationError


class DataConverter:
    """Data Conversion Tools"""
    
    @staticmethod
    def normalize_column_names(df: pd.DataFrame, 
                             naming_convention: str = 'snake_case') -> pd.DataFrame:
        """Normalize column names
        
        Args:
            df: Input DataFrame
            naming_convention: Naming convention ('snake_case', 'camel_case', 'pascal_case')
        
        Returns:
            DataFrame with normalized column names
        """
        df_copy = df.copy()
        
        if naming_convention == 'snake_case':
            # Convert to snake_case
            df_copy.columns = [
                (col.lower() if isinstance(col, str) else str(col).lower()).replace(' ', '_').replace('-', '_')
                for col in df_copy.columns
            ]
        elif naming_convention == 'camel_case':
            # Convert to camelCase
            def to_camel_case(name: str) -> str:
                components = name.lower().replace('-', '_').split('_')
                return components[0] + ''.join(word.capitalize() for word in components[1:])
            
            df_copy.columns = [to_camel_case(col) for col in df_copy.columns]
        elif naming_convention == 'pascal_case':
            # Convert to PascalCase
            def to_pascal_case(name: str) -> str:
                components = name.lower().replace('-', '_').split('_')
                return ''.join(word.capitalize() for word in components)
            
            df_copy.columns = [to_pascal_case(col) for col in df_copy.columns]
        
        return df_copy
    
    @staticmethod
    def clean_data_types(df: pd.DataFrame, 
                        type_mapping: Optional[Dict[str, str]] = None) -> pd.DataFrame:
        """Clean and convert data types
        
        Args:
            df: Input DataFrame
            type_mapping: Mapping from column names to data types
        
        Returns:
            DataFrame with cleaned data types
        """
        df_copy = df.copy()
        
        # Default type mapping
        if type_mapping is None:
            type_mapping = {}
        
        # Auto-infer and clean data types
        for col in df_copy.columns:
            if col in type_mapping:
                try:
                    if type_mapping[col] == 'datetime':
                        df_copy[col] = pd.to_datetime(df_copy[col], errors='coerce')
                    elif type_mapping[col] == 'numeric':
                        df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')
                    elif type_mapping[col] == 'string':
                        df_copy[col] = df_copy[col].astype(str)
                    elif type_mapping[col] == 'category':
                        df_copy[col] = df_copy[col].astype('category')
                except Exception as e:
                    logging.warning(f"Failed to convert column {col} to {type_mapping[col]}: {e}")
            else:
                # Auto-infer
                if df_copy[col].dtype == 'object':
                    # Try to convert to numeric
                    numeric_series = pd.to_numeric(df_copy[col], errors='coerce')
                    if not numeric_series.isna().all():
                        df_copy[col] = numeric_series
                    else:
                        # Try to convert to datetime
                        try:
                            datetime_series = pd.to_datetime(df_copy[col], errors='coerce')
                            if not datetime_series.isna().all():
                                df_copy[col] = datetime_series
                        except:
                            pass
        
        return df_copy
    
    @staticmethod
    def remove_duplicates(df: pd.DataFrame, 
                         subset: Optional[List[str]] = None,
                         keep: str = 'first') -> Tuple[pd.DataFrame, int]:
        """Remove duplicate rows
        
        Args:
            df: Input DataFrame
            subset: List of column names to use for duplicate detection
            keep: Keep strategy ('first', 'last', False)
        
        Returns:
            (Deduplicated DataFrame, number of removed duplicate rows)
        """
        original_count = len(df)
        df_clean = df.drop_duplicates(subset=subset, keep=keep)
        removed_count = original_count - len(df_clean)
        
        return df_clean, removed_count
    
    @staticmethod
    def handle_missing_values(df: pd.DataFrame, 
                            strategy: str = 'drop',
                            fill_value: Any = None,
                            columns: Optional[List[str]] = None) -> pd.DataFrame:
        """Handle missing values
        
        Args:
            df: Input DataFrame
            strategy: Handling strategy ('drop', 'fill', 'forward_fill', 'backward_fill')
            fill_value: Fill value
            columns: List of column names to process
        
        Returns:
            DataFrame with missing values handled
        """
        df_copy = df.copy()
        
        if columns is None:
            columns = df_copy.columns.tolist()
        
        if strategy == 'drop':
            df_copy = df_copy.dropna(subset=columns)
        elif strategy == 'fill':
            for col in columns:
                if col in df_copy.columns:
                    df_copy[col] = df_copy[col].fillna(fill_value)
        elif strategy == 'forward_fill':
            for col in columns:
                if col in df_copy.columns:
                    df_copy[col] = df_copy[col].fillna(method='ffill')
        elif strategy == 'backward_fill':
            for col in columns:
                if col in df_copy.columns:
                    df_copy[col] = df_copy[col].fillna(method='bfill')
        
        return df_copy


class BatchProcessor:
    """Batch Processing Tools"""
    
    def __init__(self, batch_size: int = 10000, max_workers: int = 4):
        self.batch_size = batch_size
        self.max_workers = max_workers
    
    def process_dataframe_batches(self, 
                                 df: pd.DataFrame,
                                 processor_func: Callable[[pd.DataFrame], Any],
                                 parallel: bool = True) -> List[Any]:
        """Process DataFrame in batches
        
        Args:
            df: Input DataFrame
            processor_func: Processing function
            parallel: Whether to process in parallel
        
        Returns:
            List of processing results
        """
        # Split data
        batches = []
        for i in range(0, len(df), self.batch_size):
            batch = df.iloc[i:i + self.batch_size]
            batches.append(batch)
        
        results = []
        
        if parallel and len(batches) > 1:
            # Parallel processing
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_batch = {
                    executor.submit(processor_func, batch): i 
                    for i, batch in enumerate(batches)
                }
                
                for future in as_completed(future_to_batch):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        logging.error(f"Batch processing error: {e}")
                        results.append(None)
        else:
            # Serial processing
            for batch in batches:
                try:
                    result = processor_func(batch)
                    results.append(result)
                except Exception as e:
                    logging.error(f"Batch processing error: {e}")
                    results.append(None)
        
        return results
    
    def process_files_batch(self, 
                           file_paths: List[str],
                           processor_func: Callable[[str], Any],
                           parallel: bool = True) -> List[Any]:
        """Process files in batches
        
        Args:
            file_paths: List of file paths
            processor_func: Processing function
            parallel: Whether to process in parallel
        
        Returns:
            List of processing results
        """
        results = []
        
        if parallel and len(file_paths) > 1:
            # 并行处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_file = {
                    executor.submit(processor_func, file_path): file_path 
                    for file_path in file_paths
                }
                
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    try:
                        result = future.result()
                        results.append((file_path, result))
                    except Exception as e:
                        logging.error(f"File processing error for {file_path}: {e}")
                        results.append((file_path, None))
        else:
            # 串行处理
            for file_path in file_paths:
                try:
                    result = processor_func(file_path)
                    results.append((file_path, result))
                except Exception as e:
                    logging.error(f"File processing error for {file_path}: {e}")
                    results.append((file_path, None))
        
        return results


class ReportGenerator:
    """Report generator"""
    
    @staticmethod
    def generate_validation_report(results: List[ValidationResult], 
                                 output_path: str,
                                 format: str = 'html') -> None:
        """Generate validation report
        
        Args:
            results: List of validation results
            output_path: Output path
            format: Report format ('html', 'json', 'csv')
        """
        if format.lower() == 'html':
            ReportGenerator._generate_html_report(results, output_path)
        elif format.lower() == 'json':
            ReportGenerator._generate_json_report(results, output_path)
        elif format.lower() == 'csv':
            ReportGenerator._generate_csv_report(results, output_path)
        else:
            raise ValueError(f"Unsupported report format: {format}")
    
    @staticmethod
    def _generate_html_report(results: List[ValidationResult], output_path: str) -> None:
        """Generate HTML report"""
        # Statistics
        total_results = len(results)
        passed_results = sum(1 for r in results if r.is_valid)
        failed_results = total_results - passed_results
        
        total_issues = sum(len(r.issues) for r in results)
        error_issues = sum(1 for r in results for issue in r.issues if issue.severity == ValidationSeverity.ERROR)
        warning_issues = sum(1 for r in results for issue in r.issues if issue.severity == ValidationSeverity.WARNING)
        
        # HTML template
        html_template = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Validation Report</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .summary {{ display: flex; gap: 20px; margin-bottom: 20px; }}
        .summary-card {{ background-color: #fff; border: 1px solid #ddd; padding: 15px; border-radius: 5px; flex: 1; }}
        .summary-card h3 {{ margin: 0 0 10px 0; color: #333; }}
        .summary-card .number {{ font-size: 24px; font-weight: bold; }}
        .passed {{ color: #28a745; }}
        .failed {{ color: #dc3545; }}
        .warning {{ color: #ffc107; }}
        .error {{ color: #dc3545; }}
        .results {{ margin-top: 20px; }}
        .result-item {{ border: 1px solid #ddd; margin-bottom: 10px; border-radius: 5px; }}
        .result-header {{ background-color: #f8f9fa; padding: 10px; border-bottom: 1px solid #ddd; }}
        .result-content {{ padding: 10px; }}
        .issue {{ margin: 5px 0; padding: 8px; border-radius: 3px; }}
        .issue.error {{ background-color: #f8d7da; border-left: 4px solid #dc3545; }}
        .issue.warning {{ background-color: #fff3cd; border-left: 4px solid #ffc107; }}
        .issue.info {{ background-color: #d1ecf1; border-left: 4px solid #17a2b8; }}
        .timestamp {{ color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Validation Report</h1>
        <p class="timestamp">Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="summary">
        <div class="summary-card">
            <h3>Total</h3>
            <div class="number">{total_results}</div>
            <p>Validation Tasks</p>
        </div>
        <div class="summary-card">
            <h3>Passed</h3>
            <div class="number passed">{passed_results}</div>
            <p>Successful Validations</p>
        </div>
        <div class="summary-card">
            <h3>Failed</h3>
            <div class="number failed">{failed_results}</div>
            <p>Failed Validations</p>
        </div>
        <div class="summary-card">
            <h3>Issues</h3>
            <div class="number">{total_issues}</div>
            <p>Issues Found</p>
        </div>
    </div>
    
    <div class="results">
        <h2>Detailed Results</h2>
"""
        
        # Add each validation result
        for i, result in enumerate(results):
            status_class = 'passed' if result.is_valid else 'failed'
            status_text = 'Passed' if result.is_valid else 'Failed'
            
            html_template += f"""
        <div class="result-item">
            <div class="result-header">
                <strong>Validation #{i+1}</strong>
                <span class="{status_class}">【{status_text}】</span>
                <span class="timestamp">{result.timestamp.strftime('%Y-%m-%d %H:%M:%S')}</span>
            </div>
            <div class="result-content">
                <p><strong>Validation Type:</strong> {result.validation_type}</p>
                <p><strong>Data Size:</strong> {result.data_size} rows</p>
                <p><strong>Validation Time:</strong> {result.validation_time:.2f} seconds</p>
                <p><strong>Issue Count:</strong> {len(result.issues)}</p>
"""
            
            if result.issues:
                html_template += "<div><strong>Issue Details:</strong></div>"
                for issue in result.issues:
                    severity_class = issue.severity.value.lower()
                    html_template += f"""
                <div class="issue {severity_class}">
                    <strong>[{issue.severity.value}]</strong> {issue.message}
                    {f'<br><small>Location: {issue.location}</small>' if issue.location else ''}
                    {f'<br><small>Suggestion: {issue.suggestion}</small>' if issue.suggestion else ''}
                </div>
"""
            
            html_template += "</div></div>"
        
        html_template += """
    </div>
</body>
</html>
"""
        
        # Write to file
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_template)
    
    @staticmethod
    def _generate_json_report(results: List[ValidationResult], output_path: str) -> None:
        """Generate JSON report"""
        report_data = {
            'generated_at': datetime.now().isoformat(),
            'summary': {
                'total_validations': len(results),
                'passed_validations': sum(1 for r in results if r.is_valid),
                'failed_validations': sum(1 for r in results if not r.is_valid),
                'total_issues': sum(len(r.issues) for r in results),
                'error_issues': sum(1 for r in results for issue in r.issues if issue.severity == ValidationSeverity.ERROR),
                'warning_issues': sum(1 for r in results for issue in r.issues if issue.severity == ValidationSeverity.WARNING)
            },
            'results': []
        }
        
        for result in results:
            result_data = {
                'timestamp': result.timestamp.isoformat(),
                'validation_type': result.validation_type,
                'is_valid': result.is_valid,
                'data_size': result.data_size,
                'validation_time': result.validation_time,
                'issues': [
                    {
                        'severity': issue.severity.value,
                        'message': issue.message,
                        'location': issue.location,
                        'suggestion': issue.suggestion
                    }
                    for issue in result.issues
                ],
                'metadata': result.metadata
            }
            report_data['results'].append(result_data)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    @staticmethod
    def _generate_csv_report(results: List[ValidationResult], output_path: str) -> None:
        """Generate CSV report"""
        rows = []
        
        for i, result in enumerate(results):
            base_row = {
                'validation_id': i + 1,
                'timestamp': result.timestamp.isoformat(),
                'validation_type': result.validation_type,
                'is_valid': result.is_valid,
                'data_size': result.data_size,
                'validation_time': result.validation_time,
                'total_issues': len(result.issues)
            }
            
            if result.issues:
                for issue in result.issues:
                    row = base_row.copy()
                    row.update({
                        'issue_severity': issue.severity.value,
                        'issue_message': issue.message,
                        'issue_location': issue.location or '',
                        'issue_suggestion': issue.suggestion or ''
                    })
                    rows.append(row)
            else:
                base_row.update({
                    'issue_severity': '',
                    'issue_message': '',
                    'issue_location': '',
                    'issue_suggestion': ''
                })
                rows.append(base_row)
        
        if rows:
            df = pd.DataFrame(rows)
            df.to_csv(output_path, index=False, encoding='utf-8-sig')


class FileUtils:
    """File utilities"""
    
    @staticmethod
    def calculate_file_hash(file_path: str, algorithm: str = 'md5') -> str:
        """Calculate file hash value
        
        Args:
            file_path: File path
            algorithm: Hash algorithm ('md5', 'sha1', 'sha256')
        
        Returns:
            File hash value
        """
        if not os.path.exists(file_path):
            raise FileValidationError(f"File not found: {file_path}")
        
        hash_func = getattr(hashlib, algorithm.lower())()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_func.update(chunk)
        
        return hash_func.hexdigest()
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """Get file information
        
        Args:
            file_path: File path
        
        Returns:
            File information dictionary
        """
        if not os.path.exists(file_path):
            raise FileValidationError(f"File not found: {file_path}")
        
        stat = os.stat(file_path)
        
        return {
            'path': file_path,
            'name': os.path.basename(file_path),
            'size_bytes': stat.st_size,
            'size_mb': stat.st_size / 1024 / 1024,
            'created_time': datetime.fromtimestamp(stat.st_ctime),
            'modified_time': datetime.fromtimestamp(stat.st_mtime),
            'extension': Path(file_path).suffix.lower(),
            'is_readable': os.access(file_path, os.R_OK),
            'is_writable': os.access(file_path, os.W_OK)
        }
    
    @staticmethod
    def backup_file(file_path: str, backup_dir: Optional[str] = None) -> str:
        """Backup file
        
        Args:
            file_path: Source file path
            backup_dir: Backup directory
        
        Returns:
            Backup file path
        """
        if not os.path.exists(file_path):
            raise FileValidationError(f"File not found: {file_path}")
        
        if backup_dir is None:
            backup_dir = os.path.dirname(file_path)
        
        # Create backup directory
        os.makedirs(backup_dir, exist_ok=True)
        
        # Generate backup file name
        file_name = os.path.basename(file_path)
        name, ext = os.path.splitext(file_name)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"{name}_backup_{timestamp}{ext}"
        backup_path = os.path.join(backup_dir, backup_name)
        
        # Copy file
        shutil.copy2(file_path, backup_path)
        
        return backup_path
    
    @staticmethod
    @contextmanager
    def temporary_file(suffix: str = '.tmp', prefix: str = 'validation_'):
        """Temporary file context manager
        
        Args:
            suffix: File suffix
            prefix: File prefix
        
        Yields:
            Temporary file path
        """
        temp_file = None
        try:
            temp_file = tempfile.NamedTemporaryFile(
                suffix=suffix, prefix=prefix, delete=False
            )
            temp_path = temp_file.name
            temp_file.close()
            yield temp_path
        finally:
            if temp_file and os.path.exists(temp_file.name):
                try:
                    os.unlink(temp_file.name)
                except OSError:
                    pass
    
    @staticmethod
    def find_files(directory: str, 
                  pattern: str = '*',
                  recursive: bool = True,
                  include_dirs: bool = False) -> List[str]:
        """Find files
        
        Args:
            directory: Search directory
            pattern: File pattern
            recursive: Whether to search recursively
            include_dirs: Whether to include directories
        
        Returns:
            List of file paths
        """
        if not os.path.exists(directory):
            raise FileValidationError(f"Directory not found: {directory}")
        
        path_obj = Path(directory)
        
        if recursive:
            glob_pattern = f"**/{pattern}"
            paths = path_obj.glob(glob_pattern)
        else:
            paths = path_obj.glob(pattern)
        
        result = []
        for path in paths:
            if include_dirs or path.is_file():
                result.append(str(path))
        
        return sorted(result)


class DataSampler:
    """Data sampling utilities"""
    
    @staticmethod
    def random_sample(df: pd.DataFrame, 
                     sample_size: Union[int, float],
                     random_state: Optional[int] = None) -> pd.DataFrame:
        """Random sampling
        
        Args:
            df: Input DataFrame
            sample_size: Sample size (integer for number of rows, float for proportion)
            random_state: Random seed
        
        Returns:
            Sampled DataFrame
        """
        if isinstance(sample_size, float):
            if not 0 < sample_size <= 1:
                raise ValueError("Sample size as fraction must be between 0 and 1")
            n_samples = int(len(df) * sample_size)
        else:
            n_samples = min(sample_size, len(df))
        
        return df.sample(n=n_samples, random_state=random_state)
    
    @staticmethod
    def stratified_sample(df: pd.DataFrame,
                         strata_column: str,
                         sample_size: Union[int, float],
                         random_state: Optional[int] = None) -> pd.DataFrame:
        """Stratified sampling
        
        Args:
            df: Input DataFrame
            strata_column: Stratification column name
            sample_size: Sample size
            random_state: Random seed
        
        Returns:
            Sampled DataFrame
        """
        if strata_column not in df.columns:
            raise ValueError(f"Strata column '{strata_column}' not found in DataFrame")
        
        if isinstance(sample_size, float):
            if not 0 < sample_size <= 1:
                raise ValueError("Sample size as fraction must be between 0 and 1")
        
        sampled_dfs = []
        
        for stratum_value in df[strata_column].unique():
            stratum_df = df[df[strata_column] == stratum_value]
            
            if isinstance(sample_size, float):
                stratum_sample_size = max(1, int(len(stratum_df) * sample_size))
            else:
                # Allocate samples proportionally
                stratum_proportion = len(stratum_df) / len(df)
                stratum_sample_size = max(1, int(sample_size * stratum_proportion))
            
            stratum_sample_size = min(stratum_sample_size, len(stratum_df))
            
            stratum_sample = stratum_df.sample(
                n=stratum_sample_size, random_state=random_state
            )
            sampled_dfs.append(stratum_sample)
        
        return pd.concat(sampled_dfs, ignore_index=True)
    
    @staticmethod
    def time_based_sample(df: pd.DataFrame,
                         time_column: str,
                         sample_interval: str = '1H',
                         method: str = 'first') -> pd.DataFrame:
        """Time-based sampling
        
        Args:
            df: Input DataFrame
            time_column: Time column name
            sample_interval: Sampling interval ('1H', '1D', '1W', etc.)
            method: Sampling method ('first', 'last', 'random')
        
        Returns:
            Sampled DataFrame
        """
        if time_column not in df.columns:
            raise ValueError(f"Time column '{time_column}' not found in DataFrame")
        
        df_copy = df.copy()
        df_copy[time_column] = pd.to_datetime(df_copy[time_column])
        df_copy = df_copy.sort_values(time_column)
        
        if method == 'first':
            return df_copy.groupby(pd.Grouper(key=time_column, freq=sample_interval)).first().dropna()
        elif method == 'last':
            return df_copy.groupby(pd.Grouper(key=time_column, freq=sample_interval)).last().dropna()
        elif method == 'random':
            def random_choice(group):
                return group.sample(n=1) if len(group) > 0 else group
            return df_copy.groupby(pd.Grouper(key=time_column, freq=sample_interval)).apply(random_choice).reset_index(drop=True)
        else:
            raise ValueError(f"Unsupported sampling method: {method}")


# Convenience functions
def normalize_dataframe(df: pd.DataFrame, 
                       clean_columns: bool = True,
                       clean_types: bool = True,
                       remove_duplicates: bool = True,
                       handle_missing: str = 'drop') -> pd.DataFrame:
    """Data normalization convenience function
    
    Args:
        df: Input DataFrame
        clean_columns: Whether to clean column names
        clean_types: Whether to clean data types
        remove_duplicates: Whether to remove duplicate rows
        handle_missing: Missing value handling strategy
    
    Returns:
        Normalized DataFrame
    """
    result_df = df.copy()
    
    if clean_columns:
        result_df = DataConverter.normalize_column_names(result_df)
    
    if clean_types:
        result_df = DataConverter.clean_data_types(result_df)
    
    if remove_duplicates:
        result_df, _ = DataConverter.remove_duplicates(result_df)
    
    if handle_missing and handle_missing != 'none':
        result_df = DataConverter.handle_missing_values(result_df, strategy=handle_missing)
    
    return result_df


def generate_sample_data(data_type: str, size: int = 1000) -> pd.DataFrame:
    """Generate sample data
    
    Args:
        data_type: Data type ('cdr', 'kpi', 'cfg')
        size: Data size
    
    Returns:
        Sample DataFrame
    """
    np.random.seed(42)
    
    if data_type.lower() == 'cdr':
        return pd.DataFrame({
            'call_id': [f'call_{i:06d}' for i in range(size)],
            'caller_number': [f'138{np.random.randint(10000000, 99999999)}' for _ in range(size)],
            'called_number': [f'139{np.random.randint(10000000, 99999999)}' for _ in range(size)],
            'start_time': pd.date_range('2024-01-01', periods=size, freq='1min'),
            'duration': np.random.randint(10, 3600, size),
            'cell_id': np.random.randint(1000, 9999, size),
            'call_type': np.random.choice(['voice', 'video', 'data'], size),
            'success': np.random.choice([True, False], size, p=[0.95, 0.05])
        })
    
    elif data_type.lower() == 'kpi':
        return pd.DataFrame({
            'timestamp': pd.date_range('2024-01-01', periods=size, freq='15min'),
            'cell_id': np.random.randint(1000, 9999, size),
            'throughput_mbps': np.random.uniform(10, 100, size),
            'latency_ms': np.random.uniform(5, 50, size),
            'packet_loss_rate': np.random.uniform(0, 0.05, size),
            'active_users': np.random.randint(10, 500, size),
            'signal_strength_dbm': np.random.uniform(-120, -60, size)
        })
    
    elif data_type.lower() == 'cfg':
        return pd.DataFrame({
            'config_id': [f'cfg_{i:04d}' for i in range(size)],
            'parameter_name': [f'param_{i}' for i in range(size)],
            'parameter_value': np.random.choice(['enabled', 'disabled', 'auto'], size),
            'category': np.random.choice(['network', 'security', 'performance'], size),
            'last_modified': pd.date_range('2024-01-01', periods=size, freq='1D'),
            'is_active': np.random.choice([True, False], size, p=[0.8, 0.2])
        })
    
    else:
        raise ValueError(f"Unsupported data type: {data_type}")