# -*- coding: utf-8 -*-
"""
Logging Utilities

This module provides comprehensive logging utilities for the Connect
telecommunications data processing system, including structured logging,
performance logging, and telecommunications-specific log formatting.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import logging
import logging.handlers
import sys
import json
import traceback
from typing import Dict, Any, Optional, Union, List
from datetime import datetime
from pathlib import Path
from enum import Enum
from dataclasses import dataclass, field, asdict
import threading
from contextlib import contextmanager


class LogLevel(Enum):
    """Log level enumeration."""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


@dataclass
class LogContext:
    """Log context information."""
    operation_id: Optional[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    component: Optional[str] = None
    module: Optional[str] = None
    function: Optional[str] = None
    file_path: Optional[str] = None
    line_number: Optional[int] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {k: v for k, v in asdict(self).items() if v is not None}


@dataclass
class TelecomLogEntry:
    """Telecommunications-specific log entry."""
    timestamp: datetime
    level: str
    message: str
    logger_name: str
    context: LogContext = field(default_factory=LogContext)
    
    # Telecommunications-specific fields
    imsi: Optional[str] = None
    msisdn: Optional[str] = None
    cell_id: Optional[str] = None
    lac: Optional[str] = None
    tac: Optional[str] = None
    
    # Performance fields
    duration_ms: Optional[float] = None
    records_processed: Optional[int] = None
    bytes_processed: Optional[int] = None
    memory_used_mb: Optional[float] = None
    
    # Error fields
    exception_type: Optional[str] = None
    exception_message: Optional[str] = None
    stack_trace: Optional[str] = None
    
    # Additional metadata
    extra_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['context'] = self.context.to_dict()
        return {k: v for k, v in data.items() if v is not None}


class TelecomFormatter(logging.Formatter):
    """Custom formatter for telecommunications logging.
    
    Features:
    - JSON structured logging
    - Telecommunications field extraction
    - Performance metrics inclusion
    - Error context preservation
    """
    
    def __init__(
        self,
        include_context: bool = False,  # 默认关闭详细上下文
        include_performance: bool = True,
        include_telecom_fields: bool = True,
        pretty_print: bool = False,
        simple_format: bool = True  # 新增简洁格式选项
    ):
        """Initialize formatter.
        
        Args:
            include_context: Include log context
            include_performance: Include performance metrics
            include_telecom_fields: Include telecommunications fields
            pretty_print: Pretty print JSON output
            simple_format: Use simple format for better readability
        """
        super().__init__()
        self.include_context = include_context
        self.include_performance = include_performance
        self.include_telecom_fields = include_telecom_fields
        self.pretty_print = pretty_print
        self.simple_format = simple_format
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record.
        
        Args:
            record: Log record to format
            
        Returns:
            Formatted log string
        """
        # 如果使用简洁格式，直接返回标准格式
        if self.simple_format:
            timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S')
            
            # 提取关键上下文信息
            context_info = ""
            if hasattr(record, 'operation_id') and getattr(record, 'operation_id'):
                context_info += f" [Op:{getattr(record, 'operation_id')}]"
            if hasattr(record, 'component') and getattr(record, 'component'):
                context_info += f" [Comp:{getattr(record, 'component')}]"
            if hasattr(record, 'taskName') and getattr(record, 'taskName'):
                context_info += f" [Task:{getattr(record, 'taskName')}]"
            
            return f"{timestamp} - {record.name} - {record.levelname} - {record.getMessage()}{context_info}"
        
        # 原有的JSON格式逻辑（简化版）
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'message': record.getMessage(),
            'logger': record.name
        }
        
        # 只添加重要的上下文信息
        if self.include_context:
            context = {}
            for attr in ['operation_id', 'component', 'taskName']:
                if hasattr(record, attr) and getattr(record, attr):
                    context[attr] = getattr(record, attr)
            if context:
                log_data['context'] = context
        
        # 添加性能指标
        if self.include_performance:
            perf_data = {}
            for field in ['duration_ms', 'records_processed']:
                if hasattr(record, field) and getattr(record, field) is not None:
                    perf_data[field] = getattr(record, field)
            if perf_data:
                log_data['performance'] = perf_data
        
        # 添加异常信息
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1])
            }
        
        return json.dumps(log_data, ensure_ascii=False)


class TelecomLogger:
    """Enhanced logger for telecommunications applications.
    
    Features:
    - Structured logging with telecommunications context
    - Performance metrics logging
    - Automatic context management
    - Multiple output handlers
    - Log aggregation and filtering
    """
    
    def __init__(
        self,
        name: str,
        level: LogLevel = LogLevel.INFO,
        log_file: Optional[Union[str, Path]] = None,
        max_file_size: int = 100 * 1024 * 1024,  # 100MB
        backup_count: int = 5,
        console_output: bool = True,
        json_format: bool = True
    ):
        """Initialize telecommunications logger.
        
        Args:
            name: Logger name
            level: Log level
            log_file: Log file path
            max_file_size: Maximum log file size in bytes
            backup_count: Number of backup files to keep
            console_output: Enable console output
            json_format: Use JSON formatting
        """
        self.name = name
        self.level = level
        self.json_format = json_format
        
        # Create logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level.value)
        
        # Clear existing handlers
        self.logger.handlers.clear()
        
        # Always disable propagation to avoid duplicate logs
        self.logger.propagate = False
        
        # Setup formatters
        if json_format:
            formatter = TelecomFormatter(simple_format=True)
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        
        # Console handler - always add if console_output is True
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        # File handler
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.handlers.RotatingFileHandler(
                log_path,
                maxBytes=max_file_size,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
        
        # Thread-local context storage
        self._context = threading.local()
    
    def _get_context(self) -> LogContext:
        """Get current thread context."""
        if not hasattr(self._context, 'data'):
            self._context.data = LogContext()
        return self._context.data
    
    def _set_context(self, context: LogContext) -> None:
        """Set current thread context."""
        self._context.data = context
    
    def set_context(
        self,
        operation_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        component: Optional[str] = None,
        **kwargs
    ) -> None:
        """Set logging context.
        
        Args:
            operation_id: Operation identifier
            user_id: User identifier
            session_id: Session identifier
            request_id: Request identifier
            component: Component name
            **kwargs: Additional context data
        """
        context = self._get_context()
        
        if operation_id is not None:
            context.operation_id = operation_id
        if user_id is not None:
            context.user_id = user_id
        if session_id is not None:
            context.session_id = session_id
        if request_id is not None:
            context.request_id = request_id
        if component is not None:
            context.component = component
        
        context.metadata.update(kwargs)
    
    def clear_context(self) -> None:
        """Clear logging context."""
        self._set_context(LogContext())
    
    def _log_with_context(
        self,
        level: int,
        message: str,
        *args,
        exc_info: Optional[bool] = None,
        extra: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> None:
        """Log message with context.
        
        Args:
            level: Log level
            message: Log message
            *args: Message arguments
            exc_info: Include exception info
            extra: Extra log data
            **kwargs: Additional context
        """
        # Merge context
        context = self._get_context()
        log_extra = context.to_dict()
        
        if extra:
            log_extra.update(extra)
        
        log_extra.update(kwargs)
        
        # Log message
        self.logger.log(level, message, *args, exc_info=exc_info, extra=log_extra)
    
    def debug(self, message: str, *args, **kwargs) -> None:
        """Log debug message."""
        self._log_with_context(logging.DEBUG, message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs) -> None:
        """Log info message."""
        self._log_with_context(logging.INFO, message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs) -> None:
        """Log warning message."""
        self._log_with_context(logging.WARNING, message, *args, **kwargs)
    
    def error(self, message: str, *args, exc_info: bool = True, **kwargs) -> None:
        """Log error message."""
        self._log_with_context(logging.ERROR, message, *args, exc_info=exc_info, **kwargs)
    
    def critical(self, message: str, *args, exc_info: bool = True, **kwargs) -> None:
        """Log critical message."""
        self._log_with_context(logging.CRITICAL, message, *args, exc_info=exc_info, **kwargs)
    
    def log_performance(
        self,
        operation: str,
        duration_ms: float,
        records_processed: Optional[int] = None,
        bytes_processed: Optional[int] = None,
        memory_used_mb: Optional[float] = None,
        success: bool = True,
        **kwargs
    ) -> None:
        """Log performance metrics.
        
        Args:
            operation: Operation name
            duration_ms: Duration in milliseconds
            records_processed: Number of records processed
            bytes_processed: Number of bytes processed
            memory_used_mb: Memory used in MB
            success: Whether operation was successful
            **kwargs: Additional context
        """
        level = logging.INFO if success else logging.WARNING
        
        message = f"Performance: {operation} completed in {duration_ms:.2f}ms"
        
        if records_processed:
            rps = records_processed / (duration_ms / 1000) if duration_ms > 0 else 0
            message += f" ({records_processed} records, {rps:.1f} rps)"
        
        self._log_with_context(
            level,
            message,
            duration_ms=duration_ms,
            records_processed=records_processed,
            bytes_processed=bytes_processed,
            memory_used_mb=memory_used_mb,
            operation=operation,
            success=success,
            **kwargs
        )
    
    def log_telecom_event(
        self,
        event_type: str,
        message: str,
        imsi: Optional[str] = None,
        msisdn: Optional[str] = None,
        cell_id: Optional[str] = None,
        lac: Optional[str] = None,
        tac: Optional[str] = None,
        **kwargs
    ) -> None:
        """Log telecommunications event.
        
        Args:
            event_type: Type of event
            message: Event message
            imsi: IMSI
            msisdn: MSISDN
            cell_id: Cell ID
            lac: LAC
            tac: TAC
            **kwargs: Additional context
        """
        self._log_with_context(
            logging.INFO,
            f"Telecom Event [{event_type}]: {message}",
            imsi=imsi,
            msisdn=msisdn,
            cell_id=cell_id,
            lac=lac,
            tac=tac,
            event_type=event_type,
            **kwargs
        )
    
    @contextmanager
    def operation_context(
        self,
        operation_id: str,
        operation_name: str,
        **context_kwargs
    ):
        """Context manager for operation logging.
        
        Args:
            operation_id: Operation identifier
            operation_name: Operation name
            **context_kwargs: Additional context
        """
        # Save current context
        old_context = self._get_context()
        
        # Set new context
        self.set_context(
            operation_id=operation_id,
            component=operation_name,
            **context_kwargs
        )
        
        start_time = datetime.now()
        self.info(f"Started operation: {operation_name}", operation=operation_name)
        
        try:
            yield self
            
            # Log successful completion
            duration = (datetime.now() - start_time).total_seconds() * 1000
            self.log_performance(
                operation_name,
                duration,
                success=True
            )
            
        except Exception as e:
            # Log error
            duration = (datetime.now() - start_time).total_seconds() * 1000
            self.error(
                f"Operation failed: {operation_name} - {str(e)}",
                operation=operation_name,
                duration_ms=duration,
                success=False
            )
            raise
        
        finally:
            # Restore old context
            self._set_context(old_context)


# Global logger registry
_loggers: Dict[str, TelecomLogger] = {}
_logger_lock = threading.Lock()


def setup_logging(
    log_level: LogLevel = LogLevel.INFO,
    log_file: Optional[Union[str, Path]] = None,
    console_output: bool = True,
    json_format: bool = True,
    max_file_size: int = 100 * 1024 * 1024,
    backup_count: int = 5
) -> None:
    """Setup global logging configuration.
    
    Args:
        log_level: Default log level
        log_file: Default log file path
        console_output: Enable console output
        json_format: Use JSON formatting
        max_file_size: Maximum log file size
        backup_count: Number of backup files
    """
    # Configure root logger - disable propagation to avoid duplicate logs
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level.value)
    
    # Clear existing handlers to avoid duplicates
    root_logger.handlers.clear()
    
    # Don't add handlers to root logger - let individual loggers handle their own output
    
    # Store default configuration
    global _default_config
    _default_config = {
        'log_level': log_level,
        'log_file': log_file,
        'console_output': console_output,
        'json_format': json_format,
        'max_file_size': max_file_size,
        'backup_count': backup_count
    }


def get_logger(
    name: str,
    level: Optional[LogLevel] = None,
    log_file: Optional[Union[str, Path]] = None,
    **kwargs
) -> TelecomLogger:
    """Get or create telecommunications logger.
    
    Args:
        name: Logger name
        level: Log level (uses default if not specified)
        log_file: Log file path (uses default if not specified)
        **kwargs: Additional logger configuration
        
    Returns:
        TelecomLogger instance
    """
    with _logger_lock:
        if name not in _loggers:
            # Use default configuration if not specified
            config = _default_config
            
            logger_config = {
                'level': level or config.get('log_level', LogLevel.INFO),
                'log_file': log_file or config.get('log_file'),
                'console_output': config.get('console_output', True),
                'json_format': config.get('json_format', True),
                'max_file_size': config.get('max_file_size', 100 * 1024 * 1024),
                'backup_count': config.get('backup_count', 5)
            }
            
            logger_config.update(kwargs)
            
            _loggers[name] = TelecomLogger(name, **logger_config)
        
        return _loggers[name]


# Store default configuration
_default_config = {}
get_logger._default_config = _default_config