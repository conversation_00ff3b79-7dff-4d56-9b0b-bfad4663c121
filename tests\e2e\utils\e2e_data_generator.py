#!/usr/bin/env python3
"""
E2E测试数据生成器

该模块提供:
1. 各种类型测试数据的生成
2. 性能测试数据集生成
3. 边界条件和异常数据生成
4. 地理空间测试数据生成
5. 时序数据生成

使用方法:
    from tests.e2e.utils.e2e_data_generator import TestDataGenerator
    
    generator = TestDataGenerator()
    ep_data = generator.generate_ep_data(rows=1000)
    cdr_data = generator.generate_cdr_data(rows=500)
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import json
import random
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    import pandas as pd
    import numpy as np
    from faker import Faker
    import geopandas as gpd
    from shapely.geometry import Point, Polygon
except ImportError as e:
    print(f"警告: 缺少可选依赖包: {e}")
    # 提供基础实现
    pd = None
    np = None
    Faker = None
    gpd = None
    Point = None
    Polygon = None

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class _TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self, seed: int = 42, locale: str = 'zh_CN'):
        """初始化数据生成器
        
        Args:
            seed: 随机种子，确保数据可重现
            locale: 地区设置，影响生成的数据格式
        """
        self.seed = seed
        self.locale = locale
        
        # 设置随机种子
        random.seed(seed)
        if np:
            np.random.seed(seed)
        
        # 初始化Faker
        if Faker:
            self.fake = Faker(locale)
            self.fake.seed_instance(seed)
        else:
            self.fake = None
        
        # 定义常用的地理边界（北京地区）
        self.beijing_bounds = {
            'min_longitude': 115.7,
            'max_longitude': 117.4,
            'min_latitude': 39.4,
            'max_latitude': 41.6
        }
        
        # 定义运营商和技术类型
        self.operators = ['中国移动', '中国联通', '中国电信']
        self.technologies = ['2G', '3G', '4G', '5G']
        self.call_types = ['voice', 'video', 'data', 'sms']
        
        logger.info(f"测试数据生成器初始化完成，种子: {seed}")
    
    def generate_ep_data(self, 
                        rows: int = 1000,
                        start_date: str = '2024-01-01',
                        duration_hours: int = 24,
                        include_invalid: bool = False,
                        invalid_ratio: float = 0.05) -> pd.DataFrame:
        """生成EP（工程参数）测试数据
        
        Args:
            rows: 生成的行数
            start_date: 开始日期
            duration_hours: 数据时间跨度（小时）
            include_invalid: 是否包含无效数据
            invalid_ratio: 无效数据比例
        
        Returns:
            EP数据DataFrame
        """
        if not pd or not np:
            raise ImportError("需要安装pandas和numpy")
        
        logger.info(f"生成EP数据: {rows}行, 时间范围: {start_date} + {duration_hours}小时")
        
        start_dt = pd.to_datetime(start_date)
        
        # 生成基础数据
        data = {
            'timestamp': pd.date_range(
                start=start_dt,
                periods=rows,
                freq=f'{duration_hours*60//rows}min'
            ),
            'longitude': np.random.uniform(
                self.beijing_bounds['min_longitude'],
                self.beijing_bounds['max_longitude'],
                rows
            ),
            'latitude': np.random.uniform(
                self.beijing_bounds['min_latitude'],
                self.beijing_bounds['max_latitude'],
                rows
            ),
            'signal_strength': np.random.uniform(-120, -60, rows),
            'technology': np.random.choice(self.technologies, rows),
            'operator': np.random.choice(self.operators, rows),
            'cell_id': [f'CELL_{i:08d}' for i in range(rows)],
            'frequency': np.random.choice([900, 1800, 2100, 2600, 3500], rows),
            'bandwidth': np.random.choice([5, 10, 15, 20], rows),
            'rsrp': np.random.uniform(-140, -44, rows),
            'rsrq': np.random.uniform(-20, -3, rows),
            'sinr': np.random.uniform(-20, 30, rows)
        }
        
        df = pd.DataFrame(data)
        
        # 添加无效数据
        if include_invalid and invalid_ratio > 0:
            invalid_count = int(rows * invalid_ratio)
            invalid_indices = np.random.choice(rows, invalid_count, replace=False)
            
            for idx in invalid_indices:
                # 随机选择要破坏的字段
                field_to_corrupt = np.random.choice([
                    'longitude', 'latitude', 'signal_strength', 
                    'technology', 'operator'
                ])
                
                if field_to_corrupt == 'longitude':
                    df.loc[idx, 'longitude'] = np.random.choice([200.0, -200.0, np.nan])
                elif field_to_corrupt == 'latitude':
                    df.loc[idx, 'latitude'] = np.random.choice([100.0, -100.0, np.nan])
                elif field_to_corrupt == 'signal_strength':
                    df.loc[idx, 'signal_strength'] = np.random.choice([0, -200, np.nan])
                elif field_to_corrupt == 'technology':
                    df.loc[idx, 'technology'] = 'INVALID_TECH'
                elif field_to_corrupt == 'operator':
                    df.loc[idx, 'operator'] = ''
        
        logger.info(f"EP数据生成完成: {len(df)}行")
        return df
    
    def generate_cdr_data(self,
                         rows: int = 1000,
                         start_date: str = '2024-01-01',
                         duration_hours: int = 24,
                         include_invalid: bool = False,
                         invalid_ratio: float = 0.05) -> pd.DataFrame:
        """生成CDR（呼叫详单）测试数据
        
        Args:
            rows: 生成的行数
            start_date: 开始日期
            duration_hours: 数据时间跨度（小时）
            include_invalid: 是否包含无效数据
            invalid_ratio: 无效数据比例
        
        Returns:
            CDR数据DataFrame
        """
        if not pd or not np:
            raise ImportError("需要安装pandas和numpy")
        
        logger.info(f"生成CDR数据: {rows}行, 时间范围: {start_date} + {duration_hours}小时")
        
        start_dt = pd.to_datetime(start_date)
        
        # 生成通话时长（对数正态分布，更符合实际）
        durations = np.random.lognormal(mean=4, sigma=1, size=rows).astype(int)
        durations = np.clip(durations, 1, 7200)  # 1秒到2小时
        
        # 生成开始时间
        start_times = []
        for i in range(rows):
            random_offset = np.random.randint(0, duration_hours * 3600)
            start_times.append(start_dt + timedelta(seconds=random_offset))
        
        # 生成结束时间
        end_times = [start_times[i] + timedelta(seconds=durations[i]) for i in range(rows)]
        
        data = {
            'call_id': [f'CALL_{i:010d}' for i in range(rows)],
            'start_time': start_times,
            'end_time': end_times,
            'duration': durations,
            'caller_number': [self._generate_phone_number() for _ in range(rows)],
            'callee_number': [self._generate_phone_number() for _ in range(rows)],
            'caller_longitude': np.random.uniform(
                self.beijing_bounds['min_longitude'],
                self.beijing_bounds['max_longitude'],
                rows
            ),
            'caller_latitude': np.random.uniform(
                self.beijing_bounds['min_latitude'],
                self.beijing_bounds['max_latitude'],
                rows
            ),
            'callee_longitude': np.random.uniform(
                self.beijing_bounds['min_longitude'],
                self.beijing_bounds['max_longitude'],
                rows
            ),
            'callee_latitude': np.random.uniform(
                self.beijing_bounds['min_latitude'],
                self.beijing_bounds['max_latitude'],
                rows
            ),
            'call_type': np.random.choice(self.call_types, rows),
            'operator': np.random.choice(self.operators, rows),
            'success': np.random.choice([True, False], rows, p=[0.95, 0.05]),
            'cell_id_start': [f'CELL_{np.random.randint(1, 10000):08d}' for _ in range(rows)],
            'cell_id_end': [f'CELL_{np.random.randint(1, 10000):08d}' for _ in range(rows)]
        }
        
        df = pd.DataFrame(data)
        
        # 添加无效数据
        if include_invalid and invalid_ratio > 0:
            invalid_count = int(rows * invalid_ratio)
            invalid_indices = np.random.choice(rows, invalid_count, replace=False)
            
            for idx in invalid_indices:
                corruption_type = np.random.choice([
                    'negative_duration', 'invalid_coordinates', 
                    'invalid_phone', 'invalid_time_order'
                ])
                
                if corruption_type == 'negative_duration':
                    df.loc[idx, 'duration'] = -1
                elif corruption_type == 'invalid_coordinates':
                    df.loc[idx, 'caller_longitude'] = 200.0
                    df.loc[idx, 'caller_latitude'] = 100.0
                elif corruption_type == 'invalid_phone':
                    df.loc[idx, 'caller_number'] = 'INVALID_PHONE'
                elif corruption_type == 'invalid_time_order':
                    # 结束时间早于开始时间
                    df.loc[idx, 'end_time'] = df.loc[idx, 'start_time'] - timedelta(minutes=1)
        
        logger.info(f"CDR数据生成完成: {len(df)}行")
        return df
    
    def generate_site_data(self,
                          rows: int = 100,
                          include_invalid: bool = False,
                          invalid_ratio: float = 0.05) -> pd.DataFrame:
        """生成站点数据
        
        Args:
            rows: 生成的行数
            include_invalid: 是否包含无效数据
            invalid_ratio: 无效数据比例
        
        Returns:
            站点数据DataFrame
        """
        if not pd or not np:
            raise ImportError("需要安装pandas和numpy")
        
        logger.info(f"生成站点数据: {rows}行")
        
        data = {
            'site_id': [f'SITE_{i:06d}' for i in range(rows)],
            'site_name': [f'基站_{i:04d}' for i in range(rows)],
            'longitude': np.random.uniform(
                self.beijing_bounds['min_longitude'],
                self.beijing_bounds['max_longitude'],
                rows
            ),
            'latitude': np.random.uniform(
                self.beijing_bounds['min_latitude'],
                self.beijing_bounds['max_latitude'],
                rows
            ),
            'technology': np.random.choice(self.technologies, rows),
            'operator': np.random.choice(self.operators, rows),
            'status': np.random.choice(['active', 'inactive', 'maintenance'], rows, p=[0.85, 0.10, 0.05]),
            'coverage_radius': np.random.uniform(500, 5000, rows),
            'antenna_height': np.random.uniform(20, 80, rows),
            'power': np.random.uniform(10, 60, rows),
            'frequency': np.random.choice([900, 1800, 2100, 2600, 3500], rows),
            'installation_date': [
                self.fake.date_between(start_date='-5y', end_date='today') if self.fake 
                else datetime(2020, 1, 1) + timedelta(days=np.random.randint(0, 1460))
                for _ in range(rows)
            ],
            'last_maintenance': [
                self.fake.date_between(start_date='-1y', end_date='today') if self.fake
                else datetime(2023, 1, 1) + timedelta(days=np.random.randint(0, 365))
                for _ in range(rows)
            ]
        }
        
        df = pd.DataFrame(data)
        
        # 添加无效数据
        if include_invalid and invalid_ratio > 0:
            invalid_count = int(rows * invalid_ratio)
            invalid_indices = np.random.choice(rows, invalid_count, replace=False)
            
            for idx in invalid_indices:
                corruption_type = np.random.choice([
                    'invalid_coordinates', 'negative_radius', 
                    'invalid_status', 'future_installation'
                ])
                
                if corruption_type == 'invalid_coordinates':
                    df.loc[idx, 'longitude'] = 200.0
                elif corruption_type == 'negative_radius':
                    df.loc[idx, 'coverage_radius'] = -1000
                elif corruption_type == 'invalid_status':
                    df.loc[idx, 'status'] = 'INVALID_STATUS'
                elif corruption_type == 'future_installation':
                    df.loc[idx, 'installation_date'] = datetime.now() + timedelta(days=365)
        
        logger.info(f"站点数据生成完成: {len(df)}行")
        return df
    
    def generate_kpi_data(self,
                         rows: int = 1000,
                         start_date: str = '2024-01-01',
                         duration_days: int = 30,
                         include_invalid: bool = False,
                         invalid_ratio: float = 0.05) -> pd.DataFrame:
        """生成KPI数据
        
        Args:
            rows: 生成的行数
            start_date: 开始日期
            duration_days: 数据时间跨度（天）
            include_invalid: 是否包含无效数据
            invalid_ratio: 无效数据比例
        
        Returns:
            KPI数据DataFrame
        """
        if not pd or not np:
            raise ImportError("需要安装pandas和numpy")
        
        logger.info(f"生成KPI数据: {rows}行, 时间范围: {start_date} + {duration_days}天")
        
        start_dt = pd.to_datetime(start_date)
        
        data = {
            'timestamp': pd.date_range(
                start=start_dt,
                periods=rows,
                freq=f'{duration_days*24*60//rows}min'
            ),
            'site_id': [f'SITE_{np.random.randint(1, 1000):06d}' for _ in range(rows)],
            'cell_id': [f'CELL_{np.random.randint(1, 10000):08d}' for _ in range(rows)],
            'technology': np.random.choice(self.technologies, rows),
            'operator': np.random.choice(self.operators, rows),
            
            # 网络性能指标
            'throughput_dl': np.random.uniform(10, 1000, rows),  # Mbps
            'throughput_ul': np.random.uniform(5, 100, rows),    # Mbps
            'latency': np.random.uniform(10, 100, rows),         # ms
            'packet_loss': np.random.uniform(0, 5, rows),        # %
            'jitter': np.random.uniform(1, 20, rows),            # ms
            
            # 信号质量指标
            'rsrp': np.random.uniform(-140, -44, rows),          # dBm
            'rsrq': np.random.uniform(-20, -3, rows),            # dB
            'sinr': np.random.uniform(-20, 30, rows),            # dB
            'rssi': np.random.uniform(-110, -30, rows),          # dBm
            
            # 业务指标
            'call_success_rate': np.random.uniform(85, 99.9, rows),      # %
            'handover_success_rate': np.random.uniform(90, 99.5, rows),  # %
            'drop_call_rate': np.random.uniform(0.1, 5, rows),           # %
            'block_call_rate': np.random.uniform(0.1, 3, rows),          # %
            
            # 资源利用率
            'cpu_usage': np.random.uniform(20, 90, rows),        # %
            'memory_usage': np.random.uniform(30, 85, rows),     # %
            'disk_usage': np.random.uniform(40, 80, rows),       # %
            'bandwidth_usage': np.random.uniform(10, 95, rows),  # %
            
            # 用户数量
            'active_users': np.random.randint(50, 2000, rows),
            'peak_users': np.random.randint(100, 3000, rows),
            'avg_users': np.random.randint(80, 1500, rows)
        }
        
        df = pd.DataFrame(data)
        
        # 确保peak_users >= active_users >= avg_users的逻辑关系
        df['peak_users'] = np.maximum(df['peak_users'], df['active_users'])
        df['active_users'] = np.maximum(df['active_users'], df['avg_users'])
        
        # 添加无效数据
        if include_invalid and invalid_ratio > 0:
            invalid_count = int(rows * invalid_ratio)
            invalid_indices = np.random.choice(rows, invalid_count, replace=False)
            
            for idx in invalid_indices:
                corruption_type = np.random.choice([
                    'negative_throughput', 'invalid_percentage', 
                    'extreme_latency', 'invalid_signal'
                ])
                
                if corruption_type == 'negative_throughput':
                    df.loc[idx, 'throughput_dl'] = -100
                elif corruption_type == 'invalid_percentage':
                    df.loc[idx, 'call_success_rate'] = 150  # 超过100%
                elif corruption_type == 'extreme_latency':
                    df.loc[idx, 'latency'] = 10000  # 极端延迟
                elif corruption_type == 'invalid_signal':
                    df.loc[idx, 'rsrp'] = 0  # 无效信号强度
        
        logger.info(f"KPI数据生成完成: {len(df)}行")
        return df
    
    def generate_large_dataset(self,
                              data_type: str,
                              total_rows: int,
                              batch_size: int = 100000,
                              output_dir: str = None) -> List[Path]:
        """生成大数据集（分批生成以避免内存问题）
        
        Args:
            data_type: 数据类型 ('ep', 'cdr', 'site', 'kpi')
            total_rows: 总行数
            batch_size: 批次大小
            output_dir: 输出目录
        
        Returns:
            生成的文件路径列表
        """
        if not output_dir:
            output_dir = Path("tests/e2e/data/large")
        else:
            output_dir = Path(output_dir)
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"生成大数据集: {data_type}, 总行数: {total_rows}, 批次大小: {batch_size}")
        
        file_paths = []
        batches = (total_rows + batch_size - 1) // batch_size
        
        for batch_idx in range(batches):
            start_row = batch_idx * batch_size
            end_row = min(start_row + batch_size, total_rows)
            current_batch_size = end_row - start_row
            
            logger.info(f"生成批次 {batch_idx + 1}/{batches}: {current_batch_size}行")
            
            # 生成数据
            if data_type == 'ep':
                df = self.generate_ep_data(rows=current_batch_size)
            elif data_type == 'cdr':
                df = self.generate_cdr_data(rows=current_batch_size)
            elif data_type == 'site':
                df = self.generate_site_data(rows=current_batch_size)
            elif data_type == 'kpi':
                df = self.generate_kpi_data(rows=current_batch_size)
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")
            
            # 保存文件
            file_path = output_dir / f"{data_type}_large_batch_{batch_idx:03d}.csv"
            df.to_csv(file_path, index=False)
            file_paths.append(file_path)
            
            logger.info(f"批次 {batch_idx + 1} 保存到: {file_path}")
        
        logger.info(f"大数据集生成完成: {len(file_paths)}个文件")
        return file_paths
    
    def generate_geo_test_data(self,
                              rows: int = 1000,
                              region: str = 'beijing') -> pd.DataFrame:
        """生成地理空间测试数据
        
        Args:
            rows: 生成的行数
            region: 地理区域 ('beijing', 'shanghai', 'guangzhou')
        
        Returns:
            地理空间数据DataFrame
        """
        if not pd or not np:
            raise ImportError("需要安装pandas和numpy")
        
        logger.info(f"生成地理空间测试数据: {rows}行, 区域: {region}")
        
        # 定义不同区域的边界
        region_bounds = {
            'beijing': self.beijing_bounds,
            'shanghai': {
                'min_longitude': 120.8, 'max_longitude': 122.2,
                'min_latitude': 30.7, 'max_latitude': 31.9
            },
            'guangzhou': {
                'min_longitude': 112.9, 'max_longitude': 114.0,
                'min_latitude': 22.4, 'max_latitude': 23.9
            }
        }
        
        bounds = region_bounds.get(region, self.beijing_bounds)
        
        # 生成不同类型的地理分布
        distribution_types = ['uniform', 'clustered', 'linear', 'circular']
        
        data_points = []
        
        for i in range(rows):
            dist_type = np.random.choice(distribution_types)
            
            if dist_type == 'uniform':
                # 均匀分布
                lon = np.random.uniform(bounds['min_longitude'], bounds['max_longitude'])
                lat = np.random.uniform(bounds['min_latitude'], bounds['max_latitude'])
            
            elif dist_type == 'clustered':
                # 聚类分布
                center_lon = np.random.uniform(bounds['min_longitude'], bounds['max_longitude'])
                center_lat = np.random.uniform(bounds['min_latitude'], bounds['max_latitude'])
                
                # 在中心点周围生成聚类
                offset_lon = np.random.normal(0, 0.01)
                offset_lat = np.random.normal(0, 0.01)
                
                lon = np.clip(center_lon + offset_lon, bounds['min_longitude'], bounds['max_longitude'])
                lat = np.clip(center_lat + offset_lat, bounds['min_latitude'], bounds['max_latitude'])
            
            elif dist_type == 'linear':
                # 线性分布（模拟道路）
                t = np.random.uniform(0, 1)
                start_lon = np.random.uniform(bounds['min_longitude'], bounds['max_longitude'])
                start_lat = np.random.uniform(bounds['min_latitude'], bounds['max_latitude'])
                end_lon = np.random.uniform(bounds['min_longitude'], bounds['max_longitude'])
                end_lat = np.random.uniform(bounds['min_latitude'], bounds['max_latitude'])
                
                lon = start_lon + t * (end_lon - start_lon)
                lat = start_lat + t * (end_lat - start_lat)
            
            else:  # circular
                # 圆形分布
                center_lon = (bounds['min_longitude'] + bounds['max_longitude']) / 2
                center_lat = (bounds['min_latitude'] + bounds['max_latitude']) / 2
                
                radius = np.random.uniform(0, 0.1)
                angle = np.random.uniform(0, 2 * np.pi)
                
                lon = center_lon + radius * np.cos(angle)
                lat = center_lat + radius * np.sin(angle)
            
            data_points.append({
                'point_id': f'POINT_{i:06d}',
                'longitude': lon,
                'latitude': lat,
                'distribution_type': dist_type,
                'region': region,
                'elevation': np.random.uniform(0, 200),  # 海拔
                'accuracy': np.random.uniform(1, 50),    # GPS精度
                'timestamp': datetime.now() - timedelta(minutes=np.random.randint(0, 1440))
            })
        
        df = pd.DataFrame(data_points)
        
        logger.info(f"地理空间测试数据生成完成: {len(df)}行")
        return df
    
    def generate_time_series_data(self,
                                 rows: int = 1000,
                                 start_date: str = '2024-01-01',
                                 frequency: str = '1min',
                                 trend: str = 'none',
                                 seasonality: bool = True,
                                 noise_level: float = 0.1) -> pd.DataFrame:
        """生成时序测试数据
        
        Args:
            rows: 生成的行数
            start_date: 开始日期
            frequency: 时间频率 ('1min', '5min', '1H', '1D')
            trend: 趋势类型 ('none', 'increasing', 'decreasing', 'seasonal')
            seasonality: 是否包含季节性
            noise_level: 噪声水平
        
        Returns:
            时序数据DataFrame
        """
        if not pd or not np:
            raise ImportError("需要安装pandas和numpy")
        
        logger.info(f"生成时序测试数据: {rows}行, 频率: {frequency}, 趋势: {trend}")
        
        # 生成时间索引
        timestamps = pd.date_range(start=start_date, periods=rows, freq=frequency)
        
        # 生成基础值
        base_values = np.ones(rows) * 100
        
        # 添加趋势
        if trend == 'increasing':
            trend_values = np.linspace(0, 50, rows)
        elif trend == 'decreasing':
            trend_values = np.linspace(50, 0, rows)
        elif trend == 'seasonal':
            # 年度季节性趋势
            trend_values = 20 * np.sin(2 * np.pi * np.arange(rows) / (365 * 24 * 60 / 5))  # 假设5分钟频率
        else:
            trend_values = np.zeros(rows)
        
        # 添加季节性
        if seasonality:
            # 日内季节性（24小时周期）
            daily_pattern = 15 * np.sin(2 * np.pi * np.arange(rows) / (24 * 60 / 5))  # 假设5分钟频率
            # 周内季节性（7天周期）
            weekly_pattern = 10 * np.sin(2 * np.pi * np.arange(rows) / (7 * 24 * 60 / 5))
            seasonal_values = daily_pattern + weekly_pattern
        else:
            seasonal_values = np.zeros(rows)
        
        # 添加噪声
        noise = np.random.normal(0, noise_level * 100, rows)
        
        # 组合所有成分
        values = base_values + trend_values + seasonal_values + noise
        
        # 确保值为正数
        values = np.maximum(values, 0.1)
        
        data = {
            'timestamp': timestamps,
            'value': values,
            'trend_component': trend_values,
            'seasonal_component': seasonal_values,
            'noise_component': noise,
            'metric_type': np.random.choice(['throughput', 'latency', 'users', 'errors'], rows),
            'site_id': [f'SITE_{np.random.randint(1, 100):03d}' for _ in range(rows)]
        }
        
        df = pd.DataFrame(data)
        
        logger.info(f"时序测试数据生成完成: {len(df)}行")
        return df
    
    def _generate_phone_number(self) -> str:
        """生成手机号码"""
        if self.fake:
            return self.fake.phone_number()
        else:
            # 简单的手机号生成
            prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                       '150', '151', '152', '153', '155', '156', '157', '158', '159',
                       '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
            prefix = random.choice(prefixes)
            suffix = ''.join([str(random.randint(0, 9)) for _ in range(8)])
            return f"{prefix}{suffix}"
    
    def save_test_datasets(self, output_dir: str = "tests/e2e/data") -> Dict[str, Path]:
        """保存标准测试数据集
        
        Args:
            output_dir: 输出目录
        
        Returns:
            保存的文件路径字典
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"保存标准测试数据集到: {output_path}")
        
        datasets = {
            'ep_small': self.generate_ep_data(rows=100),
            'ep_medium': self.generate_ep_data(rows=10000),
            'ep_invalid': self.generate_ep_data(rows=1000, include_invalid=True, invalid_ratio=0.1),
            
            'cdr_small': self.generate_cdr_data(rows=100),
            'cdr_medium': self.generate_cdr_data(rows=5000),
            'cdr_invalid': self.generate_cdr_data(rows=1000, include_invalid=True, invalid_ratio=0.1),
            
            'site_small': self.generate_site_data(rows=50),
            'site_medium': self.generate_site_data(rows=500),
            'site_invalid': self.generate_site_data(rows=100, include_invalid=True, invalid_ratio=0.1),
            
            'kpi_small': self.generate_kpi_data(rows=100),
            'kpi_medium': self.generate_kpi_data(rows=5000),
            'kpi_invalid': self.generate_kpi_data(rows=1000, include_invalid=True, invalid_ratio=0.1),
            
            'geo_test': self.generate_geo_test_data(rows=1000),
            'time_series': self.generate_time_series_data(rows=2000)
        }
        
        file_paths = {}
        
        for name, df in datasets.items():
            file_path = output_path / f"{name}.csv"
            df.to_csv(file_path, index=False)
            file_paths[name] = file_path
            logger.info(f"保存 {name}: {file_path} ({len(df)}行)")
        
        # 生成数据集说明文件
        readme_content = self._generate_dataset_readme(datasets)
        readme_path = output_path / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        logger.info(f"测试数据集保存完成: {len(file_paths)}个文件")
        return file_paths
    
    def _generate_dataset_readme(self, datasets: Dict[str, pd.DataFrame]) -> str:
        """生成数据集说明文档"""
        content = "# 测试数据集说明\n\n"
        content += "本目录包含用于E2E测试的各种数据集。\n\n"
        content += "## 数据集列表\n\n"
        
        for name, df in datasets.items():
            content += f"### {name}.csv\n"
            content += f"- 行数: {len(df)}\n"
            content += f"- 列数: {len(df.columns)}\n"
            content += f"- 列名: {', '.join(df.columns)}\n"
            
            if 'invalid' in name:
                content += "- 说明: 包含无效数据，用于测试数据验证功能\n"
            elif 'small' in name:
                content += "- 说明: 小数据集，用于快速测试\n"
            elif 'medium' in name:
                content += "- 说明: 中等数据集，用于性能测试\n"
            elif 'geo' in name:
                content += "- 说明: 地理空间数据，用于测试地理查询功能\n"
            elif 'time_series' in name:
                content += "- 说明: 时序数据，用于测试时间序列分析功能\n"
            
            content += "\n"
        
        content += "## 使用方法\n\n"
        content += "```python\n"
        content += "import pandas as pd\n\n"
        content += "# 加载测试数据\n"
        content += "df = pd.read_csv('ep_small.csv')\n"
        content += "```\n\n"
        
        content += "## 数据生成\n\n"
        content += "这些数据集由 `TestDataGenerator` 类生成，具有以下特点:\n\n"
        content += "- 使用固定随机种子，确保数据可重现\n"
        content += "- 包含真实的地理坐标（北京地区）\n"
        content += "- 模拟真实的电信数据分布\n"
        content += "- 包含各种边界条件和异常情况\n\n"
        
        return content


# 导出别名以便外部导入
TestDataGenerator = _TestDataGenerator

if __name__ == "__main__":
    # 示例用法
    generator = TestDataGenerator()
    
    # 生成并保存标准测试数据集
    file_paths = generator.save_test_datasets()
    
    print("生成的测试数据集:")
    for name, path in file_paths.items():
        print(f"  {name}: {path}")