#!/usr/bin/env python3
"""
Connect CI/CD集成脚本
用于在持续集成流水线中执行质量检查和测试

作者: Connect质量工程师
日期: 2024
"""

import os
import sys
import json
import yaml
import argparse
import subprocess
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import requests
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tests.monitoring.dashboard_integration import CIDashboardIntegration
from tests.monitoring.quality_gates import QualityGateChecker

class CIIntegration:
    """CI/CD集成主类"""
    
    def __init__(self, config_path: str = None):
        self.project_root = Path(__file__).parent.parent.parent
        self.config_path = config_path or self.project_root / "tests" / "config" / "ci_config.yaml"
        self.config = self._load_config()
        self.logger = self._setup_logging()
        self.dashboard = CIDashboardIntegration()
        self.quality_checker = QualityGateChecker()
        
    def _load_config(self) -> Dict[str, Any]:
        """加载CI配置"""
        if not self.config_path.exists():
            return self._get_default_config()
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'stages': {
                'build': {'enabled': True, 'timeout': 600},
                'test': {'enabled': True, 'timeout': 1800},
                'security': {'enabled': True, 'timeout': 900},
                'performance': {'enabled': True, 'timeout': 1200},
                'quality_gate': {'enabled': True, 'timeout': 300},
                'deploy': {'enabled': True, 'timeout': 600}
            },
            'test_suites': {
                'unit': {'command': 'pytest tests/unit', 'required': True},
                'integration': {'command': 'pytest tests/integration', 'required': True},
                'e2e': {'command': 'pytest tests/e2e', 'required': False},
                'performance': {'command': 'pytest tests/performance', 'required': False},
                'security': {'command': 'pytest tests/security', 'required': True}
            },
            'quality_gates': {
                'config_file': 'tests/monitoring/quality_gates.yaml',
                'fail_on_error': True,
                'generate_report': True
            },
            'notifications': {
                'slack': {'enabled': False},
                'email': {'enabled': False},
                'teams': {'enabled': False}
            },
            'artifacts': {
                'test_reports': 'test-reports/',
                'coverage_reports': 'coverage-reports/',
                'security_reports': 'security-reports/',
                'performance_reports': 'performance-reports/'
            }
        }
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('ci_integration')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def run_build_stage(self) -> Dict[str, Any]:
        """执行构建阶段"""
        self.logger.info("开始构建阶段")
        start_time = time.time()
        
        try:
            # 安装依赖
            self._run_command(['pip', 'install', '-r', 'requirements.txt'])
            
            # 前端构建（如果存在）
            if (self.project_root / 'package.json').exists():
                self._run_command(['npm', 'install'])
                self._run_command(['npm', 'run', 'build'])
            
            # 代码格式检查
            self._run_command(['black', '--check', '.'])
            self._run_command(['flake8', '.'])
            
            duration = time.time() - start_time
            result = {
                'stage': 'build',
                'status': 'passed',
                'duration': duration,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"构建阶段完成，耗时: {duration:.2f}秒")
            return result
            
        except subprocess.CalledProcessError as e:
            duration = time.time() - start_time
            result = {
                'stage': 'build',
                'status': 'failed',
                'duration': duration,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.error(f"构建阶段失败: {e}")
            return result
    
    def run_test_stage(self) -> Dict[str, Any]:
        """执行测试阶段"""
        self.logger.info("开始测试阶段")
        start_time = time.time()
        
        test_results = []
        overall_status = 'passed'
        
        for suite_name, suite_config in self.config['test_suites'].items():
            self.logger.info(f"执行测试套件: {suite_name}")
            
            try:
                # 执行测试命令
                cmd = suite_config['command'].split()
                cmd.extend([
                    '--junitxml=test-reports/junit.xml',
                    '--cov=.',
                    '--cov-report=xml:coverage-reports/coverage.xml',
                    '--cov-report=html:coverage-reports/html',
                    '--json-report',
                    '--json-report-file=test-reports/report.json'
                ])
                
                result = self._run_command(cmd, capture_output=True)
                
                suite_result = {
                    'suite': suite_name,
                    'status': 'passed',
                    'command': suite_config['command'],
                    'output': result.stdout,
                    'timestamp': datetime.now().isoformat()
                }
                
                test_results.append(suite_result)
                self.logger.info(f"测试套件 {suite_name} 通过")
                
            except subprocess.CalledProcessError as e:
                suite_result = {
                    'suite': suite_name,
                    'status': 'failed',
                    'command': suite_config['command'],
                    'error': str(e),
                    'output': e.stdout if hasattr(e, 'stdout') else '',
                    'timestamp': datetime.now().isoformat()
                }
                
                test_results.append(suite_result)
                
                if suite_config.get('required', True):
                    overall_status = 'failed'
                    self.logger.error(f"必需的测试套件 {suite_name} 失败: {e}")
                else:
                    self.logger.warning(f"可选的测试套件 {suite_name} 失败: {e}")
        
        duration = time.time() - start_time
        
        result = {
            'stage': 'test',
            'status': overall_status,
            'duration': duration,
            'test_results': test_results,
            'timestamp': datetime.now().isoformat()
        }
        
        self.logger.info(f"测试阶段完成，状态: {overall_status}，耗时: {duration:.2f}秒")
        return result
    
    def run_security_stage(self) -> Dict[str, Any]:
        """执行安全扫描阶段"""
        self.logger.info("开始安全扫描阶段")
        start_time = time.time()
        
        security_results = []
        overall_status = 'passed'
        
        try:
            # 依赖库安全扫描
            self.logger.info("执行依赖库安全扫描")
            try:
                result = self._run_command(['safety', 'check', '--json'], capture_output=True)
                security_results.append({
                    'scan_type': 'dependency',
                    'status': 'passed',
                    'output': result.stdout
                })
            except subprocess.CalledProcessError as e:
                security_results.append({
                    'scan_type': 'dependency',
                    'status': 'failed',
                    'error': str(e),
                    'output': e.stdout if hasattr(e, 'stdout') else ''
                })
                overall_status = 'failed'
            
            # 代码安全扫描
            self.logger.info("执行代码安全扫描")
            try:
                result = self._run_command(['bandit', '-r', '.', '-f', 'json'], capture_output=True)
                security_results.append({
                    'scan_type': 'static_analysis',
                    'status': 'passed',
                    'output': result.stdout
                })
            except subprocess.CalledProcessError as e:
                security_results.append({
                    'scan_type': 'static_analysis',
                    'status': 'failed',
                    'error': str(e),
                    'output': e.stdout if hasattr(e, 'stdout') else ''
                })
                overall_status = 'failed'
            
            # 执行安全测试
            self.logger.info("执行安全测试")
            try:
                result = self._run_command([
                    'pytest', 'tests/security',
                    '--junitxml=security-reports/security-junit.xml',
                    '--json-report',
                    '--json-report-file=security-reports/security-report.json'
                ], capture_output=True)
                
                security_results.append({
                    'scan_type': 'security_tests',
                    'status': 'passed',
                    'output': result.stdout
                })
            except subprocess.CalledProcessError as e:
                security_results.append({
                    'scan_type': 'security_tests',
                    'status': 'failed',
                    'error': str(e),
                    'output': e.stdout if hasattr(e, 'stdout') else ''
                })
                overall_status = 'failed'
            
            duration = time.time() - start_time
            
            result = {
                'stage': 'security',
                'status': overall_status,
                'duration': duration,
                'security_results': security_results,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"安全扫描阶段完成，状态: {overall_status}，耗时: {duration:.2f}秒")
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            result = {
                'stage': 'security',
                'status': 'failed',
                'duration': duration,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.error(f"安全扫描阶段失败: {e}")
            return result
    
    def run_performance_stage(self) -> Dict[str, Any]:
        """执行性能测试阶段"""
        self.logger.info("开始性能测试阶段")
        start_time = time.time()
        
        try:
            # 执行性能测试
            result = self._run_command([
                'pytest', 'tests/performance',
                '--junitxml=performance-reports/performance-junit.xml',
                '--json-report',
                '--json-report-file=performance-reports/performance-report.json'
            ], capture_output=True)
            
            duration = time.time() - start_time
            
            result = {
                'stage': 'performance',
                'status': 'passed',
                'duration': duration,
                'output': result.stdout,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"性能测试阶段完成，耗时: {duration:.2f}秒")
            return result
            
        except subprocess.CalledProcessError as e:
            duration = time.time() - start_time
            result = {
                'stage': 'performance',
                'status': 'failed',
                'duration': duration,
                'error': str(e),
                'output': e.stdout if hasattr(e, 'stdout') else '',
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.error(f"性能测试阶段失败: {e}")
            return result
    
    def run_quality_gate_stage(self, pipeline_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """执行质量门禁检查阶段"""
        self.logger.info("开始质量门禁检查阶段")
        start_time = time.time()
        
        try:
            # 收集测试结果
            self.dashboard.process_test_results('test-reports')
            
            # 执行质量门禁检查
            quality_result = self.quality_checker.check_quality_gates()
            
            duration = time.time() - start_time
            
            result = {
                'stage': 'quality_gate',
                'status': 'passed' if quality_result['passed'] else 'failed',
                'duration': duration,
                'quality_result': quality_result,
                'timestamp': datetime.now().isoformat()
            }
            
            if quality_result['passed']:
                self.logger.info("质量门禁检查通过")
            else:
                self.logger.error(f"质量门禁检查失败: {quality_result['failed_checks']}")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            result = {
                'stage': 'quality_gate',
                'status': 'failed',
                'duration': duration,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.error(f"质量门禁检查阶段失败: {e}")
            return result
    
    def run_deploy_stage(self, environment: str = 'staging') -> Dict[str, Any]:
        """执行部署阶段"""
        self.logger.info(f"开始部署到 {environment} 环境")
        start_time = time.time()
        
        try:
            # 这里应该包含实际的部署逻辑
            # 例如: Docker构建、Kubernetes部署等
            
            # 模拟部署过程
            self.logger.info("构建Docker镜像...")
            time.sleep(2)
            
            self.logger.info("推送到镜像仓库...")
            time.sleep(3)
            
            self.logger.info("部署到目标环境...")
            time.sleep(5)
            
            # 部署后验证
            self.logger.info("执行部署后验证...")
            verification_result = self._run_deployment_verification(environment)
            
            duration = time.time() - start_time
            
            result = {
                'stage': 'deploy',
                'status': 'passed' if verification_result['passed'] else 'failed',
                'duration': duration,
                'environment': environment,
                'verification_result': verification_result,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"部署阶段完成，状态: {result['status']}，耗时: {duration:.2f}秒")
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            result = {
                'stage': 'deploy',
                'status': 'failed',
                'duration': duration,
                'environment': environment,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.error(f"部署阶段失败: {e}")
            return result
    
    def _run_deployment_verification(self, environment: str) -> Dict[str, Any]:
        """执行部署后验证"""
        try:
            # 执行部署验证测试
            result = self._run_command([
                'pytest', 'tests/deployment',
                f'--env={environment}',
                '--junitxml=deployment-reports/verification-junit.xml'
            ], capture_output=True)
            
            return {
                'passed': True,
                'output': result.stdout
            }
            
        except subprocess.CalledProcessError as e:
            return {
                'passed': False,
                'error': str(e),
                'output': e.stdout if hasattr(e, 'stdout') else ''
            }
    
    def _run_command(self, cmd: List[str], capture_output: bool = False, cwd: str = None) -> subprocess.CompletedProcess:
        """执行命令"""
        self.logger.debug(f"执行命令: {' '.join(cmd)}")
        
        if capture_output:
            return subprocess.run(
                cmd,
                cwd=cwd or self.project_root,
                capture_output=True,
                text=True,
                check=True
            )
        else:
            return subprocess.run(
                cmd,
                cwd=cwd or self.project_root,
                check=True
            )
    
    def generate_pipeline_report(self, pipeline_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成流水线报告"""
        total_duration = sum(result.get('duration', 0) for result in pipeline_results)
        failed_stages = [result for result in pipeline_results if result.get('status') == 'failed']
        
        report = {
            'pipeline_id': os.environ.get('BUILD_NUMBER', f"local-{int(time.time())}"),
            'timestamp': datetime.now().isoformat(),
            'total_duration': total_duration,
            'overall_status': 'failed' if failed_stages else 'passed',
            'stages': pipeline_results,
            'failed_stages': failed_stages,
            'summary': {
                'total_stages': len(pipeline_results),
                'passed_stages': len([r for r in pipeline_results if r.get('status') == 'passed']),
                'failed_stages': len(failed_stages)
            }
        }
        
        # 保存报告
        report_path = self.project_root / 'pipeline-reports' / f"pipeline-{report['pipeline_id']}.json"
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"流水线报告已保存: {report_path}")
        return report
    
    def send_notifications(self, pipeline_report: Dict[str, Any]):
        """发送通知"""
        if pipeline_report['overall_status'] == 'failed':
            self.dashboard.send_alert(
                title="CI/CD流水线失败",
                message=f"流水线 {pipeline_report['pipeline_id']} 失败，失败阶段: {[s['stage'] for s in pipeline_report['failed_stages']]}",
                severity="high"
            )
    
    def run_full_pipeline(self, stages: List[str] = None, environment: str = 'staging') -> Dict[str, Any]:
        """执行完整的CI/CD流水线"""
        self.logger.info("开始执行CI/CD流水线")
        
        # 默认执行所有阶段
        if stages is None:
            stages = ['build', 'test', 'security', 'performance', 'quality_gate', 'deploy']
        
        pipeline_results = []
        
        # 执行各个阶段
        for stage in stages:
            if not self.config['stages'].get(stage, {}).get('enabled', True):
                self.logger.info(f"跳过禁用的阶段: {stage}")
                continue
            
            self.logger.info(f"执行阶段: {stage}")
            
            if stage == 'build':
                result = self.run_build_stage()
            elif stage == 'test':
                result = self.run_test_stage()
            elif stage == 'security':
                result = self.run_security_stage()
            elif stage == 'performance':
                result = self.run_performance_stage()
            elif stage == 'quality_gate':
                result = self.run_quality_gate_stage(pipeline_results)
            elif stage == 'deploy':
                result = self.run_deploy_stage(environment)
            else:
                self.logger.warning(f"未知阶段: {stage}")
                continue
            
            pipeline_results.append(result)
            
            # 如果关键阶段失败，停止流水线
            if result['status'] == 'failed' and stage in ['build', 'test', 'quality_gate']:
                self.logger.error(f"关键阶段 {stage} 失败，停止流水线")
                break
        
        # 生成报告
        pipeline_report = self.generate_pipeline_report(pipeline_results)
        
        # 发送通知
        self.send_notifications(pipeline_report)
        
        self.logger.info(f"CI/CD流水线完成，总体状态: {pipeline_report['overall_status']}")
        return pipeline_report

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Connect CI/CD集成脚本')
    parser.add_argument('--stages', nargs='+', 
                       choices=['build', 'test', 'security', 'performance', 'quality_gate', 'deploy'],
                       help='要执行的阶段')
    parser.add_argument('--environment', default='staging',
                       choices=['development', 'staging', 'production'],
                       help='部署环境')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建CI集成实例
    ci = CIIntegration(config_path=args.config)
    
    try:
        # 执行流水线
        result = ci.run_full_pipeline(stages=args.stages, environment=args.environment)
        
        # 根据结果设置退出码
        if result['overall_status'] == 'failed':
            sys.exit(1)
        else:
            sys.exit(0)
            
    except Exception as e:
        ci.logger.error(f"流水线执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()