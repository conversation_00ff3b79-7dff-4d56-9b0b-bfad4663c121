"""Validation framework exceptions.

This module defines all exceptions used by the validation framework,
providing clear error handling and debugging capabilities.
"""

from typing import Any, Dict, List, Optional

from ..database.exceptions import DatabaseError


class ValidationError(Exception):
    """Base exception for validation errors."""
    
    def __init__(
        self,
        message: str,
        rule_name: Optional[str] = None,
        validation_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        original_exception: Optional[Exception] = None,
    ):
        super().__init__(message)
        self.message = message
        self.rule_name = rule_name
        self.validation_type = validation_type
        self.details = details or {}
        self.original_exception = original_exception
    
    def __str__(self) -> str:
        """Return string representation."""
        parts = [self.message]
        if self.rule_name:
            parts.append(f"Rule: {self.rule_name}")
        if self.validation_type:
            parts.append(f"Type: {self.validation_type}")
        return " | ".join(parts)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": self.__class__.__name__,
            "message": self.message,
            "rule_name": self.rule_name,
            "validation_type": self.validation_type,
            "details": self.details,
        }


class ValidationConfigError(ValidationError):
    """Exception for validation configuration errors."""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.config_key = config_key
        self.config_value = config_value
        if config_key:
            self.details["config_key"] = config_key
        if config_value is not None:
            self.details["config_value"] = str(config_value)


class ValidationRuleError(ValidationError):
    """Exception for validation rule errors."""
    
    def __init__(
        self,
        message: str,
        rule_name: str,
        rule_parameters: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        super().__init__(message, rule_name=rule_name, **kwargs)
        self.rule_parameters = rule_parameters or {}
        self.details["rule_parameters"] = self.rule_parameters


class DataValidationError(ValidationError):
    """Exception for data validation errors."""
    
    def __init__(
        self,
        message: str,
        column: Optional[str] = None,
        row_index: Optional[int] = None,
        value: Optional[Any] = None,
        expected: Optional[Any] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.column = column
        self.row_index = row_index
        self.value = value
        self.expected = expected
        
        if column:
            self.details["column"] = column
        if row_index is not None:
            self.details["row_index"] = row_index
        if value is not None:
            self.details["value"] = str(value)
        if expected is not None:
            self.details["expected"] = str(expected)


class SchemaValidationError(ValidationError):
    """Exception for schema validation errors."""
    
    def __init__(
        self,
        message: str,
        schema_name: Optional[str] = None,
        missing_columns: Optional[List[str]] = None,
        extra_columns: Optional[List[str]] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.schema_name = schema_name
        self.missing_columns = missing_columns or []
        self.extra_columns = extra_columns or []
        
        if schema_name:
            self.details["schema_name"] = schema_name
        if self.missing_columns:
            self.details["missing_columns"] = self.missing_columns
        if self.extra_columns:
            self.details["extra_columns"] = self.extra_columns


class FileValidationError(ValidationError):
    """Exception for file validation errors."""
    
    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        file_size: Optional[int] = None,
        file_format: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.file_path = file_path
        self.file_size = file_size
        self.file_format = file_format
        
        if file_path:
            self.details["file_path"] = file_path
        if file_size is not None:
            self.details["file_size"] = file_size
        if file_format:
            self.details["file_format"] = file_format


class TelecomValidationError(ValidationError):
    """Exception for telecom-specific validation errors."""
    
    def __init__(
        self,
        message: str,
        data_source_type: Optional[str] = None,
        telecom_field: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.data_source_type = data_source_type
        self.telecom_field = telecom_field
        
        if data_source_type:
            self.details["data_source_type"] = data_source_type
        if telecom_field:
            self.details["telecom_field"] = telecom_field


class ValidationTimeoutError(ValidationError):
    """Exception for validation timeout errors."""
    
    def __init__(
        self,
        message: str,
        timeout_seconds: Optional[float] = None,
        elapsed_seconds: Optional[float] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.timeout_seconds = timeout_seconds
        self.elapsed_seconds = elapsed_seconds
        
        if timeout_seconds is not None:
            self.details["timeout_seconds"] = timeout_seconds
        if elapsed_seconds is not None:
            self.details["elapsed_seconds"] = elapsed_seconds


class ValidationDependencyError(ValidationError):
    """Exception for validation dependency errors."""
    
    def __init__(
        self,
        message: str,
        missing_dependencies: Optional[List[str]] = None,
        dependency_type: Optional[str] = None,
        **kwargs
    ):
        super().__init__(message, **kwargs)
        self.missing_dependencies = missing_dependencies or []
        self.dependency_type = dependency_type
        
        if self.missing_dependencies:
            self.details["missing_dependencies"] = self.missing_dependencies
        if dependency_type:
            self.details["dependency_type"] = dependency_type