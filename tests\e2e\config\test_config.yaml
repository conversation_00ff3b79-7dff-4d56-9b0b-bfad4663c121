# E2E测试配置文件
# 用于配置端到端测试的环境、数据库、API端点等设置

# 测试环境配置
environment:
  name: "e2e_test"
  debug: true
  log_level: "DEBUG"
  timeout: 300  # 测试超时时间(秒)

# 数据库配置
database:
  # 测试数据库连接
  test_db:
    host: "localhost"
    port: 5432
    database: "connect_test"
    username: "test_user"
    password: "test_password"
    schema: "public"
    
  # PostGIS扩展
  postgis:
    enabled: true
    version: "3.3"
    
  # 连接池配置
  pool:
    min_size: 2
    max_size: 10
    timeout: 30
    
  # 测试数据清理
  cleanup:
    enabled: true
    tables:
      - "ep_data"
      - "cdr_data"
      - "site_data"
      - "kpi_data"
      - "test_uploads"

# API服务配置
api:
  base_url: "http://localhost:8000"
  version: "v1"
  timeout: 30
  
  # 认证配置
  auth:
    enabled: true
    test_user:
      username: "test_admin"
      password: "test_password"
      email: "<EMAIL>"
    
  # API端点
  endpoints:
    auth:
      login: "/auth/login"
      logout: "/auth/logout"
      refresh: "/auth/refresh"
    
    data:
      upload: "/api/v1/data/upload"
      import: "/api/v1/data/import"
      export: "/api/v1/data/export"
      validate: "/api/v1/data/validate"
    
    analysis:
      cdr: "/api/v1/analysis/cdr"
      site: "/api/v1/analysis/site"
      kpi: "/api/v1/analysis/kpi"
      geo: "/api/v1/analysis/geo"
    
    reports:
      generate: "/api/v1/reports/generate"
      download: "/api/v1/reports/download"
      list: "/api/v1/reports/list"

# 测试数据配置
test_data:
  # 测试文件路径
  files:
    base_path: "tests/e2e/data"
    
    # EP数据测试文件
    ep_data:
      small: "ep_data_small.csv"      # 1000行
      medium: "ep_data_medium.csv"    # 10000行
      large: "ep_data_large.csv"      # 100000行
      invalid: "ep_data_invalid.csv"  # 包含错误数据
      
    # CDR数据测试文件
    cdr_data:
      small: "cdr_data_small.csv"
      medium: "cdr_data_medium.csv"
      large: "cdr_data_large.csv"
      
    # 站点数据测试文件
    site_data:
      basic: "site_data_basic.csv"
      with_geo: "site_data_geo.csv"
      
    # KPI数据测试文件
    kpi_data:
      daily: "kpi_daily.csv"
      hourly: "kpi_hourly.csv"
  
  # 地理数据配置
  geo_data:
    # 测试区域边界
    test_regions:
      - name: "北京市"
        bounds: [116.0, 39.5, 117.0, 40.5]
        center: [116.5, 40.0]
      - name: "上海市"
        bounds: [121.0, 31.0, 122.0, 32.0]
        center: [121.5, 31.5]
    
    # 坐标系配置
    coordinate_systems:
      - "EPSG:4326"  # WGS84
      - "EPSG:3857"  # Web Mercator
      - "EPSG:2154"  # RGF93 / Lambert-93

# 性能测试配置
performance:
  # 数据处理性能要求
  data_processing:
    max_import_time: 10  # 500万行数据导入最大时间(秒)
    max_query_time: 3    # 地理查询最大响应时间(秒)
    max_export_time: 15  # 数据导出最大时间(秒)
  
  # 并发测试配置
  concurrency:
    max_users: 20        # 最大并发用户数
    ramp_up_time: 60     # 用户增长时间(秒)
    test_duration: 300   # 测试持续时间(秒)
  
  # 内存使用限制
  memory:
    max_usage: 16        # 最大内存使用(GB)
    warning_threshold: 12 # 内存警告阈值(GB)

# 安全测试配置
security:
  # SQL注入测试
  sql_injection:
    enabled: true
    payloads:
      - "'; DROP TABLE users; --"
      - "' OR '1'='1"
      - "' UNION SELECT * FROM users --"
      - "'; INSERT INTO users VALUES ('hacker', 'password'); --"
  
  # XSS测试
  xss:
    enabled: true
    payloads:
      - "<script>alert('XSS')</script>"
      - "javascript:alert('XSS')"
      - "<img src=x onerror=alert('XSS')>"
  
  # 认证绕过测试
  auth_bypass:
    enabled: true
    test_endpoints:
      - "/api/v1/admin/users"
      - "/api/v1/admin/settings"
      - "/api/v1/data/sensitive"
  
  # 输入验证测试
  input_validation:
    enabled: true
    test_fields:
      - "file_upload"
      - "user_input"
      - "search_query"
      - "filter_params"

# 监控和日志配置
monitoring:
  # 测试执行监控
  execution:
    enabled: true
    metrics:
      - "test_duration"
      - "memory_usage"
      - "cpu_usage"
      - "database_connections"
  
  # 错误监控
  error_tracking:
    enabled: true
    capture_screenshots: true
    capture_logs: true
    capture_network: true
  
  # 性能监控
  performance_monitoring:
    enabled: true
    sample_rate: 1.0
    track_database_queries: true
    track_api_calls: true

# 报告配置
reporting:
  # 测试报告格式
  formats:
    - "html"
    - "json"
    - "junit"
    - "allure"
  
  # 报告输出路径
  output_dir: "tests/e2e/reports"
  
  # 报告内容配置
  content:
    include_screenshots: true
    include_logs: true
    include_performance_metrics: true
    include_security_results: true
  
  # 报告分发
  distribution:
    email:
      enabled: false
      recipients: ["<EMAIL>"]
    
    slack:
      enabled: false
      webhook_url: "${SLACK_WEBHOOK_URL}"
    
    file_storage:
      enabled: true
      path: "reports/e2e"

# 浏览器配置(用于Web UI测试)
browser:
  # 默认浏览器
  default: "chromium"
  
  # 浏览器选项
  options:
    headless: true
    viewport:
      width: 1920
      height: 1080
    
    # 超时配置
    timeouts:
      page_load: 30000
      element_wait: 10000
      action: 5000
  
  # 支持的浏览器
  supported:
    - "chromium"
    - "firefox"
    - "webkit"

# 重试和恢复配置
retry:
  # 测试重试配置
  test_retry:
    enabled: true
    max_attempts: 3
    delay: 5  # 重试间隔(秒)
  
  # 网络重试配置
  network_retry:
    enabled: true
    max_attempts: 5
    backoff_factor: 2
  
  # 数据库重试配置
  database_retry:
    enabled: true
    max_attempts: 3
    delay: 2

# 并行执行配置
parallel:
  # 是否启用并行执行
  enabled: true
  
  # 并行工作进程数
  workers: 4
  
  # 测试分组策略
  grouping:
    strategy: "by_module"  # by_module, by_tag, by_duration
    
  # 资源隔离
  isolation:
    database: true    # 每个进程使用独立数据库
    files: true       # 每个进程使用独立文件目录
    ports: true       # 每个进程使用不同端口

# 环境变量配置
environment_variables:
  # 必需的环境变量
  required:
    - "DATABASE_URL"
    - "API_BASE_URL"
    - "TEST_USER_TOKEN"
  
  # 可选的环境变量
  optional:
    - "SLACK_WEBHOOK_URL"
    - "TEAMS_WEBHOOK_URL"
    - "S3_BUCKET_NAME"
    - "GRAFANA_API_TOKEN"
  
  # 默认值
  defaults:
    LOG_LEVEL: "INFO"
    TEST_TIMEOUT: "300"
    MAX_WORKERS: "4"

# 清理配置
cleanup:
  # 测试后清理
  after_test:
    enabled: true
    actions:
      - "delete_test_data"
      - "close_connections"
      - "clear_cache"
  
  # 测试套件后清理
  after_suite:
    enabled: true
    actions:
      - "drop_test_database"
      - "remove_temp_files"
      - "stop_services"
  
  # 失败时清理
  on_failure:
    enabled: false  # 保留现场用于调试
    preserve_logs: true
    preserve_screenshots: true