name: Connect CI/CD Pipeline

# 触发条件
on:
  push:
    branches: [ main, develop, 'feature/*', 'hotfix/*' ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点运行完整测试套件
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_suite:
        description: '选择测试套件'
        required: false
        default: 'all'
        type: choice
        options:
          - all
          - unit
          - integration
          - e2e
          - performance
          - security
      deploy_environment:
        description: '部署环境'
        required: false
        default: 'none'
        type: choice
        options:
          - none
          - development
          - staging
          - production

# 环境变量
env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'
  REDIS_VERSION: '7'
  
  # 测试配置
  TEST_DATABASE_URL: postgresql://postgres:postgres@localhost:5432/connect_test
  TEST_REDIS_URL: redis://localhost:6379/0
  
  # 质量门禁配置
  QUALITY_GATE_CONFIG: tests/ci/quality_gates.yaml
  
  # 通知配置
  SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
  TEAMS_WEBHOOK_URL: ${{ secrets.TEAMS_WEBHOOK_URL }}
  
  # 监控配置
  PROMETHEUS_PUSHGATEWAY_URL: ${{ secrets.PROMETHEUS_PUSHGATEWAY_URL }}
  DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
  NEWRELIC_API_KEY: ${{ secrets.NEWRELIC_API_KEY }}
  
  # 安全扫描配置
  SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
  SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

# 作业定义
jobs:
  # 代码质量检查
  code_quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 获取完整历史用于SonarQube分析
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 代码格式检查 (Black)
      run: |
        black --check --diff .
    
    - name: 代码风格检查 (Flake8)
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: 类型检查 (MyPy)
      run: |
        mypy src/ --ignore-missing-imports
    
    - name: 安全检查 (Bandit)
      run: |
        bandit -r src/ -f json -o tests/reports/bandit_report.json
    
    - name: 依赖安全检查 (Safety)
      run: |
        safety check --json --output tests/reports/safety_report.json
    
    - name: 上传代码质量报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: code-quality-reports
        path: tests/reports/
        retention-days: 30

  # 单元测试
  unit_tests:
    name: 单元测试
    runs-on: ubuntu-latest
    needs: code_quality
    
    strategy:
      matrix:
        python-version: ['3.10', '3.11', '3.12']
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 运行单元测试
      run: |
        python -m pytest tests/unit/ \
          --cov=src \
          --cov-report=xml:tests/reports/coverage.xml \
          --cov-report=html:tests/reports/coverage_html \
          --junit-xml=tests/reports/junit_unit.xml \
          --tb=short \
          -v
    
    - name: 上传测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: unit-test-reports-py${{ matrix.python-version }}
        path: tests/reports/
        retention-days: 30
    
    - name: 上传覆盖率到Codecov
      uses: codecov/codecov-action@v3
      with:
        file: tests/reports/coverage.xml
        flags: unit
        name: unit-tests-py${{ matrix.python-version }}

  # 集成测试
  integration_tests:
    name: 集成测试
    runs-on: ubuntu-latest
    needs: unit_tests
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: connect_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:${{ env.REDIS_VERSION }}
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 数据库迁移
      run: |
        python manage.py migrate
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
    
    - name: 运行集成测试
      run: |
        python -m pytest tests/integration/ \
          --junit-xml=tests/reports/junit_integration.xml \
          --tb=short \
          -v
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
        REDIS_URL: ${{ env.TEST_REDIS_URL }}
    
    - name: 上传测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: integration-test-reports
        path: tests/reports/
        retention-days: 30

  # 性能测试
  performance_tests:
    name: 性能测试
    runs-on: ubuntu-latest
    needs: integration_tests
    if: github.event_name == 'schedule' || github.event.inputs.test_suite == 'all' || github.event.inputs.test_suite == 'performance'
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: connect_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:${{ env.REDIS_VERSION }}
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 准备测试数据
      run: |
        python tests/utils/test_helpers.py --generate-large-dataset
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
    
    - name: 运行性能测试
      run: |
        python -m pytest tests/performance/ \
          --junit-xml=tests/reports/junit_performance.xml \
          --tb=short \
          -v \
          --timeout=300
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
        REDIS_URL: ${{ env.TEST_REDIS_URL }}
    
    - name: 运行大数据量基准测试
      run: |
        python tests/performance/test_large_data_benchmarks.py
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
    
    - name: 上传性能测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: performance-test-reports
        path: tests/reports/
        retention-days: 30

  # 安全测试
  security_tests:
    name: 安全测试
    runs-on: ubuntu-latest
    needs: integration_tests
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: connect_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 启动应用服务
      run: |
        python manage.py runserver 8000 &
        sleep 10
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
    
    - name: 运行安全测试
      run: |
        python -m pytest tests/security/ \
          --junit-xml=tests/reports/junit_security.xml \
          --tb=short \
          -v
    
    - name: 运行高级安全扫描
      run: |
        python tests/security/test_advanced_security_scanning.py
    
    - name: OWASP ZAP扫描
      uses: zaproxy/action-baseline@v0.7.0
      with:
        target: 'http://localhost:8000'
        rules_file_name: '.zap/rules.tsv'
        cmd_options: '-a'
    
    - name: 上传安全测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-test-reports
        path: |
          tests/reports/
          report_html.html
          report_json.json
        retention-days: 30

  # E2E测试
  e2e_tests:
    name: E2E测试
    runs-on: ubuntu-latest
    needs: [unit_tests, integration_tests]
    if: github.event_name == 'schedule' || github.event.inputs.test_suite == 'all' || github.event.inputs.test_suite == 'e2e'
    
    services:
      postgres:
        image: postgres:${{ env.POSTGRES_VERSION }}
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: connect_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:${{ env.REDIS_VERSION }}
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v3
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 安装前端依赖
      run: |
        cd frontend
        npm ci
    
    - name: 构建前端
      run: |
        cd frontend
        npm run build
    
    - name: 数据库迁移和初始化
      run: |
        python manage.py migrate
        python manage.py loaddata tests/fixtures/test_data.json
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
    
    - name: 启动应用服务
      run: |
        python manage.py runserver 8000 &
        sleep 15
      env:
        DATABASE_URL: ${{ env.TEST_DATABASE_URL }}
        REDIS_URL: ${{ env.TEST_REDIS_URL }}
    
    - name: 运行E2E测试
      run: |
        python -m pytest tests/e2e/ \
          --junit-xml=tests/reports/junit_e2e.xml \
          --tb=short \
          -v \
          --timeout=600
    
    - name: 上传E2E测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-reports
        path: tests/reports/
        retention-days: 30
    
    - name: 上传截图
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: e2e-screenshots
        path: tests/screenshots/
        retention-days: 7

  # 质量门禁检查
  quality_gate:
    name: 质量门禁检查
    runs-on: ubuntu-latest
    needs: [code_quality, unit_tests, integration_tests, security_tests]
    if: always()
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: 下载所有测试报告
      uses: actions/download-artifact@v3
      with:
        path: tests/reports/
    
    - name: 合并测试报告
      run: |
        python tests/ci/aggregate_test_results.py \
          --input-dir tests/reports/ \
          --output tests/reports/aggregated_results.json
    
    - name: 运行质量门禁检查
      id: quality_gate
      run: |
        python tests/ci/test_ci_integration.py \
          --results-dir tests/reports/ \
          --notify \
          --push-metrics \
          --output tests/reports/ci_report.json
      continue-on-error: true
    
    - name: 生成质量报告
      run: |
        python tests/ci/generate_dashboard_data.py \
          --input tests/reports/ci_report.json \
          --output tests/reports/quality_dashboard.html
    
    - name: 上传质量报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: quality-gate-reports
        path: tests/reports/
        retention-days: 90
    
    - name: 发布质量报告到GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: tests/reports/
        destination_dir: quality-reports/${{ github.run_number }}
    
    - name: 更新PR状态
      uses: actions/github-script@v6
      if: github.event_name == 'pull_request'
      with:
        script: |
          const fs = require('fs');
          const reportPath = 'tests/reports/ci_report.json';
          
          if (fs.existsSync(reportPath)) {
            const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
            
            const status = report.status === 'passed' ? 'success' : 'failure';
            const description = `质量分数: ${report.metrics.overall_score.toFixed(1)}分 | 部署决策: ${report.deployment_decision}`;
            
            await github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sha: context.sha,
              state: status,
              target_url: `https://${context.repo.owner}.github.io/${context.repo.repo}/quality-reports/${context.runNumber}/quality_dashboard.html`,
              description: description,
              context: 'Connect Quality Gate'
            });
          }
    
    - name: 检查质量门禁结果
      run: |
        if [ "${{ steps.quality_gate.outcome }}" != "success" ]; then
          echo "质量门禁检查失败"
          exit 1
        fi

  # SonarQube分析
  sonarqube:
    name: SonarQube分析
    runs-on: ubuntu-latest
    needs: [unit_tests]
    if: github.event_name != 'pull_request' || github.event.pull_request.head.repo.full_name == github.repository
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 下载测试报告
      uses: actions/download-artifact@v3
      with:
        name: unit-test-reports-py${{ env.PYTHON_VERSION }}
        path: tests/reports/
    
    - name: SonarQube扫描
      uses: sonarqube-quality-gate-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      with:
        scanMetadataReportFile: tests/reports/sonar-report.json

  # 部署作业
  deploy_development:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: [quality_gate]
    if: |
      github.ref == 'refs/heads/develop' && 
      needs.quality_gate.result == 'success' &&
      (github.event.inputs.deploy_environment == 'development' || github.event.inputs.deploy_environment == '')
    environment:
      name: development
      url: https://connect-dev.example.com
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 部署到开发环境
      run: |
        echo "部署到开发环境..."
        # 这里添加实际的部署脚本
    
    - name: 运行部署后测试
      run: |
        python -m pytest tests/deployment/ \
          --env=development \
          --junit-xml=tests/reports/junit_deployment_dev.xml
    
    - name: 上传部署测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: deployment-test-reports-dev
        path: tests/reports/
        retention-days: 30

  deploy_staging:
    name: 部署到预发布环境
    runs-on: ubuntu-latest
    needs: [quality_gate]
    if: |
      github.ref == 'refs/heads/main' && 
      needs.quality_gate.result == 'success' &&
      (github.event.inputs.deploy_environment == 'staging' || github.event.inputs.deploy_environment == '')
    environment:
      name: staging
      url: https://connect-staging.example.com
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 部署到预发布环境
      run: |
        echo "部署到预发布环境..."
        # 这里添加实际的部署脚本
    
    - name: 运行部署后测试
      run: |
        python -m pytest tests/deployment/ \
          --env=staging \
          --junit-xml=tests/reports/junit_deployment_staging.xml
    
    - name: 上传部署测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: deployment-test-reports-staging
        path: tests/reports/
        retention-days: 30

  deploy_production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [deploy_staging]
    if: |
      github.ref == 'refs/heads/main' && 
      needs.deploy_staging.result == 'success' &&
      github.event.inputs.deploy_environment == 'production'
    environment:
      name: production
      url: https://connect.example.com
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 人工审核确认
      uses: trstringer/manual-approval@v1
      with:
        secret: ${{ github.TOKEN }}
        approvers: team-leads,senior-developers
        minimum-approvals: 2
        issue-title: "生产环境部署审批 - ${{ github.sha }}"
        issue-body: |
          请审核生产环境部署请求:
          
          - **分支**: ${{ github.ref }}
          - **提交**: ${{ github.sha }}
          - **构建**: ${{ github.run_number }}
          - **质量报告**: https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}/quality-reports/${{ github.run_number }}/quality_dashboard.html
          
          请确认所有质量检查都已通过，并且已在预发布环境验证功能正常。
    
    - name: 部署到生产环境
      run: |
        echo "部署到生产环境..."
        # 这里添加实际的部署脚本
    
    - name: 运行部署后测试
      run: |
        python -m pytest tests/deployment/ \
          --env=production \
          --junit-xml=tests/reports/junit_deployment_prod.xml
    
    - name: 上传部署测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: deployment-test-reports-prod
        path: tests/reports/
        retention-days: 90

  # 清理作业
  cleanup:
    name: 清理资源
    runs-on: ubuntu-latest
    needs: [quality_gate, deploy_development, deploy_staging, deploy_production]
    if: always()
    
    steps:
    - name: 清理临时文件
      run: |
        echo "清理临时文件和资源..."
        # 这里添加清理脚本
    
    - name: 发送最终通知
      if: github.ref == 'refs/heads/main'
      run: |
        echo "CI/CD流水线执行完成"
        # 发送最终状态通知