#!/bin/bash

# Connect 生产环境部署脚本
# 版本: 2.0.0
# 作者: Connect开发团队

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
DEPLOY_USER="connect"
DEPLOY_DIR="/opt/connect"
VENV_DIR="$DEPLOY_DIR/.venv"
SERVICE_NAME="connect-api"
BACKUP_DIR="/data/connect/backup"
LOG_DIR="/var/log/connect"

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# 检查是否以root权限运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "此脚本需要root权限运行"
    fi
}

# 检查系统要求
check_system_requirements() {
    log "检查系统要求..."
    
    # 检查操作系统
    if ! command -v lsb_release &> /dev/null; then
        warning "无法检测操作系统版本"
    else
        OS_VERSION=$(lsb_release -d | cut -f2)
        log "操作系统: $OS_VERSION"
    fi
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        error "Python 3 未安装"
    fi
    
    PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
    log "Python版本: $PYTHON_VERSION"
    
    # 检查PostgreSQL
    if ! command -v psql &> /dev/null; then
        error "PostgreSQL 未安装"
    fi
    
    PG_VERSION=$(psql --version | cut -d' ' -f3)
    log "PostgreSQL版本: $PG_VERSION"
    
    # 检查可用内存
    MEMORY_GB=$(free -g | awk 'NR==2{print $2}')
    if [[ $MEMORY_GB -lt 8 ]]; then
        warning "可用内存少于8GB，可能影响性能"
    fi
    log "可用内存: ${MEMORY_GB}GB"
    
    # 检查磁盘空间
    DISK_SPACE=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    if [[ $DISK_SPACE -lt 50 ]]; then
        warning "可用磁盘空间少于50GB"
    fi
    log "可用磁盘空间: ${DISK_SPACE}GB"
    
    success "系统要求检查完成"
}

# 创建用户和目录
setup_user_and_directories() {
    log "设置用户和目录..."
    
    # 创建部署用户
    if ! id "$DEPLOY_USER" &>/dev/null; then
        useradd -r -m -s /bin/bash "$DEPLOY_USER"
        success "创建用户: $DEPLOY_USER"
    else
        log "用户已存在: $DEPLOY_USER"
    fi
    
    # 创建目录
    mkdir -p "$DEPLOY_DIR"
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "/data/connect/input"
    mkdir -p "/data/connect/temp"
    
    # 设置权限
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_DIR"
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "/data/connect"
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$LOG_DIR"
    
    success "用户和目录设置完成"
}

# 安装系统依赖
install_system_dependencies() {
    log "安装系统依赖..."
    
    # 更新包列表
    apt update
    
    # 安装基础包
    apt install -y \
        python3-pip \
        python3-venv \
        python3-dev \
        build-essential \
        libpq-dev \
        nginx \
        supervisor \
        curl \
        wget \
        unzip \
        git \
        htop \
        vim
    
    success "系统依赖安装完成"
}

# 部署应用代码
deploy_application() {
    log "部署应用代码..."
    
    # 备份现有部署（如果存在）
    if [[ -d "$DEPLOY_DIR/src" ]]; then
        BACKUP_NAME="connect_backup_$(date +%Y%m%d_%H%M%S)"
        mv "$DEPLOY_DIR" "$BACKUP_DIR/$BACKUP_NAME"
        log "现有部署已备份到: $BACKUP_DIR/$BACKUP_NAME"
    fi
    
    # 创建部署目录
    mkdir -p "$DEPLOY_DIR"
    cd "$DEPLOY_DIR"
    
    # 复制应用文件（假设从当前目录复制）
    if [[ -d "/tmp/connect_deploy" ]]; then
        cp -r /tmp/connect_deploy/* "$DEPLOY_DIR/"
    else
        error "部署源码目录不存在: /tmp/connect_deploy"
    fi
    
    # 设置权限
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$DEPLOY_DIR"
    
    success "应用代码部署完成"
}

# 设置Python虚拟环境
setup_python_environment() {
    log "设置Python虚拟环境..."
    
    cd "$DEPLOY_DIR"
    
    # 创建虚拟环境
    sudo -u "$DEPLOY_USER" python3 -m venv "$VENV_DIR"
    
    # 激活虚拟环境并安装依赖
    sudo -u "$DEPLOY_USER" bash -c "
        source '$VENV_DIR/bin/activate'
        pip install --upgrade pip
        pip install -r requirements.txt
    "
    
    success "Python环境设置完成"
}

# 配置数据库
setup_database() {
    log "配置数据库..."
    
    # 检查数据库连接
    if sudo -u postgres psql -c "SELECT version();" > /dev/null 2>&1; then
        log "PostgreSQL服务正常"
    else
        error "无法连接到PostgreSQL"
    fi
    
    # 创建数据库用户和数据库（如果不存在）
    sudo -u postgres psql -c "
        DO \$\$
        BEGIN
            IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'to2') THEN
                CREATE USER to2 WITH PASSWORD 'TO2';
            END IF;
        END
        \$\$;
    " || warning "数据库用户可能已存在"
    
    sudo -u postgres psql -c "
        SELECT 'CREATE DATABASE connect OWNER to2'
        WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'connect')\\gexec
    " || warning "数据库可能已存在"
    
    # 创建模式
    PGPASSWORD=TO2 psql -h localhost -U to2 -d connect -c "
        CREATE SCHEMA IF NOT EXISTS ep_to2;
        CREATE SCHEMA IF NOT EXISTS cdr_to2;
        CREATE SCHEMA IF NOT EXISTS cdr_vdf;
        CREATE SCHEMA IF NOT EXISTS cdr_tdg;
        CREATE SCHEMA IF NOT EXISTS nlg_to2;
        CREATE SCHEMA IF NOT EXISTS kpi_to2;
        CREATE SCHEMA IF NOT EXISTS score_to2;
        CREATE SCHEMA IF NOT EXISTS cfg_to2;
    " || warning "数据库模式可能已存在"
    
    success "数据库配置完成"
}

# 配置Nginx
setup_nginx() {
    log "配置Nginx..."
    
    # 创建Nginx配置
    cat > /etc/nginx/sites-available/connect << 'EOF'
upstream connect_api {
    server 127.0.0.1:8000;
    keepalive 32;
}

server {
    listen 80;
    server_name _;
    
    client_max_body_size 500M;
    client_body_timeout 300s;
    
    location /api/ {
        proxy_pass http://connect_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    location /health {
        proxy_pass http://connect_api/api/health;
        access_log off;
    }
    
    location / {
        return 301 /api/docs;
    }
}
EOF
    
    # 启用站点
    ln -sf /etc/nginx/sites-available/connect /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    # 测试配置
    nginx -t || error "Nginx配置测试失败"
    
    # 重启Nginx
    systemctl restart nginx
    systemctl enable nginx
    
    success "Nginx配置完成"
}

# 配置系统服务
setup_systemd_service() {
    log "配置系统服务..."
    
    cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=Connect Telecommunications Data Import API
After=network.target postgresql.service
Requires=postgresql.service

[Service]
Type=exec
User=$DEPLOY_USER
Group=$DEPLOY_USER
WorkingDirectory=$DEPLOY_DIR
Environment=PATH=$VENV_DIR/bin
ExecStart=$VENV_DIR/bin/python -m src.api.import_api
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$DEPLOY_DIR $LOG_DIR /data/connect

LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
    
    # 重载systemd配置
    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
    
    success "系统服务配置完成"
}

# 启动服务
start_services() {
    log "启动服务..."
    
    # 启动Connect API
    systemctl start $SERVICE_NAME
    
    # 等待服务启动
    sleep 5
    
    # 检查服务状态
    if systemctl is-active --quiet $SERVICE_NAME; then
        success "Connect API服务启动成功"
    else
        error "Connect API服务启动失败"
    fi
    
    # 检查API健康状态
    if curl -f http://localhost:8000/api/health > /dev/null 2>&1; then
        success "API健康检查通过"
    else
        warning "API健康检查失败，请检查日志"
    fi
}

# 设置监控和日志
setup_monitoring() {
    log "设置监控和日志..."
    
    # 创建健康检查脚本
    cat > /opt/connect/scripts/health_check.sh << 'EOF'
#!/bin/bash
LOG_FILE="/var/log/connect/health_check.log"
API_URL="http://localhost:8000/api/health"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

if curl -s -f "$API_URL" > /dev/null; then
    log "Health check: OK"
    exit 0
else
    log "Health check: FAILED"
    exit 1
fi
EOF
    
    chmod +x /opt/connect/scripts/health_check.sh
    chown "$DEPLOY_USER:$DEPLOY_USER" /opt/connect/scripts/health_check.sh
    
    # 添加cron任务
    echo "*/5 * * * * $DEPLOY_USER /opt/connect/scripts/health_check.sh" >> /etc/crontab
    
    success "监控和日志设置完成"
}

# 主部署函数
main() {
    log "开始Connect生产环境部署..."
    
    check_root
    check_system_requirements
    setup_user_and_directories
    install_system_dependencies
    deploy_application
    setup_python_environment
    setup_database
    setup_nginx
    setup_systemd_service
    start_services
    setup_monitoring
    
    success "部署完成！"
    log "API文档地址: http://$(hostname -I | awk '{print $1}')/api/docs"
    log "健康检查地址: http://$(hostname -I | awk '{print $1}')/health"
    log "服务状态: systemctl status $SERVICE_NAME"
    log "服务日志: journalctl -u $SERVICE_NAME -f"
}

# 显示帮助信息
show_help() {
    echo "Connect 生产环境部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --version  显示版本信息"
    echo ""
    echo "示例:"
    echo "  $0             执行完整部署"
    echo ""
}

# 处理命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -v|--version)
        echo "Connect部署脚本 v2.0.0"
        exit 0
        ;;
    "")
        main
        ;;
    *)
        error "未知选项: $1"
        ;;
esac
