"""Database security tools module.

This module provides database security related tools and functions, mainly used to prevent SQL injection attacks
and validate identifier security in dynamic SQL construction.

Main features:
- SQL injection protection
- Identifier validation (table names, schema names, etc.)
- Parameterized query security checks
"""

import re
from typing import Any, Dict, Optional, Tuple

from ..exceptions import SecurityError, ValidationError
from .validators import InputValidator


class SQLInjectionGuard:
    """SQL injection protection class.

    Provides SQL injection protection functionality, mainly used to validate
    parts of dynamic SQL construction that cannot use parameterized queries
    (such as table names, schema names and other identifiers).

    Note: For query values, asyncpg's parameterized query functionality should be prioritized
    rather than relying on string cleaning methods.
    """

    # Dangerous SQL keyword patterns
    _DANGEROUS_PATTERNS = [
        r"\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b",
        r"(--|/\*|\*/|;)",
        r"\bOR\b.*\b=\b",
        r"\bAND\b.*\b=\b",
        r"\bUNION\b.*\bSELECT\b",
        r"\bEXEC\b.*\(",
        r"\bEVAL\b.*\(",
        r"\bCAST\b.*\bAS\b",
        r"\bCONVERT\b.*\(",
        r"\bCHAR\b.*\(",
        r"\bASCII\b.*\(",
        r"\bSUBSTRING\b.*\(",
        r"\bCONCAT\b.*\(",
    ]

    @staticmethod
    def validate_identifier(
        identifier: str, identifier_type: str = "identifier"
    ) -> str:
        """Validate SQL identifiers (table names, schema names, column names, etc.).

        This method is specifically used to validate identifiers in dynamic SQL
        that cannot use parameterized queries and therefore require special validation.

        Args:
            identifier: The identifier to validate
            identifier_type: Identifier type for error messages

        Returns:
            str: Validated identifier (normalized to lowercase)

        Raises:
            SecurityError: If identifier contains dangerous patterns
            ValidationError: If identifier format is incorrect

        Examples:
            >>> SQLInjectionGuard.validate_identifier('users')
            'users'
            >>> SQLInjectionGuard.validate_identifier('user_profiles')
            'user_profiles'
        """
        if not isinstance(identifier, str):
            raise ValidationError(f"{identifier_type} must be a string")

        if not identifier.strip():
            raise ValidationError(f"{identifier_type} cannot be empty")

        # Check for dangerous patterns
        for pattern in SQLInjectionGuard._DANGEROUS_PATTERNS:
            if re.search(pattern, identifier, re.IGNORECASE):
                raise SecurityError(
                    f"Potentially dangerous SQL pattern detected in {identifier_type}: {identifier}"
                )

        # Use existing InputValidator for basic validation
        is_valid, error_msg = InputValidator.validate_identifier(
            identifier, identifier_type
        )
        if not is_valid:
            raise ValidationError(error_msg)

        return InputValidator.normalize_name(identifier)

    @staticmethod
    def validate_schema_name(schema_name: str) -> str:
        """Validate schema name.

        Args:
            schema_name: The schema name to validate

        Returns:
            str: Validated schema name (normalized to lowercase)

        Raises:
            SecurityError: If schema name contains dangerous patterns
            ValidationError: If schema name format is incorrect
        """
        return SQLInjectionGuard.validate_identifier(schema_name, "schema name")

    @staticmethod
    def validate_table_name(table_name: str) -> str:
        """Validate table name.

        Args:
            table_name: The table name to validate

        Returns:
            str: Validated table name (normalized to lowercase)

        Raises:
            SecurityError: If table name contains dangerous patterns
            ValidationError: If table name format is incorrect
        """
        return SQLInjectionGuard.validate_identifier(table_name, "table name")

    @staticmethod
    def validate_column_name(column_name: str) -> str:
        """Validate column name.

        Args:
            column_name: The column name to validate

        Returns:
            str: Validated column name (normalized to lowercase)

        Raises:
            SecurityError: If column name contains dangerous patterns
            ValidationError: If column name format is incorrect
        """
        return SQLInjectionGuard.validate_identifier(column_name, "column name")

    @staticmethod
    def validate_query_template(template: str) -> str:
        """Validate query template security.

        Check if the query template uses unsafe string formatting methods,
        ensure parameterized queries are used.

        Args:
            template: SQL query template

        Returns:
            str: Validated query template

        Raises:
            SecurityError: If template uses unsafe formatting methods

        Examples:
            >>> SQLInjectionGuard.validate_query_template('SELECT * FROM users WHERE id = $1')
            'SELECT * FROM users WHERE id = $1'
        """
        if not isinstance(template, str):
            raise ValidationError("Query template must be a string")

        # Check for unsafe string formatting
        unsafe_patterns = [
            r"%[sd]",  # % formatting like %s, %d
            r"\{[^}]*\}",  # .format() formatting like {name}
            r'\+\s*["\']',  # string concatenation with quotes
            r'["\']\s*\+',  # string concatenation with quotes (reverse)
        ]

        for pattern in unsafe_patterns:
            if re.search(pattern, template):
                raise SecurityError(
                    "Use parameterized queries ($1, $2, etc.) instead of string formatting. "
                    f"Unsafe pattern detected: {pattern}"
                )

        return template

    @staticmethod
    def build_safe_identifier_query(template: str, identifiers: Dict[str, str]) -> str:
        """Build query with safe identifiers.

        This method is used to build queries containing dynamic identifiers
        (such as table names, schema names) that cannot use parameterized queries
        and need to be directly inserted into SQL.

        Args:
            template: Query template containing identifier placeholders
            identifiers: Identifier dictionary, keys are placeholder names, values are identifier values

        Returns:
            str: Built safe query

        Raises:
            SecurityError: If identifiers contain dangerous patterns
            ValidationError: If identifier format is incorrect

        Examples:
            >>> template = 'CREATE TABLE {schema}.{table} (id SERIAL PRIMARY KEY)'
            >>> identifiers = {'schema': 'public', 'table': 'users'}
            >>> SQLInjectionGuard.build_safe_identifier_query(template, identifiers)
            'CREATE TABLE public.users (id SERIAL PRIMARY KEY)'
        """
        if not isinstance(template, str):
            raise ValidationError("Query template must be a string")

        if not isinstance(identifiers, dict):
            raise ValidationError("Identifiers must be a dictionary")

        # Validate all identifiers
        validated_identifiers = {}
        for key, value in identifiers.items():
            validated_identifiers[key] = SQLInjectionGuard.validate_identifier(
                value, f"identifier '{key}'"
            )

        try:
            # Format query with validated identifiers
            return template.format(**validated_identifiers)
        except KeyError as e:
            raise ValidationError(f"Missing identifier in template: {e}")
        except Exception as e:
            raise SecurityError(f"Failed to build safe query: {e}")

    @staticmethod
    def sanitize_like_pattern(pattern: str) -> str:
        r"""Clean special characters in LIKE patterns.

        Escape special characters in LIKE queries to prevent wildcard injection.

        Args:
            pattern: LIKE pattern string

        Returns:
            str: Cleaned pattern string

        Examples:
            >>> SQLInjectionGuard.sanitize_like_pattern('user%_test')
            'user\\%\\_test'
        """
        if not isinstance(pattern, str):
            raise ValidationError("LIKE pattern must be a string")

        # Escape LIKE special characters
        pattern = pattern.replace("\\", "\\\\\\\\")
        pattern = pattern.replace("%", "\\%")
        pattern = pattern.replace("_", "\\_")

        return pattern

    @staticmethod
    def validate_limit_offset(
        limit: Optional[int] = None, offset: Optional[int] = None
    ) -> Tuple[Optional[int], Optional[int]]:
        """Validate LIMIT and OFFSET parameters.

        Args:
            limit: Limit count
            offset: Offset value

        Returns:
            Tuple[Optional[int], Optional[int]]: Validated limit and offset

        Raises:
            ValidationError: If parameters are invalid
        """
        if limit is not None:
            if not isinstance(limit, int) or limit < 0:
                raise ValidationError("LIMIT must be a non-negative integer")
            if limit > 10000:  # Prevent overly large queries
                raise ValidationError("LIMIT cannot exceed 10000")

        if offset is not None:
            if not isinstance(offset, int) or offset < 0:
                raise ValidationError("OFFSET must be a non-negative integer")

        return limit, offset
