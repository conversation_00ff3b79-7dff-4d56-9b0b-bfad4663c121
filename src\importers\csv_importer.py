"""CSV data importer.

This module provides CSV import functionality for various data formats.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import csv
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from .base import BaseImporter, ImportError, ImportResult

# Configure logging
logger = logging.getLogger(__name__)


class CSVImporter(BaseImporter):
    """CSV data importer."""

    def __init__(
        self,
        source_path: Union[str, Path],
        delimiter: str = ",",
        encoding: str = "utf-8",
        **kwargs,
    ):
        """Initialize CSV importer.

        Args:
            source_path: Path to the CSV file
            delimiter: CSV delimiter character
            encoding: File encoding
            **kwargs: Additional configuration options
        """
        super().__init__(source_path, **kwargs)
        self.delimiter = delimiter
        self.encoding = encoding

        # Validate CSV file extension
        if self.source_path.suffix.lower() not in [".csv", ".txt"]:
            logger.warning(
                f"File extension {self.source_path.suffix} may not be a CSV file"
            )

    async def import_data(self, **kwargs) -> ImportResult:
        """Import data from CSV file.

        Args:
            **kwargs: Additional import options
                - header: Row number to use as column names (default: 0)
                - skiprows: Rows to skip at the beginning
                - nrows: Number of rows to read
                - usecols: Columns to use
                - dtype: Data type for columns

        Returns:
            ImportResult: Result of the import operation

        Raises:
            ImportError: If import fails
        """
        try:
            self.validate_source()

            header = kwargs.get("header", 0)
            skiprows = kwargs.get("skiprows", None)
            nrows = kwargs.get("nrows", None)
            usecols = kwargs.get("usecols", None)
            dtype = kwargs.get("dtype", None)

            # Read CSV file
            df = pd.read_csv(
                self.source_path,
                sep=self.delimiter,
                encoding=self.encoding,
                header=header,
                skiprows=skiprows,
                nrows=nrows,
                usecols=usecols,
                dtype=dtype,
            )

            records_imported = len(df)
            file_size = self.source_path.stat().st_size

            logger.info(
                f"Successfully imported {records_imported} records from {self.source_path}"
            )

            return ImportResult(
                success=True,
                source_path=self.source_path,
                records_imported=records_imported,
                file_size_bytes=file_size,
                metadata={
                    "delimiter": self.delimiter,
                    "encoding": self.encoding,
                    "columns": list(df.columns),
                    "shape": df.shape,
                    "data_types": df.dtypes.to_dict(),
                },
            )

        except Exception as e:
            error_msg = f"Failed to import CSV: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ImportResult(
                success=False, source_path=self.source_path, error_message=error_msg
            )

    def preview_data(self, num_rows: int = 5) -> Optional[pd.DataFrame]:
        """Preview first few rows of CSV data.

        Args:
            num_rows: Number of rows to preview

        Returns:
            DataFrame: Preview of the data
        """
        try:
            self.validate_source()
            return pd.read_csv(
                self.source_path,
                sep=self.delimiter,
                encoding=self.encoding,
                nrows=num_rows,
            )
        except Exception as e:
            logger.error(f"Failed to preview CSV data: {e}")
            return None

    def get_column_info(self) -> Dict[str, Any]:
        """Get information about CSV columns.

        Returns:
            Dict: Column information
        """
        try:
            self.validate_source()

            # Read just the header
            with open(self.source_path, "r", encoding=self.encoding) as f:
                reader = csv.reader(f, delimiter=self.delimiter)
                header = next(reader)

            # Get a sample to infer types
            sample_df = pd.read_csv(
                self.source_path, sep=self.delimiter, encoding=self.encoding, nrows=100
            )

            return {
                "columns": header,
                "num_columns": len(header),
                "inferred_types": sample_df.dtypes.to_dict(),
                "sample_values": {
                    col: sample_df[col].dropna().head(3).tolist()
                    for col in sample_df.columns
                },
            }

        except Exception as e:
            logger.error(f"Failed to get column info: {e}")
            return {}
