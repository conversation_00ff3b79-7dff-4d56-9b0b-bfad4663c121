"""Core query builder classes for dynamic SQL construction.

This module provides the main QueryBuilder class and specific query types
for building complex SQL queries programmatically.
"""

import re
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union

from loguru import logger

from ..exceptions import QueryError, ValidationError
from .aggregations import Aggregation
from .conditions import Condition
from .dialects import Dialect, PostgreSQLDialect
from .joins import Join
from .validators import CompositeValidator, SQLInjectionValidator


class QueryType(Enum):
    """Enumeration of supported query types."""

    SELECT = "SELECT"
    INSERT = "INSERT"
    UPDATE = "UPDATE"
    DELETE = "DELETE"
    CREATE = "CREATE"
    ALTER = "ALTER"
    DROP = "DROP"


@dataclass
class QueryContext:
    """Context information for query building."""

    dialect: Dialect = field(default_factory=PostgreSQLDialect)
    schema: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    aliases: Dict[str, str] = field(default_factory=dict)
    table_prefix: str = ""
    validate_security: bool = True
    optimize: bool = True


class BaseQuery(ABC):
    """Abstract base class for all query types."""

    def __init__(self, context: Optional[QueryContext] = None):
        """Initialize base query.

        Args:
            context: Query context with dialect and settings
        """
        self.context = context or QueryContext()
        self.validator = CompositeValidator([])
        self.security_validator = SQLInjectionValidator()
        self._parameters = {}
        self._parameter_counter = 0

    @abstractmethod
    def build(self) -> Tuple[str, Dict[str, Any]]:
        """Build the SQL query and return query string with parameters.

        Returns:
            Tuple of (query_string, parameters)
        """
        pass

    def _add_parameter(self, value: Any, name: Optional[str] = None) -> str:
        """Add a parameter and return the placeholder.

        Args:
            value: Parameter value
            name: Optional parameter name

        Returns:
            Parameter placeholder
        """
        if name is None:
            self._parameter_counter += 1
            name = f"param_{self._parameter_counter}"

        placeholder = self.context.dialect.get_parameter_placeholder(name)
        self._parameters[name] = value
        return placeholder

    def _validate_identifier(self, identifier: str) -> str:
        """Validate and escape SQL identifier.

        Args:
            identifier: SQL identifier to validate

        Returns:
            Validated and escaped identifier
        """
        if not re.match(r"^[a-zA-Z_][a-zA-Z0-9_]*$", identifier):
            raise ValidationError(f"Invalid SQL identifier: {identifier}")

        return self.context.dialect.escape_identifier(identifier)

    def _build_table_name(self, table: str, schema: Optional[str] = None) -> str:
        """Build full table name with schema and prefix.

        Args:
            table: Table name
            schema: Optional schema name

        Returns:
            Full table name
        """
        schema = schema or self.context.schema
        table_name = f"{self.context.table_prefix}{table}"

        if schema:
            return f"{self._validate_identifier(schema)}.{self._validate_identifier(table_name)}"
        else:
            return self._validate_identifier(table_name)


class SelectQuery(BaseQuery):
    """Builder for SELECT queries."""

    def __init__(self, context: Optional[QueryContext] = None):
        """Initialize SELECT query builder."""
        super().__init__(context)
        self._select_fields: List[str] = []
        self._from_table: Optional[str] = None
        self._from_schema: Optional[str] = None
        self._joins: List[Join] = []
        self._where_conditions: List[Condition] = []
        self._group_by: List[str] = []
        self._having_conditions: List[Condition] = []
        self._order_by: List[Tuple[str, str]] = []  # (field, direction)
        self._limit: Optional[int] = None
        self._offset: Optional[int] = None
        self._distinct: bool = False
        self._aggregations: List[Aggregation] = []

    def select(self, *fields: str) -> "SelectQuery":
        """Add fields to SELECT clause.

        Args:
            *fields: Field names to select

        Returns:
            Self for method chaining
        """
        self._select_fields.extend(fields)
        return self

    def select_all(self) -> "SelectQuery":
        """Select all fields (*).

        Returns:
            Self for method chaining
        """
        self._select_fields = ["*"]
        return self

    def distinct(self) -> "SelectQuery":
        """Add DISTINCT to query.

        Returns:
            Self for method chaining
        """
        self._distinct = True
        return self

    def from_table(
        self, table: str, schema: Optional[str] = None, alias: Optional[str] = None
    ) -> "SelectQuery":
        """Set FROM table.

        Args:
            table: Table name
            schema: Optional schema name
            alias: Optional table alias

        Returns:
            Self for method chaining
        """
        self._from_table = table
        self._from_schema = schema

        if alias:
            self.context.aliases[table] = alias

        return self

    def join(self, join: Join) -> "SelectQuery":
        """Add JOIN clause.

        Args:
            join: Join object

        Returns:
            Self for method chaining
        """
        self._joins.append(join)
        return self

    def where(self, condition: Condition) -> "SelectQuery":
        """Add WHERE condition.

        Args:
            condition: Condition object

        Returns:
            Self for method chaining
        """
        self._where_conditions.append(condition)
        return self

    def group_by(self, *fields: str) -> "SelectQuery":
        """Add GROUP BY fields.

        Args:
            *fields: Field names to group by

        Returns:
            Self for method chaining
        """
        self._group_by.extend(fields)
        return self

    def having(self, condition: Condition) -> "SelectQuery":
        """Add HAVING condition.

        Args:
            condition: Condition object

        Returns:
            Self for method chaining
        """
        self._having_conditions.append(condition)
        return self

    def order_by(self, field: str, direction: str = "ASC") -> "SelectQuery":
        """Add ORDER BY clause.

        Args:
            field: Field name to order by
            direction: Sort direction (ASC or DESC)

        Returns:
            Self for method chaining
        """
        if direction.upper() not in ["ASC", "DESC"]:
            raise ValidationError(f"Invalid sort direction: {direction}")

        self._order_by.append((field, direction.upper()))
        return self

    def limit(self, count: int) -> "SelectQuery":
        """Add LIMIT clause.

        Args:
            count: Maximum number of rows to return

        Returns:
            Self for method chaining
        """
        if count < 0:
            raise ValidationError("LIMIT count must be non-negative")

        self._limit = count
        return self

    def offset(self, count: int) -> "SelectQuery":
        """Add OFFSET clause.

        Args:
            count: Number of rows to skip

        Returns:
            Self for method chaining
        """
        if count < 0:
            raise ValidationError("OFFSET count must be non-negative")

        self._offset = count
        return self

    def aggregate(self, aggregation: Aggregation) -> "SelectQuery":
        """Add aggregation function.

        Args:
            aggregation: Aggregation object

        Returns:
            Self for method chaining
        """
        self._aggregations.append(aggregation)
        return self

    def build(self) -> Tuple[str, Dict[str, Any]]:
        """Build the SELECT query.

        Returns:
            Tuple of (query_string, parameters)
        """
        try:
            if not self._from_table:
                raise QueryError("FROM table is required for SELECT query")

            # Build SELECT clause
            if self._select_fields:
                select_clause = ", ".join(self._select_fields)
            else:
                select_clause = "*"

            # Add aggregations to select
            if self._aggregations:
                agg_fields = [
                    agg.build(self.context.dialect) for agg in self._aggregations
                ]
                if self._select_fields and self._select_fields != ["*"]:
                    select_clause += ", " + ", ".join(agg_fields)
                else:
                    select_clause = ", ".join(agg_fields)

            if self._distinct:
                select_clause = f"DISTINCT {select_clause}"

            query_parts = [f"SELECT {select_clause}"]

            # Build FROM clause
            from_table = self._build_table_name(self._from_table, self._from_schema)
            if self._from_table in self.context.aliases:
                from_table += f" AS {self._validate_identifier(self.context.aliases[self._from_table])}"

            query_parts.append(f"FROM {from_table}")

            # Build JOIN clauses
            for join in self._joins:
                join_sql = join.build(self.context.dialect)
                query_parts.append(join_sql)

            # Build WHERE clause
            if self._where_conditions:
                where_parts = []
                for condition in self._where_conditions:
                    condition_sql, condition_params = condition.build(
                        self.context.dialect
                    )
                    where_parts.append(condition_sql)
                    self._parameters.update(condition_params)

                query_parts.append(f"WHERE {' AND '.join(where_parts)}")

            # Build GROUP BY clause
            if self._group_by:
                group_fields = [
                    self._validate_identifier(field) for field in self._group_by
                ]
                query_parts.append(f"GROUP BY {', '.join(group_fields)}")

            # Build HAVING clause
            if self._having_conditions:
                having_parts = []
                for condition in self._having_conditions:
                    condition_sql, condition_params = condition.build(
                        self.context.dialect
                    )
                    having_parts.append(condition_sql)
                    self._parameters.update(condition_params)

                query_parts.append(f"HAVING {' AND '.join(having_parts)}")

            # Build ORDER BY clause
            if self._order_by:
                order_parts = []
                for field, direction in self._order_by:
                    order_parts.append(
                        f"{self._validate_identifier(field)} {direction}"
                    )
                query_parts.append(f"ORDER BY {', '.join(order_parts)}")

            # Build LIMIT clause
            if self._limit is not None:
                query_parts.append(f"LIMIT {self._limit}")

            # Build OFFSET clause
            if self._offset is not None:
                query_parts.append(f"OFFSET {self._offset}")

            query = " ".join(query_parts)

            # Validate query if enabled
            if self.context.validate_security:
                self.security_validator.validate(query)

            logger.debug(f"Built SELECT query: {query}")
            return query, self._parameters

        except Exception as e:
            logger.error(f"Failed to build SELECT query: {e}")
            raise QueryError(f"Failed to build SELECT query: {e}")


class InsertQuery(BaseQuery):
    """Builder for INSERT queries."""

    def __init__(self, context: Optional[QueryContext] = None):
        """Initialize INSERT query builder."""
        super().__init__(context)
        self._table: Optional[str] = None
        self._schema: Optional[str] = None
        self._columns: List[str] = []
        self._values: List[Dict[str, Any]] = []
        self._on_conflict: Optional[str] = None
        self._returning: List[str] = []

    def into(self, table: str, schema: Optional[str] = None) -> "InsertQuery":
        """Set target table for INSERT.

        Args:
            table: Table name
            schema: Optional schema name

        Returns:
            Self for method chaining
        """
        self._table = table
        self._schema = schema
        return self

    def columns(self, *columns: str) -> "InsertQuery":
        """Set columns for INSERT.

        Args:
            *columns: Column names

        Returns:
            Self for method chaining
        """
        self._columns = list(columns)
        return self

    def values(self, **values: Any) -> "InsertQuery":
        """Add values for INSERT.

        Args:
            **values: Column-value pairs

        Returns:
            Self for method chaining
        """
        self._values.append(values)
        return self

    def values_list(self, values_list: List[Dict[str, Any]]) -> "InsertQuery":
        """Add multiple rows of values.

        Args:
            values_list: List of column-value dictionaries

        Returns:
            Self for method chaining
        """
        self._values.extend(values_list)
        return self

    def on_conflict(self, action: str) -> "InsertQuery":
        """Set ON CONFLICT action.

        Args:
            action: Conflict resolution action

        Returns:
            Self for method chaining
        """
        self._on_conflict = action
        return self

    def returning(self, *fields: str) -> "InsertQuery":
        """Add RETURNING clause.

        Args:
            *fields: Fields to return

        Returns:
            Self for method chaining
        """
        self._returning.extend(fields)
        return self

    def build(self) -> Tuple[str, Dict[str, Any]]:
        """Build the INSERT query.

        Returns:
            Tuple of (query_string, parameters)
        """
        try:
            if not self._table:
                raise QueryError("Table is required for INSERT query")

            if not self._values:
                raise QueryError("Values are required for INSERT query")

            # Determine columns
            if not self._columns:
                # Use columns from first value set
                self._columns = list(self._values[0].keys())

            # Validate all value sets have required columns
            for i, value_set in enumerate(self._values):
                missing_cols = set(self._columns) - set(value_set.keys())
                if missing_cols:
                    raise QueryError(
                        f"Missing columns in value set {i}: {missing_cols}"
                    )

            # Build query
            table_name = self._build_table_name(self._table, self._schema)
            columns_str = ", ".join(
                [self._validate_identifier(col) for col in self._columns]
            )

            query_parts = [f"INSERT INTO {table_name} ({columns_str})"]

            # Build VALUES clause
            value_rows = []
            for value_set in self._values:
                placeholders = []
                for col in self._columns:
                    placeholder = self._add_parameter(value_set[col])
                    placeholders.append(placeholder)
                value_rows.append(f"({', '.join(placeholders)})")

            query_parts.append(f"VALUES {', '.join(value_rows)}")

            # Add ON CONFLICT clause
            if self._on_conflict:
                query_parts.append(f"ON CONFLICT {self._on_conflict}")

            # Add RETURNING clause
            if self._returning:
                returning_fields = [
                    self._validate_identifier(field) for field in self._returning
                ]
                query_parts.append(f"RETURNING {', '.join(returning_fields)}")

            query = " ".join(query_parts)

            # Validate query if enabled
            if self.context.validate_security:
                self.security_validator.validate(query)

            logger.debug(f"Built INSERT query: {query}")
            return query, self._parameters

        except Exception as e:
            logger.error(f"Failed to build INSERT query: {e}")
            raise QueryError(f"Failed to build INSERT query: {e}")


class UpdateQuery(BaseQuery):
    """Builder for UPDATE queries."""

    def __init__(self, context: Optional[QueryContext] = None):
        """Initialize UPDATE query builder."""
        super().__init__(context)
        self._table: Optional[str] = None
        self._schema: Optional[str] = None
        self._set_values: Dict[str, Any] = {}
        self._where_conditions: List[Condition] = []
        self._returning: List[str] = []

    def table(self, table: str, schema: Optional[str] = None) -> "UpdateQuery":
        """Set target table for UPDATE.

        Args:
            table: Table name
            schema: Optional schema name

        Returns:
            Self for method chaining
        """
        self._table = table
        self._schema = schema
        return self

    def set(self, **values: Any) -> "UpdateQuery":
        """Set column values for UPDATE.

        Args:
            **values: Column-value pairs

        Returns:
            Self for method chaining
        """
        self._set_values.update(values)
        return self

    def where(self, condition: Condition) -> "UpdateQuery":
        """Add WHERE condition.

        Args:
            condition: Condition object

        Returns:
            Self for method chaining
        """
        self._where_conditions.append(condition)
        return self

    def returning(self, *fields: str) -> "UpdateQuery":
        """Add RETURNING clause.

        Args:
            *fields: Fields to return

        Returns:
            Self for method chaining
        """
        self._returning.extend(fields)
        return self

    def build(self) -> Tuple[str, Dict[str, Any]]:
        """Build the UPDATE query.

        Returns:
            Tuple of (query_string, parameters)
        """
        try:
            if not self._table:
                raise QueryError("Table is required for UPDATE query")

            if not self._set_values:
                raise QueryError("SET values are required for UPDATE query")

            # Build query
            table_name = self._build_table_name(self._table, self._schema)
            query_parts = [f"UPDATE {table_name}"]

            # Build SET clause
            set_parts = []
            for column, value in self._set_values.items():
                placeholder = self._add_parameter(value)
                set_parts.append(f"{self._validate_identifier(column)} = {placeholder}")

            query_parts.append(f"SET {', '.join(set_parts)}")

            # Build WHERE clause
            if self._where_conditions:
                where_parts = []
                for condition in self._where_conditions:
                    condition_sql, condition_params = condition.build(
                        self.context.dialect
                    )
                    where_parts.append(condition_sql)
                    self._parameters.update(condition_params)

                query_parts.append(f"WHERE {' AND '.join(where_parts)}")
            else:
                logger.warning(
                    "UPDATE query without WHERE clause - this will update all rows!"
                )

            # Add RETURNING clause
            if self._returning:
                returning_fields = [
                    self._validate_identifier(field) for field in self._returning
                ]
                query_parts.append(f"RETURNING {', '.join(returning_fields)}")

            query = " ".join(query_parts)

            # Validate query if enabled
            if self.context.validate_security:
                self.security_validator.validate(query)

            logger.debug(f"Built UPDATE query: {query}")
            return query, self._parameters

        except Exception as e:
            logger.error(f"Failed to build UPDATE query: {e}")
            raise QueryError(f"Failed to build UPDATE query: {e}")


class DeleteQuery(BaseQuery):
    """Builder for DELETE queries."""

    def __init__(self, context: Optional[QueryContext] = None):
        """Initialize DELETE query builder."""
        super().__init__(context)
        self._table: Optional[str] = None
        self._schema: Optional[str] = None
        self._where_conditions: List[Condition] = []
        self._returning: List[str] = []

    def from_table(self, table: str, schema: Optional[str] = None) -> "DeleteQuery":
        """Set target table for DELETE.

        Args:
            table: Table name
            schema: Optional schema name

        Returns:
            Self for method chaining
        """
        self._table = table
        self._schema = schema
        return self

    def where(self, condition: Condition) -> "DeleteQuery":
        """Add WHERE condition.

        Args:
            condition: Condition object

        Returns:
            Self for method chaining
        """
        self._where_conditions.append(condition)
        return self

    def returning(self, *fields: str) -> "DeleteQuery":
        """Add RETURNING clause.

        Args:
            *fields: Fields to return

        Returns:
            Self for method chaining
        """
        self._returning.extend(fields)
        return self

    def build(self) -> Tuple[str, Dict[str, Any]]:
        """Build the DELETE query.

        Returns:
            Tuple of (query_string, parameters)
        """
        try:
            if not self._table:
                raise QueryError("Table is required for DELETE query")

            # Build query
            table_name = self._build_table_name(self._table, self._schema)
            query_parts = [f"DELETE FROM {table_name}"]

            # Build WHERE clause
            if self._where_conditions:
                where_parts = []
                for condition in self._where_conditions:
                    condition_sql, condition_params = condition.build(
                        self.context.dialect
                    )
                    where_parts.append(condition_sql)
                    self._parameters.update(condition_params)

                query_parts.append(f"WHERE {' AND '.join(where_parts)}")
            else:
                logger.warning(
                    "DELETE query without WHERE clause - this will delete all rows!"
                )

            # Add RETURNING clause
            if self._returning:
                returning_fields = [
                    self._validate_identifier(field) for field in self._returning
                ]
                query_parts.append(f"RETURNING {', '.join(returning_fields)}")

            query = " ".join(query_parts)

            # Validate query if enabled
            if self.context.validate_security:
                self.security_validator.validate(query)

            logger.debug(f"Built DELETE query: {query}")
            return query, self._parameters

        except Exception as e:
            logger.error(f"Failed to build DELETE query: {e}")
            raise QueryError(f"Failed to build DELETE query: {e}")


class QueryBuilder:
    """Main query builder factory class."""

    def __init__(self, context: Optional[QueryContext] = None):
        """Initialize query builder.

        Args:
            context: Query context with dialect and settings
        """
        self.context = context or QueryContext()

    def select(self, *fields: str) -> SelectQuery:
        """Create a SELECT query.

        Args:
            *fields: Fields to select

        Returns:
            SelectQuery instance
        """
        query = SelectQuery(self.context)
        if fields:
            query.select(*fields)
        return query

    def insert(self) -> InsertQuery:
        """Create an INSERT query.

        Returns:
            InsertQuery instance
        """
        return InsertQuery(self.context)

    def update(self) -> UpdateQuery:
        """Create an UPDATE query.

        Returns:
            UpdateQuery instance
        """
        return UpdateQuery(self.context)

    def delete(self) -> DeleteQuery:
        """Create a DELETE query.

        Returns:
            DeleteQuery instance
        """
        return DeleteQuery(self.context)

    def set_dialect(self, dialect: Dialect) -> "QueryBuilder":
        """Set the SQL dialect.

        Args:
            dialect: SQL dialect instance

        Returns:
            Self for method chaining
        """
        self.context.dialect = dialect
        return self

    def set_schema(self, schema: str) -> "QueryBuilder":
        """Set default schema.

        Args:
            schema: Schema name

        Returns:
            Self for method chaining
        """
        self.context.schema = schema
        return self

    def set_table_prefix(self, prefix: str) -> "QueryBuilder":
        """Set table prefix.

        Args:
            prefix: Table prefix

        Returns:
            Self for method chaining
        """
        self.context.table_prefix = prefix
        return self
