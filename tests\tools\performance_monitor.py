"""Performance monitoring tools for testing."""

import asyncio
import gc
import psutil
import time
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union
from functools import wraps
import threading
from datetime import datetime


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    execution_time: float = 0.0
    memory_usage_mb: float = 0.0
    peak_memory_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    disk_io_read_mb: float = 0.0
    disk_io_write_mb: float = 0.0
    network_io_sent_mb: float = 0.0
    network_io_recv_mb: float = 0.0
    gc_collections: int = 0
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


class PerformanceMonitor:
    """Monitor performance metrics during test execution."""
    
    def __init__(self):
        """Initialize the performance monitor."""
        self.metrics_history: List[PerformanceMetrics] = []
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        
    def start_monitoring(self, interval: float = 1.0):
        """Start continuous performance monitoring.
        
        Args:
            interval: Monitoring interval in seconds
        """
        if self._monitoring:
            return
            
        self._monitoring = True
        self._stop_event.clear()
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop continuous performance monitoring."""
        if not self._monitoring:
            return
            
        self._monitoring = False
        self._stop_event.set()
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5.0)
    
    def _monitor_loop(self, interval: float):
        """Continuous monitoring loop."""
        while not self._stop_event.wait(interval):
            try:
                metrics = self._collect_system_metrics()
                self.metrics_history.append(metrics)
            except Exception as e:
                print(f"Error collecting metrics: {e}")
    
    def _collect_system_metrics(self) -> PerformanceMetrics:
        """Collect current system metrics."""
        process = psutil.Process()
        
        # Memory metrics
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        
        # CPU metrics
        cpu_percent = process.cpu_percent()
        
        # I/O metrics
        io_counters = process.io_counters()
        disk_read_mb = io_counters.read_bytes / 1024 / 1024
        disk_write_mb = io_counters.write_bytes / 1024 / 1024
        
        # Network metrics (system-wide)
        net_io = psutil.net_io_counters()
        net_sent_mb = net_io.bytes_sent / 1024 / 1024
        net_recv_mb = net_io.bytes_recv / 1024 / 1024
        
        # Garbage collection
        gc_stats = gc.get_stats()
        total_collections = sum(stat['collections'] for stat in gc_stats)
        
        return PerformanceMetrics(
            memory_usage_mb=memory_mb,
            peak_memory_mb=memory_mb,  # Will be updated by context managers
            cpu_usage_percent=cpu_percent,
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb,
            network_io_sent_mb=net_sent_mb,
            network_io_recv_mb=net_recv_mb,
            gc_collections=total_collections
        )
    
    @contextmanager
    def measure_performance(self, test_name: str = "test"):
        """Context manager to measure performance of a code block.
        
        Args:
            test_name: Name of the test being measured
        """
        # Start measurements
        start_time = time.perf_counter()
        start_metrics = self._collect_system_metrics()
        gc_before = gc.get_stats()
        
        # Force garbage collection before measurement
        gc.collect()
        
        try:
            yield
        finally:
            # End measurements
            end_time = time.perf_counter()
            end_metrics = self._collect_system_metrics()
            gc_after = gc.get_stats()
            
            # Calculate differences
            execution_time = end_time - start_time
            memory_diff = end_metrics.memory_usage_mb - start_metrics.memory_usage_mb
            cpu_avg = (start_metrics.cpu_usage_percent + end_metrics.cpu_usage_percent) / 2
            
            gc_collections = sum(
                after['collections'] - before['collections']
                for before, after in zip(gc_before, gc_after)
            )
            
            metrics = PerformanceMetrics(
                execution_time=execution_time,
                memory_usage_mb=memory_diff,
                peak_memory_mb=end_metrics.memory_usage_mb,
                cpu_usage_percent=cpu_avg,
                gc_collections=gc_collections,
                custom_metrics={'test_name': test_name}
            )
            
            self.metrics_history.append(metrics)
    
    @asynccontextmanager
    async def measure_async_performance(self, test_name: str = "async_test"):
        """Async context manager to measure performance of async code.
        
        Args:
            test_name: Name of the test being measured
        """
        # Start measurements
        start_time = time.perf_counter()
        start_metrics = self._collect_system_metrics()
        gc_before = gc.get_stats()
        
        # Force garbage collection before measurement
        gc.collect()
        
        try:
            yield
        finally:
            # End measurements
            end_time = time.perf_counter()
            end_metrics = self._collect_system_metrics()
            gc_after = gc.get_stats()
            
            # Calculate differences
            execution_time = end_time - start_time
            memory_diff = end_metrics.memory_usage_mb - start_metrics.memory_usage_mb
            cpu_avg = (start_metrics.cpu_usage_percent + end_metrics.cpu_usage_percent) / 2
            
            gc_collections = sum(
                after['collections'] - before['collections']
                for before, after in zip(gc_before, gc_after)
            )
            
            metrics = PerformanceMetrics(
                execution_time=execution_time,
                memory_usage_mb=memory_diff,
                peak_memory_mb=end_metrics.memory_usage_mb,
                cpu_usage_percent=cpu_avg,
                gc_collections=gc_collections,
                custom_metrics={'test_name': test_name, 'async': True}
            )
            
            self.metrics_history.append(metrics)
    
    def performance_test(self, test_name: str = None):
        """Decorator to measure performance of test functions.
        
        Args:
            test_name: Optional name for the test
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                name = test_name or func.__name__
                with self.measure_performance(name):
                    return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def async_performance_test(self, test_name: str = None):
        """Decorator to measure performance of async test functions.
        
        Args:
            test_name: Optional name for the test
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                name = test_name or func.__name__
                async with self.measure_async_performance(name):
                    return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of collected metrics.
        
        Returns:
            Dictionary with metrics summary
        """
        if not self.metrics_history:
            return {}
        
        execution_times = [m.execution_time for m in self.metrics_history if m.execution_time > 0]
        memory_usage = [m.memory_usage_mb for m in self.metrics_history]
        cpu_usage = [m.cpu_usage_percent for m in self.metrics_history]
        
        return {
            'total_measurements': len(self.metrics_history),
            'execution_time': {
                'min': min(execution_times) if execution_times else 0,
                'max': max(execution_times) if execution_times else 0,
                'avg': sum(execution_times) / len(execution_times) if execution_times else 0,
                'total': sum(execution_times) if execution_times else 0
            },
            'memory_usage_mb': {
                'min': min(memory_usage) if memory_usage else 0,
                'max': max(memory_usage) if memory_usage else 0,
                'avg': sum(memory_usage) / len(memory_usage) if memory_usage else 0
            },
            'cpu_usage_percent': {
                'min': min(cpu_usage) if cpu_usage else 0,
                'max': max(cpu_usage) if cpu_usage else 0,
                'avg': sum(cpu_usage) / len(cpu_usage) if cpu_usage else 0
            },
            'gc_collections': sum(m.gc_collections for m in self.metrics_history),
            'peak_memory_mb': max(m.peak_memory_mb for m in self.metrics_history) if self.metrics_history else 0
        }
    
    def assert_performance_thresholds(self, 
                                    max_execution_time: Optional[float] = None,
                                    max_memory_mb: Optional[float] = None,
                                    max_cpu_percent: Optional[float] = None):
        """Assert that performance metrics meet specified thresholds.
        
        Args:
            max_execution_time: Maximum allowed execution time in seconds
            max_memory_mb: Maximum allowed memory usage in MB
            max_cpu_percent: Maximum allowed CPU usage percentage
            
        Raises:
            AssertionError: If any threshold is exceeded
        """
        summary = self.get_metrics_summary()
        
        if max_execution_time is not None:
            actual_time = summary.get('execution_time', {}).get('max', 0)
            assert actual_time <= max_execution_time, (
                f"Execution time {actual_time:.3f}s exceeds threshold {max_execution_time}s"
            )
        
        if max_memory_mb is not None:
            actual_memory = summary.get('peak_memory_mb', 0)
            assert actual_memory <= max_memory_mb, (
                f"Memory usage {actual_memory:.2f}MB exceeds threshold {max_memory_mb}MB"
            )
        
        if max_cpu_percent is not None:
            actual_cpu = summary.get('cpu_usage_percent', {}).get('max', 0)
            assert actual_cpu <= max_cpu_percent, (
                f"CPU usage {actual_cpu:.1f}% exceeds threshold {max_cpu_percent}%"
            )
    
    def clear_metrics(self):
        """Clear all collected metrics."""
        self.metrics_history.clear()
    
    def export_metrics(self, filepath: str):
        """Export metrics to a file.
        
        Args:
            filepath: Path to export file
        """
        import json
        
        data = {
            'summary': self.get_metrics_summary(),
            'detailed_metrics': [
                {
                    'execution_time': m.execution_time,
                    'memory_usage_mb': m.memory_usage_mb,
                    'peak_memory_mb': m.peak_memory_mb,
                    'cpu_usage_percent': m.cpu_usage_percent,
                    'gc_collections': m.gc_collections,
                    'custom_metrics': m.custom_metrics,
                    'timestamp': m.timestamp.isoformat()
                }
                for m in self.metrics_history
            ]
        }
        
        with open(filepath, 'w') as f:
            json.dump(data, f, indent=2)


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


# Convenience functions
def measure_performance(test_name: str = "test"):
    """Convenience function to measure performance."""
    return performance_monitor.measure_performance(test_name)


def measure_async_performance(test_name: str = "async_test"):
    """Convenience function to measure async performance."""
    return performance_monitor.measure_async_performance(test_name)


def performance_test(test_name: str = None):
    """Convenience decorator for performance testing."""
    return performance_monitor.performance_test(test_name)


def async_performance_test(test_name: str = None):
    """Convenience decorator for async performance testing."""
    return performance_monitor.async_performance_test(test_name)