"""General utility tools module.

This module provides general utility functions needed during database framework development, including string processing, file path processing, etc.
"""

import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Union


def sanitize_string(value: str, max_length: Optional[int] = None) -> str:
    """Clean and normalize string.

    Args:
        value: String to clean
        max_length: Maximum length limit, no limit if None

    Returns:
        str: Cleaned string

    Examples:
        >>> sanitize_string('  Hello World  ')
        'Hello World'
        >>> sanitize_string('Test String', max_length=5)
        'Test '
    """
    if not isinstance(value, str):
        value = str(value)

    # Remove leading and trailing whitespace
    cleaned = value.strip()

    # Truncate if maximum length is specified
    if max_length is not None and len(cleaned) > max_length:
        cleaned = cleaned[:max_length].rstrip()

    return cleaned


def snake_to_camel(snake_str: str, capitalize_first: bool = False) -> str:
    """Convert snake_case to camelCase.

    Args:
        snake_str: Snake case string
        capitalize_first: Whether to capitalize first letter (PascalCase)

    Returns:
        str: Camel case string

    Examples:
        >>> snake_to_camel('user_name')
        'userName'
        >>> snake_to_camel('user_name', capitalize_first=True)
        'UserName'
    """
    components = snake_str.split("_")
    if not components:
        return snake_str

    if capitalize_first:
        return "".join(word.capitalize() for word in components)
    else:
        return components[0] + "".join(word.capitalize() for word in components[1:])


def camel_to_snake(camel_str: str) -> str:
    """Convert camelCase to snake_case.

    Args:
        camel_str: Camel case string

    Returns:
        str: Snake case string

    Examples:
        >>> camel_to_snake('userName')
        'user_name'
        >>> camel_to_snake('UserName')
        'user_name'
    """
    # Insert underscore before uppercase letters, then convert to lowercase
    snake_str = re.sub("([a-z0-9])([A-Z])", r"\1_\2", camel_str)
    return snake_str.lower()


def ensure_directory_exists(directory_path: Union[str, Path]) -> Path:
    """Ensure directory exists, create if it doesn't exist.

    Args:
        directory_path: Directory path

    Returns:
        Path: Path object of the directory

    Raises:
        OSError: If unable to create directory

    Examples:
        >>> ensure_directory_exists('/tmp/test_dir')
        PosixPath('/tmp/test_dir')
    """
    path = Path(directory_path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def safe_file_name(filename: str, replacement: str = "_") -> str:
    """Generate safe filename, remove or replace unsafe characters.

    Args:
        filename: Original filename
        replacement: Character to replace unsafe characters

    Returns:
        str: Safe filename

    Examples:
        >>> safe_file_name('file<name>.txt')
        'file_name_.txt'
        >>> safe_file_name('file/name.txt', '-')
        'file-name.txt'
    """
    # Define unsafe characters
    unsafe_chars = r'[<>:"/\\|?*]'

    # Replace unsafe characters
    safe_name = re.sub(unsafe_chars, replacement, filename)

    # Remove consecutive replacement characters
    safe_name = re.sub(f"{re.escape(replacement)}+", replacement, safe_name)

    # Remove leading and trailing replacement characters
    safe_name = safe_name.strip(replacement)

    return safe_name or "unnamed"


def get_file_extension(file_path: Union[str, Path]) -> str:
    """Get file extension.

    Args:
        file_path: File path

    Returns:
        str: File extension (without dot)

    Examples:
        >>> get_file_extension('data.csv')
        'csv'
        >>> get_file_extension('/path/to/file.xlsx')
        'xlsx'
    """
    return Path(file_path).suffix.lstrip(".")


def flatten_dict(nested_dict: Dict[str, Any], separator: str = ".") -> Dict[str, Any]:
    """Flatten nested dictionary.

    Args:
        nested_dict: Nested dictionary
        separator: Key name separator

    Returns:
        Dict[str, Any]: Flattened dictionary

    Examples:
        >>> flatten_dict({'a': {'b': {'c': 1}}})
        {'a.b.c': 1}
        >>> flatten_dict({'x': {'y': 2}}, separator='_')
        {'x_y': 2}
    """

    def _flatten(obj: Any, parent_key: str = "") -> Dict[str, Any]:
        items = []
        if isinstance(obj, dict):
            for key, value in obj.items():
                new_key = f"{parent_key}{separator}{key}" if parent_key else key
                items.extend(_flatten(value, new_key).items())
        else:
            return {parent_key: obj}
        return dict(items)

    return _flatten(nested_dict)


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """Split list into chunks of specified size.

    Args:
        lst: List to split
        chunk_size: Size of each chunk

    Returns:
        List[List[Any]]: List of chunks

    Examples:
        >>> chunk_list([1, 2, 3, 4, 5], 2)
        [[1, 2], [3, 4], [5]]
        >>> chunk_list(['a', 'b', 'c', 'd'], 3)
        [['a', 'b', 'c'], ['d']]
    """
    if chunk_size <= 0:
        raise ValueError("Chunk size must be positive")

    return [lst[i : i + chunk_size] for i in range(0, len(lst), chunk_size)]


def deep_merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """Deep merge two dictionaries.

    Args:
        dict1: First dictionary
        dict2: Second dictionary (higher priority)

    Returns:
        Dict[str, Any]: Merged dictionary

    Examples:
        >>> deep_merge_dicts({'a': {'b': 1}}, {'a': {'c': 2}})
        {'a': {'b': 1, 'c': 2}}
        >>> deep_merge_dicts({'x': 1}, {'x': 2})
        {'x': 2}
    """
    result = dict1.copy()

    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value

    return result


def format_bytes(bytes_value: int, decimal_places: int = 2) -> str:
    """Format bytes to human readable format.

    Args:
        bytes_value: Number of bytes
        decimal_places: Number of decimal places

    Returns:
        str: Formatted string

    Examples:
        >>> format_bytes(1024)
        '1.00 KB'
        >>> format_bytes(1048576)
        '1.00 MB'
        >>> format_bytes(1073741824, 1)
        '1.0 GB'
    """
    if bytes_value == 0:
        return "0 B"

    units = ["B", "KB", "MB", "GB", "TB", "PB"]
    unit_index = 0
    size = float(bytes_value)

    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1

    return f"{size:.{decimal_places}f} {units[unit_index]}"


def is_valid_email(email: str) -> bool:
    """Validate email address format.

    Args:
        email: Email address

    Returns:
        bool: True if format is valid, False otherwise

    Examples:
        >>> is_valid_email('<EMAIL>')
        True
        >>> is_valid_email('invalid-email')
        False
    """
    # More strict email validation regex
    # No consecutive dots, no leading/trailing dots, allow plus signs
    if ".." in email.split("@")[0]:  # Check for consecutive dots in local part
        return False
    pattern = r"^[a-zA-Z0-9]([a-zA-Z0-9._+%-]*[a-zA-Z0-9])?@[a-zA-Z0-9]([a-zA-Z0-9.-]*[a-zA-Z0-9])?\.[a-zA-Z]{2,}$"
    return bool(re.match(pattern, email))


def truncate_string(text: str, max_length: int, suffix: str = "...") -> str:
    """Truncate string to specified length.

    Args:
        text: String to truncate
        max_length: Maximum length
        suffix: Suffix to add after truncation

    Returns:
        str: Truncated string

    Examples:
        >>> truncate_string('This is a long text', 10)
        'This is...'
        >>> truncate_string('Short', 10)
        'Short'
    """
    if len(text) <= max_length:
        return text

    return text[: max_length - len(suffix)] + suffix
