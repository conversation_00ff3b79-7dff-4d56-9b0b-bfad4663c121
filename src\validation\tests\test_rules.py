"""统一验证框架规则测试

测试CDRValidationRules、KPIValidationRules、CFGValidationRules等验证规则的功能。
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from ..rules import (
    CDRValidationRules,
    KPIValidationRules,
    CFGValidationRules,
    ValidationRuleFactory
)
from ..core import ValidationFramework, ValidationSeverity
from ..exceptions import ValidationError
from .conftest import performance_test, memory_test


class TestCDRValidationRules:
    """CDR验证规则测试类"""
    
    def test_cdr_structure_rules(self, sample_cdr_data):
        """测试CDR结构规则"""
        rules = CDRValidationRules.get_structure_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_cdr_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_cdr_structure_rules_missing_columns(self, missing_columns_data):
        """测试CDR结构规则 - 缺少列"""
        rules = CDRValidationRules.get_structure_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(missing_columns_data)
        assert not result.is_valid
        assert any("Missing required columns" in issue.message for issue in result.issues)
    
    def test_cdr_value_rules(self, sample_cdr_data):
        """测试CDR值规则"""
        rules = CDRValidationRules.get_value_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_cdr_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_cdr_value_rules_invalid_data(self, invalid_cdr_data):
        """测试CDR值规则 - 无效数据"""
        rules = CDRValidationRules.get_value_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(invalid_cdr_data)
        assert not result.is_valid
        # 应该检测到负的通话时长
        assert any("Range validation failed" in issue.message for issue in result.issues)
    
    def test_cdr_telecom_rules(self, sample_cdr_data):
        """测试CDR电信规则"""
        rules = CDRValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_cdr_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_cdr_telecom_rules_invalid_coordinates(self, sample_cdr_data):
        """测试CDR电信规则 - 无效坐标"""
        # 修改数据使坐标无效
        data = sample_cdr_data.copy()
        data.loc[0, 'LONGITUDE'] = 200  # 无效经度
        data.loc[1, 'LATITUDE'] = 100   # 无效纬度
        
        rules = CDRValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        assert not result.is_valid
        assert any("Telecom validation failed" in issue.message for issue in result.issues)
    
    def test_cdr_file_rules(self, temp_csv_file):
        """测试CDR文件规则"""
        rules = CDRValidationRules.get_file_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate_file(temp_csv_file)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_cdr_file_rules_invalid_extension(self):
        """测试CDR文件规则 - 无效扩展名"""
        rules = CDRValidationRules.get_file_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate_file('/path/to/file.txt')
        assert not result.is_valid
        assert any("File does not exist" in issue.message for issue in result.issues)
    
    def test_cdr_all_rules(self, sample_cdr_data):
        """测试CDR所有规则"""
        rules = CDRValidationRules.get_all_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_cdr_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_cdr_call_duration_consistency(self, sample_cdr_data):
        """测试CDR通话时长一致性"""
        # 修改数据使通话时长与时间不一致
        data = sample_cdr_data.copy()
        data.loc[0, 'CALL_DURATION'] = 999999  # 不合理的时长
        
        rules = CDRValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        # 根据具体实现，可能会检测到不一致
    
    def test_cdr_phone_number_format(self, sample_cdr_data):
        """测试CDR电话号码格式"""
        # 修改数据使电话号码格式无效
        data = sample_cdr_data.copy()
        data.loc[0, 'CALLER_NUMBER'] = '123'  # 无效格式
        
        rules = CDRValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        assert not result.is_valid
    
    def test_cdr_time_sequence_validation(self, sample_cdr_data):
        """测试CDR时间序列验证"""
        # 修改数据使结束时间早于开始时间
        data = sample_cdr_data.copy()
        data.loc[0, 'CALL_END_TIME'] = data.loc[0, 'CALL_START_TIME'] - timedelta(minutes=1)
        
        rules = CDRValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        assert not result.is_valid


class TestKPIValidationRules:
    """KPI验证规则测试类"""
    
    def test_kpi_structure_rules(self, sample_kpi_data):
        """测试KPI结构规则"""
        rules = KPIValidationRules.get_structure_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_kpi_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_kpi_structure_rules_missing_columns(self, empty_dataframe):
        """测试KPI结构规则 - 缺少列"""
        # 创建缺少必需列的KPI数据
        data = pd.DataFrame({
            'KPI_NAME': ['RSRP', 'RSRQ'],
            # 缺少 KPI_VALUE, MEASUREMENT_TIME 等
        })
        
        rules = KPIValidationRules.get_structure_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        assert not result.is_valid
        assert any("Missing required columns" in issue.message for issue in result.issues)
    
    def test_kpi_value_rules(self, sample_kpi_data):
        """测试KPI值规则"""
        rules = KPIValidationRules.get_value_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_kpi_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_kpi_value_rules_invalid_data(self, invalid_kpi_data):
        """测试KPI值规则 - 无效数据"""
        rules = KPIValidationRules.get_value_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(invalid_kpi_data)
        assert not result.is_valid
        # 应该检测到非数字的KPI值
        assert any("Data type validation failed" in issue.message for issue in result.issues)
    
    def test_kpi_telecom_rules(self, sample_kpi_data):
        """测试KPI电信规则"""
        rules = KPIValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_kpi_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_kpi_telecom_rules_invalid_kpi_names(self, sample_kpi_data):
        """测试KPI电信规则 - 无效KPI名称"""
        # 修改数据使KPI名称无效
        data = sample_kpi_data.copy()
        data.loc[0, 'KPI_NAME'] = 'INVALID_KPI'  # 不在允许列表中
        
        rules = KPIValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        # 根据具体实现，可能会检测到无效的KPI名称
    
    def test_kpi_file_rules(self, temp_csv_file):
        """测试KPI文件规则"""
        rules = KPIValidationRules.get_file_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate_file(temp_csv_file)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_kpi_all_rules(self, sample_kpi_data):
        """测试KPI所有规则"""
        rules = KPIValidationRules.get_all_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_kpi_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_kpi_value_range_validation(self, sample_kpi_data):
        """测试KPI值范围验证"""
        # 修改数据使KPI值超出合理范围
        data = sample_kpi_data.copy()
        data.loc[data['KPI_NAME'] == 'RSRP', 'KPI_VALUE'] = -200  # RSRP通常在-140到-40之间
        
        rules = KPIValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        # 根据具体实现，可能会检测到超出范围的值
    
    def test_kpi_measurement_time_validation(self, sample_kpi_data):
        """测试KPI测量时间验证"""
        # 修改数据使测量时间无效
        data = sample_kpi_data.copy()
        data.loc[0, 'MEASUREMENT_TIME'] = 'invalid_time'
        
        rules = KPIValidationRules.get_value_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        assert not result.is_valid


class TestCFGValidationRules:
    """CFG验证规则测试类"""
    
    def test_cfg_structure_rules(self, sample_cfg_data):
        """测试CFG结构规则"""
        rules = CFGValidationRules.get_structure_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_cfg_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_cfg_structure_rules_missing_columns(self):
        """测试CFG结构规则 - 缺少列"""
        # 创建缺少必需列的CFG数据
        data = pd.DataFrame({
            'CELL_ID': ['CELL001', 'CELL002'],
            # 缺少其他必需列
        })
        
        rules = CFGValidationRules.get_structure_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        assert not result.is_valid
        assert any("Missing required columns" in issue.message for issue in result.issues)
    
    def test_cfg_value_rules(self, sample_cfg_data):
        """测试CFG值规则"""
        rules = CFGValidationRules.get_value_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_cfg_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_cfg_telecom_rules(self, sample_cfg_data):
        """测试CFG电信规则"""
        rules = CFGValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_cfg_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_cfg_telecom_rules_invalid_coordinates(self, sample_cfg_data):
        """测试CFG电信规则 - 无效坐标"""
        # 修改数据使坐标无效
        data = sample_cfg_data.copy()
        data.loc[0, 'LONGITUDE'] = 200  # 无效经度
        
        rules = CFGValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        assert not result.is_valid
    
    def test_cfg_file_rules(self, temp_csv_file):
        """测试CFG文件规则"""
        rules = CFGValidationRules.get_file_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate_file(temp_csv_file)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_cfg_all_rules(self, sample_cfg_data):
        """测试CFG所有规则"""
        rules = CFGValidationRules.get_all_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(sample_cfg_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_cfg_cell_id_uniqueness(self, sample_cfg_data):
        """测试CFG小区ID唯一性"""
        # 修改数据使小区ID重复
        data = sample_cfg_data.copy()
        data.loc[1, 'CELL_ID'] = data.loc[0, 'CELL_ID']  # 创建重复
        
        rules = CFGValidationRules.get_value_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        assert not result.is_valid
        assert any("Duplicate values found" in issue.message for issue in result.issues)
    
    def test_cfg_frequency_validation(self, sample_cfg_data):
        """测试CFG频率验证"""
        # 修改数据使频率无效
        data = sample_cfg_data.copy()
        data.loc[0, 'FREQUENCY'] = 0  # 无效频率
        
        rules = CFGValidationRules.get_telecom_rules()
        framework = ValidationFramework(rules)
        
        result = framework.validate(data)
        # 根据具体实现，可能会检测到无效频率


class TestValidationRuleFactory:
    """验证规则工厂测试类"""
    
    def test_create_cdr_validator(self, sample_cdr_data):
        """测试创建CDR验证器"""
        framework = ValidationRuleFactory.create_cdr_validator()
        
        result = framework.validate(sample_cdr_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_create_kpi_validator(self, sample_kpi_data):
        """测试创建KPI验证器"""
        framework = ValidationRuleFactory.create_kpi_validator()
        
        result = framework.validate(sample_kpi_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_create_cfg_validator(self, sample_cfg_data):
        """测试创建CFG验证器"""
        framework = ValidationRuleFactory.create_cfg_validator()
        
        result = framework.validate(sample_cfg_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_create_validator_by_type(self, sample_cdr_data, sample_kpi_data, sample_cfg_data):
        """测试根据类型创建验证器"""
        test_cases = [
            ('cdr', sample_cdr_data),
            ('kpi', sample_kpi_data),
            ('cfg', sample_cfg_data)
        ]
        
        for data_type, data in test_cases:
            framework = ValidationRuleFactory.create_validator_by_type(data_type)
            result = framework.validate(data)
            assert result.is_valid, f"Validation failed for {data_type} data"
    
    def test_create_validator_invalid_type(self):
        """测试创建无效类型验证器"""
        with pytest.raises(ValidationError):
            ValidationRuleFactory.create_validator_by_type('invalid_type')
    
    def test_create_custom_validator(self, sample_cdr_data):
        """测试创建自定义验证器"""
        custom_rules = CDRValidationRules.get_structure_rules()
        framework = ValidationRuleFactory.create_custom_validator(custom_rules)
        
        result = framework.validate(sample_cdr_data)
        assert result.is_valid
        assert len(result.issues) == 0
    
    def test_get_available_types(self):
        """测试获取可用类型"""
        types = ValidationRuleFactory.get_available_types()
        assert 'cdr' in types
        assert 'kpi' in types
        assert 'cfg' in types
        assert len(types) >= 3
    
    def test_factory_caching(self, sample_cdr_data):
        """测试工厂缓存机制"""
        # 多次创建相同类型的验证器
        framework1 = ValidationRuleFactory.create_cdr_validator()
        framework2 = ValidationRuleFactory.create_cdr_validator()
        
        # 根据实现，可能会使用缓存
        # 这里主要测试功能正确性
        result1 = framework1.validate(sample_cdr_data)
        result2 = framework2.validate(sample_cdr_data)
        
        assert result1.is_valid
        assert result2.is_valid


class TestRulePerformance:
    """规则性能测试类"""
    
    @performance_test(max_time=2.0)
    def test_cdr_rules_performance(self, large_dataset):
        """测试CDR规则性能"""
        framework = ValidationRuleFactory.create_cdr_validator()
        
        result = framework.validate(large_dataset)
        # 应该在时间限制内完成
    
    @performance_test(max_time=1.5)
    def test_kpi_rules_performance(self, large_dataset):
        """测试KPI规则性能"""
        # 将大数据集转换为KPI格式
        kpi_data = large_dataset.copy()
        kpi_data['KPI_NAME'] = 'RSRP'
        kpi_data['KPI_VALUE'] = -85.5
        kpi_data['MEASUREMENT_TIME'] = datetime.now()
        
        framework = ValidationRuleFactory.create_kpi_validator()
        
        result = framework.validate(kpi_data)
        # 应该在时间限制内完成
    
    @memory_test(max_memory_mb=150)
    def test_cfg_rules_memory(self, large_dataset):
        """测试CFG规则内存使用"""
        # 将大数据集转换为CFG格式
        cfg_data = large_dataset.copy()
        cfg_data['FREQUENCY'] = 2100
        cfg_data['AZIMUTH'] = 120
        cfg_data['ANTENNA_HEIGHT'] = 30
        
        framework = ValidationRuleFactory.create_cfg_validator()
        
        result = framework.validate(cfg_data)
        # 内存使用应该在限制内


class TestRuleIntegration:
    """规则集成测试类"""
    
    def test_mixed_data_validation(self, sample_cdr_data, sample_kpi_data):
        """测试混合数据验证"""
        # 测试在同一个会话中验证不同类型的数据
        cdr_framework = ValidationRuleFactory.create_cdr_validator()
        kpi_framework = ValidationRuleFactory.create_kpi_validator()
        
        cdr_result = cdr_framework.validate(sample_cdr_data)
        kpi_result = kpi_framework.validate(sample_kpi_data)
        
        assert cdr_result.is_valid
        assert kpi_result.is_valid
    
    def test_rule_combination(self, sample_cdr_data):
        """测试规则组合"""
        # 组合不同类型的规则
        structure_rules = CDRValidationRules.get_structure_rules()
        value_rules = CDRValidationRules.get_value_rules()
        telecom_rules = CDRValidationRules.get_telecom_rules()
        
        all_rules = structure_rules + value_rules + telecom_rules
        framework = ValidationFramework(all_rules)
        
        result = framework.validate(sample_cdr_data)
        assert result.is_valid
    
    def test_rule_priority_handling(self, invalid_cdr_data):
        """测试规则优先级处理"""
        framework = ValidationRuleFactory.create_cdr_validator()
        
        result = framework.validate(invalid_cdr_data)
        assert not result.is_valid
        
        # 检查是否有不同严重级别的问题
        severities = [issue.severity for issue in result.issues]
        assert ValidationSeverity.ERROR in severities or ValidationSeverity.WARNING in severities
    
    def test_rule_error_recovery(self, sample_cdr_data):
        """测试规则错误恢复"""
        # 创建一个会抛出异常的规则
        class FailingRule:
            def validate(self, data, context=None):
                raise Exception("Test exception")
        
        rules = CDRValidationRules.get_structure_rules() + [FailingRule()]
        framework = ValidationFramework(rules)
        
        # 验证应该继续进行，即使某个规则失败
        result = framework.validate(sample_cdr_data)
        # 根据具体实现，可能会记录错误但继续验证


if __name__ == "__main__":
    pytest.main([__file__])