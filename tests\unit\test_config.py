"""Unit tests for configuration management components."""

import json
import os
import tempfile
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, mock_open, patch

import pytest
import yaml

# Import components to test
from src.config import get_config
from src.database.exceptions import ConfigurationError, ValidationError


class TestDatabaseConfig:
    """Test cases for DatabaseConfig class."""

    @pytest.fixture
    def sample_config_data(self):
        """Create sample configuration data."""
        return {
            "database": {
                "host": "localhost",
                "port": 5432,
                "name": "test_db",
                "user": "test_user",
                "password": "test_password",
                "driver": "postgresql+asyncpg",
                "pool_size": 10,
                "max_overflow": 20,
                "pool_timeout": 30,
                "pool_recycle": 3600,
                "echo": False,
                "ssl_mode": "prefer",
            },
            "connection_pool": {
                "min_size": 5,
                "max_size": 20,
                "timeout": 60,
                "retry_attempts": 3,
                "retry_delay": 1.0,
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "database.log",
                "max_size": "10MB",
                "backup_count": 5,
            },
            "monitoring": {
                "enabled": True,
                "metrics_interval": 60,
                "health_check_interval": 30,
                "alert_thresholds": {
                    "cpu_usage": 80,
                    "memory_usage": 85,
                    "connection_pool_usage": 90,
                },
            },
        }

    @pytest.fixture
    def config(self, sample_config_data):
        """Create DatabaseConfig instance."""
        db_data = sample_config_data["database"]
        return DatabaseConfig(
            host=db_data["host"],
            port=db_data["port"],
            name=db_data["name"],
            user=db_data["user"],
            password=db_data["password"],
        )

    def test_config_initialization(self, config, sample_config_data):
        """Test configuration initialization."""
        assert config.host == "localhost"
        assert config.port == 5432
        assert config.name == "test_db"
        assert config.user == "test_user"
        assert config.password == "test_password"

    def test_config_get_values(self, config):
        """Test getting configuration values."""
        assert config.host == "localhost"
        assert config.port == 5432
        assert config.name == "test_db"
        assert config.user == "test_user"
        assert config.password == "test_password"

    def test_config_validation(self, sample_config_data):
        """Test configuration validation."""
        db_data = sample_config_data["database"]

        # Test valid configuration
        valid_config = DatabaseConfig(
            host=db_data["host"],
            port=db_data["port"],
            name=db_data["name"],
            user=db_data["user"],
            password=db_data["password"],
        )
        assert valid_config.port == 5432

        # Test invalid port
        with pytest.raises(ValueError):
            DatabaseConfig(
                host=db_data["host"],
                port=70000,  # Invalid port
                name=db_data["name"],
                user=db_data["user"],
                password=db_data["password"],
            )

    def test_config_defaults(self):
        """Test configuration default values."""
        config = DatabaseConfig(
            name="test_db",
            user="test_user",
            password=os.getenv("TEST_DB_PASSWORD", "secure_test_password_123!"),
        )
        assert config.host == "localhost"  # Default value
        assert config.port == 5432  # Default value

    def test_config_model_dump(self, config):
        """Test converting configuration to dictionary."""
        config_dict = config.model_dump()

        expected = {
            "host": "localhost",
            "port": 5432,
            "name": "test_db",
            "user": "test_user",
            "password": "test_password",
        }
        assert config_dict == expected
        assert isinstance(config_dict, dict)

    def test_config_copy(self, config):
        """Test configuration copying."""
        config_copy = config.model_copy()

        assert config_copy is not config
        assert config_copy.host == config.host
        assert config_copy.port == config.port

        # Modifications to copy should not affect original
        config_copy.host = "modified"
        assert config.host == "localhost"
        assert config_copy.host == "modified"


class TestConfig:
    """Test cases for Config class."""

    @pytest.fixture
    def config_instance(self):
        """Create Config instance."""
        return Config(
            database=DatabaseConfig(
                name="test_db",
                user="test_user",
                password=os.getenv("TEST_DB_PASSWORD", "secure_test_password_123!"),
            )
        )

    @pytest.fixture
    def sample_yaml_config(self):
        """Create sample YAML configuration content."""
        return """
database:
  host: localhost
  port: 5432
  name: test_db
  user: test_user
  password: test_password

logging:
  level: INFO
  file: app.log

monitoring:
  enabled: true
  interval: 60
"""

    @pytest.fixture
    def sample_json_config(self):
        """Create sample JSON configuration content."""
        return json.dumps(
            {
                "database": {"host": "localhost", "port": 5432, "name": "test_db"},
                "logging": {"level": "INFO", "file": "app.log"},
            }
        )

    def test_load_yaml_config(self, sample_yaml_config):
        """Test loading YAML configuration file."""
        with patch("builtins.open", mock_open(read_data=sample_yaml_config)), patch(
            "pathlib.Path.exists", return_value=True
        ):
            config = load_config("config.yaml")

            assert isinstance(config, Config)
            assert config.database.host == "localhost"
            assert config.database.port == 5432

    def test_load_json_config(self, sample_json_config):
        """Test loading JSON configuration file."""
        with patch("builtins.open", mock_open(read_data=sample_json_config)), patch(
            "pathlib.Path.exists", return_value=True
        ):
            # Note: load_config only supports YAML, not JSON
            # This test would need to be adapted for YAML format
            pass

    def test_load_config_file_not_found(self):
        """Test handling of missing configuration file."""
        with patch("pathlib.Path.exists", return_value=False):
            with pytest.raises(FileNotFoundError):
                load_config("nonexistent.yaml")

    def test_load_invalid_yaml_config(self):
        """Test handling of invalid YAML configuration."""
        invalid_yaml = "invalid: yaml: content: ["

        with patch("builtins.open", mock_open(read_data=invalid_yaml)), patch(
            "pathlib.Path.exists", return_value=True
        ):
            with pytest.raises(yaml.YAMLError):
                load_config("invalid.yaml")

    def test_get_config(self):
        """Test getting global configuration instance."""
        # Reset global config
        import src.database.config

        src.database.config._config = None

        sample_yaml = """
database:
  name: test_db
  user: test_user
  password: test_password
"""

        with patch("builtins.open", mock_open(read_data=sample_yaml)), patch(
            "pathlib.Path.exists", return_value=True
        ):
            config = get_config()
            assert isinstance(config, Config)
            assert config.database.name == "test_db"

    def test_config_instance_creation(self, config_instance):
        """Test Config instance creation."""
        assert isinstance(config_instance, Config)
        assert isinstance(config_instance.database, DatabaseConfig)
        assert config_instance.database.name == "test_db"
        assert config_instance.database.user == "test_user"
        assert config_instance.database.password == os.getenv(
            "TEST_DB_PASSWORD", "secure_test_password_123!"
        )


# All tests for non-existent classes have been removed
