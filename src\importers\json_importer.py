"""JSON data importer.

This module provides JSON import functionality for various data formats.

__author__ = "Vincent.Li"
__email__ = "<EMAIL>"
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from .base import BaseImporter, ImportError, ImportResult

# Configure logging
logger = logging.getLogger(__name__)


class JSONImporter(BaseImporter):
    """JSON data importer."""

    def __init__(
        self, source_path: Union[str, Path], encoding: str = "utf-8", **kwargs
    ):
        """Initialize JSON importer.

        Args:
            source_path: Path to the JSON file
            encoding: File encoding
            **kwargs: Additional configuration options
        """
        super().__init__(source_path, **kwargs)
        self.encoding = encoding

        # Validate JSON file extension
        if self.source_path.suffix.lower() not in [".json", ".jsonl"]:
            logger.warning(
                f"File extension {self.source_path.suffix} may not be a JSON file"
            )

    async def import_data(self, **kwargs) -> ImportResult:
        """Import data from JSON file.

        Args:
            **kwargs: Additional import options
                - orient: DataFrame orientation for JSON conversion
                - lines: Whether to read JSON lines format
                - normalize: Whether to normalize nested JSON

        Returns:
            ImportResult: Result of the import operation

        Raises:
            ImportError: If import fails
        """
        try:
            self.validate_source()

            orient = kwargs.get("orient", "records")
            lines = kwargs.get("lines", False)
            normalize = kwargs.get("normalize", False)

            if lines:
                # Read JSON lines format
                df = pd.read_json(self.source_path, lines=True, encoding=self.encoding)
            else:
                # Read regular JSON
                with open(self.source_path, "r", encoding=self.encoding) as f:
                    data = json.load(f)

                if normalize and isinstance(data, (list, dict)):
                    df = pd.json_normalize(data)
                else:
                    df = pd.DataFrame(data)

            records_imported = len(df)
            file_size = self.source_path.stat().st_size

            logger.info(
                f"Successfully imported {records_imported} records from {self.source_path}"
            )

            return ImportResult(
                success=True,
                source_path=self.source_path,
                records_imported=records_imported,
                file_size_bytes=file_size,
                metadata={
                    "encoding": self.encoding,
                    "orient": orient,
                    "lines": lines,
                    "normalize": normalize,
                    "columns": list(df.columns) if not df.empty else [],
                    "shape": df.shape,
                    "data_types": df.dtypes.to_dict() if not df.empty else {},
                },
            )

        except Exception as e:
            error_msg = f"Failed to import JSON: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ImportResult(
                success=False, source_path=self.source_path, error_message=error_msg
            )

    def preview_data(self, num_rows: int = 5) -> Optional[pd.DataFrame]:
        """Preview first few rows of JSON data.

        Args:
            num_rows: Number of rows to preview

        Returns:
            DataFrame: Preview of the data
        """
        try:
            self.validate_source()

            with open(self.source_path, "r", encoding=self.encoding) as f:
                data = json.load(f)

            if isinstance(data, list):
                preview_data = data[:num_rows]
            else:
                preview_data = data

            return pd.DataFrame(preview_data)

        except Exception as e:
            logger.error(f"Failed to preview JSON data: {e}")
            return None

    def get_structure_info(self) -> Dict[str, Any]:
        """Get information about JSON structure.

        Returns:
            Dict: Structure information
        """
        try:
            self.validate_source()

            with open(self.source_path, "r", encoding=self.encoding) as f:
                data = json.load(f)

            def analyze_structure(obj, path="root"):
                """Recursively analyze JSON structure."""
                if isinstance(obj, dict):
                    return {
                        "type": "object",
                        "keys": list(obj.keys()),
                        "nested": {
                            k: analyze_structure(v, f"{path}.{k}")
                            for k, v in obj.items()
                        },
                    }
                elif isinstance(obj, list):
                    if obj:
                        return {
                            "type": "array",
                            "length": len(obj),
                            "item_type": analyze_structure(obj[0], f"{path}[0]"),
                        }
                    else:
                        return {"type": "array", "length": 0}
                else:
                    return {"type": type(obj).__name__, "value": str(obj)[:100]}

            return {
                "structure": analyze_structure(data),
                "file_size": self.source_path.stat().st_size,
                "encoding": self.encoding,
            }

        except Exception as e:
            logger.error(f"Failed to get structure info: {e}")
            return {}
