"""Configuration settings module

This module provides configuration management functionality for the application, including:
- Database connection configuration
- Logging configuration
- Application settings
- Environment variable management

Author: <PERSON><PERSON>
Email: <EMAIL>
Version: 1.0.0
"""

import configparser
import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import yaml

logger = logging.getLogger(__name__)


class Settings:
    """Application settings class.

    Manages all configuration settings for the application, including
    database connections, logging configuration, file paths, etc.
    """

    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        """
        Initialize settings.

        Args:
            config_dir: Configuration file directory
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config_dir = Path(config_dir) if config_dir else Path.cwd() / "config"
        self.config_dir.mkdir(exist_ok=True)

        self._settings = {}
        self._load_default_settings()

    def _load_default_settings(self) -> None:
        """
        Load default settings with telecommunications-specific configurations
        """
        self._settings = {
            "project": {
                "name": "Connect Project",
                "version": "1.0.0",
                "description": "Telecommunications data processing and analysis project",
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "logs/app.log",
                "max_bytes": 10485760,  # 10MB
                "backup_count": 5,
            },
            "database": {
                "host": "localhost",
                "port": 5432,
                "name": "connect",
                "user": "connect_user",
                "password": "",
                "ssl_mode": "prefer",
                "pool": {
                    "min_size": 5,
                    "max_size": 20,
                    "timeout": 30,
                    "recycle": 3600,
                },
                "connection": {
                    "timeout": 30,
                    "command_timeout": 60,
                    "retry_attempts": 3,
                    "retry_delay": 1.0,
                },
            },
            "geo": {
                "default_crs": "EPSG:4326",
                "output_format": "GeoJSON",
                "precision": 6,
                "buffer_resolution": 16,
            },
            "qgis": {"auto_detect": True, "path": None, "plugins_enabled": True},
            "environment": {
                "use_virtual_env": True,
                "python_version": "3.12+",
                "auto_setup": True,
            },
            "data": {
                "input_dir": "data/input",
                "output_dir": "data/output",
                "temp_dir": "data/temp",
                "cache_enabled": True,
            },
            "telecom": {
                "cdr": {
                    "batch_size": 10000,
                    "table_prefix": "cdr_",
                    "validation_enabled": True,
                    "geospatial_enhancement": True,
                },
                "ep": {
                    "batch_size": 5000,
                    "table_prefix": "ep_",
                    "signal_analysis": True,
                    "coverage_analysis": True,
                },
                "kpi": {
                    "calculation_interval": 300,  # 5 minutes
                    "retention_days": 90,
                    "real_time_enabled": True,
                    "alert_thresholds": {
                        "call_success_rate": 95.0,
                        "signal_strength_min": -110.0,
                        "handover_success_rate": 98.0,
                    },
                },
                "performance": {
                    "max_memory_usage_mb": 2048,
                    "processing_timeout_seconds": 3600,
                    "parallel_workers": 4,
                },
            },
            "security": {
                "jwt": {
                    "algorithm": "HS256",
                    "access_token_expire_minutes": 30,
                    "secret_key": "",
                },
                "password": {
                    "min_length": 8,
                    "require_uppercase": True,
                    "require_lowercase": True,
                    "require_numbers": True,
                    "require_special": True,
                },
            },
            "monitoring": {
                "enabled": True,
                "metrics_interval": 60,
                "health_check_interval": 30,
                "performance_monitoring": True,
                "memory_profiling": False,
                "alerts": {
                    "enabled": True,
                    "thresholds": {
                        "cpu_usage": 80.0,
                        "memory_usage": 85.0,
                        "disk_usage": 90.0,
                        "response_time_ms": 1000.0,
                    },
                },
            },
            "processing": {"max_workers": 4, "chunk_size": 1000, "memory_limit": "2GB"},
        }

    def load_from_file(
        self, file_path: Union[str, Path], file_format: Optional[str] = None
    ) -> None:
        """
        Load settings from file

        Args:
            file_path: Configuration file path
            file_format: File format ('json', 'yaml', 'ini')
        """
        file_path = Path(file_path)

        if not file_path.exists():
            self.logger.warning(f"Configuration file does not exist: {file_path}")
            return

        if not file_format:
            file_format = file_path.suffix.lower().lstrip(".")

        try:
            if file_format in ["json"]:
                with open(file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
            elif file_format in ["yaml", "yml"]:
                with open(file_path, "r", encoding="utf-8") as f:
                    data = yaml.safe_load(f)
            elif file_format in ["ini", "cfg"]:
                config = configparser.ConfigParser()
                config.read(file_path, encoding="utf-8")
                data = {section: dict(config[section]) for section in config.sections()}
            else:
                raise ValueError(f"Unsupported file format: {file_format}")

            self._merge_settings(data)
            self.logger.info(f"Configuration file loaded successfully: {file_path}")

        except Exception as e:
            self.logger.error(f"Failed to load configuration file: {e}")
            raise ValueError(f"Failed to load configuration file: {e}")

    def save_to_file(
        self, file_path: Union[str, Path], file_format: Optional[str] = None
    ) -> None:
        """
        Save settings to file

        Args:
            file_path: Configuration file path
            file_format: File format ('json', 'yaml', 'ini')
        """
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)

        if not file_format:
            file_format = file_path.suffix.lower().lstrip(".")

        try:
            if file_format in ["json"]:
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump(self._settings, f, indent=2, ensure_ascii=False)
            elif file_format in ["yaml", "yml"]:
                with open(file_path, "w", encoding="utf-8") as f:
                    yaml.dump(
                        self._settings,
                        f,
                        default_flow_style=False,
                        allow_unicode=True,
                        indent=2,
                    )
            elif file_format in ["ini", "cfg"]:
                config = configparser.ConfigParser()
                for section, values in self._settings.items():
                    config[section] = {}
                    for key, value in values.items():
                        config[section][key] = str(value)
                with open(file_path, "w", encoding="utf-8") as f:
                    config.write(f)
            else:
                raise ValueError(f"Unsupported file format: {file_format}")

            self.logger.info(f"Configuration file saved successfully: {file_path}")

        except Exception as e:
            self.logger.error(f"Failed to save configuration file: {e}")
            raise ValueError(f"Failed to save configuration file: {e}")

    def _merge_settings(self, new_settings: Dict[str, Any]) -> None:
        """
        Merge settings

        Args:
            new_settings: New settings dictionary
        """

        def merge_dict(base: Dict, update: Dict) -> Dict:
            for key, value in update.items():
                if (
                    key in base
                    and isinstance(base[key], dict)
                    and isinstance(value, dict)
                ):
                    merge_dict(base[key], value)
                else:
                    base[key] = value
            return base

        merge_dict(self._settings, new_settings)

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get setting value

        Args:
            key: Setting key, supports dot-separated nested keys
            default: Default value

        Returns:
            Any: Setting value
        """
        keys = key.split(".")
        value = self._settings

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def set(self, key: str, value: Any) -> None:
        """
        Set value

        Args:
            key: Setting key, supports dot-separated nested keys
            value: Setting value
        """
        keys = key.split(".")
        target = self._settings

        for k in keys[:-1]:
            if k not in target:
                target[k] = {}
            target = target[k]

        target[keys[-1]] = value
        self.logger.debug(f"Setting updated: {key} = {value}")

    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get configuration section

        Args:
            section: Configuration section name

        Returns:
            dict: Configuration section dictionary
        """
        return self._settings.get(section, {})

    def set_section(self, section: str, values: Dict[str, Any]) -> None:
        """
        Set configuration section

        Args:
            section: Configuration section name
            values: Configuration values dictionary
        """
        self._settings[section] = values
        self.logger.debug(f"Configuration section updated: {section}")

    def update_section(self, section: str, values: Dict[str, Any]) -> None:
        """
        Update configuration section

        Args:
            section: Configuration section name
            values: Configuration values dictionary
        """
        if section not in self._settings:
            self._settings[section] = {}

        self._settings[section].update(values)
        self.logger.debug(f"Configuration section updated: {section}")

    def remove(self, key: str) -> bool:
        """
        Delete setting

        Args:
            key: Setting key

        Returns:
            bool: Whether deletion was successful
        """
        keys = key.split(".")
        target = self._settings

        try:
            for k in keys[:-1]:
                target = target[k]

            if keys[-1] in target:
                del target[keys[-1]]
                self.logger.debug(f"Setting deleted: {key}")
                return True
            return False
        except (KeyError, TypeError):
            return False

    def validate(self) -> List[str]:
        """
        Validate settings

        Returns:
            list: List of validation errors
        """
        errors = []

        # Validate required settings
        required_settings = [
            "project.name",
            "project.version",
            "logging.level",
            "geo.default_crs",
        ]

        for setting in required_settings:
            if self.get(setting) is None:
                errors.append(f"Missing required setting: {setting}")

        # Validate log level
        log_level = self.get("logging.level")
        if log_level not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            errors.append(f"Invalid log level: {log_level}")

        # Validate CRS format
        crs = self.get("geo.default_crs")
        if crs and not (crs.startswith("EPSG:") or crs.startswith("+proj=")):
            errors.append(f"Invalid CRS format: {crs}")

        # Validate directory paths
        directories = ["data.input_dir", "data.output_dir", "data.temp_dir"]

        for dir_setting in directories:
            dir_path = self.get(dir_setting)
            if dir_path:
                try:
                    Path(dir_path)
                except Exception:
                    errors.append(f"Invalid directory path: {dir_setting} = {dir_path}")

        return errors

    def create_directories(self) -> None:
        """Create directories specified in configuration."""
        directories = [
            self.get("data.input_dir"),
            self.get("data.output_dir"),
            self.get("data.temp_dir"),
            Path(self.get("logging.file")).parent if self.get("logging.file") else None,
        ]

        for dir_path in directories:
            if dir_path:
                try:
                    Path(dir_path).mkdir(parents=True, exist_ok=True)
                    self.logger.debug(f"Directory created successfully: {dir_path}")
                except Exception as e:
                    self.logger.warning(f"Failed to create directory: {dir_path}, {e}")

    def setup_logging(self) -> None:
        """Setup logging according to configuration."""
        log_level = self.get("logging.level", "INFO")
        log_format = self.get("logging.format")
        log_file = self.get("logging.file")

        # Create log directory
        if log_file:
            Path(log_file).parent.mkdir(parents=True, exist_ok=True)

        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format,
            filename=log_file,
            filemode="a",
            encoding="utf-8",
        )

        # Also output to console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, log_level))
        console_handler.setFormatter(logging.Formatter(log_format))

        root_logger = logging.getLogger()
        if not any(isinstance(h, logging.StreamHandler) for h in root_logger.handlers):
            root_logger.addHandler(console_handler)

        self.logger.info("Logging configuration completed")

    def get_all_settings(self) -> Dict[str, Any]:
        """Get all settings.

        Returns:
            dict: All settings dictionary
        """
        return self._settings.copy()

    def reset_to_defaults(self) -> None:
        """Reset to default settings."""
        self._load_default_settings()
        self.logger.info("Settings have been reset to default values")

    def export_template(
        self, file_path: Union[str, Path], file_format: str = "yaml"
    ) -> None:
        """Export configuration template.

        Args:
            file_path: Template file path
            file_format: File format
        """
        template_settings = self._settings.copy()

        # Add comment information
        template_settings["_comments"] = {
            "project": "Basic project information",
            "logging": "Logging configuration",
            "geo": "Geospatial processing configuration",
            "qgis": "QGIS integration configuration",
            "environment": "Environment configuration",
            "data": "Data directory configuration",
            "processing": "Processing parameters configuration",
        }

        # Temporarily save current settings
        current_settings = self._settings
        self._settings = template_settings

        try:
            self.save_to_file(file_path, file_format)
            self.logger.info(
                f"Configuration template exported successfully: {file_path}"
            )
        finally:
            # Restore current settings
            self._settings = current_settings


class ConfigManager:
    """Configuration manager for the Connect project.
    
    Provides centralized access to all configuration files and environment variables
    with proper priority handling and hot reload capabilities.
    """
    
    def __init__(self, config_root: Optional[Union[str, Path]] = None):
        """Initialize configuration manager.
        
        Args:
            config_root: Root directory for configuration files
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config_root = Path(config_root) if config_root else Path.cwd() / "config"
        self.src_config_root = Path.cwd() / "src" / "config"
        
        # Configuration cache
        self._config_cache = {}
        self._file_timestamps = {}
        self._settings_instance = None
        
        # Environment detection
        self.environment = self._detect_environment()
        
        # Load all configurations
        self._load_all_configs()
        
    def _detect_environment(self) -> str:
        """Detect current environment from environment variables.
        
        Returns:
            str: Environment name (development, testing, staging, production)
        """
        env = os.getenv('CONNECT_ENV', os.getenv('ENV', 'development')).lower()
        valid_envs = ['development', 'testing', 'staging', 'production']
        
        if env not in valid_envs:
            self.logger.warning(f"Unknown environment '{env}', defaulting to 'development'")
            env = 'development'
            
        self.logger.info(f"Detected environment: {env}")
        return env
        
    def _load_all_configs(self) -> None:
        """Load all configuration files with proper priority."""
        try:
            # 1. Load base configuration
            self._load_config_file('base', self.config_root / 'base.yaml')
            
            # 2. Load database configuration
            self._load_config_file('database', self.config_root / 'database.yaml')
            
            # 3. Load main settings
            self._load_config_file('settings', self.config_root / 'settings.yaml')
            
            # 4. Load monitoring configuration
            monitoring_file = self.config_root / 'monitoring_config.json'
            if monitoring_file.exists():
                self._load_config_file('monitoring', monitoring_file)
                
            # 5. Load environment-specific configuration
            env_file = self.config_root / 'environments' / f'{self.environment}.yaml'
            if env_file.exists():
                self._load_config_file('environment', env_file)
                
            # 6. Initialize Settings instance for backward compatibility
            self._settings_instance = Settings(self.src_config_root)
            
            # 7. Apply environment variable overrides
            self._apply_env_overrides()
            
            self.logger.info("All configuration files loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load configurations: {e}")
            raise
            
    def _load_config_file(self, config_name: str, file_path: Path) -> None:
        """Load a single configuration file.
        
        Args:
            config_name: Configuration identifier
            file_path: Path to configuration file
        """
        if not file_path.exists():
            self.logger.warning(f"Configuration file not found: {file_path}")
            return
            
        try:
            # Check file timestamp for hot reload
            current_timestamp = file_path.stat().st_mtime
            if (config_name in self._file_timestamps and 
                self._file_timestamps[config_name] == current_timestamp):
                return  # File hasn't changed
                
            # Load configuration based on file extension
            suffix = file_path.suffix.lower()
            if suffix in ['.yaml', '.yml']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
            elif suffix == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
            else:
                self.logger.warning(f"Unsupported config file format: {file_path}")
                return
                
            self._config_cache[config_name] = config_data
            self._file_timestamps[config_name] = current_timestamp
            
            self.logger.debug(f"Loaded configuration: {config_name} from {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to load config file {file_path}: {e}")
            
    def _apply_env_overrides(self) -> None:
        """Apply environment variable overrides with enhanced telecommunications support."""
        env_prefix = 'CONNECT_'
        overrides_applied = 0

        # Define environment variable mappings for telecommunications
        env_mappings = {
            'DATABASE_HOST': 'database.host',
            'DATABASE_PORT': 'database.port',
            'DATABASE_NAME': 'database.name',
            'DATABASE_USER': 'database.user',
            'DATABASE_PASSWORD': 'database.password',
            'DATABASE_SSL_MODE': 'database.ssl_mode',
            'DATABASE_POOL_MIN_SIZE': 'database.pool.min_size',
            'DATABASE_POOL_MAX_SIZE': 'database.pool.max_size',
            'DATABASE_POOL_TIMEOUT': 'database.pool.timeout',
            'CDR_BATCH_SIZE': 'telecom.cdr.batch_size',
            'EP_BATCH_SIZE': 'telecom.ep.batch_size',
            'KPI_CALCULATION_INTERVAL': 'telecom.kpi.calculation_interval',
            'KPI_RETENTION_DAYS': 'telecom.kpi.retention_days',
            'MAX_MEMORY_USAGE_MB': 'telecom.performance.max_memory_usage_mb',
            'PROCESSING_TIMEOUT_SECONDS': 'telecom.performance.processing_timeout_seconds',
            'PARALLEL_WORKERS': 'telecom.performance.parallel_workers',
            'JWT_SECRET_KEY': 'security.jwt.secret_key',
            'JWT_EXPIRE_MINUTES': 'security.jwt.access_token_expire_minutes',
            'LOG_LEVEL': 'logging.level',
            'LOG_FILE': 'logging.file',
            'QGIS_PATH': 'qgis.path',
            'QGIS_AUTO_DETECT': 'qgis.auto_detect',
            'MONITORING_ENABLED': 'monitoring.enabled',
            'PERFORMANCE_MONITORING': 'monitoring.performance_monitoring',
        }

        for key, value in os.environ.items():
            if key.startswith(env_prefix):
                # Remove prefix and check for direct mapping
                env_key = key[len(env_prefix):]

                if env_key in env_mappings:
                    config_key = env_mappings[env_key]
                else:
                    # Convert CONNECT_DATABASE_HOST to database.host (fallback)
                    config_key = env_key.lower().replace('_', '.')

                # Parse value with type conversion
                parsed_value = self._parse_env_value(value, config_key)

                self._set_nested_value(config_key, parsed_value)
                overrides_applied += 1
                self.logger.debug(f"Applied environment override: {config_key} = {parsed_value}")

        if overrides_applied > 0:
            self.logger.info(f"Applied {overrides_applied} environment variable overrides")

    def _parse_env_value(self, value: str, config_key: str) -> Any:
        """Parse environment variable value with intelligent type conversion."""
        # Boolean values
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'

        # Integer values (based on config key patterns)
        if any(pattern in config_key for pattern in ['port', 'size', 'timeout', 'interval', 'days', 'workers', 'minutes']):
            try:
                return int(value)
            except ValueError:
                pass

        # Float values (based on config key patterns)
        if any(pattern in config_key for pattern in ['threshold', 'usage', 'rate']):
            try:
                return float(value)
            except ValueError:
                pass

        # Try JSON parsing for complex values
        try:
            return json.loads(value)
        except (json.JSONDecodeError, ValueError):
            return value
            
    def _set_nested_value(self, key_path: str, value: Any) -> None:
        """Set nested configuration value.
        
        Args:
            key_path: Dot-separated key path (e.g., 'database.host')
            value: Value to set
        """
        keys = key_path.split('.')
        
        # Determine which config section this belongs to
        config_section = keys[0]
        if config_section not in self._config_cache:
            self._config_cache[config_section] = {}
            
        target = self._config_cache[config_section]
        
        # Navigate to the target location
        for key in keys[1:-1]:
            if key not in target:
                target[key] = {}
            target = target[key]
            
        # Set the final value
        if len(keys) > 1:
            target[keys[-1]] = value
        else:
            self._config_cache[config_section] = value
            
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value with dot notation.
        
        Args:
            key_path: Dot-separated key path (e.g., 'database.host')
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key_path.split('.')
        
        # Try to find in cached configurations
        for config_name, config_data in self._config_cache.items():
            if not isinstance(config_data, dict):
                continue
                
            current = config_data
            try:
                for key in keys:
                    current = current[key]
                return current
            except (KeyError, TypeError):
                continue
                
        # Fallback to Settings instance for backward compatibility
        if self._settings_instance:
            return self._settings_instance.get(key_path, default)
            
        return default
        
    def get_section(self, section_name: str) -> Dict[str, Any]:
        """Get entire configuration section.
        
        Args:
            section_name: Section name
            
        Returns:
            Configuration section dictionary
        """
        if section_name in self._config_cache:
            return self._config_cache[section_name].copy()
            
        # Try to find section in any config
        for config_data in self._config_cache.values():
            if isinstance(config_data, dict) and section_name in config_data:
                return config_data[section_name].copy()
                
        return {}
        
    def reload_configs(self) -> None:
        """Reload all configuration files (hot reload)."""
        self.logger.info("Reloading all configuration files...")
        self._file_timestamps.clear()  # Force reload
        self._load_all_configs()
        
    def get_environment(self) -> str:
        """Get current environment.
        
        Returns:
            Current environment name
        """
        return self.environment
        
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == 'development'
        
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == 'production'
        
    def get_all_configs(self) -> Dict[str, Any]:
        """Get all loaded configurations.
        
        Returns:
            Dictionary of all configurations
        """
        return self._config_cache.copy()


# Global configuration manager instance
_config_manager = None


def get_config() -> ConfigManager:
    """Get global configuration manager instance.
    
    Returns:
        ConfigManager: Global configuration manager
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def reload_config() -> None:
    """Reload global configuration."""
    global _config_manager
    if _config_manager is not None:
        _config_manager.reload_configs()


# Backward compatibility aliases
SettingsManager = Settings  # Alias for backward compatibility
