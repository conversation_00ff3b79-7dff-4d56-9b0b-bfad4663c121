"""Query validation classes for SQL security and correctness.

This module provides validators for ensuring SQL queries are safe,
correct, and follow best practices.
"""

import re
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional, Pattern, Set

from loguru import logger

from ..exceptions import SecurityError, ValidationError
from .dialects import Dialect


class ValidationLevel(Enum):
    """Validation strictness levels."""

    STRICT = "strict"
    MODERATE = "moderate"
    PERMISSIVE = "permissive"


class SecurityLevel(Enum):
    """Security check levels."""

    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class Validator(ABC):
    """Abstract base class for query validators."""

    def __init__(self, level: ValidationLevel = ValidationLevel.MODERATE):
        """Initialize validator.

        Args:
            level: Validation level
        """
        self.level = level

    @abstractmethod
    def validate(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dialect: Optional[Dialect] = None,
    ) -> bool:
        """Validate query.

        Args:
            query: SQL query
            parameters: Query parameters
            dialect: SQL dialect

        Returns:
            True if valid

        Raises:
            ValidationError: If validation fails
        """
        pass


class SQLInjectionValidator(Validator):
    """Validator for SQL injection prevention."""

    def __init__(self, security_level: SecurityLevel = SecurityLevel.HIGH):
        """Initialize SQL injection validator.

        Args:
            security_level: Security check level
        """
        super().__init__(ValidationLevel.STRICT)
        self.security_level = security_level

        # Dangerous patterns that could indicate SQL injection
        self.dangerous_patterns = [
            # Union-based injection
            re.compile(r"\bunion\s+select\b", re.IGNORECASE),
            re.compile(r"\bunion\s+all\s+select\b", re.IGNORECASE),
            # Comment patterns
            re.compile(r"--\s*$", re.MULTILINE),
            re.compile(r"/\*.*?\*/", re.DOTALL),
            re.compile(r"#.*$", re.MULTILINE),
            # Boolean-based injection
            # Tautology patterns
            re.compile(r"\bor\s+1\s*=\s*1\b", re.IGNORECASE),
            re.compile(r"\band\s+1\s*=\s*1\b", re.IGNORECASE),
            re.compile(r"\bor\s+true\b", re.IGNORECASE),
            re.compile(r"\band\s+false\b", re.IGNORECASE),
            # Time-based injection
            re.compile(r"\bwaitfor\s+delay\b", re.IGNORECASE),
            re.compile(r"\bsleep\s*\(", re.IGNORECASE),
            re.compile(r"\bbenchmark\s*\(", re.IGNORECASE),
            # Information schema access
            re.compile(r"\binformation_schema\b", re.IGNORECASE),
            re.compile(r"\bsys\.\w+", re.IGNORECASE),
            re.compile(r"\bpg_\w+", re.IGNORECASE),
            # File operations
            re.compile(r"\bload_file\s*\(", re.IGNORECASE),
            re.compile(r"\binto\s+outfile\b", re.IGNORECASE),
            re.compile(r"\binto\s+dumpfile\b", re.IGNORECASE),
            # Multiple statements
            re.compile(r";\s*\w+", re.IGNORECASE),
            # Hex encoding attempts
            re.compile(r"0x[0-9a-f]+", re.IGNORECASE),
            # Char/ASCII function abuse
            re.compile(r"\bchar\s*\(", re.IGNORECASE),
            re.compile(r"\bascii\s*\(", re.IGNORECASE),
        ]

        # Suspicious keywords
        self.suspicious_keywords = {
            "drop",
            "delete",
            "truncate",
            "alter",
            "create",
            "insert",
            "update",
            "grant",
            "revoke",
            "exec",
            "execute",
            "sp_",
            "xp_",
            "cmdshell",
            "openrowset",
            "opendatasource",
        }

    def validate(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dialect: Optional[Dialect] = None,
    ) -> bool:
        """Validate query for SQL injection.

        Args:
            query: SQL query
            parameters: Query parameters
            dialect: SQL dialect

        Returns:
            True if safe

        Raises:
            SecurityError: If potential injection detected
        """
        try:
            # Check for dangerous patterns
            for pattern in self.dangerous_patterns:
                if pattern.search(query):
                    raise SecurityError(
                        f"Potential SQL injection detected: {pattern.pattern}"
                    )

            # Check for suspicious keywords in high security mode
            if self.security_level == SecurityLevel.HIGH:
                query_lower = query.lower()
                for keyword in self.suspicious_keywords:
                    if keyword in query_lower:
                        logger.warning(f"Suspicious keyword detected: {keyword}")

            # Validate parameters if provided
            if parameters:
                self._validate_parameters(parameters)

            # Check for unparameterized queries
            if self.security_level in [SecurityLevel.HIGH, SecurityLevel.MEDIUM]:
                self._check_parameterization(query, parameters)

            return True

        except SecurityError:
            raise
        except Exception as e:
            logger.error(f"SQL injection validation failed: {e}")
            raise ValidationError(f"Validation error: {e}")

    def _validate_parameters(self, parameters: Dict[str, Any]):
        """Validate query parameters.

        Args:
            parameters: Query parameters

        Raises:
            SecurityError: If parameters are suspicious
        """
        for key, value in parameters.items():
            if isinstance(value, str):
                # Check for injection patterns in parameter values
                for pattern in self.dangerous_patterns:
                    if pattern.search(value):
                        raise SecurityError(f"Suspicious parameter value: {key}")

    def _check_parameterization(self, query: str, parameters: Optional[Dict[str, Any]]):
        """Check if query is properly parameterized.

        Args:
            query: SQL query
            parameters: Query parameters

        Raises:
            SecurityError: If query appears to have inline values
        """
        # Look for potential inline string values
        string_literals = re.findall(r"'[^']*'", query)
        if string_literals and not parameters:
            logger.warning("Query contains string literals but no parameters provided")

        # Look for potential inline numeric values in WHERE clauses
        where_numbers = re.findall(r"\bwhere\s+\w+\s*=\s*\d+", query, re.IGNORECASE)
        if where_numbers and not parameters:
            logger.warning("Query contains hardcoded values in WHERE clause")


class SyntaxValidator(Validator):
    """Validator for SQL syntax correctness."""

    def __init__(self, level: ValidationLevel = ValidationLevel.MODERATE):
        """Initialize syntax validator.

        Args:
            level: Validation level
        """
        super().__init__(level)

        # Basic SQL keywords
        self.sql_keywords = {
            "select",
            "from",
            "where",
            "group",
            "by",
            "having",
            "order",
            "limit",
            "offset",
            "insert",
            "into",
            "values",
            "update",
            "set",
            "delete",
            "join",
            "inner",
            "left",
            "right",
            "full",
            "outer",
            "on",
            "as",
            "and",
            "or",
            "not",
            "in",
            "exists",
            "between",
            "like",
            "is",
            "null",
            "distinct",
            "union",
            "all",
            "case",
            "when",
            "then",
            "else",
            "end",
            "with",
            "recursive",
        }

    def validate(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dialect: Optional[Dialect] = None,
    ) -> bool:
        """Validate query syntax.

        Args:
            query: SQL query
            parameters: Query parameters
            dialect: SQL dialect

        Returns:
            True if syntax is valid

        Raises:
            ValidationError: If syntax is invalid
        """
        try:
            # Basic structure validation
            self._validate_basic_structure(query)

            # Parentheses matching
            self._validate_parentheses(query)

            # Quote matching
            self._validate_quotes(query)

            # Keyword validation
            if self.level == ValidationLevel.STRICT:
                self._validate_keywords(query)

            return True

        except Exception as e:
            logger.error(f"Syntax validation failed: {e}")
            raise ValidationError(f"Syntax error: {e}")

    def _validate_basic_structure(self, query: str):
        """Validate basic query structure.

        Args:
            query: SQL query

        Raises:
            ValidationError: If structure is invalid
        """
        query_stripped = query.strip()
        if not query_stripped:
            raise ValidationError("Empty query")

        # Check for basic SQL statement start
        first_word = query_stripped.split()[0].lower()
        valid_starts = {
            "select",
            "insert",
            "update",
            "delete",
            "with",
            "create",
            "drop",
            "alter",
        }

        if self.level == ValidationLevel.STRICT and first_word not in valid_starts:
            raise ValidationError(f"Invalid query start: {first_word}")

    def _validate_parentheses(self, query: str):
        """Validate parentheses matching.

        Args:
            query: SQL query

        Raises:
            ValidationError: If parentheses don't match
        """
        stack = []
        for char in query:
            if char == "(":
                stack.append(char)
            elif char == ")":
                if not stack:
                    raise ValidationError("Unmatched closing parenthesis")
                stack.pop()

        if stack:
            raise ValidationError("Unmatched opening parenthesis")

    def _validate_quotes(self, query: str):
        """Validate quote matching.

        Args:
            query: SQL query

        Raises:
            ValidationError: If quotes don't match
        """
        single_quote_count = query.count("'")
        double_quote_count = query.count('"')

        if single_quote_count % 2 != 0:
            raise ValidationError("Unmatched single quotes")

        if double_quote_count % 2 != 0:
            raise ValidationError("Unmatched double quotes")

    def _validate_keywords(self, query: str):
        """Validate SQL keywords usage.

        Args:
            query: SQL query

        Raises:
            ValidationError: If keywords are misused
        """
        # This is a basic implementation
        # In practice, you might want to use a proper SQL parser
        words = re.findall(r"\b\w+\b", query.lower())

        # Check for SELECT without FROM (except for simple expressions)
        if "select" in words and "from" not in words:
            # Allow simple SELECT expressions like SELECT 1, SELECT NOW()
            if not re.search(r"select\s+\d+|select\s+\w+\s*\(", query, re.IGNORECASE):
                logger.warning("SELECT without FROM clause detected")


class PerformanceValidator(Validator):
    """Validator for query performance concerns."""

    def __init__(self, level: ValidationLevel = ValidationLevel.MODERATE):
        """Initialize performance validator.

        Args:
            level: Validation level
        """
        super().__init__(level)

        # Performance anti-patterns
        self.performance_issues = [
            # SELECT *
            (
                re.compile(r"\bselect\s+\*\s+from\b", re.IGNORECASE),
                "SELECT * can be inefficient",
            ),
            # Missing WHERE clause
            (
                re.compile(r"\bselect\b(?!.*\bwhere\b)", re.IGNORECASE | re.DOTALL),
                "Query without WHERE clause may return too many rows",
            ),
            # LIKE with leading wildcard
            (
                re.compile(r"\blike\s+'%\w", re.IGNORECASE),
                "LIKE with leading wildcard prevents index usage",
            ),
            # Functions in WHERE clause
            (
                re.compile(r"\bwhere\s+\w+\s*\([^)]*\)\s*=", re.IGNORECASE),
                "Functions in WHERE clause prevent index usage",
            ),
            # OR conditions
            (
                re.compile(r"\bwhere\s+.*\bor\b", re.IGNORECASE),
                "OR conditions can be inefficient",
            ),
            # Subqueries that could be JOINs
            (
                re.compile(r"\bwhere\s+\w+\s+in\s*\(\s*select\b", re.IGNORECASE),
                "IN subquery could potentially be rewritten as JOIN",
            ),
        ]

    def validate(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dialect: Optional[Dialect] = None,
    ) -> bool:
        """Validate query for performance issues.

        Args:
            query: SQL query
            parameters: Query parameters
            dialect: SQL dialect

        Returns:
            True if no major performance issues

        Raises:
            ValidationError: If serious performance issues detected
        """
        try:
            warnings = []
            errors = []

            for pattern, message in self.performance_issues:
                if pattern.search(query):
                    if self.level == ValidationLevel.STRICT:
                        errors.append(message)
                    else:
                        warnings.append(message)

            # Log warnings
            for warning in warnings:
                logger.warning(f"Performance warning: {warning}")

            # Raise errors if in strict mode
            if errors:
                raise ValidationError(f"Performance issues: {'; '.join(errors)}")

            return True

        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Performance validation failed: {e}")
            raise ValidationError(f"Performance validation error: {e}")


class CompositeValidator(Validator):
    """Composite validator that runs multiple validators."""

    def __init__(
        self,
        validators: List[Validator],
        level: ValidationLevel = ValidationLevel.MODERATE,
    ):
        """Initialize composite validator.

        Args:
            validators: List of validators to run
            level: Validation level
        """
        super().__init__(level)
        self.validators = validators

    def validate(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None,
        dialect: Optional[Dialect] = None,
    ) -> bool:
        """Run all validators.

        Args:
            query: SQL query
            parameters: Query parameters
            dialect: SQL dialect

        Returns:
            True if all validators pass

        Raises:
            ValidationError: If any validator fails
        """
        errors = []

        for validator in self.validators:
            try:
                validator.validate(query, parameters, dialect)
            except (ValidationError, SecurityError) as e:
                errors.append(str(e))

        if errors:
            raise ValidationError(f"Validation failed: {'; '.join(errors)}")

        return True

    def add_validator(self, validator: Validator):
        """Add a validator.

        Args:
            validator: Validator to add
        """
        self.validators.append(validator)

    def remove_validator(self, validator_type: type):
        """Remove validators of a specific type.

        Args:
            validator_type: Type of validator to remove
        """
        self.validators = [
            v for v in self.validators if not isinstance(v, validator_type)
        ]


# Convenience functions
def create_default_validator(
    security_level: SecurityLevel = SecurityLevel.HIGH,
    validation_level: ValidationLevel = ValidationLevel.MODERATE,
) -> CompositeValidator:
    """Create default validator with common validators.

    Args:
        security_level: Security check level
        validation_level: Validation level

    Returns:
        CompositeValidator instance
    """
    validators = [
        SQLInjectionValidator(security_level),
        SyntaxValidator(validation_level),
        PerformanceValidator(validation_level),
    ]

    return CompositeValidator(validators, validation_level)


def create_strict_validator() -> CompositeValidator:
    """Create strict validator for production use.

    Returns:
        CompositeValidator instance
    """
    return create_default_validator(SecurityLevel.HIGH, ValidationLevel.STRICT)


def create_permissive_validator() -> CompositeValidator:
    """Create permissive validator for development.

    Returns:
        CompositeValidator instance
    """
    return create_default_validator(SecurityLevel.MEDIUM, ValidationLevel.PERMISSIVE)


def validate_query(
    query: str,
    parameters: Optional[Dict[str, Any]] = None,
    dialect: Optional[Dialect] = None,
    security_level: SecurityLevel = SecurityLevel.HIGH,
    validation_level: ValidationLevel = ValidationLevel.MODERATE,
) -> bool:
    """Validate query with default settings.

    Args:
        query: SQL query
        parameters: Query parameters
        dialect: SQL dialect
        security_level: Security check level
        validation_level: Validation level

    Returns:
        True if valid

    Raises:
        ValidationError: If validation fails
        SecurityError: If security check fails
    """
    validator = create_default_validator(security_level, validation_level)
    return validator.validate(query, parameters, dialect)
