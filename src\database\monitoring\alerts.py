"""Manages alerting rules and notifications for database monitoring."""
from enum import Enum
from typing import List, Dict, Any

class AlertSeverity(Enum):
    """Defines the severity levels for alerts."""
    INFO = "INFO"
    WARNING = "WARNING"
    CRITICAL = "CRITICAL"

class AlertRule:
    """Represents a single alerting rule."""
    def __init__(self, name: str, condition: str, severity: AlertSeverity, description: str = ""):
        """Initializes an AlertRule.

        Args:
            name (str): The name of the alert rule.
            condition (str): The condition that triggers the alert (e.g., 'cpu_usage > 0.9').
            severity (AlertSeverity): The severity of the alert.
            description (str, optional): A description of the alert rule. Defaults to "".
        """
        self.name = name
        self.condition = condition
        self.severity = severity
        self.description = description

    def evaluate(self, metrics: Dict[str, Any]) -> bool:
        """Evaluates the alert rule against the given metrics.

        Args:
            metrics (Dict[str, Any]): A dictionary of current system metrics.

        Returns:
            bool: True if the alert condition is met, False otherwise.
        """
        # Placeholder for actual condition evaluation logic
        # This would typically involve parsing the condition string and checking against metrics
        # For example, if condition is 'cpu_usage > 0.9' and metrics is {'cpu_usage': 0.95}
        if "cpu_usage" in self.condition and "cpu_usage" in metrics:
            if self.condition.startswith("cpu_usage >"):
                try:
                    threshold = float(self.condition.split(">")[1].strip())
                    return metrics["cpu_usage"] > threshold
                except ValueError:
                    return False
        return False

class AlertManager:
    """Manages a collection of alert rules and triggers notifications."""
    def __init__(self, rules: List[AlertRule]):
        """Initializes the AlertManager with a list of alert rules.

        Args:
            rules (List[AlertRule]): A list of alert rules to manage.
        """
        self.rules = rules
        self.active_alerts: Dict[str, AlertRule] = {}

    def check_alerts(self, metrics: Dict[str, Any]):
        """Checks all alert rules against the current metrics and triggers notifications.

        Args:
            metrics (Dict[str, Any]): A dictionary of current system metrics.
        """
        for rule in self.rules:
            if rule.evaluate(metrics):
                if rule.name not in self.active_alerts:
                    self.active_alerts[rule.name] = rule
                    self.send_notification(rule)
            elif rule.name in self.active_alerts:
                # Condition no longer met, resolve alert (optional)
                del self.active_alerts[rule.name]
                self.send_resolution_notification(rule)

    def send_notification(self, rule: AlertRule):
        """Sends a notification for an triggered alert.

        Args:
            rule (AlertRule): The alert rule that was triggered.
        """
        # Placeholder for notification logic (e.g., email, Slack, PagerDuty)
        print(f"ALERT TRIGGERED: {rule.name} (Severity: {rule.severity.value}) - {rule.description}")

    def send_resolution_notification(self, rule: AlertRule):
        """Sends a notification for a resolved alert.

        Args:
            rule (AlertRule): The alert rule that was resolved.
        """
        # Placeholder for resolution notification logic
        print(f"ALERT RESOLVED: {rule.name}")