<!DOCTYPE qgis PUBLIC 'http://mrcc.com/qgis.dtd' 'SYSTEM'>
<qgis simplifyAlgorithm="0" simplifyDrawingHints="0" simplifyLocal="1" simplifyMaxScale="1" version="3.28.5-Firenze" simplifyDrawingTol="1" styleCategories="AllStyleCategories" hasScaleBasedVisibilityFlag="0" minScale="100000000" symbologyReferenceScale="-1" maxScale="0" labelsEnabled="1" readOnly="0">
  <flags>
    <Identifiable>1</Identifiable>
    <Removable>1</Removable>
    <Searchable>1</Searchable>
    <Private>0</Private>
  </flags>
  <temporal startField="" enabled="0" durationUnit="min" durationField="" fixedDuration="0" mode="0" endField="" startExpression="" accumulate="0" limitMode="0" endExpression="">
    <fixedRange>
      <start></start>
      <end></end>
    </fixedRange>
  </temporal>
  <elevation showMarkerSymbolInSurfacePlots="0" extrusionEnabled="0" clamping="Terrain" zscale="1" type="IndividualFeatures" zoffset="0" extrusion="0" binding="Centroid" respectLayerSymbol="1" symbology="Line">
    <data-defined-properties>
      <Option type="Map">
        <Option type="QString" name="name" value=""/>
        <Option name="properties"/>
        <Option type="QString" name="type" value="collection"/>
      </Option>
    </data-defined-properties>
    <profileLineSymbol>
      <symbol type="line" is_animated="0" alpha="1" name="" force_rhr="0" clip_to_extent="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </data_defined_properties>
        <layer locked="0" enabled="1" pass="0" class="SimpleLine">
          <Option type="Map">
            <Option type="QString" name="align_dash_pattern" value="0"/>
            <Option type="QString" name="capstyle" value="square"/>
            <Option type="QString" name="customdash" value="5;2"/>
            <Option type="QString" name="customdash_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="customdash_unit" value="MM"/>
            <Option type="QString" name="dash_pattern_offset" value="0"/>
            <Option type="QString" name="dash_pattern_offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="dash_pattern_offset_unit" value="MM"/>
            <Option type="QString" name="draw_inside_polygon" value="0"/>
            <Option type="QString" name="joinstyle" value="bevel"/>
            <Option type="QString" name="line_color" value="196,60,57,255"/>
            <Option type="QString" name="line_style" value="solid"/>
            <Option type="QString" name="line_width" value="0.6"/>
            <Option type="QString" name="line_width_unit" value="MM"/>
            <Option type="QString" name="offset" value="0"/>
            <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="offset_unit" value="MM"/>
            <Option type="QString" name="ring_filter" value="0"/>
            <Option type="QString" name="trim_distance_end" value="0"/>
            <Option type="QString" name="trim_distance_end_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="trim_distance_end_unit" value="MM"/>
            <Option type="QString" name="trim_distance_start" value="0"/>
            <Option type="QString" name="trim_distance_start_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="trim_distance_start_unit" value="MM"/>
            <Option type="QString" name="tweak_dash_pattern_on_corners" value="0"/>
            <Option type="QString" name="use_custom_dash" value="0"/>
            <Option type="QString" name="width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
    </profileLineSymbol>
    <profileFillSymbol>
      <symbol type="fill" is_animated="0" alpha="1" name="" force_rhr="0" clip_to_extent="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </data_defined_properties>
        <layer locked="0" enabled="1" pass="0" class="SimpleFill">
          <Option type="Map">
            <Option type="QString" name="border_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="color" value="196,60,57,255"/>
            <Option type="QString" name="joinstyle" value="bevel"/>
            <Option type="QString" name="offset" value="0,0"/>
            <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="offset_unit" value="MM"/>
            <Option type="QString" name="outline_color" value="140,43,41,255"/>
            <Option type="QString" name="outline_style" value="solid"/>
            <Option type="QString" name="outline_width" value="0.2"/>
            <Option type="QString" name="outline_width_unit" value="MM"/>
            <Option type="QString" name="style" value="solid"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
    </profileFillSymbol>
    <profileMarkerSymbol>
      <symbol type="marker" is_animated="0" alpha="1" name="" force_rhr="0" clip_to_extent="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </data_defined_properties>
        <layer locked="0" enabled="1" pass="0" class="SimpleMarker">
          <Option type="Map">
            <Option type="QString" name="angle" value="0"/>
            <Option type="QString" name="cap_style" value="square"/>
            <Option type="QString" name="color" value="196,60,57,255"/>
            <Option type="QString" name="horizontal_anchor_point" value="1"/>
            <Option type="QString" name="joinstyle" value="bevel"/>
            <Option type="QString" name="name" value="diamond"/>
            <Option type="QString" name="offset" value="0,0"/>
            <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="offset_unit" value="MM"/>
            <Option type="QString" name="outline_color" value="140,43,41,255"/>
            <Option type="QString" name="outline_style" value="solid"/>
            <Option type="QString" name="outline_width" value="0.2"/>
            <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="outline_width_unit" value="MM"/>
            <Option type="QString" name="scale_method" value="diameter"/>
            <Option type="QString" name="size" value="3"/>
            <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="size_unit" value="MM"/>
            <Option type="QString" name="vertical_anchor_point" value="1"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
    </profileMarkerSymbol>
  </elevation>
  <renderer-v2 type="categorizedSymbol" forceraster="0" enableorderby="0" symbollevels="0" attr="Tech Active" referencescale="-1">
    <categories>
      <category type="string" label="G" value="G" symbol="0" render="true"/>
      <category type="string" label="L" value="L" symbol="1" render="true"/>
      <category type="string" label="GL" value="GL" symbol="2" render="true"/>
      <category type="string" label="LN" value="LN" symbol="3" render="true"/>
      <category type="string" label="GLN" value="GLN" symbol="4" render="true"/>
      <category type="string" label="Not active or not in Huawei regions" value="" symbol="5" render="true"/>
    </categories>
    <symbols>
      <symbol type="marker" is_animated="0" alpha="1" name="0" force_rhr="0" clip_to_extent="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </data_defined_properties>
        <layer locked="0" enabled="1" pass="0" class="SimpleMarker">
          <Option type="Map">
            <Option type="QString" name="angle" value="0"/>
            <Option type="QString" name="cap_style" value="square"/>
            <Option type="QString" name="color" value="255,255,0,255"/>
            <Option type="QString" name="horizontal_anchor_point" value="1"/>
            <Option type="QString" name="joinstyle" value="bevel"/>
            <Option type="QString" name="name" value="circle"/>
            <Option type="QString" name="offset" value="0,0"/>
            <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="offset_unit" value="MM"/>
            <Option type="QString" name="outline_color" value="35,35,35,255"/>
            <Option type="QString" name="outline_style" value="solid"/>
            <Option type="QString" name="outline_width" value="0"/>
            <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="outline_width_unit" value="MM"/>
            <Option type="QString" name="scale_method" value="diameter"/>
            <Option type="QString" name="size" value="2"/>
            <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="size_unit" value="MM"/>
            <Option type="QString" name="vertical_anchor_point" value="1"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
      <symbol type="marker" is_animated="0" alpha="1" name="1" force_rhr="0" clip_to_extent="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </data_defined_properties>
        <layer locked="0" enabled="1" pass="0" class="SimpleMarker">
          <Option type="Map">
            <Option type="QString" name="angle" value="0"/>
            <Option type="QString" name="cap_style" value="square"/>
            <Option type="QString" name="color" value="255,255,0,255"/>
            <Option type="QString" name="horizontal_anchor_point" value="1"/>
            <Option type="QString" name="joinstyle" value="bevel"/>
            <Option type="QString" name="name" value="circle"/>
            <Option type="QString" name="offset" value="0,0"/>
            <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="offset_unit" value="MM"/>
            <Option type="QString" name="outline_color" value="35,35,35,255"/>
            <Option type="QString" name="outline_style" value="solid"/>
            <Option type="QString" name="outline_width" value="0"/>
            <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="outline_width_unit" value="MM"/>
            <Option type="QString" name="scale_method" value="diameter"/>
            <Option type="QString" name="size" value="2"/>
            <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="size_unit" value="MM"/>
            <Option type="QString" name="vertical_anchor_point" value="1"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
      <symbol type="marker" is_animated="0" alpha="1" name="2" force_rhr="0" clip_to_extent="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </data_defined_properties>
        <layer locked="0" enabled="1" pass="0" class="SimpleMarker">
          <Option type="Map">
            <Option type="QString" name="angle" value="0"/>
            <Option type="QString" name="cap_style" value="square"/>
            <Option type="QString" name="color" value="255,255,0,255"/>
            <Option type="QString" name="horizontal_anchor_point" value="1"/>
            <Option type="QString" name="joinstyle" value="bevel"/>
            <Option type="QString" name="name" value="circle"/>
            <Option type="QString" name="offset" value="0,0"/>
            <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="offset_unit" value="MM"/>
            <Option type="QString" name="outline_color" value="35,35,35,255"/>
            <Option type="QString" name="outline_style" value="solid"/>
            <Option type="QString" name="outline_width" value="0"/>
            <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="outline_width_unit" value="MM"/>
            <Option type="QString" name="scale_method" value="diameter"/>
            <Option type="QString" name="size" value="2"/>
            <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="size_unit" value="MM"/>
            <Option type="QString" name="vertical_anchor_point" value="1"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
      <symbol type="marker" is_animated="0" alpha="1" name="3" force_rhr="0" clip_to_extent="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </data_defined_properties>
        <layer locked="0" enabled="1" pass="0" class="SimpleMarker">
          <Option type="Map">
            <Option type="QString" name="angle" value="0"/>
            <Option type="QString" name="cap_style" value="square"/>
            <Option type="QString" name="color" value="255,255,0,255"/>
            <Option type="QString" name="horizontal_anchor_point" value="1"/>
            <Option type="QString" name="joinstyle" value="bevel"/>
            <Option type="QString" name="name" value="circle"/>
            <Option type="QString" name="offset" value="0,0"/>
            <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="offset_unit" value="MM"/>
            <Option type="QString" name="outline_color" value="35,35,35,255"/>
            <Option type="QString" name="outline_style" value="solid"/>
            <Option type="QString" name="outline_width" value="0"/>
            <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="outline_width_unit" value="MM"/>
            <Option type="QString" name="scale_method" value="diameter"/>
            <Option type="QString" name="size" value="2"/>
            <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="size_unit" value="MM"/>
            <Option type="QString" name="vertical_anchor_point" value="1"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
      <symbol type="marker" is_animated="0" alpha="1" name="4" force_rhr="0" clip_to_extent="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </data_defined_properties>
        <layer locked="0" enabled="1" pass="0" class="SimpleMarker">
          <Option type="Map">
            <Option type="QString" name="angle" value="0"/>
            <Option type="QString" name="cap_style" value="square"/>
            <Option type="QString" name="color" value="255,255,0,255"/>
            <Option type="QString" name="horizontal_anchor_point" value="1"/>
            <Option type="QString" name="joinstyle" value="bevel"/>
            <Option type="QString" name="name" value="circle"/>
            <Option type="QString" name="offset" value="0,0"/>
            <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="offset_unit" value="MM"/>
            <Option type="QString" name="outline_color" value="35,35,35,255"/>
            <Option type="QString" name="outline_style" value="solid"/>
            <Option type="QString" name="outline_width" value="0"/>
            <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="outline_width_unit" value="MM"/>
            <Option type="QString" name="scale_method" value="diameter"/>
            <Option type="QString" name="size" value="2"/>
            <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="size_unit" value="MM"/>
            <Option type="QString" name="vertical_anchor_point" value="1"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
      <symbol type="marker" is_animated="0" alpha="1" name="5" force_rhr="0" clip_to_extent="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </data_defined_properties>
        <layer locked="0" enabled="1" pass="0" class="SimpleMarker">
          <Option type="Map">
            <Option type="QString" name="angle" value="0"/>
            <Option type="QString" name="cap_style" value="square"/>
            <Option type="QString" name="color" value="194,194,194,255"/>
            <Option type="QString" name="horizontal_anchor_point" value="1"/>
            <Option type="QString" name="joinstyle" value="bevel"/>
            <Option type="QString" name="name" value="circle"/>
            <Option type="QString" name="offset" value="0,0"/>
            <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="offset_unit" value="MM"/>
            <Option type="QString" name="outline_color" value="35,35,35,255"/>
            <Option type="QString" name="outline_style" value="solid"/>
            <Option type="QString" name="outline_width" value="0"/>
            <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="outline_width_unit" value="MM"/>
            <Option type="QString" name="scale_method" value="diameter"/>
            <Option type="QString" name="size" value="2"/>
            <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="size_unit" value="MM"/>
            <Option type="QString" name="vertical_anchor_point" value="1"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
    </symbols>
    <source-symbol>
      <symbol type="marker" is_animated="0" alpha="1" name="0" force_rhr="0" clip_to_extent="1" frame_rate="10">
        <data_defined_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </data_defined_properties>
        <layer locked="0" enabled="1" pass="0" class="SimpleMarker">
          <Option type="Map">
            <Option type="QString" name="angle" value="0"/>
            <Option type="QString" name="cap_style" value="square"/>
            <Option type="QString" name="color" value="248,251,24,255"/>
            <Option type="QString" name="horizontal_anchor_point" value="1"/>
            <Option type="QString" name="joinstyle" value="bevel"/>
            <Option type="QString" name="name" value="circle"/>
            <Option type="QString" name="offset" value="0,0"/>
            <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="offset_unit" value="MM"/>
            <Option type="QString" name="outline_color" value="35,35,35,255"/>
            <Option type="QString" name="outline_style" value="solid"/>
            <Option type="QString" name="outline_width" value="0"/>
            <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="outline_width_unit" value="MM"/>
            <Option type="QString" name="scale_method" value="diameter"/>
            <Option type="QString" name="size" value="2"/>
            <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            <Option type="QString" name="size_unit" value="MM"/>
            <Option type="QString" name="vertical_anchor_point" value="1"/>
          </Option>
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
        </layer>
      </symbol>
    </source-symbol>
    <rotation/>
    <sizescale/>
  </renderer-v2>
  <labeling type="simple">
    <settings calloutType="simple">
      <text-style fontLetterSpacing="0" fontWordSpacing="0" fontSizeMapUnitScale="3x:0,0,0,0,0,0" multilineHeight="1" namedStyle="Bold" fontStrikeout="0" textColor="0,0,0,255" fontFamily="MS Shell Dlg 2" textOrientation="horizontal" forcedItalic="0" forcedBold="0" fontSizeUnit="Point" fontWeight="75" blendMode="0" fontSize="10" fontUnderline="0" fieldName="NAME" isExpression="0" previewBkgrdColor="255,255,255,255" fontKerning="1" fontItalic="0" legendString="Aa" capitalization="0" textOpacity="1" useSubstitutions="0" multilineHeightUnit="Percentage" allowHtml="0">
        <families/>
        <text-buffer bufferSize="1" bufferSizeMapUnitScale="3x:0,0,0,0,0,0" bufferBlendMode="0" bufferDraw="0" bufferNoFill="1" bufferSizeUnits="MM" bufferOpacity="1" bufferJoinStyle="128" bufferColor="255,255,255,255"/>
        <text-mask maskEnabled="0" maskJoinStyle="128" maskSize="1.5" maskOpacity="1" maskType="0" maskSizeUnits="MM" maskSizeMapUnitScale="3x:0,0,0,0,0,0" maskedSymbolLayers=""/>
        <background shapeRadiiMapUnitScale="3x:0,0,0,0,0,0" shapeRadiiY="0" shapeBorderWidth="0" shapeFillColor="255,255,255,255" shapeBorderColor="128,128,128,255" shapeJoinStyle="64" shapeRotationType="0" shapeBorderWidthUnit="MM" shapeType="0" shapeSizeY="0" shapeOpacity="1" shapeRadiiUnit="MM" shapeRadiiX="0" shapeOffsetX="0" shapeRotation="0" shapeDraw="0" shapeSizeUnit="MM" shapeSizeX="0" shapeOffsetMapUnitScale="3x:0,0,0,0,0,0" shapeBorderWidthMapUnitScale="3x:0,0,0,0,0,0" shapeOffsetY="0" shapeSVGFile="" shapeSizeType="0" shapeSizeMapUnitScale="3x:0,0,0,0,0,0" shapeOffsetUnit="MM" shapeBlendMode="0">
          <symbol type="marker" is_animated="0" alpha="1" name="markerSymbol" force_rhr="0" clip_to_extent="1" frame_rate="10">
            <data_defined_properties>
              <Option type="Map">
                <Option type="QString" name="name" value=""/>
                <Option name="properties"/>
                <Option type="QString" name="type" value="collection"/>
              </Option>
            </data_defined_properties>
            <layer locked="0" enabled="1" pass="0" class="SimpleMarker">
              <Option type="Map">
                <Option type="QString" name="angle" value="0"/>
                <Option type="QString" name="cap_style" value="square"/>
                <Option type="QString" name="color" value="243,166,178,255"/>
                <Option type="QString" name="horizontal_anchor_point" value="1"/>
                <Option type="QString" name="joinstyle" value="bevel"/>
                <Option type="QString" name="name" value="circle"/>
                <Option type="QString" name="offset" value="0,0"/>
                <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
                <Option type="QString" name="offset_unit" value="MM"/>
                <Option type="QString" name="outline_color" value="35,35,35,255"/>
                <Option type="QString" name="outline_style" value="solid"/>
                <Option type="QString" name="outline_width" value="0"/>
                <Option type="QString" name="outline_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
                <Option type="QString" name="outline_width_unit" value="MM"/>
                <Option type="QString" name="scale_method" value="diameter"/>
                <Option type="QString" name="size" value="2"/>
                <Option type="QString" name="size_map_unit_scale" value="3x:0,0,0,0,0,0"/>
                <Option type="QString" name="size_unit" value="MM"/>
                <Option type="QString" name="vertical_anchor_point" value="1"/>
              </Option>
              <data_defined_properties>
                <Option type="Map">
                  <Option type="QString" name="name" value=""/>
                  <Option name="properties"/>
                  <Option type="QString" name="type" value="collection"/>
                </Option>
              </data_defined_properties>
            </layer>
          </symbol>
          <symbol type="fill" is_animated="0" alpha="1" name="fillSymbol" force_rhr="0" clip_to_extent="1" frame_rate="10">
            <data_defined_properties>
              <Option type="Map">
                <Option type="QString" name="name" value=""/>
                <Option name="properties"/>
                <Option type="QString" name="type" value="collection"/>
              </Option>
            </data_defined_properties>
            <layer locked="0" enabled="1" pass="0" class="SimpleFill">
              <Option type="Map">
                <Option type="QString" name="border_width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
                <Option type="QString" name="color" value="255,255,255,255"/>
                <Option type="QString" name="joinstyle" value="bevel"/>
                <Option type="QString" name="offset" value="0,0"/>
                <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
                <Option type="QString" name="offset_unit" value="MM"/>
                <Option type="QString" name="outline_color" value="128,128,128,255"/>
                <Option type="QString" name="outline_style" value="no"/>
                <Option type="QString" name="outline_width" value="0"/>
                <Option type="QString" name="outline_width_unit" value="MM"/>
                <Option type="QString" name="style" value="solid"/>
              </Option>
              <data_defined_properties>
                <Option type="Map">
                  <Option type="QString" name="name" value=""/>
                  <Option name="properties"/>
                  <Option type="QString" name="type" value="collection"/>
                </Option>
              </data_defined_properties>
            </layer>
          </symbol>
        </background>
        <shadow shadowOpacity="0.69999999999999996" shadowColor="0,0,0,255" shadowRadiusAlphaOnly="0" shadowOffsetGlobal="1" shadowUnder="0" shadowRadiusUnit="MM" shadowDraw="0" shadowOffsetMapUnitScale="3x:0,0,0,0,0,0" shadowRadius="1.5" shadowOffsetAngle="135" shadowOffsetDist="1" shadowRadiusMapUnitScale="3x:0,0,0,0,0,0" shadowBlendMode="6" shadowScale="100" shadowOffsetUnit="MM"/>
        <dd_properties>
          <Option type="Map">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
        </dd_properties>
        <substitutions/>
      </text-style>
      <text-format multilineAlign="3" addDirectionSymbol="0" reverseDirectionSymbol="0" placeDirectionSymbol="0" plussign="0" wrapChar="" useMaxLineLengthForAutoWrap="1" formatNumbers="0" decimals="3" leftDirectionSymbol="&lt;" rightDirectionSymbol=">" autoWrapLength="0"/>
      <placement geometryGeneratorType="PointGeometry" repeatDistanceMapUnitScale="3x:0,0,0,0,0,0" xOffset="3" maxCurvedCharAngleOut="-25" overrunDistanceUnit="MM" predefinedPositionOrder="TR,TL,BR,BL,R,L,TSR,BSR" placement="1" rotationAngle="0" lineAnchorClipping="0" overrunDistance="0" fitInPolygonOnly="0" yOffset="-3" layerType="PointGeometry" repeatDistance="0" lineAnchorType="0" lineAnchorTextPoint="CenterOfText" offsetUnits="MM" repeatDistanceUnits="MM" quadOffset="2" priority="5" offsetType="0" preserveRotation="1" centroidInside="0" centroidWhole="0" allowDegraded="0" labelOffsetMapUnitScale="3x:0,0,0,0,0,0" rotationUnit="AngleDegrees" distMapUnitScale="3x:0,0,0,0,0,0" geometryGenerator="" overlapHandling="PreventOverlap" distUnits="MM" geometryGeneratorEnabled="0" maxCurvedCharAngleIn="25" overrunDistanceMapUnitScale="3x:0,0,0,0,0,0" lineAnchorPercent="0.5" placementFlags="10" dist="0" polygonPlacementFlags="2"/>
      <rendering obstacleFactor="1" obstacleType="1" fontMaxPixelSize="10000" unplacedVisibility="0" scaleMax="10000" scaleMin="0" scaleVisibility="1" zIndex="0" drawLabels="1" fontMinPixelSize="3" mergeLines="0" maxNumLabels="2000" minFeatureSize="0" upsidedownLabels="0" labelPerPart="0" fontLimitPixelSize="0" limitNumLabels="0" obstacle="1"/>
      <dd_properties>
        <Option type="Map">
          <Option type="QString" name="name" value=""/>
          <Option name="properties"/>
          <Option type="QString" name="type" value="collection"/>
        </Option>
      </dd_properties>
      <callout type="simple">
        <Option type="Map">
          <Option type="QString" name="anchorPoint" value="pole_of_inaccessibility"/>
          <Option type="int" name="blendMode" value="0"/>
          <Option type="Map" name="ddProperties">
            <Option type="QString" name="name" value=""/>
            <Option name="properties"/>
            <Option type="QString" name="type" value="collection"/>
          </Option>
          <Option type="bool" name="drawToAllParts" value="false"/>
          <Option type="QString" name="enabled" value="0"/>
          <Option type="QString" name="labelAnchorPoint" value="point_on_exterior"/>
          <Option type="QString" name="lineSymbol" value="&lt;symbol type=&quot;line&quot; is_animated=&quot;0&quot; alpha=&quot;1&quot; name=&quot;symbol&quot; force_rhr=&quot;0&quot; clip_to_extent=&quot;1&quot; frame_rate=&quot;10&quot;>&lt;data_defined_properties>&lt;Option type=&quot;Map&quot;>&lt;Option type=&quot;QString&quot; name=&quot;name&quot; value=&quot;&quot;/>&lt;Option name=&quot;properties&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;type&quot; value=&quot;collection&quot;/>&lt;/Option>&lt;/data_defined_properties>&lt;layer locked=&quot;0&quot; enabled=&quot;1&quot; pass=&quot;0&quot; class=&quot;SimpleLine&quot;>&lt;Option type=&quot;Map&quot;>&lt;Option type=&quot;QString&quot; name=&quot;align_dash_pattern&quot; value=&quot;0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;capstyle&quot; value=&quot;square&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;customdash&quot; value=&quot;5;2&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;customdash_map_unit_scale&quot; value=&quot;3x:0,0,0,0,0,0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;customdash_unit&quot; value=&quot;MM&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;dash_pattern_offset&quot; value=&quot;0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;dash_pattern_offset_map_unit_scale&quot; value=&quot;3x:0,0,0,0,0,0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;dash_pattern_offset_unit&quot; value=&quot;MM&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;draw_inside_polygon&quot; value=&quot;0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;joinstyle&quot; value=&quot;bevel&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;line_color&quot; value=&quot;60,60,60,255&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;line_style&quot; value=&quot;solid&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;line_width&quot; value=&quot;0.3&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;line_width_unit&quot; value=&quot;MM&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;offset&quot; value=&quot;0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;offset_map_unit_scale&quot; value=&quot;3x:0,0,0,0,0,0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;offset_unit&quot; value=&quot;MM&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;ring_filter&quot; value=&quot;0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;trim_distance_end&quot; value=&quot;0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;trim_distance_end_map_unit_scale&quot; value=&quot;3x:0,0,0,0,0,0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;trim_distance_end_unit&quot; value=&quot;MM&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;trim_distance_start&quot; value=&quot;0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;trim_distance_start_map_unit_scale&quot; value=&quot;3x:0,0,0,0,0,0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;trim_distance_start_unit&quot; value=&quot;MM&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;tweak_dash_pattern_on_corners&quot; value=&quot;0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;use_custom_dash&quot; value=&quot;0&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;width_map_unit_scale&quot; value=&quot;3x:0,0,0,0,0,0&quot;/>&lt;/Option>&lt;data_defined_properties>&lt;Option type=&quot;Map&quot;>&lt;Option type=&quot;QString&quot; name=&quot;name&quot; value=&quot;&quot;/>&lt;Option name=&quot;properties&quot;/>&lt;Option type=&quot;QString&quot; name=&quot;type&quot; value=&quot;collection&quot;/>&lt;/Option>&lt;/data_defined_properties>&lt;/layer>&lt;/symbol>"/>
          <Option type="double" name="minLength" value="0"/>
          <Option type="QString" name="minLengthMapUnitScale" value="3x:0,0,0,0,0,0"/>
          <Option type="QString" name="minLengthUnit" value="MM"/>
          <Option type="double" name="offsetFromAnchor" value="0"/>
          <Option type="QString" name="offsetFromAnchorMapUnitScale" value="3x:0,0,0,0,0,0"/>
          <Option type="QString" name="offsetFromAnchorUnit" value="MM"/>
          <Option type="double" name="offsetFromLabel" value="0"/>
          <Option type="QString" name="offsetFromLabelMapUnitScale" value="3x:0,0,0,0,0,0"/>
          <Option type="QString" name="offsetFromLabelUnit" value="MM"/>
        </Option>
      </callout>
    </settings>
  </labeling>
  <customproperties>
    <Option type="Map">
      <Option type="QString" name="embeddedWidgets/count" value="0"/>
      <Option name="variableNames"/>
      <Option name="variableValues"/>
    </Option>
  </customproperties>
  <blendMode>0</blendMode>
  <featureBlendMode>0</featureBlendMode>
  <layerOpacity>1</layerOpacity>
  <SingleCategoryDiagramRenderer diagramType="Histogram" attributeLegend="1">
    <DiagramCategory scaleBasedVisibility="0" maxScaleDenominator="1e+08" barWidth="5" direction="0" minScaleDenominator="0" minimumSize="0" height="15" showAxis="1" enabled="0" lineSizeType="MM" diagramOrientation="Up" backgroundAlpha="255" spacingUnitScale="3x:0,0,0,0,0,0" sizeType="MM" penWidth="0" sizeScale="3x:0,0,0,0,0,0" backgroundColor="#ffffff" scaleDependency="Area" penColor="#000000" lineSizeScale="3x:0,0,0,0,0,0" rotationOffset="270" spacingUnit="MM" opacity="1" spacing="5" penAlpha="255" width="15" labelPlacementMethod="XHeight">
      <fontProperties bold="0" underline="0" style="" italic="0" description="MS Shell Dlg 2,7.8,-1,5,50,0,0,0,0,0" strikethrough="0"/>
      <attribute color="#000000" colorOpacity="1" label="" field=""/>
      <axisSymbol>
        <symbol type="line" is_animated="0" alpha="1" name="" force_rhr="0" clip_to_extent="1" frame_rate="10">
          <data_defined_properties>
            <Option type="Map">
              <Option type="QString" name="name" value=""/>
              <Option name="properties"/>
              <Option type="QString" name="type" value="collection"/>
            </Option>
          </data_defined_properties>
          <layer locked="0" enabled="1" pass="0" class="SimpleLine">
            <Option type="Map">
              <Option type="QString" name="align_dash_pattern" value="0"/>
              <Option type="QString" name="capstyle" value="square"/>
              <Option type="QString" name="customdash" value="5;2"/>
              <Option type="QString" name="customdash_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="customdash_unit" value="MM"/>
              <Option type="QString" name="dash_pattern_offset" value="0"/>
              <Option type="QString" name="dash_pattern_offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="dash_pattern_offset_unit" value="MM"/>
              <Option type="QString" name="draw_inside_polygon" value="0"/>
              <Option type="QString" name="joinstyle" value="bevel"/>
              <Option type="QString" name="line_color" value="35,35,35,255"/>
              <Option type="QString" name="line_style" value="solid"/>
              <Option type="QString" name="line_width" value="0.26"/>
              <Option type="QString" name="line_width_unit" value="MM"/>
              <Option type="QString" name="offset" value="0"/>
              <Option type="QString" name="offset_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="offset_unit" value="MM"/>
              <Option type="QString" name="ring_filter" value="0"/>
              <Option type="QString" name="trim_distance_end" value="0"/>
              <Option type="QString" name="trim_distance_end_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="trim_distance_end_unit" value="MM"/>
              <Option type="QString" name="trim_distance_start" value="0"/>
              <Option type="QString" name="trim_distance_start_map_unit_scale" value="3x:0,0,0,0,0,0"/>
              <Option type="QString" name="trim_distance_start_unit" value="MM"/>
              <Option type="QString" name="tweak_dash_pattern_on_corners" value="0"/>
              <Option type="QString" name="use_custom_dash" value="0"/>
              <Option type="QString" name="width_map_unit_scale" value="3x:0,0,0,0,0,0"/>
            </Option>
            <data_defined_properties>
              <Option type="Map">
                <Option type="QString" name="name" value=""/>
                <Option name="properties"/>
                <Option type="QString" name="type" value="collection"/>
              </Option>
            </data_defined_properties>
          </layer>
        </symbol>
      </axisSymbol>
    </DiagramCategory>
  </SingleCategoryDiagramRenderer>
  <DiagramLayerSettings linePlacementFlags="18" dist="0" obstacle="0" zIndex="0" priority="0" showAll="1" placement="0">
    <properties>
      <Option type="Map">
        <Option type="QString" name="name" value=""/>
        <Option name="properties"/>
        <Option type="QString" name="type" value="collection"/>
      </Option>
    </properties>
  </DiagramLayerSettings>
  <geometryOptions removeDuplicateNodes="0" geometryPrecision="0">
    <activeChecks/>
    <checkConfiguration/>
  </geometryOptions>
  <legend type="default-vector" showLabelLegend="0"/>
  <referencedLayers/>
  <fieldConfiguration>
    <field name="REF" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="NAME" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="PROPERTY_CODE" configurationFlags="None">
      <editWidget type="Range">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="TOWN" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="WGS84_LONGITUDE" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="WGS84_LATITUDE" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="Benchmark_Area" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="Environment_definition" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="City" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="City_ID" configurationFlags="None">
      <editWidget type="Range">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="City_Type" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="State" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="City_Population" configurationFlags="None">
      <editWidget type="Range">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="Cov_Type" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="Tech Active" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="Cov_Type_1" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="GSM_Active" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="LTE_Cov_Type" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="LTE_Active" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="LTE_Deactive" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="NR_Cov_Type" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="NR_Active" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="NR_Deactive" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="Scenario" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
    <field name="Route" configurationFlags="None">
      <editWidget type="TextEdit">
        <config>
          <Option/>
        </config>
      </editWidget>
    </field>
  </fieldConfiguration>
  <aliases>
    <alias field="REF" name="" index="0"/>
    <alias field="NAME" name="" index="1"/>
    <alias field="PROPERTY_CODE" name="" index="2"/>
    <alias field="TOWN" name="" index="3"/>
    <alias field="WGS84_LONGITUDE" name="" index="4"/>
    <alias field="WGS84_LATITUDE" name="" index="5"/>
    <alias field="Benchmark_Area" name="" index="6"/>
    <alias field="Environment_definition" name="" index="7"/>
    <alias field="City" name="" index="8"/>
    <alias field="City_ID" name="" index="9"/>
    <alias field="City_Type" name="" index="10"/>
    <alias field="State" name="" index="11"/>
    <alias field="City_Population" name="" index="12"/>
    <alias field="Cov_Type" name="" index="13"/>
    <alias field="Tech Active" name="" index="14"/>
    <alias field="Cov_Type_1" name="" index="15"/>
    <alias field="GSM_Active" name="" index="16"/>
    <alias field="LTE_Cov_Type" name="" index="17"/>
    <alias field="LTE_Active" name="" index="18"/>
    <alias field="LTE_Deactive" name="" index="19"/>
    <alias field="NR_Cov_Type" name="" index="20"/>
    <alias field="NR_Active" name="" index="21"/>
    <alias field="NR_Deactive" name="" index="22"/>
    <alias field="Scenario" name="" index="23"/>
    <alias field="Route" name="" index="24"/>
  </aliases>
  <defaults>
    <default applyOnUpdate="0" field="REF" expression=""/>
    <default applyOnUpdate="0" field="NAME" expression=""/>
    <default applyOnUpdate="0" field="PROPERTY_CODE" expression=""/>
    <default applyOnUpdate="0" field="TOWN" expression=""/>
    <default applyOnUpdate="0" field="WGS84_LONGITUDE" expression=""/>
    <default applyOnUpdate="0" field="WGS84_LATITUDE" expression=""/>
    <default applyOnUpdate="0" field="Benchmark_Area" expression=""/>
    <default applyOnUpdate="0" field="Environment_definition" expression=""/>
    <default applyOnUpdate="0" field="City" expression=""/>
    <default applyOnUpdate="0" field="City_ID" expression=""/>
    <default applyOnUpdate="0" field="City_Type" expression=""/>
    <default applyOnUpdate="0" field="State" expression=""/>
    <default applyOnUpdate="0" field="City_Population" expression=""/>
    <default applyOnUpdate="0" field="Cov_Type" expression=""/>
    <default applyOnUpdate="0" field="Tech Active" expression=""/>
    <default applyOnUpdate="0" field="Cov_Type_1" expression=""/>
    <default applyOnUpdate="0" field="GSM_Active" expression=""/>
    <default applyOnUpdate="0" field="LTE_Cov_Type" expression=""/>
    <default applyOnUpdate="0" field="LTE_Active" expression=""/>
    <default applyOnUpdate="0" field="LTE_Deactive" expression=""/>
    <default applyOnUpdate="0" field="NR_Cov_Type" expression=""/>
    <default applyOnUpdate="0" field="NR_Active" expression=""/>
    <default applyOnUpdate="0" field="NR_Deactive" expression=""/>
    <default applyOnUpdate="0" field="Scenario" expression=""/>
    <default applyOnUpdate="0" field="Route" expression=""/>
  </defaults>
  <constraints>
    <constraint exp_strength="0" unique_strength="0" field="REF" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="NAME" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="PROPERTY_CODE" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="TOWN" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="WGS84_LONGITUDE" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="WGS84_LATITUDE" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="Benchmark_Area" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="Environment_definition" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="City" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="City_ID" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="City_Type" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="State" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="City_Population" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="Cov_Type" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="Tech Active" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="Cov_Type_1" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="GSM_Active" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="LTE_Cov_Type" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="LTE_Active" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="LTE_Deactive" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="NR_Cov_Type" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="NR_Active" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="NR_Deactive" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="Scenario" constraints="0" notnull_strength="0"/>
    <constraint exp_strength="0" unique_strength="0" field="Route" constraints="0" notnull_strength="0"/>
  </constraints>
  <constraintExpressions>
    <constraint field="REF" desc="" exp=""/>
    <constraint field="NAME" desc="" exp=""/>
    <constraint field="PROPERTY_CODE" desc="" exp=""/>
    <constraint field="TOWN" desc="" exp=""/>
    <constraint field="WGS84_LONGITUDE" desc="" exp=""/>
    <constraint field="WGS84_LATITUDE" desc="" exp=""/>
    <constraint field="Benchmark_Area" desc="" exp=""/>
    <constraint field="Environment_definition" desc="" exp=""/>
    <constraint field="City" desc="" exp=""/>
    <constraint field="City_ID" desc="" exp=""/>
    <constraint field="City_Type" desc="" exp=""/>
    <constraint field="State" desc="" exp=""/>
    <constraint field="City_Population" desc="" exp=""/>
    <constraint field="Cov_Type" desc="" exp=""/>
    <constraint field="Tech Active" desc="" exp=""/>
    <constraint field="Cov_Type_1" desc="" exp=""/>
    <constraint field="GSM_Active" desc="" exp=""/>
    <constraint field="LTE_Cov_Type" desc="" exp=""/>
    <constraint field="LTE_Active" desc="" exp=""/>
    <constraint field="LTE_Deactive" desc="" exp=""/>
    <constraint field="NR_Cov_Type" desc="" exp=""/>
    <constraint field="NR_Active" desc="" exp=""/>
    <constraint field="NR_Deactive" desc="" exp=""/>
    <constraint field="Scenario" desc="" exp=""/>
    <constraint field="Route" desc="" exp=""/>
  </constraintExpressions>
  <expressionfields/>
  <attributeactions>
    <defaultAction key="Canvas" value="{00000000-0000-0000-0000-000000000000}"/>
  </attributeactions>
  <attributetableconfig sortExpression="" sortOrder="0" actionWidgetStyle="dropDown">
    <columns>
      <column hidden="0" type="field" name="TOWN" width="-1"/>
      <column hidden="0" type="field" name="WGS84_LONGITUDE" width="-1"/>
      <column hidden="0" type="field" name="WGS84_LATITUDE" width="-1"/>
      <column hidden="0" type="field" name="Benchmark_Area" width="-1"/>
      <column hidden="0" type="field" name="Environment_definition" width="-1"/>
      <column hidden="0" type="field" name="City" width="-1"/>
      <column hidden="0" type="field" name="City_ID" width="-1"/>
      <column hidden="0" type="field" name="City_Type" width="-1"/>
      <column hidden="0" type="field" name="State" width="-1"/>
      <column hidden="0" type="field" name="City_Population" width="-1"/>
      <column hidden="1" type="actions" width="-1"/>
      <column hidden="0" type="field" name="NAME" width="-1"/>
      <column hidden="0" type="field" name="PROPERTY_CODE" width="-1"/>
      <column hidden="0" type="field" name="REF" width="-1"/>
      <column hidden="0" type="field" name="Cov_Type" width="-1"/>
      <column hidden="0" type="field" name="Cov_Type_1" width="-1"/>
      <column hidden="0" type="field" name="GSM_Active" width="-1"/>
      <column hidden="0" type="field" name="LTE_Cov_Type" width="-1"/>
      <column hidden="0" type="field" name="LTE_Active" width="-1"/>
      <column hidden="0" type="field" name="LTE_Deactive" width="-1"/>
      <column hidden="0" type="field" name="NR_Cov_Type" width="-1"/>
      <column hidden="0" type="field" name="NR_Active" width="-1"/>
      <column hidden="0" type="field" name="NR_Deactive" width="-1"/>
      <column hidden="0" type="field" name="Scenario" width="-1"/>
      <column hidden="0" type="field" name="Route" width="-1"/>
      <column hidden="0" type="field" name="Tech Active" width="-1"/>
    </columns>
  </attributetableconfig>
  <conditionalstyles>
    <rowstyles/>
    <fieldstyles/>
  </conditionalstyles>
  <storedexpressions/>
  <editform tolerant="1"></editform>
  <editforminit/>
  <editforminitcodesource>0</editforminitcodesource>
  <editforminitfilepath></editforminitfilepath>
  <editforminitcode><![CDATA[# -*- coding: utf-8 -*-
"""
QGIS forms can have a Python function that is called when the form is
opened.

Use this function to add extra logic to your forms.

Enter the name of the function in the "Python Init function"
field.
An example follows:
"""
from qgis.PyQt.QtWidgets import QWidget

def my_form_open(dialog, layer, feature):
	geom = feature.geometry()
	control = dialog.findChild(QWidget, "MyLineEdit")
]]></editforminitcode>
  <featformsuppress>0</featformsuppress>
  <editorlayout>generatedlayout</editorlayout>
  <editable>
    <field editable="1" name="Active Sites"/>
    <field editable="1" name="Benchmark_Area"/>
    <field editable="1" name="City"/>
    <field editable="1" name="City_ID"/>
    <field editable="1" name="City_Population"/>
    <field editable="1" name="City_Type"/>
    <field editable="1" name="Cov_Type"/>
    <field editable="1" name="Cov_Type_1"/>
    <field editable="1" name="Coverage Type"/>
    <field editable="1" name="Environment_definition"/>
    <field editable="1" name="G18"/>
    <field editable="1" name="G9"/>
    <field editable="1" name="GSM"/>
    <field editable="1" name="GSM Active"/>
    <field editable="1" name="GSM Site Name"/>
    <field editable="1" name="GSM Sites"/>
    <field editable="1" name="GSM_Active"/>
    <field editable="1" name="L18"/>
    <field editable="1" name="L21"/>
    <field editable="1" name="L26"/>
    <field editable="1" name="L7"/>
    <field editable="1" name="L8"/>
    <field editable="1" name="L9"/>
    <field editable="1" name="LTE"/>
    <field editable="1" name="LTE Active"/>
    <field editable="1" name="LTE Site Name"/>
    <field editable="1" name="LTE Sites"/>
    <field editable="1" name="LTE_Active"/>
    <field editable="1" name="LTE_Cov_Type"/>
    <field editable="1" name="LTE_Deactive"/>
    <field editable="1" name="Multiple Site Location"/>
    <field editable="1" name="N18"/>
    <field editable="1" name="N280"/>
    <field editable="1" name="N35"/>
    <field editable="1" name="N7"/>
    <field editable="1" name="NAME"/>
    <field editable="1" name="NE Code"/>
    <field editable="1" name="NR"/>
    <field editable="1" name="NR Active"/>
    <field editable="1" name="NR Site Name"/>
    <field editable="1" name="NR Sites"/>
    <field editable="1" name="NR_Active"/>
    <field editable="1" name="NR_Cov_Type"/>
    <field editable="1" name="NR_Deactive"/>
    <field editable="1" name="Name [short]"/>
    <field editable="1" name="PROPERTY_CODE"/>
    <field editable="1" name="Property Code [SO]"/>
    <field editable="1" name="REF"/>
    <field editable="1" name="Route"/>
    <field editable="1" name="Scenario"/>
    <field editable="1" name="Site Coverage Type"/>
    <field editable="1" name="State"/>
    <field editable="1" name="TOWN"/>
    <field editable="1" name="Tech"/>
    <field editable="1" name="Tech Active"/>
    <field editable="1" name="WGS84_LATITUDE"/>
    <field editable="1" name="WGS84_LONGITUDE"/>
    <field editable="1" name="fid"/>
  </editable>
  <labelOnTop>
    <field name="Active Sites" labelOnTop="0"/>
    <field name="Benchmark_Area" labelOnTop="0"/>
    <field name="City" labelOnTop="0"/>
    <field name="City_ID" labelOnTop="0"/>
    <field name="City_Population" labelOnTop="0"/>
    <field name="City_Type" labelOnTop="0"/>
    <field name="Cov_Type" labelOnTop="0"/>
    <field name="Cov_Type_1" labelOnTop="0"/>
    <field name="Coverage Type" labelOnTop="0"/>
    <field name="Environment_definition" labelOnTop="0"/>
    <field name="G18" labelOnTop="0"/>
    <field name="G9" labelOnTop="0"/>
    <field name="GSM" labelOnTop="0"/>
    <field name="GSM Active" labelOnTop="0"/>
    <field name="GSM Site Name" labelOnTop="0"/>
    <field name="GSM Sites" labelOnTop="0"/>
    <field name="GSM_Active" labelOnTop="0"/>
    <field name="L18" labelOnTop="0"/>
    <field name="L21" labelOnTop="0"/>
    <field name="L26" labelOnTop="0"/>
    <field name="L7" labelOnTop="0"/>
    <field name="L8" labelOnTop="0"/>
    <field name="L9" labelOnTop="0"/>
    <field name="LTE" labelOnTop="0"/>
    <field name="LTE Active" labelOnTop="0"/>
    <field name="LTE Site Name" labelOnTop="0"/>
    <field name="LTE Sites" labelOnTop="0"/>
    <field name="LTE_Active" labelOnTop="0"/>
    <field name="LTE_Cov_Type" labelOnTop="0"/>
    <field name="LTE_Deactive" labelOnTop="0"/>
    <field name="Multiple Site Location" labelOnTop="0"/>
    <field name="N18" labelOnTop="0"/>
    <field name="N280" labelOnTop="0"/>
    <field name="N35" labelOnTop="0"/>
    <field name="N7" labelOnTop="0"/>
    <field name="NAME" labelOnTop="0"/>
    <field name="NE Code" labelOnTop="0"/>
    <field name="NR" labelOnTop="0"/>
    <field name="NR Active" labelOnTop="0"/>
    <field name="NR Site Name" labelOnTop="0"/>
    <field name="NR Sites" labelOnTop="0"/>
    <field name="NR_Active" labelOnTop="0"/>
    <field name="NR_Cov_Type" labelOnTop="0"/>
    <field name="NR_Deactive" labelOnTop="0"/>
    <field name="Name [short]" labelOnTop="0"/>
    <field name="PROPERTY_CODE" labelOnTop="0"/>
    <field name="Property Code [SO]" labelOnTop="0"/>
    <field name="REF" labelOnTop="0"/>
    <field name="Route" labelOnTop="0"/>
    <field name="Scenario" labelOnTop="0"/>
    <field name="Site Coverage Type" labelOnTop="0"/>
    <field name="State" labelOnTop="0"/>
    <field name="TOWN" labelOnTop="0"/>
    <field name="Tech" labelOnTop="0"/>
    <field name="Tech Active" labelOnTop="0"/>
    <field name="WGS84_LATITUDE" labelOnTop="0"/>
    <field name="WGS84_LONGITUDE" labelOnTop="0"/>
    <field name="fid" labelOnTop="0"/>
  </labelOnTop>
  <reuseLastValue>
    <field reuseLastValue="0" name="Benchmark_Area"/>
    <field reuseLastValue="0" name="City"/>
    <field reuseLastValue="0" name="City_ID"/>
    <field reuseLastValue="0" name="City_Population"/>
    <field reuseLastValue="0" name="City_Type"/>
    <field reuseLastValue="0" name="Cov_Type"/>
    <field reuseLastValue="0" name="Cov_Type_1"/>
    <field reuseLastValue="0" name="Environment_definition"/>
    <field reuseLastValue="0" name="GSM_Active"/>
    <field reuseLastValue="0" name="LTE_Active"/>
    <field reuseLastValue="0" name="LTE_Cov_Type"/>
    <field reuseLastValue="0" name="LTE_Deactive"/>
    <field reuseLastValue="0" name="NAME"/>
    <field reuseLastValue="0" name="NR_Active"/>
    <field reuseLastValue="0" name="NR_Cov_Type"/>
    <field reuseLastValue="0" name="NR_Deactive"/>
    <field reuseLastValue="0" name="PROPERTY_CODE"/>
    <field reuseLastValue="0" name="REF"/>
    <field reuseLastValue="0" name="Route"/>
    <field reuseLastValue="0" name="Scenario"/>
    <field reuseLastValue="0" name="State"/>
    <field reuseLastValue="0" name="TOWN"/>
    <field reuseLastValue="0" name="Tech Active"/>
    <field reuseLastValue="0" name="WGS84_LATITUDE"/>
    <field reuseLastValue="0" name="WGS84_LONGITUDE"/>
  </reuseLastValue>
  <dataDefinedFieldProperties/>
  <widgets/>
  <previewExpression>"NAME"</previewExpression>
  <mapTip>[% "NAME" %]</mapTip>
  <layerGeometryType>0</layerGeometryType>
</qgis>
