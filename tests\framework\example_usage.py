"""综合测试框架使用示例

该文件展示了如何使用综合测试框架进行测试管理和执行。
"""

import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Any

from .comprehensive_test_framework import (
    ComprehensiveTestFramework,
    TestSuiteConfig,
    TestPriority,
    TestType
)
from .comprehensive_test_framework_execution import (
    TestExecutionEngine,
    QualityGateChecker
)
from .comprehensive_test_framework_reporting import (
    TestReportGenerator
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestFrameworkExample:
    """测试框架使用示例类"""
    
    def __init__(self, work_dir: str = "tests/framework_workspace"):
        """初始化示例
        
        Args:
            work_dir: 工作目录路径
        """
        self.work_dir = Path(work_dir)
        self.framework = None
        self.execution_engine = None
        self.quality_checker = None
        self.report_generator = None
    
    async def setup_framework(self):
        """设置测试框架"""
        logger.info("正在初始化综合测试框架...")
        
        # 初始化核心框架
        self.framework = ComprehensiveTestFramework(str(self.work_dir))
        
        # 初始化执行引擎
        self.execution_engine = TestExecutionEngine(self.framework)
        
        # 初始化质量门控检查器
        self.quality_checker = QualityGateChecker(self.framework)
        
        # 初始化报告生成器
        self.report_generator = TestReportGenerator(self.framework)
        
        logger.info("测试框架初始化完成")
    
    def register_custom_test_suite(self):
        """注册自定义测试套件示例"""
        logger.info("注册自定义测试套件...")
        
        # 创建自定义测试套件配置
        custom_suite = TestSuiteConfig(
            name="custom_api_tests",
            description="自定义API测试套件",
            test_paths=["tests/api"],
            markers=["api", "custom"],
            priority=TestPriority.HIGH,
            test_type=TestType.INTEGRATION,
            parallel=True,
            timeout=600,
            retries=2,
            dependencies=["unit_tests"],
            environment={
                "setup_method": "setup_api_environment",
                "teardown_method": "teardown_api_environment"
            },
            thresholds={
                "coverage_threshold": 85.0,
                "performance_threshold": {
                    "max_response_time": 1000,
                    "min_throughput": 100
                }
            },
            custom_config={
                "api_base_url": "http://localhost:8000",
                "auth_token": "test_token",
                "timeout": 30
            }
        )
        
        # 注册测试套件
        self.framework.register_test_suite(custom_suite)
        logger.info(f"已注册测试套件: {custom_suite.name}")
    
    def register_custom_quality_gate(self):
        """注册自定义质量门控示例"""
        logger.info("注册自定义质量门控...")
        
        from .comprehensive_test_framework import QualityGate
        
        # 创建自定义质量门控
        custom_gate = QualityGate(
            name="api_quality_gate",
            description="API质量门控",
            coverage_threshold=90.0,
            success_rate_threshold=95.0,
            performance_thresholds={
                "max_response_time": 500,
                "min_throughput": 200,
                "max_cpu_usage": 70
            },
            security_checks=[
                "no_critical_vulnerabilities",
                "api_security_check"
            ],
            custom_checks=[
                "api_documentation_check",
                "api_versioning_check"
            ],
            blocking=True
        )
        
        # 注册质量门控
        self.framework.register_quality_gate(custom_gate)
        logger.info(f"已注册质量门控: {custom_gate.name}")
    
    async def run_single_test_suite(self, suite_name: str):
        """运行单个测试套件示例
        
        Args:
            suite_name: 测试套件名称
        """
        logger.info(f"运行测试套件: {suite_name}")
        
        try:
            # 执行测试套件
            result = await self.execution_engine.execute_test_suite(suite_name)
            
            # 输出结果
            logger.info(f"测试套件 {suite_name} 执行完成:")
            logger.info(f"  状态: {result.status.value}")
            logger.info(f"  总测试数: {result.test_count}")
            logger.info(f"  通过: {result.passed_count}")
            logger.info(f"  失败: {result.failed_count}")
            logger.info(f"  跳过: {result.skipped_count}")
            logger.info(f"  错误: {result.error_count}")
            logger.info(f"  成功率: {result.success_rate:.1f}%")
            logger.info(f"  覆盖率: {result.coverage_percentage or 'N/A'}")
            logger.info(f"  耗时: {result.duration:.2f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"执行测试套件 {suite_name} 时发生错误: {e}")
            raise
    
    async def run_multiple_test_suites(self, suite_names: List[str], parallel: bool = False):
        """运行多个测试套件示例
        
        Args:
            suite_names: 测试套件名称列表
            parallel: 是否并行执行
        """
        logger.info(f"运行多个测试套件: {suite_names} (并行: {parallel})")
        
        try:
            # 执行多个测试套件
            results = await self.execution_engine.execute_test_suites(
                suite_names, parallel=parallel
            )
            
            # 输出结果摘要
            total_tests = sum(r.test_count for r in results)
            total_passed = sum(r.passed_count for r in results)
            total_failed = sum(r.failed_count for r in results)
            overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
            
            logger.info("多套件执行结果摘要:")
            logger.info(f"  总测试数: {total_tests}")
            logger.info(f"  总通过数: {total_passed}")
            logger.info(f"  总失败数: {total_failed}")
            logger.info(f"  整体成功率: {overall_success_rate:.1f}%")
            
            # 输出各套件详情
            for result in results:
                logger.info(f"  {result.suite_name}: {result.status.value} ({result.success_rate:.1f}%)")
            
            return results
            
        except Exception as e:
            logger.error(f"执行多个测试套件时发生错误: {e}")
            raise
    
    async def run_all_test_suites(self):
        """运行所有测试套件示例"""
        logger.info("运行所有测试套件...")
        
        try:
            # 获取所有测试套件
            all_suites = self.framework.list_test_suites()
            logger.info(f"发现 {len(all_suites)} 个测试套件")
            
            # 确定执行顺序
            execution_order = self.framework.get_execution_order(all_suites)
            logger.info(f"执行顺序: {execution_order}")
            
            # 执行所有测试套件
            results = await self.execution_engine.execute_test_suites(
                execution_order, parallel=False
            )
            
            return results
            
        except Exception as e:
            logger.error(f"执行所有测试套件时发生错误: {e}")
            raise
    
    async def check_quality_gates(self, execution_results: List, gate_names: List[str] = None):
        """检查质量门控示例
        
        Args:
            execution_results: 测试执行结果列表
            gate_names: 要检查的质量门控名称列表，None表示检查所有
        """
        logger.info("检查质量门控...")
        
        try:
            # 检查质量门控
            gate_results = await self.quality_checker.check_quality_gates(
                execution_results, gate_names
            )
            
            # 输出检查结果
            logger.info("质量门控检查结果:")
            for gate_name, gate_result in gate_results.items():
                status = "通过" if gate_result['overall_passed'] else "失败"
                logger.info(f"  {gate_name}: {status}")
                
                # 输出详细检查项
                for check_name, check_result in gate_result['checks'].items():
                    check_status = "✓" if check_result['passed'] else "✗"
                    logger.info(f"    {check_status} {check_name}: {check_result.get('message', '')}")
            
            return gate_results
            
        except Exception as e:
            logger.error(f"检查质量门控时发生错误: {e}")
            raise
    
    async def generate_reports(self, execution_results: List, quality_gate_results: Dict = None):
        """生成测试报告示例
        
        Args:
            execution_results: 测试执行结果列表
            quality_gate_results: 质量门控检查结果
        """
        logger.info("生成测试报告...")
        
        try:
            # 生成HTML报告
            html_report = self.report_generator.generate_html_report(
                execution_results, quality_gate_results
            )
            logger.info(f"HTML报告已生成: {html_report}")
            
            # 生成JSON报告
            json_report = self.report_generator.generate_json_report(
                execution_results, quality_gate_results
            )
            logger.info(f"JSON报告已生成: {json_report}")
            
            # 生成性能报告
            performance_report = self.report_generator.generate_performance_report(
                execution_results
            )
            logger.info(f"性能报告已生成: {performance_report}")
            
            # 生成覆盖率报告
            coverage_report = self.report_generator.generate_coverage_report(
                execution_results
            )
            logger.info(f"覆盖率报告已生成: {coverage_report}")
            
            return {
                'html_report': html_report,
                'json_report': json_report,
                'performance_report': performance_report,
                'coverage_report': coverage_report
            }
            
        except Exception as e:
            logger.error(f"生成测试报告时发生错误: {e}")
            raise
    
    async def run_complete_test_cycle(self):
        """运行完整测试周期示例"""
        logger.info("开始完整测试周期...")
        
        try:
            # 1. 设置框架
            await self.setup_framework()
            
            # 2. 注册自定义组件
            self.register_custom_test_suite()
            self.register_custom_quality_gate()
            
            # 3. 运行所有测试套件
            execution_results = await self.run_all_test_suites()
            
            # 4. 检查质量门控
            quality_gate_results = await self.check_quality_gates(execution_results)
            
            # 5. 生成报告
            reports = await self.generate_reports(execution_results, quality_gate_results)
            
            # 6. 输出最终结果
            total_tests = sum(r.test_count for r in execution_results)
            total_passed = sum(r.passed_count for r in execution_results)
            overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
            
            logger.info("完整测试周期执行完成!")
            logger.info(f"总测试数: {total_tests}")
            logger.info(f"总通过数: {total_passed}")
            logger.info(f"整体成功率: {overall_success_rate:.1f}%")
            logger.info(f"生成的报告: {list(reports.keys())}")
            
            # 检查是否所有质量门控都通过
            all_gates_passed = all(
                result['overall_passed'] 
                for result in quality_gate_results.values()
            )
            
            if all_gates_passed:
                logger.info("✓ 所有质量门控检查通过")
            else:
                logger.warning("✗ 部分质量门控检查未通过")
            
            return {
                'execution_results': execution_results,
                'quality_gate_results': quality_gate_results,
                'reports': reports,
                'overall_success': all_gates_passed and overall_success_rate >= 95.0
            }
            
        except Exception as e:
            logger.error(f"完整测试周期执行失败: {e}")
            raise


# 环境设置和清理函数示例
def setup_api_environment():
    """设置API测试环境"""
    logger.info("设置API测试环境...")
    # 这里可以添加API服务启动、数据库初始化等逻辑
    pass


def teardown_api_environment():
    """清理API测试环境"""
    logger.info("清理API测试环境...")
    # 这里可以添加服务停止、数据清理等逻辑
    pass


def setup_integration_environment():
    """设置集成测试环境"""
    logger.info("设置集成测试环境...")
    # 这里可以添加数据库连接、外部服务模拟等逻辑
    pass


def teardown_integration_environment():
    """清理集成测试环境"""
    logger.info("清理集成测试环境...")
    # 这里可以添加连接关闭、临时数据清理等逻辑
    pass


def setup_e2e_environment():
    """设置端到端测试环境"""
    logger.info("设置端到端测试环境...")
    # 这里可以添加完整系统启动、测试数据准备等逻辑
    pass


def teardown_e2e_environment():
    """清理端到端测试环境"""
    logger.info("清理端到端测试环境...")
    # 这里可以添加系统停止、环境重置等逻辑
    pass


def setup_performance_environment():
    """设置性能测试环境"""
    logger.info("设置性能测试环境...")
    # 这里可以添加性能监控工具启动、负载生成器配置等逻辑
    pass


def teardown_performance_environment():
    """清理性能测试环境"""
    logger.info("清理性能测试环境...")
    # 这里可以添加监控工具停止、性能数据收集等逻辑
    pass


def setup_security_environment():
    """设置安全测试环境"""
    logger.info("设置安全测试环境...")
    # 这里可以添加安全扫描工具配置、测试目标准备等逻辑
    pass


def teardown_security_environment():
    """清理安全测试环境"""
    logger.info("清理安全测试环境...")
    # 这里可以添加扫描结果收集、环境清理等逻辑
    pass


def setup_database_environment():
    """设置数据库测试环境"""
    logger.info("设置数据库测试环境...")
    # 这里可以添加测试数据库创建、测试数据导入等逻辑
    pass


def teardown_database_environment():
    """清理数据库测试环境"""
    logger.info("清理数据库测试环境...")
    # 这里可以添加测试数据清理、数据库连接关闭等逻辑
    pass


# 主函数示例
async def main():
    """主函数示例"""
    # 创建测试框架示例实例
    example = TestFrameworkExample()
    
    try:
        # 运行完整测试周期
        result = await example.run_complete_test_cycle()
        
        if result['overall_success']:
            logger.info("🎉 测试周期成功完成!")
            return 0
        else:
            logger.warning("⚠️ 测试周期完成，但存在问题")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 测试周期执行失败: {e}")
        return 2


if __name__ == "__main__":
    # 运行示例
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)