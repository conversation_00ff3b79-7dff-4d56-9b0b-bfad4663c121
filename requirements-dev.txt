# Development and Testing Dependencies

# Testing Framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-xdist>=3.3.0
pytest-html>=3.2.0
pytest-benchmark>=4.0.0
pytest-timeout>=2.1.0
pytest-sugar>=0.9.7

# Code Quality
black>=23.7.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0
bandit>=1.7.5
safety>=2.3.0
# semgrep>=1.35.0  # Not supported on Windows

# Code Coverage
coverage[toml]>=7.3.0
codecov>=2.1.13

# Performance Testing
locust>=2.16.0
memory-profiler>=0.61.0
psutil>=5.9.0

# Mock and Test Data
factory-boy>=3.3.0
faker>=19.6.0
responses>=0.23.0
httpx>=0.24.0

# Database Testing
pytest-postgresql>=5.0.0
testing.postgresql>=1.3.0

# Geospatial Testing
shapely>=2.0.0
geopandas>=0.13.0
fiona>=1.9.0

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

# Development Tools
ipython>=8.14.0
jupyter>=1.0.0
pre-commit>=3.4.0
tox>=4.11.0

# Type Stubs
types-PyYAML>=6.0.0
types-requests>=2.31.0
types-setuptools>=68.0.0

# Linting Extensions
flake8-docstrings>=1.7.0
flake8-import-order>=0.18.2
flake8-bugbear>=23.7.0
flake8-comprehensions>=3.14.0
flake8-simplify>=0.20.0

# Security Testing
# pytest-security>=0.1.0  # Package not available, using bandit instead

# API Testing
tavern>=2.0.0

# Load Testing
# artillery>=2.0.0  # Package not available, using locust instead