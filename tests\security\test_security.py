"""Security tests for the Connect database framework.

This module contains security tests that verify authentication, authorization,
data protection, and compliance requirements as specified in Task 22.
"""

import asyncio
import hashlib
import json
import os
import tempfile
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch, AsyncMock

import jwt
import pandas as pd
import pytest
from cryptography.fernet import <PERSON><PERSON><PERSON>
from sqlalchemy import text

from src.database.config import DatabaseConfig
from src.database.connection.session import SessionManager
from src.database.connection.pool import DatabasePoolManager
from src.database.operations.crud import CRUDOperations
from src.database.security.auth import AuthenticationManager
from src.database.security.encryption import DataEncryption
from src.database.security.audit import AuditLogger
from src.database.monitoring.logger import DatabaseLogger


@pytest.mark.security
class TestAuthenticationSecurity:
    """Security tests for authentication mechanisms."""

    @pytest.fixture
    def auth_manager(self, test_config):
        """Set up authentication manager for testing."""
        config = DatabaseConfig(test_config)
        return AuthenticationManager(config)

    @pytest.fixture
    def mock_user_data(self):
        """Mock user data for testing."""
        return {
            'valid_user': {
                'username': 'test_user',
                'password': 'SecureP@ssw0rd123',
                'email': '<EMAIL>',
                'role': 'analyst',
                'permissions': ['read_data', 'write_data']
            },
            'admin_user': {
                'username': 'admin_user',
                'password': 'AdminP@ssw0rd456',
                'email': '<EMAIL>',
                'role': 'admin',
                'permissions': ['read_data', 'write_data', 'admin_access', 'delete_data']
            },
            'readonly_user': {
                'username': 'readonly_user',
                'password': 'ReadOnlyP@ss789',
                'email': '<EMAIL>',
                'role': 'viewer',
                'permissions': ['read_data']
            }
        }

    def test_password_strength_validation(self, auth_manager):
        """Test password strength requirements."""
        # Test strong passwords (should pass)
        strong_passwords = [
            'SecureP@ssw0rd123',
            'MyStr0ng!Password',
            'C0mplex#Pass2024',
            'Adm1n$ecure&Pass'
        ]
        
        for password in strong_passwords:
            is_valid, message = auth_manager.validate_password_strength(password)
            assert is_valid, f"Strong password rejected: {password} - {message}"

        # Test weak passwords (should fail)
        weak_passwords = [
            'password',          # Too simple
            '12345678',          # Only numbers
            'PASSWORD',          # Only uppercase
            'password123',       # No special chars
            'Pass1!',           # Too short
            '',                 # Empty
            'a' * 100,          # Too long
            'Pass 123!',        # Contains space
        ]
        
        for password in weak_passwords:
            is_valid, message = auth_manager.validate_password_strength(password)
            assert not is_valid, f"Weak password accepted: {password}"
            assert message is not None and len(message) > 0

    def test_password_hashing_security(self, auth_manager):
        """Test password hashing and verification."""
        password = 'TestP@ssw0rd123'
        
        # Test password hashing
        hashed_password = auth_manager.hash_password(password)
        
        # Verify hash properties
        assert hashed_password != password, "Password should be hashed"
        assert len(hashed_password) > 50, "Hash should be sufficiently long"
        assert '$' in hashed_password, "Should use proper hashing algorithm"
        
        # Test password verification
        assert auth_manager.verify_password(password, hashed_password), "Password verification failed"
        assert not auth_manager.verify_password('wrong_password', hashed_password), "Wrong password accepted"
        
        # Test hash uniqueness (same password should produce different hashes due to salt)
        hash1 = auth_manager.hash_password(password)
        hash2 = auth_manager.hash_password(password)
        assert hash1 != hash2, "Hashes should be unique due to salt"
        
        # Both hashes should verify the same password
        assert auth_manager.verify_password(password, hash1)
        assert auth_manager.verify_password(password, hash2)

    def test_jwt_token_security(self, auth_manager, mock_user_data):
        """Test JWT token generation and validation."""
        user = mock_user_data['valid_user']
        
        # Test token generation
        token = auth_manager.generate_jwt_token(
            user_id=user['username'],
            role=user['role'],
            permissions=user['permissions']
        )
        
        assert token is not None
        assert isinstance(token, str)
        assert len(token) > 100, "JWT token should be sufficiently long"
        
        # Test token validation
        payload = auth_manager.validate_jwt_token(token)
        assert payload is not None
        assert payload['user_id'] == user['username']
        assert payload['role'] == user['role']
        assert payload['permissions'] == user['permissions']
        assert 'exp' in payload, "Token should have expiration"
        assert 'iat' in payload, "Token should have issued at time"
        
        # Test token expiration
        expired_token = auth_manager.generate_jwt_token(
            user_id=user['username'],
            role=user['role'],
            permissions=user['permissions'],
            expires_in=-3600  # Expired 1 hour ago
        )
        
        expired_payload = auth_manager.validate_jwt_token(expired_token)
        assert expired_payload is None, "Expired token should be invalid"
        
        # Test invalid token
        invalid_payload = auth_manager.validate_jwt_token('invalid.token.here')
        assert invalid_payload is None, "Invalid token should be rejected"
        
        # Test tampered token
        tampered_token = token[:-10] + 'tampered123'
        tampered_payload = auth_manager.validate_jwt_token(tampered_token)
        assert tampered_payload is None, "Tampered token should be rejected"

    def test_session_management_security(self, auth_manager, mock_user_data):
        """Test secure session management."""
        user = mock_user_data['valid_user']
        
        # Test session creation
        session_id = auth_manager.create_session(
            user_id=user['username'],
            role=user['role'],
            ip_address='*************',
            user_agent='Test Browser'
        )
        
        assert session_id is not None
        assert len(session_id) >= 32, "Session ID should be sufficiently long"
        
        # Test session validation
        session_data = auth_manager.validate_session(session_id)
        assert session_data is not None
        assert session_data['user_id'] == user['username']
        assert session_data['role'] == user['role']
        assert session_data['ip_address'] == '*************'
        
        # Test session timeout
        time.sleep(1)
        auth_manager.session_timeout = 0.5  # 0.5 seconds for testing
        time.sleep(0.6)
        
        expired_session = auth_manager.validate_session(session_id)
        assert expired_session is None, "Expired session should be invalid"
        
        # Test session invalidation
        new_session_id = auth_manager.create_session(
            user_id=user['username'],
            role=user['role']
        )
        
        auth_manager.invalidate_session(new_session_id)
        invalidated_session = auth_manager.validate_session(new_session_id)
        assert invalidated_session is None, "Invalidated session should be invalid"
        
        # Test concurrent session limits
        session_ids = []
        max_sessions = 3
        
        for i in range(max_sessions + 2):
            sid = auth_manager.create_session(
                user_id=user['username'],
                role=user['role'],
                max_concurrent_sessions=max_sessions
            )
            if sid:
                session_ids.append(sid)
        
        # Should not exceed max sessions
        valid_sessions = [sid for sid in session_ids if auth_manager.validate_session(sid)]
        assert len(valid_sessions) <= max_sessions, f"Too many concurrent sessions: {len(valid_sessions)}"

    def test_brute_force_protection(self, auth_manager, mock_user_data):
        """Test brute force attack protection."""
        user = mock_user_data['valid_user']
        username = user['username']
        correct_password = user['password']
        wrong_password = 'WrongPassword123'
        
        # Test normal authentication
        auth_result = auth_manager.authenticate(username, correct_password)
        assert auth_result['success'], "Valid credentials should authenticate"
        
        # Test failed login attempts
        max_attempts = 5
        
        for attempt in range(max_attempts):
            result = auth_manager.authenticate(username, wrong_password)
            assert not result['success'], f"Wrong password should fail (attempt {attempt + 1})"
            
            if attempt < max_attempts - 1:
                assert not result.get('account_locked', False), f"Account should not be locked yet (attempt {attempt + 1})"
            else:
                assert result.get('account_locked', False), "Account should be locked after max attempts"
        
        # Test that account is locked even with correct password
        locked_result = auth_manager.authenticate(username, correct_password)
        assert not locked_result['success'], "Locked account should reject even correct password"
        assert locked_result.get('account_locked', False), "Should indicate account is locked"
        
        # Test account unlock after timeout
        auth_manager.unlock_account(username)  # Manual unlock for testing
        
        unlocked_result = auth_manager.authenticate(username, correct_password)
        assert unlocked_result['success'], "Unlocked account should accept correct password"

    def test_multi_factor_authentication(self, auth_manager, mock_user_data):
        """Test multi-factor authentication."""
        user = mock_user_data['admin_user']
        username = user['username']
        password = user['password']
        
        # Enable MFA for user
        mfa_secret = auth_manager.enable_mfa(username)
        assert mfa_secret is not None
        assert len(mfa_secret) >= 16, "MFA secret should be sufficiently long"
        
        # Test first factor (password)
        first_factor_result = auth_manager.authenticate_first_factor(username, password)
        assert first_factor_result['success'], "First factor should succeed with correct password"
        assert first_factor_result['requires_mfa'], "Should require second factor"
        
        # Test invalid first factor
        invalid_first_result = auth_manager.authenticate_first_factor(username, 'wrong_password')
        assert not invalid_first_result['success'], "Invalid password should fail first factor"
        
        # Generate TOTP code for testing
        totp_code = auth_manager.generate_totp_code(mfa_secret)
        assert totp_code is not None
        assert len(totp_code) == 6, "TOTP code should be 6 digits"
        assert totp_code.isdigit(), "TOTP code should be numeric"
        
        # Test second factor (TOTP)
        second_factor_result = auth_manager.authenticate_second_factor(username, totp_code)
        assert second_factor_result['success'], "Valid TOTP code should succeed"
        
        # Test invalid TOTP code
        invalid_totp_result = auth_manager.authenticate_second_factor(username, '000000')
        assert not invalid_totp_result['success'], "Invalid TOTP code should fail"
        
        # Test TOTP code reuse protection
        reuse_result = auth_manager.authenticate_second_factor(username, totp_code)
        assert not reuse_result['success'], "TOTP code should not be reusable"
        
        # Test backup codes
        backup_codes = auth_manager.generate_backup_codes(username)
        assert len(backup_codes) >= 8, "Should generate sufficient backup codes"
        
        for code in backup_codes:
            assert len(code) >= 8, "Backup codes should be sufficiently long"
        
        # Test backup code usage
        backup_result = auth_manager.authenticate_backup_code(username, backup_codes[0])
        assert backup_result['success'], "Valid backup code should work"
        
        # Test backup code single use
        reuse_backup_result = auth_manager.authenticate_backup_code(username, backup_codes[0])
        assert not reuse_backup_result['success'], "Backup code should be single use"


@pytest.mark.security
class TestAuthorizationSecurity:
    """Security tests for authorization and access control."""

    @pytest.fixture
    async def auth_system(self, test_config):
        """Set up authorization system for testing."""
        config = DatabaseConfig(test_config)
        pool_manager = DatabasePoolManager(config)
        session_manager = SessionManager(pool_manager)
        crud_manager = CRUDOperations(session_manager)
        auth_manager = AuthenticationManager(config)
        
        system = {
            'config': config,
            'pool_manager': pool_manager,
            'session_manager': session_manager,
            'crud_manager': crud_manager,
            'auth_manager': auth_manager
        }
        
        yield system
        
        await pool_manager.close()

    @pytest.fixture
    def role_permissions(self):
        """Define role-based permissions for testing."""
        return {
            'admin': {
                'permissions': [
                    'read_data', 'write_data', 'delete_data',
                    'admin_access', 'user_management', 'system_config'
                ],
                'data_access': ['all_schemas', 'all_tables']
            },
            'analyst': {
                'permissions': ['read_data', 'write_data', 'export_data'],
                'data_access': ['ep_schema', 'cdr_schema', 'kpi_schema']
            },
            'viewer': {
                'permissions': ['read_data'],
                'data_access': ['ep_schema', 'kpi_schema']
            },
            'guest': {
                'permissions': [],
                'data_access': []
            }
        }

    @pytest.mark.asyncio
    async def test_role_based_access_control(self, auth_system, role_permissions):
        """Test role-based access control (RBAC)."""
        auth_manager = auth_system['auth_manager']
        crud_manager = auth_system['crud_manager']
        
        # Test permission checking
        for role, config in role_permissions.items():
            for permission in config['permissions']:
                assert auth_manager.check_permission(role, permission), \
                    f"Role {role} should have permission {permission}"
            
            # Test permissions not granted
            all_permissions = set()
            for r_config in role_permissions.values():
                all_permissions.update(r_config['permissions'])
            
            missing_permissions = all_permissions - set(config['permissions'])
            for permission in missing_permissions:
                assert not auth_manager.check_permission(role, permission), \
                    f"Role {role} should not have permission {permission}"

        # Test data access control
        test_schemas = ['ep_schema', 'cdr_schema', 'kpi_schema', 'admin_schema']
        
        for role, config in role_permissions.items():
            for schema in test_schemas:
                has_access = auth_manager.check_data_access(role, schema)
                
                if 'all_schemas' in config['data_access'] or schema in config['data_access']:
                    assert has_access, f"Role {role} should have access to {schema}"
                else:
                    assert not has_access, f"Role {role} should not have access to {schema}"

        # Test operation-level access control
        operations = {
            'SELECT': 'read_data',
            'INSERT': 'write_data',
            'UPDATE': 'write_data',
            'DELETE': 'delete_data'
        }
        
        for role, config in role_permissions.items():
            for operation, required_permission in operations.items():
                can_perform = auth_manager.check_operation_permission(role, operation)
                
                if required_permission in config['permissions']:
                    assert can_perform, f"Role {role} should be able to perform {operation}"
                else:
                    assert not can_perform, f"Role {role} should not be able to perform {operation}"

    @pytest.mark.asyncio
    async def test_data_access_isolation(self, auth_system, role_permissions):
        """Test data access isolation between users."""
        crud_manager = auth_system['crud_manager']
        auth_manager = auth_system['auth_manager']
        
        # Create test data for different schemas
        test_data = {
            'ep_data': pd.DataFrame({
                'id': range(10),
                'ep_value': [f'ep_{i}' for i in range(10)],
                'sensitive': ['confidential'] * 10
            }),
            'cdr_data': pd.DataFrame({
                'id': range(10),
                'cdr_value': [f'cdr_{i}' for i in range(10)],
                'phone_number': [f'555-000{i:04d}' for i in range(10)]
            }),
            'admin_data': pd.DataFrame({
                'id': range(5),
                'admin_value': [f'admin_{i}' for i in range(5)],
                'secret_key': ['top_secret'] * 5
            })
        }
        
        # Insert test data
        for table_name, data in test_data.items():
            result = await crud_manager.bulk_insert(table_name, data)
            assert result.success, f"Failed to insert {table_name}"
        
        # Test access control for different roles
        test_users = {
            'admin_user': {'role': 'admin', 'user_id': 'admin_001'},
            'analyst_user': {'role': 'analyst', 'user_id': 'analyst_001'},
            'viewer_user': {'role': 'viewer', 'user_id': 'viewer_001'},
            'guest_user': {'role': 'guest', 'user_id': 'guest_001'}
        }
        
        for user_name, user_info in test_users.items():
            role = user_info['role']
            user_id = user_info['user_id']
            
            # Test read access
            for table_name in test_data.keys():
                schema_name = table_name.split('_')[0] + '_schema'
                
                if auth_manager.check_data_access(role, schema_name) and \
                   auth_manager.check_permission(role, 'read_data'):
                    # Should be able to read
                    try:
                        data = await crud_manager.select_all(table_name, user_context={
                            'user_id': user_id,
                            'role': role
                        })
                        assert len(data) > 0, f"{role} should be able to read {table_name}"
                    except PermissionError:
                        pytest.fail(f"{role} should have read access to {table_name}")
                else:
                    # Should not be able to read
                    with pytest.raises(PermissionError):
                        await crud_manager.select_all(table_name, user_context={
                            'user_id': user_id,
                            'role': role
                        })
            
            # Test write access
            new_data = pd.DataFrame({
                'id': [999],
                'test_value': ['test_insert'],
                'user_id': [user_id]
            })
            
            for table_name in test_data.keys():
                schema_name = table_name.split('_')[0] + '_schema'
                
                if auth_manager.check_data_access(role, schema_name) and \
                   auth_manager.check_permission(role, 'write_data'):
                    # Should be able to write
                    try:
                        result = await crud_manager.bulk_insert(
                            f"{table_name}_write_test", 
                            new_data,
                            user_context={'user_id': user_id, 'role': role}
                        )
                        assert result.success, f"{role} should be able to write to {table_name}"
                    except PermissionError:
                        pytest.fail(f"{role} should have write access to {table_name}")
                else:
                    # Should not be able to write
                    with pytest.raises(PermissionError):
                        await crud_manager.bulk_insert(
                            f"{table_name}_write_test",
                            new_data,
                            user_context={'user_id': user_id, 'role': role}
                        )

    @pytest.mark.asyncio
    async def test_row_level_security(self, auth_system):
        """Test row-level security policies."""
        crud_manager = auth_system['crud_manager']
        auth_manager = auth_system['auth_manager']
        
        # Create test data with ownership information
        user_data = pd.DataFrame({
            'id': range(20),
            'data_value': [f'value_{i}' for i in range(20)],
            'owner_id': ['user_1'] * 5 + ['user_2'] * 5 + ['user_3'] * 5 + ['admin'] * 5,
            'department': ['sales'] * 10 + ['marketing'] * 10,
            'classification': ['public'] * 5 + ['internal'] * 10 + ['confidential'] * 5
        })
        
        result = await crud_manager.bulk_insert('user_owned_data', user_data)
        assert result.success
        
        # Test ownership-based access
        test_cases = [
            {
                'user_id': 'user_1',
                'role': 'analyst',
                'expected_rows': 5,  # Should only see own data
                'filter_condition': "owner_id = 'user_1'"
            },
            {
                'user_id': 'user_2',
                'role': 'analyst',
                'expected_rows': 5,  # Should only see own data
                'filter_condition': "owner_id = 'user_2'"
            },
            {
                'user_id': 'admin',
                'role': 'admin',
                'expected_rows': 20,  # Admin should see all data
                'filter_condition': None
            },
            {
                'user_id': 'dept_manager',
                'role': 'manager',
                'department': 'sales',
                'expected_rows': 10,  # Should see department data
                'filter_condition': "department = 'sales'"
            }
        ]
        
        for test_case in test_cases:
            user_context = {
                'user_id': test_case['user_id'],
                'role': test_case['role'],
                'department': test_case.get('department')
            }
            
            # Apply row-level security filter
            if test_case['filter_condition']:
                query = f"SELECT * FROM user_owned_data WHERE {test_case['filter_condition']}"
            else:
                query = "SELECT * FROM user_owned_data"
            
            filtered_data = await crud_manager.execute_query(
                query,
                user_context=user_context
            )
            
            assert len(filtered_data) == test_case['expected_rows'], \
                f"User {test_case['user_id']} should see {test_case['expected_rows']} rows, got {len(filtered_data)}"
        
        # Test classification-based access
        classification_tests = [
            {
                'user_id': 'public_user',
                'role': 'viewer',
                'clearance_level': 'public',
                'expected_classifications': ['public']
            },
            {
                'user_id': 'internal_user',
                'role': 'analyst',
                'clearance_level': 'internal',
                'expected_classifications': ['public', 'internal']
            },
            {
                'user_id': 'confidential_user',
                'role': 'senior_analyst',
                'clearance_level': 'confidential',
                'expected_classifications': ['public', 'internal', 'confidential']
            }
        ]
        
        for test_case in classification_tests:
            user_context = {
                'user_id': test_case['user_id'],
                'role': test_case['role'],
                'clearance_level': test_case['clearance_level']
            }
            
            # Build classification filter
            allowed_classifications = "', '".join(test_case['expected_classifications'])
            query = f"SELECT * FROM user_owned_data WHERE classification IN ('{allowed_classifications}')"
            
            classified_data = await crud_manager.execute_query(
                query,
                user_context=user_context
            )
            
            # Verify only allowed classifications are returned
            returned_classifications = set(classified_data['classification'].unique())
            expected_classifications = set(test_case['expected_classifications'])
            
            assert returned_classifications.issubset(expected_classifications), \
                f"User {test_case['user_id']} received unauthorized classifications: {returned_classifications - expected_classifications}"

    def test_privilege_escalation_prevention(self, auth_system):
        """Test prevention of privilege escalation attacks."""
        auth_manager = auth_system['auth_manager']
        
        # Test horizontal privilege escalation
        user1_token = auth_manager.generate_jwt_token(
            user_id='user_1',
            role='analyst',
            permissions=['read_data', 'write_data']
        )
        
        # Attempt to modify token to access another user's data
        try:
            # Decode token (this should fail in real scenario due to signature)
            payload = jwt.decode(user1_token, options={"verify_signature": False})
            payload['user_id'] = 'admin_user'  # Try to escalate to admin
            
            # Re-encode without proper signing
            malicious_token = jwt.encode(payload, 'wrong_secret', algorithm='HS256')
            
            # This should fail validation
            validated_payload = auth_manager.validate_jwt_token(malicious_token)
            assert validated_payload is None, "Tampered token should be rejected"
            
        except Exception:
            pass  # Expected to fail
        
        # Test vertical privilege escalation
        viewer_token = auth_manager.generate_jwt_token(
            user_id='viewer_user',
            role='viewer',
            permissions=['read_data']
        )
        
        # Verify viewer cannot perform admin operations
        viewer_payload = auth_manager.validate_jwt_token(viewer_token)
        assert viewer_payload is not None
        assert 'admin_access' not in viewer_payload['permissions']
        assert not auth_manager.check_permission(viewer_payload['role'], 'admin_access')
        
        # Test role modification attempts
        original_role = viewer_payload['role']
        viewer_payload['role'] = 'admin'  # Attempt to modify role
        
        # Role should not be modifiable without proper authentication
        assert not auth_manager.check_permission(original_role, 'admin_access')
        
        # Test permission injection
        malicious_permissions = viewer_payload['permissions'] + ['admin_access', 'delete_data']
        
        # System should only recognize original permissions
        for permission in ['admin_access', 'delete_data']:
            assert not auth_manager.check_permission(original_role, permission)


@pytest.mark.security
class TestDataProtectionSecurity:
    """Security tests for data protection and encryption."""

    @pytest.fixture
    def encryption_manager(self, test_config):
        """Set up encryption manager for testing."""
        config = DatabaseConfig(test_config)
        return DataEncryption(config)

    @pytest.fixture
    def sensitive_data(self):
        """Sample sensitive data for testing."""
        return {
            'personal_data': pd.DataFrame({
                'id': range(5),
                'name': ['John Doe', 'Jane Smith', 'Bob Johnson', 'Alice Brown', 'Charlie Wilson'],
                'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'phone': ['555-0001', '555-0002', '555-0003', '555-0004', '555-0005'],
                'ssn': ['***********', '***********', '***********', '***********', '***********'],
                'credit_card': ['4111-1111-1111-1111', '5555-5555-5555-4444', '3782-822463-10005', '6011-1111-1111-1117', '3056-930902-5904']
            }),
            'financial_data': pd.DataFrame({
                'account_id': range(5),
                'balance': [1000.50, 2500.75, 500.25, 10000.00, 750.80],
                'account_number': ['ACC001', 'ACC002', 'ACC003', 'ACC004', 'ACC005'],
                'routing_number': ['*********', '*********', '*********', '*********', '*********']
            })
        }

    def test_field_level_encryption(self, encryption_manager, sensitive_data):
        """Test field-level encryption for sensitive data."""
        personal_data = sensitive_data['personal_data']
        
        # Define fields that should be encrypted
        encrypted_fields = ['ssn', 'credit_card', 'phone']
        
        # Test encryption
        encrypted_data = personal_data.copy()
        for field in encrypted_fields:
            encrypted_values = []
            for value in personal_data[field]:
                encrypted_value = encryption_manager.encrypt_field(value)
                encrypted_values.append(encrypted_value)
                
                # Verify encryption properties
                assert encrypted_value != value, f"Field {field} should be encrypted"
                assert len(encrypted_value) > len(value), "Encrypted value should be longer"
                assert not any(char.isdigit() and char in value for char in encrypted_value[:10]), \
                    "Encrypted value should not contain original digits"
            
            encrypted_data[field] = encrypted_values
        
        # Test decryption
        decrypted_data = encrypted_data.copy()
        for field in encrypted_fields:
            decrypted_values = []
            for encrypted_value in encrypted_data[field]:
                decrypted_value = encryption_manager.decrypt_field(encrypted_value)
                decrypted_values.append(decrypted_value)
            
            decrypted_data[field] = decrypted_values
        
        # Verify decryption accuracy
        for field in encrypted_fields:
            assert list(decrypted_data[field]) == list(personal_data[field]), \
                f"Decrypted {field} should match original"
        
        # Test encryption key rotation
        old_key = encryption_manager.get_current_key()
        new_key = encryption_manager.rotate_encryption_key()
        
        assert new_key != old_key, "New key should be different from old key"
        
        # Test re-encryption with new key
        re_encrypted_value = encryption_manager.encrypt_field(personal_data['ssn'].iloc[0])
        re_decrypted_value = encryption_manager.decrypt_field(re_encrypted_value)
        
        assert re_decrypted_value == personal_data['ssn'].iloc[0], "Re-encryption should work with new key"

    def test_data_masking_and_anonymization(self, encryption_manager, sensitive_data):
        """Test data masking and anonymization techniques."""
        personal_data = sensitive_data['personal_data']
        
        # Test SSN masking
        masked_ssns = []
        for ssn in personal_data['ssn']:
            masked_ssn = encryption_manager.mask_ssn(ssn)
            masked_ssns.append(masked_ssn)
            
            # Verify masking format (XXX-XX-1234)
            assert masked_ssn.startswith('XXX-XX-'), f"SSN should be masked: {masked_ssn}"
            assert masked_ssn.endswith(ssn[-4:]), f"Last 4 digits should be visible: {masked_ssn}"
        
        # Test credit card masking
        masked_cards = []
        for card in personal_data['credit_card']:
            masked_card = encryption_manager.mask_credit_card(card)
            masked_cards.append(masked_card)
            
            # Verify masking format (XXXX-XXXX-XXXX-1111)
            assert masked_card.count('X') >= 12, f"Credit card should be mostly masked: {masked_card}"
            assert masked_card.endswith(card[-4:]), f"Last 4 digits should be visible: {masked_card}"
        
        # Test email masking
        masked_emails = []
        for email in personal_data['email']:
            masked_email = encryption_manager.mask_email(email)
            masked_emails.append(masked_email)
            
            # Verify email masking (j***@example.com)
            assert '*' in masked_email, f"Email should contain asterisks: {masked_email}"
            assert '@' in masked_email, f"Email should retain @ symbol: {masked_email}"
            assert masked_email.split('@')[1] == email.split('@')[1], "Domain should be preserved"
        
        # Test phone number masking
        masked_phones = []
        for phone in personal_data['phone']:
            masked_phone = encryption_manager.mask_phone(phone)
            masked_phones.append(masked_phone)
            
            # Verify phone masking (XXX-XX01)
            assert 'X' in masked_phone, f"Phone should contain X's: {masked_phone}"
            assert len(masked_phone) == len(phone), "Masked phone should have same length"
        
        # Test data anonymization
        anonymized_data = encryption_manager.anonymize_dataset(personal_data, {
            'name': 'hash',
            'email': 'mask',
            'phone': 'mask',
            'ssn': 'encrypt',
            'credit_card': 'mask'
        })
        
        # Verify anonymization
        assert len(anonymized_data) == len(personal_data), "Anonymized data should have same number of rows"
        
        # Names should be hashed
        for i, name in enumerate(personal_data['name']):
            anonymized_name = anonymized_data['name'].iloc[i]
            assert anonymized_name != name, "Name should be anonymized"
            assert len(anonymized_name) == 64, "Hash should be 64 characters (SHA-256)"
        
        # Verify other fields are properly masked/encrypted
        assert all('*' in email for email in anonymized_data['email']), "Emails should be masked"
        assert all('X' in phone for phone in anonymized_data['phone']), "Phones should be masked"

    def test_data_at_rest_encryption(self, encryption_manager, sensitive_data, tmp_path):
        """Test encryption of data at rest."""
        personal_data = sensitive_data['personal_data']
        
        # Test file encryption
        original_file = tmp_path / "sensitive_data.csv"
        encrypted_file = tmp_path / "sensitive_data.encrypted"
        decrypted_file = tmp_path / "sensitive_data_decrypted.csv"
        
        # Save original data
        personal_data.to_csv(original_file, index=False)
        
        # Encrypt file
        encryption_manager.encrypt_file(str(original_file), str(encrypted_file))
        
        # Verify encrypted file is different
        with open(original_file, 'rb') as f:
            original_content = f.read()
        
        with open(encrypted_file, 'rb') as f:
            encrypted_content = f.read()
        
        assert encrypted_content != original_content, "Encrypted file should be different"
        assert len(encrypted_content) > len(original_content), "Encrypted file should be larger"
        
        # Decrypt file
        encryption_manager.decrypt_file(str(encrypted_file), str(decrypted_file))
        
        # Verify decrypted content matches original
        decrypted_data = pd.read_csv(decrypted_file)
        pd.testing.assert_frame_equal(decrypted_data, personal_data)
        
        # Test database encryption
        encrypted_records = []
        for _, row in personal_data.iterrows():
            encrypted_record = encryption_manager.encrypt_record(row.to_dict())
            encrypted_records.append(encrypted_record)
            
            # Verify sensitive fields are encrypted
            sensitive_fields = ['ssn', 'credit_card']
            for field in sensitive_fields:
                if field in encrypted_record:
                    assert encrypted_record[field] != row[field], f"Field {field} should be encrypted"
        
        # Test batch decryption
        decrypted_records = []
        for encrypted_record in encrypted_records:
            decrypted_record = encryption_manager.decrypt_record(encrypted_record)
            decrypted_records.append(decrypted_record)
        
        decrypted_df = pd.DataFrame(decrypted_records)
        
        # Verify decryption accuracy
        for col in personal_data.columns:
            if col in decrypted_df.columns:
                assert list(decrypted_df[col]) == list(personal_data[col]), \
                    f"Decrypted {col} should match original"

    def test_data_in_transit_encryption(self, encryption_manager):
        """Test encryption of data in transit."""
        # Test message encryption for API communication
        test_messages = [
            {'type': 'query', 'data': 'SELECT * FROM sensitive_table'},
            {'type': 'result', 'data': [{'id': 1, 'ssn': '***********'}]},
            {'type': 'error', 'message': 'Access denied'}
        ]
        
        for message in test_messages:
            # Encrypt message
            encrypted_message = encryption_manager.encrypt_message(json.dumps(message))
            
            # Verify encryption properties
            assert encrypted_message != json.dumps(message), "Message should be encrypted"
            assert isinstance(encrypted_message, (str, bytes)), "Encrypted message should be string or bytes"
            
            # Decrypt message
            decrypted_message = encryption_manager.decrypt_message(encrypted_message)
            decrypted_data = json.loads(decrypted_message)
            
            # Verify decryption accuracy
            assert decrypted_data == message, "Decrypted message should match original"
        
        # Test TLS/SSL configuration validation
        tls_config = encryption_manager.get_tls_config()
        
        assert tls_config['min_version'] >= 'TLSv1.2', "Should require minimum TLS 1.2"
        assert 'weak_ciphers' not in tls_config['allowed_ciphers'], "Should not allow weak ciphers"
        assert tls_config['require_client_cert'] is True, "Should require client certificates"
        
        # Test certificate validation
        cert_validation = encryption_manager.validate_certificate_chain()
        assert cert_validation['valid'], "Certificate chain should be valid"
        assert cert_validation['expiry_days'] > 30, "Certificate should not expire soon"

    def test_key_management_security(self, encryption_manager):
        """Test encryption key management security."""
        # Test key generation
        key1 = encryption_manager.generate_encryption_key()
        key2 = encryption_manager.generate_encryption_key()
        
        assert key1 != key2, "Generated keys should be unique"
        assert len(key1) >= 32, "Key should be at least 256 bits"
        assert isinstance(key1, (str, bytes)), "Key should be string or bytes"
        
        # Test key derivation
        password = "SecureP@ssw0rd123"
        salt = encryption_manager.generate_salt()
        
        derived_key1 = encryption_manager.derive_key_from_password(password, salt)
        derived_key2 = encryption_manager.derive_key_from_password(password, salt)
        
        assert derived_key1 == derived_key2, "Same password and salt should produce same key"
        
        # Different salt should produce different key
        different_salt = encryption_manager.generate_salt()
        derived_key3 = encryption_manager.derive_key_from_password(password, different_salt)
        
        assert derived_key1 != derived_key3, "Different salt should produce different key"
        
        # Test key rotation
        original_key = encryption_manager.get_current_key()
        rotated_key = encryption_manager.rotate_encryption_key()
        
        assert rotated_key != original_key, "Rotated key should be different"
        
        # Test key versioning
        key_version = encryption_manager.get_key_version()
        assert key_version > 0, "Key should have version number"
        
        # Test key backup and recovery
        key_backup = encryption_manager.backup_encryption_keys()
        assert 'current_key' in key_backup, "Backup should include current key"
        assert 'previous_keys' in key_backup, "Backup should include previous keys"
        
        # Test key recovery
        recovery_success = encryption_manager.recover_encryption_keys(key_backup)
        assert recovery_success, "Key recovery should succeed"
        
        # Test key destruction
        old_keys = encryption_manager.get_old_keys()
        destruction_result = encryption_manager.destroy_old_keys(older_than_days=30)
        
        assert destruction_result['destroyed_count'] >= 0, "Should report number of destroyed keys"
        assert destruction_result['success'], "Key destruction should succeed"


@pytest.mark.security
class TestComplianceSecurity:
    """Security tests for compliance and audit requirements."""

    @pytest.fixture
    async def audit_system(self, test_config):
        """Set up audit system for testing."""
        config = DatabaseConfig(test_config)
        pool_manager = DatabasePoolManager(config)
        session_manager = SessionManager(pool_manager)
        crud_manager = CRUDOperations(session_manager)
        audit_logger = AuditLogger(config)
        
        system = {
            'config': config,
            'pool_manager': pool_manager,
            'session_manager': session_manager,
            'crud_manager': crud_manager,
            'audit_logger': audit_logger
        }
        
        yield system
        
        await pool_manager.close()

    @pytest.mark.asyncio
    async def test_audit_logging_compliance(self, audit_system):
        """Test comprehensive audit logging for compliance."""
        audit_logger = audit_system['audit_logger']
        crud_manager = audit_system['crud_manager']
        
        # Test user activity logging
        user_activities = [
            {
                'user_id': 'user_001',
                'action': 'login',
                'resource': 'system',
                'ip_address': '*************',
                'user_agent': 'Mozilla/5.0 Test Browser',
                'success': True
            },
            {
                'user_id': 'user_001',
                'action': 'data_access',
                'resource': 'ep_data_table',
                'query': 'SELECT * FROM ep_data WHERE region = "Berlin"',
                'records_accessed': 150,
                'success': True
            },
            {
                'user_id': 'user_002',
                'action': 'data_modification',
                'resource': 'cdr_data_table',
                'operation': 'UPDATE',
                'records_affected': 25,
                'success': True
            },
            {
                'user_id': 'user_003',
                'action': 'unauthorized_access',
                'resource': 'admin_table',
                'reason': 'insufficient_privileges',
                'success': False
            }
        ]
        
        # Log activities
        for activity in user_activities:
            audit_logger.log_user_activity(**activity)
        
        # Test data access logging
        test_data = pd.DataFrame({
            'id': range(10),
            'sensitive_data': [f'data_{i}' for i in range(10)]
        })
        
        # Insert with audit logging
        insert_result = await crud_manager.bulk_insert(
            'audit_test_table',
            test_data,
            user_context={'user_id': 'user_001', 'session_id': 'session_123'}
        )
        
        # Verify audit log entry was created
        audit_entries = audit_logger.get_audit_entries(
            user_id='user_001',
            action='data_insert',
            start_time=time.time() - 60
        )
        
        assert len(audit_entries) > 0, "Audit entry should be created for data insert"
        
        insert_entry = audit_entries[0]
        assert insert_entry['resource'] == 'audit_test_table'
        assert insert_entry['records_affected'] == len(test_data)
        assert insert_entry['success'] is True
        
        # Test query audit logging
        query_result = await crud_manager.execute_query(
            "SELECT * FROM audit_test_table WHERE id < 5",
            user_context={'user_id': 'user_001', 'session_id': 'session_123'}
        )
        
        query_audit_entries = audit_logger.get_audit_entries(
            user_id='user_001',
            action='data_query',
            start_time=time.time() - 60
        )
        
        assert len(query_audit_entries) > 0, "Audit entry should be created for query"
        
        query_entry = query_audit_entries[0]
        assert 'SELECT' in query_entry['query']
        assert query_entry['records_accessed'] == len(query_result)
        
        # Test failed operation logging
        try:
            await crud_manager.execute_query(
                "SELECT * FROM non_existent_table",
                user_context={'user_id': 'user_001', 'session_id': 'session_123'}
            )
        except Exception:
            pass  # Expected to fail
        
        failed_audit_entries = audit_logger.get_audit_entries(
            user_id='user_001',
            success=False,
            start_time=time.time() - 60
        )
        
        assert len(failed_audit_entries) > 0, "Failed operations should be audited"
        
        # Test audit log integrity
        audit_integrity = audit_logger.verify_audit_log_integrity()
        assert audit_integrity['valid'], "Audit log should maintain integrity"
        assert audit_integrity['total_entries'] > 0, "Should have audit entries"
        
        # Test audit log retention
        retention_policy = audit_logger.get_retention_policy()
        assert retention_policy['retention_days'] >= 365, "Should retain logs for at least 1 year"
        assert retention_policy['backup_enabled'], "Should have backup enabled"

    def test_gdpr_compliance(self, audit_system):
        """Test GDPR compliance features."""
        audit_logger = audit_system['audit_logger']
        
        # Test data subject rights
        data_subject_id = 'subject_001'
        
        # Test right to access (Article 15)
        personal_data_report = audit_logger.generate_personal_data_report(data_subject_id)
        
        assert 'data_categories' in personal_data_report
        assert 'processing_purposes' in personal_data_report
        assert 'data_sources' in personal_data_report
        assert 'retention_periods' in personal_data_report
        assert 'third_party_sharing' in personal_data_report
        
        # Test right to rectification (Article 16)
        rectification_request = {
            'data_subject_id': data_subject_id,
            'field': 'email',
            'old_value': '<EMAIL>',
            'new_value': '<EMAIL>',
            'reason': 'Data subject requested correction'
        }
        
        rectification_result = audit_logger.process_rectification_request(rectification_request)
        assert rectification_result['success'], "Rectification should succeed"
        assert rectification_result['audit_logged'], "Rectification should be audited"
        
        # Test right to erasure (Article 17)
        erasure_request = {
            'data_subject_id': data_subject_id,
            'reason': 'Data subject withdrawal of consent',
            'scope': 'all_personal_data'
        }
        
        erasure_result = audit_logger.process_erasure_request(erasure_request)
        assert erasure_result['success'], "Erasure should succeed"
        assert erasure_result['records_deleted'] >= 0, "Should report deleted records"
        assert erasure_result['audit_logged'], "Erasure should be audited"
        
        # Test right to data portability (Article 20)
        portability_request = {
            'data_subject_id': data_subject_id,
            'format': 'json',
            'include_metadata': True
        }
        
        portability_result = audit_logger.generate_data_export(portability_request)
        assert portability_result['success'], "Data export should succeed"
        assert 'export_file' in portability_result, "Should provide export file"
        assert portability_result['format'] == 'json', "Should respect requested format"
        
        # Test consent management
        consent_record = {
            'data_subject_id': data_subject_id,
            'purpose': 'analytics',
            'consent_given': True,
            'consent_date': time.time(),
            'consent_method': 'web_form',
            'legal_basis': 'consent'
        }
        
        consent_result = audit_logger.record_consent(consent_record)
        assert consent_result['success'], "Consent recording should succeed"
        
        # Test consent withdrawal
        withdrawal_result = audit_logger.withdraw_consent(
            data_subject_id, 'analytics', 'Data subject request'
        )
        assert withdrawal_result['success'], "Consent withdrawal should succeed"
        assert withdrawal_result['processing_stopped'], "Processing should stop after withdrawal"
        
        # Test data processing lawfulness
        processing_activities = audit_logger.get_processing_activities(data_subject_id)
        
        for activity in processing_activities:
            assert 'legal_basis' in activity, "Each activity should have legal basis"
            assert activity['legal_basis'] in [
                'consent', 'contract', 'legal_obligation',
                'vital_interests', 'public_task', 'legitimate_interests'
            ], "Legal basis should be valid GDPR basis"
        
        # Test data breach notification
        breach_incident = {
            'incident_id': 'BREACH_001',
            'description': 'Unauthorized access to customer database',
            'affected_records': 1000,
            'data_categories': ['personal_identifiers', 'contact_details'],
            'severity': 'high',
            'discovery_date': time.time(),
            'containment_measures': 'Access revoked, passwords reset'
        }
        
        breach_result = audit_logger.report_data_breach(breach_incident)
        assert breach_result['success'], "Breach reporting should succeed"
        assert breach_result['notification_required'], "High severity breach should require notification"
        assert breach_result['notification_deadline'] <= 72, "Should notify within 72 hours"

    def test_data_retention_compliance(self, audit_system):
        """Test data retention policy compliance."""
        audit_logger = audit_system['audit_logger']
        
        # Test retention policy definition
        retention_policies = {
            'customer_data': {
                'retention_period_days': 2555,  # 7 years
                'legal_basis': 'legal_obligation',
                'disposal_method': 'secure_deletion'
            },
            'transaction_logs': {
                'retention_period_days': 1825,  # 5 years
                'legal_basis': 'legal_obligation',
                'disposal_method': 'secure_deletion'
            },
            'marketing_data': {
                'retention_period_days': 1095,  # 3 years
                'legal_basis': 'legitimate_interests',
                'disposal_method': 'anonymization'
            },
            'session_logs': {
                'retention_period_days': 90,
                'legal_basis': 'legitimate_interests',
                'disposal_method': 'secure_deletion'
            }
        }
        
        for data_type, policy in retention_policies.items():
            policy_result = audit_logger.set_retention_policy(data_type, policy)
            assert policy_result['success'], f"Setting retention policy for {data_type} should succeed"
        
        # Test retention enforcement
        current_time = time.time()
        
        # Create test data with different ages
        test_records = [
            {
                'data_type': 'session_logs',
                'created_date': current_time - (100 * 24 * 3600),  # 100 days old
                'should_be_deleted': True
            },
            {
                'data_type': 'session_logs',
                'created_date': current_time - (50 * 24 * 3600),   # 50 days old
                'should_be_deleted': False
            },
            {
                'data_type': 'marketing_data',
                'created_date': current_time - (4 * 365 * 24 * 3600),  # 4 years old
                'should_be_deleted': True
            }
        ]
        
        # Run retention enforcement
        retention_result = audit_logger.enforce_retention_policies()
        
        assert retention_result['success'], "Retention enforcement should succeed"
        assert 'deleted_records' in retention_result, "Should report deleted records"
        assert 'anonymized_records' in retention_result, "Should report anonymized records"
        
        # Verify retention compliance report
        compliance_report = audit_logger.generate_retention_compliance_report()
        
        assert 'total_data_types' in compliance_report
        assert 'compliant_data_types' in compliance_report
        assert 'non_compliant_data_types' in compliance_report
        assert compliance_report['compliance_percentage'] >= 95, "Should maintain high compliance"
        
        # Test retention policy updates
        updated_policy = {
            'retention_period_days': 60,  # Reduced from 90
            'legal_basis': 'legitimate_interests',
            'disposal_method': 'secure_deletion',
            'update_reason': 'Business requirement change'
        }
        
        update_result = audit_logger.update_retention_policy('session_logs', updated_policy)
        assert update_result['success'], "Policy update should succeed"
        assert update_result['audit_logged'], "Policy change should be audited"

    def test_security_monitoring_compliance(self, audit_system):
        """Test security monitoring and incident response compliance."""
        audit_logger = audit_system['audit_logger']
        
        # Test security event detection
        security_events = [
            {
                'event_type': 'failed_login',
                'user_id': 'attacker_001',
                'ip_address': '*************',
                'attempts': 10,
                'time_window': 300,  # 5 minutes
                'severity': 'medium'
            },
            {
                'event_type': 'privilege_escalation',
                'user_id': 'user_002',
                'attempted_action': 'admin_access',
                'current_role': 'viewer',
                'severity': 'high'
            }
        ]
        
        # Process security events
        for event in security_events:
            detection_result = audit_logger.detect_security_event(event)
            
            assert detection_result['detected'], f"Security event {event['event_type']} should be detected"
            assert detection_result['severity'] == event['severity'], "Severity should match"
            
            if event['severity'] in ['high', 'critical']:
                assert detection_result['alert_triggered'], "High/critical events should trigger alerts"
                assert detection_result['incident_created'], "High/critical events should create incidents"
        
        # Test incident response workflow
        incident_data = {
            'incident_id': 'INC_001',
            'type': 'data_breach',
            'severity': 'critical',
            'description': 'Unauthorized access to customer database',
            'affected_systems': ['customer_db', 'web_app'],
            'discovery_time': current_time,
            'reporter': 'security_monitor'
        }
        
        incident_result = audit_logger.create_security_incident(incident_data)
        assert incident_result['success'], "Incident creation should succeed"
        assert incident_result['incident_id'] == 'INC_001'
        
        # Test incident escalation
        escalation_result = audit_logger.escalate_incident(
            'INC_001',
            'security_team',
            'Critical data breach requires immediate attention'
        )
        assert escalation_result['success'], "Incident escalation should succeed"
        assert escalation_result['notified_teams'], "Teams should be notified"
        
        # Test incident resolution
        resolution_data = {
            'incident_id': 'INC_001',
            'resolution_time': current_time + 3600,  # 1 hour later
            'resolution_summary': 'Access revoked, vulnerability patched',
            'lessons_learned': 'Implement additional access controls',
            'preventive_measures': 'Enhanced monitoring rules'
        }
        
        resolution_result = audit_logger.resolve_incident(resolution_data)
        assert resolution_result['success'], "Incident resolution should succeed"
        
        # Test compliance reporting
        compliance_report = audit_logger.generate_security_compliance_report()
        
        assert 'incident_count' in compliance_report
        assert 'mean_resolution_time' in compliance_report
        assert 'security_metrics' in compliance_report
        assert compliance_report['compliance_score'] >= 80, "Should maintain good compliance score"
        
        # Test regulatory reporting
        regulatory_report = audit_logger.generate_regulatory_report(
            start_date=current_time - (30 * 24 * 3600),  # Last 30 days
            end_date=current_time,
            regulation='SOX'  # Sarbanes-Oxley
        )
        
        assert 'audit_events' in regulatory_report
        assert 'access_controls' in regulatory_report
        assert 'data_integrity' in regulatory_report
        assert regulatory_report['compliance_status'] == 'compliant'


@pytest.mark.security
class TestVulnerabilityAssessment:
    """Security tests for vulnerability assessment and penetration testing."""

    @pytest.fixture
    def vulnerability_scanner(self, test_config):
        """Set up vulnerability scanner for testing."""
        from src.database.security.scanner import VulnerabilityScanner
        config = DatabaseConfig(test_config)
        return VulnerabilityScanner(config)

    def test_sql_injection_prevention(self, vulnerability_scanner):
        """Test SQL injection vulnerability prevention."""
        # Test common SQL injection patterns
        injection_patterns = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "' UNION SELECT * FROM admin_users --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' OR 1=1 --",
            "admin'--",
            "' OR 'x'='x",
            "'; EXEC xp_cmdshell('dir'); --"
        ]
        
        for pattern in injection_patterns:
            # Test input validation
            is_safe = vulnerability_scanner.validate_sql_input(pattern)
            assert not is_safe, f"SQL injection pattern should be detected: {pattern}"
            
            # Test parameterized query protection
            query_result = vulnerability_scanner.test_parameterized_query(
                "SELECT * FROM users WHERE username = %s",
                [pattern]
            )
            assert query_result['safe'], "Parameterized queries should prevent injection"
            assert not query_result['injection_detected'], "No injection should be detected with proper parameters"
        
        # Test legitimate inputs
        legitimate_inputs = [
            "john_doe",
            "<EMAIL>",
            "normal_password123",
            "O'Connor",  # Legitimate apostrophe
            "user-name_123"
        ]
        
        for input_value in legitimate_inputs:
            is_safe = vulnerability_scanner.validate_sql_input(input_value)
            assert is_safe, f"Legitimate input should be accepted: {input_value}"

    def test_xss_prevention(self, vulnerability_scanner):
        """Test Cross-Site Scripting (XSS) prevention."""
        # Test XSS attack patterns
        xss_patterns = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>"
        ]
        
        for pattern in xss_patterns:
            # Test input sanitization
            sanitized = vulnerability_scanner.sanitize_html_input(pattern)
            assert '<script>' not in sanitized, "Script tags should be removed"
            assert 'javascript:' not in sanitized, "JavaScript URLs should be removed"
            assert 'onerror=' not in sanitized, "Event handlers should be removed"
            assert 'onload=' not in sanitized, "Event handlers should be removed"
            
            # Test XSS detection
            is_xss = vulnerability_scanner.detect_xss_attempt(pattern)
            assert is_xss, f"XSS pattern should be detected: {pattern}"
        
        # Test legitimate HTML
        legitimate_html = [
            "<p>Normal paragraph</p>",
            "<strong>Bold text</strong>",
            "<em>Italic text</em>",
            "<a href='https://example.com'>Link</a>",
            "<img src='image.jpg' alt='Description'>"
        ]
        
        for html in legitimate_html:
            is_xss = vulnerability_scanner.detect_xss_attempt(html)
            assert not is_xss, f"Legitimate HTML should not be flagged: {html}"

    def test_csrf_prevention(self, vulnerability_scanner):
        """Test Cross-Site Request Forgery (CSRF) prevention."""
        # Test CSRF token generation
        token1 = vulnerability_scanner.generate_csrf_token('user_001', 'session_123')
        token2 = vulnerability_scanner.generate_csrf_token('user_001', 'session_123')
        
        assert token1 != token2, "CSRF tokens should be unique"
        assert len(token1) >= 32, "CSRF token should be sufficiently long"
        
        # Test CSRF token validation
        is_valid = vulnerability_scanner.validate_csrf_token(
            token1, 'user_001', 'session_123'
        )
        assert is_valid, "Valid CSRF token should be accepted"
        
        # Test invalid CSRF token
        is_invalid = vulnerability_scanner.validate_csrf_token(
            'invalid_token', 'user_001', 'session_123'
        )
        assert not is_invalid, "Invalid CSRF token should be rejected"
        
        # Test CSRF token expiration
        expired_token = vulnerability_scanner.generate_csrf_token(
            'user_001', 'session_123', expires_in=-3600  # Expired 1 hour ago
        )
        
        is_expired = vulnerability_scanner.validate_csrf_token(
            expired_token, 'user_001', 'session_123'
        )
        assert not is_expired, "Expired CSRF token should be rejected"
        
        # Test CSRF protection for state-changing operations
        state_changing_operations = [
            {'method': 'POST', 'endpoint': '/api/users', 'requires_csrf': True},
            {'method': 'PUT', 'endpoint': '/api/users/123', 'requires_csrf': True},
            {'method': 'DELETE', 'endpoint': '/api/users/123', 'requires_csrf': True},
            {'method': 'PATCH', 'endpoint': '/api/users/123', 'requires_csrf': True},
            {'method': 'GET', 'endpoint': '/api/users', 'requires_csrf': False}
        ]
        
        for operation in state_changing_operations:
            csrf_required = vulnerability_scanner.check_csrf_requirement(
                operation['method'], operation['endpoint']
            )
            assert csrf_required == operation['requires_csrf'], \
                f"CSRF requirement mismatch for {operation['method']} {operation['endpoint']}"

    def test_authentication_vulnerabilities(self, vulnerability_scanner):
        """Test authentication-related vulnerabilities."""
        # Test timing attack prevention
        valid_username = "valid_user"
        invalid_username = "invalid_user"
        password = "test_password"
        
        # Measure authentication timing
        import time
        
        start_time = time.time()
        vulnerability_scanner.authenticate_user(valid_username, password)
        valid_time = time.time() - start_time
        
        start_time = time.time()
        vulnerability_scanner.authenticate_user(invalid_username, password)
        invalid_time = time.time() - start_time
        
        # Timing difference should be minimal to prevent timing attacks
        time_difference = abs(valid_time - invalid_time)
        assert time_difference < 0.1, "Authentication timing should be consistent to prevent timing attacks"
        
        # Test session fixation prevention
        old_session_id = "old_session_123"
        new_session_id = vulnerability_scanner.regenerate_session_id(old_session_id)
        
        assert new_session_id != old_session_id, "Session ID should be regenerated"
        assert len(new_session_id) >= 32, "New session ID should be sufficiently long"
        
        # Test session hijacking prevention
        session_data = {
            'session_id': new_session_id,
            'user_id': 'user_001',
            'ip_address': '*************',
            'user_agent': 'Mozilla/5.0 Test Browser'
        }
        
        # Simulate session from different IP
        hijack_attempt = vulnerability_scanner.validate_session_security(
            new_session_id,
            ip_address='*************',  # Different IP
            user_agent='Mozilla/5.0 Test Browser'
        )
        
        assert not hijack_attempt['valid'], "Session from different IP should be suspicious"
        assert hijack_attempt['risk_level'] == 'high', "IP change should be high risk"
        
        # Simulate session from different user agent
        ua_hijack_attempt = vulnerability_scanner.validate_session_security(
            new_session_id,
            ip_address='*************',
            user_agent='Different Browser'  # Different user agent
        )
        
        assert not ua_hijack_attempt['valid'], "Session from different user agent should be suspicious"
        assert ua_hijack_attempt['risk_level'] in ['medium', 'high'], "User agent change should be risky"

    def test_authorization_vulnerabilities(self, vulnerability_scanner):
        """Test authorization-related vulnerabilities."""
        # Test insecure direct object reference (IDOR)
        user_contexts = [
            {'user_id': 'user_001', 'role': 'user'},
            {'user_id': 'user_002', 'role': 'user'},
            {'user_id': 'admin_001', 'role': 'admin'}
        ]
        
        # Test access to user-specific resources
        for user_context in user_contexts:
            # User should access own resources
            own_access = vulnerability_scanner.check_resource_access(
                user_context,
                resource_type='user_profile',
                resource_id=user_context['user_id']
            )
            assert own_access['allowed'], "User should access own resources"
            
            # User should not access other user's resources (unless admin)
            other_user_id = 'other_user_999'
            other_access = vulnerability_scanner.check_resource_access(
                user_context,
                resource_type='user_profile',
                resource_id=other_user_id
            )
            
            if user_context['role'] == 'admin':
                assert other_access['allowed'], "Admin should access other user resources"
            else:
                assert not other_access['allowed'], "Regular user should not access other user resources"
        
        # Test privilege escalation prevention
        escalation_attempts = [
            {
                'user_context': {'user_id': 'user_001', 'role': 'user'},
                'attempted_action': 'delete_user',
                'should_succeed': False
            },
            {
                'user_context': {'user_id': 'user_001', 'role': 'user'},
                'attempted_action': 'modify_system_config',
                'should_succeed': False
            },
            {
                'user_context': {'user_id': 'admin_001', 'role': 'admin'},
                'attempted_action': 'delete_user',
                'should_succeed': True
            }
        ]
        
        for attempt in escalation_attempts:
            access_result = vulnerability_scanner.check_action_permission(
                attempt['user_context'],
                attempt['attempted_action']
            )
            
            if attempt['should_succeed']:
                assert access_result['allowed'], f"Action {attempt['attempted_action']} should be allowed for {attempt['user_context']['role']}"
            else:
                assert not access_result['allowed'], f"Action {attempt['attempted_action']} should not be allowed for {attempt['user_context']['role']}"

    def test_data_exposure_vulnerabilities(self, vulnerability_scanner):
        """Test data exposure and information disclosure vulnerabilities."""
        # Test sensitive data in error messages
        error_scenarios = [
            {
                'error_type': 'database_connection',
                'original_error': 'Connection failed: password authentication failed for user "admin" host "*************"',
                'should_contain_sensitive': False
            },
            {
                'error_type': 'file_not_found',
                'original_error': 'File not found: /etc/passwd',
                'should_contain_sensitive': False
            },
            {
                'error_type': 'sql_error',
                'original_error': 'Table "secret_admin_table" doesn\'t exist',
                'should_contain_sensitive': False
            }
        ]
        
        for scenario in error_scenarios:
            sanitized_error = vulnerability_scanner.sanitize_error_message(
                scenario['original_error'],
                scenario['error_type']
            )
            
            # Check that sensitive information is removed
            sensitive_patterns = ['password', 'admin', '192.168', '/etc/', 'secret']
            
            for pattern in sensitive_patterns:
                if pattern in scenario['original_error'].lower():
                    assert pattern not in sanitized_error.lower(), \
                        f"Sensitive pattern '{pattern}' should be removed from error message"
        
        # Test debug information exposure
        debug_info = vulnerability_scanner.get_debug_info(include_sensitive=False)
        
        # Should not contain sensitive information
        sensitive_keys = ['password', 'secret', 'key', 'token', 'credential']
        debug_str = str(debug_info).lower()
        
        for sensitive_key in sensitive_keys:
            assert sensitive_key not in debug_str, f"Debug info should not contain '{sensitive_key}'"
        
        # Test HTTP header security
        security_headers = vulnerability_scanner.get_security_headers()
        
        required_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': 'default-src \'self\''
        }
        
        for header, expected_value in required_headers.items():
            assert header in security_headers, f"Security header '{header}' should be present"
            if expected_value:
                assert expected_value in security_headers[header], \
                    f"Security header '{header}' should contain '{expected_value}'"


if __name__ == '__main__':
    pytest.main([__file__, '-v', '--tb=short'])

# Sample security event data for testing SIEM integration
SAMPLE_SECURITY_EVENTS = [
    {
        'event_type': 'failed_login',
        'username': 'attacker',
        'ip_address': '*************',
        'attempts': 5,
        'severity': 'medium'
    },
    {
        'event_type': 'data_exfiltration',
        'user_id': 'user_003',
        'data_volume': 1000000,  # 1MB
        'unusual_pattern': True,
        'severity': 'critical'
    },
    {
        'event_type': 'sql_injection',
        'ip_address': '**********',
        'query_pattern': "'; DROP TABLE users; --",
        'blocked': True,
        'severity': 'high'
    }
]