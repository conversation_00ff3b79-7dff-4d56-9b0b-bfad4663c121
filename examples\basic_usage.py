#!/usr/bin/env python3
"""
Connect Project Basic Usage Examples

Demonstrates the basic usage of geospatial processing modules.
"""

import logging
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from config.settings import Settings
from src.geo import GeometryProcessor, RasterProcessor, VectorProcessor
from src.geo.qgis_integration import QGISIntegration


def setup_logging():
    """Set up logging"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )


def demo_environment_management():
    """Demonstrate environment management features"""
    print("\n=== Environment Management Demo ===")

    env_manager = EnvironmentManager(project_root)

    # Detect Python environments
    environments = env_manager.detect_python_environments()
    print(f"System Python: {environments['system_python']}")
    print(f"Virtual Environments: {len(environments['virtual_envs'])}")
    print(f"QGIS Python: {environments['qgis_python']}")

    # Check Poetry status
    poetry_status = env_manager.check_poetry_status()
    print(f"Poetry available: {poetry_status['poetry_available']}")
    print(f"Installed dependencies: {len(poetry_status['dependencies'])}")

    # Set up QGIS environment
    qgis_setup = env_manager.setup_qgis_environment()
    print(f"QGIS environment setup: {'Success' if qgis_setup else 'Failed'}")


def demo_settings_management():
    """Demonstrate settings management features"""
    print("\n=== Settings Management Demo ===")

    settings = Settings()

    # Get settings
    project_name = settings.get("project.name")
    default_crs = settings.get("geo.default_crs")
    print(f"Project Name: {project_name}")
    print(f"Default CRS: {default_crs}")

    # Update settings
    settings.set("geo.precision", 8)
    settings.update_section("processing", {"max_workers": 8, "chunk_size": 2000})

    # Validate settings
    errors = settings.validate_settings()
    if errors:
        print(f"Settings validation errors: {errors}")
    else:
        print("Settings validation passed")

    # Create necessary directories
    settings.create_directories()
    print("Necessary directories created")

    # Save configuration
    config_file = project_root / "config" / "base.yaml"
    settings.save_to_file(config_file)
    print(f"Configuration saved to: {config_file}")


def demo_geometry_processing():
    """Demonstrate geometry processing features"""
    print("\n=== Geometry Processing Demo ===")

    geo_processor = GeometryProcessor()

    # Create geometry objects
    point = geo_processor.create_point(116.4074, 39.9042)  # Beijing coordinates
    print(f"Created point: {point}")

    # Geometric operations
    buffer = geo_processor.buffer_geometry(point, 1000)  # 1000m buffer
    print(f"Buffer area: {buffer.area:.2f} square meters")

    # Create rectangle
    rectangle = geo_processor.create_rectangle(116.3, 39.8, 116.5, 40.0)
    print(f"Rectangle area: {rectangle.area:.2f} square degrees")

    # Geometric relationship analysis
    intersects = buffer.intersects(rectangle)
    print(f"Buffer intersects rectangle: {intersects}")

    # Coordinate transformation
    try:
        transformed_point = geo_processor.transform_geometry(
            point, "EPSG:4326", "EPSG:3857"
        )
        print(f"Coordinate transformation result: {transformed_point}")
    except Exception as e:
        print(f"Coordinate transformation failed: {e}")


def demo_vector_processing():
    """Demonstrate vector processing features"""
    print("\n=== Vector Processing Demo ===")

    vector_processor = VectorProcessor()

    # Create sample point data
    points_data = [
        {
            "geometry": "POINT(116.4074 39.9042)",
            "name": "Beijing",
            "population": 21540000,
        },
        {
            "geometry": "POINT(121.4737 31.2304)",
            "name": "Shanghai",
            "population": 24280000,
        },
        {
            "geometry": "POINT(113.2644 23.1291)",
            "name": "Guangzhou",
            "population": 15300000,
        },
    ]

    try:
        gdf = vector_processor.create_points_from_coords(
            [
                (116.4074, 39.9042, {"name": "Beijing", "population": 21540000}),
                (121.4737, 31.2304, {"name": "Shanghai", "population": 24280000}),
                (113.2644, 23.1291, {"name": "Guangzhou", "population": 15300000}),
            ]
        )

        print(f"Created {len(gdf)} city points")
        print(f"Data columns: {list(gdf.columns)}")

        # Create buffer
        buffered = vector_processor.create_buffer(gdf, 50000)  # 50km buffer
        print(
            f"Buffer creation complete, total area: {buffered.geometry.area.sum():.2f}"
        )

        # Get statistics
        stats = vector_processor.get_statistics(gdf)
        print(f"Statistics: {stats}")

        # Save to file
        output_dir = project_root / "data" / "output"
        output_dir.mkdir(parents=True, exist_ok=True)

        output_file = output_dir / "cities.geojson"
        vector_processor.save_to_file(gdf, output_file)
        print(f"Data saved to: {output_file}")

    except Exception as e:
        print(f"Vector processing failed: {e}")


def demo_qgis_integration():
    """Demonstrate QGIS integration features"""
    print("\n=== QGIS Integration Demo ===")

    qgis_integration = QGISIntegration()

    # Get QGIS information
    qgis_info = qgis_integration.get_qgis_info()
    print(f"QGIS Path: {qgis_info['QGIS Path']}")
    print(f"QGIS Available: {qgis_info['QGIS Available']}")

    # Try to initialize QGIS
    if qgis_integration.qgis_path:
        try:
            success = qgis_integration.initialize_qgis()
            if success:
                print("QGIS initialized successfully")

                # Get available algorithms (first 10)
                algorithms = qgis_integration.get_available_algorithms()[:10]
                print(f"Sample available algorithms: {algorithms}")

                # Create project
                project = qgis_integration.create_project()
                print("QGIS project created successfully")

                # Clean up resources
                qgis_integration.cleanup_qgis()
                print("QGIS resources cleaned up successfully")
            else:
                print("QGIS initialization failed")
        except Exception as e:
            print(f"QGIS integration test failed: {e}")
    else:
        print("QGIS installation not detected")


def main():
    """Main function demonstrating basic usage."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    logger.info("Starting Connect Project Basic Usage Example")

    # Load settings
    settings = Settings()
    logger.info(f"Loaded settings: {settings.get_all_settings()}")

    # Initialize QGIS integration
    logger.info("Initializing QGIS integration...")
    qgis = QGISIntegration()
    if qgis.initialize_qgis():
        logger.info("QGIS integration initialized successfully")
    else:
        logger.warning("QGIS integration failed to initialize")

    # Example 1: Vector Processing
    logger.info("\n=== Vector Processing Example ===")
    vector_processor = VectorProcessor()
    logger.info("Vector processor initialized")

    # Example 2: Raster Processing
    logger.info("\n=== Raster Processing Example ===")
    raster_processor = RasterProcessor()
    logger.info("Raster processor initialized")

    # Example 3: Geometry Processing
    logger.info("\n=== Geometry Processing Example ===")
    geometry_processor = GeometryProcessor()
    logger.info("Geometry processor initialized")

    logger.info("Basic usage example completed successfully!")


if __name__ == "__main__":
    main()
