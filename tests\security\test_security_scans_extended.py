#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect电信数据分析平台 - 扩展安全测试与渗透测试

本模块提供全面的安全漏洞扫描和渗透测试，包括：
- OWASP Top 10 安全测试
- API安全测试
- 认证与授权测试
- 数据泄露检测
- 网络安全扫描
- 配置安全检查
- 代码安全分析

Author: Connect Team
Date: 2024-01-20
"""

import asyncio
import aiohttp
import json
import re
import hashlib
import base64
import jwt
import ssl
import socket
import subprocess
import tempfile
import os
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict
from urllib.parse import urljoin, urlparse, parse_qs
import xml.etree.ElementTree as ET
from concurrent.futures import ThreadPoolExecutor
import logging
import warnings
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 忽略SSL警告
warnings.filterwarnings('ignore', message='Unverified HTTPS request')


@dataclass
class SecurityVulnerability:
    """安全漏洞信息"""
    vuln_id: str
    title: str
    severity: str  # critical, high, medium, low, info
    category: str  # OWASP category
    description: str
    impact: str
    remediation: str
    evidence: Dict[str, Any]
    cve_references: List[str]
    owasp_category: str
    confidence: float  # 0.0 - 1.0
    timestamp: str


@dataclass
class SecurityScanResult:
    """安全扫描结果"""
    scan_type: str
    target: str
    start_time: str
    end_time: str
    duration: float
    vulnerabilities: List[SecurityVulnerability]
    summary: Dict[str, int]
    recommendations: List[str]
    scan_coverage: Dict[str, bool]
    risk_score: float  # 0.0 - 10.0


@dataclass
class PenetrationTestResult:
    """渗透测试结果"""
    test_name: str
    target: str
    success: bool
    attack_vector: str
    payload_used: str
    response_data: str
    exploitation_proof: str
    impact_assessment: str
    mitigation_steps: List[str]
    timestamp: str


class ExtendedSecurityScanner:
    """扩展安全扫描器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", api_key: str = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = None
        self.scan_results = []
        self.pentest_results = []
        
        # OWASP Top 10 2021 测试配置
        self.owasp_tests = {
            'A01_broken_access_control': self._test_broken_access_control,
            'A02_cryptographic_failures': self._test_cryptographic_failures,
            'A03_injection': self._test_injection_attacks,
            'A04_insecure_design': self._test_insecure_design,
            'A05_security_misconfiguration': self._test_security_misconfiguration,
            'A06_vulnerable_components': self._test_vulnerable_components,
            'A07_identification_failures': self._test_identification_failures,
            'A08_software_integrity_failures': self._test_software_integrity_failures,
            'A09_logging_monitoring_failures': self._test_logging_monitoring_failures,
            'A10_ssrf': self._test_server_side_request_forgery
        }
        
        # 常见攻击载荷
        self.payloads = {
            'sql_injection': [
                "' OR '1'='1",
                "'; DROP TABLE users; --",
                "' UNION SELECT NULL, username, password FROM users --",
                "1' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --",
                "' OR 1=1 LIMIT 1 OFFSET 0 --",
                "'; WAITFOR DELAY '00:00:05' --",
                "' OR (SELECT SUBSTRING(@@version,1,1)) = '5' --"
            ],
            'xss': [
                "<script>alert('XSS')</script>",
                "<img src=x onerror=alert('XSS')>",
                "javascript:alert('XSS')",
                "<svg onload=alert('XSS')>",
                "<iframe src=javascript:alert('XSS')></iframe>",
                "<body onload=alert('XSS')>",
                "<input onfocus=alert('XSS') autofocus>"
            ],
            'command_injection': [
                "; ls -la",
                "| whoami",
                "& dir",
                "; cat /etc/passwd",
                "| type C:\\Windows\\System32\\drivers\\etc\\hosts",
                "; ping -c 4 127.0.0.1",
                "$(whoami)"
            ],
            'ldap_injection': [
                "*)(uid=*))(|(uid=*",
                "*)(|(password=*))",
                "admin)(&(password=*",
                "*))%00"
            ],
            'xpath_injection': [
                "' or '1'='1",
                "' or 1=1 or ''='",
                "x' or name()='username' or 'x'='y",
                "test' and count(/*)=1 and 'test'='test"
            ],
            'nosql_injection': [
                "{'$ne': null}",
                "{'$gt': ''}",
                "{'$regex': '.*'}",
                "{'$where': 'this.username == this.password'}"
            ]
        }
        
        # 敏感信息模式
        self.sensitive_patterns = {
            'api_keys': [
                r'api[_-]?key[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?',
                r'secret[_-]?key[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?',
                r'access[_-]?token[\s]*[:=][\s]*["\']?([a-zA-Z0-9_\-]{20,})["\']?'
            ],
            'passwords': [
                r'password[\s]*[:=][\s]*["\']?([^\s"\';]{8,})["\']?',
                r'passwd[\s]*[:=][\s]*["\']?([^\s"\';]{8,})["\']?',
                r'pwd[\s]*[:=][\s]*["\']?([^\s"\';]{8,})["\']?'
            ],
            'database_urls': [
                r'(postgresql|mysql|mongodb)://[^\s"\';]+',
                r'jdbc:[^\s"\';]+',
                r'mongodb\+srv://[^\s"\';]+'
            ],
            'private_keys': [
                r'-----BEGIN (RSA |DSA |EC |OPENSSH |PGP )?PRIVATE KEY-----',
                r'-----BEGIN ENCRYPTED PRIVATE KEY-----'
            ],
            'jwt_tokens': [
                r'eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*'
            ]
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(ssl=False, verify_ssl=False)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def _test_network_scanning(self) -> SecurityScanResult:
        """网络扫描测试"""
        logger.info("开始网络扫描测试")
        start_time = datetime.now()
        vulnerabilities = []
        
        # 解析目标URL获取主机和端口
        parsed_url = urlparse(self.base_url)
        host = parsed_url.hostname or 'localhost'
        base_port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
        
        # 常见端口扫描
        common_ports = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 3306, 5432, 6379, 27017]
        open_ports = []
        
        for port in common_ports:
            try:
                # 使用socket进行端口扫描
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:
                    open_ports.append(port)
                    
                    # 检查是否为不安全的服务
                    if port in [21, 23, 25, 110, 143]:  # FTP, Telnet, SMTP, POP3, IMAP
                        vulnerabilities.append(SecurityVulnerability(
                            vuln_id=f"NET-001-insecure-service-{port}",
                            title=f"不安全服务开放: 端口 {port}",
                            severity="medium",
                            category="Insecure Service",
                            description=f"不安全服务开放在端口 {port}",
                            impact="可能存在未加密通信或弱认证",
                            remediation="禁用不必要的服务或使用安全替代方案",
                            evidence={"port": port, "host": host},
                            cve_references=[],
                            owasp_category="Network Security",
                            confidence=0.8,
                            timestamp=datetime.now().isoformat()
                        ))
                    
                    # 检查数据库端口
                    if port in [3306, 5432, 6379, 27017]:  # MySQL, PostgreSQL, Redis, MongoDB
                        vulnerabilities.append(SecurityVulnerability(
                            vuln_id=f"NET-002-exposed-database-{port}",
                            title=f"数据库端口暴露: 端口 {port}",
                            severity="high",
                            category="Database Exposure",
                            description=f"数据库端口暴露在端口 {port}",
                            impact="数据库可能被直接访问",
                            remediation="限制数据库端口访问或使用防火墙",
                            evidence={"port": port, "host": host},
                            cve_references=[],
                            owasp_category="Network Security",
                            confidence=0.9,
                            timestamp=datetime.now().isoformat()
                        ))
                        
            except Exception as e:
                logger.debug(f"端口扫描失败 {port}: {e}")
        
        # 检查SSL/TLS配置
        if base_port == 443 or parsed_url.scheme == 'https':
            try:
                import ssl
                import socket
                
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                with socket.create_connection((host, 443), timeout=10) as sock:
                    with context.wrap_socket(sock, server_hostname=host) as ssock:
                        cert = ssock.getpeercert()
                        cipher = ssock.cipher()
                        
                        # 检查弱加密套件
                        if cipher and len(cipher) >= 3:
                            cipher_name = cipher[0]
                            if any(weak in cipher_name.upper() for weak in ['RC4', 'DES', 'MD5']):
                                vulnerabilities.append(SecurityVulnerability(
                                    vuln_id="NET-003-weak-cipher",
                                    title="弱加密套件",
                                    severity="medium",
                                    category="Weak Encryption",
                                    description=f"使用弱加密套件: {cipher_name}",
                                    impact="通信可能被破解",
                                    remediation="使用强加密套件",
                                    evidence={"cipher": cipher_name, "host": host},
                                    cve_references=[],
                                    owasp_category="Cryptographic Failures",
                                    confidence=0.9,
                                    timestamp=datetime.now().isoformat()
                                ))
                        
                        # 检查证书有效期
                        if cert:
                            import datetime as dt
                            not_after = dt.datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
                            days_until_expiry = (not_after - dt.datetime.now()).days
                            
                            if days_until_expiry < 30:
                                severity = "high" if days_until_expiry < 0 else "medium"
                                vulnerabilities.append(SecurityVulnerability(
                                    vuln_id="NET-004-cert-expiry",
                                    title="SSL证书即将过期或已过期",
                                    severity=severity,
                                    category="Certificate Issue",
                                    description=f"SSL证书将在 {days_until_expiry} 天后过期",
                                    impact="可能导致服务不可用或安全警告",
                                    remediation="更新SSL证书",
                                    evidence={"days_until_expiry": days_until_expiry, "not_after": cert['notAfter']},
                                    cve_references=[],
                                    owasp_category="Cryptographic Failures",
                                    confidence=0.9,
                                    timestamp=datetime.now().isoformat()
                                ))
                        
            except Exception as e:
                logger.error(f"SSL检查失败: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="network_scanning",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_network_recommendations(vulnerabilities),
            scan_coverage={'port_scan': True, 'ssl_check': True, 'open_ports': open_ports},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_web_penetration(self) -> SecurityScanResult:
        """Web渗透测试"""
        logger.info("开始Web渗透测试")
        start_time = datetime.now()
        vulnerabilities = []
        
        # 目录遍历测试
        directory_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "....//....//....//etc/passwd"
        ]
        
        for payload in directory_payloads:
            try:
                # 测试文件参数
                url = urljoin(self.base_url, "/api/files")
                params = {"file": payload, "path": payload}
                
                async with self.session.get(url, params=params) as response:
                    content = await response.text()
                    
                    # 检查是否成功读取系统文件
                    if any(pattern in content.lower() for pattern in ['root:', 'bin:', 'daemon:', 'localhost']):
                        vulnerabilities.append(SecurityVulnerability(
                            vuln_id=f"WEB-001-directory-traversal-{payload[:10]}",
                            title="目录遍历漏洞",
                            severity="high",
                            category="Directory Traversal",
                            description=f"目录遍历漏洞: {payload}",
                            impact="可能读取系统敏感文件",
                            remediation="验证和过滤文件路径参数",
                            evidence={"payload": payload, "response_snippet": content[:200]},
                            cve_references=[],
                            owasp_category="A01:2021 – Broken Access Control",
                            confidence=0.9,
                            timestamp=datetime.now().isoformat()
                        ))
                        
            except Exception as e:
                logger.debug(f"目录遍历测试失败 {payload}: {e}")
        
        # 文件上传测试
        upload_endpoints = ["/api/upload", "/api/files/upload", "/upload"]
        
        for endpoint in upload_endpoints:
            try:
                url = urljoin(self.base_url, endpoint)
                
                # 测试恶意文件上传
                malicious_files = [
                    ("test.php", "<?php echo 'test'; ?>", "application/x-php"),
                    ("test.jsp", "<% out.println('test'); %>", "application/x-jsp"),
                    ("test.exe", "MZ\x90\x00", "application/octet-stream")
                ]
                
                for filename, content, content_type in malicious_files:
                    data = aiohttp.FormData()
                    data.add_field('file', content, filename=filename, content_type=content_type)
                    
                    async with self.session.post(url, data=data) as response:
                        if response.status == 200:
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"WEB-002-malicious-upload-{filename}",
                                title="恶意文件上传",
                                severity="critical",
                                category="Malicious File Upload",
                                description=f"可以上传恶意文件: {filename}",
                                impact="可能导致代码执行或系统入侵",
                                remediation="实施文件类型验证和上传限制",
                                evidence={"filename": filename, "endpoint": endpoint},
                                cve_references=[],
                                owasp_category="A03:2021 – Injection",
                                confidence=0.9,
                                timestamp=datetime.now().isoformat()
                            ))
                            
            except Exception as e:
                logger.debug(f"文件上传测试失败 {endpoint}: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="web_penetration",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_web_recommendations(vulnerabilities),
            scan_coverage={'directory_traversal': True, 'file_upload': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_api_security(self) -> SecurityScanResult:
        """API安全测试"""
        logger.info("开始API安全测试")
        start_time = datetime.now()
        vulnerabilities = []
        
        # API端点发现
        api_endpoints = [
            "/api/v1/users", "/api/users", "/api/admin", "/api/config",
            "/api/data", "/api/files", "/api/auth", "/api/health"
        ]
        
        discovered_endpoints = []
        
        for endpoint in api_endpoints:
            try:
                url = urljoin(self.base_url, endpoint)
                async with self.session.get(url) as response:
                    if response.status != 404:
                        discovered_endpoints.append((endpoint, response.status))
                        
                        # 检查是否返回敏感信息
                        content = await response.text()
                        
                        # 检查API文档泄露
                        if any(keyword in content.lower() for keyword in ['swagger', 'openapi', 'api documentation']):
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"API-001-doc-exposure-{endpoint.replace('/', '_')}",
                                title="API文档暴露",
                                severity="medium",
                                category="Information Disclosure",
                                description=f"API文档暴露: {endpoint}",
                                impact="可能泄露API结构和功能",
                                remediation="保护API文档或限制访问",
                                evidence={"endpoint": endpoint, "status_code": response.status},
                                cve_references=[],
                                owasp_category="A01:2021 – Broken Access Control",
                                confidence=0.8,
                                timestamp=datetime.now().isoformat()
                            ))
                        
                        # 检查敏感数据泄露
                        sensitive_patterns = [
                            r'"password"\s*:\s*"[^"]+"',
                            r'"token"\s*:\s*"[^"]+"',
                            r'"secret"\s*:\s*"[^"]+"',
                            r'"key"\s*:\s*"[^"]+"'
                        ]
                        
                        for pattern in sensitive_patterns:
                            if re.search(pattern, content, re.IGNORECASE):
                                vulnerabilities.append(SecurityVulnerability(
                                    vuln_id=f"API-002-sensitive-data-{endpoint.replace('/', '_')}",
                                    title="API敏感数据泄露",
                                    severity="high",
                                    category="Sensitive Data Exposure",
                                    description=f"API返回敏感数据: {endpoint}",
                                    impact="敏感信息可能被泄露",
                                    remediation="过滤API响应中的敏感数据",
                                    evidence={"endpoint": endpoint, "pattern": pattern},
                                    cve_references=[],
                                    owasp_category="A02:2021 – Cryptographic Failures",
                                    confidence=0.9,
                                    timestamp=datetime.now().isoformat()
                                ))
                                break
                        
            except Exception as e:
                logger.debug(f"API端点测试失败 {endpoint}: {e}")
        
        # 测试API速率限制
        if discovered_endpoints:
            test_endpoint = discovered_endpoints[0][0]
            url = urljoin(self.base_url, test_endpoint)
            
            try:
                # 快速发送多个请求
                tasks = []
                for i in range(20):
                    tasks.append(self.session.get(url))
                
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 检查是否有速率限制
                rate_limited = False
                for response in responses:
                    if not isinstance(response, Exception) and hasattr(response, 'status'):
                        if response.status == 429:  # Too Many Requests
                            rate_limited = True
                            break
                        await response.release()
                
                if not rate_limited:
                    vulnerabilities.append(SecurityVulnerability(
                        vuln_id="API-003-no-rate-limit",
                        title="缺少API速率限制",
                        severity="medium",
                        category="No Rate Limiting",
                        description="API缺少速率限制",
                        impact="可能被滥用或遭受DoS攻击",
                        remediation="实施API速率限制",
                        evidence={"endpoint": test_endpoint, "requests_sent": 20},
                        cve_references=[],
                        owasp_category="A04:2021 – Insecure Design",
                        confidence=0.8,
                        timestamp=datetime.now().isoformat()
                    ))
                    
            except Exception as e:
                logger.error(f"API速率限制测试失败: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="api_security",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_api_recommendations(vulnerabilities),
            scan_coverage={'endpoint_discovery': True, 'rate_limiting': True, 'discovered_endpoints': discovered_endpoints},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    def _generate_network_recommendations(self, vulnerabilities: List[SecurityVulnerability]) -> List[str]:
        """生成网络安全建议"""
        recommendations = [
            "关闭不必要的端口和服务",
            "使用防火墙限制网络访问",
            "更新SSL/TLS配置",
            "定期更新SSL证书"
        ]
        return recommendations
    
    def _generate_web_recommendations(self, vulnerabilities: List[SecurityVulnerability]) -> List[str]:
        """生成Web安全建议"""
        recommendations = [
            "实施输入验证和过滤",
            "限制文件上传类型和大小",
            "使用安全的文件处理机制",
            "实施访问控制"
        ]
        return recommendations
    
    def _generate_api_recommendations(self, vulnerabilities: List[SecurityVulnerability]) -> List[str]:
        """生成API安全建议"""
        recommendations = [
            "实施API认证和授权",
            "添加API速率限制",
            "过滤敏感数据响应",
            "保护API文档"
        ]
        return recommendations
    
    async def save_results(self, results: List[SecurityScanResult], output_file: str = None) -> str:
        """保存扫描结果到文件"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"security_scan_results_{timestamp}.json"
        
        # 转换结果为可序列化格式
        serializable_results = []
        for result in results:
            serializable_result = {
                "scan_type": result.scan_type,
                "target": result.target,
                "start_time": result.start_time,
                "end_time": result.end_time,
                "duration": result.duration,
                "vulnerabilities": [
                    {
                        "vuln_id": vuln.vuln_id,
                        "title": vuln.title,
                        "severity": vuln.severity,
                        "category": vuln.category,
                        "description": vuln.description,
                        "impact": vuln.impact,
                        "remediation": vuln.remediation,
                        "evidence": vuln.evidence,
                        "cve_references": vuln.cve_references,
                        "owasp_category": vuln.owasp_category,
                        "confidence": vuln.confidence,
                        "timestamp": vuln.timestamp
                    }
                    for vuln in result.vulnerabilities
                ],
                "summary": result.summary,
                "recommendations": result.recommendations,
                "scan_coverage": result.scan_coverage,
                "risk_score": result.risk_score
            }
            serializable_results.append(serializable_result)
        
        # 保存到文件
        try:
            import json
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "scan_timestamp": datetime.now().isoformat(),
                    "total_scans": len(results),
                    "total_vulnerabilities": sum(len(r.vulnerabilities) for r in results),
                    "results": serializable_results
                }, f, indent=2, ensure_ascii=False)
            
            logger.info(f"扫描结果已保存到: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
            raise
    
    async def run_comprehensive_scan(self) -> SecurityScanResult:
        """运行全面安全扫描"""
        start_time = datetime.now()
        logger.info(f"开始对 {self.base_url} 进行全面安全扫描")
        
        all_vulnerabilities = []
        scan_coverage = {}
        
        # 运行所有OWASP测试
        for test_name, test_func in self.owasp_tests.items():
            try:
                logger.info(f"执行 {test_name} 测试")
                vulnerabilities = await test_func()
                all_vulnerabilities.extend(vulnerabilities)
                scan_coverage[test_name] = True
                logger.info(f"{test_name} 测试完成，发现 {len(vulnerabilities)} 个漏洞")
            except Exception as e:
                logger.error(f"{test_name} 测试失败: {e}")
                scan_coverage[test_name] = False
        
        # 运行额外安全测试
        additional_tests = [
            ('ssl_tls_scan', self._test_ssl_tls_security),
            ('cors_scan', self._test_cors_configuration),
            ('headers_scan', self._test_security_headers),
            ('file_upload_scan', self._test_file_upload_security),
            ('session_management_scan', self._test_session_management),
            ('rate_limiting_scan', self._test_rate_limiting),
            ('information_disclosure_scan', self._test_information_disclosure)
        ]
        
        for test_name, test_func in additional_tests:
            try:
                logger.info(f"执行 {test_name} 测试")
                vulnerabilities = await test_func()
                all_vulnerabilities.extend(vulnerabilities)
                scan_coverage[test_name] = True
                logger.info(f"{test_name} 测试完成，发现 {len(vulnerabilities)} 个漏洞")
            except Exception as e:
                logger.error(f"{test_name} 测试失败: {e}")
                scan_coverage[test_name] = False
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # 生成摘要统计
        summary = {
            'critical': len([v for v in all_vulnerabilities if v.severity == 'critical']),
            'high': len([v for v in all_vulnerabilities if v.severity == 'high']),
            'medium': len([v for v in all_vulnerabilities if v.severity == 'medium']),
            'low': len([v for v in all_vulnerabilities if v.severity == 'low']),
            'info': len([v for v in all_vulnerabilities if v.severity == 'info']),
            'total': len(all_vulnerabilities)
        }
        
        # 计算风险评分
        risk_score = self._calculate_risk_score(summary)
        
        # 生成建议
        recommendations = self._generate_recommendations(all_vulnerabilities)
        
        result = SecurityScanResult(
            scan_type="comprehensive_security_scan",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=duration,
            vulnerabilities=all_vulnerabilities,
            summary=summary,
            recommendations=recommendations,
            scan_coverage=scan_coverage,
            risk_score=risk_score
        )
        
        self.scan_results.append(result)
        logger.info(f"安全扫描完成，总计发现 {len(all_vulnerabilities)} 个漏洞，风险评分: {risk_score:.1f}/10")
        
        return result
    
    # ==================== OWASP Top 10 测试方法 ====================
    
    async def _test_broken_access_control(self) -> List[SecurityVulnerability]:
        """A01: 访问控制缺陷测试"""
        vulnerabilities = []
        
        # 测试未授权访问
        test_endpoints = [
            '/api/v1/admin',
            '/api/v1/users',
            '/api/v1/data/export',
            '/admin',
            '/dashboard',
            '/api/v1/system/config'
        ]
        
        for endpoint in test_endpoints:
            try:
                url = urljoin(self.base_url, endpoint)
                async with self.session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        if any(keyword in content.lower() for keyword in ['admin', 'user', 'config', 'dashboard']):
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"BAC-001-{endpoint.replace('/', '_')}",
                                title=f"未授权访问: {endpoint}",
                                severity="high",
                                category="Broken Access Control",
                                description=f"端点 {endpoint} 允许未授权访问",
                                impact="攻击者可能访问敏感功能或数据",
                                remediation="实施适当的访问控制和身份验证",
                                evidence={"url": url, "status_code": response.status, "response_length": len(content)},
                                cve_references=[],
                                owasp_category="A01:2021 – Broken Access Control",
                                confidence=0.8,
                                timestamp=datetime.now().isoformat()
                            ))
            except Exception as e:
                logger.debug(f"访问控制测试异常 {endpoint}: {e}")
        
        # 测试水平权限提升
        await self._test_horizontal_privilege_escalation(vulnerabilities)
        
        # 测试垂直权限提升
        await self._test_vertical_privilege_escalation(vulnerabilities)
        
        return vulnerabilities
    
    async def _test_horizontal_privilege_escalation(self, vulnerabilities: List[SecurityVulnerability]):
        """测试水平权限提升"""
        # 测试用户ID枚举
        user_endpoints = [
            '/api/v1/users/1',
            '/api/v1/users/2',
            '/api/v1/profile/1',
            '/api/v1/profile/2'
        ]
        
        for endpoint in user_endpoints:
            try:
                url = urljoin(self.base_url, endpoint)
                async with self.session.get(url) as response:
                    if response.status == 200:
                        vulnerabilities.append(SecurityVulnerability(
                            vuln_id=f"HPE-001-{endpoint.replace('/', '_')}",
                            title=f"水平权限提升: {endpoint}",
                            severity="medium",
                            category="Broken Access Control",
                            description=f"可能存在用户数据越权访问",
                            impact="用户可能访问其他用户的敏感信息",
                            remediation="实施用户级别的访问控制验证",
                            evidence={"url": url, "status_code": response.status},
                            cve_references=[],
                            owasp_category="A01:2021 – Broken Access Control",
                            confidence=0.6,
                            timestamp=datetime.now().isoformat()
                        ))
            except Exception as e:
                logger.debug(f"水平权限测试异常 {endpoint}: {e}")
    
    async def _test_vertical_privilege_escalation(self, vulnerabilities: List[SecurityVulnerability]):
        """测试垂直权限提升"""
        # 测试管理员功能访问
        admin_endpoints = [
            '/api/v1/admin/users',
            '/api/v1/admin/system',
            '/api/v1/admin/logs',
            '/api/v1/system/shutdown'
        ]
        
        for endpoint in admin_endpoints:
            try:
                url = urljoin(self.base_url, endpoint)
                async with self.session.get(url) as response:
                    if response.status in [200, 403]:  # 403也可能泄露端点存在
                        vulnerabilities.append(SecurityVulnerability(
                            vuln_id=f"VPE-001-{endpoint.replace('/', '_')}",
                            title=f"潜在垂直权限提升: {endpoint}",
                            severity="high" if response.status == 200 else "medium",
                            category="Broken Access Control",
                            description=f"管理员端点可能存在权限提升风险",
                            impact="普通用户可能获得管理员权限",
                            remediation="强化管理员功能的访问控制",
                            evidence={"url": url, "status_code": response.status},
                            cve_references=[],
                            owasp_category="A01:2021 – Broken Access Control",
                            confidence=0.7,
                            timestamp=datetime.now().isoformat()
                        ))
            except Exception as e:
                logger.debug(f"垂直权限测试异常 {endpoint}: {e}")
    
    async def _test_cryptographic_failures(self) -> List[SecurityVulnerability]:
        """A02: 加密机制失效测试"""
        vulnerabilities = []
        
        # 测试弱加密算法
        await self._test_weak_encryption(vulnerabilities)
        
        # 测试敏感数据传输
        await self._test_sensitive_data_transmission(vulnerabilities)
        
        # 测试密码存储
        await self._test_password_storage(vulnerabilities)
        
        return vulnerabilities
    
    async def _test_weak_encryption(self, vulnerabilities: List[SecurityVulnerability]):
        """测试弱加密算法"""
        try:
            # 检查SSL/TLS配置
            parsed_url = urlparse(self.base_url)
            if parsed_url.scheme == 'https':
                hostname = parsed_url.hostname
                port = parsed_url.port or 443
                
                # 检查SSL证书和加密套件
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                with socket.create_connection((hostname, port), timeout=10) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        cipher = ssock.cipher()
                        if cipher:
                            cipher_name = cipher[0]
                            # 检查弱加密套件
                            weak_ciphers = ['RC4', 'DES', '3DES', 'MD5', 'SHA1']
                            if any(weak in cipher_name for weak in weak_ciphers):
                                vulnerabilities.append(SecurityVulnerability(
                                    vuln_id="CF-001-weak-cipher",
                                    title="弱加密套件",
                                    severity="medium",
                                    category="Cryptographic Failures",
                                    description=f"使用了弱加密套件: {cipher_name}",
                                    impact="可能被攻击者破解加密通信",
                                    remediation="升级到强加密套件（如AES-256）",
                                    evidence={"cipher": cipher_name, "hostname": hostname},
                                    cve_references=[],
                                    owasp_category="A02:2021 – Cryptographic Failures",
                                    confidence=0.9,
                                    timestamp=datetime.now().isoformat()
                                ))
        except Exception as e:
            logger.debug(f"加密测试异常: {e}")
    
    async def _test_sensitive_data_transmission(self, vulnerabilities: List[SecurityVulnerability]):
        """测试敏感数据传输"""
        # 检查是否强制HTTPS
        if self.base_url.startswith('http://'):
            vulnerabilities.append(SecurityVulnerability(
                vuln_id="CF-002-http-transmission",
                title="敏感数据HTTP传输",
                severity="high",
                category="Cryptographic Failures",
                description="应用程序使用HTTP传输敏感数据",
                impact="敏感数据可能被中间人攻击截获",
                remediation="强制使用HTTPS传输所有敏感数据",
                evidence={"url": self.base_url},
                cve_references=[],
                owasp_category="A02:2021 – Cryptographic Failures",
                confidence=1.0,
                timestamp=datetime.now().isoformat()
            ))
    
    async def _test_password_storage(self, vulnerabilities: List[SecurityVulnerability]):
        """测试密码存储安全性"""
        # 尝试获取用户信息，检查密码是否明文存储
        test_endpoints = ['/api/v1/users', '/api/v1/profile']
        
        for endpoint in test_endpoints:
            try:
                url = urljoin(self.base_url, endpoint)
                async with self.session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        # 检查是否包含明文密码
                        if re.search(r'"password"\s*:\s*"[^"]{8,}"', content):
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"CF-003-plaintext-password-{endpoint.replace('/', '_')}",
                                title="明文密码存储",
                                severity="critical",
                                category="Cryptographic Failures",
                                description="API响应中包含明文密码",
                                impact="用户密码可能被泄露",
                                remediation="使用强哈希算法存储密码，不在API中返回密码",
                                evidence={"url": url, "endpoint": endpoint},
                                cve_references=[],
                                owasp_category="A02:2021 – Cryptographic Failures",
                                confidence=0.9,
                                timestamp=datetime.now().isoformat()
                            ))
            except Exception as e:
                 logger.debug(f"密码存储测试异常 {endpoint}: {e}")
    
    async def _test_injection_attacks(self) -> List[SecurityVulnerability]:
        """A03: 注入攻击测试"""
        vulnerabilities = []
        
        # SQL注入测试
        await self._test_sql_injection(vulnerabilities)
        
        # NoSQL注入测试
        await self._test_nosql_injection(vulnerabilities)
        
        # 命令注入测试
        await self._test_command_injection(vulnerabilities)
        
        # LDAP注入测试
        await self._test_ldap_injection(vulnerabilities)
        
        # XPath注入测试
        await self._test_xpath_injection(vulnerabilities)
        
        return vulnerabilities
    
    async def _test_sql_injection(self, vulnerabilities: List[SecurityVulnerability]):
        """SQL注入测试"""
        test_endpoints = [
            '/api/v1/users',
            '/api/v1/data/search',
            '/api/v1/reports',
            '/login',
            '/search'
        ]
        
        for endpoint in test_endpoints:
            for payload in self.attack_payloads['sql_injection']:
                try:
                    url = urljoin(self.base_url, endpoint)
                    
                    # GET参数注入
                    params = {'id': payload, 'search': payload, 'filter': payload}
                    async with self.session.get(url, params=params) as response:
                        content = await response.text()
                        if self._detect_sql_injection(content, response.status):
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"INJ-001-sql-{endpoint.replace('/', '_')}-{hash(payload) % 10000}",
                                title=f"SQL注入漏洞: {endpoint}",
                                severity="high",
                                category="Injection",
                                description=f"端点 {endpoint} 存在SQL注入漏洞",
                                impact="攻击者可能读取、修改或删除数据库数据",
                                remediation="使用参数化查询和输入验证",
                                evidence={"url": url, "payload": payload, "method": "GET"},
                                cve_references=[],
                                owasp_category="A03:2021 – Injection",
                                confidence=0.8,
                                timestamp=datetime.now().isoformat()
                            ))
                    
                    # POST数据注入
                    data = {'username': payload, 'password': 'test', 'email': payload}
                    async with self.session.post(url, data=data) as response:
                        content = await response.text()
                        if self._detect_sql_injection(content, response.status):
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"INJ-002-sql-{endpoint.replace('/', '_')}-{hash(payload) % 10000}",
                                title=f"SQL注入漏洞: {endpoint} (POST)",
                                severity="high",
                                category="Injection",
                                description=f"端点 {endpoint} 的POST数据存在SQL注入漏洞",
                                impact="攻击者可能读取、修改或删除数据库数据",
                                remediation="使用参数化查询和输入验证",
                                evidence={"url": url, "payload": payload, "method": "POST"},
                                cve_references=[],
                                owasp_category="A03:2021 – Injection",
                                confidence=0.8,
                                timestamp=datetime.now().isoformat()
                            ))
                    
                    await asyncio.sleep(0.1)  # 避免过于频繁的请求
                    
                except Exception as e:
                    logger.debug(f"SQL注入测试异常 {endpoint} with {payload}: {e}")
    
    def _detect_sql_injection(self, content: str, status_code: int) -> bool:
        """检测SQL注入响应特征"""
        sql_error_patterns = [
            r'SQL syntax.*MySQL',
            r'Warning.*mysql_.*',
            r'valid MySQL result',
            r'PostgreSQL.*ERROR',
            r'Warning.*pg_.*',
            r'valid PostgreSQL result',
            r'ORA-[0-9]{5}',
            r'Oracle error',
            r'Oracle.*Driver',
            r'SQLServer JDBC Driver',
            r'SqlException',
            r'SQLite.*error',
            r'sqlite3.OperationalError',
            r'SQLSTATE\[\w+\]',
            r'Unclosed quotation mark',
            r'Microsoft OLE DB Provider for ODBC Drivers'
        ]
        
        content_lower = content.lower()
        for pattern in sql_error_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        
        # 检查异常状态码
        if status_code == 500:
            return True
            
        return False
    
    async def _test_nosql_injection(self, vulnerabilities: List[SecurityVulnerability]):
        """NoSQL注入测试"""
        test_endpoints = ['/api/v1/users', '/api/v1/data/search']
        
        for endpoint in test_endpoints:
            for payload in self.attack_payloads['nosql_injection']:
                try:
                    url = urljoin(self.base_url, endpoint)
                    
                    # JSON数据注入
                    json_data = {'filter': payload, 'query': payload}
                    async with self.session.post(url, json=json_data) as response:
                        content = await response.text()
                        if self._detect_nosql_injection(content, response.status):
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"INJ-003-nosql-{endpoint.replace('/', '_')}-{hash(payload) % 10000}",
                                title=f"NoSQL注入漏洞: {endpoint}",
                                severity="high",
                                category="Injection",
                                description=f"端点 {endpoint} 存在NoSQL注入漏洞",
                                impact="攻击者可能绕过认证或访问未授权数据",
                                remediation="验证和清理输入数据，使用安全的查询方法",
                                evidence={"url": url, "payload": payload},
                                cve_references=[],
                                owasp_category="A03:2021 – Injection",
                                confidence=0.7,
                                timestamp=datetime.now().isoformat()
                            ))
                    
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.debug(f"NoSQL注入测试异常 {endpoint} with {payload}: {e}")
    
    def _detect_nosql_injection(self, content: str, status_code: int) -> bool:
        """检测NoSQL注入响应特征"""
        nosql_error_patterns = [
            r'MongoError',
            r'CouchDB.*error',
            r'RethinkDB.*error',
            r'Cassandra.*error',
            r'Redis.*error',
            r'MongoDB.*error'
        ]
        
        for pattern in nosql_error_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
                
        return False
    
    async def _test_command_injection(self, vulnerabilities: List[SecurityVulnerability]):
        """命令注入测试"""
        test_endpoints = ['/api/v1/system', '/api/v1/tools', '/api/v1/export']
        
        for endpoint in test_endpoints:
            for payload in self.attack_payloads['command_injection']:
                try:
                    url = urljoin(self.base_url, endpoint)
                    
                    # 测试各种参数
                    params = {'cmd': payload, 'command': payload, 'exec': payload}
                    async with self.session.get(url, params=params) as response:
                        content = await response.text()
                        if self._detect_command_injection(content, response.status):
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"INJ-004-cmd-{endpoint.replace('/', '_')}-{hash(payload) % 10000}",
                                title=f"命令注入漏洞: {endpoint}",
                                severity="critical",
                                category="Injection",
                                description=f"端点 {endpoint} 存在命令注入漏洞",
                                impact="攻击者可能在服务器上执行任意命令",
                                remediation="避免执行用户输入，使用白名单验证",
                                evidence={"url": url, "payload": payload},
                                cve_references=[],
                                owasp_category="A03:2021 – Injection",
                                confidence=0.9,
                                timestamp=datetime.now().isoformat()
                            ))
                    
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.debug(f"命令注入测试异常 {endpoint} with {payload}: {e}")
    
    def _detect_command_injection(self, content: str, status_code: int) -> bool:
        """检测命令注入响应特征"""
        command_output_patterns = [
            r'uid=\d+\(\w+\)',  # Unix用户信息
            r'Microsoft Windows',  # Windows版本信息
            r'Linux.*\d+\.\d+',  # Linux内核版本
            r'total \d+',  # ls命令输出
            r'Directory of',  # Windows dir命令
            r'Volume.*Serial Number',  # Windows卷信息
            r'/bin/sh',  # Shell路径
            r'command not found',  # 命令未找到错误
            r'Permission denied'  # 权限拒绝错误
        ]
        
        for pattern in command_output_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
                
        return False
    
    async def _test_ldap_injection(self, vulnerabilities: List[SecurityVulnerability]):
        """LDAP注入测试"""
        test_endpoints = ['/api/v1/auth/ldap', '/login', '/api/v1/users/search']
        
        for endpoint in test_endpoints:
            for payload in self.attack_payloads['ldap_injection']:
                try:
                    url = urljoin(self.base_url, endpoint)
                    
                    data = {'username': payload, 'password': 'test', 'search': payload}
                    async with self.session.post(url, data=data) as response:
                        content = await response.text()
                        if self._detect_ldap_injection(content, response.status):
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"INJ-005-ldap-{endpoint.replace('/', '_')}-{hash(payload) % 10000}",
                                title=f"LDAP注入漏洞: {endpoint}",
                                severity="medium",
                                category="Injection",
                                description=f"端点 {endpoint} 存在LDAP注入漏洞",
                                impact="攻击者可能绕过LDAP认证或获取敏感信息",
                                remediation="验证和转义LDAP查询输入",
                                evidence={"url": url, "payload": payload},
                                cve_references=[],
                                owasp_category="A03:2021 – Injection",
                                confidence=0.6,
                                timestamp=datetime.now().isoformat()
                            ))
                    
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.debug(f"LDAP注入测试异常 {endpoint} with {payload}: {e}")
    
    def _detect_ldap_injection(self, content: str, status_code: int) -> bool:
        """检测LDAP注入响应特征"""
        ldap_error_patterns = [
            r'LDAP.*error',
            r'Invalid DN syntax',
            r'Bad search filter',
            r'javax.naming.directory'
        ]
        
        for pattern in ldap_error_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
                
        return False
    
    async def _test_xpath_injection(self, vulnerabilities: List[SecurityVulnerability]):
        """XPath注入测试"""
        test_endpoints = ['/api/v1/xml/search', '/api/v1/data/query']
        
        for endpoint in test_endpoints:
            for payload in self.attack_payloads['xpath_injection']:
                try:
                    url = urljoin(self.base_url, endpoint)
                    
                    params = {'query': payload, 'xpath': payload}
                    async with self.session.get(url, params=params) as response:
                        content = await response.text()
                        if self._detect_xpath_injection(content, response.status):
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"INJ-006-xpath-{endpoint.replace('/', '_')}-{hash(payload) % 10000}",
                                title=f"XPath注入漏洞: {endpoint}",
                                severity="medium",
                                category="Injection",
                                description=f"端点 {endpoint} 存在XPath注入漏洞",
                                impact="攻击者可能访问XML文档中的敏感数据",
                                remediation="验证和转义XPath查询输入",
                                evidence={"url": url, "payload": payload},
                                cve_references=[],
                                owasp_category="A03:2021 – Injection",
                                confidence=0.6,
                                timestamp=datetime.now().isoformat()
                            ))
                    
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.debug(f"XPath注入测试异常 {endpoint} with {payload}: {e}")
    
    def _detect_xpath_injection(self, content: str, status_code: int) -> bool:
        """检测XPath注入响应特征"""
        xpath_error_patterns = [
            r'XPath.*error',
            r'Invalid XPath',
            r'XPathException',
            r'org.apache.xpath'
        ]
        
        for pattern in xpath_error_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
                
        return False
    
    async def _test_security_misconfiguration(self) -> SecurityScanResult:
        """A05:2021 - 安全配置错误测试"""
        logger.info("开始安全配置错误测试")
        start_time = datetime.now()
        vulnerabilities = []
        
        # 测试配置错误
        config_tests = [
            {
                "name": "默认凭据测试",
                "endpoint": "/admin",
                "method": "POST",
                "data": {"username": "admin", "password": "admin"},
                "expected_status": [401, 403]
            },
            {
                "name": "目录遍历测试",
                "endpoint": "/api/files",
                "method": "GET",
                "params": {"path": "../../../etc/passwd"},
                "expected_status": [400, 403, 404]
            },
            {
                "name": "调试信息泄露测试",
                "endpoint": "/api/debug",
                "method": "GET",
                "expected_status": [404, 403]
            },
            {
                "name": "HTTP方法测试",
                "endpoint": "/api/data",
                "method": "TRACE",
                "expected_status": [405, 501]
            }
        ]
        
        for test in config_tests:
            try:
                url = urljoin(self.base_url, test['endpoint'])
                
                if test["method"] == "POST":
                    async with self.session.post(url, json=test.get("data", {})) as response:
                        await self._check_config_response(response, test, vulnerabilities)
                elif test["method"] == "GET":
                    async with self.session.get(url, params=test.get("params", {})) as response:
                        await self._check_config_response(response, test, vulnerabilities)
                else:
                    async with self.session.request(test["method"], url) as response:
                        await self._check_config_response(response, test, vulnerabilities)
                
            except Exception as e:
                logger.error(f"配置错误测试失败 {test['name']}: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="security_misconfiguration",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_config_recommendations(vulnerabilities),
            scan_coverage={'config_tests': True, 'security_headers': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _check_config_response(self, response, test, vulnerabilities):
        """检查配置测试响应"""
        # 检查是否存在配置错误
        if response.status not in test["expected_status"]:
            severity = "high" if response.status == 200 else "medium"
            
            vulnerabilities.append(SecurityVulnerability(
                vuln_id=f"MISC-001-{test['name'].replace(' ', '_')}",
                title=f"{test['name']}失败",
                severity=severity,
                category="Security Misconfiguration",
                description=f"{test['name']}失败: 返回状态码 {response.status}",
                impact="可能暴露敏感功能或信息",
                remediation="检查服务器配置，禁用不必要的功能和端点",
                evidence={"status_code": response.status, "endpoint": test['endpoint']},
                cve_references=[],
                owasp_category="A05:2021 – Security Misconfiguration",
                confidence=0.8,
                timestamp=datetime.now().isoformat()
            ))
        
        # 检查响应头安全配置
        security_headers = [
            "X-Content-Type-Options",
            "X-Frame-Options", 
            "X-XSS-Protection",
            "Strict-Transport-Security",
            "Content-Security-Policy"
        ]
        
        missing_headers = []
        for header in security_headers:
            if header not in response.headers:
                missing_headers.append(header)
        
        if missing_headers:
            vulnerabilities.append(SecurityVulnerability(
                vuln_id=f"MISC-002-missing-headers-{test['endpoint'].replace('/', '_')}",
                title="缺少安全响应头",
                severity="medium",
                category="Security Misconfiguration",
                description=f"缺少安全响应头: {', '.join(missing_headers)}",
                impact="可能导致XSS、点击劫持等攻击",
                remediation="添加必要的安全响应头",
                evidence={"missing_headers": missing_headers, "endpoint": test['endpoint']},
                cve_references=[],
                owasp_category="A05:2021 – Security Misconfiguration",
                confidence=0.9,
                timestamp=datetime.now().isoformat()
            ))
    
    async def _test_vulnerable_components(self) -> SecurityScanResult:
        """A06:2021 - 易受攻击和过时的组件测试"""
        logger.info("开始易受攻击组件测试")
        start_time = datetime.now()
        vulnerabilities = []
        
        # 检查服务器信息泄露
        try:
            async with self.session.get(self.base_url) as response:
                # 检查服务器版本信息
                server_header = response.headers.get("Server", "")
                if server_header:
                    # 检查是否暴露版本信息
                    version_pattern = r'\d+\.\d+'
                    if re.search(version_pattern, server_header):
                        vulnerabilities.append(SecurityVulnerability(
                            vuln_id="VULN-001-server-version",
                            title="服务器版本信息泄露",
                            severity="low",
                            category="Information Disclosure",
                            description=f"服务器版本信息泄露: {server_header}",
                            impact="可能帮助攻击者识别已知漏洞",
                            remediation="隐藏或移除服务器版本信息",
                            evidence={"server_header": server_header},
                            cve_references=[],
                            owasp_category="A06:2021 – Vulnerable and Outdated Components",
                            confidence=0.9,
                            timestamp=datetime.now().isoformat()
                        ))
                
                # 检查X-Powered-By头
                powered_by = response.headers.get("X-Powered-By", "")
                if powered_by:
                    vulnerabilities.append(SecurityVulnerability(
                        vuln_id="VULN-002-powered-by",
                        title="技术栈信息泄露",
                        severity="low",
                        category="Information Disclosure",
                        description=f"技术栈信息泄露: {powered_by}",
                        impact="可能帮助攻击者了解技术栈",
                        remediation="移除X-Powered-By响应头",
                        evidence={"powered_by": powered_by},
                        cve_references=[],
                        owasp_category="A06:2021 – Vulnerable and Outdated Components",
                        confidence=0.8,
                        timestamp=datetime.now().isoformat()
                    ))
                
        except Exception as e:
            logger.error(f"组件检查失败: {e}")
        
        # 检查已知的易受攻击路径
        vulnerable_paths = [
            "/phpinfo.php", "/info.php", "/test.php", "/.env", "/config.php",
            "/wp-config.php", "/admin/config.php", "/backup.sql", "/database.sql"
        ]
        
        for path in vulnerable_paths:
            try:
                url = urljoin(self.base_url, path)
                async with self.session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        
                        # 检查敏感信息
                        sensitive_patterns = [
                            r'password\s*=\s*["\'][^"\']]+["\']',
                            r'api[_-]?key\s*[=:]\s*["\'][^"\']]+["\']',
                            r'secret\s*[=:]\s*["\'][^"\']]+["\']',
                            r'database\s*[=:]\s*["\'][^"\']]+["\']'
                        ]
                        
                        for pattern in sensitive_patterns:
                            if re.search(pattern, content, re.IGNORECASE):
                                vulnerabilities.append(SecurityVulnerability(
                                    vuln_id=f"VULN-003-sensitive-file-{path.replace('/', '_')}",
                                    title=f"敏感文件暴露: {path}",
                                    severity="critical",
                                    category="Sensitive Data Exposure",
                                    description=f"敏感文件暴露: {path}",
                                    impact="敏感信息可能被泄露",
                                    remediation="移除或保护敏感文件",
                                    evidence={"path": path, "pattern_matched": True},
                                    cve_references=[],
                                    owasp_category="A06:2021 – Vulnerable and Outdated Components",
                                    confidence=0.9,
                                    timestamp=datetime.now().isoformat()
                                ))
                                break
                        else:
                            vulnerabilities.append(SecurityVulnerability(
                                vuln_id=f"VULN-004-accessible-file-{path.replace('/', '_')}",
                                title=f"敏感文件可访问: {path}",
                                severity="medium",
                                category="Information Disclosure",
                                description=f"敏感文件可访问: {path}",
                                impact="可能泄露系统信息",
                                remediation="移除或保护敏感文件",
                                evidence={"path": path, "status_code": response.status},
                                cve_references=[],
                                owasp_category="A06:2021 – Vulnerable and Outdated Components",
                                confidence=0.7,
                                timestamp=datetime.now().isoformat()
                            ))
                
            except Exception as e:
                logger.debug(f"路径检查失败 {path}: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="vulnerable_components",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_component_recommendations(vulnerabilities),
            scan_coverage={'version_detection': True, 'path_scanning': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_identification_authentication_failures(self) -> SecurityScanResult:
        """A07:2021 - 身份识别和身份验证失败测试"""
        logger.info("开始身份验证失败测试")
        start_time = datetime.now()
        vulnerabilities = []
        
        # 测试弱密码策略
        weak_passwords = [
            "123456", "password", "admin", "test", "123123",
            "qwerty", "abc123", "password123", "admin123"
        ]
        
        login_endpoint = "/api/auth/login"
        
        for password in weak_passwords:
            try:
                url = urljoin(self.base_url, login_endpoint)
                data = {"username": "admin", "password": password}
                
                async with self.session.post(url, json=data) as response:
                    if response.status == 200:
                        vulnerabilities.append(SecurityVulnerability(
                            vuln_id=f"AUTH-001-weak-password-{password}",
                            title=f"弱密码可以登录: {password}",
                            severity="high",
                            category="Weak Password Policy",
                            description=f"弱密码可以登录: {password}",
                            impact="账户可能被暴力破解",
                            remediation="实施强密码策略",
                            evidence={"password": password, "endpoint": login_endpoint},
                            cve_references=[],
                            owasp_category="A07:2021 – Identification and Authentication Failures",
                            confidence=0.9,
                            timestamp=datetime.now().isoformat()
                        ))
                
            except Exception as e:
                logger.debug(f"弱密码测试失败 {password}: {e}")
        
        # 测试暴力破解保护
        try:
            url = urljoin(self.base_url, login_endpoint)
            # 连续尝试错误登录
            for i in range(10):
                data = {"username": "admin", "password": f"wrong_password_{i}"}
                async with self.session.post(url, json=data) as response:
                    # 检查是否有速率限制
                    if i > 5 and response.status != 429:  # 429 Too Many Requests
                        vulnerabilities.append(SecurityVulnerability(
                            vuln_id="AUTH-002-no-brute-force-protection",
                            title="缺少暴力破解保护",
                            severity="medium",
                            category="No Brute Force Protection",
                            description="缺少暴力破解保护",
                            impact="账户可能被暴力破解",
                            remediation="实施登录尝试限制和账户锁定机制",
                            evidence={"attempts": i+1, "endpoint": login_endpoint},
                            cve_references=[],
                            owasp_category="A07:2021 – Identification and Authentication Failures",
                            confidence=0.8,
                            timestamp=datetime.now().isoformat()
                        ))
                        break
                
        except Exception as e:
            logger.error(f"暴力破解测试失败: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="identification_authentication_failures",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_auth_recommendations(vulnerabilities),
            scan_coverage={'weak_passwords': True, 'brute_force': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    def _generate_config_recommendations(self, vulnerabilities: List[SecurityVulnerability]) -> List[str]:
        """生成配置安全建议"""
        recommendations = [
            "实施安全的服务器配置",
            "添加必要的安全响应头",
            "禁用不必要的HTTP方法",
            "保护敏感目录和文件"
        ]
        return recommendations
    
    def _generate_component_recommendations(self, vulnerabilities: List[SecurityVulnerability]) -> List[str]:
        """生成组件安全建议"""
        recommendations = [
            "隐藏服务器版本信息",
            "移除技术栈信息泄露",
            "保护或移除敏感文件",
            "定期更新组件版本"
        ]
        return recommendations
    
    def _generate_auth_recommendations(self, vulnerabilities: List[SecurityVulnerability]) -> List[str]:
        """生成认证安全建议"""
        recommendations = [
            "实施强密码策略",
            "添加暴力破解保护",
            "实施多因素认证",
            "使用安全的会话管理"
        ]
        return recommendations

    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=30,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': 'Connect-Security-Scanner/1.0'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def run_comprehensive_security_scan(self) -> Dict[str, Any]:
        """运行全面的安全扫描"""
        logger.info("开始全面安全扫描")
        start_time = time.time()
        
        scan_results = []
        
        # 1. OWASP Top 10 测试
        logger.info("执行 OWASP Top 10 安全测试")
        owasp_results = await self._run_owasp_tests()
        scan_results.extend(owasp_results)
        
        # 2. API安全测试
        logger.info("执行 API 安全测试")
        api_results = await self._run_api_security_tests()
        scan_results.extend(api_results)
        
        # 3. 认证与授权测试
        logger.info("执行认证与授权安全测试")
        auth_results = await self._run_authentication_tests()
        scan_results.extend(auth_results)
        
        # 4. 网络安全扫描
        logger.info("执行网络安全扫描")
        network_results = await self._run_network_security_scan()
        scan_results.extend(network_results)
        
        # 5. 配置安全检查
        logger.info("执行配置安全检查")
        config_results = await self._run_configuration_security_check()
        scan_results.extend(config_results)
        
        # 6. 数据泄露检测
        logger.info("执行数据泄露检测")
        leak_results = await self._run_data_leak_detection()
        scan_results.extend(leak_results)
        
        # 7. 代码安全分析
        logger.info("执行代码安全分析")
        code_results = await self._run_code_security_analysis()
        scan_results.extend(code_results)
        
        end_time = time.time()
        
        # 生成综合报告
        comprehensive_report = self._generate_comprehensive_report(
            scan_results, start_time, end_time
        )
        
        logger.info(f"全面安全扫描完成，耗时: {end_time - start_time:.2f}秒")
        return comprehensive_report
    
    async def _run_owasp_tests(self) -> List[SecurityScanResult]:
        """运行 OWASP Top 10 测试"""
        results = []
        
        for test_name, test_func in self.owasp_tests.items():
            logger.info(f"执行 OWASP 测试: {test_name}")
            try:
                result = await test_func()
                results.append(result)
            except Exception as e:
                logger.error(f"OWASP 测试 {test_name} 失败: {e}")
                # 创建错误结果
                error_result = SecurityScanResult(
                    scan_type=test_name,
                    target=self.base_url,
                    start_time=datetime.now().isoformat(),
                    end_time=datetime.now().isoformat(),
                    duration=0,
                    vulnerabilities=[],
                    summary={'error': 1},
                    recommendations=[f"测试执行失败: {str(e)}"],
                    scan_coverage={test_name: False},
                    risk_score=0.0
                )
                results.append(error_result)
        
        return results
    
    async def _test_broken_access_control(self) -> SecurityScanResult:
        """A01: 访问控制缺陷测试"""
        start_time = datetime.now()
        vulnerabilities = []
        
        # 测试未授权访问
        test_endpoints = [
            '/api/admin/users',
            '/api/admin/config',
            '/api/user/profile',
            '/api/data/export',
            '/admin',
            '/dashboard'
        ]
        
        for endpoint in test_endpoints:
            try:
                # 无认证访问测试
                async with self.session.get(f"{self.base_url}{endpoint}") as resp:
                    if resp.status == 200:
                        vuln = SecurityVulnerability(
                            vuln_id=f"BAC-001-{endpoint.replace('/', '_')}",
                            title=f"未授权访问: {endpoint}",
                            severity="high",
                            category="A01:2021 – Broken Access Control",
                            description=f"端点 {endpoint} 允许未授权访问",
                            impact="攻击者可能访问敏感功能或数据",
                            remediation="实施适当的访问控制和身份验证",
                            evidence={
                                'endpoint': endpoint,
                                'status_code': resp.status,
                                'response_headers': dict(resp.headers)
                            },
                            cve_references=[],
                            owasp_category="A01",
                            confidence=0.9,
                            timestamp=datetime.now().isoformat()
                        )
                        vulnerabilities.append(vuln)
                
                # 权限提升测试
                headers = {'X-User-Role': 'admin', 'X-Admin': 'true'}
                async with self.session.get(f"{self.base_url}{endpoint}", headers=headers) as resp:
                    if resp.status == 200:
                        vuln = SecurityVulnerability(
                            vuln_id=f"BAC-002-{endpoint.replace('/', '_')}",
                            title=f"权限提升漏洞: {endpoint}",
                            severity="critical",
                            category="A01:2021 – Broken Access Control",
                            description=f"通过修改请求头可以提升权限访问 {endpoint}",
                            impact="攻击者可能获得管理员权限",
                            remediation="在服务器端验证用户权限，不依赖客户端提供的权限信息",
                            evidence={
                                'endpoint': endpoint,
                                'headers_used': headers,
                                'status_code': resp.status
                            },
                            cve_references=[],
                            owasp_category="A01",
                            confidence=0.95,
                            timestamp=datetime.now().isoformat()
                        )
                        vulnerabilities.append(vuln)
            
            except Exception as e:
                logger.warning(f"访问控制测试失败 {endpoint}: {e}")
        
        # 测试路径遍历
        path_traversal_payloads = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
            '....//....//....//etc/passwd'
        ]
        
        for payload in path_traversal_payloads:
            try:
                async with self.session.get(f"{self.base_url}/api/files/{payload}") as resp:
                    response_text = await resp.text()
                    if 'root:' in response_text or 'localhost' in response_text:
                        vuln = SecurityVulnerability(
                            vuln_id=f"BAC-003-{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                            title="路径遍历漏洞",
                            severity="high",
                            category="A01:2021 – Broken Access Control",
                            description="应用程序存在路径遍历漏洞，可以访问系统文件",
                            impact="攻击者可能读取敏感系统文件",
                            remediation="验证和清理文件路径输入，使用白名单限制可访问文件",
                            evidence={
                                'payload': payload,
                                'response_snippet': response_text[:200],
                                'status_code': resp.status
                            },
                            cve_references=['CWE-22'],
                            owasp_category="A01",
                            confidence=0.9,
                            timestamp=datetime.now().isoformat()
                        )
                        vulnerabilities.append(vuln)
            except Exception as e:
                logger.warning(f"路径遍历测试失败 {payload}: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="broken_access_control",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_access_control_recommendations(vulnerabilities),
            scan_coverage={'unauthorized_access': True, 'privilege_escalation': True, 'path_traversal': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_cryptographic_failures(self) -> SecurityScanResult:
        """A02: 加密失败测试"""
        start_time = datetime.now()
        vulnerabilities = []
        
        # 测试弱加密算法
        test_urls = [f"{self.base_url}/api/login", f"{self.base_url}/api/auth"]
        
        for url in test_urls:
            try:
                # 检查SSL/TLS配置
                parsed_url = urlparse(url)
                if parsed_url.scheme == 'https':
                    ssl_issues = await self._check_ssl_configuration(parsed_url.hostname, parsed_url.port or 443)
                    vulnerabilities.extend(ssl_issues)
                
                # 测试弱密码哈希
                weak_passwords = ['123456', 'password', 'admin', 'test']
                for password in weak_passwords:
                    login_data = {'username': 'admin', 'password': password}
                    async with self.session.post(url, json=login_data) as resp:
                        if resp.status == 200:
                            response_data = await resp.text()
                            # 检查响应中是否包含明文密码或弱哈希
                            if password in response_data or len(password) < 8:
                                vuln = SecurityVulnerability(
                                    vuln_id=f"CF-001-{hashlib.md5(password.encode()).hexdigest()[:8]}",
                                    title="弱密码策略",
                                    severity="medium",
                                    category="A02:2021 – Cryptographic Failures",
                                    description="系统接受弱密码或在响应中暴露密码信息",
                                    impact="攻击者可能通过暴力破解获得访问权限",
                                    remediation="实施强密码策略，使用安全的密码哈希算法",
                                    evidence={
                                        'weak_password': password,
                                        'url': url,
                                        'status_code': resp.status
                                    },
                                    cve_references=['CWE-521'],
                                    owasp_category="A02",
                                    confidence=0.8,
                                    timestamp=datetime.now().isoformat()
                                )
                                vulnerabilities.append(vuln)
            
            except Exception as e:
                logger.warning(f"加密测试失败 {url}: {e}")
        
        # 检查敏感数据传输
        sensitive_endpoints = ['/api/user/profile', '/api/data/export']
        for endpoint in sensitive_endpoints:
            try:
                async with self.session.get(f"{self.base_url}{endpoint}") as resp:
                    if resp.status == 200:
                        # 检查是否通过HTTP传输敏感数据
                        if self.base_url.startswith('http://') and not self.base_url.startswith('https://'):
                            vuln = SecurityVulnerability(
                                vuln_id=f"CF-002-{endpoint.replace('/', '_')}",
                                title="敏感数据未加密传输",
                                severity="high",
                                category="A02:2021 – Cryptographic Failures",
                                description=f"敏感端点 {endpoint} 通过HTTP传输数据",
                                impact="敏感数据可能被中间人攻击截获",
                                remediation="使用HTTPS加密所有敏感数据传输",
                                evidence={
                                    'endpoint': endpoint,
                                    'protocol': 'HTTP',
                                    'status_code': resp.status
                                },
                                cve_references=['CWE-319'],
                                owasp_category="A02",
                                confidence=1.0,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
            
            except Exception as e:
                logger.warning(f"敏感数据传输测试失败 {endpoint}: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="cryptographic_failures",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_crypto_recommendations(vulnerabilities),
            scan_coverage={'ssl_tls': True, 'password_policy': True, 'data_transmission': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_injection_attacks(self) -> SecurityScanResult:
        """A03: 注入攻击测试"""
        start_time = datetime.now()
        vulnerabilities = []
        
        # SQL注入测试
        sql_test_endpoints = [
            '/api/users/search',
            '/api/data/query',
            '/api/login',
            '/api/search'
        ]
        
        for endpoint in sql_test_endpoints:
            for payload in self.payloads['sql_injection']:
                try:
                    # GET参数注入
                    params = {'q': payload, 'search': payload, 'id': payload}
                    async with self.session.get(f"{self.base_url}{endpoint}", params=params) as resp:
                        response_text = await resp.text()
                        if self._detect_sql_injection(response_text, resp.status):
                            vuln = SecurityVulnerability(
                                vuln_id=f"INJ-001-{hashlib.md5((endpoint + payload).encode()).hexdigest()[:8]}",
                                title=f"SQL注入漏洞: {endpoint}",
                                severity="critical",
                                category="A03:2021 – Injection",
                                description=f"端点 {endpoint} 存在SQL注入漏洞",
                                impact="攻击者可能读取、修改或删除数据库数据",
                                remediation="使用参数化查询或预编译语句",
                                evidence={
                                    'endpoint': endpoint,
                                    'payload': payload,
                                    'method': 'GET',
                                    'response_snippet': response_text[:500],
                                    'status_code': resp.status
                                },
                                cve_references=['CWE-89'],
                                owasp_category="A03",
                                confidence=0.9,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
                    
                    # POST数据注入
                    post_data = {'username': payload, 'password': 'test', 'query': payload}
                    async with self.session.post(f"{self.base_url}{endpoint}", json=post_data) as resp:
                        response_text = await resp.text()
                        if self._detect_sql_injection(response_text, resp.status):
                            vuln = SecurityVulnerability(
                                vuln_id=f"INJ-002-{hashlib.md5((endpoint + payload).encode()).hexdigest()[:8]}",
                                title=f"SQL注入漏洞 (POST): {endpoint}",
                                severity="critical",
                                category="A03:2021 – Injection",
                                description=f"端点 {endpoint} 的POST数据存在SQL注入漏洞",
                                impact="攻击者可能通过POST请求执行恶意SQL",
                                remediation="验证和清理所有用户输入",
                                evidence={
                                    'endpoint': endpoint,
                                    'payload': payload,
                                    'method': 'POST',
                                    'response_snippet': response_text[:500],
                                    'status_code': resp.status
                                },
                                cve_references=['CWE-89'],
                                owasp_category="A03",
                                confidence=0.9,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
                
                except Exception as e:
                    logger.warning(f"SQL注入测试失败 {endpoint} with {payload}: {e}")
        
        # XSS测试
        xss_test_endpoints = [
            '/api/comments',
            '/api/search',
            '/api/feedback'
        ]
        
        for endpoint in xss_test_endpoints:
            for payload in self.payloads['xss']:
                try:
                    # 反射型XSS测试
                    params = {'q': payload, 'search': payload}
                    async with self.session.get(f"{self.base_url}{endpoint}", params=params) as resp:
                        response_text = await resp.text()
                        if payload in response_text and 'text/html' in resp.headers.get('content-type', ''):
                            vuln = SecurityVulnerability(
                                vuln_id=f"INJ-003-{hashlib.md5((endpoint + payload).encode()).hexdigest()[:8]}",
                                title=f"反射型XSS漏洞: {endpoint}",
                                severity="high",
                                category="A03:2021 – Injection",
                                description=f"端点 {endpoint} 存在反射型XSS漏洞",
                                impact="攻击者可能执行恶意JavaScript代码",
                                remediation="对所有用户输入进行HTML编码",
                                evidence={
                                    'endpoint': endpoint,
                                    'payload': payload,
                                    'response_snippet': response_text[:500],
                                    'content_type': resp.headers.get('content-type')
                                },
                                cve_references=['CWE-79'],
                                owasp_category="A03",
                                confidence=0.85,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
                    
                    # 存储型XSS测试
                    post_data = {'comment': payload, 'message': payload}
                    async with self.session.post(f"{self.base_url}{endpoint}", json=post_data) as resp:
                        if resp.status in [200, 201]:
                            # 检查存储的内容
                            async with self.session.get(f"{self.base_url}{endpoint}") as get_resp:
                                get_response_text = await get_resp.text()
                                if payload in get_response_text:
                                    vuln = SecurityVulnerability(
                                        vuln_id=f"INJ-004-{hashlib.md5((endpoint + payload).encode()).hexdigest()[:8]}",
                                        title=f"存储型XSS漏洞: {endpoint}",
                                        severity="critical",
                                        category="A03:2021 – Injection",
                                        description=f"端点 {endpoint} 存在存储型XSS漏洞",
                                        impact="攻击者可能持久化执行恶意代码",
                                        remediation="对存储和输出的数据进行适当编码",
                                        evidence={
                                            'endpoint': endpoint,
                                            'payload': payload,
                                            'stored_response': get_response_text[:500]
                                        },
                                        cve_references=['CWE-79'],
                                        owasp_category="A03",
                                        confidence=0.95,
                                        timestamp=datetime.now().isoformat()
                                    )
                                    vulnerabilities.append(vuln)
                
                except Exception as e:
                    logger.warning(f"XSS测试失败 {endpoint} with {payload}: {e}")
        
        # 命令注入测试
        command_endpoints = ['/api/system/ping', '/api/tools/nslookup']
        for endpoint in command_endpoints:
            for payload in self.payloads['command_injection']:
                try:
                    params = {'host': f"127.0.0.1{payload}", 'target': f"localhost{payload}"}
                    async with self.session.get(f"{self.base_url}{endpoint}", params=params) as resp:
                        response_text = await resp.text()
                        if self._detect_command_injection(response_text):
                            vuln = SecurityVulnerability(
                                vuln_id=f"INJ-005-{hashlib.md5((endpoint + payload).encode()).hexdigest()[:8]}",
                                title=f"命令注入漏洞: {endpoint}",
                                severity="critical",
                                category="A03:2021 – Injection",
                                description=f"端点 {endpoint} 存在命令注入漏洞",
                                impact="攻击者可能执行任意系统命令",
                                remediation="避免直接执行用户输入，使用白名单验证",
                                evidence={
                                    'endpoint': endpoint,
                                    'payload': payload,
                                    'response_snippet': response_text[:500]
                                },
                                cve_references=['CWE-78'],
                                owasp_category="A03",
                                confidence=0.9,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
                
                except Exception as e:
                    logger.warning(f"命令注入测试失败 {endpoint} with {payload}: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="injection_attacks",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_injection_recommendations(vulnerabilities),
            scan_coverage={'sql_injection': True, 'xss': True, 'command_injection': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_insecure_design(self) -> SecurityScanResult:
        """A04: 不安全设计测试"""
        start_time = datetime.now()
        vulnerabilities = []
        
        # 测试业务逻辑缺陷
        # 1. 价格操纵测试
        try:
            # 模拟订单价格操纵
            order_data = {'item_id': 1, 'quantity': 1, 'price': -100}
            async with self.session.post(f"{self.base_url}/api/orders", json=order_data) as resp:
                if resp.status in [200, 201]:
                    vuln = SecurityVulnerability(
                        vuln_id="ID-001",
                        title="价格操纵漏洞",
                        severity="high",
                        category="A04:2021 – Insecure Design",
                        description="系统接受负数价格，存在价格操纵风险",
                        impact="攻击者可能通过负价格获得经济利益",
                        remediation="在服务器端验证业务逻辑，确保价格合理性",
                        evidence={
                            'payload': order_data,
                            'status_code': resp.status,
                            'response': await resp.text()
                        },
                        cve_references=['CWE-840'],
                        owasp_category="A04",
                        confidence=0.9,
                        timestamp=datetime.now().isoformat()
                    )
                    vulnerabilities.append(vuln)
        except Exception as e:
            logger.warning(f"价格操纵测试失败: {e}")
        
        # 2. 竞态条件测试
        try:
            # 并发请求测试
            tasks = []
            for i in range(10):
                task = asyncio.create_task(
                    self.session.post(f"{self.base_url}/api/transfer", 
                                    json={'from': 'user1', 'to': 'user2', 'amount': 100})
                )
                tasks.append(task)
            
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for r in responses if not isinstance(r, Exception) and r.status == 200)
            
            if success_count > 1:
                vuln = SecurityVulnerability(
                    vuln_id="ID-002",
                    title="竞态条件漏洞",
                    severity="medium",
                    category="A04:2021 – Insecure Design",
                    description="系统存在竞态条件，可能导致重复处理",
                    impact="攻击者可能通过并发请求绕过业务限制",
                    remediation="实施适当的锁机制和事务控制",
                    evidence={
                        'concurrent_requests': 10,
                        'successful_responses': success_count,
                        'expected_success': 1
                    },
                    cve_references=['CWE-362'],
                    owasp_category="A04",
                    confidence=0.8,
                    timestamp=datetime.now().isoformat()
                )
                vulnerabilities.append(vuln)
        except Exception as e:
            logger.warning(f"竞态条件测试失败: {e}")
        
        # 3. 工作流绕过测试
        try:
            # 测试跳过审批流程
            # 正常流程：创建 -> 提交 -> 审批 -> 执行
            # 尝试直接执行
            bypass_data = {'action': 'execute', 'skip_approval': True}
            async with self.session.post(f"{self.base_url}/api/workflow/execute", json=bypass_data) as resp:
                if resp.status == 200:
                    vuln = SecurityVulnerability(
                        vuln_id="ID-003",
                        title="工作流绕过漏洞",
                        severity="high",
                        category="A04:2021 – Insecure Design",
                        description="可以绕过正常的工作流程直接执行操作",
                        impact="攻击者可能绕过审批流程执行未授权操作",
                        remediation="强制执行完整的工作流程，验证每个步骤",
                        evidence={
                            'bypass_payload': bypass_data,
                            'status_code': resp.status
                        },
                        cve_references=['CWE-841'],
                        owasp_category="A04",
                        confidence=0.85,
                        timestamp=datetime.now().isoformat()
                    )
                    vulnerabilities.append(vuln)
        except Exception as e:
            logger.warning(f"工作流绕过测试失败: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="insecure_design",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_design_recommendations(vulnerabilities),
            scan_coverage={'business_logic': True, 'race_conditions': True, 'workflow_bypass': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_security_misconfiguration(self) -> SecurityScanResult:
        """A05: 安全配置错误测试"""
        start_time = datetime.now()
        vulnerabilities = []
        
        # 检查默认凭据
        default_credentials = [
            ('admin', 'admin'),
            ('admin', 'password'),
            ('root', 'root'),
            ('test', 'test'),
            ('guest', 'guest')
        ]
        
        for username, password in default_credentials:
            try:
                login_data = {'username': username, 'password': password}
                async with self.session.post(f"{self.base_url}/api/login", json=login_data) as resp:
                    if resp.status == 200:
                        response_data = await resp.text()
                        if 'token' in response_data or 'success' in response_data:
                            vuln = SecurityVulnerability(
                                vuln_id=f"SM-001-{username}",
                                title=f"默认凭据: {username}/{password}",
                                severity="critical",
                                category="A05:2021 – Security Misconfiguration",
                                description=f"系统使用默认凭据 {username}/{password}",
                                impact="攻击者可能使用默认凭据获得系统访问权限",
                                remediation="更改所有默认密码，实施强密码策略",
                                evidence={
                                    'username': username,
                                    'password': password,
                                    'status_code': resp.status,
                                    'response_snippet': response_data[:200]
                                },
                                cve_references=['CWE-521'],
                                owasp_category="A05",
                                confidence=1.0,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
            except Exception as e:
                logger.warning(f"默认凭据测试失败 {username}: {e}")
        
        # 检查敏感信息泄露
        info_disclosure_paths = [
            '/robots.txt',
            '/.env',
            '/config.json',
            '/api/config',
            '/api/info',
            '/api/version',
            '/debug',
            '/status',
            '/.git/config',
            '/backup.sql',
            '/phpinfo.php'
        ]
        
        for path in info_disclosure_paths:
            try:
                async with self.session.get(f"{self.base_url}{path}") as resp:
                    if resp.status == 200:
                        response_text = await resp.text()
                        sensitive_info = self._detect_sensitive_information(response_text)
                        if sensitive_info:
                            vuln = SecurityVulnerability(
                                vuln_id=f"SM-002-{path.replace('/', '_')}",
                                title=f"敏感信息泄露: {path}",
                                severity="medium",
                                category="A05:2021 – Security Misconfiguration",
                                description=f"路径 {path} 泄露敏感信息",
                                impact="攻击者可能获得系统配置或敏感数据",
                                remediation="移除或保护敏感信息文件",
                                evidence={
                                    'path': path,
                                    'sensitive_info': sensitive_info,
                                    'response_snippet': response_text[:300]
                                },
                                cve_references=['CWE-200'],
                                owasp_category="A05",
                                confidence=0.9,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
            except Exception as e:
                logger.warning(f"信息泄露测试失败 {path}: {e}")
        
        # 检查HTTP安全头
        security_headers = [
            'X-Frame-Options',
            'X-Content-Type-Options',
            'X-XSS-Protection',
            'Strict-Transport-Security',
            'Content-Security-Policy',
            'Referrer-Policy'
        ]
        
        try:
            async with self.session.get(self.base_url) as resp:
                missing_headers = []
                for header in security_headers:
                    if header not in resp.headers:
                        missing_headers.append(header)
                
                if missing_headers:
                    vuln = SecurityVulnerability(
                        vuln_id="SM-003",
                        title="缺少安全HTTP头",
                        severity="low",
                        category="A05:2021 – Security Misconfiguration",
                        description=f"缺少安全HTTP头: {', '.join(missing_headers)}",
                        impact="可能增加XSS、点击劫持等攻击风险",
                        remediation="配置适当的安全HTTP头",
                        evidence={
                            'missing_headers': missing_headers,
                            'present_headers': list(resp.headers.keys())
                        },
                        cve_references=['CWE-693'],
                        owasp_category="A05",
                        confidence=1.0,
                        timestamp=datetime.now().isoformat()
                    )
                    vulnerabilities.append(vuln)
        except Exception as e:
            logger.warning(f"安全头检查失败: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="security_misconfiguration",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_config_recommendations(vulnerabilities),
            scan_coverage={'default_credentials': True, 'info_disclosure': True, 'security_headers': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_vulnerable_components(self) -> SecurityScanResult:
        """A06: 易受攻击的组件测试"""
        start_time = datetime.now()
        vulnerabilities = []
        
        # 检查服务器版本信息
        try:
            async with self.session.get(self.base_url) as resp:
                server_header = resp.headers.get('Server', '')
                if server_header:
                    # 检查已知的易受攻击版本
                    vulnerable_versions = self._check_vulnerable_versions(server_header)
                    for vuln_info in vulnerable_versions:
                        vuln = SecurityVulnerability(
                            vuln_id=f"VC-001-{vuln_info['component']}",
                            title=f"易受攻击的组件: {vuln_info['component']}",
                            severity=vuln_info['severity'],
                            category="A06:2021 – Vulnerable and Outdated Components",
                            description=f"检测到易受攻击的组件版本: {vuln_info['version']}",
                            impact=vuln_info['impact'],
                            remediation=f"升级到安全版本: {vuln_info['safe_version']}",
                            evidence={
                                'server_header': server_header,
                                'component': vuln_info['component'],
                                'version': vuln_info['version']
                            },
                            cve_references=vuln_info['cve_list'],
                            owasp_category="A06",
                            confidence=0.8,
                            timestamp=datetime.now().isoformat()
                        )
                        vulnerabilities.append(vuln)
        except Exception as e:
            logger.warning(f"组件版本检查失败: {e}")
        
        # 检查JavaScript库版本
        try:
            async with self.session.get(self.base_url) as resp:
                if resp.status == 200:
                    html_content = await resp.text()
                    js_libraries = self._extract_js_libraries(html_content)
                    for lib_info in js_libraries:
                        vulnerable_js = self._check_vulnerable_js_versions(lib_info)
                        if vulnerable_js:
                            vuln = SecurityVulnerability(
                                vuln_id=f"VC-002-{lib_info['name']}",
                                title=f"易受攻击的JavaScript库: {lib_info['name']}",
                                severity=vulnerable_js['severity'],
                                category="A06:2021 – Vulnerable and Outdated Components",
                                description=f"检测到易受攻击的JavaScript库: {lib_info['name']} {lib_info['version']}",
                                impact=vulnerable_js['impact'],
                                remediation=f"升级到安全版本: {vulnerable_js['safe_version']}",
                                evidence={
                                    'library': lib_info['name'],
                                    'version': lib_info['version'],
                                    'source': lib_info['source']
                                },
                                cve_references=vulnerable_js['cve_list'],
                                owasp_category="A06",
                                confidence=0.7,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
        except Exception as e:
            logger.warning(f"JavaScript库检查失败: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="vulnerable_components",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_component_recommendations(vulnerabilities),
            scan_coverage={'server_components': True, 'js_libraries': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_identification_failures(self) -> SecurityScanResult:
        """A07: 身份识别和身份验证失败测试"""
        start_time = datetime.now()
        vulnerabilities = []
        
        # 暴力破解测试
        common_passwords = ['123456', 'password', 'admin', '123123', 'qwerty']
        test_usernames = ['admin', 'administrator', 'root', 'test', 'guest']
        
        for username in test_usernames:
            failed_attempts = 0
            for password in common_passwords:
                try:
                    login_data = {'username': username, 'password': password}
                    async with self.session.post(f"{self.base_url}/api/login", json=login_data) as resp:
                        if resp.status == 401:
                            failed_attempts += 1
                        elif resp.status == 200:
                            # 成功登录，可能是弱密码
                            vuln = SecurityVulnerability(
                                vuln_id=f"IF-001-{username}",
                                title=f"弱密码: {username}",
                                severity="high",
                                category="A07:2021 – Identification and Authentication Failures",
                                description=f"用户 {username} 使用弱密码 {password}",
                                impact="攻击者可能通过暴力破解获得访问权限",
                                remediation="实施强密码策略和账户锁定机制",
                                evidence={
                                    'username': username,
                                    'weak_password': password,
                                    'status_code': resp.status
                                },
                                cve_references=['CWE-521'],
                                owasp_category="A07",
                                confidence=1.0,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
                            break
                    
                    # 短暂延迟避免触发过于严格的限制
                    await asyncio.sleep(0.1)
                
                except Exception as e:
                    logger.warning(f"暴力破解测试失败 {username}/{password}: {e}")
            
            # 检查是否有账户锁定机制
            if failed_attempts >= 3:
                try:
                    # 再次尝试登录，检查是否被锁定
                    login_data = {'username': username, 'password': 'test'}
                    async with self.session.post(f"{self.base_url}/api/login", json=login_data) as resp:
                        if resp.status != 423:  # 423 = Locked
                            vuln = SecurityVulnerability(
                                vuln_id=f"IF-002-{username}",
                                title=f"缺少账户锁定机制: {username}",
                                severity="medium",
                                category="A07:2021 – Identification and Authentication Failures",
                                description=f"用户 {username} 在多次失败登录后未被锁定",
                                impact="攻击者可能进行无限制的暴力破解攻击",
                                remediation="实施账户锁定和登录尝试限制",
                                evidence={
                                    'username': username,
                                    'failed_attempts': failed_attempts,
                                    'final_status': resp.status
                                },
                                cve_references=['CWE-307'],
                                owasp_category="A07",
                                confidence=0.9,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
                except Exception as e:
                    logger.warning(f"账户锁定测试失败 {username}: {e}")
        
        # 会话管理测试
        try:
            # 测试会话固定
            async with self.session.get(f"{self.base_url}/api/session") as resp:
                if resp.status == 200:
                    initial_session = resp.cookies.get('sessionid')
                    if initial_session:
                        # 登录后检查会话是否改变
                        login_data = {'username': 'test', 'password': 'test'}
                        async with self.session.post(f"{self.base_url}/api/login", json=login_data) as login_resp:
                            if login_resp.status == 200:
                                new_session = login_resp.cookies.get('sessionid')
                                if initial_session == new_session:
                                    vuln = SecurityVulnerability(
                                        vuln_id="IF-003",
                                        title="会话固定漏洞",
                                        severity="medium",
                                        category="A07:2021 – Identification and Authentication Failures",
                                        description="登录后会话ID未更新，存在会话固定风险",
                                        impact="攻击者可能劫持用户会话",
                                        remediation="登录成功后重新生成会话ID",
                                        evidence={
                                            'initial_session': initial_session.value,
                                            'post_login_session': new_session.value if new_session else None
                                        },
                                        cve_references=['CWE-384'],
                                        owasp_category="A07",
                                        confidence=0.8,
                                        timestamp=datetime.now().isoformat()
                                    )
                                    vulnerabilities.append(vuln)
        except Exception as e:
            logger.warning(f"会话管理测试失败: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="identification_failures",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_auth_recommendations(vulnerabilities),
            scan_coverage={'brute_force': True, 'account_lockout': True, 'session_management': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_software_integrity_failures(self) -> SecurityScanResult:
        """A08: 软件和数据完整性失败测试"""
        start_time = datetime.now()
        vulnerabilities = []
        
        # 检查不安全的反序列化
        serialization_payloads = [
            # Python pickle payload
            "gASVKAAAAAAAAACMCGJ1aWx0aW5zlIwEZXZhbJSTlIwGX19pbXBvcnRfX5STlIwCb3OUhZRSlIwGc3lzdGVtlIwHZ2V0Y3dklIaUUpQu",
            # Java serialization
            "rO0ABXNyABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhwP0AAAAAAAAx3CAAAABAAAAABdAABYXQAAWJ4",
            # .NET BinaryFormatter
            "AAEAAAD/////AQAAAAAAAAAMAgAAAElTeXN0ZW0sIFZlcnNpb249NC4wLjAuMCwgQ3VsdHVyZT1uZXV0cmFsLCBQdWJsaWNLZXlUb2tlbj1iNzdhNWM1NjE5MzRlMDg5BQEAAAA="
        ]
        
        for payload in serialization_payloads:
            try:
                # 测试POST数据反序列化
                headers = {'Content-Type': 'application/octet-stream'}
                async with self.session.post(f"{self.base_url}/api/deserialize", data=base64.b64decode(payload), headers=headers) as resp:
                    response_text = await resp.text()
                    if self._detect_deserialization_attack(response_text, resp.status):
                        vuln = SecurityVulnerability(
                            vuln_id=f"SIF-001-{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                            title="不安全的反序列化漏洞",
                            severity="critical",
                            category="A08:2021 – Software and Data Integrity Failures",
                            description="应用程序存在不安全的反序列化漏洞",
                            impact="攻击者可能执行任意代码",
                            remediation="避免反序列化不可信数据，使用安全的数据格式",
                            evidence={
                                'payload_type': 'serialized_object',
                                'response_snippet': response_text[:300],
                                'status_code': resp.status
                            },
                            cve_references=['CWE-502'],
                            owasp_category="A08",
                            confidence=0.8,
                            timestamp=datetime.now().isoformat()
                        )
                        vulnerabilities.append(vuln)
            except Exception as e:
                logger.warning(f"反序列化测试失败: {e}")
        
        # 检查软件供应链完整性
        try:
            # 检查CDN资源完整性
            async with self.session.get(self.base_url) as resp:
                if resp.status == 200:
                    html_content = await resp.text()
                    external_resources = self._extract_external_resources(html_content)
                    for resource in external_resources:
                        if not resource.get('integrity'):
                            vuln = SecurityVulnerability(
                                vuln_id=f"SIF-002-{hashlib.md5(resource['url'].encode()).hexdigest()[:8]}",
                                title="缺少子资源完整性检查",
                                severity="medium",
                                category="A08:2021 – Software and Data Integrity Failures",
                                description=f"外部资源缺少完整性检查: {resource['url']}",
                                impact="恶意CDN或中间人攻击可能注入恶意代码",
                                remediation="为所有外部资源添加SRI (Subresource Integrity)检查",
                                evidence={
                                    'resource_url': resource['url'],
                                    'resource_type': resource['type'],
                                    'has_integrity': False
                                },
                                cve_references=['CWE-353'],
                                owasp_category="A08",
                                confidence=0.9,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
        except Exception as e:
            logger.warning(f"供应链完整性检查失败: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="software_integrity_failures",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_integrity_recommendations(vulnerabilities),
            scan_coverage={'deserialization': True, 'supply_chain': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_logging_monitoring_failures(self) -> SecurityScanResult:
        """A09: 安全日志和监控失败测试"""
        start_time = datetime.now()
        vulnerabilities = []
        
        # 测试敏感操作是否被记录
        sensitive_operations = [
            {'endpoint': '/api/admin/delete_user', 'method': 'DELETE', 'data': {'user_id': 1}},
            {'endpoint': '/api/admin/change_password', 'method': 'POST', 'data': {'user_id': 1, 'new_password': 'test123'}},
            {'endpoint': '/api/data/export', 'method': 'GET', 'data': {}},
            {'endpoint': '/api/config/update', 'method': 'PUT', 'data': {'setting': 'debug', 'value': True}}
        ]
        
        for operation in sensitive_operations:
            try:
                if operation['method'] == 'GET':
                    async with self.session.get(f"{self.base_url}{operation['endpoint']}") as resp:
                        # 检查是否有审计日志端点
                        audit_response = await self._check_audit_logs(operation['endpoint'])
                        if not audit_response:
                            vuln = SecurityVulnerability(
                                vuln_id=f"LMF-001-{operation['endpoint'].replace('/', '_')}",
                                title=f"敏感操作未记录: {operation['endpoint']}",
                                severity="medium",
                                category="A09:2021 – Security Logging and Monitoring Failures",
                                description=f"敏感操作 {operation['endpoint']} 可能未被适当记录",
                                impact="安全事件可能无法被检测和调查",
                                remediation="实施全面的安全日志记录和监控",
                                evidence={
                                    'operation': operation['endpoint'],
                                    'method': operation['method'],
                                    'audit_check': audit_response
                                },
                                cve_references=['CWE-778'],
                                owasp_category="A09",
                                confidence=0.7,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
                elif operation['method'] in ['POST', 'PUT', 'DELETE']:
                    method_func = getattr(self.session, operation['method'].lower())
                    async with method_func(f"{self.base_url}{operation['endpoint']}", json=operation['data']) as resp:
                        audit_response = await self._check_audit_logs(operation['endpoint'])
                        if not audit_response:
                            vuln = SecurityVulnerability(
                                vuln_id=f"LMF-002-{operation['endpoint'].replace('/', '_')}",
                                title=f"敏感操作未记录: {operation['endpoint']}",
                                severity="high",
                                category="A09:2021 – Security Logging and Monitoring Failures",
                                description=f"敏感操作 {operation['endpoint']} 可能未被适当记录",
                                impact="关键安全事件可能被忽略",
                                remediation="为所有敏感操作实施审计日志",
                                evidence={
                                    'operation': operation['endpoint'],
                                    'method': operation['method'],
                                    'data': operation['data']
                                },
                                cve_references=['CWE-778'],
                                owasp_category="A09",
                                confidence=0.8,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
            except Exception as e:
                logger.warning(f"日志监控测试失败 {operation['endpoint']}: {e}")
        
        # 测试日志注入
        log_injection_payloads = [
            "test\n[CRITICAL] Fake critical error",
            "user\r\n[ERROR] Injected log entry",
            "admin\x00[WARN] Null byte injection"
        ]
        
        for payload in log_injection_payloads:
            try:
                login_data = {'username': payload, 'password': 'test'}
                async with self.session.post(f"{self.base_url}/api/login", json=login_data) as resp:
                    # 检查日志是否可能被注入
                    if '\n' in payload or '\r' in payload or '\x00' in payload:
                        vuln = SecurityVulnerability(
                            vuln_id=f"LMF-003-{hashlib.md5(payload.encode()).hexdigest()[:8]}",
                            title="日志注入漏洞",
                            severity="low",
                            category="A09:2021 – Security Logging and Monitoring Failures",
                            description="应用程序可能存在日志注入漏洞",
                            impact="攻击者可能伪造日志条目",
                            remediation="清理和验证所有记录到日志的用户输入",
                            evidence={
                                'payload': payload,
                                'injection_chars': [c for c in payload if c in '\n\r\x00'],
                                'status_code': resp.status
                            },
                            cve_references=['CWE-117'],
                            owasp_category="A09",
                            confidence=0.6,
                            timestamp=datetime.now().isoformat()
                        )
                        vulnerabilities.append(vuln)
            except Exception as e:
                logger.warning(f"日志注入测试失败: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="logging_monitoring_failures",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_logging_recommendations(vulnerabilities),
            scan_coverage={'audit_logging': True, 'log_injection': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )
    
    async def _test_server_side_request_forgery(self) -> SecurityScanResult:
        """A10: 服务器端请求伪造测试"""
        start_time = datetime.now()
        vulnerabilities = []
        
        # SSRF测试载荷
        ssrf_payloads = [
            'http://127.0.0.1:22',
            'http://localhost:3306',
            'http://***************/latest/meta-data/',  # AWS metadata
            'http://metadata.google.internal/computeMetadata/v1/',  # GCP metadata
            'file:///etc/passwd',
            'ftp://127.0.0.1:21',
            'gopher://127.0.0.1:6379/_INFO',  # Redis
            'dict://127.0.0.1:11211/stats'  # Memcached
        ]
        
        ssrf_endpoints = [
            '/api/fetch_url',
            '/api/webhook',
            '/api/import_data',
            '/api/proxy'
        ]
        
        for endpoint in ssrf_endpoints:
            for payload in ssrf_payloads:
                try:
                    # GET参数SSRF
                    params = {'url': payload, 'target': payload, 'source': payload}
                    async with self.session.get(f"{self.base_url}{endpoint}", params=params) as resp:
                        response_text = await resp.text()
                        if self._detect_ssrf_response(response_text, payload):
                            vuln = SecurityVulnerability(
                                vuln_id=f"SSRF-001-{hashlib.md5((endpoint + payload).encode()).hexdigest()[:8]}",
                                title=f"SSRF漏洞: {endpoint}",
                                severity="high",
                                category="A10:2021 – Server-Side Request Forgery",
                                description=f"端点 {endpoint} 存在SSRF漏洞",
                                impact="攻击者可能访问内部服务或敏感数据",
                                remediation="验证和限制外部请求的目标URL",
                                evidence={
                                    'endpoint': endpoint,
                                    'payload': payload,
                                    'method': 'GET',
                                    'response_snippet': response_text[:300]
                                },
                                cve_references=['CWE-918'],
                                owasp_category="A10",
                                confidence=0.8,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
                    
                    # POST数据SSRF
                    post_data = {'url': payload, 'callback_url': payload}
                    async with self.session.post(f"{self.base_url}{endpoint}", json=post_data) as resp:
                        response_text = await resp.text()
                        if self._detect_ssrf_response(response_text, payload):
                            vuln = SecurityVulnerability(
                                vuln_id=f"SSRF-002-{hashlib.md5((endpoint + payload).encode()).hexdigest()[:8]}",
                                title=f"SSRF漏洞 (POST): {endpoint}",
                                severity="high",
                                category="A10:2021 – Server-Side Request Forgery",
                                description=f"端点 {endpoint} 的POST数据存在SSRF漏洞",
                                impact="攻击者可能通过POST请求访问内部资源",
                                remediation="实施URL白名单和请求验证",
                                evidence={
                                    'endpoint': endpoint,
                                    'payload': payload,
                                    'method': 'POST',
                                    'response_snippet': response_text[:300]
                                },
                                cve_references=['CWE-918'],
                                owasp_category="A10",
                                confidence=0.8,
                                timestamp=datetime.now().isoformat()
                            )
                            vulnerabilities.append(vuln)
                
                except Exception as e:
                    logger.warning(f"SSRF测试失败 {endpoint} with {payload}: {e}")
        
        end_time = datetime.now()
        
        return SecurityScanResult(
            scan_type="server_side_request_forgery",
            target=self.base_url,
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            duration=(end_time - start_time).total_seconds(),
            vulnerabilities=vulnerabilities,
            summary=self._summarize_vulnerabilities(vulnerabilities),
            recommendations=self._generate_ssrf_recommendations(vulnerabilities),
            scan_coverage={'url_fetching': True, 'internal_access': True},
            risk_score=self._calculate_risk_score(vulnerabilities)
        )