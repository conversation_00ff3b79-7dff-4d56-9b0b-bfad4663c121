"""Performance monitoring integration for Connect system.

This module provides comprehensive performance monitoring capabilities
including metrics collection, alerting, and performance analysis.
"""

import asyncio
import logging
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Callable
from collections import defaultdict, deque
import psutil
import threading
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Individual performance metric."""
    name: str
    value: float
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)
    unit: str = ""


@dataclass
class AlertRule:
    """Performance alert rule configuration."""
    metric_name: str
    threshold: float
    operator: str  # 'gt', 'lt', 'eq', 'gte', 'lte'
    duration: float = 60.0  # Seconds threshold must be exceeded
    callback: Optional[Callable] = None
    enabled: bool = True


@dataclass
class SystemMetrics:
    """System-level performance metrics."""
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used_mb: float = 0.0
    memory_available_mb: float = 0.0
    disk_io_read_mb: float = 0.0
    disk_io_write_mb: float = 0.0
    network_sent_mb: float = 0.0
    network_recv_mb: float = 0.0
    active_connections: int = 0
    timestamp: float = field(default_factory=time.time)


class PerformanceMonitor:
    """Comprehensive performance monitoring system.
    
    Collects, analyzes, and alerts on system and application performance metrics.
    Designed for single-machine deployment with minimal overhead.
    """
    
    def __init__(
        self,
        collection_interval: float = 5.0,
        retention_hours: int = 24,
        max_metrics_per_type: int = 1000
    ):
        """Initialize performance monitor.
        
        Args:
            collection_interval: Seconds between metric collections
            retention_hours: Hours to retain metrics in memory
            max_metrics_per_type: Maximum metrics to store per type
        """
        self.collection_interval = collection_interval
        self.retention_hours = retention_hours
        self.max_metrics_per_type = max_metrics_per_type
        
        # Metric storage
        self._metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_metrics_per_type))
        self._system_metrics: deque = deque(maxlen=max_metrics_per_type)
        
        # Alert system
        self._alert_rules: List[AlertRule] = []
        self._alert_states: Dict[str, Dict] = defaultdict(dict)
        
        # Control
        self._running = False
        self._collection_task: Optional[asyncio.Task] = None
        self._system_collection_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self._last_disk_io = None
        self._last_network_io = None
        self._start_time = time.time()
        
        # Thread safety
        self._lock = threading.RLock()
    
    async def start(self) -> None:
        """Start performance monitoring."""
        if self._running:
            return
        
        self._running = True
        
        # Start collection tasks
        self._collection_task = asyncio.create_task(self._collect_metrics_loop())
        self._system_collection_task = asyncio.create_task(self._collect_system_metrics_loop())
        
        logger.info("Performance monitoring started")
    
    async def stop(self) -> None:
        """Stop performance monitoring."""
        self._running = False
        
        # Cancel tasks
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        
        if self._system_collection_task:
            self._system_collection_task.cancel()
            try:
                await self._system_collection_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Performance monitoring stopped")
    
    def record_metric(
        self,
        name: str,
        value: float,
        tags: Optional[Dict[str, str]] = None,
        unit: str = ""
    ) -> None:
        """Record a performance metric.
        
        Args:
            name: Metric name
            value: Metric value
            tags: Optional tags for the metric
            unit: Unit of measurement
        """
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=time.time(),
            tags=tags or {},
            unit=unit
        )
        
        with self._lock:
            self._metrics[name].append(metric)
            
            # Check alerts
            self._check_alerts(metric)
    
    def record_timing(
        self,
        name: str,
        duration: float,
        tags: Optional[Dict[str, str]] = None
    ) -> None:
        """Record a timing metric.
        
        Args:
            name: Operation name
            duration: Duration in seconds
            tags: Optional tags
        """
        self.record_metric(f"{name}_duration", duration, tags, "seconds")
    
    def record_counter(
        self,
        name: str,
        increment: int = 1,
        tags: Optional[Dict[str, str]] = None
    ) -> None:
        """Record a counter metric.
        
        Args:
            name: Counter name
            increment: Amount to increment
            tags: Optional tags
        """
        # Get current value
        current = self.get_latest_metric(name)
        new_value = (current.value if current else 0) + increment
        
        self.record_metric(name, new_value, tags, "count")
    
    def get_latest_metric(self, name: str) -> Optional[PerformanceMetric]:
        """Get the latest metric value.
        
        Args:
            name: Metric name
            
        Returns:
            Latest metric or None if not found
        """
        with self._lock:
            metrics = self._metrics.get(name)
            return metrics[-1] if metrics else None
    
    def get_metrics(
        self,
        name: str,
        since: Optional[float] = None,
        limit: Optional[int] = None
    ) -> List[PerformanceMetric]:
        """Get metrics for a given name.
        
        Args:
            name: Metric name
            since: Timestamp to filter from
            limit: Maximum number of metrics to return
            
        Returns:
            List of metrics
        """
        with self._lock:
            metrics = list(self._metrics.get(name, []))
        
        # Filter by timestamp
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]
        
        # Apply limit
        if limit:
            metrics = metrics[-limit:]
        
        return metrics
    
    def get_system_metrics(
        self,
        since: Optional[float] = None,
        limit: Optional[int] = None
    ) -> List[SystemMetrics]:
        """Get system metrics.
        
        Args:
            since: Timestamp to filter from
            limit: Maximum number of metrics to return
            
        Returns:
            List of system metrics
        """
        with self._lock:
            metrics = list(self._system_metrics)
        
        # Filter by timestamp
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]
        
        # Apply limit
        if limit:
            metrics = metrics[-limit:]
        
        return metrics
    
    def add_alert_rule(self, rule: AlertRule) -> None:
        """Add an alert rule.
        
        Args:
            rule: Alert rule to add
        """
        self._alert_rules.append(rule)
        logger.info(f"Added alert rule for {rule.metric_name}")
    
    def remove_alert_rule(self, metric_name: str) -> None:
        """Remove alert rules for a metric.
        
        Args:
            metric_name: Metric name to remove rules for
        """
        self._alert_rules = [
            rule for rule in self._alert_rules
            if rule.metric_name != metric_name
        ]
        
        # Clear alert state
        if metric_name in self._alert_states:
            del self._alert_states[metric_name]
        
        logger.info(f"Removed alert rules for {metric_name}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary.
        
        Returns:
            Performance summary dictionary
        """
        with self._lock:
            # Get latest system metrics
            latest_system = self._system_metrics[-1] if self._system_metrics else None
            
            # Calculate uptime
            uptime_seconds = time.time() - self._start_time
            
            # Get metric counts
            metric_counts = {name: len(metrics) for name, metrics in self._metrics.items()}
            
            summary = {
                "uptime_seconds": uptime_seconds,
                "uptime_hours": uptime_seconds / 3600,
                "monitoring_active": self._running,
                "metric_types_count": len(self._metrics),
                "total_metrics_collected": sum(metric_counts.values()),
                "metric_counts": metric_counts,
                "alert_rules_count": len(self._alert_rules),
                "active_alerts": len([s for s in self._alert_states.values() if s.get("active")]),
            }
            
            # Add latest system metrics
            if latest_system:
                summary.update({
                    "current_cpu_percent": latest_system.cpu_percent,
                    "current_memory_percent": latest_system.memory_percent,
                    "current_memory_used_mb": latest_system.memory_used_mb,
                    "current_active_connections": latest_system.active_connections,
                })
            
            return summary
    
    async def _collect_metrics_loop(self) -> None:
        """Main metrics collection loop."""
        while self._running:
            try:
                await asyncio.sleep(self.collection_interval)
                await self._cleanup_old_metrics()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Metrics collection error: {e}")
                await asyncio.sleep(1)
    
    async def _collect_system_metrics_loop(self) -> None:
        """System metrics collection loop."""
        while self._running:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(self.collection_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"System metrics collection error: {e}")
                await asyncio.sleep(5)
    
    async def _collect_system_metrics(self) -> None:
        """Collect system performance metrics."""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()
            disk_read_mb = 0.0
            disk_write_mb = 0.0
            
            if disk_io and self._last_disk_io:
                read_bytes = disk_io.read_bytes - self._last_disk_io.read_bytes
                write_bytes = disk_io.write_bytes - self._last_disk_io.write_bytes
                disk_read_mb = read_bytes / (1024 * 1024)
                disk_write_mb = write_bytes / (1024 * 1024)
            
            self._last_disk_io = disk_io
            
            # Network I/O
            network_io = psutil.net_io_counters()
            network_sent_mb = 0.0
            network_recv_mb = 0.0
            
            if network_io and self._last_network_io:
                sent_bytes = network_io.bytes_sent - self._last_network_io.bytes_sent
                recv_bytes = network_io.bytes_recv - self._last_network_io.bytes_recv
                network_sent_mb = sent_bytes / (1024 * 1024)
                network_recv_mb = recv_bytes / (1024 * 1024)
            
            self._last_network_io = network_io
            
            # Network connections
            connections = psutil.net_connections()
            active_connections = len([c for c in connections if c.status == 'ESTABLISHED'])
            
            # Create system metrics
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / (1024 * 1024),
                memory_available_mb=memory.available / (1024 * 1024),
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                active_connections=active_connections
            )
            
            with self._lock:
                self._system_metrics.append(metrics)
            
            # Record individual metrics for alerting
            self.record_metric("system.cpu_percent", cpu_percent, unit="percent")
            self.record_metric("system.memory_percent", memory.percent, unit="percent")
            self.record_metric("system.memory_used_mb", metrics.memory_used_mb, unit="MB")
            self.record_metric("system.active_connections", active_connections, unit="count")
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
    
    async def _cleanup_old_metrics(self) -> None:
        """Clean up old metrics based on retention policy."""
        cutoff_time = time.time() - (self.retention_hours * 3600)
        
        with self._lock:
            # Clean application metrics
            for name, metrics in self._metrics.items():
                while metrics and metrics[0].timestamp < cutoff_time:
                    metrics.popleft()
            
            # Clean system metrics
            while self._system_metrics and self._system_metrics[0].timestamp < cutoff_time:
                self._system_metrics.popleft()
    
    def _check_alerts(self, metric: PerformanceMetric) -> None:
        """Check if metric triggers any alerts.
        
        Args:
            metric: Metric to check
        """
        for rule in self._alert_rules:
            if not rule.enabled or rule.metric_name != metric.name:
                continue
            
            # Evaluate condition
            triggered = self._evaluate_alert_condition(metric.value, rule)
            
            # Update alert state
            alert_key = f"{rule.metric_name}_{rule.operator}_{rule.threshold}"
            state = self._alert_states[alert_key]
            
            if triggered:
                if "first_triggered" not in state:
                    state["first_triggered"] = metric.timestamp
                    state["active"] = False
                
                # Check if duration threshold is met
                if metric.timestamp - state["first_triggered"] >= rule.duration:
                    if not state.get("active"):
                        state["active"] = True
                        self._fire_alert(rule, metric)
            else:
                # Reset alert state
                if state.get("active"):
                    state["active"] = False
                    logger.info(f"Alert resolved for {rule.metric_name}")
                state.pop("first_triggered", None)
    
    def _evaluate_alert_condition(self, value: float, rule: AlertRule) -> bool:
        """Evaluate if alert condition is met.
        
        Args:
            value: Metric value
            rule: Alert rule
            
        Returns:
            True if condition is met
        """
        if rule.operator == "gt":
            return value > rule.threshold
        elif rule.operator == "gte":
            return value >= rule.threshold
        elif rule.operator == "lt":
            return value < rule.threshold
        elif rule.operator == "lte":
            return value <= rule.threshold
        elif rule.operator == "eq":
            return value == rule.threshold
        else:
            return False
    
    def _fire_alert(self, rule: AlertRule, metric: PerformanceMetric) -> None:
        """Fire an alert.
        
        Args:
            rule: Alert rule that was triggered
            metric: Metric that triggered the alert
        """
        logger.warning(
            f"ALERT: {rule.metric_name} {rule.operator} {rule.threshold} "
            f"(current: {metric.value})"
        )
        
        # Call custom callback if provided
        if rule.callback:
            try:
                rule.callback(rule, metric)
            except Exception as e:
                logger.error(f"Alert callback error: {e}")


# Global performance monitor instance
_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor instance.
    
    Returns:
        Performance monitor instance
    """
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


def record_metric(name: str, value: float, tags: Optional[Dict[str, str]] = None, unit: str = "") -> None:
    """Convenience function to record a metric.
    
    Args:
        name: Metric name
        value: Metric value
        tags: Optional tags
        unit: Unit of measurement
    """
    get_performance_monitor().record_metric(name, value, tags, unit)


def record_timing(name: str, duration: float, tags: Optional[Dict[str, str]] = None) -> None:
    """Convenience function to record timing.
    
    Args:
        name: Operation name
        duration: Duration in seconds
        tags: Optional tags
    """
    get_performance_monitor().record_timing(name, duration, tags)


class TimingContext:
    """Context manager for timing operations."""
    
    def __init__(self, name: str, tags: Optional[Dict[str, str]] = None):
        """Initialize timing context.
        
        Args:
            name: Operation name
            tags: Optional tags
        """
        self.name = name
        self.tags = tags
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            record_timing(self.name, duration, self.tags)


def timing(name: str, tags: Optional[Dict[str, str]] = None) -> TimingContext:
    """Create a timing context manager.
    
    Args:
        name: Operation name
        tags: Optional tags
        
    Returns:
        Timing context manager
    """
    return TimingContext(name, tags)