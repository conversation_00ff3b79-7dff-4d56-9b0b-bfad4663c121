"""Data validation utility module.

This module provides database-related input validation functionality, including validation of schema names, table names, column names, etc.
"""

import re
from typing import Optional


class InputValidator:
    """Input validator class.

    Provides static methods for validating database-related inputs, such as schema names, table names, column names, etc.
    All validation methods follow these rules:
    - Must start with a letter
    - Can only contain letters, numbers and underscores
    - Length must not exceed 63 characters
    - Lowercase is recommended
    """

    # Validation regex: starts with letter, followed by letters, numbers or underscores
    _NAME_PATTERN = re.compile(r"^[a-zA-Z][a-zA-Z0-9_]*$")

    # Maximum length limit
    _MAX_LENGTH = 63

    @staticmethod
    def validate_schema_name(name: str) -> bool:
        """Validate schema name.

        Args:
            name: Schema name to validate

        Returns:
            bool: True if validation passes, False otherwise

        Examples:
            >>> InputValidator.validate_schema_name('my_schema')
            True
            >>> InputValidator.validate_schema_name('123invalid')
            False
            >>> InputValidator.validate_schema_name('valid_schema_name')
            True
        """
        if not isinstance(name, str):
            return False

        if not name or len(name) > InputValidator._MAX_LENGTH:
            return False

        return bool(InputValidator._NAME_PATTERN.match(name))

    @staticmethod
    def validate_table_name(name: str) -> bool:
        """Validate table name.

        Args:
            name: Table name to validate

        Returns:
            bool: True if validation passes, False otherwise

        Examples:
            >>> InputValidator.validate_table_name('users')
            True
            >>> InputValidator.validate_table_name('user_profiles')
            True
            >>> InputValidator.validate_table_name('2invalid')
            False
        """
        if not isinstance(name, str):
            return False

        if not name or len(name) > InputValidator._MAX_LENGTH:
            return False

        return bool(InputValidator._NAME_PATTERN.match(name))

    @staticmethod
    def validate_column_name(name: str) -> bool:
        """Validate column name.

        Args:
            name: Column name to validate

        Returns:
            bool: True if validation passes, False otherwise

        Examples:
            >>> InputValidator.validate_column_name('user_id')
            True
            >>> InputValidator.validate_column_name('firstName')
            True
            >>> InputValidator.validate_column_name('_invalid')
            False
        """
        if not isinstance(name, str):
            return False

        if not name or len(name) > InputValidator._MAX_LENGTH:
            return False

        return bool(InputValidator._NAME_PATTERN.match(name))

    @staticmethod
    def validate_identifier(
        name: str, identifier_type: str = "identifier"
    ) -> tuple[bool, Optional[str]]:
        """Generic identifier validation method.

        Args:
            name: Identifier name to validate
            identifier_type: Identifier type for error messages

        Returns:
            tuple[bool, Optional[str]]: (is_valid, error_message)

        Examples:
            >>> InputValidator.validate_identifier('valid_name')
            (True, None)
            >>> InputValidator.validate_identifier('123invalid')
            (False, 'identifier must start with a letter')
        """
        if not isinstance(name, str):
            return False, f"{identifier_type} must be a string"

        if not name:
            return False, f"{identifier_type} cannot be empty"

        if len(name) > InputValidator._MAX_LENGTH:
            return (
                False,
                f"{identifier_type} length cannot exceed {InputValidator._MAX_LENGTH} characters",
            )

        if not InputValidator._NAME_PATTERN.match(name):
            if not name[0].isalpha():
                return False, f"{identifier_type} must start with a letter"
            else:
                return (
                    False,
                    f"{identifier_type} can only contain letters, numbers, and underscores",
                )

        return True, None

    @staticmethod
    def is_lowercase_recommended(name: str) -> bool:
        """Check if name is in recommended lowercase format.

        Args:
            name: Name to check

        Returns:
            bool: True if name is lowercase, False otherwise

        Examples:
            >>> InputValidator.is_lowercase_recommended('user_table')
            True
            >>> InputValidator.is_lowercase_recommended('UserTable')
            False
        """
        return name == name.lower()

    @staticmethod
    def normalize_name(name: str) -> str:
        """Normalize name to lowercase format.

        Args:
            name: Name to normalize

        Returns:
            str: Normalized lowercase name

        Examples:
            >>> InputValidator.normalize_name('UserTable')
            'usertable'
            >>> InputValidator.normalize_name('user_TABLE')
            'user_table'
        """
        return name.lower() if isinstance(name, str) else str(name).lower()

    @staticmethod
    def truncate_table_name(name: str, max_length: int = 63) -> str:
        """Truncate table name to fit PostgreSQL limits with smart hashing.

        Args:
            name: Original table name
            max_length: Maximum allowed length (default: 63 for PostgreSQL)

        Returns:
            str: Truncated table name with hash suffix if needed

        Examples:
            >>> InputValidator.truncate_table_name('very_long_table_name_that_exceeds_postgresql_limit')
            'very_long_table_name_that_exceeds_postgresql_lim_a1b2c3d4'
        """
        import hashlib

        if not isinstance(name, str):
            name = str(name)

        # Normalize to lowercase
        name = name.lower()

        # If name is within limit, return as-is
        if len(name) <= max_length:
            return name

        # Calculate hash of the full name
        hash_obj = hashlib.md5(name.encode('utf-8'))
        hash_suffix = hash_obj.hexdigest()[:8]  # Use first 8 characters of hash

        # Calculate available space for the original name
        # Reserve space for underscore and hash
        available_length = max_length - len(hash_suffix) - 1

        # Truncate the original name and append hash
        truncated_name = name[:available_length] + '_' + hash_suffix

        return truncated_name

    @staticmethod
    def validate_and_fix_table_name(name: str) -> str:
        """Validate and fix table name to meet PostgreSQL requirements.

        Args:
            name: Original table name

        Returns:
            str: Valid PostgreSQL table name

        Raises:
            ValueError: If name cannot be fixed
        """
        if not name:
            raise ValueError("Table name cannot be empty")

        # Clean the name: replace invalid characters with underscores
        import re
        cleaned_name = re.sub(r'[^a-zA-Z0-9_]', '_', str(name))

        # Ensure it starts with a letter
        if not cleaned_name[0].isalpha():
            cleaned_name = 'table_' + cleaned_name

        # Normalize to lowercase
        cleaned_name = cleaned_name.lower()

        # Truncate if necessary
        final_name = InputValidator.truncate_table_name(cleaned_name)

        # Final validation
        if not InputValidator.validate_table_name(final_name):
            raise ValueError(f"Cannot create valid table name from: {name}")

        return final_name

    @staticmethod
    def validate_coordinates(lat: float, lon: float) -> bool:
        """Validate geographic coordinates.

        Args:
            lat: Latitude value
            lon: Longitude value

        Returns:
            bool: True if coordinates are valid, False otherwise

        Examples:
            >>> InputValidator.validate_coordinates(40.7128, -74.0060)
            True
            >>> InputValidator.validate_coordinates(91.0, 0.0)
            False
        """
        if not isinstance(lat, (int, float)) or not isinstance(lon, (int, float)):
            return False
        
        # Latitude must be between -90 and 90
        if lat < -90 or lat > 90:
            return False
        
        # Longitude must be between -180 and 180
        if lon < -180 or lon > 180:
            return False
        
        return True


# Convenience function for backward compatibility
validate_coordinates = InputValidator.validate_coordinates
