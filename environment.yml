# Conda environment configuration for Connect geospatial platform
# This environment provides comprehensive geospatial processing capabilities
# with QGIS integration and all necessary dependencies

name: connect-qgis
channels:
  - conda-forge
  - defaults

dependencies:
  # Python version (compatible with QGIS)
  - python=3.12

  # Core geospatial libraries
  - gdal>=3.7.0
  - geopandas>=0.14.0
  - shapely>=2.0.0
  - fiona>=1.9.0
  - pyproj>=3.6.0
  - rasterio>=1.3.0

  # QGIS and Qt dependencies
  - qgis>=3.30.0
  - qt>=5.15.0
  - pyqt>=5.15.0

  # Database connections
  - psycopg2>=2.9.0
  - sqlalchemy>=2.0.0

  # Data processing
  - pandas>=2.1.0
  - numpy>=1.24.0

  # Additional dependencies via pip
  - pip
  - pip:
    - python-dotenv>=1.0.0
    - pydantic>=2.4.0
    - loguru>=0.7.0
